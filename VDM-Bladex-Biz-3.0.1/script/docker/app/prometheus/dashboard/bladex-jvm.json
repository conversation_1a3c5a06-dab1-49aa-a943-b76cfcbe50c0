{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "limit": 100, "name": "Annotations & Alerts", "showIn": 0, "type": "dashboard"}, {"datasource": "Prometheus", "enable": true, "expr": "resets(process_uptime_seconds{application=\"$application\", instance=\"$instance\"}[1m]) > 0", "iconColor": "rgba(255, 96, 96, 1)", "limit": 100, "name": "进程重启打标", "showIn": 0, "step": "1m", "tagKeys": "restart-tag", "tags": [], "textFormat": "uptime reset", "titleFormat": "<PERSON><PERSON>", "type": "tags"}]}, "description": "SpringCloud微服务JVM监控", "editable": true, "gnetId": 12856, "graphTooltip": 1, "id": 16, "iteration": 1612272472672, "links": [{"icon": "cloud", "tags": [], "title": "BladeX", "type": "link", "url": "https://bladex.vip"}], "panels": [{"collapsed": false, "datasource": "${DS_SPRING-DEMOT}", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 125, "panels": [], "repeat": null, "title": "概览", "type": "row"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "decimals": 1, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "s", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 0, "y": 1}, "height": "", "id": 63, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "70%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "process_uptime_seconds{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 14400}], "thresholds": "", "title": "进程启动时长", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "decimals": null, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "dateTimeAsIso", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 6, "y": 1}, "height": "", "id": 92, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "70%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "process_start_time_seconds{application=\"$application\", instance=\"$instance\"}*1000", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "metric": "", "refId": "A", "step": 14400}], "thresholds": "", "title": "进程启动时间", "type": "singlestat", "valueFontSize": "70%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 12, "y": 1}, "id": 65, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "70%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"})*100/sum(jvm_memory_max_bytes{application=\"$application\",instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 14400}], "thresholds": "70,90", "title": "堆内存使用率", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": true, "colors": ["rgba(50, 172, 45, 0.97)", "rgba(237, 129, 40, 0.89)", "rgba(245, 54, 54, 0.9)"], "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 6, "x": 18, "y": 1}, "id": 75, "interval": null, "links": [], "mappingType": 2, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "70%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}, {"from": "-99999999999999999999999999999999", "text": "N/A", "to": "0"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"})*100/sum(jvm_memory_max_bytes{application=\"$application\",instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 14400}], "thresholds": "70,90", "title": "非堆内存使用率", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}, {"op": "=", "text": "x", "value": ""}], "valueName": "current"}, {"collapsed": false, "datasource": "${DS_SPRING-DEMOT}", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 4}, "id": 126, "panels": [], "repeat": null, "title": "服务黄金指标", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "平均每秒处理的请求数", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 5}, "hiddenSeries": false, "id": 111, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(http_server_requests_seconds_count{application=\"$application\", instance=\"$instance\"}[1m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "HTTP", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "QPS(1分钟平均)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"HTTP": "#890f02", "HTTP - 5xx": "#bf1b00"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 5}, "hiddenSeries": false, "id": 112, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(http_server_requests_seconds_count{application=\"$application\", instance=\"$instance\", status=~\"5..\"}[1m]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "HTTP - 5xx", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "错误数(1分钟平均)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 5}, "hiddenSeries": false, "id": 113, "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(http_server_requests_seconds_sum{application=\"$application\", instance=\"$instance\", status!~\"5..\"}[1m]))/sum(rate(http_server_requests_seconds_count{application=\"$application\", instance=\"$instance\", status!~\"5..\"}[1m]))", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "HTTP - AVG", "refId": "A"}, {"expr": "max(http_server_requests_seconds_max{application=\"$application\", instance=\"$instance\", status!~\"5..\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "HTTP - MAX", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "请求耗时(1分钟平均)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "饱和度指标来自于Google SRE的的黄金指标, 指服务的过载程度, 当系统过载时, 往往意味着请求需要排队处理", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 5}, "hiddenSeries": false, "id": 119, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "tomcat_threads_busy_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "TOMCAT - BSY", "refId": "A"}, {"expr": "tomcat_threads_current_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "TOMCAT - CUR", "refId": "B"}, {"expr": "tomcat_threads_config_max_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "TOMCAT - MAX", "refId": "C"}, {"expr": "jetty_threads_busy{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - BSY", "refId": "D"}, {"expr": "jetty_threads_current{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - CUR", "refId": "E"}, {"expr": "jetty_threads_config_max{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "JETTY - MAX", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "饱和度", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "${DS_SPRING-DEMOT}", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 127, "panels": [], "repeat": null, "title": "JVM 内存", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 13}, "hiddenSeries": false, "id": 24, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"expr": "sum(jvm_memory_committed_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "refId": "B", "step": 2400}, {"expr": "sum(jvm_memory_max_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "C", "step": 2400}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "堆内存", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 13}, "hiddenSeries": false, "id": 25, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"expr": "sum(jvm_memory_committed_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "refId": "B", "step": 2400}, {"expr": "sum(jvm_memory_max_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "C", "step": 2400}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "非堆内存", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 13}, "hiddenSeries": false, "id": 26, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"expr": "sum(jvm_memory_committed_bytes{application=\"$application\", instance=\"$instance\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "committed", "refId": "B", "step": 2400}, {"expr": "sum(jvm_memory_max_bytes{application=\"$application\", instance=\"$instance\"})", "format": "time_series", "intervalFactor": 2, "legendFormat": "max", "refId": "C", "step": 2400}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "总内存", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "从操作系统层面看JVM进程的内存使用, 因为JVM并不是直接按照配置的内存参数申请全部内存", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 13}, "hiddenSeries": false, "id": 86, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_memory_vss_bytes{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": true, "intervalFactor": 2, "legendFormat": "vss", "metric": "", "refId": "A", "step": 2400}, {"expr": "process_memory_rss_bytes{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "rss", "refId": "B"}, {"expr": "process_memory_swap_bytes{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "swap", "refId": "C"}, {"expr": "process_memory_rss_bytes{application=\"$application\", instance=\"$instance\"} + process_memory_swap_bytes{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "total", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "JVM 进程内存", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 128, "panels": [], "repeat": null, "title": "JVM 负载", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 21}, "hiddenSeries": false, "id": 106, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "system_cpu_usage{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "system", "metric": "", "refId": "A", "step": 2400}, {"expr": "process_cpu_usage{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "process", "refId": "B"}, {"expr": "avg_over_time(process_cpu_usage{application=\"$application\", instance=\"$instance\"}[1h])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "process-1h", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU 使用率", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 1, "format": "percentunit", "label": "", "logBase": 1, "max": "1", "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 21}, "hiddenSeries": false, "id": 93, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "system_load_average_1m{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "load1", "metric": "", "refId": "A", "step": 2400}, {"expr": "system_cpu_count{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "cpu核数", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Load", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 1, "format": "short", "label": "", "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 21}, "hiddenSeries": false, "id": 32, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_threads_live_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "live", "metric": "", "refId": "A", "step": 2400}, {"expr": "jvm_threads_daemon_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "daemon", "metric": "", "refId": "B", "step": 2400}, {"expr": "jvm_threads_peak_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "peak", "refId": "C", "step": 2400}, {"expr": "process_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "process", "refId": "D", "step": 2400}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "线程数", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"blocked": "#bf1b00", "new": "#fce2de", "runnable": "#7eb26d", "terminated": "#511749", "timed-waiting": "#c15c17", "waiting": "#eab839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "需特别关注blocked的线程数, 这意味着线程被阻塞了, 如果线程全部是blocked状态, 则系统无法处理新请求", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 21}, "hiddenSeries": false, "id": 124, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_threads_states_threads{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "{{state}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "各状态线程数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"debug": "#1F78C1", "error": "#BF1B00", "info": "#508642", "trace": "#6ED0E0", "warn": "#EAB839"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 18, "x": 0, "y": 28}, "height": "", "hiddenSeries": false, "id": 91, "legend": {"alignAsTable": false, "avg": false, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "error", "yaxis": 1}, {"alias": "warn", "yaxis": 1}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(logback_events_total{application=\"$application\", instance=\"$instance\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{level}}", "metric": "", "refId": "A", "step": 1200}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Logback日志数", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "opm", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 28}, "hiddenSeries": false, "id": 61, "legend": {"avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_files_open_files{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "open", "metric": "", "refId": "A", "step": 2400}, {"expr": "process_files_max_files{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "B", "step": 2400}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "文件描述符", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 10, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 35}, "id": 129, "panels": [], "repeat": "persistence_counts", "title": "JVM 堆内存详细", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 36}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 3, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "jvm_memory_pool_heap", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 1800}, {"expr": "jvm_memory_committed_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "commited", "metric": "", "refId": "B", "step": 1800}, {"expr": "jvm_memory_max_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_heap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "C", "step": 1800}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "$jvm_memory_pool_heap", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 43}, "id": 130, "panels": [], "repeat": null, "title": "JVM 非堆内存详细", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 44}, "hiddenSeries": false, "id": 78, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 3, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "repeat": "jvm_memory_pool_nonheap", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 1800}, {"expr": "jvm_memory_committed_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "commited", "metric": "", "refId": "B", "step": 1800}, {"expr": "jvm_memory_max_bytes{application=\"$application\", instance=\"$instance\", id=~\"$jvm_memory_pool_nonheap\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "max", "metric": "", "refId": "C", "step": 1800}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "$jvm_memory_pool_nonheap", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["mbytes", "short"], "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 51}, "id": 131, "panels": [], "repeat": null, "title": "垃圾回收(GC)", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 52}, "hiddenSeries": false, "id": 98, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(jvm_gc_pause_seconds_count{application=\"$application\", instance=\"$instance\"}[1m])", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "{{action}} ({{cause}})", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC 次数", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 52}, "hiddenSeries": false, "id": 101, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(jvm_gc_pause_seconds_sum{application=\"$application\", instance=\"$instance\"}[1m])/rate(jvm_gc_pause_seconds_count{application=\"$application\", instance=\"$instance\"}[1m])", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "avg {{action}} ({{cause}})", "refId": "A"}, {"expr": "jvm_gc_pause_seconds_max{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "hide": false, "instant": false, "intervalFactor": 1, "legendFormat": "max {{action}} ({{cause}})", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC暂停时间", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "内存分配的大小, 以及从新生代晋升到老年代的内存大小", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 52}, "hiddenSeries": false, "id": 99, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(jvm_gc_memory_allocated_bytes_total{application=\"$application\", instance=\"$instance\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "allocated", "refId": "A"}, {"expr": "rate(jvm_gc_memory_promoted_bytes_total{application=\"$application\", instance=\"$instance\"}[1m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "promoted", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "内存分配/晋升", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 59}, "id": 132, "panels": [], "repeat": null, "title": "类加载", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 60}, "hiddenSeries": false, "id": 37, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_classes_loaded_classes{application=\"$application\", instance=\"$instance\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "loaded", "metric": "", "refId": "A", "step": 1200}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "已加载的类的数量", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "description": "可能增加或减少, 在Java中使用一些脚本语言例如groovy时, 需要关注, 防止因为逻辑异常产生大量的类, 进而导致metaspace满, 而metaspace满会触发full gc, 如无法释放则会导致JVM hang住", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 60}, "hiddenSeries": false, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "delta(jvm_classes_loaded_classes{application=\"$application\",instance=\"$instance\"}[1m])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "delta-1m", "metric": "", "refId": "A", "step": 1200}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "加载类数量变化", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["ops", "short"], "yaxes": [{"decimals": null, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": "Prometheus", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 67}, "id": 133, "panels": [], "repeat": null, "title": "Buffer Pools", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 68}, "hiddenSeries": false, "id": 33, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_buffer_memory_used_bytes{application=\"$application\", instance=\"$instance\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"expr": "jvm_buffer_total_capacity_bytes{application=\"$application\", instance=\"$instance\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "capacity", "metric": "", "refId": "B", "step": 2400}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Direct Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 68}, "hiddenSeries": false, "id": 83, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_buffer_count_buffers{application=\"$application\", instance=\"$instance\", id=\"direct\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "count", "metric": "", "refId": "A", "step": 2400}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Direct Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 68}, "hiddenSeries": false, "id": 85, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_buffer_memory_used_bytes{application=\"$application\", instance=\"$instance\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "used", "metric": "", "refId": "A", "step": 2400}, {"expr": "jvm_buffer_total_capacity_bytes{application=\"$application\", instance=\"$instance\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "capacity", "metric": "", "refId": "B", "step": 2400}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Mapped Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {"leftLogBase": 1, "leftMax": null, "leftMin": null, "rightLogBase": 1, "rightMax": null, "rightMin": null}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 68}, "hiddenSeries": false, "id": 84, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "jvm_buffer_count_buffers{application=\"$application\", instance=\"$instance\", id=\"mapped\"}", "format": "time_series", "intervalFactor": 2, "legendFormat": "count", "metric": "", "refId": "A", "step": 2400}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Mapped Buffers", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "x-axis": true, "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "y-axis": true, "y_formats": ["short", "short"], "yaxes": [{"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": 0, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": "10s", "schemaVersion": 26, "style": "dark", "tags": ["jvm"], "templating": {"list": [{"allValue": null, "current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": "Prometheus", "definition": "", "error": null, "hide": 0, "includeAll": false, "label": "服务名", "multi": false, "name": "application", "options": [], "query": "label_values(application)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allFormat": "glob", "allValue": null, "current": {"isNone": true, "selected": false, "text": "None", "value": ""}, "datasource": "Prometheus", "definition": "", "error": null, "hide": 0, "includeAll": false, "label": "实例地址", "multi": false, "multiFormat": "glob", "name": "instance", "options": [], "query": "label_values(jvm_memory_used_bytes{application=\"$application\"}, instance)", "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allFormat": "glob", "allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "", "error": null, "hide": 0, "includeAll": true, "label": "JVM堆内存", "multi": false, "multiFormat": "glob", "name": "jvm_memory_pool_heap", "options": [], "query": "label_values(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"heap\"},id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allFormat": "glob", "allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "", "error": null, "hide": 0, "includeAll": true, "label": "JVM非堆内存", "multi": false, "multiFormat": "glob", "name": "jvm_memory_pool_nonheap", "options": [], "query": "label_values(jvm_memory_used_bytes{application=\"$application\", instance=\"$instance\", area=\"nonheap\"},id)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"now": true, "refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "BladeX监控大屏--JVM", "uid": "bladex-jvm", "version": 1}