{"annotations": {"list": [{"builtIn": 1, "datasource": "Prometheus", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "limit": 100, "name": "Annotations & Alerts", "showIn": 0, "type": "dashboard"}]}, "description": "SpringCloud服务Docker监控", "editable": true, "gnetId": 11600, "graphTooltip": 1, "id": 26, "iteration": 1612431043072, "links": [{"icon": "cloud", "tags": [], "title": "BladeX", "type": "link", "url": "https://bladex.vip"}], "panels": [{"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 8, "x": 0, "y": 0}, "height": "20", "id": 28, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "count(container_last_seen{image!=\"\"})", "intervalFactor": 2, "legendFormat": "", "metric": "container_last_seen", "refId": "A", "step": 240}], "thresholds": "", "title": "运行容器", "transparent": true, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "avg"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "mbytes", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 8, "x": 8, "y": 0}, "height": "20", "id": 24, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum(container_memory_usage_bytes{image!=\"\"})/1024/1024", "intervalFactor": 2, "legendFormat": "", "metric": "container_memory_usage_bytes", "refId": "A", "step": 240}], "thresholds": "", "title": "内存占用", "transparent": true, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "percent", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 3, "w": 8, "x": 16, "y": 0}, "height": "20", "id": 26, "interval": null, "isNew": true, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false}, "tableColumn": "", "targets": [{"expr": "sum(rate(container_cpu_user_seconds_total{image!=\"\"}[5m]) * 100)", "intervalFactor": 2, "legendFormat": "", "metric": "container_memory_usage_bytes", "refId": "A", "step": 240}], "thresholds": "", "title": "CPU占用", "transparent": true, "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "current"}, {"aliasColors": {"{id=\"/\",instance=\"cadvisor:8080\",job=\"prometheus\"}": "#BA43A9"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 3, "fillGradient": 0, "grid": {}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 3}, "hiddenSeries": false, "id": 5, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(container_cpu_system_seconds_total{instance=~\"$node\"}[1m]))", "hide": true, "intervalFactor": 2, "legendFormat": "a", "refId": "B", "step": 120}, {"expr": "sum(rate(container_cpu_system_seconds_total{instance=~\"$node\",name=~\".+\"}[1m]))", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "nur container", "refId": "F", "step": 10}, {"expr": "sum(rate(container_cpu_system_seconds_total{instance=~\"$node\",id=\"/\"}[1m]))", "hide": true, "interval": "", "intervalFactor": 2, "legendFormat": "nur docker host", "metric": "", "refId": "A", "step": 20}, {"expr": "sum(rate(process_cpu_seconds_total{instance=~\"$node\"}[$interval])) * 100", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "host", "metric": "", "refId": "C", "step": 4}, {"expr": "sum(rate(container_cpu_system_seconds_total{instance=~\"$node\",name=~\".+\"}[1m])) + sum(rate(container_cpu_system_seconds_total{instance=~\"$node\",id=\"/\"}[1m])) + sum(rate(process_cpu_seconds_total{instance=~\"$node\"}[1m]))", "hide": true, "intervalFactor": 2, "legendFormat": "", "refId": "D", "step": 120}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点CPU用量", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": false, "values": []}, "yaxes": [{"format": "percent", "label": "", "logBase": 1, "max": 120, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"SENT": "#BF1B00"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 5, "fillGradient": 0, "grid": {}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 3}, "hiddenSeries": false, "id": 19, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 1, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_receive_bytes_total{instance=~\"$node\",id=\"/\"}[$interval])) by (id)", "intervalFactor": 2, "legendFormat": "RECEIVED", "refId": "A", "step": 4}, {"expr": "- sum(rate(container_network_transmit_bytes_total{instance=~\"$node\",id=\"/\"}[$interval])) by (id)", "hide": false, "intervalFactor": 2, "legendFormat": "SENT", "refId": "B", "step": 4}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "节点网络传输", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": false, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 9}, "hiddenSeries": false, "id": 30, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(container_cpu_user_seconds_total{image!=\"\"}[5m]) * 100", "intervalFactor": 2, "legendFormat": "{{name}}", "metric": "cpu", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU占用趋势", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "decimals": 2, "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 16}, "hiddenSeries": false, "id": 32, "isNew": true, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "container_memory_usage_bytes{image!=\"\"}", "hide": false, "intervalFactor": 2, "legendFormat": "{{name}}", "metric": "container_memory_usage_bytes", "refId": "A", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "内存占用趋势", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 3, "fillGradient": 0, "grid": {}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 23}, "hiddenSeries": false, "id": 11, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "container_memory_rss{instance=~\"$node\",name=~\".+\"}", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "A", "step": 20}, {"expr": "container_memory_usage_bytes{instance=~\"$node\",name=~\".+\"}", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "B", "step": 20}, {"expr": "sum(container_memory_cache{instance=~\"$node\",name=~\".+\"}) by (name)", "hide": false, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "C", "step": 2}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "容器缓存内存", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 3, "fillGradient": 0, "grid": {}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 23}, "hiddenSeries": false, "id": 10, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(container_memory_rss{instance=~\"$node\",name=~\".+\"}) by (name)", "hide": false, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "A", "step": 2}, {"expr": "container_memory_usage_bytes{instance=~\"$node\",name=~\".+\"}", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "B", "step": 240}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "容器内存占用", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 33}, "hiddenSeries": false, "id": 9, "isNew": true, "legend": {"avg": false, "current": false, "hideEmpty": false, "hideZero": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_transmit_bytes_total{instance=~\"$node\",name=~\".+\"}[$interval])) by (name)", "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "A", "step": 2}, {"expr": "rate(container_network_transmit_bytes_total{instance=~\"$node\",id=\"/\"}[$interval])", "hide": true, "intervalFactor": 2, "legendFormat": "", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "容器网络发送趋势", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": "", "logBase": 10, "max": 8, "min": 0, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 5, "fillGradient": 0, "grid": {}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 33}, "hiddenSeries": false, "id": 1, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(rate(container_cpu_usage_seconds_total{instance=~\"$node\",name=~\".+\"}[$interval])) by (name) * 100", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{name}}", "metric": "container_cp", "refId": "F", "step": 2}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "容器CPU占用", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": "", "logBase": 1, "max": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "grid": {}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 43}, "hiddenSeries": false, "id": 8, "isNew": true, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.7", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(rate(container_network_receive_bytes_total{instance=~\"$node\",name=~\".+\"}[$interval])) by (name)", "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "A", "step": 2}, {"expr": "- rate(container_network_transmit_bytes_total{instance=~\"$node\",name=~\".+\"}[$interval])", "hide": true, "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "B", "step": 10}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "容器网络接收趋势", "tooltip": {"msResolution": true, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "Bps", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"columns": [{"text": "Avg", "value": "avg"}], "datasource": "Prometheus", "editable": true, "error": false, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fontSize": "100%", "gridPos": {"h": 10, "w": 12, "x": 12, "y": 43}, "hideTimeOverride": false, "id": 18, "isNew": true, "links": [], "pageSize": 100, "scroll": true, "showHeader": true, "sort": {"col": 0, "desc": true}, "styles": [{"align": "auto", "dateFormat": "YYYY-MM-DD HH:mm:ss", "pattern": "Time", "type": "date"}, {"align": "auto", "colorMode": null, "colors": ["rgba(245, 54, 54, 0.9)", "rgba(237, 129, 40, 0.89)", "rgba(50, 172, 45, 0.97)"], "decimals": 2, "pattern": "/.*/", "thresholds": [], "type": "number", "unit": "short"}], "targets": [{"expr": "cadvisor_version_info{instance=~\"$node\"}", "intervalFactor": 2, "legendFormat": "cAdvisor Version: {{cadvisorVersion}}", "refId": "A", "step": 2}, {"expr": "prometheus_build_info{}", "intervalFactor": 2, "legendFormat": "Prometheus Version: {{version}}", "refId": "B", "step": 2}, {"expr": "node_exporter_build_info{instance=~\"$node\"}", "intervalFactor": 2, "legendFormat": "Node-Exporter Version: {{version}}", "refId": "C", "step": 2}, {"expr": "cadvisor_version_info{instance=~\"$node\"}", "hide": false, "intervalFactor": 2, "legendFormat": "Docker Version: {{dockerVersion}}", "refId": "D", "step": 2}, {"expr": "cadvisor_version_info{instance=~\"$node\"}", "intervalFactor": 2, "legendFormat": "Host OS Version: {{osVersion}}", "refId": "E", "step": 2}, {"expr": "cadvisor_version_info{instance=~\"$node\"}", "intervalFactor": 2, "legendFormat": "Host Kernel Version: {{kernelVersion}}", "refId": "F", "step": 2}], "timeFrom": null, "timeShift": null, "title": "中间件版本", "transform": "timeseries_aggregations", "type": "table-old"}], "refresh": "5s", "schemaVersion": 26, "style": "dark", "tags": ["docker"], "templating": {"list": [{"auto": true, "auto_count": 50, "auto_min": "50s", "current": {"selected": false, "text": "auto", "value": "$__auto_interval_interval"}, "datasource": null, "error": null, "hide": 0, "includeAll": false, "label": "间隔", "multi": false, "name": "interval", "options": [{"selected": true, "text": "auto", "value": "$__auto_interval_interval"}, {"selected": false, "text": "30s", "value": "30s"}, {"selected": false, "text": "1m", "value": "1m"}, {"selected": false, "text": "2m", "value": "2m"}, {"selected": false, "text": "3m", "value": "3m"}, {"selected": false, "text": "5m", "value": "5m"}, {"selected": false, "text": "7m", "value": "7m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "30s,1m,2m,3m,5m,7m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "refresh": 2, "skipUrlSync": false, "type": "interval"}, {"allValue": null, "current": {"selected": false, "text": "docker", "value": "docker"}, "datasource": "Prometheus", "definition": "label_values(container_cpu_user_seconds_total, job)", "error": null, "hide": 0, "includeAll": false, "label": "任务", "multi": false, "name": "job", "options": [], "query": "label_values(container_cpu_user_seconds_total, job)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(container_cpu_user_seconds_total{job=~\"$job\"}, instance)", "error": null, "hide": 0, "includeAll": true, "label": "节点", "multi": true, "name": "node", "options": [], "query": "label_values(container_cpu_user_seconds_total{job=~\"$job\"}, instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "All", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(container_cpu_user_seconds_total{instance=~\"***********:18080|***********:18080\", name!=\"cadvisor\"}, name)", "error": null, "hide": 2, "includeAll": true, "label": "Prod", "multi": true, "name": "Prod", "options": [], "query": "label_values(container_cpu_user_seconds_total{instance=~\"***********:18080|***********:18080\", name!=\"cadvisor\"}, name)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 1, "tagValuesQuery": "apps.prod", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": "All", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "Prometheus", "definition": "label_values(container_cpu_user_seconds_total{instance=~\"***********:18080|***********:180800\", name!=\"cadvisor\"}, name)", "error": null, "hide": 2, "includeAll": true, "label": "NonProd", "multi": true, "name": "NonProd", "options": [], "query": "label_values(container_cpu_user_seconds_total{instance=~\"***********:18080|***********:180800\", name!=\"cadvisor\"}, name)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "BladeX监控大屏--<PERSON>er", "uid": "bladex-docker", "version": 1}