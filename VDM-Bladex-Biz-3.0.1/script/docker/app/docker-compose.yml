version: '3'
services:

  ####################################################################################################
  ###===================================  以下为中间件模块  =========================================###
  ####################################################################################################

  nacos:
    image: nacos/nacos-server:1.3.2
    hostname: "nacos-standalone"
    environment:
      - MODE=standalone
      - TZ=Asia/Shanghai
    volumes:
      - /docker/nacos/standalone-logs/:/home/<USER>/logs
      - /docker/nacos/init.d/custom.properties:/home/<USER>/init.d/custom.properties
    ports:
      - 8848:8848
    networks:
      blade_net:
        ipv4_address: ***********

  sentinel:
    image: bladex/sentinel-dashboard:1.8.0
    hostname: "sentinel"
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 8858:8858
    restart: on-failure
    networks:
      blade_net:
        ipv4_address: ***********

  seata-server:
    image: seataio/seata-server:1.4.1
    hostname: "seata-server"
    ports:
      - 8091:8091
    environment:
      - TZ=Asia/Shanghai
      - SEATA_PORT=8091
      - STORE_MODE=file
    networks:
      blade_net:
        ipv4_address: ***********

  blade-nginx:
    image: nginx:stable-alpine-perl
    hostname: "blade-nginx"
    environment:
      - TZ=Asia/Shanghai
    ports:
    - 88:88
    volumes:
    - /docker/nginx/api/nginx.conf:/etc/nginx/nginx.conf
    privileged: true
    restart: always
    networks:
    - blade_net

  web-nginx:
    image: nginx:stable-alpine-perl
    hostname: "web-nginx"
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 8000:8000
    volumes:
      - /docker/nginx/web/html:/usr/share/nginx/html
      - /docker/nginx/web/nginx.conf:/etc/nginx/nginx.conf
    privileged: true
    restart: always
    networks:
      - blade_net

  blade-redis:
    image: redis:5.0.8-alpine
    hostname: "blade-redis"
    environment:
      - TZ=Asia/Shanghai
    ports:
    - 3379:6379
    volumes:
    - /docker/redis/data:/data
    command: "redis-server --appendonly yes"
    privileged: true
    restart: always
    networks:
    - blade_net

  ####################################################################################################
  ###=================================  以下为BladeX服务模块  =======================================###
  ####################################################################################################

  blade-admin:
    image: "${REGISTER}/blade-admin:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    ports:
    - 7002:7002
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  blade-swagger:
    image: "${REGISTER}/blade-swagger:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 18000:18000
    privileged: true
    restart: always
    networks:
      - blade_net

  blade-turbine:
    image: "${REGISTER}/blade-turbine:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 7003:7003
    privileged: true
    restart: always
    networks:
      - blade_net

  blade-gateway1:
    image: "${REGISTER}/blade-gateway:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  blade-gateway2:
    image: "${REGISTER}/blade-gateway:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  blade-auth1:
    image: "${REGISTER}/blade-auth:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  blade-auth2:
    image: "${REGISTER}/blade-auth:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  blade-report:
    image: "${REGISTER}/blade-report:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  blade-log:
    image: "${REGISTER}/blade-log:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    networks:
    - blade_net

  blade-desk:
    image: "${REGISTER}/blade-desk:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    networks:
    - blade_net

  blade-user:
    image: "${REGISTER}/blade-user:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    networks:
    - blade_net

  blade-system:
    image: "${REGISTER}/blade-system:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    networks:
    - blade_net

  blade-flow:
    image: "${REGISTER}/blade-flow:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    networks:
      - blade_net

  blade-flow-design:
    image: "${REGISTER}/blade-flow-design:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    ports:
      - 9999:9999
    networks:
      - blade_net

  blade-resource:
    image: "${REGISTER}/blade-resource:${TAG}"
    environment:
      - TZ=Asia/Shanghai
    privileged: true
    restart: always
    networks:
      - blade_net

  ####################################################################################################
  ###===============================  以下为Prometheus监控模块  =====================================###
  ####################################################################################################

  prometheus:
    image: prom/prometheus:v2.24.1
    hostname: "prometheus"
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 9090:9090
    volumes:
      - /docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - /docker/prometheus/rules:/etc/prometheus/rules
    command: "--config.file=/etc/prometheus/prometheus.yml  --web.enable-lifecycle"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  node-exporter:
    image: prom/node-exporter:v1.0.1
    hostname: "node-exporter"
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 9190:9100
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  mysqld-exporter:
    image: prom/mysqld-exporter:v0.12.1
    hostname: "mysqld-exporter"
    environment:
      - TZ=Asia/Shanghai
      # 需要先在mysql服务执行如下语句
      # =====================================================================================
      # === CREATE USER 'exporter'@'mysql服务ip' IDENTIFIED BY '密码';                     ===
      # === GRANT PROCESS, REPLICATION CLIENT, SELECT ON *.* TO 'exporter'@'mysql服务ip';  ===
      # === flush privileges;                                                             ===
      # =====================================================================================
      - DATA_SOURCE_NAME=exporter:密码@(mysql服务ip:mysql服务端口)/
    ports:
      - 9104:9104
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  cadvisor:
    image: google/cadvisor:v0.33.0
    hostname: "cadvisor"
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 18080:8080
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    command: "detach=true"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ************

  grafana:
    image: grafana/grafana:7.3.7
    hostname: "grafana"
    environment:
      - TZ=Asia/Shanghai
      - GF_SERVER_ROOT_URL=https://grafana.bladex.vip
      - GF_SECURITY_ADMIN_PASSWORD=1qaz@WSX
    ports:
      - 3000:3000
    volumes:
      - /docker/grafana/grafana.ini:/etc/grafana/grafana.ini
      - /docker/grafana:/var/lib/grafana
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  alertmanager:
    image: prom/alertmanager:v0.21.0
    hostname: "alertmanager"
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 9093:9093
    volumes:
      - /docker/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml
      - /docker/alertmanager/data:/etc/alertmanager/data
      - /docker/alertmanager/templates:/etc/alertmanager/templates
    command: "--config.file=/etc/alertmanager/alertmanager.yml --storage.path=/etc/alertmanager/data"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

  webhook-dingtalk:
    image: timonwong/prometheus-webhook-dingtalk:v1.4.0
    hostname: "webhook-dingtalk"
    environment:
      - TZ=Asia/Shanghai
    ports:
      - 8060:8060
    command: "ding.profile=webhook_robot=https://oapi.dingtalk.com/robot/send?access_token=xxxxx"
    privileged: true
    restart: always
    networks:
      blade_net:
        ipv4_address: ***********

networks:
  blade_net:
    driver: bridge
    ipam:
      config:
      - subnet: **********/16
