/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.example.stream.api.process;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;

/**
 * 通道定义
 *
 * <AUTHOR>
 */
public interface StreamProcess {
	/**
	 * 输出通道名称
	 */
	String OUTPUT = "stream-output";
	/**
	 * 输入通道名称
	 */
	String INPUT = "stream-input";

	/**
	 * 输出通道定义
	 *
	 * @return MessageChannel
	 */
	@Output(StreamProcess.OUTPUT)
	MessageChannel output();

	/**
	 * 输入通道定义
	 *
	 * @return SubscribableChannel
	 */
	@Input(StreamProcess.INPUT)
	SubscribableChannel input();

}
