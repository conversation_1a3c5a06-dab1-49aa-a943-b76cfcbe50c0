<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>BladeX-Biz</artifactId>
        <groupId>org.springblade</groupId>
        <version>*******.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>blade-example-api</artifactId>
    <name>${project.artifactId}</name>
    <version>*******.RELEASE</version>
    <packaging>pom</packaging>
    <description>BladeX 微服务范例API集合</description>

    <modules>
        <module>blade-dubbo-provider-api</module>
        <module>blade-stream-api</module>
    </modules>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                    <finalName>${project.name}</finalName>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
