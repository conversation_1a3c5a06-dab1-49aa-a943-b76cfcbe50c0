package com.xh.vdm.alarm.entity;

import lombok.Data;

/**
 * @Description: 今日报警情况
 * @Author: zhouxw
 * @Date: 2023/6/14 20:30
 */
@Data
public class AlarmInfoToday {

	//今日报警总数
	private Long todayTotalAlarmCount = 0L;

	//超速报警数
	private Long overSpeedCount = 0L;

	//疲劳报警数
	private Long fatigueCount = 0L;

	//夜间异动报警数
	private Long nightCount = 0L;

	//主动安全报警数
	private Long activeCount = 0L;

	//ADAS报警数
	private Long adasCount;

	//DSM报警数
	private Long dsmCount;

	//紧急报警数
	private Long emergencyCount;

	//其他实时报警数
	private Long otherCount = 0L;


}
