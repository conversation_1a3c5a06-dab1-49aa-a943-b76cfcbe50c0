package com.xh.vdm.alarm.entity;

import lombok.Data;

/**
 * @Description: 实时报警情况
 * @Author: zhouxw
 * @Date: 2023/6/14 20:30
 */
@Data
public class AlarmInfo {

	//实时报警总数
	private Long realTimeAlarmTotalCount = 0L;

	//实时报警处理总数
	private Long realTimeAlarmHandleTotalCount = 0L;

	//超速实时报警数
	private Long realTimeOverSpeedCount = 0L;

	//超速实时报警处理数
	private Long realTimeOverSpeedHandleCount = 0L;

	//疲劳实时报警数
	private Long realTimeTiredCount = 0L;

	//疲劳实时报警处理数
	private Long realTimeTiredHandleCount = 0L;

	//夜间实时报警数
	private Long realTimeNightCount = 0L;

	//夜间实时报警处理数
	private Long realTimeNightHandleCount = 0L;

	//主动安全实时报警数
	private Long realTimeAcitveCount = 0L;

	//主动安全实时报警处理
	private Long realTimeAcitveHandleCount = 0L;

	//其他实时报警数
	private Long realTimeOtherCount = 0L;

	//其他实时报警处理数
	private Long realTimeOtherHandleCount = 0L;

	//今日报警总数
	private Long todayTotalAlarmCount = 0L;

	//报警处理率
	private String handleRate;

}
