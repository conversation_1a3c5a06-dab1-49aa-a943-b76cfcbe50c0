package com.xh.vdm.alarm.feign;

import com.xh.vdm.alarm.entity.AlarmInfo;
import org.springblade.common.constant.ApplicationConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Description: 报警feign
 * @Author: zhouxw
 * @Date: 2023/6/13 20:33
 */
@FeignClient(
	value = CommonConstant.APP_NAME_ALARM+""
)
public interface IAlarmClient {

	//根路径
	String API_PREFIX = "/client";

	//查询今日报警总数
	String TODAY_ALARM_COUNT = API_PREFIX + "/today-alarm-count";

	//查询今日报警处理总数
	String TODAY_ALARM_HANDLE_COUNT = API_PREFIX + "/today-alarm-handle-count";

	//查询今日实时报警总数
	String TODAY_REALTIME_ALARM_COUNT = API_PREFIX + "/today-realtime-alarm-count";

	//报警情况：今日报警总数、实时报警总数、超速实时报警数、疲劳实时报警数、夜间行驶实时报警数、主动安全实时报警总数 、报警处理率
	String TODAY_REALTIME_ALARM_INFO = API_PREFIX + "/today-realtime-alarm-info";

	//今日累计报警情况
	String TODAY_ALARM_INFO = API_PREFIX + "/today-alarm-info";

	//报警处理情况
	String TODAY_REALTIME_ALARM_HANDLE_INFO = API_PREFIX + "/today-realtime-alarm-handle-info";

	/**
	 * 查询今日报警总数
	 * @return
	 */
	@GetMapping(TODAY_ALARM_COUNT)
	R<Long> getTodayAlarmCount(@RequestParam("deptId") Long deptId, @RequestParam("userId") Long userId);


	/**
	 * @description: 查询今日报警处理总数
	 * @author: zhouxw
	 * @date: 2023-06-167 08:14:58
	 * @param: [deptId, userId, roleType 用户角色类型 1 服务商  2 第三方服务商 3 企业]
	 * @return: org.springblade.core.tool.api.R<java.lang.Long>
	 **/
	@GetMapping(TODAY_ALARM_HANDLE_COUNT)
	R<Long> getTodayAlarmHandleCount(@RequestParam("deptId") Long deptId, @RequestParam("userId") Long userId, @RequestParam("roleType") String roleType);


	/**
	 * @description: 统计今日实时报警数
	 * @author: zhouxw
	 * @date: 2023-06-165 10:38:06
	 * @param: [deptId, userId]
	 * @return: org.springblade.core.tool.api.R<java.lang.Long>
	 **/
	@GetMapping(TODAY_REALTIME_ALARM_COUNT)
	R<Long> getRealTimeAlarmCount(@RequestParam("deptId") Long deptId, @RequestParam("userId") Long userId);


	/**
	 * @description: 统计报警情况
	 * @author: zhouxw
	 * @date: 2023-06-165 20:40:15
	 * @param: [deptId, userId]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.alarm.entity.AlarmInfo>
	 **/
	@GetMapping(TODAY_REALTIME_ALARM_INFO)
	R<AlarmInfo> getAlarmInfo(@RequestParam("deptId") Long deptId, @RequestParam("userId") Long userId);


	/**
	 * 统计今日累计报警情况
	 * @param deptId
	 * @param userId
	 * @return
	 */
	@GetMapping(TODAY_ALARM_INFO)
	R<AlarmInfo> getTodayAlarmInfo(@RequestParam("deptId") Long deptId, @RequestParam("userId") Long userId);


	/**
	 * @description: 统计报警处理情况
	 * @author: zhouxw
	 * @date: 2023-06-165 20:40:15
	 * @param: [deptId, userId]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.alarm.entity.AlarmInfo>
	 **/
	@GetMapping(TODAY_REALTIME_ALARM_HANDLE_INFO)
	R<AlarmInfo> getAlarmHandleInfo(@RequestParam("deptId") Long deptId, @RequestParam("userId") Long userId, @RequestParam("roleType") String roleType);

}
