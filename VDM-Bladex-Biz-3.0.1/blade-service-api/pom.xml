<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>BladeX-Biz</artifactId>
        <groupId>org.springblade</groupId>
        <version>*******.RELEASE</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>blade-service-api</artifactId>
    <name>${project.artifactId}</name>
    <version>*******.RELEASE</version>
    <packaging>pom</packaging>
    <description>BladeX 微服务API集合</description>

    <modules>
        <module>blade-demo-api</module>
        <module>vdm-big-data-api</module>
        <module>go-baseinfo-api</module>
        <module>go-terminal-api</module>
        <module>go-safe-education-api</module>
        <module>vdm-alarm-api</module>
        <module>go-security-api</module>
        <module>vdm-inter-manager-api</module>
        <module>vdm-base-info-api</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-models</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-core-auto</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                    <finalName>${project.name}</finalName>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
