/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.base.feign;

import com.xh.vdm.base.entity.Alarms;
import org.springblade.common.constant.ApplicationConstant;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Notice Feign接口类
 * 添加feign配置，在调用go服务接口时，添加 tenantId 和 account
 * <AUTHOR>
 */
@FeignClient(
	value = ApplicationConstant.APPLICATION_GO_SECURITY/*,
	configuration = GoFeignRequestInterceptor.class*/
)
public interface IAlarmInfoClient {

	String API_PREFIX = "/securitymanagement";

	String SEND_ALARM = API_PREFIX + "/dealalarmsync";

	/**
	 * 传递报警id列表，将报警信息传到kafka
	 * @return
	 */
	@PostMapping(SEND_ALARM)
	R sendAlarm(@RequestBody Alarms alarms);

}
