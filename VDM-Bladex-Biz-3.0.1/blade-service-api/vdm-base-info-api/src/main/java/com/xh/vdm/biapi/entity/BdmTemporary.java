package com.xh.vdm.biapi.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 外包人员表
 */
@Data
public class BdmTemporary implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	@Compare("人员姓名")
	private String name;

	@Compare("工号/身份证号")
	private String wkno;

	private Integer targetType;
	@Compare("所属机构")
	private Long deptId;
	@Compare("从业类型")
	private Integer post;
	@Compare("岗位类型")
	private Integer industry;

	@Compare("联系电话")
	private String phone;

	private Long companyId;
	@Compare("所属公司名称")
	private String company;
	@JsonFormat(pattern = "yyyy-MM-dd")
	@Compare("有效日期")
	private Date validFrom;
	@JsonFormat(pattern = "yyyy-MM-dd")
	@Compare("终止日期")
	private Date validTo;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;
}

