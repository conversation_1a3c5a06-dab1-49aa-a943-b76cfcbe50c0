package com.xh.vdm.biapi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.util.Date;
import java.io.Serializable;

/**
 * @Description: 精密装备实体表
 */
@Data
public class BdmPrecisionAssembly implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	@Compare("装备编号")
	private String number;

	private Integer targetType;
	@Compare("装备名称")
	private String name;
	@Compare("装备型号")
	private String model;
	@Compare("制造商")
	private String manufacturer;
	@Compare("所属部门id")
	private Long deptId;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	/**
	 * 错误信息
	 */
	@TableField(exist = false)
	private String msg;

	private String createAccount;

}

