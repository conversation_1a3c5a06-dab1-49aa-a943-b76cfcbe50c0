package com.xh.vdm.biapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.util.Date;
import java.io.Serializable;

/**
 * (BdmMiningTruck)实体类
 */
@Data
public class BdmMiningTruck implements Serializable {
	private static final long serialVersionUID = 1L;
	@TableId(type = IdType.AUTO)
	private Long id;
	@Compare("车辆编号")
	private String number;
	@Compare("车辆类型")
	private Integer category;

	private Integer targetType;
	@Compare("所属机构")
	private Long deptId;
	@Compare("车架号")
	private String vin;
	@Compare("最大马力")
	private Float maxPower;
	@Compare("制造商")
	private String manufacturer;
	@Compare("额定载重")
	private Float ratedLoad;
	@Compare("车辆型号")
	private String model;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	@TableField(exist = false)
	private String deptName;
	@TableField(exist = false)
	private String  terminalCategories;
	@TableField(exist = false)
	private String uniqueId;

	private String createAccount;
}

