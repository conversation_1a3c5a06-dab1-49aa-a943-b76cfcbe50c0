/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.biapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
@TableName("bdm_rdss_device")
@ApiModel(value = "RdssDevice对象", description = "RdssDevice对象")
public class BdmRdssDevice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，唯一标识码
     */
    @Column(name="id")
    @ApiModelProperty(value = "主键，唯一标识码")
	@TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 设备id,也叫终端号
     */
    @Column(name="unique_id")
    @ApiModelProperty(value = "设备id,也叫终端号")
	@Compare("序列号")
    private String uniqueId;
    /**
     * 国际移动设备识别码
     */
    @Column(name="imei")
    @ApiModelProperty(value = "国际移动设备识别码")
	@Compare("IMEI")
    private String imei;
    /**
     * 终端型号
     */
    @Column(name="model")
    @ApiModelProperty(value = "终端型号")
	@Compare("终端型号")
    private String model;
    /**
     * 生产厂商名称
     */
    @Column(name="vendor")
    @ApiModelProperty(value = "生产厂商名称")
	@Compare("厂商名称")
    private String vendor;
    /**
     * 北斗芯片序列号
     */
    @Column(name="bd_chip_sn")
    @ApiModelProperty(value = "北斗芯片序列号")
	@Compare("北斗芯片序列号")
    private String bdChipSn;
    /**
     * 终端类别，1-北斗定位终端，2-北斗穿戴式终端，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端
     */
    @Column(name="device_type")
    @ApiModelProperty(value = "终端类别，1-北斗定位终端，2-北斗穿戴式终端，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端")
    private Integer deviceType;
    /**
     * 特殊性，1-旧设备，2-新设备，3-特殊设备
     */
    @Column(name="specificity")
    @ApiModelProperty(value = "特殊性，1-旧设备，2-新设备，3-特殊设备")
	@Compare("设备")
    private Integer specificity;
    /**
     * 所属部门id
     */
    @Column(name="dept_id")
    @ApiModelProperty(value = "所属部门id")
	@Compare("所属机构")
    private Long deptId;
    /**
     * 监控对象id
     */
    @Column(name="target_id")
    @ApiModelProperty(value = "监控对象id")
    private Long targetId;
    /**
     * 监控对象类型
     */
    @Column(name="target_type")
    @ApiModelProperty(value = "监控对象类型")
    private Integer targetType;
    /**
     * 监控对象名称
     */
    @Column(name="target_name")
    @ApiModelProperty(value = "监控对象名称")
    private String targetName;
    /**
     * 激活状态，0-未激活，1-已激活
     */
    @Column(name="activated")
    @ApiModelProperty(value = "激活状态，0-未激活，1-已激活")
    private Integer activated;
    /**
     * 终端种类/功能类型，1-手表/环，2-安全帽，...
     */
    @Column(name="category")
    @ApiModelProperty(value = "终端种类/功能类型，1-手表/环，2-安全帽，...")
	@Compare("终端类型")
    private Integer category;
    /**
     * 赋码值，16位字符串
     */
    @Column(name="device_num")
    @ApiModelProperty(value = "赋码值，16位字符串")
	@Compare("赋码值")
    private String deviceNum;
    /**
     * 北斗卡号码，7位
     */
    @Column(name="bd_card")
    @ApiModelProperty(value = "北斗卡号码，7位")
	@Compare("北斗卡号")
    private String bdCard;
    /**
     * 北斗卡等级，1-一级，2-二级，3-三级，4-四级，5-五级
     */
    @Column(name="bd_card_level")
    @ApiModelProperty(value = "北斗卡等级，1-一级，2-二级，3-三级，4-四级，5-五级")
	@Compare("北斗卡等级")
    private Integer bdCardLevel;
    /**
     * 安装日期
     */
    @Column(name="installdate")
    @ApiModelProperty(value = "安装日期")
	@Compare("安装日期")
    private Date installdate;
    /**
     * 创建时间
     */
    @Column(name="create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改时间
     */
    @Column(name="update_time")
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 0-未删除，1-已删除
     */
    @Column(name="deleted")
    @ApiModelProperty(value = "0-未删除，1-已删除")
    private Integer deleted;
    /**
     * 主从模式，0-从机，1-主机
     */
    @Column(name="master")
    @ApiModelProperty(value = "主从模式，0-从机，1-主机")
    private Integer master;
    /**
     * 应用场景，详见 blade_dict_biz
表中相关字典定义
     */
    @Column(name="scenario")
    @ApiModelProperty(value = "应用场景，详见 blade_dict_biz 表中相关字典定义")
	@Compare("应用场景")
    private Integer scenario;
    /**
     * 应用方向/领域，详见
blade_dict_biz 表中相关字典定
义
     */
    @Column(name="domain")
    @ApiModelProperty(value = "应用方向/领域，详见 blade_dict_biz 表中相关字典定义")
	@Compare("应用方向")
    private Integer domain;
    /**
     * 定位模式，详见blade_dict_biz	表中相关字典定义
     */
    @Column(name="gnss_mode")
    @ApiModelProperty(value = "定位模式，详见blade_dict_biz	表中相关字典定义")
	@Compare("定位模式")
    private Integer gnssMode;

    @Column(name="iot_protocol")
	private Integer iotProtocol;

    @Column(name="terminal_id")
	@Compare("终端编号")
	private String terminalId;

	@TableField(exist = false)
	@Compare("物联网卡ID")
	private String numbers;

	private String createAccount;
	@Column(name="asset_type")
	@Compare("资产类型，1-固定资产，2-非固定资产")
	private Short assetType;

	@Column(name="own_dept_type")
	@Compare("归属单位类型，1-内部单位，2-外部单位")
	private Short ownDeptType;

	@Column(name="own_dept_name")
	@Compare("归属单位名称，当归属单位类型为2时有效，即外部单位名称")
	private String ownDeptName;
}
