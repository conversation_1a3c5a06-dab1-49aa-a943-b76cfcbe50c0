package com.xh.vdm.biapi.entity;

import org.springblade.common.utils.compare.Compare;
import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * @Description: 人员表
 */
@Data
public class BdmWorker implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	@Compare("人员姓名")
	private String name;

	private Integer targetType;
	@Compare("所属机构")
	private Long deptId;
	@Compare("从业类型")
	private Integer post;
	@Compare("岗位类型")
	private Integer industry;
	@Compare("手机号码")
	private String phone;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;
	@Compare("工号")
	private String wkno;

	private String createAccount;
}

