package com.xh.vdm.biapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 抽象终端表
 */
@Data
public class BdmAbstractDevice implements Serializable {
	private static final long serialVersionUID = 1L;

	//主键，唯一标识码
	@TableId(type = IdType.ASSIGN_ID) // 使用雪花算法生成ID
	private Long id;

	//设备id,也叫终端号
	private String uniqueId;

	//终端类别, 1-北斗定位终端，2-北斗穿戴式终端，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端
	private Integer deviceType;

	//所属部门id
	private Long deptId;

	//终端种类/功能类型
	private Integer category;

	//赋码值，16位字符串
	private String deviceNum;

	//监控对象id
	private Long targetId;

	//监控对象类型
	private Integer targetType;

	//0-未删除，1-已删除
	private Integer deleted;

	//物联网协议，1-JT/T808，2-MQTT，3-SNMP，...
	private Integer iotProtocol;

	//创建人，对应国能登录账号（也是工号）
	private String createAccount;

	//特殊性，0-未知，1-旧设备，2-新设备，3-特殊设备
	private Integer specificity;

	//视频通道个数
	private Integer channelNum;

	//终端型号
	private String model;

	//IMEI  定位设备必填，其他类型非必填
	private String imei;

	/** 大类，应用方向，取分类表中的code */
	private String classCode;

	/** 小类，取分类表中的code */
	private String subClassCode;

	/** 安装位置经度 */
	private Double longitude;

	/** 安装位置纬度 */
	private Double latitude;

	/** 安装位置描述 */
	private String deviceAddr;

	/** 入网方式 */
	private Integer inNetType;

	/** 入网服务商 */
	private String inNetProvider;

	/** 北斗卡号 */
	private String bdCardNumber;

	/** 联系人 */
	private String contact;

	/** 联系方式 */
	private String contactPhone;

	/** 物联网卡号 */
	private String iotNumber;

	/** 终端编号 */
	private String deviceNo;

	/** 0:新终端 1:存量终端 */
	private Integer deviceSource;

	//其他终端类型名称
	private String otherTypes;

	//厂商名称
	private String manufacturerName;
}

