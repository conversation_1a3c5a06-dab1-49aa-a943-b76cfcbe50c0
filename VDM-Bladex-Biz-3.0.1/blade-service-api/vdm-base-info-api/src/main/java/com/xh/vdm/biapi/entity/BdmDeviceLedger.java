package com.xh.vdm.biapi.entity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springblade.common.utils.compare.Compare;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (BdmDeviceLedger)实体类
 */
@Data
@ExcelIgnoreUnannotated
public class BdmDeviceLedger implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	@Compare("序列号")
	private String uniqueId;
	@Compare("IMEI号")
	private String imei;
	@Compare("终端型号")
	private String model;
	@Compare("生产厂商")
	private String vendor;
	@Compare("北斗芯片序列号")
	private String bdChipSn;
	@Compare("终端类别")
	private Integer deviceType;
	@Compare("终端类型")
	private Integer category;

	@ExcelProperty(value = "终端赋码号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	@Compare("终端赋码号")
	private String deviceNum;

	private Integer storageState;

	@JsonFormat(pattern = "yyyy-MM-dd")
	@Compare("采购时间")
	private Date buytime;

	private Date entryTime;

	private Date deliveryTime;

	private Long userDeptId;

	private String type;

	private Long inputer;

	private Long outputer;


	/**
	 * 导入错误信息
	 */
	@TableField(exist = false)
	@ExcelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
    private String errorMsg;

	@TableField(exist = false)
	private String deptName;
	@TableField(exist = false)
	private Integer backStatus;
	@TableField(exist = false)
	private String backStatusName;
	@TableField(exist = false)
	private String inputerName;
	@TableField(exist = false)
	private String outputerName;
	@TableField(exist = false)
	private String categoryName;
	@TableField(exist = false)
	private String vendorName;

}

