package com.xh.vdm.biapi.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 集装箱实体
 */
@Data
public class BdmContainer implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	@Compare("集装箱编号")
	private String number;
	@Compare("箱型")
	private Integer model;
	@Compare("尺寸，单位英尺")
	private Integer size;
	@Compare("所属部门id")
	private Long deptId;
	@Compare("总重，单位kg")
	private Float maxGross;
	@Compare("自重/皮重，单位kg")
	private Float tare;
	@Compare("载重/净重，单位kg")
	private Float net;
	@Compare("最大装货容积，单位m³")
	private Float cuCap;
	@Compare("长度，单位mm")
	private Integer length;
	@Compare("高度，单位mm")
	private Integer height;
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	private Integer targetType;

	private String createAccount;
}

