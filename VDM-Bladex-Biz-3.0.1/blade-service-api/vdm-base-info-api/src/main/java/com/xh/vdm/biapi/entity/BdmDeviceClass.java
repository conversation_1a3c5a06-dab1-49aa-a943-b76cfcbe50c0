package com.xh.vdm.biapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;
/**
 * 设备分类实体类
 * 对应数据库表：bdm_device_class
 * 用于存储设备分类信息，支持树形结构
 */

@Data
@Table(name = "bdm_device_class")
public class BdmDeviceClass {

	/**
	 * 分类ID（主键）
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 分类编码（唯一标识）
	 */
	private String code;

	/**
	 * 父分类编码
	 * 关联到本类的code字段，形成树形结构
	 */
	private String parentCode;

	/**
	 * 分类描述
	 */
	private String className;

	/**
	 * 创建人员
	 */
	private String createAccount;

	/**
	 * 创建时间
	 */
	private Date createTime;

	//数据来源：0 系统配置 1 用户输入。所有用户输入的类型均当作“其他”来对待
	private Integer source;

	/**
	 * 删除标记：
	 * 0 - 未删除
	 * 1 - 已删除
	 */
	private Integer deleted;
}
