package com.xh.vdm.biapi.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 访客人员表
 */
@Data
public class BdmVisitor implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private String name;

	private String idNumber;

	private Integer targetType;

	private Long deptId;


	private Integer post;

	private Integer industry;

	private String phone;

	private String company;

	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date validFrom;
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date validTo;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	private String createAccount;
}

