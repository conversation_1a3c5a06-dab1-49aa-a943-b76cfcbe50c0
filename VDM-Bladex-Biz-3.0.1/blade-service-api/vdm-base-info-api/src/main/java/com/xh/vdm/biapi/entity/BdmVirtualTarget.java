package com.xh.vdm.biapi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (BdmVirtualTarget)实体类
 */
@Data
public class BdmVirtualTarget implements Serializable {
	private static final long serialVersionUID = 1;

	@TableId(type = IdType.ASSIGN_ID)
	private Long id;

	private String number;

	private Integer category;

	private Integer targetType;

	private Long deptId;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	private String deviceCode;

	@TableField(exist = false)
	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	@TableField(exist = false)
	private String deviceNum;

	@TableField(exist = false)
	private Long deviceId;
	/**
	 * 终端类别
	 */
	@TableField(exist = false)
	private Integer deviceType;
	@TableField(exist = false)
	private String categoryName;
	@TableField(exist = false)
	private String deptName;
	private String createAccount;

}

