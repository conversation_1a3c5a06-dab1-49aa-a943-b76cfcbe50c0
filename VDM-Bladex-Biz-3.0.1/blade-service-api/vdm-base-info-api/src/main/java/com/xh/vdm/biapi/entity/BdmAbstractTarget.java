package com.xh.vdm.biapi.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 抽象监控对象实体表
 */
@Data
public class BdmAbstractTarget implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private String number;

	private String name;

	private Integer targetType;

	private Long deptId;

	private Integer category;

	private Integer deleted;

	private String createAccount;
}

