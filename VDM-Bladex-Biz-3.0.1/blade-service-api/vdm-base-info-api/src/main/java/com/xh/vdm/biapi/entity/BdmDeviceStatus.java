package com.xh.vdm.biapi.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (BdmDeviceLink)实体类
 */
@Data
public class BdmDeviceStatus implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private Long deviceId;

	private Integer deviceType;

	private String uniqueId;

	private String deviceNum;

	private Long targetId;

	private Integer targetType;

	private String targetName;

	private Integer action;

	private Date actionTime;

	private Integer faultCount;

}

