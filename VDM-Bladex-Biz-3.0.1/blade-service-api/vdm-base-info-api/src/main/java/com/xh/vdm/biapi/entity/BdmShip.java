package com.xh.vdm.biapi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.util.Date;
import java.io.Serializable;

/**
 * @Description: 船舶表
 */
@Data
public class BdmShip implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	@Compare("船舶编号")
	private String number;

	private Integer targetType;
	@Compare("船舶类型")
	private Integer category;
	@Compare("船舶名称")
	private String name;
	@Compare("船舶英文名称")
	private String nameEn;
	@Compare("水上移动业务识别码")
	private String mmsi;
	@Compare("国际海事船舶识别码")
	private String imoNumber;
	@Compare("船舶呼号")
	private String callSign;
	@Compare("所属部门id")
	private Long deptId;
	@Compare("总重")
	private  Float maxGross;
	@Compare("载重/净重")
	private Float net;
	@Compare("排水量")
	private Float displcement;
	@Compare("型长")
	private Integer length;
	@Compare("型宽")
	private Integer breadth;
	@Compare("型深")
	private Integer depth;
	@Compare("吃水")
	private Integer draught;
	@Compare("航速")
	private Float cruiseSpeed;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	private Date constructionTime;

	/**
	 * 错误信息
	 */
	@TableField(exist = false)
	private String msg;

	private String createAccount;
}

