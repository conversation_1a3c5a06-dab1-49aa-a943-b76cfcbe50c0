package com.xh.vdm.interManager.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BdmContainer implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private String number;
	private Integer model;
	private Integer size;
	private Long deptId;
	private Float maxGross;
	private Float tare;
	private Float net;
	private Integer cuCap;
	private Integer length;
	private Integer height;
	private Date createTime;
	private Date updateTime;
	private Integer deleted;
	private Integer targetType;
}

