package com.xh.vdm.interManager.vo;

import lombok.Data;

import java.util.List;

/**
 * @Description: 根据模板创建接口参数
 * @Auther: zxing
 * @Date: 2020/2/13 10:34
 * @company：CTTIC
 */
@Data
public class InterfaceParamWithModel {

	private String type;
	private Long interfaceManageId;
	private String hasToken;
	private String tokenStation;
	private String tokenKey;
	private String tokenPrefix;
	private String hasHeaderParam;
	private List<HeaderParamModel> paramsInHeader;
	private String hasBodyParam;
	private List<BodyParamModel> paramsInBody;
	private String hasUrlParam;
	private List<UrlParamModel> paramsInUrl;
	private String dataStation;
	private String dataKey;
	private String hasAuth;
	private String username;
	private String password;
	private String isJson;
	private String publickKeyKey;
	private String publicKeyStation;
	private String accessTokenKey;
	private String accessTokenDuration;
}
