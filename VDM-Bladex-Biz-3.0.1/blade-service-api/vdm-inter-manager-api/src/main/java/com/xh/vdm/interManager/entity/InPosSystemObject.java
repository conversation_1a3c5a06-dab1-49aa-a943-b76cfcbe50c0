package com.xh.vdm.interManager.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 平台--业务对象映射表。多对多的关系。	业务对象包含终端、运输车辆、职工、生产厂区、铁路货车车厢、矿用卡车、精密装备、货船。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class InPosSystemObject implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 平台id
     */
    private Long systemId;

    /**
     * 业务对象id
     */
    private Long objectId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 业务对象类型：1 终端、2 运输车辆、3 职工、4 生产厂区、5 铁路货车车厢、6 矿用卡车、7 精密装备、8 货船
     */
    private Integer objectType;


}
