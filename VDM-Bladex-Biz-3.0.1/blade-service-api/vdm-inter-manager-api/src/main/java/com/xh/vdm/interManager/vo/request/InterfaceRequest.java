package com.xh.vdm.interManager.vo.request;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InterfaceRequest {
	/**
	 * 接口编码
	 */
	@ApiModelProperty(value = "接口编码")
	private String code;
	/**
	 * 接口名称
	 */
	@ApiModelProperty(value = "接口名称")
	private String name;
	/**
	 * 归属业务服务
	 */
	@ApiModelProperty(value = "归属业务服务")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long serviceId;

	//平台账号id
	@JsonSerialize(using = ToStringSerializer.class)
	private Long userId;
}
