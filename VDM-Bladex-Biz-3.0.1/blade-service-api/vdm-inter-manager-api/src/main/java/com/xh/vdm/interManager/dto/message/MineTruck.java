package com.xh.vdm.interManager.dto.message;

import lombok.Data;

@Data
public class MineTruck {

	// 车辆编号
	private String vehicleNo;

	// 车辆识别号
	private String vin;

	// 单位ID
	private String ownerId;

	// 车辆类型（类型值与名称的映射，详见协议附录A.13）
	private String vehicleType;

	// 制造商
	private String manufacturer;

	// 车辆型号
	private String model;

	// 最大马力（单位：kw）
	private String maxPower;

	// 额定载重（单位：t）
	private String ratedLoad;

	// 单位名称
	private String ownerName;

	// 操作类型（A：新增，U：更新，D：删除）
	private String operateState;

	// 操作时间（格式：yyyyMMddHHmmss）
	private String operateTime;

}
