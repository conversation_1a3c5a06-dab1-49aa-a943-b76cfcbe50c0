package com.xh.vdm.interManager.entity;

import lombok.Data;

/**
 * mqtt推送日志
 */
@Data
public class MqttPushLog {

	//主键id，没有业务意义。建表规则要求
	private Long id;
	//mqtt主题，如 send/RA0003/1859521080621895681
	private String topic;
	//往emqx上发送的时间
	private Long sendTime;
	//业务编码，如RA0003
	private String busiCode;
	//接收推送数据的系统的id，如1859521080621895681
	private Long systemId;
	//推送的报文内容
	private String content;
	//日志入库时间
	private Long createTime;
	//推送结果：1 成功  0 失败
	private Integer res;
	//推送结果描述
	private String resMessage;
}
