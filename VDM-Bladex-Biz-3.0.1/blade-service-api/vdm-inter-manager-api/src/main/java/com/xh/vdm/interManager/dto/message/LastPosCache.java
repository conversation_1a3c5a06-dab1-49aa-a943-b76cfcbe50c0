package com.xh.vdm.interManager.dto.message;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

/**
 * 最终位置点--redis缓存
 */
@Data
public class LastPosCache {

	@JsonProperty("device_id")
	private Long deviceId;
	@JsonProperty("device_type")
	private Integer deviceType;
	@JsonProperty("device_model")
	private String deviceModel;
	@JsonProperty("device_no")
	private String deviceNo;
	@JsonProperty("device_unique_id")
	private String deviceUniqueId;
	@JsonProperty(value = "target_id", required = true)
	private Long targetId;
	@JsonProperty("target_type")
	private Integer targetType;
	@JsonProperty("target_name")
	private String targetName;
	@JsonProperty("device_num")
	private String deviceNum;
	@JsonProperty("dept_id")
	private Long deptId;
	@JsonProperty("longitude")
	private Double longitude;
	@JsonProperty("latitude")
	private Double latitude;
	@JsonProperty("altitude")
	private Integer altitude;
	@JsonProperty("speed")
	private Double speed;
	@JsonProperty("bearing")
	private Integer bearing;
	@JsonProperty("alarm_flag")
	private Long alarmFlag;
	@JsonProperty("state_flag")
	private Long stateFlag;
	@JsonProperty("loc_time")
	private Long locTime;
	@JsonProperty("recv_time")
	private Long recvTime;
	@JsonProperty("valid")
	private Integer valid;
	@JsonProperty("mileage")
	private Double mileage;
	@JsonProperty("gnss_num")
	private Integer gnssNum;
	@JsonProperty("wireless")
	private Integer wireless;
	@JsonProperty("real_speed")
	private Double realSpeed;
	@JsonProperty("expand_signal")
	private Long expandSignal;
	@JsonProperty("io_status")
	private Long ioStatus;
	@JsonProperty("temperature")
	private String temperature;
	@JsonProperty("batch")
	private Long batch;
	@JsonProperty("aux_str")
	private String auxStr;
	@JsonProperty("auxs_normal")
	private Map<Integer, Object> auxsNormal;
	@JsonProperty("dsm_unique_id")
	private String dsmUniqueId;
	@JsonProperty("te_state")
	private Long teState;
	@JsonProperty("loc_time_f")
	private String locTimeF;
	@JsonProperty("recv_time_f")
	private String recvTimeF;
	@JsonProperty("off_line_time")
	private String offLineTime;
}
