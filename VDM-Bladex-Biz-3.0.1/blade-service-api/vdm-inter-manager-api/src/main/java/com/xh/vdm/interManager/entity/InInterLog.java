/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Data
@ApiModel(value = "InInterLog对象", description = "InInterLog对象")
public class InInterLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
	@JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 接口方向：1 访问位置平台   2 访问外部平台
     */
    @ApiModelProperty(value = "接口方向：1 访问位置平台   2 访问外部平台")
    private Integer direction;
    /**
     * 接口编码
     */
    @ApiModelProperty(value = "接口编码")
    private String interCode;
    /**
     * 接口名称
     */
    @ApiModelProperty(value = "接口名称")
    private String interName;

	@JsonSerialize(using = ToStringSerializer.class)
  	private Long systemId;
    /**
     * 0 外部服务；   其他数值，位置平台服务 service_id
     */
    @ApiModelProperty(value = "0 外部服务；   其他数值，位置平台服务 service_id")
	@JsonSerialize(using = ToStringSerializer.class)
    private Long serviceId;

	//业务报文中的服务编码
	private String serviceCode;

    /**
     * 接口类型：1 HTTP   2 WebService  3 MQTT
     */
    @ApiModelProperty(value = "接口类型：1 HTTP   2 WebService  3 MQTT")
    private String interType;
    /**
     * 接口url
     */
    @ApiModelProperty(value = "接口url")
    private String url;
    /**
     * 接口参数
     */
    @ApiModelProperty(value = "接口参数")
    private String param;
    /**
     * 调用结果 1 成功   0 失败
     */
    @ApiModelProperty(value = "调用结果 1 成功   0 失败")
    private Integer result;
    /**
     * 接口返回报文
     */
    @ApiModelProperty(value = "接口返回报文")
    private String resMessage;
    /**
     * 调用时间
     */
    @ApiModelProperty(value = "调用时间")
    private Date callTime;
    /**
     * 调用耗时
     */
    @ApiModelProperty(value = "调用耗时")
    private Long callCost;
    /**
     * 数据创建时间
     */
    @ApiModelProperty(value = "数据创建时间")
    private Date createTime;

	//调用账户
	private String callAccount;

	//请求ip
	private String serverIp;

	//业务id，对应平台协议中报文类型
	private String busiServiceId;

}
