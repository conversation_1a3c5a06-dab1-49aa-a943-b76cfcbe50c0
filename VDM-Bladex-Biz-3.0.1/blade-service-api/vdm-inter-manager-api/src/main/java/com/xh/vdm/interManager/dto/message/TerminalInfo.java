package com.xh.vdm.interManager.dto.message;

import lombok.Data;

/**
 * 终端信息报文
 */
@Data
public class TerminalInfo {

	//终端唯一标识码
	private String deviceID;
	//终端类别
	private Integer deviceCategory;
	//终端类型
	private Integer deviceType;
	//终端IMEI号
	private String imei;
	//终端型号
	private String deviceModel;
	//厂商编号
	private String vendorId;
	//赋码值
	private String tagcode;
	//赋码签名
	private String signature;
	//所属单位ID
	private String ownerId;
	//所属企业名称
	private String ownerName;
	//定位模式
	private Integer locateMode;
	//应用场景
	private Integer scenario;
	//应用方向/领域
	private Integer domain;
	//操作状态
	private String operateState;
	//操作时间(YYYYMMDDhhmiss)
	private String operateTime;
}
