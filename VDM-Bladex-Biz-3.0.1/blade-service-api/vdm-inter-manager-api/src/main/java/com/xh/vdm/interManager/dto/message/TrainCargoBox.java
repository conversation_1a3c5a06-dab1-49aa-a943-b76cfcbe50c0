package com.xh.vdm.interManager.dto.message;

import lombok.Data;

@Data
public class TrainCargoBox {

	// 车厢编号
	private String coachNo;

	// 车厢类型（类型值与名称的映射，详见附录A.12）
	private String coachType;

	// 车厢重量
	private String coachWeight;

	// 车厢容积
	private String coachVolume;

	// 单位ID
	private String ownerId;

	// 单位名称
	private String ownerName;

	// 操作类型（A：新增，U：更新，D：删除）
	private String operateState;

	// 操作时间（格式：yyyyMMddHHmmss）
	private String operateTime;
}
