package com.xh.vdm.interManager.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 简单token对象
 * @Author: zhouxw
 * @Date: 2023/5/31 17:12
 */
@Data
public class SimpleToken {


	private Integer code;

	private Boolean success;

	@JsonProperty("access_token")
	private String accessToken;
	@JsonProperty("refresh_token")
	private String refreshToken;

	@JsonProperty("token_type")
	private String tokenType;

	@JsonProperty("expires_in")
	private Long expiresIn;

}
