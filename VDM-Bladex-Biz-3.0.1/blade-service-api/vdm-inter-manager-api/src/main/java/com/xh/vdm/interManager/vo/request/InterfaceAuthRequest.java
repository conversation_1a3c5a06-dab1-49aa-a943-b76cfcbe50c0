package com.xh.vdm.interManager.vo.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InterfaceAuthRequest {
	/**
	 * 接口编码
	 */
	@ApiModelProperty(value = "接口编码")
	private String code;
	/**
	 * 接口名称
	 */
	@ApiModelProperty(value = "接口名称")
	private String name;
	/**
	 * 归属业务服务
	 */
	@ApiModelProperty(value = "归属业务服务")
	private Long serviceId;

	//用户id
	private Long userId;
}
