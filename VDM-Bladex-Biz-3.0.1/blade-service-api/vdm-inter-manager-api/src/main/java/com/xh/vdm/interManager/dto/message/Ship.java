package com.xh.vdm.interManager.dto.message;

import lombok.Data;

@Data
public class Ship {

	// 货船编号
	private String vesselNo;

	// 中文船名
	private String vesselNameCn;

	// 英文船名
	private String vesselNameEn;

	// 海上移动通信业务标识（MMSI）
	private String vesselMmsi;

	// 国际海事组织编号（IMO）
	private String vesselImo;

	// 呼号
	private String vesselCallSign;

	// 船舶类型（类型值与名称的映射，详见附录A.14）
	private String vesselType;

	// 船长（单位：dm）
	private String vesselLength;

	// 船宽（单位：dm）
	private String vesselWidth;

	// 总吨位（单位：t）
	private String vesselGt;

	// 载重（单位：t）
	private String vesselDwt;

	// 净吨位（单位：t）
	private String vesselNt;

	// 单位ID
	private String ownerId;

	// 单位名称
	private String ownerName;

	// 建造日期
	private String vesselConstructionTime;

	// 操作类型（A：新增，U：更新，D：删除）
	private String operateState;

	// 操作时间（格式：yyyyMMddHHmmss）
	private String operateTime;
}
