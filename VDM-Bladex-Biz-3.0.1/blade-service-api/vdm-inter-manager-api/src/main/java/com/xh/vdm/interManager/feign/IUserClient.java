/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.feign;

import com.xh.vdm.interManager.entity.InPosInterface;
import com.xh.vdm.interManager.entity.InPosUserInterface;
import com.xh.vdm.interManager.util.R;
import org.springblade.common.constant.ApplicationConstant;
import org.springblade.common.constant.CommonConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 轨迹操作 Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = ApplicationConstant.APPLICATION_INTER_MANAGER
)
public interface IUserClient {

	//请求前缀
	String API_PREFIX = CommonConstant.FEIGN_PREFIX;

	//查询用户接口权限
	String GET_USER_INTERFACE_AUTH = API_PREFIX + "/getUserInterfaceAuth";


	/**
	 * @description: 获取用户接口权限
	 * @author: zhouxw
	 * @date: 2023-05-146 15:13:13
	 * @param: [licencePlate 车牌号; licenceColor 车牌颜色; startTime 开始时间; endTime 结束时间; isFilter 是否过滤无效点 0 表示不过滤，1表示过滤]
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.LocationKudu>>
	 **/
	@GetMapping(GET_USER_INTERFACE_AUTH)
	R<List<InPosInterface>> getUserInterfaceAuth (
		@RequestParam(value = "userId") Long userId
	);


}
