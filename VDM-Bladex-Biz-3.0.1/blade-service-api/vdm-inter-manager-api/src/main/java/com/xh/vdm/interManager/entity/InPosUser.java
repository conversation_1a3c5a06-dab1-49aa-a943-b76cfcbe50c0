/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
@Data
@ApiModel(value = "InPosUser对象", description = "InPosUser对象")
public class InPosUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
	@JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
	@NotNull(message = "用户名不能为空")
	@Compare("账号")
    private String account;
    /**
     * 密码
     */
    @ApiModelProperty(value = "密码")
	@NotNull(message = "密码不能为空")
    private String password;
    /**
     * 昵称
     */
    @ApiModelProperty(value = "联系人")
	@Compare("联系人")
    private String contactName;
    /**
     * 账户用途描述
     */
    @ApiModelProperty(value = "账户用途描述")
	@Compare("账户用途描述")
    private String userDesc;
    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
	@Compare("联系邮箱")
    private String contactEmail;
    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
	@Compare("联系电话")
    private String contactPhone;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createUser;
    /**
     * 创建部门
     */
    @ApiModelProperty(value = "创建部门")
    private Long createDept;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private Long updateUser;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
	@Compare("账号状态")
    private String status;
    /**
     * 是否已删除
     */
    @ApiModelProperty(value = "是否已删除")
    private Integer isDel;
    /**
     * 告警规则id，多个id之间使用逗号分隔
     */
    @ApiModelProperty(value = "告警规则id，多个id之间使用逗号分隔")
    private String alarmRuleId;
    /**
     * 平台id
     */
    @ApiModelProperty(value = "平台id")
	@JsonSerialize(using = ToStringSerializer.class)
	@Compare("所属平台")
    private Long systemId;
    /**
     * 监管部门，用于数据权限，多个id之间使用逗号分隔
     */
    @ApiModelProperty(value = "监管部门，用于数据权限，多个id之间使用逗号分隔")
	@Compare("所属机构")
    private String deptId;


}
