package com.xh.vdm.interManager.dto.message;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * @Description: 请求报文体
 * @Auther: zhouxw
 * @Date: 2019/10/28 11:10
 * @company：CTTIC
 */
@Data
public class Body implements Serializable {

	@NotEmpty(message = "内容加密串为空。")
	@Pattern(regexp = "\\w+", message = "内容加密串不正确。")
	private String encryptedContent;

}
