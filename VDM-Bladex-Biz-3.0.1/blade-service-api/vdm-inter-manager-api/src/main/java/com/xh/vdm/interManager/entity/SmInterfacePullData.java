package com.xh.vdm.interManager.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("in_interface_pull_data")
public class SmInterfacePullData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@JsonSerialize(using = ToStringSerializer.class)
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 接口编码
     */
    private String code;

    /**
     * 接口返回数据
     */
    private String data;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


}
