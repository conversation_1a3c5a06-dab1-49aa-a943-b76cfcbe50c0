/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;
import java.util.Date;

/**
 * 接口管理表实体类
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@Data
@TableName("in_interface_manage")
public class SmInterfaceManage implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 接口id
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;
	/**
	 * 接口编号
	 */
	@Compare("接口编码")
	private String interCode;
	/**
	 * 接口类型
	 */
	@Compare("接口类型")
	private String interType;
	/**
	 * 请求方式
	 */
	@Compare("请求方式")
	private String requestType;
	/**
	 * 备注
	 */
	private String interNote;
	/**
	 * 协议
	 */
	@Compare("协议")
	private String protocol;
	/**
	 * ip
	 */
	@Compare("ip")
	private String ip;
	/**
	 * 端口
	 */
	@Compare("端口")
	private String port;


	/**
	 * 系统分类
	 */
	@Compare("所属平台")
	private String systemType;
	/**
	 * 接口功能
	 */
	@Compare("接口功能")
	private String functionType;

	/**
	 * 请求地址
	 */
	@Compare("请求url")
	private String url;
	/**
	 * 已有token接口code配置
	 */
	@Compare("token编码")
	private String tokenInterCode;
	/**
	 * 创建时间
	 */
	private Date createDate;
	/**
	 * 修改时间
	 */
	private Date updateDate;
	/**
	 * 接口状态
	 */
	private String state;
	/**
	 * 发送请求是否需要token
	 */
	@Compare("是否需要token")
	private String isNeedToken;


}
