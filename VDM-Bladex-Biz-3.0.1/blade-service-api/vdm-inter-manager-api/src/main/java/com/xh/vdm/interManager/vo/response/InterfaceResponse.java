package com.xh.vdm.interManager.vo.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InterfaceResponse {
	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;
	/**
	 * 接口编码
	 */
	@ApiModelProperty(value = "接口编码")
	private String code;
	/**
	 * 接口名称
	 */
	@ApiModelProperty(value = "接口名称")
	private String name;
	/**
	 * 接口描述
	 */
	@ApiModelProperty(value = "接口描述")
	private String interfaceDesc;
	/**
	 * 接口url
	 */
	@ApiModelProperty(value = "接口url")
	private String url;
	/**
	 * 归属业务服务
	 */
	@ApiModelProperty(value = "归属业务服务")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long serviceId;


	//业务服务名称
	private String serviceName;


	/**
	 * 接口协议类型：H http    W webservice  M mqtt
	 */
	@ApiModelProperty(value = "接口协议类型：H http    W webservice  M mqtt")
	private String protocolType;
	/**
	 * 接口请求方式：1 GET   2 POST
	 */
	@ApiModelProperty(value = "接口请求方式：1 GET   2 POST")
	private String requestType;
}
