package com.xh.vdm.interManager.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
@Data
@ApiModel(value = "InPosInterface对象", description = "InPosInterface对象")
public class InPosInterface implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
	@JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 接口编码
     */
    @ApiModelProperty(value = "接口编码")
	@Compare("接口编码")
    private String code;
    /**
     * 接口名称
     */
    @ApiModelProperty(value = "接口名称")
	@Compare("接口名称")
    private String name;
    /**
     * 接口描述
     */
    @ApiModelProperty(value = "接口描述")
	@Compare("接口功能描述")
    private String interfaceDesc;
    /**
     * 接口url
     */
    @ApiModelProperty(value = "接口url")
	@Compare("接口url")
    private String url;
    /**
     * 归属业务服务
     */
    @ApiModelProperty(value = "归属业务服务")
	@JsonSerialize(using = ToStringSerializer.class)
	@Compare("归属业务服务")
    private Long serviceId;
    /**
     * 接口协议类型：H http    W webservice  M mqtt
     */
    @ApiModelProperty(value = "接口协议类型：H http    W webservice  M mqtt")
    private String protocolType;
    /**
     * 接口请求方式：1 GET   2 POST
     */
    @ApiModelProperty(value = "接口请求方式：1 GET   2 POST")
    private String requestType;
    /**
     * 是否删除：0 未删除    1 删除
     */
    @ApiModelProperty(value = "是否删除：0 未删除    1 删除")
    private Integer isDel;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createUser;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private Long updateUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
