package com.xh.vdm.interManager.vo.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class UserResponse {

	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;
	/**
	 * 账号
	 */
	@ApiModelProperty(value = "账号")
	@NotNull(message = "用户名不能为空")
	private String account;
	/**
	 * 昵称
	 */
	@ApiModelProperty(value = "联系人")
	private String contactName;
	/**
	 * 账户用途描述
	 */
	@ApiModelProperty(value = "账户用途描述")
	private String userDesc;
	/**
	 * 邮箱
	 */
	@ApiModelProperty(value = "邮箱")
	private String contactEmail;
	/**
	 * 手机
	 */
	@ApiModelProperty(value = "手机")
	private String contactPhone;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String status;
	/**
	 * 告警规则id，多个id之间使用逗号分隔
	 */
	@ApiModelProperty(value = "告警规则id，多个id之间使用逗号分隔")
	private String alarmRuleId;
	/**
	 * 平台id
	 */
	@ApiModelProperty(value = "平台id")
	private Long systemId;
	/**
	 * 监管部门，用于数据权限，多个id之间使用逗号分隔
	 */
	@ApiModelProperty(value = "监管部门，用于数据权限，多个id之间使用逗号分隔")
	private String deptId;



}
