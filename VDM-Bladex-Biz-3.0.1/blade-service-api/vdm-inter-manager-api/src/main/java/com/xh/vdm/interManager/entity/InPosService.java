/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
@Data
@ApiModel(value = "InPosService对象", description = "InPosService对象")
public class InPosService implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
	@JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 服务名称
     */
    @ApiModelProperty(value = "服务名称")
	@Compare("服务名称")
    private String name;
    /**
     * 服务编号
     */
    @ApiModelProperty(value = "服务编号")
	@Compare("服务编号")
    private String code;
    /**
     * 服务描述
     */
    @ApiModelProperty(value = "服务描述")
	@Compare("服务功能描述")
    private String serviceDesc;
    /**
     * 是否删除 0 未删除  1 删除
     */
    @ApiModelProperty(value = "是否删除 0 未删除  1 删除")
    private Integer isDel;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private Long createUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private Long updateUser;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
