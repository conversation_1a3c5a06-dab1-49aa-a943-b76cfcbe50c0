package com.xh.vdm.interManager.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: token请求参数
 * @Author: zhouxw
 * @Date: 2023/5/31 17:35
 */
@Data
public class TokenRequest {

	@JsonProperty("tenant_id")
	private String tenantId;
	@JsonProperty("username")
	private String username;
	@JsonProperty("password")
	private String password;
	@JsonProperty("grant_type")
	private String grantType;
}
