package com.xh.vdm.interManager.dto.message;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

@Data
public class Header {

	//流水号：6位系统编码+时间+12位序列号，时间格式为YYYYMMDDhhmmss
	@NotEmpty(message = "流水号为空。")
	@Pattern(regexp = "\\w{32}", message = "流水号不正确。")
	private String serialNum;

	//发起方系统编码
	@NotEmpty(message = "发起方系统编码为空。")
	@Pattern(regexp = "\\w{6}", message = "发起方系统编码不正确。")
	private String systemId;

	//业务接口代码
	@NotEmpty(message = "业务接口代码为空。")
	@Pattern(regexp = "\\w{6}", message = "业务接口代码不正确。")
	private String serviceId;

	//请求时间
	@NotEmpty(message = "请求时间为空。")
	@Pattern(regexp = "\\w{14}", message = "请求时间不正确。")
	private String reqTime;

	//校验签名:采用SM3算法，对数据结构体Body进行校验并采用SM2私钥进行签名
	@NotEmpty(message = "校验签名为空。")
	@Pattern(regexp = "\\w{1,512}", message = "校验签名不正确。")
	private String crc;

	//数字签名：采用国密SM2加密的SM4密钥生成的加密字符串
	@NotEmpty(message = "数字签名为空。")
	@Pattern(regexp = "\\w{1,512}", message = "数字签名不正确。")
	private String encryptedCode;
}
