package com.xh.vdm.interManager.feign;

import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.interManager.util.R;
import org.springblade.common.constant.ApplicationConstant;
import org.springblade.common.constant.CommonConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 外部系统调用 数据同步 Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = ApplicationConstant.APPLICATION_INTER_MANAGER
)
public interface IMessageClient {

	//请求前缀
	String API_PREFIX = CommonConstant.FEIGN_PREFIX + "/message";

	//车辆消息接口
	String VEHICLE_URL = API_PREFIX + "/vehicle";

	//车辆消息批量处理接口
	String VEHICLE_BATCH_URL = API_PREFIX + "/vehicleBatch";

	//职工消息接口
	String STAFF_URL = API_PREFIX + "/staff";

	//职工消息批量处理接口
	String STAFF_BATCH_URL = API_PREFIX + "/staffBatch";

	//基础设施消息接口
	String FACILITY_URL = API_PREFIX + "/facility";

	//基础设施消息批量同步接口
	String FACILITY_BATCH_URL = API_PREFIX + "/facilityBatch";

	//终端消息接口
	String TERMINAL_URL = API_PREFIX + "/terminal";

	//终端消息批量同步接口
	String TERMINAL_BATCH_URL = API_PREFIX + "/terminalBatch";

	//精密装备
	String PRECISION_URL = API_PREFIX + "/precision";

	//精密装备批量同步接口
	String PRECISION_BATCH_URL = API_PREFIX + "/precisionBatch";

	//货船
	String SHIP_URL = API_PREFIX + "/ship";

	//货船批量处理接口
	String SHIP_BATCH_URL = API_PREFIX + "/shipBatch";


	/**
	 * @description: 车辆同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 **/
	@PostMapping(VEHICLE_URL)
	R<String> vehicle(@RequestParam String operType, @RequestBody BdmVehicle vehicle);


	/**
	 * @description: 车辆批量同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 **/
	@PostMapping(VEHICLE_BATCH_URL)
	R<String> vehicleBatch (@RequestParam String operType, @RequestBody List<BdmVehicle> vehicles);



	/**
	 * 人员信息同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 *
	 * @param operType A 新增  U 修改  D 删除
	 * @param worker
	 * @return
	 */
	@PostMapping(STAFF_URL)
	R<String> staff(@RequestParam String operType, @RequestBody BdmWorker worker);

	/**
	 * 人员信息批量同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 * @param operType A 新增  U 修改  D 删除
	 * @param workers
	 * @return
	 */
	@PostMapping(STAFF_BATCH_URL)
	R<String> staffBatch (@RequestParam String operType, @RequestBody List<BdmWorker> workers);


	/**
	 * 基础设施信息同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 *
	 * @param operType A 新增  U 修改  D 删除
	 * @param facility
	 * @return
	 */
	@PostMapping(FACILITY_URL)
	R<String> facility(@RequestParam String operType, @RequestBody BdmFacility facility);

	/**
	 * 基础设施信息批量同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 * @param operType A 新增  U 修改  D 删除
	 * @param facilities
	 * @return
	 */
	@PostMapping(FACILITY_BATCH_URL)
	R<String> facilityBatch (@RequestParam String operType, @RequestBody List<BdmFacility> facilities);


	/**
	 * 终端信息同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 *
	 * @param operType   A 新增  U 修改  D 删除
	 * @param terminal
	 * @param deviceType 终端类型 终端类别，1-北斗定位终端，2-北斗穿戴式终端，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端
	 * @return
	 */
	@PostMapping(TERMINAL_URL)
	R<String> terminal(@RequestParam String operType, @RequestParam Integer deviceType, @RequestBody Object terminal);


	/**
	 * 终端信息批量同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 * @param operType A 新增  U 修改  D 删除
	 * @param terminals
	 * @param deviceType 终端类型 终端类别，1-北斗定位终端，2-北斗穿戴式终端，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端
	 * @return
	 */
	@PostMapping(TERMINAL_BATCH_URL)
	R<String> terminalBatch (@RequestParam String operType, @RequestParam Integer deviceType, @RequestBody List<Object> terminals);


	/**
	 * @description: 精密装备同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 **/
	@PostMapping(PRECISION_URL)
	R<String> precision(@RequestParam String operType, @RequestBody BdmPrecisionAssembly precision);

	/**
	 * @description: 精密装备批量同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 **/
	@PostMapping(PRECISION_BATCH_URL)
	R<String> precisionBatch (@RequestParam String operType, @RequestBody List<BdmPrecisionAssembly> precisions);


	/**
	 * @description: 货船同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 **/
	@PostMapping(SHIP_URL)
	R<String> ship(@RequestParam String operType, @RequestBody BdmShip ship);

	/**
	 * @description: 货船批量同步接口
	 * 目标：位置平台数据更新后，将调用外部平台接口，将数据同步过去
	 * 这里执行的操作：将数据写入到kafka中，后续会由消费者消费调用外部平台接口，将数据发送过去
	 **/
	@PostMapping(SHIP_BATCH_URL)
	R<String> shipBatch (@RequestParam String operType, @RequestBody List<BdmShip> ships);

}
