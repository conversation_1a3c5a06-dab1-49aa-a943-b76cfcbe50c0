package com.xh.vdm.interManager.dto.message;

import lombok.Data;

import java.util.List;

/**
 * 基础设施
 */
@Data
public class Facility {

	//设施名称
	private String facilityName;
	//设施编号
	private String facilityCode;
	//设施类型
	private Integer facilityType;
	//设施轮廓：地理多边形，坐标点串标识
	private List<Geometry> geometry;
	//地址
	private String address;
	//所属单位ID
	private String ownerId;
	//所属单位名称
	private String ownerName;
	//操作状态
	private String operateState;
	//操作时间
	private String operateTime;


	//设施轮廓
	@Data
	public static class Geometry{
		private Double longitude;
		private Double latitude;
	}

}
