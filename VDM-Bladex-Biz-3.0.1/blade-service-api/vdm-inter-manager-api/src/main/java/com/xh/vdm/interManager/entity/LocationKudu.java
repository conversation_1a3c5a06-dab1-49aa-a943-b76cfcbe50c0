package com.xh.vdm.interManager.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * @Description: Ku<PERSON>中的定位数据 pos_gn.locations
 * @Author: zhouxw
 * @Date: 2023/5/23 19:12
 */
@Data
@TableName(value = "locations")
public class LocationKudu {

	@TableField(jdbcType = JdbcType.VARBINARY)
	private String id;
	@TableField(jdbcType = JdbcType.BIGINT)
	@JsonProperty("target_id")
	private Long targetId;
	@TableField(jdbcType = JdbcType.TINYINT)
	@JsonProperty("target_type")
	private Byte targetType;
	@TableField(jdbcType = JdbcType.BIGINT)
	@JsonProperty("device_id")
	private Long deviceId;
	@TableField(jdbcType = JdbcType.VARCHAR)
	@JsonProperty("device_num")
	private String deviceNum;
	@TableField(jdbcType = JdbcType.TINYINT)
	@JsonProperty("device_type")
	private Byte deviceType;
	@TableField(jdbcType = JdbcType.DOUBLE)
	private Double longitude;
	@TableField(jdbcType = JdbcType.DOUBLE)
	private Double latitude;
	@TableField(jdbcType = JdbcType.INTEGER)
	private Integer altitude;
	@TableField(jdbcType = JdbcType.FLOAT)
	private Float speed;
	@TableField(jdbcType = JdbcType.SMALLINT)
	private Short bearing;
	@TableField(jdbcType = JdbcType.BIGINT)
	@JsonProperty("time")
	private Long time;
	@TableField(jdbcType = JdbcType.INTEGER)
	@JsonProperty("status")
	private Integer status;
	@TableField(jdbcType = JdbcType.INTEGER)
	@JsonProperty("alarm")
	private Integer alarm;
	@TableField(jdbcType = JdbcType.FLOAT)
	private Float mileage;
	@TableField(jdbcType = JdbcType.TINYINT)
	private Byte valid;
	@TableField(jdbcType = JdbcType.VARCHAR)
	private String auxiliary;
	@TableField(jdbcType = JdbcType.BIGINT)
	@JsonProperty("recv_time")
	private Long recvTime;
	@TableField(jdbcType = JdbcType.TINYINT)
	private Byte batch;
	@TableField(jdbcType = JdbcType.TINYINT)
	private Byte correction;
	@TableField(jdbcType = JdbcType.TINYINT)
	@JsonProperty("pos_sys")
	private Byte posSystem;
}
