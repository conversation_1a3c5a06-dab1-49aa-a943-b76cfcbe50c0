package com.xh.vdm.interManager.dto.message;

import lombok.Data;

/**
 * 报警数据
 */
@Data
public class AlarmData {
	//设备标识
	private String imei;
	//所属组织结构
	private String orgCode;
	//报警类型
	private String alarmType;
	//报警名称
	private String alarmName;
	//经度
	private double longitude;
	//纬度
	private double latitude;
	//高程
	private double height;
	//速度 单位：km/h
	private double speed;
	//方向
	private int direction;
	//当前时间
	private long time;
}
