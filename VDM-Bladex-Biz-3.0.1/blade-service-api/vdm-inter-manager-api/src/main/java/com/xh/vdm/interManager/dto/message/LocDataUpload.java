package com.xh.vdm.interManager.dto.message;

import lombok.Data;

/**
 * 位置数据上报
 */
@Data
public class LocDataUpload {

	//终端ID
	private String deviceId;
	//终端类型
	private String deviceType;
	//所属单位
	private String ownerId;
	//使用者类型
	private String userType;
	//使用者编号
	private String userCode;
	//终端赋码号
	private String deviceNum;
	//定位时间
	private String time;
	//经度
	private Double longitude;
	//纬度
	private Double latitude;
	//高程
	private Float altitude;
	//x
	private Double x;
	//y
	private Double y;
	//z
	private Float z;

	//速度
	private Float speed;
	//方向
	private Integer direction;
	//定位状态，是否新定位： 1 是   0 否
	private Integer state;
	//定位方式: 0 卫星定位  1 蓝牙信标
	private Integer locType;
	//地图坐标系 0：WGS84坐标系 1：GCJ02坐标系 2：CGCS2000系。
	private Integer mapType;

}
