package com.xh.vdm.interManager.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 业务字典表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BladeDictBiz implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 父主键
     */
    private Long parentId;

    /**
     * 字典码
     */
    private String code;

    /**
     * 字典值
     */
    private String dictKey;

    /**
     * 字典名称
     */
    private String dictValue;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 字典备注
     */
    private String remark;

    /**
     * 是否已封存
     */
    private Integer isSealed;

    /**
     * 是否已删除
     */
    private Integer isDeleted;

    private Date createdAt;

    private Date updatedAt;


}
