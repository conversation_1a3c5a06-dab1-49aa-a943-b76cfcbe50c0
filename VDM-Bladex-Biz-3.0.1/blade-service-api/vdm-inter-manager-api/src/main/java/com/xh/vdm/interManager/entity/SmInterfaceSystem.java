/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;
import java.util.Date;

/**
 * 接口管理系统表实体类
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Data
@TableName("in_interface_system")
public class SmInterfaceSystem implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId("ID")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;
	/**
	 * 系统名称
	 */
	@TableField("SYSTEM_NAME")
	@Compare("平台名称")
	private String systemName;
	/**
	 * 接口返回报文中的 data域 key
	 */
	@TableField("RES_DATA_KEY")
	@Compare("返回的data域key")
	private String resDataKey;
	/**
	 * 接口返回报文中的 code域 key
	 */
	@TableField("RES_CODE_KEY")
	@Compare("返回的code域key")
	private String resCodeKey;
	/**
	 * 接口请求成功时返回的code值(多个时，中间使用逗号分隔)
	 */
	@TableField("SUCCESS_CODE_VALUE")
	@Compare("返回的成功码")
	private String successCodeValue;
	/**
	 * 接口返回报文中的 message域 key
	 */
	@TableField("RES_MESSAGE_KEY")
	@Compare("返回的message域key")
	private String resMessageKey;
	/**
	 * 创建时间
	 */
	@TableField("CREATE_DATE")
	private Date createDate;
	/**
	 * 修改时间
	 */
	@TableField("UPDATE_DATE")
	private Date updateDate;
	/**
	 * 参数状态
	 */
	@TableField("STATE")
	private String state;
	/**
	 * 备注
	 */
	@TableField("NOTE")
	@Compare("备注")
	private String note;

	@TableField("WEBSERVICE_AUTH_MODEL")
	@Compare("WEBSERVICE接口Header中的认证模板")
	private String webserviceAuthModel;

	//第三方系统的公钥
	private String thirdPublicKey;
	//本系统为第三方系统生成的公钥
	private String publicKey;
	//本系统私钥
	private String privateKey;

	//联系人姓名
	@Compare("平台联系人")
	private String contactName;

	//联系人电话
	@Compare("联系方式")
	private String contactPhone;

	//是否调用该平台接口：0 不调用  1 调用
	//当调用该平台接口时，表示位置平台会调用该平台的接口，那么就需要配置接口的返回参数等4个参数字段
	@Compare("是否调用该平台接口")
	private Integer isCallInterface;

	//组织id列表,多个之间使用逗号分隔
	private String deptId;

	//http推送接口code，用于平台协议推送数据
	private String platHttpSendCode;
}
