/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 接口参数表实体类
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@Data
@TableName("in_interface_param")
public class SmInterfaceParam implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	* 参数id
	*/
	@JsonSerialize(using = ToStringSerializer.class)
		private Long id;
	/**
	* 对应接口id
	*/
	@JsonSerialize(using = ToStringSerializer.class)
		private Long interfaceManageId;
	/**
	* 参数类型：1：header中的参数； 2：body中的参数； 3.用于请求token接口：token中的参数(token中的access_token的key名称，如果为空，则表示返回的内容直接是token串，param_key 表示access_token的key ，param_value 表示有效期，单位为秒); 4.要转发的报文的信息，param_key为报文data的key(如果为空，则表示data为直接要发送的报文)，param_value 为位置(header还是body)；11.webservice中的用户名key和value设置；12.webservice中的密码key和value设置; 21.用于请求接口时使用token：param_key为使用token时的token key，param_value 为token的位置(header还是body);  22.用于推送公钥信息，param_key 为报文中公钥信息对象的key(如果为空，则表示直接为要推送的报文)，param_value为位置(header还是body)
	*/
		private String paramType;
	/**
	* 参数key
	*/
		private String paramKey;
	/**
	* 参数value
	*/
		private String paramValue;
	/**
	* 创建时间
	*/
		private Date createDate;
	/**
	* 修改时间
	*/
		private Date updateDate;
	/**
	* 参数状态
	*/
		private String state;
	/**
	* 备注
	*/
		private String note;


}
