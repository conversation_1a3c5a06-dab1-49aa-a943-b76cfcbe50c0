package com.xh.vdm.interManager.vo;

import lombok.Data;

/**
 * @Description: 调用其他系统接口时使用
 * @Auther: zxing
 * @Date: 2020/6/5 11:38
 * @company：CTTIC
 */
@Data
public class InterfaceDTO {
	private String interCode;
	//原来为Map，后来为了适配请求参数为json array [{},{}] 的情况，所以改成了 Object  ; 如果 param 实际为 Map类型，是可以强制类型转换的，使用 CommonUtil 中的 objectToMap也可以
	//对于 非 map 的情况，暂时只支持 post 请求
	private Object param;
	//调用接口之后的返回码，主要用来解决返回是否是 401 的问题(之前是通过正则表达式找401，后来正文中出现了 : 401!!，所以正则的方式不科学 )
	private String resCodeKey;
	//调用接口成功之后的 value 值
	private String[] successCodeValue;
	//返回报文中message域的key
	private String resMessageKey;
	//webservice header 中的认证模板
	private String webserviceAuthModel;
	//返回博文中 data域 的key
	private String resDataKey;
}
