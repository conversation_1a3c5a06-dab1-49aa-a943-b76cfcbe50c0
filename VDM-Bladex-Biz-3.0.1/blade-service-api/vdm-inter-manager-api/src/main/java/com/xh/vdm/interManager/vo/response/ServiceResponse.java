package com.xh.vdm.interManager.vo.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ServiceResponse {
	/**
	 * 主键
	 */
	@ApiModelProperty(value = "主键")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;
	/**
	 * 服务名称
	 */
	@ApiModelProperty(value = "服务名称")
	private String name;
	/**
	 * 服务编号
	 */
	@ApiModelProperty(value = "服务编号")
	private String code;
	/**
	 * 服务描述
	 */
	@ApiModelProperty(value = "服务描述")
	private String serviceDesc;
}
