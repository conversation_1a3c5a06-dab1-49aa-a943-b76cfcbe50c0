package com.xh.vdm.interManager.dto.message;

import lombok.Data;

/**
 * 短报文数据
 */
@Data
public class RdssData {
	//发送卡号
	private String fromCardNo;
	//接收卡号
	private String toCardNo;
	//所属组织结构
	private String orgCode;
	//经度
	private Double longitude;
	//纬度
	private Double latitude;
	//高程
	private Double height;
	//速度 单位：km/h
	private Double speed;
	//方向
	private Integer direction;
	//短报文内容
	private String content;
	//当前时间
	private Long time;
}
