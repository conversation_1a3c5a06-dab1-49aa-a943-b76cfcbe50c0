package com.xh.vdm.interManager.dto.message;

import lombok.Data;

@Data
public class PrecisionAssembly {

	// 设备序列号
	private String deviceId;

	// 设备名称
	private String deviceName;

	// 单位ID
	private String ownerId;

	// 单位名称
	private String ownerName;

	// 设备厂商
	private String manufacturer;

	// 设备型号
	private String model;

	// 操作状态（A：新增，U：更新，D：删除）
	private String operateState;

	// 操作时间（格式：yyyyMMddHHmmss）
	private String operateTime;

}
