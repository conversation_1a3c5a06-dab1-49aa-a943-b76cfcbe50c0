package com.xh.vdm.interManager.feign;

import com.xh.vdm.interManager.entity.InPosInterface;
import com.xh.vdm.interManager.util.R;
import org.springblade.common.constant.ApplicationConstant;
import org.springblade.common.constant.CommonConstant;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 轨迹操作 Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = ApplicationConstant.APPLICATION_INTER_MANAGER
)
public interface IInterfaceClient {

	//请求前缀
	String API_PREFIX = CommonConstant.FEIGN_PREFIX;

	//查询接口列表
	String GET_INTERFACE_BY_URL = API_PREFIX + "/getInterfaceByUrl";


	/**
	 * @description: 查询轨迹
	 * @author: zhouxw
	 * @date: 2023-05-146 15:13:13
	 * @param: [licencePlate 车牌号; licenceColor 车牌颜色; startTime 开始时间; endTime 结束时间; isFilter 是否过滤无效点 0 表示不过滤，1表示过滤]
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.LocationKudu>>
	 **/
	@GetMapping(GET_INTERFACE_BY_URL)
	R<List<InPosInterface>> getInterfaceByUrl (
		@RequestParam(value = "url") String url
	);


}
