/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;

/**
 * 业务字典表实体类
 *
 * <AUTHOR>
 * @since 2022-05-22
 */
@Data
@TableName("in_interface_dict")
public class SmInterfaceDict implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	* 主键
	*/
	@JsonSerialize(using = ToStringSerializer.class)
		private Long id;
	/**
	* 父主键
	*/
	@JsonSerialize(using = ToStringSerializer.class)
		private Long parentId;
	/**
	* 字典码
	*/
		private String code;
	/**
	* 字典值
	*/
		private String dictKey;
	/**
	* 字典名称
	*/
		private String dictValue;
	/**
	* 排序
	*/
		private Integer sort;
	/**
	* 字典备注
	*/
		private String remark;
	/**
	* 是否已封存
	*/
		private Integer isSealed;
	/**
	* 是否已删除
	*/
		private Integer isDeleted;


}
