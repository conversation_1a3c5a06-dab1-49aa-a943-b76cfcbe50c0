package com.xh.vdm.interManager.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@TableName("bdm_rdss_msg")
@Data
public class ShortMessageLog implements Serializable {

    private Long id; // 主键,雪花id

    private String sender; // 发信方卡号

    private String receiver; // 收信方卡号

    private Long deptId; // 所属组织id

    private Double longitude; // 经度

    private Double latitude; // 纬度

    private Integer altitude; // 高程

    private Float speed; // 速度,km/h

    private Short bearing; // 航向,单位:度

    private Long time; // 定位时间

    private String content; // 短报文内容
}
