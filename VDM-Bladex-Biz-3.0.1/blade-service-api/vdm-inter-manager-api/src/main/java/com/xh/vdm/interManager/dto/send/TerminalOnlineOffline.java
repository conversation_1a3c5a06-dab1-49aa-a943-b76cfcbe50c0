package com.xh.vdm.interManager.dto.send;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springblade.common.config.CustomAsiaZoneDateDeserializer;
import org.springblade.common.config.ISO8601DateDeserializer;
import org.springblade.common.utils.DateUtil;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;


/**
 * 终端上下线状态
 */
@Data
public class TerminalOnlineOffline {

	private String phone;
	private Long deviceId;
	private Integer deviceType;
	private String deviceNum;
	private String uniqueId;
	private Long targetId;
	private Integer targetType;
	private String targetName;
	@JSONField(deserializeUsing = CustomAsiaZoneDateDeserializer.class)
	private Date time;
	private Integer onOffLine; //0-上线，1-下线

}
