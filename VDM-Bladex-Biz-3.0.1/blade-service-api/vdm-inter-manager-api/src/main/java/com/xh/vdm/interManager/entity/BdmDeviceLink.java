package com.xh.vdm.interManager.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (BdmDeviceLink)实体类
 */
@Data
public class BdmDeviceLink implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private Long deviceId;

	private Integer deviceType;

	private String uniqueId;

	private String deviceNum;

	private Long targetId;

	private Integer targetType;

	private String targetName;

	private Double longitude;

	private Double latitude;

	private String address;

	private Integer action;

	private Date time;

	private String remark;

	/**
	 * 根据 监控对象id 监控对象类别 进行分组，按时间排序取最新数据的一个标志
	 */
	@TableField(exist = false)
	private Integer rn;

	/**
	 * 告警数量
	 */
	@TableField(exist = false)
	private Long total;

}

