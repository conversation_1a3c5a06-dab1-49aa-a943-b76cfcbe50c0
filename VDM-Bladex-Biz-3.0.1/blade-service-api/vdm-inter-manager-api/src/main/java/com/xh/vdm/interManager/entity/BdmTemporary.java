package com.xh.vdm.interManager.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BdmTemporary implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private String name;
	private String wkno;
	private Integer targetType;
	private Long deptId;
	private Integer post;
	private Integer industry;
	private String phone;
	private Long companyId;
	private String company;
	private Date validFrom;
	private Date validTo;
	private Date createTime;
	private Date updateTime;
	private Integer deleted;
	private Integer terminalType;
	private String uniqueId;
	private Integer status;
	private String msg;
}

