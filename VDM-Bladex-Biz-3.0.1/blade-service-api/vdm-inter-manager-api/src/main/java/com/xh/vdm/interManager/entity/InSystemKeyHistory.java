package com.xh.vdm.interManager.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 公私钥变更历史表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class InSystemKeyHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 系统id
     */
    private Long systemId;

    /**
     * 系统名称
     */
    private String systemName;

    /**
     * 私钥
     */
    private String privateKey;

    /**
     * 公钥
     */
    private String publicKey;

    /**
     * 更改时间
     */
    private Date createTime;

    /**
     * 更改人
     */
    private Long createUser;


}
