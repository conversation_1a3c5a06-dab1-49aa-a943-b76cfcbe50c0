package com.xh.vdm.bd.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * @Description: <PERSON><PERSON>中的定位数据 pos_gn.locations
 * @Author: zhouxw
 * @Date: 2023/5/23 19:12
 */
@Data
@TableName("locations_d")
public class LocationHDFS {

	private String licencePlate;
	private Long licenceColor;
	private Long locTime;
	private Double longitude;
	private Double latitude;
	private String phone;
	private Long altitude;
	private Double speed;
	private Long recvTime;
	private Long bearing;
	private Double mileage;
	private Long satelliteNum;
	private Long alarmFlag;
	private Long stateFlag;
	private Long valid;
	private Long expandSignal;
	private Long ioStatus;
	private String temperature;
	private Long batch;
	private Long areacode;
	private Long curareacode;
}
