/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.bd.feign;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.LocationKuduPage;
import com.xh.vdm.bd.entity.VehicleBase;
import org.springblade.common.constant.ApplicationConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 轨迹操作 Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = ApplicationConstant.APPLICATION_BIG_DATA
)
public interface ILocationClient {

	//请求前缀
	String API_PREFIX = CommonConstant.FEIGN_PREFIX;

	//查询定位点列表
	String LOCATION = API_PREFIX + "/location";

	//分页查询轨迹
	String LOCATION_PAGE = API_PREFIX + "/locationByPage";

	//查询车辆列表
	String VEHICLE_LIST = API_PREFIX + "/getVehicleList";


	/**
	 * @description: 查询轨迹
	 * @author: zhouxw
	 * @date: 2023-05-146 15:13:13
	 * @param: [licencePlate 车牌号; licenceColor 车牌颜色; startTime 开始时间; endTime 结束时间; isFilter 是否过滤无效点 0 表示不过滤，1表示过滤]
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.LocationKudu>>
	 **/
	@GetMapping(LOCATION)
	R<List<LocationKudu>> location (
		@RequestParam(value = "target_id") Long targetId,
		@RequestParam(value = "target_type") Integer targetType,
		@RequestParam(value = "device_id") Long deviceId,
		@RequestParam(value = "device_type") Integer deviceType,
		@RequestParam(value = "start_time") Long startTime,
		@RequestParam(value = "end_time") Long endTime,
		@RequestParam(value = "valid") Byte valid,
		@RequestParam(value = "batch") Byte batch,
		@RequestParam(value="pos_system", required = false) Byte posSystem
	);


	/**
	 * @description: 查询车辆列表
	 * @author: zhouxw
	 * @date: 2023-06-159 12:00:10
	 * @param: [isFilter 是否过滤无效点 0 表示不过滤，1表示过滤 ;  startTime 开始时间； endTime 结束沈佳妮]
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.VehicleBase>>
	 **/
	@GetMapping(VEHICLE_LIST)
	R<List<VehicleBase>> getVehicleList(@RequestParam("is_filter")Integer isFilter, @RequestParam("start_time") Long startTime, @RequestParam("end_time") Long endTime);

	/**
	 *
	 * @param targetId
	 * @param targetType
	 * @param deviceId
	 * @param deviceType
	 * @param valid
	 * @param batch
	 * @param startTime
	 * @param endTime
	 * @param posSystem
	 * @param startIndex 从1开始
	 * @param endIndex 含头不含尾
	 * @return
	 */
	@GetMapping(LOCATION_PAGE)
	R<LocationKuduPage> locationByPage (
		@RequestParam(value = "target_id", required = false) Long targetId,
		@RequestParam(value = "target_type", required = false) Integer targetType,
		@RequestParam(value = "device_id", required = false) Long deviceId,
		@RequestParam(value = "device_type", required = false) Integer deviceType,
		@RequestParam(value = "valid", required = false) Byte valid,
		@RequestParam(value = "batch", required = false) Byte batch,
		@RequestParam(value = "start_time", required = true) Long startTime,
		@RequestParam(value = "end_time", required = true) Long endTime,
		@RequestParam(value = "pos_system",required = false ) Byte posSystem,
		@RequestParam(value = "start_index", required = true) Long startIndex,
		@RequestParam(value = "end_index", required = true) Long endIndex
	);

	/**
	 * @description: 根据定位时间查询车辆的定位数据列表
	 * @author: zhouxw
	 * @date: 2023-06-159 16:41:45
	 * @param: [isFilter, licencePlate, licenceColor, locTimes]
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.LocationKudu>>
	 **/
	//@GetMapping(VEHICLE_LIST)
	//R<List<LocationKudu>> locationForVehicleTimes(@RequestParam("licence_plate") String licencePlate, @RequestParam("licence_color") Long licenceColor, String locTimes);

}
