package com.xh.vdm.bd.feign;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bd.entity.BdmSecurity;
import com.xh.vdm.bd.vo.request.AlarmRequest;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/7/11 22:05
 */
public interface IAlarmClient {

	//请求前缀
	String API_PREFIX = CommonConstant.FEIGN_PREFIX;

	//分页查询报警信息
	String ALARM_INFO_PAGE = API_PREFIX + "/alarm/alarm-info-page";

	/**
	 * @description: 分页查询报警信息
	 * @author: zhouxw
	 * @date: 2023-07-192 22:01:25
	 * @param: [request]
	 * @return: org.springblade.core.tool.api.R<com.baomidou.mybatisplus.core.metadata.IPage<com.xh.vdm.bd.entity.BdmSecurity>>
	 **/
	@PostMapping(ALARM_INFO_PAGE)
	R<IPage<BdmSecurity>> alarmInfoPage(@RequestBody AlarmRequest request);




}
