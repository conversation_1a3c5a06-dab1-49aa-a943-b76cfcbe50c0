package com.xh.vdm.bd.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.constant.AlarmConstant;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmSecurity implements Serializable {

    private static final long serialVersionUID = 1L;


	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	/**
	 * 车牌号
	 */
	private String licencePlate;

	/**
	 * 车牌颜色
	 */
	private String licenceColor;

	/**
	 * 报警手机号
	 */
	private String phone;

	/**
	 * 车组id
	 */
	private Long deptId;

	/**
	 * 所属企业
	 */
	private String deptFullName;

	private Integer vehicleUseType;

	/**
	 * 报警类型
	 */
	private Integer alarmType;

	/**
	 * 报警等级
	 */
	private Integer alarmLevel;

	/**
	 * 驾驶员身份证
	 */
	private String idCard;

	/**
	 * 驾驶员姓名
	 */
	private String driverName;

	/**
	 * 车辆仪表速度
	 */
	private Double vehicleSpeed = 0D;

	/**
	 * 最大速度（针对终端超速和分段限速）
	 */
	private Double maxSpeed = 0D;

	/**
	 * 速度
	 */
	private Double speed = 0D;

	/**
	 * 限速
	 */
	private Double limitSpeed = 0D;

	/**
	 * 道路名称
	 */
	private String roadName;

	private Integer roadType;

	/**
	 * 定位纬度
	 */
	private BigDecimal latitude;

	/**
	 * 定位经度
	 */
	private BigDecimal longitude;

	/**
	 * 方向
	 */
	private Integer direction;

	/**
	 * 高程
	 */
	private Integer altitude;

	/**
	 * 里程
	 */
	private Double mileage;

	/**
	 * 报警时间
	 */
	private Long alarmTime;

	/**
	 * 报警位置
	 */
	private String alarmAddress;

	/**
	 * 报警来源
	 */
	private String alarmOrigin;

	/**
	 * sim卡号
	 */
	private String simId;

	/**
	 * 持续状态是否结束（0：未结束，1：已结束）
	 */
	private Integer alarmComplete;

	/**
	 * 服务商是否自动处理（0：否，1：是）
	 */
	private Integer serverAutoDeal;

	/**
	 * 第三方是否自动处理（0：否，1：是）
	 */
	private Integer thirdAutoDeal;

	/**
	 * 报警处理状态，0-待处理；1：处理中；2；处理完毕
	 */
	private Integer alarmDealState;

	/**
	 * 解除报警状态， -1-未解除  0-报警处理  1-无风险解除  2-标记误报解除
	 */
	private Integer removeAlarm;

	/**
	 * 处理描述
	 */
	private String dealDescribe;

	/**
	 * 处理人
	 */
	private String dealMan;

	/**
	 * 平台规则id
	 */
	private Integer ruleId;

	/**
	 * 规则类型id
	 */
	private Integer ruleTypeId;

	/**
	 * 平台规则名称
	 */
	private String ruleName;

	/**
	 * 车辆状态
	 */
	private String vehicleStates;

	/**
	 * 报警结束时间
	 */
	private Long alarmEndTime;

	/**
	 * 报警结束位置
	 */
	private String alarmEndAddress;

	/**
	 * 报警结束纬度
	 */
	private Double latitudeEnd;

	/**
	 * 报警结束经度
	 */
	private Double longitudeEnd;

	/**
	 * 终端状态描述
	 */
	private String terminalStateString;

	/**
	 * 车辆id
	 */
	private Integer vehicleId;

	/**
	 * 告警附件关联
	 */
	private String uniqueId;

	/**
	 * 服务状态（状态值与名称的映射，详见blade_dict_biz表code=enjoy_service的记录）
	 */
	private Integer serviceState;

	/**
	 * 是否误报 （1 误报  0 非误报）
	 */
	private Integer isWrong;

	/**
	 * 上报状态
	 */
	private Integer reportingState;

	/**
	 * 809第三方转发平台名称
	 */
	private String thirdPlatform;

	/**
	 * 服务商处理状态（0：未处理，1：已处理，2：误报）
	 */
	private Integer serverState;

	/**
	 * 服务商处理结果，0：误报；1：确认报警
	 */
	private Integer serverResult;

	/**
	 * 服务商处理措施（英文逗号分隔的多个措施值，措施值与名称的映射，详见blade_dict_biz表code=server_measures的记录）
	 */
	private String serverMeasures;

	/**
	 * 服务商处理内容
	 */
	private String serverContent;

	/**
	 * 服务商处理人员
	 */
	private Long serverUser;

	/**
	 * 服务商处理时间
	 */
	private Long serverTime;

	/**
	 * 第三方处理状态（0：未处理，1：已处理，2：误报）
	 */
	private Integer thirdState;

	/**
	 * 第三方处理结果，0：误报；1：确认报警
	 */
	private Integer thirdResult;

	/**
	 * 第三方处理措施（英文逗号分隔的多个措施值，措施值与名称的映射，详见blade_dict_biz表code=third_measures的记录）
	 */
	private String thirdMeasures;

	/**
	 * 第三方处理内容
	 */
	private String thirdContent;

	/**
	 * 第三方处理人员
	 */
	private Long thirdUser;

	/**
	 * 第三方处理时间
	 */
	private Long thirdTime;

	/**
	 * 企业处理状态（0：未处理，1：已处理）
	 */
	private Integer companyState;

	/**
	 * 企业处理措施（英文逗号分隔的多个措施值，措施值与名称的映射，详见blade_dict_biz表code=company_measures的记录）
	 */
	private String companyMeasures;

	/**
	 * 企业处理内容
	 */
	private String companyContent;

	/**
	 * 企业处理附件URL
	 */
	private String companyAttach;

	/**
	 * 企业处理人员
	 */
	private Long companyUser;

	/**
	 * 企业处理时间
	 */
	private Long companyTime;

	/**
	 * 企业申诉状态（0：未申诉，1：已申诉，2：申诉通过，3：申诉驳回）
	 */
	private Integer appealState;

	/**
	 * 企业申诉理由
	 */
	private String appealReason;

	/**
	 * 企业申诉附件URL
	 */
	private String appealAttach;

	/**
	 * 企业申诉人员
	 */
	private Long appealUser;

	/**
	 * 企业申诉时间
	 */
	private Long appealTime;

	/**
	 * 处理结果
	 */
	private Integer appealResult;

	/**
	 * 申诉审核状态，0：待审核；1：已审核
	 */
	private Integer auditState;

	/**
	 * 审核结果，0：误报；1：确认报警
	 */
	private Integer auditResult;

	/**
	 * 申诉处理内容
	 */
	private String auditContent;

	/**
	 * 申诉审核人员
	 */
	private Long auditUser;

	/**
	 * 申诉处理时间
	 */
	private Long auditTime;

	/**
	 * 是否已开罚单（0：未开具，1：已开具）
	 */
	private Integer punishState;

	/**
	 * 报警附件数量
	 */
	private Integer alarmDmsAppendix;

	/**
	 * 是否申诉（1：已申诉 0：未申诉）
	 */
	private Integer isAppeal;

	/**
	 * 协议类型： 1 808      2  809
	 */
	private Integer protocolType;

	/**
	 * 行驶时间
	 */
	private Long drivingTime;

	/**
	 * 疲劳开始时间
	 */
	private Long fatigueStartTime;

	/**
	 * 疲劳结束时间
	 */
	private Long fatigueEndTime;

	/**
	 * 车辆归属区域ID
	 */
	private Integer regionId;

	private Integer dealOperateState;

	/**
	 * 创建时间
	 */
	private Long createTime;

	/**
	 * 更新时间
	 */
	private Long updateTime;

	private Integer autoDeal;

	public static String getServerStateName (Integer serverState) {
		if ((serverState == null) || (serverState == AlarmConstant.SERVER_STATE_PEND)) {
			return "未处理";
		} else if (serverState == AlarmConstant.SERVER_STATE_DONE) {
			return "已处理";
		} else if (serverState == AlarmConstant.SERVER_DEAL_STATE_IS_WRONG) {
			return "误报";
		} else {
			return "";
		}
	}

	public static String getThirdStateName (Integer thirdState) {
		if ((thirdState == null) || (thirdState == AlarmConstant.THIRD_STATE_PEND)) {
			return "未处理";
		} else if (thirdState == AlarmConstant.THIRD_STATE_DONE) {
			return "已处理";
		} else if (thirdState == AlarmConstant.THIRD_DEAL_STATE_IS_WRONG) {
			return "误报";
		} else {
			return "";
		}
	}

	public static String getCompanyStateName (Integer companyState) {
		if ((companyState == null) || (companyState == AlarmConstant.COMPANY_STATE_PEND)) {
			return "未处理";
		} else if (companyState == AlarmConstant.COMPANY_STATE_DONE) {
			return "已处理";
		} else {
			return "";
		}
	}

	public static String getAlarmCompleteName(Integer alarmComplete) {
		if ((alarmComplete == null) || (alarmComplete == AlarmConstant.ALARM_COMPLETE_CONTINUE)) {
			return "持续中";
		} else if (alarmComplete == AlarmConstant.ALARM_COMPLETE_DONE) {
			return "已结束";
		} else {
			return "";
		}
	}
	public static String getAppealStateName (Integer appealState) {
		if ((appealState == null) || (appealState == AlarmConstant.APPEAL_STATE_PEND)) {
			return "未申诉";
		} else if (appealState == AlarmConstant.APPEAL_STATE_DONE) {
			return "已申诉";
		} else if (appealState == AlarmConstant.APPEAL_STATE_PASS) {
			return "申诉通过";
		} else if (appealState == AlarmConstant.APPEAL_STATE_REJECT) {
			return "申诉驳回";
		} else if (appealState == AlarmConstant.APPEAL_STATE_NO) {
			return "不可申诉";
		} else {
			return "";
		}
	}

}
