package com.xh.vdm.bd.vo.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 报警请求参数
 * @Author: zhouxw
 * @Date: 2023/7/11 20:21
 */
@Data
public class AlarmRequest {

	@JsonProperty("dept_id")
	private Long deptId;

	@JsonProperty("licence_plate")
	private String licencePlate;

	@JsonProperty("licence_color")
	private String licenceColor;

	@JsonProperty("alarm_level")
	private Integer alarmLevel;

	@JsonProperty("alarm_type")
	private Integer alarmType;

	@JsonProperty("start_time")
	private Long startTime;

	@JsonProperty("end_time")
	private Long endTime;

	@JsonProperty("server_state")
	private Integer serverState;

	@JsonProperty("third_state")
	private Integer thirdState;

	@JsonProperty("company_state")
	private Integer companyState;

	@JsonProperty("appeal_state")
	private Integer appealState;

	@JsonProperty("alarm_complete")
	private Integer alarmComplete;

	@JsonProperty("company_measures")
	private Integer companyMeasures;

	//当前页码
	private Integer current;

	//分页大小
	private Integer size;

	@JsonIgnore
	private Integer limit;

	@JsonIgnore
	private Integer offset;
}
