package com.xh.vdm.bd.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 抽象终端表
 */
@Data
public class BdmAbstractDevice implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private String uniqueId;

	private Integer deviceType;

	private Long deptId;

	private Integer category;

	private String deviceNum;

	private Long targetId;

	private Integer targetType;

	private Integer deleted;

	private Integer iotProtocol;

	private String createAccount;

	private Integer specificity;

	private Integer channelNum;

	private String model;
}

