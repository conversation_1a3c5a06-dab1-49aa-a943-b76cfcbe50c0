package com.xh.vdm.base.feign;

import com.xh.vdm.base.vo.SendMsgToTerminalRequest;
import org.springblade.common.constant.ApplicationConstant;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
	value = ApplicationConstant.APPLICATION_GO_TERMINAL
)
public interface ITerminalClient {

	@PostMapping("regulatorycenter/sendtextmsg")
	R sendMsgToTerminal (@RequestBody SendMsgToTerminalRequest request);


}
