package com.xh.vdm.base.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
public class SendMsgToTerminalRequest {

	@JsonProperty("alarm")
	@JSONField(name = "alarm")
	private List<SendMsgToTerminalRequest.Alarm> alarmList;

	@JsonProperty("tts_read")
	@JSONField(name = "tts_read")
	private Byte audio;

	@JsonProperty("display")
	@JSONField(name = "display")
	private Byte screen;

	@JsonProperty("urgent")
	@JSONField(name = "urgent")
	private Byte urgent;

	@JsonProperty("text")
	@JSONField(name = "text")
	private String content;

	@Data
	public static class Alarm {

		@JsonProperty("alarm_id")
		@JSONField(name = "alarm_id")
		private Long alarmId;

		@JsonProperty("device_type")
		@JSONField(name = "device_type")
		private Byte deviceType;

		@JsonProperty("device_id")
		@JSONField(name = "device_id")
		private Long deviceId;
	}
}
