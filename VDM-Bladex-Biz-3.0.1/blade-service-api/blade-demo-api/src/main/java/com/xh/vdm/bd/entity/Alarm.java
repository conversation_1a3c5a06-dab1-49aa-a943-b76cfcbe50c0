package com.xh.vdm.bd.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "bdm_security", autoResultMap = true)
public class Alarm implements Serializable {

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	@TableField(value = "dept_id")
	private Integer deptId;

	@TableField(value = "licence_color")
	private Byte licenceColor;

	@TableField(value = "licence_plate")
	private String licencePlate;

	@TableField(value = "alarm_level")
	private Short alarmLevel;

	@TableField(value = "alarm_type")
	private Short alarmType;

	@TableField(value = "alarm_time")
	private Long startTime;

	@TableField(value = "longitude")
	private Double startLon;

	@TableField(value = "latitude")
	private Double startLat;

	@TableField(value = "alarm_address")
	private String startAddress;

	@TableField(value = "alarm_end_time")
	private Long endTime;

	@TableField(value = "longitude_end")
	private Double endLon;

	@TableField(value = "latitude_end")
	private Double endLat;

	@TableField(value = "alarm_end_address")
	private String endAddress;

	@TableField(value = "speed")
	private Double speed;

	@TableField(value = "limit_speed")
	private Double limitSpeed;

	@TableField(value = "service_state")
	private Integer serviceState;

	@TableField(value = "server_state")
	private Byte serverState;

	@TableField(value = "server_result")
	private String serverResult;

	@TableField(value = "server_remark")
	private String serverRemark;

	@TableField(value = "server_time")
	private String serverTime;

	@TableField(value = "server_user")
	private String serverUser;

	@TableField(value = "third_state")
	private Byte thirdState;

	@TableField(value = "third_result")
	private String thirdResult;

	@TableField(value = "third_remark")
	private String thirdRemark;

	@TableField(value = "third_time")
	private String thirdTime;

	@TableField(value = "third_user")
	private String thirdUser;

	@TableField(value = "company_state")
	private Byte companyState;

	@TableField(value = "company_result")
	private String companyResult;

	@TableField(value = "company_remark")
	private String companyRemark;

	@TableField(value = "company_time")
	private String companyTime;

	@TableField(value = "company_user")
	private String companyUser;

	@TableField(value = "appeal_state")
	private Byte appealState;

	@TableField(value = "appeal_result")
	private String appealResult;

	@TableField(value = "appeal_remark")
	private String appealRemark;

	@TableField(value = "appeal_time")
	private String appealTime;

	@TableField(value = "appeal_user")
	private String appealUser;

	@TableField(value = "audit_result")
	private String auditResult;

	@TableField(value = "audit_remark")
	private String auditRemark;

	@TableField(value = "audit_time")
	private String auditTime;

	@TableField(value = "audit_user")
	private String auditUser;
}
