package com.xh.vdm.base.config;

import com.alibaba.druid.support.json.JSONUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;

/**
 * @Description: go服务 feign client 请求配置
 * @Author: zhouxw
 * @Date: 2023/6/7 18:30
 */
@Slf4j
public class GoFeignRequestInterceptor implements RequestInterceptor {


	//租户id
	public static final String TENANT_ID = "tenantId";

	//用户名
	public static final String ACCOUNT = "account";

	@Override
	public void apply(RequestTemplate requestTemplate) {

		/**
		 * @description: 为适配go接口，调用go接口时，header中添加tenantId 和 account
		 * @author: zhouxw
		 * @date: 2023-06-158 18:34:45
		 * @param: [requestTemplate]
		 * @return: void
		 **/
		//1.获取用户信息
		String tenantId = "000000";
		String account = "admin";
		BladeUser user = AuthUtil.getUser();
		if(user != null){
			tenantId = StringUtils.isBlank(user.getTenantId()) ? "000000" : user.getTenantId();
			account = StringUtils.isBlank(user.getAccount()) ? "admin" : user.getAccount();
		}
		requestTemplate.header(TENANT_ID, tenantId);
		requestTemplate.header(ACCOUNT, account);
		log.info("tenantId =" + tenantId +", account = "+account);
		log.info(JSONUtils.toJSONString(requestTemplate.headers()));

	}
}
