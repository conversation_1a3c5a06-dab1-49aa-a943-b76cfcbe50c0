package com.xh.vdm.base.feign;

import com.xh.vdm.base.Constant.EduApplicationConstant;
import com.xh.vdm.base.config.GoFeignRequestInterceptor;
import com.xh.vdm.base.vo.SyncDeptInfoRequest;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(
	value = EduApplicationConstant.APPLICATION_GO_SAFE_EDUCATION,
	configuration = GoFeignRequestInterceptor.class
)
public interface ISafeEducationClient {

	@PostMapping("/thirdparty/syncdept")
	R syncDept (@RequestBody SyncDeptInfoRequest request);
}
