/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.base.feign;

import com.xh.vdm.base.config.GoFeignRequestInterceptor;
import org.springblade.common.constant.ApplicationConstant;
import org.springblade.core.tool.api.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Notice Feign接口类
 * 添加feign配置，在调用go服务接口时，添加 tenantId 和 account
 * <AUTHOR>
 */
@FeignClient(
	value = ApplicationConstant.APPLICATION_GO_BASEINFO,
	configuration = GoFeignRequestInterceptor.class
)
public interface IBaseInfoClient {

	String API_PREFIX = "/baseinfo";
	String GET_PERSON = API_PREFIX + "/driver/get";

	String GET_VEHICLE = API_PREFIX + "/vehicle/list";

	/**
	 * 获取 人员列表
	 * 分页
	 * @return
	 */
	@GetMapping(GET_PERSON)
	R getDriver(@RequestParam("current") Integer current, @RequestParam("count") Integer count);

	/**
	 * @description: 获取 车辆列表
	 * 分页
	 * @author: zhouxw
	 * @date: 2023-06-164 20:04:18
	 * @param: [current, count]
	 * @return: org.springblade.core.tool.api.R
	 **/
	@GetMapping(GET_VEHICLE)
	R getVehicle(@RequestParam("current") Integer current, @RequestParam("size") Integer size);

}
