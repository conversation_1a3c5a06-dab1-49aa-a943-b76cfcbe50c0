#服务器配置
server:
  undertow:
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 16
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 400
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true

#spring配置
spring:
  cloud:
    sentinel:
      eager: true
  devtools:
    restart:
      log-condition-evaluation-delta: false
    livereload:
      port: 23333

#feign配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false

#对外暴露端口
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always

#knife4j配置
knife4j:
  #启用
  enable: true
  #基础认证
  basic:
    enable: false
    username: blade
    password: blade
  #增强配置
  setting:
    enableSwaggerModels: true
    enableDocumentManage: true
    enableHost: false
    enableHostText: http://localhost
    enableRequestCache: true
    enableFilterMultipartApis: false
    enableFilterMultipartApiMethodType: POST
    language: zh-CN
    enableFooter: false
    enableFooterCustom: true
    footerCustomContent: Copyright © 2021 BladeX All Rights Reserved

#swagger公共信息
swagger:
  title: BladeX 接口文档系统
  description: BladeX 接口文档系统
  version: *******.RELEASE
  license: Powered By BladeX
  license-url: https://bladex.vip
  terms-of-service-url: https://bladex.vip
  contact:
    name: smallchill
    email: <EMAIL>
    url: https://gitee.com/smallc

#blade配置
blade:
  #token配置
  token:
    #是否有状态
    state: false
  #redis序列化方式
  redis:
    serializer-type: protostuff
  #接口配置
  api:
    #报文加密配置
    crypto:
      #启用报文加密配置
      enabled: false
      #使用AesUtil.genAesKey()生成
      aes-key: O2BEeIv399qHQNhD6aGW8R8DEj4bqHXm
      #使用DesUtil.genDesKey()生成
      des-key: jMVCBsFGDQr1USHo
  #jackson配置
  jackson:
    #null自动转空值
    null-to-empty: true
    #大数字自动转字符串
    big-num-to-string: true
    #支持text文本请求,与报文加密同时开启
    support-text-plain: false
  #xss配置
  xss:
    enabled: true
    skip-url:
      - /weixin
      - /notice/submit
      - /model/submit
  #安全框架配置
  secure:
    #接口放行
    skip-url:
      - /test/**
    #授权认证配置
    auth:
      - method: ALL
        pattern: /weixin/**
        expression: "hasAuth()"
      - method: POST
        pattern: /dashboard/upload
        expression: "hasTimeAuth(9, 17)"
      - method: POST
        pattern: /dashboard/submit
        expression: "hasAnyRole('administrator', 'admin', 'user')"
    #基础认证配置
    basic:
      - method: ALL
        pattern: /dashboard/info
        username: "blade"
        password: "blade"
    #动态签名认证配置
    sign:
      - method: ALL
        pattern: /dashboard/sign
        crypto: "sha1"
    #多终端认证配置
    client:
      - client-id: sword
        path-patterns:
          - /sword/**
      - client-id: saber
        path-patterns:
          - /saber/**
  #多租户配置
  tenant:
    #多租户增强
    enhance: true
    #多租户授权保护
    license: false
    #动态数据源功能
    dynamic-datasource: false
    #动态数据源全局扫描
    dynamic-global: false
    #多租户字段名
    column: tenant_id
    #排除多租户逻辑
    exclude-tables:
      - blade_user
