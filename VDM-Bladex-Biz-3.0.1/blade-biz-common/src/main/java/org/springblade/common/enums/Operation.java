package org.springblade.common.enums;

/**
 * 业务操作类型
 */
public enum Operation {
    /**
     * 其它
     */
    OTHER,

    /**
     * 新增
     */
    INSERT,

    /**
     * 修改
     */
    UPDATE,

    /**
     * 删除
     */
    DELETE,

    /**
     * 授权
     */
    GRANT,

    /**
     * 导出
     */
    EXPORT,

    /**
     * 导入
     */
    IMPORT,

	/**
	 * 入库
	 */
	INLET,

	/**
	 * 出库
	 */
	OUTLET,

	/**
	 * 回库
	 */
	RETURN_BACK,

	/**
	 * 绑定/解绑终端
	 */
	BIND_OR_UNBIND,
	/**
	 * 绑定终端
	 */
	BIND,
	/**
	 * 解绑终端
	 */
	UNBIND,
}
