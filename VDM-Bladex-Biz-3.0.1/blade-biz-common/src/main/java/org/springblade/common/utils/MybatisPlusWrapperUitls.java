package org.springblade.common.utils;

import com.baomidou.mybatisplus.core.conditions.AbstractWrapper;
import com.baomidou.mybatisplus.core.conditions.Wrapper;

import java.util.List;

/**
 * 数据库语句拼接辅助工具类
 */
public class MybatisPlusWrapperUitls {

    /**
     * 长数组in处理
     **/
    public static void longListIn(AbstractWrapper<?, String, ? extends Wrapper<?>> wrapper, String col, List list){
        //sql查询数组过长处理代码
        if(null != list && 0 != list.size()){
            wrapper.and(wp->{
                //mysql中in数组长度不能超过1k
                Integer step = 900;
                Integer size = list.size();
                Integer begin = size - size%step;
                Integer end = size;
                wp.in(!begin.equals(end), col, list.subList(begin, end));
                while (begin >= step){
                    end = begin;
                    begin = begin - step;
                    wp.or().in(col, list.subList(begin, end));
                }
            });
        }else {
            wrapper.eq("1", 0);
        }
    }
}
