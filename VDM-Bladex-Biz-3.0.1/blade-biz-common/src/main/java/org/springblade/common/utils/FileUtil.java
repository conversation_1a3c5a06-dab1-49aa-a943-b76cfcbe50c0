package org.springblade.common.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
public class FileUtil {

	public static R<JSONObject> upload (String url, File file, String fileFiledName, Map<String, String> headerMap) {
		CloseableHttpClient httpClient = HttpClients.createDefault();
		try {
			HttpPost httpPost = new HttpPost(url);
			if ((headerMap != null) && (!headerMap.isEmpty())) {
				for (Map.Entry<String, String> header : headerMap.entrySet()) {
					httpPost.addHeader(header.getKey(), header.getValue());
				}
			}

			MultipartEntityBuilder builder = MultipartEntityBuilder.create();
			builder.setCharset(StandardCharsets.UTF_8);
			builder.setContentType(ContentType.MULTIPART_FORM_DATA.withCharset("utf-8"));
			builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
			builder.addPart(fileFiledName, new FileBody(file, ContentType.APPLICATION_JSON.withCharset("utf-8")));
			httpPost.setEntity(builder.build());
			HttpResponse response = httpClient.execute(httpPost);
			httpClient.close();
			if (response == null) {
				return R.fail(ResultCode.FAILURE.getCode(), "文件上传失败。");
			}

			HttpEntity res = response.getEntity();
			if (res == null) {
				return R.fail(ResultCode.FAILURE.getCode(), "文件上传异常。");
			}

			JSONObject jo = JSON.parseObject(EntityUtils.toString(res, StandardCharsets.UTF_8));
			if (jo == null) {
				return R.fail(ResultCode.FAILURE.getCode(), "无法解析上传结果。");
			}

			return R.data(ResultCode.SUCCESS.getCode(), jo, "");
		} catch (IOException e) {
			try {
				httpClient.close();
			} catch (IOException ep) {
				log.error(ep.getMessage(), ep);
				return R.fail(ResultCode.FAILURE.getCode(), ep.getMessage());
			}

			log.error(e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}
	}
}
