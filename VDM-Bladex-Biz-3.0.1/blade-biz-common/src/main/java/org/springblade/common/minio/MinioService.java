package org.springblade.common.minio;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.http.Method;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class MinioService {
	@Value("${minio.BucketName}")
	private String bucketName;

	@Value("${minio.Endpoint}")
	private String endpoint;

	@Value("${minio.AccessKey}")
	private String accessKey;

	@Value("${minio.SecretKey}")
	private String secretKey;

	@Value("${minio.proxy-prefix}")
	private String minioProxyPrefix;

	private MinioClient minioClient;

	@PostConstruct
	public void init() {
		minioClient = MinioClient.builder()
			.endpoint(endpoint)
			.credentials(accessKey, secretKey)
			.build();
	}

	/**
	 * 将数据流写到minio的excel文件
	 *
	 * @param menu
	 * @param inputStream
	 * @param size
	 * @return
	 */
	public String uploadFile(String menu, InputStream inputStream, long size) {
		Date date = new Date();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
		String dateStr = dateFormat.format(date);

		SimpleDateFormat timeFormat = new SimpleDateFormat("HHmmss");
		String timeStr = timeFormat.format(date);

		// 创建日期文件夹路径
		String folderPath = "export" + StrUtil.SLASH + dateStr;
		// 在文件夹路径下创建文件名
		String objectName = folderPath + StrUtil.SLASH + menu + "_" + dateStr + "_" + timeStr + ".xlsx";

		String uploadUrl = null;
		try {
			// 编码对象名称以确保URL的正确性
			minioClient.putObject(
				PutObjectArgs.builder()
					.bucket(bucketName)
					.object(objectName)
					.stream(inputStream, size, -1)
					.contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
					.build()
			);

			//生成预签名URL
			String preFileUrl = minioClient.getPresignedObjectUrl(
				GetPresignedObjectUrlArgs.builder()
					.method(Method.GET)
					.bucket(bucketName)
					.object(objectName)
					//设置文件有效期为5分钟（防止重复下载）
					.expiry(5, TimeUnit.MINUTES)
					.build()
			);

			// 构建文件上传后的URL
			String tailPath = preFileUrl.substring(findFourthSlashIndex(preFileUrl));
			uploadUrl =  minioProxyPrefix + bucketName + tailPath;
		} catch (Exception e) {
			// 处理异常
			e.printStackTrace();
		}
		return uploadUrl;
	}

	public String getObjectName(String fileName, InputStream inputStream, long size) {
		Date date = new Date();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
		String dateStr = dateFormat.format(date);

		SimpleDateFormat timeFormat = new SimpleDateFormat("HHmmss");
		String timeStr = timeFormat.format(date);

		// 创建日期文件夹路径
		String folderPath = "export" + StrUtil.SLASH + dateStr+ StrUtil.SLASH + timeStr;
		// 在文件夹路径下创建文件名
		String objectName = folderPath + StrUtil.SLASH +  fileName;
		try {
			// 编码对象名称以确保URL的正确性
			minioClient.putObject(
				PutObjectArgs.builder()
					.bucket(bucketName)
					.object(objectName)
					.stream(inputStream, size, -1)
					.contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
					.build()
			);
		} catch (Exception e) {
			// 处理异常
			e.printStackTrace();
		}
		return objectName;
	}

	public String getStreamObjectName(String fileName, InputStream inputStream, long size) {
		Date date = new Date();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
		String dateStr = dateFormat.format(date);

		SimpleDateFormat timeFormat = new SimpleDateFormat("HHmmss");
		String timeStr = timeFormat.format(date);

		// 创建日期文件夹路径
		String folderPath = "export" + StrUtil.SLASH + dateStr+ StrUtil.SLASH + timeStr;
		// 在文件夹路径下创建文件名
		String objectName = folderPath + StrUtil.SLASH +  fileName;
		try {
			// 编码对象名称以确保URL的正确性
			minioClient.putObject(
				PutObjectArgs.builder()
					.bucket(bucketName)
					.object(objectName)
					.stream(inputStream, size, -1)
					.contentType("application/octet-stream")
					.build()
			);
		} catch (Exception e) {
			// 处理异常
			e.printStackTrace();
		}
		return objectName;
	}

	public String getPresignedObjectUrl(String objectName) {
		String uploadUrl = null;
		try {
			//生成预签名URL
			String preFileUrl = minioClient.getPresignedObjectUrl(
				GetPresignedObjectUrlArgs.builder()
					.method(Method.GET)
					.bucket(bucketName)
					.object(objectName)
					//设置文件有效期为5分钟（防止重复下载）
					.expiry(5, TimeUnit.MINUTES)
					.build()
			);
			// 构建文件上传后的URL
			String tailPath = preFileUrl.substring(findFourthSlashIndex(preFileUrl));
			uploadUrl =  minioProxyPrefix + bucketName + tailPath;
		}catch (Exception e){
			// 处理异常
			e.printStackTrace();
		}
		return uploadUrl;
	}

	/**
	 * 指定文件名称上传到minio
	 * 上传普通文件，不指定格式
	 * @param fileName
	 * @param inputStream
	 * @param size
	 * @return
	 */
	public String uploadFileWithFileName(String fileName, InputStream inputStream, long size) {
		Date date = new Date();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
		String dateStr = dateFormat.format(date);

		SimpleDateFormat timeFormat = new SimpleDateFormat("HHmmss");
		String timeStr = timeFormat.format(date);

		// 创建日期文件夹路径
		String folderPath = "export" + StrUtil.SLASH + dateStr;
		// 在文件夹路径下创建文件名
		String objectName = folderPath + StrUtil.SLASH + fileName ;
		String uploadUrl = null;
		try {
			// 编码对象名称以确保URL的正确性
			minioClient.putObject(
				PutObjectArgs.builder()
					.bucket(bucketName)
					.object(objectName)
					.stream(inputStream, size, -1)
					//.contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
					.build()
			);

			//生成预签名URL
			String preFileUrl = minioClient.getPresignedObjectUrl(
				GetPresignedObjectUrlArgs.builder()
					.method(Method.GET)
					.bucket(bucketName)
					.object(objectName)
					//设置文件有效期为5分钟（防止重复下载）
					.expiry(5, TimeUnit.MINUTES)
					.build()
			);

			// 构建文件上传后的URL
			String tailPath = preFileUrl.substring(findFourthSlashIndex(preFileUrl));
			uploadUrl =  minioProxyPrefix + bucketName + tailPath;
		} catch (Exception e) {
			// 处理异常
			e.printStackTrace();
		}
		return uploadUrl;
	}


	private static int findFourthSlashIndex(String str) {
		int count = 0;
		for (int i = 0; i < str.length(); i++) {
			if (str.charAt(i) == '/') {
				count++;
				if (count == 4) {
					return i;
				}
			}
		}
		return -1; // 如果没有找到第四个'/'字符，则返回-1
	}

	/**
	 * 根据class的@ExcelProperty注解进行导出
	 * @param menu
	 * @param data
	 * @param clazz
	 * @return
	 * @throws IOException
	 */
	public String exportToMinIO(String menu, List<?> data, Class<?> clazz) throws IOException {
		// 生成 Excel 文件到内存
		ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
		EasyExcel.write(outputStream, clazz)
			.sheet(menu)
			.doWrite(data);

		// 获取文件内容
		byte[] excelBytes = outputStream.toByteArray();

		Date date = new Date();
		SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
		String dateStr = dateFormat.format(date);

		SimpleDateFormat timeFormat = new SimpleDateFormat("HHmmss");
		String timeStr = timeFormat.format(date);
		// 创建日期文件夹路径
		String folderPath = "import" + StrUtil.SLASH + dateStr;
		// 在文件夹路径下创建文件名
		String fileName = folderPath + StrUtil.SLASH + menu + "_" + dateStr + "_" + timeStr + ".xlsx";

		// 上传到 MinIO
		try {
			minioClient.putObject(
				PutObjectArgs.builder()
					.bucket(bucketName)
					.object(fileName)
					.stream(new ByteArrayInputStream(excelBytes), excelBytes.length, -1)
					.contentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
					.build());
			//fileName =  minioProxyPrefix + bucketName + StrUtil.SLASH + fileName;

			//生成预签名URL
			String preFileUrl = minioClient.getPresignedObjectUrl(
				GetPresignedObjectUrlArgs.builder()
					.method(Method.GET)
					.bucket(bucketName)
					.object(fileName)
					//设置文件有效期为5分钟（防止重复下载）
					.expiry(5, TimeUnit.MINUTES)
					.build()
			);

			// 构建文件上传后的URL
			String tailPath = preFileUrl.substring(findFourthSlashIndex(preFileUrl));
			fileName =  minioProxyPrefix + bucketName + tailPath;

		} catch (Exception e) {
			throw new IOException("Failed to upload file to MinIO", e);
		}

		return fileName;
	}

}
