package org.springblade.common.constant;

import org.springblade.core.launch.constant.AppConstant;

import static org.springblade.core.launch.constant.AppConstant.APPLICATION_NAME_PREFIX;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface LauncherConstant {

	//国能测试环境 code
	String TEST_GN_CODE = "test-gn";

	//国能压测环境
	String TEST_GN_STRESS_CODE = "test-gn-stress";

	//星航终端接入测试
	String XH_TEST_DEVICE = "xh-test-device";

	/**
	 * nacos namespace id
	 */
	String NACOS_NAMESPACE = "f447a694-519a-4255-95f9-bcbb5a5d6369";

	/**
	 * nacos dev 地址
	 */
	String NACOS_DEV_ADDR = "192.168.1.101:8848";
	//String NACOS_DEV_ADDR = "10.10.10.97:8848"; //数据权限验证

	/**
	 * nacos prod 地址（国能）
	 */
	String NACOS_PROD_ADDR = "192.169.137.80:8848";

	/**
	 * nacos test 地址（星航测试环境）
	 */
	String NACOS_TEST_ADDR = "10.10.10.208:8848";


	/**
	 * nacos test 地址 （国能测试环境，勿动）
	 */
	String NACOS_TEST_GN_ADDR = "192.200.32.166:8848";

	/**
	 * nacos 压测 地址 （国能压测环境，勿动）
	 */
	String NACOS_TEST_GN_STRESS_ADDR = "192.200.32.204:8848";

	String NACOS_XH_TEST_DEVICE_ADDR = "10.10.10.97:8848"; //星航设备接入测试


	/**
	 * sentinel dev 地址
	 */
	String SENTINEL_DEV_ADDR = "127.0.0.1:8858";

	/**
	 * sentinel prod 地址
	 */
	String SENTINEL_PROD_ADDR = "172.30.0.58:8858";

	/**
	 * sentinel test 地址
	 */
	String SENTINEL_TEST_ADDR = "172.30.0.58:8858";

	/**
	 * seata dev 地址
	 */
	String SEATA_DEV_ADDR = "127.0.0.1:8091";

	/**
	 * seata prod 地址
	 */
	String SEATA_PROD_ADDR = "172.30.0.68:8091";

	/**
	 * seata test 地址
	 */
	String SEATA_TEST_ADDR = "172.30.0.68:8091";

	/**
	 * dbuuo提供者
	 */
	String APPLICATION_DUBBO_PROVIDER_NAME = APPLICATION_NAME_PREFIX + "dubbo-provider";

	/**
	 * dbuuo消费者
	 */
	String APPLICATION_DUBBO_CONSUMER_NAME = APPLICATION_NAME_PREFIX + "dubbo-consumer";

	/**
	 * seata订单
	 */
	String APPLICATION_SEATA_ORDER_NAME = APPLICATION_NAME_PREFIX + "seata-order";

	/**
	 * seata库存
	 */
	String APPLICATION_SEATA_STORAGE_NAME = APPLICATION_NAME_PREFIX + "seata-storage";

	/**
	 * easypoi
	 */
	String APPLICATION_EASYPOI_NAME = APPLICATION_NAME_PREFIX + "easypoi";

	/**
	 * kafka
	 */
	String APPLICATION_KAFKA_NAME = APPLICATION_NAME_PREFIX + "kafka";

	/**
	 * rabbit
	 */
	String APPLICATION_RABBIT_NAME = APPLICATION_NAME_PREFIX + "rabbit";

	/**
	 * stream消费者
	 */
	String APPLICATION_STREAM_CONSUMER_NAME = APPLICATION_NAME_PREFIX + "stream-consumer";

	/**
	 * stream生产者
	 */
	String APPLICATION_STREAM_PROVIDER_NAME = APPLICATION_NAME_PREFIX + "stream-provider";

	/**
	 * seata file模式
	 */
	String FILE_MODE = "file";

	/**
	 * seata nacos模式
	 */
	String NACOS_MODE = "nacos";

	/**
	 * seata default模式
	 */
	String DEFAULT_MODE = "default";

	/**
	 * seata group后缀
	 */
	String GROUP_NAME = "-group";

	String DEV_NACOS_USERNAME = "nacos";
	String DEV_NACOS_PASSWORD="V@L1!lMclpY$ZsCX";

	String TEST_NACOS_USERNAME = "nacos";
	String TEST_NACOS_PASSWORD="jBvFrz7$mn";

	String TEST_GN_NACOS_USERNAME = "nacos";
	String TEST_GN_NACOS_PASSWORD = "oPVoqUR6M1";

	String TEST_GN_NACOS_STRESS_USERNAME = "nacos";
	String TEST_GN_NACOS_STRESS_PASSWORD = "oPVoqUR6M1";

	String PROD_NACOS_USERNAME = "bdsnacos";
	String PROD_NACOS_PASSWORD="D80o$zmC^DEFRkzd2";

	String DEV_NACOS_NAMESPACE = "dead9f84-fd3f-4856-b463-567f2ac60523";
	String TEST_NACOS_NAMESPACE = "488f4f21-bffc-47d3-b94e-5ac745564504";

	String TEST_GN_NACOS_NAMESPACE = "bdswz";

	String TEST_GN_NACOS_STRESS_NAMESPACE = "bdswz";
	String PROD_NACOS_NAMESPACE = "4792b021-1894-4933-8229-71109f69d323";

	String DEV_NACOS_GROUP = "DEFAULT_GROUP";
	String TEST_NACOS_GROUP = "BDS_WZ";
	String TEST_GN_NACOS_GROUP = "BDS_WZ";

	String TEST_GN_NACOS_STRESS_GROUP = "BDS_WZ";
	String PROD_NACOS_GROUP = "BDS_WZ";


	/**
	 * seata 服务组格式
	 *
	 * @param appName 服务名
	 * @return group
	 */
	static String seataServiceGroup(String appName) {
		return appName.concat(GROUP_NAME);
	}

	/**
	 * 动态获取nacos地址
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String nacosAddr(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return NACOS_PROD_ADDR;
			case (AppConstant.TEST_CODE):
				return NACOS_TEST_ADDR;
			case (TEST_GN_CODE):
				return NACOS_TEST_GN_ADDR;
			case (TEST_GN_STRESS_CODE):
				return NACOS_TEST_GN_STRESS_ADDR;
			case (XH_TEST_DEVICE):
				return NACOS_XH_TEST_DEVICE_ADDR;
			default:
				return NACOS_DEV_ADDR;
		}
	}

	/**
	 * 动态获取sentinel地址
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String sentinelAddr(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return SENTINEL_PROD_ADDR;
			case (AppConstant.TEST_CODE):
				return SENTINEL_TEST_ADDR;
			default:
				return SENTINEL_DEV_ADDR;
		}
	}



	static String nacosNamespace(String profile){
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return PROD_NACOS_NAMESPACE;
			case (AppConstant.TEST_CODE):
				return TEST_NACOS_NAMESPACE;
			case (TEST_GN_CODE):
				return TEST_GN_NACOS_NAMESPACE;
			case (TEST_GN_STRESS_CODE):
				return TEST_GN_NACOS_STRESS_NAMESPACE;
			default:
				return DEV_NACOS_NAMESPACE;
		}
	}

	static String nacosGroup(String profile){
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return PROD_NACOS_GROUP;
			case (AppConstant.TEST_CODE):
				return TEST_NACOS_GROUP;
			case (TEST_GN_CODE):
				return TEST_GN_NACOS_GROUP;
			case (TEST_GN_STRESS_CODE):
				return TEST_GN_NACOS_STRESS_GROUP;
			default:
				return DEV_NACOS_GROUP;
		}
	}


	/**
	 * 动态获取seata地址
	 *
	 * @param profile 环境变量
	 * @return addr
	 */
	static String seataAddr(String profile) {
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return SEATA_PROD_ADDR;
			case (AppConstant.TEST_CODE):
				return SEATA_TEST_ADDR;
			default:
				return SEATA_DEV_ADDR;
		}
	}

	static String nacosUsername(String profile){
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return PROD_NACOS_USERNAME;
			case (AppConstant.TEST_CODE):
				return TEST_NACOS_USERNAME;
			case (TEST_GN_CODE):
				return TEST_GN_NACOS_USERNAME;
			case (TEST_GN_STRESS_CODE):
				return TEST_GN_NACOS_STRESS_USERNAME;
			default:
				return DEV_NACOS_USERNAME;
		}
	}

	static String nacosPassword(String profile){
		switch (profile) {
			case (AppConstant.PROD_CODE):
				return PROD_NACOS_PASSWORD;
			case (AppConstant.TEST_CODE):
				return TEST_NACOS_PASSWORD;
			case (TEST_GN_CODE):
				return TEST_GN_NACOS_PASSWORD;
			case (TEST_GN_STRESS_CODE):
				return TEST_GN_NACOS_STRESS_PASSWORD;
			default:
				return DEV_NACOS_PASSWORD;
		}
	}

}
