package org.springblade.common.config;

import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;

import java.util.List;

/**
 * @Description: easyexcel 表头配置
 * @Author: zhouxw
 * @Date: 2022/10/8 11:52 AM
 */
public class ExcelExportFontAndStyleConfig extends HorizontalCellStyleStrategy {


	private final WriteCellStyle headWriteCellStyle;
	private final WriteCellStyle contentWriteCellStyle;

	/**
	 * 操作列
	 */
	private final List<Integer> columnIndexes;


	public ExcelExportFontAndStyleConfig(List<Integer> columnIndexes, WriteCellStyle headWriteCellStyle, WriteCellStyle contentWriteCellStyle) {
		this.headWriteCellStyle = headWriteCellStyle;
		this.contentWriteCellStyle = contentWriteCellStyle;
		this.columnIndexes = columnIndexes;
	}

	/**
	 * @description: 设置表头的单元格样式
	 * @author: zhouxw
	 * @date: 2022/11/3 10:25 AM
	 * @param:
	 * @return:
	 **/
	@Override
	protected void setHeadCellStyle(CellWriteHandlerContext context) {
		// 获取字体实例
		WriteFont headWriteFont = new WriteFont();
		//对符合表头的设置（excel中的第一行）
		if (columnIndexes.get(0).equals(context.getRowIndex())) {
			headWriteFont.setFontHeightInPoints((short) 14);
			headWriteFont.setBold(true);
			headWriteFont.setFontName("Calibri");
		} else {
			headWriteFont.setFontHeightInPoints((short) 10);
			headWriteFont.setBold(false);
			headWriteFont.setFontName("Arial");
		}
		headWriteCellStyle.setWriteFont(headWriteFont);
		headWriteCellStyle.setFillPatternType(FillPatternTypeEnum.SOLID_FOREGROUND.getPoiFillPatternType());
		headWriteCellStyle.setFillForegroundColor((short)9);

		if (stopProcessing(context)) {
			return;
		}
		WriteCellData<?> cellData = context.getFirstCellData();
		WriteCellStyle.merge(headWriteCellStyle, cellData.getOrCreateStyle());
	}

	/**
	 * @description: 设置数据行的单元格样式
	 * @author: zhouxw
	 * @date: 2022/11/3 10:26 AM
	 * @param: [context]
	 * @return: void
	 **/
	@Override
	protected void setContentCellStyle(CellWriteHandlerContext context) {
		WriteFont contentWriteFont = new WriteFont();
		contentWriteFont.setFontName("Arial");
		contentWriteFont.setFontHeightInPoints((short) 10);
		//设置数据填充后的实线边框
		contentWriteCellStyle.setWriteFont(contentWriteFont);
		contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);
		contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
		contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
		contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
		contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
		contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		WriteCellData<?> cellData = context.getFirstCellData();
		WriteCellStyle.merge(contentWriteCellStyle, cellData.getOrCreateStyle());
	}
}
