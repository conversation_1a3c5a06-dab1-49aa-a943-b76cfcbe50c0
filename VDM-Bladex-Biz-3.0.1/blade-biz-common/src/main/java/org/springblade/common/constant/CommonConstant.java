package org.springblade.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface CommonConstant {

	/**
	 * sword 系统名
	 */
	String SWORD_NAME = "sword";

	/**
	 * saber 系统名
	 */
	String SABER_NAME = "saber";

	/**
	 * 顶级父节点id
	 */
	Integer TOP_PARENT_ID = 0;

	/**
	 * 顶级父节点名称
	 */
	String TOP_PARENT_NAME = "顶级";


	/**
	 * 默认密码
	 */
	String DEFAULT_PASSWORD = "123456";


	/**
	 * @description: feigh接口前缀
	 * @author: zhouxw
	 * @date: 2023-05-146 15:08:25
	 * @param: * @param null
	 * @return:
	 * @return: null
	 **/
	String FEIGN_PREFIX = "/client";

	/* ******************** 服务名称 ******************** */

	String APP_NAME_ALARM = "vdm-alarm";

	/* ******************** == ******************** */

	/* ******************** 字典相关 ******************** */

	// 字典类型
	String DICT_LICENCE_COLOR = "licence_color";
	//车辆类型
	String DICT_VEHICLE_MODEL = "1";

	String DICT_VEHICLE_MODEL_LEVEL = "26";
	String DICT_VEHICLE_USE_TYPE = "2";
	String DICT_ACCESS_MODE = "access_mode";
	String DICT_ALARM_TYPE = "alarm_type";
	String DICT_ALARM_TYPE_SPECIAL = "alarm_type_special";  // 特殊报警类型
	String DICT_ALARM_LEVEL = "alarm_level";
	String DICT_OPERATE_STATE = "alarm_deal_state"; //报警处理状态
	String DICT_ROAD_TYPE = "road_type"; //道路类型
	String DICT_TERMINAL_TYPE = "5";
	String DICT_ONLINE_STATE = "te_state"; //车辆在线状态
	String DICT_COMPANY_MEASURES = "company_measures";

	/** 字典编码：设备型号 */
	String DICT_CODE_DEVICE_MODEL = "bdm_device_model";
	/** 字典编码：应用场景 */
	String DICT_CODE_SCENARIO = "bdm_app_sense";
	/** 字典编码：终端型号与终端（用途）类型映射关系 */
	String DICT_CODE_DEV_MODEL_USAGE_MAP = "device_model_usage_map";

	/** 报警监控消息推送TOPIC*/
	String DICT_ALARM_PUSH_MONITOR = "alarm_push_monitor"; //监控一览要推送的报警类型

	String ALARM_INFO_BATCH_0 = "0"; // 报警信息-0:正常报送，2：补报
	String DICT_ALARM_PUSH_MONITOR_DICT_KEY = "-1000"; //监控一览要推送的报警类型，字典key，防止数据库当作 0 处理


	String DICT_TERMINAL_TYPE_NEW = "device_type_new"; //终端类型


	//****************************报警类型**************************
	String DICT_ALARM_TYPE_HARDWARE_FAULT = "88"; //硬件故障报警
	String DICT_ALARM_TYPE_VIDEO = "91"; //部标视频类
	String DICT_ALARM_TYPE_STOP_ERR_MOVE = "112"; //报停异动报警


	//*************************系统默认值****************************
	String DEFAULT_TENANT_ID = "000000";  //默认租户id，用于跑批程序中的字典查询逻辑

	String COMPANY_MEASURES_CODE = "company_measures";
	String SERVICE_STATE_CODE = "enjoy_service";
	// 字典类型根节点
	String DICT_ROOT = "-1";

	// 服务角色
	/** 服务商角色*/
	String DICT_SERVICE_ROLE_SERVER = "1";
	/** 第三方监控角色*/
	String DICT_SERVICE_ROLE_THIRD = "2";
	/** 运输企业角色*/
	String DICT_SERVICE_ROLE_COMPANY = "3";

	// 报警处理操作类型 operateType
	/** 服务商用户查询*/
	String ALARM_INFO_SERVER_OPERATE_QUERY = "server_query";
	/** 服务商处理*/
	String ALARM_INFO_SERVER_OPERATE_DEAL = "server_deal";
	/** 服务商批量处理*/
	String ALARM_INFO_SERVER_OPERATE_DEAL_BATCH = "server_deal_batch";
	/** 服务商报警监控 认领*/
	String ALARM_INFO_SERVER_OPERATE_CLAIM = "server_claim";
	/** 服务商报警监控 释放*/
	String ALARM_INFO_SERVER_OPERATE_RELEASE = "server_release";
	/** 服务商报警监控 导出*/
	String ALARM_INFO_SERVER_OPERATE_EXPORT = "server_export";

	/** 第三方用户查询*/
	String ALARM_INFO_THIRD_OPERATE_QUERY = "third_query";
	/** 第三方处理*/
	String ALARM_INFO_THIRD_OPERATE_DEAL = "third_deal";
	/** 第三方批量处理*/
	String ALARM_INFO_THIRD_OPERATE_DEAL_BATCH = "third_deal_batch";
	/** 第三方报警处理 认领*/
	String ALARM_INFO_THIRD_OPERATE_CLAIM = "third_claim";
	/** 第三方报警处理 释放*/
	String ALARM_INFO_THIRD_OPERATE_RELEASE = "third_release";
	/** 第三方报警处理 导出*/
	String ALARM_INFO_THIRD_OPERATE_EXPORT = "third_export";
	/** 第三方报警处理 审核*/
	String ALARM_INFO_THIRD_OPERATE_AUDIT = "third_audit";

	/** 运输企业用户报警处理查询*/
	String ALARM_INFO_COMPANY_OPERATE_QUERY = "company_query";

	/** 运输企业用户报警处理*/
	String ALARM_INFO_COMPANY_OPERATE_DEAL = "company_deal";

	/** 运输企业用户报警处理批量处理*/
	String ALARM_INFO_COMPANY_OPERATE_DEAL_BATCH = "company_deal_batch";

	/** 运输企业用户报警处理 导出*/
	String ALARM_INFO_COMPANY_OPERATE_EXPORT = "company_export";
	/** 运输企业用户报警处理 申诉*/
	String ALARM_INFO_COMPANY_OPERATE_APPEAL = "company_appeal";

	/** 运输企业用户报警处理 开罚单*/
	String ALARM_INFO_COMPANY_OPERATE_PUNISH = "company_punish";

	/** 运输企业开具罚单 查询*/
	String ALARM_INFO_COMPANY_OPERATE_PUNISH_QUERY = "company_punish_query";

	/** 服务商、第三方误报处理*/
	String ALARM_INFO_SERVER_OPERATE_DEAL_WRONG = "server_deal_wrong";


	/* ******************** == ******************** */

	/* ******************** 基础信息相关 ******************** */

	// 角色相关
	String ROLE_ADMINISTRATOR = "administrator"; //超级管理员
	String ROLE_ADMIN = "admin"; //管理员
	String ROLE_SERVER = "server"; //服务商
	String ROLE_THIRD = "third"; //第三方
	String ROLE_BOTH = "both"; //既包含服务器上又包含第三方
	String ROLE_COMPANY = "company"; //企业

	// 账号相关
	long SERVER_AUTO_DEAL_ACCOUNT = 1669543092038295554L;
	long THIRD_AUTO_DEAL_ACCOUNT = 1669543337128255490L;

	//长时间未上线时间天数
	int LONG_OFFLINE_DAYS_COUNT = 15;


	/* ******************** == ******************** */

	//******************缓存相关************************************
	String CACHE_VEHICLE_STATE = "vehicle_state:";  //车辆在线数据

	List<Integer> ALARM_LEVEL_DISPLAY_LIMIT = Arrays.asList(1,2,3);
	String CACHE_DRIVER_INFO = "realTime:driver_info"; //驾驶员信息

	/**
	 *数据字典缓存，后面加字典类型
	 */
	String PREFIX_DICTIONARY_HASH_KEY = "DICTIONARY-TYPE:";

	//终端类型
	String CACHE_TERMINAL_TYPE = "bdm_device_type";

	//终端型号
	String CACHE_TERMINAL_MODEL = "terminal_model";

	//终端检测类型
	String CACHE_CHECK_TERMINAL_TYPE = "test_device_type";

	//北斗识别，告警推送抽稀时间间隔，防止前端卡顿
	String CACHE_MAP_PUSH_ALL_DURATION = "map_push_all_duration";


	//***********************岗位类型*****************************
	String DICT_CODE_JOB_TYPE = "0"; //岗位类型
	String DICT_KEY_JOB_TYPE_DRIVER = "0";  //驾驶员
	String DICT_KEY_JOB_TYPE_STEWARD = "1"; //乘务员




	//***********************平台版本******************************
	String PARAM_SYSTEM_VERSION = "system.version";

	//***********************websocket地址配置**********************************
	String PARAM_WEBSOCKET = "websocket.param";

	//***********************企业日报、月报***********************
	//日报
	String REPORT_TYPE_DAY = "day";
	//月报
	String REPORT_TYPE_MONTH = "month";

	//报警日报
	String REPORT_TYPE_DAY_ALARM = "alarm";
	//总结日报
	String REPORT_TYPE_DAY_REPORT = "report";
	//运行日报
	String REPORT_TYPE_DAY_RUNNING = "running";

	//拓展协议中的卫星信息
	Integer AUXS_STATELLITE_KEY = 255;

	//北斗检测中的userCode前缀
	String REDIS_PREFIX_BD_CHECK_USER_CODE = "ws::bdCheck::push::userCode:";

	//星历数据，存储在redis中，有效期2个小时
	String PREFIX_REDIS_SATE_DATA = "sate::data::";

	//*****************************终端在redis中的前缀********************
	String PREFIX_REDIS_TERMINAL = "Loc:"; //redis中的终端前缀
	String PREFIX_REDIS_ONLINETODAY = "OnLineToday:"; //今日上线总数

	String TodayOnline = "Today:Online:"; //今日上线总数

	/** redis键前缀：今日 */
	String RKEY_PREFIX_TODAY = "Today:";
	/** redis键前缀：上线 */
	String RKEY_PREFIX_ONLINE = "Online:";

	String PREFIX_REDIS_ONLINESTATE = "OnLineState:"; //在线总数

	String RealDevSte = "Real:DevSte"; //在线总数

	String PREFIX_REDIS_ONLINESTATE_DELAY = "OnLineState:Delay:"; //在线总数

	String ALARMFAULTNUM = "AlarmFaultNum:";

	//*******************redis中的最终位置点**************************
	/*String REDIS_PREFIX_LAST_POST_RNSS = "Loc:Rnss:"; //北斗定位终端
	String REDIS_PREFIX_LAST_POST_RDSS = "Loc:Rdss:"; //北斗短报文
	String REDIS_PREFIX_LAST_POST_WEARABLE = "Loc:Wearable:"; //北斗穿戴式终端
	String REDIS_PREFIX_LAST_POST_MONIT = "Loc:Monit:"; //北斗监控终端
	String REDIS_PREFIX_LAST_POST_PNT = "Loc:Pnt:"; //北斗授时终端*/
	String REDIS_CACHE_LAST_POST = "Real:Loc";


	//****************************终端类型**************************
	String TERMINAL_TYPE_VEHICLE = "车载终端"; //车载终端 名称



	//****************************kafka topic*********************
	//入网之后的北斗实时检测结果
	String TOPIC_TERMINAL_LOC_CHECK_RESULT = "TerminalLocCheckResult";

	//入网之前的北斗识别检测结果
	String TOPIC_TERMINAL_CHECK_RESULT = "TerminalCheckResult";


	//****************************北斗识别****************************
	//终端识别结果
	Integer TERMINAL_CHECK_RES_BD = 1; //识别通过
	Integer TERMINAL_CHECK_RES_NO_BD = 2; //识别不通过

	//定位点识别结果
	String LOCATION_CHECK_RES_BD = "1"; //识别为北斗
	String LOCATION_CHECK_RES_NO_BD = "0"; //识别为非北斗

	//报告结果
	Integer REPORT_CHECK_RES_BD = 1; //识别通过
	Integer REPORT_CHECK_RES_NO_BD = 2; //识别不通过

	//识别阈值：超过该阈值，则认为终端为单北斗终端；否则，认为疑似非单北斗终端
	String PARAM_BD_CHECK_THRESHOLD = "bd_check_threshold";

	String CHECK_PROCESS_BEFORE_CHECK = "0"; //未开始检测

	String CHECK_PROCESS_INTERFACE_CHECKING = "1"; //接口检测

	String CHECK_PROCESS_BD_CHECKING = "2"; //北斗检测
	String CHECK_PROCESS_FINISH = "3"; //检测完成

	String PARAM_CHECK_REPORT_PREFIX = "bd_check_download_prefix"; //报告下载路径前缀（平台参数）


	//***************状态字段************************
	String STATE_U = "U"; //有效
	String STATE_E = "E"; //无效


	String REPORT_NO_PREFIX = "CETB-";


	//****************设备类型*********************
	String DEVICE_TYPE_RNSS = "1"; //北斗定位终端
	String DEVICE_TYPE_WEARABLE = "2"; //北斗穿戴式终端
	String DEVICE_TYPE_RDSS = "3"; //北斗短报文终端
	String DEVICE_TYPE_MONIT = "4"; //北斗监测终端
	String DEVICE_TYPE_PNT = "5"; //北斗授时终端

	// 告警消息类型
	String KAFKA_ALARM_MSG_KEY_ADD = "add"; // 添加告警

	String DEFAULT_TOKEN_KEY = "token::default";


	//*******************加解密*************************
	//位置平台id
	Long DEFAULT_SYSTEM_ID = 0L;

	//用于加密私钥
	String SM4_KEY_FOR_PRIVATE_KEY = "sGPlBdo9dSpjZKE5mZlvqg==";
	//私钥缓存
	String CACHE_KEY_PRIVATE = "private_key";
	//公钥缓存
	String CACHE_KEY_PUBLIC = "public_key:";
	//第三方平台的公钥
	String CACHE_KEY_THIRD_PUBLIC = "third_public_key:";
	//缓存存在时间（秒）
	Long CACHE_KEY_TTL = 24 * 3600L;

	//*******************平台协议对接：接口编码*******************
	String BUSI_CODE_ONLINE = "RA0001"; //终端上线，支持MQTT和HTTP
	String BUSI_CODE_LOCATION = "RA0002"; //位置数据上报，支持MQTT和HTTP
	String BUSI_CODE_ALARM = "RA0003"; //告警数据上报，支持MQTT和HTTP
	String BUSI_CODE_OFFLINE = "RA0004"; //终端下线，支持MQTT和HTTP
	String BUSI_CODE_TERMINAL_STATE = "RA0005"; //终端状态信息，支持MQTT和HTTP

	String BUSI_CODE_VEHICLE = "RB0001"; //车辆信息，支持HTTP
	String BUSI_CODE_STAFF = "RB0002"; //职工信息，支持HTTP
	String BUSI_CODE_FACILITY = "RB0003"; //基础设施信息，支持HTTP
	String BUSI_CODE_TERMINAL = "RB0004"; //终端信息，支持HTTP
	String BUSI_CODE_TRAIN_CARGO_BOX = "RB0005"; // 铁路货车车厢信息，支持HTTP
	String BUSI_CODE_MINE_TRUCK = "RB0006"; //矿用卡车信息，支持HTTP
	String BUSI_CODE_PRECISION_ASSEMBLY = "RB0007"; //精密装备信息，支持HTTP
	String BUSI_CODE_SHIP = "RB0008"; //货船信息，支持HTTP
	String BUSI_CODE_KEY_SEARCH = "RS0001"; //密钥查询，支持HTTP
	String BUSI_CODE_KEY_UPLOAD = "RS0002"; //密钥上报，支持HTTP

	//******************平台协议************************
	//******************平台协议对应的kafka topic*************
	String MESSAGE_SEND_TOPIC = "message_send";

	//与短报文、高精度的业务数据同步
	String MESSAGE_BUSI_SYNC_TOPIC = "sync_device_data";

	//与基础服务平台的平台数据同步，包括组织、人员信息
	String MESSAGE_PLAT_SYNC_TOPIC = "sync_base_data";

	//终端上下线数据topic
	String MESSAGE_SEND_TERMIANL_ONLINE_OFFLINE = "terminalstatus";

	//接口日志topic--kafka
	String TOPIC_INTER_LOG = "interface_log";
	//mqtt 推送日志
	String TOPIC_MQTT_PUSH_LOG = "mqtt_push_log";
	//位置平台 平台对接接口编码
	String VDM_PLAT_COLLECTION_INTER_CODE = "plat_collect";
	//位置平台 平台对接接口名称
	String VDM_PLAT_COLLECTION_INTER_NAME = "平台协议-位置平台接口";


	//mqtt中用于接收数据的topic前缀
	String MQTT_PREFIX = "collect/";

	//平台对平台协议接收的转kafka的topic
	String TOPIC_MESSAGE_COLLECT_TO_KAFKA = "message_collect";

	//位置kafka
	String TOPIC_LOCATION = "GuoNeng_position_data";

	String REDIS_HASH_DEVICE_STATE = "device_state";


	//位置平台id
	String POS_SYSTEM_ID = "000000";

	//上线缓存，redis前缀
	String REDIS_KEY_ONLINE_STATE = "Real:DevSte";

	//今日上线终端
	String REDIS_KEY_PREFIX_TODAY_ONLINE = "Today:Online:";

	//终端状态 kafka topic
	String KAFKA_TOPIC_TERMINAL_STATUS = "terminalstatus";

	//页面对接时，加密串前缀
	String REDIS_KEY_PREFIX_ENCRYPT_KEY = "encrypt:";

	//****************告警来源*********************
	int ALARM_ORIGIN_SECONDARY = 3; //外部平台

	//***********************平台协议：操作类型****************************
	String OPER_TYPE_ADD = "A"; //新增
	String OPER_TYPE_UPDATE = "U"; //修改
	String OPER_TYPE_DELETE = "D"; //删除

	//todo test
	String OPER_TEST = "test";

	//****************目标类型*********************
	int TARGET_TYPE_VEHICLE = 1; //车辆
	int TARGET_TYPE_WORKER = 2; //人员
	int TARGET_TYPE_FACILITY = 3; //基础设施
	int TARGET_TYPE_CONTAINER = 4; //集装箱
	int TARGET_TYPE_TEMP = 5; //外包人员
	int TARGET_TYPE_VISITOR = 6; //访客


	//********************* 目标类别（迁移自vdm-base-info） ****************************
	//待分配终端对象
	public static final Integer VIRTUAL_TARGET_TYPE = 0;
	public static final Integer VEHICLE_TARGET_TYPE = 1;
	public static final Integer WORKER_TARGET_TYPE = 2;
	public static final Integer FACILITY_TARGET_TYPE = 3;
	public static final Integer CONTAINER_TARGET_TYPE = 4;
	public static final Integer TEMPORARY_TARGET_TYPE = 5;
	public static final Integer VISITOR_TARGET_TYPE = 6;
	public static final Integer SHIP_TARGET_TYPE = 7;
	public static final Integer BOX_TARGET_TYPE = 8;
	public static final Integer PRECISION_TARGET_TYPE = 9;
	public static final Integer TRUCK_TARGET_TYPE = 10;

	public static final String BASEINFO_DEVICE = "baseinfo_device";

	public static final String BASEINFO_TARGET = "baseinfo_target";


	//***************平台协议上报的终端类型（与系统字典不一致，这里只适用于平台协议）*****************************
	String PLAT_DEVICE_TYPE_RNSS = "10"; //北斗定位终端
	String PLAT_DEVICE_TYPE_WEARABLE = "20"; //北斗穿戴式终端
	String PLAT_DEVICE_TYPE_RDSS = "30"; //北斗短报文终端
	String PLAT_DEVICE_TYPE_MONIT = "40"; //北斗监测终端
	String PLAT_DEVICE_TYPE_PNT = "50"; //北斗授时终端


	//*****************接口访问方向***********************************
	Integer INTER_DIRECTION_POS = 1; //访问位置平台
	Integer INTER_DIRECTION_OUT = 2; //访问外部平台


	//接口信息前缀
	String CACHE_POS_INTER_PREFIX = "in_pos_interface:";

	//系统与业务对象的映射关系
	//业务对象id-外部平台id
	String CACHE_INTER_SYSTEM_OBJECT = "inter_system_object:";

	//外部平台缓存
	//外部平台id-外部平台信息
	String CACHE_INTER_SYSTEM = "inter_system:";

	//终端信息
	String CACHE_DEVICE = "device_info:";

	//业务对象信息(redis，key)
	String CACHE_PREFIX_BDM_ABSTRACT_TARGET = "bdm:abstract:target:";

	//业务字典key
	String CACHE_HASH_DICT_TREE = "dict:tree:000000";

	//在线保持field（业务字典）
	String CACHE_FIELD_KEEP_ONLINE_TIME = "online_category_nodisplay";

	//默认在线保持时间，单位秒
	Integer DEFAULT_KEEP_ONLINE_TIME = 300;

	//*****************国能数据权限****************************
	String CE_DATA_AUTH_PERSON = "1"; //国能数据权限，本人
	String CE_DATA_AUTH_ORG = "2"; //国能数据权限，组织列表


	//******************定时任务相关*********************************
	String TASK_EXECUTING_PREFIX = "TASK_EXECUTING:"; //跑批任务执行标记前缀

	String CLEAR_BD_CHECK_REAL_BEFORE = "CLEAR_BD_CHECK_REAL_BEFORE"; //删除3天之前的北斗实时检测数据

	String STAT_BD_CHECK_REAL_BEFORE = "STAT_BD_CHECK_REAL_BEFORE"; //统计之前的实时北斗检测情况

	String SYNC_TARGET_ODOMETER_FROM_CACHE_TO_DB = "SYNC_TARGET_ODOMETER_FROM_CACHE_TO_DB"; //从缓存同步累计里程数据至数据库

	String SYNC_LOC_COLLECT_FROM_CACHE_TO_DB = "SYNC_LOC_COLLECT_FROM_CACHE_TO_DB"; //从缓存同步定位采集数据至数据库

	String SYNC_LOC_SHARE_FROM_CACHE_TO_DB = "SYNC_LOC_SHARE_FROM_CACHE_TO_DB"; //从缓存同步定位共享数据至数据库

	String UPDATE_BD_SATELLITES_DATA = "UPDATE_BD_SATELLITES_DATA"; //定时更新北斗数据

	String SAVE_ALARM_DEVICE_ID_FROM_REDIS = "SAVE_ALARM_DEVICE_ID_FROM_REDIS"; //定时保存redis中记录的发生告警的终端id

	String COMMON_TRUE = "true";

}
