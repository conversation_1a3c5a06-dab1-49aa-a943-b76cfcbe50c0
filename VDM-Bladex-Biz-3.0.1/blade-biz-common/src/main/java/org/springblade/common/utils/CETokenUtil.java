package org.springblade.common.utils;

import cn.hutool.http.HttpRequest;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.client.naming.NacosNamingService;
import com.alibaba.nacos.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.tool.utils.WebUtil;
import org.springblade.entity.DataAuthCE;
import org.springblade.entity.DataAuthEntity;
import org.springblade.system.feign.ISysClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 国能token工具类
 */
@Component
@Slf4j
public class CETokenUtil {

	//**************************国能权限相关****************************
	String CE_USERNAME = "CE_USERNAME"; //底座访问时，底座用户的登录用户名，全局唯一
	String CE_TOKEN = "Ce-Auth"; //header中的国能token key
	String HEADER_GN_DATA_AUTH_TYPE = "gn_data_auth_type"; //header中的参数名称，国能数据权限类型
	String REDIS_KEY_PREFIX_AUTH_DATA_ORG_LIST = "authority_gn_org_list:data:"; //数据权限，redis中的组织列表 key前缀，value是组织列表
	String REDIS_KEY_PREFIX_AUTH_DATA_USER_ID = "authority_gn_user_id:data:"; //数据权限，redis中的人员id key前缀，value是人员id
	Integer REDIS_TTL_AUTH_DATA = 5; //数据权限在redis中的有效期，5分钟
	String ADMIN_USER = "admin_user"; // 管理员用户，参数配置中配置

	@Autowired
	private NacosServiceManager nacosServiceManager;

	@Autowired
	private NacosDiscoveryProperties nacosDiscoveryProperties;


	@Resource
	private NacosNamingService nacosNamingService;

	@Resource
	private ISysClient sysClient;

	//数据权限配置
	//用于请求数据权限接口的配置
	@Value("${ce.auth.data-auth.ip}")
	private String dataAuthIp;
	@Value("${ce.auth.data-auth.port}")
	private String dataAuthPort;
	@Value("${ce.auth.data-auth.url}")
	private String dataAuthUrl;
	@Value("${ce.auth.data-auth.workSpaceCode}")
	private String dataAuthWorkspaceCode;
	@Value("${ce.auth.data-auth.cache-ttl}")
	private Integer dataAuthCacheTTL;

	//国能平台校验token接口
	//调用国能平台校验token接口时，header中token的key
	@Value("${ce.token.header.key}")
	private String ceHeaderKey;


	@Resource
	private StringRedisTemplate stringRedisTemplate;


	private static Instance instance;

	/**
	 * 判断当前请求是否是从底座中发起的
	 * @return true 是国能token
	 */
	public boolean isCELogin(){
		HttpServletRequest request = WebUtil.getRequest();
		if (request == null) {
			log.info("request为空");
			return false;
		}
		//当是从底座登录时，会在gateway中向request的header中写入client_type = gn
		String type = request.getHeader("client_type");
		log.info("从header中获取client_type的值为：" + type);
		if("gn".equals(type)){
			return true;
		}else{
			return false;
		}
	}

	public  boolean isAdmin(){
		String userAccount = AuthUtil.getUserAccount();
		R<String> result = sysClient.getParamValue(ADMIN_USER);
		if (result.isSuccess()) {
			String data = result.getData();
			if (data.contains(userAccount)) {
				return true;
			}
		}

		return AuthUtil.isAdministrator() || AuthUtil.isAdmin();
	}

	/**
	 * 获取数据权限
	 * 判断是否是国能底座登录，如果是的话，就获取国能数据权限；如果不是，则查询当前的bladex数据权限
	 * @return
	 */
	public DataAuthCE getDataAuth(){
		log.info(">>==将要获取数据权限");
		if(isCELogin()){
			log.info(">>==国能登录，将要获取国能数据权限");
			//如果是国能底座登录，则查询国能数据权限
			DataAuthCE da = getCEDataAuth();
			da.setIsSuper(false);
			log.info("获取到的国能数据权限为：" + (da == null?"":"type="+da.getGnDataAuthType()+"," + "account=" +da.getAccount()+","+"isSuper = "+da.getIsSuper()+", org 数量 =  "+(da.getOrgList()==null?0:da.getOrgList().size())));
			return da;
		}else{
			log.info(">>==位置平台登录");
			//从nacos中获取blade-system服务的地址
			/*if(namingService == null){
				namingService = nacosServiceManager.getNamingService(nacosDiscoveryProperties.getNacosProperties());
			}*/
			//Instance instance = this.instance;
			if(instance == null){
				try {
					//instance = namingService.selectOneHealthyInstance("blade-system", nacosDiscoveryProperties.getGroup());
					//this.instance = instance;
					instance = nacosNamingService.selectOneHealthyInstance("blade-system", nacosDiscoveryProperties.getGroup());
				} catch (NacosException e) {
					e.printStackTrace();
				}
			}

			//如果是超级管理员，就返回所有的组织信息
			if(AuthUtil.isAdministrator()){
				log.info(">>==位置平台登录：超级管理员");
				String findDeptUrl = "http://" + instance.getIp() + ":" + instance.getPort() +"/client/dept-ids-all";
				log.info(">>==请求 blade-system，url is " + findDeptUrl);
				String body = HttpRequest.get(findDeptUrl)
					.execute().body();
				JSONObject json = JSON.parseObject(body);
				DataAuthCE dataAuth = new DataAuthCE();
				dataAuth.setIsSuper(true);
				if("200".equals(json.get("code")+"")){
					//如果请求成功
					log.info(">>==请求 blade-system 成功，结果为："+ body);
					String orgArrayStr = json.getString("data");
					List<Long> deptIds = Func.toLongList(orgArrayStr);
					List<String> deptIdArray = deptIds.stream().map(item -> item.toString()).collect(Collectors.toList());
					dataAuth.setGnDataAuthType("2");
					dataAuth.setOrgList(deptIdArray);
					dataAuth.setOrgListStr(getOrgListStr(dataAuth.getOrgList()));
				}else{
					//如果请求失败，则设置为仅本人，确保数据安全
					log.error("请求组织接口失败，将设置为仅本人");
					dataAuth.setIsSuper(false);
					dataAuth.setGnDataAuthType("1");
					dataAuth.setAccount(AuthUtil.getUserAccount());
				}
				return dataAuth;
			}
			//如果不是国能底座登录，并且不是超级管理员，则查询bladex数据权限(用户的监管机构)
			log.info(">>==位置平台登录：非超级管理员");
			Long userId = AuthUtil.getUserId();

			String findDeptUrl = "http://" + instance.getIp() + ":" + instance.getPort() +"/client/dept-regulate-by-user-id?userId="+userId;
			String body = HttpRequest.get(findDeptUrl)
				.execute().body();
			JSONObject json = JSON.parseObject(body);
			DataAuthCE dataAuth = new DataAuthCE();
			dataAuth.setIsSuper(false);
			dataAuth.setGnDataAuthType(DataAuthCE.CE_DATA_AUTH_TYPE_ORG);
			if("200".equals(json.get("code")+"")){
				//如果请求成功
				String orgArrayStr = json.getString("data");
				List<String> deptIdArray = JSON.parseArray(orgArrayStr, String.class);
				dataAuth.setOrgList(deptIdArray);
				dataAuth.setOrgListStr(getOrgListStr(dataAuth.getOrgList()));
			}else{
				//如果请求失败，则只能查询自己的数据，保证数据安全
				log.error("查询组织接口失败，将设置为仅本人");
				dataAuth.setGnDataAuthType(DataAuthCE.CE_DATA_AUTH_TYPE_SELF);
				dataAuth.setAccount(AuthUtil.getUserAccount());
				dataAuth.setIsSuper(false);
			}
			return dataAuth;
		}
	}


	/**
	 * 获取国能数据权限
	 * @return
	 */
	private DataAuthCE getCEDataAuth(){
		String ceUsername = AuthUtil.getUserAccount();
		HttpServletRequest request = WebUtil.getRequest();
		String ceToken = request.getHeader(CE_TOKEN);

		//1.1 获取redis中保存的数据权限
		//判断权限是否是人员
		String ceUserIdCache = stringRedisTemplate.opsForValue().get(REDIS_KEY_PREFIX_AUTH_DATA_USER_ID + ceUsername);
		String ceOrgListCache = stringRedisTemplate.opsForValue().get(REDIS_KEY_PREFIX_AUTH_DATA_ORG_LIST + ceUsername);
		DataAuthCE dataAuth = new DataAuthCE();
		if(StringUtils.isNotBlank(ceOrgListCache)){
			//如果组织列表不是空，则认为数据权限是组织
			//数据权限只区分本人和其他，这里设置为 本单位及下属 2
			dataAuth.setGnDataAuthType("2");
			dataAuth.setOrgList(JSON.parseArray(ceOrgListCache, String.class));
			dataAuth.setOrgListStr(getOrgListStr(dataAuth.getOrgList()));
		}else if(StringUtils.isNotBlank(ceUserIdCache)){
			//如果人员id不是空，则认为数据权限是本人
			dataAuth.setGnDataAuthType("1");
			dataAuth.setAccount(AuthUtil.getUserAccount());
		}else{
			//1.2 如果redis中存储的人员和组织的数据权限信息都是空，则查询数据权限接口
			DataAuthEntity entity = new DataAuthEntity();
			try{
				//调用数据权限接口
				entity = callDataAuth(ceToken);
			}catch (Exception e){
				log.error("调用国能数据权限接口失败",e);
			}

			if(entity.getAuthType() == 5){
				log.info("国能用户数据权限为 仅本人");
				//如果是包含，则直接把组织列表写入到redis
				stringRedisTemplate.opsForValue().set(REDIS_KEY_PREFIX_AUTH_DATA_USER_ID + ceUsername, ceUsername);
				//设置有效期
				stringRedisTemplate.expire(REDIS_KEY_PREFIX_AUTH_DATA_USER_ID + ceUsername, dataAuthCacheTTL, TimeUnit.SECONDS);//设置有效期
				dataAuth.setAccount(ceUsername);
				dataAuth.setGnDataAuthType("1");
			}else{
				//如果是组织权限，则向redis中写入组织列表
				dataAuth.setGnDataAuthType("2");
				//判断包含和不包含
				if(entity.getOperation() == 1){
					//如果是包含，则直接把组织列表写入到redis
					stringRedisTemplate.opsForValue().set(REDIS_KEY_PREFIX_AUTH_DATA_ORG_LIST + ceUsername, JSON.toJSONString(entity.getAuthOrgList()));
					//设置有效期
					stringRedisTemplate.expire(REDIS_KEY_PREFIX_AUTH_DATA_ORG_LIST + ceUsername, dataAuthCacheTTL, TimeUnit.SECONDS);//设置有效期
					dataAuth.setOrgList(entity.getAuthOrgList());
					dataAuth.setOrgListStr(getOrgListStr(dataAuth.getOrgList()));
				}else{
					//如果是不包含，则排除组织列表里边的数据后，再写入到redis
					//查询所有组织的id
					//从nacos中获取blade-system服务的地址
					/*if(namingService == null){
						namingService = nacosServiceManager.getNamingService(nacosDiscoveryProperties.getNacosProperties());
					}*/
					//Instance instance = null;
					if(instance == null){
						try {
							instance = nacosNamingService.selectOneHealthyInstance("blade-system", nacosDiscoveryProperties.getGroup());
						} catch (NacosException e) {
							e.printStackTrace();
						}
					}
					String findDeptUrl = "http://" + instance.getIp() + ":" + instance.getPort() +"/client/dept-ids-all";
					String body = HttpRequest.get(findDeptUrl)
						.execute().body();
					String[] deptIds = body.split(",");
					List<String> deptList = new ArrayList<>();
					deptList.addAll(Arrays.asList(deptIds));
					//获取要查询的id
					List<String> noContain = entity.getAuthOrgList();
					deptList.removeAll(noContain);
					//向redis中写入组织列表
					stringRedisTemplate.opsForValue().set(REDIS_KEY_PREFIX_AUTH_DATA_ORG_LIST + ceUsername, JSON.toJSONString(deptList));
					//设置有效期
					stringRedisTemplate.expire(REDIS_KEY_PREFIX_AUTH_DATA_ORG_LIST + ceUsername, dataAuthCacheTTL, TimeUnit.SECONDS);
					dataAuth.setOrgList(deptList);
					dataAuth.setOrgListStr(getOrgListStr(deptList));
				}
			}
		}
		return dataAuth;
	}

	private String getOrgListStr(List<String> list){
		if(list == null || list.size() < 1){
			return "";
		}
		StringBuffer sb = new StringBuffer();
		sb.append("'{");
		for(String item : list){
			sb.append(item).append(",");
		}
		String res = sb.substring(0, sb.length()-1);
		res = res + "}'";
		return res;
	}


	/**
	 * 调用数据权限接口
	 * @param ceToken 国能token字符串
	 * @return 权限数据
	 * @throws Exception
	 */
	private DataAuthEntity callDataAuth(String ceToken) throws Exception{
		if(StringUtils.isBlank(ceToken)){
			log.error("调用国能数据权限接口失败：带有的CE token 为空");
			throw new Exception("调用国能数据权限接口失败：未获取到ce token");
		}
		//请求国能数据权限接口
		String body = "";
		try {
			String url = "http://"+dataAuthIp+":"+dataAuthPort+dataAuthUrl;
			log.info("数据权限接口url = "+ url);
			log.info("工作空间编码 = "+dataAuthWorkspaceCode);
			if(ceToken.contains(" ")){
				ceToken = ceToken.split(" ")[1];
			}
			//请求参数
			Map<String,String> requestBody = new HashMap<>();
			requestBody.put("token",ceToken);
			requestBody.put("workSpaceCode", dataAuthWorkspaceCode);
			body = HttpRequest.post(url).header(ceHeaderKey, ceToken)
				.body(JSON.toJSONString(requestBody))
				.execute().body();
		}catch (Exception e){
			log.error("-->> 调用国能数据权限接口失败",e);
			throw new Exception(e);
		}
		log.info("-->> 调用国能数据权限接口接口，返回的数据为："+ body);
		JSONObject jsonObject = JSON.parseObject(body);
		Object data = jsonObject.get("result");
		String success = jsonObject.get("success")==null?"":jsonObject.get("success").toString();
		DataAuthEntity dataAuth = null;
		if(data != null){
			dataAuth = JSON.parseObject(JSON.toJSONString(data), DataAuthEntity.class);
		}
		if("true".equals(success)){
			log.info("-->> 请求国能数据权限接口成功");
			return dataAuth;
		}else{
			throw new Exception("请求国能数据权限接口失败");
		}
	}
}
