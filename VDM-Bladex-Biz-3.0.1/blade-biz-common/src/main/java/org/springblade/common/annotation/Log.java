package org.springblade.common.annotation;

import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 * <AUTHOR>
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {

	/**
	 * 模块
	 */
	String menu() default "";

    /**
	 * 被操作对象类别
	 */
	ObjectType objectType() default ObjectType.OTHER;

	/**
	 * 功能
	 */
	Operation operation() default Operation.OTHER;

	/**
	 * 是否保存请求的参数
	 */
	boolean isSaveRequestData() default true;

	/**
	 * 是否保存响应的参数
	 */
	boolean isSaveResponseData() default true;

	/**
	 * 排除指定的请求参数
	 */
	String[] excludeParamNames() default {};

}
