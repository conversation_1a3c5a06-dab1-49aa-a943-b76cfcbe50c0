package org.springblade.common.constant;

// redis key
public interface RedisConstant {

	String HASH_BASE_USER = "baseUser:";
	String HASH_DEPT_NODE = "deptNode:";
	String HASH_BASE_INFO_TARGET = "baseinfo_target";
	String HASH_BASE_INFO_DEVICE = "baseinfo_device";
	String STRING_DEVICE_NUM_SEQ = "bdcheck:coding:device_num_seq";
	String HASH_DEVICE_NUM_CHECK = "bdcheck:coding:device_num_check";
	String HASH_TARGET_ODOMETER = "target:odometer";
	String HASH_TARGET_LOCATION_COLLECT = "target:loc_collect";
	String HASH_TARGET_LOCATION_SHARE = "target:loc_share";
	/** 用户-板块:国能组织视图缓存 */
	String VALUE_GN_USER_SECTOR_ORG = "gnProd:userSector-org:";
}
