package org.springblade.common.utils;

import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel(value = "请求体：报警情况汇总一览表")
@Data
public class ReportVO {

	@ColumnWidth(100)
	private String column1;


	@ColumnWidth(100)
	private String column2;

	@ColumnWidth(100)
	private String column3;


	@ColumnWidth(100)
	private String column4;


	@ColumnWidth(100)
	private String column5;


	@ColumnWidth(100)
	private String column6;

	@ColumnWidth(100)
	private String column7;

}
