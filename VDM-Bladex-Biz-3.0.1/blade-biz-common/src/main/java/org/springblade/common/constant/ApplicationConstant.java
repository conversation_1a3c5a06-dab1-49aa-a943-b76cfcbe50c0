package org.springblade.common.constant;

/**
 * @Description: 应用服务参数
 * @Author: zhouxw
 * @Date: 2023/5/26 14:55
 */
public interface ApplicationConstant {

	/**
	 * @description: 大数据服务
	 * @author: zhouxw
	 * @date: 2023-05-146 14:56:42
	 * @param: * @param null
	 * @return:
	 * @return: null
	 **/
	String APPLICATION_BIG_DATA = "vdm-big-data";

	/**
	 * @description: 北斗检测
	 * @author: zhouxw
	 * @date: 2023-05-146 14:56:42
	 * @param: * @param null
	 * @return:
	 * @return: null
	 **/
	String APPLICATION_BD_CHECK = "vdm-bd-check";

	/**
	 * 接口管理
	 */
	String APPLICATION_INTER_MANAGER = "vdm-inter-manager";

	/**
	 * 接口管理--认证服务
	 */
	String APPLICATION_INTER_AUTH = "vdm-inter-auth";

	/**
	 * @description: 统计分析服务
	 * @author: zhouxw
	 * @date: 2023-06-159 10:12:41
	 * @param: * @param null
	 * @return:
	 * @return: null
	 **/
	String APPLICATION_STATISTIC = "vdm-statistic";

	/**
	 * @description: websocket服务
	 * @author: zhouxw
	 * @date: 2023-06-181 10:17:31
	 * @param: * @param null
	 * @return:
	 * @return: null
	 **/
	String APPLICATION_WEBSOCKET = "vdm-websocket";

	/**
	 * @description: 第三方平台服务
	 * @author: hebin
	 * @date: 2024-04-17 16:46:31
	 **/
	String APPLICATION_THIRD_PLATFORM = "vdm-third-platform";

	/**
	 * @description: 基础信息
	 * @author: liyu
	 **/
	String APPLICATION_BASE_INFO = "vdm-base-info";





	//********************** go服务 ***********************************
	/**
	 * @description: 基础信息服务
	 * @author: zhouxw
	 * @date: 2023-06-158 17:26:26
	 * @param: * @param null
	 * @return:
	 * @return: null
	 **/
	String APPLICATION_GO_BASEINFO = "go-baseinfo";

	/**
	 * @description: 终端服务
	 * @author: hebin
	 * @date: 2023-06-14 20:44:00
	 * @param: * @param null
	 * @return: null
	 **/
	String APPLICATION_GO_TERMINAL = "go-regulatoryCenter";


	/**
	 * 安全管理服务
	 */
	String APPLICATION_GO_SECURITY = "go-security";


}
