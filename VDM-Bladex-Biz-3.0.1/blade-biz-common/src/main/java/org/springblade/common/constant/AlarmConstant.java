package org.springblade.common.constant;

public interface AlarmConstant {

	/* ******************** 字典相关 ******************** */

	//报警类型根值
	short DICT_ALARM_TYPE_ROOT_VALUE = 16;

	// 紧急报警
	short DICT_ALARM_TYPE_EMERGENCY = 0;

	// 碰撞
	short DICT_ALARM_TYPE_COLLISION = 29;

	// 侧翻
	short DICT_ALARM_TYPE_ROLL_OVER = 30;

	// 超速
	short DICT_ALARM_TYPE_OVER_SPEED = 82;

	// 超速（终端）
	short DICT_ALARM_TYPE_OVER_SPEED_TERMINAL = 1;

	// 疲劳
	short DICT_ALARM_TYPE_TIRED = 83;

	// 疲劳（终端）
	short DICT_ALARM_TYPE_TIRED_TERMINAL = 2;

	// 疲劳（平台）
	short DICT_ALARM_TYPE_TIRED_PLATFORM = 102;

	// 分段限速
	short DICT_ALARM_TYPE_SEG_SPEED_LIMIT = 100;

	// 夜间限速
	short DICT_ALARM_TYPE_OVER_SPEED_NIGHT = 101;

	//禁行异动报警（平台）
	short DICT_ALARM_TYPE_PLAT_MOVE_LIMIT = 104;

	// 道路限速
	short DICT_ALARM_TYPE_OVER_SPEED_ROAD = 105;

	// 区域/线路
	short DICT_ALARM_TYPE_AREA_LIMIT = 84;

	// 进出区域
	short DICT_ALARM_TYPE_INOUT = 20;

	// 路线不符
	short DICT_ALARM_TYPE_WRONG_ROUTE = 64;

	// 夜间行驶
	short DICT_ALARM_TYPE_NIGHT = 103;

	//累计驾驶疲劳（平台）
	short DICT_ALARM_TYPE_ACCUMULATE_TIRED = 111;

	// 生理疲劳
	short DICT_ALARM_TYPE_FATIGUED = 161;

	// 接打电话
	short DICT_ALARM_TYPE_PHONE = 162;

	// 抽烟
	short DICT_ALARM_TYPE_SMOKE = 163;

	// 长时间不目视前方
	short DICT_ALARM_TYPE_DISTRACTION = 164;

	// 前车碰撞预警
	short DICT_ALARM_TYPE_CAR_COLLISION = 211;

	// 车道偏离报警
	short DICT_ALARM_TYPE_LANE_DEPARTURE = 212;

	// 车距过近报警
	short DICT_ALARM_TYPE_CLOSE_DISTANCE = 213;

	// 行人碰撞报警
	short DICT_ALARM_TYPE_PEDESTRIAN_COLLISION = 214;

	//驾驶辅助功能失效报警
	short DICT_ALARM_TYPE_FUNCTION_EXPIRE = 274;

	// 高级驾驶辅助系统报警（ADAS）
	short DICT_ALARM_TYPE_ADAS = 85;

	// 驾驶员辅助监测类报警（DSM）
	short DICT_ALARM_TYPE_DSM = 86;

	//激烈驾驶
	short DICT_ALARM_TYPE_INTENSE_DRIVING = 87;


	// 字典代码：享有服务
	int DICT_ENJOY_SERVICE_ONLY_SERVER = 1;
	int DICT_ENJOY_SERVICE_ONLY_THIRD = 2;
	int DICT_ENJOY_SERVICE_BOTH = 3;

	// 字典代码：服务商处理措施
	int DICT_SERVER_MEASURES_TTS_AUDIO = 1;
	int DICT_SERVER_MEASURES_TERMINAL_SCREEN = 2;
	int DICT_SERVER_MEASURES_URGENT = 5;

	// 字典代码：第三方处理措施
	int DICT_THIRD_MEASURES_TTS_AUDIO = 1;
	int DICT_THIRD_MEASURES_TERMINAL_SCREEN = 2;
	int DICT_THIRD_MEASURES_URGENT = 5;

	String COMPANY_MEASURES_CODE = "company_measures";
	/* ******************** == ******************** */

	/* ******************** 非字典业务常量 ******************** */

	// 享有服务
	String SERVICE_STATE_CODE = "enjoy_service";
	/** 动态监控*/
	int ENJOY_SERVICE_ONLY_SERVER = 1;
	/** 安全监控*/
	int ENJOY_SERVICE_ONLY_THIRD = 2;
	/** 动态监控和安全监控*/
	int ENJOY_SERVICE_BOTH = 3;

	// 报警处理状态
	/** 待处理 */
	int ALARM_DEAL_STATE_PAND = 0;
	/** 处理中 */
	int ALARM_DEAL_STATE_PROCESSING = 1;
	/** 处理完毕 */
	int ALARM_DEAL_STATE_DONE = 2;

	// 持续报警是否已结束
	int ALARM_COMPLETE_CONTINUE = 0;
	int ALARM_COMPLETE_DONE = 1;

	// 是否误报
	int IS_WRONG_NO = 0;
	int IS_WRONG_YES = 1;

	int REMOVE_ALARM_UNRELEASED = 0;
	int REMOVE_ALARM_NO_RISK_FREE = 1;
	int REMOVE_ALARM_WRONG = 2;

	// 实时报警操作阶段
	String SERVER_DEAL_ALARM = "server_deal_alarm";
	String THIRD_DEAL_ALARM = "third_deal_alarm";
	String COMPANY_DEAL_ALARM = "company_deal_alarm";
	String COMPANY_APPEAL_ALARM = "company_appeal_alarm";
	String AUDIT_APPEAL = "audit_appeal";

	// 实时报警自动处理规则
	int DAILY_REPORT_YES = 1;
	int DAILY_REPORT_NO = 0;
	int MONTHLY_REPORT_YES = 1;
	int MONTHLY_REPORT_NO = 0;
	int AUTO_DEAL_YES = 1;
	int AUTO_DEAL_NO = 0;
	int TTS_AUDIO_YES = 1;
	int TTS_AUDIO_NO = 0;
	int TERMINAL_SCREEN_YES = 1;
	int TERMINAL_SCREEN_NO = 0;
	int URGENT_YES = 1;
	int URGENT_NO = 0;



	// 服务商处理状态（0：未处理，1：已处理）
	int SERVER_STATE_PEND = 0;
	int SERVER_STATE_DONE = 1;
	int SERVER_DEAL_STATE_IS_WRONG = 2;

	// 第三方处理状态（0：未处理，1：已处理）
	int THIRD_STATE_PEND = 0;
	int THIRD_STATE_DONE = 1;
	int THIRD_DEAL_STATE_IS_WRONG = 2;
	// 企业处理状态（0：未处理，1：已处理）
	int COMPANY_STATE_PEND = 0;
	int COMPANY_STATE_DONE = 1;

	int DEAL_RESULT_WRONG = 0;
	int DEAL_RESULT_CONFIRM = 1;
	String DEAL_ALARM_WRONG_CONTENT = "核实误报";


	// 企业申诉状态（0：未申诉，1：已申诉，2：申诉通过，3：申诉驳回）

	/** 是否申诉 未申诉 */
	int IS_APPEAL_PEND = 0;
	/** 是否申诉 已申诉 */
	int IS_APPEAL_DONE = 1;


	/** 申诉状态 未申诉 */
	int APPEAL_STATE_PEND = 0;
	/** 申诉状态 已申诉 */
	int APPEAL_STATE_DONE = 1;
	/** 申诉状态  审核通过*/
	int APPEAL_STATE_PASS = 2;
	/** 申诉状态  被驳回*/
	int APPEAL_STATE_REJECT = 3;
	/** 只享受服务商监控的报警，不可申诉 */
	int APPEAL_STATE_NO = 4;



	/** 申诉结果 未通过 */
	int APPEAL_RESULT_NO_PASS = 0;

	/** 申诉结果 已通过 */
	int APPEAL_RESULT_PASS = 1;



	/** 申诉审核状态 待审核*/
	int AUDIT_STATE_PEND = 0;

	/** 申诉审核状态 已审核*/
	int AUDIT_STATE_DONE = 1;

	/** 申诉审核结果 误报*/
	int AUDIT_RESULT_WRONG = 0;

	/** 申诉审核状态 确认报警*/
	int AUDIT_RESULT_YES = 1;



	// 是否已开罚单（0：未开具，1：已开具）
	int PUNISH_STATE_PEND = 0;
	int PUNISH_STATE_DONE = 1;

	// 罚单状态（0：未确认，1：已确认）
	int TICKET_STATUS_PEND = 0;
	int TICKET_STATUS_DONE = 1;

	/** 报警信息查询、统计时报警等级展示权限，默认不查询0级报警*/
	String ALARM_LEVEL_LIST_LIMIT = "alarm_level_display_limit";

	// 报警级别3级，严重报警
	Short ALARM_LEVEL_3 = 3;

	/** 查询已处理的三级持续报警信息*/
	String CONTINUOUS_ALARM_QUERY_ALARM_LEVEL_3 = "yes";

	/* ******************** == ******************** */

	/* ******************** Redis Key ******************** */

	String FLAG_AUTO_DEAL_ALARM = "flag:auto_deal_alarm";
	String DEAL_ALARM_LOCK = "deal_alarm_lock:";

	/** 待认领 */
	int DEAL_OPERATE_STATE_CLAIM_PEND = 0;

	/** 已认领 */
	int DEAL_OPERATE_STATE_CLAIM_DONE = 1;



	String THIRD_TMP_MANUAL_DEPT = "third_tmp_manual_dept";
	String THIRD_TMP_MANUAL_CAR = "third_tmp_manual_car";
	String THIRD_TMP_MANUAL_ALARM = "third_tmp_manual_alarm";
	/* ******************** == ******************** */
}
