package org.springblade.common.utils;

import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import org.apache.poi.ss.formula.functions.T;
import org.springblade.common.config.ExcelExportFontAndStyleConfig;
import org.springblade.common.config.ExcelExportRowHeightConfig;
import org.springblade.common.config.ExcelFillCellMergePrevCol;
import org.springblade.core.tool.utils.CollectionUtil;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2022/11/1 3:09 PM
 */
public class EasyExcelUtils {

	public static final String DEFAULT_PROXY_PATH = "/bt/statistics/files/";

	/**
	 * @description: 使用 easyexcel 导出数据
	 * @author: zhouxw
	 * @date: 2022/12/1 9:59 AM
	 * @param: [fileSavedPath：文件存储路径, fileProxyPath：文件代理路径，如使用 nginx 代理时通过解析该路径找到目标文件, title：表格标题, list：数据列表, exportClass：文件导出时的数据类，fileNameIsContainDate: 当导出日报、月报时，文件名称中需要带有当日或者当月的日期，而不应该带有当天的日期，普通导出设置为 false]
	 * @return: java.lang.String
	 **/
	public static String export(String fileSavedPath, String fileProxyPath, String title, List list, Class exportClass, boolean fileNameIsContainDate) throws FileNotFoundException {
		Date date = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
		String dateStr = format.format(date);
		String fileName = "";
		if (fileNameIsContainDate) {
			//如果文件名称中带有日期，则不再添加日期
			fileName = title + ".xlsx";
		} else {
			fileName = title + "_" + dateStr + ".xlsx";
		}
		FileOutputStream file = new FileOutputStream(fileSavedPath + fileName);
		fileName = fileProxyPath + fileName;
		//主标题和副标题在excel中分别是是第0和第1行
		List<Integer> columnIndexes = Arrays.asList(0, 1);
		//自定义标题和内容策略(具体定义在下文)
		ExcelExportFontAndStyleConfig cellStyleStrategy =
			new ExcelExportFontAndStyleConfig(columnIndexes, new WriteCellStyle(), new WriteCellStyle());

		EasyExcel.write(file, exportClass)
			.registerWriteHandler(cellStyleStrategy)
			.registerWriteHandler(new ExcelExportRowHeightConfig()).sheet(title).doWrite(list);
		return fileName;
	}


	public static String exportExcelDynamicsHead(String fileSavedPath, String fileProxyPath, String title, List<T> dataList, List<String> headNameList, List<String> columnNameList, Class classType, boolean fileNameIsContainDate) throws FileNotFoundException {

		Date date = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
		String dateStr = format.format(date);
		String fileName = "";
		if (fileNameIsContainDate) {
			//如果文件名称中带有日期，则不再添加日期
			fileName = title + ".xlsx";
		} else {
			fileName = title + "_" + dateStr + ".xlsx";
		}

		String saveFile = fileSavedPath + fileName;
		fileName = fileProxyPath + fileName;
		//主标题和副标题在excel中分别是是第0和第1行
		List<Integer> columnIndexes = Arrays.asList(0, 1);
		//自定义标题和内容策略(具体定义在下文)
		ExcelExportFontAndStyleConfig cellStyleStrategy =
			new ExcelExportFontAndStyleConfig(columnIndexes, new WriteCellStyle(), new WriteCellStyle());

		ExcelWriter excelWriter = EasyExcel.write(saveFile).registerWriteHandler(cellStyleStrategy)
			.registerWriteHandler(new ExcelExportRowHeightConfig())
			.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();

		// 处理表头
		List<List<String>> titleList = new ArrayList<List<String>>();
		List<List<String>> head = new ArrayList<List<String>>();
		for (String headName : headNameList) {
			titleList.add(Collections.singletonList(title));
			head.add(Collections.singletonList(headName));
		}

		WriteSheet writeSheet = EasyExcel.writerSheet(title).needHead(Boolean.TRUE).head(titleList).build();

		WriteTable writeTable = EasyExcel.writerTable(1).needHead(Boolean.TRUE).head(head).build();

		// 处理表格内容
		List<List<String>> data = dealCellData(columnNameList, dataList, classType);
		excelWriter.write(data, writeSheet, writeTable);

		excelWriter.finish();
		return fileName;

	}

	/**
	 * 处理表格业务数据
	 *
	 * @param columnNameList
	 * @param list
	 * @param classType
	 * @return
	 */
	public static List<List<String>> dealCellData(List<String> columnNameList, List<T> list, Class classType) {
		List<List<String>> dataList = new ArrayList<List<String>>();
		if (CollectionUtil.isNotEmpty(list)) {
			int size = list.size();

			for (Object item : list) {

				List<String> lineList = new ArrayList<String>();
				//添加数据行
				for (String columnName : columnNameList) {
					//获得首字母
					String firstLetter = columnName.substring(0, 1).toUpperCase();
					//获得get方法,getName,getAge等
					String getMethodName = "get" + firstLetter + columnName.substring(1);
					Method method;
					try {
						//通过反射获得相应的get方法，用于获得相应的属性值
						method = classType.getMethod(getMethodName, new Class[]{});
						try {

							Object object = method.invoke(item, new Class[]{});
							if (object != null) {
								lineList.add(object.toString());
							} else {
								lineList.add("");
							}
						} catch (IllegalArgumentException e) {
							e.printStackTrace();
						} catch (IllegalAccessException e) {
							e.printStackTrace();
						} catch (InvocationTargetException e) {
							e.printStackTrace();
						}

					} catch (SecurityException e) {
						e.printStackTrace();
					} catch (NoSuchMethodException e) {
						e.printStackTrace();
					}
				}
				dataList.add(lineList);

			}
		}

		return dataList;
	}

	public static <T> void exportExcelToStream(String title, ByteArrayOutputStream outputStream, List<T> dataList, List<String> headNameList, List<String> columnNameList, Class<T> classType) {
		// 主标题和副标题在excel中分别是第0和第1行
		List<Integer> columnIndexes = Arrays.asList(0, 1);
		// 自定义标题和内容策略
		ExcelExportFontAndStyleConfig cellStyleStrategy =
			new ExcelExportFontAndStyleConfig(columnIndexes, new WriteCellStyle(), new WriteCellStyle());
		// 创建 ExcelWriter，输出到 ByteArrayOutputStream
		ExcelWriter excelWriter = EasyExcel.write(outputStream)
			.registerWriteHandler(cellStyleStrategy)
			.registerWriteHandler(new ExcelExportRowHeightConfig())
			.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
			.build();

		// 处理表头
		List<List<String>> titleList = new ArrayList<>();
		List<List<String>> head = new ArrayList<>();
		for (String headName : headNameList) {
			titleList.add(Collections.singletonList(title));
			head.add(Collections.singletonList(headName));
		}

		WriteSheet writeSheet = EasyExcel.writerSheet(title)
			.needHead(Boolean.TRUE)
			.head(titleList)
			.build();

		WriteTable writeTable = EasyExcel.writerTable(1)
			.needHead(Boolean.TRUE)
			.head(head)
			.build();

		// 处理表格内容，调用 dealCellData 时使用原始类型
		List<List<String>> data = EasyExcelUtils.<T>dealCellData(columnNameList, (List) dataList, classType);

		excelWriter.write(data, writeSheet, writeTable);
		// 完成写入，关闭 ExcelWriter
		excelWriter.finish();
	}

	/*public static void main(String[] args) throws FileNotFoundException {

		String fileName = "D:/tmp/" + "write" + System.currentTimeMillis() + ".xlsx";

		List<List> lists = listHandle(buildBody());


		//生成每组数据表头
		List<List<List<String>>> nameHeaderList = buildNameHeader();

		//这里生成的是字段名的表头
		List<List<List<String>>> contentHeaderList = buildContentHeader();

		//主标题和副标题在excel中分别是是第0和第1行
		List<Integer> columnIndexes = Arrays.asList(0, 1);
		//自定义标题和内容策略(具体定义在下文)
		ExcelExportFontAndStyleConfig cellStyleStrategy =
			new ExcelExportFontAndStyleConfig(columnIndexes, new WriteCellStyle(), new WriteCellStyle());

		ExcelFillCellMergePrevCol mergePrevCol = new ExcelFillCellMergePrevCol();

		// 设置需要合并行单元格的行号
		int i = 0;
		for (List bodyList:  lists){
			if (i > 0) {
				for (int j=0; j < bodyList.size(); j++ ) {
					// (i + 4 , 3 , 3)  从i+4 行操作 在第4列合并4列
					mergePrevCol.add( i+4,3,3); // i + 4 ?  因为表头占了4行所以要加4
					i++ ;
				}
			}
			// 子表头、内容表头和空行各占一行，所以 + 3
			i = i + 3;
		}

		FileOutputStream file = new FileOutputStream(fileName);
		ExcelWriter excelWriter = EasyExcel.write(file, ReportVO.class).registerWriteHandler(cellStyleStrategy)	.registerWriteHandler(mergePrevCol).build();

		List<List<String>> header = new ArrayList<>();
		//加一个公共表头 因为一个header一个单元格，合并几个单元格就循环几次
		String title = "北屯市天汇商砼有限公司车辆8月份车辆驾驶行为排名表";
		for (int k =0; k < 7; k++) {
			header.add(Collections.singletonList(title));
		}

		// 设置sheet名称
		WriteSheet writeSheet = EasyExcel.writerSheet(title).needHead(Boolean.TRUE).head(header).build();

		// 子表头的数量
		int num = 0;
		int sortCode = 0;
		// 遍历 按照表头顺序遍历
		for(List<List<String>> nameHeader: nameHeaderList) {
			// 这里必须指定需要头
			// 第一次写入会创建头
			WriteTable writeTable0 = EasyExcel.writerTable(num).needHead(Boolean.TRUE).head(nameHeader).build();
			excelWriter.write((Collection<?>) null, writeSheet, writeTable0);

			WriteTable writeTable1 = EasyExcel.writerTable(num + 1).needHead(Boolean.TRUE).head(contentHeaderList.get(sortCode)).build();
			List contentList = lists.get(sortCode);
			excelWriter.write(contentList, writeSheet, writeTable1);
			// 插入空行
			excelWriter.write(Arrays.asList(""), writeSheet, writeTable1);
			sortCode++;

			// 插入两次表头加2
			num = num + 2;
			// 写入尾行
			if (sortCode == nameHeaderList.size() ) {
				excelWriter.write(buildTail(), writeSheet, writeTable1);
			}
		}
		excelWriter.finish();
	}*/


	/**
	 * 组装表头字段
	 * @return
	 */
	public static List<List<List<String>>> buildNameHeader() {
		List<List<List<String>>> headerList = new ArrayList<>();
		List<List<String>> summaryHeader = new ArrayList<>();
		List<List<String>> overspeedHeader = new ArrayList<>();
		List<List<String>> tiredheader = new ArrayList<>();
		List<List<String>> offlineMoveHeader = new ArrayList<>();
		List<List<String>> stopHeader = new ArrayList<>();
		List<List<String>> offlineAlarmheader = new ArrayList<>();
		for(int i = 0 ;i <7 ; i++ ) {
			summaryHeader.add(Collections.singletonList("报警情况汇总一览表"));
			overspeedHeader.add(Collections.singletonList("（一）超速行驶车辆报警统计"));
			tiredheader.add(Collections.singletonList("（二）疲劳驾驶车辆报警统计"));
			offlineMoveHeader.add(Collections.singletonList("（三）离线位移车辆报警统计"));
			stopHeader.add(Collections.singletonList("（四）1-4点禁行车辆报警统计"));
			offlineAlarmheader.add(Collections.singletonList("（五）离线车辆报警统计"));

		}
		headerList.add(summaryHeader);
		headerList.add(overspeedHeader);
		headerList.add(tiredheader);
		headerList.add(offlineMoveHeader);
		headerList.add(stopHeader);
		headerList.add(offlineAlarmheader);
		return headerList;
	}

	public static List<List<List<String>>>  buildContentHeader() {
		List<List<List<String>>> contentList = new ArrayList<>();
		List<List<String>> subContentList = new ArrayList<>();
		subContentList.add(Collections.singletonList("公司车辆总数"));
		subContentList.add(Collections.singletonList("报警累计"));
		subContentList.add(Collections.singletonList("超速行驶"));
		subContentList.add(Collections.singletonList("疲劳驾驶"));
		subContentList.add(Collections.singletonList("离线"));
		subContentList.add(Collections.singletonList("离线位移"));
		subContentList.add(Collections.singletonList("1-4点禁行"));
		contentList.add(subContentList);

		List<List<String>> overSpeedContentList = new ArrayList<>();
		overSpeedContentList.add(Collections.singletonList("排名"));
		overSpeedContentList.add(Collections.singletonList("车牌号"));
		overSpeedContentList.add(Collections.singletonList("超速行驶次数"));
		overSpeedContentList.add(Collections.singletonList("驾驶员处理情况"));
		overSpeedContentList.add(Collections.singletonList("驾驶员处理情况"));
		overSpeedContentList.add(Collections.singletonList("驾驶员处理情况"));
		overSpeedContentList.add(Collections.singletonList("驾驶员处理情况"));
		contentList.add(overSpeedContentList);

		List<List<String>> tiredSpeedContentList = new ArrayList<>();
		tiredSpeedContentList.add(Collections.singletonList("排名"));
		tiredSpeedContentList.add(Collections.singletonList("车牌号"));
		tiredSpeedContentList.add(Collections.singletonList("疲劳驾驶次数"));
		tiredSpeedContentList.add(Collections.singletonList("驾驶员处理情况"));
		tiredSpeedContentList.add(Collections.singletonList("驾驶员处理情况"));
		tiredSpeedContentList.add(Collections.singletonList("驾驶员处理情况"));
		tiredSpeedContentList.add(Collections.singletonList("驾驶员处理情况"));
		contentList.add(tiredSpeedContentList);

		List<List<String>> offlineContentList = new ArrayList<>();
		offlineContentList.add(Collections.singletonList("排名"));
		offlineContentList.add(Collections.singletonList("车牌号"));
		offlineContentList.add(Collections.singletonList("离线位移次数"));
		offlineContentList.add(Collections.singletonList("驾驶员处理情况"));
		offlineContentList.add(Collections.singletonList("驾驶员处理情况"));
		offlineContentList.add(Collections.singletonList("驾驶员处理情况"));
		offlineContentList.add(Collections.singletonList("驾驶员处理情况"));
		contentList.add(offlineContentList);

		List<List<String>> stopContentList = new ArrayList<>();
		stopContentList.add(Collections.singletonList("排名"));
		stopContentList.add(Collections.singletonList("车牌号"));
		stopContentList.add(Collections.singletonList("1-4点禁行次数"));
		stopContentList.add(Collections.singletonList("驾驶员处理情况"));
		stopContentList.add(Collections.singletonList("驾驶员处理情况"));
		stopContentList.add(Collections.singletonList("驾驶员处理情况"));
		stopContentList.add(Collections.singletonList("驾驶员处理情况"));
		contentList.add(stopContentList);

		List<List<String>> offlineAlarmContentList = new ArrayList<>();
		offlineAlarmContentList.add(Collections.singletonList("排名"));
		offlineAlarmContentList.add(Collections.singletonList("车牌号"));
		offlineAlarmContentList.add(Collections.singletonList("离线次数"));
		offlineAlarmContentList.add(Collections.singletonList("驾驶员处理情况"));
		offlineAlarmContentList.add(Collections.singletonList("驾驶员处理情况"));
		offlineAlarmContentList.add(Collections.singletonList("驾驶员处理情况"));
		offlineAlarmContentList.add(Collections.singletonList("驾驶员处理情况"));
		contentList.add(offlineAlarmContentList);
		return contentList;
	}

	public static List<List<String>>  buildTail() {
		List<List<String>> tailList = new ArrayList<>();
		tailList.add(Collections.singletonList("注：企业月报 是指每月初至月末企业的报警总数及具体车辆报警次数统计。"));
		return tailList;
	}

	/**
	 * 造数据--一览表
	 * @return
	 */
	public static List<ReportVO> buildSubBody() {
		ReportVO vo = new ReportVO();
		vo.setColumn1("100");
		vo.setColumn2("250");
		vo.setColumn3("30");
		vo.setColumn4("40");
		vo.setColumn5("50");
		vo.setColumn6("60");
		vo.setColumn7("70");
		List<ReportVO> list = new ArrayList<>();
		list.add(vo);
		return list;
	}

	/**
	 * 造数据
	 * @return
	 */
	public static List<ReportVO> buildBody() {
		ReportVO vo = new ReportVO();
		ReportVO vo1 = new ReportVO();
		ReportVO vo2 = new ReportVO();

		vo.setColumn1("1");
		vo.setColumn2("粤1");
		vo.setColumn3("132");
		vo.setColumn4("警告");

		vo1.setColumn1("2");
		vo1.setColumn2("粤234");
		vo1.setColumn3("13");
		vo1.setColumn4("警告");

		vo2.setColumn1("3");
		vo2.setColumn2("粤sdf");
		vo2.setColumn3("1");
		vo2.setColumn4("警告");

		List<ReportVO> list = new ArrayList<>();
		list.add(vo);
		list.add(vo1);
		list.add(vo2);
		return list;
	}

	/**
	 * 处理内容行数据
	 * @param list
	 * @return
	 */
	public static List<List> listHandle(List<ReportVO> list){
		List<List> rtnList = new ArrayList<>();
		rtnList.add(buildSubBody());

		for (int i = 0; i < 5; i++) {
			rtnList.add(list);
		}

		return rtnList;
	}


}
