package org.springblade.common.annotation;

import org.springblade.common.validator.EnumValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.METHOD, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = {EnumValidator.class})
public @interface Enum {

	// 校验错误信息
	String message () default "";

	// 目标枚举类
	Class<?>[] target () default {};

	// 生效校验组
	Class<?>[] groups () default {};

	// 不知道是啥，资料里有，那就写上。
	Class<? extends Payload>[] payload () default {};
}
