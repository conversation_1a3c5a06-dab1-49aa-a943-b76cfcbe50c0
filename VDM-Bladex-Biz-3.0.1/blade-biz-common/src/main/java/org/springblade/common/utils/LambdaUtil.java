package org.springblade.common.utils;

import com.baomidou.mybatisplus.core.toolkit.support.SFunction;

import java.lang.invoke.SerializedLambda;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * Lambda表达式工具类，用于获取Lambda表达式引用的字段名
 */
public class LambdaUtil {

    /**
     * 获取Lambda表达式引用的字段名
     *
     * @param func Lambda表达式，例如 BaseNode::getTotal
     * @param <T>  实体类类型
     * @param <R>  返回值类型
     * @return 字段名称，例如 "total"
     */
    public static <T, R> String getFieldName(SFunction<T, R> func) {
        try {
            // 获取SerializedLambda
            Method method = func.getClass().getDeclaredMethod("writeReplace");
            method.setAccessible(true);
            SerializedLambda serializedLambda = (SerializedLambda) method.invoke(func);

            // 获取方法名，例如 "getTotal"
            String methodName = serializedLambda.getImplMethodName();

            // 判断方法前缀，转换为字段名
            if (methodName.startsWith("get")) {
                methodName = methodName.substring(3);
            } else if (methodName.startsWith("is")) {
                methodName = methodName.substring(2);
            } else {
                throw new IllegalArgumentException("Lambda表达式必须是getter方法引用");
            }

            // 将首字母转为小写
            char firstChar = methodName.charAt(0);
            if (Character.isUpperCase(firstChar)) {
                methodName = Character.toLowerCase(firstChar) + methodName.substring(1);
            }

            // 获取类名，验证字段是否存在
            String className = serializedLambda.getImplClass().replace('/', '.');
            Class<?> clazz = Class.forName(className);

            try {
                Field field = clazz.getDeclaredField(methodName);
                return methodName;
            } catch (NoSuchFieldException e) {
                // 如果当前类没有找到字段，尝试在父类中查找
                Class<?> superClass = clazz.getSuperclass();
                while (superClass != null) {
                    try {
                        Field field = superClass.getDeclaredField(methodName);
                        return methodName;
                    } catch (NoSuchFieldException ex) {
                        superClass = superClass.getSuperclass();
                    }
                }
                throw new RuntimeException("无法找到字段: " + methodName);
            }
        } catch (Exception e) {
            throw new RuntimeException("获取字段名失败", e);
        }
    }
}
