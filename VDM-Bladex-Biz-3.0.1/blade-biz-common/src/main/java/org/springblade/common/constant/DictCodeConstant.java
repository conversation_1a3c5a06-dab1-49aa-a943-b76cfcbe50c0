package org.springblade.common.constant;

// 字典code常量
public interface DictCodeConstant {

	// 目标类型
	String TARGET_TYPE = "target_type";

	// 设备类型
	String DEVICE_TYPE = "bdm_device_type";

	// 车辆类型
	String VEHICLE_TYPE = "bdm_car_category";

	// 人员岗位类型
	String WORKER_POST = "bdm_worker_post";

	// 基础设施类型
	String FACILITY_TYPE = "facility_type";

	// 船舶类型
	String SHIP_TYPE = "ship_type";

	// 火车车厢类型
	String CARRIAGE_TYPE = "carriage_model";

	// 矿用卡车类型
	String TRUCK_TYPE = "bdm_truck_category";

	// 入网检测设备类型
	String TEST_DEVICE_TYPE = "test_device_type";

	// 赋码状态
	String CODE_RESULT = "code_result";

	// 告警类型（分类统计数量所用）
	String ALARM_TYPE_SPECIAL = "alarm_type_special";

	// 告警类型
	String ALARM_TYPE = "alarm_type";

	// 告警等级
	String ALARM_LEVEL = "alarm_level";

	// 告警来源
	String ALARM_SOURCE = "alarm_origin";

	// 告警处理措施
	String HANDLE_MEASURES = "handle_measures";
}
