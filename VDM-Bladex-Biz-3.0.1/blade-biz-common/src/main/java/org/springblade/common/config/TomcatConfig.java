package org.springblade.common.config;

import org.apache.catalina.Context;
import org.apache.tomcat.util.descriptor.web.SecurityCollection;
import org.apache.tomcat.util.descriptor.web.SecurityConstraint;
import org.springframework.boot.web.embedded.tomcat.TomcatConnectorCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 禁用trace/track，修复漏洞
 */
@Configuration
public class TomcatConfig {
	@Bean
	public ServletWebServerFactory tomcatServletWebServerFactory() {
		TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory() {
			@Override
			protected void postProcessContext(Context context) {
				SecurityConstraint securityConstraint = new SecurityConstraint();
				securityConstraint.setUserConstraint("CONFIDENTIAL");
				SecurityCollection collection = new SecurityCollection();
				collection.addPattern("/*");
				collection.addMethod("HEAD");
				collection.addMethod("PUT");
				collection.addMethod("DELETE");
				collection.addMethod("OPTIONS");
				collection.addMethod("TRACE");
				collection.addMethod("COPY");
				collection.addMethod("SEARCH");
				collection.addMethod("PROPFIND");
				securityConstraint.addCollection(collection);
				context.addConstraint(securityConstraint);
			}
		};
		tomcat.addConnectorCustomizers((TomcatConnectorCustomizer) connector ->
			connector.setAllowTrace(true));
		return tomcat;
	}
}
