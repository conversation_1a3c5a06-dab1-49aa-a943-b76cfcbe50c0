package org.springblade.common.utils;

import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.BladeUser;

import java.util.Arrays;

public class UserUtil {

	public static boolean isServiceRoleServer (BladeUser user) {
		if ((user == null) || (user.getRoleName() == null) || (user.getRoleName().length() <= 0)) {
			return false;
		}

		return Arrays.asList(user.getRoleName().split(",")).contains(CommonConstant.ROLE_SERVER);
	}

	public static boolean isServiceRoleThird (BladeUser user) {
		if ((user == null) || (user.getRoleName() == null) || (user.getRoleName().length() <= 0)) {
			return false;
		}

		return Arrays.asList(user.getRoleName().split(",")).contains(CommonConstant.ROLE_THIRD);
	}

	public static boolean isServiceRoleCompany (BladeUser user) {
		if ((user == null) || (user.getRoleName() == null) || (user.getRoleName().length() <= 0)) {
			return false;
		}

		return Arrays.asList(user.getRoleName().split(",")).contains(CommonConstant.ROLE_COMPANY);
	}

	public static String getServiceRole (BladeUser user) {
		if ((user == null) || (user.getRoleName() == null) || (user.getRoleName().length() <= 0)) {
			return "";
		}
		if (isServiceRoleThird(user)) {
			return CommonConstant.DICT_SERVICE_ROLE_THIRD;
		} else if (isServiceRoleServer(user)) {
			return CommonConstant.DICT_SERVICE_ROLE_SERVER;
		} else if (isServiceRoleCompany(user)) {
			return CommonConstant.DICT_SERVICE_ROLE_COMPANY;
		} else {
			return "";
		}
	}

	/** 根据用户ID获取用户名称*/
	public static String getUserNameByUserId (long userId) {
		if (userId == 0 ) {
			return "";
		}
		// TODO: 2023/8/16
		String userName = "待转义"+userId;
		return userName;
	}
}
