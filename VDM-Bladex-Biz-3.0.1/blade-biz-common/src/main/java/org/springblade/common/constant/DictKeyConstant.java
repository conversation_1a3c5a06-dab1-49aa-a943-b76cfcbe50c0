package org.springblade.common.constant;

// 字典key常量
public interface DictKeyConstant {

	String DEFAULT_ROOT_KEY = "-1";
	String ALARM_TYPE_ROOT_KEY = "16";

	// 目标类型：车辆
	String TARGET_TYPE_VEHICLE = "1";

	// 目标类型：人员
	String TARGET_TYPE_WORKER = "2";

	// 目标类型：基础设施
	String TARGET_TYPE_FACILITY = "3";

	// 目标类型：外派
	String TARGET_TYPE_TEMPORARY = "5";

	// 目标类型：船舶
	String TARGET_TYPE_SHIP = "7";

	// 目标类型：火车车厢
	String TARGET_TYPE_TRAIN = "8";

	// 目标类型：精密装备
	String TARGET_TYPE_PRECISION = "9";

	// 目标类型：矿用卡车
	String TARGET_TYPE_TRUCK = "10";

	// 设备类型：北斗定位终端
	String DEVICE_TYPE_LOCATE = "1";

	// 设备类型：北斗穿戴式终端
	String DEVICE_TYPE_WEAR = "2";

	// 设备类型：北斗短报文终端
	String DEVICE_TYPE_SHORT = "3";

	// 设备类型：北斗监测终端
	String DEVICE_TYPE_MONITOR = "4";

	// 设备类型：北斗授时终端
	String DEVICE_TYPE_TIME = "5";
}
