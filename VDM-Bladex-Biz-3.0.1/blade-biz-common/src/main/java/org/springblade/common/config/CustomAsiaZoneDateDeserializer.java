package org.springblade.common.config;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.StringUtils;

import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

/**
 * 自定义反序列化器，实现ISO8601的UTC标准时间转java.util.Date
 * 2025-01-10T09:28:31.127006342+08:00 转 java.util.Date
 * 注意：+08:00 表示前边的日期已经是北京时间了，没有必要再加8处理。
 * 这里将年月日时分秒之外的部分全部去除，直接使用 SimpleDateFormat 进行转换
 */
public class CustomAsiaZoneDateDeserializer implements ObjectDeserializer {

	public static final ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
	@Override
	public Date deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
		String text = (String) parser.parse();
		try {
			//1.按照小数点进行分割，只保留小数点前边的部分
			if(StringUtils.isEmpty(text)){
				return null;
			}
			text = text.split("\\.")[0];
			//2.将T替换为空格
			text = text.replace("T", " ");
			Date date = sdfHolder.get().parse(text);
			return date;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public int getFastMatchToken() {
		return 0;
	}
}
