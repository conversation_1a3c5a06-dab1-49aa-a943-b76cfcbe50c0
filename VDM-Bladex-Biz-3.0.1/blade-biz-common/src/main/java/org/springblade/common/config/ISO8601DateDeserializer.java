package org.springblade.common.config;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;

import java.lang.reflect.Type;
import java.text.ParseException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Date;

/**
 * 自定义反序列化器，实现ISO8601的UTC标准时间转java.util.Date
 * 2024-11-18T12:56:00.246161277Z 转 java.util.Date
 * 注意：转换后的日期与原日期差8小时，因为中国处在 UTC+08:00 时区
 */
public class ISO8601DateDeserializer implements ObjectDeserializer {

	@Override
	public Date deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
		String text = (String) parser.parse();
		try {
			// 解析字符串为 Instant
			Instant instant = Instant.parse(text);

			// 将 Instant 转换为指定时区的 ZonedDateTime
			ZonedDateTime zonedDateTime = instant.atZone(ZoneId.of("Asia/Shanghai"));

			// 将 ZonedDateTime 转换为 java.util.Date
			Date date = Date.from(zonedDateTime.toInstant());
			return date;
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public int getFastMatchToken() {
		return 0;
	}
}
