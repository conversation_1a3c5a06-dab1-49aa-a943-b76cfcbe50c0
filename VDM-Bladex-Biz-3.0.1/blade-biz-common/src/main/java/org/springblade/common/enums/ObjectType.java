package org.springblade.common.enums;

/**
 * 被操作对象类别
 */
public enum ObjectType {
    /**
     * 其它
     */
    OTHER,

    /**
     * 北斗定位终端
     */
    RNSS,

    /**
     * 北斗穿戴式终端
     */
    WEAR,

    /**
     * 北斗短报文终端
     */
    RDSS,

    /**
     * 北斗授时终端
     */
    PNT,

    /**
     * 北斗检测终端
     */
    MONIT,

    /**
     * 物联网卡
     */
    IOT,

	/**
	 * 车辆
	 */
	VEHICLE,

	/**
	 * 人员
	 */
	WORKER,

	/**
	 * 基础设置
	 */
	FACILITY,

	/**
	 * 集装箱
	 */
	CONTAINER,

	/**
	 * 船舶
	 */
	SHIP,

	/**
	 * 平台账号管理
	 */
	PLATFORM_ACCOUNT,

	/**
	 * 本平台接口管理
	 */
	PLATFORM_INTERFACE,

    /**
     * 平台管理
     */
	PLATFORM_MANAGEMENT,

	/**
	 * 业务服务管理
	 */
	BUSINESS_SERVICE,

	/**
	 * 第三方平台接口管理
	 */
	THIRD_PARTY_INTERFACE,

	/**
	 * 厂商信息管理
	 */
	MANUFACTURER,

	/**
	 * 外派管理
	 */
	TEMPORARY,

	/**
	 * 访客管理
	 */
	VISITOR,

	/**
	 * 台账管理
	 */
	LEDGER,

	/**
	 * 委托企业管理
	 */
	COMPANY,

	/**
	 * 入网终端管理
	 */
	TERMINALCHECK,

	/**
	 * 赋码机管理
	 */
	MACHINE,

	/**
	 * 铁路货车车厢管理
	 */
	CARGOBOX,

	/**
	 * 精密装备管理
	 */
	PRECISION,

	/**
	 * 矿用卡车管理
	 */
	TRUCK,

	/**
	 * 存量终端
	 */
	EXISTINGTERMINAL
}
