package org.springblade.common.utils;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springblade.entity.AuthInfo;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.DataAuthCE;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 部门处理器
 */
@Component
@Slf4j
public class DeptProcessingUtil {

    /**
     *
     * @param ceDataAuth
     * @return
     */
    public AuthInfo handle(DataAuthCE ceDataAuth){
        AuthInfo response = new AuthInfo();

        StringBuffer sb = new StringBuffer();
        if (AuthUtil.isAdministrator()) {
			//如果是超级管理员，则将account和orgList置为空，则sql中就不再添加查询条件
            response.setAccount(null);
            response.setOrgList(null);
        }else {
            if("1".equals(ceDataAuth.getGnDataAuthType())){
                response.setOrgList(null);
                response.setAccount(ceDataAuth.getAccount());
            }else {
                response.setAccount(null);
                //将部门转字符串
                if (ceDataAuth.getOrgList() != null && !ceDataAuth.getOrgList().isEmpty()){

                    List<Long> orgListAsLongs = new ArrayList<Long>();
					List<String> orgList = ceDataAuth.getOrgList();
					for(int i = 0 ; i < orgList.size(); i++){
						try{
							Long orgId = Long.parseLong(orgList.get(i));
							orgListAsLongs.add(orgId);
						}catch (Exception e){
							log.error("数据权限，部门id字符串转数字失败，字符串为:" + orgList.get(i));
							log.info("数据权限部门数据为：" + JSON.toJSONString(ceDataAuth.getOrgList()));
						}
					}
                    sb.append("'{");
                    for(Long dept : orgListAsLongs){
                        sb.append(dept+"").append(",");
                    }
                    if(sb.length() > 0){
                        sb = new StringBuffer(sb.substring(0,sb.length()-1));
                    }
                    sb.append("}'");
                }
                if (sb.length() > 4) {
                    response.setOrgList(sb.toString());
                } else {
                    response.setOrgList(null);
                }
            }
        }
        return response;
    }
}
