package org.springblade.common.validator;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.annotation.Enum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

@Slf4j
public class EnumValidator implements ConstraintValidator<Enum, Object> {

	// 目标枚举类
	Class<?>[] classList;

	@Override
	public void initialize (Enum e) {
		this.classList = e.target();
	}

	@Override
	public boolean isValid (Object value, ConstraintValidatorContext context) {

		// 字段非必填时，value为空即校验通过，否则，后面的代码会报空指针；
		// 字段必填时，若value为空，会被@NotNull/@NotEmpty拦截，不会到这里。
		if (value == null) {
			return true;
		}
		if ((this.classList == null) || (this.classList.length <= 0)) {
			return false;
		}
		for (Class<?> c : this.classList) {
			if (!c.isEnum()) {
				return false;
			}
			try {
				Object[] objList = c.getEnumConstants();
				Method method = c.getMethod("getValue");
				for (Object obj : objList) {
					Object v = method.invoke(obj);
					if (value.equals(v)) {
						return true;
					}
				}
			} catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
				log.error("fail validate enum type: " + e.getMessage(), e);
			}
		}

		return false;
	}
}
