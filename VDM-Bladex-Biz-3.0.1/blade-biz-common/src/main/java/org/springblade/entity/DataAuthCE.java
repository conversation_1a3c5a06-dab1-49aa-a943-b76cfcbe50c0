package org.springblade.entity;

import lombok.Data;

import java.util.List;

@Data
public class DataAuthCE {
	/** 国能数据权限类型：本人 */
	public static final String CE_DATA_AUTH_TYPE_SELF = "1";
	/** 国能数据权限类型：组织 */
	public static final String CE_DATA_AUTH_TYPE_ORG = "2";

	//国能数据权限类型：1 本人    2 组织
	private String gnDataAuthType;
	//组织列表，当数据权限是组织时，用于存放组织信息
	private List<String> orgList;
	//组织列表构成的数组字符串 ('{xxxx,xxxx,xxxx}')，用于sql中any函数查询，解决in参数过多报错的问题
	private String orgListStr;
	//登录账户，当数据权限是人员时，用于存放账户名。账户就是工号，数据库中会用该信息来筛选仅本人的权限业务数据
	private String account;
	//是否是超级管理员。如果是超级管理员，orgList中会返回所有的组织，但是这样在sql中效率很低，所以也可以通过isSuper时，不添加部门的筛选条件
	//另外，强调一下，国能登录，没有超级管理员的概念，给哪些组织就查询哪些组织
	private Boolean isSuper;
}
