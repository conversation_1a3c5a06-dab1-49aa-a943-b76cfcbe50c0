package org.springblade.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 报警信息
 * <AUTHOR>
 * @since 2023-07-03
 */
@Data
public class BdmSecurity implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 车牌号
	 */
	@JsonProperty(value = "licence_plate",required = false)
	private String licencePlate;

	/**
	 * 车牌颜色
	 */
	@JsonProperty(value = "licence_color",required = false)
	private String licenceColor;

	/**
	 * 报警手机号
	 */
	private String phone;

	/**
	 * 车组id
	 */
	@JsonProperty(value = "dept_id",required = false)
	private Long deptId;

	/**
	 * 报警类型
	 */
	@JsonProperty(value = "alarm_type",required = false)
	private Integer alarmType;

	/**
	 * 报警等级
	 */
	@JsonProperty(value = "alarm_level",required = false)
	private Integer alarmLevel;

	/**
	 * 驾驶员身份证
	 */
	@JsonProperty("id_card")
	private String idCard;

	/**
	 * 驾驶员姓名
	 */
	@JsonProperty(value = "driver_name",required = false)
	private String driverName;

	/**
	 * 车辆仪表速度
	 */
	@JsonProperty("vehicle_speed")
	private Double vehicleSpeed;

	/**
	 * 速度
	 */
	private Double speed;

	/**
	 * 限速
	 */
	@JsonProperty(value = "limit_speed",required = false)
	private Double limitSpeed;

	/**
	 * 道路名称
	 */
	@JsonProperty("road_name")
	private String roadName;

	@JsonProperty("road_type")
	private Byte roadType;

	/**
	 * 定位纬度
	 */
	private BigDecimal latitude;

	/**
	 * 定位经度
	 */
	private BigDecimal longitude;

	@JsonProperty(value = "alarm_time",required = false)
	private Date alarmTime;

	@JsonProperty("alarm_address")
	private String alarmAddress;

	//高程
	private Integer altitude;

	//里程
	private Double mileage;

	/**
	 * 报警来源
	 */
	@JsonProperty(value = "alarm_origin",required = false)
	private String alarmOrigin;

	/**
	 * sim卡号
	 */
	@JsonProperty(value = "sim_id",required = false)
	private String simId;

	/**
	 * 持续状态是否结束（0：未结束，1：已结束）
	 */
	@JsonProperty(value = "alarm_complete",required = false)
	private Integer alarmComplete;

	/**
	 * 是否为自动处理：0 否   1 是
	 */
	@JsonProperty(value = "server_auto_deal",required = false)
	private Byte serverAutoDeal;


	private Byte batch;


	/**
	 * 是否为自动处理：0 否   1 是
	 */
	@JsonProperty(value = "third_auto_deal",required = false)
	private Byte thirdAutoDeal;

	/**
	 * 报警处理状态，0-待处理；1：处理中；2；处理完毕
	 */
	@JsonProperty(value = "alarm_deal_state",required = false)
	private Integer alarmDealState;

	/**
	 * 解除报警状态， -1-未解除  0-报警处理  1-无风险解除  2-标记误报解除
	 */
	@JsonProperty(value = "remove_alarm",required = false)
	private Integer removeAlarm;

	@JsonProperty(value = "deal_describe",required = false)
	private String dealDescribe;

	@JsonProperty(value = "deal_man",required = false)
	private String dealMan;

	/**
	 * 平台规则id
	 */
	@JsonProperty(value = "rule_id",required = false)
	private Integer ruleId;

	/**
	 * 规则类型id
	 */
	@JsonProperty(value = "rule_type_id",required = false)
	private Integer ruleTypeId;


	@JsonProperty("rule_name")
	private String ruleName;

	/**
	 * 车辆状态
	 */
	@JsonProperty(value = "vehicle_states",required = false)
	private String vehicleStates;

	@JsonProperty(value = "alarm_end_time",required = false)
	private Date alarmEndTime;

	/**
	 * 报警结束位置
	 */
	@JsonProperty(value = "alarm_end_address",required = false)
	private String alarmEndAddress;

	/**
	 * 报警结束纬度
	 */
	@JsonProperty("latitude_end")
	private BigDecimal latitudeEnd;

	/**
	 * 报警结束经度
	 */
	@JsonProperty("longitude_end")
	private BigDecimal longitudeEnd;

	/**
	 * 终端状态描述
	 */
	@JsonProperty(value = "terminal_state_string",required = false)
	private String terminalStateString;

	/**
	 * 车辆id
	 */
	@JsonProperty(value = "vehicle_id",required = false)
	private Integer vehicleId;

	/**
	 * 告警附件关联
	 */
	@JsonProperty(value = "unique_id",required = false)
	private String uniqueId;

	/**
	 * 服务状态（状态值与名称的映射，详见blade_dict_biz表code=enjoy_service的记录）
	 */
	@JsonProperty(value = "service_state",required = false)
	private Integer serviceState;

	/**
	 * 是否误报 （1 误报  0 非误报）
	 */
	@JsonProperty(value = "is_wrong",required = false)
	private Integer isWrong;

	/**
	 * 上报状态
	 */
	@JsonProperty(value = "reporting_state",required = false)
	private Integer reportingState;

	/**
	 * 行业类型
	 */
	@JsonProperty(value = "vehicle_use_type",required = false)
	private Integer vehicleUseType;

	/**
	 * 809第三方转发平台名称
	 */
	@JsonProperty(value = "third_platform",required = false)
	private String thirdPlatform;

	/**
	 * 服务商处理状态（0：未处理，1：已处理，2：误报）
	 */
	@JsonProperty(value = "server_state",required = false)
	private Integer serverState;

	/**
	 * 服务商处理结果，0：误报；1：确认报警
	 */
	@JsonProperty(value = "server_result",required = false)
	private Integer serverResult;

	/**
	 * 服务商处理措施（英文逗号分隔的多个措施值，措施值与名称的映射，详见blade_dict_biz表code=server_measures的记录）
	 */
	@JsonProperty(value = "server_measures",required = false)
	private String serverMeasures;

	/**
	 * 服务商处理内容
	 */
	@JsonProperty(value = "server_content",required = false)
	private String serverContent;

	/**
	 * 服务商处理人员
	 */
	@JsonProperty("server_user")
	private Long serverUser;

	/**
	 * 服务商处理时间
	 */
	@JsonProperty(value = "server_time",required = false)
	private Date serverTime;

	/**
	 * 第三方处理状态（0：未处理，1：已处理，2：误报）
	 */
	@JsonProperty(value = "third_state",required = false)
	private Integer thirdState;

	/**
	 * 第三方处理结果，0：误报；1：确认报警
	 */
	@JsonProperty(value = "third_result",required = false)
	private Integer thirdResult;

	/**
	 * 第三方处理措施（英文逗号分隔的多个措施值，措施值与名称的映射，详见blade_dict_biz表code=third_measures的记录）
	 */
	@JsonProperty(value = "third_measures",required = false)
	private String thirdMeasures;

	/**
	 * 第三方处理内容
	 */
	@JsonProperty(value = "third_content",required = false)
	private String thirdContent;

	/**
	 * 第三方处理人员
	 */
	@JsonProperty(value = "third_user",required = false)
	private Long thirdUser;

	/**
	 * 第三方处理时间
	 */
	@JsonProperty(value = "third_time",required = false)
	private Date thirdTime;

	/**
	 * 企业处理状态（0：未处理，1：已处理）
	 */
	@JsonProperty(value = "company_state",required = false)
	private Integer companyState;


	private Integer direction;
	/**
	 * 企业处理措施（英文逗号分隔的多个措施值，措施值与名称的映射，详见blade_dict_biz表code=company_measures的记录）
	 */
	@JsonProperty(value = "company_measures",required = false)
	private String companyMeasures;

	/**
	 * 企业处理内容
	 */
	@JsonProperty(value = "company_content",required = false)
	private String companyContent;

	/**
	 * 企业处理附件URL
	 */
	@JsonProperty(value = "company_attach",required = false)
	private String companyAttach;

	/**
	 * 企业处理人员
	 */
	@JsonProperty(value = "company_user",required = false)
	private Long companyUser;

	/**
	 * 企业处理时间
	 */
	@JsonProperty("company_time")
	private Date companyTime;

	/**
	 * 企业申诉状态（0：未申诉，1：已申诉，2：申诉通过，3：申诉驳回）
	 */
	@JsonProperty(value = "appeal_state",required = false)
	private Integer appealState;

	/**
	 * 企业申诉理由
	 */
	@JsonProperty(value = "appeal_reason",required = false)
	private String appealReason;

	/**
	 * 企业申诉附件URL
	 */
	@JsonProperty(value = "appeal_attach",required = false)
	private String appealAttach;

	/**
	 * 企业申诉人员
	 */
	@JsonProperty(value = "appeal_user",required = false)
	private Long appealUser;

	/**
	 * 企业申诉时间
	 */
	@JsonProperty(value = "appeal_time",required = false)
	private Date appealTime;

	/**
	 * 处理结果
	 */
	@JsonProperty(value = "appeal_result",required = false)
	private Integer appealResult;

	/**
	 * 申诉审核状态，0：待审核；1：已审核
	 */
	@JsonProperty(value = "audit_state",required = false)
	private Integer auditState;

	/**
	 * 审核结果，0：误报；1：确认报警
	 */
	@JsonProperty(value = "audit_result",required = false)
	private Integer auditResult;

	/**
	 * 申诉处理内容
	 */
	@JsonProperty(value = "audit_content",required = false)
	private String auditContent;

	/**
	 * 申诉审核人员
	 */
	@JsonProperty(value = "audit_user",required = false)
	private Long auditUser;

	/**
	 * 申诉处理时间
	 */
	@JsonProperty(value = "audit_time",required = false)
	private Date auditTime;

	/**
	 * 是否已开罚单（0：未开具，1：已开具）
	 */
	@JsonProperty(value = "punish_state",required = false)
	private Integer punishState;

	/**
	 * 报警附件数量
	 */
	@JsonProperty(value = "alarm_dms_appendix",required = false)
	private Integer alarmDmsAppendix;

	/**
	 * 是否申诉（1：已申诉 0：未申诉）
	 */
	@JsonProperty(value = "is_appeal",required = false)
	private Integer isAppeal;

	/**
	 * 协议类型： 1 808      2  809
	 */
	@JsonProperty(value = "protocol_type",required = false)
	private Integer protocolType;

	/**
	 * 行驶时间
	 */
	@JsonProperty(value = "driving_time",required = false)
	private Date drivingTime;

	/**
	 * 疲劳开始时间
	 */
	@JsonProperty(value = "fatigue_start_time",required = false)
	private Date fatigueStartTime;

	/**
	 * 疲劳结束时间
	 */
	@JsonProperty(value = "fatigue_end_time",required = false)
	private Date fatigueEndTime;

	/**
	 * 区域id
	 */
	@JsonProperty(value = "region_id",required = false)
	private Integer regionId;

	/**
	 * 创建时间
	 */
	@JsonProperty(value = "create_time",required = false)
	private Date createTime;

	/**
	 * 更新时间
	 */
	@JsonProperty(value = "update_time",required = false)
	private Date updateTime;


	@JsonProperty(value = "dept_full_name")
	private String deptFullName;

	@JsonProperty(value = "deal_operate_state")
	private Integer dealOperateState;

	@JsonProperty(value = "max_speed")
	private Double maxSpeed;

}
