package org.springblade.entity;

import lombok.Data;

import java.util.List;

/**
 * 数据权限实体
 */
@Data
public class DataAuthEntity {

	//权限类型（数据权限优化后，只用区分本人和本部门）
	//1:本单位 2:本单位及下属 3:本部门 4:本部门及下属 5:本人 6:自定义权限。默认为本单位及下属
	private Integer authType;

	//是否包含： 1 包含  2 不包含
	//因为组织机构有5W个，对于权限大的账户，会返回大量参数，使用起来并不方便。当该参数为 包含 时，表示要查询的部门是 authOrgList 中的部门；
	//当该参数为 不包含 时，表示要查询的部门不包含 authOrgList中的内容。
	//可以理解为sql中的 in 和 not in，参数就是 authOrgList
	private Integer operation;

	//部门列表
	private List<String> authOrgList;
}
