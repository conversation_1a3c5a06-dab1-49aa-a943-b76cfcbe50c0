package org.springblade.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

// 告警
@Data
public class Alarm implements Serializable {

	// 告警ID
	@JsonProperty("id")
	@JSONField(name = "id")
	private Long id;

	// 目标类型（类型值与名称的映射，详见blade_dict_biz表code=target_type的记录）
	@JsonProperty("target_type")
	@JSONField(name = "target_type")
	private Byte targetType;

	// 目标ID
	@JsonProperty("target_id")
	@JSONField(name = "target_id")
	private Long targetId;

	// 设备类别（类型值与名称的映射，详见blade_dict_biz表code=bdm_device_type的记录）
	@JsonProperty("device_type")
	@JSONField(name = "device_type")
	private Byte deviceType;

	// 设备ID
	@JsonProperty("device_id")
	@JSONField(name = "device_id")
	private Long deviceId;

	// 设备赋码
	@JsonProperty("device_num")
	@JSONField(name = "device_num")
	private String deviceNum;

	// 单位ID
	@JsonProperty("dept_id")
	@JSONField(name = "dept_id")
	private Long deptId;

	// 告警类型（类型值与名称的映射，详见blade_dict_biz表code=alarm_type的记录）
	@JsonProperty("type")
	@JSONField(name = "type")
	private Short type;

	// 告警等级（等级值与名称的映射，详见blade_dict_biz表code=alarm_level的记录）
	@JsonProperty("level")
	@JSONField(name = "level")
	private Byte level;

	// 告警来源（来源值与名称的映射，详见blade_dict_biz表code=alarm_origin的记录）
	@JsonProperty("source")
	@JSONField(name = "source")
	private Byte source;

	// 告警结束状态（0：未结束，1：已结束）
	@JsonProperty("completed")
	@JSONField(name = "completed")
	private Byte completed;

	// 开始经度
	@JsonProperty("start_lon")
	@JSONField(name = "start_lon")
	private Double startLon;

	// 开始纬度
	@JsonProperty("start_lat")
	@JSONField(name = "start_lat")
	private Double startLat;

	// 开始地址
	@JsonProperty("start_addr")
	@JSONField(name = "start_addr")
	private String startAddr;

	// 开始时间（秒数）
	@JsonProperty("start_time")
	@JSONField(name = "start_time")
	private Long startTime;

	// 结束经度
	@JsonProperty("end_lon")
	@JSONField(name = "end_lon")
	private Double endLon;

	// 结束纬度
	@JsonProperty("end_lat")
	@JSONField(name = "end_lat")
	private Double endLat;

	// 结束地址
	@JsonProperty("end_addr")
	@JSONField(name = "end_addr")
	private String endAddr;

	// 结束时间（秒数）
	@JsonProperty("end_time")
	@JSONField(name = "end_time")
	private Long endTime;

	// 附加信息
	@JsonProperty("auxiliary")
	@JSONField(name = "auxiliary")
	private String auxiliary;

	// 处理状态（0：未处理，1：已处理，2：误报）
	@JsonProperty("handle_state")
	@JSONField(name = "handle_state")
	private Byte handleState;

	// 处理方式（方式值与名称的映射，详见blade_dict_biz表code=handle_measures的记录）
	@JsonProperty("handle_measures")
	@JSONField(name = "handle_measures")
	private String handleMeasures;

	// 处理内容
	@JsonProperty("handle_content")
	@JSONField(name = "handle_content")
	private String handleContent;

	// 处理人ID
	@JsonProperty("handler")
	@JSONField(name = "handler")
	private Long handler;

	// 处理时间（秒数）
	@JsonProperty("handle_time")
	@JSONField(name = "handle_time")
	private Long handleTime;

	// 创建时间（秒数）
	@JsonProperty("create_time")
	@JSONField(name = "create_time")
	private Long createTime;

	private Double speed;
}
