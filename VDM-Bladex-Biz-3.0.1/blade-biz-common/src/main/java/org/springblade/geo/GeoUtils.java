package org.springblade.geo;

public class GeoUtils {
	/**
	 * Haversine公式计算两点间距离（米）
	 */
	public static double haversineDistance(double lat1, double lon1, double lat2, double lon2) {
		final int R = 6371000; // 地球半径（米）
		double dLat = Math.toRadians(lat2 - lat1);
		double dLon = Math.toRadians(lon2 - lon1);
		lat1 = Math.toRadians(lat1);
		lat2 = Math.toRadians(lat2);

		double a = Math.sin(dLat/2) * Math.sin(dLat/2) +
			Math.sin(dLon/2) * Math.sin(dLon/2) * Math.cos(lat1) * Math.cos(lat2);
		double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
		return R * c;
	}

	/**
	 * 计算点到线段的距离（米）
	 */
	public static double haversineDistanceToSegment(double pLat, double pLon,
													double aLat, double aLon,
													double bLat, double bLon) {
		// 计算线段长度
		double segmentLength = haversineDistance(aLat, aLon, bLat, bLon);
		if (segmentLength < 1.0) { // 小于1米视为同一点
			return haversineDistance(pLat, pLon, aLat, aLon);
		}

		// 计算投影比例
		double[] abVector = {bLon - aLon, bLat - aLat};
		double[] apVector = {pLon - aLon, pLat - aLat};

		double dotProduct = apVector[0] * abVector[0] + apVector[1] * abVector[1];
		double t = dotProduct / (segmentLength * segmentLength);
		t = Math.max(0, Math.min(1, t));

		// 计算投影点
		double projLon = aLon + t * abVector[0];
		double projLat = aLat + t * abVector[1];

		return haversineDistance(pLat, pLon, projLat, projLon);
	}

	/**
	 * 计算两点间的方向角（0-360度，正北为0）
	 */
	public static double calculateBearing(double lat1, double lon1, double lat2, double lon2) {
		double φ1 = Math.toRadians(lat1);
		double φ2 = Math.toRadians(lat2);
		double Δλ = Math.toRadians(lon2 - lon1);

		double y = Math.sin(Δλ) * Math.cos(φ2);
		double x = Math.cos(φ1) * Math.sin(φ2) -
			Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ);
		double θ = Math.atan2(y, x);

		return (Math.toDegrees(θ) + 360) % 360;
	}
}
