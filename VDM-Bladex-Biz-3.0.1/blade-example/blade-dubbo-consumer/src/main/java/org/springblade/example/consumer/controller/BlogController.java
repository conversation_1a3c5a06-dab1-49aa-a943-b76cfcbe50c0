/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.example.consumer.controller;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springblade.core.tool.api.R;
import org.springblade.example.provider.entity.Blog;
import org.springblade.example.provider.rpc.IBlogRpc;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Blog控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("blog")
public class BlogController {

	@DubboReference(
		version = "${blade.service.version}",
		mock = "org.springblade.example.provider.rpc.BlogMock",
		//stub = "org.springblade.example.consumer.rpc.BlogStub",
		check = false
	)
	private IBlogRpc rpc;

	@GetMapping("detail/{id}")
	public R<Blog> detail(@PathVariable Integer id) {
		Blog detail = rpc.detail(id);
		return R.data(detail);
	}

}
