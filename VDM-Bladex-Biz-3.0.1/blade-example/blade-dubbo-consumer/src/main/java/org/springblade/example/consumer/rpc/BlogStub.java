/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.example.consumer.rpc;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.example.provider.entity.Blog;
import org.springblade.example.provider.rpc.IBlogRpc;

/**
 * 本地存根
 *
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
public class BlogStub implements IBlogRpc {

	private final IBlogRpc rpc;

	@Override
	public Blog detail(Integer id) {
		try {
			return rpc.detail(id);
		} catch (Exception ex) {
			log.error(ex.getMessage());
			return null;
		}
	}
}
