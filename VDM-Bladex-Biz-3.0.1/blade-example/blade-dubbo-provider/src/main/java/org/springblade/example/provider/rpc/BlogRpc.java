/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.example.provider.rpc;

import org.apache.dubbo.config.annotation.DubboService;
import org.springblade.example.provider.entity.Blog;

/**
 * BlogRpc实现类
 *
 * <AUTHOR>
 */
@DubboService(
	version = "${blade.service.version}"
)
public class BlogRpc implements IBlogRpc {
	@Override
	public Blog detail(Integer id) {
		Blog blog = new Blog();
		blog.setId(id);
		blog.setTitle("博客标题");
		blog.setContent("博客内容");
		return blog;
	}
}
