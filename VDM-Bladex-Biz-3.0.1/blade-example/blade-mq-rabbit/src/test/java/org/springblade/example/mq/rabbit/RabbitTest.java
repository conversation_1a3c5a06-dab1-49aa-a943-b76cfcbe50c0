package org.springblade.example.mq.rabbit;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springblade.core.test.BladeBootTest;
import org.springblade.core.test.BladeSpringExtension;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.example.mq.rabbit.constant.RabbitConstant;
import org.springblade.example.mq.rabbit.message.MessageStruct;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@ExtendWith(BladeSpringExtension.class)
@SpringBootTest(classes = RabbitApplication.class)
@BladeBootTest(appName = "blade-rabbit", profile = "test", enableLoader = true)
public class RabbitTest {
	@Autowired
	private RabbitTemplate rabbitTemplate;

	/**
	 * 测试直接模式发送
	 */
	@Test
	public void sendDirect() {
		rabbitTemplate.convertAndSend(RabbitConstant.DIRECT_MODE_QUEUE_ONE, new MessageStruct("direct message"));
	}

	/**
	 * 测试分列模式发送
	 */
	@Test
	public void sendFanout() {
		rabbitTemplate.convertAndSend(RabbitConstant.FANOUT_MODE_QUEUE, "", new MessageStruct("fanout message"));
	}

	/**
	 * 测试主题模式发送1
	 */
	@Test
	public void sendTopic1() {
		rabbitTemplate.convertAndSend(RabbitConstant.TOPIC_MODE_QUEUE, "queue.aaa.bbb", new MessageStruct("topic message"));
	}

	/**
	 * 测试主题模式发送2
	 */
	@Test
	public void sendTopic2() {
		rabbitTemplate.convertAndSend(RabbitConstant.TOPIC_MODE_QUEUE, "ccc.queue", new MessageStruct("topic message"));
	}

	/**
	 * 测试主题模式发送3
	 */
	@Test
	public void sendTopic3() {
		rabbitTemplate.convertAndSend(RabbitConstant.TOPIC_MODE_QUEUE, "3.queue", new MessageStruct("topic message"));
	}

	/**
	 * 测试延迟队列发送
	 */
	@Test
	public void sendDelay() {
		rabbitTemplate.convertAndSend(RabbitConstant.DELAY_MODE_QUEUE, RabbitConstant.DELAY_QUEUE,
			new MessageStruct("delay message, delay 5s, " + DateUtil.now()), message -> {
				message.getMessageProperties().setHeader("x-delay", 5000);
				return message;
			});
		rabbitTemplate.convertAndSend(RabbitConstant.DELAY_MODE_QUEUE, RabbitConstant.DELAY_QUEUE,
			new MessageStruct("delay message,  delay 2s, " + DateUtil.now()), message -> {
				message.getMessageProperties().setHeader("x-delay", 2000);
				return message;
			});
		rabbitTemplate.convertAndSend(RabbitConstant.DELAY_MODE_QUEUE, RabbitConstant.DELAY_QUEUE,
			new MessageStruct("delay message,  delay 8s, " + DateUtil.now()), message -> {
				message.getMessageProperties().setHeader("x-delay", 8000);
				return message;
			});
	}

}

