<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>BladeX-Biz</artifactId>
        <groupId>org.springblade</groupId>
        <version>*******.RELEASE</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>blade-example</artifactId>
    <name>${project.artifactId}</name>
    <version>*******.RELEASE</version>
    <packaging>pom</packaging>
    <description>BladeX 微服务范例集合</description>

    <modules>
        <module>blade-dubbo-consumer</module>
        <module>blade-dubbo-provider</module>
        <module>blade-easypoi</module>
        <module>blade-mq-kafka</module>
        <module>blade-mq-rabbit</module>
        <module>blade-seata-order</module>
        <module>blade-seata-storage</module>
        <module>blade-websocket</module>
        <module>blade-stream-consumer</module>
        <module>blade-stream-provider</module>
    </modules>

    <dependencies>
        <dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-biz-common</artifactId>
        </dependency>
    </dependencies>

</project>
