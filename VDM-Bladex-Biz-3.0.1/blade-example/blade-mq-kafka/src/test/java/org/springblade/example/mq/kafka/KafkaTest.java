package org.springblade.example.mq.kafka;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springblade.core.test.BladeBootTest;
import org.springblade.core.test.BladeSpringExtension;
import org.springblade.example.mq.kafka.constant.KafkaConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.core.KafkaTemplate;

@ExtendWith(BladeSpringExtension.class)
@SpringBootTest(classes = KafkaApplication.class)
@BladeBootTest(appName = "blade-kafka", profile = "test", enableLoader = true)
public class KafkaTest {
    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;

    /**
     * 测试发送消息
     */
    @Test
    public void testSend() {
        kafkaTemplate.send(KafkaConstant.TOPIC_TEST, "hello,kafka...");
    }

}

