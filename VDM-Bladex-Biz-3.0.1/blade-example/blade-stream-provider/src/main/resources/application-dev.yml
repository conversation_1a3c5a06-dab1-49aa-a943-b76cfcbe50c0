spring:
  cloud:
    stream:
      # 进行rabbit的相关绑定配置
      rabbit:
        bindings:
          stream-output:
            # 进行生产端端配置
            producer:
              #定义 Routing<PERSON>ey 的表达式配置
              routing-key-expression: '''stream-key'''
      # 在此处配置要绑定的rabbitmq的服务信息；
      binders:
        # 表示定义的名称，用于于binding整合
        default-rabbit:
          # 消息组件类型
          type: rabbit
          # 设置rabbitmq的相关的环境配置
          environment:
            spring:
              rabbitmq:
                host: 127.0.0.1
                port: 5672
                username: guest
                password: guest
                virtual-host: /
      # 服务的整合处理
      bindings:
        # 设定通道的名称
        stream-output:
          # 设定Exchange名称定义
          destination: queue.stream.messages
          # 设定消息类型，对象类型，如果是文本则设置"text/plain"
          content-type: application/json
          # 设置要绑定的消息服务的定义名称
          binder: default-rabbit
          # 进行操作的分组，表示持久化
          group: stream-group
  #数据源配置
  datasource:
    url: ${blade.datasource.dev.url}
    username: ${blade.datasource.dev.username}
    password: ${blade.datasource.dev.password}

