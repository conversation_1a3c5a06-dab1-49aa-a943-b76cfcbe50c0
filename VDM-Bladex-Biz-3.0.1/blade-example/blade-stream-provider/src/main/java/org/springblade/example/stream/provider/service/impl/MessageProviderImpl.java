/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.example.stream.provider.service.impl;

import lombok.AllArgsConstructor;
import org.springblade.example.stream.api.model.StreamBody;
import org.springblade.example.stream.api.process.StreamProcess;
import org.springblade.example.stream.provider.service.IMessageProvider;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.stereotype.Component;

/**
 * MessageProviderImpl
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@EnableBinding(StreamProcess.class)
public class MessageProviderImpl implements IMessageProvider {

	/**
	 * 消息的发送管道
	 */
	private final StreamProcess process;

	@Override
	public void send(StreamBody body) {
		process.output().send(MessageBuilder.withPayload(body).build());
	}

}
