/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.example.stream;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springblade.core.test.BladeBootTest;
import org.springblade.core.test.BladeSpringExtension;
import org.springblade.example.stream.api.model.StreamBody;
import org.springblade.example.stream.provider.StreamProviderApplication;
import org.springblade.example.stream.provider.service.IMessageProvider;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * StreamProviderTest
 *
 * <AUTHOR>
 */
@ExtendWith(BladeSpringExtension.class)
@SpringBootTest(classes = StreamProviderApplication.class)
@BladeBootTest(appName = "blade-rabbit", profile = "test", enableLoader = true)
public class StreamProviderTest {

	@Resource
	private IMessageProvider messageProvider;

	/**
	 * 测试通道模式发送
	 */
	@Test
	public void sendChannel() {
		StreamBody sb = new StreamBody();
		sb.setTitle("this is title");
		sb.setMessage("this is message");
		messageProvider.send(sb);
	}

}
