package org.springblade.example.poi;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> j<PERSON><PERSON><PERSON> on 19-3-29.
 */
public class GongHeTiShu {

	public static void main(String[] args) {
		List<Map<String, String>> list = ExcelImportUtil.importExcel(new File(""), Map.class, new ImportParams());

		for (int i = 0; i < list.size(); i++) {

		}
	}
}
