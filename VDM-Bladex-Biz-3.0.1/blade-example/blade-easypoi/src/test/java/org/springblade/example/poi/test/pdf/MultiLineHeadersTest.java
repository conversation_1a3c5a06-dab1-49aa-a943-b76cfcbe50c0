/**
 * Copyright 2013-2015 Ju<PERSON><PERSON>ue (<EMAIL>)
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.example.poi.test.pdf;

import cn.afterturn.easypoi.pdf.PdfExportUtil;
import cn.afterturn.easypoi.pdf.entity.PdfExportParams;
import com.itextpdf.text.Document;
import org.junit.Before;
import org.junit.Test;
import org.springblade.example.poi.test.entity.CourseEntity;
import org.springblade.example.poi.test.entity.StudentEntity;
import org.springblade.example.poi.test.entity.TeacherEntity;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 多行表头导出
 * <AUTHOR>
 *   2016年1月6日 下午9:57:52
 */
public class MultiLineHeadersTest {

	List<CourseEntity> list = new ArrayList<CourseEntity>();
	CourseEntity courseEntity;

	@Before
	public void testBefore() {

		for (int i = 0; i < 50; i++) {
			courseEntity = new CourseEntity();
			courseEntity.setId("1131");
			courseEntity.setName("海贼王必修(" + (i + 1) + ")");

			TeacherEntity teacherEntity = new TeacherEntity();
			teacherEntity.setId("12131231");
			teacherEntity.setName("路飞");
			courseEntity.setChineseTeacher(teacherEntity);

			teacherEntity = new TeacherEntity();
			teacherEntity.setId("121312314312421131");
			teacherEntity.setName("老王");
			courseEntity.setMathTeacher(teacherEntity);

			StudentEntity studentEntity = new StudentEntity();
			studentEntity.setId("11231");
			studentEntity.setName("撒旦法司法局");
			studentEntity.setBirthday(new Date());
			studentEntity.setSex(1);
			List<StudentEntity> studentList = new ArrayList<StudentEntity>();
			studentList.add(studentEntity);
			studentList.add(studentEntity);
			courseEntity.setStudents(studentList);
			list.add(courseEntity);
		}
	}

	@Test
	public void testExportPdf() {
		PdfExportParams params = new PdfExportParams("绝月的测试", "作者绝月");
		Document document = null;
		try {
			File file = new File("D:/home/<USER>//MultiLineHeadersTest.testExportPdf.pdf");
			file.createNewFile();
			document = PdfExportUtil.exportPdf(params, CourseEntity.class, list,
				new FileOutputStream(file));
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			document.close();
		}
	}

}
