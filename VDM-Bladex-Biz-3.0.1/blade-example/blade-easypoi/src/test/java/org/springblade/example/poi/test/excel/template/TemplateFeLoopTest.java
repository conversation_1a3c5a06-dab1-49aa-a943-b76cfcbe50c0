package org.springblade.example.poi.test.excel.template;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import org.springblade.example.poi.test.excel.export.ExcelExportOneToManyHaseNameTest;
import org.springblade.example.poi.test.excel.handler.ExcelDictHandlerImpl;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> jue<PERSON>e on 19-9-26.
 */
public class TemplateFeLoopTest {

	@Test
	public void nestedLoopTest() {
		try {
			Map<String, Object> map = new HashMap<String, Object>();
			map.put("list", ExcelExportOneToManyHaseNameTest.getList());
			TemplateExportParams params = new TemplateExportParams(
				"doc/nestedloop.xlsx");
			params.setDictHandler(new ExcelDictHandlerImpl());
			Workbook workbook = ExcelExportUtil.exportExcel(params, map);
			FileOutputStream fos = new FileOutputStream("D:/home/<USER>/TemplateFeLoopTest.nestedLoopTest.xlsx");
			workbook.write(fos);
			fos.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

}
