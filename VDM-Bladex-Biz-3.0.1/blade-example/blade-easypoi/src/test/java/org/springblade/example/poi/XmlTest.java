package org.springblade.example.poi;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;

/**
 * <AUTHOR> j<PERSON><PERSON><PERSON> on 19-10-9.
 */
public class XmlTest {

	@Test
	public void test() throws Exception {
		BufferedReader rows = new BufferedReader(new InputStreamReader(new FileInputStream("C:\\Users\\<USER>\\Desktop\\山东组织机构.xml")));
		StringBuilder xml = new StringBuilder();
		String str = "";
		while ((str = rows.readLine()) != null) {
			xml.append(str);
		}
		Document doc = Jsoup.parse(xml.toString());
		Elements elements = doc.getElementsByClass("TreeViewUnit_0");
		for (int i = 0; i < elements.size(); i++) {
			System.out.println(elements.get(i).text());
		}

	}
}
