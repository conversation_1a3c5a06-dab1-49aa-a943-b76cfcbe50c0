/**
 * Copyright 2013-2015 JueYue (<EMAIL>)
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.example.poi.test.entity;

import cn.afterturn.easypoi.excel.annotation.Excel;

public class StudentEntityImage extends StudentEntity {

	/**
	 *
	 */
	private static final long serialVersionUID = 1L;

	@Excel(height = 40, width = 60, type = 2, name = "相片")
	private String image;

	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}

}
