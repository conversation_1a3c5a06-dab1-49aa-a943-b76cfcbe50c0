package org.springblade.example.poi.view;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.afterturn.easypoi.view.PoiBaseView;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import cn.afterturn.easypoi.entity.vo.NormalExcelConstants;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import org.springblade.example.poi.test.entity.MsgClient;
import org.springblade.example.poi.test.entity.MsgClientGroup;

@Controller
@RequestMapping("/EasypoiSingleExcelViewTest")
public class EasypoiSingleExcelViewTest {

	@RequestMapping()
	public String download(ModelMap map) {
		List<MsgClient> list = new ArrayList<MsgClient>();
		for (int i = 0; i < 100; i++) {
			MsgClient client = new MsgClient();
			client.setBirthday(new Date());
			client.setClientName("小明" + i);
			client.setClientPhone("18797" + i);
			client.setCreateBy("JueYue");
			client.setId("1" + i);
			client.setRemark("测试" + i);
			MsgClientGroup group = new MsgClientGroup();
			group.setGroupName("测试" + i);
			client.setGroup(group);
			list.add(client);
		}
		ExportParams params = new ExportParams("2412312", "测试", ExcelType.XSSF);
		params.setFreezeCol(2);
		map.put(NormalExcelConstants.DATA_LIST, list);
		map.put(NormalExcelConstants.CLASS, MsgClient.class);
		map.put(NormalExcelConstants.PARAMS, params);
		return NormalExcelConstants.EASYPOI_EXCEL_VIEW;

	}

	/**
	 * 如果上面的方法不行,可以使用下面的用法
	 * 同样的效果,只不过是直接问输出了,不经过view了
	 *
	 * @param map
	 * @param request
	 * @param response
	 */

	@RequestMapping("load")
	public void downloadByPoiBaseView(ModelMap map, HttpServletRequest request,
									  HttpServletResponse response) {
		List<MsgClient> list = new ArrayList<MsgClient>();
		for (int i = 0; i < 100; i++) {
			MsgClient client = new MsgClient();
			client.setBirthday(new Date());
			client.setClientName("小明" + i);
			client.setClientPhone("18797" + i);
			client.setCreateBy("JueYue");
			client.setId("1" + i);
			client.setRemark("测试" + i);
			MsgClientGroup group = new MsgClientGroup();
			group.setGroupName("测试" + i);
			client.setGroup(group);
			list.add(client);
		}
		ExportParams params = new ExportParams("2412312", "测试", ExcelType.XSSF);
		params.setFreezeCol(2);
		map.put(NormalExcelConstants.DATA_LIST, list);
		map.put(NormalExcelConstants.CLASS, MsgClient.class);
		map.put(NormalExcelConstants.PARAMS, params);
		PoiBaseView.render(map, request, response, NormalExcelConstants.EASYPOI_EXCEL_VIEW);

	}
}
