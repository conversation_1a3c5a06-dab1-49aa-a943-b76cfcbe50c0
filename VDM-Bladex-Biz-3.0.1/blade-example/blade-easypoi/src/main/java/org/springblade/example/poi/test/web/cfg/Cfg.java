package org.springblade.example.poi.test.web.cfg;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.view.BeanNameViewResolver;

@Configuration(proxyBeanMethods = false)
public class Cfg {

	@Bean
	public BeanNameViewResolver getBeanNameViewResolver() {
		BeanNameViewResolver view = new BeanNameViewResolver();
		view.setOrder(-100);
		return view;
	}

}
