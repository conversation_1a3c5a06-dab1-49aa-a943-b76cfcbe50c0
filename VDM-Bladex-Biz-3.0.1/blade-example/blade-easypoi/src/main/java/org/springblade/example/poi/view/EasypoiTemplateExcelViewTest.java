package org.springblade.example.poi.view;

import cn.afterturn.easypoi.entity.vo.TemplateExcelConstants;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.view.PoiBaseView;
import org.springblade.example.poi.test.entity.temp.TemplateExcelExportEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/EasypoiTemplateExcelViewTest")
public class EasypoiTemplateExcelViewTest {

	@RequestMapping()
	public String download(ModelMap modelMap) {
		Map<String, Object> map = new HashMap<String, Object>();
		TemplateExportParams params = new TemplateExportParams(
			"doc/foreach.xlsx");
		List<TemplateExcelExportEntity> list = new ArrayList<TemplateExcelExportEntity>();

		for (int i = 0; i < 4; i++) {
			TemplateExcelExportEntity entity = new TemplateExcelExportEntity();
			entity.setIndex(i + 1 + "");
			entity.setAccountType("开源项目");
			entity.setProjectName("EasyPoi " + i + "期");
			entity.setAmountApplied(i * 10000 + "");
			entity.setApprovedAmount((i + 1) * 10000 - 100 + "");
			list.add(entity);
		}
		map.put("entitylist", list);
		map.put("manmark", "1");
		map.put("letest", "********");
		map.put("fntest", "********.2341234");
		map.put("fdtest", null);
		List<Map<String, Object>> mapList = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < 1; i++) {
			Map<String, Object> testMap = new HashMap<String, Object>();

			testMap.put("id", "xman");
			testMap.put("name", "小明" + i);
			testMap.put("sex", "1");
			mapList.add(testMap);
		}
		map.put("maplist", mapList);

		mapList = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < 6; i++) {
			Map<String, Object> testMap = new HashMap<String, Object>();

			testMap.put("si", "xman");
			mapList.add(testMap);
		}
		map.put("sitest", mapList);
		modelMap.put(TemplateExcelConstants.FILE_NAME, "用户信息");
		modelMap.put(TemplateExcelConstants.PARAMS, params);
		modelMap.put(TemplateExcelConstants.MAP_DATA, map);
		return TemplateExcelConstants.EASYPOI_TEMPLATE_EXCEL_VIEW;

	}

	/**
	 * 如果上面的方法不行,可以使用下面的用法
	 * 同样的效果,只不过是直接问输出了,不经过view了
	 *
	 * @param map
	 * @param request
	 * @param response
	 */

	@RequestMapping("load")
	public void downloadByPoiBaseView(ModelMap modelMap, HttpServletRequest request,
									  HttpServletResponse response) {
		Map<String, Object> map = new HashMap<String, Object>();
		TemplateExportParams params = new TemplateExportParams(
			"doc/foreach.xlsx");
		List<TemplateExcelExportEntity> list = new ArrayList<TemplateExcelExportEntity>();

		for (int i = 0; i < 4; i++) {
			TemplateExcelExportEntity entity = new TemplateExcelExportEntity();
			entity.setIndex(i + 1 + "");
			entity.setAccountType("开源项目");
			entity.setProjectName("EasyPoi " + i + "期");
			entity.setAmountApplied(i * 10000 + "");
			entity.setApprovedAmount((i + 1) * 10000 - 100 + "");
			list.add(entity);
		}
		map.put("entitylist", list);
		map.put("manmark", "1");
		map.put("letest", "********");
		map.put("fntest", "********.2341234");
		map.put("fdtest", null);
		List<Map<String, Object>> mapList = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < 1; i++) {
			Map<String, Object> testMap = new HashMap<String, Object>();

			testMap.put("id", "xman");
			testMap.put("name", "小明" + i);
			testMap.put("sex", "1");
			mapList.add(testMap);
		}
		map.put("maplist", mapList);

		mapList = new ArrayList<Map<String, Object>>();
		for (int i = 0; i < 6; i++) {
			Map<String, Object> testMap = new HashMap<String, Object>();

			testMap.put("si", "xman");
			mapList.add(testMap);
		}
		map.put("sitest", mapList);
		modelMap.put(TemplateExcelConstants.FILE_NAME, "用户信息");
		modelMap.put(TemplateExcelConstants.PARAMS, params);
		modelMap.put(TemplateExcelConstants.MAP_DATA, map);
		PoiBaseView.render(modelMap, request, response,
			TemplateExcelConstants.EASYPOI_TEMPLATE_EXCEL_VIEW);

	}

}
