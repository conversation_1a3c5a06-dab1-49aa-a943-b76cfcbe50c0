/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.example.stream.consumer.handler;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springblade.example.stream.api.model.StreamBody;
import org.springblade.example.stream.api.process.StreamProcess;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.cloud.stream.annotation.StreamListener;
import org.springframework.stereotype.Component;

/**
 * StreamChannelHandler
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@EnableBinding(StreamProcess.class)
public class StreamInputHandler {

	/**
	 * 对消息的监听处理
	 */
	@StreamListener(StreamProcess.INPUT)
	private void receiver(StreamBody body) {
		log.info("hello : " + JsonUtil.toJson(body));
	}

}
