FROM adoptopenjdk/openjdk8-openj9:alpine-slim

MAINTAINER <EMAIL>

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

RUN apk add -U tzdata \
    && cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone \
    && apk add --no-cache bash bash-doc bash-completion \
    && apk del tzdata \
    && rm -rf /var/cache/apk/*

RUN mkdir -p /blade/desk

WORKDIR /blade/desk

EXPOSE 8105

ADD ./target/blade-demo.jar ./app.jar

ENTRYPOINT java ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -jar app.jar

CMD ["--spring.profiles.active=test"]
