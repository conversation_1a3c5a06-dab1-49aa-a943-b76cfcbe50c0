package com.xh.vdm.bd.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.bd.service.ITerminalCommLogService;
import com.xh.vdm.bd.vo.response.CommLogResponse;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 终端协议通信日志逻辑层。
 */
@RestController
@RequestMapping("/comm")
public class CommLogController {
	@Resource
	private ITerminalCommLogService logService;

	@GetMapping("/list")
	public R<IPage<CommLogResponse>> queryByPage(Query query, Integer deviceType, String deviceNum,
												 String uniqueId, Byte event, Byte errCode, Long startTime, Long endTime, BladeUser user) {
		if ((user == null) || StringUtils.isBlank(user.getRoleName())) {
			return R.fail(ResultCode.FAILURE.getCode(), "用户登录信息异常。");
		}

		// TODO 查询用户是否有访问终端的权限
		return R.data(logService.queryByPage(query, deviceType, deviceNum, uniqueId, event, errCode, startTime, endTime));
	}
}
