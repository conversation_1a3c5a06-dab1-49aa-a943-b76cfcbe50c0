package com.xh.vdm.bd.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bd.dto.BaseLocDTO;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.LocationKuduPage;
import com.xh.vdm.bd.entity.VehicleBase;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: kudu中的定位表 pos_gn.locations
 * @author: zhouxw
 * @date: 2023-05-143 19:10:14
 * @param: * @param null
 * @return:
 * @return: null
 **/
@DS("impala")
public interface LocationKuduMapper extends BaseMapper<LocationKudu> {

	/**
	 * @description: 向 kudu 中写入数据
	 * @author: zhouxw
	 * @date: 2023-05-143 19:47:19
	 * +
	 * @param: [list]
	 * @return: void
	 **/
	void saveToKudu(List<LocationKudu> list);

	/**
	 * @description: 查询上传有效定位点的车辆列表
	 * @author: zhouxw
	 * @date: 2023-06-159 13:47:13
	 * @param: [startTime, endTime]
	 * @return: java.util.List<com.xh.vdm.bd.entity.VehicleBase>
	 **/
	List<VehicleBase> getVehicleListForValid(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

	/**
	 * @description: 查询上传定位点的车辆列表，包括无效定位点
	 * @author: zhouxw
	 * @date: 2023-06-159 13:47:52
	 * @param: [startTime, endTime]
	 * @return: java.util.List<com.xh.vdm.bd.entity.VehicleBase>
	 **/
	List<VehicleBase> getVehicleListForAll(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

	/**
	 * 查询轨迹点
	 *
	 * @param targetId 目标实体id
	 * @param targetType 目标类别
	 * @param deviceId 设备id
	 * @param deviceType 设备类别
	 * @param valid 定位有效性
	 * @param batch 定位补传标记
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 轨迹
	 */
	List<LocationKudu> getLocations (@Param("targetId") Long targetId, @Param("targetType") Integer targetType,
									 @Param("deviceId") Long deviceId, @Param("deviceType") Integer deviceType,
									 @Param("valid") Byte valid, @Param("batch") Byte batch,
									 @Param("startTime") Long startTime, @Param("endTime") Long endTime,
									@Param("posSystem") Byte posSystem);


	/**
	 * 查询轨迹点（分页）
	 *
	 * @param targetId 目标实体id
	 * @param targetType 目标类别
	 * @param deviceId 设备id
	 * @param deviceType 设备类别
	 * @param valid 定位有效性
	 * @param batch 定位补传标记
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 轨迹
	 */
	List<LocationKudu> getLocationsPage (@Param("startIndex") Long startIndex, @Param("endIndex") Long endIndex, @Param("targetId") Long targetId, @Param("targetType") Integer targetType,
											 @Param("deviceId") Long deviceId, @Param("deviceType") Integer deviceType,
											 @Param("valid") Byte valid, @Param("batch") Byte batch,
											 @Param("startTime") Long startTime, @Param("endTime") Long endTime,
											 @Param("posSystem") Byte posSystem);


	/**
	 * 根据条件查询数据总量
	 * @param targetId
	 * @param targetType
	 * @param deviceId
	 * @param deviceType
	 * @param valid
	 * @param batch
	 * @param startTime
	 * @param endTime
	 * @param posSystem
	 * @return
	 */
	long getLocationsCount (@Param("targetId") Long targetId, @Param("targetType") Integer targetType,
										   @Param("deviceId") Long deviceId, @Param("deviceType") Integer deviceType,
										   @Param("valid") Byte valid, @Param("batch") Byte batch,
										   @Param("startTime") Long startTime, @Param("endTime") Long endTime,
										   @Param("posSystem") Byte posSystem);

	/**
	 * 获取基础定位信息点。
	 * @param targetId 目标id
	 * @param deviceId 设备id
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 符合条件的基础定位信息点
	 */
	List<BaseLocDTO> getBaseLocs(@Param("targetId") Long targetId, @Param("deviceId") Long deviceId,
								 @Param("startTime") Long startTime, @Param("endTime") Long endTime,
								 @Param("valid") Byte valid, @Param("batch") Byte batch,
								 @Param("posSystem") Byte posSystem);
}
