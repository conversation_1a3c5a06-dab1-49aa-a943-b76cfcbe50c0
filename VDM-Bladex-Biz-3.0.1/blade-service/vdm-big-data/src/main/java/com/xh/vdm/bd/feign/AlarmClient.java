package com.xh.vdm.bd.feign;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bd.entity.BdmSecurity;
import com.xh.vdm.bd.service.IBdmSecurityService;
import com.xh.vdm.bd.vo.request.AlarmRequest;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/7/11 22:07
 */
@RestController
@Slf4j
public class AlarmClient implements IAlarmClient{

	@Resource
	private IBdmSecurityService securityService;

	@PostMapping(ALARM_INFO_PAGE)
	@Override
	public R<IPage<BdmSecurity>> alarmInfoPage(@RequestBody AlarmRequest request) {
		try{
			return R.data(securityService.findAlarmInfoPage(request));
		}catch (Exception e){
			log.error("查询数据出错",e);
			return R.fail("查询出错");
		}
	}

}
