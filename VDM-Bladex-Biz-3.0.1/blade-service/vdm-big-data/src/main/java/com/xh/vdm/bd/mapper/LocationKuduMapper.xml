<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bd.mapper.LocationKuduMapper">

    <select id="saveToKudu" parameterType="com.xh.vdm.bd.entity.LocationKudu">
        insert into locations (
        licence_plate,
        licence_color,
        loc_time,
        longitude,
        latitude,
        phone,
        altitude,
        speed,
        recv_time,
        bearing,
        mileage,
        satellite_num,
        alarm_flag,
        state_flag,
        valid,
        expand_signal,
        io_status,
        temperature,
        batch,
        areacode,
        curareacode)
            values
                <foreach collection="list" item="item" close=")" open="(" separator="," >
                    (
                        #{item.licencePlate, jdbcType=VARCHAR},
                        #{item.licenceColor, jdbcType=BIGINT},
                        #{item.locTime, jdbcType=BIGINT},
                        #{item.longitude, jdbcType=DOUBLE},
                        #{item.latitude, jdbcType=DOUBLE},
                        #{item.phone, jdbcType=VARCHAR},
                        #{item.altitude, jdbcType=BIGINT},
                        #{item.speed, jdbcType=DOUBLE},
                        #{item.recvTime, jdbcType=BIGINT},
                        #{item.bearing, jdbcType=BIGINT},
                        #{item.mileage, jdbcType=DOUBLE},
                        #{item.satelliteNum, jdbcType=BIGINT},
                        #{item.alarmFlag, jdbcType=BIGINT},
                        #{item.stateFlag, jdbcType=BIGINT},
                        #{item.valid, jdbcType=BIGINT},
                        #{item.expandSignal, jdbcType=BIGINT},
                        #{item.ioStatus, jdbcType=BIGINT},
                        #{item.temperature, jdbcType=VARCHAR},
                        #{item.batch, jdbcType=BIGINT},
                        #{item.areacode, jdbcType=BIGINT},
                        #{item.curareacode, jdbcType=BIGINT}
                    )

                </foreach>
    </select>


    <select id="getVehicleListForAll" resultType="com.xh.vdm.bd.entity.VehicleBase">
        select distinct licence_plate licencePlate, licence_color licenceColor from locations where loc_time >= #{startTime,jdbcType=BIGINT} and loc_time &lt; #{endTime,jdbcType=BIGINT}
    </select>

    <select id="getVehicleListForValid" resultType="com.xh.vdm.bd.entity.VehicleBase">
        select distinct licence_plate licencePlate, licence_color licenceColor from locations where loc_time >= #{startTime,jdbcType=BIGINT} and loc_time &lt; #{endTime,jdbcType=BIGINT} and valid > 0
    </select>

    <select id="getLocations" resultType="com.xh.vdm.bd.entity.LocationKudu">
        select * from locations
        <where>
            <if test="targetId != null">
                and target_id = #{targetId}
            </if>
            <if test="targetType != null">
                and target_type = #{targetType}
            </if>
            <if test="deviceId != null">
                and device_id = #{deviceId}
            </if>
            <if test="deviceType != null">
                and device_type = #{deviceType}
            </if>
            <if test="valid != null">
                and valid = #{valid}
            </if>
            <if test="batch != null">
                and batch = #{batch}
            </if>
            <if test="startTime != null">
                and time &gt;= #{startTime, jdbcType=BIGINT}
            </if>
            <if test="endTime != null">
                and time &lt;= #{endTime, jdbcType=BIGINT}
            </if>
            <if test="posSystem != null">
                and pos_system = #{posSystem}
            </if>
        </where>
        order by time
    </select>

    <select id="getLocationsPage" resultType="com.xh.vdm.bd.entity.LocationKudu">
        select * from (
            select row_number() over(order by time) as rn, * from locations
            <where>
                <if test="targetId != null">
                    and target_id = #{targetId}
                </if>
                <if test="targetType != null">
                    and target_type = #{targetType}
                </if>
                <if test="deviceId != null">
                    and device_id = #{deviceId}
                </if>
                <if test="deviceType != null">
                    and device_type = #{deviceType}
                </if>
                <if test="valid != null">
                    and valid = #{valid}
                </if>
                <if test="batch != null">
                    and batch = #{batch}
                </if>
                <if test="startTime != null">
                    and time &gt;= #{startTime, jdbcType=BIGINT}
                </if>
                <if test="endTime != null">
                    and time &lt;= #{endTime, jdbcType=BIGINT}
                </if>
                <if test="posSystem != null">
                    and pos_system = #{posSystem}
                </if>
            </where>
            order by time
        ) a
        where rn >= #{startIndex,jdbcType=BIGINT} and rn &lt; #{endIndex}
    </select>

    <select id="getLocationsCount" resultType="long">
        select count(*) from locations
        <where>
            <if test="targetId != null">
                and target_id = #{targetId}
            </if>
            <if test="targetType != null">
                and target_type = #{targetType}
            </if>
            <if test="deviceId != null">
                and device_id = #{deviceId}
            </if>
            <if test="deviceType != null">
                and device_type = #{deviceType}
            </if>
            <if test="valid != null">
                and valid = #{valid}
            </if>
            <if test="batch != null">
                and batch = #{batch}
            </if>
            <if test="startTime != null">
                and time &gt;= #{startTime, jdbcType=BIGINT}
            </if>
            <if test="endTime != null">
                and time &lt;= #{endTime, jdbcType=BIGINT}
            </if>
            <if test="posSystem != null">
                and pos_system = #{posSystem}
            </if>
        </where>
    </select>

    <resultMap id="baseLocDTO" type="com.xh.vdm.bd.dto.BaseLocDTO">
        <result property="lat" column="latitude"/>
        <result property='lng' column="longitude"/>
        <result property="time" column="time"/>
    </resultMap>

    <select id="getBaseLocs" resultMap="baseLocDTO">
        select longitude, latitude, time from locations
        <where>
            <if test="targetId != null">
                and target_id = #{targetId}
            </if>
            <if test="deviceId != null">
                and device_id = #{deviceId}
            </if>
            <if test="startTime != null">
                and time &gt;= #{startTime, jdbcType=BIGINT}
            </if>
            <if test="endTime != null">
                and time &lt;= #{endTime, jdbcType=BIGINT}
            </if>
            <if test="valid != null">
                and valid = #{valid}
            </if>
            <if test="batch != null">
                and batch = #{batch}
            </if>
            <if test="posSystem != null">
                and pos_system = #{posSystem}
            </if>
        </where>
        order by time
    </select>
</mapper>
