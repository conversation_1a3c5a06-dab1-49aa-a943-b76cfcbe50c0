/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.bd.feign;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.LocationKuduPage;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.bd.service.ILocationKuduService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Notice Feign
 *
 * <AUTHOR>
 */
@ApiIgnore()
@RestController
@AllArgsConstructor
@Slf4j
public class BDClient implements ILocationClient {

	@Resource
	private ILocationKuduService kuduService;

	@Override
	@GetMapping(LOCATION)
	public R<List<LocationKudu>> location (
		@RequestParam(value = "target_id", required = false) Long targetId,
		@RequestParam(value = "target_type", required = false) Integer targetType,
		@RequestParam(value = "device_id", required = false) Long deviceId,
		@RequestParam(value = "device_type", required = false) Integer deviceType,
		@RequestParam(value = "start_time", required = true) Long startTime,
		@RequestParam(value = "end_time", required = true) Long endTime,
		@RequestParam(value = "valid", required = false) Byte valid,
		@RequestParam(value = "batch", required = false) Byte batch,
		@RequestParam(value = "pos_system", required = false) Byte posSystem
	) {
		if ((null == targetId && null != targetType) || (null != targetId && null == targetType)) {
			return R.fail("请同时指定目标id和目标类别");
		}

		if ((null == deviceId && null != deviceType) || (null != deviceId && null == deviceType)) {
			return R.fail("请同时指定设备id和设备类别");
		}

		// 优先按终端查（当目标和终端参数都有时）
		if (null != deviceId) {
			targetId = null;
			targetType = null;
		}

		return R.data(this.kuduService.findLocation(targetId, targetType, deviceId, deviceType, valid, batch, startTime, endTime, posSystem));
	}

	/**
	 * @description: 查询上传定位点数据的车辆列表
	 * @author: zhouxw
	 * @date: 2023-06-159 14:57:41
	 * @param: [isFilter, startTime, endTime]
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.VehicleBase>>
	 **/
	@GetMapping(VEHICLE_LIST)
	@Override
	public R<List<VehicleBase>> getVehicleList(Integer isFilter, Long startTime, Long endTime) {
		if(startTime == null || endTime == null){
			log.error("查询参数不完整，startTime={},endTime={}", startTime, endTime);
			return R.fail("查询参数不完整");
		}
		List<VehicleBase> list = new ArrayList<>();
		if(isFilter != null && isFilter > 0){
			//过滤无效点，只查询上传有效轨迹点数据的车辆列表
			list = kuduService.findVehicleListForValid(startTime, endTime);
		}else{
			list = kuduService.findVehicleListForAll(startTime, endTime);
		}
		return R.data(list);
	}

	@GetMapping(LOCATION_PAGE)
	public R<LocationKuduPage> locationByPage (
		@RequestParam(value = "target_id", required = false) Long targetId,
		@RequestParam(value = "target_type", required = false) Integer targetType,
		@RequestParam(value = "device_id", required = false) Long deviceId,
		@RequestParam(value = "device_type", required = false) Integer deviceType,
		@RequestParam(value = "valid", required = false) Byte valid,
		@RequestParam(value = "batch", required = false) Byte batch,
		@RequestParam(value = "start_time", required = true) Long startTime,
		@RequestParam(value = "end_time", required = true) Long endTime,
		@RequestParam(value = "pos_system",required = false ) Byte posSystem,
		@RequestParam(value = "start_index", required = true) Long startIndex,
		@RequestParam(value = "end_index", required = true) Long endIndex
	) {

		if(startIndex == null || startIndex < 0){
			startIndex = 0L;
		}

		if(endIndex == null || endIndex < 0){
			endIndex = 1000L;
		}

		if ((null == targetId && null != targetType) || (null != targetId && null == targetType)) {
			return R.fail("请同时指定目标id和目标类别");
		}

		if ((null == deviceId && null != deviceType) || (null != deviceId && null == deviceType)) {
			return R.fail("请同时指定设备id和设备类别");
		}

		// 优先按终端查（当目标和终端参数都有时）
		if (null != deviceId) {
			targetId = null;
			targetType = null;
		}
		return R.data(this.kuduService.findLocationPage(startIndex, endIndex, targetId, targetType, deviceId, deviceType, valid, batch, startTime, endTime, posSystem));
	}


	/**
	 * @description: 根据定位时间查询车辆的定位点数据列表
	 * 如果不指定定位时间列表，则不返回数据
	 * @author: zhouxw
	 * @date: 2023-06-159 16:44:15
	 * @param: [licencePlate, licenceColor, locTimes]
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.LocationKudu>>
	 **/
	/*@Override
	public R<List<LocationKudu>> locationForVehicleTimes(String licencePlate, Long licenceColor, String locTimes) {
		if(StringUtils.isEmpty(licencePlate) || licenceColor == null){
			log.error("查询参数不完整，licencePlate={}, licenceColor={}", licencePlate, licenceColor);
			return R.fail("查询参数不完整");
		}
		if(StringUtils.isEmpty(locTimes)){
			return R.data(null);
		}
		LambdaQueryWrapper<LocationKudu> wrapper = Wrappers.lambdaQuery(LocationKudu.class).eq(LocationKudu::getLicencePlate, licencePlate).eq(LocationKudu::getLicenceColor, licenceColor).in(LocationKudu::getLocTime, locTimes);
		List<LocationKudu> list = kuduService.list(wrapper);
		return R.data(list);
	}*/
}
