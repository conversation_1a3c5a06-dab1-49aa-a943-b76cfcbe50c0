<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bd.mapper.BdmSecurityMapper">

    <sql id="queryCondition">
        <if test="request.idList != null and request.idList.size() gt 0">
            and vehicle_id in
            <foreach collection="request.idList" item="id" index="i" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>

        <if test="request.startTime != null">
            and alarm_time >= #{request.startTime}
        </if>
        <if test="request.endTime != null">
            and alarm_time &lt;= #{request.endTime}
        </if>
        <if test="request.alarmLevel != null and request.alarmLevel.size gt 0">
            and alarm_level in
            <foreach collection="request.alarmLevel" item="al" separator="," open="(" close=")">
                #{al}
            </foreach>
        </if>
        <if test="request.deptIdList != null  and request.deptIdList.size() gt 0">
            and dept_id in
            <foreach collection="request.deptIdList" item="deptId" index="i" open="(" close=")" separator="," >
                #{deptId}
            </foreach>
        </if>
        <if test="request.alarmTypeList != null  and request.alarmTypeList.size() gt 0">
            and alarm_type in
            <foreach collection="request.alarmTypeList" item="alarmType" index="i" open="(" close=")" separator="," >
                #{alarmType}
            </foreach>
        </if>
        <if test="request.alarmLevelList != null  and request.alarmLevelList.size() gt 0">
            and alarm_level in
            <foreach collection="request.alarmLevelList" item="alarmLevel" index="i" open="(" close=")" separator="," >
                #{alarmLevel}
            </foreach>
        </if><if test="request.vehicleUseTypeList != null  and request.vehicleUseTypeList.size() gt 0">
            and vehicle_use_type in
            <foreach collection="request.vehicleUseTypeList" item="vehicleUseType" index="i" open="(" close=")" separator="," >
                #{vehicleUseType}
            </foreach>
        </if>
        <if test="request.serverState != null">
            and server_state = #{request.serverState}
        </if>
        <if test="request.thirdState != null">
            and third_state = #{request.thirdState}
        </if>
        <if test="request.companyState != null">
            and company_state = #{request.companyState}
        </if>
        <if test="request.appealState != null">
            and appeal_state = #{request.appealState}
        </if>
        <if test="request.companyMeasures != null">
            and company_measures = #{request.companyMeasures}
        </if>

        <if test="request.isWrong != null">
            and is_wrong = #{request.isWrong}
        </if>
        <if test="request.duration != null">
            and alarm_end_time - alarm_time  >= #{request.duration} * 60
        </if>
        <if test="request.alarmSpeed != null">
            and speed  >= #{request.alarmSpeed}
        </if>
		<if test="request.alarmOrigin != null">
            and alarm_origin = #{request.alarmOrigin}
        </if>
		<if test="request.ruleName != null">
			and rule_name like concat('%,', #{request.ruleName}, ',%')
		</if>

        <if test="request.phone != null">
            and phone = #{request.phone}
        </if>
        <if test="request.serviceState != null">
            and service_state = #{request.serviceState}
        </if>
        <if test="request.alarmComplete != null">
            and alarm_complete = #{request.alarmComplete,jdbcType=VARCHAR}
        </if>
        <if test="request.idCard != null">
            and id_card = #{request.idCard}
        </if>
        <if test="request.driverName != null">
            and driver_name = #{request.driverName}
        </if>
        <if test="request.thirdPlatform != null">
            and third_platform like  concat('%,',#{request.thirdPlatform}, ',%')
        </if>
    </sql>

    <select id="getAlarmInfoPage" parameterType="com.xh.vdm.bd.vo.request.AlarmRequest" resultType="com.xh.vdm.bd.entity.BdmSecurity">
        select * from alarms where 1 = 1
        <include refid="queryCondition" />
        order by alarm_time desc
        <if test="request.limit != null and request.offset != null">
            limit #{request.limit,jdbcType=INTEGER} offset #{request.offset,jdbcType=INTEGER}
        </if>
    </select>

    <select id="getAlarmInfoPageNoZeroAlarmLevel" parameterType="com.xh.vdm.bd.vo.request.AlarmRequest" resultType="com.xh.vdm.bd.entity.BdmSecurity">
        select * from alarms where 1 = 1
        and alarm_level != 0
        <include refid="queryCondition" />
        order by alarm_time desc
        <if test="request.limit != null and request.offset != null">
            limit #{request.limit,jdbcType=INTEGER} offset #{request.offset,jdbcType=INTEGER}
        </if>
    </select>



    <select id="getAlarmInfoCount" parameterType="com.xh.vdm.bd.vo.request.AlarmRequest" resultType="long">
        select count(*) from alarms where 1 = 1
        <include refid="queryCondition" />
    </select>

    <select id="getAlarmInfoCountNoZeroAlarmLevel" parameterType="com.xh.vdm.bd.vo.request.AlarmRequest" resultType="long">
        select count(*) from alarms where 1 = 1
        and alarm_level != 0
        <include refid="queryCondition" />
    </select>


</mapper>
