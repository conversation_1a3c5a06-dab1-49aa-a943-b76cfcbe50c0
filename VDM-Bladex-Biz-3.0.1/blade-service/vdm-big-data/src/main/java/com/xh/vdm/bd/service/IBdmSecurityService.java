package com.xh.vdm.bd.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bd.entity.BdmSecurity;
import com.xh.vdm.bd.vo.request.AlarmRequest;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-03
 */
public interface IBdmSecurityService extends IService<BdmSecurity> {

	/**
	 * @description: 查询报警信息
	 * @author: zhouxw
	 * @date: 2023-07-192 19:17:36
	 * @param: [licencePlate, licenceColor, startTime, endTime]
	 * @return: java.util.List<com.xh.vdm.bd.entity.BdmSecurity>
	 **/
	List<BdmSecurity> findAlarmInfo(AlarmRequest request) throws Exception;


	/**
	 * 查询报警信息
	 * 可以控制是否包含0级报警
	 * @param request
	 * @param isContainZeroAlarmLevel false 表示不包含0级报警，则会添加条件  and alarm_level != 0
	 * @return
	 * @throws Exception
	 */
	List<BdmSecurity> findAlarmInfo(AlarmRequest request, boolean isContainZeroAlarmLevel) throws Exception;

	/**
	 * @description: 查询报警数量
	 * @author: zhouxw
	 * @date: 2023-07-192 21:40:03
	 * @param: [request]
	 * @return: long
	 **/
	long findAlarmCount(AlarmRequest request) throws Exception;

	/**
	 * 查询报警数量
	 * 可以控制是否包含0级报警
	 * @param request
	 * @param isContainZeroAlarmLevel false，表示不包含0级报警，那么就要添加 and alarm_level != 0 的条件
	 * @return
	 * @throws Exception
	 */
	long findAlarmCount(AlarmRequest request, boolean isContainZeroAlarmLevel) throws Exception;

	/**
	 * @description: 分页查询报警信息
	 * @author: zhouxw
	 * @date: 2023-07-192 21:51:19
	 * @param: [request]
	 * @return: com.baomidou.mybatisplus.core.metadata.IPage<com.xh.vdm.bd.entity.BdmSecurity>
	 **/
	IPage<BdmSecurity> findAlarmInfoPage(AlarmRequest request) throws Exception;


}
