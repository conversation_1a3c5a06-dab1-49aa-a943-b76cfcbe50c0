<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bd.mapper.LocationHDFSMapper">

    <select id="saveToHDFS" parameterType="com.xh.vdm.bd.entity.LocationHDFS">
        insert into locations_d (
        licence_plate,
        licence_color,
        loc_time,
        longitude,
        latitude,
        phone,
        altitude,
        speed,
        recv_time,
        bearing,
        mileage,
        satellite_num,
        alarm_flag,
        state_flag,
        valid,
        expand_signal,
        io_status,
        temperature,
        batch,
        areacode,
        curareacode)
        values
        <foreach collection="list" item="item" close=")" open="(" separator="," >
            (
            #{item.licencePlate, jdbcType=VARCHAR},
            #{item.licenceColor, jdbcType=BIGINT},
            #{item.locTime, jdbcType=BIGINT},
            #{item.longitude, jdbcType=DOUBLE},
            #{item.latitude, jdbcType=DOUBLE},
            #{item.phone, jdbcType=VARCHAR},
            #{item.altitude, jdbcType=BIGINT},
            #{item.speed, jdbcType=DOUBLE},
            #{item.recvTime, jdbcType=BIGINT},
            #{item.bearing, jdbcType=BIGINT},
            #{item.mileage, jdbcType=DOUBLE},
            #{item.satelliteNum, jdbcType=BIGINT},
            #{item.alarmFlag, jdbcType=BIGINT},
            #{item.stateFlag, jdbcType=BIGINT},
            #{item.valid, jdbcType=BIGINT},
            #{item.expandSignal, jdbcType=BIGINT},
            #{item.ioStatus, jdbcType=BIGINT},
            #{item.temperature, jdbcType=VARCHAR},
            #{item.batch, jdbcType=BIGINT},
            #{item.areacode, jdbcType=BIGINT},
            #{item.curareacode, jdbcType=BIGINT}
            )

        </foreach>
    </select>
</mapper>
