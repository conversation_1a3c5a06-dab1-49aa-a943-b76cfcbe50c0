package com.xh.vdm.bd.dto;

import lombok.Data;
import org.jetbrains.annotations.NotNull;

/**
 * 基本定位信息。
 */
@Data
public class BaseLocDTO implements Comparable<BaseLocDTO> {
	/** 纬度 */
	private Double lat;
	/** 经度 */
	private Double lng;
	/** 时间 */
	private Long time;

	public BaseLocDTO() {}

	public BaseLocDTO(double lat, double lng, long time) {
		this.lat = lat;
		this.lng = lng;
		this.time = time;
	}

	public boolean equalLatLng(BaseLocDTO other) {
		return lat == other.lat && lng == other.lng;
	}

	@Override
	public boolean equals(Object o) {
		if (!(o instanceof BaseLocDTO))
			return false;

		BaseLocDTO obj = (BaseLocDTO) o;
		return lat == obj.lat && lng == obj.lng && time == obj.time;
	}

	@Override
	public int compareTo(@NotNull BaseLocDTO o) {
		return time.compareTo(o.time);
	}
}
