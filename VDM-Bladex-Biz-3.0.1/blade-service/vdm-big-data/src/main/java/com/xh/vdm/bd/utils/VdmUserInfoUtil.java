package com.xh.vdm.bd.utils;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.AlarmConstant;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.Dept;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.user.entity.BdmVehicle;
import org.springblade.system.user.feign.IUserClient;
import org.springblade.system.vo.DictTreeNodeVO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 用户信息解析
 */
@Component
@Slf4j
public class VdmUserInfoUtil {


	@Resource
	private ISysClient sysClient;

	@Resource
	private IDictBizClient dictBizClient;

	@Resource
	private IUserClient userClient;

	/**
	 * 查询 当前部门及子部门 的部门id
	 * @param user
	 * @return
	 * @throws Exception
	 */
	public List<Long> getChildrenAndSelfDeptId(BladeUser user) throws Exception{
		List<Long> list = new ArrayList<>();
		//部门可能会存在多个
		String[] deptIds = user.getDeptId().split(",");
		Set<Long> set = new HashSet<>();
		for(String deptId : deptIds){
			R<List<Dept>> resDept = sysClient.getDeptChild(Long.parseLong(deptId));
			if(resDept == null || !resDept.isSuccess()){
				throw new Exception("查询部门feign接口失败");
			}
			for(Dept d : resDept.getData()){
				set.add(d.getId());
			}
			set.add(Long.parseLong(deptId));
		}
		list.addAll(set);
		return list;
	}

	/**
	 * 查询 当前部门及子部门 的部门id
	 * @return
	 * @throws Exception
	 */
	public List<Long> getChildrenAndSelfDeptId(Long deptId) throws Exception{
		List<Long> list = new ArrayList<>();
		R<List<Dept>> resDept = sysClient.getDeptChild(deptId);
		if(resDept == null || !resDept.isSuccess()){
			throw new Exception("查询部门feign接口失败");
		}
		for(Dept d : resDept.getData()){
			list.add(d.getId());
		}
		list.add(deptId);
		return list;
	}

	/**
	 * 查询 当前部门的子部门id
	 * @param deptId
	 * @return
	 * @throws Exception
	 */
	public List<Long> getChildrenDeptId(Long deptId) throws Exception{
		List<Long> list = new ArrayList<>();
		R<List<Dept>> resDept = sysClient.getDeptChild(deptId);
		if(resDept == null || !resDept.isSuccess()){
			throw new Exception("查询部门feign接口失败");
		}
		for(Dept d : resDept.getData()){
			list.add(d.getId());
		}
		return list;
	}

	/**
	 * 获取登录用户关联的车辆列表
	 * @param user
	 * @return
	 * @throws Exception
	 */
	public List<BdmVehicle> getVehicleListByUserId(BladeUser user) throws Exception {
		//用户关联的车辆
		List<BdmVehicle> vehicleList = null;
		R<List<BdmVehicle>> userVehicleRes = userClient.findVehiclesByUserId(user.getUserId());
		if(userVehicleRes == null || !userVehicleRes.isSuccess()){
			throw new Exception("查询用户关联车辆信息feign接口失败");
		}
		vehicleList = userVehicleRes.getData();
		return vehicleList;
	}

	/**
	 * 获取登录用户关联的车辆列表
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	public List<BdmVehicle> getVehicleListByUserId(Long userId) throws Exception {
		//用户关联的车辆
		List<BdmVehicle> vehicleList = null;
		R<List<BdmVehicle>> userVehicleRes = userClient.findVehiclesByUserId(userId);
		if(userVehicleRes == null || !userVehicleRes.isSuccess()){
			throw new Exception("查询用户关联车辆信息feign接口失败");
		}
		vehicleList = userVehicleRes.getData();
		return vehicleList;
	}

	/**
	 * 获取用户关联的车辆id列表
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	public List<Integer> getVehicleIdList(Long userId) throws Exception{
		List<BdmVehicle> vehicleList =  getVehicleListByUserId(userId);
		List<Integer> vehicleIds = new ArrayList<>();
		vehicleList.forEach(item -> {
			vehicleIds.add(item.getId());
		});
		return vehicleIds;
	}



	/**
	 * 获取超速报警类型
	 * @return
	 */
	public static List<Integer> getOverSpeedAlarmType(){
		List<Integer> list = new ArrayList<>();
		//分段限速报警(平台)
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_SEG_SPEED_LIMIT);
		//夜间限速报警(平台)
		list.add((int)AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED_NIGHT);
		//道路限速报警(平台)
		list.add((int)AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED_ROAD);
		return list;
	}

	/**
	 * 获取疲劳驾驶报警类型
	 * @return
	 */
	public static List<Integer> getFatigueAlarmType(){
		List<Integer> list = new ArrayList<>();
		//累计驾驶疲劳（平台）
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_ACCUMULATE_TIRED);
		//疲劳驾驶报警(平台)
		list.add((int)AlarmConstant.DICT_ALARM_TYPE_TIRED_PLATFORM);
		return list;
	}

	/**
	 * 获取夜间异动报警类型
	 * @return
	 */
	public static List<Integer> getNightLimitAlarmType(){
		List<Integer> list = new ArrayList<>();
		//禁行异动报警(平台)
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_PLAT_MOVE_LIMIT);
		return list;
	}


	/**
	 * 获取主动安全报警类型
	 * @return
	 */
	public static List<Integer> getActiveAlarmType(){
		List<Integer> list = new ArrayList<>();
		//前向碰撞预警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_CAR_COLLISION);
		//车道偏离报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_LANE_DEPARTURE);
		//车距过近报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_CLOSE_DISTANCE);
		//行人碰撞报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_PEDESTRIAN_COLLISION);
		//驾驶辅助功能失效报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_FUNCTION_EXPIRE);
		//生理疲劳报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_FATIGUED);
		//接打电话报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_PHONE);
		//抽烟报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_SMOKE);
		//长时间不目视前方报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_DISTRACTION);
		return list;
	}

	/**
	 * 获取紧急报警类型
	 * @return
	 */
	public static List<Integer> getEmergencyAlarmType(){
		List<Integer> list = new ArrayList<>();
		//紧急报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_EMERGENCY);
		return list;
	}


	/**
	 * 获取adas报警类型
	 * @return
	 */
	public static List<Integer> getAdasAlarmType(){
		List<Integer> list = new ArrayList<>();
		//前向碰撞预警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_CAR_COLLISION);
		//车道偏离报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_LANE_DEPARTURE);
		//车距过近报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_CLOSE_DISTANCE);
		//行人碰撞报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_PEDESTRIAN_COLLISION);
		//驾驶辅助功能失效报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_FUNCTION_EXPIRE);
		return list;
	}

	/**
	 * 查询DSM报警类型
	 * @return
	 */
	public static List<Integer> getDsmAlarmType(){
		List<Integer> list = new ArrayList<>();
		//生理疲劳报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_FATIGUED);
		//接打电话报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_PHONE);
		//抽烟报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_SMOKE);
		//长时间不目视前方报警
		list.add((int) AlarmConstant.DICT_ALARM_TYPE_DISTRACTION);
		return list;
	}

	private Set<DictTreeNodeVO> getChildrenFlatAlarmTypesByTenantId (String code, String key, String tenantId) {
		//查找所有子报警类型
		DictTreeNodeVO overSpeedTreeNode = null;
		log.info(">> getChildrenFlatAlarmTypes tenantId is {}", tenantId);
		try{
			R<DictTreeNodeVO> res = dictBizClient.getDictTreeByCodeAndKeyCacheTenant(code, key, tenantId);
			if(res == null || res.getData() == null){
				log.error("未查询到子类型");
				overSpeedTreeNode = null;
			}else{
				overSpeedTreeNode = res.getData();
			}
		}catch (Exception e){
			log.error("查询子类型失败",e);
			overSpeedTreeNode = null;
		}
		log.info(">> getChildrenFlatAlarmTypes code={} key={} 获取到的结果为{}", code, key , JSON.toJSONString(overSpeedTreeNode));
		Set<DictTreeNodeVO> set = new HashSet<>();
		if(overSpeedTreeNode != null){
			set = getDictChildFlat(overSpeedTreeNode, set);
		}
		return set;
	}

	/**
	 * @description: 递归遍历字典
	 * @author: zhouxw
	 * @date: 2023-06-165 20:17:17
	 * @param: [vo, list]
	 * @return: java.util.List<org.springblade.system.vo.DictTreeNodeVO>
	 **/
	private Set<DictTreeNodeVO> getDictChildFlat(DictTreeNodeVO vo, Set<DictTreeNodeVO> set){
		if(vo != null ){
			if(vo.getChildren() != null && vo.getChildren().size() > 0){
				List<DictTreeNodeVO> tmpList = vo.getChildren();
				for(DictTreeNodeVO v : tmpList){
					set.addAll(getDictChildFlat(v, set));
				}
			}else{
				set.add(vo);
			}
		}
		return set;
	}

}
