package com.xh.vdm.bd.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.xh.vdm.bd.config.ExcelExportFontAndStyleConfig;
import com.xh.vdm.bd.config.ExcelExportRowHeightConfig;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2022/11/1 3:09 PM
 */
public class EasyExcelUtils {

	//轨迹点导出代理路径
    public static final String LOCATION_EXPORT_PROXY_PATH = "/vdm/locations/files/";

    /**
     * @description: 使用 easyexcel 导出数据
     * @author: zhouxw
     * @date: 2022/12/1 9:59 AM
     * @param: [fileSavedPath：文件存储路径, fileProxyPath：文件代理路径，如使用 nginx 代理时通过解析该路径找到目标文件, title：表格标题, list：数据列表, exportClass：文件导出时的数据类，fileNameIsContainDate: 当导出日报、月报时，文件名称中需要带有当日或者当月的日期，而不应该带有当天的日期，普通导出设置为 false]
     * @return: java.lang.String
     **/
    public static String export(String fileSavedPath , String fileProxyPath ,String title , List list , Class exportClass,boolean fileNameIsContainDate) throws FileNotFoundException {
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String dateStr = format.format(date);
        String fileName = "";
        if(fileNameIsContainDate){
            //如果文件名称中带有日期，则不再添加日期
            fileName = title + ".xlsx";
        }else{
            fileName = title + "_" + dateStr + ".xlsx";
        }
        FileOutputStream file = new FileOutputStream(fileSavedPath + fileName);
        fileName =  fileProxyPath + fileName;
        //主标题和副标题在excel中分别是是第0和第1行
        List<Integer> columnIndexes = Arrays.asList(0,1);
        //自定义标题和内容策略(具体定义在下文)
        ExcelExportFontAndStyleConfig cellStyleStrategy =
                new ExcelExportFontAndStyleConfig(columnIndexes,new WriteCellStyle(), new WriteCellStyle());

        EasyExcel.write(file, exportClass)
                .registerWriteHandler(cellStyleStrategy)
                .registerWriteHandler(new ExcelExportRowHeightConfig()).sheet(title).doWrite(list);
        return fileName;
    }
}
