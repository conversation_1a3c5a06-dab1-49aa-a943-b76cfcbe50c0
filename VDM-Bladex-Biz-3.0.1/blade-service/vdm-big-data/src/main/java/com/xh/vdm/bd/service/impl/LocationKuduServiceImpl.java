package com.xh.vdm.bd.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.dto.BaseLocDTO;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.LocationKuduPage;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.bd.mapper.LocationKuduMapper;
import com.xh.vdm.bd.service.ILocationKuduService;
import org.springblade.geo.GeoUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/5/24 13:34
 */
@Service
@DS("impala")
public class LocationKuduServiceImpl extends ServiceImpl<LocationKuduMapper, LocationKudu> implements ILocationKuduService {


	@Override
	public long saveValidBatch(List<LocationKudu> list) throws Exception {
		try{
			List<LocationKudu> li = new ArrayList<>();
			for(int i = 0 ; i < list.size(); i++){
				LocationKudu item = list.get(i);
				if(item.getTargetId() == null || item.getTargetType() == null
					|| item.getDeviceId() == null || null == item.getDeviceType()) {
					continue;
				}
				li.add(list.get(i));
			}
			if(li.size() < 1){
				return 0;
			}
			baseMapper.saveToKudu(li);
			return li.size();
		}catch (Exception e){
			log.error("[kudu] mapper保存数据失败",e);
			return 0;
		}
	}

	@Override
	public List<VehicleBase> findVehicleListForValid(Long startTime, Long endTime) {
		return baseMapper.getVehicleListForValid(startTime, endTime);
	}

	@Override
	public List<VehicleBase> findVehicleListForAll(Long startTime, Long endTime) {
		return baseMapper.getVehicleListForAll(startTime, endTime);
	}

	@Override
	public List<LocationKudu> findLocation (Long targetId, Integer targetType, Long deviceId,
											Integer deviceType, Byte valid, Byte batch, Long startTime, Long endTime, Byte posSystem) {
		return this.baseMapper.getLocations(targetId, targetType, deviceId, deviceType, valid, batch, startTime, endTime, posSystem);
	}

	@Override
	public LocationKuduPage findLocationPage(Long startIndex, Long endIndex, Long targetId, Integer targetType, Long deviceId, Integer deviceType, Byte valid, Byte batch, Long startTime, Long endTime, Byte posSystem) {
		//1.查询分页数据
		List<LocationKudu> locationList = baseMapper.getLocationsPage(startIndex, endIndex, targetId, targetType, deviceId, deviceType, valid, batch, startTime, endTime, posSystem);

		//2.查询数据总量
		long count = baseMapper.getLocationsCount(targetId, targetType, deviceId, deviceType, valid, batch, startTime, endTime, posSystem);

		//3.拼装数据
		LocationKuduPage page = new LocationKuduPage();
		page.setData(locationList);
		page.setTotal(count);

		return page;
	}

//	@Override
//	public List<TrackSegment> findTrackSegmentPage(Long targetId, Long deviceId,
//												   Long startTime, Long endTime, boolean byInterval) {
//		// 1.获取该时段范围内的坐标和时间
//		List<BaseLocDTO> baseLocDTOS = baseMapper.getBaseLocs(targetId, deviceId, startTime, endTime);
//		if (CollectionUtils.isEmpty(baseLocDTOS)) return new ArrayList<>();
//
//		List<TrackSegment> segments = new ArrayList<>();
//
//		// 2.计算两点间的平均速度，如果超过160km/h，就进行轨迹分段
//		BaseLocDTO lastLoc = null;
//		TrackSegment segment = null;
//		for (BaseLocDTO baseLoc : baseLocDTOS) {
//			if (null == lastLoc) {
//				lastLoc = baseLoc;
//				segment = new TrackSegment();
//				segment.startTm = baseLoc.getTime();
//				continue;
//			}
//
//			// 点相同，去重
//			if (baseLoc.equals(lastLoc))
//				continue;
//
//			// 时间相同，去重（理论上不执行）
//			long interval = baseLoc.getTime() - lastLoc.getTime();
//			if (0 == interval)
//				continue;
//
//			// 时间间隔超过5min，分段
//			if (byInterval && interval >= 300) {
//				if (0 != segment.endTm)
//					segments.add(segment);
//
//				lastLoc = baseLoc;
//				segment = new TrackSegment();
//				segment.startTm = baseLoc.getTime();
//				continue;
//			}
//
//			// 计算距离
//			double dist = GeoUtils.haversineDistance(lastLoc.getLat(), lastLoc.getLng(), baseLoc.getLat(), baseLoc.getLng());
//			if (dist/interval >= 45) { // 车速≥45m/s
//				if (0 != segment.endTm)
//					segments.add(segment);
//
//				lastLoc = baseLoc;
//				segment = new TrackSegment();
//				segment.startTm = baseLoc.getTime();
//				continue;
//			}
//
//			lastLoc = baseLoc;
//			segment.endTm = baseLoc.getTime();
//			++segment.posNum;
//		}
//
//		// 添加最后一个分段
//		if (0 != segment.endTm)
//			segments.add(segment);
//
//		// 避免返回为空
//		if (segments.isEmpty())
//			segments.add(new TrackSegment(startTime, endTime, baseLocDTOS.size()));
//
//		return segments;
//	}

	@Override
	public List<BaseLocDTO> findBaseLoc(Long targetId, Long deviceId, Byte valid,
										Byte batch, Byte posSystem, Long startTime, Long endTime) {
		return baseMapper.getBaseLocs(targetId, deviceId, startTime, endTime, valid, batch, posSystem);
	}
}
