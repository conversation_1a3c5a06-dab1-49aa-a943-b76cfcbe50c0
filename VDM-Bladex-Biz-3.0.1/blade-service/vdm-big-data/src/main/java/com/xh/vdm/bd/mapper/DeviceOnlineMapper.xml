<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bd.mapper.DeviceOnlineMapper">

    <resultMap id="devOnlineDTO" type="com.xh.vdm.bd.dto.DevOnlineDTO">
        <result property="startTm" column="start_time"/>
        <result property="endTm" column="end_time"/>
    </resultMap>

    <select id="getDevOnlineDTOS" resultMap="devOnlineDTO">
        select start_time,end_time from bdm_device_online
        <where>
            <if test="targetId != null">
                and target_id = #{targetId}
            </if>
            <if test="deviceId != null">
                and device_id = #{deviceId}
            </if>
            <if test="startTime != null">
                and (end_time &gt; #{startTime} or end_time is null)
            </if>
            <if test="endTime != null">
                and start_time &lt; #{endTime}
            </if>
        </where>
        order by start_time
    </select>
</mapper>
