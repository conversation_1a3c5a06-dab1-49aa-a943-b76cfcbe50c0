package com.xh.vdm.bd.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bd.entity.BdmSecurity;
import com.xh.vdm.bd.service.IBdmSecurityService;
import com.xh.vdm.bd.utils.AlarmBusiUtil;
import com.xh.vdm.bd.utils.MathUtil;
import com.xh.vdm.bd.utils.VdmUserInfoUtil;
import com.xh.vdm.bd.vo.request.AlarmRequest;
import com.xh.vdm.bd.vo.response.BdmSecurityVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.EasyExcelUtils;
import org.springblade.common.utils.SplitListUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.StringUtil;
import org.springblade.system.cache.SysCache;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/7/7 14:59
 */
@RestController
@RequestMapping("/bd/alarm")
@Slf4j
public class AlarmController {

	@Value("${static.file.path}")
	private String staticFilePath;

	@Value("${proxy.file.path}")
	private String proxyFilePath;

	@Value("${alarm.export.limit}")
	private Long limit ;

	@Resource
	private IBdmSecurityService service;

	@Resource
	private AlarmBusiUtil busiUtil;

	@Resource
	private IDictBizClient dictBizClient;

	@Resource
	private VdmUserInfoUtil userInfoUtil;

	private ExecutorService threadPool = new ThreadPoolExecutor(10,20,100, TimeUnit.SECONDS,new LinkedBlockingQueue<Runnable>());

	/**
	 * @description: 查询报警数据
	 * @author: zhouxw
	 * @date: 2023-07-192 19:21:42
	 * @param: [licencePlate, licenceColor, startTime, endTime]
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.BdmSecurity>>
	 **/
	@PostMapping("/alarmInfo")
	public R<IPage<BdmSecurityVO>> alarmInfo(@RequestBody AlarmRequest request, BladeUser user){
		try{
			// 获取登录用户本下级部门ID列表
			List<Long> deptIdList = userInfoUtil.getChildrenAndSelfDeptId(user);

			request.setDeptIdList(deptIdList);

			// 判断用户查询数据范围权限
			if (StringUtil.containsIgnoreCase(user.getRoleName(), CommonConstant.ROLE_ADMINISTRATOR)) {
				request.setAlarmLevelList(null);
			} else {
				//request.setAlarmLevelList(CommonConstant.ALARM_LEVEL_DISPLAY_LIMIT);
			}
			IPage<BdmSecurity> page = service.findAlarmInfoPage(request);
			IPage<BdmSecurityVO> pageRes = new Page<>();
			pageRes.setSize(page.getSize());
			pageRes.setCurrent(page.getCurrent());
			pageRes.setTotal(page.getTotal());
			pageRes.setPages(page.getPages());
			List<BdmSecurityVO> list = new ArrayList<>();
			//日期格式转换
			if(page.getRecords() != null){
				page.getRecords().forEach(item -> {
					BdmSecurityVO vo = new BdmSecurityVO();
					BeanUtils.copyProperties(item, vo);
					vo.setLicenceColor(StringUtils.isBlank(item.getLicenceColor()) ? "" : item.getLicenceColor());
					vo.setAlarmDealState((item.getAlarmDealState() == null) ? "" : item.getAlarmDealState().toString());
					vo.setAlarmType((item.getAlarmType() == null) ? "" : item.getAlarmType().toString());
					vo.setAlarmLevel((item.getAlarmLevel() == null) ? "" : item.getAlarmLevel().toString());
					vo.setSpeed((item.getSpeed() == null) ? "-" : String.valueOf(MathUtil.roundDouble(item.getSpeed(), 2)));
					vo.setMaxSpeed(((item.getMaxSpeed() == null) || (item.getMaxSpeed() <= 0)) ? "-" : String.valueOf(MathUtil.roundDouble(item.getMaxSpeed(), 2)));
					vo.setLimitSpeed(((item.getLimitSpeed() == null) || (item.getLimitSpeed() <= 0)) ? "-" : String.valueOf(MathUtil.roundDouble(item.getLimitSpeed(), 2)));
					vo.setVehicleSpeed((item.getVehicleSpeed() == null) ? "-" : String.valueOf(MathUtil.roundDouble(item.getVehicleSpeed(), 2)));
					vo.setStartTime((item.getAlarmTime() == null) ? "" : DateUtil.getDateTimeString(item.getAlarmTime()));
					vo.setEndTime((item.getAlarmEndTime() == null) ? "" : DateUtil.getDateTimeString(item.getAlarmEndTime()));
					vo.setServerTimeStr((item.getServerTime() == null) ? "" : DateUtil.getDateTimeString(item.getServerTime()));
					vo.setThirdTimeStr((item.getThirdTime() == null) ? "" : DateUtil.getDateTimeString(item.getThirdTime()));
					vo.setCompanyTimeStr((item.getCompanyTime() == null) ? "" : DateUtil.getDateTimeString(item.getCompanyTime()));
					vo.setAppealTimeStr((item.getAppealTime() == null) ? "" : DateUtil.getDateTimeString(item.getAppealTime()));
					vo.setAuditTimeStr((item.getAuditTime() == null) ? "" : DateUtil.getDateTimeString(item.getAuditTime()));
					vo.setDrivingTimeStr((item.getDrivingTime() == null) ? "" : DateUtil.getDateTimeString(item.getDrivingTime()));
					vo.setFatigueStartTimeStr((item.getFatigueStartTime() == null) ? "" : DateUtil.getDateTimeString(item.getFatigueStartTime()));
					vo.setFatigueEndTimeStr((item.getFatigueEndTime() == null) ? "" : DateUtil.getDateTimeString(item.getFatigueEndTime()));
					vo.setCreateTimeStr((item.getCreateTime() == null) ? "" : DateUtil.getDateTimeString(item.getCreateTime()));
					vo.setUpdateTimeStr((item.getUpdateTime() == null) ? "" : DateUtil.getDateTimeString(item.getUpdateTime()));
					vo.setAlarmComplete(String.valueOf((item.getAlarmComplete() == null) ? 0 : item.getAlarmComplete()));
					vo.setServiceState((item.getServiceState() == null) ? "" : item.getServiceState().toString());
					vo.setServerState(String.valueOf((item.getServerState() == null) ? 0 : item.getServerState()));
					vo.setThirdState(String.valueOf((item.getThirdState() == null) ? 0 : item.getThirdState()));
					vo.setCompanyState(String.valueOf((item.getCompanyState() == null) ? 0 : item.getCompanyState()));
					vo.setAppealState(String.valueOf((item.getAppealState() == null) ? 0 : item.getAppealState()));
					vo.setCompanyMeasures(StringUtils.isBlank(item.getCompanyMeasures()) ? "" : item.getCompanyMeasures());
					vo.setServerUser((item.getServerUser() == null) ? "" : this.busiUtil.getUsernameByUserId(item.getServerUser()));
					vo.setThirdUser((item.getThirdUser() == null) ? "" : this.busiUtil.getUsernameByUserId(item.getThirdUser()));
					vo.setCompanyUser((item.getCompanyUser() == null) ? "" : this.busiUtil.getUsernameByUserId(item.getCompanyUser()));
					vo.setAppealUser((item.getAppealUser() == null) ? "" : this.busiUtil.getUsernameByUserId(item.getAppealUser()));
					vo.setAuditUser((item.getAuditUser() == null) ? "" : this.busiUtil.getUsernameByUserId(item.getAuditUser()));
					vo.setVehicleUseType((item.getVehicleUseType() == null) ? "" : item.getVehicleUseType().toString());
					vo.setRoadType((item.getRoadType() == null) ? "" : item.getRoadType().toString());
					vo.setMileage((item.getMileage() == null) ? "-" : String.valueOf(MathUtil.roundDouble(item.getMileage(), 2)));
					vo.setAlarmDmsAppendix(String.valueOf((item.getAlarmDmsAppendix() == null) ? 0 : item.getAlarmDmsAppendix()));
					vo.setIsAppeal(String.valueOf((item.getIsAppeal() == null) ? 0 : item.getIsAppeal()));
					vo.setIsWrong(String.valueOf((item.getIsWrong() == null) ? 0 : item.getIsWrong()));
					vo.setReportingState("");
					vo.setRemoveAlarm((item.getRemoveAlarm() == null) ? "" : item.getRemoveAlarm().toString());
					vo.setPunishState(String.valueOf((item.getPunishState() == null) ? 0 : item.getPunishState()));
					vo.setProtocolType((item.getProtocolType() == null) ? "" : item.getProtocolType().toString());
					vo.setDealOperateState(String.valueOf((item.getDealOperateState() == null) ? 0 : item.getDealOperateState()));

					list.add(vo);
				});
				pageRes.setRecords(list);
			}
			return R.data(ResultCode.SUCCESS.getCode(), pageRes, "");
		}catch (Exception e){
			log.error("查询数据失败",e);
			return R.fail("查询失败");
		}
	}


	/**
	 * @description: 导出报警数据
	 * @author: zhouxw
	 * @date: 2023-07-192 19:21:42
	 * @param: [licencePlate, licenceColor, startTime, endTime]
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.BdmSecurity>>
	 **/
	@PostMapping("/export")
	public R<String>  export(@RequestBody AlarmRequest request, BladeUser user){
		try{
			// 获取登录用户本下级部门ID列表
			List<Long> deptIdList = SysCache.getDeptChildIds(Long.valueOf(user.getDeptId()));

			request.setDeptIdList(deptIdList);

			// 判断用户查询数据范围权限
			if (StringUtil.containsIgnoreCase(user.getRoleName(), CommonConstant.ROLE_ADMINISTRATOR)) {
				request.setAlarmLevelList(null);
			} else {
				//request.setAlarmLevelList(CommonConstant.ALARM_LEVEL_DISPLAY_LIMIT);
			}
			request.setLimit(limit.intValue());
			request.setOffset(0);
			List<BdmSecurity> alarmInfoList = service.findAlarmInfo(request);

			List exportList = new ArrayList<>();
			//日期格式转换
			if(alarmInfoList!= null && alarmInfoList.size() > 0){

				R<Map<String, String>> vehicleUseTypeMap = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_VEHICLE_USE_TYPE, "-1");
				if (!vehicleUseTypeMap.isSuccess()) {
					return R.fail(vehicleUseTypeMap.getCode(), vehicleUseTypeMap.getMsg());
				}
				if ((vehicleUseTypeMap.getData() == null) || vehicleUseTypeMap.getData().isEmpty()) {
					return R.fail(ResultCode.FAILURE.getCode(), "行业类型数据获取异常。");
				}

				R<Map<String, String>>  companyMeasuresMap =  dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.COMPANY_MEASURES_CODE,"-1");
				if (!companyMeasuresMap.isSuccess()) {
					return R.fail(companyMeasuresMap.getCode(), companyMeasuresMap.getMsg());
				}
				if ((companyMeasuresMap.getData() == null) || companyMeasuresMap.getData().isEmpty()) {
					return R.fail(ResultCode.FAILURE.getCode(), "企业处理措施数据获取异常。");
				}
				R<Map<String, String>>  serviceStateMap =  dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.SERVICE_STATE_CODE,"-1");
				if (!serviceStateMap.isSuccess()) {
					return R.fail(serviceStateMap.getCode(), serviceStateMap.getMsg());
				}
				if ((serviceStateMap.getData() == null) || serviceStateMap.getData().isEmpty()) {
					return R.fail(ResultCode.FAILURE.getCode(), "服务类型数据获取异常。");
				}

				R<Map<String, String>> licenceColorMap = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_LICENCE_COLOR, "-1");
				if (!licenceColorMap.isSuccess()) {
					return R.fail(licenceColorMap.getCode(), licenceColorMap.getMsg());
				}
				if ((licenceColorMap.getData() == null) || licenceColorMap.getData().isEmpty()) {
					return R.fail(ResultCode.FAILURE.getCode(), "车牌颜色数据获取异常。");
				}

				R<Map<String, String>> alarmTypeMap = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_ALARM_TYPE, "16");
				if (!alarmTypeMap.isSuccess()) {
					return R.fail(alarmTypeMap.getCode(), alarmTypeMap.getMsg());
				}
				if ((alarmTypeMap.getData() == null) || alarmTypeMap.getData().isEmpty()) {
					return R.fail(ResultCode.FAILURE.getCode(), "报警类型数据获取异常。");
				}

				R<Map<String, String>> alarmLevelMap = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_ALARM_LEVEL, "-1");
				if (!alarmLevelMap.isSuccess()) {
					return R.fail(alarmLevelMap.getCode(), alarmLevelMap.getMsg());
				}
				if ((alarmLevelMap.getData() == null) || alarmLevelMap.getData().isEmpty()) {
					return R.fail(ResultCode.FAILURE.getCode(), "报警等级数据获取异常。");
				}

				//报警处理状态
				R<Map<String, String>> operateStateMap = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_OPERATE_STATE, "-1");
				if (!operateStateMap.isSuccess()) {
					return R.fail(operateStateMap.getCode(), operateStateMap.getMsg());
				}
				if ((operateStateMap.getData() == null) || operateStateMap.getData().isEmpty()) {
					return R.fail(ResultCode.FAILURE.getCode(), "报警处理状态数据获取异常。");
				}

				//道路类型
				R<Map<String, String>> roadTypeMap = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_ROAD_TYPE, "-1");
				if (!roadTypeMap.isSuccess()) {
					return R.fail(roadTypeMap.getCode(), roadTypeMap.getMsg());
				}
				if ((roadTypeMap.getData() == null) || roadTypeMap.getData().isEmpty()) {
					return R.fail(ResultCode.FAILURE.getCode(), "报警处理状态数据获取异常。");
				}

				// 处理状态
				R<Map<String, String>> dealStateMap = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap("alarm_deal_state", "-1");
				if (!dealStateMap.isSuccess()) {
					return R.fail(dealStateMap.getCode(), dealStateMap.getMsg());
				}
				if ((dealStateMap.getData() == null) || dealStateMap.getData().isEmpty()) {
					return R.fail(ResultCode.FAILURE.getCode(), "处理状态数据获取异常。");
				}

				R<Map<String, String>> removeAlarmMap = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap("remove_alarm", "-1");
				if (!removeAlarmMap.isSuccess()) {
					return R.fail(removeAlarmMap.getCode(), removeAlarmMap.getMsg());
				}
				if ((removeAlarmMap.getData() == null) || removeAlarmMap.getData().isEmpty()) {
					return R.fail(ResultCode.FAILURE.getCode(), "解除报警状态数据获取异常。");
				}

				R<Map<String, String>> protocolTypeMap = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap("protocol_type", "-1");
				if (!protocolTypeMap.isSuccess()) {
					return R.fail(protocolTypeMap.getCode(), protocolTypeMap.getMsg());
				}
				if ((protocolTypeMap.getData() == null) || protocolTypeMap.getData().isEmpty()) {
					return R.fail(ResultCode.FAILURE.getCode(), "协议类型数据获取异常。");
				}

				// 多线程处理
				int numEachSubList = (int) Math.ceil(alarmInfoList.size() / 10.0);
				int numSubList = (int) Math.ceil(alarmInfoList.size() / (numEachSubList * 1.0));
				List<List<BdmSecurity>> splitList = SplitListUtils.split(alarmInfoList, numEachSubList);
//				List<List<BdmSecurity>> splitList = SplitListUtils.split(alarmInfoList, (int) Math.ceil(alarmInfoList.size() / 9.0));//拆分10个子list

				CountDownLatch countDownLatch = new CountDownLatch(numSubList);
				List<Future<List<BdmSecurityVO>>> futureList = new ArrayList<>();
				System.out.println("开始时间："+System.currentTimeMillis());
				for(List<BdmSecurity> subList : splitList) {
					Future<List<BdmSecurityVO>> submit  = threadPool.submit(()-> {
						List<BdmSecurityVO> midList = new ArrayList<>();
						for (BdmSecurity item : subList ) {
							BdmSecurityVO vo = new BdmSecurityVO();
							BeanUtils.copyProperties(item, vo);
							vo.setLicenceColor(StringUtils.isBlank(item.getLicenceColor()) ? "" : licenceColorMap.getData().getOrDefault(item.getLicenceColor(), ""));
							vo.setAlarmDealState((item.getAlarmDealState() == null) ? "" : dealStateMap.getData().getOrDefault(item.getAlarmDealState().toString(), ""));
							vo.setAlarmType((item.getAlarmType() == null) ? "" : alarmTypeMap.getData().getOrDefault(item.getAlarmType().toString(), ""));
							vo.setAlarmLevel((item.getAlarmLevel() == null) ? "" : alarmLevelMap.getData().getOrDefault(item.getAlarmLevel().toString(), ""));
							vo.setSpeed((item.getSpeed() == null) ? "-" : String.valueOf(MathUtil.roundDouble(item.getSpeed(), 2)));
							vo.setMaxSpeed(((item.getMaxSpeed() == null) || (item.getMaxSpeed() <= 0)) ? "-" : String.valueOf(MathUtil.roundDouble(item.getMaxSpeed(), 2)));
							vo.setLimitSpeed(((item.getLimitSpeed() == null) || (item.getLimitSpeed() <= 0)) ? "-" : String.valueOf(MathUtil.roundDouble(item.getLimitSpeed(), 2)));
							vo.setVehicleSpeed((item.getVehicleSpeed() == null) ? "-" : String.valueOf(MathUtil.roundDouble(item.getVehicleSpeed(), 2)));
							vo.setStartTime((item.getAlarmTime() == null) ? "" : DateUtil.getDateTimeString(item.getAlarmTime()));
							vo.setEndTime((item.getAlarmEndTime() == null) ? "" : DateUtil.getDateTimeString(item.getAlarmEndTime()));
							vo.setServerTimeStr((item.getServerTime() == null) ? "" : DateUtil.getDateTimeString(item.getServerTime()));
							vo.setThirdTimeStr((item.getThirdTime() == null) ? "" : DateUtil.getDateTimeString(item.getThirdTime()));
							vo.setCompanyTimeStr((item.getCompanyTime() == null) ? "" : DateUtil.getDateTimeString(item.getCompanyTime()));
							vo.setAppealTimeStr((item.getAppealTime() == null) ? "" : DateUtil.getDateTimeString(item.getAppealTime()));
							vo.setAuditTimeStr((item.getAuditTime() == null) ? "" : DateUtil.getDateTimeString(item.getAuditTime()));
							vo.setDrivingTimeStr((item.getDrivingTime() == null) ? "" : DateUtil.getDateTimeString(item.getDrivingTime()));
							vo.setFatigueStartTimeStr((item.getFatigueStartTime() == null) ? "" : DateUtil.getDateTimeString(item.getFatigueStartTime()));
							vo.setFatigueEndTimeStr((item.getFatigueEndTime() == null) ? "" : DateUtil.getDateTimeString(item.getFatigueEndTime()));
							vo.setCreateTimeStr((item.getCreateTime() == null) ? "" : DateUtil.getDateTimeString(item.getCreateTime()));
							vo.setUpdateTimeStr((item.getUpdateTime() == null) ? "" : DateUtil.getDateTimeString(item.getUpdateTime()));
							vo.setAlarmComplete(BdmSecurity.getAlarmCompleteName(item.getAlarmComplete()));
							vo.setServiceState((item.getServiceState() == null) ? "" : serviceStateMap.getData().getOrDefault(item.getServiceState().toString(), ""));
							vo.setServerState(BdmSecurity.getServerStateName(item.getServerState()));
							vo.setThirdState(BdmSecurity.getThirdStateName(item.getThirdState()));
							vo.setCompanyState(BdmSecurity.getCompanyStateName(item.getCompanyState()));
							vo.setAppealState(BdmSecurity.getAppealStateName(item.getAppealState()));
							vo.setCompanyMeasures(StringUtils.isBlank(item.getCompanyMeasures()) ? "" : companyMeasuresMap.getData().getOrDefault(item.getCompanyMeasures(), ""));
							vo.setServerUser((item.getServerUser() == null) ? "" : this.busiUtil.getUsernameByUserId(item.getServerUser()));
							vo.setThirdUser((item.getThirdUser() == null) ? "" : this.busiUtil.getUsernameByUserId(item.getThirdUser()));
							vo.setCompanyUser((item.getCompanyUser() == null) ? "" : this.busiUtil.getUsernameByUserId(item.getCompanyUser()));
							vo.setAppealUser((item.getAppealUser() == null) ? "" : this.busiUtil.getUsernameByUserId(item.getAppealUser()));
							vo.setAuditUser((item.getAuditUser() == null) ? "" : this.busiUtil.getUsernameByUserId(item.getAuditUser()));
							vo.setVehicleUseType((item.getVehicleUseType() == null) ? "" : vehicleUseTypeMap.getData().getOrDefault(item.getVehicleUseType().toString(), ""));
							vo.setRoadType((item.getRoadType() == null) ? "" : roadTypeMap.getData().getOrDefault(item.getRoadType().toString(), ""));
							vo.setMileage((item.getMileage() == null) ? "-" : String.valueOf(MathUtil.roundDouble(item.getMileage(), 2)));
							vo.setAlarmDmsAppendix(String.valueOf((item.getAlarmDmsAppendix() == null) ? 0 : item.getAlarmDmsAppendix()));
							vo.setIsWrong(((item.getIsWrong() == null) || (item.getIsWrong().equals(0))) ? "非误报" : "误报");
							vo.setIsAppeal(((item.getIsAppeal() == null) || (item.getIsAppeal().equals(0))) ? "未申诉" : "已申诉");
							vo.setReportingState("");
							vo.setRemoveAlarm((item.getRemoveAlarm() == null) ? "" : removeAlarmMap.getData().getOrDefault(item.getRemoveAlarm().toString(), ""));
							vo.setPunishState(((item.getPunishState() == null) || (item.getPunishState().equals(0))) ? "未开具" : "已开具");
							vo.setProtocolType((item.getProtocolType() == null) ? "" : protocolTypeMap.getData().getOrDefault(item.getProtocolType().toString(), ""));
							vo.setDealOperateState(((item.getDealOperateState() == null) || item.getDealOperateState().equals(0)) ? "未认领" : "已认领");
							if ((item.getAlarmTime() == null) || (item.getAlarmEndTime() == null)) {
								vo.setDuration("-");
							} else {
								String duration = DateUtil.timeFormat(item.getAlarmEndTime() - item.getAlarmTime());
								vo.setDuration(StringUtils.isBlank(duration) ? "-" : duration);
							}

							midList.add(vo);
						}
						return midList;
					});
					futureList.add(submit);
					countDownLatch.countDown();
				}
				countDownLatch.await();
				futureList.forEach(integerFuture -> {
					try {
						exportList.addAll(integerFuture.get());
					} catch (InterruptedException e) {
						e.printStackTrace();
					} catch (ExecutionException e) {
						e.printStackTrace();
					}
				});
				log.info("计数{}", exportList.size());
				log.info("结束时间："+System.currentTimeMillis());

			}

			return R.data(
					ResultCode.SUCCESS.getCode(),
					EasyExcelUtils.exportExcelDynamicsHead(this.staticFilePath, this.proxyFilePath, "安全事件列表", exportList , request.getHeadNameList(), request.getColumnNameList(), BdmSecurityVO.class, false),
					""
			);
		} catch (FileNotFoundException e) {
			log.error(e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		} catch (Exception e) {
			log.error("导出失败",e);
			return R.fail("导出失败");
		}
	}


}
