package com.xh.vdm.bd.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bd.dto.DevOnlineDTO;
import com.xh.vdm.bd.entity.DeviceOnline;

import java.util.List;

/**
 * 设备上下线记录服务。
 */
public interface IDeviceOnlineService extends IService<DeviceOnline> {

	/**
	 * 获取目标或终端的上下线记录。
	 * @param targetId 目标id
	 * @param deviceId 设备id
	 * @param startTime 上线时刻
	 * @param endTime 下线时刻
	 * @return 终端上下线记录列表
	 */
	List<DeviceOnline> getDeviceOnlines(Long targetId, Long deviceId, Long startTime, Long endTime);

	/**
	 * 获取目标或终端的上下线记录基础信息。
	 * @param targetId 目标id
	 * @param deviceId 设备id
	 * @param startTime 上线时刻
	 * @param endTime 下线时刻
	 * @return 终端上下线记录列表
	 */
	List<DevOnlineDTO> getDevOnlineDTOS(Long targetId, Long deviceId, Long startTime, Long endTime);
}
