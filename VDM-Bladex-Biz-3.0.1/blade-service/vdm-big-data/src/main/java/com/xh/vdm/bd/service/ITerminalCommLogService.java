package com.xh.vdm.bd.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bd.vo.response.CommLogResponse;
import org.springblade.core.mp.support.Query;

/**
 * 终端协议通信日志服务。
 */
public interface ITerminalCommLogService {

	/**
	 * 终端通信日志分页查询。
	 *
	 * @param query      分页条件
	 * @param deviceType 设备类型枚举
	 * @param deviceNum  设备赋码值
	 * @param uniqueId   终端号
	 * @param event      事件id
	 * @param errCode    错误码
	 * @param startTime  起始时间
	 * @param endTime    结束时间
	 * @return 分页查询结果
	 */
	IPage<CommLogResponse> queryByPage(Query query, Integer deviceType, String deviceNum,
									   String uniqueId, Byte event, Byte errCode, Long startTime, Long endTime);
}
