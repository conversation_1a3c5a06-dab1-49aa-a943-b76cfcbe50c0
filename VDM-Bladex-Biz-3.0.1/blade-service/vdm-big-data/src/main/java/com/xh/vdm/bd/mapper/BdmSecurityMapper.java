package com.xh.vdm.bd.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bd.entity.BdmSecurity;
import com.xh.vdm.bd.vo.request.AlarmRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报警数据处理
 * <AUTHOR>
 * @since 2023-07-03
 */
@DS("impala")
public interface BdmSecurityMapper extends BaseMapper<BdmSecurity> {

	/**
	 * @description: 查询报警信息
	 * @author: zhouxw
	 * @date: 2023-07-192 19:03:55
	 * @param: [licencePlate, licenceColor, startTime, endTime]
	 * @return: java.util.List<com.xh.vdm.bd.entity.BdmSecurity>
	 **/
	List<BdmSecurity> getAlarmInfoPage(@Param("request") AlarmRequest request);

	/**
	 * 查询报警信息，不包含0级报警
	 * @param request
	 * @return
	 */
	List<BdmSecurity> getAlarmInfoPageNoZeroAlarmLevel(@Param("request") AlarmRequest request);

	/**
	 * @description: 查询报警数量
	 * @author: zhouxw
	 * @date: 2023-07-192 21:40:41
	 * @param: [request]
	 * @return: long
	 **/
	long getAlarmInfoCount(@Param("request") AlarmRequest request);

	/**
	 * 查询报警数量，不包含0级报警
	 * @param request
	 * @return
	 */
	long getAlarmInfoCountNoZeroAlarmLevel(@Param("request") AlarmRequest request);

}
