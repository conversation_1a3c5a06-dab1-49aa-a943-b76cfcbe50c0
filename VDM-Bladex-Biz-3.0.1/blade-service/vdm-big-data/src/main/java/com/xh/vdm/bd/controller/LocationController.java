package com.xh.vdm.bd.controller;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.bd.dto.BaseLocDTO;
import com.xh.vdm.bd.entity.*;
import com.xh.vdm.bd.service.IBdmAbstractDeviceService;
import com.xh.vdm.bd.service.IDeviceOnlineService;
import com.xh.vdm.bd.service.ILocationKuduService;
import com.xh.vdm.bd.utils.DistanceUtils;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.StringUtils;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.entity.Location;
import org.springblade.geo.GeoUtils;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.vo.DictTreeNodeVO;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description: 大数据接口
 * @Author: zhouxw
 * @Date: 2023/6/12 9:43
 */
@RestController
@RequestMapping("/bd")
@Slf4j
@RefreshScope
public class LocationController {

	/**
	 * @description: 默认在线保持时长300秒
	 * @author: zhouxw
	 * @date: 2025-06-174 15:45:17
	 * @param: * @param null
	 * @return:
	 * @return: null
	 **/
	@Value("${exception-location.default.keep-online-time:300}")
	private Integer defaultKeepOnlineTime;

	/**
	 * @description:默认离线异常位移距离50米
	 * @author: zhouxw
	 * @date: 2025-06-174 15:45:50
	 * @param: * @param null
	 * @return:
	 * @return: null
	 **/
	@Value("${exception-location.default.exception-distance:50}")
	private Integer defaultExceptionDistance;

	@Resource
	private ILocationKuduService kuduService;

	@Resource
	private IDeviceOnlineService deviceOnlineService;

	@Resource
	private RedisTemplate redisTemplate;

	@Resource
	private IDictBizClient dictBizClient;

	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;

	/**
	 * @description: 轨迹查询接口
	 * @author: zhouxw
	 * @date: 2023-06-163 09:47:55
	 * @param: [target_id, target_type, device_id, device_type, startTime, endTime]
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.LocationKudu>>
	 **/
	@GetMapping("/location")
	public R<List<LocationKudu>> location (
		@RequestParam(value = "target_id", required = false) Long targetId,
		@RequestParam(value = "target_type", required = false) Integer targetType,
		@RequestParam(value = "device_id", required = false) Long deviceId,
		@RequestParam(value = "device_type", required = false) Integer deviceType,
		@RequestParam(value = "valid", required = false) Byte valid,
		@RequestParam(value = "batch", required = false) Byte batch,
		@RequestParam(value = "start_time", required = true) Long startTime,
		@RequestParam(value = "end_time", required = true) Long endTime,
		@RequestParam(value="pos_system",required = false ) Byte posSystem
	) {
		if ((null == targetId && null != targetType) || (null != targetId && null == targetType)) {
			return R.fail("请同时指定目标id和目标类别");
		}

		if ((null == deviceId && null != deviceType) || (null != deviceId && null == deviceType)) {
			return R.fail("请同时指定设备id和设备类别");
		}

		// 优先按终端查（当目标和终端参数都有时）
		if (null != deviceId) {
			targetId = null;
			targetType = null;
		}

		return R.data(this.kuduService.findLocation(targetId, targetType, deviceId, deviceType, valid, batch, startTime, endTime, posSystem));
	}


	/**
	 * @description: 轨迹查询接口（分页，指定区间，含头不含尾）
	 * 用于支持前端分批加载
	 * @author: zhouxw
	 * @date: 2023-06-163 09:47:55
	 * @param: [target_id, target_type, device_id, device_type, startTime, endTime]
	 * @param startIndex: 分页开始下标，从1开始
	 * @param endIndex: 分页结束下标，不包含
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.LocationKudu>>
	 **/
	@GetMapping("/locationByPage")
	public R<LocationKuduPage> location (
		@RequestParam(value = "target_id", required = false) Long targetId,
		@RequestParam(value = "target_type", required = false) Integer targetType,
		@RequestParam(value = "device_id", required = false) Long deviceId,
		@RequestParam(value = "device_type", required = false) Integer deviceType,
		@RequestParam(value = "valid", required = false) Byte valid,
		@RequestParam(value = "batch", required = false) Byte batch,
		@RequestParam(value = "start_time", required = true) Long startTime,
		@RequestParam(value = "end_time", required = true) Long endTime,
		@RequestParam(value = "pos_system",required = false ) Byte posSystem,
		@RequestParam(value = "start_index", required = true) Long startIndex,
		@RequestParam(value = "end_index", required = true) Long endIndex
	) {

		if(startIndex == null || startIndex < 0){
			startIndex = 0L;
		}
		if(endIndex == null || endIndex < 0){
			startIndex = 1000L;
		}

		if ((null == targetId && null != targetType) || (null != targetId && null == targetType)) {
			return R.fail("请同时指定目标id和目标类别");
		}

		if ((null == deviceId && null != deviceType) || (null != deviceId && null == deviceType)) {
			return R.fail("请同时指定设备id和设备类别");
		}

		// 优先按终端查（当目标和终端参数都有时）
		if (null != deviceId) {
			targetId = null;
			targetType = null;
		}
		return R.data(this.kuduService.findLocationPage(startIndex, endIndex, targetId, targetType, deviceId, deviceType, valid, batch, startTime, endTime, posSystem));
	}

//	/**
//	 * 获取指定终端或目标在指定时段内的轨迹分段。
//	 * @param targetId 目标id
//	 * @param deviceId 设备id
//	 * @param startTime 开始时刻，单位s
//	 * @param endTime 结束时刻，单位s
//	 * @param byInterval 是否使用时间间隔进行分段
//	 * @return 分段后的轨迹分段列表结果
//	 */
//	@GetMapping("/trackSegments")
//	public R<List<TrackSegment>> getTrackSegments(@RequestParam(value = "target_id", required = false) Long targetId,
//											   @RequestParam(value = "device_id", required = false) Long deviceId,
//											   @RequestParam(value = "start_time", required = true) Long startTime,
//											   @RequestParam(value = "end_time", required = true) Long endTime,
//											   @RequestParam(value = "by_interval", required = false) Boolean byInterval) {
//		// 1.若需要按时间间隔分隔轨迹时
//		List<TrackSegment> segments;
//		if (byInterval) { // 直接按时间阈值分隔轨迹时
//			segments = kuduService.findTrackSegmentPage(targetId, deviceId, startTime, endTime, byInterval);
//			return R.data(segments);
//		}
//
//		// 2.查询指定时段内的终端上下线记录
//		List<DeviceOnline> onlines = deviceOnlineService.getDeviceOnlines(targetId, deviceId, startTime, endTime);
//		// 3.没有上下线记录
//		if (onlines.isEmpty()) {
//			segments = kuduService.findTrackSegmentPage(targetId, deviceId, startTime, endTime, false);
//			return R.data(segments);
//		}
//
//		// 4.第一条记录的开始时间采用查询的开始时间，结束时间采用查询的结束时间
//		if (onlines.get(0).getStartTime().getTime() < startTime*1000)
//			onlines.get(0).setStartTime(new Date(startTime*1000));
//		if (null == onlines.get(onlines.size() - 1).getEndTime() ||
//			onlines.get(onlines.size()-1).getEndTime().getTime() > endTime*1000)
//			onlines.get(onlines.size()-1).setEndTime(new Date(endTime*1000));
//
//		// 5.遍历查询上下线记录中的轨迹分段
//		segments = new ArrayList<>();
//		DeviceOnline prevOnline = null;
//		for (DeviceOnline online : onlines) {
//			if (null == prevOnline) {
//				prevOnline = online;
//				continue;
//			}
//
//			// 如果下线再上线的时间间隔太小，就将两段合并
//			if (null == prevOnline.getEndTime() ||
//				online.getStartTime().getTime() - prevOnline.getEndTime().getTime() <= 300000) {
//				prevOnline.setEndTime(online.getEndTime());
//				continue;
//			}
//
//			// 时间间隔太小的丢弃（后面考虑是合并还是丢弃）
//			if (prevOnline.getEndTime().getTime() - prevOnline.getStartTime().getTime() <= 300000) {
//				prevOnline = online;
//				continue;
//			}
//
//			List<TrackSegment> subSegments =
//				kuduService.findTrackSegmentPage(targetId, deviceId, prevOnline.getStartTime().getTime()/1000,
//					prevOnline.getEndTime().getTime()/1000, false);
//			segments.addAll(subSegments);
//
//			// 将当前时段置为前一时段
//			prevOnline = online;
//		}
//
//		// 将最后的时段进行轨迹分段，时间间隔太小的丢弃
//		if (prevOnline.getEndTime().getTime() - prevOnline.getStartTime().getTime() > 300000) {
//			List<TrackSegment> subSegments =
//				kuduService.findTrackSegmentPage(targetId, deviceId, prevOnline.getStartTime().getTime()/1000,
//					prevOnline.getEndTime().getTime()/1000, false);
//			segments.addAll(subSegments);
//		}
//
//		return R.data(segments);
//	}

	/**
	 * 获取指定终端或目标在指定时段内的轨迹分段。
	 * @param targetId 目标id
	 * @param deviceId 设备id
	 * @param startTime 开始时刻，单位s
	 * @param endTime 结束时刻，单位s
	 * @param byInterval 是否使用时间间隔进行分段
	 * @return 分段后的轨迹分段列表结果
	 */
	@GetMapping("/trackSegments")
	public R<List<TrackSegment>> getTrackSegments(@RequestParam(value = "target_id", required = false) Long targetId,
												  @RequestParam(value = "device_id", required = false) Long deviceId,
												  @RequestParam(value = "start_time", required = true) Long startTime,
												  @RequestParam(value = "end_time", required = true) Long endTime,
												  @RequestParam(value = "invalid", required = false) Byte invalid,
												  @RequestParam(value = "batch", required = false) Byte batch,
												  @RequestParam(value = "has_lbs", required = false) Byte hasLBS,
												  @RequestParam(value = "by_interval", required = false) Boolean byInterval,
												  @RequestParam(value = "interval", required = false) Long interval) {
		// 1.1.参数检查
		if ((null == targetId || 0 == targetId) && (null == deviceId || 0 == deviceId))
			return R.fail(ResultCode.PARAM_MISS, "请指定设备或监管目标！");

		// 1.2.参数检查
		if (null == startTime || null == endTime || endTime < startTime+300)
			return R.fail(ResultCode.PARAM_VALID_ERROR, "请设置有效时段，且不能小于5分钟");

		// 1.3.参数检查
		if (null == byInterval) byInterval = false;

		// 1.4.参数检查
		Byte valid = null;
		if (null == invalid || 0 == invalid) valid = 1;

		// 1.5.参数检查
		if (null != batch && 1 == batch) {
			batch = null;
		} else {
			batch = 0;
		}

		// 1.6.参数检查
		Byte posSystem = null;
		if (null == hasLBS || 0 == hasLBS) posSystem = 0;

		// 2.查询该时段内的轨迹点基础信息
		List<BaseLocDTO> baseLocs = kuduService.findBaseLoc(targetId, deviceId, valid, batch, posSystem, startTime, endTime);

		// 3.若需要按时间间隔分隔轨迹时
		if (byInterval) {
			// 3.1.若未设置时间间隔，则时间间隔默认为5min
			if (null == interval) interval = 300L;
			// 3.2.按时间间隔分隔轨迹
			List<TrackSegment> segments = findTrackSegments(startTime, endTime, false, byInterval, interval, baseLocs);
			return R.data(segments);
		}

		// 4.查询指定时段内的终端上下线记录
		List<DeviceOnline> onlines = deviceOnlineService.getDeviceOnlines(targetId, deviceId, startTime, endTime);
		// 4.1.没有上下线记录
		if (onlines.isEmpty()) {
			List<TrackSegment> segments = findTrackSegments(startTime, endTime, false, false, 0L, baseLocs);
			return R.data(segments);
		}

		// 5.根据上下线记录生成轨迹分段
		List<TrackSegment> segments = findTrackSegmentsWithOnlines(startTime, endTime, onlines, baseLocs);

		return R.data(segments);
	}

	/**
	 * 根据指定时段内的上下线记录查找轨迹分段。
	 * @param startTime 开始时刻，单位s
	 * @param endTime 结束时刻，单位s
	 * @param onlines 开始-结束时段内的上下线记录
	 * @param baseLocs 轨迹点序
	 * @return 轨迹分段列表
	 */
	private List<TrackSegment> findTrackSegmentsWithOnlines(Long startTime, Long endTime,
															List<DeviceOnline> onlines, List<BaseLocDTO> baseLocs) {
		// 1.第一条记录的开始时间采用查询的开始时间，结束时间采用查询的结束时间
		if (onlines.get(0).getStartTime().getTime() < startTime*1000)
			onlines.get(0).setStartTime(new Date(startTime*1000));
		if (null == onlines.get(onlines.size() - 1).getEndTime() ||
			onlines.get(onlines.size()-1).getEndTime().getTime() > endTime*1000)
			onlines.get(onlines.size()-1).setEndTime(new Date(endTime*1000));

		// 2.遍历查询上下线记录中的轨迹分段
		DeviceOnline prevOnline = null;
		List<TrackSegment> segments = new ArrayList<>();
		for (DeviceOnline online : onlines) {
			if (null == prevOnline) {
				prevOnline = online;
				continue;
			}

			// 如果下线再上线的时间间隔太小，就将两段合并
			if (null == prevOnline.getEndTime() ||
				online.getStartTime().getTime() - prevOnline.getEndTime().getTime() <= 300000) {
				prevOnline.setEndTime(online.getEndTime());
				continue;
			}

			// 时间间隔太小的丢弃（后面考虑是合并还是丢弃）
			if (prevOnline.getEndTime().getTime() - prevOnline.getStartTime().getTime() <= 300000) {
				prevOnline = online;
				continue;
			}

			// 生成该上下线时段的轨迹分段
			List<TrackSegment> subSegments = findTrackSegmentsWithOnline(prevOnline, baseLocs);
			segments.addAll(subSegments);

			// 将当前时段置为前一时段
			prevOnline = online;
		}

		// 3.将最后的时段进行轨迹分段，时间间隔太小的丢弃
		if (prevOnline.getEndTime().getTime() - prevOnline.getStartTime().getTime() > 300000) {
			// 生成该上下线时段的轨迹分段
			List<TrackSegment> subSegments = findTrackSegmentsWithOnline(prevOnline, baseLocs);
			segments.addAll(subSegments);
		}

		return segments;
	}

	/**
	 * 根据上下线记录查询查询轨迹分段。
	 * @param online 上下线记录
	 * @param baseLocs 记录时段内的轨迹点序
	 * @return 轨迹分段
	 */
	private List<TrackSegment> findTrackSegmentsWithOnline(DeviceOnline online, List<BaseLocDTO> baseLocs) {
		// 二分法查找开始时刻在列表中的位置
		int beginIdx = Collections.binarySearch(baseLocs,
			new BaseLocDTO(0,0, online.getStartTime().getTime()/1000));
		if (beginIdx < 0) beginIdx = -beginIdx;

		// 二分法查找结束时刻在列表中的位置
		int endIdx = Collections.binarySearch(baseLocs,
			new BaseLocDTO(0,0, online.getEndTime().getTime()/1000));
		if (endIdx < 0) endIdx = -endIdx - 1;

		return findTrackSegments(online.getStartTime().getTime()/1000, online.getEndTime().getTime()/1000,
			false, false, 0L, baseLocs.subList(beginIdx, endIdx));
	}


	/**
	 * 按时段查找轨迹分段。
	 * @param startTime 开始时刻，单位s
	 * @param endTime 结束时刻，单位s
	 * @param bySpeed 是否按速度分段（漂移点）
	 * @param byInterval 是否按时间间隔分段
	 * @param baseLocs 指定时段内的轨迹点序
	 * @return 轨迹分段列表
	 */
	private List<TrackSegment> findTrackSegments(Long startTime, Long endTime, Boolean bySpeed,
												 Boolean byInterval, Long interval, List<BaseLocDTO> baseLocs) {
		if (CollectionUtils.isEmpty(baseLocs)) return new ArrayList<>();

		List<TrackSegment> segments = new ArrayList<>();

		// 2.计算两点间的平均速度，如果超过160km/h，就进行轨迹分段
		BaseLocDTO lastLoc = null;
		TrackSegment segment = null;
		for (BaseLocDTO baseLoc : baseLocs) {
			if (null == lastLoc) {
				lastLoc = baseLoc;
				segment = new TrackSegment();
				segment.startTm = baseLoc.getTime();
				continue;
			}

			// 点相同，去重
			if (baseLoc.equals(lastLoc))
				continue;

			// 时间相同，去重（理论上不执行）
			long duration = baseLoc.getTime() - lastLoc.getTime();
			if (0 == duration)
				continue;

			// 时间间隔超过设定的时长，分段
			if (byInterval && duration >= interval) {
				if (0 != segment.endTm)
					segments.add(segment);

				lastLoc = baseLoc;
				segment = new TrackSegment();
				segment.startTm = baseLoc.getTime();
				continue;
			}

			// 根据速度分段
			if (bySpeed) {
				// 计算距离
				double dist = GeoUtils.haversineDistance(lastLoc.getLat(), lastLoc.getLng(), baseLoc.getLat(), baseLoc.getLng());
				if (dist/duration >= 45) { // 车速≥45m/s
					if (0 != segment.endTm)
						segments.add(segment);

					lastLoc = baseLoc;
					segment = new TrackSegment();
					segment.startTm = baseLoc.getTime();
					continue;
				}
			}

			lastLoc = baseLoc;
			segment.endTm = baseLoc.getTime();
			++segment.posNum;
		}

		// 添加最后一个分段
		if (0 != segment.endTm)
			segments.add(segment);

		// 避免返回为空
		if (segments.isEmpty())
			segments.add(new TrackSegment(startTime, endTime, baseLocs.size()));

		return segments;
	}


	/**
	 * @description: 返回指定时间范围内异常上下线的数据
	 * @author: zhouxw
	 * @date: 2025-06-174 11:28:28
	 * @param: [targetId, targetType, deviceId, deviceType, valid, batch, startTime, endTime, posSystem, startIndex, endIndex]
	 * @return: org.springblade.core.tool.api.R<java.util.List<java.util.List<com.xh.vdm.bd.entity.LocationBaseInfo>>>
	 **/
	@GetMapping("/exceptionLocationList")
	public R<List<LinkedHashSet<LocationKudu>>> exceptionLocationList (
		@RequestParam(value = "target_id", required = false) Long targetId,
		@RequestParam(value = "target_type", required = false) Integer targetType,
		@RequestParam(value = "device_id", required = false) Long deviceId,
		@RequestParam(value = "device_type", required = false) Integer deviceType,
		@RequestParam(value = "valid", required = false) Byte valid,
		@RequestParam(value = "batch", required = false) Byte batch,
		@RequestParam(value = "start_time", required = true) Long startTime,
		@RequestParam(value = "end_time", required = true) Long endTime,
		@RequestParam(value = "pos_system",required = false ) Byte posSystem
	) {

		if ((null == targetId && null != targetType) || (null != targetId && null == targetType)) {
			return R.fail("请同时指定目标id和目标类别");
		}

		if ((null == deviceId && null != deviceType) || (null != deviceId && null == deviceType)) {
			return R.fail("请同时指定设备id和设备类别");
		}

		// 优先按终端查（当目标和终端参数都有时）
		if (null != deviceId) {
			targetId = null;
			targetType = null;
		}
		//1.查询给定条件的全部轨迹点
		List<LocationKudu> list = this.kuduService.findLocation( targetId, targetType, deviceId, deviceType, valid, batch, startTime, endTime, posSystem);
		if(list == null || list.size() < 1){
			return R.data(null);
		}

		//2.查询在线保持时间
		Map<String,String> onlineMap = new HashMap<>();
		Object obj = redisTemplate.opsForHash().get(CommonConstant.CACHE_HASH_DICT_TREE, CommonConstant.CACHE_FIELD_KEEP_ONLINE_TIME);
		if(obj == null){
			//如果未在缓存中读取到在线保持时间，则查询数据库
			R<List<DictBiz>> res = dictBizClient.getList(CommonConstant.CACHE_FIELD_KEEP_ONLINE_TIME);
			if(res.isSuccess() && res.getData() != null && res.getData().size() > 0){
				List<DictBiz> dictList = res.getData();
				for(DictBiz d : dictList){
					String key = d.getDictKey();
					String value = d.getDictValue();
					onlineMap.put(key, value);
				}
			}
		}else{
			List<DictTreeNodeVO> dictList = JSON.parseArray(JSON.toJSONString(obj), DictTreeNodeVO.class);
			for(DictTreeNodeVO d : dictList){
				String key = d.getDictKey();
				String value = d.getDictValue();
				onlineMap.put(key, value);
			}
		}

		//2.如果两个点之间的定位时间大于在线保持时间，并且两个点之间的距离大于50米，则认为是异常点。记录连续的异常点。
		//2.1 查询category
		Long dId = list.get(0).getDeviceId();
		BdmAbstractDevice device = abstractDeviceService.getById(dId);
		if(device == null){
			//如果未查询到终端信息
			return R.fail("终端信息不存在");
		}
		Integer category = device.getCategory();
		//2.2 查询在线保持时间
		String onlineTimeStr = onlineMap.get(category+"");
		int onlineTime = defaultKeepOnlineTime;
		if(StringUtils.isEmpty(onlineTimeStr)){
			log.info("根据[category={}]未查询到在线保持时间，将使用默认在线保持时间", category);
		}else{
			try {
				onlineTime = Integer.parseInt(onlineTimeStr);
			}catch (Exception e){
				log.error("在线保持时间【{}】格式错误，将使用默认在线保持时间", onlineTimeStr, e);
			}
		}
		LinkedHashSet<LocationKudu> eList = new LinkedHashSet<>();
		List<LinkedHashSet<LocationKudu>> eAllList = new ArrayList<>();
		//2.3 遍历定位点，查找异常点
		for(int i = 0 ; i < list.size() - 1; i ++){
			LocationKudu current = list.get(i);
			LocationKudu next = list.get(i + 1);
			//todo 默认在线保持时间、默认离线位移需要改成配置化，配置refreshScope
			if(next.getTime() - current.getTime() > onlineTime * 1000L && DistanceUtils.wgs84Distance(current.getLongitude(), current.getLatitude(), next.getLongitude(), next.getLatitude()) > defaultExceptionDistance){
				log.info("【异常位移检测】>> 两点距离为：{}", DistanceUtils.wgs84Distance(current.getLongitude(), current.getLatitude(), next.getLongitude(), next.getLatitude()));
				log.info("【异常位移检测】>> 两点时间差为：{}", next.getTime() - current.getTime());
				//异常点
				eList.add(current);
				eList.add(next);
			}else{
				//连续的异常点结束，则重置状态
				if(!eList.isEmpty()){
					eAllList.add(eList);
					eList = new LinkedHashSet<>();
				}
			}
		}

		return R.data(eAllList);
	}



}
