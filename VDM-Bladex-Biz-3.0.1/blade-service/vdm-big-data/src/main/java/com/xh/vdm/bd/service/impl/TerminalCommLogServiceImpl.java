package com.xh.vdm.bd.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.mapper.CommLogMapper;
import com.xh.vdm.bd.service.ITerminalCommLogService;
import com.xh.vdm.bd.vo.response.CommLogResponse;
import org.springblade.core.mp.support.Query;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 终端协议通信日志服务实例。
 */
@Service
@DS("impala")
public class TerminalCommLogServiceImpl extends ServiceImpl<CommLogMapper, CommLogResponse> implements ITerminalCommLogService {

	@Override
	public IPage<CommLogResponse> queryByPage(Query query, Integer deviceType, String deviceNum,
											  String uniqueId, Byte event, Byte errCode, Long startTime, Long endTime) {
		IPage<CommLogResponse> page = new Page<>(query.getCurrent(), query.getSize());
		return baseMapper.queryAll(page, deviceType, deviceNum, uniqueId, event, errCode, startTime, endTime);
	}
}
