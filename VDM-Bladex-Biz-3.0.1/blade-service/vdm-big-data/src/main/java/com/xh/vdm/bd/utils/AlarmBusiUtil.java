package com.xh.vdm.bd.utils;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class AlarmBusiUtil {

	@Resource
	private IUserClient userClient;

	/**
	 * 根据userId查询username
	 * @param userId
	 * @return
	 */
	public String getUsernameByUserId(Long userId){
		try{
			R<String> res = userClient.findUsernameByUserIdCache(userId);
			if(res != null && res.isSuccess()){
				return res.getData();
			}
			return "";
		}catch (Exception e){
			log.error("查询 user feign 失败",e);
			return "";
		}
	}
}
