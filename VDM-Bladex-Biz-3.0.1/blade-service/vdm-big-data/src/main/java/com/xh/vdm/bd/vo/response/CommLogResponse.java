package com.xh.vdm.bd.vo.response;

import lombok.Data;

import java.util.Date;

/**
 * 终端协议通信应答。
 */
@Data
public class CommLogResponse {
	/** 终端类别 */
	private byte deviceType;
	/** 终端号 */
	private String uniqueId;
	/** 终端赋码 */
	private String deviceNum;
	/** 事件类型枚举 */
	private byte event;
	/** 事件发生时刻 */
	private long eventTime;
	/** 通信内容 */
	private String content;
	/** 物联网协议，1-JT/T808，2-MQTT，3-SNMP，... */
	private byte iotProtocol;
}
