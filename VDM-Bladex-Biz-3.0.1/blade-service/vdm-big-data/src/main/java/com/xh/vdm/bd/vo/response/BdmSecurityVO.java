package com.xh.vdm.bd.vo.response;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class BdmSecurityVO  {

	private Long id;

	/**
	 * 车牌号
	 */
	private String licencePlate;

	/**
	 * 车牌颜色
	 */
	private String licenceColor;

	/**
	 * 报警手机号
	 */
	private String phone;

	/**
	 * 车组id
	 */
	private Long deptId;

	/**
	 * 所属企业
	 */
	private String deptFullName;



	/**
	 * 报警类型
	 */
	private String alarmType;

	/**
	 * 报警等级
	 */
	private String alarmLevel;

	/**
	 * 驾驶员身份证
	 */
	private String idCard;

	/**
	 * 驾驶员姓名
	 */
	private String driverName;

	/**
	 * 车辆仪表速度
	 */
	private String vehicleSpeed;

	/**
	 * 行业类型
	 */
	private String vehicleUseType;

	/**
	 * 最大速度（针对终端超速和分段限速）
	 */
	private String maxSpeed;

	/**
	 * 速度
	 */
	private String speed;

	/**
	 * 限速
	 */
	private String limitSpeed;



	/**
	 * 道路名称
	 */
	private String roadName;

	private String roadType;

	/**
	 * 定位纬度
	 */
	private BigDecimal latitude;

	/**
	 * 定位经度
	 */
	private BigDecimal longitude;

	/**
	 * 方向
	 */
	private Integer direction;

	/**
	 * 高程
	 */
	private Integer altitude;

	/**
	 * 里程
	 */
	private String mileage;

	/**
	 * 报警时间
	 */
	private Long alarmTime;

	/**
	 * 报警位置
	 */
	private String alarmAddress;

	/**
	 * 报警来源
	 */
	private String alarmOrigin;

	/**
	 * sim卡号
	 */
	private String simId;

	/**
	 * 持续状态是否结束（0：未结束，1：已结束）
	 */
	private String alarmComplete;

	/**
	 * 服务商是否自动处理（0：否，1：是）
	 */
	private String serverAutoDeal;

	/**
	 * 第三方是否自动处理（0：否，1：是）
	 */
	private String thirdAutoDeal;

	/**
	 * 报警处理状态，0-待处理；1：处理中；2；处理完毕
	 */
	private String alarmDealState;

	/**
	 * 解除报警状态， -1-未解除  0-报警处理  1-无风险解除  2-标记误报解除
	 */
	private String removeAlarm;

	/**
	 * 处理描述
	 */
	private String dealDescribe;

	/**
	 * 处理人
	 */
	private String dealMan;

	/**
	 * 平台规则id
	 */
	private Integer ruleId;

	/**
	 * 规则类型id
	 */
	private Integer ruleTypeId;

	/**
	 * 平台规则名称
	 */
	private String ruleName;

	/**
	 * 车辆状态
	 */
	private String vehicleStates;

	/**
	 * 报警结束时间
	 */
	private Long alarmEndTime;

	/**
	 * 报警结束位置
	 */
	private String alarmEndAddress;

	/**
	 * 报警结束纬度
	 */
	private Double latitudeEnd;

	/**
	 * 报警结束经度
	 */
	private Double longitudeEnd;

	/**
	 * 终端状态描述
	 */
	private String terminalStateString;

	/**
	 * 车辆id
	 */
	private Integer vehicleId;

	/**
	 * 告警附件关联
	 */
	private String uniqueId;

	/**
	 * 服务状态（状态值与名称的映射，详见blade_dict_biz表code=enjoy_service的记录）
	 */
	private String serviceState;

	/**
	 * 是否误报 （1 误报  0 非误报）
	 */
	private String isWrong;

	/**
	 * 上报状态
	 */
	private String reportingState;

	/**
	 * 809第三方转发平台名称
	 */
	private String thirdPlatform;

	/**
	 * 服务商处理状态（0：未处理，1：已处理，2：误报）
	 */
	private String serverState;

	/**
	 * 服务商处理结果，0：误报；1：确认报警
	 */
	private String serverResult;

	/**
	 * 服务商处理措施（英文逗号分隔的多个措施值，措施值与名称的映射，详见blade_dict_biz表code=server_measures的记录）
	 */
	private String serverMeasures;

	/**
	 * 服务商处理内容
	 */
	private String serverContent;

	/**
	 * 服务商处理人员
	 */
	private String serverUser;

	/**
	 * 服务商处理时间
	 */
	private Long serverTime;

	/**
	 * 第三方处理状态（0：未处理，1：已处理，2：误报）
	 */
	private String thirdState;

	/**
	 * 第三方处理结果，0：误报；1：确认报警
	 */
	private String thirdResult;

	/**
	 * 第三方处理措施（英文逗号分隔的多个措施值，措施值与名称的映射，详见blade_dict_biz表code=third_measures的记录）
	 */
	private String thirdMeasures;

	/**
	 * 第三方处理内容
	 */
	private String thirdContent;

	/**
	 * 第三方处理人员
	 */
	private String thirdUser;

	/**
	 * 第三方处理时间
	 */
	private Long thirdTime;

	/**
	 * 企业处理状态（0：未处理，1：已处理）
	 */
	private String companyState;

	/**
	 * 企业处理措施（英文逗号分隔的多个措施值，措施值与名称的映射，详见blade_dict_biz表code=company_measures的记录）
	 */
	private String companyMeasures;

	/**
	 * 企业处理内容
	 */
	private String companyContent;

	/**
	 * 企业处理附件URL
	 */
	private String companyAttach;

	/**
	 * 企业处理人员
	 */
	private String companyUser;

	/**
	 * 企业处理时间
	 */
	private Long companyTime;

	/**
	 * 企业申诉状态（0：未申诉，1：已申诉，2：申诉通过，3：申诉驳回）
	 */
	private String appealState;

	/**
	 * 企业申诉理由
	 */
	private String appealReason;

	/**
	 * 企业申诉附件URL
	 */
	private String appealAttach;

	/**
	 * 企业申诉人员
	 */
	private String appealUser;

	/**
	 * 企业申诉时间
	 */
	private Long appealTime;

	/**
	 * 处理结果
	 */
	private String appealResult;

	/**
	 * 申诉审核状态，0：待审核；1：已审核
	 */
	private String auditState;

	/**
	 * 审核结果，0：误报；1：确认报警
	 */
	private String auditResult;

	/**
	 * 申诉处理内容
	 */
	private String auditContent;

	/**
	 * 申诉审核人员
	 */
	private String auditUser;

	/**
	 * 申诉处理时间
	 */
	private Long auditTime;

	/**
	 * 是否已开罚单（0：未开具，1：已开具）
	 */
	private String punishState;

	/**
	 * 报警附件数量
	 */
	private String alarmDmsAppendix;

	/**
	 * 是否申诉（1：已申诉 0：未申诉）
	 */
	private String isAppeal;

	/**
	 * 协议类型： 1 808      2  809
	 */
	private String protocolType;

	/**
	 * 行驶时间
	 */
	private Long drivingTime;

	/**
	 * 疲劳开始时间
	 */
	private Long fatigueStartTime;

	/**
	 * 疲劳结束时间
	 */
	private Long fatigueEndTime;

	/**
	 * 车辆归属区域ID
	 */
	private Integer regionId;

	private String dealOperateState;

	/**
	 * 创建时间
	 */
	private Long createTime;

	/**
	 * 更新时间
	 */
	private Long updateTime;

	private String startTime;
	private String endTime;
	private String serverTimeStr;
	private String thirdTimeStr;
	private String companyTimeStr;
	private String appealTimeStr;
	private String auditTimeStr;
	private String drivingTimeStr;
	private String fatigueStartTimeStr;
	private String fatigueEndTimeStr;
	private String createTimeStr;
	private String updateTimeStr;
	private String duration;
}
