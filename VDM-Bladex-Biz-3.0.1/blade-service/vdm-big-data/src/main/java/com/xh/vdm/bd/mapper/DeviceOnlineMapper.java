package com.xh.vdm.bd.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bd.dto.DevOnlineDTO;
import com.xh.vdm.bd.entity.DeviceOnline;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 终端上下线记录。
 */
public interface DeviceOnlineMapper extends BaseMapper<DeviceOnline> {
	/**
	 * 获取目标或终端的上下线记录基础信息。
	 * @param targetId 目标id
	 * @param deviceId 设备id
	 * @param startTime 上线时刻
	 * @param endTime 下线时刻
	 * @return 终端上下线记录列表
	 */
	List<DevOnlineDTO> getDevOnlineDTOS(@Param("targetId") Long targetId, @Param("deviceId") Long deviceId,
										@Param("startTime") Long startTime, @Param("endTime") Long endTime);
}
