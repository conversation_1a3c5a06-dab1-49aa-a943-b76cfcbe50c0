package com.xh.vdm.bd.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.dto.DevOnlineDTO;
import com.xh.vdm.bd.entity.DeviceOnline;
import com.xh.vdm.bd.mapper.DeviceOnlineMapper;
import com.xh.vdm.bd.service.IDeviceOnlineService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

/**
 * 设备上下线记录服务。
 */
@Service
public class DeviceOnlineServiceImpl extends ServiceImpl<DeviceOnlineMapper, DeviceOnline> implements IDeviceOnlineService {
	@Override
	public List<DeviceOnline> getDeviceOnlines(Long targetId, Long deviceId, Long startTime, Long endTime) {
		QueryWrapper<DeviceOnline> wrapper = new QueryWrapper<>();
		if (null != targetId)
			wrapper.eq("target_id", targetId);
		if (null != deviceId)
			wrapper.eq("device_id", deviceId);
		if (null != startTime && null != endTime) {
			wrapper.lt("start_time", new Date(endTime*1000)).and(queryWrapper -> queryWrapper
                .gt("end_time", new Date(startTime*1000)).or().isNull("end_time"));
		}
		wrapper.orderByAsc("start_time");

		return baseMapper.selectList(wrapper);
	}

	@Override
	public List<DevOnlineDTO> getDevOnlineDTOS(Long targetId, Long deviceId, Long startTime, Long endTime) {
		return baseMapper.getDevOnlineDTOS(targetId, deviceId, startTime, endTime);
	}
}
