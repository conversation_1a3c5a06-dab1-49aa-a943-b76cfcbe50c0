package com.xh.vdm.bd.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bd.entity.BdmSecurity;
import com.xh.vdm.bd.mapper.BdmSecurityMapper;
import com.xh.vdm.bd.service.IBdmSecurityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.vo.request.AlarmRequest;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CommonUtil;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * 报警信息
 * <AUTHOR>
 * @since 2023-07-03
 */
@Service
@Slf4j
public class BdmSecurityServiceImpl extends ServiceImpl<BdmSecurityMapper, BdmSecurity> implements IBdmSecurityService {

	@Override
	public List<BdmSecurity> findAlarmInfo(AlarmRequest request) throws Exception {
		return baseMapper.getAlarmInfoPage(request);
	}

	@Override
	public List<BdmSecurity> findAlarmInfo(AlarmRequest request, boolean isContainZeroAlarmLevel) throws Exception {
		if(isContainZeroAlarmLevel){
			//如果包含0级报警
			return baseMapper.getAlarmInfoPage(request);
		}else{
			//如果不包含0级报警
			return baseMapper.getAlarmInfoPageNoZeroAlarmLevel(request);
		}
	}

	@Override
	public long findAlarmCount(AlarmRequest request) throws Exception {
		return baseMapper.getAlarmInfoCount(request);
	}

	@Override
	public long findAlarmCount(AlarmRequest request, boolean isContainZeroAlarmLevel) throws Exception {
		if(isContainZeroAlarmLevel){
			//如果包含0级报警
			return baseMapper.getAlarmInfoCount(request);
		}else{
			//如果不包含0级报警
			return baseMapper.getAlarmInfoCountNoZeroAlarmLevel(request);
		}
	}


	@Override
	public IPage<BdmSecurity> findAlarmInfoPage(AlarmRequest request) throws Exception {
		//设置分页
		int current = request.getCurrent();
		int size = request.getSize();
		int limit = size;
		int offset = (current-1) * size;
		request.setLimit(limit);
		request.setOffset(offset);

		BladeUser user = AuthUtil.getUser();
		String roleType = user.getRoleName();
		List<String> roleTypeList = Arrays.asList(roleType.split(","));

		long count = 0;
		List<BdmSecurity> list = new ArrayList<>();
		if(roleTypeList.contains(CommonConstant.ROLE_ADMINISTRATOR) || roleTypeList.contains(CommonConstant.ROLE_ADMIN)){
			//如果包含管理员权限，则可以展示0级报警；否则，不展示0级报警
			count = findAlarmCount(request);
			list = findAlarmInfo(request);
		}else{
			//如果不包含管理员权限，则排除0级报警
			count = findAlarmCount(request, false);
			list = findAlarmInfo(request, false);
		}

		//3.设置返回数据
		IPage<BdmSecurity> page = new Page<>();
		page.setCurrent(current);
		page.setSize(size);
		page.setTotal(count);
		page.setRecords(list);
		return page;
	}
}
