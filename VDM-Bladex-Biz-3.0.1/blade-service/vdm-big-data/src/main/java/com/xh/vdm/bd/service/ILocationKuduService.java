package com.xh.vdm.bd.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bd.dto.BaseLocDTO;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.LocationKuduPage;
import com.xh.vdm.bd.entity.TrackSegment;
import com.xh.vdm.bd.entity.VehicleBase;

import java.util.List;

public interface ILocationKuduService extends IService<LocationKudu> {

	/**
	 * @description: 批量保存校验通过的数据
	 * @author: zhouxw
	 * @date: 2023-05-144 14:18:35
	 * @param: []
	 * @return: void
	 **/
	long saveValidBatch(List<LocationKudu> list) throws Exception;

	/**
	 * @description: 查询上传有效定位点的车辆列表
	 * @author: zhouxw
	 * @date: 2023-06-159 13:55:57
	 * @param: [startTime, endTime]
	 * @return: java.util.List<com.xh.vdm.bd.entity.VehicleBase>
	 **/
	List<VehicleBase> findVehicleListForValid(Long startTime, Long endTime);

	/**
	 * @description: 查询所有上传定位点的车辆列表，包含无效定位点
	 * @author: zhouxw
	 * @date: 2023-06-159 13:56:39
	 * @param: [startTime, endTime]
	 * @return: java.util.List<com.xh.vdm.bd.entity.VehicleBase>
	 **/
	List<VehicleBase> findVehicleListForAll(Long startTime, Long endTime);

	/**
	 * 查询轨迹点
	 * @param targetId 目标实体id
	 * @param targetType 目标类别
	 * @param deviceId 设备id
	 * @param deviceType 设备类别
	 * @param valid 定位有效性，0-无效，1-有效
	 * @param batch 补传标志
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 轨迹
	 */
	List<LocationKudu> findLocation (Long targetId, Integer targetType, Long deviceId,
									 Integer deviceType, Byte valid, Byte batch, Long startTime, Long endTime, Byte posSystem);


	/**
	 * 查询轨迹点（分页）
	 * @param targetId 目标实体id
	 * @param targetType 目标类别
	 * @param deviceId 设备id
	 * @param deviceType 设备类别
	 * @param valid 定位有效性，0-无效，1-有效
	 * @param batch 补传标志
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return 轨迹
	 */
	LocationKuduPage findLocationPage (Long startIndex, Long endIndex, Long targetId, Integer targetType, Long deviceId,
											  Integer deviceType, Byte valid, Byte batch, Long startTime, Long endTime, Byte posSystem);

//	/**
//	 * 查询指定时段内的轨迹分段信息。
//	 * @param targetId 目标id
//	 * @param deviceId 设备id
//	 * @param startTime 开始时刻，单位s
//	 * @param endTime 结束时刻，单位s
//	 * @param byInterval 是否通过时间间隔过滤
//	 * @return 轨迹分段列表信息
//	 */
//	List<TrackSegment> findTrackSegmentPage(Long targetId, Long deviceId,
//											Long startTime, Long endTime, boolean byInterval);

	/**
	 * 查询指定设备或目标在指定时段内的轨迹点基础信息。
	 * @param targetId 目标id
	 * @param deviceId 设备id
	 * @param valid 定位有效性，0-无效，1-有效
	 * @param batch 轨迹点补传标志
	 * @param posSystem 坐标系统
	 * @param startTime 开始时刻，单位s
	 * @param endTime 结束时刻，单位s
	 * @return 符合条件的轨迹点基础信息
	 */
	List<BaseLocDTO> findBaseLoc(Long targetId, Long deviceId, Byte valid, Byte batch, Byte posSystem, Long startTime, Long endTime);
}
