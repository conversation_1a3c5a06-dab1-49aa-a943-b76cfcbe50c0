package com.xh.vdm.bd.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bd.entity.LocationHDFS;

import java.util.List;

/**
 * @description: kudu中的定位表 pos_gn.locations
 * @author: zhouxw
 * @date: 2023-05-143 19:10:14
 * @param: * @param null
 * @return:
 * @return: null
 **/
@DS("impala")
public interface LocationHDFSMapper extends BaseMapper<LocationHDFS> {

	/**
	 * @description: 向 HDFS 中写入数据
	 * @author: zhouxw
	 * @date: 2023-05-143 19:47:19
	 * @param: [list]
	 * @return: void
	 **/
	void saveToHDFS(List<LocationHDFS> list);
}
