<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bd.mapper.CommLogMapper">

    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.bd.vo.response.CommLogResponse">
        select
            device_type, unique_id, device_num, event, event_time, content, iot_protocol
        from terminal_comm_log
        <where>
            <if test="deviceType != null">
                and device_type = #{deviceType}
            </if>
            <if test="deviceNum != null and deviceNum != ''">
                and device_num like concat('%' , #{deviceNum} , '%')
            </if>
            <if test="uniqueId != null and uniqueId != ''">
                and unique_id like concat('%' , #{uniqueId} , '%')
            </if>
            <if test="event != null">
                and event = #{event}
            </if>
            <if test="errCode != null">
                and err_code = #{errCode}
            </if>
            <if test="startTime != null and endTime != null">
                and event_time between #{startTime} and #{endTime}
            </if>
        </where>
        order by event_time desc
    </select>
</mapper>
