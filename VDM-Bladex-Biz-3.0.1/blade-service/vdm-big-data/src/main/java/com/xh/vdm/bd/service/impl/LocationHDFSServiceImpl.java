package com.xh.vdm.bd.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.entity.LocationHDFS;
import com.xh.vdm.bd.mapper.LocationHDFSMapper;
import com.xh.vdm.bd.service.ILocationHDFSService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/5/24 13:34
 */
@Service
@DS("impala")
public class LocationHDFSServiceImpl extends ServiceImpl<LocationHDFSMapper, LocationHDFS> implements ILocationHDFSService {

	public long saveValidBatch(List<LocationHDFS> list) throws Exception {
		try{
			List<LocationHDFS> li = new ArrayList<>();
			for(int i = 0 ; i < list.size(); i++){
				LocationHDFS item = list.get(i);
				if(item.getLicencePlate() == null || item.getLicenceColor() == null || item.getLocTime() == null){
					continue;
				}
				li.add(list.get(i));
			}
			if(li.size() < 1){
				return 0;
			}
			baseMapper.saveToHDFS(li);
			return li.size();
		}catch (Exception e){
			log.error("[HDFS] mapper保存数据失败",e);
			return 0;
		}
	}
}
