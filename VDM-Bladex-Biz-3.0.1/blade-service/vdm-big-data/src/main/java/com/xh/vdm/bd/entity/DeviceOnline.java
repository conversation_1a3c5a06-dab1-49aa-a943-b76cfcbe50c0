package com.xh.vdm.bd.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 设备上下线记录。
 */
@Data
@TableName("bdm_device_online")
public class DeviceOnline implements Serializable {
	/** 主键id */
	private Long id;
	/** 设备id */
	private Long deviceId;
	/** 设备类型 */
	private Byte deviceType;
	/** 设备序列号 */
	private String uniqueId;
	/** 设备赋码号 */
	private String deviceNum;
	/** 目标id */
	private Long targetId;
	/** 目标类型 */
	private Byte targetType;
	/** 目标名称 */
	private String targetName;
	/** 所属部门id */
	private Long deptId;
	/** 上线时刻 */
	private Date startTime;
	/** 下线时刻 */
	private Date endTime;
}
