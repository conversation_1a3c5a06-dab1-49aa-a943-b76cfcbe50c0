package com.xh.vdm.bd.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bd.vo.response.CommLogResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;

/**
 * 终端协议通信日志数据访问层接口。
 */
@Mapper
@DS("impala")
public interface CommLogMapper extends BaseMapper<CommLogResponse> {
	/**
	 * 分页查询终端协议通信日志。
	 *
	 * @param page       分页信息
	 * @param deviceType
	 * @param deviceNum
	 * @param uniqueId
	 * @param event
	 * @param errCode
	 * @param startTime
	 * @param endTime
	 * @return 分页查询结果
	 */
	IPage<CommLogResponse> queryAll(IPage<CommLogResponse> page, @Param("deviceType") Integer deviceType,
									@Param("deviceNum") String deviceNum, @Param("uniqueId") String uniqueId,
									@Param("event") Byte event, @Param("errCode") Byte errCode,
									@Param("startTime") Long startTime, @Param("endTime") Long endTime);
}
