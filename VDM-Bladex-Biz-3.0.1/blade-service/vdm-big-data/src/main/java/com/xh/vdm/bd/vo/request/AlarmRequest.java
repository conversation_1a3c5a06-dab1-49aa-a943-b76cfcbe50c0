package com.xh.vdm.bd.vo.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 报警请求参数
 * @Author: zhouxw
 * @Date: 2023/7/11 20:21
 */
@Data
public class AlarmRequest {

	/** 车辆ID列表*/
	@JsonProperty("id_list")
	private List<Long>  idList;

	/** 用户本下级部门ID列表*/
	@JsonProperty("dept_id_list")
	private List<Long> deptIdList;

	/** 行业类型列表 */
	@JsonProperty("vehicle_use_type_list")
	private List<Long> vehicleUseTypeList;

	/** 报警等级 */
	@JsonProperty("alarm_level")
	private List<Integer> alarmLevel;

	/** 报警等级权限列表*/
	private List<Integer> alarmLevelList;

	/** 报警类型列表 */
	@JsonProperty("alarm_type_list")
	private List<Integer> alarmTypeList;

	/** 开始时间，秒时间戳 */
	@JsonProperty("start_time")
	private Long startTime;

	/** 结束时间，秒时间戳 */
	@JsonProperty("end_time")
	private Long endTime;

	/** 服务商处理状态 */
	@JsonProperty("server_state")
	private Integer serverState;

	/** 第三方处理状态爱 */
	@JsonProperty("third_state")
	private Integer thirdState;

	/** 企业处理状态 */
	@JsonProperty("company_state")
	private Integer companyState;

	/** 申诉状态 */
	@JsonProperty("appeal_state")
	private Integer appealState;

	/** 服务类型 */
	@JsonProperty("service_state")
	private Integer serviceState;

	/** 报警持续状态 */
	@JsonProperty("alarm_complete")
	private Integer alarmComplete;

	/** 企业处理措施 */
	@JsonProperty("company_measures")
	private String companyMeasures;

	/** 是否误报 */
	@JsonProperty("is_wrong")
	private Integer isWrong;

	/** 持续时长 ， 分钟数 */
	@JsonProperty("duration")
	private Integer duration;

	/** 不低于报警速度，速度值 */
	@JsonProperty("alarm_speed")
	private Double alarmSpeed;

	/** 报警来源 */
	@JsonProperty("alarm_origin")
	private String alarmOrigin;


	/** 平台规则名称 */
	@JsonProperty("rule_type_name")
	private String ruleTypeName;

	/** 规则名称 */
	@JsonProperty("rule_name")
	private String ruleName;

	/** 报警手机号 */
	@JsonProperty("phone")
	private String phone;

	/** 驾驶员身份证号 */
	@JsonProperty("id_card")
	private String idCard;

	/** 驾驶员姓名 */
	@JsonProperty("driver_name")
	private String driverName;

	/** 其他平台名称 */
	@JsonProperty("third_platform")
	private String thirdPlatform;

	/** 当前页码 */
	private Integer current;

	/** 分页大小 */
	private Integer size;

	/**  */
	@JsonIgnore
	private Integer limit;

	/**  */
	@JsonIgnore
	private Integer offset;


	/**	 导出动态表头中文名*/
	@JsonProperty("head_name_list")
	@ApiModelProperty(name = "head_name_list", value = "动态表头中文名", example = "", required = false)
	private List headNameList;

	/**	 导出动态表头字段名*/
	@JsonProperty("column_name_list")
	@ApiModelProperty(name = "column_name_list", value = "动态表头字段名", example = "", required = false)
	private List columnNameList;
}
