#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:com/xh/**/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.xh.**.entity

#swagger扫描路径配置
swagger:
  base-packages:
    - org.springblade
    - com.example

#blade配置
blade:
  #本地文件上传
  file:
    remote-mode: true
    upload-domain: http://localhost:8999
    remote-path: /usr/share/nginx/html
  jackson:
    null-to-empty: false
