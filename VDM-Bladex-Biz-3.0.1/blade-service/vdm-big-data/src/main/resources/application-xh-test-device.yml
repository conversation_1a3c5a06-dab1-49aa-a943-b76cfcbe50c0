#服务器端口
server:
  port: 8200

#数据源配置
#spring:
#  datasource:
#    url: ${blade.datasource.dev.url}
#    username: ${blade.datasource.dev.username}
#    password: ${blade.datasource.dev.password}

spring:
  datasource:
    dynamic:
      primary: busi
      enabled: true
      strict: false
      #设置默认的数据源或者数据源组,默认值即为master
      datasource:
        busi:
          url: ${blade.datasource.dev.url}
          username: ${blade.datasource.dev.username}
          password: ${blade.datasource.dev.password}
          druid:
            # 初始化
            initial-size: 30
            # 最大
            max-wait: 200
            # 最小
            min-idle: 3
            # 最大连接等待超时时间
            max-active: 60000
            # 周期性剔除长时间呆在池子里未被使用的空闲连接, 1 min 一次,单位毫秒
            time-between-eviction-runs-millis: 60000
            # 配置一个连接在池中最小生存的时间,单位是毫秒
            min-evictable-idle-time-millis: 300000
            # 设置连接在池中最大存活时长，超过上限才会被清理
            max-evictable-idle-time-millis: 600000
            # 验证连接是否可用，使用的SQL语句
            validation-query: SELECT 'x'
            # 连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
            test-while-idle: false
            # 借出连接时不要测试，否则很影响性能
            test-on-borrow: false
            # 指明是否在归还到池中前进行检验
            test-on-return: false
        #slave:
        #  druid:
            #独立校验配置
        #    validation-query: select 1
            #oracle校验
            #validation-query: select 1 from dual
        #  url: ${blade.datasource.demo.slave.url}
        #  username: ${blade.datasource.demo.slave.username}
        #  password: ${blade.datasource.demo.slave.password}
        impala:
          driver-class-name: com.cloudera.impala.jdbc41.Driver
          #url: ***********************************
          url: *************************************
          username:
          password:
          # Druid连接池配置
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            # 初始化
            initial-size: 30
            # 最大
            max-wait: 200
            # 最小
            min-idle: 3
            # 最大连接等待超时时间
            max-active: 60000
            # 周期性剔除长时间呆在池子里未被使用的空闲连接, 1 min 一次,单位毫秒
            time-between-eviction-runs-millis: 60000
            # 配置一个连接在池中最小生存的时间,单位是毫秒
            min-evictable-idle-time-millis: 300000
            # 设置连接在池中最大存活时长，超过上限才会被清理
            max-evictable-idle-time-millis: 600000
            # 验证连接是否可用，使用的SQL语句
            validation-query: SELECT 'x'
            # 连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
            test-while-idle: false
            # 借出连接时不要测试，否则很影响性能
            test-on-borrow: false
            # 指明是否在归还到池中前进行检验
            test-on-return: false

kudu:
  alarm:
    table_name: pos_gn.alarms


alarm:
  duration:
    start_time: 1683443146
    end_time: 1691391946
  #报警数据导出的限制
  export:
    limit: 5000


mybatis-plus:
  #这个配置会将执行的sql打印出来，在开发或测试的时候可以用
  log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
