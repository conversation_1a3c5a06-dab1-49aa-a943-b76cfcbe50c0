#服务器端口
server:
  port: 8108




#数据源配置
spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          #开发环境
          url: ${blade.datasource.dev.url}
          username: ${blade.datasource.dev.username}
          password: ${blade.datasource.dev.password}
          driver-class-name: org.postgresql.Driver
          hikari:
            auto-commit: true
            pool-name: DatebookHikariCP
            minimum-idle: 5
            maximum-pool-size: 80
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 400000
            # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 500000
            # 数据库连接超时时间,默认30秒，即30000。配置3s。
            connection-timeout: 60000
            connection-test-query: SELECT 1
            validation-timeout: 10000
        bdCheck:
          #开发环境
          url: **************************************************************
          username: ${blade.datasource.dev.username}
          password: ${blade.datasource.dev.password}
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            auto-commit: true
            pool-name: DatebookHikariCP
            minimum-idle: 5
            maximum-pool-size: 80
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 400000
            # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 500000
            # 数据库连接超时时间,默认30秒，即30000。配置3s。
            connection-timeout: 60000
            connection-test-query: SELECT 1
            validation-timeout: 10000
        impala:
          driver-class-name: com.cloudera.impala.jdbc41.Driver
          url: ****************************************
          username:
          password:
          # 连接池配置
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            auto-commit: true
            pool-name: DatebookHikariCP
            minimum-idle: 5
            maximum-pool-size: 80
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 400000
            # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 500000
            # 数据库连接超时时间,默认30秒，即30000。配置3s。
            connection-timeout: 60000
            connection-test-query: SELECT 1
            validation-timeout: 10000
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      auto-commit: true
      pool-name: DatebookHikariCP
      minimum-idle: 5
      maximum-pool-size: 80
      # 空闲连接存活最大时间，默认600000（10分钟）
      idle-timeout: 400000
      # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
      max-lifetime: 500000
      # 数据库连接超时时间,默认30秒，即30000。配置3s。
      connection-timeout: 60000
      connection-test-query: SELECT 1
      validation-timeout: 10000



  autoconfigure:
    exclude: org.springblade.core.tool.config.MessageConfiguration

  redis:
    ##redis 单机环境配置
    host: ***********
    port: 20142
    password: xh123456
    database: 0
    ssl:
      enabled: false





  #数据同步-调用外部接口-车辆
  message-send:
    bootstrap-servers: ***********:21002,*************:21002,*************:21022
    # 生产者配置
    producer:
      retries: 1
      acks: 1
      batch-size: 1024
      #properties:
      #linger.ms: 0
      #partitioner.class: com.xh.vdm.CustomPartioner
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
      #value-serializer: org.apache.kafka.common.serialization.StringSerializer
      #添加受信任的bean目录
      properties:
        spring:
          json:
            trusted:
              packages: "com.springblade.vdm.bdCheck"
    #消费者配置
    consumer:
      auto-offset-reset: latest
      key-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      group-id: message_inter_vehicle_group
      properties:
        spring.deserializer.key.delegate.class: org.apache.kafka.common.serialization.StringDeserializer
        spring.deserializer.value.delegate.class: org.apache.kafka.common.serialization.StringDeserializer
        spring.json.value.default.type: java.lang.String
        session:
          timeout:
            ms: 120000
        request:
          timeout:
            ms: 180000
        #添加受信任的bean目录
        spring:
          json:
            trusted:
              packages: "com.springblade.vdm.interManager"

      enable-auto-commit: false
      auto:
        commit:
          interval:
            ms: 1000
      #value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 10000
    listener:
      missing-topics-fatal: false
      type: batch
      ack-mode: manual_immediate




  #kafka配置
  locations:
    bootstrap-servers: ***********:21002,*************:21002,*************:21022
    # 生产者配置
    producer:
      retries: 1
      acks: 1
      batch-size: 1024
      #properties:
      #linger.ms: 0
      #partitioner.class: com.xh.vdm.CustomPartioner
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      #value-serializer: org.apache.kafka.common.serialization.StringSerializer
      #添加受信任的bean目录
      properties:
        spring:
          json:
            trusted:
              packages: "org.springblade.entity"

    #消费者配置
    consumer:
      auto-offset-reset: earliest
      key-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      group-id: message_inter_vehicle_group_zxw
      properties:
        spring.deserializer.key.delegate.class: org.apache.kafka.common.serialization.StringDeserializer
        spring.deserializer.value.delegate.class: org.springframework.kafka.support.serializer.JsonDeserializer
        spring.json.value.default.type: org.springblade.entity.LocationNew
        session:
          timeout:
            ms: 120000
        request:
          timeout:
            ms: 180000
        #添加受信任的bean目录
        spring:
          json:
            trusted:
              packages: "org.springblade.entity"

      enable-auto-commit: false
      auto:
        commit:
          interval:
            ms: 1000
      #value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 10000

    listener:
      missing-topics-fatal: false
      type: batch
      ack-mode: manual_immediate



  main:
    allow-circular-references: true

#接收消息后的操作
message:
  kafka:
    to_location: GuoNeng_position_data_tmp #位置kafka
    to_alarm: GuoNeng_alarm_from_secondary



#ftp配置
ftp:
  host: ftp2.csno-tarc.cn
  port: 21
  username: pub
  password: tarc
  path: /brdc/
  localTmpPath: C:\Users\<USER>\Desktop\tmp


#北斗识别报告
bd-check:
  template-path: E:\tmp\vdm-gn\template\北斗识别报告模板-表达式.docx
  file-path: E:\tmp\vdm-gn\file\
  proxy-path: /report/bd-check/



mqtt:
  hostUrl: tcp://************:1883
  username: emqx
  password: emq@@2024
  cleanSession: true
  reconnect: true
  timeout: 100
  keepAlive: 100
  defaultTopic: $share/c/collect #多个topic前缀之间，使用 , 进行分隔；使用共享消费者组，解决多消费者集群启动时，重复消费的问题
  isOpen: true
  qos: 0

