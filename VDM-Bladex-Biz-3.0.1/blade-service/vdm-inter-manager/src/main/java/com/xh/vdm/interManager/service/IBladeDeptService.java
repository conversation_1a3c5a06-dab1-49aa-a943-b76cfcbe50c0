package com.xh.vdm.interManager.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.interManager.entity.BladeDept;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
public interface IBladeDeptService extends IService<BladeDept> {

	/**
	 * 查找存在的dept信息
	 * @param deptIdStr
	 * @return
	 */
	public List<BladeDept> findExistDept(String deptIdStr);
}
