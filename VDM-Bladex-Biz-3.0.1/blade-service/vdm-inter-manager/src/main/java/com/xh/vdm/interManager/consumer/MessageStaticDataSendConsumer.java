package com.xh.vdm.interManager.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.interManager.core.InterManager;
import com.xh.vdm.interManager.dto.message.*;
import com.xh.vdm.interManager.entity.*;
import com.xh.vdm.interManager.service.*;
import com.xh.vdm.interManager.utils.BeanUtil;
import com.xh.vdm.interManager.utils.MessageUtils;
import com.xh.vdm.interManager.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.StringUtils;
import com.xh.vdm.biapi.entity.BdmWorker;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 向外部平台推送消息
 * 静态数据推送。静态数据推送只支持HTTP形式的推送
 * 接收发送到kafka中的基础信息，然后推送
 * 基础信息在新增、修改、删除后，会向 message_send topic中发送消息，然后在此消费者中消费执行推送动作
 */
@Component
@EnableKafka
@Slf4j
public class MessageStaticDataSendConsumer {

	@Resource
	private IInPosSystemObjectService objectService;

	@Resource
	private ISmInterfaceSystemService systemService;

	@Resource
	private InterManager interManager;

	@Resource
	private MessageUtils messageUtils;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private ISmInterfaceManageService interfaceManageService;

	@Resource
	private IInInterLogService logService;

	/**
	 * @description:
	 * 外部平台通过mqtt协议将数据发到位置平台，位置平台将数据保存到kafka，然后消费kafka执行不同动作
	 * @author: zhouxw
	 * @param: [record]
	 * @return: void
	 **/
	@KafkaListener(containerFactory = "messageSendListener", topics = {CommonConstant.MESSAGE_SEND_TOPIC},  batch = "true")
	public void messageCollect(List<String> consumerRecords, Acknowledgment acknowledgment){
		try{
			for(String recordStr : consumerRecords){
				MessageContainer record = JSON.parseObject(recordStr, MessageContainer.class);
				//1.根据业务编号判断业务类型
				String busiCode = record.getBusiCode();
				Object message = record.getMessage();
				Object busiObject = record.getBusiObject();
				//判断是否是批量数据
				Boolean isBatch = record.getIsBatch();
				switch (busiCode){
					case CommonConstant.BUSI_CODE_ONLINE: //终端上线
						break;
					case CommonConstant.BUSI_CODE_LOCATION: //位置数据上报
						break;
					case CommonConstant.BUSI_CODE_ALARM: //告警数据上报
						break;
					case CommonConstant.BUSI_CODE_OFFLINE: //终端下线
						break;
					case CommonConstant.BUSI_CODE_TERMINAL_STATE : //终端状态信息
						break;
					case CommonConstant.BUSI_CODE_VEHICLE: //车辆信息

						List<Vehicle> list = new ArrayList<>();
						BdmVehicle vehicleNew = null;
						if(isBatch){
							list = JSON.parseArray(JSON.toJSONString(message), Vehicle.class);
							List<BdmVehicle> listTmp = JSON.parseArray(JSON.toJSONString(busiObject), BdmVehicle.class);
							vehicleNew = listTmp.get(0);
						}else{
							Vehicle vehicle = JSON.parseObject(JSON.toJSONString(message), Vehicle.class);
							list.add(vehicle);
							vehicleNew = JSON.parseObject(JSON.toJSONString(busiObject), BdmVehicle.class);
						}
						messageUtils.sendMessage(list, vehicleNew.getId(),CommonConstant.BUSI_CODE_VEHICLE);
						break;
					case CommonConstant.BUSI_CODE_STAFF: //职工信息
						List<Staff> staffList = new ArrayList<>();
						BdmWorker worker = null;
						if(isBatch){
							staffList = JSON.parseArray(JSON.toJSONString(message), Staff.class);
							List<BdmWorker> staffListTmp = JSON.parseArray(JSON.toJSONString(busiObject), BdmWorker.class);
							worker = staffListTmp.get(0);
						}else{
							Staff staff = JSON.parseObject(JSON.toJSONString(message), Staff.class);
							staffList.add(staff);
							worker = JSON.parseObject(JSON.toJSONString(busiObject), BdmWorker.class);
						}
						messageUtils.sendMessage(staffList, worker.getId(),CommonConstant.BUSI_CODE_STAFF);
						break;
					case CommonConstant.BUSI_CODE_FACILITY: //基础设施信息
						List<Facility> facList = new ArrayList<>();
						BdmFacility bdmFac = null;
						if(isBatch){
							facList = JSON.parseArray(JSON.toJSONString(message), Facility.class);
							List<BdmFacility> facListTmp = JSON.parseArray(JSON.toJSONString(busiObject), BdmFacility.class);
							bdmFac = facListTmp.get(0);
						}else{
							Facility faci = JSON.parseObject(JSON.toJSONString(message), Facility.class);
							facList.add(faci);
							bdmFac = JSON.parseObject(JSON.toJSONString(busiObject), BdmFacility.class);
						}
						messageUtils.sendMessage(facList, bdmFac.getId(),CommonConstant.BUSI_CODE_FACILITY);
						break;
					case CommonConstant.BUSI_CODE_TERMINAL: //终端信息
						JSONObject obj = JSON.parseObject(JSON.toJSONString(busiObject));
						List<TerminalInfo> termList = new ArrayList<>();
						if(isBatch){
							termList = JSON.parseArray(JSON.toJSONString(message), TerminalInfo.class);
						}else{
							TerminalInfo terminal = JSON.parseObject(JSON.toJSONString(message), TerminalInfo.class);
							termList.add(terminal);
						}
						sendTerminal(termList, obj);
						break;
					case CommonConstant.BUSI_CODE_TRAIN_CARGO_BOX: //铁路货车车厢信息
						List<TrainCargoBox> trainList = new ArrayList<>();
						BdmTrainCargoBox tr = null;
						if(isBatch){
							trainList = JSON.parseArray(JSON.toJSONString(message), TrainCargoBox.class);
							List<BdmTrainCargoBox> trListTmp = JSON.parseArray(JSON.toJSONString(busiObject), BdmTrainCargoBox.class);
							tr = trListTmp.get(0);
						}else{
							TrainCargoBox faci = JSON.parseObject(JSON.toJSONString(message), TrainCargoBox.class);
							trainList.add(faci);
							tr = JSON.parseObject(JSON.toJSONString(busiObject), BdmTrainCargoBox.class);
						}
						messageUtils.sendMessage(trainList, tr.getId(),CommonConstant.BUSI_CODE_TRAIN_CARGO_BOX);
						break;
					case CommonConstant.BUSI_CODE_MINE_TRUCK: //矿用卡车信息
						List<Vehicle> vList = new ArrayList<>();
						BdmVehicle bv = null;
						if(isBatch){
							vList = JSON.parseArray(JSON.toJSONString(message), Vehicle.class);
							List<BdmVehicle> bvListTmp = JSON.parseArray(JSON.toJSONString(busiObject), BdmVehicle.class);
							bv = bvListTmp.get(0);
						}else{
							Vehicle faci = JSON.parseObject(JSON.toJSONString(message), Vehicle.class);
							vList.add(faci);
							bv = JSON.parseObject(JSON.toJSONString(busiObject), BdmVehicle.class);
						}
						messageUtils.sendMessage(vList, bv.getId(),CommonConstant.BUSI_CODE_MINE_TRUCK);
						break;
					case CommonConstant.BUSI_CODE_PRECISION_ASSEMBLY: //精密装备
						List<PrecisionAssembly> preList = new ArrayList<>();
						BdmPrecisionAssembly bp = null;
						if(isBatch){
							preList = JSON.parseArray(JSON.toJSONString(message), PrecisionAssembly.class);
							List<BdmPrecisionAssembly> bpListTmp = JSON.parseArray(JSON.toJSONString(busiObject), BdmPrecisionAssembly.class);
							bp = bpListTmp.get(0);
						}else{
							PrecisionAssembly preA = JSON.parseObject(JSON.toJSONString(message), PrecisionAssembly.class);
							preList.add(preA);
							bp = JSON.parseObject(JSON.toJSONString(busiObject), BdmPrecisionAssembly.class);
						}
						messageUtils.sendMessage(preList, bp.getId(),CommonConstant.BUSI_CODE_PRECISION_ASSEMBLY);
						break;
					case CommonConstant.BUSI_CODE_SHIP: //货船信息
						List<Ship> shipList = new ArrayList<>();
						BdmShip bs = null;
						if(isBatch){
							shipList = JSON.parseArray(JSON.toJSONString(message), Ship.class);
							List<BdmShip> bsListTmp = JSON.parseArray(JSON.toJSONString(busiObject), BdmShip.class);
							bs = bsListTmp.get(0);
						}else{
							Ship sh = JSON.parseObject(JSON.toJSONString(message), Ship.class);
							shipList.add(sh);
							bs = JSON.parseObject(JSON.toJSONString(busiObject), BdmShip.class);
						}
						messageUtils.sendMessage(shipList, bs.getId(),CommonConstant.BUSI_CODE_SHIP);
						break;
				}
			}
			// 手动提交offset
			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("调用外部数据同步接口消费失败 :" + e.getMessage(), e);
		}
	}

	private void sendVehicle(List<Vehicle> vehicles, BdmVehicle busiObject) throws Exception{
		//1.查找车辆对应的平台
		long vehicleId = busiObject.getId();
		List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
			.eq(InPosSystemObject::getObjectId, vehicleId)
			.eq(InPosSystemObject::getObjectType,2)
		);
		//systemId去重
		Set<Long> systemIds = new HashSet<>();
		list.forEach(item -> {
			systemIds.add(item.getSystemId());
		});

		//2.查找平台对应的接口
		List<SmInterfaceSystem> systems = systemService.listByIds(systemIds);

		//3.调用接口推送数据
		//组装报文
		Header header = new Header();
		header.setSystemId(CommonConstant.POS_SYSTEM_ID);
		header.setServiceId(CommonConstant.BUSI_CODE_VEHICLE);
		List<InInterLog> logs = new ArrayList<>();
		for(SmInterfaceSystem s : systems){
			String code = s.getPlatHttpSendCode();
			header.setSerialNum((new Date()).getTime()+"");
			header.setReqTime(DateUtil.sdfHolder.get().format(new Date()).replace("-","").replace(" ",""));
			Message message = messageUtils.encryptData(header, JSON.toJSONString(vehicles),s.getId()+"");
			Map<String,Object> map = BeanUtil.beanToMap(message);
			InInterLog interLog = new InInterLog();
			if(!StringUtils.isEmpty(code)){
				long start = System.currentTimeMillis();
				R<Object> res = interManager.call(code, map);
				long end = System.currentTimeMillis();
				//查询接口基本信息
				String interJSONStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_POS_INTER_PREFIX + code);
				SmInterfaceManage inter = null;
				if(StringUtils.isEmpty(interJSONStr)){
					inter = interfaceManageService.findInterfaceManageByInterCode(code);
					//有效期12个小时
					stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_POS_INTER_PREFIX + code, JSON.toJSONString(inter), 12, TimeUnit.HOURS);
				}else{
					inter = JSON.parseObject(interJSONStr, SmInterfaceManage.class);
				}

				interLog.setDirection(CommonConstant.INTER_DIRECTION_OUT);
				interLog.setInterCode(code);
				interLog.setInterName(inter.getInterNote());
				interLog.setSystemId(s.getId());
				interLog.setServiceId(0L);
				interLog.setBusiServiceId(CommonConstant.BUSI_CODE_VEHICLE);
				interLog.setInterType(inter.getInterType());
				interLog.setUrl(inter.getUrl());
				interLog.setParam(JSON.toJSONString(map));
				interLog.setResMessage(JSON.toJSONString(res));
				interLog.setCallTime(new Date());
				interLog.setCreateTime(new Date());
				interLog.setCallCost(end-start);
				interLog.setServerIp(inter.getIp());
				if(!res.isSuccess()){
					interLog.setResult(0);
				}else{
					interLog.setResult(1);
				}
			}
			logs.add(interLog);
		}
		logService.saveBatch(logs);
	}



	/**
	 * 推送人员信息
	 * @param staffs
	 * @param busiObject
	 * @throws Exception
	 */
	/*private void sendWorker(List<Staff> staffs, BdmWorker busiObject) throws Exception{
		//1.查找人员对应的平台
		long staffId = busiObject.getId();
		List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
			.eq(InPosSystemObject::getObjectId, staffId)
			.eq(InPosSystemObject::getObjectType,2)
		);
		//systemId去重
		Set<Long> systemIds = new HashSet<>();
		list.forEach(item -> {
			systemIds.add(item.getSystemId());
		});

		//2.查找平台对应的接口
		List<SmInterfaceSystem> systems = systemService.listByIds(systemIds);

		//3.调用接口推送数据
		//组装报文
		Header header = new Header();
		header.setSystemId(CommonConstant.POS_SYSTEM_ID);
		header.setServiceId(CommonConstant.BUSI_CODE_STAFF);
		List<InInterLog> logs = new ArrayList<>();
		for(SmInterfaceSystem s : systems){
			String code = s.getPlatHttpSendCode();
			header.setSerialNum((new Date()).getTime()+"");
			header.setReqTime(DateUtil.sdfHolder.get().format(new Date()).replace("-","").replace(" ",""));
			Message message = messageUtils.encryptData(header, JSON.toJSONString(staffs),s.getId()+"");
			Map<String,Object> map = BeanUtil.beanToMap(message);
			InInterLog interLog = new InInterLog();
			if(!StringUtils.isEmpty(code)){
				long start = System.currentTimeMillis();
				R<Object> res = interManager.call(code, map);
				long end = System.currentTimeMillis();
				//查询接口基本信息
				String interJSONStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_POS_INTER_PREFIX + code);
				SmInterfaceManage inter = null;
				if(StringUtils.isEmpty(interJSONStr)){
					inter = interfaceManageService.findInterfaceManageByInterCode(code);
					//有效期12个小时
					stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_POS_INTER_PREFIX + code, JSON.toJSONString(inter), 12, TimeUnit.HOURS);
				}else{
					inter = JSON.parseObject(interJSONStr, SmInterfaceManage.class);
				}

				interLog.setDirection(CommonConstant.INTER_DIRECTION_OUT);
				interLog.setInterCode(code);
				interLog.setInterName(inter.getInterNote());
				interLog.setSystemId(s.getId());
				interLog.setServiceId(0L);
				interLog.setBusiServiceId(CommonConstant.BUSI_CODE_STAFF);
				interLog.setInterType(inter.getInterType());
				interLog.setUrl(inter.getUrl());
				interLog.setParam(JSON.toJSONString(map));
				interLog.setResMessage(JSON.toJSONString(res));
				interLog.setCallTime(new Date());
				interLog.setCreateTime(new Date());
				interLog.setCallCost(end-start);
				interLog.setServerIp(inter.getIp());
				if(!res.isSuccess()){
					interLog.setResult(0);
				}else{
					interLog.setResult(1);
				}
			}
			logs.add(interLog);
		}
		logService.saveBatch(logs);
	}*/

	/**
	 * 推送基础设施
	 * @param facs
	 * @param busiObject
	 * @throws Exception
	 */
	/*private void sendFacility(List<Facility> facs, BdmFacility busiObject) throws Exception{
		//1.查找基础设施对应的平台
		long staffId = busiObject.getId();
		List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
			.eq(InPosSystemObject::getObjectId, staffId)
			.eq(InPosSystemObject::getObjectType,2)
		);
		//systemId去重
		Set<Long> systemIds = new HashSet<>();
		list.forEach(item -> {
			systemIds.add(item.getSystemId());
		});

		//2.查找平台对应的接口
		List<SmInterfaceSystem> systems = systemService.listByIds(systemIds);

		//3.调用接口推送数据
		//组装报文
		Header header = new Header();
		header.setSystemId(CommonConstant.POS_SYSTEM_ID);
		header.setServiceId(CommonConstant.BUSI_CODE_FACILITY);
		List<InInterLog> logs = new ArrayList<>();
		for(SmInterfaceSystem s : systems){
			String code = s.getPlatHttpSendCode();
			header.setSerialNum((new Date()).getTime()+"");
			header.setReqTime(DateUtil.sdfHolder.get().format(new Date()).replace("-","").replace(" ",""));
			Message message = messageUtils.encryptData(header, JSON.toJSONString(facs),s.getId()+"");
			Map<String,Object> map = BeanUtil.beanToMap(message);
			InInterLog interLog = new InInterLog();
			if(!StringUtils.isEmpty(code)){
				long start = System.currentTimeMillis();
				R<Object> res = interManager.call(code, map);
				long end = System.currentTimeMillis();
				//查询接口基本信息
				String interJSONStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_POS_INTER_PREFIX + code);
				SmInterfaceManage inter = null;
				if(StringUtils.isEmpty(interJSONStr)){
					inter = interfaceManageService.findInterfaceManageByInterCode(code);
					//有效期12个小时
					stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_POS_INTER_PREFIX + code, JSON.toJSONString(inter), 12, TimeUnit.HOURS);
				}else{
					inter = JSON.parseObject(interJSONStr, SmInterfaceManage.class);
				}

				interLog.setDirection(CommonConstant.INTER_DIRECTION_OUT);
				interLog.setInterCode(code);
				interLog.setInterName(inter.getInterNote());
				interLog.setSystemId(s.getId());
				interLog.setServiceId(0L);
				interLog.setBusiServiceId(CommonConstant.BUSI_CODE_FACILITY);
				interLog.setInterType(inter.getInterType());
				interLog.setUrl(inter.getUrl());
				interLog.setParam(JSON.toJSONString(map));
				interLog.setResMessage(JSON.toJSONString(res));
				interLog.setCallTime(new Date());
				interLog.setCreateTime(new Date());
				interLog.setCallCost(end-start);
				interLog.setServerIp(inter.getIp());
				if(!res.isSuccess()){
					interLog.setResult(0);
				}else{
					interLog.setResult(1);
				}
			}
			logs.add(interLog);
		}
		logService.saveBatch(logs);
	}*/

	/**
	 * 推送终端信息
	 * @param terms
	 * @param busiObject
	 * @throws Exception
	 */
	private void sendTerminal(List<TerminalInfo> terms, JSONObject busiObject) throws Exception{
		//1.查找终端对应的平台
		long terminalId = (long)busiObject.get("id");
		List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
			.eq(InPosSystemObject::getObjectId, terminalId)
			.eq(InPosSystemObject::getObjectType,2)
		);
		//systemId去重
		Set<Long> systemIds = new HashSet<>();
		list.forEach(item -> {
			systemIds.add(item.getSystemId());
		});

		//2.查找平台对应的接口
		List<SmInterfaceSystem> systems = systemService.listByIds(systemIds);

		//3.调用接口推送数据
		//组装报文
		Header header = new Header();
		header.setSystemId(CommonConstant.POS_SYSTEM_ID);
		header.setServiceId(CommonConstant.BUSI_CODE_TERMINAL);
		List<InInterLog> logs = new ArrayList<>();
		for(SmInterfaceSystem s : systems){
			String code = s.getPlatHttpSendCode();
			header.setSerialNum((new Date()).getTime()+"");
			header.setReqTime(DateUtil.sdfHolder.get().format(new Date()).replace("-","").replace(" ",""));
			Message message = messageUtils.encryptData(header, JSON.toJSONString(terms),s.getId()+"");
			Map<String,Object> map = BeanUtil.beanToMap(message);
			InInterLog interLog = new InInterLog();
			if(!StringUtils.isEmpty(code)){
				long start = System.currentTimeMillis();
				R<Object> res = interManager.call(code, map);
				long end = System.currentTimeMillis();
				//查询接口基本信息
				String interJSONStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_POS_INTER_PREFIX + code);
				SmInterfaceManage inter = null;
				if(StringUtils.isEmpty(interJSONStr)){
					inter = interfaceManageService.findInterfaceManageByInterCode(code);
					//有效期12个小时
					stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_POS_INTER_PREFIX + code, JSON.toJSONString(inter), 12, TimeUnit.HOURS);
				}else{
					inter = JSON.parseObject(interJSONStr, SmInterfaceManage.class);
				}

				interLog.setDirection(CommonConstant.INTER_DIRECTION_OUT);
				interLog.setInterCode(code);
				interLog.setInterName(inter.getInterNote());
				interLog.setSystemId(s.getId());
				interLog.setServiceId(0L);
				interLog.setServiceCode(header.getServiceId());
				interLog.setBusiServiceId(CommonConstant.BUSI_CODE_TERMINAL);
				interLog.setInterType(inter.getInterType());
				interLog.setUrl(inter.getUrl());
				interLog.setParam(JSON.toJSONString(map));
				interLog.setResMessage(JSON.toJSONString(res));
				interLog.setCallTime(new Date());
				interLog.setCreateTime(new Date());
				interLog.setCallCost(end-start);
				interLog.setServerIp(inter.getIp());
				if(!res.isSuccess()){
					interLog.setResult(0);
				}else{
					interLog.setResult(1);
				}
			}
			logs.add(interLog);
		}
		logService.saveBatchInterLogAsync(logs);
	}

	/**
	 * 推送铁路货车车厢信息
	 * @param trains
	 * @param busiObject
	 * @throws Exception
	 */
	/*private void sendTrainCargoBox(List<TrainCargoBox> trains, BdmTrainCargoBox busiObject) throws Exception{
		//1.查找基础设施对应的平台
		long trainId = busiObject.getId();
		List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
			.eq(InPosSystemObject::getObjectId, trainId)
			.eq(InPosSystemObject::getObjectType,2)
		);
		//systemId去重
		Set<Long> systemIds = new HashSet<>();
		list.forEach(item -> {
			systemIds.add(item.getSystemId());
		});

		//2.查找平台对应的接口
		List<SmInterfaceSystem> systems = systemService.listByIds(systemIds);

		//3.调用接口推送数据
		//组装报文
		Header header = new Header();
		header.setSystemId(CommonConstant.POS_SYSTEM_ID);
		header.setServiceId(CommonConstant.BUSI_CODE_TRAIN_CARGO_BOX);
		List<InInterLog> logs = new ArrayList<>();
		for(SmInterfaceSystem s : systems){
			String code = s.getPlatHttpSendCode();
			header.setSerialNum((new Date()).getTime()+"");
			header.setReqTime(DateUtil.sdfHolder.get().format(new Date()).replace("-","").replace(" ",""));
			Message message = messageUtils.encryptData(header, JSON.toJSONString(trains),s.getId()+"");
			Map<String,Object> map = BeanUtil.beanToMap(message);
			InInterLog interLog = new InInterLog();
			if(!StringUtils.isEmpty(code)){
				long start = System.currentTimeMillis();
				R<Object> res = interManager.call(code, map);
				long end = System.currentTimeMillis();
				//查询接口基本信息
				String interJSONStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_POS_INTER_PREFIX + code);
				SmInterfaceManage inter = null;
				if(StringUtils.isEmpty(interJSONStr)){
					inter = interfaceManageService.findInterfaceManageByInterCode(code);
					//有效期12个小时
					stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_POS_INTER_PREFIX + code, JSON.toJSONString(inter), 12, TimeUnit.HOURS);
				}else{
					inter = JSON.parseObject(interJSONStr, SmInterfaceManage.class);
				}

				interLog.setDirection(CommonConstant.INTER_DIRECTION_OUT);
				interLog.setInterCode(code);
				interLog.setInterName(inter.getInterNote());
				interLog.setSystemId(s.getId());
				interLog.setServiceId(0L);
				interLog.setBusiServiceId(CommonConstant.BUSI_CODE_TRAIN_CARGO_BOX);
				interLog.setInterType(inter.getInterType());
				interLog.setUrl(inter.getUrl());
				interLog.setParam(JSON.toJSONString(map));
				interLog.setResMessage(JSON.toJSONString(res));
				interLog.setCallTime(new Date());
				interLog.setCreateTime(new Date());
				interLog.setCallCost(end-start);
				interLog.setServerIp(inter.getIp());
				if(!res.isSuccess()){
					interLog.setResult(0);
				}else{
					interLog.setResult(1);
				}
			}
			logs.add(interLog);
		}
		logService.saveBatch(logs);
	}*/

	/**
	 * 推送精密装备
	 * @param pres
	 * @param busiObject
	 * @throws Exception
	 */
	/*private void sendPrecision(List<PrecisionAssembly> pres, BdmPrecisionAssembly busiObject) throws Exception{
		//1.查找精密装备对应的平台
		long preId = busiObject.getId();
		List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
			.eq(InPosSystemObject::getObjectId, preId)
			.eq(InPosSystemObject::getObjectType,2)
		);
		//systemId去重
		Set<Long> systemIds = new HashSet<>();
		list.forEach(item -> {
			systemIds.add(item.getSystemId());
		});

		//2.查找平台对应的接口
		List<SmInterfaceSystem> systems = systemService.listByIds(systemIds);

		//3.调用接口推送数据
		//组装报文
		Header header = new Header();
		header.setSystemId(CommonConstant.POS_SYSTEM_ID);
		header.setServiceId(CommonConstant.BUSI_CODE_PRECISION_ASSEMBLY);
		List<InInterLog> logs = new ArrayList<>();
		for(SmInterfaceSystem s : systems){
			String code = s.getPlatHttpSendCode();
			header.setSerialNum((new Date()).getTime()+"");
			header.setReqTime(DateUtil.sdfHolder.get().format(new Date()).replace("-","").replace(" ",""));
			Message message = messageUtils.encryptData(header, JSON.toJSONString(pres),s.getId()+"");
			Map<String,Object> map = BeanUtil.beanToMap(message);
			InInterLog interLog = new InInterLog();
			if(!StringUtils.isEmpty(code)){
				long start = System.currentTimeMillis();
				R<Object> res = interManager.call(code, map);
				long end = System.currentTimeMillis();
				//查询接口基本信息
				String interJSONStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_POS_INTER_PREFIX + code);
				SmInterfaceManage inter = null;
				if(StringUtils.isEmpty(interJSONStr)){
					inter = interfaceManageService.findInterfaceManageByInterCode(code);
					//有效期12个小时
					stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_POS_INTER_PREFIX + code, JSON.toJSONString(inter), 12, TimeUnit.HOURS);
				}else{
					inter = JSON.parseObject(interJSONStr, SmInterfaceManage.class);
				}

				interLog.setDirection(CommonConstant.INTER_DIRECTION_OUT);
				interLog.setInterCode(code);
				interLog.setInterName(inter.getInterNote());
				interLog.setSystemId(s.getId());
				interLog.setServiceId(0L);
				interLog.setBusiServiceId(CommonConstant.BUSI_CODE_PRECISION_ASSEMBLY);
				interLog.setInterType(inter.getInterType());
				interLog.setUrl(inter.getUrl());
				interLog.setParam(JSON.toJSONString(map));
				interLog.setResMessage(JSON.toJSONString(res));
				interLog.setCallTime(new Date());
				interLog.setCreateTime(new Date());
				interLog.setCallCost(end-start);
				interLog.setServerIp(inter.getIp());
				if(!res.isSuccess()){
					interLog.setResult(0);
				}else{
					interLog.setResult(1);
				}
			}
			logs.add(interLog);
		}
		logService.saveBatch(logs);
	}*/

	/**
	 * 推送货船信息
	 * @param ships
	 * @param busiObject
	 * @throws Exception
	 */
	/*private void sendShip(List<Ship> ships, BdmShip busiObject) throws Exception{
		//1.查找货船对应的平台
		long shipId = busiObject.getId();
		List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
			.eq(InPosSystemObject::getObjectId, shipId)
			.eq(InPosSystemObject::getObjectType,2)
		);
		//systemId去重
		Set<Long> systemIds = new HashSet<>();
		list.forEach(item -> {
			systemIds.add(item.getSystemId());
		});

		//2.查找平台对应的接口
		List<SmInterfaceSystem> systems = systemService.listByIds(systemIds);

		//3.调用接口推送数据
		//组装报文
		Header header = new Header();
		header.setSystemId(CommonConstant.POS_SYSTEM_ID);
		header.setServiceId(CommonConstant.BUSI_CODE_SHIP);
		List<InInterLog> logs = new ArrayList<>();
		for(SmInterfaceSystem s : systems){
			String code = s.getPlatHttpSendCode();
			header.setSerialNum((new Date()).getTime()+"");
			header.setReqTime(DateUtil.sdfHolder.get().format(new Date()).replace("-","").replace(" ",""));
			Message message = messageUtils.encryptData(header, JSON.toJSONString(ships),s.getId()+"");
			Map<String,Object> map = BeanUtil.beanToMap(message);
			InInterLog interLog = new InInterLog();
			if(!StringUtils.isEmpty(code)){
				long start = System.currentTimeMillis();
				R<Object> res = interManager.call(code, map);
				long end = System.currentTimeMillis();
				//查询接口基本信息
				String interJSONStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_POS_INTER_PREFIX + code);
				SmInterfaceManage inter = null;
				if(StringUtils.isEmpty(interJSONStr)){
					inter = interfaceManageService.findInterfaceManageByInterCode(code);
					//有效期12个小时
					stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_POS_INTER_PREFIX + code, JSON.toJSONString(inter), 12, TimeUnit.HOURS);
				}else{
					inter = JSON.parseObject(interJSONStr, SmInterfaceManage.class);
				}

				interLog.setDirection(CommonConstant.INTER_DIRECTION_OUT);
				interLog.setInterCode(code);
				interLog.setInterName(inter.getInterNote());
				interLog.setSystemId(s.getId());
				interLog.setServiceId(0L);
				interLog.setBusiServiceId(CommonConstant.BUSI_CODE_SHIP);
				interLog.setInterType(inter.getInterType());
				interLog.setUrl(inter.getUrl());
				interLog.setParam(JSON.toJSONString(map));
				interLog.setResMessage(JSON.toJSONString(res));
				interLog.setCallTime(new Date());
				interLog.setCreateTime(new Date());
				interLog.setCallCost(end-start);
				interLog.setServerIp(inter.getIp());
				if(!res.isSuccess()){
					interLog.setResult(0);
				}else{
					interLog.setResult(1);
				}
			}
			logs.add(interLog);
		}
		logService.saveBatch(logs);
	}*/



}
