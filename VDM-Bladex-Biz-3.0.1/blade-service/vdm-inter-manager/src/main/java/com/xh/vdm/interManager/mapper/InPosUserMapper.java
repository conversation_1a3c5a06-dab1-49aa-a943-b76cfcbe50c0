/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.mapper;

import com.xh.vdm.interManager.entity.InPosUser;
import com.xh.vdm.interManager.vo.InPosUserVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.vo.request.InterfaceAuthRequest;
import com.xh.vdm.interManager.vo.response.InterfaceResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
public interface InPosUserMapper extends BaseMapper<InPosUser> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inPosUser
	 * @return
	 */
	List<InPosUserVO> selectInPosUserPage(IPage page, InPosUserVO inPosUser);


	/**
	 * 查询用户授权的接口
	 * @param request
	 * @return
	 */
	List<InterfaceResponse> getInterfaceAuth(@Param("request") InterfaceAuthRequest request);

}
