package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.BdmDeviceStatus;
import com.xh.vdm.interManager.mapper.BdmDeviceStatusMapper;
import com.xh.vdm.interManager.service.BdmDeviceStatusService;
import org.springblade.core.mp.support.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * (BdmDeviceLink)表服务实现类
 */
@Service
public class BdmDeviceStatusServiceImpl extends ServiceImpl<BdmDeviceStatusMapper, BdmDeviceStatus> implements BdmDeviceStatusService {

}
