package com.xh.vdm.interManager.service.impl;

import com.xh.vdm.interManager.mapper.TerminalMapper;
import com.xh.vdm.interManager.service.IBdmVehicleNewService;
import com.xh.vdm.interManager.service.ITerminalService;
import com.xh.vdm.interManager.service.IWorkerService;
import com.xh.vdm.interManager.service.terminal.*;
import org.springblade.common.constant.CommonConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class TerminalServiceImpl implements ITerminalService {

	@Resource
	private TerminalMapper terminalMapper;

	@Resource
	private IBdmVehicleNewService vehicleNewService;

	@Resource
	private IWorkerService workerService;

	@Resource
	private IRnssDeviceService rnssDeviceService;

	@Resource
	private IRdssDeviceService rdssDeviceService;

	@Resource
	private IWearableDeviceService wearableDeviceService;

	@Resource
	private IMonitDeviceService monitDeviceService;

	@Resource
	private IPntDeviceService pntDeviceService;



	@Override
	public boolean checkExistByDeviceNum(String deviceNum) {
		long count = terminalMapper.getDeviceCountByDeviceNum(deviceNum);
		if(count > 0){
			return false;
		}
		return true;
	}


	@Override
	public boolean checkExistByImei(String imei) {
		long count = terminalMapper.getDeviceCountByImei(imei);
		if(count > 0){
			return false;
		}
		return true;
	}

	@Override
	public boolean checkExistByUniqueId(String uniqueId) {
		long count = terminalMapper.getDeviceCountByUniqueId(uniqueId);
		if(count > 0){
			return false;
		}
		return true;
	}

	@Override
	public long findDeviceCountByDeviceNum(String deviceNum) {
		return terminalMapper.getDeviceCountByDeviceNum(deviceNum);
	}

	@Override
	public long findDeviceCountByImei(String imei) {
		return terminalMapper.getDeviceCountByImei(imei);
	}

	@Override
	public long findDeviceCountByUniqueId(String uniqueId) {
		return terminalMapper.getDeviceCountByUniqueId(uniqueId);
	}

	@Override
	public long findIdByUniqueId(String uniqueId) {
		Long id = terminalMapper.getIdByUniqueId(uniqueId);
		if(id == null){
			return -1;
		}
		return id;
	}

	@Override
	public Object findTargetByTypeAndId(String type, Long id) {
		if(type.startsWith("1")){
			//如果是车辆

		}else if(type.startsWith("2")){
			//如果是人员

		}else if(type.startsWith("3")){
			//如果是基础设施

		}else if(type.startsWith("4")){
			//如果是集装箱

		}else if(type.startsWith("5")){
			//如果是外派人员

		}else if(type.startsWith("6")){
			//如果是访客
		}
		//todo 未开发完
		return null;
	}


	@Override
	public Object findDeviceByTypeAndId(String type, Long id) {
		if(CommonConstant.DEVICE_TYPE_RNSS.equals(type)){
			//北斗定位终端
			return rnssDeviceService.getById(id);
		}else if(CommonConstant.DEVICE_TYPE_WEARABLE.equals(type)){
			//穿戴式终端
			return wearableDeviceService.getById(id);
		}else if(CommonConstant.DEVICE_TYPE_RDSS.equals(type)){
			//短报文终端
			return rdssDeviceService.getById(id);
		}else if(CommonConstant.DEVICE_TYPE_MONIT.equals(type)){
			//监测终端
			return monitDeviceService.getById(id);
		}else if(CommonConstant.DEVICE_TYPE_PNT.equals(type)){
			//北斗授时终端
			return pntDeviceService.getById(id);
		}
		return null;
	}
}
