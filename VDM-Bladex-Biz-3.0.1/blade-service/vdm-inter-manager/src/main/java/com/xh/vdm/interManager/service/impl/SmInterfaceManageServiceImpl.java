/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.SmInterfaceManage;
import com.xh.vdm.interManager.mapper.SmInterfaceManageMapper;
import com.xh.vdm.interManager.service.ISmInterfaceManageService;
import com.xh.vdm.interManager.vo.SmInterfaceManageVO;
import org.springblade.common.constant.CommonConstant;
import org.springframework.stereotype.Service;

/**
 * 接口管理表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@Service
public class SmInterfaceManageServiceImpl extends ServiceImpl<SmInterfaceManageMapper, SmInterfaceManage> implements ISmInterfaceManageService {

	@Override
	public IPage<SmInterfaceManageVO> selectSmInterfaceManagePage(IPage<SmInterfaceManageVO> page, SmInterfaceManageVO smInterfaceManage) {
		return page.setRecords(baseMapper.selectSmInterfaceManagePage(page, smInterfaceManage));
	}

	@Override
	public SmInterfaceManage findInterfaceManageByInterCode(String interCode) {
		return baseMapper.selectOne(Wrappers.lambdaQuery(SmInterfaceManage.class)
			.eq(SmInterfaceManage::getInterCode, interCode)
			.eq(SmInterfaceManage::getState, CommonConstant.STATE_U));
	}
}
