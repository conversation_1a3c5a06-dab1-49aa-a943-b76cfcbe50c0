package com.xh.vdm.interManager.dto;

import lombok.Data;

// 二级平台告警（校准版）
@Data
public class AlarmFromSecondary {

	// 单位ID
	private Long deptId;

	// 目标类型（类型值与名称的映射，详见blade_dict_biz表code=target_type的记录）
	private Byte targetType;

	// 目标ID
	private Long targetId;

	// 设备类型（类型值与名称的映射，详见blade_dict_biz表code=bdm_device_type的记录）
	private Byte deviceType;

	// 设备ID
	private Long deviceId;

	// 设备赋码号
	private String deviceNum;

	// 告警类型（类型值与名称的映射，详见blade_dict_biz表code=alarm_type的记录）
	private Short alarmType;

	// 告警等级（等级值与名称的映射，详见blade_dict_biz表code=alarm_level的记录）
	private Byte alarmLevel;

	// 告警来源（来源值与名称的映射，详见blade_dict_biz表code=alarm_origin的记录）
	private Byte alarmSource;

	// 告警结束状态（0：未结束，1：已结束）
	private Byte completed;

	// 经度
	private Double longitude;

	// 纬度
	private Double latitude;

	// 海拔
	private Float altitude;

	// x轴坐标
	private Double x;

	// y轴坐标
	private Double y;

	// z轴坐标
	private Float z;

	// 速度（单位：km/h）
	private Float speed;

	// 方向
	private Integer direction;

	// 告警时间（格式：秒数）
	private Long time;

	// 定位类型（0：卫星，1：蓝牙信标）
	private Integer locType;

	// 地图坐标系类型（0：WGS84坐标，1：GCJ02坐标）
	private Integer mapType;

	// 是否新定位（0：否，1：是）
	private Integer state;
}
