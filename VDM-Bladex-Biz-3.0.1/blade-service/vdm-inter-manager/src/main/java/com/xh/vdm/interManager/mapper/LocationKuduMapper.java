package com.xh.vdm.interManager.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.interManager.entity.LocationKudu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: kudu中的定位表 pos_gn.locations
 * @author: zhouxw
 * @date: 2023-05-143 19:10:14
 * @param: * @param null
 * @return:
 * @return: null
 **/
@DS("impala")
public interface LocationKuduMapper extends BaseMapper<LocationKudu> {

	/**
	 * 查询在指定时间之内最新的数据，有可能为空
	 * @param deviceId
	 * @param timestampBefore 查询该时间戳之后的最新数据
	 * @return
	 */
	LocationKudu getLatestLocation(@Param("deviceId") Long deviceId, @Param("timestampBefore") Long timestampBefore);
}
