/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.interManager.entity.SmInterfaceParam;
import com.xh.vdm.interManager.vo.SmInterfaceParamVO;

/**
 * 接口参数表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
public interface ISmInterfaceParamService extends IService<SmInterfaceParam> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param smInterfaceParam
	 * @return
	 */
	IPage<SmInterfaceParamVO> selectSmInterfaceParamPage(IPage<SmInterfaceParamVO> page, SmInterfaceParamVO smInterfaceParam);




	/**
	 * @Description 获取主键id
	 * <AUTHOR>
	 * @Date 2020/1/19 8:31
	 * @Company CTTIC
	 **/
	long getNewId();

}
