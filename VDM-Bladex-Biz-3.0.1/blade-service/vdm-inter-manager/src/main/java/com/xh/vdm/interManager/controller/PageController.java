package com.xh.vdm.interManager.controller;

import com.xh.vdm.interManager.entity.SmInterfaceDict;
import com.xh.vdm.interManager.service.ISmInterfaceDictService;
import com.xh.vdm.interManager.utils.R;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: TODO
 * @Auther: zxing
 * @Date: 2022/3/1 00:50
 * @company：CTTIC
 */

@Controller
@RequestMapping("/interManager/")
public class PageController {

    @Resource
    private ISmInterfaceDictService dictService;

    @GetMapping("/toManage")
    public String toManage(){
        return "manage";
    }

    @GetMapping("/toParam")
    public ModelAndView toParam(HttpServletRequest request){
        String interfaceManageId = request.getParameter("manageId");
        ModelAndView mv = new ModelAndView("param");
        mv.addObject("interfaceManageId", interfaceManageId);
        return mv;
    }

    @GetMapping("/toSystem")
    public String toSystem(){
        return "system";
    }

    @GetMapping("/toEditParam")
    public ModelAndView toEditParam(HttpServletRequest request){
        String interfaceManageId = request.getParameter("manageId");
        ModelAndView mv = new ModelAndView("editParam");
        mv.addObject("interfaceManageId", interfaceManageId);
        return mv;
    }

    /**
     * 外部推送接口管理
     * @return
     */
    @GetMapping("/toInterfacePush")
    public String toInterfacePush(){
        return "interfacePush";
    }

    /**
     * 外部推送接口推送记录
     * @return
     */
    @GetMapping("/toInterfacePushData")
    public String toInterfacePushData(){
        return "interfacePushData";
    }

    /**
     * 获取字典
     */
    @GetMapping("/dict")
    @ResponseBody
    public R<List<SmInterfaceDict>> dictionary(String code) {
        List<SmInterfaceDict> tree = dictService.getList(code);
        return R.data(tree);
    }

}
