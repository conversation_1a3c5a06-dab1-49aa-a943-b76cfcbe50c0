package com.xh.vdm.interManager.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.entity.InPosSystemObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.interManager.vo.request.PlatformObjRequest;
import com.xh.vdm.interManager.vo.request.SystemObjectSaveRequest;
import org.springblade.core.mp.support.Query;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 平台--业务对象映射表。多对多的关系。	业务对象包含终端、运输车辆、职工、生产厂区、铁路货车车厢、矿用卡车、精密装备、货船。 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
public interface IInPosSystemObjectService extends IService<InPosSystemObject> {

	/**
	 * 保存平台-业务对象映射信息
	 * @param request
	 */
	void save(SystemObjectSaveRequest request);



	// 平台当前已绑定业务对象
	JSONObject objMap (long systemId);

	// 平台业务对象分页列表
	IPage<JSONObject> objPage (PlatformObjRequest request, Query query, String tenantId) throws Exception;

	// 平台绑定业务对象
	void bindObj (long systemId, Map<Byte, List<Long>> targetMap, Map<Byte, List<Long>> deviceMap);


}
