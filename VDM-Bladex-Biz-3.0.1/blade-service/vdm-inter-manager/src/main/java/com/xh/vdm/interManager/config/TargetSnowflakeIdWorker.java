package com.xh.vdm.interManager.config;

import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.net.NetworkInterface;

/**
 * <p>名称：SnowflakeIdWorker.java</p>
 * <p>描述：分布式自增长ID</p>
 * <pre>
 *     Twitter的 Snowflake　JAVA实现方案
 * </pre>
 * 核心代码为其IdPerson这个类实现，其原理结构如下，我分别用一个0表示一位，用—分割开部分的作用：
 * 1||0---0000000000 0000000000 0000000000 --- 00000 --- 00000 --- 00000 ---00000000000000000
 * 在上面的字符串中，第一位为未使用（实际上也可作为long的符号位），接下来的32位为毫秒级时间，
 * 然后5位设备类型,5位datacenter标识位，5位机器ID（并不算标识符，实际是为线程标识），
 * 然后16位该毫秒内的当前毫秒内的计数，加起来刚好64位，为一个Long型。
 * 这样的好处是，整体上按照时间自增排序，并且整个分布式系统内不会产生ID碰撞（由datacenter和机器ID作区分），
 * 并且效率较高，经测试，snowflake每秒能够产生26万ID左右，完全满足需要。
 * <p>
 * 64位ID (32(毫秒)+5(设备类型）+5(机器ID)+5(业务编码)+16(重复累加))
 *
 */
@Component
public class TargetSnowflakeIdWorker {

	private final static long twepoch = 1717171200L;

	private final static long DEVICE_TYPE_BITS = 5L;
	private final static long DATACENTER_ID_BITS = 5L;
	private final static long MACHINE_ID_BITS = 5L;
	private final static long SEQUENCE_BITS = 16L;

	private final static long MAX_DEVICE_TYPE = -1L ^ (-1L << DEVICE_TYPE_BITS);
	private final static long MAX_DATACENTER_ID = -1L ^ (-1L << DATACENTER_ID_BITS);
	private final static long MAX_MACHINE_ID = -1L ^ (-1L << MACHINE_ID_BITS);
	private final static long SEQUENCE_MASK = -1L ^ (-1L << SEQUENCE_BITS);

	//业务编码
	private final static long MACHINE_ID_SHIFT = SEQUENCE_BITS;
	private final static long DATACENTER_ID_SHIFT = MACHINE_ID_SHIFT + MACHINE_ID_BITS;
	private final static long DEVICE_TYPE_SHIFT = DATACENTER_ID_SHIFT + DATACENTER_ID_BITS;
	private final static long TIME_SHIFT = DEVICE_TYPE_SHIFT + DEVICE_TYPE_BITS;

	private static long lastTimestamp = -1L;
	private long sequence = 0L;
	private final long deviceType;
	private final long datacenterId;
	private final long machineId;

	public TargetSnowflakeIdWorker() {
		this.deviceType = 1L; // 默认设备类型为1
		this.datacenterId = getDatacenterId(MAX_DATACENTER_ID);
		this.machineId = getMaxMachineId(datacenterId, MAX_MACHINE_ID);
	}

	public TargetSnowflakeIdWorker(long deviceType, long datacenterId, long machineId) {
		if (deviceType < 0 || deviceType > MAX_DEVICE_TYPE) {
			throw new IllegalArgumentException("无效的设备类型");
		}
		if (datacenterId < 0 || datacenterId > MAX_DATACENTER_ID) {
			throw new IllegalArgumentException(String.format("数据中心标识位数必须在0和%d之间", MAX_DATACENTER_ID));
		}
		if (machineId < 0 || machineId > MAX_MACHINE_ID) {
			throw new IllegalArgumentException(String.format("机器ID必须在0和%d之间", MAX_MACHINE_ID));
		}
		this.deviceType = deviceType;
		this.datacenterId = datacenterId;
		this.machineId = machineId;
	}

	public synchronized long nextId() {
		long timestamp = timeGen();

		if (timestamp < lastTimestamp) {
			throw new RuntimeException(String.format("时钟向后移动，拒绝生成%d毫秒内的ID", lastTimestamp - timestamp));
		}

		if (lastTimestamp == timestamp) {
			sequence = (sequence + 1) & SEQUENCE_MASK;
			if (sequence == 0) {
				timestamp = tilNextMillis(lastTimestamp);
			}
		} else {
			sequence = 0L;
		}

		lastTimestamp = timestamp;

		long nextId = ((timestamp - twepoch) << TIME_SHIFT)
			| (deviceType << DEVICE_TYPE_SHIFT)
			| (datacenterId << DATACENTER_ID_SHIFT)
			| (machineId << MACHINE_ID_SHIFT)
			| sequence + 1;

		nextId = nextId & Long.MAX_VALUE;

		return nextId;
	}

	private long tilNextMillis(final long lastTimestamp) {
		long timestamp = timeGen();
		while (timestamp <= lastTimestamp) {
			timestamp = timeGen();
		}
		return timestamp;
	}

	private long timeGen() {
		return System.currentTimeMillis() /1000;
	}

	protected static long getMaxMachineId(long datacenterId, long maxMachineId) {
		StringBuilder mpid = new StringBuilder();
		mpid.append(datacenterId);
		String name = ManagementFactory.getRuntimeMXBean().getName();
		if (!name.isEmpty()) {
			mpid.append(name.split("@")[0]);
		}
		return (mpid.toString().hashCode() & 0xffff) % (maxMachineId + 1);
	}

	protected static long getDatacenterId(long maxDatacenterId) {
		long id = 0L;
		try {
			InetAddress ip = InetAddress.getLocalHost();
			NetworkInterface network = NetworkInterface.getByInetAddress(ip);
			if (network == null) {
				id = 1L;
			} else {
				byte[] mac = network.getHardwareAddress();
				id = ((0x000000FF & (long) mac[mac.length - 1])
					| (0x0000FF00 & (((long) mac[mac.length - 2]) << 8))) >> 6;
				id = id % (maxDatacenterId + 1);
			}
		} catch (Exception e) {
			System.out.println("获取数据中心标识位异常：" + e.getMessage());
		}
		return id;
	}
}
