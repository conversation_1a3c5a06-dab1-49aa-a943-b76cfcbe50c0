
package com.xh.vdm.interManager.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.interManager.constant.CommonConstant;
import com.xh.vdm.interManager.entity.SmInterfaceManage;
import com.xh.vdm.interManager.service.ISmInterfaceManageService;
import com.xh.vdm.interManager.utils.CommonUtil;
import com.xh.vdm.interManager.vo.SmInterfaceManageVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.annotation.Log;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.Objects;

/**
 * 接口管理表 控制器
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@RestController
@AllArgsConstructor
@RequestMapping("/interManager/sminterfacemanage")
@Slf4j
public class SmInterfaceManageController {

	private ISmInterfaceManageService smInterfaceManageService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<SmInterfaceManage> detail(SmInterfaceManage smInterfaceManage) {
		SmInterfaceManage detail = smInterfaceManageService.getOne(Wrappers.lambdaQuery(smInterfaceManage));
		return R.data(detail);
	}

	/**
	 * 分页 接口管理表
	 */
	@GetMapping("/list")
	public R<IPage<SmInterfaceManage>> list(SmInterfaceManage smInterfaceManage, Query query) {
		LambdaQueryWrapper<SmInterfaceManage> wrapper = Wrappers.lambdaQuery(SmInterfaceManage.class);
		if(StringUtils.hasText(smInterfaceManage.getInterCode())){
			wrapper.like(SmInterfaceManage::getInterCode, smInterfaceManage.getInterCode());
		}
		if(StringUtils.hasText(smInterfaceManage.getFunctionType())){
			wrapper.eq(SmInterfaceManage::getFunctionType, smInterfaceManage.getFunctionType());
		}
		if(StringUtils.hasText(smInterfaceManage.getInterType())){
			wrapper.eq(SmInterfaceManage::getInterType, smInterfaceManage.getInterType());
		}
		wrapper.orderByDesc(SmInterfaceManage::getCreateDate);
		IPage<SmInterfaceManage> page = Condition.getPage(query);
		IPage<SmInterfaceManage> pages = smInterfaceManageService.page(page, wrapper);
		return R.data(pages);
	}

	/**
	 * 自定义分页 接口管理表
	 */
	@GetMapping("/page")
	public R<IPage<SmInterfaceManageVO>> page(SmInterfaceManageVO smInterfaceManage, Page<SmInterfaceManageVO> page) {
		IPage<SmInterfaceManageVO> pages = smInterfaceManageService.selectSmInterfaceManagePage(page, smInterfaceManage);
		return R.data(pages);
	}

	/**
	 * 新增 接口管理表
	 */
	@Log(menu = "第三方平台接口管理", operation = Operation.INSERT, objectType = ObjectType.THIRD_PARTY_INTERFACE)
	@PostMapping("/save")
	public R save(@RequestBody SmInterfaceManage smInterfaceManage, BladeUser user) {
		try {
			boolean flag = smInterfaceManageService.save(smInterfaceManage);
			if (flag) {
				return R.data(ResultCode.SUCCESS.getCode(), smInterfaceManage.getId().toString(),"新增成功");
			}
			return R.fail("保存第三方接口信息失败");
		} catch (Exception e) {
			log.info("保存第三方接口信息失败",e);
			return R.fail("保存第三方接口信息失败");
		}
	}

	/**
	 * 修改 接口管理表
	 */
	@Log(menu = "第三方平台接口管理", operation = Operation.UPDATE, objectType = ObjectType.THIRD_PARTY_INTERFACE)
	@PostMapping("/update")
	public R update(@RequestBody SmInterfaceManage smInterfaceManage, BladeUser user) {
		SmInterfaceManage interfaceManage = smInterfaceManageService.getBaseMapper().selectById(smInterfaceManage.getId());
		boolean result = smInterfaceManageService.updateById(smInterfaceManage);
		if (result) {
			String compare = new CompareUtils<SmInterfaceManage>().compare(interfaceManage, smInterfaceManage);
			return R.data(ResultCode.SUCCESS.getCode(), compare,"编辑成功");
		} else {
			return R.fail("修改第三方接口信息失败");
		}
	}

	/**
	 * 新增或修改 接口管理表
	 */
	@PostMapping("/submit")
	public R submit(@RequestBody SmInterfaceManage smInterfaceManage, BladeUser user) {
		boolean operation = true;
		SmInterfaceManage interfaceManage = new SmInterfaceManage();
		if (StringUtils.isEmpty(smInterfaceManage.getId())) {
			//如果是新增
			smInterfaceManage.setState(CommonConstant.STATE_U);
			smInterfaceManage.setCreateDate(new Date());
			operation = true;
		} else {
			interfaceManage = smInterfaceManageService.getBaseMapper().selectById(smInterfaceManage.getId());
			smInterfaceManage.setUpdateDate(new Date());
			operation = false;
		}
		boolean result = smInterfaceManageService.saveOrUpdate(smInterfaceManage);
		if (result){
			if (operation){
				//return R.success(ResultCode.SUCCESS, smInterfaceManage.getId().toString());
				return R.data(ResultCode.SUCCESS.getCode(),smInterfaceManage.getId().toString(),"");
			}else {
				String compare = new CompareUtils<SmInterfaceManage>().compare(interfaceManage, smInterfaceManage);
				return R.data(ResultCode.SUCCESS.getCode(),compare,"");
			}
		}else {
			return R.fail(ResultCode.FAILURE, "");
		}
	}


	/**
	 * 删除 接口管理表
	 */
	@Log(menu = "第三方平台接口管理", operation = Operation.DELETE, objectType = ObjectType.THIRD_PARTY_INTERFACE)
	@PostMapping("/remove")
	public R remove(@RequestParam String ids, BladeUser user) {
		boolean result = smInterfaceManageService.removeByIds(CommonUtil.toLongList(ids));
		if (result) {
			return R.data(ids);
		} else {
			return R.fail("删除第三方接口信息失败");
		}
	}


}
