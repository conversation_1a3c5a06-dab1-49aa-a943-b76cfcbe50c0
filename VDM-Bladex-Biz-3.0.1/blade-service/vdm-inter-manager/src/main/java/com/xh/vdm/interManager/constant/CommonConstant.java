package com.xh.vdm.interManager.constant;

import org.springblade.core.launch.constant.AppConstant;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface CommonConstant {

	/**
	 * app name
	 */
	String APPLICATION_NAME = AppConstant.APPLICATION_NAME_PREFIX + "api";

	/**
	 * sword 系统名
	 */
	String SWORD_NAME = "sword";

	/**
	 * saber 系统名
	 */
	String SABER_NAME = "saber";

	/**
	 * 顶级父节点id
	 */
	Long TOP_PARENT_ID = 0L;

	/**
	 * 顶级父节点名称
	 */
	String TOP_PARENT_NAME = "顶级";

	/**
	 * 未封存状态值
	 */
	Integer NOT_SEALED_ID = 0;

	/**
	 * 默认密码
	 */
	String DEFAULT_PASSWORD = "123456";

	/**
	 * 默认密码参数值
	 */
	String DEFAULT_PARAM_PASSWORD = "account.initPassword";

	/**
	 * 默认排序字段
	 */
	String SORT_FIELD = "sort";

	/**
	 * 数据权限类型
	 */
	Integer DATA_SCOPE_CATEGORY = 1;

	/**
	 * 接口权限类型
	 */
	Integer API_SCOPE_CATEGORY = 2;



	//**************************接口管理中的接口类型******************************
	//REST接口
	String INTER_TYPE_HTTP_URL = "HTTP_URL";
	//webservice接口
	String INTER_TYPE_WEBSERVICE_URL = "WEBSERVICE_URL";


	//***************************接口功能***************************************
	String INTER_FUNCTION_TYPE_TOKEN = "INTER_TOKEN";  //token认证
	String INTER_FUNCTION_TYPE_BUSI = "INTER_BUSI"; //业务接口


	//************************接口管理中的请求参数类型**********************************
	String PARAM_TYEP_HEADER = "1"; //请求头 header
	String PARAM_TYPE_BODY = "2";  //请求体 body

	String PARAM_TYPE_URL = "30";  //请求url
	String PARAM_TYPE_TOKEN_KEY = "3";  //发送请求token接口时，返回报文中access_token的key名称
	String PARAM_TYPE_WEBSERVICE_USERNAME = "11";
	String PARAM_TYPE_WEBSERVICE_PASSWORD = "12";
	String PARAM_TYPE_WEBSERVICE_METHOD_NAME = "14";
	String PARAM_TYPE_WEBSERVICE_MESSAGE_CLASS = "15"; //webservice服务端的报文对象类型(服务端接口命名空间+.+Message)
	String PARAM_TYPE_USE_TOKEN_KEY = "21"; //在调用接口时使用token，用于设置token的key
	String PARAM_TYPE_USE_TOKEN_PREFIX = "22"; //在调用接口时使用token，用于设置token值的前缀，如bladex中会带有前缀 bearer xxx

	//************************webservice接口 header 中的认证模板********************************************************
	String WEBSERVICE_AUTH_MODEL_DEFAULT = "<SecurityHeader><username>#username#</username><password>#password#</password></SecurityHeader>";

	//*************************接口管理中的备注***************************************
	String NOTE_INTERFACE_PARAM_HTTP_TOKEN = "HTTP接口--token配置";
	String NOTE_INTERFACE_PARAM_HEADER = "HTTP接口--header中的参数配置";
	String NOTE_INTERFACE_PARAM_BODY = "HTTP接口--body中的参数配置";

	String NOTE_INTERFACE_PARAM_URL = "HTTP接口--url中的参数配置";
	String NOTE_INTERFACE_PARAM_WEBSERVICE_FORWARD_USERNAME = "WEBSERVICE接口--认证--用户名配置";
	String NOTE_INTERFACE_PARAM_WEBSERVICE_FORWARD_PASSWORD = "WEBSERVICE接口--认证--密码配置";
	String NOTE_INTERFACE_PARAM_REQUEST_TOKEN_HEADER = "请求token--header中的参数配置";
	String NOTE_INTERFACE_PARAM_REQUEST_TOKEN_BODY = "请求token--body中的参数配置";
	String NOTE_INTERFACE_PARAM_REQUEST_TOKEN_ACCESS_TOKEN_KEY = "请求token--返回报文中access_token key的参数配置";

	String STATE_U = "U";  //生效状态
	String STATE_E = "E"; //删除状态
	String STATE_EXP = "EXP"; //过期失效状态

	//*****************************请求类型*********************************************
	String REQUEST_TYPE_GET = "GET";
	String REQUEST_TYPE_POST = "POST";



	//*******************************是否需要token******************************
	String NEED_TOKEN = "Y";
	String NOT_NEED_TOKEN = "N";


	//*******************************调用接口状态*****************************
	String SUCCESS = "SUCCESS";
	String FAIL = "FAIL";


	//*******************************token前缀***********************************
	String TOKEN_PRIFIX = "token::";

	//*******************************inter_collect重试标志前缀*******************
	String REPEAT = "repeat::";

	//******************************接口管理常用提示语*********************
	String INTER_MESSAGE_ERROR = "调用 接口 失败";









	// ********** 设备类型 *********
	int DEVICE_TYPE_RNSS = 1; //北斗定位终端
	int DEVICE_TYPE_WEARABLE = 2; //北斗穿戴式终端
	int DEVICE_TYPE_RDSS = 3; //北斗短报文终端
	int DEVICE_TYPE_MONIT = 4; //北斗监测终端
	int DEVICE_TYPE_PNT = 5; //北斗授时终端

	// ********** 目标类型 *********
	int TARGET_TYPE_VEHICLE = 1; // 车辆
	int TARGET_TYPE_WORKER = 2; // 人员
	int TARGET_TYPE_FACILITY = 3; // 基础设施
	int TARGET_TYPE_CONTAINER = 4; // 集装箱
	int TARGET_TYPE_TEMPORARY = 5; // 外包人员
	int TARGET_TYPE_VISITOR = 6; // 访客
	int TARGET_TYPE_SHIP = 7; // 船舶
	int TARGET_TYPE_TRAIN = 8; // 列车货箱
	int TARGET_TYPE_PRECISION = 9; // 精密装备
	int TARGET_TYPE_TRUCK = 10; // 矿用卡车

	// ********** 业务对象类型 *********
	String OBJ_TYPE_TARGET = "1";
	String OBJ_TYPE_DEVICE = "2";
	String OBJ_TYPE_PARAM_TARGET = "target";
	String OBJ_TYPE_PARAM_DEVICE = "device";

	// ********** 平台业务对象绑定状态 *********
	byte PLATFORM_OBJ_BIND_NO = 0; // 未绑定
	byte PLATFORM_OBJ_BIND_YES = 1; // 已绑定

	//最新位置点（仅用作go开发优化，不可用做业务，会缺少定位点，会从一批定位点中取最新的一个写入到redis）
	String RealLoc="Real:Loc";

	//最新位置点（可用作业务，不会缺少点）
	String CACHE_HASH_LOCATION_LATEST = "location:latest";
	/**
	 *数据字典表，后面加字典类型
	 */
	String DICTIONARY_HASH_KEY = "DICTIONARY-TYPE:";

	String DICT_TYPE_COLOR = "licence_color";  //车牌颜色


}
