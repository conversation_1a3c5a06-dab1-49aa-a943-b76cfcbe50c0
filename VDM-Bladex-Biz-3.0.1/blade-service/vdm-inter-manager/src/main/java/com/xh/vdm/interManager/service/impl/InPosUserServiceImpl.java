/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service.impl;

import com.xh.vdm.interManager.entity.InPosUser;
import com.xh.vdm.interManager.vo.InPosUserVO;
import com.xh.vdm.interManager.mapper.InPosUserMapper;
import com.xh.vdm.interManager.service.IInPosUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.vo.request.InterfaceAuthRequest;
import com.xh.vdm.interManager.vo.response.InterfaceResponse;
import org.springblade.common.utils.StringUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
@Service
public class InPosUserServiceImpl extends ServiceImpl<InPosUserMapper, InPosUser> implements IInPosUserService {

	@Override
	public IPage<InPosUserVO> selectInPosUserPage(IPage<InPosUserVO> page, InPosUserVO inPosUser) {
		return page.setRecords(baseMapper.selectInPosUserPage(page, inPosUser));
	}

	@Override
	public List<InterfaceResponse> findInterfaceAuth(InterfaceAuthRequest request) {
		if(!StringUtils.isEmpty(request.getCode())){
			request.setCode("%" + request.getCode() + "%");
		}
		if(!StringUtils.isEmpty(request.getName())){
			request.setName("%" + request.getName() + "%");
		}
		return baseMapper.getInterfaceAuth(request);
	}
}
