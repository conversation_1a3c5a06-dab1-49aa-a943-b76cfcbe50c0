/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.interManager.entity.*;
import com.xh.vdm.interManager.service.IBladeDeptService;
import com.xh.vdm.interManager.service.IInPosUserInterfaceService;
import com.xh.vdm.interManager.service.ISmInterfaceSystemService;
import com.xh.vdm.interManager.vo.DeptVO;
import com.xh.vdm.interManager.vo.InPosUserInterfaceVO;
import com.xh.vdm.interManager.vo.SystemVO;
import com.xh.vdm.interManager.vo.response.UserResponse;



import lombok.AllArgsConstructor;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.annotation.Log;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.DigestUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.user.entity.User;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.vo.InPosUserVO;
import com.xh.vdm.interManager.service.IInPosUserService;

import java.util.*;

/**
 * 账号管理
 * 位置平台给外部平台开通账号
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/user")
@Slf4j
public class InPosUserController {

	private IInPosUserService inPosUserService;

	private IInPosUserInterfaceService userInterfaceService;

	private IBladeDeptService deptService;

	private ISmInterfaceSystemService systemService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<UserResponse> detail(InPosUser inPosUser) {
		inPosUser.setIsDel(0);
		InPosUser detail = inPosUserService.getOne(Condition.getQueryWrapper(inPosUser));
		UserResponse res = new UserResponse();
		BeanUtils.copyProperties(detail, res);
		return R.data(res);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	public R<IPage<InPosUserVO>> list(InPosUser inPosUser, Query query) {
		inPosUser.setIsDel(0);
		String account = inPosUser.getAccount();
		inPosUser.setAccount(null);
		QueryWrapper wrapper = Condition.getQueryWrapper(inPosUser);
		if (StringUtils.hasText(account)) {
			wrapper.like("account", "%" + account + "%");
		}
		wrapper.orderByDesc("create_time");
		IPage<InPosUser> pages = inPosUserService.page(Condition.getPage(query), wrapper);
		//todo 最好对所属组织进行清理：dept_id中存的是多个组织，中间使用逗号分隔。有些组织已经不存在了，就没有必要再返回。目前是在前端名值转换的时候做的判断。
		//对deptId进行处理，获取deptName，无法获取deptName的，表示已经删除了，就不再向前台返回；system也采用同样的逻辑
		StringBuffer deptIdsStr = new StringBuffer();
		StringBuffer systemIdsStr = new StringBuffer();
		List<BladeDept> deptList = new ArrayList<>();
		List<SmInterfaceSystem> systemList = new ArrayList<>();
		Map<String,BladeDept> map = new HashMap<>();
		Map<String, SmInterfaceSystem> systemMap = new HashMap<>();

		List<InPosUserVO> voList = new ArrayList<>();
		IPage<InPosUserVO> userPage = new Page<>();
		if(pages != null && pages.getRecords() != null && pages.getRecords().size() > 0){
			pages.getRecords().forEach(item -> {
				if(StringUtils.hasText(item.getDeptId())){
					deptIdsStr.append(item.getDeptId()).append(",");
				}
				if(StringUtils.hasText(item.getSystemId()+"")){
					systemIdsStr.append(item.getSystemId()+"").append(",");
				}
			});
			//查询存在的dept信息
			String paramStr = "";
			if(deptIdsStr.length() > 0){
				paramStr = deptIdsStr.substring(0, deptIdsStr.length() - 1);
			}
			deptList = deptService.findExistDept(paramStr);
			for(BladeDept d : deptList){
				map.put(d.getId()+"", d);
			}
			//查询存在的system信息
			String systemParamStr = "";
			if(systemIdsStr.length() > 0){
				systemParamStr = systemIdsStr.substring(0, systemIdsStr.length() - 1);
			}
			systemList = systemService.findExistSystem(systemParamStr);
			for(SmInterfaceSystem system : systemList){
				systemMap.put(system.getId()+"", system);
			}

			//添加dept信息和system信息
			pages.getRecords().forEach(item -> {
				InPosUserVO vo = new InPosUserVO();
				BeanUtils.copyProperties(item, vo);
				//添加dept信息
				List<DeptVO> dvList = new ArrayList<>();
				if(StringUtils.hasText(item.getDeptId())){
					String[] arr = item.getDeptId().split(",");
					for(String a : arr){
						BladeDept bd = map.get(a);
						if(bd != null){
							DeptVO dv = new DeptVO();
							dv.setDeptId(a);
							dv.setDeptName(bd.getDeptName());
							dvList.add(dv);
						}
					}
				}
				//添加system信息
				SystemVO systemVO = new SystemVO();
				if(StringUtils.hasText(item.getSystemId()+"")){
					SmInterfaceSystem system = systemMap.get(item.getSystemId()+"");
					if(system != null){
						systemVO.setSystemId(item.getSystemId()+"");
						systemVO.setSystemName(system.getSystemName());
					}
				}
				vo.setDeptList(dvList);
				vo.setSystemInfo(systemVO);
				//不显示密码
				vo.setPassword("");
				voList.add(vo);
			});
			userPage.setPages(pages.getPages());
			userPage.setTotal(pages.getTotal());
			userPage.setSize(pages.getSize());
			userPage.setCurrent(pages.getCurrent());
			userPage.setRecords(voList);
		}
		return R.data(userPage);
	}

	/**
	 * 新增
	 */
	@Log(menu = "平台账号管理", operation = Operation.INSERT, objectType = ObjectType.PLATFORM_ACCOUNT)
	@PostMapping("/save")
	public R save(@Valid @RequestBody InPosUser inPosUser, BladeUser user) {

		//判断数据库中是否已经存在该account
		List<InPosUser> users = inPosUserService.list(Wrappers.lambdaQuery(InPosUser.class)
			.eq(InPosUser::getAccount, inPosUser.getAccount())
			.eq(InPosUser::getIsDel, 0));
		if (users.size() > 0) {
			//如果系统中已经存在该account
			log.error("账户[{}]已经存在", inPosUser.getAccount());
			return R.fail("保存账户信息失败，账户" + inPosUser.getAccount() + "已经存在");
		}

		inPosUser.setCreateUser(AuthUtil.getUserId());
		inPosUser.setCreateTime(new Date());
		inPosUser.setIsDel(0);
		inPosUser.setStatus(CommonConstant.STATE_U);
		//设置密码
		String password = inPosUser.getPassword();
		if (StringUtils.isEmpty(password)) {
			return R.fail("密码不能为空");
		}

		inPosUser.setPassword(DigestUtil.hex(password));
		try {
			boolean flag = inPosUserService.save(inPosUser);
			if (flag) {
				return R.data(ResultCode.SUCCESS.getCode(), inPosUser.getId().toString(),"新增成功");
			}
			return R.fail("保存账户信息失败");
		} catch (Exception e) {
			log.error("保存账户信息失败", e);
			return R.fail("保存账户信息失败，" + e.getMessage());
		}
	}

	/**
	 * 修改
	 */
	@Log(menu = "平台账号管理", operation = Operation.UPDATE, objectType = ObjectType.PLATFORM_ACCOUNT)
	@PostMapping("/update")
	public R update(@Valid @RequestBody InPosUser inPosUser, BladeUser user) {
		InPosUser posUser = inPosUserService.getById(inPosUser.getId());

		inPosUser.setUpdateUser(AuthUtil.getUserId());
		inPosUser.setUpdateTime(new Date());
		if (StringUtils.hasText(inPosUser.getPassword())) {
			inPosUser.setPassword(DigestUtil.hex(inPosUser.getPassword()));
		}else{
			inPosUser.setPassword(null);
		}
		boolean result = inPosUserService.updateById(inPosUser);
		if (result) {
			String compare = new CompareUtils<InPosUser>().compare(posUser, inPosUser);
			return R.data(ResultCode.SUCCESS.getCode(), compare,"编辑成功");
		} else {
			return R.fail("修改账户信息失败");
		}
	}


	/**
	 * 删除
	 * 逻辑删除
	 */
	@Log(menu = "平台账号管理", operation = Operation.DELETE, objectType = ObjectType.PLATFORM_ACCOUNT)
	@GetMapping("/remove")
	public R remove(@RequestParam String ids, BladeUser user) {
		List<InPosUser> list = inPosUserService.listByIds(Func.toLongList(ids));
		list.forEach(item -> {
			item.setUpdateTime(new Date());
			item.setUpdateUser(AuthUtil.getUserId());
			item.setIsDel(1);
		});
		boolean result = inPosUserService.updateBatchById(list);
		if (result) {
			return R.data(ids);
		} else {
			return R.fail("删除账户信息失败");
		}
	}


	/**
	 * 重置密码
	 *
	 * @param userIds
	 * @return
	 */
	@GetMapping("/resetPassword")
	public R resetPassword(String userIds, String password) {
		InPosUser user = new InPosUser();
		user.setPassword(DigestUtil.sha1Hex(password));
		user.setUpdateTime(DateUtil.now());
		user.setUpdateUser(AuthUtil.getUserId());
		return R.status(inPosUserService.update(user, Wrappers.<InPosUser>update().lambda().in(InPosUser::getId, Func.toLongList(userIds))));
	}

	/**
	 * 冻结账号
	 *
	 * @param userIds
	 * @return
	 */
	@GetMapping("/freeze")
	public R freeze(String userIds) {
		InPosUser user = new InPosUser();
		user.setStatus(CommonConstant.STATE_E);
		user.setUpdateTime(DateUtil.now());
		user.setUpdateUser(AuthUtil.getUserId());
		return R.status(inPosUserService.update(user, Wrappers.<InPosUser>update().lambda().in(InPosUser::getId, Func.toLongList(userIds))));
	}


	/**
	 * 解冻账号
	 * @param userIds
	 * @return
	 */
	@GetMapping("/unfreeze")
	public R unfreeze(String userIds) {
		InPosUser user = new InPosUser();
		user.setStatus(CommonConstant.STATE_U);
		user.setUpdateTime(DateUtil.now());
		user.setUpdateUser(AuthUtil.getUserId());
		return R.status(inPosUserService.update(user, Wrappers.<InPosUser>update().lambda().in(InPosUser::getId, Func.toLongList(userIds))));
	}

	/**
	 * 接口授权
	 *
	 * @param interfaces
	 * @param userIds
	 * @return
	 */
	@GetMapping("/interAuth")
	public R interAuth(String interfaces, String userIds) {
		if (!StringUtils.hasText(userIds)) {
			log.error("userIds 为空");
			return R.fail("用户不能为空");
		}
		if (!StringUtils.hasText(interfaces)) {
			log.error("interfaces 为空");
			return R.fail("未选择接口");
		}
		try {
			//1.删除之前的关联关系
			userInterfaceService.remove(Wrappers.lambdaQuery(InPosUserInterface.class).in(InPosUserInterface::getUserId, Func.toLongList(userIds)));
			//2.创建新的关联关系
			List<Long> interList = Func.toLongList(interfaces);
			List<Long> userList = Func.toLongList(userIds);
			if (interList == null || userList == null || interList.size() < 1 || userList.size() < 1) {
				return R.fail("未包含用户数据或接口数据");
			}
			List<InPosUserInterface> list = new ArrayList<>();
			Date createTime = new Date();
			for (Long userId : userList) {
				for (Long interId : interList) {
					InPosUserInterface pi = new InPosUserInterfaceVO();
					pi.setUserId(userId);
					pi.setInterfaceId(interId);
					pi.setCreateTime(createTime);
					pi.setCreateUser(AuthUtil.getUserId());
					list.add(pi);
				}
			}
			userInterfaceService.saveBatch(list);
			return R.success("操作成功");
		} catch (Exception e) {
			log.error("授权失败", e);
			return R.fail("授权失败, " + e.getMessage());
		}
	}

	/**
	 * 绑定部门
	 * 用于设置数据权限
	 *
	 * @param userIds
	 * @param deptIds
	 * @return
	 */
	@GetMapping("/bindDept")
	public R bindDept(String userIds, String deptIds) {
		if (!StringUtils.hasText(userIds)) {
			log.error("userIds 为空");
			return R.fail("用户不能为空");
		}
		if (!StringUtils.hasText(deptIds)) {
			log.error("deptIds 为空");
			return R.fail("部门不能为空");
		}
		//直接更新用户表中的 deptId 字段
		InPosUser user = new InPosUser();
		user.setDeptId(deptIds);
		try {
			return R.status(inPosUserService.update(user, Wrappers.lambdaUpdate(InPosUser.class).in(InPosUser::getId, Func.toLongList(userIds))));
		} catch (Exception e) {
			log.error("绑定部门失败", e);
			return R.fail("绑定失败, " + e.getMessage());
		}
	}

	/**
	 * 绑定报警规则
	 *
	 * @param userIds
	 * @param ruleIds
	 * @return
	 */
	@GetMapping("/bindAlarmRule")
	public R bindAlarmRule(String userIds, String ruleIds) {
		if (!StringUtils.hasText(userIds)) {
			log.error("userIds 为空");
			return R.fail("用户不能为空");
		}
		//允许为空，比如已经选择了告警规则的，后来不想要告警规则了，要清空，这时提交到后台的就是空
		//如果报警规则为空，则认为是要清空告警规则
		//直接更新用户表中的 alarm_rule_id 字段
		InPosUser user = new InPosUser();
		user.setAlarmRuleId(ruleIds);
		try {
			return R.status(inPosUserService.update(user, Wrappers.lambdaUpdate(InPosUser.class).in(InPosUser::getId, Func.toLongList(userIds))));
		} catch (Exception e) {
			log.error("绑定告警规则失败", e);
			return R.fail("绑定失败, " + e.getMessage());
		}
	}

}
