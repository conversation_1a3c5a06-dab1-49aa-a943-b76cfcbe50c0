/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service.terminal.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.biapi.entity.BdmWearableDevice;
import com.xh.vdm.interManager.mapper.terminal.WearableDeviceMapper;
import com.xh.vdm.interManager.service.terminal.IWearableDeviceService;
import org.springblade.common.constant.CommonConstant;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Service
public class WearableDeviceServiceImpl extends ServiceImpl<WearableDeviceMapper, BdmWearableDevice> implements IWearableDeviceService {

	@Resource
	private RedisTemplate redisTemplate;

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateDept(Long id, Integer targetType, Long deptId) {
		List<BdmWearableDevice> wearableDevices = baseMapper.selectList(
			new QueryWrapper<BdmWearableDevice>()
				.eq("target_id", id)
				.eq("target_type", targetType)
				.eq("deleted", 0)
				.select("id,unique_id,device_type,dept_id,category,device_num,iot_protocol")
		);
		if (!wearableDevices.isEmpty()) {
			baseMapper.updateDept(id, targetType, deptId);

			Map<String, String> map = new HashMap<>();
			for (BdmWearableDevice wearableDevice : wearableDevices) {
				String key = wearableDevice.getDeviceType() + "-" + wearableDevice.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("uniqueId", wearableDevice.getUniqueId());
				innerMap.put("deviceNum", wearableDevice.getDeviceNum());
				innerMap.put("category", wearableDevice.getCategory());
				innerMap.put("deviceType", wearableDevice.getDeviceType());
				innerMap.put("deptId", deptId);
				innerMap.put("iotProtocol", wearableDevice.getIotProtocol());
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
			if (!map.isEmpty()) {
				redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_DEVICE, map);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteByTargetIds(Long[] ids, Integer targetType) {
		baseMapper.deleteByTargetIds(ids, targetType);
	}
}
