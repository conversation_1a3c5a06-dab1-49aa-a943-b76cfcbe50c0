package com.xh.vdm.interManager.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import com.xh.vdm.interManager.constant.CommonConstant;
import com.xh.vdm.interManager.dto.Track;
import com.xh.vdm.interManager.entity.BladeDictBiz;
import com.xh.vdm.interManager.entity.LocationKudu;
import com.xh.vdm.interManager.service.IBdmAbstractDeviceService;
import com.xh.vdm.interManager.service.IBdmAbstractTargetService;
import com.xh.vdm.interManager.service.IBladeDictBizService;
import com.xh.vdm.interManager.service.ILocationKuduService;
import com.xh.vdm.interManager.vo.request.LatestPositionRequest;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.StringUtils;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/location")
@Slf4j
public class LocationController {

	@Autowired
	private StringRedisTemplate redisTemplate;

	@Resource
	private IBdmAbstractTargetService targetService;

	@Resource
	private IBdmAbstractDeviceService deviceService;

	@Resource
	private IBladeDictBizService dictService;

	@Resource
	private ILocationKuduService locationKuduService;

	@PostMapping("/latestPosition")
	public R<Track> latestPosition(@RequestBody LatestPositionRequest request){
		try{
			log.info("查询最新定位，参数为：{}", JSON.toJSONString(request));
			//1.参数判断
			if(request == null){
				return R.fail("查询条件不能为空");
			}
			if(StringUtils.isEmpty(request.getLicencePlate())){
				return R.fail("车牌号不能为空");
			}
			if(StringUtils.isEmpty(request.getPlateColor())){
				return R.fail("车牌颜色不能为空");
			}
			Map<Object, Object> map = redisTemplate.opsForHash().entries(CommonConstant.DICTIONARY_HASH_KEY + "-" + CommonConstant.DICT_TYPE_COLOR);
			if(map == null){
				map = new HashMap<>();
			}
			//2.根据车牌号+车牌颜色查询终端信息
			if(map.size() < 1){
				//如果缓存没有加载，则重新加载缓存
				//1.查询车辆颜色
				List<BladeDictBiz> colorList = dictService.list(Wrappers.lambdaQuery(BladeDictBiz.class).eq(BladeDictBiz::getIsDeleted, 0).eq(BladeDictBiz::getIsSealed, 0).eq(BladeDictBiz::getCode, CommonConstant.DICT_TYPE_COLOR));
				Map<Object, Object> finalMap = map;
				colorList.forEach(item -> {
					finalMap.put(item.getDictKey(), item.getDictValue());

					//2.加载到缓存
					redisTemplate.opsForHash().put(CommonConstant.DICTIONARY_HASH_KEY + "-" + CommonConstant.DICT_TYPE_COLOR , item.getDictKey() , item.getDictValue());
				});
			}
			Object colorObject = map.get(request.getPlateColor());
			String colorName = colorObject==null?"":colorObject.toString();
			String number = request.getLicencePlate() + "-" + colorName;
			List<BdmAbstractTarget> targetList = targetService.list(Wrappers.lambdaQuery(BdmAbstractTarget.class)
				.eq(BdmAbstractTarget::getDeleted, 0)
				.eq(BdmAbstractTarget::getNumber, number));
			if(targetList == null || targetList.size() < 1){
				log.error("未查询到该车辆："+number);
				return R.fail("未查询到该车辆");
			}
			BdmAbstractTarget target = new BdmAbstractTarget();
			if(targetList.size() > 1){
				log.info("根据条件查询到多辆车：" + number);
				target = targetList.get(0);
			}else{
				target = targetList.get(0);
			}
			//根据targetId查询deviceId
			List<BdmAbstractDevice> deviceList = deviceService.list(Wrappers.lambdaQuery(BdmAbstractDevice.class)
				.eq(BdmAbstractDevice::getTargetId, target.getId()));
			if(deviceList == null || deviceList.size() < 0){
				log.error("未查询到终端");
				return R.fail("未查询到终端");
			}
			BdmAbstractDevice device = new BdmAbstractDevice();
			if(deviceList.size() > 1){
				log.info("根据条件查询到多个终端" + number);
				device = deviceList.get(0);
			}else{
				device = deviceList.get(0);
			}
			Long deviceId = device.getId();

			//3.查询redis获取最新位置点
			Object obj =redisTemplate.opsForHash().get(CommonConstant.CACHE_HASH_LOCATION_LATEST,deviceId + "");
			if (obj != null){
				Track track = JSONObject.parseObject(obj.toString(), Track.class);
				log.info("从redis中获取最新轨迹数据为：{}", JSON.toJSONString(track));
				return R.data(track);
			}

		}catch (Exception e){
			log.error("查询最新定位失败",e);
		}
		return R.data(null);
	}
}
