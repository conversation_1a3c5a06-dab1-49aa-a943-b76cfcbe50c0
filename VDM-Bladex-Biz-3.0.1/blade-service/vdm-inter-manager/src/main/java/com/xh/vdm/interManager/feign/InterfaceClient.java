package com.xh.vdm.interManager.feign;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.interManager.entity.InPosInterface;
import com.xh.vdm.interManager.service.IInPosInterfaceService;
import com.xh.vdm.interManager.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
public class InterfaceClient implements IInterfaceClient{

	@Resource
	private IInPosInterfaceService interfaceService;

	@GetMapping(GET_INTERFACE_BY_URL)
	@Override
	public R<List<InPosInterface>> getInterfaceByUrl(String url) {
		try {
			List<InPosInterface> list = interfaceService.list(Wrappers.lambdaQuery(InPosInterface.class).eq(InPosInterface::getIsDel, 0)
				.like(InPosInterface::getUrl, "%" + url));
			return R.data(list);
		}catch (Exception e){
			log.error("[feign]根据url查询接口失败",e);
			return R.fail("查询接口失败");
		}
	}
}
