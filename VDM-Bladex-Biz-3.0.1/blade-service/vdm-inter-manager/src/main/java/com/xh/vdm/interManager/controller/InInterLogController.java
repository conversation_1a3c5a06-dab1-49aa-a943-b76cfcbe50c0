/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.controller;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;

import javax.annotation.Resource;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.entity.InInterLog;
import com.xh.vdm.interManager.vo.InInterLogVO;
import com.xh.vdm.interManager.service.IInInterLogService;

import java.util.Date;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ininterlog")
@Slf4j
public class InInterLogController {

	private IInInterLogService inInterLogService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<InInterLog> detail(InInterLog inInterLog) {
		InInterLog detail = inInterLogService.getOne(Condition.getQueryWrapper(inInterLog));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	public R<IPage<InInterLog>> list(InInterLogVO inInterLog, Query query) {
		String interCode = inInterLog.getInterCode();
		inInterLog.setInterCode(null);
		QueryWrapper<InInterLog> wrapper = Condition.getQueryWrapper(inInterLog);
		if(inInterLog.getStartTime() != null && inInterLog.getStartTime() > 0){
			Date startDate = new Date();
			startDate.setTime(inInterLog.getStartTime()*1000);
			wrapper.gt("call_time", startDate);
		}
		if(inInterLog.getEndTime() != null && inInterLog.getEndTime() > 0){
			Date endDate = new Date();
			endDate.setTime(inInterLog.getEndTime()*1000);
			wrapper.lt("call_time", endDate);
		}
		if(!StringUtils.isEmpty(interCode)){
			//模糊查询
			wrapper.like("inter_code","%"+interCode+"%");
		}
		IPage<InInterLog> pages = inInterLogService.page(Condition.getPage(query), wrapper);
		return R.data(pages);
	}

}
