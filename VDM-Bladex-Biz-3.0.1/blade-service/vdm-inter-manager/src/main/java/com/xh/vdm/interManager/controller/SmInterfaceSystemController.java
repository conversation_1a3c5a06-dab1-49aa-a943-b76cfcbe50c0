package com.xh.vdm.interManager.controller;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.SM2;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.interManager.constant.CommonConstant;
import com.xh.vdm.interManager.core.InterManager;
import com.xh.vdm.interManager.dto.message.Header;
import com.xh.vdm.interManager.dto.message.KeyUpload;
import com.xh.vdm.interManager.dto.message.Message;
import com.xh.vdm.interManager.entity.*;
import com.xh.vdm.interManager.service.*;
import com.xh.vdm.interManager.utils.BeanUtil;
import com.xh.vdm.interManager.utils.MessageUtils;
import com.xh.vdm.interManager.vo.DeptVO;
import com.xh.vdm.interManager.vo.SmInterfaceSystemVO;
import com.xh.vdm.interManager.vo.request.PlatformObjRequest;
import com.xh.vdm.interManager.vo.request.group.PlatformObjBindGroup;
import com.xh.vdm.interManager.vo.request.group.PlatformObjListGroup;
import com.xh.vdm.interManager.vo.request.group.PlatformObjMapGroup;
import com.xh.vdm.interManager.vo.response.InterfaceSystemResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.annotation.Log;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 平台管理 控制器
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@RestController
@RequestMapping("/interManager/sminterfacesystem")
@Slf4j
public class SmInterfaceSystemController {

	@Resource
	private ISmInterfaceSystemService smInterfaceSystemService;

	@Resource
	private IInPosUserService posUserService;

	@Resource
	private IInPosSystemObjectService inPosSystemObjectService;

	@Resource
	private ISmInterfaceSystemService systemService;

	@Resource
	private IInInterLogService logService;

	@Resource
	private MessageUtils messageUtils;

	@Resource
	private ISmInterfaceManageService interfaceManageService;

	@Resource
	private IBladeDeptService deptService;

	@Resource
	private InterManager interManager;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private IInSystemKeyHistoryService keyHistoryService;

	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(20, 50, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());


	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<SmInterfaceSystem> detail(SmInterfaceSystem smInterfaceSystem) {
		SmInterfaceSystem detail = smInterfaceSystemService.getOne(Wrappers.query(smInterfaceSystem));
		return R.data(detail);
	}

	/**
	 * 分页 接口管理系统表
	 */
	@GetMapping("/list")
	public R<IPage<InterfaceSystemResponse>> list(SmInterfaceSystem smInterfaceSystem, Query query) {
		LambdaQueryWrapper<SmInterfaceSystem> wrapper = Wrappers.lambdaQuery(SmInterfaceSystem.class).eq(SmInterfaceSystem::getState, CommonConstant.STATE_U);
		if(!StringUtils.isEmpty(smInterfaceSystem.getSystemName())){
			//模糊查询
			wrapper.like(SmInterfaceSystem::getSystemName, smInterfaceSystem.getSystemName());
		}
		wrapper.orderByDesc(SmInterfaceSystem::getId);
		IPage<SmInterfaceSystem> page = Condition.getPage(query);
		IPage<SmInterfaceSystem> pages = smInterfaceSystemService.page(page, wrapper);
		StringBuffer deptIdsStr = new StringBuffer();
		List<BladeDept> deptList = new ArrayList<>();
		Map<String,BladeDept> map = new HashMap<>();
		List<InterfaceSystemResponse> resList = new ArrayList<>();
		if(pages != null && pages.getRecords() != null && pages.getRecords().size() > 0){
			pages.getRecords().forEach(item -> {
				if(org.springframework.util.StringUtils.hasText(item.getDeptId())){
					deptIdsStr.append(item.getDeptId()).append(",");
				}
			});
			//查询存在的dept信息
			String paramStr = "";
			if(deptIdsStr.length() > 0){
				paramStr = deptIdsStr.substring(0, deptIdsStr.length() - 1);
			}
			deptList = deptService.findExistDept(paramStr);
			for(BladeDept d : deptList){
				map.put(d.getId()+"", d);
			}
			pages.getRecords().forEach(item -> {
				InterfaceSystemResponse r = new InterfaceSystemResponse();
				BeanUtils.copyProperties(item, r);
				if(!StringUtils.isEmpty(item.getDeptId())){
					List<DeptVO> voList = new ArrayList<>();
					for(String id : item.getDeptId().split(",")){
						BladeDept dept = map.get(id);
						if(dept != null){
							DeptVO vo = new DeptVO();
							vo.setDeptId(id);
							vo.setDeptName(dept.getDeptName());
							voList.add(vo);
						}
					}
					r.setDeptList(voList);
				}
				resList.add(r);
			});
		}
		IPage<InterfaceSystemResponse> resPage = new Page<>();
		resPage.setTotal(pages.getTotal());
		resPage.setCurrent(pages.getCurrent());
		resPage.setSize(pages.getSize());
		resPage.setRecords(resList);
		return R.data(resPage);
	}


	/**
	 * 查询系统列表
	 */
	@GetMapping("/list-system")
	public R<List<SmInterfaceSystem>> list() {
		List<SmInterfaceSystem> list = smInterfaceSystemService.list(Wrappers.<SmInterfaceSystem>query().lambda().eq(SmInterfaceSystem::getState, CommonConstant.STATE_U));
		return R.data(list);
	}


	/**
	 * 自定义分页 接口管理系统表
	 */
	@GetMapping("/page")
	public R<IPage<SmInterfaceSystemVO>> page(SmInterfaceSystemVO smInterfaceSystem, Page<SmInterfaceSystemVO> page) {
		IPage<SmInterfaceSystemVO> pages = smInterfaceSystemService.selectSmInterfaceSystemPage(page, smInterfaceSystem);
		return R.data(pages);
	}

	/**
	 * 新增 接口管理系统表
	 */
	@Log(menu = "平台管理", operation = Operation.INSERT, objectType = ObjectType.PLATFORM_MANAGEMENT)
	@PostMapping("/save")
	public R save(@RequestBody SmInterfaceSystem smInterfaceSystem, BladeUser user) {
		boolean result = smInterfaceSystemService.save(smInterfaceSystem);
		if (result) {
			Long id = smInterfaceSystem.getId();
			return R.data(ResultCode.SUCCESS.getCode(), id,"新增成功");
		} else {
			return R.fail(ResultCode.FAILURE, "");
		}
	}

	/**
	 * 修改 接口管理系统表
	 */
	@Log(menu = "平台管理", operation = Operation.UPDATE, objectType = ObjectType.PLATFORM_MANAGEMENT)
	@PostMapping("/update")
	public R update(@RequestBody SmInterfaceSystem smInterfaceSystem, BladeUser user) {
		SmInterfaceSystem interfaceSystem = smInterfaceSystemService.getById(smInterfaceSystem.getId());

		boolean result = smInterfaceSystemService.updateById(smInterfaceSystem);
		if (result) {
			String compare = new CompareUtils<SmInterfaceSystem>().compare(interfaceSystem, smInterfaceSystem);
			return R.data(ResultCode.SUCCESS.getCode(), compare,"编辑成功");
		} else {
			return R.fail("");
		}
	}

	/**
	 * 新增或修改 接口管理系统表
	 */
	@PostMapping("/submit")
	public R submit(@RequestBody SmInterfaceSystem smInterfaceSystem) {
		return R.status(smInterfaceSystemService.saveOrUpdate(smInterfaceSystem));
	}


	/**
	 * 删除 平台表
	 */
	@Log(menu = "平台管理", operation = Operation.DELETE, objectType = ObjectType.PLATFORM_MANAGEMENT)
	@PostMapping("/remove")
	public R remove(@RequestParam String ids, BladeUser user) {
		List<Long> idList = Func.toLongList(ids);
		List<String> idStrList = new ArrayList<>();
		idList.forEach(id -> {
			idStrList.add(id + "");
		});
		//1.删除之前要先进行判断，是否有接口和账号关联了改平台
		//1.1 关联的平台账号
		long userCount = posUserService.count(Wrappers.lambdaQuery(InPosUser.class).in(InPosUser::getSystemId, idList).eq(InPosUser::getIsDel, 0));
		if (userCount > 0) {
			return R.fail("有平台账号关联要删除的平台信息，请删除相关账号后再试");
		}
		//1.2 关联的第三方平台接口
		long interCount = interfaceManageService.count(Wrappers.lambdaQuery(SmInterfaceManage.class)
			.in(SmInterfaceManage::getSystemType, idStrList).eq(SmInterfaceManage::getState, "U"));
		if (interCount > 0) {
			return R.fail("有第三方平台接口关联要删除的平台信息，请删除相关第三方平台接口后再试");
		}
		//2.逻辑删除
		try {
			smInterfaceSystemService.update(Wrappers.lambdaUpdate(SmInterfaceSystem.class).in(SmInterfaceSystem::getId, idList).set(SmInterfaceSystem::getState, CommonConstant.STATE_E));
		} catch (Exception e) {
			log.error("删除系统数据出错", e);
			return R.fail("删除系统数据出错");
		}
		return R.data(ids);
	}

	/**
	 * 重置位置平台密钥
	 * @return
	 */
	@GetMapping("/resetPosKey")
	public R<String> resetPosKey(){

		try{
			//1.生成新的公私钥
			SM2 sm2 = SmUtil.sm2().initKeys();
			String publicKey = sm2.getPublicKeyBase64();
			log.info(publicKey);
			String privateKey = sm2.getPrivateKeyBase64();
			log.info("位置平台新生成了公私钥，公钥为：" + publicKey);

			//2.记录原公私钥
			SmInterfaceSystem system = smInterfaceSystemService.getById(0);
			String oldPublicKey = system.getPublicKey();
			InSystemKeyHistory history = new InSystemKeyHistory();
			history.setSystemId(system.getId());
			history.setPrivateKey(system.getPrivateKey());
			history.setPublicKey(system.getPublicKey());
			history.setCreateUser(AuthUtil.getUserId());
			history.setCreateTime(new Date());
			keyHistoryService.save(history);

			//3.存储公私钥
			system.setPrivateKey(privateKey);
			system.setPublicKey(publicKey);
			system.setUpdateDate(new Date());
			smInterfaceSystemService.updateById(system);

			//3.异步向所有外部平台推送公钥
			threadPool.submit(() -> {
				try {
					sendPublicKey(oldPublicKey, publicKey);
				}catch (Exception e){
					log.error("推送公钥整体任务失败 ",e);
				}
			});

		}catch (Exception e){
			log.error("位置平台重新生成公私钥失败",e);
			return R.fail("重新生成公私钥失败");
		}
		return R.success("操作成功");
	}

	private void sendPublicKey(String oldPublicKey, String publicKey ) throws Exception{
		//1.查找所有平台
		List<SmInterfaceSystem> systems = systemService.list(Wrappers.lambdaQuery(SmInterfaceSystem.class)
			.eq(SmInterfaceSystem::getState, CommonConstant.STATE_U));

		//3.调用接口推送数据
		//组装报文
		KeyUpload key = new KeyUpload();
		key.setOldPublicKey(oldPublicKey);
		key.setPublicKey(publicKey);
		key.setOperateTime(DateUtil.sdfHolderNoLine.get().format(new Date()));
		key.setUserName(null);
		Header header = new Header();
		header.setSystemId(org.springblade.common.constant.CommonConstant.POS_SYSTEM_ID);
		header.setServiceId(org.springblade.common.constant.CommonConstant.BUSI_CODE_KEY_UPLOAD);
		List<InInterLog> logs = new ArrayList<>();
		for(SmInterfaceSystem s : systems){
			String code = s.getPlatHttpSendCode();
			header.setSerialNum((new Date()).getTime()+"");
			header.setReqTime(DateUtil.sdfHolder.get().format(new Date()).replace("-","").replace(" ",""));
			Message message = messageUtils.encryptData(header, JSON.toJSONString(key),s.getId()+"");
			Map<String,Object> map = BeanUtil.beanToMap(message);
			InInterLog interLog = new InInterLog();
			if(!org.springblade.common.utils.StringUtils.isEmpty(code)){
				long start = System.currentTimeMillis();
				com.xh.vdm.interManager.utils.R<Object> res = null;
				try {
					res = interManager.call(code, map);
				}catch (Exception e){
					log.error("推送公钥失败 ",e);
				}
				long end = System.currentTimeMillis();
				//查询接口基本信息
				String interJSONStr = stringRedisTemplate.opsForValue().get(org.springblade.common.constant.CommonConstant.CACHE_POS_INTER_PREFIX + code);
				SmInterfaceManage inter = null;
				if(org.springblade.common.utils.StringUtils.isEmpty(interJSONStr)){
					inter = interfaceManageService.findInterfaceManageByInterCode(code);
					//有效期12个小时
					stringRedisTemplate.opsForValue().set(org.springblade.common.constant.CommonConstant.CACHE_POS_INTER_PREFIX + code, JSON.toJSONString(inter), 12, TimeUnit.HOURS);
				}else{
					inter = JSON.parseObject(interJSONStr, SmInterfaceManage.class);
				}

				interLog.setDirection(org.springblade.common.constant.CommonConstant.INTER_DIRECTION_OUT);
				interLog.setInterCode(code);
				interLog.setInterName(inter.getInterNote());
				interLog.setSystemId(s.getId());
				interLog.setServiceId(0L);
				interLog.setBusiServiceId(org.springblade.common.constant.CommonConstant.BUSI_CODE_KEY_UPLOAD);
				interLog.setInterType(inter.getInterType());
				interLog.setUrl(inter.getUrl());
				interLog.setParam(JSON.toJSONString(map));
				interLog.setResMessage(JSON.toJSONString(res));
				interLog.setCallTime(new Date());
				interLog.setCreateTime(new Date());
				interLog.setCallCost(end-start);
				interLog.setServerIp(inter.getIp());
				if(!res.isSuccess()){
					interLog.setResult(0);
				}else{
					interLog.setResult(1);
				}
			}
			logs.add(interLog);
		}
		logService.saveBatch(logs);
	}




	// 平台当前已绑定业务对象
	@PostMapping("/obj/map")
	public R<JSONObject> objMap (@Validated(PlatformObjMapGroup.class) @RequestBody PlatformObjRequest request) {
		try {
			return R.data(this.inPosSystemObjectService.objMap(request.getSystemId()));
		} catch (Exception e) {
			log.error("fail get platform obj map: {}", e.getMessage(), e);
			return R.fail("获取平台当前已绑定业务对象失败：" + e.getMessage());
		}
	}

	// 平台业务对象分页列表
	@PostMapping("/obj/page")
	public R<IPage<JSONObject>> objPage (
		@Validated(PlatformObjListGroup.class) @RequestBody PlatformObjRequest request,
		Query query,
		BladeUser user
	) {
		try {
			if (StringUtils.isBlank(user.getTenantId())) {
				return R.fail(ResultCode.FAILURE, "用户信息异常。");
			}

			return R.data(this.inPosSystemObjectService.objPage(request, query, user.getTenantId()));
		} catch (Exception e) {
			log.error("fail get platform obj page: {}", e.getMessage(), e);
			return R.fail("获取平台业务对象分页列表失败：" + e.getMessage());
		}
	}

	// 平台绑定业务对象
	@PostMapping("/obj/bind")
	public R<String> bindObj (@Validated(PlatformObjBindGroup.class) @RequestBody PlatformObjRequest request) {
		try {
			Map<String, Map<Byte, List<Long>>> objMap = request.getObjMap();
			if (
				(objMap == null) ||
				(!objMap.containsKey(CommonConstant.OBJ_TYPE_PARAM_TARGET)) ||
				(!objMap.containsKey(CommonConstant.OBJ_TYPE_PARAM_DEVICE))
			) {
				return R.fail(ResultCode.FAILURE, "业务对象信息不正确");
			}

			Map<Byte, List<Long>> targetMap = objMap.get(CommonConstant.OBJ_TYPE_PARAM_TARGET);
			Map<Byte, List<Long>> deviceMap = objMap.get(CommonConstant.OBJ_TYPE_PARAM_DEVICE);
			this.inPosSystemObjectService.bindObj(request.getSystemId(), targetMap, deviceMap);
			return R.success(ResultCode.SUCCESS);
		} catch (Exception e) {
			log.error("fail bind platform obj: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "平台绑定业务对象失败：" + e.getMessage());
		}
	}
}
