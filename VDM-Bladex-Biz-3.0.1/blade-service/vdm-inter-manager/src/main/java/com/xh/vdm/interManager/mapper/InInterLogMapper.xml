<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.InInterLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inInterLogResultMap" type="com.xh.vdm.interManager.entity.InInterLog">
        <id column="id" property="id"/>
        <result column="direction" property="direction"/>
        <result column="inter_code" property="interCode"/>
        <result column="inter_name" property="interName"/>
        <result column="system_id" property="systemId"/>
        <result column="service_id" property="serviceId"/>
        <result column="inter_type" property="interType"/>
        <result column="url" property="url"/>
        <result column="param" property="param"/>
        <result column="result" property="result"/>
        <result column="res_message" property="resMessage"/>
        <result column="call_time" property="callTime"/>
        <result column="call_cost" property="callCost"/>
        <result column="create_time" property="createTime"/>
    </resultMap>


    <select id="selectInInterLogPage" resultMap="inInterLogResultMap">
        select * from in_inter_log where is_deleted = 0
    </select>

</mapper>
