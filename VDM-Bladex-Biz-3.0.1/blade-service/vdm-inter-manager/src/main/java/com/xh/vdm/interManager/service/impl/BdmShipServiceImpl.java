package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.mapper.BdmShipMapper;
import com.xh.vdm.interManager.service.IBdmShipService;
import com.xh.vdm.biapi.entity.BdmShip;
import org.springframework.stereotype.Service;

/**
 * (BdmShip)表服务实现类
 */
@Service
public class BdmShipServiceImpl extends ServiceImpl<BdmShipMapper, BdmShip> implements IBdmShipService {
}
