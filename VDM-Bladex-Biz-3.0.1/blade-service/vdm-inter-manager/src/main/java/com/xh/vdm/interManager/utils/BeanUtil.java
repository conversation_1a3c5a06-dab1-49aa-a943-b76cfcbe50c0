package com.xh.vdm.interManager.utils;

import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

/**
 * @Description: 对象操作工具类
 * @Author: zhouxw
 * @Date: 2022/11/14 4:51 PM
 */
public class BeanUtil {

    public static Object mapToBean (Map<String,Object> map , Class clazz) throws IntrospectionException, NoSuchMethodException, InvocationTargetException, InstantiationException, IllegalAccessException {
        Object obj = clazz.getDeclaredConstructor().newInstance();
        for(String key : map.keySet()){
            PropertyDescriptor pd = new PropertyDescriptor(key,clazz);
            pd.getWriteMethod().invoke(obj , map.get(key));
        }
        return obj;
    }

    public static Map<String,Object> beanToMap(Object obj) throws Exception {
        Map<String, Object> map = new HashMap<>();
        BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        Method readMethod;
        String propertyName;
        Object value;
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            readMethod = propertyDescriptor.getReadMethod();
            propertyName = propertyDescriptor.getName();
            if (readMethod != null)
            {
                value = readMethod.invoke(obj, null);
                map.put(propertyName, value);
            }
        }
        return map;
    }

    public static Map<String,Object> beanToMap(Object obj , List<String> excludes) throws Exception {
        Map<String, Object> map = new HashMap<>();
        BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        Method readMethod;
        String propertyName;
        Object value;
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            if(excludes != null && excludes.contains(propertyDescriptor.getName())){
                continue;
            }
            readMethod = propertyDescriptor.getReadMethod();
            propertyName = propertyDescriptor.getName();
            if (readMethod != null)
            {
                value = readMethod.invoke(obj, null);
                map.put(propertyName, value);
            }
        }
        return map;
    }


    /**
     * @description: 如果属性值为null，则设置为0
     * @author: zhouxw
     * @date: 2022/12/21 4:47 PM
     * @param: [list]
     * @return: void
     **/
    public static void ifNullSetZero(List list) throws Exception {
        if(list == null || list.size() < 1){
            return ;
        }
        Class clazz = list.get(0).getClass();
        Field[] fs = clazz.getDeclaredFields();
        for(Object o : list){
            for(Field f : fs){
                String fClass = f.getType().getName();
                if(fClass.toUpperCase().contains("INTEGER")  || fClass.toUpperCase().contains("FLOAT") || fClass.toUpperCase().contains("DOUBLE")|| fClass.toUpperCase().contains("LONG")){
                    PropertyDescriptor pd = new PropertyDescriptor(f.getName(),clazz);
                    if(pd != null && pd.getReadMethod().invoke(o) == null){
                        pd.getWriteMethod().invoke(o , 0);
                    }
                }
            }
        }
    }

	/**
	 * 获取对象中的空值属性名称
	 * @param source
	 * @return
	 */
	public static String[] getNullPropertyNames (Object source) {
		final BeanWrapper src = new BeanWrapperImpl(source);
		PropertyDescriptor[] pds = src.getPropertyDescriptors();

		Set<String> emptyNames = new HashSet<String>();
		for(PropertyDescriptor pd : pds) {
			Object srcValue = src.getPropertyValue(pd.getName());
			if (srcValue == null) {
				emptyNames.add(pd.getName());
			}
		}
		String[] result = new String[emptyNames.size()];
		return emptyNames.toArray(result);
	}

}
