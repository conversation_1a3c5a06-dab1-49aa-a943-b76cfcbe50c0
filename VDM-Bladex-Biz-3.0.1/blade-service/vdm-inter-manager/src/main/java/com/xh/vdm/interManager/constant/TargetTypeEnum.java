package com.xh.vdm.interManager.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 监控对象类型
 */
@AllArgsConstructor
@Getter
public enum TargetTypeEnum {
	/**
	 * 待分配终端
	 */
	ENDPOINTS(0, "待分配终端"),
	/**
	 * 车辆
	 */
	VEHICLE(1, "车辆"),
	/**
	 * 人员
	 */
	WORKER(2, "人员"),
	/**
	 * 基础设施
	 */
	FACILITY(3, "基础设施"),
	/**
	 * 集装箱
	 */
	RECEIVER(4, "集装箱"),
	/**
	 * 外包人员
	 */
	TEMPORARY(5, "外包人员"),
	/**
	 * 访客
	 */
	VISITOR(6, "访客"),
	/**
	 * 访客
	 */
	SHIP(7, "船舶"),
	/**
	 * 访客
	 */
	BOX(8, "列车货箱"),
	/**
	 * 访客
	 */
	PRECISION(9, "精密装备"),
	/**
	 * 访客
	 */
	TRUCK(10, "矿用卡车"),


	;

	/**
	 * 标识
	 */
	private final Integer symbol;
	/**
	 * 状态
	 */
	private final String value;

}
