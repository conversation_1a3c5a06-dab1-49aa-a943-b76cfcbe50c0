package com.xh.vdm.interManager.service.impl;

import com.xh.vdm.interManager.entity.InSystemKeyHistory;
import com.xh.vdm.interManager.mapper.InSystemKeyHistoryMapper;
import com.xh.vdm.interManager.service.IInSystemKeyHistoryService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 公私钥变更历史表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-21
 */
@Service
public class InSystemKeyHistoryServiceImpl extends ServiceImpl<InSystemKeyHistoryMapper, InSystemKeyHistory> implements IInSystemKeyHistoryService {

}
