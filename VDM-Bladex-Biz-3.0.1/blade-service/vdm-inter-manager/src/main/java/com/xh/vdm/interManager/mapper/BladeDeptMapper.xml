<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.BladeDeptMapper">

    <select id="getExistDept" resultType="com.xh.vdm.interManager.entity.BladeDept">
        select * from blade_dept where is_deleted = 0
        and id = any (${deptIds});
    </select>
</mapper>
