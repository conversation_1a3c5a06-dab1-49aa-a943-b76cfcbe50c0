package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import com.xh.vdm.biapi.entity.BdmVirtualTarget;
import com.xh.vdm.interManager.mapper.BdmAbstractDeviceMapper;
import com.xh.vdm.interManager.service.IBdmAbstractDeviceService;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (BdmAbstractDevice)表服务实现类
 */
@Service
public class BdmAbstractDeviceServiceImpl extends ServiceImpl<BdmAbstractDeviceMapper,BdmAbstractDevice> implements IBdmAbstractDeviceService {
	@Resource
	private DeptProcessingUtil deptProcessingUtil;
	@Override
	public void deleteByIds(Long[] ids) {
		baseMapper.deleteByIds(ids);
	}

	@Override
	public void insertBatch(List<BdmAbstractDevice> abstractDeviceList) {
		baseMapper.insertBatch(abstractDeviceList);
	}

	@Override
	public void saveDevice(BdmAbstractDevice abstractDevice) {
		baseMapper.saveDevice(abstractDevice);
	}

	@Override
	public void unbinding(Long id, Integer targetType) {
		baseMapper.unbinding(id, targetType);
	}


	@Override
	public void deleteByTargetIds(Long[] ids, Integer targetType) {
		baseMapper.deleteByTargetIds(ids, targetType);
	}

	@Override
	public void updateDept(Long id, Integer targetType, Long deptId) {
		baseMapper.updateDept(id, targetType, deptId);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateBatchByUniqueId(BdmVirtualTarget virtualTarget) {
		baseMapper.updateBatchByUniqueId(virtualTarget);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public List<BdmAbstractDevice> getListByUserRole(DataAuthCE ceDataAuth) {
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return baseMapper.getListByUserRole(response.getAccount(), response.getOrgList());
	}

	public BdmAbstractDevice getBadByUniqueId(String uniqueId){
		return baseMapper.getBadByUniqueId(uniqueId);
	}

}






