package com.xh.vdm.interManager.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.biapi.entity.BdmPrecisionAssembly;
import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.biapi.service.*;
import com.xh.vdm.interManager.constant.CommonConstant;
import com.xh.vdm.interManager.entity.InPosSystemObject;
import com.xh.vdm.interManager.mapper.InPosSystemObjectMapper;
import com.xh.vdm.interManager.service.IInPosSystemObjectService;
import com.xh.vdm.interManager.vo.request.PlatformObjRequest;
import com.xh.vdm.interManager.vo.request.SystemObjectSaveRequest;
import org.springblade.common.constant.DictCodeConstant;
import org.springblade.common.constant.DictKeyConstant;
import org.springblade.common.constant.RedisConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.DeptNode;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台--业务对象映射。
 * 多对多的关系。
 * 业务对象包含终端、运输车辆、职工、生产厂区、铁路货车车厢、矿用卡车、精密装备、货船。
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@Service
public class InPosSystemObjectServiceImpl extends ServiceImpl<InPosSystemObjectMapper, InPosSystemObject> implements IInPosSystemObjectService {

	@Resource
	private RedisTemplate<String, DeptNode> deptNodeRedisTemplate;

	@Resource
	private IDictBizClient dictBizClient;

	@Resource
	private IBdmMonitDeviceService bdmMonitDeviceService;

	@Resource
	private IBdmPntDeviceService bdmPntDeviceService;

	@Resource
	private IBdmRdssDeviceService bdmRdssDeviceService;

	@Resource
	private IBdmRnssDeviceService bdmRnssDeviceService;

	@Resource
	private IBdmWearableDeviceService bdmWearableDeviceService;

	@Resource
	private IBdmVehicleService bdmVehicleService;

	@Resource
	private IBdmWorkerService bdmWorkerService;

	@Resource
	private IBdmFacilityService bdmFacilityService;

	@Resource
	private ITemporaryService bdmTemporaryService;

	@Resource
	private IShipService bdmShipService;

	@Resource
	private ITrainCargoBoxService bdmTrainCargoBoxService;

	@Resource
	private IPrecisionAssemblyService bdmPrecisionAssemblyService;

	@Resource
	private IBdmMiningTruckService bdmMiningTruckService;



	@Override
	@Transactional
	public void save(SystemObjectSaveRequest request) {
		//1.删除原记录
		Long systemId = request.getSystemId();
		remove(Wrappers.lambdaQuery(InPosSystemObject.class)
			.eq(InPosSystemObject::getSystemId, systemId));
		//2.保存新记录
		List<Long> objectIds = request.getObjectIds();
		Integer objectType = request.getObjectType();
		List<InPosSystemObject> list = new ArrayList<>();
		for(Long objectId : objectIds){
			InPosSystemObject obj = new InPosSystemObject();
			obj.setObjectId(objectId);
			obj.setSystemId(systemId);
			obj.setObjectType(objectType);
			obj.setCreateTime(new Date());
			list.add(obj);
		}
		saveBatch(list);
		//3.记录日志
		//todo 记录日志
	}



	@Override
	public JSONObject objMap (long systemId) {
		JSONObject platformObjMap = new JSONObject();
		List<InPosSystemObject> platformObjList
			= this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, systemId));
		if (CollectionUtils.isEmpty(platformObjList)) {
			platformObjMap.put(CommonConstant.OBJ_TYPE_PARAM_TARGET, new JSONObject());
			platformObjMap.put(CommonConstant.OBJ_TYPE_PARAM_DEVICE, new JSONObject());
			return platformObjMap;
		}

		String objTypeStr;
		String objCate;
		byte targetType;
		byte deviceType;
		Map<Byte, List<String>> targetMap = new HashMap<>();
		Map<Byte, List<String>> deviceMap = new HashMap<>();
		for (InPosSystemObject platformObj : platformObjList) {
			objTypeStr = String.valueOf(platformObj.getObjectType());
			objCate = String.valueOf(objTypeStr.charAt(0));
			if (objCate.equals(CommonConstant.OBJ_TYPE_TARGET)) {
				targetType = Byte.parseByte(objTypeStr.substring(1));
				if (!targetMap.containsKey(targetType)) {
					targetMap.put(targetType, new ArrayList<>());
				}

				targetMap.get(targetType).add(String.valueOf(platformObj.getObjectId()));
			} else if (objCate.equals(CommonConstant.OBJ_TYPE_DEVICE)) {
				deviceType = Byte.parseByte(objTypeStr.substring(1));
				if (!deviceMap.containsKey(deviceType)) {
					deviceMap.put(deviceType, new ArrayList<>());
				}

				deviceMap.get(deviceType).add(String.valueOf(platformObj.getObjectId()));
			}
		}
		platformObjMap.put(CommonConstant.OBJ_TYPE_PARAM_TARGET, targetMap);
		platformObjMap.put(CommonConstant.OBJ_TYPE_PARAM_DEVICE, deviceMap);
		return platformObjMap;
	}

	@Override
	public IPage<JSONObject> objPage (PlatformObjRequest request, Query query, String tenantId) throws Exception {
		String objCate = request.getObjCate();
		if (objCate.equals(CommonConstant.OBJ_TYPE_PARAM_TARGET)) {
			switch (String.valueOf(request.getObjType())) {
				case DictKeyConstant.TARGET_TYPE_VEHICLE:
					return this.vehiclePage(request, query, tenantId);
				case DictKeyConstant.TARGET_TYPE_WORKER:
					return this.workerPage(request, query, tenantId);
				case DictKeyConstant.TARGET_TYPE_FACILITY:
					return this.facilityPage(request, query, tenantId);
				case DictKeyConstant.TARGET_TYPE_TEMPORARY:
					return this.tempPage(request, query, tenantId);
				case DictKeyConstant.TARGET_TYPE_SHIP:
					return this.shipPage(request, query, tenantId);
				case DictKeyConstant.TARGET_TYPE_TRAIN:
					return this.trainPage(request, query, tenantId);
				case DictKeyConstant.TARGET_TYPE_PRECISION:
					return this.precisionPage(request, query, tenantId);
				case DictKeyConstant.TARGET_TYPE_TRUCK:
					return this.truckPage(request, query, tenantId);
				default:
					throw new Exception("目标类型不正确。");
			}
		} else if (objCate.equals(CommonConstant.OBJ_TYPE_PARAM_DEVICE)) {
			switch (String.valueOf(request.getObjType())) {
				case DictKeyConstant.DEVICE_TYPE_MONITOR:
					return this.monitPage(request, query, tenantId);
				case DictKeyConstant.DEVICE_TYPE_TIME:
					return this.pntPage(request, query, tenantId);
				case DictKeyConstant.DEVICE_TYPE_SHORT:
					return this.rdssPage(request, query, tenantId);
				case DictKeyConstant.DEVICE_TYPE_LOCATE:
					return this.rnssPage(request, query, tenantId);
				case DictKeyConstant.DEVICE_TYPE_WEAR:
					return this.wearPage(request, query, tenantId);
				default:
					throw new Exception("设备类型不正确。");
			}
		} else {
			throw new Exception("对象类别不正确。");
		}
	}



	private Map<Long, InPosSystemObject> getPlatformObjMap (List<Long> objIdList, PlatformObjRequest request) {
		String objCate = request.getObjCate();
		byte objType = request.getObjType();
		int objTypeNum;
		if (objCate.equals(CommonConstant.OBJ_TYPE_PARAM_TARGET)) {
			objTypeNum = Integer.parseInt(CommonConstant.OBJ_TYPE_TARGET + objType);
		} else if (objCate.equals(CommonConstant.OBJ_TYPE_PARAM_DEVICE)) {
			objTypeNum = Integer.parseInt(CommonConstant.OBJ_TYPE_DEVICE + objType);
		} else {
			return new HashMap<>();
		}

		List<InPosSystemObject> tmpList = this.list(
			Wrappers.lambdaQuery(InPosSystemObject.class)
				.eq(InPosSystemObject::getSystemId, request.getSystemId())
				.eq(InPosSystemObject::getObjectType, objTypeNum)
				.in(InPosSystemObject::getObjectId, objIdList)
		);
		return CollectionUtils.isEmpty(tmpList) ?
			new HashMap<>() :
			tmpList.parallelStream().collect(Collectors.toMap(InPosSystemObject::getObjectId, inPosSystemObject -> inPosSystemObject));
	}

	private Map<String, String> getObjTypeMap (String objCate) {
		R<Map<String, String>> rot;
		if (objCate.equals(CommonConstant.OBJ_TYPE_PARAM_TARGET)) {
			rot = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(DictCodeConstant.TARGET_TYPE, DictKeyConstant.DEFAULT_ROOT_KEY);
			if (rot.isSuccess() && (rot.getData() != null) && (!rot.getData().isEmpty())) {
				return rot.getData();
			}
		} else if (objCate.equals(CommonConstant.OBJ_TYPE_PARAM_DEVICE)) {
			rot = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(DictCodeConstant.DEVICE_TYPE, DictKeyConstant.DEFAULT_ROOT_KEY);
			if (rot.isSuccess() && (rot.getData() != null) && (!rot.getData().isEmpty())) {
				return rot.getData();
			}
		}

		return new HashMap<>();
	}

	private Map<Long, String> getDeptMap (Set<Long> deptIdList, String tenantId) {
		Map<Long, String> deptMap = new HashMap<>();
		List<DeptNode> deptList = this.deptNodeRedisTemplate.<Long, DeptNode>opsForHash().multiGet(
			RedisConstant.HASH_DEPT_NODE + tenantId,
			deptIdList
		);
		for (DeptNode dept : deptList) {
			if ((dept != null) && (dept.id != null) && StringUtils.isNotBlank(dept.name)) {
				deptMap.put(dept.id, dept.name);
			}
		}

		return deptMap;
	}

	private IPage<JSONObject> monitPage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmMonitDevice> wrapper = Wrappers.lambdaQuery(BdmMonitDevice.class).eq(BdmMonitDevice::getDeleted,0);
		if (request.getCategory() != null) {
			wrapper.eq(BdmMonitDevice::getCategory, request.getCategory());
		}
		if (StringUtils.isNotBlank(request.getDeviceSeq())) {
			wrapper.like(BdmMonitDevice::getUniqueId, request.getDeviceSeq());
		}
		if (StringUtils.isNotBlank(request.getDeviceNum())) {
			wrapper.like(BdmMonitDevice::getDeviceNum, request.getDeviceNum());
		}
		if (request.getDeptId() != null) {
			wrapper.eq(BdmMonitDevice::getDeptId, request.getDeptId());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmMonitDevice::getId, deviceIdList);
		}

		IPage<BdmMonitDevice> tmp = Condition.getPage(query);
		this.bdmMonitDeviceService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmMonitDevice> monitList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<String, String> objTypeMap = this.getObjTypeMap(request.getObjCate());

		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			monitList.parallelStream().map(BdmMonitDevice::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			monitList.parallelStream().map(BdmMonitDevice::getDeptId).collect(Collectors.toSet()),
			tenantId
		);
		for (BdmMonitDevice monit : monitList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(monit.getId()));
			response.put("category", objTypeMap.getOrDefault(String.valueOf(monit.getCategory()), ""));
			response.put("device_seq", monit.getUniqueId());
			response.put("device_num", monit.getDeviceNum());
			response.put("dept_name", deptMap.getOrDefault(monit.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(monit.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);
			if (request.getBindStatus().equals(CommonConstant.PLATFORM_OBJ_BIND_YES)){
				if(platformObjMap.containsKey(monit.getId())){
					responseList.add(response);
				}
			}else {
				responseList.add(response);
			}
		}
		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> pntPage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmPntDevice> wrapper = Wrappers.lambdaQuery(BdmPntDevice.class).eq(BdmPntDevice::getDeleted,0);
		if (request.getCategory() != null) {
			wrapper.eq(BdmPntDevice::getCategory, request.getCategory());
		}
		if (StringUtils.isNotBlank(request.getDeviceSeq())) {
			wrapper.like(BdmPntDevice::getUniqueId, request.getDeviceSeq());
		}
		if (StringUtils.isNotBlank(request.getDeviceNum())) {
			wrapper.like(BdmPntDevice::getDeviceNum, request.getDeviceNum());
		}
		if (request.getDeptId() != null) {
			wrapper.eq(BdmPntDevice::getDeptId, request.getDeptId());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmPntDevice::getId, deviceIdList);
		}

		IPage<BdmPntDevice> tmp = Condition.getPage(query);
		this.bdmPntDeviceService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmPntDevice> pntList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<String, String> objTypeMap = this.getObjTypeMap(request.getObjCate());
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			pntList.parallelStream().map(BdmPntDevice::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			pntList.parallelStream().map(BdmPntDevice::getDeptId).collect(Collectors.toSet()),
			tenantId
		);
		for (BdmPntDevice pnt : pntList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(pnt.getId()));
			response.put("category", objTypeMap.getOrDefault(String.valueOf(pnt.getCategory()), ""));
			response.put("device_seq", pnt.getUniqueId());
			response.put("device_num", pnt.getDeviceNum());
			response.put("dept_name", deptMap.getOrDefault(pnt.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(pnt.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);

			responseList.add(response);
		}

		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> rdssPage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmRdssDevice> wrapper = Wrappers.lambdaQuery(BdmRdssDevice.class).eq(BdmRdssDevice::getDeleted,0);
		if (request.getCategory() != null) {
			wrapper.eq(BdmRdssDevice::getCategory, request.getCategory());
		}
		if (StringUtils.isNotBlank(request.getDeviceSeq())) {
			wrapper.like(BdmRdssDevice::getUniqueId, request.getDeviceSeq());
		}
		if (StringUtils.isNotBlank(request.getDeviceNum())) {
			wrapper.like(BdmRdssDevice::getDeviceNum, request.getDeviceNum());
		}
		if (request.getDeptId() != null) {
			wrapper.eq(BdmRdssDevice::getDeptId, request.getDeptId());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmRdssDevice::getId, deviceIdList);
		}

		IPage<BdmRdssDevice> tmp = Condition.getPage(query);
		this.bdmRdssDeviceService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmRdssDevice> rdssList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<String, String> objTypeMap = this.getObjTypeMap(request.getObjCate());
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			rdssList.parallelStream().map(BdmRdssDevice::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			rdssList.parallelStream().map(BdmRdssDevice::getDeptId).collect(Collectors.toSet()),
			tenantId
		);
		for (BdmRdssDevice rdss : rdssList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(rdss.getId()));
			response.put("category", objTypeMap.getOrDefault(String.valueOf(rdss.getCategory()), ""));
			response.put("device_seq", rdss.getUniqueId());
			response.put("device_num", rdss.getDeviceNum());
			response.put("dept_name", deptMap.getOrDefault(rdss.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(rdss.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);

			responseList.add(response);
		}

		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> rnssPage (PlatformObjRequest request, Query query, String tenantId) {
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmRnssDevice> wrapper = Wrappers.lambdaQuery(BdmRnssDevice.class).eq(BdmRnssDevice::getDeleted,0);
		if (request.getCategory() != null) {
			wrapper.eq(BdmRnssDevice::getCategory, request.getCategory());
		}
		if (StringUtils.isNotBlank(request.getDeviceSeq())) {
			wrapper.like(BdmRnssDevice::getUniqueId, request.getDeviceSeq());
		}
		if (StringUtils.isNotBlank(request.getDeviceNum())) {
			wrapper.like(BdmRnssDevice::getDeviceNum, request.getDeviceNum());
		}
		if (request.getDeptId() != null) {
			wrapper.eq(BdmRnssDevice::getDeptId, request.getDeptId());
		}
		if (request.getBindStatus()!=null&&request.getBindStatus()==1){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmRnssDevice::getId, deviceIdList);
		}
		IPage<BdmRnssDevice> tmp = Condition.getPage(query);
		this.bdmRnssDeviceService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}
		List<BdmRnssDevice> rnssList = tmp.getRecords();
		Map<String, String> objTypeMap = this.getObjTypeMap(request.getObjCate());
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			rnssList.parallelStream().map(BdmRnssDevice::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			rnssList.parallelStream().map(BdmRnssDevice::getDeptId).collect(Collectors.toSet()),
			tenantId
		);
		for (BdmRnssDevice rnss : rnssList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(rnss.getId()));
			response.put("category", objTypeMap.getOrDefault(String.valueOf(rnss.getCategory()), ""));
			response.put("device_seq", rnss.getUniqueId());
			response.put("device_num", rnss.getDeviceNum());
			response.put("dept_name", deptMap.getOrDefault(rnss.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(rnss.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);
			responseList.add(response);
		}
		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> wearPage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmWearableDevice> wrapper = Wrappers.lambdaQuery(BdmWearableDevice.class).eq(BdmWearableDevice::getDeleted,0);
		if (request.getCategory() != null) {
			wrapper.eq(BdmWearableDevice::getCategory, request.getCategory());
		}
		if (StringUtils.isNotBlank(request.getDeviceSeq())) {
			wrapper.like(BdmWearableDevice::getUniqueId, request.getDeviceSeq());
		}
		if (StringUtils.isNotBlank(request.getDeviceNum())) {
			wrapper.like(BdmWearableDevice::getDeviceNum, request.getDeviceNum());
		}
		if (request.getDeptId() != null) {
			wrapper.eq(BdmWearableDevice::getDeptId, request.getDeptId());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmWearableDevice::getId, deviceIdList);
		}

		IPage<BdmWearableDevice> tmp = Condition.getPage(query);
		this.bdmWearableDeviceService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmWearableDevice> wearList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<String, String> objTypeMap = this.getObjTypeMap(request.getObjCate());
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			wearList.parallelStream().map(BdmWearableDevice::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			wearList.parallelStream().map(BdmWearableDevice::getDeptId).collect(Collectors.toSet()),
			tenantId
		);
		for (BdmWearableDevice wear : wearList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(wear.getId()));
			response.put("category", objTypeMap.getOrDefault(String.valueOf(wear.getCategory()), ""));
			response.put("device_seq", wear.getUniqueId());
			response.put("device_num", wear.getDeviceNum());
			response.put("dept_name", deptMap.getOrDefault(wear.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(wear.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);

			responseList.add(response);
		}

		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> vehiclePage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmVehicle> wrapper = Wrappers.lambdaQuery(BdmVehicle.class).eq(BdmVehicle::getDeleted,0);
		if (request.getCategory() != null) {
			wrapper.eq(BdmVehicle::getCategory, request.getCategory());
		}
		if (StringUtils.isNotBlank(request.getNumber())) {
			wrapper.eq(BdmVehicle::getNumber, request.getNumber());
		}
		if (request.getDeptId() != null) {
			wrapper.eq(BdmVehicle::getDeptId, request.getDeptId());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmVehicle::getId, deviceIdList);
		}

		IPage<BdmVehicle> tmp = Condition.getPage(query);
		this.bdmVehicleService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmVehicle> vehicleList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			vehicleList.parallelStream().map(BdmVehicle::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			vehicleList.parallelStream().map(BdmVehicle::getDeptId).collect(Collectors.toSet()),
			tenantId
		);

		Map<String, String> vehicleTypeMap = new HashMap<>();
		R<Map<String, String>> rvc = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(
			DictCodeConstant.VEHICLE_TYPE,
			DictKeyConstant.DEFAULT_ROOT_KEY
		);
		if (rvc.isSuccess() && (rvc.getData() != null) && (!rvc.getData().isEmpty())) {
			vehicleTypeMap = rvc.getData();
		}
		for (BdmVehicle vehicle : vehicleList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(vehicle.getId()));
			response.put("category", vehicleTypeMap.getOrDefault(String.valueOf(vehicle.getCategory()), ""));
			response.put("number", vehicle.getNumber());
			response.put("dept_name", deptMap.getOrDefault(vehicle.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(vehicle.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);

			responseList.add(response);
		}

		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> workerPage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmWorker> wrapper = Wrappers.lambdaQuery(BdmWorker.class).eq(BdmWorker::getDeleted,0);
		if (request.getIndustry() != null) {
			wrapper.eq(BdmWorker::getIndustry, request.getIndustry());
		}
		if (StringUtils.isNotBlank(request.getNumber())) {
			wrapper.like(BdmWorker::getWkno, request.getNumber());
		}
		if (StringUtils.isNotBlank(request.getName())) {
			wrapper.like(BdmWorker::getName, request.getName());
		}
		if (request.getDeptId() != null) {
			wrapper.eq(BdmWorker::getDeptId, request.getDeptId());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmWorker::getId, deviceIdList);
		}

		IPage<BdmWorker> tmp = Condition.getPage(query);
		this.bdmWorkerService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmWorker> workerList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			workerList.parallelStream().map(BdmWorker::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			workerList.parallelStream().map(BdmWorker::getDeptId).collect(Collectors.toSet()),
			tenantId
		);

		Map<String, String> workerPostMap = new HashMap<>();
		R<Map<String, String>> rwp;
		rwp = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(DictCodeConstant.WORKER_POST, DictKeyConstant.DEFAULT_ROOT_KEY);
		if (rwp.isSuccess() && (rwp.getData() != null) && (!rwp.getData().isEmpty())) {
			workerPostMap = rwp.getData();
		}
		for (BdmWorker worker : workerList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(worker.getId()));
			response.put("industry", workerPostMap.getOrDefault(String.valueOf(worker.getIndustry()), ""));
			response.put("post", workerPostMap.getOrDefault(String.valueOf(worker.getPost()), ""));
			response.put("number", worker.getWkno());
			response.put("name", worker.getName());
			response.put("dept_name", deptMap.getOrDefault(worker.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(worker.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);

			responseList.add(response);
		}

		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> facilityPage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmFacility> wrapper = Wrappers.lambdaQuery(BdmFacility.class).eq(BdmFacility::getDeleted,0);
		if (request.getCategory() != null) {
			wrapper.eq(BdmFacility::getCategory, request.getCategory());
		}
		if (StringUtils.isNotBlank(request.getName())) {
			wrapper.like(BdmFacility::getName, request.getName());
		}
		if (request.getDeptId() != null) {
			wrapper.eq(BdmFacility::getDeptId, request.getDeptId());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmFacility::getId, deviceIdList);
		}

		IPage<BdmFacility> tmp = Condition.getPage(query);
		this.bdmFacilityService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmFacility> facilityList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			facilityList.parallelStream().map(BdmFacility::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			facilityList.parallelStream().map(BdmFacility::getDeptId).collect(Collectors.toSet()),
			tenantId
		);

		Map<String, String> facilityTypeMap = new HashMap<>();
		R<Map<String, String>> rft = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(
			DictCodeConstant.FACILITY_TYPE,
			DictKeyConstant.DEFAULT_ROOT_KEY
		);
		if (rft.isSuccess() && (rft.getData() != null) && (!rft.getData().isEmpty())) {
			facilityTypeMap = rft.getData();
		}
		for (BdmFacility facility : facilityList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(facility.getId()));
			response.put("category", facilityTypeMap.getOrDefault(String.valueOf(facility.getCategory()), ""));
			response.put("number", facility.getCode());
			response.put("name", facility.getName());
			response.put("dept_name", deptMap.getOrDefault(facility.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(facility.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);

			responseList.add(response);
		}

		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> tempPage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmTemporary> wrapper = Wrappers.lambdaQuery(BdmTemporary.class).eq(BdmTemporary::getDeleted,0);
		if (request.getIndustry() != null) {
			wrapper.eq(BdmTemporary::getIndustry, request.getIndustry());
		}
		if (StringUtils.isNotBlank(request.getNumber())) {
			wrapper.like(BdmTemporary::getWkno, request.getNumber());
		}
		if (StringUtils.isNotBlank(request.getName())) {
			wrapper.like(BdmTemporary::getName, request.getName());
		}
		if (request.getDeptId() != null) {
			wrapper.eq(BdmTemporary::getDeptId, request.getDeptId());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmTemporary::getId, deviceIdList);
		}

		IPage<BdmTemporary> tmp = Condition.getPage(query);
		this.bdmTemporaryService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmTemporary> tempList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			tempList.parallelStream().map(BdmTemporary::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			tempList.parallelStream().map(BdmTemporary::getDeptId).collect(Collectors.toSet()),
			tenantId
		);

		Map<String, String> workerPostMap = new HashMap<>();
		R<Map<String, String>> rwp;
		rwp = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(DictCodeConstant.WORKER_POST, DictKeyConstant.DEFAULT_ROOT_KEY);
		if (rwp.isSuccess() && (rwp.getData() != null) && (!rwp.getData().isEmpty())) {
			workerPostMap = rwp.getData();
		}
		for (BdmTemporary temp : tempList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(temp.getId()));
			response.put("industry", workerPostMap.getOrDefault(String.valueOf(temp.getIndustry()), ""));
			response.put("post", workerPostMap.getOrDefault(String.valueOf(temp.getPost()), ""));
			response.put("number", temp.getWkno());
			response.put("name", temp.getName());
			response.put("dept_name", deptMap.getOrDefault(temp.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(temp.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);

			responseList.add(response);
		}

		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> shipPage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmShip> wrapper = Wrappers.lambdaQuery(BdmShip.class).eq(BdmShip::getDeleted,0);
		if (request.getDeptId() != null) {
			wrapper.eq(BdmShip::getDeptId, request.getDeptId());
		}
		if (StringUtils.isNotBlank(request.getNumber())) {
			wrapper.like(BdmShip::getNumber, request.getNumber());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmShip::getId, deviceIdList);
		}

		IPage<BdmShip> tmp = Condition.getPage(query);
		this.bdmShipService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmShip> shipList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			shipList.parallelStream().map(BdmShip::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			shipList.parallelStream().map(BdmShip::getDeptId).collect(Collectors.toSet()),
			tenantId
		);

		Map<String, String> shipTypeMap = new HashMap<>();
		R<Map<String, String>> rst = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(
			DictCodeConstant.SHIP_TYPE,
			DictKeyConstant.DEFAULT_ROOT_KEY
		);
		if (rst.isSuccess() && (rst.getData() != null) && (!rst.getData().isEmpty())) {
			shipTypeMap = rst.getData();
		}
		for (BdmShip ship : shipList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(ship.getId()));
			response.put("category", shipTypeMap.getOrDefault(String.valueOf(ship.getCategory()), ""));
			response.put("number", ship.getNumber());
			response.put("dept_name", deptMap.getOrDefault(ship.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(ship.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);

			responseList.add(response);
		}

		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> trainPage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmTrainCargoBox> wrapper = Wrappers.lambdaQuery(BdmTrainCargoBox.class).eq(BdmTrainCargoBox::getDeleted,0);
		if (request.getDeptId() != null) {
			wrapper.eq(BdmTrainCargoBox::getDeptId, request.getDeptId());
		}
		if (StringUtils.isNotBlank(request.getNumber())) {
			wrapper.like(BdmTrainCargoBox::getNumber, request.getNumber());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmTrainCargoBox::getId, deviceIdList);
		}

		IPage<BdmTrainCargoBox> tmp = Condition.getPage(query);
		this.bdmTrainCargoBoxService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmTrainCargoBox> trainList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			trainList.parallelStream().map(BdmTrainCargoBox::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			trainList.parallelStream().map(BdmTrainCargoBox::getDeptId).collect(Collectors.toSet()),
			tenantId
		);

		Map<String, String> carriageTypeMap = new HashMap<>();
		R<Map<String, String>> rct = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(
			DictCodeConstant.CARRIAGE_TYPE,
			DictKeyConstant.DEFAULT_ROOT_KEY
		);
		if (rct.isSuccess() && (rct.getData() != null) && (!rct.getData().isEmpty())) {
			carriageTypeMap = rct.getData();
		}
		for (BdmTrainCargoBox train : trainList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(train.getId()));
			response.put("category", carriageTypeMap.getOrDefault(String.valueOf(train.getModel()), ""));
			response.put("number", train.getNumber());
			response.put("dept_name", deptMap.getOrDefault(train.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(train.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);

			responseList.add(response);
		}

		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> precisionPage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmPrecisionAssembly> wrapper = Wrappers.lambdaQuery(BdmPrecisionAssembly.class).eq(BdmPrecisionAssembly::getDeleted,0);
		if (request.getDeptId() != null) {
			wrapper.eq(BdmPrecisionAssembly::getDeptId, request.getDeptId());
		}
		if (StringUtils.isNotBlank(request.getNumber())) {
			wrapper.like(BdmPrecisionAssembly::getNumber, request.getNumber());
		}
		if (request.getDeptId() != null) {
			wrapper.eq(BdmPrecisionAssembly::getDeptId, request.getDeptId());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmPrecisionAssembly::getId, deviceIdList);
		}

		IPage<BdmPrecisionAssembly> tmp = Condition.getPage(query);
		this.bdmPrecisionAssemblyService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmPrecisionAssembly> precisionList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			precisionList.parallelStream().map(BdmPrecisionAssembly::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			precisionList.parallelStream().map(BdmPrecisionAssembly::getDeptId).collect(Collectors.toSet()),
			tenantId
		);
		for (BdmPrecisionAssembly precision : precisionList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(precision.getId()));
			response.put("number", precision.getNumber());
			response.put("dept_name", deptMap.getOrDefault(precision.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(precision.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);

			responseList.add(response);
		}

		res.setRecords(responseList);
		return res;
	}

	private IPage<JSONObject> truckPage (PlatformObjRequest request, Query query, String tenantId) {
		IPage<JSONObject> res = Condition.getPage(query);
		LambdaQueryWrapper<BdmMiningTruck> wrapper = Wrappers.lambdaQuery(BdmMiningTruck.class).eq(BdmMiningTruck::getDeleted,0);
		if (request.getCategory() != null) {
			wrapper.eq(BdmMiningTruck::getCategory, request.getCategory());
		}
		if (StringUtils.isNotBlank(request.getNumber())) {
			wrapper.eq(BdmMiningTruck::getNumber, request.getNumber());
		}
		if (request.getDeptId() != null) {
			wrapper.eq(BdmMiningTruck::getDeptId, request.getDeptId());
		}
		if (request.getBindStatus()!=null){
			List<InPosSystemObject> platformObjList = this.list(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, request.getSystemId()));
			List<Long> deviceIdList = platformObjList.parallelStream().map(InPosSystemObject::getObjectId).collect(Collectors.toList());
			wrapper.in(BdmMiningTruck::getId, deviceIdList);
		}

		IPage<BdmMiningTruck> tmp = Condition.getPage(query);
		this.bdmMiningTruckService.page(tmp, wrapper);
		res.setTotal(tmp.getTotal());
		if (res.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return res;
		}

		List<BdmMiningTruck> truckList = tmp.getRecords();
		List<JSONObject> responseList = new ArrayList<>();
		JSONObject response;
		Map<Long, InPosSystemObject> platformObjMap = this.getPlatformObjMap(
			truckList.parallelStream().map(BdmMiningTruck::getId).collect(Collectors.toList()),
			request
		);
		Map<Long, String> deptMap = this.getDeptMap(
			truckList.parallelStream().map(BdmMiningTruck::getDeptId).collect(Collectors.toSet()),
			tenantId
		);

		Map<String, String> truckTypeMap = new HashMap<>();
		R<Map<String, String>> rtt = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(
			DictCodeConstant.TRUCK_TYPE,
			DictKeyConstant.DEFAULT_ROOT_KEY
		);
		if (rtt.isSuccess() && (rtt.getData() != null) && (!rtt.getData().isEmpty())) {
			truckTypeMap = rtt.getData();
		}
		for (BdmMiningTruck truck : truckList) {
			response = new JSONObject();
			response.put("obj_id", String.valueOf(truck.getId()));
			response.put("category", truckTypeMap.getOrDefault(String.valueOf(truck.getCategory()), ""));
			response.put("number", truck.getNumber());
			response.put("dept_name", deptMap.getOrDefault(truck.getDeptId(), ""));
			response.put(
				"bind_status",
				platformObjMap.containsKey(truck.getId()) ? CommonConstant.PLATFORM_OBJ_BIND_YES : CommonConstant.PLATFORM_OBJ_BIND_NO
			);

			responseList.add(response);
		}

		res.setRecords(responseList);
		return res;
	}

	@Override
	public void bindObj (long systemId, Map<Byte, List<Long>> targetMap, Map<Byte, List<Long>> deviceMap) {
		targetMap = ((targetMap == null) || targetMap.isEmpty()) ? new HashMap<>() : targetMap;
		deviceMap = ((deviceMap == null) || deviceMap.isEmpty()) ? new HashMap<>() : deviceMap;
		List<Long> targetList;
		List<Long> deviceList;
		List<InPosSystemObject> inPosSystemObjectList = new ArrayList<>();
		InPosSystemObject inPosSystemObject;
		Date date = new Date();
		for (Byte targetType : targetMap.keySet()) {
			targetList = targetMap.get(targetType);
			if (CollectionUtils.isNotEmpty(targetList)) {
				for (long targetId : targetList) {
					inPosSystemObject = new InPosSystemObject();
					inPosSystemObject.setSystemId(systemId);
					inPosSystemObject.setObjectId(targetId);
					inPosSystemObject.setObjectType(Integer.parseInt(CommonConstant.OBJ_TYPE_TARGET + targetType));
					inPosSystemObject.setCreateTime(date);
					inPosSystemObjectList.add(inPosSystemObject);
				}
			}
		}
		for (Byte deviceType : deviceMap.keySet()) {
			deviceList = deviceMap.get(deviceType);
			if (CollectionUtils.isNotEmpty(deviceList)) {
				for (long deviceId : deviceList) {
					inPosSystemObject = new InPosSystemObject();
					inPosSystemObject.setSystemId(systemId);
					inPosSystemObject.setObjectId(deviceId);
					inPosSystemObject.setObjectType(Integer.parseInt(CommonConstant.OBJ_TYPE_DEVICE + deviceType));
					inPosSystemObject.setCreateTime(date);
					inPosSystemObjectList.add(inPosSystemObject);
				}
			}
		}

		this.remove(Wrappers.lambdaQuery(InPosSystemObject.class).eq(InPosSystemObject::getSystemId, systemId));
		if (CollectionUtils.isNotEmpty(inPosSystemObjectList)) {
			this.saveBatch(inPosSystemObjectList);
		}
	}

}
