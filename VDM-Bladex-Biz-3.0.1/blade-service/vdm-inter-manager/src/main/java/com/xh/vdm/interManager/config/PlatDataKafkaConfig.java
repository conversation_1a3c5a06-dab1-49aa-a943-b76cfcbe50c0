package com.xh.vdm.interManager.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;

/**
 * @Description: 位置平台同步国能基础服务平台数据，机构、人员
 */
@Configuration
public class PlatDataKafkaConfig {

	@ConfigurationProperties(prefix = "spring.sync-plat-data")
	@Bean("platDataKafkaProperties")
	public KafkaProperties dataKafkaProperties() {
		return new KafkaProperties();
	}

	@Bean("platDataKafkaListenerContainerFactory")
	public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>>
	firstKafkaListenerContainerFactory(@Autowired @Qualifier("platDataKafkaProperties") KafkaProperties firstKafkaProperties) {
		ConcurrentKafkaListenerContainerFactory<Integer, String> factory =
			new ConcurrentKafkaListenerContainerFactory<>();
		factory.setConsumerFactory(firstConsumerFactory(firstKafkaProperties));
		factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
		factory.getContainerProperties().setMissingTopicsFatal(false);
		factory.getContainerProperties().setGroupId(firstKafkaProperties.getConsumer().getGroupId());
		return factory;
	}

	private ConsumerFactory<? super Integer, ? super String> firstConsumerFactory(KafkaProperties firstKafkaProperties) {
		return new DefaultKafkaConsumerFactory<>(firstKafkaProperties.buildConsumerProperties());
	}


}
