package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.mapper.FacilityMapper;
import com.xh.vdm.interManager.service.IFacilityService;
import lombok.extern.slf4j.Slf4j;
import com.xh.vdm.biapi.entity.BdmFacility;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * (BdmFacility)表服务实现类
 */
@Service
@Slf4j
public class FacilityServiceImpl extends ServiceImpl<FacilityMapper, BdmFacility> implements IFacilityService {

	@Resource
	private FacilityMapper facilityMapper;

}
