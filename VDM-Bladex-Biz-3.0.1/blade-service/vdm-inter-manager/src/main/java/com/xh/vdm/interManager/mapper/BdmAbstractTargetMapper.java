package com.xh.vdm.interManager.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmAbstractTarget)表数据库访问层
 */
public interface BdmAbstractTargetMapper extends BaseMapper<BdmAbstractTarget> {
	void deleteByIds(Long[] ids);

	void insertBatch(@Param("list") List<BdmAbstractTarget> list);
}

