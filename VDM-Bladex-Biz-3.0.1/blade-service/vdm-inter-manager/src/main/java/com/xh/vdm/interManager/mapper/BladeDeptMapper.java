package com.xh.vdm.interManager.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.interManager.entity.BladeDept;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
public interface BladeDeptMapper extends BaseMapper<BladeDept> {

	/**
	 * 查找存在的dept信息
	 * deptIds的格式： '{xxx,xxx,xx}'
	 * @param deptIds
	 * @return
	 */
	List<BladeDept> getExistDept(String deptIds);
}
