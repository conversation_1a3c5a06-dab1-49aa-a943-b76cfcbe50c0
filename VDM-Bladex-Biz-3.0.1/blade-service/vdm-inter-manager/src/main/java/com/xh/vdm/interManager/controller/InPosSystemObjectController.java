package com.xh.vdm.interManager.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.interManager.entity.InPosSystemObject;
import com.xh.vdm.interManager.service.IInPosSystemObjectService;
import com.xh.vdm.interManager.vo.request.SystemObjectRequest;
import com.xh.vdm.interManager.vo.request.SystemObjectSaveRequest;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 平台--业务对象映射。
 * 多对多的关系。
 * 业务对象包含终端、运输车辆、职工、生产厂区、铁路货车车厢、矿用卡车、精密装备、货船。 前端控制器
 *
 * <AUTHOR>
 * @since 2024-07-20
 */
@RestController
@Slf4j
@RequestMapping("/inPosSystemObject")
public class InPosSystemObjectController {

	@Resource
	private IInPosSystemObjectService systemObjectService;

	/**
	 * 保存平台-业务对象映射信息
	 * @param request
	 * @return
	 */
	@PostMapping("/save")
	public R<String> save(@RequestBody SystemObjectSaveRequest request){

		try{
			systemObjectService.save(request);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("操作失败",e);
			return R.fail("操作失败");
		}
	}

	/**
	 * 分页查询平台-业务对象信息
	 * @param request
	 * @return
	 */
	@PostMapping("/list")
	public R<List<InPosSystemObject>> page(@RequestBody SystemObjectRequest request){
		if(request == null){
			return R.fail("参数有误");
		}
		if(request.getSystemId() == null){
			return R.fail("请选择平台");
		}
		if(request.getObjectType() == null){
			return R.fail("请选择业务对象类型");
		}
		List<InPosSystemObject> list = systemObjectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
			.eq(InPosSystemObject::getSystemId, request.getSystemId())
			.eq(InPosSystemObject::getObjectType, request.getObjectType()));
		return R.data(list);
	}
}
