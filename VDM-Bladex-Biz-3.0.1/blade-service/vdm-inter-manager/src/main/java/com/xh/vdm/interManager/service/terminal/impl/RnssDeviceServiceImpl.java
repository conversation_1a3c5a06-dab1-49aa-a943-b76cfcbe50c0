/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service.terminal.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.interManager.feign.IMessageClient;
import com.xh.vdm.interManager.mapper.terminal.RnssDeviceMapper;
import com.xh.vdm.interManager.service.IBdmAbstractDeviceService;
import com.xh.vdm.interManager.service.IBdmAbstractTargetService;
import com.xh.vdm.interManager.service.terminal.IRnssDeviceService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.common.utils.StringUtils;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Service
public class RnssDeviceServiceImpl extends ServiceImpl<RnssDeviceMapper, BdmRnssDevice> implements IRnssDeviceService {

	@Resource
	private RnssDeviceMapper rnssDeviceMapper;

	@Resource
	private RedisTemplate redisTemplate;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteByTargetIds(Long[] ids, Integer targetType) {
		rnssDeviceMapper.deleteByTargetIds(ids, targetType);
	}


	@Override
	public void updateDept(Long id, Integer targetType, Long deptId) {
		List<BdmRnssDevice> rnssDevices = baseMapper.selectList(
			new QueryWrapper<BdmRnssDevice>()
				.eq("target_id", id)
				.eq("target_type", targetType)
				.eq("deleted", 0)
				.select("id,unique_id,device_type,dept_id,category,device_num,iot_protocol")
		);
		if(!rnssDevices.isEmpty()){
			baseMapper.updateDept(id, targetType, deptId);

			Map<String, String> map = new HashMap<>();
			for (BdmRnssDevice rnssDevice : rnssDevices) {
				String key = rnssDevice.getDeviceType() + "-" + rnssDevice.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("uniqueId", rnssDevice.getUniqueId());
				innerMap.put("deviceNum", rnssDevice.getDeviceNum());
				innerMap.put("category", rnssDevice.getCategory());
				innerMap.put("deviceType", rnssDevice.getDeviceType());
				innerMap.put("deptId", deptId);
				innerMap.put("iotProtocol", rnssDevice.getIotProtocol());
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
			if (!map.isEmpty()) {
				redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_DEVICE, map);
			}
		}
	}
}
