<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.terminal.RdssDeviceMapper">

    <update id="updateDept">
        update bdm_rdss_device set dept_id = #{deptId} where target_id = #{id} and target_type = #{targetType}
    </update>

    <delete id="deleteByTargetIds">
        <if test="ids != null and ids.length > 0">
            update bdm_rdss_device
            set target_id = 0,target_type = 0,target_name = ''
            where target_type = #{targetType}
            and target_id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>
</mapper>
