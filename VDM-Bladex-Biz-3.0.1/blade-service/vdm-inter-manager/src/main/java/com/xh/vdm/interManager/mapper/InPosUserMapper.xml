<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.InPosUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inPosUserResultMap" type="com.xh.vdm.interManager.entity.InPosUser">
        <id column="id" property="id"/>
        <result column="account" property="account"/>
        <result column="password" property="password"/>
        <result column="contact_name" property="contactName"/>
        <result column="desc" property="desc"/>
        <result column="contact_email" property="contactEmail"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDel"/>
        <result column="alarm_rule_id" property="alarmRuleId"/>
        <result column="system_id" property="systemId"/>
        <result column="dept_id" property="deptId"/>
    </resultMap>


    <select id="selectInPosUserPage" resultMap="inPosUserResultMap">
        select * from in_pos_user where is_deleted = 0
    </select>

    <select id="getInterfaceAuth" resultType="com.xh.vdm.interManager.vo.response.InterfaceResponse">
        select * from in_pos_user a, in_pos_interface b, in_pos_user_interface c
        where c.user_id = a.id and c.interface_id = b.id
          and a.id = #{request.userId}
        <if test="request.code != null and request.code != ''">
            and b.code like #{request.code}
        </if>
        <if test="request.name != null and request.name != ''">
            and b.name like #{request.name}
        </if>
        <if test="request.serviceId != null and request.serviceId != ''">
            and b.service_id = #{request.serviceId}
        </if>
    </select>


</mapper>
