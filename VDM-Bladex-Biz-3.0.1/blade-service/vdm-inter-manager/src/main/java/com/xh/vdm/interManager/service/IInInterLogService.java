package com.xh.vdm.interManager.service;

import com.xh.vdm.interManager.entity.InInterLog;
import com.xh.vdm.interManager.vo.InInterLogVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface IInInterLogService extends IService<InInterLog> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inInterLog
	 * @return
	 */
	IPage<InInterLogVO> selectInInterLogPage(IPage<InInterLogVO> page, InInterLogVO inInterLog);

	/**
	 * 保存接口日志
	 * HTTP接口，保存完整的接口调用日志
	 * MQTT接口，保存接口统计数据
	 * @param interLog
	 */
	void saveInterLogAsync(InInterLog interLog);

	/**
	 * 批量保存接口日志
	 * @param interLogList
	 */
	void saveBatchInterLogAsync(List<InInterLog> interLogList);

}
