/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service.impl;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.interManager.entity.InInterLog;
import com.xh.vdm.interManager.vo.InInterLogVO;
import com.xh.vdm.interManager.mapper.InInterLogMapper;
import com.xh.vdm.interManager.service.IInInterLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
@Service
@Slf4j
public class InInterLogServiceImpl extends ServiceImpl<InInterLogMapper, InInterLog> implements IInInterLogService {

	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(10, 20, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(5));

	@Resource
	private KafkaTemplate kafkaTemplate;

	@Override
	public IPage<InInterLogVO> selectInInterLogPage(IPage<InInterLogVO> page, InInterLogVO inInterLog) {
		return page.setRecords(baseMapper.selectInInterLogPage(page, inInterLog));
	}

	@Override
	public void saveInterLogAsync(InInterLog interLog) {
		//使用额外的线程执行，不影响主业务
		threadPool.submit(() -> {
			try{
				interLog.setCreateTime(new Date());
				kafkaTemplate.send(CommonConstant.TOPIC_INTER_LOG, JSON.toJSONString(interLog));
				log.debug("保存接口日志成功，数据为：{}", JSON.toJSONString(interLog));
			}catch (Exception e){
				log.error("保存接口日志报错，要保存的数据为{}",JSON.toJSONString(interLog),e);
			}
		});
	}

	@Override
	public void saveBatchInterLogAsync(List<InInterLog> interLogList) {
		for(InInterLog interLog : interLogList){
			saveInterLogAsync(interLog);
		}
	}
}
