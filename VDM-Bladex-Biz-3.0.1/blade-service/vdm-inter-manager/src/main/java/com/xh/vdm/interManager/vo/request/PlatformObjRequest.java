package com.xh.vdm.interManager.vo.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.interManager.vo.request.group.PlatformObjBindGroup;
import com.xh.vdm.interManager.vo.request.group.PlatformObjListGroup;
import com.xh.vdm.interManager.vo.request.group.PlatformObjMapGroup;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Data
public class PlatformObjRequest {

	// 平台ID
	@JsonProperty("system_id")
	@JSONField(name = "system_id")
	@NotNull(message = "平台ID为空。", groups = {PlatformObjBindGroup.class, PlatformObjListGroup.class, PlatformObjMapGroup.class})
	@DecimalMin(value = "1", message = "平台ID不正确。", groups = {PlatformObjBindGroup.class, PlatformObjListGroup.class})
	private Long systemId;

	// 单位ID
	@JsonProperty("dept_id")
	@JSONField(name = "dept_id")
	private Long deptId;

	// 对象类别（target：目标，device：设备）
	@JsonProperty("obj_cate")
	@JSONField(name = "obj_cate")
	@NotEmpty(message = "对象类别为空。", groups = {PlatformObjListGroup.class})
	private String objCate;

	// 目标类型或设备类型（根据对象类别决定该字段的含义，类型值与名称的映射，详见blade_dict_biz表中，code为target_type或bdm_device_type的记录）
	@JsonProperty("obj_type")
	@JSONField(name = "obj_type")
	@NotNull(message = "目标类型或设备类型为空。", groups = {PlatformObjListGroup.class})
	private Byte objType;

	// 目标分类或设备分类（根据对象类别决定该字段的含义，分类值与名称的映射，详见blade_dict_biz表中，code为target_type或bdm_device_type的记录）
	@JsonProperty("category")
	@JSONField(name = "category")
	private Short category;

	// 对象编号
	@JsonProperty("number")
	@JSONField(name = "number")
	private String number;

	// 对象名称
	@JsonProperty("name")
	@JSONField(name = "name")
	private String name;

	// 绑定状态（0：未绑定，1：已绑定）
	@JsonProperty("bind_status")
	@JSONField(name = "bind_status")
	private Byte bindStatus;

	// 设备序列号
	@JsonProperty("device_seq")
	@JSONField(name = "device_seq")
	private String deviceSeq;

	// 设备赋码号
	@JsonProperty("device_num")
	@JSONField(name = "device_num")
	private String deviceNum;

	// 行业类型
	@JsonProperty("industry")
	@JSONField(name = "industry")
	private Short industry;

	// 业务对象信息
	@JsonProperty("obj_map")
	@JSONField(name = "obj_map")
	private Map<String, Map<Byte, List<Long>>> objMap;
}
