/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.mapper;

import com.xh.vdm.interManager.entity.InPosInterface;
import com.xh.vdm.interManager.vo.InPosInterfaceVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.vo.request.InterfaceRequest;
import com.xh.vdm.interManager.vo.response.InterfaceResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
public interface InPosInterfaceMapper extends BaseMapper<InPosInterface> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inPosInterface
	 * @return
	 */
	List<InPosInterfaceVO> selectInPosInterfacePage(IPage page, InPosInterfaceVO inPosInterface);


	/**
	 * 根据用户id查询接口权限
	 * @param userId
	 * @return
	 */
	List<InPosInterface> getInterfaceByUserId(Long userId);

	/**
	 * 查询未绑定的接口
	 * @param request
	 * @return
	 */
	IPage<InterfaceResponse> getInterfacesUnBind(@Param("request") InterfaceRequest request, IPage<InterfaceResponse> page);


	/**
	 * 查询绑定了的接口
	 * @param request
	 * @return
	 */
	List<InterfaceResponse> getInterfacesBind(@Param("request") InterfaceRequest request);
}
