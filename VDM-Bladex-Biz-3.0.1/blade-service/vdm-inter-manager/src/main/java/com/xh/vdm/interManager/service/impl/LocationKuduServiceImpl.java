package com.xh.vdm.interManager.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.LocationKudu;
import com.xh.vdm.interManager.mapper.LocationKuduMapper;
import com.xh.vdm.interManager.service.ILocationKuduService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/5/24 13:34
 */
@Service
@DS("impala")
public class LocationKuduServiceImpl extends ServiceImpl<LocationKuduMapper, LocationKudu> implements ILocationKuduService {

	@Override
	public LocationKudu findLatestLocationByTimestamp(Long deviceId, Long timestampBefore) throws Exception {
		return baseMapper.getLatestLocation(deviceId, timestampBefore);
	}
}
