/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.SmInterfaceSystem;
import com.xh.vdm.interManager.mapper.SmInterfaceSystemMapper;
import com.xh.vdm.interManager.service.ISmInterfaceSystemService;
import com.xh.vdm.interManager.vo.SmInterfaceSystemVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 接口管理系统表 服务实现类
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@Service
public class SmInterfaceSystemServiceImpl extends ServiceImpl<SmInterfaceSystemMapper, SmInterfaceSystem> implements ISmInterfaceSystemService {

	@Override
	public IPage<SmInterfaceSystemVO> selectSmInterfaceSystemPage(IPage<SmInterfaceSystemVO> page, SmInterfaceSystemVO smInterfaceSystem) {
		return page.setRecords(baseMapper.selectSmInterfaceSystemPage(page, smInterfaceSystem));
	}

	@Override
	public List<String> findPublicKeyByAccount(String account) {
		return baseMapper.getPublicKeyByAccount(account);
	}

	@Override
	public List<SmInterfaceSystem> findExistSystem(String ids) {
		//对参数进行处理：拼接 '{}'，因为自己拼接了标识符，不会产生sql注入
		String param = "'{" + ids + "}'";
		return baseMapper.getExistSystem(param);
	}
}
