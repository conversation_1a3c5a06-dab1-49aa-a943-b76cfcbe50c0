package com.xh.vdm.interManager.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.crypto.symmetric.SM4;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.xh.vdm.interManager.dto.message.*;
import com.xh.vdm.interManager.entity.BdmDeviceLink;
import com.xh.vdm.interManager.entity.SmInterfaceSystem;
import com.xh.vdm.interManager.mqtt.MqttSendClient;
import com.xh.vdm.interManager.service.BdmDeviceLinkService;
import com.xh.vdm.interManager.service.ISmInterfaceManageService;
import com.xh.vdm.interManager.service.ISmInterfaceSystemService;
import com.xh.vdm.interManager.service.ITerminalService;
import com.xh.vdm.interManager.utils.MessageUtils;
import com.xh.vdm.interManager.vo.MockEncryptRequest;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.crypto.SecretKey;
import java.security.KeyPair;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

	@Resource
	private MessageUtils messageUtils;

    private ISmInterfaceManageService smInterfaceManageService;


	@Resource
	private BdmDeviceLinkService deviceLinkService;

	@Resource
	private ISmInterfaceSystemService interfaceSystemService;

	@Resource
	private ITerminalService terminalService;

	@Resource
	private RedisTemplate<String,Object> redisTemplate;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private MqttSendClient client;

	@GetMapping("/test")
	public R<String> test(String s){
		List<String> list = new ArrayList<>();
		list.add("1123123123");
		list.add("123123123123");
		list.add("3463453245");
		stringRedisTemplate.opsForValue().set("test:test:data",JSON.toJSONString(list));
		return R.data("this is ok 222, "+s);
	}


	@GetMapping("/testSave")
	public R<String> testSave(){
		try{
			BdmDeviceLink link = new BdmDeviceLink();
			link.setDeviceId(12512315213124L);
			link.setDeviceType(1);
			link.setUniqueId("test");
			link.setDeviceNum("test");
			link.setTargetId(123123123123123L);
			link.setTargetType(1);
			link.setTargetName("test");
			//todo 上线时，没有经纬度和位置
			link.setAction(0);
			link.setTime(new Date());
			deviceLinkService.save(link);
			return R.success("保存成功");
		}catch (Exception e){
			log.error("保存失败");
			return R.fail("保存失败");
		}
	}



	@PostMapping("/send")
	public R<String> sendMessage(){
		try {
			String topic = "collect/RA0002";
			String message = "[{\"deviceId\":\"123123\",\"deviceKind\":\"1\",\"ownerId\":\"123\",\"userType\":\"1\",\"userCode\":\"123\",\"deviceNum\":\"123\",\"time\":\"1719460519\",\"longitude\":113.453138,\"latitude\":23.154946,\"altitude\":12,\"speed\":10,\"direction\":0,\"state\":1,\"locType\":1,\"mapType\":1}]";
			client.publish(false, topic, message);
			return R.success("消息发送成功");
		}catch (Exception e){
			log.error("消息发送失败",e);
			return R.fail("消息发送失败，"+e.getMessage());
		}
	}

	@GetMapping("/testForLocation")
	public R<String> testForLocation(){
		Long id = terminalService.findIdByUniqueId("123123123123123123123123123123123");
		return R.data(id+"");
	}

	@GetMapping("/testForVehicleMessage")
	public R<Message> testForVehicleMessage(){
		List<Vehicle> vList = new ArrayList<>();
		Vehicle v = new Vehicle();
		v.setVehicleNo("京A89789");
		v.setVehicleColor("黄色");
		v.setVehicleNum("12345");
		v.setVehicleType("10");
		v.setOwnerId("1810652695300804608");
		v.setOwnerName("海格华维");
		v.setOperateState("A");
		v.setOperateTime("202407221627");
		vList.add(v);
		String data = JSON.toJSONString(vList);
		Message message = null;
		try {
			Header header = new Header();
			header.setSerialNum(new Date().getTime()+"");
			header.setServiceId("RB0001");
			header.setSystemId("1813411529945534466");
			message = encryptData(header, data);
		}catch (Exception e){
			log.error("操作失败",e);
			return R.fail("操作失败，"+e.getMessage());
		}
		return R.data(message);
	}

	/**
	 * 模拟外部平台加密报文
	 * 发送方 公务车测试平台
	 * 接收方 位置平台
	 * @param bodyJson
	 * @return
	 * @throws Exception
	 */
	public Message encryptData(Header header, String bodyJson) throws Exception{
		String systemId = header.getSystemId();
		//1.发送方随机产生一个SM4对称加密密钥并用此密钥对要发送的数据信息明文进
		//行加密（SM4算法），形成数据信息密文
		SecretKey sm4SecretKey = SmUtil.sm4().getSecretKey();
		String sm4Key = Base64.encode(sm4SecretKey.getEncoded());
		header.setSystemId(systemId);
		log.info("sm4Key is ");
		log.info(sm4Key);
		String encryptedBody = SmUtil.sm4(sm4SecretKey.getEncoded()).encryptBase64(bodyJson.getBytes());
		//2.发送方用接收方的公钥对SM4密钥进行加密(SM2算法)，生成加密密钥串
		//2.1 获取接收方平台公钥（位置平台）
		String thirdPublicKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6+wdFzk0mLD7Seg8eE+a/swipd2UgNIQKRxduhx42cMCbR7xZ17DthyUL8jyVxZF23i/FhIvBB9+2Yzj+QzMSA==";
		//2.2 获取发送方平台私钥（公务车测试平台）
		String privateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgagG1QutuBzV2t8/MCU+xR1TAhCcMJKVCe1GvDQrWmRCgCgYIKoEcz1UBgi2hRANCAAScSCmEJasA4n8Q9x8AaYFQFjlEKJ2lmdcFyOJ8jEE+rC2+wj/yrG4/fjvnfWJ4D7PBM3OX476IIprLbyaW4qVs";
		//2.3 使用接收方公钥加密
		SM2 thirdSm2 = SmUtil.sm2(null, thirdPublicKey);
		String encryptedSm4Key = Base64.encode(thirdSm2.encrypt(sm4Key, KeyType.PublicKey));

		//3.发送方对数据信息密文计算信息摘要（SM3算法），然后对摘要用自己的私钥生
		//成数字签名(SM2算法)
		//3.1 生成摘要
		log.info("encryptedBody is ");
		log.info(encryptedBody);
		byte[] crc = SmUtil.sm3().digest(encryptedBody);
		log.info("crc is ");
		log.info(Base64.encode(crc));
		//3.2 生成数字签名
		SM2 sm2 = SmUtil.sm2(privateKey, null);
		String sign = Base64.encode(sm2.sign(crc));

		//4.发送方将数字签名、加密密钥串附着在报文头中，数据信息密文附着在报文体中，
		//组成报文发送给接收方
		Message message = new Message();
		header.setCrc(sign);
		header.setEncryptedCode(encryptedSm4Key);
		Body body = new Body();
		body.setEncryptedContent(encryptedBody.trim());
		message.setHeader(header);
		message.setBody(body);
		return message;
	}

	/**
	 * 公务车-页面对接，获取加密串
	 * @return
	 */
	@GetMapping("/getEncryptedPassword")
	public R getEncryptedPassword() {
		//用户： inter_test
		Map<String,Object> map = new HashMap<>();
		map.put("password","841d804073f203eae1dd95567298f987");
		map.put("timestamp",new Date().getTime());
		String fromSystemId = "1813411529945534466";
		String toSystemId = "0";
		Map<String,String> res = new HashMap<>();
		try {
			res = encryptData(fromSystemId, JSON.toJSONString(map), toSystemId);
		}catch (Exception e){
			log.error("加密失败");
		}
		return R.data(res);
	}


	/**
	 * 加密消息体（模拟公务车发送的页面请求报文）
	 * @param bodyJson 消息体的json字符串
	 * @param toSystemId 根据该id查找接收方的公钥，用于加密数据
	 * @return
	 */
	private Map<String,String> encryptData(String fromSystemId, String bodyJson, String toSystemId) throws Exception{
		//1.发送方随机产生一个SM4对称加密密钥并用此密钥对要发送的数据信息明文进
		//行加密（SM4算法），形成数据信息密文
		SecretKey sm4SecretKey = SmUtil.sm4().getSecretKey();
		String sm4Key = Base64.encode(sm4SecretKey.getEncoded());
		String systemId = fromSystemId;
		log.info(sm4Key);
		String encryptedBody = SmUtil.sm4(sm4SecretKey.getEncoded()).encryptBase64(bodyJson.getBytes());
		//2.发送方用接收方的公钥对SM4密钥进行加密(SM2算法)，生成加密密钥串
		//2.1 获取接收方平台公钥（位置平台公钥）
		String thirdPublicKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6+wdFzk0mLD7Seg8eE+a/swipd2UgNIQKRxduhx42cMCbR7xZ17DthyUL8jyVxZF23i/FhIvBB9+2Yzj+QzMSA==";

		//2.2 获取发送方私钥（公务车私钥）
		String privateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgagG1QutuBzV2t8/MCU+xR1TAhCcMJKVCe1GvDQrWmRCgCgYIKoEcz1UBgi2hRANCAAScSCmEJasA4n8Q9x8AaYFQFjlEKJ2lmdcFyOJ8jEE+rC2+wj/yrG4/fjvnfWJ4D7PBM3OX476IIprLbyaW4qVs";

		//2.3 使用接收方公钥加密
		SM2 thirdSm2 = SmUtil.sm2(null, thirdPublicKey);
		String encryptedSm4Key = Base64.encode(thirdSm2.encrypt(sm4Key, KeyType.PublicKey));
		//3.发送方对数据信息密文计算信息摘要（SM3算法），然后对摘要用自己的私钥生
		//成数字签名(SM2算法)
		//3.1 生成摘要
		byte[] crc = SmUtil.sm3().digest(encryptedBody);
		log.info("crc is " + Base64.encode(crc));
		//3.2 生成数字签名
		SM2 sm2 = SmUtil.sm2(privateKey, null);
		String sign = Base64.encode(sm2.sign(crc));
		log.info("sign is "+ sign);
		//4.发送方将数字签名、加密密钥串附着在报文头中，数据信息密文附着在报文体中，
		//组成报文发送给接收方
		Map<String,String> map = new HashMap<>();
		map.put("code", encryptedSm4Key);
		map.put("content",encryptedBody);
		map.put("sign",sign);
		return map;
	}


	/**
	 * 公务车-页面对接，模拟获取上报车辆报文
	 * @param v
	 * @return
	 */
	@PostMapping("/getGovVehMessage")
	public R<Message> getGovVehMessage(@RequestBody Vehicle v){
		List<Vehicle> vList = new ArrayList<>();
		vList.add(v);
		String data = JSON.toJSONString(vList);
		Message message = null;
		try {
			Header header = new Header();
			header.setSerialNum(new Date().getTime()+"");
			header.setServiceId("RB0001");
			header.setSystemId("1813411529945534466");
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
			header.setReqTime(sdf.format(new Date()));
			message = encryptData(header, data);
		}catch (Exception e){
			log.error("操作失败",e);
			return R.fail("操作失败，"+e.getMessage());
		}
		return R.data(message);
	}

	/**
	 * 公务车-页面对接，获取 查询密钥 报文
	 * 查询位置平台公钥
	 * @return
	 */
	@GetMapping("/getSearchKeyMessage")
	public R<Message> getSearchKeyMessage(){
		KeyRequest request = new KeyRequest();
		request.setUserName("inter_test");
		String data = JSON.toJSONString(request);
		Message message = null;
		try {
			Header header = new Header();
			header.setSerialNum(new Date().getTime()+"");
			header.setServiceId("RS0001");
			header.setSystemId("1813411529945534466");
			SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
			header.setReqTime(sdf.format(new Date()));
			message = encryptData(header, data);
			return R.data(message);
		}catch (Exception e){
			log.error("操作失败",e);
			return R.fail("操作失败，"+e.getMessage());
		}
	}

	/**
	 * 公务车-页面对接，获取解密之后的公钥
	 * 注意：如果要传递加密数据，尽量使用post在body中传递，url中很容易超出长度，造成数据损坏
	 * @return
	 */
	@PostMapping("/getDecPublic")
	public String getDecPublic(@RequestBody Map<String,String> map){

		String encPublicKey = map.get("encPublicKey");
		String encCode = map.get("encCode");
		String privateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgHxSowCmSEZEvCUd54Jj4+l574BWWuN8Mxj2yXGJrG0WgCgYIKoEcz1UBgi2hRANCAATr7B0XOTSYsPtJ6Dx4T5r+zCKl3ZSA0hApHF26HHjZwwJtHvFnXsO2HJQvyPJXFkXbeL8WEi8EH37ZjOP5DMxI";
		//2.2 解密sm4密钥
		String encryptedCode = encCode;
		SM2 sm2 = SmUtil.sm2(privateKey, null);
		String sm4Key = new String(sm2.decrypt(Base64.decode(encryptedCode)));

		//2.3 解密报文
		SM4 sm4 = SmUtil.sm4(Base64.decode(sm4Key));
		byte[] bytes = sm4.decrypt(Base64.decode(encPublicKey));

		//类型转换
		Object list = JSON.parseObject(bytes, Map.class);
		return JSON.toJSONString(list);
	}

	/**
	 * url传递加密串测试
	 * @param username
	 * @param encryptedData
	 * @return
	 */
	@GetMapping("/getUrlTest")
	public String getUrlTest(String username, String encryptedData){
		log.info("username is ");
		log.info(username);
		log.info("encryptedData is");
		log.info(encryptedData);
		return encryptedData;
	}

	/**
	 * 测试最终位置点缓存
	 * @return
	 */
	@GetMapping("/testSaveCache")
	public R<Object> testSaveCache(){
		LastPosCache cache = new LastPosCache();
		cache.setTeState(1L);
		cache.setIoStatus(1L);
		cache.setDeviceUniqueId("123");
		cache.setValid(null);
		stringRedisTemplate.opsForValue().set("Loc:Rnss:"+cache.getDeviceUniqueId(), JSON.toJSONString(cache, SerializerFeature.WriteMapNullValue));
		return R.data(stringRedisTemplate.opsForValue().get("Loc:Rnss:"+cache.getDeviceUniqueId()));
	}


	/**
	 * 测试报文推送
	 * 该接口用于模拟接收数据
	 * @param message
	 * @return
	 */
	@PostMapping("/testReceive")
	public R<String> testReceiveSendedMessage(@RequestBody Message message){
		log.info("接收到位置平台推送的数据");
		log.info(JSON.toJSONString(message));
		return R.data(JSON.toJSONString(message));
	}

	/**
	 * 生成密钥
	 * @return
	 */
	@GetMapping("/testGenerateKey")
	public R<Map> testGenerateKey (){
		KeyPair pair = SecureUtil.generateKeyPair("SM2");
		byte[] privateKey = pair.getPrivate().getEncoded();
		byte[] publicKey = pair.getPublic().getEncoded();
		String privateKeyStr = Base64.encode(privateKey);
		String publicKeyStr = Base64.encode(publicKey);
		Map<String,String> map = new HashMap<>();
		map.put("privateKey",privateKeyStr);
		map.put("publickey", publicKeyStr);
		return R.data(map);
	}


	/**
	 * 获取加密报文
	 * @return
	 */
	@PostMapping("/testEncryptMessage")
	public R<Message> testEncryptMessage(@RequestBody MockEncryptRequest request){
		Object param = request.getMessage();
		String systemId = request.getSystemId();
		String serviceId = request.getServiceId();
		String privateKey = request.getPrivateKey();
		String data = JSON.toJSONString(param);
		Message message = null;
		try {
			Header header = new Header();
			header.setSerialNum(new Date().getTime()+"");
			header.setServiceId(serviceId);
			header.setSystemId(systemId);

			message = encryptDataWithPrivateKey(header, data, privateKey);
		}catch (Exception e){
			log.error("操作失败",e);
			return R.fail("操作失败，"+e.getMessage());
		}
		return R.data(message);
	}

	private Message encryptDataWithPrivateKey(Header header, String bodyJson, String privateKey) throws Exception{
		String systemId = header.getSystemId();
		//1.发送方随机产生一个SM4对称加密密钥并用此密钥对要发送的数据信息明文进
		//行加密（SM4算法），形成数据信息密文
		SecretKey sm4SecretKey = SmUtil.sm4().getSecretKey();
		String sm4Key = Base64.encode(sm4SecretKey.getEncoded());
		header.setSystemId(systemId);
		log.info("sm4Key is ");
		log.info(sm4Key);
		String encryptedBody = SmUtil.sm4(sm4SecretKey.getEncoded()).encryptBase64(bodyJson.getBytes());
		//2.发送方用接收方的公钥对SM4密钥进行加密(SM2算法)，生成加密密钥串
		//2.1 获取接收方平台公钥（位置平台）
		//查询位置平台公钥
		SmInterfaceSystem system = interfaceSystemService.getById(0);
		String thirdPublicKey = system.getPublicKey();
		//2.2 获取发送方平台私钥（公务车测试平台）
		//2.3 使用接收方公钥加密
		SM2 thirdSm2 = SmUtil.sm2(null, thirdPublicKey);
		String encryptedSm4Key = Base64.encode(thirdSm2.encrypt(sm4Key, KeyType.PublicKey));

		//3.发送方对数据信息密文计算信息摘要（SM3算法），然后对摘要用自己的私钥生
		//成数字签名(SM2算法)
		//3.1 生成摘要
		log.info("encryptedBody is ");
		log.info(encryptedBody);
		byte[] crc = SmUtil.sm3().digest(encryptedBody);
		log.info("crc is ");
		log.info(Base64.encode(crc));
		//3.2 生成数字签名
		SM2 sm2 = SmUtil.sm2(privateKey, null);
		String sign = Base64.encode(sm2.sign(crc));

		//4.发送方将数字签名、加密密钥串附着在报文头中，数据信息密文附着在报文体中，
		//组成报文发送给接收方
		Message message = new Message();
		header.setCrc(sign);
		header.setEncryptedCode(encryptedSm4Key);
		Body body = new Body();
		body.setEncryptedContent(encryptedBody.trim());
		message.setHeader(header);
		message.setBody(body);
		return message;
	}

	/**
	 * 测试报文推送
	 * 该接口用于模拟接收数据
	 * @param message
	 * @return
	 */
	@PostMapping("/testRecvMessage")
	public R<String> testReceive(@RequestBody Message message){
		log.info("接收到位置平台推送的数据");
		log.info(JSON.toJSONString(message));
		//将数据保存到redis中
		stringRedisTemplate.opsForValue().set("mock:message:recv",JSON.toJSONString(message));
		return R.data(JSON.toJSONString(message));
	}

	/**
	 * 查询模拟推送结果
	 * 该接口用于模拟接收数据
	 * @return
	 */
	@GetMapping("/checkRecvMessage")
	public R<String> checkReceive(){
		//将数据保存到redis中
		Object obj = stringRedisTemplate.opsForValue().get("mock:message:recv");
		return R.data(JSON.toJSONString(obj));
	}

}
