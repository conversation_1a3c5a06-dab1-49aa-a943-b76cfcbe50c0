/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.controller;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.interManager.entity.InPosInterface;
import com.xh.vdm.interManager.entity.InPosUserInterface;
import com.xh.vdm.interManager.service.IInPosInterfaceService;
import com.xh.vdm.interManager.service.IInPosUserInterfaceService;
import com.xh.vdm.interManager.service.IInPosUserService;
import com.xh.vdm.interManager.vo.request.InterfaceAuthRequest;
import com.xh.vdm.interManager.vo.request.InterfaceRequest;
import com.xh.vdm.interManager.vo.response.InterfaceResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.annotation.Log;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/interface")
@Slf4j
public class InPosInterfaceController {

	private IInPosInterfaceService inPosInterfaceService;

	private IInPosUserInterfaceService userInterfaceService;

	private IInPosUserService userService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<InterfaceResponse> detail(InPosInterface inPosInterface) {
		inPosInterface.setIsDel(0);
		InPosInterface detail = inPosInterfaceService.getOne(Condition.getQueryWrapper(inPosInterface));
		InterfaceResponse res = new InterfaceResponse();
		BeanUtils.copyProperties(detail, res);
		return R.data(res);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	public R<IPage<InPosInterface>> list(InPosInterface inPosInterface, Query query) {
		inPosInterface.setIsDel(0);
		String url = inPosInterface.getUrl();
		if(!StringUtils.isEmpty(url)){
			inPosInterface.setUrl(null);
		}
		String code = inPosInterface.getCode();
		inPosInterface.setCode(null);
		QueryWrapper<InPosInterface> wrapper = Condition.getQueryWrapper(inPosInterface);
		if(!StringUtils.isEmpty(url)){
			wrapper.like("url", "%"+url+"%");
		}
		if(!StringUtils.isEmpty(code)){
			wrapper.like("code","%"+code+"%");
		}
		wrapper.orderByDesc("create_time");
		IPage<InPosInterface> pages = inPosInterfaceService.page(Condition.getPage(query), wrapper);
		return R.data(pages);
	}


	/**
	 * 新增
	 */
	@Log(menu = "本平台接口管理", operation = Operation.INSERT, objectType = ObjectType.PLATFORM_INTERFACE)
	@PostMapping("/save")
	public R save(@Valid @RequestBody InPosInterface inPosInterface, BladeUser user) {
		String code = inPosInterface.getCode();
		if (StringUtils.isEmpty(code)) {
			log.error("code不能为空");
			return R.fail("接口编码不能为空");
		}
		//查看code是否已经存在
		List<InPosInterface> list = inPosInterfaceService.list(Wrappers.lambdaQuery(InPosInterface.class)
			.eq(InPosInterface::getCode, code)
			.eq(InPosInterface::getIsDel, 0));
		if (list != null && list.size() > 0) {
			return R.fail("接口[" + code + "]已经存在");
		}
		inPosInterface.setCreateTime(new Date());
		inPosInterface.setCreateUser(AuthUtil.getUserId());
		inPosInterface.setIsDel(0);
		try {
			boolean flag = inPosInterfaceService.save(inPosInterface);
			if (flag) {
				return R.data(ResultCode.SUCCESS.getCode(), inPosInterface.getId().toString(),"新增成功");
			}
			return R.fail("保存接口信息失败");
		} catch (Exception e) {
			log.error("保存接口信息失败", e);
			return R.fail("保存接口信息失败，" + e.getMessage());
		}
	}

	/**
	 * 修改
	 */
	@Log(menu = "本平台接口管理", operation = Operation.UPDATE, objectType = ObjectType.PLATFORM_INTERFACE)
	@PostMapping("/update")
	public R update(@Valid @RequestBody InPosInterface inPosInterface, BladeUser user) {
		InPosInterface posInterface = inPosInterfaceService.getById(inPosInterface.getId());

		inPosInterface.setUpdateTime(new Date());
		inPosInterface.setUpdateUser(AuthUtil.getUserId());
		boolean result = inPosInterfaceService.updateById(inPosInterface);
		if (result) {
			String compare = new CompareUtils<InPosInterface>().compare(posInterface, inPosInterface);
			return R.data(ResultCode.SUCCESS.getCode(), compare,"编辑成功");
		} else {
			return R.fail("修改接口信息失败");
		}
	}


	/**
	 * 删除
	 * 逻辑删除
	 */
	@Log(menu = "本平台接口管理", operation = Operation.DELETE, objectType = ObjectType.PLATFORM_INTERFACE)
	@GetMapping("/remove")
	public R remove(@RequestParam String ids, BladeUser user) {
		List<InPosInterface> list = inPosInterfaceService.listByIds(Func.toLongList(ids));
		list.forEach(item -> {
			item.setUpdateUser(AuthUtil.getUserId());
			item.setUpdateTime(new Date());
			item.setIsDel(1);
		});
		boolean result = inPosInterfaceService.updateBatchById(list);
		if (result) {
			return R.data(ids);
		} else {
			return R.fail("删除接口信息失败");
		}
	}


	/**
	 * 查询指定账号没有绑定的所有接口信息
	 *
	 * @return
	 */
	@GetMapping("/interfaces")
	public R<IPage<InterfaceResponse>> interfaces(InterfaceRequest request, Query query) {
		if(request == null || request.getUserId() == null){
			return R.fail("未指定用户id");
		}
		IPage<InterfaceResponse> resPage = inPosInterfaceService.findInterfacesUnBind(request, Condition.getPage(query));
		return R.data(resPage);
	}

	/**
	 * 查询账号关联的接口数据
	 * 接口权限
	 *
	 * @param request
	 * @return
	 */
	@GetMapping("/getInterfaceAuth")
	public R<List<InterfaceResponse>> findSelectedInterfacesByUserId(InterfaceRequest request) {
		List<InterfaceResponse> list = inPosInterfaceService.findInterfacesBind(request);
		return R.data(list);
	}


	/**
	 * 跟新用户绑定的接口信息
	 * @param type
	 * @param ids
	 * @param userId
	 * @return
	 */
	@GetMapping("/updateUserInterface")
	public R<String> updateUserInterface(String type, String ids, Long userId){
		try{
			if("add".equals(type)){
				//如果是新增
				List<InPosUserInterface> list = new ArrayList<>();
				for(Long id : Func.toLongList(ids)){
					InPosUserInterface inter = new InPosUserInterface();
					inter.setInterfaceId(id);
					inter.setUserId(userId);
					inter.setCreateUser(AuthUtil.getUserId());
					inter.setCreateTime(new Date());
					list.add(inter);
				}
				userInterfaceService.saveBatch(list);
			}else if("remove".equals(type)){
				//如果是移除
				userInterfaceService.remove(Wrappers.lambdaQuery(InPosUserInterface.class)
					.eq(InPosUserInterface::getUserId, userId)
					.in(InPosUserInterface::getInterfaceId, Func.toLongList(ids)));
			}
		}catch (Exception e){
			log.error("更新用户接口权限失败",e);
			return R.fail("更新用户接口权限失败");
		}
		return R.success("操作成功");
	}


}
