<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.LocationKuduMapper">

    <select id="getLatestLocation" resultType="com.xh.vdm.interManager.entity.LocationKudu">
        select * from locations where device_id = #{deviceId} and time > #{timestampBefore} order by time desc limit 1
    </select>
</mapper>
