<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.BdmAbstractDeviceMapper">

    <insert id="saveDevice">
        INSERT INTO bdm_abstract_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id and '' != id">
                id,
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                unique_id,
            </if>
            <if test="null != deviceType and '' != deviceType">
                device_type,
            </if>
            <if test="null != deptId and '' != deptId">
                dept_id,
            </if>
            <if test="null != category and '' != category">
                category,
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                device_num,
            </if>
            <if test="null != targetId and '' != targetId">
                target_id,
            </if>
            <if test="null != targetType and '' != targetType">
                target_type,
            </if>
            <if test="null != deleted and '' != deleted">
                deleted,
            </if>
            <if test="null != iotProtocol and '' != iotProtocol">
                iot_protocol,
            </if>
            <if test="null != specificity and '' != specificity">
                specificity
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id and '' != id">
                #{id},
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                #{uniqueId},
            </if>
            <if test="null != deviceType and '' != deviceType">
                #{deviceType},
            </if>
            <if test="null != deptId and '' != deptId">
                #{deptId},
            </if>
            <if test="null != category and '' != category">
                #{category},
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                #{deviceNum},
            </if>
            <if test="null != targetId and '' != targetId">
                #{targetId},
            </if>
            <if test="null != targetType and '' != targetType">
                #{targetType},
            </if>
            <if test="null != deleted and '' != deleted">
                #{deleted},
            </if>
            <if test="null != iotProtocol and '' != iotProtocol">
                #{iotProtocol},
            </if>
            <if test="null != specificity and '' != specificity">
                #{specificity}
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bdm_abstract_device
        <set>
            <if test="uniqueId != null and uniqueId != ''">
                unique_id = #{uniqueId},
            </if>
            <if test="deviceType != null">
                device_type = #{deviceType},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="category != null">
                category = #{category},
            </if>
            <if test="deviceNum != null and deviceNum != ''">
                device_num = #{deviceNum},
            </if>
            <if test="targetId != null">
                target_id = #{targetId},
            </if>
            <if test="targetType != null">
                target_type = #{targetType},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="iotProtocol != null">
                iot_protocol = #{iotProtocol}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键逻辑删除-->
    <update id="deleteByIds">
        update bdm_abstract_device
        set deleted = 1
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bdm_abstract_device(id,unique_id, device_type, dept_id, category, device_num, target_id, target_type,
         iot_protocol)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.uniqueId}, #{entity.deviceType}, #{entity.deptId}, #{entity.category}, #{entity.deviceNum},
            #{entity.targetId}, #{entity.targetType}, #{entity.iotProtocol})
        </foreach>
    </insert>

    <update id="unbinding">
        update bdm_abstract_device
        set target_id = 0, target_type = 0
        where target_id = #{id} and target_type = #{targetType}
    </update>

    <update id="updateBatch">
        update bdm_abstract_device
        set
            target_id = #{id},
            target_type = #{targetType},
            dept_id = #{deptId}
        where id in
        <if test="ids != null and ids.size() > 0">
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByTargetIds">
        <if test="ids != null and ids.length > 0">
            update bdm_abstract_device
            set target_id = 0, target_type = 0
            where target_type =  #{targetType}
            and target_id in
            <foreach collection="ids" item="id" index="i" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="bindFacility">
        update bdm_abstract_device
        set
            target_id = #{id},
            target_type = #{targetType},
            dept_id = #{deptId}
        where id in
        <if test="ids != null and ids.size() > 0">
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateDept">
        update bdm_abstract_device set dept_id = #{deptId} where target_id = #{id} and target_type = #{targetType}
    </update>

    <update id="updateBatchByUniqueId">
        update bdm_wearable_device
        set target_id = #{id} , target_type = 0
        where unique_id = #{number}
    </update>

    <select id="getListByUserRole" resultType="com.xh.vdm.biapi.entity.BdmAbstractDevice">
        select * from bdm_abstract_device bad
        WHERE bad.deleted = 0 and bad.target_id > 0
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and create_account = #{account}
        </if>
    </select>

    <select id="getBadByUniqueId" resultType="com.xh.vdm.biapi.entity.BdmAbstractDevice">
        select * from bdm_abstract_device bad
        WHERE bad.deleted = 0 and bad.unique_id = #{uniqueId}
    </select>

    <update id="bindAbstractTarget">
        update bdm_abstract_device
        set  target_id= #{request.targetId} , target_type = #{request.targetType},dept_id = #{request.deptId}
        where deleted = 0 and unique_id = #{request.uniqueId}
    </update>

</mapper>

