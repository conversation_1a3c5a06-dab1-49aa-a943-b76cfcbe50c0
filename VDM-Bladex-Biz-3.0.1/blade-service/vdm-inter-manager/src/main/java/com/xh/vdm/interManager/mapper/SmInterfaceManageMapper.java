package com.xh.vdm.interManager.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.entity.SmInterfaceManage;
import com.xh.vdm.interManager.vo.SmInterfaceManageVO;

import java.util.List;

/**
 * 接口管理表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
public interface SmInterfaceManageMapper extends BaseMapper<SmInterfaceManage> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param smInterfaceManage
	 * @return
	 */
	List<SmInterfaceManageVO> selectSmInterfaceManagePage(IPage page, SmInterfaceManageVO smInterfaceManage);

	/**
	 * 获取新的id
	 * @return
	 */
	long getNewId();

	/**
	 * 根据账户查询公钥信息
	 * @param account
	 * @return
	 */
	String getPublicKeyByAccount(String account);

}
