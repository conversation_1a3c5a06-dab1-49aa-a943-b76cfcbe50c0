package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.mapper.BdmTrainCargoBoxMapper;
import com.xh.vdm.interManager.service.IBdmTrainCargoBoxService;
import com.xh.vdm.biapi.entity.BdmTrainCargoBox;
import org.springframework.stereotype.Service;

/**
 * (BdmTrainCargoBox)表服务实现类
 */
@Service
public class BdmTrainCargoBoxServiceImpl extends ServiceImpl<BdmTrainCargoBoxMapper, BdmTrainCargoBox> implements IBdmTrainCargoBoxService {
}
