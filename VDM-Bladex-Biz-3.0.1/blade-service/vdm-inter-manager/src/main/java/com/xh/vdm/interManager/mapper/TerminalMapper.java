package com.xh.vdm.interManager.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.dto.message.TerminalInfo;
import com.xh.vdm.interManager.vo.InInterLogVO;
import org.springframework.context.annotation.Primary;

import java.util.List;

/**
 *  终端综合处理
 *
 * <AUTHOR>
 * @since 2024-06-04
 */
public interface TerminalMapper extends BaseMapper<TerminalInfo> {

	/**
	 * 根据赋码号查询已经存在的终端数量
	 * @param deviceNum
	 * @return
	 */
	long getDeviceCountByDeviceNum(String deviceNum);

	/**
	 * 根据imei号查询已经存在的终端数量
	 * @param imei
	 * @return
	 */
	long getDeviceCountByImei(String imei);

	/**
	 * 根据终端编号查询终端数量
	 * @param uniqueId
	 * @return
	 */
	long getDeviceCountByUniqueId(String uniqueId);


	/**
	 * 根据unique查询终端id
	 * @param uniqueId
	 * @return
	 */
	Long getIdByUniqueId(String uniqueId);
}
