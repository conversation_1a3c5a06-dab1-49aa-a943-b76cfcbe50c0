package com.xh.vdm.interManager.interceptor;

import com.xh.vdm.interManager.utils.CommonUtil;
import org.apache.cxf.binding.soap.SoapMessage;
import org.apache.cxf.headers.Header;
import org.apache.cxf.interceptor.Fault;
import org.apache.cxf.phase.AbstractPhaseInterceptor;
import org.apache.cxf.phase.Phase;
import org.w3c.dom.Element;

import javax.xml.namespace.QName;
import java.util.List;

/**
 * @Description: webservice 登录拦截
 * @Auther: zhouxw
 * @Date: 2019/11/1 10:31
 * @company：CTTIC
 */
public class LoginInterceptor extends AbstractPhaseInterceptor<SoapMessage> {
	private String username = "root"; //用户名
	private String password = "admin"; //密码
	private String authModel = "<SecurityHeader>\t<username>#username#</username>\t<password>#password#</password> \t</SecurityHeader>"; //认证模板，实际上是 Header 中的认证信息模板

	public LoginInterceptor(String username, String password, String authModel) {
		//设置在发送请求前阶段进行拦截
		super(Phase.PREPARE_SEND);
		this.username=username;
		this.password=password;
		this.authModel = authModel;
	}

	/*@Override
	public void handleMessage(SoapMessage soapMessage) throws Fault {
		List<Header> headers = soapMessage.getHeaders();
		Document doc = DOMUtils.createDocument();
		//Element auth = doc.createElementNS("http://cxf.wolfcode.cn/","SecurityHeader");
		Element auth = doc.createElement("SecurityHeader");
		//Element UserName = doc.createElement("username");
		//Element UserPass = doc.createElement("password");

		//替换模板中的用户名和密码
		this.authModel.replace("#username#" , this.username);
		this.authModel.replace("#password#" , this.password);
		auth.setTextContent(this.authModel);


		//UserName.setTextContent(username);
		//UserPass.setTextContent(password);

		//auth.appendChild(UserName);
		//auth.appendChild(UserPass);

		headers.add(0, new Header(new QName("SecurityHeader"),auth));
	}
*/


	@Override
	public void handleMessage(SoapMessage soapMessage) throws Fault {
		List<Header> headers = soapMessage.getHeaders();

		//String model = "<wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"><wsse:UsernameToken xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\" wsu:Id=\"UsernameToken-2\"><wsse:Username>#username#</wsse:Username><wsse:Password Type=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText\">#password#</wsse:Password></wsse:UsernameToken></wsse:Security>";
		String model = this.authModel;
		model = model.replaceAll("#username#" , this.username);
		model = model.replaceAll("#password#" , this.password);
		Element auth = CommonUtil.convertToElement(model);

		headers.add(0, new Header(new QName("Security"),auth));
	}



}
