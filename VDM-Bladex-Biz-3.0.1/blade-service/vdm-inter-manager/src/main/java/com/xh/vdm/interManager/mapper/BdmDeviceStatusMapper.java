package com.xh.vdm.interManager.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.interManager.entity.BdmDeviceStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.mp.support.Query;

import java.util.List;

/**
 * (BdmDeviceLink)表数据库访问层
 */
@Mapper
public interface BdmDeviceStatusMapper extends BaseMapper<BdmDeviceStatus> {

}

