/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.controller;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.interManager.entity.InPosService;
import com.xh.vdm.interManager.service.IInPosServiceService;
import com.xh.vdm.interManager.vo.response.ServiceResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.annotation.Log;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/service")
@Slf4j
public class InPosServiceController {

	private IInPosServiceService inPosServiceService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<ServiceResponse> detail(InPosService inPosService) {
		inPosService.setIsDel(0);
		InPosService detail = inPosServiceService.getOne(Condition.getQueryWrapper(inPosService));
		ServiceResponse service = new ServiceResponse();
		BeanUtils.copyProperties(detail, service);
		return R.data(service);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	public R<IPage<InPosService>> list(InPosService inPosService, Query query) {
		LambdaQueryWrapper<InPosService> wrapper = Wrappers.lambdaQuery(InPosService.class)
			.eq(InPosService::getIsDel, 0);
		if(!StringUtils.isEmpty(inPosService.getCode())){
			wrapper.like(InPosService::getCode, "%"+inPosService.getCode()+"%");
		}
		if(!StringUtils.isEmpty(inPosService.getName())){
			wrapper.like(InPosService::getName, "%"+inPosService.getName()+"%");
		}
		wrapper.orderByDesc(InPosService::getCreateTime);
		IPage<InPosService> pages = inPosServiceService.page(Condition.getPage(query), wrapper);
		return R.data(pages);
	}


	/**
	 * 查询所有业务服务，部分也
	 *
	 * @param inPosService
	 * @return
	 */
	@GetMapping("/listAll")
	public R<List<InPosService>> listAll(InPosService inPosService) {
		inPosService.setIsDel(0);
		List<InPosService> pages = inPosServiceService.list(Condition.getQueryWrapper(inPosService));
		return R.data(pages);
	}


	/**
	 * 新增
	 */
	@Log(menu = "业务服务管理", operation = Operation.INSERT, objectType = ObjectType.BUSINESS_SERVICE)
	@PostMapping("/save")
	public R save(@Valid @RequestBody InPosService inPosService, BladeUser user) {

		String code = inPosService.getCode();
		if (StringUtils.isEmpty(code)) {
			log.error("code不能为空");
			return R.fail("code不能为空");
		}

		//判断相同code的service是否存在
		List<InPosService> list = inPosServiceService.list(Wrappers.lambdaQuery(InPosService.class)
			.eq(InPosService::getCode, code).eq(InPosService::getIsDel, 0));
		if (list != null && list.size() > 0) {
			return R.fail("服务[" + code + "]已存在");
		}

		inPosService.setIsDel(0);
		inPosService.setCreateTime(new Date());
		inPosService.setCreateUser(AuthUtil.getUserId());
		try {
			boolean result = inPosServiceService.save(inPosService);
			if (result) {
				return R.data(ResultCode.SUCCESS.getCode(), inPosService.getId().toString(),"新增成功");
			}
			return R.fail("保存业务信息失败");
		} catch (Exception e) {
			log.error("保存业务信息失败", e);
			return R.fail("保存业务信息失败");
		}
	}

	/**
	 * 修改
	 */
	@Log(menu = "业务服务管理", operation = Operation.UPDATE, objectType = ObjectType.BUSINESS_SERVICE)
	@PostMapping("/update")
	public R update(@Valid @RequestBody InPosService inPosService, BladeUser user) {
		InPosService posService = inPosServiceService.getBaseMapper().selectById(inPosService.getId());
		inPosService.setUpdateTime(new Date());
		inPosService.setUpdateUser(AuthUtil.getUserId());
		boolean result = inPosServiceService.updateById(inPosService);
		if (result) {
			String compare = new CompareUtils<InPosService>().compare(posService, inPosService);
			return R.data(ResultCode.SUCCESS.getCode(), compare,"编辑成功");
		} else {
			return R.fail("修改业务信息失败");
		}
	}


	/**
	 * 删除
	 * 逻辑删除
	 */
	@Log(menu = "业务服务管理", operation = Operation.DELETE, objectType = ObjectType.BUSINESS_SERVICE)
	@GetMapping("/remove")
	public R remove(@RequestParam String ids, BladeUser user) {
		//逻辑删除
		List<Long> idList = Func.toLongList(ids);
		List<InPosService> list = inPosServiceService.listByIds(idList);
		list.forEach(item -> {
			item.setIsDel(1);
			item.setUpdateUser(AuthUtil.getUserId());
			item.setUpdateTime(new Date());
		});
		boolean result = inPosServiceService.updateBatchById(list);
		if (result) {
			return R.data(ids);
		} else {
			return R.fail("删除业务信息失败");
		}
	}


	/**
	 * 获取所有服务信息
	 *
	 * @return
	 */
	@GetMapping("/getAllService")
	public R getAllService() {
		try {
			List<InPosService> list = inPosServiceService.list(Wrappers.lambdaQuery(InPosService.class).eq(InPosService::getIsDel, 0));
			return R.data(list);
		} catch (Exception e) {
			return R.fail("查询服务列表失败");
		}
	}


}
