/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.interManager.entity.SmInterfaceManage;
import com.xh.vdm.interManager.vo.SmInterfaceManageVO;

/**
 * 接口管理表 服务类
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
public interface ISmInterfaceManageService extends IService<SmInterfaceManage> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param smInterfaceManage
	 * @return
	 */
	IPage<SmInterfaceManageVO> selectSmInterfaceManagePage(IPage<SmInterfaceManageVO> page, SmInterfaceManageVO smInterfaceManage);

	/**
	 * 根据interCode查找接口信息
	 * @param interCode
	 * @return
	 */
	SmInterfaceManage findInterfaceManageByInterCode(String interCode);

}
