package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.mapper.WorkerMapper;
import com.xh.vdm.interManager.service.IWorkerService;
import lombok.extern.slf4j.Slf4j;
import com.xh.vdm.biapi.entity.BdmWorker;
import org.springframework.stereotype.Service;

/**
 * (BdmWorker)表服务实现类
 */
@Service
@Slf4j
public class WorkerServiceImpl extends ServiceImpl<WorkerMapper, BdmWorker> implements IWorkerService {

}
