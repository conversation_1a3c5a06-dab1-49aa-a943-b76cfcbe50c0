package com.xh.vdm.interManager.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.interManager.dto.message.Header;
import com.xh.vdm.interManager.dto.message.Message;
import com.xh.vdm.interManager.entity.InInterLog;
import com.xh.vdm.interManager.service.IInInterLogService;
import com.xh.vdm.interManager.service.IMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;

@Slf4j
@RestController
@RequestMapping("/celoc")
public class MessageController {

	@Resource
	private IMessageService messageService;

	@Resource
	private IInInterLogService interLogService;

	/**
	 * 外部平台调用接口
	 * @param message
	 * @return
	 */
	@PostMapping("/collection")
	public R<Message> collection (@Validated @RequestBody Message message, HttpServletRequest request) {
		InInterLog interLog = new InInterLog();
		//接口方向：1 访问位置平台  2 访问外部平台
		interLog.setDirection(1);
		//接口编码：平台对接时，位置平台只对外提供一个HTTP接口，通过业务编码区分业务
		interLog.setInterCode(CommonConstant.VDM_PLAT_COLLECTION_INTER_CODE);
		//接口名称：平台对接时，位置平台接口只有一个，名称固定
		interLog.setInterName(CommonConstant.VDM_PLAT_COLLECTION_INTER_NAME);
		//接口类型：1 HTTP   2 MQTT
		interLog.setInterType("1");
		//请求接口的url
		interLog.setUrl("/celoc/collection");
		//接口参数：这里记录原始报文
		interLog.setParam(JSON.toJSONString(message));
		//调用时间
		Date callTime = new Date();
		interLog.setCallTime(callTime);
		Header header = message.getHeader();
		if(header != null){
			String systemId = header.getSystemId();
			interLog.setSystemId(Long.parseLong(systemId));
			//外部平台调用位置平台接口，这里服务id定为1
			interLog.setServiceId(1L);
			interLog.setServiceCode(header.getServiceId());
		}
		//调用结果：1 成功  0 失败
		int result = 1;
		long start = System.currentTimeMillis();
		try {
			Message messageRes = this.messageService.handleMessage(message);
			return R.data(messageRes);
		} catch (Exception e) {
			log.error("fail process request from secondary platform to gn platform: {}", e.getMessage(), e);
			result = 0;
			return R.fail(ResultCode.FAILURE, "平台协议，数据上报异常：" + e.getMessage());
		}finally {
			long end = System.currentTimeMillis();
			//调用耗时
			long costTime = end - start;
			interLog.setCallCost(costTime);
			//调用账户
			String callAccount = AuthUtil.getUserAccount();
			interLog.setCallAccount(callAccount);
			//请求方ip
			String ip = ServletUtil.getClientIP(request);
			interLog.setServerIp(ip);
			interLog.setResult(result);
			//保存日志
			interLogService.saveInterLogAsync(interLog);
		}
	}
}
