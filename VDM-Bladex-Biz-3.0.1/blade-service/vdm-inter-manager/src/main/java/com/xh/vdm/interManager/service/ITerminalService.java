package com.xh.vdm.interManager.service;

/**
 * 终端综合处理（包含5中终端）
 */
public interface ITerminalService {

	/**
	 * 根据赋码值判断终端是否存在
	 * @param deviceNum 赋码值
	 * @return true 已存在   false 不存在
	 */
	boolean checkExistByDeviceNum(String deviceNum);

	/**
	 * 根据赋码值获取终端数量
	 * @param deviceNum
	 * @return
	 */
	long findDeviceCountByDeviceNum(String deviceNum);

	/**
	 * 根据imei号判断终端是否存在
	 * @param imei
	 * @return true 已存在   false 不存在
	 */
	boolean checkExistByImei(String imei);

	/**
	 * 根据imei获取终端数量
	 * @param imei
	 * @return
	 */
	long findDeviceCountByImei(String imei);

	/**
	 * 根据终端编号判断终端是否已经存在
	 * @param uniqueId 终端编号，出厂序列号
	 * @return
	 */
	boolean checkExistByUniqueId(String uniqueId);

	/**
	 * 根据终端编号获取终端数量
	 * @param uniqueId
	 * @return
	 */
	long findDeviceCountByUniqueId(String uniqueId);

	/**
	 * 根据uniqueId查询终端在终端表中的主键id
	 * @param uniqueId
	 * @return
	 */
	long findIdByUniqueId(String uniqueId);

	/**
	 * 根据目标类型和id查询目标信息
	 * @param type
	 * @param id
	 * @return
	 */
	Object findTargetByTypeAndId(String type, Long id);


	/**
	 * 根据终端类型和id查询终端信息
	 * @param type
	 * @param id
	 * @return
	 */
	Object findDeviceByTypeAndId(String type, Long id);

}
