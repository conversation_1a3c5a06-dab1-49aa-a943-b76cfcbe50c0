package com.xh.vdm.interManager.consumer;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xh.vdm.biapi.entity.BdmRdssDevice;
import com.xh.vdm.interManager.dto.AlarmFromSecondary;
import com.xh.vdm.interManager.dto.message.*;
import com.xh.vdm.interManager.entity.Location;
import com.xh.vdm.interManager.mapper.ShortMessageLogMapper;
import com.xh.vdm.interManager.service.terminal.IRdssDeviceService;
import com.xh.vdm.interManager.service.terminal.IRnssDeviceService;
import com.xh.vdm.interManager.utils.SequenceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 数据同步：短报文、高精度系统数据同步
 * 说明：目前实际只有短报文终端的数据会通过直接kafka的方式传过来，高精度终端会通过808的方式直连位置平台
 */
@Component
@EnableKafka
@Slf4j
public class InnerSystemMessageSyncConsumer {
	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private SequenceUtil sequenceUtil;

	@Resource
	private IRnssDeviceService rnssDeviceService;

	@Resource
	private IRdssDeviceService rdssDeviceService;

	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private RedisTemplate<String, String> redisTemplate;
	@Resource
	private ShortMessageLogMapper shortMessageLogMapper;
	@Resource
	@Qualifier("locationKafkaTemplate")
	private KafkaTemplate locationKafkaTemplate;
	@Value("${message.kafka.to_alarm}")
	private String toAlarmTopic;
	@Value("${message.kafka.to_location}")
	private String toKafkaTopic;

	/**
	 * @description: 短报文、高精度数据同步：短报文、高精度将数据通过kafka的形式传到位置平台
	 **/
	@KafkaListener(containerFactory = "dataKafkaListenerContainerFactory", topics = {CommonConstant.MESSAGE_BUSI_SYNC_TOPIC})
	public void messageCollect(String messageData, Acknowledgment acknowledgment) {
		try {
			log.info("接收到短报文信息，数据为：");
			log.info(messageData);
			messageData = JSON.parse(messageData).toString();
			MessageDataContainer record = JSON.parseObject(messageData, MessageDataContainer.class);
			//1.数据类型 : 1设备台账、2状态数据、3位置数据、4报警数据、5业务数据、6短报⽂数据
			int type = record.getType();
			//操作类型 : 1添加、2修改、3删除
			int opt = record.getOpt();
			//数据内容
			Object data = record.getData();
			String dataStr = JSON.parse(data==null?"":data.toString()).toString();
			dataStr = JSON.parse(dataStr).toString();

			switch (type) {
				case 1: //设备台账
					break;
				case 2: //状态数据
					List<StatusData> statusDataList = JSON.parseArray(dataStr, StatusData.class);
					if (opt != 3) {
						this.statusDataUpload(statusDataList);
					} else {
						this.statusDataDelete(statusDataList);
					}
					break;
				case 3: //位置数据
					List<LocData> locDataList = JSON.parseArray(dataStr, LocData.class);
					if (opt != 3) {
						this.locDataUpload(locDataList);
					} else {
						this.locDataDelete(locDataList);
					}
					break;
				case 4: //报警数据
					List<AlarmData> alarmDataList = JSON.parseArray(dataStr, AlarmData.class);
					if (opt != 3) {
						this.alarmDataUpload(alarmDataList);
					} else {
						// todo 删除缓存
					}
					break;
				case 5: //业务数据
					break;
				case 6: //短报⽂数据
					// modify@zhouxw 2024-11-21 经与国能伟东确认，暂不处理短报文消息
					/*List<RdssData> rdssDataList = JSON.parseArray(dataStr, RdssData.class);
					if (opt == 1) {
						for (RdssData rdssData : rdssDataList) {
							ShortMessageLog log = new ShortMessageLog();
							log.setId(Long.valueOf(formLocationId()));
							log.setSender(rdssData.getFromCardNo());
							log.setReceiver(rdssData.getToCardNo());
							log.setDeptId(Long.parseLong(rdssData.getOrgCode()));
							log.setLongitude(rdssData.getLongitude());
							log.setLatitude(rdssData.getLatitude());
							log.setAltitude(rdssData.getHeight()==null?0:rdssData.getHeight().intValue());
							log.setSpeed(rdssData.getSpeed()==null?0:rdssData.getSpeed().floatValue());
							log.setBearing(rdssData.getDirection()==null?0:rdssData.getDirection().shortValue());
							log.setTime(rdssData.getTime());
							log.setContent(rdssData.getContent());
							shortMessageLogMapper.insert(log);
						}
					}*/
					break;
				default:
					break;
			}
			// 手动提交offset
			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("调用外部数据同步接口消费失败 :" + e.getMessage(), e);
		}
	}

	/**
	 *  1添加、2修改 状态数据
	 *
	 * @param statusDataList
	 * @throws Exception
	 */
	private void statusDataUpload(List<StatusData> statusDataList) throws Exception {
		for (StatusData tmp : statusDataList) {
			QueryWrapper<BdmRdssDevice> wrapper = new QueryWrapper<>();
			wrapper.eq("imei", tmp.getImei());
			wrapper.eq("deleted", 0);
			List<BdmRdssDevice> rdssDevices = rdssDeviceService.getBaseMapper().selectList(wrapper);

			if (rdssDevices.isEmpty()) {
				log.error("未找到该终端！");
				continue;
			}
			if (rdssDevices.size() > 1) {
				log.error("不存在位置数据!");
				continue;
			}
			BdmRdssDevice rdssDevice = rdssDevices.get(0);
			this.redisTemplate.opsForHash().put(CommonConstant.REDIS_HASH_DEVICE_STATE, rdssDevice.getDeviceNum(), JSON.toJSONString(tmp));
		}
	}

	/**
	 *  3删除 状态数据
	 *
	 * @param statusDataList
	 * @throws Exception
	 */
	private void statusDataDelete(List<StatusData> statusDataList) throws Exception {
		for (StatusData tmp : statusDataList) {
			QueryWrapper<BdmRdssDevice> wrapper = new QueryWrapper<>();
			wrapper.eq("imei", tmp.getImei());
			wrapper.eq("deleted", 0);
			List<BdmRdssDevice> rdssDevices = rdssDeviceService.getBaseMapper().selectList(wrapper);

			if (rdssDevices.isEmpty()) {
				log.error("未找到该终端！");
				continue;
			}
			if (rdssDevices.size() > 1) {
				log.error("不存在位置数据!");
				continue;
			}
			BdmRdssDevice rdssDevice = rdssDevices.get(0);
			this.redisTemplate.opsForHash().delete(CommonConstant.REDIS_HASH_DEVICE_STATE , rdssDevice.getDeviceNum());
		}
	}

	/**
	 * 1添加、2修改 位置数据
	 *
	 * @param locDataList
	 * @throws Exception
	 */
	private void locDataUpload(List<LocData> locDataList) throws Exception {
		//保存数据
		for (LocData u : locDataList) {

			QueryWrapper<BdmRdssDevice> wrapper = new QueryWrapper<>();
			wrapper.eq("imei", u.getImei());
			wrapper.eq("deleted", 0);
			List<BdmRdssDevice> rdssDevices = rdssDeviceService.getBaseMapper().selectList(wrapper);

			if (rdssDevices.isEmpty()) {
				log.error("未找到该终端！");
				continue;
			}
			if (rdssDevices.size() > 1) {
				log.error("不存在位置数据!");
				continue;
			}
			BdmRdssDevice rdssDevice = rdssDevices.get(0);

			Location loc = new Location();
			//id
			loc.setId(formLocationId());
			//经度
			if (u.getLongitude() != null) {
				loc.setLongitude(u.getLongitude());
			}
			//纬度
			if (u.getLatitude() != null) {
				loc.setLatitude(u.getLatitude());
			}
			//海拔
			if (u.getHeight() != null) {
				loc.setAltitude(u.getHeight());
			}
			//速度
			if (u.getSpeed() != null) {
				loc.setSpeed(u.getSpeed());
			}
			//方向
			if (u.getDirection() != null) {
				loc.setBearing(u.getDirection().shortValue());
			}
			//定位时间
			if (u.getTime() != null) {
				loc.setTime(u.getTime());
			}
			//终端类型
			if (rdssDevice.getDeviceType() != null) {
				loc.setDeviceType(rdssDevice.getDeviceType().byteValue());
			}
			//终端id
			loc.setDeviceId(rdssDevice.getId());
			//赋码号
			if (!StringUtils.isEmpty(rdssDevice.getDeviceNum())) {
				loc.setDeviceNum(rdssDevice.getDeviceNum());
			}
			//平台接收时间
			loc.setRecvTime(System.currentTimeMillis() / 1000);
			//数据有效性 0-无效，1-有效
			loc.setValid((byte) 1);
			//是否纠正 0-未纠正，1-纠正点
			loc.setCorrection((byte) 0);
			//是否批量上传 0-正常上传，1-批量上传，2-补传
			loc.setBatch((byte) 0);
			//定位系统：0-未知，1-卫星，2-网络基站，3-WIFI，4-蓝牙，5-惯导
			loc.setPosSystem((byte) 0);
			//终端状态（默认为0）
			loc.setStatus(0);
			//报警状态
			loc.setAlarm(0);
			//附加信息（此处保存完整报文）
			loc.setAuxiliary(JSON.toJSONString(u));
			//将位置上报数据改造后，上传到位置kafka中
			locationKafkaTemplate.send(toKafkaTopic, loc);

			//位置点记录到redis中，作为最终位置点
			LastPosCache cache = new LastPosCache();
			cache.setDeviceUniqueId(rdssDevice.getUniqueId());
			cache.setDeviceId(rdssDevice.getId());
			cache.setDeviceModel(rdssDevice.getModel());
			cache.setDeviceNo(rdssDevice.getUniqueId());
			cache.setTargetId(rdssDevice.getTargetId());
			cache.setTargetType(rdssDevice.getTargetType());
			cache.setTargetName(rdssDevice.getTargetName());
			cache.setDeviceNum(rdssDevice.getDeviceNum());
			cache.setDeptId(rdssDevice.getDeptId());
			cache.setLongitude(u.getLongitude());
			cache.setLatitude(u.getLatitude());
			cache.setSpeed(u.getSpeed());
			cache.setBearing(u.getDirection());
			cache.setAlarmFlag(0L);
			cache.setStateFlag(0L);
			cache.setLocTime(u.getTime() / 1000);
			cache.setRecvTime(System.currentTimeMillis() / 1000);
			cache.setValid(1);
			cache.setMileage(0D);
			cache.setGnssNum(0);
			cache.setWireless(0);
			cache.setRealSpeed(0D);
			cache.setExpandSignal(0L);
			cache.setIoStatus(0L);
			cache.setTemperature("");
			cache.setBatch(0L);
			cache.setAuxStr("");
			cache.setAuxsNormal(null);
			cache.setDsmUniqueId("");
			//默认设置为离线，终端上线时，应修改该值
			cache.setTeState(0L);
			cache.setLocTimeF(DateUtil.sdfHolder.get().format(new Date(u.getTime())));
			cache.setRecvTimeF(DateUtil.sdfHolder.get().format(new Date()));
			cache.setOffLineTime("");

			stringRedisTemplate.opsForHash().put(CommonConstant.REDIS_CACHE_LAST_POST , rdssDevice.getId() , JSON.toJSONString(cache));
		}

	}

	/**
	 * 3删除 位置数据
	 *
	 * @param locDataList
	 * @throws Exception
	 */
	private void locDataDelete(List<LocData> locDataList) throws Exception {
		//保存数据
		for (LocData u : locDataList) {

			QueryWrapper<BdmRdssDevice> wrapper = new QueryWrapper<>();
			wrapper.eq("imei", u.getImei());
			wrapper.eq("deleted", 0);
			List<BdmRdssDevice> rdssDevices = rdssDeviceService.getBaseMapper().selectList(wrapper);

			if (rdssDevices.isEmpty()) {
				log.error("未找到该终端！");
				continue;
			}
			if (rdssDevices.size() > 1) {
				log.error("不存在位置数据!");
				continue;
			}
			BdmRdssDevice rdssDevice = rdssDevices.get(0);

			Location loc = new Location();
			//id
			loc.setId(formLocationId());
			//经度
			if (u.getLongitude() != null) {
				loc.setLongitude(u.getLongitude());
			}
			//纬度
			if (u.getLatitude() != null) {
				loc.setLatitude(u.getLatitude());
			}
			//海拔
			if (u.getHeight() != null) {
				loc.setAltitude(u.getHeight());
			}
			//速度
			if (u.getSpeed() != null) {
				loc.setSpeed(u.getSpeed());
			}
			//方向
			if (u.getDirection() != null) {
				loc.setBearing(u.getDirection().shortValue());
			}
			//定位时间
			if (u.getTime() != null) {
				loc.setTime(u.getTime());
			}
			//终端类型
			if (rdssDevice.getDeviceType() != null) {
				loc.setDeviceType(rdssDevice.getDeviceType().byteValue());
			}
			//终端id
			loc.setDeviceId(rdssDevice.getId());
			//赋码号
			if (!StringUtils.isEmpty(rdssDevice.getDeviceNum())) {
				loc.setDeviceNum(rdssDevice.getDeviceNum());
			}
			//平台接收时间
			loc.setRecvTime(System.currentTimeMillis() / 1000);
			//数据有效性 0-无效，1-有效
			loc.setValid((byte) 1);
			//是否纠正 0-未纠正，1-纠正点
			loc.setCorrection((byte) 0);
			//是否批量上传 0-正常上传，1-批量上传，2-补传
			loc.setBatch((byte) 0);
			//定位系统：0-未知，1-卫星，2-网络基站，3-WIFI，4-蓝牙，5-惯导
			loc.setPosSystem((byte) 0);
			//终端状态（默认为0）
			loc.setStatus(0);
			//报警状态
			loc.setAlarm(0);
			//附加信息（此处保存完整报文）
			loc.setAuxiliary(JSON.toJSONString(u));
			//将位置上报数据改造后，上传到位置kafka中
			locationKafkaTemplate.send(toKafkaTopic, loc);

			stringRedisTemplate.opsForHash().delete(CommonConstant.REDIS_CACHE_LAST_POST, loc.getDeviceId());
		}

	}

	private String formLocationId() {
		try {
			return String.valueOf(this.sequenceUtil.nextId());
		} catch (Exception e) {
			log.error("fail form location id: {}", e.getMessage(), e);
			return String.valueOf(UUID.randomUUID());
		}
	}

	/**
	 * 告警数据
	 *
	 * @param alarmDataList
	 * @throws Exception
	 */
	private void alarmDataUpload(List<AlarmData> alarmDataList) throws Exception {

		for (AlarmData tmp : alarmDataList) {
			QueryWrapper<BdmRdssDevice> wrapper = new QueryWrapper<>();
			wrapper.eq("imei", tmp.getImei());
			wrapper.eq("deleted", 0);
			List<BdmRdssDevice> rdssDevices = rdssDeviceService.getBaseMapper().selectList(wrapper);

			if (rdssDevices.isEmpty()) {
				log.error("未找到该终端！");
				continue;
			}
			if (rdssDevices.size() > 1) {
				log.error("终端重复，imei为{}", tmp.getImei());
				continue;
			}
			BdmRdssDevice rdssDevice = rdssDevices.get(0);

			AlarmFromSecondary alarm = new AlarmFromSecondary();
			long targetId = rdssDevice.getTargetId();
			long deviceId = rdssDevice.getId();
			BeanUtils.copyProperties(tmp, alarm);
			alarm.setDeptId(rdssDevice.getDeptId());
			alarm.setTargetType(rdssDevice.getTargetType().byteValue());
			alarm.setTargetId(targetId);
			alarm.setDeviceType(rdssDevice.getDeviceType().byteValue());
			alarm.setDeviceId(deviceId);
			//alarm.setAlarmType(Short.valueOf(tmp.getAlarmType()));
			//经与国能确认，短报文发过来的告警只有SOS，这里转换为紧急告警
			alarm.setAlarmType((short)81);
			alarm.setAlarmLevel((byte)5);
			alarm.setTime(tmp.getTime() / 1000);
			this.kafkaTemplate.send(this.toAlarmTopic, JSON.toJSONString(alarm));
		}
	}


}
