package com.xh.vdm.interManager.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.interManager.entity.SmInterfaceSystem;
import com.xh.vdm.interManager.vo.SmInterfaceSystemVO;

import java.util.List;

/**
 * 接口管理系统表 服务类
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
public interface ISmInterfaceSystemService extends IService<SmInterfaceSystem> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param smInterfaceSystem
	 * @return
	 */
	IPage<SmInterfaceSystemVO> selectSmInterfaceSystemPage(IPage<SmInterfaceSystemVO> page, SmInterfaceSystemVO smInterfaceSystem);

	/**
	 * 根据用户名查询系统公钥信息（位置平台为外部第三方平台分配的公钥）
	 * @param account
	 * @return
	 */
	List<String> findPublicKeyByAccount(String account);

	/**
	 * 根据给定的id列表查询存在的系统信息
	 * @param ids
	 * @return
	 */
	List<SmInterfaceSystem> findExistSystem(String ids);

}
