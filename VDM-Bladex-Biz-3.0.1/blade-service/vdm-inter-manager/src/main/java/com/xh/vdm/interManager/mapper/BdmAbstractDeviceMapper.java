package com.xh.vdm.interManager.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import com.xh.vdm.biapi.entity.BdmVirtualTarget;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmAbstractDevice)表数据库访问层
 */
public interface BdmAbstractDeviceMapper extends BaseMapper<BdmAbstractDevice> {


    void deleteByIds(@Param("ids") Long[] ids);

	void insertBatch(@Param("entities") List<BdmAbstractDevice> abstractDeviceList);

	void saveDevice(BdmAbstractDevice abstractDevice);

	void unbinding(@Param("id") Long id, @Param("targetType") Integer targetType);

	void updateBatch(@Param("ids") List<Long> ids, @Param("id") Long id, @Param("targetType") Integer targetType, @Param("targetName") String targetName, @Param("deptId") Long deptId);

	void deleteByTargetIds(@Param("ids") Long[] ids, @Param("targetType") Integer targetType);

	void bindFacility(@Param("ids") List<Long> ids,@Param("id") Long id, @Param("targetType") Integer targetType, @Param("deptId") Long deptId);

	void updateDept(@Param("id") Long id, @Param("targetType") Integer targetType, @Param("deptId") Long deptId);


    void updateBatchByUniqueId(BdmVirtualTarget virtualTarget);

	List<BdmAbstractDevice> getListByUserRole(@Param("account") String account, @Param("deptIds") String deptIds);

	BdmAbstractDevice getBadByUniqueId(@Param("uniqueId") String uniqueId);

}

