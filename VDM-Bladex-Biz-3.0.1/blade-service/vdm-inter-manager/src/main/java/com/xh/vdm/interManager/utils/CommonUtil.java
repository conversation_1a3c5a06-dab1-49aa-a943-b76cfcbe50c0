package com.xh.vdm.interManager.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.jdom.input.SAXBuilder;
import org.jdom.output.DOMOutputter;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: TODO
 * @Auther: zxing
 * @Date: 2021/1/28 09:30
 * @company：CTTIC
 */

@Component
@Slf4j
public class CommonUtil {

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddhhmmss");

    /**
     * @Description 判断数组中是否含有某个元素
     * <AUTHOR>
     * @Date 2020/11/26 19:53
     * @Company CTTIC
     * @Param [arr, str]
     * @return boolean
     **/
    public static boolean containItem(String[] arr , String str){
        return Arrays.asList(arr).contains(str);
    }


    /**
     * @Description 将对象转换为map
     * <AUTHOR>
     * @Date 2020/7/11 21:06
     * @Company CTTIC
     * @Param [obj]
     * @return java.util.Map<java.lang.String,java.lang.String>
     **/
    public static Map<String,Object> ObjectToMap(Object obj){
        return BeanUtils.beanToMap(obj);
    }

    /**
     * @Description 获取指定层级下的属性值，如 keyHier 设置为 data:token ，可以获取 data节点下 token 属性的值
     * 注意：如果节点是数组，则值获取  第一个   数组成员中相应属性的值
     * {
     * 		"status": true,
     * 		"code": 200,
     * 		"message": "登陆成功",
     * 		"data": {
     * 				"token": "wRXMV0nK+HVBCddAAzH3Y1SCV4l3r9+l7yR6zFRQs+kIJ0jlw4djL/vvu3JX84xzsHfCJzLNgqHMHtGRa0h4+oNUeNcWuo/O+HhdLNQ9BYQiOpPNl+U6LVJn4LsSndaCWFcMjLMKTf53fWCr8Z8Ea0SD7i1fDgqDV9u5pypo9zthpSy0yK/VL7p4LNQ20KXwA6Nu1pfuXuZ9QGGhuCiuKlirC+OKYIMTGoMYCa2R+huei8Yp9/LSAx1pZawRmm1tJdywc3uSmgVFbALXVJkaEFbVRrL30ogKH7iZj6AXOFX+Ed7JszjBtATIPHuVUmQEWgax0eKGp9b2fwWw7i1DyhKyeewZmqO24F5eKWXBLjsmUJcpzuQqaib03yXDnIsDTgiZXE3h9GI="
     *       }
     *}
     * <AUTHOR>
     * @Date 2020/7/13 10:46
     * @Company CTTIC
     * @Param [jsonObject, keyHier]
     * @return java.lang.Object
     **/
    public static Object getValueFromJsonHier(JSONObject jsonObject, String keyHier){
        if(StringUtils.isEmpty(keyHier) || jsonObject == null){
            return null;
        }
        if(keyHier.indexOf(":") < 0){
            return jsonObject.get(keyHier);
        }
        String keyO = keyHier.split(":")[0];
        if(jsonObject.get(keyO) instanceof JSONArray){
            //如果下一个节点是 JSONArray
            return getValueFromJsonHier(JSONObject.parseObject(JSONObject.toJSONString(JSONArray.parseArray(JSONArray.toJSONString(jsonObject.get(keyO))).get(0))),keyHier.substring(keyHier.indexOf(":")+1));
        }
        return getValueFromJsonHier(JSONObject.parseObject(JSONObject.toJSONString(jsonObject.get(keyO))),keyHier.substring(keyHier.indexOf(":")+1));
    }

    /**
     * @Description 获取指定层级下的属性值，如 keyHier 设置为 data:token ，可以获取 data节点下 token 属性的值
     * 	注意：如果节点是数组，则值获取   第一个   数组成员中相应属性的值
     * 	如果调用方是字符串，这时，可以通过截取 [] 或者 {} 来获取 标准json字符串:detectS.indexOf("[");    detectS.lastIndexOf("]");
     * {
     * 		"status": true,
     * 		"code": 200,
     * 		"message": "登陆成功",
     * 		"data": {
     * 				"token": "wRXMV0nK+HVBCddAAzH3Y1SCV4l3r9+l7yR6zFRQs+kIJ0jlw4djL/vvu3JX84xzsHfCJzLNgqHMHtGRa0h4+oNUeNcWuo/O+HhdLNQ9BYQiOpPNl+U6LVJn4LsSndaCWFcMjLMKTf53fWCr8Z8Ea0SD7i1fDgqDV9u5pypo9zthpSy0yK/VL7p4LNQ20KXwA6Nu1pfuXuZ9QGGhuCiuKlirC+OKYIMTGoMYCa2R+huei8Yp9/LSAx1pZawRmm1tJdywc3uSmgVFbALXVJkaEFbVRrL30ogKH7iZj6AXOFX+Ed7JszjBtATIPHuVUmQEWgax0eKGp9b2fwWw7i1DyhKyeewZmqO24F5eKWXBLjsmUJcpzuQqaib03yXDnIsDTgiZXE3h9GI="
     *       }
     *}
     * <AUTHOR>
     * @Date 2020/7/13 10:46
     * @Company CTTIC
     * @Param [jsonObject, keyHier]
     * @return java.lang.Object
     **/
    public static Object getValueFromJsonHier(Object obj,String keyHier){
        JSONObject jsonObject = null;
        try {
            //如果是 [ 开头，表示是数组，则拼上 data，构成对象
            String jsonStr = JSONObject.toJSONString(obj);
            if(StringUtils.isEmpty(jsonStr)){
                return "";
            }
            if(jsonStr.startsWith("[")){
                JSONArray array = JSONArray.parseArray(jsonStr);
                jsonObject =  (JSONObject) array.get(0);
            }else{
                jsonObject = JSONObject.parseObject(jsonStr);
            }
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
        if(StringUtils.isEmpty(keyHier) || jsonObject == null){
            return null;
        }
        if(keyHier.indexOf(":") < 0){
            return jsonObject.get(keyHier);
        }
        String keyO = keyHier.split(":")[0];
        if(jsonObject.get(keyO) instanceof JSONArray){
            //如果下一个节点是 JSONArray
            if(JSONArray.parseArray(JSONArray.toJSONString(jsonObject.get(keyO))).size() < 1){
                return null;
            }
            return getValueFromJsonHier(JSONObject.parseObject(JSONObject.toJSONString(JSONArray.parseArray(JSONArray.toJSONString(jsonObject.get(keyO))).get(0))),keyHier.substring(keyHier.indexOf(":")+1));
        }
        return getValueFromJsonHier(JSONObject.parseObject(JSONObject.toJSONString(jsonObject.get(keyO))),keyHier.substring(keyHier.indexOf(":")+1));
    }

    /**
       * @Description xml字符串转node
    	* <AUTHOR>
    	* @Date 2021/9/9 11:36
    	* @Company CTTIC
    	* @Param [str]
    	* @return org.w3c.dom.Document
    	**/

    public static Document StringTOXml(String str) {

        StringBuilder sXML = new StringBuilder();
        sXML.append(str);
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        Document doc = null;
        try {
            InputStream is = new ByteArrayInputStream(sXML.toString().getBytes("utf-8"));
            doc = dbf.newDocumentBuilder().parse(is);
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return doc;
    }


    public static Element convertToElement(String xmlStr) {
        String content = xmlStr;
        ByteArrayInputStream tInputStringStream = null;
        Element element = null;
        try {
            tInputStringStream = new ByteArrayInputStream(content.getBytes());
            SAXBuilder builder = new SAXBuilder(false);
            DOMOutputter outputter = new DOMOutputter();
            Document doc =  outputter.output(builder.build(tInputStringStream));
            element = doc.getDocumentElement();
        } catch (Exception jdome) {
            jdome.printStackTrace();
        } finally {
            if (tInputStringStream != null) {
                try {
                    tInputStringStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return element;
    }


    /**
     * @Description 根据层级获取json中的属性值，并进行类型的转换
     * 	支持 属性值为 对象 或者 数组
     * <AUTHOR>
     * @Date 2020/7/14 8:57
     * @Company CTTIC
     * @Param [obj, keyHier, clazz]
     * @return java.util.List
     **/
    public static List getPropertyFromJsonHier(Object obj, String keyHier, Class clazz){
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(obj));
        List list = new ArrayList();
        if(StringUtils.isEmpty(keyHier) || jsonObject == null){
            return null;
        }
        if(keyHier.indexOf(":") < 0){
            if(jsonObject.get(keyHier) instanceof JSONArray){
                //如果是JSONArray，则返回数组
                list =  JSONArray.parseArray(JSONArray.toJSONString(jsonObject.get(keyHier)),clazz);
            }else{
                //如果不是JSONArray，则包装成 list 返回
                Object object = JSONObject.parseObject(JSONObject.toJSONString(jsonObject.get(keyHier)),clazz);
                list.add(object);
            }
            return list;
        }
        String keyO = keyHier.split(":")[0];
        return getPropertyFromJsonHier(JSONObject.parseObject(JSONObject.toJSONString(jsonObject.get(keyO))),keyHier.substring(keyHier.indexOf(":")+1),clazz);
    }

    /**
     * @Description 根据层级获取json中的属性值，不进行类型的转换
     * 	支持 属性值为 对象 或者 数组
     * <AUTHOR>
     * @Date 2020/7/14 8:57
     * @Company CTTIC
     * @Param [obj, keyHier, clazz]
     * @return java.util.List
     **/
    public static List<Object> getPropertyFromJsonHier(Object obj, String keyHier){
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(obj));
        List<Object> list = new ArrayList();
        if(StringUtils.isEmpty(keyHier) || jsonObject == null){
            return null;
        }
        if(keyHier.indexOf(":") < 0){
            if(jsonObject.get(keyHier) instanceof JSONArray){
                //如果是JSONArray，则返回数组
                list =  JSONArray.parseArray(JSONArray.toJSONString(jsonObject.get(keyHier)));
            }else{
                //如果不是JSONArray，则包装成 list 返回
                Object object = JSONObject.parseObject(JSONObject.toJSONString(jsonObject.get(keyHier)));
                list.add(object);
            }
            return list;
        }
        String keyO = keyHier.split(":")[0];
        return getPropertyFromJsonHier(JSONObject.parseObject(JSONObject.toJSONString(jsonObject.get(keyO))),keyHier.substring(keyHier.indexOf(":")+1));
    }



    /**
       * @Description 格式化日期字符串(2019-12-18T17:10:36.3508582+08:00  -> 20191219T011036.3508582)
        * 给定UTC+8的日期字符串，然后格式化为 yyyyMMddHHmmss 格式的日期字符串(加8小时)
    	* <AUTHOR>
    	* @Date 2021/2/25 17:34
    	* @Company CTTIC
    	* @Param [dateTime]
    	* @return java.lang.String
    	**/
    public String formatDateTimeWithDot(String dateTime){
        if(StringUtils.isEmpty(dateTime)){
            return null;
        }
        String checkDate = dateTime.split("\\.")[0];
        checkDate = checkDate.replace("T","").replace("-","").replace(":","");
        try{
            Date tmp = sdf.parse(checkDate);
            tmp.setTime(tmp.getTime()+8*3600*1000);
            checkDate = sdf.format(tmp);
        }catch(Exception e){
            e.printStackTrace();
            log.error("[formatDateTime]格式化日期格式出错："+e.getMessage());
            checkDate = null;
        }
        return checkDate;
    }

    /**
       * @Description 字符串(以,分隔)转换为String List
    	* <AUTHOR>
    	* @Date 2022/1/30 14:47
    	* @Company CTTIC
    	* @Param [listString]
    	* @return java.util.List<java.lang.Object>
    	**/
    public static List<String> toStringList(String listString){
        if(StringUtils.isEmpty(listString)){
            return null;
        }
        String[] objs = listString.split(",");
        List<String> list = Arrays.asList(objs);
        return list;
    }

    /**
     * @Description 字符串(以,分隔)转换为Long List
     * <AUTHOR>
     * @Date 2022/1/30 14:47
     * @Company CTTIC
     * @Param [listString]
     * @return java.util.List<java.lang.Object>
     **/
    public static List<Long> toLongList(String listString){
        if(StringUtils.isEmpty(listString)){
            return null;
        }
        String[] objs = listString.split(",");
        List<Long> list = Arrays.stream(objs).map((s) -> Long.parseLong(s)).collect(Collectors.toList());
        return list;
    }

}
