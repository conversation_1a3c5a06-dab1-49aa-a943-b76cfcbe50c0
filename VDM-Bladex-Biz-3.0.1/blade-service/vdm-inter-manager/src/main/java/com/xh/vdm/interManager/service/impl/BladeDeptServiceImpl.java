package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.BladeDept;
import com.xh.vdm.interManager.mapper.BladeDeptMapper;
import com.xh.vdm.interManager.service.IBladeDeptService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@Service
public class BladeDeptServiceImpl extends ServiceImpl<BladeDeptMapper, BladeDept> implements IBladeDeptService {

	@Override
	public List<BladeDept> findExistDept(String deptIdStr) {
		//对参数进行处理：拼接 '{}'，因为自己拼接了标识符，不会产生sql注入
		String param = "'{" + deptIdStr + "}'";
		return baseMapper.getExistDept(param);
	}
}
