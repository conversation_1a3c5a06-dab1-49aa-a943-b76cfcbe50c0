package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.BdmContainer;
import com.xh.vdm.interManager.mapper.BdmContainerMapper;
import com.xh.vdm.interManager.service.IBdmContainerService;
import org.springframework.stereotype.Service;

@Service
public class BdmContainerServiceImpl extends ServiceImpl<BdmContainerMapper, BdmContainer> implements IBdmContainerService {
}
