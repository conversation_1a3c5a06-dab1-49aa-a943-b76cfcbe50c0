package com.xh.vdm.interManager.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.interManager.config.TargetSnowflakeIdWorker;
import com.xh.vdm.interManager.constant.TargetTypeEnum;
import com.xh.vdm.interManager.dto.AlarmFromSecondary;
import com.xh.vdm.interManager.dto.DeviceInfo;
import com.xh.vdm.interManager.dto.message.*;
import com.xh.vdm.interManager.entity.*;
import com.xh.vdm.interManager.entity.BdmContainer;
import com.xh.vdm.interManager.entity.BdmDeviceStatus;
import com.xh.vdm.interManager.entity.BdmTemporary;
import com.xh.vdm.interManager.entity.Location;
import com.xh.vdm.interManager.service.*;
import com.xh.vdm.interManager.service.terminal.*;
import com.xh.vdm.interManager.utils.CoordinateUtil;
import com.xh.vdm.interManager.utils.MessageUtils;
import com.xh.vdm.interManager.utils.SequenceUtil;
import com.xh.vdm.interManager.vo.BaseDeviceVO;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.DateUtil;
import org.springblade.core.secure.utils.AuthUtil;
import com.xh.vdm.biapi.entity.BdmFacility;
import com.xh.vdm.biapi.entity.BdmPrecisionAssembly;
import com.xh.vdm.biapi.entity.BdmShip;
import com.xh.vdm.biapi.entity.BdmTrainCargoBox;
import com.xh.vdm.biapi.entity.BdmVisitor;
import com.xh.vdm.biapi.entity.BdmWorker;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报文处理
 */
@Slf4j
@Service
public class MessageServiceImpl implements IMessageService {

	@Resource
	private MessageUtils messageUtils;

	@Resource
	private ISmInterfaceSystemService systemService;

	@Resource
	private IBdmAbstractTargetService abstractTargetService;

	@Resource
	private IInPosUserService userService;

	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;

	//终端编号-终端在pg表中的主键id
	private Map<String, Long> idMap = new HashMap<>();

	//终端id-终端映射，配合idMap使用
	private Map<Long, Object> deviceMap = new HashMap<>();

	@Resource
	private ITerminalService terminalService;

	@Resource
	private IRnssDeviceService rnssDeviceService;

	@Resource
	private IWearableDeviceService wearableDeviceService;

	@Resource
	private IRdssDeviceService rdssDeviceService;

	@Resource
	private IMonitDeviceService monitDeviceService;

	@Resource
	private IPntDeviceService pntDeviceService;

	@Resource
	private IFacilityService facilityService;

	@Resource
	private IWorkerService workerService;

	@Resource
	private IBdmVehicleNewService vehicleNewService;

	@Resource
	private IBdmContainerService containerService;

	@Resource
	private IBdmTemporaryService temporaryService;

	@Resource
	private IBdmVisitorService visitorService;

	@Resource
	private IBdmTrainCargoBoxService trainCargoBoxService;

	@Resource
	private IBdmPrecisionAssemblyService precisionAssemblyService;

	@Resource
	private IBdmShipService shipService;

	@Resource
	private IBladeDeptService deptService;

	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;

	@Resource
	private RedisTemplate<String, String> redisTemplate;

	@Resource
	private RedisTemplate<String, Object> objectRedisTemplate;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	@Qualifier("locationKafkaTemplate")
	private KafkaTemplate locationKafkaTemplate;

	@Value("${message.kafka.to_location}")
	private String toKafkaTopic;

	@Value("${message.kafka.to_alarm}")
	private String toAlarmTopic;

	@Resource
	private SequenceUtil sequenceUtil;

	@Resource
	private BdmDeviceLinkService deviceLinkService;

	@Resource
	private BdmDeviceOnlineService deviceOnlineService;

	@Resource
	private BdmDeviceStatusService deviceStatusService;


	private final SimpleDateFormat timeFormat = new SimpleDateFormat("yyyyMMddHHmmss");

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Message handleMessage(Message message) throws Exception {
		Header header = message.getHeader();
		//1.根据服务id处理报文
		//系统id
		String systemId = header.getSystemId();
		//服务id
		String serviceId = header.getServiceId();
		//序列号
		String serialNum = header.getSerialNum();

		//根据服务id解析和处理报文
		Object obj = null;
		try{
			switch (serviceId){
				case CommonConstant.BUSI_CODE_ONLINE: //终端上线
					List<DeviceOnline> onlines = messageUtils.decryptBody(message, DeviceOnline.class);
					String onlineArray = JSON.toJSONString(onlines);
					this.deviceOnline(onlineArray);
					break;
				case CommonConstant.BUSI_CODE_LOCATION: //位置数据上报
					List<LocDataUpload> locs = messageUtils.decryptBody(message, LocDataUpload.class);
					String locArray = JSON.toJSONString(locs);
					this.locDataUpload(locArray);
					break;
				case CommonConstant.BUSI_CODE_ALARM: //告警数据上报
					this.alarmDataUpload(message, null);
					break;
				case CommonConstant.BUSI_CODE_OFFLINE: //终端下线
					List<DeviceOffline> offlines = messageUtils.decryptBody(message, DeviceOffline.class);
					String offlineArray = JSON.toJSONString(offlines);
					this.deviceOffline(offlineArray);
					break;
				case CommonConstant.BUSI_CODE_TERMINAL_STATE: //终端状态信息
					this.terminalState(message, null);
					break;
				case CommonConstant.BUSI_CODE_VEHICLE: //车辆信息
					vehicleData(message);
					break;
				case CommonConstant.BUSI_CODE_STAFF: //职工信息
					workerData(message);
					break;
				case CommonConstant.BUSI_CODE_FACILITY: //基础设施信息
					facilityData(message);
					break;
				case CommonConstant.BUSI_CODE_TERMINAL: //终端信息
					terminalInfo(message);
					break;
				case CommonConstant.BUSI_CODE_TRAIN_CARGO_BOX: //铁路货车车厢信息
					this.trainCargoBoxData(message);
					break;
				case CommonConstant.BUSI_CODE_MINE_TRUCK: //矿用卡车信息
					this.mineTruckData(message);
					break;
				case CommonConstant.BUSI_CODE_PRECISION_ASSEMBLY:
					this.precisionAssemblyData(message);
				case CommonConstant.BUSI_CODE_SHIP: //货船信息
					this.shipData(message);
					break;
				case CommonConstant.BUSI_CODE_KEY_SEARCH://密钥查询
					obj = keySearch(message);
					break;
				case CommonConstant.BUSI_CODE_KEY_UPLOAD: //密钥上报
					keyUpload(message);
					break;
			}
			if(obj == null){
				//如果不需要返回数据
				return null;
			}else{
				//2.加密返回报文
				Header headerRes = new Header();
				headerRes.setSystemId(CommonConstant.DEFAULT_SYSTEM_ID+"");
				headerRes.setServiceId(serviceId);
				headerRes.setSerialNum(serialNum);
				headerRes.setReqTime(DateUtil.sdfHolder.get().format(new Date()).replace("- ","").replace(" ",""));
				Message resMessage = new Message();
				if(CommonConstant.BUSI_CODE_KEY_SEARCH.equals(serviceId) || CommonConstant.BUSI_CODE_KEY_UPLOAD.equals(serviceId)){
					//密钥查询和密钥上传，不进行加密
					resMessage.setHeader(headerRes);
					Body body = new Body();
					body.setEncryptedContent(JSON.toJSONString(obj));
					resMessage.setBody(body);
				}else{
					resMessage = messageUtils.encryptData(headerRes, JSON.toJSONString(obj), systemId);
				}
				return resMessage;
			}
		}catch (Exception e){
			throw e;
		}
	}


	@Override
	public void handleMqttMessage(String topic, String message) {
		log.info("topic is {}", topic);
		log.info("message is {}", message);

		try{
			if(topic.startsWith(CommonConstant.MQTT_PREFIX)){
				String serviceId = topic.replace(CommonConstant.MQTT_PREFIX, "");
				switch (serviceId){
					case CommonConstant.BUSI_CODE_ONLINE: //终端上线
						deviceOnline(message);
						break;
					case CommonConstant.BUSI_CODE_LOCATION: //位置数据上报
						locDataUpload(message);
						break;
					case CommonConstant.BUSI_CODE_ALARM: //告警数据上报
						this.alarmDataUpload(null, message);
						break;
					case CommonConstant.BUSI_CODE_OFFLINE: //终端下线
						deviceOffline(message);
						break;
					case CommonConstant.BUSI_CODE_TERMINAL_STATE: //终端状态信息
						this.terminalState(null, message);
						break;
				}
			}
		}catch (Exception e){
			log.error("处理mqtt消息失败, message={}", message, e);
		}
	}

	/**
	 * RA0001 终端上线
	 * 思路：
	 *  1.校验数据：终端id、所属单位、使用者编号、上线时间不能为空；所属单位、使用者编号需要在系统中存在；上线时间不能大于当前时间。
	 *  2.数据添加到数据库、redis中
	 *  3.向 kafka 【terminalstatus】topic中写入消息，用于向前端推送上下线提醒
	 * 数据库：bdm_device_link、bdm_device_online、bdm_device_status
	 * redis: OnLineState:[unique_id]、Loc:TerminalRealLoc:[unique_id]
	 */
	private void deviceOnline (String message) throws Exception {
		log.info("【RA0001】数据处理，原始数据为："+ message);
		List<DeviceOnline> array = JSON.parseArray(message, DeviceOnline.class);
		for(DeviceOnline d : array) {
			//1.数据校验
			//1.1 终端id、所属单位、使用者编号、上线时间不能为空
			if(StringUtils.isEmpty(d.getDeviceId())){
				//终端id为空，跳过本次数据处理
				log.info("deviceId为空，不处理数据");
				continue;
			}
			if(StringUtils.isEmpty(d.getOwnerId())){
				//所属机构为空，跳过本次数据处理
				log.info("ownerId为空，不处理数据");
				continue;
			}
			if(StringUtils.isEmpty(d.getUserCode())){
				//使用者编号为空，跳过本次数据处理
				log.info("userCode为空，不处理数据");
				continue;
			}
			if(StringUtils.isEmpty(d.getOnlineTime())){
				//上线时间为空，跳过本次数据处理
				log.info("onlineTime为空，不处理数据");
				continue;
			}

			//1.2 所属单位、使用者编号需要在系统中存在
			//校验单位是否存在
			BladeDept dept = deptService.getOne(Wrappers.lambdaQuery(BladeDept.class)
				.eq(BladeDept::getId, Long.parseLong(d.getOwnerId()))
				.eq(BladeDept::getIsDeleted, 0));
			if(dept == null){
				log.info("组织信息不存在，deptId={}，不处理数据", d.getOwnerId());
				continue;
			}
			//按照最新的位置平台存储概念（核心是终端，不一定非要绑定设备），不再校验使用者
			//查询终端信息
			String uniqueId = d.getDeviceId();
			String deviceType = d.getDeviceType();
			BaseDeviceVO vo = getDevice(d.getDeviceId(), Integer.parseInt(deviceType));
			//2.数据存储
			//2.1 写入数据库
			//bdm_device_link、bdm_device_online、bdm_device_status
			//bdm_device_link
			BdmDeviceLink link = new BdmDeviceLink();
			link.setDeviceId(vo.getDeviceId());
			link.setDeviceType(vo.getDeviceType());
			link.setUniqueId(vo.getUniqueId());
			link.setDeviceNum(vo.getDeviceNum());
			link.setTargetId(vo.getTargetId());
			link.setTargetType(vo.getTargetType());
			link.setTargetName(vo.getTargetName());
			//todo 上线时，没有经纬度和位置
			link.setAction(0);
			link.setTime(new Date());
			deviceLinkService.save(link);

			//bdm_device_online
			BdmDeviceOnline online = new BdmDeviceOnline();
			//在线状态的id与redis中的状态中的id相同，使用雪花id（与终端接入服务保持一致）
			//主动获取 MyBatis-Plus 中的雪花id
			long id = IdWorker.getId();
			online.setId(id);
			online.setDeviceId(vo.getDeviceId());
			online.setDeviceType(vo.getDeviceType());
			online.setUniqueId(vo.getUniqueId());
			online.setDeviceNum(vo.getDeviceNum());
			online.setTargetId(vo.getTargetId());
			online.setTargetType(vo.getTargetType());
			online.setTargetName(vo.getTargetName());
			online.setStartTime(new Date());
			deviceOnlineService.save(online);

			//bdm_device_status
			BdmDeviceStatus status = new BdmDeviceStatus();
			status.setDeviceId(vo.getDeviceId());
			status.setDeviceType(vo.getDeviceType());
			status.setUniqueId(vo.getUniqueId());
			status.setDeviceNum(vo.getDeviceNum());
			status.setTargetId(vo.getTargetId());
			status.setTargetType(vo.getTargetType());
			status.setTargetName(vo.getTargetName());
			status.setAction(0);
			status.setActionTime(new Date());
			deviceStatusService.save(status);

			//2.2 写入redis
			//上线标记，有效期5分钟
			//todo 需要补充终端类型
			String key = CommonConstant.REDIS_KEY_ONLINE_STATE;
			Map<String,Object> map = new HashMap<>();

			map.put("id", id);
			map.put("device_id", vo.getDeviceId());
			map.put("device_type",vo.getDeviceType());
			map.put("unique_id", vo.getUniqueId());
			map.put("device_num", vo.getDeviceNum());
			map.put("target_id", vo.getTargetId());
			map.put("target_type",vo.getTargetType());
			map.put("target_name", vo.getTargetName());
			map.put("action", 0);
			long timestamp = DateUtil.getTimestamp() / 1000;
			String dateString = DateUtil.sdfHolder.get().format(new Date());
			map.put("action_time", dateString);
			map.put("fault_count",0);
			map.put("dept_id", dept.getId());
			map.put("on_line_time_stamp", timestamp);
			map.put("update_time",dateString);
			//device_category 和 kinestate 不写入redis
			objectRedisTemplate.opsForHash().put(key, vo.getDeviceId(), JSON.toJSONString(map));

			//3.写入今日上线终端
			String keyTodayOnline = CommonConstant.REDIS_KEY_PREFIX_TODAY_ONLINE + vo.getDeviceId();
			objectRedisTemplate.opsForValue().set(keyTodayOnline,0);

			//4.发送topic消息
			Map<String,Object> mapK = new HashMap<>();
			mapK.put("phone", vo.getUniqueId());
			mapK.put("device_type",vo.getDeviceType());
			mapK.put("device_num", vo.getDeviceNum());
			mapK.put("unique_id", vo.getUniqueId());
			mapK.put("target_type",vo.getTargetType());
			mapK.put("target_id", vo.getTargetId());
			mapK.put("target_name", vo.getTargetName());
			mapK.put("time", new Date());
			mapK.put("on_off_line", 0);
			kafkaTemplate.send(CommonConstant.KAFKA_TOPIC_TERMINAL_STATUS, JSON.toJSONString(mapK));
		}
	}


	/**
	 * RA0004 终端下线
	 * 思路：
	 *  1.校验数据：终端id、所属单位、下线时间不能为空；所属单位需要在系统中存在；下线时间不能大于当前时间。
	 *  2.数据添加到数据库、redis中
	 *  3.向 kafka 【terminalstatus】topic中写入消息，用于向前端推送上下线提醒
	 * 数据库：bdm_device_link、bdm_device_online、bdm_device_status
	 * redis: OnLineState:[unique_id]、Loc:TerminalRealLoc:[unique_id]
	 */
	private void deviceOffline (String message) throws Exception {
		log.info("【RA0004】数据处理，原始数据为："+ message);
		List<DeviceOffline> array = JSON.parseArray(message, DeviceOffline.class);
		for(DeviceOffline d : array) {
			//1.数据校验
			//1.1 终端id、所属单位、使用者编号、上线时间不能为空
			if(StringUtils.isEmpty(d.getDeviceId())){
				//终端id为空，跳过本次数据处理
				log.info("deviceId为空，不处理数据");
				continue;
			}
			if(StringUtils.isEmpty(d.getOwnerId())){
				//所属机构为空，跳过本次数据处理
				log.info("ownerId为空，不处理数据");
				continue;
			}
			if(StringUtils.isEmpty(d.getOfflineTime())){
				//下线时间为空，跳过本次数据处理
				log.info("offlineTime为空，不处理数据");
				continue;
			}

			//1.2 所属单位、使用者编号需要在系统中存在
			//校验单位是否存在
			BladeDept dept = deptService.getOne(Wrappers.lambdaQuery(BladeDept.class)
				.eq(BladeDept::getId, Long.parseLong(d.getOwnerId()))
				.eq(BladeDept::getIsDeleted, 0));
			if(dept == null){
				log.info("组织信息不存在，deptId={}，不处理数据", d.getOwnerId());
				continue;
			}
			//按照最新的位置平台存储概念（核心是终端，不一定非要绑定设备），不再校验使用者

			//查询终端信息
			String uniqueId = d.getDeviceId();
			String deviceType = d.getDeviceType();
			BaseDeviceVO vo = getDevice(d.getDeviceId(), Integer.parseInt(deviceType));
			//******************说明：服务下线在终端接入服务中统一处理，这里不做实际的数据处理操作*************************
			//2.数据存储
			//2.1 写入数据库
			//bdm_device_link、bdm_device_online、bdm_device_status
			//bdm_device_link
			/*BdmDeviceLink link = new BdmDeviceLink();
			link.setDeviceId(vo.getDeviceId());
			link.setDeviceType(vo.getDeviceType());
			link.setUniqueId(vo.getUniqueId());
			link.setDeviceNum(vo.getDeviceNum());
			link.setTargetId(vo.getTargetId());
			link.setTargetType(vo.getTargetType());
			link.setTargetName(vo.getTargetName());
			//todo 下线时，没有经纬度和位置
			link.setAction(1);
			link.setTime(new Date());
			deviceLinkService.save(link);

			//bdm_device_online
			//查找最新的一条上线记录，并且下线时间为空的记录，更新数据
			Query query = new Query();
			query.setSize(1);
			query.setCurrent(1);
			IPage<BdmDeviceOnline> onlinePage = deviceOnlineService.page(Condition.getPage(query), Wrappers.lambdaQuery(BdmDeviceOnline.class)
				.orderByDesc(BdmDeviceOnline::getStartTime)
				.eq(BdmDeviceOnline::getEndTime, null)
				);
			if(onlinePage != null && onlinePage.getRecords() != null && onlinePage.getRecords().size() > 0){
				BdmDeviceOnline online = onlinePage.getRecords().get(0);
				online.setEndTime(new Date());
				deviceOnlineService.updateById(online);
			}

			//bdm_device_status
			BdmDeviceStatus status = new BdmDeviceStatus();
			status.setDeviceId(vo.getDeviceId());
			status.setDeviceType(vo.getDeviceType());
			status.setUniqueId(vo.getUniqueId());
			status.setDeviceNum(vo.getDeviceNum());
			status.setTargetId(vo.getTargetId());
			status.setTargetType(vo.getTargetType());
			status.setTargetName(vo.getTargetName());
			status.setAction(1);
			status.setActionTime(new Date());
			deviceStatusService.save(status);

			//2.2 删除redis标记
			//上线标记，有效期5分钟
			String key = CommonConstant.REDIS_KEY_ONLINE_STATE + uniqueId;
			objectRedisTemplate.delete(key);*/
			//****************************end***********************************

			//3.发送topic消息
			Map<String,Object> mapK = new HashMap<>();
			mapK.put("phone", vo.getUniqueId());
			mapK.put("device_type",vo.getDeviceType());
			mapK.put("device_num", vo.getDeviceNum());
			mapK.put("unique_id", vo.getUniqueId());
			mapK.put("target_type",vo.getTargetType());
			mapK.put("target_id", vo.getTargetId());
			mapK.put("target_name", vo.getTargetName());
			mapK.put("time", new Date());
			mapK.put("on_off_line", 1);
			kafkaTemplate.send(CommonConstant.KAFKA_TOPIC_TERMINAL_STATUS, JSON.toJSONString(mapK));
		}
	}



	private long getDeviceIdBySeq (String deviceSeq) {
		BdmMonitDevice monitDevice = this.monitDeviceService.getOne(
			Wrappers.lambdaQuery(BdmMonitDevice.class).eq(BdmMonitDevice::getUniqueId, deviceSeq)
		);
		if (monitDevice != null) {
			return monitDevice.getId();
		}

		BdmPntDevice pntDevice = this.pntDeviceService.getOne(
			Wrappers.lambdaQuery(BdmPntDevice.class).eq(BdmPntDevice::getUniqueId, deviceSeq)
		);
		if (pntDevice != null) {
			return pntDevice.getId();
		}

		BdmRdssDevice rdssDevice = this.rdssDeviceService.getOne(
			Wrappers.lambdaQuery(BdmRdssDevice.class).eq(BdmRdssDevice::getUniqueId, deviceSeq)
		);
		if (rdssDevice != null) {
			return rdssDevice.getId();
		}

		BdmRnssDevice rnssDevice = this.rnssDeviceService.getOne(
			Wrappers.lambdaQuery(BdmRnssDevice.class).eq(BdmRnssDevice::getUniqueId, deviceSeq)
		);
		if (rnssDevice != null) {
			return rnssDevice.getId();
		}

		BdmWearableDevice wearableDevice = this.wearableDeviceService.getOne(
			Wrappers.lambdaQuery(BdmWearableDevice.class).eq(BdmWearableDevice::getUniqueId, deviceSeq)
		);
		if (wearableDevice != null) {
			return wearableDevice.getId();
		}

		return 0;
	}

	//RA0002 位置数据上报
	private void locDataUpload(Message message) throws Exception{
		//1.解析报文
		List<LocDataUpload> locList = null;
		try {
			locList = messageUtils.decryptBody(message, LocDataUpload.class);
		}catch (Exception e){
			log.error("解析报文出错",e);
			throw new Exception("解析报文出错："+e.getMessage());
		}
		//2.保存数据
		//2.1 数据解析组装

		//将位置上报数据改造后，上传到位置kafka中
		locationKafkaTemplate.send(toKafkaTopic, locList);
	}

	/**
	 * 位置上报
	 * 已经过测试，国能生产和测试在正式使用
	 * @param arrayStr
	 * @throws Exception
	 */
	private void locDataUpload(String arrayStr) throws Exception{
		//保存数据
		//数据解析组装
		List<LocDataUpload> array = JSON.parseArray(arrayStr, LocDataUpload.class);
		for(LocDataUpload u : array){

			//判断定位坐标系，转为 CGCS2000进行存储，如果没有指定坐标系，则认为是CGCS2000
			Integer mapType = u.getMapType();
			if(u.getLocType().equals("0") && mapType != null && mapType == 0){
				//如果是WGS84
				double[] loc = CoordinateUtil.WGS84_to_CGCS2000(u.getLongitude(), u.getLatitude());
				u.setLongitude(loc[0]);
				u.setLatitude(loc[1]);
			}else if(u.getLocType().equals("0") && mapType != null && mapType == 1){
				//如果是GCJ02
				double[] loc = CoordinateUtil.GCJ02_to_CGCS2000(u.getLongitude(), u.getLatitude());
				u.setLongitude(loc[0]);
				u.setLatitude(loc[1]);
			}else{
				//如果是2000，不做操作
			}



			Location loc = new Location();
			//id
			loc.setId(formLocationId());
			//定位时间
			if(!StringUtils.isEmpty(u.getTime())){
				loc.setTime(Long.parseLong(u.getTime()));
			}
			//终端类型
			if(!StringUtils.isEmpty(u.getDeviceType())){
				loc.setDeviceType(Byte.parseByte(u.getDeviceType()));
			}
			//终端id
			if(!StringUtils.isEmpty(u.getDeviceId())){
				//根据终端编号，查询终端在数据库中的id
				Long id = idMap.get(u.getDeviceId());
				if(id == null){
					//查询在缓存中查询不到，则查询数据库
					id = terminalService.findIdByUniqueId(u.getDeviceId());
					idMap.put(u.getDeviceId(), id);
				}
				loc.setDeviceId(id);
			}
			//赋码号
			if(!StringUtils.isEmpty(u.getDeviceNum())){
				loc.setDeviceNum(u.getDeviceNum());
			}
			//经度
			if(u.getLongitude() != null){
				loc.setLongitude(u.getLongitude());
			}
			//纬度
			if(u.getLatitude() != null){
				loc.setLatitude(u.getLatitude());
			}
			//速度
			if(u.getSpeed() != null){
				loc.setSpeed(u.getSpeed().doubleValue());
			}
			//方向
			if(u.getDirection() != null){
				loc.setBearing(u.getDirection().shortValue());
			}
			//平台接收时间
			loc.setRecvTime(new Date().getTime()/1000);
			//数据有效性 0-无效，1-有效
			loc.setValid((byte) 1);
			//是否纠正 0-未纠正，1-纠正点
			loc.setCorrection((byte)0);
			//是否批量上传 0-正常上传，1-批量上传，2-补传
			loc.setBatch((byte)0);
			//定位系统：0-未知，1-卫星，2-网络基站，3-WIFI，4-蓝牙，5-惯导
			if(u.getLocType() != null){
				loc.setPosSystem(u.getLocType().byteValue());
			}else{
				loc.setPosSystem((byte)0);
			}
			//终端状态（默认为0）
			loc.setStatus(0);
			//报警状态
			loc.setAlarm(0);
			//附加信息（此处保存完整报文）
			loc.setAuxiliary(JSON.toJSONString(u));
			//将位置上报数据改造后，上传到位置kafka中
			locationKafkaTemplate.send(toKafkaTopic, loc);

			//位置点记录到redis中，作为最终位置点
			String redisKey = CommonConstant.REDIS_CACHE_LAST_POST;


			LastPosCache cache = new LastPosCache();
			cache.setDeviceUniqueId(u.getDeviceId());
			//根据uniqueId查询终端id
			Long deviceId = idMap.get(u.getDeviceId());
			if(deviceId == null){
				deviceId = terminalService.findIdByUniqueId(u.getDeviceId());
			}
			cache.setDeviceId(deviceId);
			//根据终端id查询终端信息
			Object device = deviceMap.get(deviceId);
			if(device == null){
				if(CommonConstant.PLAT_DEVICE_TYPE_RNSS.equals(u.getDeviceType())){ //北斗定位终端
					device = rnssDeviceService.getById(deviceId);
				}else if(CommonConstant.PLAT_DEVICE_TYPE_WEARABLE.equals(u.getDeviceType())){ //北斗穿戴式终端
					device = wearableDeviceService.getById(deviceId);
				}else if(CommonConstant.PLAT_DEVICE_TYPE_RDSS.equals(u.getDeviceType())){ //北斗短报文终端
					device = rdssDeviceService.getById(deviceId);
				}else if(CommonConstant.PLAT_DEVICE_TYPE_MONIT.equals(u.getDeviceType())){ //北斗监测终端
					device = monitDeviceService.getById(deviceId);
				}else if(CommonConstant.PLAT_DEVICE_TYPE_PNT.equals(u.getDeviceType())){ //北斗授时终端
					device = pntDeviceService.getById(deviceId);
				}
				deviceMap.put(deviceId, device);
			}
			if(CommonConstant.PLAT_DEVICE_TYPE_RNSS.equals(u.getDeviceType())){ //北斗定位终端
				BdmRnssDevice d = (BdmRnssDevice) device;
				cache.setDeviceModel(d.getModel());
				cache.setDeviceNo(d.getUniqueId());
				cache.setTargetId(d.getTargetId());
				cache.setTargetType(d.getTargetType());
				cache.setTargetName(d.getTargetName());
				cache.setDeviceNum(d.getDeviceNum());
				cache.setDeptId(d.getDeptId());
				if(u.getLocType().equals("0")){
					//如果是卫星定位
					cache.setLongitude(u.getLongitude());
					cache.setLatitude(u.getLatitude());
					cache.setAltitude(u.getAltitude().intValue());
				}else if(u.getLocType().equals("1")){
					//如果是蓝牙信标定位
					cache.setLongitude(u.getX());
					cache.setLatitude(u.getY());
					cache.setAltitude(u.getZ().intValue());
				}
				cache.setSpeed(u.getSpeed().doubleValue());
				cache.setBearing(u.getDirection());
				cache.setAlarmFlag(0L);
				cache.setStateFlag(0L);
				cache.setLocTime(DateUtil.sdfHolderNoLine.get().parse(u.getTime()).getTime()/1000);
				cache.setRecvTime(new Date().getTime()/1000);
				cache.setValid(1);
				cache.setMileage(0D);
				cache.setGnssNum(0);
				cache.setWireless(0);
				cache.setRealSpeed(0D);
				cache.setExpandSignal(0L);
				cache.setIoStatus(0L);
				cache.setTemperature("");
				cache.setBatch(0L);
				cache.setAuxStr("");
				cache.setAuxsNormal(null);
				cache.setDsmUniqueId("");
				//默认设置为离线，终端上线时，应修改该值
				cache.setTeState(0L);
				cache.setLocTimeF(DateUtil.sdfHolder.get().format(DateUtil.sdfHolderNoLine.get().parse(u.getTime())));
				cache.setRecvTimeF(DateUtil.sdfHolder.get().format(new Date()));
				cache.setOffLineTime("");
			}else if(CommonConstant.PLAT_DEVICE_TYPE_WEARABLE.equals(u.getDeviceType())){ //北斗穿戴式终端
				BdmWearableDevice d = (BdmWearableDevice) device;
				cache.setDeviceModel(d.getModel());
				cache.setDeviceNo(d.getUniqueId());
				cache.setTargetId(d.getTargetId());
				cache.setTargetType(d.getTargetType());
				cache.setTargetName(d.getTargetName());
				cache.setDeviceNum(d.getDeviceNum());
				cache.setDeptId(d.getDeptId());
				if(u.getLocType().equals("0")){
					//如果是卫星定位
					cache.setLongitude(u.getLongitude());
					cache.setLatitude(u.getLatitude());
					cache.setAltitude(u.getAltitude().intValue());
				}else if(u.getLocType().equals("1")){
					//如果是蓝牙信标定位
					cache.setLongitude(u.getX());
					cache.setLatitude(u.getY());
					cache.setAltitude(u.getZ().intValue());
				}
				cache.setSpeed(u.getSpeed().doubleValue());
				cache.setBearing(u.getDirection().intValue());
				cache.setAlarmFlag(0L);
				cache.setStateFlag(0L);
				cache.setLocTime(DateUtil.sdfHolderNoLine.get().parse(u.getTime()).getTime()/1000);
				cache.setRecvTime(new Date().getTime()/1000);
				cache.setValid(1);
				cache.setMileage(0D);
				cache.setGnssNum(0);
				cache.setWireless(0);
				cache.setRealSpeed(0D);
				cache.setExpandSignal(0L);
				cache.setIoStatus(0L);
				cache.setTemperature("");
				cache.setBatch(0L);
				cache.setAuxStr("");
				cache.setAuxsNormal(null);
				cache.setDsmUniqueId("");
				//默认设置为离线，终端上线时，应修改该值
				cache.setTeState(0L);
				cache.setLocTimeF(DateUtil.sdfHolder.get().format(DateUtil.sdfHolderNoLine.get().parse(u.getTime())));
				cache.setRecvTimeF(DateUtil.sdfHolder.get().format(new Date()));
				cache.setOffLineTime("");
			}else if(CommonConstant.PLAT_DEVICE_TYPE_RDSS.equals(u.getDeviceType())){ //北斗短报文终端
				BdmRdssDevice d = (BdmRdssDevice) device;
				cache.setDeviceModel(d.getModel());
				cache.setDeviceNo(d.getUniqueId());
				cache.setTargetId(d.getTargetId());
				cache.setTargetType(d.getTargetType());
				cache.setTargetName(d.getTargetName());
				cache.setDeviceNum(d.getDeviceNum());
				cache.setDeptId(d.getDeptId());
				if(u.getLocType().equals("0")){
					//如果是卫星定位
					cache.setLongitude(u.getLongitude());
					cache.setLatitude(u.getLatitude());
					cache.setAltitude(u.getAltitude().intValue());
				}else if(u.getLocType().equals("1")){
					//如果是蓝牙信标定位
					cache.setLongitude(u.getX());
					cache.setLatitude(u.getY());
					cache.setAltitude(u.getZ().intValue());
				}
				cache.setSpeed(u.getSpeed().doubleValue());
				cache.setBearing(u.getDirection());
				cache.setAlarmFlag(0L);
				cache.setStateFlag(0L);
				cache.setLocTime(DateUtil.sdfHolderNoLine.get().parse(u.getTime()).getTime()/1000);
				cache.setRecvTime(new Date().getTime()/1000);
				cache.setValid(1);
				cache.setMileage(0D);
				cache.setGnssNum(0);
				cache.setWireless(0);
				cache.setRealSpeed(0D);
				cache.setExpandSignal(0L);
				cache.setIoStatus(0L);
				cache.setTemperature("");
				cache.setBatch(0L);
				cache.setAuxStr("");
				cache.setAuxsNormal(null);
				cache.setDsmUniqueId("");
				//默认设置为离线，终端上线时，应修改该值
				cache.setTeState(0L);
				cache.setLocTimeF(DateUtil.sdfHolder.get().format(DateUtil.sdfHolderNoLine.get().parse(u.getTime())));
				cache.setRecvTimeF(DateUtil.sdfHolder.get().format(new Date()));
				cache.setOffLineTime("");
			}else if(CommonConstant.PLAT_DEVICE_TYPE_MONIT.equals(u.getDeviceType())){ //北斗监测终端
				BdmMonitDevice d = (BdmMonitDevice) device;
				cache.setDeviceModel(d.getModel());
				cache.setDeviceNo(d.getUniqueId());
				cache.setTargetId(d.getTargetId());
				cache.setTargetType(d.getTargetType());
				cache.setDeviceNum(d.getDeviceNum());
				cache.setDeptId(d.getDeptId());
				if(u.getLocType().equals("0")){
					//如果是卫星定位
					cache.setLongitude(u.getLongitude());
					cache.setLatitude(u.getLatitude());
					cache.setAltitude(u.getAltitude().intValue());
				}else if(u.getLocType().equals("1")){
					//如果是蓝牙信标定位
					cache.setLongitude(u.getX());
					cache.setLatitude(u.getY());
					cache.setAltitude(u.getZ().intValue());
				}
				cache.setSpeed(u.getSpeed().doubleValue());
				cache.setBearing(u.getDirection());
				cache.setAlarmFlag(0L);
				cache.setStateFlag(0L);
				cache.setLocTime(DateUtil.sdfHolderNoLine.get().parse(u.getTime()).getTime()/1000);
				cache.setRecvTime(new Date().getTime()/1000);
				cache.setValid(1);
				cache.setMileage(0D);
				cache.setGnssNum(0);
				cache.setWireless(0);
				cache.setRealSpeed(0D);
				cache.setExpandSignal(0L);
				cache.setIoStatus(0L);
				cache.setTemperature("");
				cache.setBatch(0L);
				cache.setAuxStr("");
				cache.setAuxsNormal(null);
				cache.setDsmUniqueId("");
				//默认设置为离线，终端上线时，应修改该值
				cache.setTeState(0L);
				cache.setLocTimeF(DateUtil.sdfHolder.get().format(DateUtil.sdfHolderNoLine.get().parse(u.getTime())));
				cache.setRecvTimeF(DateUtil.sdfHolder.get().format(new Date()));
				cache.setOffLineTime("");
			}else if(CommonConstant.PLAT_DEVICE_TYPE_PNT.equals(u.getDeviceType())){ //北斗授时终端
				BdmPntDevice d = (BdmPntDevice) device;
				cache.setDeviceModel(d.getModel());
				cache.setDeviceNo(d.getUniqueId());
				cache.setTargetId(d.getTargetId());
				cache.setTargetType(d.getTargetType());
				cache.setTargetName("");
				cache.setDeviceNum(d.getDeviceNum());
				cache.setDeptId(d.getDeptId());
				if(u.getLocType().equals("0")){
					//如果是卫星定位
					cache.setLongitude(u.getLongitude());
					cache.setLatitude(u.getLatitude());
					cache.setAltitude(u.getAltitude().intValue());
				}else if(u.getLocType().equals("1")){
					//如果是蓝牙信标定位
					cache.setLongitude(u.getX());
					cache.setLatitude(u.getY());
					cache.setAltitude(u.getZ().intValue());
				}
				cache.setSpeed(u.getSpeed().doubleValue());
				cache.setBearing(u.getDirection());
				cache.setAlarmFlag(0L);
				cache.setStateFlag(0L);
				cache.setLocTime(DateUtil.sdfHolderNoLine.get().parse(u.getTime()).getTime()/1000);
				cache.setRecvTime(new Date().getTime()/1000);
				cache.setValid(1);
				cache.setMileage(0D);
				cache.setGnssNum(0);
				cache.setWireless(0);
				cache.setRealSpeed(0D);
				cache.setExpandSignal(0L);
				cache.setIoStatus(0L);
				cache.setTemperature("");
				cache.setBatch(0L);
				cache.setAuxStr("");
				cache.setAuxsNormal(null);
				cache.setDsmUniqueId("");
				//默认设置为离线，终端上线时，应修改该值
				cache.setTeState(0L);
				cache.setLocTimeF(DateUtil.sdfHolder.get().format(DateUtil.sdfHolderNoLine.get().parse(u.getTime())));
				cache.setRecvTimeF(DateUtil.sdfHolder.get().format(new Date()));
				cache.setOffLineTime("");
			}
			stringRedisTemplate.opsForHash().put(redisKey, loc.getDeviceId(), JSON.toJSONString(cache));
		}

	}

	private String formLocationId () {
		try {
			return String.valueOf(this.sequenceUtil.nextId());
		} catch (Exception e) {
			log.error("fail form location id: {}", e.getMessage(), e);
			return String.valueOf(UUID.randomUUID());
		}
	}





	//RA0003 告警数据上报
	private void alarmDataUpload (Message message, String msg) throws Exception {
		List<AlarmDataUpload> tmpList;
		if (message != null) {
			tmpList = this.messageUtils.decryptBody(message, AlarmDataUpload.class);
		} else if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(msg)) {
			tmpList = JSON.parseArray(msg, AlarmDataUpload.class);
		} else {
			tmpList = new ArrayList<>();
		}
		for (AlarmDataUpload tmp : tmpList) {
			AlarmFromSecondary alarm = new AlarmFromSecondary();
			long targetId = this.getTargetIdByTypeAndCode(tmp.getUserType(), tmp.getUserCode());
			long deviceId = this.getDeviceIdByTypeAndSeq(tmp.getDeviceType(), tmp.getDeviceId());
			if ((targetId <= 0) || (deviceId <= 0)) {
				log.error("fail find target or device from msg: {}", tmp);
				continue;
			}

			BeanUtils.copyProperties(tmp, alarm);
			alarm.setDeptId(Long.parseLong(tmp.getOwnerId()));
			alarm.setTargetType(Byte.parseByte(tmp.getUserType()));
			alarm.setTargetId(targetId);
			alarm.setDeviceType(Byte.parseByte(tmp.getDeviceType()));
			alarm.setDeviceId(deviceId);
			alarm.setAlarmType(tmp.getAlarmType().shortValue());
			alarm.setAlarmLevel(tmp.getAlarmLevel().byteValue());
			alarm.setAlarmSource((byte) CommonConstant.ALARM_ORIGIN_SECONDARY);
			alarm.setCompleted(tmp.getCompleted().byteValue());
			alarm.setTime(this.timeFormat.parse(tmp.getTime()).getTime() / 1000);
			this.kafkaTemplate.send(this.toAlarmTopic, JSON.toJSONString(alarm));
		}
	}

	//RA0004 终端下线
	private void terminalOffline(Message message) throws Exception{

	}

	//RA0005 终端状态信息
	private void terminalState (Message message, String msg) throws Exception{
		List<DeviceState> tmpList;
		if (message != null) {
			tmpList = this.messageUtils.decryptBody(message, DeviceState.class);
		} else if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(msg)) {
			tmpList = JSON.parseArray(msg, DeviceState.class);
		} else {
			tmpList = new ArrayList<>();
		}
		for (DeviceState tmp : tmpList) {
			this.redisTemplate.opsForHash().put(CommonConstant.REDIS_HASH_DEVICE_STATE, tmp.getDeviceNum(), JSON.toJSONString(tmp));
		}
	}

	//RB0001 车辆信息
	private void vehicleData(Message message) throws Exception{
		List<Vehicle> requestArray = null;
		try{
			requestArray = messageUtils.decryptBody(message, Vehicle.class);
			if(requestArray == null){
				throw new Exception("报文内容为空");
			}
		}catch (Exception e){
			log.error("报文解析失败",e);
			throw new Exception("报文解析失败，"+e.getMessage());
		}

		//校验上传的报文中，车辆信息是否重复
		Set<String> set = new HashSet<>();
		for(Vehicle request : requestArray){
			String number = request.getVehicleNo()+"-"+request.getVehicleColor();
			set.add(number);
		}
		if(set.size() < requestArray.size()){
			throw new Exception("报文中存在重复车辆信息，请确认");
		}

		for(Vehicle request : requestArray){
			//1.判断操作类型
			String operType = request.getOperateState();
			if(StringUtils.isEmpty(operType)){
				throw new Exception("未指定操作类型");
			}

			String number = request.getVehicleNum(); //车辆编号
			String vehicleNo = request.getVehicleNo(); //车牌号
			String vehicleColor = request.getVehicleColor(); //车牌颜色
			String vehicleType = request.getVehicleType(); //车辆类型
			String ownerId = request.getOwnerId(); //所属企业ID
			String ownerName = request.getOwnerName(); //所属企业名称
			String operateState = request.getOperateState(); //操作状态
			String operTime = request.getOperateTime(); //操作时间 YYYYMMDDhhmiss

			//位置平台中，使用车牌号-车牌颜色作为编号
			number = vehicleNo+"-"+vehicleColor;
			List<BdmVehicle> vehicleList = vehicleNewService.list(Wrappers.lambdaQuery(BdmVehicle.class)
				.eq(BdmVehicle::getNumber, number).eq(BdmVehicle::getDeleted, 0));

			if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
				//新增
				//规则：
				// a.车辆编号、所属单位id 不能为空；
				// b.根据车辆编号判断员工是否存在;
				// c.校验所属单位id是否存在
				//1.校验空值
				/*if(StringUtils.isEmpty(number)){
					throw new Exception("不能为空");
				}*/
				if(StringUtils.isEmpty(ownerId)){
					throw new Exception("所属单位id不能为空");
				}
				//2.判断信息是否已经存在
				if(vehicleList != null && vehicleList.size() > 0){
					//如果已经存在，则不允许再添加
					throw new Exception("该车辆信息已经存在,车辆编号=" + number);
				}
				//3.校验所属单位id是否存在，如果不存在，则不允许添加
				//todo 需先完成基础服务平台 组织数据同步：传入的是4A数据中的code，需要从基础服务平台传过来的code映射到 blade_dept中，可以判断同步过来的组织数据中是否存在该组织

				//4.保存车辆信息
				BdmVehicle f = new BdmVehicle();
				TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(CommonConstant.TARGET_TYPE_VEHICLE, 0, 0);
				f.setId(targetId.nextId());
				f.setNumber(number);
				f.setCategory(Integer.parseInt(vehicleType));
				//todo 设置 deptId，应使用 blade_dept 中的id
				f.setDeptId(Long.parseLong(ownerId));
				f.setCreateTime(new Date());
				f.setDeleted(0);
				vehicleNewService.save(f);

				//5.添加到业务总表（bdm_abstract_target）中
				BdmAbstractTarget target = new BdmAbstractTarget();
				target.setId(f.getId());
				target.setNumber(number);
				target.setName(number);
				//目标类别，0-未知，1-车辆，2-人员，3-基础设施、4-集装箱、5-外包人员、6-访客、7-船舶、8-列车货箱
				target.setTargetType(CommonConstant.TARGET_TYPE_VEHICLE);
				target.setDeptId(Long.parseLong(ownerId));
				target.setCategory(Integer.parseInt(vehicleType));
				target.setDeleted(0);
				abstractTargetService.save(target);

				Map<String, String> map = new HashMap<>();
				String key = target.getTargetType() + "-" + target.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", target.getNumber());
				innerMap.put("targetType", target.getTargetType());
				innerMap.put("targetCategory", target.getCategory());
				innerMap.put("deptId", target.getDeptId());
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_TARGET, map);

				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.INSERT);
				deviceInfo.setTargetId(target.getId());
				deviceInfo.setTargetName(target.getNumber());
				deviceInfo.setTargetCategory(target.getCategory());
				deviceInfo.setDeptId(target.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_vehicle_new", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("车辆信息更新消息发送到kafka失败", e);
				}
			}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
				//修改
				//规则：判断车辆信息是否存在；判断单位id是否存在
				//2.判断信息是否已经存在
				//判断车辆信息是否已经存在，不存在，则不允许修改
				if(vehicleList == null || vehicleList.size() < 1){
					//如果不存在，则不允许修改
					throw new Exception("车辆信息不存在，车辆编号="+number);
				}
				if(vehicleList.size() > 1){
					throw new Exception("系统中该车辆信息重复，车辆编号="+number);
				}
				//3.校验所属单位id是否存在，如果不存在，则不允许修改
				//todo 需先完成基础服务平台 组织数据同步：传入的是4A数据中的code，需要从基础服务平台传过来的code映射到 blade_dept中，可以判断同步过来的组织数据中是否存在该组织

				//4.修改终端信息
				//根据终端类型保存终端
				BdmVehicle f = vehicleList.get(0);
				f.setNumber(number);
				f.setCategory(Integer.parseInt(vehicleType));
				//todo 设置 deptId，应使用 blade_dept 中的id
				f.setUpdateTime(new Date());
				vehicleNewService.updateById(f);

				//5.修改业务总表
				BdmAbstractTarget target = abstractTargetService.getById(f.getId());
				target.setNumber(number);
				target.setCategory(Integer.parseInt(vehicleType));
				abstractTargetService.updateById(target);

				//6.更新缓存
				String key = target.getTargetType() + "-" + target.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", target.getNumber());
				innerMap.put("targetType", target.getTargetType());
				innerMap.put("targetCategory", target.getCategory());
				innerMap.put("deptId", target.getDeptId());
				Map<String, String> map = new HashMap<>();
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				//todo：Map<String, String> map这样写go那边解析有问题
				redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_TARGET, map);

				//7.通知其他服务
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.UPDATE);
				deviceInfo.setTargetId(target.getId());
				deviceInfo.setTargetName(target.getNumber());
				deviceInfo.setTargetCategory(target.getCategory());
				deviceInfo.setTargetType(target.getTargetType());
				deviceInfo.setDeptId(target.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_vehicle_new", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("车辆信息更新消息发送到kafka失败", e);
				}

			}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
				//删除
				//删除：判断车辆信息是否存在
				//判断车辆信息是否存在
				/*if(vehicleList == null || vehicleList.size() < 1){
					//如果不存在，则不允许删除
					throw new Exception("车辆信息不存在，车辆编码="+number);
				}
				if(vehicleList.size() > 1){
					throw new Exception("系统中车辆信息重复，车辆编码="+number);
				}*/

				//1.删除车辆信息
				BdmVehicle f = vehicleList.get(0);
				f.setUpdateTime(new Date());
				f.setDeleted(1);
				vehicleNewService.updateById(f);

				//2.删除总表中的数据
				BdmAbstractTarget target = abstractTargetService.getById(f.getId());
				target.setDeleted(1);
				abstractTargetService.updateById(target);

				//3.删除缓存
				String key = CommonConstant.VEHICLE_TARGET_TYPE + "-" + f.getId();
				redisTemplate.delete(CommonConstant.BASEINFO_TARGET + ":" + key);

				//4.重置关联的终端信息
				Long[] ids = {target.getId()};
				rnssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.VEHICLE.getSymbol());
				abstractDeviceService.deleteByTargetIds(ids, TargetTypeEnum.VEHICLE.getSymbol());

				//5.通知其他服务
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.DELETE);
				Long lastId = ids[ids.length - 1];
				deviceInfo.setTargetId(lastId);
				deviceInfo.setTargetIds(CollUtil.newHashSet(ids));
				try {
					kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_vehicle_new", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("车辆信息更新消息发送到kafka失败", e);
				}
			}
		}
	}

	//RB0003 职工信息
	private void workerData(Message message) throws Exception{
		List<Staff> requestArray = null;
		try{
			requestArray = messageUtils.decryptBody(message, Staff.class);
			if(requestArray == null){
				throw new Exception("报文内容为空");
			}
		}catch (Exception e){
			log.error("报文解析失败",e);
			throw new Exception("报文解析失败"+e.getMessage());
		}
		for(Staff request : requestArray){
			//1.判断操作类型
			String operType = request.getOperateState();
			if(StringUtils.isEmpty(operType)){
				throw new Exception("未指定操作类型");
			}

			String staffCode = request.getStaffCode(); //员工编号
			String staffName = request.getStaffName(); //员工姓名
			String ownerId = request.getOwnerId(); //所属单位id
			Integer industry = request.getIndustry(); //行业类型
			Integer post = request.getPost(); //岗位类型
			String phone = request.getPhone(); //联系电话
			String ownerName = request.getOwnerName(); //所属企业名称
			String operTime = request.getOperateTime(); //操作时间 YYYYMMDDhhmiss

			List<BdmWorker> workerList = workerService.list(Wrappers.lambdaQuery(BdmWorker.class)
				.eq(BdmWorker::getWkno, staffCode).eq(BdmWorker::getDeleted, 0));

			if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
				//新增
				//规则：
				// a.姓名、工号、所属单位id 不能为空；
				// b.根据工号判断员工是否存在;
				// c.校验所属单位id是否存在
				//1.校验空值
				if(StringUtils.isEmpty(staffCode)){
					throw new Exception("工号不能为空");
				}
				if(StringUtils.isEmpty(staffName)){
					throw new Exception("姓名不能为空");
				}
				if(StringUtils.isEmpty(ownerId)){
					throw new Exception("所属单位id不能为空");
				}
				//2.判断信息是否已经存在
				// 判断员工是否已经存
				if(workerList != null && workerList.size() > 0){
					//如果已经存在，则不允许再添加
					throw new Exception("该职工信息已经存在,员工编号=" + staffCode);
				}
				//3.校验所属单位id是否存在，如果不存在，则不允许添加
				//todo 需先完成基础服务平台 组织数据同步：传入的是4A数据中的code，需要从基础服务平台传过来的code映射到 blade_dept中，可以判断同步过来的组织数据中是否存在该组织

				//4.保存员工信息
				BdmWorker f = new BdmWorker();
				TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(CommonConstant.TARGET_TYPE_WORKER, 0, 0);
				f.setId(targetId.nextId());
				f.setWkno(staffCode);
				f.setName(staffName);
				f.setIndustry(industry);
				f.setPost(post);
				f.setPhone(phone);
				f.setDeptId(Long.parseLong(ownerId));
				f.setCreateTime(new Date());
				f.setDeleted(0);
				workerService.save(f);

				//5.添加到业务总表
				BdmAbstractTarget target = new BdmAbstractTarget();
				target.setId(f.getId());
				target.setNumber(staffCode);
				target.setName(staffName);
				//目标类别，0-未知，1-车辆，2-人员，3-基础设施、4-集装箱、5-外包人员、6-访客、7-船舶、8-列车货箱
				target.setTargetType(CommonConstant.TARGET_TYPE_WORKER);
				target.setDeptId(Long.parseLong(ownerId));
				//不写入category，与base-info一致
				//target.setCategory(Integer.parseInt(vehicleType));
				target.setDeleted(0);
				abstractTargetService.save(target);

				//6.添加缓存信息
				Map<String, String> map = new HashMap<>();
				String key = CommonConstant.WORKER_TARGET_TYPE + "-" + f.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", f.getName() + "(" + f.getWkno() + ")");
				innerMap.put("targetType", f.getTargetType());
				innerMap.put("deptId", f.getDeptId());
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_TARGET, map);

				//7.通知其他服务
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.INSERT);
				deviceInfo.setTargetId(f.getId());
				deviceInfo.setTargetName(f.getName());
				deviceInfo.setTargetType(f.getTargetType());
				deviceInfo.setDeptId(f.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("人员信息更新消息发送到kafka失败", e);
				}

			}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
				//修改
				//规则：判断员工信息是否存在；判断单位id是否存在
				//2.判断信息是否已经存在
				//判断员工信息是否已经存在，不存在，则不允许修改
				if(workerList == null || workerList.size() < 1){
					//如果不存在，则不允许修改
					throw new Exception("员工信息不存在，员工编号="+staffCode);
				}
				if(workerList.size() > 1){
					throw new Exception("系统中该员工信息重复，员工编号="+staffCode);
				}
				//3.校验所属单位id是否存在，如果不存在，则不允许修改
				//todo 需先完成基础服务平台 组织数据同步：传入的是4A数据中的code，需要从基础服务平台传过来的code映射到 blade_dept中，可以判断同步过来的组织数据中是否存在该组织

				//4.修改职工信息
				//根据终端类型保存终端
				BdmWorker f = workerList.get(0);

				//修改之前记录原来的数据
				BdmWorker workerInDB = workerService.getById(f.getId());

				f.setWkno(staffCode);
				f.setName(staffName);
				f.setIndustry(industry);
				f.setPost(post);
				f.setPhone(phone);
				f.setDeptId(Long.parseLong(ownerId));
				f.setUpdateTime(new Date());
				workerService.updateById(f);

				//5.修改业务总表
				BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
				BeanUtils.copyProperties(f, abstractTarget);
				abstractTarget.setNumber(f.getWkno());
				abstractTargetService.updateById(abstractTarget);

				//6.更新缓存
				String key = CommonConstant.WORKER_TARGET_TYPE + "-" + f.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", f.getName() + "(" + f.getWkno() + ")");
				innerMap.put("targetType", f.getTargetType());
				innerMap.put("deptId", f.getDeptId());
				Map<String, String> map = new HashMap<>();
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				//todo：Map<String, String> map这样写go那边解析有问题
				redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_TARGET, map);

				//7.通知其他服务
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.UPDATE);
				deviceInfo.setTargetId(f.getId());
				deviceInfo.setTargetName(f.getName());
				deviceInfo.setTargetType(f.getTargetType());
				deviceInfo.setDeptId(f.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("人员信息更新消息发送到kafka失败", e);
				}

				//8.对于已绑定的设备进行部门更新操作
				//修改之后，查询新的数据
				BdmWorker worker = workerService.getById(f.getId());
				if (!workerInDB.getDeptId().equals(worker.getDeptId())) {
					// 对于WearableDevice的更新
					wearableDeviceService.updateDept(worker.getId(), worker.getTargetType(), worker.getDeptId());

					// 对于RdssDevice的更新
					rdssDeviceService.updateDept(worker.getId(), worker.getTargetType(), worker.getDeptId());

					// 对于RnssDevice的更新
					rnssDeviceService.updateDept(worker.getId(), worker.getTargetType(), worker.getDeptId());

					// 对于BdmAbstractDevice的更新
					abstractDeviceService.updateDept(worker.getId(), worker.getTargetType(), worker.getDeptId());
				}

			}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
				//删除
				//删除：判断职工信息是否存在
				//判断职工信息是否存在
				if(workerList == null || workerList.size() < 1){
					//如果不存在，则不允许删除
					throw new Exception("职工信息不存在，设施编码="+staffCode);
				}
				if(workerList.size() > 1){
					throw new Exception("系统中职工信息重复，设施编码="+staffCode);
				}

				//1.删除员工信息
				BdmWorker f = workerList.get(0);
				f.setUpdateTime(new Date());
				f.setDeleted(1);
				workerService.updateById(f);

				//2.删除业务总表
				Long[] ids = {f.getId()};
				abstractTargetService.deleteByIds(ids);

				//3.删除缓存
				String key = CommonConstant.WORKER_TARGET_TYPE + "-" + f.getId();
				redisTemplate.delete(CommonConstant.BASEINFO_TARGET + ":" + key);

				//4.通知其他服务
				DeviceInfo deviceInfo = new DeviceInfo();
				Long lastId = ids[ids.length - 1];
				deviceInfo.setTargetId(lastId);
				deviceInfo.setTargetIds(CollUtil.newHashSet(ids));
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("人员信息更新消息发送到kafka失败", e);
				}

				//5.重置关联的终端
				this.wearableDeviceService.deleteByTargetIds(ids, TargetTypeEnum.WORKER.getSymbol());
				this.rdssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.WORKER.getSymbol());
				this.rnssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.WORKER.getSymbol());
				abstractDeviceService.deleteByTargetIds(ids, TargetTypeEnum.WORKER.getSymbol());
			}
		}
	}

	//RB0004 基础设施
	private void facilityData(Message message) throws Exception{
		List<Facility> requestArray = null;
		try{
			requestArray = messageUtils.decryptBody(message, Facility.class);
			if(requestArray == null){
				throw new Exception("报文内容为空");
			}
		}catch (Exception e){
			log.error("报文解析失败",e);
			throw new Exception("报文解析失败"+e.getMessage());
		}

		for(Facility request : requestArray){
			//1.判断操作类型
			String operType = request.getOperateState();
			if(StringUtils.isEmpty(operType)){
				throw new Exception("未指定操作类型");
			}

			String name = request.getFacilityName(); //基础设施名称
			String code = request.getFacilityCode(); //基础设施编码
			Integer category = request.getFacilityType(); //基础设施类型
			List<Facility.Geometry> geometry = request.getGeometry(); //基础设施范围
			String address = request.getAddress(); //地址
			String ownerId = request.getOwnerId(); //所属单位id
			String operTime = request.getOperateTime(); //操作时间

			List<BdmFacility> facilityList = facilityService.list(Wrappers.lambdaQuery(BdmFacility.class)
				.eq(BdmFacility::getCode, code).eq(BdmFacility::getDeleted, 0));

			if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
				//新增
				//规则：
				// a.基础设施编码、基础设施名称、基础设施类型、所属单位id 不能为空；
				// b.根据基础设施编码判断基础设施是否存在;
				// c.校验所属单位id是否存在
				//1.校验空值
				if(StringUtils.isEmpty(code)){
					throw new Exception("设施编号不能为空");
				}
				if(StringUtils.isEmpty(name)){
					throw new Exception("设施名称不能为空");
				}
				if(category == null){
					throw new Exception("设施类型不能为空");
				}
				if(StringUtils.isEmpty(ownerId)){
					throw new Exception("所属单位id不能为空");
				}
				//2.判断信息是否已经存在
				// 判断基础设施是否已经存
				if(facilityList != null && facilityList.size() > 0){
					//如果已经存在，则不允许再添加
					throw new Exception("基础设施已经存在, 设施编号=" + code);
				}
				//3.校验所属单位id是否存在，如果不存在，则不允许添加
				//todo 需先完成基础服务平台 组织数据同步：传入的是4A数据中的code，需要从基础服务平台传过来的code映射到 blade_dept中，可以判断同步过来的组织数据中是否存在该组织

				//4.保存基础设施信息
				BdmFacility f = new BdmFacility();
				TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(CommonConstant.TARGET_TYPE_FACILITY, 0, 0);
				f.setId(targetId.nextId());
				f.setCode(code);
				f.setName(name);
				f.setCategory(category);
				f.setAddress(address);
				//todo 设置 deptId，应使用 blade_dept 中的id
				StringBuffer sb = new StringBuffer("");
				geometry.forEach(item -> {
					sb.append(item.getLongitude()).append(" ").append(item.getLatitude()).append(" ");
				});
				String geo = "";
				if(sb.length() > 0){
					geo = sb.substring(0, sb.length() - 1);
				}
				f.setGeometry(geo);
				f.setCreateTime(new Date());
				f.setDeleted(0);
				facilityService.save(f);

				//5.添加到业务总表
				BdmAbstractTarget target = new BdmAbstractTarget();
				target.setId(f.getId());
				target.setNumber(code);
				target.setName(name);
				//目标类别，0-未知，1-车辆，2-人员，3-基础设施、4-集装箱、5-外包人员、6-访客、7-船舶、8-列车货箱
				target.setTargetType(CommonConstant.TARGET_TYPE_FACILITY);
				target.setDeptId(Long.parseLong(ownerId));
				target.setCategory(f.getCategory());
				target.setDeleted(0);
				abstractTargetService.save(target);

				//6.添加缓存
				Map<String, String> map = new HashMap<>();
				String key = target.getTargetType() + "-" + target.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", target.getName());
				innerMap.put("targetType", target.getTargetType());
				innerMap.put("deptId", target.getDeptId());
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_TARGET, map);

				//7.通知其他服务
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.INSERT);
				deviceInfo.setTargetId(f.getId());
				deviceInfo.setTargetName(f.getName());
				deviceInfo.setTargetCategory(f.getCategory());
				deviceInfo.setDeptId(f.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_facility", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("设施管理信息更新消息发送到kafka失败", e.getMessage());
				}


			}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
				//修改
				//规则：判断设施是否存在；判断单位id是否存在
				//2.判断信息是否已经存在
				//判断设施编号是否已经存在，不存在，则不允许修改
				if(facilityList == null || facilityList.size() < 1){
					//如果不存在，则不允许修改
					throw new Exception("基础设施不存在，设施编号="+code);
				}
				if(facilityList.size() > 1){
					throw new Exception("系统中基础设施重复，设施编号="+code);
				}
				//3.校验所属单位id是否存在，如果不存在，则不允许修改
				//todo 需先完成基础服务平台 组织数据同步：传入的是4A数据中的code，需要从基础服务平台传过来的code映射到 blade_dept中，可以判断同步过来的组织数据中是否存在该组织

				//4.修改终端信息
				//根据终端类型保存终端
				BdmFacility f = facilityList.get(0);
				BdmFacility facilityInDB = facilityService.getById(f.getId());
				f.setCode(code);
				f.setName(name);
				f.setCategory(category);
				f.setAddress(address);
				//todo 设置dept_id，应使用blade_dept 中的id
				StringBuffer sb = new StringBuffer("");
				geometry.forEach(item -> {
					sb.append(item.getLongitude()).append(" ").append(item.getLatitude()).append(" ");
				});
				String geo = "";
				if(sb.length() > 0){
					geo = sb.substring(0, sb.length() - 1);
				}
				f.setGeometry(geo);
				f.setUpdateTime(new Date());
				facilityService.updateById(f);

				//5.修改业务总表
				BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
				BeanUtils.copyProperties(f, abstractTarget);
				abstractTarget.setNumber(f.getCode());
				abstractTargetService.updateById(abstractTarget);

				//6.修改缓存
				String key = f.getTargetType() + "-" + f.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", f.getName());
				innerMap.put("targetType", f.getTargetType());
				innerMap.put("deptId", f.getDeptId());
				Map<String, String> map = new HashMap<>();
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
				redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_TARGET, map);

				//7.通知其他服务
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.UPDATE);
				deviceInfo.setTargetId(f.getId());
				deviceInfo.setTargetName(f.getName());
				deviceInfo.setTargetType(f.getTargetType());
				deviceInfo.setTargetCategory(f.getCategory());
				deviceInfo.setDeptId(f.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_facility", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("设施管理信息更新消息发送到kafka失败", e);
				}

				//8.更新关联终端
				BdmFacility facility = facilityService.getById(f.getId());
				if(!facilityInDB.getDeptId().equals(facility.getDeptId())){
					// 对于MntDevice的更新
					pntDeviceService.updateDept(facility.getId(),facility.getTargetType(),facility.getDeptId());

					// 对于MonitDevic的更新
					monitDeviceService.updateDept(facility.getId(),facility.getTargetType(),facility.getDeptId());

					// 对于BdmAbstractDevice的更新
					abstractDeviceService.updateDept(facility.getId(),facility.getTargetType(),facility.getDeptId());
				}

			}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
				//删除
				//删除：判断基础设施是否存在
				//判断基础设施是否存在
				if(facilityList == null || facilityList.size() < 1){
					//如果不存在，则不允许删除
					throw new Exception("基础设施不存在，设施编码="+code);
				}
				if(facilityList.size() > 1){
					throw new Exception("系统中基础设施重复，设施编码="+code);
				}

				//1.删除基础设施
				BdmFacility f = facilityList.get(0);
				f.setUpdateTime(new Date());
				f.setDeleted(1);
				facilityService.updateById(f);

				//2.删除业务总表
				BdmAbstractTarget target = abstractTargetService.getById(f.getId());
				target.setDeleted(1);
				abstractTargetService.updateById(target);

				//3.删除缓存
				String key = CommonConstant.FACILITY_TARGET_TYPE + "-" + f.getId();
				redisTemplate.delete(CommonConstant.BASEINFO_TARGET + ":" + key);

				//4.重置关联的终端
				Long[] ids = {f.getId()};
				monitDeviceService.deleteByTargetIds(ids);
				pntDeviceService.deleteByTargetIds(ids);
				abstractDeviceService.deleteByTargetIds(ids, TargetTypeEnum.FACILITY.getSymbol());

				//5.通知其他服务
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.DELETE);
				deviceInfo.setTargetId(f.getId());
				deviceInfo.setTargetIds(CollUtil.newHashSet(ids));
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_facility", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("设施管理信息更新消息发送到kafka失败", e);
				}
			}
		}
	}

	//RB0005 终端信息
	private void terminalInfo(Message message) throws Exception{
		List<TerminalInfo> requestArray = null;
		try{
			requestArray = messageUtils.decryptBody(message, TerminalInfo.class);
			if(requestArray == null){
				throw new Exception("报文内容为空");
			}
		}catch (Exception e){
			log.error("报文解析失败",e);
			throw new Exception("报文解析失败"+e.getMessage());
		}

		for(TerminalInfo request : requestArray){
			//1.判断操作类型
			String operType = request.getOperateState();
			if(StringUtils.isEmpty(operType)){
				throw new Exception("未指定操作类型");
			}

			String uniqueId = request.getDeviceID(); //终端编号（出厂序列号）
			Integer deviceKind = request.getDeviceCategory(); //终端类别
			Integer deviceType = request.getDeviceType(); //终端类型
			String imei = request.getImei(); //imei
			String deviceModel = request.getDeviceModel(); //终端型号
			String vendorId = request.getVendorId(); //厂商编号
			String deviceNum = request.getTagcode(); //赋码值
			String ownerId = request.getOwnerId(); //所属单位id
			if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
				//新增
				//规则：
				// a.终端id、终端类别、终端类型、终端型号、终端厂商、所属单位ID 不能为空；
				// b.根据赋码值查询终端是否存在;
				// c.校验IMEI号是否存在
				// d.校验所属单位id是否存在
				//1.校验空值
				if(StringUtils.isEmpty(uniqueId)){
					throw new Exception("终端编号不能为空");
				}
				if(deviceKind == null){
					throw new Exception("终端列别不能为空");
				}
				if(deviceType == null){
					throw new Exception("终端类型不能为空");
				}
				if(StringUtils.isEmpty(imei)){
					throw new Exception("IMEI不能为空");
				}
				if(StringUtils.isEmpty(deviceModel)){
					throw new Exception("终端型号不能为空");
				}
				if(StringUtils.isEmpty(ownerId)){
					throw new Exception("所属单位id不能为空");
				}
				//2.判断信息是否已经存在
				//2 判断终端id是否已经存在
				if(terminalService.checkExistByUniqueId(uniqueId)){
					//如果已经存在，则不允许再添加
					throw new Exception("终端ID已经存在");
				}
				//2.根据赋码值查询终端是否存在：如果存在，则不允许新增
				if(!StringUtils.isEmpty(deviceNum) && terminalService.checkExistByDeviceNum(deviceNum)){
					//如果已经存在，则不允许再添加
					throw new Exception("赋码值已存在");
				}
				//3.校验imei号是否存在：如果存在，则不允许新增
				if(terminalService.checkExistByImei(imei)){
					//如果已经存在，则不允许再添加
					throw new Exception("imei号已经存在");
				}
				//4.校验所属单位id是否存在，如果不存在，则不允许添加
				//todo 需先完成基础服务平台 组织数据同步：传入的是4A数据中的code，需要从基础服务平台传过来的code映射到 blade_dept中，可以判断同步过来的组织数据中是否存在该组织

				//5.保存终端信息
				//todo 应该同时向终端总表中写入数据
				//5.1 向总表中写入终端信息


				//5.2 根据终端类型保存终端

				if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_RNSS)){
					//北斗定位终端
					BdmRnssDevice rnss = new BdmRnssDevice();
					rnss.setUniqueId(uniqueId);
					rnss.setImei(imei);
					rnss.setModel(deviceModel);
					rnss.setVendor(vendorId);
					rnss.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RNSS));
					//todo 所属部门id应该映射成blade_dept中的id
					//rnss.setDeptId();
					rnss.setDeviceNum(deviceNum);
					rnss.setScenario(request.getScenario());
					rnss.setDomain(request.getDomain());
					rnss.setGnssMode(request.getLocateMode());
					rnss.setCreateTime(new Date());
					rnss.setDeleted(0);
					rnssDeviceService.save(rnss);
				}else if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_WEARABLE)){
					//穿戴式终端
					BdmWearableDevice wear = new BdmWearableDevice();
					wear.setUniqueId(uniqueId);
					wear.setImei(imei);
					wear.setModel(deviceModel);
					wear.setVendor(vendorId);
					wear.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_WEARABLE));
					//todo 所属部门id应该映射成blade_dept中的id
					//rnss.setDeptId();
					wear.setScenario(request.getScenario());
					wear.setDomain(request.getDomain());
					wear.setGnssMode(request.getLocateMode());
					wear.setDeviceNum(deviceNum);
					wear.setCreateTime(new Date());
					wear.setDeleted(0);
					wearableDeviceService.save(wear);
				}else if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_RDSS)){
					//北斗短报文终端
					BdmRdssDevice rdss = new BdmRdssDevice();
					rdss.setUniqueId(uniqueId);
					rdss.setImei(imei);
					rdss.setModel(deviceModel);
					rdss.setVendor(vendorId);
					rdss.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RDSS));
					//todo 所属部门id应该映射成blade_dept中的id
					//rnss.setDeptId();
					rdss.setScenario(request.getScenario());
					rdss.setDomain(request.getDomain());
					rdss.setGnssMode(request.getLocateMode());
					rdss.setDeviceNum(deviceNum);
					rdss.setCreateTime(new Date());
					rdss.setDeleted(0);
					rdssDeviceService.save(rdss);
				}else if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_MONIT)){
					//北斗监测终端
					BdmMonitDevice monit = new BdmMonitDevice();
					monit.setUniqueId(uniqueId);
					monit.setImei(imei);
					monit.setModel(deviceModel);
					monit.setVendor(vendorId);
					monit.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RDSS));
					//todo 所属部门id应该映射成blade_dept中的id
					//rnss.setDeptId();
					monit.setScenario(request.getScenario());
					monit.setDomain(request.getDomain());
					monit.setGnssMode(request.getLocateMode());
					monit.setDeviceNum(deviceNum);
					monit.setCreateTime(new Date());
					monit.setDeleted(0);
					monitDeviceService.save(monit);
				}else if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_PNT)){
					//北斗授时终端
					BdmPntDevice pnt = new BdmPntDevice();
					pnt.setUniqueId(uniqueId);
					pnt.setImei(imei);
					pnt.setModel(deviceModel);
					pnt.setVendor(vendorId);
					pnt.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RDSS));
					//todo 所属部门id应该映射成blade_dept中的id
					//rnss.setDeptId();
					pnt.setScenario(request.getScenario());
					pnt.setDomain(request.getDomain());
					pnt.setGnssMode(request.getLocateMode());
					pnt.setDeviceNum(deviceNum);
					pnt.setCreateTime(new Date());
					pnt.setDeleted(0);
					pntDeviceService.save(pnt);
				}
			}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
				//修改
				//2.判断信息是否已经存在
				//2.1 判断终端id是否已经存在，不存在，则不允许修改
				if(!terminalService.checkExistByUniqueId(uniqueId)){
					//如果不存在，则不允许修改
					throw new Exception("终端不存在，终端ID="+uniqueId);
				}
				if(terminalService.findDeviceCountByUniqueId(uniqueId) > 1){
					throw new Exception("系统中终端重复，终端ID="+uniqueId);
				}
				//2.2.根据赋码值查询终端是否存在：如果不存在，则不允许修改
				if(!StringUtils.isEmpty(deviceNum) && !terminalService.checkExistByDeviceNum(deviceNum)){
					//如果不存在，则不允许修改
					throw new Exception("终端不存在，赋码值="+deviceNum);
				}
				if(terminalService.findDeviceCountByDeviceNum(deviceNum) > 1){
					throw new Exception("系统中终端重复，赋码值=" + deviceNum);
				}
				//3.校验imei号是否存在：如果不存在，则不允许修改
				if(!terminalService.checkExistByImei(imei)){
					//如果不存在，则不允许修改
					throw new Exception("终端不存在，imei=" + imei);
				}
				if(terminalService.findDeviceCountByImei(imei) > 1){
					throw new Exception("终端不存在，imei=" + imei);
				}
				//4.校验所属单位id是否存在，如果不存在，则不允许修改
				//todo 需先完成基础服务平台 组织数据同步：传入的是4A数据中的code，需要从基础服务平台传过来的code映射到 blade_dept中，可以判断同步过来的组织数据中是否存在该组织

				//5.修改终端信息
				//根据终端类型保存终端
				if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_RNSS)){
					//北斗定位终端
					//查询终端
					BdmRnssDevice rnss = rnssDeviceService.getOne(Wrappers.lambdaQuery(BdmRnssDevice.class).eq(BdmRnssDevice::getUniqueId, uniqueId)
						.eq(BdmRnssDevice::getDeleted, 0));

					rnss.setUniqueId(uniqueId);
					rnss.setImei(imei);
					rnss.setModel(deviceModel);
					rnss.setVendor(vendorId);
					rnss.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RNSS));
					//todo 所属部门id应该映射成blade_dept中的id
					//rnss.setDeptId();
					rnss.setDeviceNum(deviceNum);
					rnss.setScenario(request.getScenario());
					rnss.setDomain(request.getDomain());
					rnss.setGnssMode(request.getLocateMode());
					rnss.setUpdateTime(new Date());
					rnssDeviceService.updateById(rnss);
				}else if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_WEARABLE)){
					//穿戴式终端
					//查询终端
					BdmWearableDevice wear = wearableDeviceService.getOne(Wrappers.lambdaQuery(BdmWearableDevice.class)
						.eq(BdmWearableDevice::getUniqueId, uniqueId).eq(BdmWearableDevice::getDeleted, 0));
					wear.setUniqueId(uniqueId);
					wear.setImei(imei);
					wear.setModel(deviceModel);
					wear.setVendor(vendorId);
					wear.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_WEARABLE));
					//todo 所属部门id应该映射成blade_dept中的id
					//rnss.setDeptId();
					wear.setScenario(request.getScenario());
					wear.setDomain(request.getDomain());
					wear.setGnssMode(request.getLocateMode());
					wear.setDeviceNum(deviceNum);
					wear.setUpdateTime(new Date());
					wearableDeviceService.updateById(wear);
				}else if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_RDSS)){
					//北斗短报文终端
					//查询终端
					BdmRdssDevice rdss = rdssDeviceService.getOne(Wrappers.lambdaQuery(BdmRdssDevice.class)
						.eq(BdmRdssDevice::getUniqueId, uniqueId).eq(BdmRdssDevice::getDeleted, 0));
					rdss.setUniqueId(uniqueId);
					rdss.setImei(imei);
					rdss.setModel(deviceModel);
					rdss.setVendor(vendorId);
					rdss.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RDSS));
					//todo 所属部门id应该映射成blade_dept中的id
					//rnss.setDeptId();
					rdss.setScenario(request.getScenario());
					rdss.setDomain(request.getDomain());
					rdss.setGnssMode(request.getLocateMode());
					rdss.setDeviceNum(deviceNum);
					rdss.setUpdateTime(new Date());
					rdssDeviceService.updateById(rdss);
				}else if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_MONIT)){
					//北斗监测终端
					//查询终端
					BdmMonitDevice monit = monitDeviceService.getOne(Wrappers.lambdaQuery(BdmMonitDevice.class)
						.eq(BdmMonitDevice::getUniqueId, uniqueId).eq(BdmMonitDevice::getDeleted, 0));
					monit.setUniqueId(uniqueId);
					monit.setImei(imei);
					monit.setModel(deviceModel);
					monit.setVendor(vendorId);
					monit.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RDSS));
					//todo 所属部门id应该映射成blade_dept中的id
					//rnss.setDeptId();
					monit.setScenario(request.getScenario());
					monit.setDomain(request.getDomain());
					monit.setGnssMode(request.getLocateMode());
					monit.setDeviceNum(deviceNum);
					monit.setUpdateTime(new Date());
					monitDeviceService.save(monit);
				}else if(deviceKind == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_PNT)){
					//北斗授时终端
					//查询终端
					BdmPntDevice pnt = pntDeviceService.getOne(Wrappers.lambdaQuery(BdmPntDevice.class)
						.eq(BdmPntDevice::getUniqueId, uniqueId).eq(BdmPntDevice::getDeleted, 0));
					pnt.setUniqueId(uniqueId);
					pnt.setImei(imei);
					pnt.setModel(deviceModel);
					pnt.setVendor(vendorId);
					pnt.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RDSS));
					//todo 所属部门id应该映射成blade_dept中的id
					//rnss.setDeptId();
					pnt.setScenario(request.getScenario());
					pnt.setDomain(request.getDomain());
					pnt.setGnssMode(request.getLocateMode());
					pnt.setDeviceNum(deviceNum);
					pnt.setUpdateTime(new Date());
					pntDeviceService.save(pnt);
				}

			}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
				//删除
				//判断终端编号是否存在
				if(!terminalService.checkExistByUniqueId(uniqueId)){
					//如果不存在，则不允许删除
					throw new Exception("终端不存在，终端ID="+uniqueId);
				}
				if(terminalService.findDeviceCountByUniqueId(uniqueId) > 1){
					throw new Exception("系统中终端重复，终端ID="+uniqueId);
				}

				//5.修改终端信息
				//根据终端类型删除终端
				if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_RNSS)){
					//北斗定位终端
					//查询终端
					BdmRnssDevice rnss = rnssDeviceService.getOne(Wrappers.lambdaQuery(BdmRnssDevice.class).eq(BdmRnssDevice::getUniqueId, uniqueId)
						.eq(BdmRnssDevice::getDeleted, 0));
					rnss.setUpdateTime(new Date());
					rnss.setDeleted(1);
					rnssDeviceService.updateById(rnss);
				}else if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_WEARABLE)){
					//穿戴式终端
					//查询终端
					BdmWearableDevice wear = wearableDeviceService.getOne(Wrappers.lambdaQuery(BdmWearableDevice.class)
						.eq(BdmWearableDevice::getUniqueId, uniqueId).eq(BdmWearableDevice::getDeleted, 0));
					wear.setUpdateTime(new Date());
					wear.setDeleted(1);
					wearableDeviceService.updateById(wear);
				}else if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_RDSS)){
					//北斗短报文终端
					//查询终端
					BdmRdssDevice rdss = rdssDeviceService.getOne(Wrappers.lambdaQuery(BdmRdssDevice.class)
						.eq(BdmRdssDevice::getUniqueId, uniqueId).eq(BdmRdssDevice::getDeleted, 0));
					rdss.setUpdateTime(new Date());
					rdss.setDomain(1);
					rdssDeviceService.updateById(rdss);
				}else if(deviceType == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_MONIT)){
					//北斗监测终端
					//查询终端
					BdmMonitDevice monit = monitDeviceService.getOne(Wrappers.lambdaQuery(BdmMonitDevice.class)
						.eq(BdmMonitDevice::getUniqueId, uniqueId).eq(BdmMonitDevice::getDeleted, 0));
					monit.setUpdateTime(new Date());
					monit.setDeleted(1);
					monitDeviceService.save(monit);
				}else if(deviceKind == Integer.parseInt(CommonConstant.PLAT_DEVICE_TYPE_PNT)){
					//北斗授时终端
					//查询终端
					BdmPntDevice pnt = pntDeviceService.getOne(Wrappers.lambdaQuery(BdmPntDevice.class)
						.eq(BdmPntDevice::getUniqueId, uniqueId).eq(BdmPntDevice::getDeleted, 0));
					pnt.setUpdateTime(new Date());
					pnt.setDeleted(1);
					pntDeviceService.save(pnt);
				}

			}
		}
	}

	//RB0005 铁路货车车厢
	private void trainCargoBoxData (Message message) throws Exception {
		List<TrainCargoBox> tmpList = this.messageUtils.decryptBody(message, TrainCargoBox.class);
		if (CollectionUtils.isEmpty(tmpList)) {
			throw new Exception("未能解析到铁路货车车厢数据。");
		}

		Date date = new Date();
		BdmTrainCargoBox trainCargoBox;
		for (TrainCargoBox tmp : tmpList) {
			String coachNo = tmp.getCoachNo();
			BdmTrainCargoBox temp = this.trainCargoBoxService.getOne(
				Wrappers.lambdaQuery(BdmTrainCargoBox.class).eq(BdmTrainCargoBox::getNumber, coachNo)
			);
			switch (tmp.getOperateState()) {
				case CommonConstant.OPER_TYPE_ADD:
					if (temp != null) {
						throw new Exception("铁路货车车厢信息已存在，车厢编号：" + coachNo);
					}
					//1.新增基础信息
					trainCargoBox = new BdmTrainCargoBox();
					TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(CommonConstant.BOX_TARGET_TYPE, 0, 0);
					trainCargoBox.setId(targetId.nextId());
					trainCargoBox.setNumber(coachNo);
					trainCargoBox.setTargetType(Integer.parseInt(tmp.getCoachType().substring(0, 1)));
					trainCargoBox.setModel(Integer.parseInt(tmp.getCoachType()));
					trainCargoBox.setDeptId(Long.parseLong(tmp.getOwnerId()));
					trainCargoBox.setTare(Float.parseFloat(tmp.getCoachWeight()));
					trainCargoBox.setCuCap(Float.parseFloat(tmp.getCoachVolume()));
					trainCargoBox.setCreateTime(date);
					trainCargoBox.setUpdateTime(date);
					this.trainCargoBoxService.save(trainCargoBox);

					//2.添加到业务总表
					BdmTrainCargoBox cargoBox = trainCargoBoxService.getById(trainCargoBox.getId());
					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(cargoBox, abstractTarget);
					abstractTarget.setName(cargoBox.getNumber());
					abstractTarget.setCreateAccount(AuthUtil.getUserAccount());
					abstractTargetService.save(abstractTarget);

					//3.添加缓存
					Map<String, String> map = new HashMap<>();
					String key = cargoBox.getTargetType() + "-" + cargoBox.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", cargoBox.getNumber());
					innerMap.put("targetType", cargoBox.getTargetType());
					innerMap.put("deptId", cargoBox.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
					redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_TARGET, map);

					//4.通知其他服务
					DeviceInfo deviceInfo = new DeviceInfo();
					deviceInfo.setOperation(Operation.INSERT);
					deviceInfo.setTargetId(cargoBox.getId());
					deviceInfo.setTargetName(cargoBox.getNumber());
					deviceInfo.setDeptId(cargoBox.getDeptId());
					try {
						this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_train_cargo_box", JSON.toJSONString(deviceInfo));
					} catch (Exception e) {
						log.error("铁路货车车厢信息更新消息发送到kafka失败", e);
					}

					break;
				case CommonConstant.OPER_TYPE_UPDATE:
					if (temp == null) {
						throw new Exception("铁路货车车厢信息不存在，车厢编号：" + coachNo);
					}
					//1.修改基础信息
					trainCargoBox = new BdmTrainCargoBox();
					BdmTrainCargoBox trainCargoBoxInDB = trainCargoBoxService.getById(temp.getId());
					trainCargoBox.setId(temp.getId());
					trainCargoBox.setModel(Integer.parseInt(tmp.getCoachType()));
					trainCargoBox.setDeptId(Long.parseLong(tmp.getOwnerId()));
					trainCargoBox.setTare(Float.parseFloat(tmp.getCoachWeight()));
					trainCargoBox.setCuCap(Float.parseFloat(tmp.getCoachVolume()));
					trainCargoBox.setUpdateTime(date);
					this.trainCargoBoxService.updateById(trainCargoBox);

					//2.修改业务总表
					BdmAbstractTarget abstractTargetTrainCargoBox = new BdmAbstractTarget();
					BeanUtils.copyProperties(trainCargoBox, abstractTargetTrainCargoBox);
					abstractTargetTrainCargoBox.setName(trainCargoBox.getNumber());
					abstractTargetService.updateById(abstractTargetTrainCargoBox);

					//3.修改缓存
					Map<String, String> mapU = new HashMap<>();
					String keyU = abstractTargetTrainCargoBox.getTargetType() + "-" + abstractTargetTrainCargoBox.getId();
					Map<String, Object> innerMapU = new HashMap<>();
					innerMapU.put("targetName", abstractTargetTrainCargoBox.getNumber());
					innerMapU.put("targetType", abstractTargetTrainCargoBox.getTargetType());
					innerMapU.put("deptId", abstractTargetTrainCargoBox.getDeptId());
					try {
						mapU.put(keyU, new ObjectMapper().writeValueAsString(innerMapU));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
					redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_TARGET, mapU);

					//4.通知其他服务
					DeviceInfo deviceInfoU = new DeviceInfo();
					deviceInfoU.setOperation(Operation.UPDATE);
					deviceInfoU.setTargetId(abstractTargetTrainCargoBox.getId());
					deviceInfoU.setTargetName(abstractTargetTrainCargoBox.getNumber());
					deviceInfoU.setDeptId(abstractTargetTrainCargoBox.getDeptId());
					try {
						this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_train_cargo_box", JSON.toJSONString(deviceInfoU));
					} catch (Exception e) {
						log.error("铁路货车车厢信息更新消息发送到kafka失败", e);
					}

					//5.更新相应终端的组织
					BdmTrainCargoBox box = trainCargoBoxService.getById(trainCargoBox.getId());
					if(!trainCargoBoxInDB.getDeptId().equals(box.getDeptId())){
						// 对于RnssDevice的更新
						rnssDeviceService.updateDept(box.getId(),box.getTargetType(),box.getDeptId());

						// 对于BdmAbstractDevice的更新
						abstractDeviceService.updateDept(box.getId(),box.getTargetType(),box.getDeptId());
					}

					break;
				case CommonConstant.OPER_TYPE_DELETE:
					if (temp == null) {
						throw new Exception("铁路货车车厢信息不存在，车厢编号：" + coachNo);
					}
					//1.删除基础信息
					temp.setUpdateTime(date);
					temp.setDeleted(1);
					this.trainCargoBoxService.updateById(temp);

					//2.删除主表
					Long[] idsArray = {temp.getId()};
					abstractTargetService.deleteByIds(idsArray);

					//3.重置关联的终端
					rnssDeviceService.deleteByTargetIds(idsArray,TargetTypeEnum.PRECISION.getSymbol());
					abstractDeviceService.deleteByTargetIds(idsArray, TargetTypeEnum.BOX.getSymbol());

					//4.删除缓存
					String keyD = CommonConstant.BOX_TARGET_TYPE + "-" + temp.getId();
					redisTemplate.delete(CommonConstant.BASEINFO_TARGET + ":" + keyD);

					//5.通知其他服务
					DeviceInfo deviceInfoD = new DeviceInfo();
					deviceInfoD.setOperation(Operation.DELETE);
					deviceInfoD.setTargetId(temp.getId());
					deviceInfoD.setTargetIds(CollUtil.newHashSet(idsArray));
					try {
						this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_train_cargo_box", JSON.toJSONString(deviceInfoD));
					} catch (Exception e) {
						log.error("铁路货车车厢信息更新消息发送到kafka失败", e);
					}
					break;
				default:
					throw new Exception("操作类型不正确。");
			}
		}
	}

	//RB0006 矿用卡车信息
	private void mineTruckData (Message message) throws Exception {
		List<MineTruck> tmpList = this.messageUtils.decryptBody(message, MineTruck.class);
		if (CollectionUtils.isEmpty(tmpList)) {
			throw new Exception("未能解析到矿用卡车数据。");
		}

		Date date = new Date();
		BdmVehicle vehicle;
		for (MineTruck tmp : tmpList) {
			String vehicleNo = tmp.getVehicleNo();
			BdmVehicle temp = this.vehicleNewService.getOne(
				Wrappers.lambdaQuery(BdmVehicle.class).eq(BdmVehicle::getNumber, vehicleNo)
			);
			switch (tmp.getOperateState()) {
				case CommonConstant.OPER_TYPE_ADD:
					if (temp != null) {
						throw new Exception("车辆信息已存在，车辆编号：" + vehicleNo);
					}

					//1.保存基础信息
					vehicle = new BdmVehicle();
					TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(CommonConstant.TRUCK_TARGET_TYPE, 0, 0);
					vehicle.setId(targetId.nextId());
					vehicle.setNumber(vehicleNo);
					vehicle.setTargetType(Integer.parseInt(tmp.getVehicleType().substring(0, 1)));
					vehicle.setCategory(Integer.parseInt(tmp.getVehicleType()));
					vehicle.setDeptId(Long.parseLong(tmp.getOwnerId()));
					vehicle.setVin(tmp.getVin());
					vehicle.setManufacturer(tmp.getManufacturer());
					vehicle.setModel(tmp.getModel());
					vehicle.setMaxPower(Float.parseFloat(tmp.getMaxPower()));
					vehicle.setRatedLoad(Float.parseFloat(tmp.getRatedLoad()));
					vehicle.setCreateTime(date);
					vehicle.setUpdateTime(date);
					this.vehicleNewService.save(vehicle);

					//2.保存业务总表
					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(vehicle, abstractTarget);
					abstractTarget.setName(vehicle.getNumber());
					abstractTargetService.save(abstractTarget);

					//3.保存缓存
					//todo 写到了这里
					/*BdmMiningTruck truck = this.getBaseMapper().selectById(bdmMiningTruck.getId());

					Map<String, String> map = new HashMap<>();
					String key = BaseInfoConstants.TRUCK_TARGET_TYPE + "-" + truck.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", truck.getNumber());
					innerMap.put("targetType", truck.getTargetType());
					innerMap.put("targetCategory", truck.getCategory());
					innerMap.put("deptId", truck.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
					redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);*/



					break;
				case CommonConstant.OPER_TYPE_UPDATE:
					if (temp == null) {
						throw new Exception("车辆信息不存在，车辆编号：" + vehicleNo);
					}

					vehicle = new BdmVehicle();
					vehicle.setId(temp.getId());
					vehicle.setCategory(Integer.parseInt(tmp.getVehicleType()));
					vehicle.setDeptId(Long.parseLong(tmp.getOwnerId()));
					vehicle.setMaxPower(Float.parseFloat(tmp.getMaxPower()));
					vehicle.setManufacturer(tmp.getManufacturer());
					vehicle.setRatedLoad(Float.parseFloat(tmp.getRatedLoad()));
					vehicle.setModel(tmp.getModel());
					vehicle.setUpdateTime(date);
					this.vehicleNewService.updateById(vehicle);
					break;
				case CommonConstant.OPER_TYPE_DELETE:
					if (temp == null) {
						throw new Exception("车辆信息不存在，车辆编号：" + vehicleNo);
					}

					temp.setUpdateTime(date);
					temp.setDeleted(1);
					this.vehicleNewService.updateById(temp);
					break;
				default:
					throw new Exception("操作类型不正确。");
			}
		}
	}

	//RB0007 精密装备信息
	private void precisionAssemblyData (Message message) throws Exception {
		List<PrecisionAssembly> tmpList = this.messageUtils.decryptBody(message, PrecisionAssembly.class);
		if (CollectionUtils.isEmpty(tmpList)) {
			throw new Exception("未能解析到精密装备数据。");
		}

		Date date = new Date();
		BdmPrecisionAssembly precisionAssembly;
		for (PrecisionAssembly tmp : tmpList) {
			String deviceId = tmp.getDeviceId();
			BdmPrecisionAssembly temp = this.precisionAssemblyService.getOne(
				Wrappers.lambdaQuery(BdmPrecisionAssembly.class).eq(BdmPrecisionAssembly::getNumber, deviceId)
			);
			switch (tmp.getOperateState()) {
				case CommonConstant.OPER_TYPE_ADD:
					if (temp != null) {
						throw new Exception("精密装备信息已存在，装备编号：" + deviceId);
					}

					//1.保存基础数据
					precisionAssembly = new BdmPrecisionAssembly();
					TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(CommonConstant.PRECISION_TARGET_TYPE, 0, 0);
					precisionAssembly.setId(targetId.nextId());
					precisionAssembly.setNumber(deviceId);
					precisionAssembly.setName(tmp.getDeviceName());
					precisionAssembly.setManufacturer(tmp.getManufacturer());
					precisionAssembly.setModel(tmp.getModel());
					precisionAssembly.setDeptId(Long.parseLong(tmp.getOwnerId()));
					precisionAssembly.setCreateTime(date);
					precisionAssembly.setUpdateTime(date);
					this.precisionAssemblyService.save(precisionAssembly);

					//2.保存业务主表
					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(precisionAssembly, abstractTarget);
					abstractTargetService.save(abstractTarget);

					//3.保存缓存
					Map<String, String> map = new HashMap<>();
					String key = precisionAssembly.getTargetType() + "-" + precisionAssembly.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", precisionAssembly.getNumber());
					innerMap.put("targetType", precisionAssembly.getTargetType());
					innerMap.put("deptId", precisionAssembly.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
					redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_TARGET, map);


					break;
				case CommonConstant.OPER_TYPE_UPDATE:
					if (temp == null) {
						throw new Exception("精密装备信息不存在，装备编号：" + deviceId);
					}

					precisionAssembly = new BdmPrecisionAssembly();
					precisionAssembly.setId(temp.getId());
					precisionAssembly.setName(tmp.getDeviceName());
					precisionAssembly.setManufacturer(tmp.getManufacturer());
					precisionAssembly.setModel(tmp.getModel());
					precisionAssembly.setDeptId(Long.parseLong(tmp.getOwnerId()));
					precisionAssembly.setCreateTime(date);
					precisionAssembly.setUpdateTime(date);
					this.precisionAssemblyService.save(precisionAssembly);
					break;
				case CommonConstant.OPER_TYPE_DELETE:
					if (temp == null) {
						throw new Exception("精密装备信息不存在，装备编号：" + deviceId);
					}

					temp.setUpdateTime(date);
					temp.setDeleted(1);
					this.precisionAssemblyService.updateById(temp);
				default:
					throw new Exception("操作类型不正确。");
			}
		}
	}

	//RB0008 货船信息
	private void shipData (Message message) throws Exception {
		List<Ship> tmpList = this.messageUtils.decryptBody(message, Ship.class);
		if (CollectionUtils.isEmpty(tmpList)) {
			throw new Exception("未能解析到货船数据。");
		}

		Date date = new Date();
		BdmShip ship;
		for (Ship tmp : tmpList) {
			String vesselNo = tmp.getVesselNo();
			BdmShip temp = this.shipService.getOne(
				Wrappers.lambdaQuery(BdmShip.class).eq(BdmShip::getNumber, vesselNo)
			);
			switch (tmp.getOperateState()) {
				case CommonConstant.OPER_TYPE_ADD:
					if (temp != null) {
						throw new Exception("货船信息已存在，货船编号：" + vesselNo);
					}
					//1.保存基础数据
					ship = new BdmShip();
					TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker( CommonConstant.SHIP_TARGET_TYPE, 0, 0);
					ship.setId(targetId.nextId());
					ship.setNumber(vesselNo);
					ship.setName(tmp.getVesselNameCn());
					ship.setNameEn(tmp.getVesselNameEn());
					ship.setMmsi(tmp.getVesselMmsi());
					ship.setImoNumber(tmp.getVesselImo());
					ship.setCallSign(tmp.getVesselCallSign());
					ship.setLength(Integer.parseInt(tmp.getVesselLength()));
					ship.setBreadth(Integer.parseInt(tmp.getVesselWidth()));
					ship.setMaxGross(Float.valueOf(tmp.getVesselGt()));
					ship.setNet(Float.valueOf(tmp.getVesselDwt()));
					ship.setDeptId(Long.parseLong(tmp.getOwnerId()));
					ship.setCreateTime(date);
					ship.setUpdateTime(date);
					this.shipService.save(ship);

					//2.保存业务总表
					BdmShip shipD = shipService.getById(ship.getId());
					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(shipD, abstractTarget);
					abstractTargetService.save(abstractTarget);

					//3.添加缓存
					Map<String, String> map = new HashMap<>();
					String key = ship.getTargetType() + "-" + ship.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", ship.getNumber());
					innerMap.put("targetType", ship.getTargetType());
					innerMap.put("targetCategory", ship.getCategory());
					innerMap.put("deptId", ship.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
					redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_TARGET, map);

					//4.通知其他服务
					DeviceInfo deviceInfo = new DeviceInfo();
					deviceInfo.setOperation(Operation.INSERT);
					deviceInfo.setTargetId(ship.getId());
					deviceInfo.setTargetName(ship.getNumber());
					deviceInfo.setDeptId(ship.getDeptId());
					try {
						this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_ship", JSON.toJSONString(deviceInfo));
					} catch (Exception e) {
						log.error("货船信息更新消息发送到kafka失败", e);
					}

					break;
				case CommonConstant.OPER_TYPE_UPDATE:
					if (temp == null) {
						throw new Exception("货船信息不存在，车厢编号：" + vesselNo);
					}

					//1.更新基础数据
					ship = new BdmShip();
					ship.setId(temp.getId());
					BdmShip shipInDB = shipService.getById(ship.getId());
					ship.setName(tmp.getVesselNameCn());
					ship.setNameEn(tmp.getVesselNameEn());
					ship.setLength(Integer.parseInt(tmp.getVesselLength()));
					ship.setBreadth(Integer.parseInt(tmp.getVesselWidth()));
					ship.setMaxGross(Float.valueOf(tmp.getVesselGt()));
					ship.setNet(Float.valueOf(tmp.getVesselDwt()));
					ship.setDeptId(Long.parseLong(tmp.getOwnerId()));
					ship.setCreateTime(date);
					ship.setUpdateTime(date);
					this.shipService.updateById(ship);

					//2.更新主表数据
					BdmShip shipE = shipService.getById(ship.getId());
					BdmAbstractTarget abstractTargetE = new BdmAbstractTarget();
					BeanUtils.copyProperties(shipE, abstractTargetE);
					abstractTargetService.updateById(abstractTargetE);

					//3.更新缓存
					Map<String, String> mapE = new HashMap<>();
					String keyE = ship.getTargetType() + "-" + ship.getId();
					Map<String, Object> innerMapE = new HashMap<>();
					innerMapE.put("targetName", ship.getNumber());
					innerMapE.put("targetType", ship.getTargetType());
					innerMapE.put("targetCategory", ship.getCategory());
					innerMapE.put("deptId", ship.getDeptId());
					try {
						mapE.put(keyE, new ObjectMapper().writeValueAsString(innerMapE));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}
					redisTemplate.opsForHash().putAll(CommonConstant.BASEINFO_TARGET, mapE);

					//4.通知其他服务
					DeviceInfo deviceInfoE = new DeviceInfo();
					deviceInfoE.setOperation(Operation.UPDATE);
					deviceInfoE.setTargetId(ship.getId());
					deviceInfoE.setTargetName(ship.getNumber());
					deviceInfoE.setDeptId(ship.getDeptId());
					try {
						this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_ship", JSON.toJSONString(deviceInfoE));
					} catch (Exception e) {
						log.error("货船信息更新消息发送到kafka失败", e);
					}

					//5.更新相应终端
					if(shipInDB.getDeptId().equals(ship.getDeptId())){
						// 对于RnssDevice的更新
						rnssDeviceService.updateDept(ship.getId(),ship.getTargetType(),ship.getDeptId());

						// 对于BdmAbstractDevice的更新
						abstractDeviceService.updateDept(ship.getId(),ship.getTargetType(),ship.getDeptId());
					}

					break;
				case CommonConstant.OPER_TYPE_DELETE:
					if (temp == null) {
						throw new Exception("货船信息不存在，车厢编号：" + vesselNo);
					}
					//1.删除基础信息
					temp.setUpdateTime(date);
					temp.setDeleted(1);
					this.shipService.updateById(temp);

					//2.删除缓存
					String keyD1 = CommonConstant.SHIP_TARGET_TYPE + "-" + temp.getId();
					List<String> keys = new ArrayList<>();
					keys.add(keyD1);
					Map<Long, Object> mapD = new HashMap<>();
					// 获取 Redis 数据
					List<Object> values = redisTemplate.opsForHash().multiGet(CommonConstant.BASEINFO_TARGET, Collections.singleton(keys));
					// 处理获取到的值
					for (int i = 0; i < keys.size(); i++) {
						String keyD = keys.get(i);
						Object value = values.get(i);
						if (value != null) {
							String jsonValue = value.toString();
							BdmPrecisionAssembly precisionAssembly = JSONObject.parseObject(jsonValue, BdmPrecisionAssembly.class);

							int index = keyD.indexOf("-");
							if (index != -1) {
								String idValue = keyD.substring(index + 1);
								Long deptId = precisionAssembly.getDeptId();
								if (mapD.containsKey(deptId)) {
									String existingValues = (String) mapD.get(deptId);
									mapD.put(deptId, existingValues + "、" + idValue);
								} else {
									mapD.put(deptId, idValue);
								}
							}
						}
					}
					redisTemplate.delete(keys.stream()
						.map(keyD2 -> CommonConstant.BASEINFO_TARGET + ":" + keyD2)
						.collect(Collectors.toSet()));

					//3.通知其他服务
					DeviceInfo deviceInfoD = new DeviceInfo();
					deviceInfoD.setOperation(Operation.DELETE);
					//todo 此处有改动，注意
					Long lastId = temp.getId();
					deviceInfoD.setTargetId(lastId);
					deviceInfoD.setTargetIds(CollUtil.newHashSet(lastId));
					try {
						this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_ship", JSON.toJSONString(deviceInfoD));
					} catch (Exception e) {
						log.error("货船信息更新消息发送到kafka失败", e);
					}

					//4.更新关联信息
					Long[] idsArray = {temp.getId()};
					//更新bdm_abstract_target
					abstractTargetService.deleteByIds(idsArray);
					//更新bdm_abstract_device
					abstractDeviceService.deleteByTargetIds(idsArray, TargetTypeEnum.VEHICLE.getSymbol());
					break;

				default:
					throw new Exception("操作类型不正确。");
			}
		}
	}

	//RS0001 密钥查询
	private Map<String,String> keySearch(Message message) throws Exception{
		KeyRequest request = null;
		try{
			//request = (KeyRequest) messageUtils.decryptBodyBean(message, KeyRequest.class);
			request = JSON.parseObject(message.getBody().getEncryptedContent(), KeyRequest.class);
			if(request == null || message.getBody() == null){
				throw new Exception("报文内容为空");
			}
		}catch (Exception e){
			log.error("报文解析失败",e);
			throw new Exception("报文解析失败"+e.getMessage());
		}
		//1.校验参数
		String username = request.getUserName();
		if(StringUtils.isEmpty(username)){
			//如果用户名为空，则表示查询位置平台公钥
			SmInterfaceSystem sys = systemService.getById(CommonConstant.DEFAULT_SYSTEM_ID);
			Map<String,String> publicKeyMap = new HashMap<>();
			publicKeyMap.put("publicKey",sys.getPublicKey());
			return publicKeyMap;
		}
		//2.判断用户名与token中的用户名是否一致
		String usernameToken = AuthUtil.getUserAccount();
		if(!username.equals(usernameToken)){
			//如果参数中的用户名和token中的用户名不一致，则校验不通过
			throw new Exception("用户名与认证用户名不一致");
		}
		//3.查询密钥信息
		List<String> keys = systemService.findPublicKeyByAccount(username);
		if(keys == null || keys.size() < 1){
			log.info("未查询到{}的公钥信息", username);
			throw new Exception("该账户未设置公钥信息");
		}
		if(keys.size() > 1){
			log.info("账户[{}]密钥信息重复", username);
			throw new Exception("账户["+username+"]密钥信息重复");
		}
		Map<String,String> publicKeyMap = new HashMap<>();
		publicKeyMap.put("userName", username);
		publicKeyMap.put("publicKey",keys.get(0));
		return publicKeyMap;
	}

	//RS0002 密钥上传
	//密钥上传不应用加解密
	private void keyUpload(Message message) throws Exception{
		KeyUpload request = null;
		try{
			request = JSON.parseObject(message.getBody().getEncryptedContent(), KeyUpload.class);
			if(request == null){
				throw new Exception("报文内容为空");
			}
		}catch (Exception e){
			log.error("报文解析失败",e);
			throw new Exception("报文解析失败"+e.getMessage());
		}
		//1.校验参数
		String username = request.getUserName();
		if(StringUtils.isEmpty(username)){
			throw new Exception("用户名不能为空");
		}

		//判断用户是否存在
		//根据用户名查询用户数量
		List<InPosUser> userList =  userService.list(Wrappers.lambdaQuery(InPosUser.class)
			.eq(InPosUser::getIsDel, 0).eq(InPosUser::getAccount, username));
		if(userList == null || userList.size() == 0){
			throw new Exception("用户["+username+"]不存在");
		}
		if(userList.size() > 1){
			throw new Exception("用户["+username+"]重复");
		}

		//查询用户与token中的用户是否相同
		String usernameInToken = AuthUtil.getUserAccount();
		if(usernameInToken == null){
			throw new Exception("未查询到认证信息");
		}

		if(!username.equals(usernameInToken)){
			throw new Exception("用户信息与认证不匹配");
		}

		//查看用户是否是指定业务系统的用户
		if(userList.get(0).getSystemId() != Long.parseLong(message.getHeader().getSystemId())){
			throw new Exception("非指定系统用户，不能操作密钥");
		}




		//2.根据操作类型执行操作
		String operType = request.getOperateState();
		if(StringUtils.isEmpty(operType)){
			throw new Exception("未指定操作类型");
		}

		SmInterfaceSystem systemO = systemService.getById(userList.get(0).getSystemId());


		if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
			//新增
			//判断公钥是否为空
			if(StringUtils.isEmpty(request.getPublicKey())){
				throw new Exception("新增操作，公钥不能为空");
			}
			//判断公钥是否存在
			//List<String> publicKeys = systemService.findPublicKeyByAccount(username);
			if(!StringUtils.isEmpty((systemO.getPublicKey()==null?"":systemO.getPublicKey()).trim())){
				throw new Exception("平台密钥信息已存在");
			}
			//保存用户公钥信息
			InPosUser user = userList.get(0);
			String publicKey = request.getPublicKey();
			SmInterfaceSystem system = new SmInterfaceSystem();
			system.setId(user.getSystemId());
			system.setPublicKey(publicKey);
			systemService.updateById(system);

		}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
			//修改
			//判断公钥是否为空
			if(StringUtils.isEmpty(request.getPublicKey())){
				throw new Exception("修改操作，公钥不能为空");
			}
			//判断原公钥是否相同
			InPosUser user = userList.get(0);
			long systemId = user.getSystemId();
			SmInterfaceSystem system = systemService.getById(systemId);
			String publicKeyInDB = system.getPublicKey();
			if(!StringUtils.isEmpty(publicKeyInDB) && !publicKeyInDB.equals(request.getOldPublicKey())){
				//如果原公钥与数据库中的不一致，则不允许修改
				throw new Exception("原公钥与数据库中不一致，不允许修改");
			}
			//保存用户公钥信息
			system.setPublicKey(request.getPublicKey());
			systemService.updateById(system);
		}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
			//删除
			InPosUser user = userList.get(0);
			long systemId = user.getSystemId();
			SmInterfaceSystem system = systemService.getById(systemId);
			//保存用户公钥信息
			system.setPublicKey(null);
			systemService.updateById(system);
		}
		//todo 需要增加密钥变更日志

		//删除公钥缓存
		stringRedisTemplate.delete(CommonConstant.CACHE_KEY_PUBLIC + message.getHeader().getSystemId());

	}

	private long getTargetIdByTypeAndCode (String targetType, String targetCode) {
		int targetCate = Integer.parseInt(targetType.substring(0, 1));
		switch (targetCate) {
			case CommonConstant.TARGET_TYPE_VEHICLE:
				BdmVehicle vehicle = this.vehicleNewService.getOne(
					Wrappers.lambdaQuery(BdmVehicle.class).eq(BdmVehicle::getNumber, targetCode)
				);

				return (vehicle == null) ? 0 : vehicle.getId();
			case CommonConstant.TARGET_TYPE_WORKER:
				BdmWorker worker = this.workerService.getOne(
					Wrappers.lambdaQuery(BdmWorker.class).eq(BdmWorker::getWkno, targetCode)
				);

				return (worker == null) ? 0 : worker.getId();
			case CommonConstant.TARGET_TYPE_FACILITY:
				BdmFacility facility = this.facilityService.getOne(
					Wrappers.lambdaQuery(BdmFacility.class).eq(BdmFacility::getCode, targetCode)
				);

				return (facility == null) ? 0 : facility.getId();
			case CommonConstant.TARGET_TYPE_CONTAINER:
				BdmContainer container = this.containerService.getOne(
					Wrappers.lambdaQuery(BdmContainer.class).eq(BdmContainer::getNumber, targetCode)
				);

				return (container == null) ? 0 : container.getId();
			case CommonConstant.TARGET_TYPE_TEMP:
				BdmTemporary temporary = this.temporaryService.getOne(
					Wrappers.lambdaQuery(BdmTemporary.class).eq(BdmTemporary::getWkno, targetCode)
				);

				return (temporary == null) ? 0 : temporary.getId();
			case CommonConstant.TARGET_TYPE_VISITOR:
				BdmVisitor visitor = this.visitorService.getOne(
					Wrappers.lambdaQuery(BdmVisitor.class).eq(BdmVisitor::getIdNumber, targetCode)
				);

				return (visitor == null) ? 0 : visitor.getId();
			default:
				return -1;
		}
	}

	private long getDeviceIdByTypeAndSeq (String deviceType, String deviceSeq) {
		int deviceCate = Integer.parseInt(deviceType.substring(0, 1));
		switch (deviceCate + "") {
			case CommonConstant.DEVICE_TYPE_RNSS:
				BdmRnssDevice rnssDevice = this.rnssDeviceService.getOne(
					Wrappers.lambdaQuery(BdmRnssDevice.class).eq(BdmRnssDevice::getUniqueId, deviceSeq)
				);

				return (rnssDevice == null) ? 0 : rnssDevice.getId();
			case CommonConstant.DEVICE_TYPE_WEARABLE:
				BdmWearableDevice wearableDevice = this.wearableDeviceService.getOne(
					Wrappers.lambdaQuery(BdmWearableDevice.class).eq(BdmWearableDevice::getUniqueId, deviceSeq)
				);

				return (wearableDevice == null) ? 0 : wearableDevice.getId();
			case CommonConstant.DEVICE_TYPE_RDSS:
				BdmRdssDevice rdssDevice = this.rdssDeviceService.getOne(
					Wrappers.lambdaQuery(BdmRdssDevice.class).eq(BdmRdssDevice::getUniqueId, deviceSeq)
				);

				return (rdssDevice == null) ? 0 : rdssDevice.getId();
			case CommonConstant.DEVICE_TYPE_MONIT:
				BdmMonitDevice monitDevice = this.monitDeviceService.getOne(
					Wrappers.lambdaQuery(BdmMonitDevice.class).eq(BdmMonitDevice::getUniqueId, deviceSeq)
				);

				return (monitDevice == null) ? 0 : monitDevice.getId();
			case CommonConstant.DEVICE_TYPE_PNT:
				BdmPntDevice pntDevice = this.pntDeviceService.getOne(
					Wrappers.lambdaQuery(BdmPntDevice.class).eq(BdmPntDevice::getUniqueId, deviceSeq)
				);

				return (pntDevice == null) ? 0 : pntDevice.getId();
			default:
				return -1;
		}
	}


	/**
	 * 查询终端基本信息
	 * @param uniqueId
	 * @param deviceType
	 * @return
	 */
	private BaseDeviceVO getDevice(String uniqueId, Integer deviceType){
		BaseDeviceVO vo = null;
		switch (deviceType+""){
			case CommonConstant.DEVICE_TYPE_RNSS: //北斗定位终端
				BdmRnssDevice rnss = rnssDeviceService.getOne(Wrappers.lambdaQuery(BdmRnssDevice.class)
					.eq(BdmRnssDevice::getUniqueId, uniqueId)
					.eq(BdmRnssDevice::getDeleted, 0));
				vo = new BaseDeviceVO();
				vo.setDeviceId(rnss.getId());
				vo.setTargetId(rnss.getTargetId());
				vo.setDeviceType(rnss.getDeviceType());
				vo.setDeviceNum(rnss.getDeviceNum());
				vo.setTargetType(rnss.getTargetType());
				vo.setUniqueId(uniqueId);
				vo.setTargetName(rnss.getTargetName());
				break;
			case CommonConstant.DEVICE_TYPE_WEARABLE: //北斗穿戴式终端
				BdmWearableDevice wear = wearableDeviceService.getOne(Wrappers.lambdaQuery(BdmWearableDevice.class)
					.eq(BdmWearableDevice::getUniqueId, uniqueId)
					.eq(BdmWearableDevice::getDeleted, 0));
				vo = new BaseDeviceVO();
				vo.setDeviceId(wear.getId());
				vo.setDeviceType(wear.getDeviceType());
				vo.setDeviceNum(wear.getDeviceNum());
				vo.setTargetType(CommonConstant.TARGET_TYPE_WORKER);
				vo.setUniqueId(uniqueId);
				vo.setTargetName(null);
				break;
			case CommonConstant.DEVICE_TYPE_RDSS: //北斗短报文终端
				BdmRdssDevice rdss = rdssDeviceService.getOne(Wrappers.lambdaQuery(BdmRdssDevice.class)
					.eq(BdmRdssDevice::getUniqueId, uniqueId)
					.eq(BdmRdssDevice::getDeleted, 0));
				vo = new BaseDeviceVO();
				vo.setDeviceId(rdss.getId());
				vo.setTargetId(rdss.getTargetId());
				vo.setDeviceType(rdss.getDeviceType());
				vo.setDeviceNum(rdss.getDeviceNum());
				vo.setTargetType(rdss.getTargetType());
				vo.setUniqueId(uniqueId);
				vo.setTargetName(rdss.getTargetName());
				break;
			case CommonConstant.DEVICE_TYPE_MONIT: //北斗监控终端
				BdmMonitDevice monit = monitDeviceService.getOne(Wrappers.lambdaQuery(BdmMonitDevice.class)
					.eq(BdmMonitDevice::getUniqueId, uniqueId)
					.eq(BdmMonitDevice::getDeleted, 0));
				vo = new BaseDeviceVO();
				vo.setDeviceId(monit.getId());
				vo.setTargetId(monit.getTargetId());
				vo.setDeviceType(monit.getDeviceType());
				vo.setDeviceNum(monit.getDeviceNum());
				vo.setTargetType(monit.getTargetType());
				vo.setUniqueId(uniqueId);
				vo.setTargetName(null);
				break;
			case CommonConstant.DEVICE_TYPE_PNT: //北斗授时终端
				BdmPntDevice pnt = pntDeviceService.getOne(Wrappers.lambdaQuery(BdmPntDevice.class)
					.eq(BdmPntDevice::getUniqueId, uniqueId)
					.eq(BdmPntDevice::getDeleted, 0));
				vo = new BaseDeviceVO();
				vo.setDeviceId(pnt.getId());
				vo.setTargetId(pnt.getTargetId());
				vo.setDeviceType(pnt.getDeviceType());
				vo.setDeviceNum(pnt.getDeviceNum());
				vo.setTargetType(pnt.getTargetType());
				vo.setUniqueId(uniqueId);
				vo.setTargetName(null);
				break;
		}
		return vo;
	}
}
