package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.BdmUsualLog;
import com.xh.vdm.interManager.mapper.BdmUsualLogMapper;
import com.xh.vdm.interManager.service.BdmUsualLogService;
import org.springframework.stereotype.Service;

/**
 * (BdmUsualLog)表服务实现类
 */
@Service
public class BdmUsualLogServiceImpl extends ServiceImpl<BdmUsualLogMapper, BdmUsualLog> implements BdmUsualLogService {


}
