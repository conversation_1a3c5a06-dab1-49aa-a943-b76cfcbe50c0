package com.xh.vdm.interManager.feign;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.interManager.dto.message.*;
import com.xh.vdm.interManager.util.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.RedisConstant;
import org.springblade.common.utils.DateUtil;
import com.xh.vdm.biapi.entity.BdmFacility;
import com.xh.vdm.biapi.entity.BdmPrecisionAssembly;
import com.xh.vdm.biapi.entity.BdmShip;
import com.xh.vdm.biapi.entity.BdmWorker;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 数据信息同步接口
 */
@Slf4j
@RestController
public class MessageClient implements IMessageClient{

	public static final ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMddHHmmss"));

	@Resource
	private KafkaTemplate kafkaTemplate;

	@Resource
	private RedisTemplate redisTemplate;

	@PostMapping(VEHICLE_URL)
	@Override
	public R<String> vehicle(@Param("operType") String operType, @RequestBody BdmVehicle vehicle) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_UPDATE) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(vehicle == null){
			return R.fail("数据不能为空");
		}
		try{
			//2.构建协议报文对象
			Vehicle veh = new Vehicle();
			veh.setOperateTime(sdfHolder.get().format(new Date()));
			veh.setVehicleNum(vehicle.getNumber());
			veh.setOwnerId(vehicle.getDeptId()+"");
			Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, vehicle.getDeptId());
			String ownerName = "";
			if(owner != null){
				ownerName = owner.toString();
			}
			veh.setOwnerName(ownerName);
			veh.setVehicleType(vehicle.getCategory()+"");
			//2.根据不同操作类型执行操作
			if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
				//如果是新增
				veh.setOperateState(CommonConstant.OPER_TYPE_ADD);
			}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
				//如果是修改
				veh.setOperateState(CommonConstant.OPER_TYPE_UPDATE);
			}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
				//如果是删除
				veh.setOperateState(CommonConstant.OPER_TYPE_DELETE);
			}
			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_VEHICLE);
			container.setMessage(veh);
			container.setBusiObject(vehicle);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}


	@PostMapping(VEHICLE_BATCH_URL)
	@Override
	public R<String> vehicleBatch(@Param("operType") String operType, @RequestBody List<BdmVehicle> vehicles) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		//批量接口不支持修改
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(vehicles == null || vehicles.size() < 0){
			return R.fail("数据不能为空");
		}
		long deptId = vehicles.get(0).getDeptId();
		Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
		String ownerName = "";
		if(owner != null){
			ownerName = owner.toString();
		}
		try{
			List<Vehicle> list = new ArrayList<>();
			for(BdmVehicle vehicle : vehicles){
				//2.构建协议报文对象
				Vehicle veh = new Vehicle();
				veh.setOperateTime(sdfHolder.get().format(new Date()));
				veh.setVehicleNum(vehicle.getNumber());
				veh.setOwnerId(deptId+"");
				veh.setOwnerName(ownerName);
				veh.setVehicleType(vehicle.getCategory()+"");
				//2.根据不同操作类型执行操作
				if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
					//如果是新增
					veh.setOperateState(CommonConstant.OPER_TYPE_ADD);
				}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
					//如果是修改
					veh.setOperateState(CommonConstant.OPER_TYPE_UPDATE);
				}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
					//如果是删除
					veh.setOperateState(CommonConstant.OPER_TYPE_DELETE);
				}
				list.add(veh);
			}
			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_VEHICLE);
			container.setMessage(list);
			container.setBusiObject(vehicles);
			//添加批量标识
			container.setIsBatch(true);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}

	@PostMapping(STAFF_URL)
	@Override
	public R<String> staff(String operType, BdmWorker worker) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_UPDATE) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(worker == null){
			return R.fail("数据不能为空");
		}
		try{
			//2.构建协议报文对象
			Staff staff = new Staff();
			staff.setOperateTime(sdfHolder.get().format(new Date()));
			staff.setStaffCode(worker.getWkno());
			long deptId = worker.getDeptId();
			Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
			String ownerName = "";
			if(owner != null){
				ownerName = owner.toString();
			}
			staff.setOwnerId(deptId + "");
			staff.setOwnerName(ownerName);
			staff.setIndustry(worker.getIndustry());
			staff.setPost(worker.getPost());
			staff.setStaffName(worker.getName());
			staff.setPhone(worker.getPhone());
			//2.根据不同操作类型执行操作
			if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
				//如果是新增
				staff.setOperateState(CommonConstant.OPER_TYPE_ADD);
			}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
				//如果是修改
				staff.setOperateState(CommonConstant.OPER_TYPE_UPDATE);
			}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
				//如果是删除
				staff.setOperateState(CommonConstant.OPER_TYPE_DELETE);
			}
			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_STAFF);
			container.setMessage(staff);
			container.setBusiObject(worker);
			container.setIsBatch(false);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}


	@PostMapping(STAFF_BATCH_URL)
	@Override
	public R<String> staffBatch(String operType, List<BdmWorker> workers) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		//批量操作不支持修改
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(workers == null || workers.size() < 1){
			return R.fail("数据不能为空");
		}
		long deptId = workers.get(0).getDeptId();
		Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
		String ownerName = "";
		if(owner != null){
			ownerName = owner.toString();
		}
		try{
			List<Staff> list = new ArrayList<>();
			for(BdmWorker worker : workers){
				//2.构建协议报文对象
				Staff staff = new Staff();
				staff.setOperateTime(sdfHolder.get().format(new Date()));
				staff.setStaffCode(worker.getWkno());
				staff.setOwnerId(deptId + "");
				staff.setOwnerName(ownerName);
				staff.setIndustry(worker.getIndustry());
				staff.setPost(worker.getPost());
				staff.setStaffName(worker.getName());
				staff.setPhone(worker.getPhone());
				//2.根据不同操作类型执行操作
				if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
					//如果是新增
					staff.setOperateState(CommonConstant.OPER_TYPE_ADD);
				}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
					//如果是修改
					staff.setOperateState(CommonConstant.OPER_TYPE_UPDATE);
				}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
					//如果是删除
					staff.setOperateState(CommonConstant.OPER_TYPE_DELETE);
				}
				list.add(staff);
			}

			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_STAFF);
			//设置批量标记
			container.setIsBatch(true);
			container.setMessage(list);
			container.setBusiObject(workers);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}

	@PostMapping(FACILITY_URL)
	@Override
	public R<String> facility(String operType, BdmFacility facility) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_UPDATE) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(facility == null){
			return R.fail("数据不能为空");
		}
		try{
			//2.构建协议报文对象
			Facility f = new Facility();
			f.setOperateTime(sdfHolder.get().format(new Date()));
			f.setFacilityCode(facility.getCode());
			long deptId = facility.getDeptId();
			Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
			String ownerName = "";
			if(owner != null){
				ownerName = owner.toString();
			}
			f.setOwnerId(deptId + "");
			f.setOwnerName(ownerName);
			f.setFacilityType(facility.getCategory());
			f.setAddress(facility.getAddress());
			f.setFacilityName(facility.getName());
			String geometryStr = facility.getGeometry();
			String[] array = geometryStr.split(" ");
			List<Facility.Geometry> geometry = new ArrayList<>();
			for(int i = 0; i < array.length; i++){
				if(i % 2 == 0){
					Facility.Geometry geo = new Facility.Geometry();
					geo.setLongitude(Double.parseDouble(array[i]));
					geo.setLatitude(Double.parseDouble(array[i + 1]));
					geometry.add(geo);
				}
			}
			f.setGeometry(geometry);
			//2.根据不同操作类型执行操作
			if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
				//如果是新增
				f.setOperateState(CommonConstant.OPER_TYPE_ADD);
			}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
				//如果是修改
				f.setOperateState(CommonConstant.OPER_TYPE_UPDATE);
			}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
				//如果是删除
				f.setOperateState(CommonConstant.OPER_TYPE_DELETE);
			}
			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_FACILITY);
			container.setIsBatch(false);
			container.setMessage(f);
			container.setBusiObject(facility);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}

	@PostMapping(FACILITY_BATCH_URL)
	@Override
	public R<String> facilityBatch(String operType, List<BdmFacility> facilities) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		//批量操作不支持更新
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(facilities == null || facilities.size() < 1){
			return R.fail("数据不能为空");
		}
		try{
			List<Facility> list = new ArrayList<>();
			long deptId = facilities.get(0).getDeptId();
			Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
			String ownerName = "";
			if(owner != null){
				ownerName = owner.toString();
			}
			for(BdmFacility facility : facilities){
				//2.构建协议报文对象
				Facility f = new Facility();
				f.setOperateTime(sdfHolder.get().format(new Date()));
				f.setFacilityCode(facility.getCode());
				f.setFacilityType(facility.getCategory());
				f.setOwnerId(deptId + "");
				f.setOwnerName(ownerName);
				f.setAddress(facility.getAddress());
				f.setFacilityName(facility.getName());
				String geometryStr = facility.getGeometry();
				String[] array = geometryStr.split(" ");
				List<Facility.Geometry> geometry = new ArrayList<>();
				for(int i = 0; i < array.length; i++){
					if(i % 2 == 0){
						Facility.Geometry geo = new Facility.Geometry();
						geo.setLongitude(Double.parseDouble(array[i]));
						geo.setLatitude(Double.parseDouble(array[i + 1]));
						geometry.add(geo);
					}
				}
				f.setGeometry(geometry);
				//2.根据不同操作类型执行操作
				if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
					//如果是新增
					f.setOperateState(CommonConstant.OPER_TYPE_ADD);
				}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
					//如果是修改
					f.setOperateState(CommonConstant.OPER_TYPE_UPDATE);
				}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
					//如果是删除
					f.setOperateState(CommonConstant.OPER_TYPE_DELETE);
				}
				list.add(f);
			}
			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_FACILITY);
			container.setIsBatch(true);
			container.setMessage(list);
			container.setBusiObject(facilities);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}

	@PostMapping(TERMINAL_URL)
	@Override
	public R<String> terminal(String operType, Integer deviceType, Object terminal) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_UPDATE) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(terminal == null){
			return R.fail("数据不能为空");
		}
		try{
			TerminalInfo t = new TerminalInfo();
			//2.构建协议报文对象
			if(deviceType == Integer.parseInt(CommonConstant.DEVICE_TYPE_RNSS)){
				//北斗定位终端
				BdmRnssDevice rnss = JSON.parseObject(JSON.toJSONString(terminal), BdmRnssDevice.class);
				t.setDeviceID(rnss.getUniqueId());
				t.setImei(rnss.getImei());
				t.setDomain(rnss.getDomain());
				t.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RNSS));
				long deptId = rnss.getDeptId();
				Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
				String ownerName = "";
				if(owner != null){
					ownerName = owner.toString();
				}
				t.setOwnerId(deptId + "");
				t.setOwnerName(ownerName);
				t.setDeviceModel(rnss.getModel());
				t.setDeviceCategory(rnss.getCategory());
				t.setLocateMode(rnss.getGnssMode());
				t.setScenario(rnss.getScenario());
				t.setTagcode(rnss.getDeviceNum());
				t.setVendorId(rnss.getVendor());
				t.setOperateState(operType);
				t.setOperateTime(sdfHolder.get().format(new Date()));
			}else if(deviceType == Integer.parseInt(CommonConstant.DEVICE_TYPE_WEARABLE)){
				//穿戴式终端
				BdmWearableDevice wear = JSON.parseObject(JSON.toJSONString(terminal), BdmWearableDevice.class);
				t.setDeviceID(wear.getUniqueId());
				t.setImei(wear.getImei());
				t.setDomain(wear.getDomain());
				t.setDeviceType(Integer.parseInt((CommonConstant.DEVICE_TYPE_WEARABLE)));
				long deptId = wear.getDeptId();
				Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
				String ownerName = "";
				if(owner != null){
					ownerName = owner.toString();
				}
				t.setOwnerId(deptId + "");
				t.setOwnerName(ownerName);
				t.setDeviceModel(wear.getModel());
				t.setDeviceCategory(wear.getCategory());
				t.setLocateMode(wear.getGnssMode());
				t.setScenario(wear.getScenario());
				t.setTagcode(wear.getDeviceNum());
				t.setVendorId(wear.getVendor());
				t.setOperateState(operType);
				t.setOperateTime(sdfHolder.get().format(new Date()));
			}else if(deviceType == Integer.parseInt(CommonConstant.DEVICE_TYPE_RDSS)){
				//北斗短报文终端
				BdmRdssDevice rdss = JSON.parseObject(JSON.toJSONString(terminal), BdmRdssDevice.class);
				t.setDeviceID(rdss.getUniqueId());
				t.setImei(rdss.getImei());
				t.setDomain(rdss.getDomain());
				t.setDeviceType(Integer.parseInt((CommonConstant.DEVICE_TYPE_RDSS)));
				long deptId = rdss.getDeptId();
				Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
				String ownerName = "";
				if(owner != null){
					ownerName = owner.toString();
				}
				t.setOwnerId(deptId + "");
				t.setOwnerName(ownerName);
				t.setDeviceModel(rdss.getModel());
				t.setDeviceCategory(rdss.getCategory());
				t.setLocateMode(rdss.getGnssMode());
				t.setScenario(rdss.getScenario());
				t.setTagcode(rdss.getDeviceNum());
				t.setVendorId(rdss.getVendor());
				t.setOperateState(operType);
				t.setOperateTime(sdfHolder.get().format(new Date()));
			}else if(deviceType == Integer.parseInt(CommonConstant.DEVICE_TYPE_MONIT)){
				//北斗监测终端
				BdmMonitDevice monit = JSON.parseObject(JSON.toJSONString(terminal), BdmMonitDevice.class);
				t.setDeviceID(monit.getUniqueId());
				t.setImei(monit.getImei());
				t.setDomain(monit.getDomain());
				t.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RDSS));
				long deptId = monit.getDeptId();
				Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
				String ownerName = "";
				if(owner != null){
					ownerName = owner.toString();
				}
				t.setOwnerId(deptId + "");
				t.setOwnerName(ownerName);
				t.setDeviceModel(monit.getModel());
				t.setDeviceCategory(monit.getCategory());
				t.setLocateMode(monit.getGnssMode());
				t.setScenario(monit.getScenario());
				t.setTagcode(monit.getDeviceNum());
				t.setVendorId(monit.getVendor());
				t.setOperateState(operType);
				t.setOperateTime(sdfHolder.get().format(new Date()));
			}else if(deviceType == Integer.parseInt(CommonConstant.DEVICE_TYPE_PNT)){
				//北斗授时终端
				BdmPntDevice pnt = JSON.parseObject(JSON.toJSONString(terminal), BdmPntDevice.class);
				t.setDeviceID(pnt.getUniqueId());
				t.setImei(pnt.getImei());
				t.setDomain(pnt.getDomain());
				t.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_PNT));
				long deptId = pnt.getDeptId();
				Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
				String ownerName = "";
				if(owner != null){
					ownerName = owner.toString();
				}
				t.setOwnerId(deptId + "");
				t.setOwnerName(ownerName);
				t.setDeviceModel(pnt.getModel());
				t.setDeviceCategory(pnt.getCategory());
				t.setLocateMode(pnt.getGnssMode());
				t.setScenario(pnt.getScenario());
				t.setTagcode(pnt.getDeviceNum());
				t.setVendorId(pnt.getVendor());
				t.setOperateState(operType);
				t.setOperateTime(sdfHolder.get().format(new Date()));
			}
			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_VEHICLE);
			container.setIsBatch(false);
			container.setBusiObject(terminal);
			container.setMessage(t);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}



	@PostMapping(TERMINAL_BATCH_URL)
	@Override
	public R<String> terminalBatch(String operType, Integer deviceType, List<Object> terminals) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		//批量操作不支持修改
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(terminals == null || terminals.size() < 1){
			return R.fail("数据不能为空");
		}
		try{
			List<TerminalInfo> list = new ArrayList<>();
			for(Object terminal : terminals){
				TerminalInfo t = new TerminalInfo();
				//2.构建协议报文对象
				if(deviceType == Integer.parseInt(CommonConstant.DEVICE_TYPE_RNSS)){
					//北斗定位终端
					BdmRnssDevice rnss = JSON.parseObject(JSON.toJSONString(terminal), BdmRnssDevice.class);
					t.setDeviceID(rnss.getUniqueId());
					t.setImei(rnss.getImei());
					t.setDomain(rnss.getDomain());
					t.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RNSS));
					long deptId = rnss.getDeptId();
					Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
					String ownerName = "";
					if(owner != null){
						ownerName = owner.toString();
					}
					t.setOwnerId(deptId + "");
					t.setOwnerName(ownerName);
					t.setDeviceModel(rnss.getModel());
					t.setDeviceCategory(rnss.getCategory());
					t.setLocateMode(rnss.getGnssMode());
					t.setScenario(rnss.getScenario());
					t.setTagcode(rnss.getDeviceNum());
					t.setVendorId(rnss.getVendor());
					t.setOperateState(operType);
					t.setOperateTime(sdfHolder.get().format(new Date()));
				}else if(deviceType == Integer.parseInt(CommonConstant.DEVICE_TYPE_WEARABLE)){
					//穿戴式终端
					BdmWearableDevice wear = JSON.parseObject(JSON.toJSONString(terminal), BdmWearableDevice.class);
					t.setDeviceID(wear.getUniqueId());
					t.setImei(wear.getImei());
					t.setDomain(wear.getDomain());
					t.setDeviceType(Integer.parseInt((CommonConstant.DEVICE_TYPE_WEARABLE)));
					long deptId = wear.getDeptId();
					Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
					String ownerName = "";
					if(owner != null){
						ownerName = owner.toString();
					}
					t.setOwnerId(deptId + "");
					t.setOwnerName(ownerName);
					t.setDeviceModel(wear.getModel());
					t.setDeviceCategory(wear.getCategory());
					t.setLocateMode(wear.getGnssMode());
					t.setScenario(wear.getScenario());
					t.setTagcode(wear.getDeviceNum());
					t.setVendorId(wear.getVendor());
					t.setOperateState(operType);
					t.setOperateTime(sdfHolder.get().format(new Date()));
				}else if(deviceType == Integer.parseInt(CommonConstant.DEVICE_TYPE_RDSS)){
					//北斗短报文终端
					BdmRdssDevice rdss = JSON.parseObject(JSON.toJSONString(terminal), BdmRdssDevice.class);
					t.setDeviceID(rdss.getUniqueId());
					t.setImei(rdss.getImei());
					t.setDomain(rdss.getDomain());
					t.setDeviceType(Integer.parseInt((CommonConstant.DEVICE_TYPE_RDSS)));
					long deptId = rdss.getDeptId();
					Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
					String ownerName = "";
					if(owner != null){
						ownerName = owner.toString();
					}
					t.setOwnerId(deptId + "");
					t.setOwnerName(ownerName);
					t.setDeviceModel(rdss.getModel());
					t.setDeviceCategory(rdss.getCategory());
					t.setLocateMode(rdss.getGnssMode());
					t.setScenario(rdss.getScenario());
					t.setTagcode(rdss.getDeviceNum());
					t.setVendorId(rdss.getVendor());
					t.setOperateState(operType);
					t.setOperateTime(sdfHolder.get().format(new Date()));
				}else if(deviceType == Integer.parseInt(CommonConstant.DEVICE_TYPE_MONIT)){
					//北斗监测终端
					BdmMonitDevice monit = JSON.parseObject(JSON.toJSONString(terminal), BdmMonitDevice.class);
					t.setDeviceID(monit.getUniqueId());
					t.setImei(monit.getImei());
					t.setDomain(monit.getDomain());
					t.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_RDSS));
					long deptId = monit.getDeptId();
					Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
					String ownerName = "";
					if(owner != null){
						ownerName = owner.toString();
					}
					t.setOwnerId(deptId + "");
					t.setOwnerName(ownerName);
					t.setDeviceModel(monit.getModel());
					t.setDeviceCategory(monit.getCategory());
					t.setLocateMode(monit.getGnssMode());
					t.setScenario(monit.getScenario());
					t.setTagcode(monit.getDeviceNum());
					t.setVendorId(monit.getVendor());
					t.setOperateState(operType);
					t.setOperateTime(sdfHolder.get().format(new Date()));
				}else if(deviceType == Integer.parseInt(CommonConstant.DEVICE_TYPE_PNT)){
					//北斗授时终端
					BdmPntDevice pnt = JSON.parseObject(JSON.toJSONString(terminal), BdmPntDevice.class);
					t.setDeviceID(pnt.getUniqueId());
					t.setImei(pnt.getImei());
					t.setDomain(pnt.getDomain());
					t.setDeviceType(Integer.parseInt(CommonConstant.DEVICE_TYPE_PNT));
					long deptId = pnt.getDeptId();
					Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
					String ownerName = "";
					if(owner != null){
						ownerName = owner.toString();
					}
					t.setOwnerId(deptId + "");
					t.setOwnerName(ownerName);
					t.setDeviceModel(pnt.getModel());
					t.setDeviceCategory(pnt.getCategory());
					t.setLocateMode(pnt.getGnssMode());
					t.setScenario(pnt.getScenario());
					t.setTagcode(pnt.getDeviceNum());
					t.setVendorId(pnt.getVendor());
					t.setOperateState(operType);
					t.setOperateTime(sdfHolder.get().format(new Date()));
				}
				list.add(t);
			}

			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_TERMINAL);
			container.setIsBatch(true);
			//此处比较特殊，要求只传一个对象
			container.setBusiObject(terminals.get(0));
			container.setMessage(list);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}




	/**
	 * 精密装备
	 * @param operType
	 * @param precision
	 * @return
	 */
	@PostMapping(PRECISION_URL)
	@Override
	public R<String> precision(@Param("operType") String operType, @RequestBody BdmPrecisionAssembly precision) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_UPDATE) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(precision == null){
			return R.fail("数据不能为空");
		}
		try{
			//2.构建协议报文对象
			PrecisionAssembly pre = new PrecisionAssembly();
			pre.setOperateTime(sdfHolder.get().format(new Date()));
			pre.setDeviceId(precision.getNumber());
			pre.setDeviceName(precision.getName());
			pre.setModel(precision.getModel());
			pre.setManufacturer(precision.getManufacturer());
			long deptId = precision.getDeptId();
			Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
			String ownerName = "";
			if(owner != null){
				ownerName = owner.toString();
			}
			pre.setOwnerId(deptId + "");
			pre.setOwnerName(ownerName);

			//2.根据不同操作类型执行操作
			if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
				//如果是新增
				pre.setOperateState(CommonConstant.OPER_TYPE_ADD);
			}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
				//如果是修改
				pre.setOperateState(CommonConstant.OPER_TYPE_UPDATE);
			}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
				//如果是删除
				pre.setOperateState(CommonConstant.OPER_TYPE_DELETE);
			}
			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_PRECISION_ASSEMBLY);
			container.setMessage(pre);
			container.setBusiObject(precision);
			container.setIsBatch(false);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}


	/**
	 * 精密装备（批量）
	 * @param operType
	 * @param precisions
	 * @return
	 */
	@PostMapping(PRECISION_BATCH_URL)
	@Override
	public R<String> precisionBatch(@Param("operType") String operType, @RequestBody List<BdmPrecisionAssembly> precisions) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		//批量操作不支持修改
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(precisions == null || precisions.size() < 1){
			return R.fail("数据不能为空");
		}
		try{
			List<PrecisionAssembly> list = new ArrayList<>();
			long deptId = precisions.get(0).getDeptId();
			Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
			String ownerName = "";
			if(owner != null){
				ownerName = owner.toString();
			}
			for(BdmPrecisionAssembly precision : precisions){
				//2.构建协议报文对象
				PrecisionAssembly pre = new PrecisionAssembly();
				pre.setOperateTime(sdfHolder.get().format(new Date()));
				pre.setDeviceId(precision.getNumber());
				pre.setDeviceName(precision.getName());
				pre.setModel(precision.getModel());
				pre.setManufacturer(precision.getManufacturer());
				pre.setOwnerId(deptId + "");
				pre.setOwnerName(ownerName);

				//2.根据不同操作类型执行操作
				if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
					//如果是新增
					pre.setOperateState(CommonConstant.OPER_TYPE_ADD);
				}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
					//如果是修改
					pre.setOperateState(CommonConstant.OPER_TYPE_UPDATE);
				}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
					//如果是删除
					pre.setOperateState(CommonConstant.OPER_TYPE_DELETE);
				}
				list.add(pre);
			}
			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_PRECISION_ASSEMBLY);
			container.setMessage(list);
			container.setBusiObject(precisions);
			container.setIsBatch(true);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}

	/**
	 * 船舶
	 * @param operType
	 * @param ship
	 * @return
	 */
	@PostMapping(SHIP_URL)
	@Override
	public R<String> ship(@Param("operType") String operType, @RequestBody BdmShip ship) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_UPDATE) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(ship == null){
			return R.fail("数据不能为空");
		}
		try{
			//2.构建协议报文对象
			Ship sh = new Ship();
			sh.setOperateTime(sdfHolder.get().format(new Date()));
			sh.setVesselNo(ship.getNumber());
			sh.setVesselNameCn(ship.getName());
			sh.setVesselNameEn(ship.getNameEn());
			sh.setVesselMmsi(ship.getMmsi());
			sh.setVesselImo(ship.getImoNumber());
			sh.setVesselCallSign(ship.getCallSign());
			sh.setVesselConstructionTime(DateUtil.sdfHolderNoLine.get().format(ship.getConstructionTime()));
			sh.setVesselType(ship.getCategory()+"");
			sh.setVesselLength(ship.getLength()+"");
			sh.setVesselWidth(ship.getBreadth()+"");
			sh.setVesselGt(String.valueOf(ship.getMaxGross()));
			sh.setVesselDwt(String.valueOf(ship.getNet()));
			sh.setVesselNt(String.valueOf(ship.getNet()));
			long deptId = ship.getDeptId();
			Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
			String ownerName = "";
			if(owner != null){
				ownerName = owner.toString();
			}
			sh.setOwnerId(deptId + "");
			sh.setOwnerName(ownerName);

			//2.根据不同操作类型执行操作
			if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
				//如果是新增
				sh.setOperateState(CommonConstant.OPER_TYPE_ADD);
			}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
				//如果是修改
				sh.setOperateState(CommonConstant.OPER_TYPE_UPDATE);
			}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
				//如果是删除
				sh.setOperateState(CommonConstant.OPER_TYPE_DELETE);
			}
			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_SHIP);
			container.setMessage(sh);
			container.setBusiObject(ship);
			container.setIsBatch(false);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}


	/**
	 * 船舶（批量）
	 * @param operType
	 * @param ships
	 * @return
	 */
	@PostMapping(SHIP_BATCH_URL)
	@Override
	public R<String> shipBatch(@Param("operType") String operType, @RequestBody List<BdmShip> ships) {
		//1.校验参数
		if(StringUtils.isEmpty(operType)){
			return R.fail("操作类型不能为空");
		}
		//批量操作不支持修改
		if(!(operType.equals(CommonConstant.OPER_TYPE_ADD) || operType.equals(CommonConstant.OPER_TYPE_DELETE))){
			return R.fail("操作类型["+operType+"]不正确");
		}
		if(ships == null || ships.size() < 1){
			return R.fail("数据不能为空");
		}

		try{
			long deptId = ships.get(0).getDeptId();
			Object owner = redisTemplate.opsForHash().get(RedisConstant.HASH_DEPT_NODE + CommonConstant.DEFAULT_TENANT_ID, deptId);
			String ownerName = "";
			if(owner != null){
				ownerName = owner.toString();
			}
			List<Ship> list = new ArrayList<>();
			for(BdmShip ship : ships){
				//2.构建协议报文对象
				Ship sh = new Ship();
				sh.setOperateTime(sdfHolder.get().format(new Date()));
				sh.setVesselNo(ship.getNumber());
				sh.setVesselNameCn(ship.getName());
				sh.setVesselNameEn(ship.getNameEn());
				sh.setVesselMmsi(ship.getMmsi());
				sh.setVesselImo(ship.getImoNumber());
				sh.setVesselCallSign(ship.getCallSign());
				sh.setVesselConstructionTime(DateUtil.sdfHolderNoLine.get().format(ship.getConstructionTime()));
				sh.setVesselType(ship.getCategory()+"");
				sh.setVesselLength(ship.getLength()+"");
				sh.setVesselWidth(ship.getBreadth()+"");
				sh.setVesselGt(String.valueOf(ship.getMaxGross()));
				sh.setVesselDwt(String.valueOf(ship.getNet()));
				sh.setVesselNt(String.valueOf(ship.getNet()));
				sh.setOwnerId(deptId + "");
				sh.setOwnerName(ownerName);

				//2.根据不同操作类型执行操作
				if(CommonConstant.OPER_TYPE_ADD.equals(operType)){
					//如果是新增
					sh.setOperateState(CommonConstant.OPER_TYPE_ADD);
				}else if(CommonConstant.OPER_TYPE_UPDATE.equals(operType)){
					//如果是修改
					sh.setOperateState(CommonConstant.OPER_TYPE_UPDATE);
				}else if(CommonConstant.OPER_TYPE_DELETE.equals(operType)){
					//如果是删除
					sh.setOperateState(CommonConstant.OPER_TYPE_DELETE);
				}
				list.add(sh);
			}
			//3.发送到kafka
			MessageContainer container = new MessageContainer();
			container.setBusiCode(CommonConstant.BUSI_CODE_SHIP);
			container.setMessage(list);
			container.setBusiObject(ships);
			container.setIsBatch(true);
			kafkaTemplate.send(CommonConstant.MESSAGE_SEND_TOPIC, container);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("报文信息发送到kafka失败",e);
			return R.fail("操作失败，"+ e.getMessage());
		}
	}
}
