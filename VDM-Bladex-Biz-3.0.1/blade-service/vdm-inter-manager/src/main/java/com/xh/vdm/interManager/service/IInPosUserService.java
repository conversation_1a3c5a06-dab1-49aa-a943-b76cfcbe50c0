package com.xh.vdm.interManager.service;

import com.xh.vdm.interManager.entity.InPosUser;
import com.xh.vdm.interManager.vo.InPosUserVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.vo.request.InterfaceAuthRequest;
import com.xh.vdm.interManager.vo.response.InterfaceResponse;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
public interface IInPosUserService extends IService<InPosUser> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inPosUser
	 * @return
	 */
	IPage<InPosUserVO> selectInPosUserPage(IPage<InPosUserVO> page, InPosUserVO inPosUser);


	/**
	 * 查询用户授权的接口
	 * @param request
	 * @return
	 */
	List<InterfaceResponse> findInterfaceAuth(InterfaceAuthRequest request);

}
