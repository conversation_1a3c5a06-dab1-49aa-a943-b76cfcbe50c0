<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.terminal.PntDeviceMapper">

    <update id="updateDept">
        update bdm_pnt_device
        set dept_id = #{deptId}
        where target_id = #{id}
          and target_type = #{targetType}

    </update>

    <delete id="deleteByTargetIds">
        update bdm_pnt_device
        set target_id = 0, target_type = 0
        where target_id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" index="i" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>

</mapper>
