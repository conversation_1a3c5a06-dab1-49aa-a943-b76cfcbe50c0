package com.xh.vdm.interManager.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.interManager.entity.LocationKudu;

import java.util.List;

public interface ILocationKuduService extends IService<LocationKudu> {

	/**
	 * 查询最近给定的时间戳内最新的定位点
	 * @param deviceId
	 * @param timestampBefore
	 * @return
	 * @throws Exception
	 */
	LocationKudu findLatestLocationByTimestamp(Long deviceId, Long timestampBefore) throws Exception;

}
