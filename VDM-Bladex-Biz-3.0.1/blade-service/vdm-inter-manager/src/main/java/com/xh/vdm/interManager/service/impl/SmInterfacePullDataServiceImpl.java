package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.SmInterfacePullData;
import com.xh.vdm.interManager.mapper.SmInterfacePullDataMapper;
import com.xh.vdm.interManager.service.ISmInterfacePullDataService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Service
public class SmInterfacePullDataServiceImpl extends ServiceImpl<SmInterfacePullDataMapper, SmInterfacePullData> implements ISmInterfacePullDataService {

}
