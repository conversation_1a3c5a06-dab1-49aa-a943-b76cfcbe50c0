package com.xh.vdm.interManager.consumer;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import com.xh.vdm.interManager.core.InterManager;
import com.xh.vdm.interManager.dto.message.*;
import com.xh.vdm.interManager.dto.send.TerminalOnlineOffline;
import com.xh.vdm.interManager.dto.send.TerminalStatus;
import com.xh.vdm.interManager.entity.*;
import com.xh.vdm.interManager.mqtt.MqttSendClient;
import com.xh.vdm.interManager.service.*;
import com.xh.vdm.interManager.utils.BeanUtil;
import com.xh.vdm.interManager.utils.MessageUtils;
import com.xh.vdm.interManager.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictKeyConstant;
import org.springblade.common.constant.RedisConstant;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.StringUtils;
import org.springblade.core.tool.utils.Func;
import org.springblade.entity.Alarm;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 平台协议动态数据报文推送
 * 动态数据推送，动态数据推送支持HTTP 和 MQTT形式的推送
 * 从对应的topic中接收数据，推送到相应平台，如果配置了 plat_http_send_code，则推送到HTTP，否则推送到MQTT
 */
@Component
@EnableKafka
@Slf4j
public class MessageActiveDataSendConsumer {


	@Resource
	private IInPosSystemObjectService objectService;

	@Resource
	private ISmInterfaceSystemService systemService;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	@Resource
	private MqttSendClient mqttSendClient;

	@Resource
	private MessageUtils messageUtils;

	@Resource
	private IInInterLogService logService;

	@Resource
	private ISmInterfaceManageService interfaceManageService;

	@Resource
	private InterManager interManager;

	@Resource
	private ITerminalService terminalService;

	@Resource
	private IBdmAbstractTargetService targetService;

	/**
	 * 终端上下线推送
	 * @param consumerRecords
	 * @param acknowledgment
	 */
	@KafkaListener(containerFactory = "terminalOnlineOfflineKafkaListenerContainerFactory", topics = {CommonConstant.MESSAGE_SEND_TERMIANL_ONLINE_OFFLINE},  batch = "true")
	public void sendOnlineOffline(List<String> consumerRecords, Acknowledgment acknowledgment){
		try{
			for(String recordStr : consumerRecords){
				log.info("message-send>>sendOnlineOffline:将要处理的数据为："+recordStr);
				TerminalOnlineOffline record = JSON.parseObject(recordStr, TerminalOnlineOffline.class);
				String objectType = "2" + record.getDeviceType();
				//1.查找终端对应的平台id列表
				String key = objectType + "-" + record.getDeviceId();
				String systemIdsStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_INTER_SYSTEM_OBJECT + key);
				List<Long> systemIds = new ArrayList<>();
				if(StringUtil.isEmpty(systemIdsStr)){
					//如果未获取到外部平台id列表
					List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
						.eq(InPosSystemObject::getObjectId, record.getDeviceId())
						.eq(InPosSystemObject::getObjectType,Integer.parseInt(objectType))
					);
					//systemId去重
					Set<Long> systemIdSet = new HashSet<>();
					list.forEach(item -> {
						systemIdSet.add(item.getSystemId());
					});
					for(Long id : systemIdSet){
						if(StringUtils.isEmpty(systemIdsStr)){
							systemIdsStr = id + "";
						}else{
							systemIdsStr = systemIdsStr + "," + id;
						}
					}
					if(StringUtils.isNotEmpty(systemIdsStr)){
						//保存 业务对象id-系统id列表，有效期2小时
						stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_INTER_SYSTEM_OBJECT + key, systemIdsStr,12,TimeUnit.HOURS);
					}
				}
				systemIds = Func.toLongList(systemIdsStr);

				//2.查找平台对应的接口
				List<SmInterfaceSystem> systemList = new ArrayList<>();
				if(systemIds != null && systemIds.size() > 0){
					for(Long id : systemIds){
						String systemStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_INTER_SYSTEM+id);
						if(StringUtils.isEmpty(systemStr)){
							SmInterfaceSystem system = systemService.getById(id);
							stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_INTER_SYSTEM+id, JSON.toJSONString(system), 12, TimeUnit.HOURS);
							systemList.add(system);
						}else{
							SmInterfaceSystem system = JSON.parseObject(systemStr, SmInterfaceSystem.class);
							systemList.add(system);
						}
					}
				}

				//3.遍历平台，推送数据
				for(SmInterfaceSystem system : systemList){
					if(system == null){
						continue;
					}
					String httpSendCode = system.getPlatHttpSendCode();
					//上下线状态：0-上线  1-下线
					Integer type = record.getOnOffLine();
					String busiCode = "";
					//查询终端信息，获取deptId
					String k = CommonConstant.CACHE_DEVICE + record.getDeviceType() + "-" + record.getDeviceId();
					String deptId = null;
					String deviceStr = stringRedisTemplate.opsForValue().get(k);
					if(StringUtils.isEmpty(deviceStr)){
						Object device = terminalService.findDeviceByTypeAndId(record.getDeviceType() + "", (long)record.getDeviceId());
						stringRedisTemplate.opsForValue().set(k, JSON.toJSONString(device), 12, TimeUnit.HOURS);
						deviceStr = JSON.toJSONString(device);
					}
					JSONObject obj = JSON.parseObject(deviceStr);
					if(obj != null){
						deptId = obj.getString("deptId");
					}

					Object o = null;
					if(type == 0){
						//如果是上线
						busiCode = CommonConstant.BUSI_CODE_ONLINE;
						DeviceOnline online = new DeviceOnline();
						online.setDeviceId(record.getUniqueId() + "");
						online.setDeviceType(record.getDeviceNum());
						online.setDeviceNum(record.getDeviceNum());
						String dateStr = DateUtil.sdfHolderNoLine.get().format(record.getTime());
						online.setOnlineTime(dateStr);
						online.setOwnerId(deptId);
						online.setUserType(record.getTargetType()+"");
						online.setUserCode(record.getTargetName());
						online.setOwnerId(deptId);
						DeviceOnline[] array = new DeviceOnline[]{online};
						Map<String,Object> map = new HashMap<>();
						map.put("reqInfo",array);
						o = map;
					}else{
						//如果是下线
						busiCode = CommonConstant.BUSI_CODE_OFFLINE;
						DeviceOffline offline = new DeviceOffline();
						offline.setDeviceId(record.getUniqueId() + "");
						offline.setDeviceType(record.getDeviceNum());
						offline.setDeviceNum(record.getDeviceNum());
						offline.setOfflineTime(DateUtil.sdfHolderNoLine.get().format(record.getTime()));
						offline.setOwnerId(deptId);
						offline.setOwnerId(deptId);
						DeviceOffline[] array = new DeviceOffline[]{offline};
						Map<String,Object> map = new HashMap<>();
						map.put("reqInfo",array);
						o = map;
					}

					if(!StringUtils.isEmpty(httpSendCode)){
						//如果带有http标识，则认为配置了http接口，那就推送到http接口
						sendHttpMessage(o, system, busiCode);
					}else{
						//如果没有配置http推送code，则推送到mqtt
						String topic = "send/" + busiCode + "/" + system.getId();
						mqttSendClient.publish(false, topic, JSON.toJSONString(o));
					}
				}
			}
			// 手动提交offset
			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("终端上下线数据同步接口消费失败 :" + e.getMessage(), e);
		}
	}


	/**
	 * 定位数据推送
	 * @param consumerRecords
	 * @param acknowledgment
	 */
	@KafkaListener(
		containerFactory = "locationKafkaListenerContainerFactory",
		topics = {"ce.comms.fct.location.0"},
		batch = "true"
	)
	public void locationsSend (List<String> consumerRecords, Acknowledgment acknowledgment) {
		try{
			long startTime = System.currentTimeMillis();
			log.info("message-send>>locationsSend:平台协议--定位数据推送：将要执行数据推送，将要处理{}条数据", consumerRecords==null?0:consumerRecords.size());
			for(String recordStr : consumerRecords){

				long start1 = System.currentTimeMillis();
				log.info("message-send>>locationsSend:将要推送的原始数据为："+ recordStr);
				Location record = JSON.parseObject(recordStr, Location.class);

				//只推送有效的数据
				if(record.getValid() != 1){
					continue;
				}

				String objectType = "2" + record.getDeviceType();
				//1.查找终端对应的平台id列表
				long start11 = System.currentTimeMillis();
				String key = objectType + "-" + record.getDeviceId();
				log.info("message-send>>locationsSend:redis的key为 " + key);
				String systemIdsStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_INTER_SYSTEM_OBJECT + key);
				List<Long> systemIds = new ArrayList<>();
				if(StringUtil.isEmpty(systemIdsStr)){
					log.info("message-send>>locationsSend:从缓存中获取终端对应的平台id失败，将要查询数据库");
					//如果未获取到外部平台id列表
					List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
						.eq(InPosSystemObject::getObjectId, record.getDeviceId())
						.eq(InPosSystemObject::getObjectType,Integer.parseInt(objectType))
					);
					log.info("message-send>>locationsSend:查询数据库获取的数据为：" + JSON.toJSONString(list));
					//systemId去重
					Set<Long> systemIdSet = new HashSet<>();
					list.forEach(item -> {
						systemIdSet.add(item.getSystemId());
					});
					for(Long id : systemIdSet){
						if(StringUtils.isEmpty(systemIdsStr)){
							systemIdsStr = id + "";
						}else{
							systemIdsStr = systemIdsStr + "," + id;
						}
					}
					if(StringUtils.isNotEmpty(systemIdsStr)){
						//保存 业务对象id-系统id列表，有效期2小时
						stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_INTER_SYSTEM_OBJECT + key, systemIdsStr,12,TimeUnit.HOURS);
					}
				}
				log.info("message-send>>locationsSend:获取终端对应的平台id为：" + systemIdsStr);
				systemIds = Func.toLongList(systemIdsStr);
				long end11 = System.currentTimeMillis();
				log.info("location_send_log，查找平台id耗时"+(end11 - start11));

				//2.查找平台对应的接口
				long start12 = System.currentTimeMillis();
				List<SmInterfaceSystem> systemList = new ArrayList<>();
				if(systemIds != null && systemIds.size() > 0){
					for(Long id : systemIds){
						String systemStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_INTER_SYSTEM+id);
						if(StringUtils.isEmpty(systemStr)){
							SmInterfaceSystem system = systemService.getById(id);
							stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_INTER_SYSTEM+id, JSON.toJSONString(system), 12, TimeUnit.HOURS);
							systemList.add(system);
						}else{
							SmInterfaceSystem system = JSON.parseObject(systemStr, SmInterfaceSystem.class);
							systemList.add(system);
						}
					}
				}
				log.info("message-send>>locationsSend:查找平台信息完成，数量为：{}", systemList ==null?0:systemList.size());
				long end12 = System.currentTimeMillis();
				log.info("location_send_log, 查找平台对应的接口耗时 "+(end12 - start12));


				//3.遍历平台，推送数据
				long start14 = System.currentTimeMillis();
				for(SmInterfaceSystem system : systemList){
					if(system == null){
						continue;
					}
					String httpSendCode = system.getPlatHttpSendCode();

					//构建报文数据
					LocDataUpload loc = new LocDataUpload();
					//处理定位方式：0 卫星定位  1 蓝牙信标  2 LBS定位  3 惯导   4 WIFI定位
					//todo sss
					/*if(){

					}*/
					loc.setLocType((int)(record.getPosSystem()));
					loc.setDirection(record.getBearing().intValue());
					//modify by zhouxw 2024-11-25 协议中的设备编号使用设备序列号
					loc.setDeviceId(record.getDeviceUniqueId());
					//查询终端信息，获取deptId
					long start111 = System.currentTimeMillis();
					String k = CommonConstant.CACHE_DEVICE + record.getDeviceType() + "-" + record.getDeviceId();
					String deptId = null;
					String deviceStr = stringRedisTemplate.opsForValue().get(k);
					if(StringUtils.isEmpty(deviceStr)){
						Object device = terminalService.findDeviceByTypeAndId(record.getDeviceType() + "", (long)record.getDeviceId());
						stringRedisTemplate.opsForValue().set(k, JSON.toJSONString(device), 12, TimeUnit.HOURS);
						deviceStr = JSON.toJSONString(device);
					}
					JSONObject obj = JSON.parseObject(deviceStr);
					if(obj != null){
						deptId = obj.getString("deptId");
					}
					loc.setOwnerId(deptId);
					long end111 = System.currentTimeMillis();
					log.info("location_send_log，查询deptId耗时" + (end111 - start111));
					if(loc.getLocType().equals("3") || loc.getLocType().equals("4")){
						//如果是惯导或者蓝牙，则传的是xyz
						loc.setX(record.getLongitude());
						loc.setY(record.getLatitude());
						loc.setZ(record.getAltitude().floatValue());
					}else{
						//如果是卫星定位
						loc.setLatitude(record.getLatitude());
						loc.setLongitude(record.getLongitude());
						loc.setAltitude(record.getAltitude().floatValue());
					}
					loc.setSpeed(record.getSpeed().floatValue());
					Date date = new Date();
					date.setTime(record.getTime() * 1000);
					String dateStr = DateUtil.sdfHolderNoLine.get().format(date);
					loc.setTime(dateStr);
					//如果是批量上报或者补报，则为非新定位
					//batch：0：正常上传，1：批量上传，2：补传
					if(record.getBatch() != 0){
						//非新定位
						loc.setState(0);
					}else{
						//新定位
						loc.setState(1);
					}
					loc.setDeviceNum(record.getDeviceNum());
					loc.setUserType(record.getTargetType()+"");
					//使用者编号，number字段
					Long targetId = record.getTargetId();
					if(targetId != null){
						//从redis中获取
						String targetData = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_PREFIX_BDM_ABSTRACT_TARGET + record.getTargetId());
						if(StringUtils.isEmpty(targetData)){
							//如果缓存中不存在，则查询数据库
							BdmAbstractTarget target = targetService.getById(record.getTargetId());
							if(target != null){
								stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_PREFIX_BDM_ABSTRACT_TARGET + record.getTargetId(), JSON.toJSONString(target));
								loc.setUserCode(target.getNumber());
							}else{
								loc.setUserCode("");
							}
						}else{
							BdmAbstractTarget tar = JSON.parseObject(targetData, BdmAbstractTarget.class);
							loc.setUserCode(tar.getNumber()+"");
						}
					}else{
						loc.setUserCode("");
					}

					loc.setDeviceType(record.getDeviceType()+"");
					//终端传上来的定位数据，均使用CGCS2000
					loc.setMapType(2);
					LocDataUpload[] array = new LocDataUpload[]{loc};
					Map<String,Object> map = new HashMap<>();
					map.put("reqInfo",array);
					Object o = map;
					long start112 = System.currentTimeMillis();
					if(!StringUtils.isEmpty(httpSendCode)){
						log.info("location_send_log>>locationsSend:将要使用HTTP进行推送");
						//如果带有http标识，则认为配置了http接口，那就推送到http接口
						sendHttpMessage(o, system, CommonConstant.BUSI_CODE_LOCATION);
					}else{
						log.info("location_send_log>>locationsSend:将要使用MQTT进行推送");
						//如果没有配置http推送code，则推送到mqtt
						String topic = "send/" + CommonConstant.BUSI_CODE_LOCATION + "/" + system.getId();
						log.info("location_send_log>>locationsSend:MQTT topic的名称为 {}",topic);
						//推送时，允许字段为空
						mqttSendClient.publish(false, topic, JSON.toJSONString(o, SerializerFeature.WriteMapNullValue));
						log.info("message-send>>locationsSend:mqtt推送完成，推送的数据为：{}", JSON.toJSONString(array,SerializerFeature.WriteMapNullValue));
					}
					//todo 此处写得有问题，影响生产，先注释
					this.recordLocShare(record);
					long end112 = System.currentTimeMillis();
					log.info("location_send_log，执行发送动作耗时" + (end112 - start112));
					long end14 = System.currentTimeMillis();
					log.info("location_send_log，推送耗时" + (end14 - start14));
				}
				long end1 = System.currentTimeMillis();
				log.info("location_send_log，推送一次耗时" + (end1 - start1));
			}
			// 手动提交offset
			log.info("location_send_log，将要提交offset");
			acknowledgment.acknowledge();
			long endTime = System.currentTimeMillis();
			log.info("推送完成，共推送数据"+(consumerRecords==null?0:consumerRecords.size())+"条数据，本次推送耗时：" + (endTime - startTime));
		} catch (Exception e) {
			log.error("message-send>>locationsSend:定位数据推送同步接口消费失败 :" + e.getMessage(), e);
		}
	}




	/**
	 * 告警数据推送
	 * @param consumerRecords
	 * @param acknowledgment
	 */
	@KafkaListener(
		containerFactory = "alarmKafkaListenerContainerFactory",
		topics = {"GuoNeng_alarm"},
		batch = "true"
	)
	public void alarmSend (List<String> consumerRecords, Acknowledgment acknowledgment) {
		try{
			for(String recordStr : consumerRecords){
				Alarm record = JSON.parseObject(recordStr, Alarm.class);
				String objectType = "2" + record.getDeviceType();
				//1.查找终端对应的平台id列表
				String key = objectType + "-" + record.getDeviceId();
				String systemIdsStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_INTER_SYSTEM_OBJECT + key);
				List<Long> systemIds = new ArrayList<>();
				if(StringUtil.isEmpty(systemIdsStr)){
					//如果未获取到外部平台id列表
					List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
						.eq(InPosSystemObject::getObjectId, record.getDeviceId())
						.eq(InPosSystemObject::getObjectType,Integer.parseInt(objectType))
					);
					//systemId去重
					Set<Long> systemIdSet = new HashSet<>();
					list.forEach(item -> {
						systemIdSet.add(item.getSystemId());
					});
					for(Long id : systemIdSet){
						if(StringUtils.isEmpty(systemIdsStr)){
							systemIdsStr = id + "";
						}else {
							systemIdsStr = systemIdsStr + "," + id;
						}
					}
					if(StringUtils.isNotEmpty(systemIdsStr)){
						//保存 业务对象id-系统id列表，有效期2小时
						stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_INTER_SYSTEM_OBJECT + key, systemIdsStr,12,TimeUnit.HOURS);
					}
				}
				systemIds = Func.toLongList(systemIdsStr);

				//2.查找平台对应的接口
				List<SmInterfaceSystem> systemList = new ArrayList<>();
				if(systemIds != null && systemIds.size() > 0){
					for(Long id : systemIds){
						String systemStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_INTER_SYSTEM+id);
						if(StringUtils.isEmpty(systemStr)){
							SmInterfaceSystem system = systemService.getById(id);
							stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_INTER_SYSTEM+id, JSON.toJSONString(system), 12, TimeUnit.HOURS);
							systemList.add(system);
						}else{
							SmInterfaceSystem system = JSON.parseObject(systemStr, SmInterfaceSystem.class);
							systemList.add(system);
						}
					}
				}

				//3.遍历平台，推送数据
				for(SmInterfaceSystem system : systemList){
					String httpSendCode = system.getPlatHttpSendCode();

					//构建报文数据
					AlarmDataUpload alarm = new AlarmDataUpload();
					//todo 告警数据怎么获取定位类型比较合适
					alarm.setLocType(0);
					//todo 方向要怎么获取比较合适
					//alarm.setDirection(record.getBearing()+"");
					alarm.setDeviceId(record.getDeviceId()+"");
					//查询终端信息，获取deptId
					String k = CommonConstant.CACHE_DEVICE + record.getDeviceType() + "-" + record.getDeviceId();
					String deptId = null;
					String deviceStr = stringRedisTemplate.opsForValue().get(k);
					if(StringUtils.isEmpty(deviceStr)){
						Object device = terminalService.findDeviceByTypeAndId(record.getDeviceType() + "", (long)record.getDeviceId());
						stringRedisTemplate.opsForValue().set(k, JSON.toJSONString(device), 12, TimeUnit.HOURS);
						deviceStr = JSON.toJSONString(device);
					}
					JSONObject obj = JSON.parseObject(deviceStr);
					if(obj != null){
						deptId = obj.getString("deptId");
					}
					alarm.setOwnerId(deptId);
					if(alarm.getLocType().equals("0")){
						//todo 能否通过获取定位信息来获取经纬度，告警中有大量的定位信息
						//如果是卫星定位
						alarm.setLatitude(record.getStartLat());
						alarm.setLongitude(record.getStartLon());
						//alarm.setAltitude(record.getAltitude()+"");
					}else if(alarm.getLocType().equals("1")){
						//蓝牙定位
						/*alarm.setX(record.getLongitude()+"");
						alarm.setY(record.getLatitude()+"");
						alarm.setZ(record.getAltitude()+"");*/
					}
					//alarm.setSpeed(record.getSpeed()+"");
					alarm.setTime(record.getStartTime()+"");
					//新定位
					alarm.setState(1);
					alarm.setDeviceNum(record.getDeviceNum());
					alarm.setUserType(record.getTargetType()+"");
					alarm.setUserCode(record.getTargetId()+"");
					alarm.setDeviceType(record.getDeviceType()+"");
					//终端传上来的定位数据，均使用CGCS2000
					alarm.setMapType(2);
					alarm.setAlarmLevel((int)record.getLevel());
					//todo 查询告警名称，使用业务字典中的 alarm_type
					//alarm.setAlarmName(record.get);
					alarm.setAlarmType((int)record.getType());
					alarm.setCompleted(record.getEndTime() * 1000 - System.currentTimeMillis() > 0?1:0);

					AlarmDataUpload[] array = new AlarmDataUpload[]{alarm};
					Map<String,Object> map = new HashMap<>();
					map.put("reqInfo",array);
					Object o = map;
					if(!StringUtils.isEmpty(httpSendCode)){
						//如果带有http标识，则认为配置了http接口，那就推送到http接口
						sendHttpMessage(o, system, CommonConstant.BUSI_CODE_ALARM);
					}else{
						//如果没有配置http推送code，则推送到mqtt
						String topic = "send/" + CommonConstant.BUSI_CODE_ALARM + "/" + system.getId();
						mqttSendClient.publish(false, topic, JSON.toJSONString(o));
					}
				}
			}
			// 手动提交offset
			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("终端上下线数据同步接口消费失败 :" + e.getMessage(), e);
		}
	}




	/**
	 * 终端状态数据推送
	 * @param consumerRecords
	 * @param acknowledgment
	 */
	@KafkaListener(
		containerFactory = "terminalStatusKafkaListenerContainerFactory",
		topics = {"ce.monit.dev.status.0"},
		batch = "true"
	)
	public void terminalStatusSend (List<String> consumerRecords, Acknowledgment acknowledgment) {
		try{
			//todo 需要跟焱林确认kafka中数据结构
			for(String recordStr : consumerRecords){
				TerminalStatus record = JSON.parseObject(recordStr, TerminalStatus.class);
				String objectType = "2" + record.getDeviceType();
				//1.查找终端对应的平台id列表
				String key = objectType + "-" + record.getDeviceId();
				String systemIdsStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_INTER_SYSTEM_OBJECT + key);
				List<Long> systemIds = new ArrayList<>();
				if(StringUtil.isEmpty(systemIdsStr)){
					//如果未获取到外部平台id列表
					List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
						.eq(InPosSystemObject::getObjectId, record.getDeviceId())
						.eq(InPosSystemObject::getObjectType,Integer.parseInt(objectType))
					);
					//systemId去重
					Set<Long> systemIdSet = new HashSet<>();
					list.forEach(item -> {
						systemIdSet.add(item.getSystemId());
					});
					for(Long id : systemIdSet){
						if(StringUtils.isEmpty(systemIdsStr)){
							systemIdsStr = id + "";
						}else{
							systemIdsStr = systemIdsStr + "," + id;
						}
					}
					if(StringUtils.isNotEmpty(systemIdsStr)){
						//保存 业务对象id-系统id列表，有效期2小时
						stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_INTER_SYSTEM_OBJECT + key, systemIdsStr,12,TimeUnit.HOURS);
					}
				}
				systemIds = Func.toLongList(systemIdsStr);

				//2.查找平台对应的接口
				List<SmInterfaceSystem> systemList = new ArrayList<>();
				if(systemIds != null && systemIds.size() > 0){
					for(Long id : systemIds){
						String systemStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_INTER_SYSTEM+id);
						if(StringUtils.isEmpty(systemStr)){
							SmInterfaceSystem system = systemService.getById(id);
							stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_INTER_SYSTEM+id, JSON.toJSONString(system), 12, TimeUnit.HOURS);
							systemList.add(system);
						}else{
							SmInterfaceSystem system = JSON.parseObject(systemStr, SmInterfaceSystem.class);
							systemList.add(system);
						}
					}
				}

				//3.遍历平台，推送数据
				for(SmInterfaceSystem system : systemList){
					String httpSendCode = system.getPlatHttpSendCode();

					//构建报文数据
					DeviceState state = new DeviceState();
					state.setDeviceState(record.getDeviceState());
					state.setAlarmTime(DateUtil.sdfHolderNoLine.get().format(record.getAlarmTime()));
					state.setOwnerId(record.getDeptId()+"");
					state.setDeviceType(record.getDeviceType()+"");
					state.setDeviceNum(record.getDeviceNum());

					//协议中的deviceId应该传uniqueId
					String k = CommonConstant.CACHE_DEVICE + record.getDeviceType() + "-" + record.getDeviceId();
					String deviceStr = stringRedisTemplate.opsForValue().get(k);
					if(StringUtils.isEmpty(deviceStr)){
						Object device = terminalService.findDeviceByTypeAndId(record.getDeviceType() + "", (long)record.getDeviceId());
						stringRedisTemplate.opsForValue().set(k, JSON.toJSONString(device), 12, TimeUnit.HOURS);
						deviceStr = JSON.toJSONString(device);
					}
					JSONObject obj = JSON.parseObject(deviceStr);
					String uniqueId = "";
					if(obj != null){
						uniqueId = obj.getString("uniqueId");
					}
					state.setDeviceId(uniqueId);
					DeviceState[] array = new DeviceState[]{state};
					Map<String,Object> map = new HashMap<>();
					map.put("reqInfo",array);
					Object o = map;
					if(!StringUtils.isEmpty(httpSendCode)){
						//如果带有http标识，则认为配置了http接口，那就推送到http接口
						sendHttpMessage(o, system, CommonConstant.BUSI_CODE_TERMINAL_STATE);
					}else{
						//如果没有配置http推送code，则推送到mqtt
						String topic = "send/" + CommonConstant.BUSI_CODE_TERMINAL_STATE + "/" + system.getId();
						mqttSendClient.publish(false, topic, JSON.toJSONString(o));
					}
				}
			}
		} catch (Exception e) {
			log.error("终端上下线数据同步接口消费失败 :" + e.getMessage(), e);
		}
		// 手动提交offset
		// 终端状态数据，目前没有实际的应用，此处设置为允许失败不推送
		acknowledgment.acknowledge();
	}




	/**
	 * 位置平台向外部平台推送报文
	 * @param object 报文列表，这里必须是一个list
	 * @param busiCode 报文中的业务编码
	 * @throws Exception
	 */
	private void sendHttpMessage(Object object, SmInterfaceSystem s, String busiCode) throws Exception {

		//3.调用接口推送数据
		//组装报文
		Header header = new Header();
		header.setSystemId(CommonConstant.POS_SYSTEM_ID);
		header.setServiceId(busiCode);
		List<InInterLog> logs = new ArrayList<>();
		String code = s.getPlatHttpSendCode();
		header.setSerialNum((new Date()).getTime() + "");
		header.setReqTime(DateUtil.sdfHolder.get().format(new Date()).replace("-", "").replace(" ", ""));
		Message message = messageUtils.encryptData(header, JSON.toJSONString(object), s.getId() + "");
		Map<String, Object> map = BeanUtil.beanToMap(message);
		InInterLog interLog = new InInterLog();
		if (!StringUtils.isEmpty(code)) {
			long start = System.currentTimeMillis();
			R<Object> res = null;
			//一个平台推送失败，不应影响其他平台的推送
			try {
				res = interManager.call(code, map);
			} catch (Exception e) {
				log.error("推送报文失败 ", e);
			}
			long end = System.currentTimeMillis();
			//查询接口基本信息
			String interJSONStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_POS_INTER_PREFIX + code);
			SmInterfaceManage inter = null;
			if (org.springblade.common.utils.StringUtils.isEmpty(interJSONStr)) {
				inter = interfaceManageService.findInterfaceManageByInterCode(code);
				//有效期12个小时
				stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_POS_INTER_PREFIX + code, JSON.toJSONString(inter), 12, TimeUnit.HOURS);
			} else {
				inter = JSON.parseObject(interJSONStr, SmInterfaceManage.class);
			}
			//动态数据，数据量比较大，暂不记录日志
			/*interLog.setDirection(CommonConstant.INTER_DIRECTION_OUT);
			interLog.setInterCode(code);
			interLog.setInterName(inter.getInterNote());
			interLog.setSystemId(s.getId());
			interLog.setServiceId(0L);
			interLog.setBusiServiceId(busiCode);
			interLog.setInterType(inter.getInterType());
			interLog.setUrl(inter.getUrl());
			interLog.setParam(JSON.toJSONString(map));
			interLog.setResMessage(JSON.toJSONString(res));
			interLog.setCallTime(new Date());
			interLog.setCreateTime(new Date());
			interLog.setCallCost(end-start);
			interLog.setServerIp(inter.getIp());
			if(!res.isSuccess()){
				interLog.setResult(0);
			}else{
				interLog.setResult(1);
			}
		}
		logs.add(interLog);
		logService.saveBatch(logs);*/
		}

	}

	private void recordLocShare (Location location) {
		Map<Object, Object> targetMap =
			Boolean.TRUE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_BASE_INFO_TARGET)) ?
			this.redisTemplate.opsForHash().entries(RedisConstant.HASH_BASE_INFO_TARGET) :
			new HashMap<>();

		Object targetO;
		Object locShareO;
		JSONObject locShareJ;
		long quantity;
		if (!String.valueOf(location.getTargetType()).equals(DictKeyConstant.TARGET_TYPE_VEHICLE)) {
			return;
		}

		String targetKey = location.getTargetType() + "-" + location.getTargetId();
		if ((!targetMap.containsKey(targetKey)) || ((targetO = targetMap.get(targetKey)) == null)) {
			log.error("fail get target info when record location share: {}", targetKey);
			return;
		}

		JSONObject targetJ = JSONObject.parseObject(targetO.toString());
		if ((targetJ == null) || (!targetJ.containsKey("deptId"))) {
			log.error("fail get dept info when record location share: {}", targetO);
			return;
		}

		targetKey += ("-" + targetJ.get("deptId"));
		if (
			Boolean.FALSE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_TARGET_LOCATION_SHARE)) ||
			(!this.redisTemplate.opsForHash().hasKey(RedisConstant.HASH_TARGET_LOCATION_SHARE, targetKey)) ||
			((locShareO = this.redisTemplate.opsForHash().get(RedisConstant.HASH_TARGET_LOCATION_SHARE, targetKey)) == null)
		) {
			locShareJ = new JSONObject();
			quantity = 0;
		} else {
			locShareJ = JSON.parseObject(JSON.toJSONString(locShareO));
			quantity = locShareJ.getLongValue("quantity");
		}

		locShareJ.put("quantity", ++quantity);
		locShareJ.put("time", System.currentTimeMillis() / 1000);
		this.redisTemplate.opsForHash().put(RedisConstant.HASH_TARGET_LOCATION_SHARE, targetKey, locShareJ);
	}

}
