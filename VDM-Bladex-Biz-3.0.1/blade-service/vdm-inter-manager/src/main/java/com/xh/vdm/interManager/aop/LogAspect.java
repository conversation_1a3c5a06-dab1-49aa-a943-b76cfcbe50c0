package com.xh.vdm.interManager.aop;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.MapUtil;
import com.xh.vdm.interManager.entity.BdmUsualLog;
import com.xh.vdm.interManager.entity.SmInterfaceManage;
import com.xh.vdm.interManager.service.BdmUsualLogService;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springblade.common.annotation.Log;
import org.springblade.common.dept.DeptIdAware;
import org.springblade.common.enums.HttpMethod;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.IPUtils;
import org.springblade.common.utils.ServletUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 操作日志记录处理
 */
@Slf4j
@Aspect
@Component
public class LogAspect {
	@Resource
	private BdmUsualLogService bulUsualLogService;

	private static final Pattern ID_PATTERN = Pattern.compile("id=(\\d+)");

	@Pointcut("execution(* com.xh.vdm.interManager.controller.SmInterfaceManageController.submit(..))")
	public void logSubmitMethod() {
	}

	/**
	 * 环绕 submit 方法的执行。在方法执行前后，我们可以根据 SmInterfaceManage 对象的 id 字段是否为空来判断是新增还是更新操作，并据此记录相应的日志。
	 *
	 * @param joinPoint
	 * @return
	 * @throws Throwable
	 */
	@Around("logSubmitMethod()")
	public Object logAroundSubmitMethod(ProceedingJoinPoint joinPoint) throws Throwable {
		BdmUsualLog usualLog = new BdmUsualLog();
		BladeUser user = new BladeUser();
		Object[] args = joinPoint.getArgs();
		boolean isNew = true;
		if (args != null && args[0] instanceof SmInterfaceManage) {
			SmInterfaceManage smInterfaceManage = (SmInterfaceManage) args[0];
			isNew = Objects.isNull(smInterfaceManage.getId());
			usualLog.setObjectId(smInterfaceManage.getId());
		}
		if (args != null && args.length > 1 && args[1] instanceof BladeUser) {
			user = (BladeUser) args[1];
		}
		// 根据是否为新增操作选择操作类型
		Operation operation = isNew ? Operation.INSERT : Operation.UPDATE;
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		try {
			// 继续执行原方法
			Object result = joinPoint.proceed();

			// 设置日志信息
			usualLog.setUserId(user.getUserId());
			usualLog.setUserAccount(user.getAccount());
			// 获取登录者的IP地址
			String ipAddress = IPUtils.getClientIpAddr(request);
			// 设置IP地址
			usualLog.setServerIp(ipAddress);

			for (Object arg : args) {
				if (arg instanceof DeptIdAware) {
					Long deptId = ((DeptIdAware) arg).getDeptId();
					usualLog.setDeptId(deptId);
					break;
				}
			}
			// 设置操作类型
			usualLog.setOperation(operation.ordinal());
			// 设置操作菜单项
			usualLog.setMenu("第三方平台接口管理");

			// 是否需要保存response，参数和值
			if (ObjectUtil.isNotNull(result)) {

				String jsonResultString = result.toString();

				if (!usualLog.getOperation().equals(Operation.DELETE.ordinal())) {

					// 提取msg字段的值
					int dataIndex = jsonResultString.indexOf("data=") + "data=".length();
					int msgIndex = jsonResultString.indexOf(", msg=");
					String msg = jsonResultString.substring(dataIndex, msgIndex).trim();

					if (usualLog.getOperation().equals(Operation.INSERT.ordinal())) {
						if (msg != null) {
							//新增的信息记录
							usualLog.setDescription(usualLog.getMenu() + "新增了一条ID为" + msg + "的数据");
						}
					} else {
						int startIndex = jsonResultString.indexOf("data");
						String msgText = jsonResultString.substring(startIndex);
						if (msgText.contains("[")) {
							// 使用正则表达式匹配所有方括号内的内容
							String regex = "\\[(.*?)\\]";
							Pattern pattern = Pattern.compile(regex);
							Matcher matcher = pattern.matcher(msgText);
							StringBuilder msgBuilder = new StringBuilder();
							while (matcher.find()) {
								msgBuilder.append(matcher.group(0)).append(" ");
							}
							String editMsg = msgBuilder.toString().trim();
							//修改的信息记录
							usualLog.setDescription(usualLog.getMenu() + "修改了一条ID为" + usualLog.getObjectId() + "的数据，修改的内容为：" + editMsg);
						}
					}
				}
			}
			// 数据保存到数据库
			bulUsualLogService.getBaseMapper().insert(usualLog);
			return result;
		} catch (Throwable e) {
			// 记录异常日志
			log.error("异常信息: {}", e.getMessage());
			e.printStackTrace();
			return R.fail("操作失败");
		}
	}

	/**
	 * 处理完请求后执行
	 *
	 * @param joinPoint 切点
	 */
	@AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
	public void doAfterReturning(JoinPoint joinPoint, Log controllerLog, Object jsonResult) {
		BladeUser user = new BladeUser();
		Object[] args = joinPoint.getArgs();
		// 遍历参数数组，查找 BladeUser 类型的参数
		for (Object arg : args) {
			if (arg instanceof BladeUser) {
				user = (BladeUser) arg;
				break; // 找到后退出循环
			}
		}

		// 如果没有找到 BladeUser，您可以选择抛出异常或记录日志
		if (user == null) {
			log.warn("未找到 BladeUser 参数");
		}
		handleLog(joinPoint, controllerLog, null, jsonResult, user);
	}

	/**
	 * 拦截异常操作
	 *
	 * @param joinPoint 切点
	 * @param e         异常
	 */
	@AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
	public void doAfterThrowing(JoinPoint joinPoint, Log controllerLog, Exception e) {
		BladeUser user = new BladeUser();
		Object[] args = joinPoint.getArgs();
		// 遍历参数数组，查找 BladeUser 类型的参数
		for (Object arg : args) {
			if (arg instanceof BladeUser) {
				user = (BladeUser) arg;
				break; // 找到后退出循环
			}
		}

		// 如果没有找到 BladeUser，您可以选择抛出异常或记录日志
		if (user == null) {
			log.warn("未找到 BladeUser 参数");
		}
		handleLog(joinPoint, controllerLog, e, null, user);
	}

	protected void handleLog(final JoinPoint joinPoint, Log controllerLog, final Exception e, Object jsonResult, BladeUser user) {

		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		String requestMethod = request.getMethod();
		try {

			// *========数据库日志=========*//
			BdmUsualLog usualLog = new BdmUsualLog();
			usualLog.setUserId(user.getUserId());
			usualLog.setUserAccount(user.getAccount());
			// 获取登录者的IP地址
			String ipAddress = IPUtils.getClientIpAddr(request);
			// 设置IP地址
			usualLog.setServerIp(ipAddress);
			Object[] args = joinPoint.getArgs();
			for (Object arg : args) {
				if (arg instanceof DeptIdAware) {
					Long deptId = ((DeptIdAware) arg).getDeptId();
					usualLog.setDeptId(deptId);
					break;
				}
			}
//			异常捕抓
//            if (e != null) {
//				usualLog.setAfterContent(StringUtils.substring(e.getMessage(), 0, 2000));
//            }
			// 处理设置注解上的参数
			getControllerMethodDescription(joinPoint, controllerLog, usualLog, jsonResult);
			if (e == null && jsonResult != null && ((R) jsonResult).isSuccess()) {
				// 数据保存到数据库
				bulUsualLogService.getBaseMapper().insert(usualLog);
			}
		} catch (Exception exp) {
			// 记录本地异常日志
			log.error("异常信息:{}", exp.getMessage());
			exp.printStackTrace();
		}
	}

	/**
	 * 获取注解中对方法的描述信息 用于Controller层注解
	 *
	 * @param log      日志
	 * @param usualLog 操作日志
	 * @throws Exception
	 */
	public void getControllerMethodDescription(JoinPoint joinPoint, Log log, BdmUsualLog usualLog, Object jsonResult) throws Exception {
		// 设置operation动作
		usualLog.setOperation(log.operation().ordinal());
		// 设置操作菜单项，
		usualLog.setMenu(log.menu());
		// 是否需要保存request，参数和值
		if (log.isSaveRequestData()) {
			// 获取参数的信息，传入到数据库中。
			setRequestValue(joinPoint, usualLog, log.excludeParamNames());
		}

		// 是否需要保存response，参数和值
		if (log.isSaveResponseData() && ObjectUtil.isNotNull(jsonResult)) {
			String jsonResultString = jsonResult.toString();

			if (!usualLog.getOperation().equals(Operation.DELETE.ordinal())) {

				// 提取msg字段的值
				int dataIndex = jsonResultString.indexOf("data=") + "data=".length();
				int msgIndex = jsonResultString.indexOf(", msg=");
				String msg = jsonResultString.substring(dataIndex, msgIndex).trim();

				if (usualLog.getOperation().equals(Operation.INSERT.ordinal())) {
					//新增的信息记录
					usualLog.setDescription(usualLog.getMenu() + "新增了一条ID为" + msg + "的数据");
				} else {
					int startIndex = jsonResultString.indexOf("data");
					String msgText = jsonResultString.substring(startIndex);
					if (msgText.contains("[")) {
						// 使用正则表达式匹配所有方括号内的内容
						String regex = "\\[(.*?)\\]";
						Pattern pattern = Pattern.compile(regex);
						Matcher matcher = pattern.matcher(msgText);
						StringBuilder msgBuilder = new StringBuilder();
						while (matcher.find()) {
							msgBuilder.append(matcher.group(0)).append(" ");
						}
						String editMsg = msgBuilder.toString().trim();
						//修改的信息记录
						usualLog.setDescription(usualLog.getMenu() + "修改了一条ID为" + usualLog.getObjectId() + "的数据，修改的内容为：" + editMsg);
					}
				}
			}
		}
	}

	/**
	 * 获取请求的参数，放到log中
	 *
	 * @param usualLog 操作日志
	 * @throws Exception 异常
	 */
	private void setRequestValue(JoinPoint joinPoint, BdmUsualLog usualLog, String[] excludeParamNames) throws Exception {

		Map<String, String> paramsMap = ServletUtils.getParamMap(ServletUtils.getRequest());
		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		String requestMethod = request.getMethod();

		Object[] args = joinPoint.getArgs();

		if (MapUtil.isNotEmpty(paramsMap) && HttpMethod.GET.name().equals(requestMethod)) {
			//删除的信息记录
			usualLog.setDescription(usualLog.getMenu() + "删除了ID为" + paramsMap.get("ids") + "的数据");
		} else {
			if (args.length > 0) {
				Matcher matcher = ID_PATTERN.matcher(args[0].toString());
				if (matcher.find()) {
					String id = matcher.group(1);
					usualLog.setObjectId(Long.parseLong(id));
				}
			}
		}
	}

}
