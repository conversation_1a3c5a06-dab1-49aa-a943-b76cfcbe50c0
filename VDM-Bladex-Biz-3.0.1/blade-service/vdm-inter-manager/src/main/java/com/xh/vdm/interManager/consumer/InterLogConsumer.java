package com.xh.vdm.interManager.consumer;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.interManager.entity.InInterLog;
import com.xh.vdm.interManager.service.IInInterLogService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 接口日志消费者
 * HTTP接口日志，记录完整日志报文。
 * MQTT接口日志，只记录统计信息
 */
@Component
@EnableKafka
@Slf4j
public class InterLogConsumer {

	@Resource
	private IInInterLogService logService;

	@KafkaListener(containerFactory = "interLogKafkaListenerContainerFactory", topics = {CommonConstant.TOPIC_INTER_LOG})
	public void messageCollect(String interLog, Acknowledgment acknowledgment) {
		try {
			InInterLog record = JSON.parseObject(interLog, InInterLog.class);
			logService.save(record);
			// 手动提交offset
			acknowledgment.acknowledge();
			log.debug("保存接口日志成功，日志信息为：" + JSON.toJSONString(interLog));
		} catch (Exception e) {
			log.error("保存接口日志失败 :" + e.getMessage(), e);
		}
	}


}
