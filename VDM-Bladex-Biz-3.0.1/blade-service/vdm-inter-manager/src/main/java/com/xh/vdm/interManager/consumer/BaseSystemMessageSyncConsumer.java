package com.xh.vdm.interManager.consumer;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.interManager.dto.message.*;
import com.xh.vdm.interManager.entity.BladeDept;
import com.xh.vdm.interManager.entity.BladeUser;
import com.xh.vdm.interManager.service.IBladeDeptService;
import com.xh.vdm.interManager.service.IBladeUserService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 位置平台同步国能基础服务平台数据，机构、人员
 * 目前只考虑增量数据同步（增删改）
 * todo 国能的上游数据推送逻辑还没有开发，所以这里要先设置开关，暂时关闭数据同步逻辑，待上游逻辑完善之后，再开启
 */
@Component
@EnableKafka
@Slf4j
public class BaseSystemMessageSyncConsumer {

	//是否开启基础服务平台数据同步（组织、人员），默认不开启，防止关键数据错乱
	@Value("${sync.plat.enable:false}")
	private boolean syncEnable;

	//操作：添加
	public static final String ADD = "add";
	//操作：修改
	public static final String UPDATE = "update";
	//操作：删除
	public static final String DELETE = "del";

	@Resource
	private IBladeDeptService deptService;

	@Resource
	private IBladeUserService userService;




	/**
	 * @description: 国能基础服务平台将组织、人员等数据写入到kafka中，位置平台进行解析和数据同步
	 **/
	@KafkaListener(containerFactory = "platDataKafkaListenerContainerFactory", topics = {CommonConstant.MESSAGE_PLAT_SYNC_TOPIC})
	public void messageCollect(String messageData, Acknowledgment acknowledgment) {
		try {
			if(syncEnable){
				SyncPlatDataContainer record = JSON.parseObject(messageData, SyncPlatDataContainer.class);
				//1.数据类型 : 1 组织机构、2 系统用户、3 系统角色、4 角色用户、5 功能权限、6 人员信息
				String code = record.getCode();
				//操作类型 : add 添加、update 修改、del 删除
				String opt = record.getOpt();
				if(StringUtils.isEmpty(code) || StringUtils.isEmpty(opt)){
					log.info("[kafka]基础服务平台发送的数据中 code 或 opt 为空");
					return;
				}
				//数据内容
				Object data = record.getData();
				switch (code.trim()) {
					case "1": //组织机构
						List<PlatOrg> list = JSON.parseArray(JSON.toJSONString(data), PlatOrg.class);
						handleOrg(opt, list);
						break;
					case "2": //用户/人员
						List<PlatUser> listU = JSON.parseArray(JSON.toJSONString(data), PlatUser.class);
						handleUser(opt, listU);
						break;
					default:
						break;
				}
			}
			// 手动提交offset
			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("同步基础服务平台数据失败 :" + e.getMessage(), e);
		}
	}

	/**
	 * 处理组织数据
	 * @param opt
	 * @param list
	 */
	private void handleOrg(String opt, List<PlatOrg> list){
		if(ADD.equals(opt)){
			//新增
			List<BladeDept> deptList = new ArrayList<>();
			for(PlatOrg org : list){
				BladeDept dept = new BladeDept();
				dept.setDeptName(org.getName());
				dept.setId(Long.parseLong(org.getCode()));
				dept.setParentId(Long.parseLong(org.getParentCode()));
				dept.setFullName(org.getFullName());
				dept.setSort(org.getSort().longValue());
				//位置平台中用不到type、brief，此处不处理
				dept.setCreateTime(new Date());
				dept.setUpdateTime(new Date());
				dept.setRemark("同步自基础服务平台");
				dept.setIsDeleted(0);
				dept.setSource(2);
				deptList.add(dept);
			}
			deptService.saveBatch(deptList);
		}else if(UPDATE.equals(opt)){
			//修改
			//1.按照id查询组织信息
			List<Long> deptIds = list.stream().map(item -> Long.parseLong(item.getCode())).collect(Collectors.toList());
			List<BladeDept> deptList = deptService.listByIds(deptIds);
			Map<Long,PlatOrg> platOrgMap = new HashMap<>();
			for(PlatOrg o : list){
				platOrgMap.put(Long.parseLong(o.getCode()), o);
			}
			//2.处理数据修改
			for(BladeDept d : deptList){
				PlatOrg o = platOrgMap.get(d.getId());
				if(o != null){
					if(!StringUtils.isEmpty(o.getName())){
						d.setDeptName(o.getName());
					}
					if(!StringUtils.isEmpty(o.getFullName())){
						d.setFullName(o.getFullName());
					}
					if(!StringUtils.isEmpty(o.getParentCode())){
						d.setParentId(Long.parseLong(o.getParentCode()));
					}
					if(o.getSort() != null){
						d.setSort(o.getSort().longValue());
					}
					d.setUpdateTime(new Date());
				}
			}
			deptService.updateBatchById(deptList);
		}else if(DELETE.equals(opt)){
			//删除（逻辑删除）
			List<Long> deptIds = list.stream().map(item -> Long.parseLong(item.getCode())).collect(Collectors.toList());
			List<BladeDept> deptList = new ArrayList<>();
			for(Long id : deptIds){
				BladeDept dept = new BladeDept();
				dept.setId(id);
				dept.setIsDeleted(1);
				dept.setUpdateTime(new Date());
				deptList.add(dept);
			}
			deptService.updateBatchById(deptList);
		}
	}

	/**
	 * 处理组织数据
	 * @param opt
	 * @param list
	 */
	private void handleUser(String opt, List<PlatUser> list){
		if(ADD.equals(opt)){
			//新增
			List<BladeUser> userList = new ArrayList<>();
			for(PlatUser u : list){
				BladeUser user = new BladeUser();
				user.setId(Long.parseLong(u.getAccount()));
				user.setAccount(u.getAccount());
				user.setName(u.getName());
				user.setRealName(u.getName());
				user.setDeptId(u.getOrgCode());
				user.setSex(u.getSex());
				user.setEmail(u.getEmail());
				user.setPhone(u.getPhone());
				user.setStatus(1);
				user.setIsDeleted(0);
				user.setSource(2);
				userList.add(user);
			}
			userService.saveBatch(userList);
		}else if(UPDATE.equals(opt)){
			//修改
			//1.按照id查询组织信息
			List<Long> userIds = list.stream().map(item -> Long.parseLong(item.getAccount())).collect(Collectors.toList());
			List<BladeUser> userList = userService.listByIds(userIds);
			Map<Long,PlatUser> platUserMap = new HashMap<>();
			for(PlatUser o : list){
				platUserMap.put(Long.parseLong(o.getAccount()), o);
			}
			//2.处理数据修改
			for(BladeUser d : userList){
				PlatUser o = platUserMap.get(d.getId());
				if(o != null){
					if(!StringUtils.isEmpty(o.getName())){
						d.setName(o.getName());
					}
					if(!StringUtils.isEmpty(o.getOrgCode())){
						d.setDeptId(o.getOrgCode());
					}
					if(o.getSex() != null){
						d.setSex(o.getSex());
					}
					if(!StringUtils.isEmpty(o.getEmail())){
						d.setEmail(o.getEmail());
					}
					if(!StringUtils.isEmpty(o.getPhone())){
						d.setPhone(o.getPhone());
					}
					d.setUpdateTime(new Date());
				}
			}
			userService.updateBatchById(userList);
		}else if(DELETE.equals(opt)){
			//删除（逻辑删除）
			List<Long> userIds = list.stream().map(item -> Long.parseLong(item.getAccount())).collect(Collectors.toList());
			List<BladeUser> userList = new ArrayList<>();
			for(Long id : userIds){
				BladeUser dept = new BladeUser();
				dept.setId(id);
				dept.setIsDeleted(1);
				dept.setUpdateTime(new Date());
				userList.add(dept);
			}
			userService.updateBatchById(userList);
		}
	}

}
