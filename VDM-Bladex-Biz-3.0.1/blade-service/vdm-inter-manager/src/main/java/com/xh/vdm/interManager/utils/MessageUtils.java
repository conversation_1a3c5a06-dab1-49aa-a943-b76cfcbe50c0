package com.xh.vdm.interManager.utils;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.hash.Hash;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.crypto.symmetric.SM4;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.interManager.core.InterManager;
import com.xh.vdm.interManager.dto.message.Body;
import com.xh.vdm.interManager.dto.message.Header;
import com.xh.vdm.interManager.dto.message.Message;
import com.xh.vdm.interManager.dto.message.Vehicle;
import com.xh.vdm.interManager.entity.InInterLog;
import com.xh.vdm.interManager.entity.InPosSystemObject;
import com.xh.vdm.interManager.entity.SmInterfaceManage;
import com.xh.vdm.interManager.entity.SmInterfaceSystem;
import com.xh.vdm.interManager.service.IInInterLogService;
import com.xh.vdm.interManager.service.IInPosSystemObjectService;
import com.xh.vdm.interManager.service.ISmInterfaceManageService;
import com.xh.vdm.interManager.service.ISmInterfaceSystemService;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jcajce.provider.digest.SM3;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtil;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.crypto.SecretKey;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 报文工具类
 */
@Slf4j
@Component
public class MessageUtils {

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private IInPosSystemObjectService objectService;

	@Resource
	private ISmInterfaceSystemService systemService;

	@Resource
	private MessageUtils messageUtils;

	@Resource
	private ISmInterfaceManageService interfaceManageService;

	@Resource
	private InterManager interManager;

	@Resource
	private IInInterLogService logService;

	/**
	 * 加密消息体
	 * @param bodyJson 消息体的json字符串
	 * @param toSystemId 根据该id查找接收方的公钥，用于加密数据
	 * @return
	 */
	public Message encryptData(Header header, String bodyJson, String toSystemId) throws Exception{
		//1.发送方随机产生一个SM4对称加密密钥并用此密钥对要发送的数据信息明文进
		//行加密（SM4算法），形成数据信息密文
		SecretKey sm4SecretKey = SmUtil.sm4().getSecretKey();
		String sm4Key = Base64.encode(sm4SecretKey.getEncoded());
		String systemId = header.getSystemId();
		log.info(sm4Key);
		String encryptedBody = SmUtil.sm4(sm4SecretKey.getEncoded()).encryptBase64(bodyJson.getBytes());
		//2.发送方用接收方的公钥对SM4密钥进行加密(SM2算法)，生成加密密钥串
		//2.1 获取接收方平台公钥
		String thirdPublicKey = "";
		Object sm2ThirdPublicKeyO = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_KEY_PUBLIC + toSystemId);
		SmInterfaceSystem system = null;
		if(sm2ThirdPublicKeyO == null || StringUtils.isEmpty(sm2ThirdPublicKeyO.toString())){
			//如果从缓存中没有获取到接收方公钥，则查询数据库
			system = systemService.getById(Long.parseLong(toSystemId));
			if(system == null){
				//如果未配置系统信息
				log.info("系统[{}]不存在", systemId);
				throw new Exception("该系统信息不存在");
			}
			if(StringUtils.isEmpty(system.getPublicKey())){
				//如果未配置公钥
				log.info("系统[{}]未配置公钥信息", systemId);
				throw new Exception("该系统未添加公钥信息");
			}
			thirdPublicKey = system.getPublicKey();
			//添加到redis中
			stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_KEY_PUBLIC + toSystemId, thirdPublicKey);
			stringRedisTemplate.expire(CommonConstant.CACHE_KEY_PUBLIC + toSystemId, CommonConstant.CACHE_KEY_TTL, TimeUnit.SECONDS);
		}else{
			thirdPublicKey = sm2ThirdPublicKeyO.toString();
		}
		//2.2 获取位置平台私钥
		String privateKey = "";
		Object sm2PrivateKeyO = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_KEY_PRIVATE);
		if(sm2PrivateKeyO == null || StringUtils.isEmpty(sm2PrivateKeyO.toString())){
			//如果从缓存中没有获取私钥，则查询数据库
			SmInterfaceSystem posSystem = systemService.getById(CommonConstant.DEFAULT_SYSTEM_ID);
			if(StringUtils.isEmpty(posSystem.getPrivateKey())){
				//如果未配置私钥
				log.info("位置平台未配置私钥信息");
				throw new Exception("位置平台未添加私钥信息");
			}
			privateKey = posSystem.getPrivateKey();
			//添加到缓存中
			stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_KEY_PRIVATE, privateKey);
			stringRedisTemplate.expire(CommonConstant.CACHE_KEY_PRIVATE, CommonConstant.CACHE_KEY_TTL, TimeUnit.SECONDS);
		}else{
			privateKey = sm2PrivateKeyO.toString();
		}
		//2.3 使用发送方公钥加密
		SM2 thirdSm2 = SmUtil.sm2(null, thirdPublicKey);
		String encryptedSm4Key = Base64.encode(thirdSm2.encrypt(sm4Key, KeyType.PublicKey));
		//3.发送方对数据信息密文计算信息摘要（SM3算法），然后对摘要用自己的私钥生
		//成数字签名(SM2算法)
		//3.1 生成摘要
		byte[] crc = SmUtil.sm3().digest(encryptedBody);
		//3.2 生成数字签名
		SM2 sm2 = SmUtil.sm2(privateKey, null);
		String sign = Base64.encode(sm2.sign(crc));

		//4.发送方将数字签名、加密密钥串附着在报文头中，数据信息密文附着在报文体中，
		//组成报文发送给接收方
		Message message = new Message();
		header.setCrc(sign);
		header.setEncryptedCode(encryptedSm4Key);
		Body body = new Body();
		body.setEncryptedContent(encryptedBody);
		message.setHeader(header);
		message.setBody(body);
		return message;
	}


	/**
	 * 加密消息体
	 * @param bodyJson 消息体的json字符串
	 * @param toSystemId 根据该id查找接收方的公钥，用于加密数据
	 * @return
	 */
	public Map<String,String> encryptData(String fromSystemId, String bodyJson, String toSystemId) throws Exception{
		//1.发送方随机产生一个SM4对称加密密钥并用此密钥对要发送的数据信息明文进
		//行加密（SM4算法），形成数据信息密文
		SecretKey sm4SecretKey = SmUtil.sm4().getSecretKey();
		String sm4Key = Base64.encode(sm4SecretKey.getEncoded());
		String systemId = fromSystemId;
		log.info(sm4Key);
		String encryptedBody = SmUtil.sm4(sm4SecretKey.getEncoded()).encryptBase64(bodyJson.getBytes());
		//2.发送方用接收方的公钥对SM4密钥进行加密(SM2算法)，生成加密密钥串
		//2.1 获取接收方平台公钥
		String thirdPublicKey = "";
		Object sm2ThirdPublicKeyO = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_KEY_PUBLIC + toSystemId);
		SmInterfaceSystem system = null;
		if(sm2ThirdPublicKeyO == null || StringUtils.isEmpty(sm2ThirdPublicKeyO.toString())){
			//如果从缓存中没有获取到接收方公钥，则查询数据库
			system = systemService.getById(Long.parseLong(toSystemId));
			if(system == null){
				//如果未配置系统信息
				log.info("系统[{}]不存在", systemId);
				throw new Exception("该系统信息不存在");
			}
			if(StringUtils.isEmpty(system.getPublicKey())){
				//如果未配置公钥
				log.info("系统[{}]未配置公钥信息", systemId);
				throw new Exception("该系统未添加公钥信息");
			}
			thirdPublicKey = system.getPublicKey();
			//添加到redis中
			stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_KEY_PUBLIC + toSystemId, thirdPublicKey);
			stringRedisTemplate.expire(CommonConstant.CACHE_KEY_PUBLIC + toSystemId, CommonConstant.CACHE_KEY_TTL, TimeUnit.SECONDS);
		}else{
			thirdPublicKey = sm2ThirdPublicKeyO.toString();
		}
		//2.2 获取发送方私钥
		String privateKey = "";
		Object sm2PrivateKeyO = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_KEY_PRIVATE);
		if(sm2PrivateKeyO == null || StringUtils.isEmpty(sm2PrivateKeyO.toString())){
			//如果从缓存中没有获取私钥，则查询数据库
			SmInterfaceSystem posSystem = systemService.getById(CommonConstant.DEFAULT_SYSTEM_ID);
			if(StringUtils.isEmpty(posSystem.getPrivateKey())){
				//如果未配置私钥
				log.info("位置平台未配置私钥信息");
				throw new Exception("位置平台未添加私钥信息");
			}
			privateKey = posSystem.getPrivateKey();
			//添加到缓存中
			stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_KEY_PRIVATE, privateKey);
			stringRedisTemplate.expire(CommonConstant.CACHE_KEY_PRIVATE, CommonConstant.CACHE_KEY_TTL, TimeUnit.SECONDS);
		}else{
			privateKey = sm2PrivateKeyO.toString();
		}
		//2.3 使用接收方公钥加密
		SM2 thirdSm2 = SmUtil.sm2(null, thirdPublicKey);
		String encryptedSm4Key = Base64.encode(thirdSm2.encrypt(sm4Key, KeyType.PublicKey));
		//3.发送方对数据信息密文计算信息摘要（SM3算法），然后对摘要用自己的私钥生
		//成数字签名(SM2算法)
		//3.1 生成摘要
		byte[] crc = SmUtil.sm3().digest(encryptedBody);
		log.info("crc is " + Base64.encode(crc));
		//3.2 生成数字签名
		SM2 sm2 = SmUtil.sm2(privateKey, null);
		String sign = Base64.encode(sm2.sign(crc));

		//4.发送方将数字签名、加密密钥串附着在报文头中，数据信息密文附着在报文体中，
		//组成报文发送给接收方
		Map<String,String> map = new HashMap<>();
		map.put("code", encryptedSm4Key);
		map.put("content",encryptedBody);
		map.put("sign",sign);
		return map;
	}


	/**
	 * 解密消息体，array
	 * @param message
	 * @return
	 */
	public <T> List<T> decryptBody(Message message, Class clazz) throws Exception{

		Header header = message.getHeader();
		if(header == null){
			throw new Exception("header不能为空");
		}
		String systemId = header.getSystemId();
		if(StringUtils.isEmpty(systemId)){
			throw new Exception("s1ystemId不能为空");
		}
		Body body = message.getBody();
		if(body == null){
			throw new Exception("body不能为空");
		}
		String encryptedBody = body.getEncryptedContent();
		//1.接收方收到报文后，对报文体中的数据信息密文计算信息摘要（SM3算法），用
		//发送方的公钥验证发送方数字签名(SM2算法)
		//1.2 获取发送方的公钥
		String thirdPublicKey = "";
		Object sm2ThirdPublicKeyO = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_KEY_PUBLIC + systemId);
		SmInterfaceSystem system = null;
		if(sm2ThirdPublicKeyO == null || StringUtils.isEmpty(sm2ThirdPublicKeyO.toString())){
			//如果从缓存中没有获取到公钥，则查询数据库
			system = systemService.getById(Long.parseLong(systemId));
			if(system == null){
				//如果未配置系统信息
				log.info("系统[{}]不存在", systemId);
				throw new Exception("该系统信息不存在");
			}
			if(StringUtils.isEmpty(system.getPublicKey())){
				//如果未配置第三方公钥
				log.info("系统[{}]未配置发送方公钥信息", systemId);
				throw new Exception("该系统未配置发送方公钥信息");
			}
			thirdPublicKey = system.getPublicKey();
			//添加到缓存中
			stringRedisTemplate.expire(CommonConstant.CACHE_KEY_PUBLIC + systemId, CommonConstant.CACHE_KEY_TTL, TimeUnit.SECONDS);
		}else{
			thirdPublicKey = sm2ThirdPublicKeyO.toString();
		}
		//1.3 验证签名
		String sign = header.getCrc();
		byte[] crcNow = SmUtil.sm3().digest(encryptedBody);
		log.info("crcNow is " + Base64.encode(crcNow));
		SM2 thirdSm2 = SmUtil.sm2(null, thirdPublicKey);
		boolean signResult = thirdSm2.verify(crcNow, Base64.decode(sign));
		if(!signResult){
			log.error("验签不通过，Message={}",JSON.toJSONString(message));
			throw new Exception("验签不通过");
		}
		//2.接收方用自己的私钥，解密报文头中的加密密钥串(SM2算法)，得到本次通信的SM4密钥，然后用SM4密钥对数据信息密文进行解密（SM4算法），得出数据信息明文
		//2.1 获取私钥
		String privateKey = "";
		Object sm2PrivateKeyO = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_KEY_PRIVATE);
		SmInterfaceSystem systemO = null;
		if(sm2PrivateKeyO == null || StringUtils.isEmpty(sm2PrivateKeyO.toString())){
			//如果从缓存中没有获取私钥，则查询数据库
			systemO = systemService.getById(CommonConstant.DEFAULT_SYSTEM_ID);
			if(StringUtils.isEmpty(systemO.getPrivateKey())){
				//如果未配置私钥
				log.info("位置平台未配置私钥信息");
				throw new Exception("位置平台未配置私钥信息");
			}
			privateKey = systemO.getPrivateKey();
			//添加到缓存中
			stringRedisTemplate.expire(CommonConstant.CACHE_KEY_PRIVATE, CommonConstant.CACHE_KEY_TTL, TimeUnit.SECONDS);
		}else{
			privateKey = new String(sm2PrivateKeyO.toString().getBytes());
		}

		//2.2 解密sm4密钥
		String encryptedCode = header.getEncryptedCode();
		SM2 sm2 = SmUtil.sm2(privateKey, null);
		String sm4Key = new String(sm2.decrypt(Base64.decode(encryptedCode)));

		//2.3 解密报文
		SM4 sm4 = SmUtil.sm4(Base64.decode(sm4Key));
		byte[] bytes = sm4.decrypt(Base64.decode(body.getEncryptedContent()));
		//类型转换
		String json = new String(bytes);
		List<T> list = JSON.parseArray(json, clazz);
		return list;
	}


	/**
	 * 解密，单个bean
	 * @param message
	 * @param clazz
	 * @return
	 * @throws Exception
	 */
	public Object decryptBodyBean(Message message, Class clazz) throws Exception{

		Header header = message.getHeader();
		if(header == null){
			throw new Exception("header不能为空");
		}
		String systemId = header.getSystemId();
		if(StringUtils.isEmpty(systemId)){
			throw new Exception("systemId不能为空");
		}
		Body body = message.getBody();
		if(body == null){
			throw new Exception("body不能为空");
		}
		String encryptedBody = body.getEncryptedContent();
		//1.接收方收到报文后，对报文体中的数据信息密文计算信息摘要（SM3算法），用
		//发送方的公钥验证发送方数字签名(SM2算法)
		//1.2 获取发送方的公钥
		String thirdPublicKey = "";
		Object sm2ThirdPublicKeyO = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_KEY_PUBLIC + systemId);
		SmInterfaceSystem system = null;
		if(sm2ThirdPublicKeyO == null || StringUtils.isEmpty(sm2ThirdPublicKeyO.toString())){
			//如果从缓存中没有获取到公钥，则查询数据库
			system = systemService.getById(Long.parseLong(systemId));
			if(system == null){
				//如果未配置系统信息
				log.info("系统[{}]不存在", systemId);
				throw new Exception("该系统信息不存在");
			}
			if(StringUtils.isEmpty(system.getPublicKey())){
				//如果未配置第三方公钥
				log.info("系统[{}]未配置发送方公钥信息", systemId);
				throw new Exception("该系统未配置发送方公钥信息");
			}
			thirdPublicKey = system.getPublicKey();
			//添加到缓存中
			stringRedisTemplate.expire(CommonConstant.CACHE_KEY_PUBLIC + systemId, CommonConstant.CACHE_KEY_TTL, TimeUnit.SECONDS);
		}else{
			thirdPublicKey = sm2ThirdPublicKeyO.toString();
		}
		//1.3 验证签名
		String sign = header.getCrc();
		byte[] crcNow = SmUtil.sm3().digest(encryptedBody);
		log.info("crcNow is " + Base64.encode(crcNow));
		SM2 thirdSm2 = SmUtil.sm2(null, thirdPublicKey);
		boolean signResult = thirdSm2.verify(crcNow, Base64.decode(sign));
		if(!signResult){
			log.error("验签不通过，Message={}",JSON.toJSONString(message));
			throw new Exception("验签不通过");
		}
		//2.接收方用自己的私钥，解密报文头中的加密密钥串(SM2算法)，得到本次通信的SM4密钥，然后用SM4密钥对数据信息密文进行解密（SM4算法），得出数据信息明文
		//2.1 获取私钥
		String privateKey = "";
		Object sm2PrivateKeyO = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_KEY_PRIVATE);
		SmInterfaceSystem systemO = null;
		if(sm2PrivateKeyO == null || StringUtils.isEmpty(sm2PrivateKeyO.toString())){
			//如果从缓存中没有获取私钥，则查询数据库
			systemO = systemService.getById(CommonConstant.DEFAULT_SYSTEM_ID);
			if(StringUtils.isEmpty(systemO.getPrivateKey())){
				//如果未配置私钥
				log.info("位置平台未配置私钥信息");
				throw new Exception("位置平台未配置私钥信息");
			}
			privateKey = systemO.getPrivateKey();
			//添加到缓存中
			stringRedisTemplate.expire(CommonConstant.CACHE_KEY_PRIVATE, CommonConstant.CACHE_KEY_TTL, TimeUnit.SECONDS);
		}else{
			privateKey = new String(sm2PrivateKeyO.toString().getBytes());
		}

		//2.2 解密sm4密钥
		String encryptedCode = header.getEncryptedCode();
		SM2 sm2 = SmUtil.sm2(privateKey, null);
		String sm4Key = new String(sm2.decrypt(Base64.decode(encryptedCode)));

		//2.3 解密报文
		SM4 sm4 = SmUtil.sm4(Base64.decode(sm4Key));
		byte[] bytes = sm4.decrypt(Base64.decode(body.getEncryptedContent()));
		//类型转换
		String json = new String(bytes);
		Object list = JSON.parseObject(json, clazz);
		return list;
	}

	/**
	 * 位置平台向外部平台推送报文
	 * @param objectList 报文列表，这里必须是一个list
	 * @param busiObjectId 原业务对象id，如果是批量操作，也只需要一个id即可。通过该id查找平台
	 * @param busiCode 报文中的业务编码
	 * @throws Exception
	 */
	public void sendMessage(Object objectList, Long busiObjectId, String busiCode) throws Exception{
		//1.查找车辆对应的平台
		List<InPosSystemObject> list = objectService.list(Wrappers.lambdaQuery(InPosSystemObject.class)
			.eq(InPosSystemObject::getObjectId, busiObjectId)
			.eq(InPosSystemObject::getObjectType,2)
		);
		//systemId去重
		Set<Long> systemIds = new HashSet<>();
		list.forEach(item -> {
			systemIds.add(item.getSystemId());
		});

		//2.查找平台对应的接口
		List<SmInterfaceSystem> systems = systemService.listByIds(systemIds);

		//3.调用接口推送数据
		//组装报文
		Header header = new Header();
		header.setSystemId(CommonConstant.POS_SYSTEM_ID);
		header.setServiceId(busiCode);
		List<InInterLog> logs = new ArrayList<>();
		for(SmInterfaceSystem s : systems){
			String code = s.getPlatHttpSendCode();
			header.setSerialNum((new Date()).getTime()+"");
			header.setReqTime(DateUtil.sdfHolder.get().format(new Date()).replace("-","").replace(" ",""));
			Message message = messageUtils.encryptData(header, JSON.toJSONString(objectList),s.getId()+"");
			Map<String,Object> map = BeanUtil.beanToMap(message);
			InInterLog interLog = new InInterLog();
			if(!org.springblade.common.utils.StringUtils.isEmpty(code)){
				long start = System.currentTimeMillis();
				R<Object> res = null;
				//一个平台推送失败，不应影响其他平台的推送
				try {
					res = interManager.call(code, map);
				}catch (Exception e){
					log.error("推送报文失败 ",e);
				}
				long end = System.currentTimeMillis();
				//查询接口基本信息
				String interJSONStr = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_POS_INTER_PREFIX + code);
				SmInterfaceManage inter = null;
				if(org.springblade.common.utils.StringUtils.isEmpty(interJSONStr)){
					inter = interfaceManageService.findInterfaceManageByInterCode(code);
					//有效期12个小时
					stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_POS_INTER_PREFIX + code, JSON.toJSONString(inter), 12, TimeUnit.HOURS);
				}else{
					inter = JSON.parseObject(interJSONStr, SmInterfaceManage.class);
				}

				interLog.setDirection(CommonConstant.INTER_DIRECTION_OUT);
				interLog.setInterCode(code);
				interLog.setInterName(inter.getInterNote());
				interLog.setSystemId(s.getId());
				interLog.setServiceId(0L);
				interLog.setBusiServiceId(busiCode);
				interLog.setInterType(inter.getInterType());
				interLog.setUrl(inter.getUrl());
				interLog.setParam(JSON.toJSONString(map));
				interLog.setResMessage(JSON.toJSONString(res));
				interLog.setCallTime(new Date());
				interLog.setCreateTime(new Date());
				interLog.setCallCost(end-start);
				interLog.setServerIp(inter.getIp());
				if(!res.isSuccess()){
					interLog.setResult(0);
				}else{
					interLog.setResult(1);
				}
			}
			logs.add(interLog);
		}
		logService.saveBatchInterLogAsync(logs);
	}


	/*public static void main(String[] args) {

		//用于加密存储私钥的SM4 key
		String sm4KeyForPrivateKey = "sGPlBdo9dSpjZKE5mZlvqg==";
		//第三方公私钥
		//公钥：MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEnEgphCWrAOJ/EPcfAGmBUBY5RCidpZnXBcjifIxBPqwtvsI/8qxuP347531ieA+zwTNzl+O+iCKay28mluKlbA==
		//私钥：MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgagG1QutuBzV2t8/MCU+xR1TAhCcMJKVCe1GvDQrWmRCgCgYIKoEcz1UBgi2hRANCAAScSCmEJasA4n8Q9x8AaYFQFjlEKJ2lmdcFyOJ8jEE+rC2+wj/yrG4/fjvnfWJ4D7PBM3OX476IIprLbyaW4qVs
		SM2 sm2 = SmUtil.sm2().initKeys();
		String publicKey = sm2.getPublicKeyBase64();
		log.info(publicKey);
		String privateKey = sm2.getPrivateKeyBase64();
		log.info(privateKey);
		//位置平台公私钥
		//公钥：MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6+wdFzk0mLD7Seg8eE+a/swipd2UgNIQKRxduhx42cMCbR7xZ17DthyUL8jyVxZF23i/FhIvBB9+2Yzj+QzMSA==
		//私钥：MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgHxSowCmSEZEvCUd54Jj4+l574BWWuN8Mxj2yXGJrG0WgCgYIKoEcz1UBgi2hRANCAATr7B0XOTSYsPtJ6Dx4T5r+zCKl3ZSA0hApHF26HHjZwwJtHvFnXsO2HJQvyPJXFkXbeL8WEi8EH37ZjOP5DMxI

		String posPrivateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgHxSowCmSEZEvCUd54Jj4+l574BWWuN8Mxj2yXGJrG0WgCgYIKoEcz1UBgi2hRANCAATr7B0XOTSYsPtJ6Dx4T5r+zCKl3ZSA0hApHF26HHjZwwJtHvFnXsO2HJQvyPJXFkXbeL8WEi8EH37ZjOP5DMxI";
		String encPrivate = Base64.encode(SmUtil.sm4(Base64.decode(sm4KeyForPrivateKey)).encrypt(posPrivateKey));
		log.info("encPrivate is ");
		log.info(encPrivate);
	}*/

	/*public static void main(String[] args) {
		String crc = "uaEMU5dYNuy2RmprNhYKT8qwbdBFSKgStiR6iMwYGzE=";
		String publicKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6+wdFzk0mLD7Seg8eE+a/swipd2UgNIQKRxduhx42cMCbR7xZ17DthyUL8jyVxZF23i/FhIvBB9+2Yzj+QzMSA==";
		String privateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgHxSowCmSEZEvCUd54Jj4+l574BWWuN8Mxj2yXGJrG0WgCgYIKoEcz1UBgi2hRANCAATr7B0XOTSYsPtJ6Dx4T5r+zCKl3ZSA0hApHF26HHjZwwJtHvFnXsO2HJQvyPJXFkXbeL8WEi8EH37ZjOP5DMxI";
		//加密
		SM2 sm2 = SmUtil.sm2(privateKey, publicKey);
		String eStr = Base64.encode(sm2.encrypt(crc, KeyType.PublicKey));
		//解密
		byte[] res = sm2.decrypt(Base64.decode(eStr));
		String resStr = new String(res);
		log.info(resStr);
	}*/

	public static void main1(String[] args) {
		//位置平台密钥
		/*String publicKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAE6+wdFzk0mLD7Seg8eE+a/swipd2UgNIQKRxduhx42cMCbR7xZ17DthyUL8jyVxZF23i/FhIvBB9+2Yzj+QzMSA==";
		String privateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgHxSowCmSEZEvCUd54Jj4+l574BWWuN8Mxj2yXGJrG0WgCgYIKoEcz1UBgi2hRANCAATr7B0XOTSYsPtJ6Dx4T5r+zCKl3ZSA0hApHF26HHjZwwJtHvFnXsO2HJQvyPJXFkXbeL8WEi8EH37ZjOP5DMxI";
		SM2 sm2 = SmUtil.sm2(privateKey, publicKey);
		Map<String,Object> map = new HashMap<>();
		map.put("password","cabdea416f8c33158c2cde6b7643938f");
		map.put("timestamp",1721962311717L);
		String data = JSON.toJSONString(map);
		String dataE1 = Base64.encode(sm2.encrypt(data.getBytes(), KeyType.PublicKey));
		log.info("dataE1 is " + dataE1);*/


		//公务车平台密钥
		/*String publicKey = "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEnEgphCWrAOJ/EPcfAGmBUBY5RCidpZnXBcjifIxBPqwtvsI/8qxuP347531ieA+zwTNzl+O+iCKay28mluKlbA==";
		String privateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgagG1QutuBzV2t8/MCU+xR1TAhCcMJKVCe1GvDQrWmRCgCgYIKoEcz1UBgi2hRANCAAScSCmEJasA4n8Q9x8AaYFQFjlEKJ2lmdcFyOJ8jEE+rC2+wj/yrG4/fjvnfWJ4D7PBM3OX476IIprLbyaW4qVs";
		String data = "wk+hZm+ePCfy8r9P4Vx5UL6m3Tchy9/bAwXZtBPKD0oXQ7b3tdomkuogEpxINj/bgiHMe41uTTntIwTYDw3Jf49HU0y/BXc76eg1xgts+oQl04bIqVCpwXcjwn/IQmpwddzF0T751xSVIRjoq2zPapvhKV4j035eKxvdU7k5M892vyiNHN3ckmZJv6jtsgkF";
		SM2 sm2 = SmUtil.sm2(privateKey, publicKey);
		String key = Base64.encode(sm2.decrypt(Base64.decode(data),KeyType.PrivateKey));
		log.info("key is {}", key);*/
		/*String dataE2 = Base64.encode(sm2.encrypt(data.getBytes(), KeyType.PublicKey));
		log.info("dataE2 is " + dataE2);
		String dataO1 = new String(sm2.decrypt(Base64.decode(dataE1), KeyType.PrivateKey));
		log.info("dataO1 is " + dataO1);
		String dataO2 = new String(sm2.decrypt(Base64.decode(dataE2), KeyType.PrivateKey));
		log.info("dataO2 is " + dataO2);*/

		//公务车平台查询位置平台公钥--解密报文
		String encData = "hxPEgtajbcJhWrRlx/SLbxuIuJGhya8I2kqaCQMxery1XrzWBgtnQjZHAOKspOBJ4g78LBpFY+cKdhQwM4TDaA5XAZEoSxNNqN/wpM0gOSLj54UmbGxuiOfmtRWRPeaht+seC7RWQNVH+dvNdqBzHxW94ge3FZItmAMr21L0518tLgg+dZDDZlHSfeLh37dI";
		String encCode = "BC1Kfn0yD2x1oBCU5fhbKnrQiZpmgYWWV5LIn2aY8z6k0wtleJQ/o6lbqrJ7oCuLr0fVMQq2nwopHG3tASFKw9AoY3+zH1UCka+unjMWmC0GyuElD0zY71IKhYWNUDDYZDsGseEVECPYQW8xrMTh/ydZKQs3nxwF1g==";
		//公务车私钥
		String privateKey = "MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQgagG1QutuBzV2t8/MCU+xR1TAhCcMJKVCe1GvDQrWmRCgCgYIKoEcz1UBgi2hRANCAAScSCmEJasA4n8Q9x8AaYFQFjlEKJ2lmdcFyOJ8jEE+rC2+wj/yrG4/fjvnfWJ4D7PBM3OX476IIprLbyaW4qVs";
		SM2 sm2 = SmUtil.sm2(privateKey, null);
		byte[] sm4Bytes = sm2.decrypt(Base64.decode(encCode));
		String sm4Key = new String(sm4Bytes);
		SM4 sm4 = SmUtil.sm4(Base64.decode(sm4Key));
		log.info("code is " + sm4Key);
		byte[] bytes = sm4.decrypt(Base64.decode(encData));
		String data = new String(bytes);
		log.info("data is "+ data);
	}

}
