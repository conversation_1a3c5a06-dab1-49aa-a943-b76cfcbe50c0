package com.xh.vdm.interManager.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import com.xh.vdm.biapi.entity.BdmVirtualTarget;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmAbstractDevice)表服务接口
 */
public interface IBdmAbstractDeviceService extends IService<BdmAbstractDevice> {

	/**
	 * 通过主键逻辑删除
	 * @param ids
	 */
	void deleteByIds(Long[] ids);

	/**
	 * 批量插入
	 * @param abstractDeviceList
	 */
	void insertBatch(List<BdmAbstractDevice> abstractDeviceList);

	/**
	 * 新增单个
	 * @param abstractDevice
	 */
	void saveDevice(BdmAbstractDevice abstractDevice);

	/**
	 * 解除绑定
	 * @param id
	 * @param targetType
	 */
    void unbinding(Long id, Integer targetType);


	/**
	 * 删除目标解绑抽象终端表
	 * @param ids
	 * @param targetType
	 */
	void deleteByTargetIds(Long[] ids, Integer targetType);


	void updateDept(Long id, Integer targetType, Long deptId);

	void updateBatchByUniqueId(BdmVirtualTarget virtualTarget);
	List<BdmAbstractDevice> getListByUserRole(DataAuthCE ceDataAuth);
	BdmAbstractDevice getBadByUniqueId(String uniqueId);
}
