package com.xh.vdm.interManager.mqtt;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.interManager.entity.MqttPushLog;
import com.xh.vdm.interManager.props.MqttProperties;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@Slf4j
public class MqttSendClient {

	@Autowired
	private MqttSendCallBack mqttSendCallBack;

	@Autowired
	private MqttProperties mqttProperties;

	public static MqttClient client;

	@Autowired
	private KafkaTemplate<String,String> kafkaTemplate;

	private static void setClient(MqttClient client) {
		MqttSendClient.client = client;
	}

	public MqttClient connect() {
		if(client == null){
			MqttClient clientLocal = null;
			try {
				String uuid = UUID.randomUUID().toString().replaceAll("-", "");
				clientLocal = new MqttClient(mqttProperties.getHostUrl(), uuid, new MemoryPersistence());
				MqttConnectOptions options = new MqttConnectOptions();
				options.setUserName(mqttProperties.getUsername());
				options.setPassword(mqttProperties.getPassword().toCharArray());
				options.setConnectionTimeout(mqttProperties.getTimeout());
				options.setKeepAliveInterval(mqttProperties.getKeepAlive());
				options.setCleanSession(true);
				//设置自动重连
				options.setAutomaticReconnect(true);
				// 设置回调
				clientLocal.setCallback(mqttSendCallBack);
				clientLocal.connect(options);
				//全局使用一个连接
				client = clientLocal;
				MqttSendClient.setClient(clientLocal);
			} catch (Exception e) {
				log.error("MqttSendClient connect error,message:{}", e.getMessage());
				e.printStackTrace();
			}
		}
		return client;
	}

	/**
	 * 重新连接
	 */
	public void reconnection() {
		try {
			client.connect();
		} catch (MqttException e) {
			log.error("MqttSendClient reconnection error,message:{}", e.getMessage());
			e.printStackTrace();
		}
	}

	/**
	 * 发布消息
	 *
	 * @param retained 是否保留
	 * @param topic 主题，格式： server:${env}:report:${topic}
	 * @param content 消息内容
	 */
	public void publish(boolean retained, String topic, String content) {
		MqttMessage message = new MqttMessage();
		message.setQos(mqttProperties.getQos());
		message.setRetained(retained);
		message.setPayload(content.getBytes());
		MqttDeliveryToken token;
		MqttClient mqttClient = connect();
		int res = 1;
		String resMessage = "推送成功";
		try {
			mqttClient.publish(topic, message);
		} catch (MqttException e) {
			log.error("MqttSendClient publish error,message:{}", e.getMessage());
			e.printStackTrace();
			res = 0;
			resMessage = "推送失败："+e.getMessage();
		} finally {
			MqttPushLog mqttLog = new MqttPushLog();
			Long id = IdUtil.getSnowflakeNextId();
			mqttLog.setId(id);
			mqttLog.setRes(res);
			mqttLog.setResMessage(resMessage);
			String busiCode = "";
			String systemId = "";
			if(!StringUtils.isEmpty(topic) && topic.contains("/")){
				String[] array = topic.split("/");
				busiCode = array[1];
				systemId = array[2];
			}
			mqttLog.setBusiCode(busiCode);
			mqttLog.setSystemId(StringUtils.isEmpty(systemId)?0L:Long.parseLong(systemId));
			mqttLog.setTopic(topic);
			mqttLog.setContent(content);
			mqttLog.setSendTime(System.currentTimeMillis()/1000);
			saveMqttLogToKafka(mqttLog);
			//此处不可以关掉连接，每次创建连接开销很大，需要2s左右
			//disconnect(mqttClient);
			//close(mqttClient);
		}
	}

	/**
	 * 保存mqtt推送日志到kafka中，后续消费者消费入kudu
	 * @param mqttLog
	 */
	private void saveMqttLogToKafka(MqttPushLog mqttLog){
		try{
			kafkaTemplate.send(CommonConstant.TOPIC_MQTT_PUSH_LOG, JSON.toJSONString(mqttLog));
			log.debug("保存mqtt推送日志数据到kafka成功，数据为", JSON.toJSONString(mqttLog));
		}catch (Exception e){
			log.error("保存mqtt推送日志到kafka中失败",e);
		}
	}


	/**
	 * 关闭连接
	 *
	 * @param mqttClient
	 */
	public static void disconnect(MqttClient mqttClient) {
		try {
			if (mqttClient != null)
				mqttClient.disconnect();
		} catch (MqttException e) {
			log.error("MqttSendClient disconnect error,message:{}", e.getMessage());
			e.printStackTrace();
		}
	}

	/**
	 * 释放资源
	 *
	 * @param mqttClient
	 */
	public static void close(MqttClient mqttClient) {
		try {
			if (mqttClient != null)
				mqttClient.close();
		} catch (MqttException e) {
			log.error("MqttSendClient close error,message:{}", e.getMessage());
			e.printStackTrace();
		}
	}
}
