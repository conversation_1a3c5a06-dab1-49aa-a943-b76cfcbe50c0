package com.xh.vdm.interManager.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.interManager.constant.CommonConstant;
import com.xh.vdm.interManager.entity.SmInterfacePush;
import com.xh.vdm.interManager.service.ISmInterfacePushService;
import com.xh.vdm.interManager.utils.CommonUtil;
import com.xh.vdm.interManager.utils.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@RestController
@RequestMapping("/interManager/sminterfacepush")
@Slf4j
public class SmInterfacePushController {

	@Resource
	private ISmInterfacePushService smInterfacePushService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<SmInterfacePush> detail(SmInterfacePush smInterfacePush) {
		SmInterfacePush detail = smInterfacePushService.getOne(Wrappers.lambdaQuery(smInterfacePush));
		return R.data(detail);
	}

	/**
	 * 分页 接口管理表
	 */
	@GetMapping("/list")
	public R<IPage<SmInterfacePush>> list(SmInterfacePush smInterfacePush, Page<SmInterfacePush> page) {
		IPage<SmInterfacePush> pages = smInterfacePushService.page(page, Wrappers.lambdaQuery(smInterfacePush).eq(SmInterfacePush::getState,CommonConstant.STATE_U));
		return R.data(pages);
	}


	/**
	 * 新增 接口管理表
	 */
	@PostMapping("/save")
	public R save( @RequestBody SmInterfacePush smInterfacePush) {
		return R.status(smInterfacePushService.save(smInterfacePush));
	}

	/**
	 * 修改 接口管理表
	 */
	@PostMapping("/update")
	public R update( @RequestBody SmInterfacePush smInterfacePush) {
		return R.status(smInterfacePushService.updateById(smInterfacePush));
	}

	/**
	 * 新增或修改 接口管理表
	 */
	@PostMapping("/submit")
	public R submit(@RequestBody SmInterfacePush smInterfacePush) {
		if(StringUtils.isEmpty(smInterfacePush.getId())){
			//如果是新增
			smInterfacePush.setState(CommonConstant.STATE_U);
			smInterfacePush.setCreateTime(new Date());
		}else{
			smInterfacePush.setUpdateTime(new Date());
		}
		return R.status(smInterfacePushService.saveOrUpdate(smInterfacePush));
	}


	/**
	 * 删除 接口管理表
	 */
	@PostMapping("/remove")
	public R remove( @RequestParam String ids) {
		//逻辑删除
		try {
			smInterfacePushService.update(Wrappers.lambdaUpdate(SmInterfacePush.class).in(SmInterfacePush::getId, CommonUtil.toLongList(ids)).set(SmInterfacePush::getState, CommonConstant.STATE_E));
		}catch (Exception e){
			log.error("删除系统数据出错",e);
			return R.fail("删除系统数据出错");
		}
		return R.success("操作成功");
		//return R.status(smInterfaceSystemService.removeByIds(CommonUtil.toLongList(ids)));
	}

}
