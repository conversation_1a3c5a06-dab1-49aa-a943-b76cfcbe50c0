<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.InPosInterfaceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inPosInterfaceResultMap" type="com.xh.vdm.interManager.entity.InPosInterface">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="url" property="url"/>
        <result column="service_id" property="serviceId"/>
        <result column="protocol_type" property="protocolType"/>
        <result column="request_type" property="requestType"/>
        <result column="is_del" property="isDel"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <select id="selectInPosInterfacePage" resultMap="inPosInterfaceResultMap">
        select * from in_pos_interface where is_deleted = 0
    </select>


    <select id="getInterfaceByUserId" resultType="com.xh.vdm.interManager.entity.InPosInterface">
        select b.*
        from in_pos_user_interface a, in_pos_interface b
        where a.interface_id = b.id and b.is_del = 0
          and a.user_id = #{userId}
    </select>

    <select id="getInterfacesUnBind" resultType="com.xh.vdm.interManager.vo.response.InterfaceResponse">
        select
        a.id,
        a.code,
        a.name,
        a.interface_desc,
        a.url ,
        a.service_id,
        c.name as service_name,
        a.protocol_type,
        a.request_type
        from
        in_pos_interface a
        left join in_pos_service c on a.service_id = c.id and c.is_del  = 0
        where
        1 = 1
        and a.is_del = 0
        and a.id not in (
        select
        interface_id
        from
        in_pos_user_interface
        where
        user_id = #{request.userId} )
        <if test="request.code != null and request.code != ''">
            and a.code like #{request.code}
        </if>
        <if test="request.name != null and request.name != '' ">
            and a.name like #{request.name}
        </if>
        <if test="request.serviceId != null and request.serviceId != ''">
            and a.service_id = #{request.serviceId,jdbcType=BIGINT}
        </if>
    </select>

    <select id="getInterfacesBind" resultType="com.xh.vdm.interManager.vo.response.InterfaceResponse">
        select a.id, a.code, a.name, a.interface_desc , a.url, a.service_id , c.name service_name, a.protocol_type , a.request_type
        from in_pos_interface a, in_pos_user_interface b, in_pos_service c
        where a.id = b.interface_id and a.service_id = c.id
        and b.user_id = #{request.userId}
        and a.is_del = 0 and c.is_del = 0
        <if test="request.code != null and request.code != ''">
            and a.code like #{request.code}
        </if>
        <if test="request.name != null and request.name != '' ">
            and a.name like #{request.name}
        </if>
        <if test="request.serviceId != null and request.serviceId != ''">
            and a.service_id = #{request.serviceId,jdbcType=BIGINT}
        </if>
    </select>

</mapper>
