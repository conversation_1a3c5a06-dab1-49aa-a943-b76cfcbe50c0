package com.xh.vdm.interManager.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.*;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;

/**
 * @Description: 终端状态 kafka 配置
 * @Author: zhouxw
 * @Date: 2023/7/10 20:10
 */
@Configuration
public class TerminalStatusKafkaConfig {



	/**
	 * Producer 工厂配置
	 */
	@Bean
	public ProducerFactory<String, String> producerFactory() {
		return new DefaultKafkaProducerFactory<>(firstKafkaProperties().buildProducerProperties());
	}


    @ConfigurationProperties(prefix = "spring.terminal-status-kafka")
    @Bean("terminalStatusKafkaProperties")
    public KafkaProperties firstKafkaProperties() {
        return new KafkaProperties();
    }

    //此处最好手动指定数据源factory的bean名称，在消费者端会用到
    @Bean("terminalStatusKafkaListenerContainerFactory")
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>>
    firstKafkaListenerContainerFactory(@Autowired @Qualifier("terminalStatusKafkaProperties") KafkaProperties firstKafkaProperties) {
        ConcurrentKafkaListenerContainerFactory<Integer, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(firstConsumerFactory(firstKafkaProperties));
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        factory.getContainerProperties().setMissingTopicsFatal(false);
		factory.getContainerProperties().setGroupId(firstKafkaProperties.getConsumer().getGroupId());
        return factory;
    }

    private ConsumerFactory<? super Integer, ? super String> firstConsumerFactory(KafkaProperties firstKafkaProperties) {
        return new DefaultKafkaConsumerFactory<>(firstKafkaProperties.buildConsumerProperties());
    }


}
