<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.TerminalMapper">



    <select id="getDeviceCountByDeviceNum" resultType="long">
        select (a1.num + a2.num + a3.num + a4.num + a5.num) num
        from
            (select count(*) num from bdm_rnss_device where deleted = 0 and device_num = #{deviceNum,jdbcType=VARCHAR}) a1,
            (select count(*) num from bdm_rdss_device where deleted = 0  and device_num = #{deviceNum,jdbcType=VARCHAR}) a2,
            (select count(*) num from bdm_monit_device where deleted  = 0  and device_num = #{deviceNum,jdbcType=VARCHAR}) a3,
            (select count(*) num from bdm_wearable_device where deleted = 0 and device_num = #{deviceNum,jdbcType=VARCHAR}) a4,
            (select count(*) num from bdm_pnt_device where deleted = 0 and device_num = #{deviceNum,jdbcType=VARCHAR}) a5;
    </select>


    <select id="getDeviceCountByImei" resultType="long">
        select (a1.num + a2.num + a3.num + a4.num + a5.num) num
        from
            (select count(*) num from bdm_rnss_device where deleted = 0 and imei = #{imei,jdbcType=VARCHAR}) a1,
            (select count(*) num from bdm_rdss_device where deleted = 0  and imei = #{imei,jdbcType=VARCHAR}) a2,
            (select count(*) num from bdm_monit_device where deleted  = 0  and imei = #{imei,jdbcType=VARCHAR}) a3,
            (select count(*) num from bdm_wearable_device where deleted = 0 and imei = #{imei,jdbcType=VARCHAR}) a4,
            (select count(*) num from bdm_pnt_device where deleted = 0 and imei = #{imei,jdbcType=VARCHAR}) a5;
    </select>

    <select id="getDeviceCountByUniqueId" resultType="long">
        select (a1.num + a2.num + a3.num + a4.num + a5.num) num
        from
            (select count(*) num from bdm_rnss_device where deleted = 0 and unique_id = #{uniqueId,jdbcType=VARCHAR}) a1,
            (select count(*) num from bdm_rdss_device where deleted = 0  and unique_id = #{uniqueId,jdbcType=VARCHAR}) a2,
            (select count(*) num from bdm_monit_device where deleted  = 0  and unique_id = #{uniqueId,jdbcType=VARCHAR}) a3,
            (select count(*) num from bdm_wearable_device where deleted = 0 and unique_id = #{uniqueId,jdbcType=VARCHAR}) a4,
            (select count(*) num from bdm_pnt_device where deleted = 0 and unique_id = #{uniqueId,jdbcType=VARCHAR}) a5;
    </select>


    <select id="getIdByUniqueId" parameterType="string" resultType="java.lang.Long">
        select id
        from (
             select id from bdm_rnss_device where deleted = 0 and unique_id = #{uniqueId,jdbcType=VARCHAR}
             union
             select id from bdm_rdss_device where deleted = 0 and unique_id = #{uniqueId,jdbcType=VARCHAR}
             union
            select id from bdm_monit_device where deleted = 0 and unique_id = #{uniqueId,jdbcType=VARCHAR}
            union
            select id from bdm_wearable_device where deleted = 0 and unique_id = #{uniqueId,jdbcType=VARCHAR}
            union
            select id from bdm_pnt_device where deleted = 0 and unique_id = #{uniqueId,jdbcType=VARCHAR}
             ) a
    </select>
</mapper>
