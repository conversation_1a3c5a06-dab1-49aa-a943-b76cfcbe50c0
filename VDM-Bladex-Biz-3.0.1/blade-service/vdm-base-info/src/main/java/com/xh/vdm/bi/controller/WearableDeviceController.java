package com.xh.vdm.bi.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.enums.BdmTypeEnum;
import com.xh.vdm.bi.service.IBdmDeviceStatusService;
import com.xh.vdm.bi.service.IotCardService;
import com.xh.vdm.bi.service.WearableDeviceService;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.utils.DictUtil;
import com.xh.vdm.bi.vo.request.WearableDeviceRequest;
import com.xh.vdm.bi.vo.response.WearableDeviceResponse;
import com.xh.vdm.biapi.entity.BdmDeviceStatus;
import com.xh.vdm.biapi.entity.BdmIotCard;
import com.xh.vdm.biapi.entity.BdmWearableDevice;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springblade.common.annotation.Log;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.minio.MinioService;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.EasyExcelUtils;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.entity.DataAuthCE;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 北斗穿戴实体管理
 */
@RestController
@RequestMapping("/device/wearableDevice")
@Slf4j
@Validated
public class WearableDeviceController {

	@Resource
	private WearableDeviceService wearableDeviceService;
	@Resource
	private IBdmDeviceStatusService deviceStatusService;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private IotCardService iotCardService;
	@Resource
	private IDictBizClient dictBizClient;
	@Resource
	private MinioService minioService;
	@Resource
	private CETokenUtil ceTokenUtil;
	@Resource
	private DictUtil dictUtil;

	/**
	 * 分页查询
	 *
	 * @param request 筛选条件
	 * @return 查询结果
	 */
	@PostMapping("/list")
	public R<IPage<WearableDeviceResponse>> queryByPage(@RequestBody WearableDeviceRequest request, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		IPage<WearableDeviceResponse> page = this.wearableDeviceService.queryByPage(request, ceDataAuth);
		enrichDataWithDict(page.getRecords());
		return R.data(page);
	}

	/**
	 * 新增数据
	 *
	 * @param request 实体
	 * @return 新增结果
	 */
	@Log(menu = "穿戴式终端管理", operation = Operation.INSERT, objectType = ObjectType.WEAR)
	@PostMapping("/save")
	public R save(@RequestBody WearableDeviceRequest request, BladeUser user) {
		//判断序列号是否已经存在
		QueryWrapper<BdmWearableDevice> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("unique_id", request.getUniqueId());
		queryWrapper.eq("deleted", 0);
		BdmWearableDevice wearableDevice = wearableDeviceService.getOne(queryWrapper);
		if (wearableDevice != null) {
			return R.fail("序列号已存在！");
		}
		//判断物联网卡是否存在
		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			QueryWrapper<BdmIotCard> wrapper = new QueryWrapper<>();
			wrapper.eq("number", request.getNumbers());
			wrapper.eq("deleted", 0);
			BdmIotCard card = iotCardService.getOne(wrapper);
			if (card == null) {
				return R.fail("物联网卡不存在！");
			}

			wrapper.ne("device_id", 0);
			List<BdmIotCard> resultList = iotCardService.getBaseMapper().selectList(wrapper);
			if (!resultList.isEmpty()) {
				return R.fail("物联网卡已使用！");
			}
		}

		BdmWearableDevice result = this.wearableDeviceService.insert(request);
		if (result == null) {
			return R.fail(ResultCode.FAILURE, "终端赋码相关信息输入错误！");
		}
		return R.data(ResultCode.SUCCESS.getCode(), result.getId().toString(), "新增成功");
	}

	/**
	 * 编辑数据
	 *
	 * @param request 实体
	 * @return 编辑结果
	 */
	@Log(menu = "穿戴式终端管理", operation = Operation.UPDATE, objectType = ObjectType.WEAR)
	@PostMapping("/update")
	public R update(@RequestBody WearableDeviceRequest request, BladeUser user) {
		// 对于不是新终端的序列号要校验唯一
		if (request.getSpecificity() != null && request.getSpecificity() != BdmTypeEnum.NEW.getValue()) {
			QueryWrapper<BdmWearableDevice> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("unique_id", request.getUniqueId())
				.ne("id", request.getId())
				.eq("deleted", 0);
			BdmWearableDevice device = wearableDeviceService.getOne(queryWrapper);
			if(device != null){
				return R.fail("序列号已存在！");
			}
		}

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			QueryWrapper<BdmIotCard> wrapper = new QueryWrapper<>();
			wrapper.eq("number", request.getNumbers());
			wrapper.eq("deleted", 0);
			BdmIotCard card = iotCardService.getOne(wrapper);
			if (card == null) {
				return R.fail("物联网卡不存在！");
			}
			wrapper.ne("device_id", 0)
				.and(w -> w.ne("device_type", request.getDeviceType())
					.ne("device_id", request.getId()));
			List<BdmIotCard> resultList = iotCardService.getBaseMapper().selectList(wrapper);
			if (!resultList.isEmpty()) {
				return R.fail("物联网卡已使用！");
			}
		}

		BdmWearableDevice wearableDeviceInDB = this.wearableDeviceService.getBaseMapper().selectById(request.getId());

		List<String> list = iotCardService.getBaseMapper()
			.selectList(new QueryWrapper<BdmIotCard>()
				.eq("deleted", 0)
				.eq("device_id", request.getId())
				.eq("device_type", request.getDeviceType()))
			.stream()
			.map(BdmIotCard::getNumber)
			.collect(Collectors.toList());
		wearableDeviceInDB.setNumbers(list.toString());

		BdmWearableDevice bdmWearableDevice = new BdmWearableDevice();
		BeanUtils.copyProperties(request, bdmWearableDevice);
		bdmWearableDevice.setNumbers(request.getNumbers());
		BdmWearableDevice wearableDevice = this.wearableDeviceService.update(request);
		if (wearableDevice == null) {
			return R.fail("编辑失败");
		}
		String compareResult = new CompareUtils<BdmWearableDevice>().compare(wearableDeviceInDB, bdmWearableDevice);
		return R.data(ResultCode.SUCCESS.getCode(), compareResult, "编辑成功");
	}

	/**
	 * 删除数据
	 *
	 * @param id 主键
	 * @return 删除是否成功
	 */
	@GetMapping("/delete/{id}")
	public R<T> deleteById(@PathVariable Long id) {
		QueryWrapper<BdmDeviceStatus> wrapper = new QueryWrapper<>();
		wrapper.eq("device_id", id);
		deviceStatusService.remove(wrapper);
		return R.status(this.wearableDeviceService.deleteById(id));
	}

	/**
	 * 导出数据
	 */
	@PostMapping("/export")
	public R<String> export(@RequestBody WearableDeviceRequest request, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		try {
			request.setCurrent(1);
			request.setSize(Integer.MAX_VALUE);
			IPage<WearableDeviceResponse> list = this.wearableDeviceService.queryByPage(request, ceDataAuth);
			Map<String, String> map = new HashMap<>();
			if (list.getRecords().isEmpty()) {
				return R.fail("没有数据可导出");
			}
			//从字典中获取数据
			enrichDataWithDict(list.getRecords());

			for (WearableDeviceResponse response : list.getRecords()) {
				String key = BaseInfoConstants.WEAR_DEVICE_TYPE + "-" + response.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("uniqueId", response.getUniqueId());
				innerMap.put("deviceNum", response.getDeviceNum());
				innerMap.put("category", response.getCategory());
				innerMap.put("deviceType", response.getDeviceType());
				innerMap.put("deptId", response.getDeptId());
				innerMap.put("iotProtocol", response.getIotProtocol());
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			}
			// todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

			String menu = "北斗穿戴终端管理";
			try {
				ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
				EasyExcelUtils.exportExcelToStream(
					menu,
					outputStream,
					(List) list.getRecords(),
					request.getHeadNameList(),
					request.getColumnNameList(),
					WearableDeviceResponse.class
				);
				ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
				String minioFileUrl = minioService.uploadFile(menu, inputStream, outputStream.size());
				if (minioFileUrl != null) {
					return R.data(minioFileUrl);
				} else {
					return R.fail("文件上传失败");
				}
			} catch (Exception e) {
				log.info("文件上传失败 " + e.getMessage());
				return R.fail("文件上传失败");
			}
		} catch (Exception e) {
			log.error("导出失败", e);
			return R.fail("导出失败");
		}
	}

	/**
	 * 导入数据
	 */
	@Log(menu = "穿戴式终端管理", operation = Operation.IMPORT, objectType = ObjectType.WEAR)
	@PostMapping("/importExcel")
	public R importExcel(@RequestBody List<WearableDeviceRequest> list, BladeUser user) {
		if (list.isEmpty()) {
			return R.fail("数据为空");
		}
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		List<WearableDeviceRequest> result = this.wearableDeviceService.importExcel(list, user.getUserId());
		if (result.isEmpty()) {
			Map<Long, StringBuilder> deptIdMap = new HashMap<>();
			for (WearableDeviceRequest request : list) {
				Long deptId = request.getDeptId();
				Long id = request.getId();
				if (deptIdMap.containsKey(deptId)) {
					deptIdMap.get(deptId).append("、").append(id);
				} else {
					deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
				}
			}
			return R.data(ResultCode.SUCCESS.getCode(), deptIdMap, "导入成功");
		}
		String filePath = "";
		String menu = "穿戴终端管理";
		try {
			filePath = minioService.exportToMinIO(menu, result, WearableDeviceRequest.class);
		} catch (IOException e) {
			log.error("导出错误数据失败 " + e.getMessage());
		}
		List<WearableDeviceRequest> filteredList = list.stream()
			.filter(item -> !result.contains(item))
			.collect(Collectors.toList());
		Map<Long, StringBuilder> deptIdMap = new HashMap<>();
		if (!filteredList.isEmpty()) {
			for (WearableDeviceRequest request : filteredList) {
				Long deptId = request.getDeptId();
				Long id = request.getId();
				if (deptIdMap.containsKey(deptId)) {
					deptIdMap.get(deptId).append("、").append(id);
				} else {
					deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
				}
			}
		}
		return R.data(207, deptIdMap, filePath);
	}

	/**
	 * 批量删除数据
	 *
	 * @param ids 主键
	 * @return 删除是否成功
	 */
	@Log(menu = "穿戴式终端管理", operation = Operation.DELETE, objectType = ObjectType.WEAR)
	@GetMapping("/delete")
	public R deleteByIds(@RequestParam("ids") Long[] ids, BladeUser user) {
		QueryWrapper<BdmDeviceStatus> wrapper = new QueryWrapper<>();
		wrapper.in("device_id", ids);
		deviceStatusService.remove(wrapper);
		List<BdmWearableDevice> list = this.wearableDeviceService.getBaseMapper().selectList(new QueryWrapper<BdmWearableDevice>().in("id", ids))
			.stream()
			.filter(bdmWearableDevice -> bdmWearableDevice.getTargetType() != 0)
			.filter(bdmWearableDevice -> bdmWearableDevice.getTargetId() != 0)
			.collect(Collectors.toList());
		if (!list.isEmpty()) {
			return R.fail(ResultCode.FAILURE, "先解绑人员或基础设施");
		}
		Map<Long, Object> map = new HashMap<>();
		List<String> keys = new ArrayList<>();
		for (Long id : ids) {
			keys.add(BaseInfoConstants.WEAR_DEVICE_TYPE + "-" + id);
		}
		// 获取 Redis 数据
		List<Object> values = redisTemplate.opsForHash().multiGet(BaseInfoConstants.BASEINFO_DEVICE, keys);
		// 处理获取到的值
		for (int i = 0; i < keys.size(); i++) {
			String key = keys.get(i);
			Object value = values.get(i);
			if (value != null) {
				String jsonValue = value.toString();
				BdmWearableDevice wearableDevice = JSONObject.parseObject(jsonValue, BdmWearableDevice.class);

				int index = key.indexOf("-");
				if (index != -1) {
					String idValue = key.substring(index + 1);
					Long deptId = wearableDevice.getDeptId();
					if (map.containsKey(deptId)) {
						String existingValues = (String) map.get(deptId);
						map.put(deptId, existingValues + "、" + idValue);
					} else {
						map.put(deptId, idValue);
					}
				}
			}
		}
		boolean result = this.wearableDeviceService.deleteByIds(ids);
		if (result) {
			return R.data(ResultCode.SUCCESS.getCode(), map, "删除成功");
		} else {
			return R.fail("删除失败");
		}
	}

	private void enrichDataWithDict(List<WearableDeviceResponse> records) {
		if (records.isEmpty()) {
			return;
		}
		Map<String, String> deviceTypeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_DEVICE_TYPE);
		Map<String, String> typeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_TYPE);
		Map<String, String> gnssModeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_GNSS_MODE);

		for (WearableDeviceResponse response : records) {
			response.setCategoryName(deviceTypeMap.getOrDefault(String.valueOf(response.getDeviceType()), null));
			response.setDeviceName(deviceTypeMap.getOrDefault(response.getCategory(), null));
			response.setSpecificityName(typeMap.getOrDefault(response.getSpecificity(), null));
			response.setGnssModeName(gnssModeMap.getOrDefault(response.getGnssMode(), null));
		}
	}

}

