package com.xh.vdm.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.converter.BdmAbstractTargetConverter;
import com.xh.vdm.bi.enums.DeviceTypeEnum;
import com.xh.vdm.bi.enums.IotProtocolEnum;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.mapper.BdmAbstractDeviceMapper;
import com.xh.vdm.bi.mapper.BdmVehicleMapper;
import com.xh.vdm.bi.mapper.RnssDeviceMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.*;
import com.xh.vdm.bi.vo.response.ConnectResponse;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.VehicleResponse;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import com.xh.vdm.biapi.entity.BdmVehicle;
import com.xh.vdm.interManager.feign.IMessageClient;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <p>
 * 服务实现类
 * </p>
 */
@Service
public class BdmVehicleServiceImpl extends ServiceImpl<BdmVehicleMapper, BdmVehicle> implements IBdmVehicleService {
	@Resource
	private RnssDeviceMapper rnssDeviceMapper;
	@Resource
	private BdmVehicleMapper bdmVehicleMapper;
	@Resource
	private BdmAbstractDeviceMapper bdmAbstractDeviceMapper;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private IMessageClient messageClient;
	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;
	@Resource
	private IBdmDeviceStatusService deviceStatusService;
	@Resource
	private IBdmVirtualTargetService virtualTargetService;
	@Resource
	private WearableDeviceService wearableDeviceService;
	@Resource
	private RdssDeviceService rdssDeviceService;
	@Resource
	private MonitDeviceService monitDeviceService;
	@Resource
	private PntDeviceService pntDeviceService;

	@Override
	public IPage<VehicleResponse> queryByPage(VehicleRequest vehicleRequest, DataAuthCE ceDataAuth) {
		Page page = new Page<>();
		page.setSize(vehicleRequest.getSize());
		page.setCurrent(vehicleRequest.getCurrent());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.bdmVehicleMapper.queryAll(page, vehicleRequest, response.getAccount(), response.getOrgList());
}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmVehicle insert(VehicleRequest request) {
		BdmVehicle vehicle = this.getBaseMapper().selectOne(new QueryWrapper<BdmVehicle>().eq("number", request.getNumber()));

		BdmVehicle bdmVehicle = new BdmVehicle();
		if (vehicle != null) {
			if (vehicle.getDeleted() == 0) {
				throw new RuntimeException("车辆编号已存在: " + request.getNumber());
			} else {
				BeanUtils.copyProperties(request, bdmVehicle, "id");
				bdmVehicle.setCreateTime(new Date());
				bdmVehicle.setDeleted(0);
				bdmVehicle.setId(vehicle.getId());
				bdmVehicle.setTargetType(TargetTypeEnum.VEHICLE.getSymbol());
				this.baseMapper.updateById(bdmVehicle);

				//新增目标同步到bdm_abstract_target
				BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
				BeanUtils.copyProperties(bdmVehicle, abstractTarget);
				abstractTarget.setName(bdmVehicle.getNumber());
				abstractTargetService.updateById(abstractTarget);
			}
		} else {
			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VEHICLE_TARGET_TYPE, 0, 0);
			BeanUtils.copyProperties(request, bdmVehicle);
			bdmVehicle.setId(targetId.nextId());
			bdmVehicle.setCreateTime(new Date());
			bdmVehicle.setTargetType(TargetTypeEnum.VEHICLE.getSymbol());
			bdmVehicle.setCreateAccount(AuthUtil.getUserAccount());
			this.bdmVehicleMapper.insertVehicle(bdmVehicle);

			//新增目标同步到bdm_abstract_target
			BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
			BeanUtils.copyProperties(bdmVehicle, abstractTarget);
			abstractTarget.setName(bdmVehicle.getNumber());
			abstractTarget.setCreateAccount(AuthUtil.getUserAccount());
			abstractTargetService.save(abstractTarget);
		}

		BdmVehicle ve = this.getBaseMapper().selectById(bdmVehicle.getId());

		Map<String, String> map = new HashMap<>();
		String key = ve.getTargetType() + "-" + ve.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", ve.getNumber());
		innerMap.put("targetType", ve.getTargetType());
		innerMap.put("targetCategory", ve.getCategory());
		innerMap.put("deptId", ve.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setTargetId(ve.getId());
		deviceInfo.setTargetName(ve.getNumber());
		deviceInfo.setTargetCategory(ve.getCategory());
		deviceInfo.setDeptId(ve.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_vehicle_new", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("车辆信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.vehicle(CommonConstant.OPER_TYPE_ADD, ve);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return ve;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmVehicle update(VehicleRequest request) {
		BdmVehicle vehicleInDB = baseMapper.selectById(request.getId());

		BdmVehicle bdmVehicle = new BdmVehicle();
		bdmVehicle.setId(request.getId());
		bdmVehicle.setNumber(request.getNumber() != null ? request.getNumber() : "");
		bdmVehicle.setCategory(request.getCategory() != null ? request.getCategory() : 0);
		bdmVehicle.setTargetType(request.getTargetType() != null ? request.getTargetType() : TargetTypeEnum.VEHICLE.getSymbol());
		bdmVehicle.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		bdmVehicle.setVin(request.getVin() != null ? request.getVin() : "");
		bdmVehicle.setMaxPower(request.getMaxPower() != null ? request.getMaxPower() : 0);
		bdmVehicle.setManufacturer(request.getManufacturer() != null ? request.getManufacturer() : "");
		bdmVehicle.setRatedLoad(request.getRatedLoad() != null ? request.getRatedLoad() : 0);
		bdmVehicle.setModel(request.getModel() != null ? request.getModel() : "");
		bdmVehicle.setUpdateTime(new Date());
		this.bdmVehicleMapper.update(bdmVehicle);

		BdmVehicle vehicle = this.baseMapper.selectById(request.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(vehicle, abstractTarget);
		abstractTarget.setName(bdmVehicle.getNumber());
		abstractTargetService.updateById(abstractTarget);

		String key = vehicle.getTargetType() + "-" + vehicle.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", vehicle.getNumber());
		innerMap.put("targetType", vehicle.getTargetType());
		innerMap.put("targetCategory", vehicle.getCategory());
		innerMap.put("deptId", vehicle.getDeptId());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setTargetId(vehicle.getId());
		deviceInfo.setTargetName(vehicle.getNumber());
		deviceInfo.setTargetCategory(vehicle.getCategory());
		deviceInfo.setTargetType(vehicle.getTargetType());
		deviceInfo.setDeptId(vehicle.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_vehicle_new", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("车辆信息更新消息发送到kafka失败", e);
		}

		//messageClient
		try {
			messageClient.vehicle(CommonConstant.OPER_TYPE_UPDATE, vehicle);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		//@zhouxw modify 2025-06-27 修改监管对象，终端不再同步更改组织
		/*if(!vehicleInDB.getDeptId().equals(vehicle.getDeptId())){
			// 对于RnssDevice的更新
			rnssDeviceService.updateDept(vehicle.getId(), vehicle.getTargetType(), vehicle.getDeptId());

			// 对于BdmAbstractDevice的更新
			abstractDeviceService.updateDept(vehicle.getId(), vehicle.getTargetType(), vehicle.getDeptId());
		}*/

		return vehicle;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {
		List<BdmVehicle> vehicles = baseMapper.selectList(new QueryWrapper<BdmVehicle>().in("id", ids));
		boolean result = this.bdmVehicleMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.VEHICLE_TARGET_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + key);
			}
			this.rnssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.VEHICLE.getSymbol());

			DeviceInfo deviceInfo = new DeviceInfo();
			Long lastId = ids[ids.length - 1];
			deviceInfo.setOperation(Operation.DELETE);
			deviceInfo.setTargetId(lastId);
			deviceInfo.setTargetIds(CollUtil.newHashSet(ids));
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_vehicle_new", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("车辆信息更新消息发送到kafka失败", e);
			}
			//messageClient
			try {
				messageClient.vehicleBatch(CommonConstant.OPER_TYPE_DELETE, vehicles);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
			//更新bdm_abstract_target
			abstractTargetService.deleteByIds(ids);
			//更新bdm_abstract_device
			abstractDeviceService.deleteByTargetIds(ids, TargetTypeEnum.VEHICLE.getSymbol());
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<VehicleRequest> importExcel(List<VehicleRequest> list) {
		//重复的的数据
		List<VehicleRequest> duplicateVehicleRequests = getDuplicateVehicleRequests(list);
		// 去重后的数据（去除所有重复数据）
		List<VehicleRequest> requests = new ArrayList<>(list);
		// 移除确定为重复的 WorkerRequest 对象
		requests.removeAll(duplicateVehicleRequests);

		if (!requests.isEmpty()) {
			List<String> numberList = requests.stream()
					.map(VehicleRequest::getNumber)
					.collect(Collectors.toList());
			//数据库的数据
			QueryWrapper<BdmVehicle> queryWrapper = new QueryWrapper<>();
			if (!numberList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(queryWrapper, "number", numberList);
			}
			//数据库的数据
			Map<String, BdmVehicle> vehicleMap = this.bdmVehicleMapper.selectList(queryWrapper)
					.stream()
					.collect(Collectors.toMap(BdmVehicle::getNumber, BdmVehicle -> BdmVehicle));

			Map<String, String> map = new HashMap<>();
			List<BdmVehicle> vehicleList = new ArrayList<>();
			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VEHICLE_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			List<Long> arrayList = new ArrayList<>();
			List<BdmAbstractTarget> bdmAbstractTargets = new ArrayList<>();

			for (VehicleRequest request : requests) {
				BdmVehicle bdmVehicle = vehicleMap.get(request.getNumber());

				if (bdmVehicle != null) {
					if (bdmVehicle.getDeleted() == 1) {
						BeanUtils.copyProperties(request, bdmVehicle, "id");
						bdmVehicle.setCreateTime(new Date());
						bdmVehicle.setDeleted(0);
						bdmVehicle.setTargetType(TargetTypeEnum.VEHICLE.getSymbol());
						this.bdmVehicleMapper.updateById(bdmVehicle);
						request.setId(bdmVehicle.getId());

						String key = BaseInfoConstants.VEHICLE_TARGET_TYPE + "-" + bdmVehicle.getId();
						Map<String, Object> innerMap = new HashMap<>();
						innerMap.put("targetName", bdmVehicle.getNumber());
						innerMap.put("targetType", bdmVehicle.getTargetType());
						innerMap.put("targetCategory", bdmVehicle.getCategory());
						innerMap.put("deptId", bdmVehicle.getDeptId());
						try {
							map.put(key, new ObjectMapper().writeValueAsString(innerMap));
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}

						//新增目标同步到bdm_abstract_target
						BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
						BeanUtils.copyProperties(bdmVehicle, abstractTarget);
						abstractTarget.setName(bdmVehicle.getNumber());
						abstractTargetService.updateById(abstractTarget);
					} else {
						request.setMsg("车辆编号已存在");
						duplicateVehicleRequests.add(request);
					}
				} else {
					BdmVehicle vehicle = new BdmVehicle();
					vehicle.setId(targetId.nextId());
					vehicle.setNumber(request.getNumber() != null ? request.getNumber() : "");
					vehicle.setCategory(request.getCategory() != null ? request.getCategory() : 0);
					vehicle.setTargetType(TargetTypeEnum.VEHICLE.getSymbol());
					vehicle.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
					vehicle.setVin(request.getVin() != null ? request.getVin() : "");
					vehicle.setMaxPower(request.getMaxPower() != null ? request.getMaxPower() : 0);
					vehicle.setManufacturer(request.getManufacturer() != null ? request.getManufacturer() : "");
					vehicle.setRatedLoad(request.getRatedLoad() != null ? request.getRatedLoad() : 0);
					vehicle.setModel(request.getModel() != null ? request.getModel() : "");
					vehicle.setCreateTime(new Date());
					vehicle.setDeleted(0);
					vehicleList.add(vehicle);

					request.setId(vehicle.getId());
					arrayList.add(vehicle.getId());

					String key = BaseInfoConstants.VEHICLE_TARGET_TYPE + "-" + vehicle.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", vehicle.getNumber());
					innerMap.put("targetType", vehicle.getTargetType());
					innerMap.put("targetCategory", vehicle.getCategory());
					innerMap.put("deptId", vehicle.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}

					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(vehicle, abstractTarget);
					abstractTarget.setName(vehicle.getNumber());
					bdmAbstractTargets.add(abstractTarget);
				}
			}

			if (!vehicleList.isEmpty()) {
				//将剩余的数据批量插入数据库
				MapperUtils.splitListByCapacity(vehicleList, MapperUtils.DEFAULT_CAPACITY)
					.forEach(vehicles -> this.bdmVehicleMapper.insertBatch(vehicles));

				MapperUtils.splitListByCapacity(bdmAbstractTargets, MapperUtils.DEFAULT_CAPACITY)
					.forEach(abstractTargetList -> this.abstractTargetService.insertBatch(abstractTargetList));

				if (!map.isEmpty()) {
					// todo 这样写go那边解析有问题
					redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
				}

				BdmVehicle lastVehicle = vehicleList.get(vehicleList.size() - 1);
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.IMPORT);
				deviceInfo.setTargetId(lastVehicle.getId());
				Set<Long> ids = vehicleList.stream().map(BdmVehicle::getId).collect(Collectors.toSet());
				deviceInfo.setTargetIds(ids);
				deviceInfo.setTargetType(lastVehicle.getTargetType());
				deviceInfo.setTargetCategory(lastVehicle.getCategory());
				deviceInfo.setTargetName(lastVehicle.getNumber());
				deviceInfo.setDeptId(lastVehicle.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_vehicle_new", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("车辆信息更新消息发送到kafka失败", e);
				}
				QueryWrapper<BdmVehicle> wrapper = new QueryWrapper<>();
				if (!arrayList.isEmpty()) {
					MybatisPlusWrapperUitls.longListIn(wrapper, "id", arrayList);
					List<BdmVehicle> vehicles = baseMapper.selectList(wrapper);
					//messageClient
					try {
						messageClient.vehicleBatch(CommonConstant.OPER_TYPE_ADD, vehicles);
					} catch (Exception e) {
						log.error("消息发送到messageClient失败：" + e.getMessage());
					}
				}
			}
		}
		return duplicateVehicleRequests;
	}

	public List<VehicleRequest> getDuplicateVehicleRequests(List<VehicleRequest> list) {
		Map<String, Long> numberCountMap = list.stream()
			.map(VehicleRequest::getNumber)
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<String> duplicateNumbers = numberCountMap.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		return list.stream()
			.filter(vehicleRequest -> duplicateNumbers.contains(vehicleRequest.getNumber()))
			.peek(vehicleRequest -> vehicleRequest.setMsg("车辆编号重复"))
			.collect(Collectors.toList());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean connectTerminal(List<VehicleTerminalRequest> list, Long id, Long deptId) {
		BdmVehicle vehicle = this.bdmVehicleMapper.selectById(id);
		String targetName = vehicle.getNumber();

		List<BdmAbstractDevice> abstractDevices = abstractDeviceService.getBaseMapper()
			.selectList(new QueryWrapper<BdmAbstractDevice>()
				.eq("target_id", id)
				.eq("target_type", TargetTypeEnum.VEHICLE.getSymbol())
			);

//		List<String> uniqueIdList = new ArrayList<>();
//		if(!abstractDevices.isEmpty()){
//			//bdm_device_status更新
//			for (BdmAbstractDevice request:abstractDevices) {
//				QueryWrapper<BdmDeviceStatus> wrapper = new QueryWrapper<>();
//				wrapper.eq("device_id", request.getId());
//				wrapper.eq("device_type", request.getDeviceType());
//				wrapper.eq("target_id", request.getTargetId());
//				wrapper.eq("target_type", request.getTargetType());
//				deviceStatusService.remove(wrapper);
//
//				uniqueIdList.add(request.getUniqueId());
//			}
//
//			// bdm_virtual_target数据 对abstractDevices 进行恢复
//			virtualTargetService.updateBatch(uniqueIdList);
//		}
		if (list.isEmpty()) {
			//根据用户ID去清空原来绑定的终端数据
			rnssDeviceService.updateByVehicleId(id, TargetTypeEnum.VEHICLE.getSymbol());
			//bdm_abstract_device更新绑定关系
			abstractDeviceService.unbinding(id, TargetTypeEnum.VEHICLE.getSymbol());
		} else {
			List<VehicleTerminalRequest> rnssList = list.stream()
				.filter(w -> DeviceTypeEnum.RNSS.getSymbol().equals(w.getDeviceType()))
				.collect(Collectors.toList());

			//根据用户ID去清空原来绑定的终端数据
			rnssDeviceService.updateByVehicleId(id, TargetTypeEnum.VEHICLE.getSymbol());
			//bdm_abstract_device更新绑定关系
			abstractDeviceService.unbinding(id, TargetTypeEnum.VEHICLE.getSymbol());

			if (!rnssList.isEmpty()) {
				rnssDeviceService.connectVehicleTerminal(rnssList, id, TargetTypeEnum.VEHICLE.getSymbol(), targetName, deptId);
			}
			abstractDeviceService.bindVehicle(list, id, TargetTypeEnum.VEHICLE.getSymbol(), targetName, deptId);

			List<String> uniqueIds = list.stream()
				.filter(request -> IotProtocolEnum.JT808.getCode().equals(request.getIotProtocol()))
				.map(VehicleTerminalRequest::getUniqueId)
				.collect(Collectors.toList());
			// bdm_virtual_target数据 对于list中的808进行删除。
			if(!uniqueIds.isEmpty()){
				virtualTargetService.updateByUniqueId(uniqueIds);
			}
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.BIND);
		deviceInfo.setTargetId(vehicle.getId());
		deviceInfo.setTargetName(vehicle.getNumber());
		deviceInfo.setTargetType(vehicle.getTargetType());
		deviceInfo.setDeptId(vehicle.getDeptId());
		Set<Long> ids = abstractDevices.stream().map(BdmAbstractDevice::getId).collect(Collectors.toSet());
		Set<Long> idList = list.stream().map(VehicleTerminalRequest::getId).collect(Collectors.toSet());
		ids.addAll(idList);
		deviceInfo.setDeviceIds(idList);
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("人员信息更新消息发送到kafka失败", e);
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ConnectResponse connectUniqueId(String uniqueId, Long id, Long deptId) {
		ConnectResponse res=new ConnectResponse();
		//后台需要判断设备是否被其他对象绑定，若已被绑定则提示操作失败
		BdmAbstractDevice bad=abstractDeviceService.getBadByUniqueId(uniqueId);
		if (bad==null) {
			res.setCode(1);
			res.setMsg("设备序列号不存在");
			return res;
		}
		if (bad.getTargetId()>0){
			res.setCode(2);
			BdmAbstractTarget bat=abstractTargetService.getById(bad.getTargetId());
			String msg="设备已被其他对象绑定："+bat.getName()+"("+bat.getNumber()+")";
			res.setMsg(msg);
			return res;
		}
		BdmVehicle vehicle = this.bdmVehicleMapper.selectById(id);
		VehicleTerminalRequest request=new VehicleTerminalRequest();
		request.setId(bad.getId());
		this.rnssDeviceMapper.connectVehicleTerminal(request, id, vehicle.getTargetType(), vehicle.getNumber(), deptId);
		List<VehicleTerminalRequest> list = new ArrayList<>();
		list.add(request);
		abstractDeviceService.bindVehicle(list, id, vehicle.getTargetType(), vehicle.getNumber(), deptId);
		List<String> uniqueIds=new ArrayList<>();
		uniqueIds.add(uniqueId);
		virtualTargetService.updateByUniqueId(uniqueIds);
		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.BIND);
		deviceInfo.setTargetId(vehicle.getId());
		deviceInfo.setTargetName(vehicle.getNumber());
		deviceInfo.setTargetType(vehicle.getTargetType());
		deviceInfo.setDeptId(vehicle.getDeptId());
		deviceInfo.setDeviceId(bad.getId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("人员信息更新消息发送到kafka失败", e);
		}
		res.setCode(0);
		return res;
	}

	public ConnectResponse bindOneTarget(TargetDeviceRequest req){
		ConnectResponse res=new ConnectResponse();
		//后台需要判断设备是否被其他对象绑定，若已被绑定则提示操作失败
		BdmAbstractDevice bad=abstractDeviceService.getBadByUniqueId(req.getUniqueId());
		if (bad==null) {
			res.setCode(1);
			res.setMsg("设备序列号不存在");
			return res;
		}
		if (bad.getTargetId()>0&&bad.getTargetType()>0&&!bad.getTargetId().equals(req.getTargetId())){
			res.setCode(2);
			BdmAbstractTarget bat=abstractTargetService.getById(bad.getTargetId());
			String msg="设备已被其他对象绑定："+bat.getName()+"("+bat.getNumber()+")";
			res.setMsg(msg);
			return res;
		}
//		if (bad.getDeviceType()<=2&&bad.getIotProtocol()==1){
//			TaskParam param=new TaskParam();
//			param.setDeviceId(bad.getId());
//			param.setDeviceType(bad.getDeviceType());
//			param.setTargetId(req.getTargetId());
//			param.setTargetType(req.getTargetType());
//			Integer result= SetParam.SendSetParam(param);
//			if (result!=0){
//				res.setCode(3);
//				res.setMsg("设备离线，创建离线任务，等待设备上线绑定");
//				return res;
//			}
//		}
		bdmAbstractDeviceMapper.bindAbstractTarget(req);
		List<String> uniqueIds=new ArrayList<>();
		uniqueIds.add(req.getUniqueId());
		virtualTargetService.updateByUniqueId(uniqueIds);
		//bdm_abstract_target的关联待分配终端的deleted也要置为1
		//BdmAbstractTarget target =abstractTargetService.getOne(new QueryWrapper<BdmAbstractTarget>().eq("number", req.getUniqueId()).eq("deleted", 0));
		//abstractTargetService.restoreStatus(target.getId(),1);
		List<PersonTerminalRequest> list=new ArrayList<>();
		PersonTerminalRequest personReq=new PersonTerminalRequest();
		personReq.setId(bad.getId());
		list.add(personReq);
		if(DeviceTypeEnum.RNSS.getSymbol().equals(bad.getDeviceType())){
			rnssDeviceService.updateByWorkerId(req.getTargetId(), req.getTargetType());
			rnssDeviceService.connectWorkerTerminal(list, req.getTargetId(), req.getTargetType(), req.getTargetName(), req.getDeptId());
		}
		if(DeviceTypeEnum.RDSS.getSymbol().equals(bad.getDeviceType())){
			rdssDeviceService.updateBatchByWorkerId(req.getTargetId(), req.getTargetType());
			rdssDeviceService.updateBatch(list, req.getTargetId(), req.getTargetType(), req.getTargetName(), req.getDeptId());
		}
		if(DeviceTypeEnum.WEARABLE.getSymbol().equals(bad.getDeviceType())){
			wearableDeviceService.updateByWorkerId(req.getTargetId(), req.getTargetType());
			wearableDeviceService.connectWorkerTerminal(personReq, req.getTargetId(), req.getTargetType(), req.getTargetName(), req.getDeptId());
		}
		if(DeviceTypeEnum.MONIT.getSymbol().equals(bad.getDeviceType())){
			monitDeviceService.updateByDeviceId(req.getTargetId(), req.getTargetType());
			List<FacilityTerminalRequest> pnts =new ArrayList<>();
			FacilityTerminalRequest pnt=new FacilityTerminalRequest();
			pnt.setId(bad.getId());
			pnts.add(pnt);
			monitDeviceService.updateBatchByTerminalId(pnts, req.getTargetId(), req.getTargetType(), req.getDeptId());
		}
		if(DeviceTypeEnum.PNT.getSymbol().equals(bad.getDeviceType())){
			pntDeviceService.updateByDeviceId(req.getTargetId(), req.getTargetType());
			List<FacilityTerminalRequest> pnts =new ArrayList<>();
			FacilityTerminalRequest pnt=new FacilityTerminalRequest();
			pnt.setId(bad.getId());
			pnts.add(pnt);
			pntDeviceService.updateBatchByTerminalId(pnts, req.getTargetId(), req.getTargetType(), req.getDeptId());
		}
		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.BIND);
		deviceInfo.setTargetId(req.getTargetId());
		deviceInfo.setTargetType(req.getTargetType());
		deviceInfo.setTargetName(req.getTargetName());
		deviceInfo.setDeviceId(bad.getId());
		deviceInfo.setDeptId(req.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("人员信息更新消息发送到kafka失败", e);
		}
		res.setCode(0);
		return res;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest) {
		int update = bdmVehicleMapper.batchUpdate(batchUpdateRequest.getIds(),batchUpdateRequest.getDeptId());
		if (update == 0) {
			return false;
		}
		//查询出所有的本次要更新的字段
		List<BdmVehicle> bdmVehicleList = bdmVehicleMapper.selectList(new LambdaQueryWrapper<BdmVehicle>().in(BdmVehicle::getId, batchUpdateRequest.getIds()));
		//转换
		List<BdmAbstractTarget> bdmAbstractTargetList = bdmVehicleList.stream().map(BdmAbstractTargetConverter::toBdmVehicle).collect(Collectors.toList());
		//更新主表
		abstractTargetService.saveOrUpdateBatch(bdmAbstractTargetList);
		//批量更新redis和kafka
		batchProcess(bdmVehicleList);
		if (update > 0){
			return true;
		}
		return false;
	}


	/**
	 * 批量更新redis
	 * 批量发送kafka
	 */
	private void batchProcess(List<BdmVehicle> bdmVehicleList){
		for (BdmVehicle vehicle : bdmVehicleList) {
			String key = vehicle.getTargetType() + "-" + vehicle.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("targetName", vehicle.getNumber());
			innerMap.put("targetType", vehicle.getTargetType());
			innerMap.put("targetCategory", vehicle.getCategory());
			innerMap.put("deptId", vehicle.getDeptId());
			Map<String, String> map = new HashMap<>();
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.OTHER);
			deviceInfo.setTargetId(vehicle.getId());
			deviceInfo.setTargetName(vehicle.getNumber());
			deviceInfo.setTargetCategory(vehicle.getCategory());
			deviceInfo.setTargetType(vehicle.getTargetType());
			deviceInfo.setDeptId(vehicle.getDeptId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_vehicle_new", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("车辆信息更新消息发送到kafka失败", e);
			}

			//messageClient
			try {
				messageClient.vehicle(CommonConstant.OPER_TYPE_UPDATE, vehicle);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

}
