package com.xh.vdm.bi.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.vo.request.TargetDeviceRequest;
import com.xh.vdm.bi.vo.response.ConnectResponse;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.biapi.entity.BdmDeviceStatus;
import com.xh.vdm.biapi.entity.BdmRnssDevice;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springblade.common.annotation.Log;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Description: 终端解绑操作
 */
@RestController
@RequestMapping("/target")
@Slf4j
public class TargetController {
	@Resource
	private RnssDeviceService  rnssDeviceService;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private IBdmDeviceStatusService deviceStatusService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private IBdmVehicleService iBdmVehicleService;
	@GetMapping("/unbind")
	@Transactional
	public R unbind(@RequestParam Long id,@RequestParam Integer targetType, BladeUser user, @DeptId Long deptId){
		BdmRnssDevice device = rnssDeviceService.getOne(new QueryWrapper<BdmRnssDevice>().eq("target_id", id).eq("target_type", targetType).eq("deleted", 0));
		int row = rnssDeviceService.unbind(id, targetType);
		if (row > 0){
			QueryWrapper<BdmDeviceStatus> wrapper = new QueryWrapper<>();
			wrapper.eq("device_id", device.getId());
			wrapper.eq("device_type",device.getDeviceType());
			wrapper.eq("target_id",id);
			wrapper.eq("target_type",targetType);
			deviceStatusService.remove(wrapper);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.UNBIND);
			deviceInfo.setTargetId(id);
			deviceInfo.setTargetType(targetType);
			deviceInfo.setDeviceId(device.getId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rnss_device", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("定位终端被解绑更新消息发送到kafka失败", e);
			}
			return R.success("解绑成功");
		}
		return R.fail("解绑失败");
	}
	@Log(operation = Operation.BIND_OR_UNBIND)
	@PostMapping("/connectuniqueid")
	public R<T> connectUniqueId(@RequestBody TargetDeviceRequest req,BladeUser user) {
		ConnectResponse res = this.iBdmVehicleService.bindOneTarget(req);
		if (res.getCode()!=0) {
			return R.fail(ResultCode.FAILURE, res.getMsg());
		}
		return R.success("操作成功");
	}
}
