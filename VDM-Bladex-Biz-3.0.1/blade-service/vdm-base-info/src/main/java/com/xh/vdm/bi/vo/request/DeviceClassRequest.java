package com.xh.vdm.bi.vo.request;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.xh.vdm.biapi.entity.BdmDeviceClass;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;

@Data
@ExcelIgnoreUnannotated
public class DeviceClassRequest {

	private Long id;

	/**
	 * 分类编码（唯一标识）
	 */
	private String code;

	/**
	 * 父分类编码
	 * 关联到本类的code字段，形成树形结构
	 */
	private String parentCode;

	/**
	 * 分类名称
	 */
	private String className;

	/**
	 * 创建人员
	 */
	private String createAccount;

	/**
	 * 创建时间
	 */
	private Date createTime;

	//数据来源：0 系统配置 1 用户输入。所有用户输入的类型均当作“其他”来对待
	private Integer source;

	/**
	 * 删除标记：
	 * 0 - 未删除
	 * 1 - 已删除
	 */
	private Short deleted;


	/**
	 * DTO转VO的基础方法（手动映射）
	 */
	public static DeviceClassRequest dtoToVo(BdmDeviceClass deviceClass) {
		if (deviceClass == null) {
			return null;
		}
		DeviceClassRequest vo = new DeviceClassRequest();
		BeanUtil.copyProperties(deviceClass,vo);
		return vo;
	}
}
