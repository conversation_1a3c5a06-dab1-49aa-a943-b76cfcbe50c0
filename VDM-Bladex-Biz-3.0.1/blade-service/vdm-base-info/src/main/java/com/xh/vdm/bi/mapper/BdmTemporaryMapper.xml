<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmTemporaryMapper">

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryByPage" resultType="com.xh.vdm.bi.vo.response.BdmTemporaryResponse">
        SELECT
        t.id,t.name,t.post,t.phone,t.industry,t.dept_id,t.wkno,t.company_id,t.company,to_char(t.valid_from, 'YYYY-MM-DD') as validFrom,
        to_char(t.valid_to, 'YYYY-MM-DD') as validTo,t.create_time, t.target_type,d.dept_name,
        STRING_AGG(CAST(bad.category AS TEXT), ',') AS terminalCategories,
        CASE WHEN
        CURRENT_DATE > DATE(t.valid_to) THEN 2
        WHEN
        CURRENT_DATE BETWEEN DATE(t.valid_from) AND DATE(t.valid_to) THEN 1
        ELSE 0
        END AS status
        FROM bdm_temporary t
        LEFT JOIN blade_dept d ON t.dept_id = d.id
        LEFT JOIN bdm_abstract_device bad ON t.ID = bad.target_id AND bad.deleted = 0 and bad.target_type = t.target_type
        WHERE t.deleted = 0
        <if test="bdmTemporary.ids != null and bdmTemporary.ids.size() gt 0 ">
            and t.id in
            <foreach collection="bdmTemporary.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="bdmTemporary.ids == null or bdmTemporary.ids == ''">
            <if test="bdmTemporary.name != null and bdmTemporary.name != ''">
                and t.name like concat('%',#{bdmTemporary.name},'%')
            </if>
            <if test="bdmTemporary.post != null">
                and t.post = #{bdmTemporary.post}
            </if>
            <if test="bdmTemporary.industry != null">
                and t.industry = #{bdmTemporary.industry}
            </if>
            <if test="bdmTemporary.wkno != null and bdmTemporary.wkno != ''">
                and t.wkno like concat('%',#{bdmTemporary.wkno},'%')
            </if>
            <if test="bdmTemporary.deptId != null">
                and t.dept_id = #{bdmTemporary.deptId}
            </if>
            <if test="bdmTemporary.terminalType != null">
                and (bad.category = #{bdmTemporary.terminalType} or bad.device_type = #{bdmTemporary.terminalType})
            </if>
            <if test="bdmTemporary.uniqueId != null and bdmTemporary.uniqueId != ''">
                and bad.unique_id like concat('%',#{bdmTemporary.uniqueId},'%')
            </if>
            <if test="bdmTemporary.deviceNum != null and bdmTemporary.deviceNum != ''">
                and bad.device_num like concat('%',#{bdmTemporary.deviceNum},'%')
            </if>
            <if test="bdmTemporary.status == 0">
                and t.valid_from > now()
            </if>
            <if test="bdmTemporary.status == 1">
                and now() between t.valid_from and t.valid_to
            </if>
            <if test="bdmTemporary.status == 2">
                and now() > t.valid_to
            </if>
            <if test="deptIds != null and deptIds != ''">
                and d.id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and t.create_account = #{account}
            </if>
        </if>
        GROUP BY
        t.id,t.name,t.post,t.phone,t.industry,t.dept_id,t.wkno,t.company_id,t.company,t.valid_from,t.valid_to,t.create_time, t.target_type,d.dept_name
        ORDER BY t.create_time DESC
    </select>

    <!--新增所有列-->
    <insert id="insertTemporary" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO bdm_temporary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != name and '' != name">
                name,
            </if>
            <if test="null != wkno and '' != wkno">
                wkno,
            </if>
            <if test="null != targetType">
                target_type,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != post">
                post,
            </if>
            <if test="null != industry">
                industry,
            </if>
            <if test="null != phone and '' != phone">
                phone,
            </if>
            <if test="null != companyId">
                company_id,
            </if>
            <if test="null != company and '' != company">
                company,
            </if>
            <if test="null != validFrom">
                valid_from,
            </if>
            <if test="null != validTo">
                valid_to,
            </if>
            <if test="null != createAccount">
                create_account
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != name and '' != name">
                #{name},
            </if>
            <if test="null != wkno and '' != wkno">
                #{wkno},
            </if>
            <if test="null != targetType">
                #{targetType},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != post">
                #{post},
            </if>
            <if test="null != industry">
                #{industry},
            </if>
            <if test="null != phone and '' != phone">
                #{phone},
            </if>
            <if test="null != companyId">
                #{companyId},
            </if>
            <if test="null != company and '' != company">
                #{company},
            </if>
            <if test="null != validFrom">
                #{validFrom},
            </if>
            <if test="null != validTo">
                #{validTo},
            </if>
            <if test="null != createAccount">
                #{createAccount,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bdm_temporary(id, name, wkno, dept_id, post, industry, phone, company_id, company, valid_from, valid_to)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.name}, #{entity.wkno}, #{entity.deptId}, #{entity.post}, #{entity.industry},
            #{entity.phone}, #{entity.companyId}, #{entity.company}, #{entity.validFrom}, #{entity.validTo})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bdm_temporary
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="wkno != null and wkno != ''">
                wkno = #{wkno},
            </if>
            <if test="targetType != null">
                target_type = #{targetType},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="post != null">
                post = #{post},
            </if>
            <if test="industry != null">
                industry = #{industry},
            </if>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
            <if test="companyId != null">
                company_id = #{companyId},
            </if>
            <if test="company != null and company != ''">
                company = #{company},
            </if>
            <if test="validFrom != null">
                valid_from = #{validFrom},
            </if>
            <if test="validTo != null">
                valid_to = #{validTo},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted}
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="deleteByIds">
        update bdm_temporary
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="batchUpdate">
        UPDATE bdm_temporary
        <set>
            dept_id = #{deptId}
        </set>
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

</mapper>

