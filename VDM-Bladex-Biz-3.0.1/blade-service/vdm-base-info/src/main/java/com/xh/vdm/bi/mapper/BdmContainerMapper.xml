<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmContainerMapper">

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryByPage" resultType="com.xh.vdm.bi.vo.response.ContainerResponse">
        select
        c.id,
        c.number,
        c.model,
        c.size,
        c.dept_id,
        c.max_gross,
        c.tare,
        c.net,
        c.cu_cap,
        c.length,
        c.height,
        to_char(c.create_time, 'YYYY-MM-DD HH24:MI:SS') as createTime,
        c.update_time,
        c.deleted,
        c.target_type,
        d.dept_name,
        rd.unique_id,
        rd.device_num,
        rd.category as terminalCategories
        from bdm_container c
        left join blade_dept d on c.dept_id = d.id
        left join bdm_rnss_device rd ON c.id = rd.target_id
        AND rd.deleted = 0 and rd.target_type = c.target_type
        where c.deleted = 0
        <if test="container.ids != null and container.ids.size() gt 0 ">
            and c.id in
            <foreach collection="container.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="container.ids == null or container.ids == ''">
            <if test="container.number != null and container.number != ''">
                and c.number like concat('%',#{container.number},'%')
            </if>
            <if test="container.deptId != null">
                and c.dept_id = #{container.deptId}
            </if>
            <if test="container.uniqueId != null and container.uniqueId != ''">
                and rd.unique_id like concat('%',#{container.uniqueId},'%')
            </if>
            <if test="container.deviceNum != null and container.deviceNum != ''">
                and rd.device_num like concat('%',#{container.deviceNum},'%')
            </if>
            <if test="deptIds != null and deptIds != ''">
                and d.id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and c.create_account = #{account}
            </if>
        </if>
        order by c.create_time DESC
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO bdm_container
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != number and '' != number">
                number,
            </if>
            <if test="null != model">
                model,
            </if>
            <if test="null != size">
                size,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != maxGross">
                max_gross,
            </if>
            <if test="null != tare">
                tare,
            </if>
            <if test="null != net">
                net,
            </if>
            <if test="null != cuCap">
                cu_cap,
            </if>
            <if test="null != length">
                length,
            </if>
            <if test="null != height">
                height,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted,
            </if>
            <if test="null != targetType">
                target_type,
            </if>
            <if test="null != createAccount">
                create_account
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != number and '' != number">
                #{number},
            </if>
            <if test="null != model">
                #{model},
            </if>
            <if test="null != size">
                #{size},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != maxGross">
                #{maxGross},
            </if>
            <if test="null != tare">
                #{tare},
            </if>
            <if test="null != net">
                #{net},
            </if>
            <if test="null != cuCap">
                #{cuCap},
            </if>
            <if test="null != length">
                #{length},
            </if>
            <if test="null != height">
                #{height},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted},
            </if>
            <if test="null != targetType">
                #{targetType},
            </if>
            <if test="null != createAccount">
                #{createAccount,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bdm_container(id, number, model, size, dept_id, max_gross, tare, net, cu_cap, length, height,
        create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.number}, #{entity.model}, #{entity.size}, #{entity.deptId}, #{entity.maxGross}, #{entity.tare},
            #{entity.net}, #{entity.cuCap}, #{entity.length}, #{entity.height},#{entity.createTime})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bdm_container
        <set>
            <if test="number != null">
                number = #{number},
            </if>
            <if test="model != null">
                model = #{model},
            </if>
            <if test="size != null">
                size = #{size},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="maxGross != null">
                max_gross = #{maxGross},
            </if>
            <if test="tare != null">
                tare = #{tare},
            </if>
            <if test="net != null">
                net = #{net},
            </if>
            <if test="cuCap != null">
                cu_cap = #{cuCap},
            </if>
            <if test="length != null">
                length = #{length},
            </if>
            <if test="height != null">
                height = #{height},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="targetType != null">
                target_type = #{targetType}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteByIds">
        update bdm_container
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>


    <update id="batchUpdate">
        UPDATE bdm_container
        <set>
            dept_id = #{deptId}
        </set>
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

</mapper>

