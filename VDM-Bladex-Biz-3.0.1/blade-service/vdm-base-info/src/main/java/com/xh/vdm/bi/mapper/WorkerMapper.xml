<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.WorkerMapper">

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.WorkerResponse">
        SELECT
        w.id,w.name,w.post,w.phone,w.industry,w.dept_id,w.wkno,d.dept_name,w.create_time, w.target_type,
        STRING_AGG(CAST(bad.category AS TEXT), ',') AS terminalCategories
        FROM bdm_worker w
        LEFT JOIN blade_dept d ON w.dept_id = d.id
        LEFT JOIN bdm_abstract_device bad ON w.ID = bad.target_id AND bad.deleted = 0 and bad.target_type = w.target_type
        WHERE w.deleted = 0
        <if test="request.ids != null and request.ids.size() gt 0 ">
            and w.id in
            <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="request.ids == null or request.ids == ''">
            <if test="request.name != null and request.name != ''">
                and w.name like concat('%',#{request.name},'%')
            </if>
            <if test="request.post != null and request.post != ''">
                and w.post = #{request.post}
            </if>
            <if test="request.industry != null and request.industry != ''">
                and w.industry = #{request.industry}
            </if>
            <if test="request.phone != null and request.phone != ''">
                and w.phone like concat('%',#{request.phone},'%')
            </if>
            <if test="request.wkno != null and request.wkno != ''">
                and w.wkno like concat('%',#{request.wkno},'%')
            </if>
            <if test="request.deptId != null">
                and w.dept_id = #{request.deptId}
            </if>
            <if test="request.deviceType != null">
                and bad.device_type = #{request.deviceType}
            </if>
            <if test="request.terminalType != null">
                and (bad.category = #{request.terminalType} or bad.device_type = #{request.terminalType})
            </if>
            <if test="request.uniqueId != null and request.uniqueId != ''">
                and bad.unique_id like concat('%',#{request.uniqueId},'%')
            </if>
            <if test="request.deviceNum != null and request.deviceNum != ''">
                and bad.device_num like concat('%',#{request.deviceNum},'%')
            </if>
            <if test="deptIds != null and deptIds != ''">
                and d.id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and w.create_account = #{account}
            </if>
        </if>
        GROUP BY
        w.id,w.name,w.post,w.phone,w.industry,w.dept_id,w.wkno,d.dept_name,w.create_time, w.target_type
        ORDER BY w.create_time DESC
    </select>

    <!--新增所有列-->
    <insert id="insertWorker" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO bdm_worker
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != name and '' != name">
                name,
            </if>
            <if test="null != targetType">
                target_type,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != post">
                post,
            </if>
            <if test="null != industry">
                industry,
            </if>
            <if test="null != phone and '' != phone">
                phone,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted,
            </if>
            <if test="null != wkno and '' != wkno">
                wkno,
            </if>
            <if test="null != createAccount and '' != createAccount">
                create_account
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != name and '' != name">
                #{name},
            </if>
            <if test="null != targetType">
                #{targetType},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != post">
                #{post},
            </if>
            <if test="null != industry">
                #{industry},
            </if>
            <if test="null != phone and '' != phone">
                #{phone},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted},
            </if>
            <if test="null != wkno and '' != wkno">
                #{wkno},
            </if>
            <if test="null != createAccount and '' != createAccount">
                #{createAccount}
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bdm_worker
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="targetType != null">
                target_type = #{targetType},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="post != null">
                post = #{post},
            </if>
            <if test="industry != null">
                industry = #{industry},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="wkno != null">
                wkno = #{wkno}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteByIds">
        update bdm_worker
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="batchUpdate">
        UPDATE bdm_worker
        <set>
            dept_id = #{deptId}
        </set>
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>


    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bdm_worker(id, name, dept_id, post, industry, phone,create_time, wkno)
        values
        <foreach collection="list" item="entity" separator=",">
            ( #{entity.id}, #{entity.name}, #{entity.deptId}, #{entity.post},
            #{entity.industry}, #{entity.phone}, now(),#{entity.wkno})
        </foreach>
    </insert>

</mapper>
