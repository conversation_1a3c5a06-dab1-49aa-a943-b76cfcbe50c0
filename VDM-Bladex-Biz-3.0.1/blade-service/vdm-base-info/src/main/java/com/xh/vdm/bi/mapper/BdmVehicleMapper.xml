<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmVehicleMapper">

    <sql id="Base_Column_List">
        ve.id,
        ve.number,
        ve.category,
        ve.target_type,
        ve.dept_id,
        ve.create_time,
        ve.update_time,
        ve.deleted,
        ve.vin,
        ve.max_power,
        ve.manufacturer,
        ve.rated_load,
        ve.model,
        d.dept_name,
        rd.category,
        rd.unique_id
    </sql>

    <!-- TODO 本人权限-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.VehicleResponse">
        SELECT
        ve.id,
        ve.number,
        ve.category,
        ve.target_type,
        ve.dept_id,
        to_char(ve.create_time, 'YYYY-MM-DD HH24:MI:SS') as createTime,
        ve.update_time,
        ve.deleted,
        ve.vin,
        ve.max_power,
        ve.manufacturer,
        ve.rated_load,
        ve.model,
        d.dept_name,
        array_to_string(ARRAY ( SELECT UNNEST ( ARRAY_AGG ( rd.category ) ) ), ',' ) AS terminalCategories,
        rd.unique_id,
        rd.device_num
        FROM
        bdm_vehicle_new ve
        left join blade_dept d on ve.dept_id = d.id
        left join bdm_rnss_device rd on ve.id = rd.target_id AND rd.deleted = 0 and rd.target_type = ve.target_type
        WHERE 1 = 1
        and ve.deleted = 0
        <if test="request.ids != null and request.ids.size() gt 0 ">
            and ve.id in
            <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="request.ids == null or request.ids == ''">
            <if test="request.number != null and request.number != ''">
                and ve.number like concat('%', #{request.number}, '%')
            </if>
            <if test="request.category != null">
                and ve.category = #{request.category}
            </if>
            <if test="request.deptId != null">
                and ve.dept_id = #{request.deptId}
            </if>
            <if test="request.uniqueId != null and request.uniqueId != ''">
                and rd.unique_id like concat('%',#{request.uniqueId},'%')
            </if>
            <if test="request.deviceNum != null and request.deviceNum != ''">
                and rd.device_num like concat('%',#{request.deviceNum},'%')
            </if>
            <if test="account != null and account != ''">
                and ve.create_account = #{account}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and d.id = any(${deptIds})
            </if>
        </if>
        GROUP BY
        ve.id, ve.number, ve.category, ve.target_type, ve.dept_id, ve.create_time, ve.update_time, ve.deleted, d.dept_name, rd.unique_id,rd.device_num
        ORDER BY ve.create_time DESC
    </select>

    <insert id="insertVehicle" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.xh.vdm.biapi.entity.BdmVehicle">
        INSERT INTO bdm_vehicle_new
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != category">
                category,
            </if>
            <if test="null != targetType">
                target_type,
            </if>
            <if test="null != deptId ">
                dept_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted,
            </if>
            <if test="null != number and '' != number">
                number,
            </if>
            <if test="null != vin and '' != vin">
                vin,
            </if>
            <if test="null != maxPower">
                max_power,
            </if>
            <if test="null != manufacturer and '' != manufacturer">
                manufacturer,
            </if>
            <if test="null != ratedLoad">
                rated_load,
            </if>
            <if test="null != model and '' != model">
                model
            </if>
            <if test="null != createAccount and '' != createAccount">
                create_account
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != category">
                #{category},
            </if>
            <if test="null != targetType">
                #{targetType},
            </if>
            <if test="null != deptId ">
                #{deptId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted},
            </if>
            <if test="null != number and '' != number">
                #{number},
            </if>
            <if test="null != vin and '' != vin">
                #{vin},
            </if>
            <if test="null != maxPower">
                #{maxPower},
            </if>
            <if test="null != manufacturer and '' != manufacturer">
                #{manufacturer},
            </if>
            <if test="null != ratedLoad">
                #{ratedLoad},
            </if>
            <if test="null != model and '' != model">
                #{model}
            </if>
            <if test="null != createAccount and '' != createAccount">
                #{createAccount}
            </if>
        </trim>
    </insert>

    <update id="update">
        UPDATE bdm_vehicle_new
        <set>
            <if test="null != number and '' != number">number = #{number},</if>
            <if test="null != category">category = #{category},</if>
            <if test="null != targetType">target_type = #{targetType},</if>
            <if test="null != deptId">dept_id = #{deptId},</if>
            <if test="null != vin">vin = #{vin},</if>
            <if test="null != maxPower">max_power = #{maxPower},</if>
            <if test="null != manufacturer">manufacturer = #{manufacturer},</if>
            <if test="null != ratedLoad">rated_load = #{ratedLoad},</if>
            <if test="null != model">model = #{model},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != deleted">deleted = #{deleted}</if>
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteByIds">
        update bdm_vehicle_new
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

    <insert id="insertBatch">
        insert into bdm_vehicle_new( id,number,category,dept_id,create_time,vin,max_power,manufacturer,rated_load,model)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id}, #{entity.number}, #{entity.category}, #{entity.deptId}, now(), #{entity.vin}, #{entity.maxPower},
             #{entity.manufacturer}, #{entity.ratedLoad}, #{entity.model})
        </foreach>
    </insert>

    <select id="selectAllNumbers" resultType="java.lang.String">
        select number
        from bdm_vehicle_new
        <where>
            <if test="null != number">
                and number != #{number}
            </if>
        </where>
    </select>

    <update id="batchUpdate">
        UPDATE bdm_vehicle_new
        <set>
            dept_id = #{deptId}
        </set>
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

</mapper>
