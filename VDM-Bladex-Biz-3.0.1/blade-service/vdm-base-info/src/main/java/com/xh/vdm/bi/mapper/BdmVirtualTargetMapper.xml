<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmVirtualTargetMapper">

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryByPage" resultType="com.xh.vdm.biapi.entity.BdmVirtualTarget">
        select
        vt.id,
        vt.number,
        vt.category,
        vt.target_type,
        vt.dept_id,
        vt.create_time,
        vt.update_time,
        vt.deleted,
        bad.id as deviceId,
        bad.device_type,
        bad.unique_id,
        bad.device_num,
        bd.dept_name
        from bdm_virtual_target vt
        left join bdm_abstract_device bad on bad.unique_id = vt.number
        left join blade_dept bd on vt.dept_id = bd.id
        where vt.deleted = 0 and bad.deleted=0
            <if test="virtualTarget.number != null and virtualTarget.number != ''">
                and vt.number like concat('%',#{virtualTarget.number},'%')
            </if>
            <if test="virtualTarget.category != null">
                and vt.category = #{virtualTarget.category}
            </if>
            <if test="virtualTarget.targetType != null">
                and vt.target_type = #{virtualTarget.targetType}
            </if>
            <if test="virtualTarget.deptId != null">
                and vt.dept_id = #{virtualTarget.deptId}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and bd.id = any(${deptIds})
            </if>
            <if test="virtualTarget.uniqueId != null and virtualTarget.uniqueId != ''">
                and bad.unique_id like concat('%',#{virtualTarget.uniqueId},'%')
            </if>
            <if test="virtualTarget.deviceNum != null and virtualTarget.deviceNum != ''">
                and bad.device_num like concat('%',#{virtualTarget.deviceNum},'%')
            </if>
        <if test="account != null and account != ''">
            and vt.create_account = #{account}
        </if>

    </select>

    <insert id="insertBatch">
        INSERT INTO "bdm_virtual_target" (
        "id",
        "number",
        "category",
        "target_type",
        "dept_id",
        "create_time",
        "deleted",
        "device_code"
        ) VALUES
        <foreach collection="entities" item="item" index="index" separator=",">
            (
            #{item.id},
            #{item.number},
            #{item.category},
            #{item.targetType},
            #{item.deptId},
            #{item.createTime},
            #{item.deleted},
            #{item.deviceCode}
            )
        </foreach>
    </insert>

    <update id="updateByUniqueId">
        update bdm_virtual_target
        set deleted = 1,update_time = now()
        where number in
        <foreach collection="uniqueIdList" open="(" item="uniqueId" separator="," close=")">
            #{uniqueId}
        </foreach>
    </update>

    <update id="updateStatus">
        update bdm_virtual_target
        set deleted = 0,update_time = now()
        where number = #{uniqueId}
    </update>

    <update id="restoreStatus">
        update bdm_virtual_target
        set deleted = #{deleted},update_time = now()
        where id = #{targetId}
    </update>

    <update id="updateBatch">
        update bdm_virtual_target
        set deleted = 0,update_time = now()
        where number in
        <foreach collection="uniqueIdList" open="(" item="uniqueId" separator="," close=")">
            #{uniqueId}
        </foreach>
    </update>

    <update id="updateBatchByUniqueId">
        update bdm_rnss_device
        set target_id = #{id}, target_type = 0, target_name = #{number}
        where unique_id = #{number};

        update bdm_wearable_device
        set target_id = #{id}, target_type = 0, target_name = #{number}
        where unique_id = #{number};

        update bdm_rdss_device
        set target_id = #{id}, target_type = 0, target_name = #{number}
        where unique_id = #{number};

        update bdm_monit_device
        set target_id = #{id} , target_type = 0
        where unique_id = #{number};

        update bdm_pnt_device
        set target_id = #{id} , target_type = 0
        where unique_id = #{number};
    </update>

</mapper>

