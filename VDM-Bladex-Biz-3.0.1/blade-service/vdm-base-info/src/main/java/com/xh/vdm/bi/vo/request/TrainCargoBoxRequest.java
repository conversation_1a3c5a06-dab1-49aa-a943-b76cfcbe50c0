package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import org.springblade.common.dept.DeptIdAware;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.util.Date;
import java.util.List;

/**
 * @Description: 铁路货车车厢管理入参
 */
@Data
@ExcelIgnoreUnannotated
public class TrainCargoBoxRequest implements DeptIdAware {

	private Long id;
	@Compare("货车车厢编号")
	@ExcelProperty(value = "货车车厢编号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String number;

	private Integer targetType;
	@Compare("箱型")
	private Integer model;
	@Compare("尺寸")
	private Integer size;
	@Compare("所属部门id")
	private Long deptId;
	@Override
	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	@Compare("总重")
	private Float maxGross;
	@Compare("自重/皮重")
	private Float tare;
	@Compare("载重/净重")
	private Float net;
	@Compare("最大装货容积")
	private Float cuCap;
	@Compare("长度")
	private Integer length;
	@Compare("高度")
	private Integer height;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	/**
	 * 错误信息
	 */
	@TableField(exist = false)
	@ExcelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String msg;

	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;
	private String terminalCategories;

	/**
	 * 导出动态表头中文名
	 */
	private List headNameList;

	/**
	 * 导出动态表头字段名
	 */
	private List columnNameList;

	/**
	 * 导出数据的id集合
	 */
	private List<Long> ids;

	@ExcelIgnore
	private String createAccount;
}

