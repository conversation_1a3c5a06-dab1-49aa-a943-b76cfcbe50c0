<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.bdCheck.BdcTerminalMapper">

    <update id="updateFormalByDeviceSeq">
        update bdc_terminal
        set formal = 1
        where device_seq = #{deviceSeq}
    </update>

    <update id="updateFormalByDeviceSeqs">
        UPDATE bdc_terminal
        SET formal = 0
        WHERE device_seq IN
        <if test="uniqueIdList != null and uniqueIdList.size() > 0">
            <foreach collection="uniqueIdList" item="uniqueId" open="(" separator="," close=")">
                #{uniqueId}
            </foreach>
        </if>
    </update>
</mapper>
