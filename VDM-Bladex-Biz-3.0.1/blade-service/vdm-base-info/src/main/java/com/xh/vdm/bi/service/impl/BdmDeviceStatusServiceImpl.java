package com.xh.vdm.bi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.dto.DeviceModelCount;
import com.xh.vdm.bi.dto.DeviceModelInfo;
import com.xh.vdm.bi.mapper.BdmDeviceStatusMapper;
import com.xh.vdm.bi.service.IBdmDeviceStatusService;
import com.xh.vdm.biapi.entity.BdmDeviceStatus;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * (BdmDeviceLink)表服务实现类
 */
@Service
public class BdmDeviceStatusServiceImpl extends ServiceImpl<BdmDeviceStatusMapper, BdmDeviceStatus> implements IBdmDeviceStatusService {
	@Resource
	private BdmDeviceStatusMapper baseMapper;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	@Override
	public long countOnlineDevices(DataAuthCE ceDataAuth) {
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return baseMapper.countOnlineDevices(response.getAccount(), response.getOrgList());
	}

	@Override
	public List<DeviceModelCount> getDeviceModelOnlineByUserAuth(DataAuthCE auth) {
		AuthInfo response = deptProcessingUtil.handle(auth);
		return baseMapper.getDeviceModelOnlineByUserAuth(response.getAccount(), response.getOrgList());
	}

	@Override
	public List<DeviceModelInfo> getDevOnlineInfoWith(DataAuthCE auth, List<String> models, String district, Long deptId, Byte usage) {
		// 如果未指定部门id
		String deptIds = null;
		String account = null;
		if (null == deptId) {
			if (!AuthUtil.isAdministrator()) {
				AuthInfo response = deptProcessingUtil.handle(auth);
				account = response.getAccount();
				deptIds = response.getOrgList();
			}
		}
		return baseMapper.getDevOnlineInfoWith(account, deptIds, deptId, models, usage, district);
	}

	@Override
	public List<DeviceModelCount> getDevOnlineCntWith(DataAuthCE auth, List<String> models, String district,
														   Long deptId, Byte usage) {
		// 如果未指定部门id
		String deptIds = null;
		String account = null;
		if (null == deptId) {
			if (!AuthUtil.isAdministrator()) {
				AuthInfo response = deptProcessingUtil.handle(auth);
				account = response.getAccount();
				deptIds = response.getOrgList();
			}
		}
		return baseMapper.getDevOnlineCntWith(account, deptIds, deptId, models, usage, district);
	}

	@Override
	public long countByUserAuth(Byte specificity, Byte online, DataAuthCE auth) {
		AuthInfo response = deptProcessingUtil.handle(auth);
		return baseMapper.countByUserAuth(specificity, online, response.getAccount(), response.getOrgList());
	}

	@Override
	public List<Long> getDeviceIdsWith(Byte specificity, Byte online,
									   DataAuthCE auth, String district, Long deptId, Byte usage) {
		// 如果未指定部门id
		String deptIds = null;
		String account = null;
		if (null == deptId) {
			if (!AuthUtil.isAdministrator()) {
				AuthInfo response = deptProcessingUtil.handle(auth);
				account = response.getAccount();
				deptIds = response.getOrgList();
			}
		}

		return baseMapper.getDeviceIdsWith(specificity, online, account, deptIds, deptId, usage, district);
	}

	@Override
	public long countByUserWith(Byte specificity, Byte online, DataAuthCE auth, String district, Long deptId, Byte usage) {
		// 如果未指定部门id
		String deptIds = null;
		String account = null;
		if (null == deptId) {
			if (!AuthUtil.isAdministrator()) {
				AuthInfo response = deptProcessingUtil.handle(auth);
				account = response.getAccount();
				deptIds = response.getOrgList();
			}
		}

		return baseMapper.countByUserWith(specificity, online, account, deptIds, deptId, usage, district);
	}
}
