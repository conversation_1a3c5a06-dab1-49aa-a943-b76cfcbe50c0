package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.ShipRequest;
import com.xh.vdm.bi.vo.response.ShipResponse;
import com.xh.vdm.biapi.entity.BdmShip;
import org.springblade.core.mp.support.Query;
import org.springblade.entity.DataAuthCE;

import java.util.List;


/**
 * (BdmShip)表服务接口
 */
public interface BdmShipService extends IService<BdmShip> {

	/**
     * 分页查询
     *
     * @param shipRequest 筛选条件
     * @param query       分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<ShipResponse> queryByPage(ShipRequest shipRequest, Query query, DataAuthCE ceDataAuth);

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmShip insert(ShipRequest request);

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmShip update(ShipRequest request);

	List<ShipRequest> importExcel(List<ShipRequest> list);

	Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest);
}
