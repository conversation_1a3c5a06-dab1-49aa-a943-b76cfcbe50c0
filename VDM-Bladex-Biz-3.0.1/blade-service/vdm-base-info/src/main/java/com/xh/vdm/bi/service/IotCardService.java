package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.IotCardRequest;
import com.xh.vdm.bi.vo.request.TerminalIotRequest;
import com.xh.vdm.bi.vo.response.BdmIotCardExportResponse;
import com.xh.vdm.bi.vo.response.IotCardResponse;
import com.xh.vdm.biapi.entity.BdmIotCard;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmIotCard)表服务接口
 */
public interface IotCardService extends IService<BdmIotCard> {

	/**
     * 分页查询
     *
     * @param iotCardRequest 筛选条件
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<BdmIotCardExportResponse> queryByPage(IotCardRequest iotCardRequest, DataAuthCE ceDataAuth);

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	BdmIotCard queryById(Long id);

	/**
	 * 新增数据
	 *
	 * @param iotCardRequest 实例对象
	 * @return 实例对象
	 */
	BdmIotCard insert(IotCardRequest iotCardRequest);

	/**
	 * 修改数据
	 *
	 * @param iotCardRequest 实例对象
	 * @return 实例对象
	 */
	BdmIotCard update(IotCardRequest iotCardRequest);

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	boolean deleteById(Long id);

	boolean deleteByIds(Long[] ids);

    void updateIotCardByDeviceId(String numbers, Long id, Integer deviceType);

	List<IotCardResponse> iotList(String number, DataAuthCE ceDataAuth);

	void updateDeviceIdById(Long id, Integer deviceType, String numbers);

	List<IotCardRequest> importExcel(List<IotCardRequest> list);

	void updateDeviceId(List<TerminalIotRequest> terminalIotRequests);

	void deleteByDeviceIds(Long[] ids, Integer deviceType);

	List<String> queryIot(List<String> numberedList, Long userId);

	/**
	 * @description: 查询物联网卡信息（国能底座逻辑）
	 * @author: zhouxw
	 * @date: 2025-06-161 14:08:26
	 * @param: [numberedList, userId]
	 * @return: java.util.List<java.lang.String>
	 **/
	List<String> queryIotCE(List<String> numberedList, String account, String deptArrayStr);
}
