package com.xh.vdm.bi.mapper.gnProd;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bi.entity.gnProd.BdsOrgLite;
import com.xh.vdm.bi.entity.gnProd.BdsOrg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("gnProd")
@Mapper
public interface BdsOrgMapper extends BaseMapper<BdsOrg> {
	/**
	 * 获取指定产业板块下的组织。
	 * @param orgCodes 组织代码表
	 * @param sectors 产业板块列表
	 * @return 符合条件的组织列表
	 */
	List<BdsOrgLite> getBdsOrgWith(@Param("orgCodes") String orgCodes, @Param("sectors") List<String> sectors);
}
