package com.xh.vdm.bi.vo.request;

import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class TargetDeviceRequest {
	/**
	 * 監控对象ID
	 */
	@NotNull(message = "targetId不能为空")
	private Long targetId;
	/**
	 * 監控对象类型
	 */
	@NotNull(message = "targetType不能为空")
	private Integer targetType;
	/**
	 * 監控对象名稱
	 */
	@NotNull(message = "targetName不能为空")
	private String targetName;
	/**
	 * 監控对象單位Id
	 */
	@NotNull(message = "deptId不能为空")
	private Long deptId;
	/**
	 * 序列号
	 */
	@NotNull(message = "uniqueId不能为空")
	private String uniqueId;
}
