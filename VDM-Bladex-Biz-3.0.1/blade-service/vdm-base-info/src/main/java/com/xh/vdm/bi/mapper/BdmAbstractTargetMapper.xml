<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmAbstractTargetMapper">

    <!--通过主键修改数据-->
    <update id="update">
        update bdm_abstract_target
        <set>
            <if test="number != null and number != ''">
                number = #{number},
            </if>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="targetType != null">
                target_type = #{targetType},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="category != null">
                category = #{category},
            </if>
            <if test="deleted != null">
                deleted = #{deleted}
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="restoreStatus">
        update bdm_abstract_target
        set deleted = #{deleted}
        where id = #{targetId}
    </update>

    <insert id="insertBatch">
        <!-- 批量插入 -->
        INSERT INTO "bdm_abstract_target" ("id", "number", "name", "target_type", "dept_id", "category", "deleted")
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id}, #{item.number}, #{item.name}, #{item.targetType}, #{item.deptId}, #{item.category},
            #{item.deleted})
        </foreach>
    </insert>

    <update id="deleteByIds">
        update bdm_abstract_target
        set deleted =1
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

</mapper>

