package com.xh.vdm.bi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.entity.UserDeptRegulates;
import com.xh.vdm.bi.mapper.UserDeptRegulatesMapper;
import com.xh.vdm.bi.service.IUserDeptRegulatesService;
import com.xh.vdm.bi.vo.response.UserDeptRegulatesResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 服务实现类
 */
@Service
public class UserDeptRegulatesServiceImpl extends ServiceImpl<UserDeptRegulatesMapper, UserDeptRegulates> implements IUserDeptRegulatesService {

	@Resource
	private UserDeptRegulatesMapper userDeptRegulatesMapper;

	@Override
	public List<UserDeptRegulatesResponse> query(Long userId) {
		return userDeptRegulatesMapper.query(userId);
	}
}
