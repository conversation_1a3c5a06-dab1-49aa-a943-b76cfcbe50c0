package com.xh.vdm.bi.service.impl.bdCheck;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.entity.bdCheck.BdcTerminal;
import com.xh.vdm.bi.mapper.bdCheck.BdcTerminalMapper;
import com.xh.vdm.bi.service.bdCheck.IBdcTerminalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Slf4j
@Service
@DS("bdCheck")
public class BdcTerminalServiceImpl extends ServiceImpl<BdcTerminalMapper, BdcTerminal> implements IBdcTerminalService {

	@Resource
	private BdcTerminalMapper bdcTerminalMapper;

    @Override
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void updateFormalByDeviceSeq(String deviceSeq) {
		bdcTerminalMapper.updateFormalByDeviceSeq(deviceSeq);
    }

	@Override
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
	public void updateFormalByDeviceSeqs(List<String> uniqueIdList) {
		bdcTerminalMapper.updateFormalByDeviceSeqs(uniqueIdList);
	}
}
