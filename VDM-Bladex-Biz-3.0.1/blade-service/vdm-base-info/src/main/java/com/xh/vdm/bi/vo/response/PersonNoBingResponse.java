package com.xh.vdm.bi.vo.response;

import lombok.Data;

/**
 * 绑定终端按钮展示列表
 */
@Data
public class PersonNoBingResponse {
	//终端ID
	private Long id;
	//终端类型
	private Integer category;

	private String categoryName;
	//终端型号
	private String model;
	//所属机构
	private Long deptId;
	//机构名称
	private String deptName;
	//终端类别
	private Integer deviceType;

	private String deviceTypeName;
	/**
	 * 序列号
	 */
	private String uniqueId;
	/**
	 * 目标类别
	 */
	private Integer targetType;

	private String deviceNum;

	private Integer iotProtocol;
	private Integer specificity;
}
