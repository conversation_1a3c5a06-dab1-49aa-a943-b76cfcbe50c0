package com.xh.vdm.bi.vo.request;

import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;

/**
 * @Description:
 */
@Data
public class DeviceCodeRequest implements Serializable {
	@Compare("终端赋码号")
	private String deviceNum;
	@Compare("序列号")
	private String deviceSeq;
	@Compare("终端型号")
	private String deviceModel;
	@Compare("IMEI号")
	private String imei;
	@Compare("北斗芯片序列号")
	private String chipSeq;
	@Compare("生产厂商")
	private String manufacturer;
	private Integer status;
}

