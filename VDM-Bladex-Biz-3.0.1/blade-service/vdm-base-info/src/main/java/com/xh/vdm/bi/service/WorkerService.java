package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.WorkerRequest;
import com.xh.vdm.bi.vo.response.ConnectResponse;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.WorkerResponse;
import com.xh.vdm.biapi.entity.BdmWorker;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmWorker)表服务接口
 */
public interface WorkerService extends IService<BdmWorker> {

	/**
     * 分页查询
     *
     * @param workerRequest 筛选条件
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<WorkerResponse> queryByPage(WorkerRequest workerRequest, DataAuthCE ceDataAuth);

	/**
	 * 新增数据
	 *
	 * @param workerRequest 实例对象
	 * @return 实例对象
	 */
	BdmWorker insert(WorkerRequest workerRequest);

	/**
	 * 修改数据
	 *
	 * @param bdmWorker 实例对象
	 * @return 实例对象
	 */
	BdmWorker update(BdmWorker bdmWorker);

	/**
	 * 通过主键删除数据
	 *
	 * @param ids 主键
	 * @return 是否成功
	 */
	boolean deleteByIds(Long[] ids);

	ConnectResponse connectTerminal(List<PersonTerminalRequest> list, Long id, Long deptId);

	IPage<PersonNoBingResponse> select(DeviceNoBindRequest deviceNoBindRequest, DataAuthCE ceDataAuth);

	List<PersonNoBingResponse> terminalInfo(Long id);

	List<PersonNoBingResponse> selectBind(DeviceNoBindRequest deviceNoBindRequest, Long id);

    List<WorkerRequest> importExcel(List<WorkerRequest> list);

	Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest);
}
