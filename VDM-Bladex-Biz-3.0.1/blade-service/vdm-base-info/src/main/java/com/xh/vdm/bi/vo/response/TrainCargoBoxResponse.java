package com.xh.vdm.bi.vo.response;

import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;

/**
 * @Description: 铁路货车车厢管理返参
 */
@Data
public class TrainCargoBoxResponse implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	@Compare("货车车厢编号")
	private String number;

	private Integer targetType;
	@Compare("箱型")
	private String model;
	private String modelName;
	@Compare("尺寸")
	private Integer size;
	@Compare("所属部门id")
	private Long deptId;
	@Compare("总重")
	private Float maxGross;
	@Compare("自重/皮重")
	private Float tare;
	@Compare("载重/净重")
	private Float net;
	@Compare("最大装货容积")
	private Float cuCap;
	@Compare("长度")
	private Integer length;
	@Compare("高度")
	private Integer height;

	private String createTime;

	private String updateTime;

	private Integer deleted;

	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;

	private String terminalCategories;

	private String deptName;
}

