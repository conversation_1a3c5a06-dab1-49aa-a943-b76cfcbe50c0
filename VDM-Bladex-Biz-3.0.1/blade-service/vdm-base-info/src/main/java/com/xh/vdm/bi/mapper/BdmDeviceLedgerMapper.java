package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.BdmDeviceLedgerRequest;
import com.xh.vdm.biapi.entity.BdmDeviceLedger;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmDeviceLedger)表数据库访问层
 */
public interface BdmDeviceLedgerMapper extends BaseMapper<BdmDeviceLedger> {

	long insertBatch(List<BdmDeviceLedger> list);

	long updateByIds(@Param("ids") List<Long> ids, @Param("userDeptId") Long userDeptId, @Param("userId") Long userId);

    IPage<BdmDeviceLedger> queryAll(@Param("request") BdmDeviceLedgerRequest request, IPage<BdmDeviceLedger> page, @Param("schema") String schema);

    void recycle(@Param("deviceLedger") BdmDeviceLedger bdmDeviceLedger);

	void updateByUniqueId(BdmDeviceLedger request);

	List<Object>  selectDevice(String uniqueId);

	IPage<BdmDeviceLedger> query(@Param("request") BdmDeviceLedgerRequest request, IPage<BdmDeviceLedger> page, @Param("schema") String schema);

	boolean update(BdmDeviceLedger deviceLedger);
}

