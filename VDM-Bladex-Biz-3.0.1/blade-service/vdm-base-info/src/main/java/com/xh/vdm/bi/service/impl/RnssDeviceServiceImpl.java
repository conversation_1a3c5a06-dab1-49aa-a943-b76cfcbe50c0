package com.xh.vdm.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.enums.*;
import com.xh.vdm.bi.mapper.RnssDeviceMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bi.utils.DeviceNumUtils;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.SnowflakeIdWorker;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.RnssDeviceRequest;
import com.xh.vdm.bi.vo.request.TerminalIotRequest;
import com.xh.vdm.bi.vo.request.VehicleTerminalRequest;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.RnssDeviceResponse;
import com.xh.vdm.bi.vo.response.WorkerBingResponse;
import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.interManager.feign.IMessageClient;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.common.utils.StringUtils;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Description: 短报文终端管理
 */
@Service
@Slf4j
public class RnssDeviceServiceImpl extends ServiceImpl<RnssDeviceMapper, BdmRnssDevice> implements RnssDeviceService {
	@Resource
	private RnssDeviceMapper rnssDeviceMapper;

	@Resource
	private CETokenUtil ceTokenUtil;

	@Resource
	private IotCardService iotCardService;

	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private DeviceNumUtils deviceNumUtils;
	@Resource
	private IBdcTerminalService terminalService;
	@Resource
	private BdmDeviceCodeService deviceCodeService;
	@Resource
	private BdmDeviceLedgerService deviceLedgerService;
	@Resource
	private IMessageClient messageClient;
	@Value("${current.schema}")
	String schema;
	@Resource
	private IBdmVirtualTargetService bdmVirtualTargetService;
	@Resource
	private IBdmAbstractDeviceService bdmAbstractDeviceService;
	@Resource
	private IBdmAbstractTargetService bdmAbstractTargetService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
	 * 分页查询
	 *
	 * @param request    筛选条件
	 * @param ceDataAuth
	 * @return 查询结果
	 */
	@Override
	public IPage<RnssDeviceResponse> queryByPage(RnssDeviceRequest request, DataAuthCE ceDataAuth) {
		Page page = new Page();
		page.setCurrent(request.getCurrent());
		page.setSize(request.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.rnssDeviceMapper.queryAll(page, request, response.getAccount(), response.getOrgList(), schema);
	}

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public RnssDeviceResponse queryById(Long id) {
		return this.rnssDeviceMapper.queryById(id);
	}


	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmRnssDevice insert(RnssDeviceRequest request) {
		BdmRnssDevice bdmRnssDevice = new BdmRnssDevice();
		QueryWrapper<BdmRnssDevice> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("unique_id", request.getUniqueId());
		BdmRnssDevice rnss = baseMapper.selectOne(queryWrapper);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		if (rnss != null) {
			request.setId(rnss.getId());
			if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
				log.info("北斗定位终端旧设备生成赋码值:{},{}", r1, request);
			}
			if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
				log.info("北斗定位终端特殊设备生成赋码值:{},{}", r1, request);
			}
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			request.setDeleted(0);
			bdmRnssDevice = this.init(request);
			// 同步到待分配终端对象实体表
			QueryWrapper<BdmVirtualTarget> wrapper = new QueryWrapper<>();
			wrapper.eq("number", bdmRnssDevice.getUniqueId());
			wrapper.last("LIMIT 1");
			BdmVirtualTarget latestRecord = bdmVirtualTargetService.getOne(wrapper);
			// 如果此时为空，应当向 bdm_virtual_target中新增数据。
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmRnssDevice, virtualTarget);
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmRnssDevice.getDeviceNum());
			virtualTarget.setNumber(bdmRnssDevice.getUniqueId());
			if (latestRecord == null) {
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
				boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
				if (saveRes) {
					log.info("bdmVirtualTargetService漏掉的 新增成功");
				} else {
					log.info("bdmVirtualTargetService漏掉的 新增失败:{}", virtualTarget);
				}
			} else {
				virtualTarget.setId(latestRecord.getId());
				boolean updateRes = bdmVirtualTargetService.updateById(virtualTarget);
				if (updateRes) {
					log.info("bdmVirtualTargetService 更新成功");
				} else {
					log.info("bdmVirtualTargetService 更新失败:{}", virtualTarget);
				}
			}
			QueryWrapper<BdmAbstractTarget> batWrapper = new QueryWrapper<>();
			batWrapper.eq("number", bdmRnssDevice.getUniqueId());
			batWrapper.last("LIMIT 1");
			BdmAbstractTarget batLatestRecord = bdmAbstractTargetService.getOne(batWrapper);
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmRnssDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmRnssDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmRnssDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			if (batLatestRecord == null) {
				boolean saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
				if (saveRes) {
					log.info("bdmAbstractTargetService漏掉的 新增成功");
				} else {
					log.info("bdmAbstractTargetService漏掉的 新增失败:{}", bdmAbstractTarget);
				}
			} else {
				boolean updateRes = bdmAbstractTargetService.updateById(bdmAbstractTarget);
				if (updateRes) {
					log.info("bdmAbstractTargetService 更新成功");
				} else {
					log.info("bdmAbstractTargetService 更新失败:{}", bdmAbstractTarget);
				}
			}
			bdmRnssDevice.setTargetId(virtualTarget.getId());
			baseMapper.update(bdmRnssDevice);
			// 同步到抽象监控对象实体表
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(bdmRnssDevice, abstractDevice);
			abstractDevice.setTargetType(0);
			abstractDevice.setDeleted(0);
			boolean updateRes = bdmAbstractDeviceService.updateById(abstractDevice);
			if (updateRes) {
				log.info("bdmAbstractDeviceService 更新成功");
			} else {
				log.info("bdmAbstractDeviceService 更新失败:{}", abstractDevice);
			}
		} else {
			if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
				log.info("北斗定位终端旧设备生成赋码值:{},{}", r1, request);
			}
			if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
				log.info("北斗定位终端特殊设备生成赋码值:{},{}", r1, request);
			}
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.RNSS_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			request.setId(idWorker.nextId());
			request.setCreateTime(new Date());
			bdmRnssDevice = this.init(request);
			bdmRnssDevice.setCreateAccount(AuthUtil.getUserAccount());
			if (bdmRnssDevice.getDeviceNum().length() == 0) {
				return null;
			}
			// 同步到待分配终端对象实体表
			// if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmRnssDevice, virtualTarget);
			virtualTarget.setId(targetId.nextId());
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmRnssDevice.getDeviceNum());
			virtualTarget.setNumber(bdmRnssDevice.getUniqueId());
			virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
			boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
			if (saveRes) {
				log.info("bdmVirtualTargetService 新增成功");
			} else {
				log.info("bdmVirtualTargetService 新增失败:{}", virtualTarget);
			}
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmRnssDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmRnssDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmRnssDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			bdmAbstractTarget.setCreateAccount(AuthUtil.getUserAccount());
			saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
			if (saveRes) {
				log.info("bdmAbstractTargetService 新增成功");
			} else {
				log.info("bdmAbstractTargetService 新增失败:{}", bdmAbstractTarget);
			}
			//}
			bdmRnssDevice.setTargetId(virtualTarget.getId());
			this.rnssDeviceMapper.insertRnss(bdmRnssDevice);
			// 同步到抽象监控对象实体表
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(bdmRnssDevice, abstractDevice);
			abstractDevice.setTargetType(0);
			abstractDevice.setCreateAccount(AuthUtil.getUserAccount());
			bdmAbstractDeviceService.saveDevice(abstractDevice);
		}

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			// 更新存在且未绑定的物联网卡信息
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), bdmRnssDevice.getId(), bdmRnssDevice.getDeviceType());
		}
		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			// 跟新bdm_terminal
			terminalService.updateFormalByDeviceSeq(request.getUniqueId());
			// 更新bdm_device_code
			deviceCodeService.updateActivated(request.getDeviceNum());
		}

		BdmRnssDevice rnssDevice = this.getBaseMapper().selectById(bdmRnssDevice.getId());

		Map<String, String> map = new HashMap<>();
		String key = rnssDevice.getDeviceType() + "-" + rnssDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", rnssDevice.getUniqueId());
		innerMap.put("deviceNum", rnssDevice.getDeviceNum());
		innerMap.put("category", rnssDevice.getCategory());
		innerMap.put("deviceType", rnssDevice.getDeviceType());
		innerMap.put("deptId", rnssDevice.getDeptId());
		innerMap.put("iotProtocol", rnssDevice.getIotProtocol());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		// todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			Map<String, String> targetMap = new HashMap<>();
			String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + rnssDevice.getId();
			Map<String, Object> targetInnerMap = new HashMap<>();
			targetInnerMap.put("targetName", rnssDevice.getUniqueId());
			targetInnerMap.put("targetType", 0);
			targetInnerMap.put("deptId", rnssDevice.getDeptId());
			try {
				map.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			// todo：这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targetMap);
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setDeviceId(rnssDevice.getId());
		deviceInfo.setDeviceType(rnssDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(rnssDevice.getUniqueId());
		deviceInfo.setDeviceModel(rnssDevice.getModel());
		deviceInfo.setDeviceNum(rnssDevice.getDeviceNum());
		deviceInfo.setDeptId(rnssDevice.getDeptId());
		deviceInfo.setSpecificity(rnssDevice.getSpecificity());
		deviceInfo.setTargetId(rnssDevice.getTargetId());
		deviceInfo.setTargetName(rnssDevice.getUniqueId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rnss_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("定位终端信息更新消息发送到kafka失败", e);
		}
		// messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, rnssDevice.getDeviceType(), rnssDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		return rnssDevice;
	}

	@Override
	public BdmRnssDevice insertRnssDevice(RnssDeviceRequest request) {
		BdmRnssDevice bdmRnssDevice = new BdmRnssDevice();
		QueryWrapper<BdmRnssDevice> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("unique_id", request.getUniqueId());
		BdmRnssDevice rnss = baseMapper.selectOne(queryWrapper);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		boolean exist = rnss != null;
		if (exist) {
			request.setId(rnss.getId());
			request.setCreateTime(new Date());
			request.setDeleted(0);
			bdmRnssDevice = this.init(request);
			// 同步到待分配终端对象实体表
			QueryWrapper<BdmVirtualTarget> wrapper = new QueryWrapper<>();
			wrapper.eq("number", bdmRnssDevice.getUniqueId());
			wrapper.last("LIMIT 1");
			BdmVirtualTarget latestRecord = bdmVirtualTargetService.getOne(wrapper);
			// 如果此时为空，应当向 bdm_virtual_target中新增数据。
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmRnssDevice, virtualTarget);
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmRnssDevice.getDeviceNum());
			virtualTarget.setNumber(bdmRnssDevice.getUniqueId());
			if (latestRecord == null) {
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
				boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
				if (saveRes) {
					log.info("bdmVirtualTargetService漏掉的 新增成功");
				} else {
					log.info("bdmVirtualTargetService漏掉的 新增失败:{}", virtualTarget);
				}
			} else {
				virtualTarget.setId(latestRecord.getId());
				boolean updateRes = bdmVirtualTargetService.updateById(virtualTarget);
				if (updateRes) {
					log.info("bdmVirtualTargetService 更新成功");
				} else {
					log.info("bdmVirtualTargetService 更新失败:{}", virtualTarget);
				}
			}
			QueryWrapper<BdmAbstractTarget> batWrapper = new QueryWrapper<>();
			batWrapper.eq("number", bdmRnssDevice.getUniqueId());
			batWrapper.last("LIMIT 1");
			BdmAbstractTarget batLatestRecord = bdmAbstractTargetService.getOne(batWrapper);
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmRnssDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmRnssDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmRnssDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			if (batLatestRecord == null) {
				boolean saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
				if (saveRes) {
					log.info("bdmAbstractTargetService漏掉的 新增成功");
				} else {
					log.info("bdmAbstractTargetService漏掉的 新增失败:{}", bdmAbstractTarget);
				}
			} else {
				boolean updateRes = bdmAbstractTargetService.updateById(bdmAbstractTarget);
				if (updateRes) {
					log.info("bdmAbstractTargetService 更新成功");
				} else {
					log.info("bdmAbstractTargetService 更新失败:{}", bdmAbstractTarget);
				}
			}
			bdmRnssDevice.setTargetId(virtualTarget.getId());
			baseMapper.update(bdmRnssDevice);
		} else {
			SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.RNSS_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			request.setId(idWorker.nextId());
			request.setCreateTime(new Date());
			bdmRnssDevice = this.init(request);
			bdmRnssDevice.setCreateAccount(AuthUtil.getUserAccount());
			if (bdmRnssDevice.getDeviceNum().length() == 0) {
				return null;
			}
			// 同步到待分配终端对象实体表
			// if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmRnssDevice, virtualTarget);
			virtualTarget.setId(null);
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmRnssDevice.getDeviceNum());
			virtualTarget.setNumber(bdmRnssDevice.getUniqueId());
			virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
			boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
			if (saveRes) {
				log.info("bdmVirtualTargetService 新增成功");
			} else {
				log.info("bdmVirtualTargetService 新增失败:{}", virtualTarget);
			}
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmRnssDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmRnssDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmRnssDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			bdmAbstractTarget.setCreateAccount(AuthUtil.getUserAccount());
			saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
			if (saveRes) {
				log.info("bdmAbstractTargetService 新增成功");
			} else {
				log.info("bdmAbstractTargetService 新增失败:{}", bdmAbstractTarget);
			}
			//}
			bdmRnssDevice.setTargetId(virtualTarget.getId());
			bdmRnssDevice.setId(null);
			this.rnssDeviceMapper.insert(bdmRnssDevice);
		}

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			// 更新存在且未绑定的物联网卡信息
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), bdmRnssDevice.getId(), bdmRnssDevice.getDeviceType());
		}
		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			// 跟新bdm_terminal
			terminalService.updateFormalByDeviceSeq(request.getUniqueId());
			// 更新bdm_device_code
			deviceCodeService.updateActivated(request.getDeviceNum());
		}

		BdmRnssDevice rnssDevice = this.getBaseMapper().selectById(bdmRnssDevice.getId());

		Map<String, String> map = new HashMap<>();
		String key = rnssDevice.getDeviceType() + "-" + rnssDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", rnssDevice.getUniqueId());
		innerMap.put("deviceNum", rnssDevice.getDeviceNum());
		innerMap.put("category", rnssDevice.getCategory());
		innerMap.put("deviceType", rnssDevice.getDeviceType());
		innerMap.put("deptId", rnssDevice.getDeptId());
		innerMap.put("iotProtocol", rnssDevice.getIotProtocol());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		// todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			Map<String, String> targetMap = new HashMap<>();
			String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + rnssDevice.getId();
			Map<String, Object> targetInnerMap = new HashMap<>();
			targetInnerMap.put("targetName", rnssDevice.getUniqueId());
			targetInnerMap.put("targetType", 0);
			targetInnerMap.put("deptId", rnssDevice.getDeptId());
			try {
				map.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			// todo：这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targetMap);
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setDeviceId(rnssDevice.getId());
		deviceInfo.setDeviceType(rnssDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(rnssDevice.getUniqueId());
		deviceInfo.setDeviceModel(rnssDevice.getModel());
		deviceInfo.setDeviceNum(rnssDevice.getDeviceNum());
		deviceInfo.setDeptId(rnssDevice.getDeptId());
		deviceInfo.setSpecificity(rnssDevice.getSpecificity());
		deviceInfo.setTargetId(rnssDevice.getTargetId());
		deviceInfo.setTargetName(rnssDevice.getUniqueId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rnss_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("定位终端信息更新消息发送到kafka失败", e);
		}
		// messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, rnssDevice.getDeviceType(), rnssDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		return rnssDevice;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmRnssDevice update(RnssDeviceRequest request) {
		BdmRnssDevice bdmRnssDevice = this.init(request);
		this.rnssDeviceMapper.update(bdmRnssDevice);

		iotCardService.updateDeviceIdById(request.getId(), request.getDeviceType(), request.getNumbers());

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), request.getId(), request.getDeviceType());
		}

		BdmRnssDevice rnssDevice = this.getBaseMapper().selectById(bdmRnssDevice.getId());

		// 未绑定的非新设备更新抽象终端表 TODO 是否需要判断是否已经绑定
		// if (rnssDevice.getSpecificity() != BdmTypeEnum.NEW.getValue()) {
		BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
		BeanUtils.copyProperties(rnssDevice, abstractDevice);
		bdmAbstractDeviceService.updateById(abstractDevice);
		//}

		// 更新Redis缓存
		String key = rnssDevice.getDeviceType() + "-" + rnssDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", rnssDevice.getUniqueId());
		innerMap.put("deviceNum", rnssDevice.getDeviceNum());
		innerMap.put("category", rnssDevice.getCategory());
		innerMap.put("deviceType", rnssDevice.getDeviceType());
		innerMap.put("deptId", rnssDevice.getDeptId());
		innerMap.put("iotProtocol", rnssDevice.getIotProtocol());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		// todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setDeviceId(rnssDevice.getId());
		deviceInfo.setDeviceType(rnssDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(rnssDevice.getUniqueId());
		deviceInfo.setDeviceModel(rnssDevice.getModel());
		deviceInfo.setDeviceNum(rnssDevice.getDeviceNum());
		deviceInfo.setDeptId(rnssDevice.getDeptId());
		deviceInfo.setSpecificity(rnssDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rnss_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("定位终端信息更新消息发送到kafka失败", e);
		}
		// messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_UPDATE, rnssDevice.getDeviceType(), rnssDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return rnssDevice;
	}

	@Override
	public BdmRnssDevice updateRnssDevice(RnssDeviceRequest request) {
		BdmRnssDevice bdmRnssDevice = this.init(request);
		this.rnssDeviceMapper.update(bdmRnssDevice);

		iotCardService.updateDeviceIdById(request.getId(), request.getDeviceType(), request.getNumbers());

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), request.getId(), request.getDeviceType());
		}

		BdmRnssDevice rnssDevice = this.getBaseMapper().selectById(bdmRnssDevice.getId());

		// 更新Redis缓存
		String key = rnssDevice.getDeviceType() + "-" + rnssDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", rnssDevice.getUniqueId());
		innerMap.put("deviceNum", rnssDevice.getDeviceNum());
		innerMap.put("category", rnssDevice.getCategory());
		innerMap.put("deviceType", rnssDevice.getDeviceType());
		innerMap.put("deptId", rnssDevice.getDeptId());
		innerMap.put("iotProtocol", rnssDevice.getIotProtocol());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		// todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setDeviceId(rnssDevice.getId());
		deviceInfo.setDeviceType(rnssDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(rnssDevice.getUniqueId());
		deviceInfo.setDeviceModel(rnssDevice.getModel());
		deviceInfo.setDeviceNum(rnssDevice.getDeviceNum());
		deviceInfo.setDeptId(rnssDevice.getDeptId());
		deviceInfo.setSpecificity(rnssDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rnss_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("定位终端信息更新消息发送到kafka失败", e);
		}
		// messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_UPDATE, rnssDevice.getDeviceType(), rnssDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return rnssDevice;
	}

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	@Override
	public boolean deleteById(Long id) {
		return this.rnssDeviceMapper.deleteById(id) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {
		QueryWrapper<BdmRnssDevice> wrapper = new QueryWrapper<>();
		wrapper.in("id", ids);
		List<BdmRnssDevice> list = this.rnssDeviceMapper.selectList(wrapper);

		List<String> deviceNumList = new ArrayList<>();
		List<String> uniqueIdList = new ArrayList<>();

		if (!list.isEmpty()) {
			// 查询 deviceNum 列表
			deviceNumList = list.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
				.map(BdmRnssDevice::getDeviceNum)
				.collect(Collectors.toList());

			// 查询 uniqueId 列表
			uniqueIdList = list.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
				.map(BdmRnssDevice::getUniqueId)
				.collect(Collectors.toList());
		}

		boolean result = this.rnssDeviceMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.RNSS_DEVICE_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_DEVICE + ":" + key);
			}
			this.iotCardService.deleteByDeviceIds(ids, DeviceTypeEnum.RNSS.getSymbol());

			if (!uniqueIdList.isEmpty()) {
				// 跟新bdm_terminal
				terminalService.updateFormalByDeviceSeqs(uniqueIdList);
			}
			if (!deviceNumList.isEmpty()) {
				// 更新bdm_device_code
				deviceCodeService.updateActivatedByDeviceNum(deviceNumList);
			}

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.DELETE);
			Long lastId = ids[ids.length - 1];
			deviceInfo.setDeviceId(lastId);
			deviceInfo.setDeviceIds(CollUtil.newHashSet(ids));
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rnss_device", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("定位终端信息更新消息发送到kafka失败", e);
			}
			// messageClient
			try {
				List<Object> terminals = new ArrayList<>(list);
				messageClient.terminalBatch(CommonConstant.OPER_TYPE_DELETE, BaseInfoConstants.RNSS_DEVICE_TYPE, terminals);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}

			// 同步更新抽象监控对象实体表
			bdmAbstractDeviceService.deleteByIds(ids);
			// 同步更新待分配终端对象实体表 删除
			if (!uniqueIdList.isEmpty()) {
				bdmVirtualTargetService.updateByUniqueId(uniqueIdList);

				QueryWrapper<BdmVirtualTarget> virtualTargetQueryWrapper = new QueryWrapper<>();
				if (!uniqueIdList.isEmpty()) {
					MybatisPlusWrapperUitls.longListIn(virtualTargetQueryWrapper, "number", uniqueIdList);
				}
				virtualTargetQueryWrapper.select("id");
				List<Long> idList = bdmVirtualTargetService.getBaseMapper()
					.selectList(virtualTargetQueryWrapper)
					.stream()
					.map(BdmVirtualTarget::getId)
					.collect(Collectors.toList());

				// 同步更新抽象监控对象实体表
				if (!idList.isEmpty()) {
					bdmAbstractTargetService.deleteByIds(idList.toArray(new Long[0]));
					for (Long id : idList) {
						String targetKey = BaseInfoConstants.VIRTUAL_TARGET_TYPE + "-" + id;
						redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + targetKey);
					}
				}
			}
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<RnssDeviceRequest> importExcel(List<RnssDeviceRequest> list, Long userId) {
		List<RnssDeviceRequest> duplicateRequests = getDuplicateRequests(list);
		// 假设requests和duplicateRequests已经被正确初始化
		List<RnssDeviceRequest> requests = new ArrayList<>(list);
		// 移除duplicateRequests中的所有元素
		requests.removeIf(request -> duplicateRequests.contains(request));
		if (!requests.isEmpty()) {
			List<RnssDeviceRequest> filteredList = processRequests(requests, userId, duplicateRequests);
			if (!filteredList.isEmpty()) {
				List<Long> arrayList = updateDatabaseAndCache(filteredList);
				sendNotifications(filteredList, arrayList);
			}
		}
		return duplicateRequests;
	}

	/**
	 * 重复数据
	 *
	 * @param list
	 * @return
	 */
	public List<RnssDeviceRequest> getDuplicateRequests(List<RnssDeviceRequest> list) {
		// 1. 统计每个number出现的次数
		List<String> numberList = list.stream()
			.filter(Objects::nonNull)
			.filter(item -> !StringUtils.isEmpty(item.getNumbers()))
			.map(RnssDeviceRequest::getNumbers)
			.collect(Collectors.toList());

		// 2. 过滤出重复的number
		List<String> duplicates = numberList.stream()
			.filter(number -> Collections.frequency(numberList, number) > 1)
			.distinct()
			.collect(Collectors.toList());

		List<String> uniqueIdList = list.stream()
			.map(RnssDeviceRequest::getUniqueId)
			.collect(Collectors.toList());

		List<String> duplicateUniqueIds = uniqueIdList.stream()
			.filter(id -> Collections.frequency(uniqueIdList, id) > 1)
			.distinct()
			.collect(Collectors.toList());

		return list.stream()
			.filter(rnssDeviceRequest -> duplicates.contains(rnssDeviceRequest.getNumbers()) || duplicateUniqueIds.contains(rnssDeviceRequest.getUniqueId()))
			.peek(rnssDeviceRequest -> {
				if (duplicates.contains(rnssDeviceRequest.getNumbers())) {
					rnssDeviceRequest.setMsg("导入的数据物联网卡号重复");
				}
				if (duplicateUniqueIds.contains(rnssDeviceRequest.getUniqueId())) {
					rnssDeviceRequest.setMsg("导入的数据序列号重复");
				}
			})
			.collect(Collectors.toList());
	}

	/**
	 * 数据处理
	 */
	private List<RnssDeviceRequest> processRequests(List<RnssDeviceRequest> requests, Long userId, List<RnssDeviceRequest> duplicateRequests) {
		// 按部门ID分组
		Map<Long, List<RnssDeviceRequest>> groupedByDept = requests.stream()
			.collect(Collectors.groupingBy(RnssDeviceRequest::getDeptId));

		List<RnssDeviceRequest> filterList = new ArrayList<>();

		for (Map.Entry<Long, List<RnssDeviceRequest>> entry : groupedByDept.entrySet()) {
			// TODO 终端赋码号不存在！和 序列号已存在！可连表查进行判断 检查输入的赋码号的合法性
			Map<String, BdmDeviceLedger> deviceLedgerMap = getDeviceLedgers(entry.getKey())
				.stream()
				.collect(Collectors.toMap(BdmDeviceLedger::getDeviceNum, ledger -> ledger));

			List<RnssDeviceRequest> requestsForDept = entry.getValue();

			// 过滤出该部门下设备编号存在于deviceLedgers中的请求
			List<RnssDeviceRequest> validRequestsForDept = requestsForDept.stream()
				.filter(request -> {
					if (!BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
						return true;
					} else {
						return isValidRequest(request, deviceLedgerMap);
					}
				})
				.collect(Collectors.toList());

			filterList.addAll(validRequestsForDept);

			// 检查输入的赋码号的合法性
			requestsForDept.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()) && !deviceLedgerMap.containsKey(request.getDeviceNum()))
				.peek(request -> request.setMsg("终端赋码号不存在！"))
				.forEach(duplicateRequests::add);
		}

		if (!filterList.isEmpty()) {

			List<String> uniqueIdList = filterList.stream()
				.filter(Objects::nonNull)
				.map(RnssDeviceRequest::getUniqueId)
				.collect(Collectors.toList());

			QueryWrapper<BdmRnssDevice> wrapper = new QueryWrapper<>();
			wrapper.eq("deleted", 0);
			if (!uniqueIdList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(wrapper, "unique_id", uniqueIdList);
			}
			wrapper.select("unique_id");
			// 序列号判重
			List<String> uniqueIds = baseMapper.selectList(wrapper)
				.stream()
				.map(BdmRnssDevice::getUniqueId)
				.collect(Collectors.toList());

			List<RnssDeviceRequest> resultList = filterList.stream()
				.filter(rnssDeviceRequest ->
					rnssDeviceRequest.getUniqueId() != null && !uniqueIds.contains(rnssDeviceRequest.getUniqueId()))
				.collect(Collectors.toList());
			filterList.stream()
				.filter(request -> !resultList.contains(request))
				.peek(request -> request.setMsg("序列号已存在！"))
				.forEach(duplicateRequests::add);
			filterList = resultList;

			if (!resultList.isEmpty()) {

				List<String> numberedList = resultList.stream()
					.filter(Objects::nonNull)
					.map(RnssDeviceRequest::getNumbers)
					.collect(Collectors.toList());

				List<String> numberList = new ArrayList<>();

				if (!numberedList.isEmpty()) {
					log.info("[importExcel]终端信息导入，将要判断是否是底座登录");
					DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
					boolean isCE = ceTokenUtil.isCELogin();
					if (isCE) {
						log.info("[importExcel]终端信息导入，国能底座登录");
						// 如果是国能底座，则查询数据权限范围内的物联网卡信息
						String account = ceDataAuth.getAccount();
						String deptArrayStr = ceDataAuth.getOrgListStr();
						numberList = iotCardService.queryIotCE(numberedList, account, deptArrayStr);
						log.info("[importExcel]终端信息导入，获取数据权限范围内的物联网卡数量为：{}", numberList == null ? 0 : numberList.size());
					} else {
						log.info("[importExcel]终端信息导入，位置平台登录");
						// 如果不是国能底座，则按照位置平台的逻辑查询
						userId = AuthUtil.isAdministrator() ? null : userId;
						// 物联网卡验证
						numberList = iotCardService.queryIot(numberedList, userId);
						log.info("[importExcel]终端信息导入，获取位置平台数据范围内的物联网卡数量为：{}", numberList == null ? 0 : numberList.size());
					}
				}

				final List<String> finalNumberList = numberList;

				List<RnssDeviceRequest> filteredList = resultList.stream()
					.filter(rnssDeviceRequest ->
						rnssDeviceRequest.getNumbers() == null || finalNumberList.contains(rnssDeviceRequest.getNumbers()))
					.collect(Collectors.toList());
				// 将除了filteredList的数据加到duplicateRequests
				resultList.stream()
					.filter(request -> !filteredList.contains(request))
					.peek(request -> request.setMsg("该物联网卡号不存在或已绑定！"))
					.forEach(duplicateRequests::add);
				filterList = filteredList;
			}
		}

		return filterList;
	}

	/**
	 * 查询出库到本单位数据
	 */
	private List<BdmDeviceLedger> getDeviceLedgers(Long deptId) {
		return deviceLedgerService.getBaseMapper()
			.selectList(new QueryWrapper<BdmDeviceLedger>()
				.eq("device_type", BaseInfoConstants.RNSS_DEVICE_TYPE)
				.eq("user_dept_id", deptId));
	}

	/**
	 * 新设备给设置正确的赋码信息
	 */
	private boolean isValidRequest(RnssDeviceRequest request, Map<String, BdmDeviceLedger> targetMap) {
		BdmDeviceLedger deviceLedger = targetMap.get(request.getDeviceNum());
		if (deviceLedger != null) {
			request.setModel(deviceLedger.getModel());
			request.setImei(deviceLedger.getImei());
			request.setVendor(deviceLedger.getVendor());
			request.setUniqueId(deviceLedger.getUniqueId());
			request.setBdChipSn(deviceLedger.getBdChipSn());
			request.setDeviceType(deviceLedger.getDeviceType());
			request.setCategory(deviceLedger.getCategory());
			return true;
		}
		return false;
	}

	/**
	 * 更新数据库和进行数据redis缓存
	 */
	private List<Long> updateDatabaseAndCache(List<RnssDeviceRequest> filteredList) {

		SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.RNSS_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		Map<String, BdmRnssDevice> rnssMap = baseMapper.selectList(new QueryWrapper<BdmRnssDevice>().eq("deleted", 1))
			.stream()
			.collect(Collectors.toMap(BdmRnssDevice::getUniqueId, RnssDevice -> RnssDevice));
		// 用于判断恢复被删除的设备时，判断是执行新增还是恢复操作
		Map<String, BdmVirtualTarget> allVirtualTargetMap = bdmVirtualTargetService.list()
			.stream()
			.collect(Collectors.toMap(BdmVirtualTarget::getNumber, VirtualTarget -> VirtualTarget, (existing, replacement) -> existing));
		Map<String, BdmAbstractTarget> allBatMap = bdmAbstractTargetService.list()
			.stream()
			.collect(Collectors.toMap(BdmAbstractTarget::getNumber, bat -> bat, (existing, replacement) -> existing));
		// 创建一个 Map 来存储所有的键值对
		List<TerminalIotRequest> terminalIotRequests = new ArrayList<>();
		Map<String, String> map = new HashMap<>();
		Map<String, String> targrtMap = new HashMap<>();
		List<Long> arrayList = new ArrayList<>();
		List<BdmVirtualTarget> virtualTargets = new ArrayList<>();
		List<BdmAbstractDevice> abstractDevices = new ArrayList<>();
		List<BdmAbstractTarget> abstractTargets = new ArrayList<>();

		// 在处理filteredList之前，收集所有设备的序列号
		Set<String> newDeviceUniqueIds = filteredList.stream()
			.map(RnssDeviceRequest::getUniqueId)
			.collect(Collectors.toSet());

		Map<String, BdmVirtualTarget> virtualTargetMap = new HashMap<>();
		// 一次性查询所有匹配的BdmVirtualTarget记录
		if (!newDeviceUniqueIds.isEmpty()) {
			List<BdmVirtualTarget> allVirtualTargets = bdmVirtualTargetService.list(
				new QueryWrapper<BdmVirtualTarget>().in("number", newDeviceUniqueIds).select("id", "number")
			);
			// 将查询结果存储在一个Map中，以便后续快速查找
			virtualTargetMap = allVirtualTargets.stream().collect(Collectors.toMap(BdmVirtualTarget::getNumber, Function.identity()));
		}

		for (RnssDeviceRequest request : filteredList) {
			BdmRnssDevice device = rnssMap.get(request.getUniqueId());
			// device为空新增，不为空修改
			long deviceId = (device == null) ? idWorker.nextId() : device.getId();
			request.setId(deviceId);
			request.setDeleted(0);
			// 设备赋码
			if (device == null) {
				if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
					R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
					if (r1.isSuccess()) {
						request.setDeviceNum(r1.getData());
					}
				}
				if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
					R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
					if (r1.isSuccess()) {
						request.setDeviceNum(r1.getData());
					}
				}
			} else {
				request.setDeviceNum(device.getDeviceNum());
			}
			// 新设备的定位模式单独处理
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			BdmRnssDevice bdmRnssDevice = this.init(request);

			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			if (device == null) {
				// 同步到待分配终端对象实体表
				BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
				BeanUtils.copyProperties(bdmRnssDevice, virtualTarget, "id");
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setTargetType(0);
				virtualTarget.setDeviceCode(bdmRnssDevice.getDeviceNum());
				virtualTarget.setNumber(bdmRnssDevice.getUniqueId());
				virtualTargets.add(virtualTarget);

				// TODO 同步到抽象监控对象实体表 abstractTargets
				bdmAbstractTarget.setId(virtualTarget.getId());
				bdmAbstractTarget.setNumber(bdmRnssDevice.getUniqueId());
				bdmAbstractTarget.setName(bdmRnssDevice.getUniqueId());
				bdmAbstractTarget.setTargetType(0);
				bdmAbstractTarget.setDeptId(bdmRnssDevice.getDeptId());
				bdmAbstractTarget.setCategory(0);
				bdmAbstractTarget.setDeleted(0);
				abstractTargets.add(bdmAbstractTarget);
				bdmRnssDevice.setTargetId(virtualTarget.getId());
				this.rnssDeviceMapper.insertRnss(bdmRnssDevice);
				// 同步到抽象监控对象实体表
				BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
				BeanUtils.copyProperties(bdmRnssDevice, abstractDevice);

				abstractDevice.setTargetType(0);
				abstractDevice.setDeleted(0);
				abstractDevices.add(abstractDevice);
			} else {
				// 同步到待分配终端对象实体表
				BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
				BeanUtils.copyProperties(bdmRnssDevice, virtualTarget, "id");
				virtualTarget.setTargetType(0);
				virtualTarget.setDeleted(0);
				virtualTarget.setDeviceCode(bdmRnssDevice.getDeviceNum());
				virtualTarget.setNumber(bdmRnssDevice.getUniqueId());
				if (allVirtualTargetMap.containsKey(device.getUniqueId())) {
					virtualTarget.setId(virtualTargetMap.get(bdmRnssDevice.getUniqueId()).getId());
					bdmVirtualTargetService.updateById(virtualTarget);
				} else {
					virtualTarget.setId(targetId.nextId());
					virtualTargets.add(virtualTarget);
				}
				// TODO 更新到抽象监控对象实体表
				bdmAbstractTarget.setNumber(bdmRnssDevice.getUniqueId());
				bdmAbstractTarget.setName(bdmRnssDevice.getUniqueId());
				bdmAbstractTarget.setTargetType(0);
				bdmAbstractTarget.setDeptId(bdmRnssDevice.getDeptId());
				bdmAbstractTarget.setCategory(0);
				bdmAbstractTarget.setDeleted(0);
				bdmAbstractTarget.setId(virtualTarget.getId());
				if (allBatMap.containsKey(device.getUniqueId())) {
					bdmAbstractTargetService.updateById(bdmAbstractTarget);
				} else {
					abstractTargets.add(bdmAbstractTarget);
				}
				bdmRnssDevice.setTargetId(virtualTarget.getId());
				baseMapper.updateById(bdmRnssDevice);
				// 同步到抽象监控对象实体表
				BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
				BeanUtils.copyProperties(bdmRnssDevice, abstractDevice);
				abstractDevice.setTargetType(0);
				abstractDevice.setDeleted(0);
				bdmAbstractDeviceService.updateById(abstractDevice);
			}
			request.setId(bdmRnssDevice.getId());
			arrayList.add(bdmRnssDevice.getId());

			if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
				TerminalIotRequest terminalIotRequest = new TerminalIotRequest();
				terminalIotRequest.setId(bdmRnssDevice.getId());
				terminalIotRequest.setNumber(request.getNumbers());
				terminalIotRequest.setDeviceType(BaseInfoConstants.RNSS_DEVICE_TYPE);
				terminalIotRequests.add(terminalIotRequest);
			}

			String key = bdmRnssDevice.getDeviceType() + "-" + bdmRnssDevice.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", bdmRnssDevice.getUniqueId());
			innerMap.put("deviceNum", bdmRnssDevice.getDeviceNum());
			innerMap.put("category", bdmRnssDevice.getCategory());
			innerMap.put("deviceType", bdmRnssDevice.getDeviceType());
			innerMap.put("deptId", bdmRnssDevice.getDeptId());
			innerMap.put("iotProtocol", bdmRnssDevice.getIotProtocol());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}

			// 待分配终端对象 缓存到 baseinfo_target
			// if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
			String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + bdmAbstractTarget.getId();
			Map<String, Object> targetInnerMap = new HashMap<>();
			targetInnerMap.put("targetName", bdmAbstractTarget.getNumber());
			targetInnerMap.put("targetType", bdmAbstractTarget.getTargetType());
			targetInnerMap.put("deptId", bdmAbstractTarget.getDeptId());
			try {
				targrtMap.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//}
		}
		if (!virtualTargets.isEmpty()) {
			MapperUtils.splitListByCapacity(virtualTargets, MapperUtils.DEFAULT_CAPACITY)
				.forEach(virtualTargetList -> this.bdmVirtualTargetService.insertBatch(virtualTargetList));
		}
		if (!abstractDevices.isEmpty()) {
			MapperUtils.splitListByCapacity(abstractDevices, MapperUtils.DEFAULT_CAPACITY)
				.forEach(abstractDeviceList -> this.bdmAbstractDeviceService.insertBatch(abstractDeviceList));
		}
		// 新设备同步到抽象监控对象实体表
		if (!abstractTargets.isEmpty()) {
			MapperUtils.splitListByCapacity(abstractTargets, MapperUtils.DEFAULT_CAPACITY)
				.forEach(abstractTargetList -> this.bdmAbstractTargetService.insertBatch(abstractTargetList));
		}

		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
		if (!targrtMap.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targrtMap);
		}

		if (!terminalIotRequests.isEmpty()) {
			this.iotCardService.updateDeviceId(terminalIotRequests);
		}

		// 查询 deviceNum 列表
		List<String> deviceNumList = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(RnssDeviceRequest::getDeviceNum)
			.collect(Collectors.toList());

		// 查询 uniqueId 列表
		List<String> uniqueIdList = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(RnssDeviceRequest::getUniqueId)
			.collect(Collectors.toList());

		if (!uniqueIdList.isEmpty()) {
			// 跟新bdm_terminal
			terminalService.updateFormalByDeviceSeqs(uniqueIdList);
		}
		if (!deviceNumList.isEmpty()) {
			// 更新bdm_device_code
			deviceCodeService.updateActivatedByDeviceNum(deviceNumList);
		}
		return arrayList;
	}

	/**
	 * 发送消息
	 */
	private void sendNotifications(List<RnssDeviceRequest> filteredList, List<Long> arrayList) {
		RnssDeviceRequest request = filteredList.get(filteredList.size() - 1);
		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.IMPORT);
		deviceInfo.setDeviceId(request.getId());
		deviceInfo.setDeviceType(request.getDeviceType());
		deviceInfo.setDeviceModel(request.getModel());
		deviceInfo.setDeviceNum(request.getDeviceNum());
		deviceInfo.setDeviceUniqueId(request.getUniqueId());
		deviceInfo.setDeptId(request.getDeptId());
		Set<Long> ids = filteredList.stream().map(RnssDeviceRequest::getId).collect(Collectors.toSet());
		deviceInfo.setDeviceIds(ids);
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_rnss_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("定位终端信息更新消息发送到kafka失败", e);
		}
		QueryWrapper<BdmRnssDevice> wrapper = new QueryWrapper<>();
		if (!arrayList.isEmpty()) {
			MybatisPlusWrapperUitls.longListIn(wrapper, "id", arrayList);
			List<BdmRnssDevice> rnssDevices = baseMapper.selectList(wrapper);
			// messageClient
			List<Object> terminals = new ArrayList<>(rnssDevices);
			try {
				messageClient.terminalBatch(CommonConstant.OPER_TYPE_ADD, BaseInfoConstants.RNSS_DEVICE_TYPE, terminals);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateByWorkerId(Long id, Integer targetType) {
		this.rnssDeviceMapper.updateByWorkerId(id, targetType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateByVehicleId(Long id, Integer targetType) {
		this.rnssDeviceMapper.updateByVehicleId(id, targetType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void connectWorkerTerminal(List<PersonTerminalRequest> requests, Long id, Integer targetType, String targetName, Long deptId) {
		Map<String, String> map = new HashMap<>();
		for (PersonTerminalRequest request : requests) {
			this.rnssDeviceMapper.connectWorkerTerminal(request, id, targetType, targetName, deptId);

			String key = request.getDeviceType() + "-" + request.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", request.getUniqueId());
			innerMap.put("deviceNum", request.getDeviceNum());
			innerMap.put("category", request.getCategory());
			innerMap.put("deviceType", request.getDeviceType());
			innerMap.put("deptId", request.getDeptId());
			innerMap.put("iotProtocol", request.getIotProtocol());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				log.error(e.getMessage());
			}
		}
		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void connectVehicleTerminal(List<VehicleTerminalRequest> requests, Long id, Integer targetType, String targetName, Long deptId) {
		Map<String, String> map = new HashMap<>();
		for (VehicleTerminalRequest request : requests) {
			this.rnssDeviceMapper.connectVehicleTerminal(request, id, targetType, targetName, deptId);

			String key = request.getDeviceType() + "-" + request.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", request.getUniqueId());
			innerMap.put("deviceNum", request.getDeviceNum());
			innerMap.put("category", request.getCategory());
			innerMap.put("deviceType", request.getDeviceType());
			innerMap.put("deptId", request.getDeptId());
			innerMap.put("iotProtocol", request.getIotProtocol());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				log.error(e.getMessage());
			}
		}
		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
	}

	@Override
	public List<PersonNoBingResponse> selectBindByWorkId(Long id, Integer targetType) {
		return this.rnssDeviceMapper.selectBindByWorkId(id, targetType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteByTargetIds(Long[] ids, Integer targetType) {
		this.rnssDeviceMapper.deleteByTargetIds(ids, targetType);
	}

	@Override
	public List<WorkerBingResponse> selectByWorkId(Long id, Integer targetType) {
		return this.rnssDeviceMapper.selectByWorkId(id, targetType);
	}

	@Override
	public int unbind(Long id, Integer targetType) {
		return this.rnssDeviceMapper.unbind(id, targetType);
	}

	@Override
	public int bindTarget(Long deviceId, Long targetId, Integer targetType, String targetName) {
		return this.rnssDeviceMapper.bindTarget(deviceId, targetId, targetType, targetName);
	}

	@Override
	public long countByUserRole(DataAuthCE ceDataAuth) {
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return baseMapper.countByUserRole(response.getAccount(), response.getOrgList());
	}

	@Override
	public void updateDept(Long id, Integer targetType, Long deptId) {
		List<BdmRnssDevice> rnssDevices = baseMapper.selectList(
			new QueryWrapper<BdmRnssDevice>()
				.eq("target_id", id)
				.eq("target_type", targetType)
				.eq("deleted", 0)
				.select("id,unique_id,device_type,dept_id,category,device_num,iot_protocol")
		);
		if (!rnssDevices.isEmpty()) {
			baseMapper.updateDept(id, targetType, deptId);

			Map<String, String> map = new HashMap<>();
			for (BdmRnssDevice rnssDevice : rnssDevices) {
				String key = rnssDevice.getDeviceType() + "-" + rnssDevice.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("uniqueId", rnssDevice.getUniqueId());
				innerMap.put("deviceNum", rnssDevice.getDeviceNum());
				innerMap.put("category", rnssDevice.getCategory());
				innerMap.put("deviceType", rnssDevice.getDeviceType());
				innerMap.put("deptId", deptId);
				innerMap.put("iotProtocol", rnssDevice.getIotProtocol());
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
			if (!map.isEmpty()) {
				redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
			}
		}
	}

	/**
	 * 对象字段初始化
	 *
	 * @param request
	 * @return
	 */
	public BdmRnssDevice init(RnssDeviceRequest request) {
		BdmRnssDevice rnssDevice = new BdmRnssDevice();
		rnssDevice.setId(request.getId());
		rnssDevice.setUniqueId(request.getUniqueId() != null ? request.getUniqueId() : "");
		rnssDevice.setImei(request.getImei() != null ? request.getImei() : "");
		rnssDevice.setModel(request.getModel() != null ? request.getModel() : "");
		rnssDevice.setVendor(request.getVendor() != null ? request.getVendor() : "");
		rnssDevice.setBdChipSn(request.getBdChipSn() != null ? request.getBdChipSn() : "");
		rnssDevice.setDeviceType(DeviceTypeEnum.RNSS.getSymbol());
		rnssDevice.setSpecificity(request.getSpecificity() != null ? request.getSpecificity() : 0);
		rnssDevice.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		rnssDevice.setCategory(request.getCategory() != null ? request.getCategory() : 0);
		rnssDevice.setDeviceNum(request.getDeviceNum() != null ? request.getDeviceNum() : "");
		rnssDevice.setChannelNum(request.getChannelNum() != null ? request.getChannelNum() : 0);
		rnssDevice.setInstalldate(request.getInstalldate() != null ? request.getInstalldate() : null);
		rnssDevice.setCreateTime(request.getCreateTime() != null ? request.getCreateTime() : null);
		rnssDevice.setUpdateTime(request.getUpdateTime() != null ? request.getUpdateTime() : new Date());
		rnssDevice.setDeleted(request.getDeleted() != null ? request.getDeleted() : 0);
		rnssDevice.setScenario(request.getScenario() != null ? request.getScenario() : 0);
		rnssDevice.setDomain(request.getDomain() != null ? request.getDomain() : 0);
		rnssDevice.setGnssMode(request.getGnssMode() != null ? request.getGnssMode() : 0);
		rnssDevice.setIotProtocol(request.getIotProtocol() != null ? request.getIotProtocol() : IotProtocolEnum.JT808.getCode());
		rnssDevice.setTerminalId(request.getTerminalId() != null ? request.getTerminalId() : "");
		rnssDevice.setAssetType(request.getAssetType() != null ? request.getAssetType() : 0);
		rnssDevice.setOwnDeptType(request.getOwnDeptType() != null ? request.getOwnDeptType() : 0);
		rnssDevice.setOwnDeptName(request.getOwnDeptName() != null ? request.getOwnDeptName() : "");
		return rnssDevice;
	}
}
