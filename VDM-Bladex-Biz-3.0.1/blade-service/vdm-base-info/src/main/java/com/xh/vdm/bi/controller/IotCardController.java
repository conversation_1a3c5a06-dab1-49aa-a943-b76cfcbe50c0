package com.xh.vdm.bi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.service.IotCardService;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.utils.DictUtil;
import com.xh.vdm.bi.vo.request.IotCardRequest;
import com.xh.vdm.bi.vo.response.BdmIotCardExportResponse;
import com.xh.vdm.bi.vo.response.IotCardResponse;
import com.xh.vdm.biapi.entity.BdmIotCard;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springblade.common.annotation.Log;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.minio.MinioService;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.EasyExcelUtils;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 物联网卡管理
 */
@RestController
@RequestMapping("/iotCard")
@Slf4j
public class IotCardController {
	/**
	 * 服务对象
	 */
	@Resource
	private IotCardService iotCardService;
	@Resource
	private IDictBizClient dictBizClient;
	@Resource
	private MinioService minioService;
	@Resource
	private CETokenUtil ceTokenUtil;
	@Resource
	private DictUtil dictUtil;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;


	/**
	 * 分页查询
	 *
	 * @param iotCardRequest 筛选条件
	 * @return 查询结果
	 */
	@PostMapping("/list")
	public R<IPage<BdmIotCardExportResponse>> queryByPage(@RequestBody IotCardRequest iotCardRequest,  BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		//AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		IPage<BdmIotCardExportResponse> page = this.iotCardService.queryByPage(iotCardRequest, ceDataAuth);
		enrichDataWithDict(page.getRecords());
		return R.data(page);
	}

	/**
	 * 通过主键查询单条数据
	 *
	 * @param id 主键
	 * @return 单条数据
	 */
	@GetMapping("{id}")
	public R<BdmIotCard> queryById(@PathVariable("id") Long id) {
		return R.data(this.iotCardService.queryById(id));
	}

	/**
	 * 新增数据
	 *
	 * @param iotCardRequest 实体
	 * @return 新增结果
	 */
	@Log(menu = "物联网卡管理", operation = Operation.INSERT, objectType = ObjectType.IOT)
	@PostMapping("/save")
	public R add(@RequestBody IotCardRequest iotCardRequest, BladeUser user) {
		BdmIotCard iotCard = this.iotCardService.insert(iotCardRequest);
		if (iotCard != null) {
			return R.data(ResultCode.SUCCESS.getCode(), iotCard.getId().toString(), "新增成功");
		}
		return R.fail(ResultCode.FAILURE, "");
	}

	/**
	 * 编辑数据
	 *
	 * @param iotCardRequest 实体
	 * @return 编辑结果
	 */
	@Log(menu = "物联网卡管理", operation = Operation.UPDATE, objectType = ObjectType.IOT)
	@PostMapping("/update")
	public R edit(@RequestBody IotCardRequest iotCardRequest, BladeUser user) {
		BdmIotCard iotCardInDB = this.iotCardService.getBaseMapper().selectById(iotCardRequest.getId());
		BdmIotCard iotCard = this.iotCardService.update(iotCardRequest);
		if (iotCard != null) {
			String result = new CompareUtils<BdmIotCard>().compare(iotCardInDB, iotCard);
			return R.data(ResultCode.SUCCESS.getCode(), result, "编辑成功");
		}
		return R.fail(ResultCode.FAILURE, "");
	}

	/**
	 * 删除数据
	 *
	 * @param id 主键
	 * @return 删除是否成功
	 */
	@GetMapping("/delete/{id}")
	public R<T> deleteById(@PathVariable Long id) {
		return R.status(this.iotCardService.deleteById(id));
	}

	/**
	 * 删除数据
	 *
	 * @param ids 主键
	 * @return 删除是否成功
	 */
	@Log(menu = "物联网卡管理", operation = Operation.DELETE, objectType = ObjectType.IOT)
	@GetMapping("/delete")
	public R<T> deleteByIds(@RequestParam("ids") Long[] ids, BladeUser user) {
		List<BdmIotCard> result = this.iotCardService.getBaseMapper().selectList(new QueryWrapper<BdmIotCard>().in("id", ids))
			.stream()
			.filter(bdmIotCard -> bdmIotCard.getDeviceId() != 0)
			.collect(Collectors.toList());
		if (!result.isEmpty()) {
			return R.fail(ResultCode.FAILURE, "先解绑终端");
		}
		return R.status(this.iotCardService.deleteByIds(ids));
	}

	/**
	 * 导出
	 */
	@PostMapping("/export")
	public R<String> export(@RequestBody IotCardRequest iotCardRequest, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		try {
			iotCardRequest.setCurrent(1);
			iotCardRequest.setSize(Integer.MAX_VALUE);
			IPage<BdmIotCardExportResponse> list = this.iotCardService.queryByPage(iotCardRequest, ceDataAuth);
			if (list.getRecords().isEmpty()) {
				return R.fail("没有数据可导出");
			}

			//从字典中获取数据
			enrichDataWithDict(list.getRecords());
			String menu = "物联网卡管理";
			try {
				// 使用ByteArrayOutputStream创建Excel文件
				ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
				EasyExcelUtils.exportExcelToStream(
					menu,
					outputStream,
					(List) list.getRecords(),
					iotCardRequest.getHeadNameList(),
					iotCardRequest.getColumnNameList(),
					BdmIotCardExportResponse.class
				);
				ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
				String minioFileUrl = minioService.uploadFile(menu, inputStream, outputStream.size());
				if (minioFileUrl != null) {
					return R.data(minioFileUrl);
				} else {
					return R.fail("文件上传失败");
				}
			} catch (Exception e) {
				log.info("文件上传失败 " + e.getMessage());
				return R.fail("文件上传失败");
			}
		} catch (Exception e) {
			log.error("导出失败", e);
			return R.fail("导出失败");
		}
	}

	/**
	 * 查询未绑定的物联网卡
	 *
	 * @return
	 */
	@GetMapping
	public R<List<IotCardResponse>> list(String number, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		return R.data(this.iotCardService.iotList(number, ceDataAuth));
	}

	/**
	 * 导入数据
	 */
	@Log(menu = "物联网卡管理", operation = Operation.IMPORT, objectType = ObjectType.IOT)
	@PostMapping("/importExcel")
	public R importExcel(@RequestBody List<IotCardRequest> list, BladeUser user) {
		if (list.isEmpty()) {
			return R.fail("数据为空");
		}
		List<IotCardRequest> result = this.iotCardService.importExcel(list);
		if (result.isEmpty()) {
			Map<Long, StringBuilder> deptIdMap = new HashMap<>();
			for (IotCardRequest request : list) {
				Long deptId = request.getDeptId();
				Long id = request.getId();
				if (deptIdMap.containsKey(deptId)) {
					deptIdMap.get(deptId).append("、").append(id);
				} else {
					deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
				}
			}
			return R.data(ResultCode.SUCCESS.getCode(), deptIdMap, "导入成功");
		} else {
			String filePath = "";
			String menu = "物联网卡管理";
			try {
				filePath = minioService.exportToMinIO(menu, result, IotCardRequest.class);
			} catch (IOException e) {
				log.error("导出错误数据失败");
			}
			List<IotCardRequest> filteredList = list.stream()
				.filter(item -> !result.contains(item))
				.collect(Collectors.toList());
			Map<Long, StringBuilder> deptIdMap = new HashMap<>();
			if (!filteredList.isEmpty()) {
				for (IotCardRequest request : filteredList) {
					Long deptId = request.getDeptId();
					Long id = request.getId();
					if (deptIdMap.containsKey(deptId)) {
						deptIdMap.get(deptId).append("、").append(id);
					} else {
						deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
					}
				}
			}
			return R.data(207, deptIdMap, filePath);
		}
	}

	private void enrichDataWithDict(List<BdmIotCardExportResponse> records) {
		if (records.isEmpty()) {
			return;
		}
		Map<String, String> deviceTypeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_DEVICE_TYPE);
		Map<String, String> categoryMap = dictUtil.getDictMap(BaseInfoConstants.IOTCARD_CATEGORY);
		Map<String, String> operatorMap = dictUtil.getDictMap(BaseInfoConstants.OPERATOR);
		Map<String, String> dataPlanMap = dictUtil.getDictMap(BaseInfoConstants.DATA_PLAN);
		Map<String, String> statusMap = dictUtil.getDictMap(BaseInfoConstants.IOTCARD_STATUS);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		for (BdmIotCardExportResponse response : records) {
			response.setCategoryName(categoryMap.getOrDefault(response.getCategory(), null));
			response.setDeviceTypeName(deviceTypeMap.getOrDefault(response.getDeviceType(), null));
			response.setOperatorName(operatorMap.getOrDefault(response.getOperator(), null));
			response.setDataPlanName(dataPlanMap.getOrDefault(response.getDataPlan(), null));
			response.setStatusName(statusMap.getOrDefault(response.getStatus(), null));

			try {
				response.setIssueTime(response.getIssueTime() != null ? sdf.format(sdf.parse(response.getIssueTime())) : "");
				response.setActivationTime(response.getActivationTime() != null ? sdf.format(sdf.parse(response.getActivationTime())) : "");
				response.setExpire(response.getExpire() != null ? sdf.format(sdf.parse(response.getExpire())) : "");
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
		}
	}

}

