package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import org.springblade.common.dept.DeptIdAware;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Description: 铁路货车车厢管理入参
 */
@Data
@ExcelIgnoreUnannotated
public class ShipRequest  implements DeptIdAware {

	private Long id;

	@NotNull(message = "船舶编号不能为空")
	@Compare("船舶编号")
	@ApiModelProperty(value = "船舶编号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "船舶编号")
	@ColumnWidth(22)
	private String number;

	private Integer targetType;
	@Compare("船舶类型")
	private Integer category;
	@Compare("船舶名称")
	private String name;
	@Compare("船舶英文名称")
	private String nameEn;
	@Compare("水上移动业务识别码")
	private String mmsi;
	@Compare("国际海事船舶识别码")
	private String imoNumber;
	@Compare("船舶呼号")
	private String callSign;
	@Compare("所属部门id")
	private Long deptId;
	@Override
	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	@Compare("总重")
	private  Float maxGross;
	@Compare("载重/净重")
	private Float net;
	@Compare("排水量")
	private Float displcement;
	@Compare("型长")
	private Integer length;
	@Compare("型宽")
	private Integer breadth;
	@Compare("型深")
	private Integer depth;
	@Compare("吃水")
	private Integer draught;
	@Compare("航速")
	private Float cruiseSpeed;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	private Date constructionTime;

	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;
	private String terminalCategories;


	/**
	 * 错误信息
	 */
	@ApiModelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "错误信息")
	@ColumnWidth(22)
	private String msg;


	/**
	 * 导出动态表头中文名
	 */
	private List headNameList;

	/**
	 * 导出动态表头字段名
	 */
	private List columnNameList;

	/**	 导出数据的id集合*/
	private List<Long> ids;

	@ExcelIgnore
	private String createAccount;
}

