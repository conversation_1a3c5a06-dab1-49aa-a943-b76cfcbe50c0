package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 存量终端
 */
@Data
public class ExistingTerminalRequest {

	private Long id;

	private Integer current;

	private Integer size;

	/**
	 * 错误信息
	 */
	@ApiModelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "错误信息")
	@ColumnWidth(22)
	private String msg;

	/**
	 * 导出动态表头中文名
	 */
	private List headNameList;

	/**
	 * 导出动态表头字段名
	 */
	private List columnNameList;

	/**
	 * 导出数据的id集合
	 */
	private List<Long> ids;

	//序列号
	private String uniqueId;

	//终端类型
	private Integer deviceType;

	//所属部门 归属单位
	private Long deptId;

	//终端种类/功能类型
	private Integer category;

	//赋码值，16位字符串
	private String deviceNum;

	//监控对象id
	private Long targetId;

	//监控对象类型
	private Integer targetType;

	//0-未删除，1-已删除
	private Integer deleted;

	//物联网协议，1-JT/T808，2-MQTT，3-SNMP，...  默认1
	private Integer iotProtocol;

	//创建人，对应国能登录账号（也是工号）
	private String createAccount;

	//特殊性，0-未知，1-旧设备，2-新设备，3-特殊设备  默认1
	private Integer specificity;

	//视频通道个数
	private Integer channelNum;

	//终端型号
	private String model;

	//IMEI  定位设备必填，其他类型非必填
	private String imei;

	/** 大类，应用方向，取分类表中的code */
	private String classCode;

	/** 小类，终端类型 取分类表中的code */
	private String subClassCode;

	/** 安装位置经度 */
	private Double longitude;

	/** 安装位置纬度 */
	private Double latitude;

	/** 设备安装位置  安装位置描述 */
	private String deviceAddr;

	/** 入网方式 */
	private Integer inNetType;

	/** 导入的时候填写   入网方式 */
	private String inNetTypeName;

	/** 入网运营商 */
	private String inNetProvider;

	/** 北斗卡号 */
	private String bdCardNumber;

	/** 联系人 */
	private String contact;

	/** 联系方式 */
	private String contactPhone;

	/** 物联网卡号 */
	private String iotNumber;

	/** 终端编号 */
	private String deviceNo;

	/** 0:新终端 1:存量终端 */
	private Integer deviceSource;


	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**
	 * 修改时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	//其他的终端类型名称
	private String otherTypes;

	/**
	 * 厂商名称,与子类里面的厂商名称不一致，不需要同步到子类厂商名称中
	 */
	private String manufacturerName;

	/**
	 * 安装日期
	 */
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date installdate;

	private String deptName;

	private String tableValue;
}
