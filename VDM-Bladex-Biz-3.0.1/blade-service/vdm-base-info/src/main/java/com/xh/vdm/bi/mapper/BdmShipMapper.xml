<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmShipMapper">

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.ShipResponse">
        select
        s.id,
        s.number,
        s.target_type,
        s.category,
        s.name,
        s.name_en,
        s.mmsi,
        s.imo_number,
        s.call_sign,
        s.dept_id,
        s.max_gross,
        s.net,
        s.displcement,
        s.length,
        s.breadth,
        s.depth,
        s.draught,
        s.cruise_speed,
        to_char(s.create_time, 'YYYY-MM-DD HH24:MI:SS') as createTime,
        s.update_time,
        s.deleted,
        d.dept_name,
        rd.unique_id,
        rd.device_num,
        rd.category as terminalCategories
        from bdm_ship s
        left join blade_dept d on s.dept_id = d.id
        left join bdm_rnss_device rd ON s.id = rd.target_id
        AND rd.deleted = 0 and rd.target_type = s.target_type
        where s.deleted = 0
            <if test="request.ids != null and request.ids.size() gt 0 ">
                and s.id in
                <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="request.ids == null or request.ids == ''">
                <if test="request.number != null and request.number != ''">
                    and s.number  like concat('%', #{request.number}, '%')
                </if>
                <if test="request.deptId != null">
                    and s.dept_id = #{request.deptId}
                </if>
                <if test="request.uniqueId != null and request.uniqueId != ''">
                    and rd.unique_id like concat('%',#{request.uniqueId},'%')
                </if>
                <if test="request.deviceNum != null and request.deviceNum != ''">
                    and rd.device_num like concat('%',#{request.deviceNum},'%')
                </if>
                <if test="deptIds != null and deptIds != ''">
                    and d.id = any(${deptIds})
                </if>
                <if test="account != null and account != ''">
                    and s.create_account = #{account}
                </if>
            </if>
        order by s.create_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.xh.vdm.biapi.entity.BdmShip">
        INSERT INTO bdm_ship
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != number and '' != number">
                number,
            </if>
            <if test="null != targetType">
                target_type,
            </if>
            <if test="null != category">
                category,
            </if>
            <if test="null != name and '' != name">
                name,
            </if>
            <if test="null != nameEn and '' != nameEn">
                name_en,
            </if>
            <if test="null != mmsi and '' != mmsi">
                mmsi,
            </if>
            <if test="null != imoNumber and '' != imoNumber">
                imo_number,
            </if>
            <if test="null != callSign and '' != callSign">
                call_sign,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != maxGross">
                max_gross,
            </if>
            <if test="null != net">
                net,
            </if>
            <if test="null != displcement">
                displcement,
            </if>
            <if test="null != length">
                length,
            </if>
            <if test="null != breadth">
                breadth,
            </if>
            <if test="null != depth">
                depth,
            </if>
            <if test="null != draught">
                draught,
            </if>
            <if test="null != cruiseSpeed">
                cruise_speed,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted,
            </if>
            <if test="null != createAccount">
                create_account
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != number and '' != number">
                #{number},
            </if>
            <if test="null != targetType">
                #{targetType},
            </if>
            <if test="null != category">
                #{category},
            </if>
            <if test="null != name and '' != name">
                #{name},
            </if>
            <if test="null != nameEn and '' != nameEn">
                #{nameEn},
            </if>
            <if test="null != mmsi and '' != mmsi">
                #{mmsi},
            </if>
            <if test="null != imoNumber and '' != imoNumber">
                #{imoNumber},
            </if>
            <if test="null != callSign and '' != callSign">
                #{callSign},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != maxGross">
                #{maxGross},
            </if>
            <if test="null != net">
                #{net},
            </if>
            <if test="null != displcement">
                #{displcement},
            </if>
            <if test="null != length">
                #{length},
            </if>
            <if test="null != breadth">
                #{breadth},
            </if>
            <if test="null != depth">
                #{depth},
            </if>
            <if test="null != draught">
                #{draught},
            </if>
            <if test="null != cruiseSpeed">
                #{cruiseSpeed},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted},
            </if>
            <if test="null != createAccount">
                #{createAccount,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bdm_ship(id, number, category, name, name_en, mmsi, imo_number, call_sign, dept_id,
        max_gross, net, displcement, length, breadth, depth, draught, cruise_speed)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.number}, #{entity.category}, #{entity.name}, #{entity.nameEn},
            #{entity.mmsi}, #{entity.imoNumber}, #{entity.callSign}, #{entity.deptId}, #{entity.maxGross},
            #{entity.net}, #{entity.displcement}, #{entity.length}, #{entity.breadth}, #{entity.depth},
            #{entity.draught}, #{entity.cruiseSpeed})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bdm_ship
        <set>
            <if test="number != null">
                number = #{number},
            </if>
            <if test="targetType != null">
                target_type = #{targetType},
            </if>
            <if test="category != null">
                category = #{category},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="nameEn != null">
                name_en = #{nameEn},
            </if>
            <if test="mmsi != null">
                mmsi = #{mmsi},
            </if>
            <if test="imoNumber != null">
                imo_number = #{imoNumber},
            </if>
            <if test="callSign != null">
                call_sign = #{callSign},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="maxGross != null">
                max_gross = #{maxGross},
            </if>
            <if test="net != null">
                net = #{net},
            </if>
            <if test="displcement != null">
                displcement = #{displcement},
            </if>
            <if test="length != null">
                length = #{length},
            </if>
            <if test="breadth != null">
                breadth = #{breadth},
            </if>
            <if test="depth != null">
                depth = #{depth},
            </if>
            <if test="draught != null">
                draught = #{draught},
            </if>
            <if test="cruiseSpeed != null">
                cruise_speed = #{cruiseSpeed},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted}
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="batchUpdate">
        UPDATE bdm_ship
        <set>
            dept_id = #{deptId}
        </set>
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

</mapper>

