<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.RnssDeviceMapper">

    <sql id="Base_Column_List">
        rd.id,
        rd.unique_id,
        rd.imei,
        rd.model,
        rd.vendor,
        rd.bd_chip_sn,
        rd.device_type,
        rd.specificity,
        rd.dept_id,
        rd.target_id,
        rd.target_type,
        rd.target_name,
        rd.activated,
        rd.category,
        rd.device_num,
        rd.channel_num,
        rd.installdate,
        rd.create_time,
        rd.update_time,
        rd.deleted,
        rd.scenario,
        rd.domain,
        rd.gnss_mode,
        rd.terminal_id,
        d.dept_name
    </sql>

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.RnssDeviceResponse">
        SELECT
            rd.id, rd.unique_id, rd.imei, rd.model, rd.vendor, rd.bd_chip_sn, rd.device_type, rd.specificity, rd.dept_id,
            rd.target_id, rd.target_type, rd.target_name, rd.activated, rd.category, rd.device_num,rd.channel_num,
            rd.installdate,rd.terminal_id, rd.scenario, rd.domain, rd.gnss_mode,rd.asset_type,rd.own_dept_type,rd.own_dept_name, d.dept_name,
            ic."number" as numbers, ma.name as vendorName,rd.iot_protocol
        FROM bdm_rnss_device rd
        LEFT JOIN blade_dept d ON rd.dept_id = d.id
        LEFT JOIN bdm_iot_card ic ON rd.id = ic.device_id and rd.device_type = ic.device_type
        LEFT JOIN ${schema}.bdc_manufactor ma ON ma.code = rd.vendor and ma.is_del = 0
        WHERE rd.deleted = 0
        <if test="request.ids != null and request.ids.size() gt 0 ">
            and rd.id in
            <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="request.ids == null or request.ids == ''">
            <if test="request.category != null">
                AND rd.category = #{request.category}
            </if>
            <if test="request.uniqueId != null and request.uniqueId != ''">
                AND rd.unique_id LIKE CONCAT('%', #{request.uniqueId}, '%')
            </if>
            <if test="request.imei != null and request.imei != ''">
                AND rd.imei LIKE CONCAT('%', #{request.imei}, '%')
            </if>
            <if test="request.number != null and request.number != ''">
                AND ic.number LIKE CONCAT('%', #{request.number}, '%')
            </if>
            <if test="request.deviceNum != null and request.deviceNum != ''">
                and rd.device_num like concat('%', #{request.deviceNum}, '%')
            </if>
            <if test="request.specificity != null">
                AND rd.specificity = #{request.specificity}
            </if>
            <if test="request.deptId != null">
                AND rd.dept_id = #{request.deptId}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and d.id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and rd.create_account = #{account}
            </if>
        </if>
        ORDER BY rd.create_time DESC
    </select>

    <!--查询单个-->
    <select id="queryById" resultType="com.xh.vdm.bi.vo.response.RnssDeviceResponse">
        SELECT <include refid="Base_Column_List" />
        from bdm_rnss_device rd
        left join blade_dept d on rd.dept_id = d.id
        where rd.id = #{id}
    </select>

    <!--新增所有列-->
    <insert id="insertRnss" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.xh.vdm.biapi.entity.BdmRnssDevice">
        INSERT INTO bdm_rnss_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                unique_id,
            </if>
            <if test="null != imei and '' != imei">
                imei,
            </if>
            <if test="null != model and '' != model">
                model,
            </if>
            <if test="null != vendor and '' != vendor">
                vendor,
            </if>
            <if test="null != bdChipSn and '' != bdChipSn">
                bd_chip_sn,
            </if>
            <if test="null != deviceType">
                device_type,
            </if>
            <if test="null != specificity">
                specificity,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != targetId">
                target_id,
            </if>
            <if test="null != targetType">
                target_type,
            </if>
            <if test="null != targetName and '' != targetName">
                target_name,
            </if>
            <if test="null != activated">
                activated,
            </if>
            <if test="null != category">
                category,
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                device_num,
            </if>
            <if test="null != channelNum">
                channel_num,
            </if>
            <if test="null != installdate">
                installdate,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted,
            </if>
            <if test="null != scenario">
                scenario,
            </if>
            <if test="null != domain">
                domain,
            </if>
            <if test="null != gnssMode">
                gnss_mode,
            </if>
            <if test="null != terminalId and '' != terminalId">
                terminal_id,
            </if>
            <if test="null != createAccount and '' != createAccount">
                create_account,
            </if>
            <if test="null != assetType">
                asset_type,
            </if>
            <if test="null != ownDeptType">
                own_dept_type,
            </if>
            <if test="null != ownDeptName">
                own_dept_name
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                #{uniqueId},
            </if>
            <if test="null != imei and '' != imei">
                #{imei},
            </if>
            <if test="null != model and '' != model">
                #{model},
            </if>
            <if test="null != vendor and '' != vendor">
                #{vendor},
            </if>
            <if test="null != bdChipSn and '' != bdChipSn">
                #{bdChipSn},
            </if>
            <if test="null != deviceType">
                #{deviceType},
            </if>
            <if test="null != specificity">
                #{specificity},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != targetId">
                #{targetId},
            </if>
            <if test="null != targetType">
                #{targetType},
            </if>
            <if test="null != targetName and '' != targetName">
                #{targetName},
            </if>
            <if test="null != activated">
                #{activated},
            </if>
            <if test="null != category">
                #{category},
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                #{deviceNum},
            </if>
            <if test="null != channelNum">
                #{channelNum},
            </if>
            <if test="null != installdate">
                #{installdate},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted},
            </if>
            <if test="null != scenario">
                #{scenario},
            </if>
            <if test="null != domain">
                #{domain},
            </if>
            <if test="null != gnssMode">
                #{gnssMode},
            </if>
            <if test="null != terminalId and '' != terminalId">
                #{terminalId},
            </if>
            <if test="null != createAccount and '' != createAccount">
                #{createAccount},
            </if>
            <if test="null != assetType">
                #{assetType},
            </if>
            <if test="null != ownDeptType">
                #{ownDeptType},
            </if>
            <if test="null != ownDeptName">
                #{ownDeptName}
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update" parameterType="com.xh.vdm.biapi.entity.BdmRnssDevice">
        UPDATE bdm_rnss_device
        <set>
            <if test="null != uniqueId">unique_id = #{uniqueId},</if>
            <if test="null != imei">imei = #{imei},</if>
            <if test="null != model">model = #{model},</if>
            <if test="null != vendor">vendor = #{vendor},</if>
            <if test="null != bdChipSn">bd_chip_sn = #{bdChipSn},</if>
            <if test="null != deviceType">device_type = #{deviceType},</if>
            <if test="null != specificity">specificity = #{specificity},</if>
            <if test="null != deptId">dept_id = #{deptId},</if>
            <if test="null != targetId">target_id = #{targetId},</if>
            <if test="null != targetType">target_type = #{targetType},</if>
            <if test="null != targetName">target_name = #{targetName},</if>
            <if test="null != activated">activated = #{activated},</if>
            <if test="null != category">category = #{category},</if>
            <if test="null != deviceNum">device_num = #{deviceNum},</if>
            <if test="installdate != null">installdate = #{installdate},</if>
            <if test="installdate == null">installdate = NULL,</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != deleted">deleted = #{deleted},</if>
            <if test="null != channelNum">channel_num = #{channelNum},</if>
            <if test="null != scenario">scenario = #{scenario},</if>
            <if test="null != domain">domain = #{domain},</if>
            <if test="null != gnssMode">gnss_mode = #{gnssMode},</if>
            <if test="null != terminalId">terminal_id = #{terminalId}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
        update bdm_rnss_device
        set deleted     = 1,
            update_time = now()
        where id = #{id}
    </update>

    <update id="deleteByIds">
        update bdm_rnss_device
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO bdm_rnss_device (
        "id","unique_id", "imei", "model", "vendor", "bd_chip_sn", "dept_id", "category", "channel_num",
        "installdate", "create_time", "scenario", "domain", "specificity", "device_num", "gnss_mode","terminal_id"
        ) VALUES
        <foreach collection="list" item="device" index="index" separator=",">
            (
            #{device.id},  #{device.uniqueId}, #{device.imei}, #{device.model}, #{device.vendor}, #{device.bdChipSn},
            #{device.deptId}, #{device.category},#{device.channelNum}, #{device.installdate}, NOW(), #{device.scenario},
            #{device.domain}, #{device.specificity}, #{device.deviceNum}, #{device.gnssMode}, #{device.terminalId}
            )
        </foreach>
    </insert>

    <update id="updateByWorkerId">
        update bdm_rnss_device
        set
            target_id = 0,
            target_type = 0,
            target_name = ''
        where
            target_id = #{id}
          and target_type = #{targetType}
          and deleted = 0
    </update>

    <update id="updateByVehicleId">
        update bdm_rnss_device
        set
            target_id = 0,
            target_type = 0,
            target_name = ''
        where
            target_id = #{id}
          and target_type = #{targetType}
          and deleted = 0
    </update>

    <update id="connectWorkerTerminal">
        update bdm_rnss_device
        set
            target_id = #{id},
            target_type = #{targetType},
            target_name = #{targetName}
        where id = #{request.id}
    </update>

    <update id="connectVehicleTerminal">
        update bdm_rnss_device
        set
            target_id = #{id},
            target_type = #{targetType},
            target_name = #{targetName},
            dept_id = #{deptId}
        where id = #{request.id}
    </update>

    <select id="selectBindByWorkId" resultType="com.xh.vdm.bi.vo.response.PersonNoBingResponse">
        SELECT
            rd.id,
            rd.unique_id,
            rd.model,
            rd.dept_id,
            rd.category,
            rd.device_type,
            rd.device_num,
            rd.iot_protocol,
            d.dept_name
        FROM bdm_rnss_device rd
        LEFT JOIN blade_dept d on rd.dept_id = d.id
        where rd.target_id = #{id}
          and rd.target_type = #{targetType}
        AND rd.deleted = 0
    </select>

    <delete id="deleteByTargetIds">
        <if test="ids != null and ids.length > 0">
            update bdm_rnss_device
            set target_id = 0,target_type = 0,target_name=''
            where target_type =  #{targetType}
            and target_id in
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>

    <select id="selectByWorkId" resultType="com.xh.vdm.bi.vo.response.WorkerBingResponse">
        SELECT
            rd.unique_id,
            rd.model,
            rd.dept_id,
            rd.category,
            rd.device_type,
            rd.device_num,
            rd.iot_protocol,
            d.dept_name
        FROM bdm_rnss_device rd
                 LEFT JOIN blade_dept d on rd.dept_id = d.id
        where rd.target_id = #{id}
          and rd.target_type = #{targetType}
          AND rd.deleted = 0
    </select>

    <update id="unbind">
        update bdm_rnss_device
        set target_id = 0, target_type = 0, target_name = ''
        where target_id = #{id} and target_type = #{targetType}
    </update>

    <update id="bindTarget">
        update bdm_rnss_device
        set target_id = #{targetId}, target_type = #{targetType}, target_name = #{targetName}
        where id = #{deviceId}
    </update>

    <select id="countByUserRole" resultType="java.lang.Long">
        select count(*) from bdm_rnss_device rd
        WHERE rd.deleted = 0
        <if test="deptIds != null and deptIds != ''">
            and rd.dept_id = any(${deptIds})
        </if>
    </select>

    <update id="updateDept">
        update bdm_rnss_device set dept_id = #{deptId} where target_id = #{id} and target_type = #{targetType}
    </update>

</mapper>

