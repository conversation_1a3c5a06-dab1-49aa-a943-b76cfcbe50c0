<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmDeviceStatusMapper">
    <select id="countOnlineDevices" resultType="java.lang.Long">
        select count(*) from bdm_device_status dbs
        where dbs.action = 0
            <if test="deptIds != null and deptIds != ''">
                and dbs.dept_id = any(${deptIds})
            </if>
    </select>

    <resultMap id="deviceModelInfo" type="com.xh.vdm.bi.dto.DeviceModelInfo">
        <result property="model" column="model" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="ids" column="ids" jdbcType="ARRAY" typeHandler="com.xh.vdm.bi.handler.ListTypeHandler"/>
    </resultMap>

    <resultMap id="deviceModelCount" type="com.xh.vdm.bi.dto.DeviceModelCount">
        <result property="model" column="model" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="number" column="cnt" javaType="int" jdbcType="INTEGER"/>
    </resultMap>

    <select id="getDeviceModelOnlineByUserAuth" resultMap="deviceModelCount">
        select a.model,count(a.id) as cnt from bdm_abstract_device a
        inner join bdm_device_status b on a.id=b.device_id
        where a.deleted=0 and b.action=0 and a.model != ''
        <if test="deptIds != null and deptIds != ''">
            and a.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and a.create_account = #{account}
        </if>
        group by a.model;
    </select>

    <select id="getDevOnlineInfoWith" resultMap="deviceModelInfo">
        select a.model,array_agg(a.id) as ids from bdm_abstract_device a
        inner join bdm_device_status b on a.id=b.device_id
        left join blade_dept c on a.dept_id = c.id
        where a.deleted=0 and b.action=0
        <if test="deptIds != null and deptIds != ''">
            and a.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (a.dept_Id = #{deptId} or c.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and a.create_account = #{account}
        </if>
        <choose>
            <when test="models != null and models.size() > 0">
                and a.model in
                <foreach collection="models" item="model" open="(" separator="," close=")">
                    #{model}
                </foreach>
            </when>
            <otherwise>
                and a.model != ''
            </otherwise>
        </choose>
        <if test="usage != null">
            and a.usage = #{usage}
        </if>
        <if test="district != null and district != ''">
            and b.district LIKE concat(#{district}, '%')
        </if>
        group by a.model;
    </select>

    <select id="getDevOnlineCntWith" resultMap="deviceModelCount">
        select a.model,count(a.id) as cnt from bdm_abstract_device a
        inner join bdm_device_status b on a.id=b.device_id
        left join blade_dept c on a.dept_id = c.id
        where a.deleted=0 and b.action=0
        <if test="deptIds != null and deptIds != ''">
            and a.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (a.dept_Id = #{deptId} or c.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and a.create_account = #{account}
        </if>
        <choose>
          <when test="models != null and models.size() > 0">
              and a.model in
              <foreach collection="models" item="model" open="(" separator="," close=")">
                  #{model}
              </foreach>
          </when>
          <otherwise>
              and a.model != ''
          </otherwise>
        </choose>
<!--        <if test="scenarios != null and scenarios.size() > 0">-->
<!--            and a.scenario in-->
<!--            <foreach collection="scenarios" item="scenario" open="(" separator="," close=")">-->
<!--                #{scenario}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="usage != null">
            and a.usage = #{usage}
        </if>
        <if test="district != null and district != ''">
            and b.district LIKE concat(#{district}, '%')
        </if>
        group by a.model;
    </select>

    <select id="countByUserAuth" resultType="java.lang.Long">
        select count(*) from bdm_abstract_device a
        inner join bdm_device_status b on a.id = b.device_id
        WHERE a.deleted = 0
        <if test="specificity != null">
            and a.specificity = #{specificity}
        </if>
        <if test="online != null">
            and b.action = #{online}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and a.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and a.create_account = #{account}
        </if>
    </select>

    <select id="getDeviceIdsWith" resultType="java.lang.Long">
        select a.id from bdm_abstract_device a
        inner join bdm_device_status b on a.id = b.device_id
        left join blade_dept c on a.dept_id = c.id
        WHERE a.deleted = 0
        <if test="specificity != null">
            and a.specificity = #{specificity}
        </if>
        <if test="online != null">
            and b.action = #{online}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and a.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (a.dept_Id = #{deptId} or c.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and a.create_account = #{account}
        </if>
        <if test="usage != null">
            and a.usage = #{usage}
        </if>
        <if test="district != null and district != ''">
            and b.district LIKE concat(#{district}, '%')
        </if>
    </select>

    <select id="countByUserWith" resultType="java.lang.Long">
        select count(*) from bdm_abstract_device a
        inner join bdm_device_status b on a.id = b.device_id
        left join blade_dept c on a.dept_id = c.id
        WHERE a.deleted = 0
        <if test="specificity != null">
            and a.specificity = #{specificity}
        </if>
        <if test="online != null">
            and b.action = #{online}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and a.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (a.dept_Id = #{deptId} or c.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and a.create_account = #{account}
        </if>
<!--        <if test="scenarios != null and scenarios.size() > 0">-->
<!--            and a.scenario in-->
<!--            <foreach collection="scenarios" item="scenario" open="(" separator="," close=")">-->
<!--                #{scenario}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="usage != null">
            and a.usage = #{usage}
        </if>
        <if test="district != null and district != ''">
            and b.district LIKE concat(#{district}, '%')
        </if>
    </select>
</mapper>

