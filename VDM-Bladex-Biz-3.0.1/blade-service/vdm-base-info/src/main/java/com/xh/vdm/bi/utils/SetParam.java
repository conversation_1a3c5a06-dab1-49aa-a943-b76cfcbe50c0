package com.xh.vdm.bi.utils;

import cn.hutool.http.HttpRequest;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.xh.vdm.bi.vo.request.TaskParam;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
@Slf4j
@Component
public class SetParam {
	private NacosServiceManager nacosServiceManager;
	private NacosDiscoveryProperties nacosDiscoveryProperties;

	private static SetParam instance;

	@Autowired
	public SetParam(NacosServiceManager nacosServiceManager, NacosDiscoveryProperties nacosDiscoveryProperties) {
		this.nacosServiceManager = nacosServiceManager;
		this.nacosDiscoveryProperties = nacosDiscoveryProperties;
	}

	@PostConstruct
	public void init() {
		instance = this;
	}

	public static Integer SendSetParam(TaskParam req) {
		if (instance.nacosServiceManager == null || instance.nacosDiscoveryProperties == null) {
			log.error("NacosServiceManager or NacosDiscoveryProperties is not initialized.");
			return -3;
		}
		String finalName = "go-monitorcars";
		NamingService namingService;
		try {
			namingService = instance.nacosServiceManager.getNamingService(instance.nacosDiscoveryProperties.getNacosProperties());
		} catch (Exception e) {
			log.error("Failed to get NamingService: ", e);
			return -4;
		}
		Instance instanceObj = null;
		try {
			instanceObj = namingService.selectOneHealthyInstance(finalName, instance.nacosDiscoveryProperties.getGroup());
		} catch (NacosException e) {
			log.error("Failed to select a healthy instance: ", e);
			return -5;
		}
		if (instanceObj == null) {
			log.error("No healthy instance found.");
			return -6;
		}
		String url = instanceObj.getIp() + ":" + instanceObj.getPort() + "/monitorcars/vehicle/settarget";
		String bodyStr = JSON.toJSONString(req);
		log.info("请求设置参数接口地址:{},{}", url, bodyStr);
		String res;
		try {
			res = HttpRequest.post(url).body(bodyStr).execute().body();
		} catch (Exception e) {
			log.error("Failed to send POST request: ", e);
			return -7;
		}
		log.info("请求返回结果:{}", res);
		try {
			JSONObject jo = JSON.parseObject(res);
			if (jo == null) {
				return -1;
			}
			if (jo.get("code") == null) {
				return -2;
			}
			return Integer.parseInt(jo.get("code").toString());
		} catch (Exception e) {
			log.error("Failed to parse JSON response: ", e);
			return -8;
		}
	}
}
