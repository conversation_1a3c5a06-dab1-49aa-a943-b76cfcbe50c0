package com.xh.vdm.bi.config;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.xh.vdm.bi.entity.gnProd.BdsOrgLite;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.util.List;

/**
 * 国能组织视图Redis缓存模板配置。
 */
@Configuration
public class RedisBdsOrgTemplateConfig {
	@Bean("bdsOrgRedisTemplate")
	public RedisTemplate<String, List<BdsOrgLite>> bdsOrgRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
		RedisTemplate<String, List<BdsOrgLite>> template = new RedisTemplate<>();
		template.setConnectionFactory(redisConnectionFactory);

		// 使用StringRedisSerializer来序列化键
		template.setKeySerializer(new StringRedisSerializer());
		template.setHashKeySerializer(new StringRedisSerializer());

		// 配置Value的序列化器 - 使用Jackson处理List<BdsOrgLite>
		Jackson2JsonRedisSerializer<List<BdsOrgLite>> serializer =
			createListBdsOrgSerializer();

		// 使用自定义序列化器来序列化值
		template.setValueSerializer(serializer);
		template.setHashValueSerializer(serializer);

		template.afterPropertiesSet();
		return template;
	}

	private Jackson2JsonRedisSerializer<List<BdsOrgLite>> createListBdsOrgSerializer() {
		// 创建ObjectMapper并配置
		ObjectMapper objectMapper = new ObjectMapper();
//		// 启用默认类型信息，确保能正确反序列化
//		objectMapper.enableDefaultTyping(
//			ObjectMapper.DefaultTyping.NON_FINAL,
//			JsonTypeInfo.As.PROPERTY);

		// 创建序列化器
		Jackson2JsonRedisSerializer<List<BdsOrgLite>> serializer =
			new Jackson2JsonRedisSerializer<>(getListBdsOrgType());
		serializer.setObjectMapper(objectMapper);

		return serializer;
	}

	// 获取List<BdsOrg>的类型引用
	private JavaType getListBdsOrgType() {
		return TypeFactory.defaultInstance()
			.constructCollectionType(List.class, BdsOrgLite.class);
	}
}
