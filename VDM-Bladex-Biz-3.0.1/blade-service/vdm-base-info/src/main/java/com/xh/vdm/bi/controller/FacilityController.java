package com.xh.vdm.bi.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.service.DeptId;
import com.xh.vdm.bi.service.FacilityService;
import com.xh.vdm.bi.service.IBladeDictBizService;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.utils.DictUtil;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.FacilityRequest;
import com.xh.vdm.bi.vo.request.FacilityTerminalRequest;
import com.xh.vdm.bi.vo.response.FacilityNoBingResponse;
import com.xh.vdm.bi.vo.response.FacilityResponse;
import com.xh.vdm.biapi.entity.BdmFacility;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springblade.common.annotation.Log;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.minio.MinioService;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.EasyExcelUtils;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.entity.DataAuthCE;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 基础设施管理
 */
@RestController
@Slf4j
@RequestMapping("/base/facility")
public class FacilityController {
	/**
	 * 服务对象
	 */
	@Resource
	private FacilityService facilityService;
	@Resource
	private IBladeDictBizService iBladeDictBizService;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private IDictBizClient dictBizClient;
	@Resource
	private MinioService minioService;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private CETokenUtil ceTokenUtil;
	@Resource
	private DictUtil dictUtil;


	/**
	 * 分页查询
	 *
	 * @param facilityRequest 筛选条件
	 * @return 查询结果
	 */
	//pre_auth_test
	@PostMapping("/list")
	public R<IPage<FacilityResponse>> queryByPage(@RequestBody FacilityRequest facilityRequest, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		IPage<FacilityResponse> page = this.facilityService.queryByPage(facilityRequest, ceDataAuth);
		enrichDataWithDict(page.getRecords());
		return R.data(page);
	}

	/**
	 * 新增数据
	 *
	 * @param facilityRequest 实体
	 * @return 新增结果
	 */
	//pre_auth_test:
	@Log(menu = "基础设施管理", operation = Operation.INSERT, objectType = ObjectType.FACILITY)
	@PostMapping("/save")
	public R add(@Valid @RequestBody FacilityRequest facilityRequest, BladeUser user) {
		List<BdmFacility> facilityList = facilityService.getBaseMapper()
				.selectList(new QueryWrapper<BdmFacility>().eq("deleted", 0).eq("code", facilityRequest.getCode()));
		if (!facilityList.isEmpty()) {
			return R.fail("编码已存在！");
		}

		BdmFacility facility = this.facilityService.insert(facilityRequest);
		if (facility != null) {
			return R.data(ResultCode.SUCCESS.getCode(), facility.getId().toString(), "新增成功");
		}
		return R.fail(ResultCode.FAILURE, "");
	}

	/**
	 * 编辑数据
	 *
	 * @param facilityRequest 实体
	 * @return 编辑结果
	 */
	@Log(menu = "基础设施管理", operation = Operation.UPDATE, objectType = ObjectType.FACILITY)
	@PostMapping("/update")
	public R edit(@Valid @RequestBody FacilityRequest facilityRequest, BladeUser user) {
		BdmFacility facilityInDB = this.facilityService.getBaseMapper().selectById(facilityRequest.getId());

		BdmFacility bdmFacility = new BdmFacility();
		BeanUtils.copyProperties(facilityRequest, bdmFacility);
		BdmFacility facility = this.facilityService.update(facilityRequest);

		if (facility != null) {
			String result = new CompareUtils<BdmFacility>().compare(facilityInDB, bdmFacility);
			return R.data(ResultCode.SUCCESS.getCode(), result, "编辑成功");
		}
		return R.fail(ResultCode.FAILURE, "");
	}

	/**
	 * 批量更新所属机构
	 * @param batchUpdateRequest
	 * @return
	 */
	@Log(menu = "基础设施管理", operation = Operation.UPDATE, objectType = ObjectType.FACILITY)
	@PostMapping("/batchUpdate")
	public R batchUpdate(@RequestBody BatchUpdateRequest batchUpdateRequest) {
		if (CollectionUtils.isEmpty(batchUpdateRequest.getIds())) {
			return R.fail("参数不能为空");
		}
		if (null == batchUpdateRequest.getDeptId()) {
			return R.fail("所属机构不能为空");
		}

		Boolean b = facilityService.batchUpdate(batchUpdateRequest);
		if (b){
			return R.data(ResultCode.SUCCESS.getCode(), "编辑成功");
		}else {
			return R.fail("操作失败");
		}

	}

	/**
	 * 导出
	 */
	@PostMapping("/export")
	public R<String> export(@RequestBody FacilityRequest facilityRequest, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		try {
			facilityRequest.setCurrent(1);
			facilityRequest.setSize(Integer.MAX_VALUE);
			IPage<FacilityResponse> list = this.facilityService.queryByPage(facilityRequest, ceDataAuth);
			if (list.getRecords().isEmpty()) {
				return R.fail("没有数据可导出");
			}

			Map<String, String> map = new HashMap<>();
			//从字典中获取数据
			enrichDataWithDict(list.getRecords());
			for (FacilityResponse response : list.getRecords()) {
				String key = BaseInfoConstants.FACILITY_TARGET_TYPE + "-" + response.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", response.getName());
				innerMap.put("targetType", response.getTargetType());
				innerMap.put("targetCategory", response.getCategory());
				innerMap.put("deptId", response.getDeptId());
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			}
			// todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			String menu = "基础设施管理";
			try {
				// 使用ByteArrayOutputStream创建Excel文件
				ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
				EasyExcelUtils.exportExcelToStream(
					menu,
					outputStream,
					(List) list.getRecords(),
					facilityRequest.getHeadNameList(),
					facilityRequest.getColumnNameList(),
					FacilityResponse.class
				);
				ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
				String minioFileUrl = minioService.uploadFile(menu, inputStream, outputStream.size());
				if (minioFileUrl != null) {
					return R.data(minioFileUrl);
				} else {
					return R.fail("文件上传失败");
				}
			} catch (Exception e) {
				log.info("文件上传失败 " + e.getMessage());
				return R.fail("文件上传失败");
			}
		} catch (Exception e) {
			log.error("导出失败", e);
			return R.fail("导出失败");
		}
	}

	/**
	 * 导入数据
	 */
	@Log(menu = "基础设施管理", operation = Operation.IMPORT, objectType = ObjectType.FACILITY)
	@PostMapping("/importExcel")
	public R importExcel(@Valid @RequestBody List<FacilityRequest> list, BladeUser user) {
		if (list.isEmpty()) {
			return R.fail("数据为空");
		}
		List<FacilityRequest> result = this.facilityService.importExcel(list);
		if (result.isEmpty()) {
			Map<Long, StringBuilder> deptIdMap = new HashMap<>();
			for (FacilityRequest request : list) {
				Long deptId = request.getDeptId();
				Long id = request.getId();
				if (deptIdMap.containsKey(deptId)) {
					deptIdMap.get(deptId).append("、").append(id);
				} else {
					deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
				}
			}
			return R.data(ResultCode.SUCCESS.getCode(), deptIdMap, "导入成功");
		}
		String filePath = "";
		String menu = "基础设施管理";
		try {
			filePath = minioService.exportToMinIO(menu, result, FacilityRequest.class);
		} catch (IOException e) {
			log.error("导出错误数据失败");
		}
		List<FacilityRequest> filteredList = list.stream()
			.filter(item -> !result.contains(item))
			.collect(Collectors.toList());
		Map<Long, StringBuilder> deptIdMap = new HashMap<>();
		if (!filteredList.isEmpty()) {
			for (FacilityRequest request : filteredList) {
				Long deptId = request.getDeptId();
				Long id = request.getId();
				if (deptIdMap.containsKey(deptId)) {
					deptIdMap.get(deptId).append("、").append(id);
				} else {
					deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
				}
			}
		}
		return R.data(207, deptIdMap, filePath);
	}

	/**
	 * 删除数据
	 *
	 * @param ids 主键
	 * @return 删除是否成功
	 */
	@Log(menu = "基础设施管理", operation = Operation.DELETE, objectType = ObjectType.FACILITY)
	@GetMapping("/delete")
	public R deleteByIds(@RequestParam("ids") Long[] ids, BladeUser user) {
		Map<Long, Object> map = new HashMap<>();
		List<String> keys = new ArrayList<>();
		for (Long id : ids) {
			keys.add(BaseInfoConstants.FACILITY_TARGET_TYPE + "-" + id);
		}
		// 获取 Redis 数据
		List<Object> values = redisTemplate.opsForHash().multiGet(BaseInfoConstants.BASEINFO_TARGET, keys);
		// 处理获取到的值
		for (int i = 0; i < keys.size(); i++) {
			String key = keys.get(i);
			Object value = values.get(i);
			if (value != null) {
				String jsonValue = value.toString();
				BdmFacility facility = JSONObject.parseObject(jsonValue, BdmFacility.class);

				int index = key.indexOf("-");
				if (index != -1) {
					String idValue = key.substring(index + 1);
					Long deptId = facility.getDeptId();
					if (map.containsKey(deptId)) {
						String existingValues = (String) map.get(deptId);
						map.put(deptId, existingValues + "、" + idValue);
					} else {
						map.put(deptId, idValue);
					}
				}
			}
		}
		boolean result = this.facilityService.deleteByIds(ids);
		if (result) {
			return R.data(ResultCode.SUCCESS.getCode(), map, "删除成功");
		} else {
			return R.fail("删除失败");
		}
	}

	/**
	 * 绑定终端
	 * <p>
	 * id 基础设施id
	 */
	@Log(menu = "基础设施管理", operation = Operation.BIND_OR_UNBIND, objectType = ObjectType.FACILITY)
	@PostMapping("/connect")
	public R<T> connectTerminal(@RequestBody List<FacilityTerminalRequest> list, Long id, @DeptId Long deptId, BladeUser user) {
		if (id == null) {
			return R.fail("人员id不能为空");
		}
		return R.status(this.facilityService.connectTerminal(list, id, deptId));
	}

	/**
	 * 未绑定终端列表
	 */
	//pre_auth_test:
	@PostMapping("/selectNoBind")
	public R<IPage<FacilityNoBingResponse>> selectNoBind(@RequestBody DeviceNoBindRequest deviceNoBindRequest, BladeUser user) {
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		IPage<FacilityNoBingResponse> page = this.facilityService.selectNoBind(deviceNoBindRequest, ceDataAuth);
		enrichDataByDict(page.getRecords());
		return R.data(page);
	}

	/**
	 * 已绑定终端列表
	 */
	@PostMapping("/selectBind")
	public R<List<FacilityNoBingResponse>> selectBind(@RequestBody DeviceNoBindRequest deviceNoBindRequest, Long id) {
		if (id == null) {
			return R.fail("人员id不能为空");
		}
		List<FacilityNoBingResponse> list = this.facilityService.selectBind(deviceNoBindRequest, id);
		enrichDataByDict(list);
		return R.data(list);
	}

	/**
	 * 查看绑定终端
	 */
	@GetMapping("/terminalInfo")
	public R<List<FacilityNoBingResponse>> terminalInfo(@RequestParam Long id) {
		List<FacilityNoBingResponse> list = this.facilityService.terminalInfo(id);
		enrichDataByDict(list);
		return R.data(list);
	}

	/**
	 * 终端类别
	 */
	@GetMapping("/deviceType")
	public R<List<Map<String, Object>>> deviceType() {
		return R.data(this.iBladeDictBizService.deviceType(BaseInfoConstants.BDM_DEVICE_TYPE, BaseInfoConstants.MENU_TREE_ROOT_ID));
	}

	private void enrichDataWithDict(List<FacilityResponse> records) {
		if (records.isEmpty()) {
			return;
		}
		Map<String, String> deviceTypeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_DEVICE_TYPE);
		Map<String, String> facilityMap = dictUtil.getDictMap(BaseInfoConstants.FACILITY_TYPE);

		for (FacilityResponse response : records) {
			response.setCategoryName(facilityMap.getOrDefault(response.getCategory(), null));
			if (StringUtils.isNotBlank(response.getTerminalCategories())) {
				String[] categories = response.getTerminalCategories().split(",");
				Set<String> uniqueCategories = new HashSet<>(Arrays.asList(categories));
				StringBuilder terminalCategories = new StringBuilder();
				for (String category : uniqueCategories) {
					String deviceType = deviceTypeMap.get(category);
					if (deviceType != null) {
						if (terminalCategories.length() > 0) {
							terminalCategories.append(", ");
						}
						terminalCategories.append(deviceType);
					}
				}
				response.setTerminalCategories(terminalCategories.toString());
			}
		}
	}

	private void enrichDataByDict(List<FacilityNoBingResponse> records) {
		if (records.isEmpty()) {
			return;
		}
		Map<String, String> deviceTypeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_DEVICE_TYPE);

		for (FacilityNoBingResponse response : records) {
			response.setCategoryName(deviceTypeMap.getOrDefault(response.getCategory().toString(), null));
			response.setDeviceTypeName(deviceTypeMap.getOrDefault(response.getCategory().toString(), null));
		}
	}

}

