package com.xh.vdm.bi.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BdmDeviceCode implements Serializable {

	private String deviceNum;
	private String signature;
	@JsonProperty("deviceCate")
	private String kind;
	@TableField(exist = false)
	private String deviceCateName;
	@JsonProperty("manufacturer")
	private String vendor;
	@TableField(exist = false)
	private String manufacturerName;
	@JsonProperty("deviceModel")
	private String model;
	@JsonProperty("deviceSeq")
	private String serial;
	@JsonProperty("imei")
	private String imei;
	@JsonProperty("chipSeq")
	private String bdChipSerial;
	private String createTime;

	private Integer activated;
}
