package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.biapi.entity.BdmVirtualTarget;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmVirtualTarget)表数据库访问层
 */
public interface BdmVirtualTargetMapper extends BaseMapper<BdmVirtualTarget> {

	/**
	 * 查询指定行数据
	 *
	 * @param virtualTarget 查询条件
	 * @param page          分页对象
	 * @param account
	 * @return 对象列表
	 */
	IPage<BdmVirtualTarget> queryByPage(@Param("virtualTarget") BdmVirtualTarget virtualTarget, @Param("page") IPage page, @Param("account") String account, @Param("deptIds") String deptIds);

	void insertBatch(@Param("entities") List<BdmVirtualTarget> virtualTargetList);

    void updateByUniqueId(@Param("uniqueIdList") List<String> uniqueIdList);

    void updateStatus(String uniqueId);

	void restoreStatus(@Param("targetId") Long targetId,@Param("deleted") Integer deleted);
	void updateBatch(@Param("uniqueIdList") List<String> uniqueIdList);

    void updateBatchByUniqueId(BdmVirtualTarget virtualTarget);
}

