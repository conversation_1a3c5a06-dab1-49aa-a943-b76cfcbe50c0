package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.entity.BdmDeviceCode;
import com.xh.vdm.bi.vo.request.DeviceCodeRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmDeviceCode)表数据库访问层
 */
public interface BdmDeviceCodeMapper extends BaseMapper<BdmDeviceCode> {

    void updateActivatedByDeviceNum(@Param("deviceNumList") List<String> deviceNumList);

	void updateActivated(String deviceNum);

	IPage<BdmDeviceCode> select(@Param("request") DeviceCodeRequest request, IPage<BdmDeviceCode> page, @Param("schema") String schema);

	List<String> selectDeviceNum();
}

