package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.dto.DeviceModelCount;
import com.xh.vdm.bi.dto.DeviceModelInfo;
import com.xh.vdm.biapi.entity.BdmDeviceStatus;
import org.apache.ibatis.annotations.Param;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmDeviceLink)表服务接口
 */
public interface IBdmDeviceStatusService extends IService<BdmDeviceStatus> {
	/**
	 * 统计在线终端数。
	 * @param ceDataAuth 账号权限信息
	 */
	long countOnlineDevices(DataAuthCE ceDataAuth);

	/**
	 * 获取用户监管的设备型号在线统计信息。
	 * @param auth 用户权限信息
	 * @return 按型号分类的终端在线数
	 */
	List<DeviceModelCount> getDeviceModelOnlineByUserAuth(DataAuthCE auth);

	/**
	 * 获取用户监管的设备型号在线统计信息。
	 * @param auth 用户权限
	 * @param models 设备型号列表
	 * @param district 行政区划编码
	 * @param deptId 指定部门id
	 * @param usage 使用类型
	 * @return 按型号分类的终端在线数
	 */
	List<DeviceModelInfo> getDevOnlineInfoWith(DataAuthCE auth, List<String> models,
											   String district, Long deptId, Byte usage);

	/**
	 * 获取用户监管的设备型号在线统计数量。
	 * @param auth 用户权限
	 * @param models 设备型号列表
	 * @param district 行政区划编码
	 * @param deptId 指定部门id
	 * @param usage 使用类型
	 * @return 按型号分类的终端在线数
	 */
	List<DeviceModelCount> getDevOnlineCntWith(DataAuthCE auth, List<String> models,
													String district, Long deptId, Byte usage);

	/**
	 * 统计终端总数。
	 * @param specificity 终端特殊性
	 * @param online 0-在线，1-下线
	 * @param auth 用户权限
	 * @return 符合条件的终端总数
	 */
	long countByUserAuth(Byte specificity, Byte online, DataAuthCE auth);

	/**
	 * 统计指定条件下的终端id列表。
	 * @param specificity 终端特殊性
	 * @param online 0-在线，1-下线
	 * @param auth 用户权限
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param usage 使用类型
	 * @param district 行政区划编码
	 * @return 符合条件的终端id列表
	 */
	List<Long> getDeviceIdsWith(Byte specificity, Byte online, DataAuthCE auth, String district, Long deptId, Byte usage);

	/**
	 * 统计终端总数。
	 * @param specificity 终端特殊性
	 * @param online 0-在线，1-下线
	 * @param auth 用户权限
	 * @param district 行政区划编码
	 * @param deptId 指定部门id
	 * @param usage 使用类型
	 * @return 符合条件的终端总数
	 */
	long countByUserWith(Byte specificity, Byte online, DataAuthCE auth, String district, Long deptId, Byte usage);
}
