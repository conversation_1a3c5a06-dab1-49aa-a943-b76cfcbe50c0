<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.PntDeviceMapper">

    <sql id="Base_Column_List">
        pd.id,
        pd.unique_id,
        pd.imei,
        pd.model,
        pd.vendor,
        pd.bd_chip_sn,
        pd.device_type,
        pd.specificity,
        pd.dept_id,
        pd.target_id,
        pd.category,
        pd.device_num,
        pd.longitude,
        pd.latitude,
        pd.altitude,
        pd.address,
        pd.ref_source,
        pd.clock_signal,
        pd.installdate,
        pd.create_time,
        pd.update_time,
        pd.deleted,
        pd.scenario,
        pd.domain,
        pd.gnss_mode,
        pd.terminal_id,
        d.dept_name
    </sql>

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.PntDeviceResponse">
        SELECT
        pd.id,pd.unique_id,pd.imei,pd.model,pd.vendor,pd.bd_chip_sn,pd.device_type,pd.specificity,pd.dept_id,
        pd.target_id,pd.category,pd.device_num,pd.longitude,pd.latitude,pd.altitude,pd.address,pd.ref_source,pd.clock_signal,
        pd.installdate,pd.scenario,pd.domain,pd.gnss_mode,pd.asset_type,pd.own_dept_type,pd.own_dept_name,d.dept_name, pd.terminal_id,
        ic."number" as numbers, ma.name as vendorName,pd.iot_protocol
        FROM bdm_pnt_device pd
        left join blade_dept d on pd.dept_id = d.id
        LEFT JOIN bdm_iot_card ic ON pd.id = ic.device_id and pd.device_type = ic.device_type
        LEFT JOIN ${schema}.bdc_manufactor ma ON ma.code = pd.vendor and ma.is_del = 0
        WHERE 1 = 1
        and pd.deleted = 0
        <if test="request.ids != null and request.ids.size() gt 0 ">
            and pd.id in
            <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="request.ids == null or request.ids == ''">
            <if test="request.category != null">
                and pd.category = #{request.category}
            </if>
            <if test="request.uniqueId != null and request.uniqueId != ''">
                and pd.unique_id like concat('%', #{request.uniqueId}, '%')
            </if>
            <if test="request.imei != null and request.imei != ''">
                and pd.imei like concat('%', #{request.imei}, '%')
            </if>
            <if test="request.deviceNum != null and request.deviceNum != ''">
                and pd.device_num like concat('%', #{request.deviceNum}, '%')
            </if>
            <if test="request.specificity != null">
                AND pd.specificity = #{request.specificity}
            </if>
            <if test="request.deptId != null">
                AND pd.dept_id = #{request.deptId}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and d.id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and pd.create_account = #{account}
            </if>
        </if>
        order by pd.create_time desc
    </select>

    <!--查询单个-->
    <select id="queryById" resultType="com.xh.vdm.bi.vo.response.PntDeviceResponse">
        SELECT
        <include refid="Base_Column_List"/>
        FROM bdm_pnt_device pd
        left join blade_dept d on pd.dept_id = d.id
        where pd.id = #{id}
    </select>

    <!--新增所有列-->
    <insert id="insertPnt" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.xh.vdm.biapi.entity.BdmPntDevice">
        INSERT INTO bdm_pnt_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                unique_id,
            </if>
            <if test="null != imei and '' != imei">
                imei,
            </if>
            <if test="null != model and '' != model">
                model,
            </if>
            <if test="null != vendor and '' != vendor">
                vendor,
            </if>
            <if test="null != bdChipSn and '' != bdChipSn">
                bd_chip_sn,
            </if>
            <if test="null != deviceType">
                device_type,
            </if>
            <if test="null != specificity">
                specificity,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != targetId">
                target_id,
            </if>
            <if test="null != category">
                category,
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                device_num,
            </if>
            <if test="null != longitude">
                longitude,
            </if>
            <if test="null != latitude">
                latitude,
            </if>
            <if test="null != altitude">
                altitude,
            </if>
            <if test="null != address and '' != address">
                address,
            </if>
            <if test="null != refSource and '' != refSource">
                ref_source,
            </if>
            <if test="null != clockSignal and '' != clockSignal">
                clock_signal,
            </if>
            <if test="null != installdate">
                installdate,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted,
            </if>
            <if test="null != scenario">
                scenario,
            </if>
            <if test="null != domain">
                domain,
            </if>
            <if test="null != gnssMode">
                gnss_mode,
            </if>
            <if test="null != terminalId and '' != terminalId">
                terminal_id,
            </if>
            <if test="null != createAccount and '' != createAccount">
                create_account,
            </if>
            <if test="null != assetType">
                asset_type,
            </if>
            <if test="null != ownDeptType">
                own_dept_type,
            </if>
            <if test="null != ownDeptName">
                own_dept_name
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                #{uniqueId},
            </if>
            <if test="null != imei and '' != imei">
                #{imei},
            </if>
            <if test="null != model and '' != model">
                #{model},
            </if>
            <if test="null != vendor and '' != vendor">
                #{vendor},
            </if>
            <if test="null != bdChipSn and '' != bdChipSn">
                #{bdChipSn},
            </if>
            <if test="null != deviceType">
                #{deviceType},
            </if>
            <if test="null != specificity">
                #{specificity},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != targetId">
                #{targetId},
            </if>
            <if test="null != category">
                #{category},
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                #{deviceNum},
            </if>
            <if test="null != longitude">
                #{longitude},
            </if>
            <if test="null != latitude">
                #{latitude},
            </if>
            <if test="null != altitude">
                #{altitude},
            </if>
            <if test="null != address and '' != address">
                #{address},
            </if>
            <if test="null != refSource and '' != refSource">
                #{refSource},
            </if>
            <if test="null != clockSignal and '' != clockSignal">
                #{clockSignal},
            </if>
            <if test="null != installdate">
                #{installdate},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted},
            </if>
            <if test="null != scenario">
                #{scenario},
            </if>
            <if test="null != domain">
                #{domain},
            </if>
            <if test="null != gnssMode">
                #{gnssMode},
            </if>
            <if test="null != terminalId and '' != terminalId">
                #{terminalId},
            </if>
            <if test="null != createAccount and '' != createAccount">
                #{createAccount,jdbcType=VARCHAR},
            </if>
            <if test="null != assetType">
                #{assetType},
            </if>
            <if test="null != ownDeptType">
                #{ownDeptType},
            </if>
            <if test="null != ownDeptName">
                #{ownDeptName}
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update" parameterType="com.xh.vdm.biapi.entity.BdmPntDevice">
        UPDATE bdm_pnt_device
        <set>
            <if test="null != uniqueId">unique_id = #{uniqueId},</if>
            <if test="null != imei">imei = #{imei},</if>
            <if test="null != model">model = #{model},</if>
            <if test="null != vendor">vendor = #{vendor},</if>
            <if test="null != bdChipSn">bd_chip_sn = #{bdChipSn},</if>
            <if test="null != deviceType">device_type = #{deviceType},</if>
            <if test="null != specificity">specificity = #{specificity},</if>
            <if test="null != deptId">dept_id = #{deptId},</if>
            <if test="null != targetId">target_id = #{targetId},</if>
            <if test="null != category">category = #{category},</if>
            <if test="null != deviceNum">device_num = #{deviceNum},</if>
            <if test="null != longitude">longitude = #{longitude},</if>
            <if test="null != latitude">latitude = #{latitude},</if>
            <if test="null != altitude">altitude = #{altitude},</if>
            <if test="null != address">address = #{address},</if>
            <if test="null != refSource">ref_source = #{refSource},</if>
            <if test="null != clockSignal">clock_signal = #{clockSignal},</if>
            <if test="null != installdate">installdate = #{installdate},</if>
            <if test="installdate == null">installdate = NULL,</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != deleted">deleted = #{deleted},</if>
            <if test="null != scenario">scenario = #{scenario},</if>
            <if test="null != domain">domain = #{domain},</if>
            <if test="null != gnssMode">gnss_mode = #{gnssMode},</if>
            <if test="null != terminalId">terminal_id = #{terminalId}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
        update bdm_pnt_device
        set deleted     = 1,
            update_time = now()
        where id = #{id}
    </update>

    <delete id="deleteByIds">
        update bdm_pnt_device
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>

    <!-- TODO 本人权限-->
    <select id="selectNoBind" resultType="com.xh.vdm.bi.vo.response.FacilityNoBingResponse">
        SELECT
        p.id,
        p.unique_id,
        p.model,
        p.dept_id,
        p.category,
        p.device_type,
        p.device_num,
        p.iot_protocol,
        d.dept_name
        FROM bdm_pnt_device p
        LEFT JOIN blade_dept d on p.dept_id = d.id
        WHERE p.deleted = 0
        and p.target_id = 0
        and p.category = #{request.category}
        <if test="request.uniqueId != null and request.uniqueId != ''">
            AND p.unique_id LIKE concat('%', #{request.uniqueId}, '%')
        </if>
        <if test="request.deptId != null">
            AND p.dept_id = #{request.deptId}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and d.id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and p.create_account = #{account}
        </if>
    </select>

    <select id="selectBindByFacilityId" resultType="com.xh.vdm.bi.vo.response.FacilityNoBingResponse">
        SELECT
        p.id,
        p.unique_id,
        p.model,
        p.dept_id,
        p.category,
        p.device_type,
        p.longitude,
        p.latitude,
        p.device_num,
        p.iot_protocol,
        d.dept_name
        FROM bdm_pnt_device p
        LEFT JOIN blade_dept d on p.dept_id = d.id
        where p.target_id = #{id}
        and p.target_type = #{targetType}
        and p.deleted = 0
        <if test="request.category != null and request.category != ''">
            AND p.category = #{request.category}
        </if>
        <if test="request.deptId != null">
            AND p.dept_id = #{request.deptId}
        </if>
    </select>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO bdm_pnt_device(
        "id","unique_id", "imei", "model", "vendor", "bd_chip_sn", "dept_id", "category","address", "ref_source",
        "installdate", "create_time", "clock_signal", "scenario", "domain", "specificity", "device_num", "gnss_mode","terminal_id"
        ) VALUES
        <foreach collection="list" item="device" separator=",">
            (
            #{device.id}, #{device.uniqueId}, #{device.imei}, #{device.model}, #{device.vendor},
            #{device.bdChipSn}, #{device.deptId}, #{device.category},#{device.address}, #{device.refSource},
            #{device.installdate}, now(), #{device.clockSignal}, #{device.scenario}, #{device.domain},
            #{device.specificity}, #{device.deviceNum}, #{device.gnssMode}, #{device.terminalId}
            )
        </foreach>
    </insert>

    <update id="updateByDeviceId">
        update
            bdm_pnt_device
        set target_id   = 0,
            target_type = 0
        where target_id = #{id}
            and target_type = #{targetType}
            and deleted = 0
    </update>

    <update id="updateBatchByTerminalId">
        update bdm_pnt_device
        <set>
            target_id = #{id},
            <if test="pnt.latitude != null">
                latitude = #{pnt.latitude},
            </if>
            <if test="pnt.longitude != null">
                longitude = #{pnt.longitude},
            </if>
            target_type = #{targetType}
        </set>
        where id = #{pnt.id}
        and deleted = 0
    </update>

    <delete id="deleteByTargetIds">
        update bdm_pnt_device
        set target_id = 0, target_type = 0
        where target_id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" index="i" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>

    <select id="countByUserRole" resultType="java.lang.Long">
        select count(*) from bdm_pnt_device pd
        WHERE pd.deleted = 0
        <if test="deptIds != null and deptIds != ''">
            and pd.dept_id = any(${deptIds})
        </if>
    </select>

    <update id="updateDept">
        update bdm_pnt_device
        set dept_id = #{deptId}
        where target_id = #{id}
          and target_type = #{targetType}

    </update>

    <update id="bindTarget">
        update bdm_monit_device
        set target_id = #{targetId}, target_type = #{targetType}
        where id = #{deviceId}
    </update>
</mapper>

