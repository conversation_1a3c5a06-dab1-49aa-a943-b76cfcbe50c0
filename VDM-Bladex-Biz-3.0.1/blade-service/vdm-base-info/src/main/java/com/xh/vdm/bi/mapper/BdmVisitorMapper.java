package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.VisitorRequest;
import com.xh.vdm.bi.vo.response.VisitorResponse;
import com.xh.vdm.biapi.entity.BdmVisitor;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmVisitor)表数据库访问层
 */
public interface BdmVisitorMapper extends BaseMapper<BdmVisitor> {

	/**
	 * 查询指定行数据
	 *
	 * @param visitor 查询条件
	 * @param page       分页对象
	 * @param account
	 * @param deptIds
	 * @return 对象列表
	 */
	IPage<VisitorResponse> queryByPage(@Param("visitor") VisitorRequest visitor, @Param("page") IPage page, @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 新增数据
	 *
	 * @param bdmVisitor 实例对象
	 * @return 影响行数
	 */
	int insert(VisitorRequest bdmVisitor);

	/**
	 * 批量新增数据（MyBatis原生foreach方法）
	 *
	 * @param entities List<BdmVisitor> 实例对象列表
	 * @return 影响行数
	 */
	int insertBatch(@Param("entities") List<BdmVisitor> entities);

	/**
	 * 修改数据
	 *
	 * @param bdmVisitor 实例对象
	 * @return 影响行数
	 */
	int update(BdmVisitor bdmVisitor);

	int deleteByIds(Long[] ids);

	String selectIdNumberByPattern(String pattern);

	int batchUpdate(@Param("ids") List<Long> ids,@Param("deptId") Long deptId);
}

