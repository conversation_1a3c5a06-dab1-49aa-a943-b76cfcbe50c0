<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmUsualLogMapper">

    <select id="test" resultType="com.xh.vdm.bi.vo.response.UsualLogTreeResponse">
        WITH RECURSIVE menu_tree AS (SELECT DISTINCT bm.id   AS menu_id,
                                                     bm.name AS menu_name,
                                                     bm.parent_id,
                                                     bm.code
                                     FROM bdm_usual_log ul
                                              JOIN
                                          blade_menu bm ON ul.menu = bm.name
                                     WHERE bm.is_deleted = 0
                                     UNION ALL
                                     SELECT DISTINCT bm.id          AS menu_id,
                                                     bm.name        AS menu_name,
                                                     bm.parent_id,
                                                     parent_bm.code AS parent_code
                                     FROM menu_tree mt
                                              JOIN
                                          blade_menu bm ON mt.parent_id = bm.id
                                              LEFT JOIN
                                          blade_menu parent_bm ON bm.parent_id = parent_bm.id)
        SELECT DISTINCT menu_id,
                        menu_name,
                        parent_id
        FROM menu_tree
        ORDER BY menu_id;
    </select>

    <select id="selectOperations" resultType="com.xh.vdm.bi.vo.response.MenuOperationsResponse">
        SELECT menu, array_to_string(ARRAY(SELECT unnest(array_agg(DISTINCT operation)) ),',') as operations
        FROM bdm_usual_log
        GROUP BY menu;
    </select>
</mapper>

