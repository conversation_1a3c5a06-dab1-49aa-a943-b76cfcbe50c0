package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;

import java.util.List;

/**
 * (BdmAbstractTarget)表服务接口
 */
public interface IBdmAbstractTargetService extends IService<BdmAbstractTarget> {

	/**
	 * 批量逻辑删除
	 * @param ids
	 */
    void deleteByIds(Long[] ids);

	/**
	 * 批量插入
	 * @param abstractTargetList
	 */
    void insertBatch(List<BdmAbstractTarget> abstractTargetList);

	void restoreStatus(Long targetId,Integer deleted);
}
