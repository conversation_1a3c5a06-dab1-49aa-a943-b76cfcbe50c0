package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import org.springblade.common.dept.DeptIdAware;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Description: 精密装备管理入参
 */
@Data
@ExcelIgnoreUnannotated
public class PrecisionAssemblyRequest  implements DeptIdAware {
	private Long id;

	@NotNull(message = "装备编号不能为空")
	@Compare("装备编号")
	@ApiModelProperty(value = "装备编号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "装备编号")
	@ColumnWidth(22)
	private String number;

	private Integer targetType;
	@Compare("装备名称")
	private String name;
	@Compare("装备型号")
	private String model;
	@Compare("制造商")
	private String manufacturer;
	@Compare("所属部门id")
	private Long deptId;
	@Override
	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	/**
	 * 错误信息
	 */
	@ApiModelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "错误信息")
	@ColumnWidth(22)
	private String msg;

	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;
	private String terminalCategories;

	/**
	 * 导出动态表头中文名
	 */
	private List headNameList;

	/**
	 * 导出动态表头字段名
	 */
	private List columnNameList;

	/**	 导出数据的id集合*/
	private List<Long> ids;

	@ExcelIgnore
	private String createAccount;
}

