package com.xh.vdm.bi.config;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

@Component
public class ExcelDateConverter implements Converter<Date> {

	@Override
	public Class<?> supportJavaTypeKey() {
		return Converter.super.supportJavaTypeKey();
	}

	@Override
	public CellDataTypeEnum supportExcelTypeKey() {
		return Converter.super.supportExcelTypeKey();
	}

	@Override
	public WriteCellData<?> convertToExcelData(Date value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String dateValue = sdf.format(value);
		return new WriteCellData<>(dateValue);
	}
}
