package com.xh.vdm.bi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.entity.BdmDeviceCode;
import com.xh.vdm.bi.service.BdmDeviceCodeService;
import com.xh.vdm.bi.utils.DictUtil;
import com.xh.vdm.bi.vo.request.DeviceCodeRequest;
import com.xh.vdm.bi.vo.response.BdmDeviceCodeResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description: 设备信息管理 -- 赋码信息回显
 */
@RestController
@RequestMapping("/device/terminal")
@Slf4j
public class TerminalController {

	@Resource
	private BdmDeviceCodeService deviceCodeService;
	@Resource
	private IDictBizClient dictBizClient;
	@Resource
	private DictUtil dictUtil;

	/**
	 * 赋码号列表选择+模糊搜索
	 *
	 * @param deviceCodeRequest 搜索条件
	 * @return
	 */
	@PostMapping("/deviceNum")
	public R<IPage<BdmDeviceCode>> getDeviceNum(@RequestBody DeviceCodeRequest deviceCodeRequest, Query query) {
		IPage<BdmDeviceCode> page = deviceCodeService.select(deviceCodeRequest, query);
		enrichDataWithDict(page.getRecords());
		return R.data(page);
	}

	/**
	 * 获取赋码有关信息（终端型号、厂商名称、IMEI、北斗芯片序列号）
	 *
	 * @param id 主键id
	 * @return
	 */
	@GetMapping("/info")
	public R<BdmDeviceCodeResponse> getTerminal(@RequestParam String id) {
		BdmDeviceCode bdcTerminal = deviceCodeService.getBaseMapper().selectOne(new QueryWrapper<BdmDeviceCode>().eq("device_num", id));
		BdmDeviceCodeResponse codeResponse = new BdmDeviceCodeResponse();
		BeanUtils.copyProperties(bdcTerminal, codeResponse);
		return R.data(codeResponse);
	}

	private void enrichDataWithDict(List<BdmDeviceCode> records) {
		if (records.isEmpty()) {
			return;
		}
		Map<String, String> testDeviceTypeMap = dictUtil.getDictMap(BaseInfoConstants.TEST_DEVICE_TYPE);
		for (BdmDeviceCode response : records) {
			response.setDeviceCateName((response.getKind() == null) ? "" : testDeviceTypeMap.getOrDefault(response.getKind().toString(), ""));
		}
	}

}

