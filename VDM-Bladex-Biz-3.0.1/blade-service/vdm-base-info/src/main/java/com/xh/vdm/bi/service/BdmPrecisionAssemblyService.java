package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.PrecisionAssemblyRequest;
import com.xh.vdm.bi.vo.response.PrecisionAssemblyResponse;
import com.xh.vdm.biapi.entity.BdmPrecisionAssembly;
import org.springblade.core.mp.support.Query;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * @Description:
 */
public interface BdmPrecisionAssemblyService extends IService<BdmPrecisionAssembly> {

	/**
     * 分页查询
     *
     * @param precisionAssemblyRequest 筛选条件
     * @param query                    分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<PrecisionAssemblyResponse> queryByPage(PrecisionAssemblyRequest precisionAssemblyRequest, Query query, DataAuthCE ceDataAuth);

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmPrecisionAssembly insert(PrecisionAssemblyRequest request);

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmPrecisionAssembly update(PrecisionAssemblyRequest request);

	List<PrecisionAssemblyRequest> importExcel(List<PrecisionAssemblyRequest> list);

	Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest);
}
