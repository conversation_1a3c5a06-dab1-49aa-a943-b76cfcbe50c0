package com.xh.vdm.bi.validator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = IntegerValidator.class)
public @interface ValidInteger {

    String message() default "该字段类型错误";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
