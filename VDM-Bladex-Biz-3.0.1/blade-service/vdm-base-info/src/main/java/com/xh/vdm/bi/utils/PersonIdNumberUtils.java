package com.xh.vdm.bi.utils;

import com.xh.vdm.bi.mapper.BdmVisitorMapper;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Description: 访客人员生成虚拟身份证号
 */
@Component
public class PersonIdNumberUtils {

	@Resource
	private BdmVisitorMapper bdmVisitorMapper;

	private String FIRST = "999999";


	/**
	 * 访客人员生成虚拟身份证号
	 *
	 * 虚拟身份证号格式为“999999“+“yyyy-mm-dd“+序号，其中序号为4位16进制数字，比如99999920240507000A
	 * @return
	 */
	public R<String> idNumberProducer() {

		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
		String pattern = dateTimeFormatter.format(LocalDateTime.now());

		//判断当天是否已经在数据库生成过虚拟身份证号
		String idNumberInDB = bdmVisitorMapper.selectIdNumberByPattern(pattern);

		String SERIAL;
		if (idNumberInDB != null && !idNumberInDB.isEmpty()) {
			int serialNumber = Integer.parseInt(idNumberInDB.substring(14), 18) + 1;
			SERIAL = String.format("%04X", serialNumber);
		} else {
			SERIAL = "000A";
		}

		String idNumber = FIRST + pattern + SERIAL;

		return R.data(ResultCode.SUCCESS.getCode(), idNumber, "");
	}


}
