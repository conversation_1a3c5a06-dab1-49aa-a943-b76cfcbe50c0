package com.xh.vdm.bi.vo.response;

import lombok.Data;

import java.io.Serializable;

/**
 * Description: 设备信息缓存字段
 */
@Data
public class DeviceRedisResponse implements Serializable {

	private Long id;

	/**
	 * unique_id
	 */
	private String uniqueId;

	/**
	 * device_type
	 */
	private Integer deviceType;

	/**
	 * category
	 */
	private Integer category;

	/**
	 * 赋码值
	 */
	private String deviceNum;

	private Long deptId;

	private Integer iotProtocol;
}
