<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.FacilityMapper">

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.FacilityResponse">
        SELECT
        f.ID,
        f.NAME,
        f.category,
        f.address,
        f.dept_id,
        f.geometry,
        f.code,
        f.create_time,
        f.target_type,
        d.dept_name,
        STRING_AGG(CAST(bad.category AS TEXT), ',') AS terminalCategories
        FROM bdm_facility f
        LEFT JOIN blade_dept d ON f.dept_id = d.id
        LEFT JOIN bdm_abstract_device bad ON f.ID = bad.target_id AND bad.deleted = 0 and bad.target_type = f.target_type
        WHERE f.deleted = 0
        <if test="request.ids != null and request.ids.size() gt 0 ">
            and f.id in
            <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="request.ids == null or request.ids == ''">
            <if test="request.name != null and request.name != ''">
                and f.name like concat('%',#{request.name},'%')
            </if>
            <if test="request.category != null">
                and f.category = #{request.category}
            </if>
            <if test="request.uniqueId != null and request.uniqueId != ''">
                and bad.unique_id like concat('%',#{request.uniqueId},'%')
            </if>
            <if test="request.deviceNum != null and request.deviceNum != ''">
                and bad.device_num like concat('%',#{request.deviceNum},'%')
            </if>
            <if test="deptIds != null and deptIds != ''">
                and d.id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and f.create_account = #{account}
            </if>

        </if>
        GROUP BY f.ID, f.NAME, f.category, f.address, f.dept_id, f.geometry, f.code, f.create_time, f.target_type, d.dept_name
        ORDER BY f.create_time DESC
    </select>

    <!--新增所有列-->
    <insert id="insertFacility" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.xh.vdm.biapi.entity.BdmFacility">
        INSERT INTO bdm_facility
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != name and '' != name">
                name,
            </if>
            <if test="null != category">
                category,
            </if>
            <if test="null != address and '' != address">
                address,
            </if>
            <if test="null != targetType">
                target_type,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted,
            </if>
            <if test="null != geometry and '' != geometry">
                geometry,
            </if>
            <if test="null != code and '' != code">
                code,
            </if>
            <if test="null != createAccount and '' != createAccount">
                create_account
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != name and '' != name">
                #{name},
            </if>
            <if test="null != category">
                #{category},
            </if>
            <if test="null != address and '' != address">
                #{address},
            </if>
            <if test="null != targetType">
                #{targetType},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted},
            </if>
            <if test="null != geometry and '' != geometry">
                #{geometry},
            </if>
            <if test="null != code and '' != code">
                #{code},
            </if>
            <if test="null != createAccount and '' != createAccount">
                #{createAccount,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        update bdm_facility
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="category != null">
                category = #{category},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="targetType != null">
                target_type = #{targetType},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="null != geometry">
                geometry = #{geometry},
            </if>
            <if test="null != code">
                code = #{code}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteByIds">
        update bdm_facility
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="batchUpdate">
        UPDATE bdm_facility
        <set>
            dept_id = #{deptId}
        </set>
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

    <!--批量插入数据    -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bdm_facility(id, name, category, dept_id, create_time,geometry,code)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id}, #{entity.name}, #{entity.category}, #{entity.deptId}, now(), #{entity.geometry},#{entity.code})
        </foreach>
    </insert>

</mapper>

