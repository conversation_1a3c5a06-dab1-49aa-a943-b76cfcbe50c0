package com.xh.vdm.bi.vo.request;

import lombok.Data;

/**
 * 人员/访客/外包人员管理绑定终端
 */
@Data
public class PersonTerminalRequest {

	/**
	 * 终端ID
	 */
	private Long id;
	/**
	 * 终端类别
	 */
	private Integer deviceType;
	/**
	 * 终端种类/功能类型
	 */
	private Integer category;
	/**
	 * 序列号
	 */
	private String uniqueId;
	/**
	 * 终端型号
	 */
	private String model;
	/**
	 * 所属机构
	 */
	private Long deptId;
	/**
	 * 对象类型
	 */
	private Integer targetType;

	private String deviceNum;

	private Integer iotProtocol;
}

