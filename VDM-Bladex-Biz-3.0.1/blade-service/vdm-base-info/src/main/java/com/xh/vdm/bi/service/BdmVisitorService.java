package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.VisitorRequest;
import com.xh.vdm.bi.vo.response.ConnectResponse;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.VisitorResponse;
import com.xh.vdm.biapi.entity.BdmVisitor;
import org.springblade.core.mp.support.Query;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmVisitor)表服务接口
 */
public interface BdmVisitorService extends IService<BdmVisitor> {

	/**
     * 分页查询
     *
     * @param request    筛选条件
     * @param query      分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<VisitorResponse> queryByPage(VisitorRequest request, Query query, DataAuthCE ceDataAuth);

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmVisitor insert(VisitorRequest request);

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmVisitor update(VisitorRequest request);

	boolean deleteByIds(Long[] ids);

	ConnectResponse connectTerminal(List<PersonTerminalRequest> list, Long id, Long deptId);

	IPage<PersonNoBingResponse> select(DeviceNoBindRequest deviceNoBindRequest, DataAuthCE ceDataAuth);

	List<PersonNoBingResponse> selectBind(DeviceNoBindRequest deviceNoBindRequest, Long id);

	List<VisitorRequest> importExcel(List<VisitorRequest> list);

	Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest);
}
