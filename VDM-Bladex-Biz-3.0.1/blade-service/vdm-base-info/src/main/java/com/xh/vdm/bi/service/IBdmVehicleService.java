package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.*;
import com.xh.vdm.bi.vo.response.ConnectResponse;
import com.xh.vdm.bi.vo.response.VehicleResponse;
import com.xh.vdm.biapi.entity.BdmVehicle;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 */
public interface IBdmVehicleService extends IService<BdmVehicle> {


	IPage<VehicleResponse> queryByPage(VehicleRequest vehicleRequest, DataAuthCE ceDataAuth);

	BdmVehicle insert(VehicleRequest vehicleRequest);

	BdmVehicle update(VehicleRequest vehicleRequest);

	boolean deleteByIds(Long[] ids);

	List<VehicleRequest> importExcel(List<VehicleRequest> list);

	boolean connectTerminal(List<VehicleTerminalRequest> list, Long id, Long deptId);

	ConnectResponse connectUniqueId(String uniqueId, Long id, Long deptId);
	ConnectResponse bindOneTarget(TargetDeviceRequest req);

	Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest);
}
