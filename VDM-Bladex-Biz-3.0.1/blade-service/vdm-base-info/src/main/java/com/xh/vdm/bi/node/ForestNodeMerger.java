package com.xh.vdm.bi.node;

import java.util.List;
import java.util.Objects;

public class ForestNodeMerger {
	public ForestNodeMerger() {
	}

	public static <T extends INode<T>> List<T> merge(List<T> items) {
		ForestNodeManager<T> forestNodeManager = new ForestNodeManager<>(items);
		items.forEach((forestNode) -> {
			if (!Objects.equals(forestNode.getParentId(), "0")) {
				INode<T> node = forestNodeManager.getTreeNodeAt(forestNode.getParentId());
				if (node != null) {
					node.getChildren().add(forestNode);
				} else {
					forestNodeManager.addParentId(forestNode.getId());
				}
			}

		});
		return forestNodeManager.getRoot();
	}

	public static <T extends INode<T>> List<T> mergeWith(List<T> items, List<String> rootIds) {
		ForestNodeManager<T> forestNodeManager = new ForestNodeManager<>(items);
		items.forEach((forestNode) -> {
			if (!Objects.equals(forestNode.getParentId(), "0")) {
				INode<T> node = forestNodeManager.getTreeNodeAt(forestNode.getParentId());
				if (node != null) {
					node.getChildren().add(forestNode);
				}
			}

		});
		return forestNodeManager.getRoots(rootIds);
	}
}
