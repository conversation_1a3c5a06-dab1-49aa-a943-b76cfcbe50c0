<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmPrecisionAssemblyMapper">

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.PrecisionAssemblyResponse">
        select
        a.id, a.number, a.target_type, a.name, a.model, a.manufacturer, a.dept_id, to_char(a.create_time, 'YYYY-MM-DD HH24:MI:SS') as createTime,
        a.update_time, a.deleted, d.dept_name,  rd.unique_id,rd.device_num, rd.category as terminalCategories
        from bdm_precision_assembly  a
        left join blade_dept d on a.dept_id = d.id
        left join bdm_rnss_device rd ON a.id = rd.target_id
        AND rd.deleted = 0 and rd.target_type = a.target_type
        where a.deleted = 0
            <if test="request.ids != null and request.ids.size() gt 0 ">
                and a.id in
                <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="request.ids == null or request.ids == ''">
                <if test="request.number != null and request.number != ''">
                    and a.number  like concat('%', #{request.number}, '%')
                </if>
                <if test="request.deptId != null">
                    and a.dept_id = #{request.deptId}
                </if>
                <if test="request.uniqueId != null and request.uniqueId != ''">
                    and rd.unique_id like concat('%',#{request.uniqueId},'%')
                </if>
                <if test="request.deviceNum != null and request.deviceNum != ''">
                    and rd.device_num like concat('%',#{request.deviceNum},'%')
                </if>
                <if test="deptIds != null and deptIds != ''">
                    and d.id = any(${deptIds})
                </if>
                <if test="account != null and account != ''">
                    and a.create_account = #{account}
                </if>
            </if>
        order by a.create_time desc
    </select>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.xh.vdm.biapi.entity.BdmPrecisionAssembly">
        INSERT INTO bdm_precision_assembly
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != number and '' != number">
                number,
            </if>
            <if test="null != targetType">
                target_type,
            </if>
            <if test="null != name and '' != name">
                name,
            </if>
            <if test="null != model and '' != model">
                model,
            </if>
            <if test="null != manufacturer and '' != manufacturer">
                manufacturer,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted,
            </if>
            <if test="null != createAccount">
                create_account
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != number and '' != number">
                #{number},
            </if>
            <if test="null != targetType">
                #{targetType},
            </if>
            <if test="null != name and '' != name">
                #{name},
            </if>
            <if test="null != model and '' != model">
                #{model},
            </if>
            <if test="null != manufacturer and '' != manufacturer">
                #{manufacturer},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted},
            </if>
            <if test="null != createAccount">
                #{createAccount,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bdm_precision_assembly(id,number, name, model, manufacturer, dept_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.number}, #{entity.name}, #{entity.model}, #{entity.manufacturer},#{entity.deptId})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bdm_precision_assembly
        <set>
            <if test="number != null">
                number = #{number},
            </if>
            <if test="targetType != null">
                target_type = #{targetType},
            </if>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="model != null">
                model = #{model},
            </if>
            <if test="manufacturer != null">
                manufacturer = #{manufacturer},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted}
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="batchUpdate">
        UPDATE bdm_precision_assembly
        <set>
            dept_id = #{deptId}
        </set>
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

</mapper>

