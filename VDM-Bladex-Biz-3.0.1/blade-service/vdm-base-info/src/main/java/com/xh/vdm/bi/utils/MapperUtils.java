package com.xh.vdm.bi.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * 批量插入工具类
 */
public class MapperUtils {

    public static final Integer DEFAULT_CAPACITY = 1000;

    /**
     * 根据目标容量 划分List
     *
     * @param source 准备划分的list
     * @param capacity 划分完成的单个List容量
     * @param <T> 任意实体
     * @return 切分后的list
     */
    public static <T> List<List<T>> splitListByCapacity(List<T> source, int capacity) {
        List<List<T>> result = new ArrayList<>();
        if (source != null) {
            int size = source.size();
            if (size > 0) {
                for (int i = 0; i < size; ) {
                    List<T> value;
                    int end = i + capacity;
                    if (end > size) {
                        end = size;
                    }
                    value = source.subList(i, end);
                    i = end;

                    result.add(value);
                }
            } else {
                result = null;
            }
        } else {
            result = null;
        }
        return result;
    }
}
