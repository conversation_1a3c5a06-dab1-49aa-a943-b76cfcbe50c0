<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.MonitDeviceMapper">

    <sql id="Base_Column_List">
        md.id,
        md.unique_id,
        md.imei,
        md.model,
        md.vendor,
        md.bd_chip_sn,
        md.device_type,
        md.specificity,
        md.dept_id,
        md.target_id,
        md.category,
        md.device_num,
        md.longitude,
        md.latitude,
        md.address,
        md.installdate,
        md.create_time,
        md.update_time,
        md.deleted,
        md.altitude,
        md.scenario,
        md.domain,
        md.gnss_mode,
        md.terminal_id,
        d.dept_name
    </sql>

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.MonitDeviceResponse">
        SELECT
        md.id,md.unique_id,md.imei,md.model,md.vendor,md.bd_chip_sn,md.device_type,
        md.specificity,md.dept_id,md.target_id,md.category,md.device_num,md.longitude,md.latitude,
        md.address,md.installdate,md.altitude,md.scenario,md.domain,md.gnss_mode,md.asset_type,md.own_dept_type,md.own_dept_name,md.terminal_id,
        ic."number" as numbers, ma.name as vendorName, d.dept_name,md.iot_protocol
        FROM bdm_monit_device md
        left join blade_dept d on md.dept_id = d.id
        LEFT JOIN bdm_iot_card ic ON md.id = ic.device_id and md.device_type = ic.device_type
        LEFT JOIN ${schema}.bdc_manufactor ma ON ma.code = md.vendor and ma.is_del = 0
        WHERE 1 = 1
        and md.deleted = 0
        <if test="request.ids != null and request.ids.size() gt 0 ">
            and md.id in
            <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="request.ids == null or request.ids == ''">
            <if test="request.category != null">
                and md.category = #{request.category}
            </if>
            <if test="request.uniqueId != null and request.uniqueId != ''">
                and md.unique_id like concat('%', #{request.uniqueId}, '%')
            </if>
            <if test="request.imei != null and request.imei != ''">
                and md.imei like concat('%', #{request.imei}, '%')
            </if>
            <if test="request.deviceNum != null and request.deviceNum != ''">
                and md.device_num like concat('%', #{request.deviceNum}, '%')
            </if>
            <if test="request.specificity != null">
                AND md.specificity = #{request.specificity}
            </if>
            <if test="request.deptId != null">
                AND md.dept_id = #{request.deptId}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and d.id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and md.create_account = #{account}
            </if>
        </if>
        order by md.create_time desc
    </select>

    <!--查询单个-->
    <select id="detail" resultType="com.xh.vdm.bi.vo.response.MonitDeviceResponse">
        SELECT
        <include refid="Base_Column_List"/>
        FROM bdm_monit_device md
        left join blade_dept d on md.dept_id = d.id
        WHERE md.id = #{id}
    </select>

    <!--新增所有列-->
    <insert id="insertMonit" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.xh.vdm.biapi.entity.BdmMonitDevice">
        INSERT INTO bdm_monit_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                unique_id,
            </if>
            <if test="null != imei and '' != imei">
                imei,
            </if>
            <if test="null != model and '' != model">
                model,
            </if>
            <if test="null != vendor and '' != vendor">
                vendor,
            </if>
            <if test="null != bdChipSn and '' != bdChipSn">
                bd_chip_sn,
            </if>
            <if test="null != deviceType">
                device_type,
            </if>
            <if test="null != specificity">
                specificity,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != targetId">
                target_id,
            </if>
            <if test="null != category">
                category,
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                device_num,
            </if>
            <if test="null != longitude">
                longitude,
            </if>
            <if test="null != latitude">
                latitude,
            </if>
            <if test="null != address and '' != address">
                address,
            </if>
            <if test="null != installdate">
                installdate,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted,
            </if>
            <if test="null != altitude">
                altitude,
            </if>
            <if test="null != scenario">
                scenario,
            </if>
            <if test="null != domain">
                domain,
            </if>
            <if test="null != gnssMode">
                gnss_mode,
            </if>
            <if test="null != terminalId and '' != terminalId">
                terminal_id,
            </if>
            <if test="null != createAccount and '' != createAccount">
                create_account,
            </if>
            <if test="null != assetType">
                asset_type,
            </if>
            <if test="null != ownDeptType">
                own_dept_type,
            </if>
            <if test="null != ownDeptName">
                own_dept_name
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                #{uniqueId},
            </if>
            <if test="null != imei and '' != imei">
                #{imei},
            </if>
            <if test="null != model and '' != model">
                #{model},
            </if>
            <if test="null != vendor and '' != vendor">
                #{vendor},
            </if>
            <if test="null != bdChipSn and '' != bdChipSn">
                #{bdChipSn},
            </if>
            <if test="null != deviceType">
                #{deviceType},
            </if>
            <if test="null != specificity">
                #{specificity},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != targetId">
                #{targetId},
            </if>
            <if test="null != category">
                #{category},
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                #{deviceNum},
            </if>
            <if test="null != longitude">
                #{longitude},
            </if>
            <if test="null != latitude">
                #{latitude},
            </if>
            <if test="null != address and '' != address">
                #{address},
            </if>
            <if test="null != installdate">
                #{installdate},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted},
            </if>
            <if test="null != altitude">
                #{altitude},
            </if>
            <if test="null != scenario">
                #{scenario},
            </if>
            <if test="null != domain">
                #{domain},
            </if>
            <if test="null != gnssMode">
                #{gnssMode},
            </if>
            <if test="null != terminalId and '' != terminalId">
                #{terminalId},
            </if>
            <if test="null != createAccount and '' != createAccount">
                #{createAccount},
            </if>
            <if test="null != assetType">
                #{assetType},
            </if>
            <if test="null != ownDeptType">
                #{ownDeptType},
            </if>
            <if test="null != ownDeptName">
                #{ownDeptName}
            </if>
        </trim>
    </insert>


    <!--通过主键修改数据-->
    <update id="update" parameterType="com.xh.vdm.biapi.entity.BdmMonitDevice">
        UPDATE bdm_monit_device
        <set>
            <if test="null != uniqueId">unique_id = #{uniqueId},</if>
            <if test="null != imei">imei = #{imei},</if>
            <if test="null != model">model = #{model},</if>
            <if test="null != vendor">vendor = #{vendor},</if>
            <if test="null != bdChipSn">bd_chip_sn = #{bdChipSn},</if>
            <if test="null != deviceType">device_type = #{deviceType},</if>
            <if test="null != specificity">specificity = #{specificity},</if>
            <if test="null != deptId">dept_id = #{deptId},</if>
            <if test="null != targetId">target_id = #{targetId},</if>
            <if test="null != category">category = #{category},</if>
            <if test="null != deviceNum">device_num = #{deviceNum},</if>
            <if test="null != longitude">longitude = #{longitude},</if>
            <if test="null != latitude">latitude = #{latitude},</if>
            <if test="null != address">address = #{address},</if>
            <if test="null != installdate">installdate = #{installdate},</if>
            <if test="installdate == null">installdate = NULL,</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != deleted">deleted = #{deleted},</if>
            <if test="null != altitude">altitude = #{altitude},</if>
            <if test="null != scenario">scenario = #{scenario},</if>
            <if test="null != domain">domain = #{domain},</if>
            <if test="null != gnssMode">gnss_mode = #{gnssMode},</if>
            <if test="null != terminalId">terminal_id = #{terminalId}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
        update bdm_monit_device
        set deleted     = 1,
            update_time = now()
        where id = #{id}
    </update>

    <delete id="deleteByIds">
        update bdm_monit_device
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>

    <!-- TODO 本人权限-->
    <select id="selectNoBind" resultType="com.xh.vdm.bi.vo.response.FacilityNoBingResponse">
        SELECT
        t.id,
        t.unique_id,
        t.model,
        t.dept_id,
        t.category,
        t.device_type,
        t.device_num,
        t.iot_protocol,
        d.dept_name
        FROM bdm_monit_device t
        LEFT JOIN blade_dept d on t.dept_id = d.id
        WHERE t.deleted = 0
        and t.target_id = 0
        and t.category = #{request.category}
        <if test="request.uniqueId != null and request.uniqueId != ''">
            AND t.unique_id LIKE concat('%', #{request.uniqueId}, '%')
        </if>
        <if test="request.deptId != null">
            AND t.dept_id = #{request.deptId}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and d.id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and t.create_account = #{account}
        </if>
    </select>

    <select id="selectBindByFacilityId" resultType="com.xh.vdm.bi.vo.response.FacilityNoBingResponse">
        SELECT t.id,
        t.unique_id,
        t.model,
        t.dept_id,
        t.category,
        t.device_type,
        t.longitude,
        t.latitude,
        t.device_num,
        t.iot_protocol,
        d.dept_name
        FROM bdm_monit_device t
        LEFT JOIN blade_dept d on t.dept_id = d.id
        where t.target_id = #{id}
        and t.target_type = #{targetType}
        and t.deleted = 0
        <if test="request.category != null and request.category != ''">
            AND t.category = #{request.category}
        </if>
        <if test="request.deptId != null">
            AND t.dept_id = #{request.deptId}
        </if>
    </select>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO bdm_monit_device (
        "id","unique_id", "imei", "model", "vendor", "bd_chip_sn", "dept_id", "category","address", "installdate",
        "create_time", "scenario", "domain", "specificity", "device_num", "gnss_mode","terminal_id"
        )
        <foreach collection="list" item="device" index="index" separator=",">
            (
            #{device.id}, #{device.uniqueId}, #{device.imei}, #{device.model}, #{device.vendor}, #{device.bdChipSn},
            #{device.deptId},#{device.category}, #{device.address},#{device.installdate}, now(),
            #{device.scenario}, #{device.domain}, #{device.specificity}, #{device.deviceNum}, #{device.gnssMode}, #{device.terminalId}
            )
        </foreach>
    </insert>

    <update id="updateByDeviceId">
        update
            bdm_monit_device
        set target_id   = 0,
            target_type = 0
        where target_id = #{id}
            and target_type = #{targetType}
            and deleted = 0
    </update>

    <update id="updateBatchByTerminalId">
        update bdm_monit_device
        set
            target_id = #{id},
            <if test="monit.latitude != null">
                latitude = #{monit.latitude},
            </if>
            <if test="monit.longitude != null">
                longitude = #{monit.longitude},
            </if>
            target_type = #{targetType}
        where id = #{monit.id}
            and deleted = 0
    </update>

    <delete id="deleteByTargetIds">
        update bdm_monit_device
        set target_id = 0, target_type = 0
        where target_id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" index="i" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        and deleted = 0
    </delete>

    <select id="countByUserRole" resultType="java.lang.Long">
        select count(*) from bdm_monit_device md
        WHERE md.deleted = 0
        <if test="deptIds != null and deptIds != ''">
            and md.dept_id = any(${deptIds})
        </if>
    </select>

    <update id="updateDept">
        update bdm_monit_device
        set dept_id = #{deptId}
        where target_id = #{id}
          and target_type = #{targetType}
    </update>

    <update id="bindTarget">
        update bdm_monit_device
        set target_id = #{targetId}, target_type = #{targetType}
        where id = #{deviceId}
    </update>
</mapper>

