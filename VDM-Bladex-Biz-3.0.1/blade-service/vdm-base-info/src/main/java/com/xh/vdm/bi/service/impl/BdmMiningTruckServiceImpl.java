package com.xh.vdm.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.converter.BdmAbstractTargetConverter;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.mapper.BdmMiningTruckMapper;
import com.xh.vdm.bi.service.IBdmAbstractDeviceService;
import com.xh.vdm.bi.service.IBdmAbstractTargetService;
import com.xh.vdm.bi.service.IBdmMiningTruckService;
import com.xh.vdm.bi.service.RnssDeviceService;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.MiningTruckRequest;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.MiningTruckResponse;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import com.xh.vdm.biapi.entity.BdmMiningTruck;
import com.xh.vdm.biapi.entity.BdmVehicle;
import com.xh.vdm.interManager.feign.IMessageClient;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 矿用卡车管理
 */
@Service
public class BdmMiningTruckServiceImpl extends ServiceImpl<BdmMiningTruckMapper, BdmMiningTruck> implements IBdmMiningTruckService {
	@Resource
	private BdmMiningTruckMapper bdmMiningTruckMapper;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private IMessageClient messageClient;
	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
     * 分页查询
     *
     * @param request    筛选条件
     * @param query      分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	@Override
	public IPage<MiningTruckResponse> queryByPage(MiningTruckRequest request, Query query, DataAuthCE ceDataAuth) {
		IPage page = new Page(query.getCurrent(), query.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.bdmMiningTruckMapper.queryByPage(request, page, response.getAccount(), response.getOrgList());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmMiningTruck insert(MiningTruckRequest request) {
		BdmMiningTruck miningTruck = baseMapper.selectOne(new QueryWrapper<BdmMiningTruck>().eq("number", request.getNumber()));

		BdmMiningTruck bdmMiningTruck = new BdmMiningTruck();
		if (miningTruck == null) {
			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.TRUCK_TARGET_TYPE, 0, 0);
			BeanUtils.copyProperties(request, bdmMiningTruck);
			bdmMiningTruck.setId(targetId.nextId());
			bdmMiningTruck.setCreateTime(new Date());
			bdmMiningTruck.setTargetType(TargetTypeEnum.TRUCK.getSymbol());
			bdmMiningTruck.setCreateAccount(AuthUtil.getUserAccount());
			this.baseMapper.insert(bdmMiningTruck);

			//新增目标同步到bdm_abstract_target
			BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
			BeanUtils.copyProperties(bdmMiningTruck, abstractTarget);
			abstractTarget.setName(bdmMiningTruck.getNumber());
			abstractTarget.setCreateAccount(AuthUtil.getUserAccount());
			abstractTargetService.save(abstractTarget);
		} else {
			if (miningTruck.getDeleted() == 0) {
				throw new RuntimeException("车辆编号已存在: " + request.getNumber());
			} else {
				BeanUtils.copyProperties(request, bdmMiningTruck, "id");
				bdmMiningTruck.setCreateTime(new Date());
				bdmMiningTruck.setDeleted(0);
				bdmMiningTruck.setId(miningTruck.getId());
				bdmMiningTruck.setTargetType(TargetTypeEnum.TRUCK.getSymbol());
				this.baseMapper.updateById(bdmMiningTruck);

				//新增目标同步到bdm_abstract_target
				BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
				BeanUtils.copyProperties(bdmMiningTruck, abstractTarget);
				abstractTarget.setName(bdmMiningTruck.getNumber());
				abstractTargetService.updateById(abstractTarget);
			}
		}

		BdmMiningTruck truck = this.getBaseMapper().selectById(bdmMiningTruck.getId());

		Map<String, String> map = new HashMap<>();
		String key = BaseInfoConstants.TRUCK_TARGET_TYPE + "-" + truck.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", truck.getNumber());
		innerMap.put("targetType", truck.getTargetType());
		innerMap.put("targetCategory", truck.getCategory());
		innerMap.put("deptId", truck.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setTargetId(truck.getId());
		deviceInfo.setTargetName(truck.getNumber());
		deviceInfo.setTargetCategory(truck.getCategory());
		deviceInfo.setDeptId(truck.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_mining_truck", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("矿用卡车信息更新消息发送到kafka失败：" + e);
		}

		BdmVehicle vehicle = new BdmVehicle();
		BeanUtils.copyProperties(truck, vehicle);
		try {
			messageClient.vehicle(CommonConstant.OPER_TYPE_ADD, vehicle);
		} catch (Exception e) {
			log.error("人员信息更新消息发送到kafka失败：" + e.getMessage());
		}
		return truck;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmMiningTruck update(MiningTruckRequest request) {
		BdmMiningTruck miningTruckInDB = baseMapper.selectById(request.getId());

		BdmMiningTruck miningTruck = new BdmMiningTruck();
		miningTruck.setId(request.getId());
		miningTruck.setNumber(request.getNumber() != null ? request.getNumber() : "");
		miningTruck.setCategory(request.getCategory() != null ? request.getCategory() : 0);
		miningTruck.setTargetType(request.getTargetType() != null ? request.getTargetType() : TargetTypeEnum.TRUCK.getSymbol());
		miningTruck.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		miningTruck.setVin(request.getVin() != null ? request.getVin() : "");
		miningTruck.setMaxPower(request.getMaxPower() != null ? request.getMaxPower() : 0);
		miningTruck.setManufacturer(request.getManufacturer() != null ? request.getManufacturer() : "");
		miningTruck.setRatedLoad(request.getRatedLoad() != null ? request.getRatedLoad() : 0);
		miningTruck.setModel(request.getModel() != null ? request.getModel() : "");
		miningTruck.setUpdateTime(new Date());
		baseMapper.update(miningTruck);

		BdmMiningTruck truck = this.baseMapper.selectById(request.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(truck, abstractTarget);
		abstractTarget.setName(truck.getNumber());
		abstractTargetService.updateById(abstractTarget);

		String key = truck.getTargetType() + "-" + truck.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", truck.getNumber());
		innerMap.put("targetType", truck.getTargetType());
		innerMap.put("targetCategory", truck.getCategory());
		innerMap.put("deptId", truck.getDeptId());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setTargetId(truck.getId());
		deviceInfo.setTargetName(truck.getNumber());
		deviceInfo.setTargetCategory(truck.getCategory());
		deviceInfo.setTargetType(truck.getTargetType());
		deviceInfo.setDeptId(truck.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_mining_truck", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("矿用卡车信息更新消息发送到kafka失败：" + e);
		}

		BdmVehicle vehicle = new BdmVehicle();
		BeanUtils.copyProperties(truck, vehicle);
		try {
			messageClient.vehicle(CommonConstant.OPER_TYPE_UPDATE, vehicle);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		//@zhouxw modify 2025-06-27 修改监管对象，终端不再同步更改组织
		/*if(!miningTruckInDB.getDeptId().equals(truck.getDeptId())){
			// 对于RnssDevice的更新
			rnssDeviceService.updateDept(truck.getId(),truck.getTargetType(),truck.getDeptId());

			// 对于BdmAbstractDevice的更新
			abstractDeviceService.updateDept(truck.getId(),truck.getTargetType(),truck.getDeptId());
		}*/

		return truck;
	}

	@Override
	public boolean deleteByIds(Long[] ids) {
		List<BdmMiningTruck> miningTrucks = baseMapper.selectList(new QueryWrapper<BdmMiningTruck>().in("id", ids));

		boolean result = this.baseMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.TRUCK_TARGET_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + key);
			}
			this.rnssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.TRUCK.getSymbol());

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.DELETE);
			Long lastId = ids[ids.length - 1];
			deviceInfo.setTargetId(lastId);
			deviceInfo.setTargetIds(CollUtil.newHashSet(ids));
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_mining_truck", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("矿用卡车信息更新消息发送到kafka失败：" + e);
			}
			// messageClient
			List<BdmVehicle> vehicles = miningTrucks.stream().map(miningTruck -> BeanUtil.copy(miningTruck, BdmVehicle.class)).collect(Collectors.toList());
			try {
				messageClient.vehicleBatch(CommonConstant.OPER_TYPE_UPDATE, vehicles);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
			//更新bdm_abstract_target
			abstractTargetService.deleteByIds(ids);
			//更新bdm_abstract_device
			abstractDeviceService.deleteByTargetIds(ids, TargetTypeEnum.TRUCK.getSymbol());
		}
		return result;
	}

	@Override
	public List<MiningTruckRequest> importExcel(List<MiningTruckRequest> list) {
		//重复的的数据
		List<MiningTruckRequest> duplicateRequests = getDuplicateRequests(list);
		List<MiningTruckRequest> requests = new ArrayList<>(list);
		// 去重后的数据（去除所有重复数据）
		requests.removeAll(duplicateRequests);

		if (!requests.isEmpty()) {

			List<String> collect = requests.stream().map(MiningTruckRequest::getNumber).collect(Collectors.toList());
			QueryWrapper<BdmMiningTruck> wrapper = new QueryWrapper<>();
			if (!collect.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(wrapper, "number", collect);
			}
			Map<String, BdmMiningTruck> truckMap = this.baseMapper.selectList(wrapper)
					.stream()
					.collect(Collectors.toMap(BdmMiningTruck::getNumber, BdmMiningTruck -> BdmMiningTruck));

			List<BdmMiningTruck> miningTruckList = new ArrayList<>();
			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.TRUCK_TARGET_TYPE, 0, 0);
			List<Long> arrayList = new ArrayList<>();
			List<BdmAbstractTarget> bdmAbstractTargets = new ArrayList<>();
			Map<String, String> map = new HashMap<>();

			for (MiningTruckRequest request : requests) {
				/*Optional<BdmMiningTruck> existingCard = miningTrucks.stream()
					.filter(truck -> truck.getNumber().equals(request.getNumber()))
					.findFirst();*/
				BdmMiningTruck bdmMiningTruck = truckMap.get(request.getNumber());

				if (bdmMiningTruck != null) {
					if (bdmMiningTruck.getDeleted() == 1) {
						BeanUtils.copyProperties(request, bdmMiningTruck, "id");
						bdmMiningTruck.setCreateTime(new Date());
						bdmMiningTruck.setDeleted(0);
						bdmMiningTruck.setTargetType(TargetTypeEnum.TRUCK.getSymbol());
						this.baseMapper.updateById(bdmMiningTruck);

						arrayList.add(bdmMiningTruck.getId());
						request.setId(bdmMiningTruck.getId());

						String key = BaseInfoConstants.TRUCK_TARGET_TYPE + "-" + bdmMiningTruck.getId();
						Map<String, Object> innerMap = new HashMap<>();
						innerMap.put("targetName", bdmMiningTruck.getNumber());
						innerMap.put("targetType", bdmMiningTruck.getTargetType());
						innerMap.put("targetCategory", bdmMiningTruck.getCategory());
						innerMap.put("deptId", bdmMiningTruck.getDeptId());
						try {
							map.put(key, new ObjectMapper().writeValueAsString(innerMap));
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}

						//新增目标同步到bdm_abstract_target
						BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
						BeanUtils.copyProperties(bdmMiningTruck, abstractTarget);
						abstractTarget.setName(bdmMiningTruck.getNumber());
						abstractTargetService.updateById(abstractTarget);

					} else {
						request.setMsg("车辆编号已存在");
						duplicateRequests.add(request);
					}
				} else {
					BdmMiningTruck miningTruck = new BdmMiningTruck();
					miningTruck.setId(targetId.nextId());
					miningTruck.setNumber(request.getNumber() != null ? request.getNumber() : "");
					miningTruck.setCategory(request.getCategory() != null ? request.getCategory() : 0);
					miningTruck.setTargetType(TargetTypeEnum.TRUCK.getSymbol());
					miningTruck.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
					miningTruck.setVin(request.getVin() != null ? request.getVin() : "");
					miningTruck.setMaxPower(request.getMaxPower() != null ? request.getMaxPower() : 0);
					miningTruck.setManufacturer(request.getManufacturer() != null ? request.getManufacturer() : "");
					miningTruck.setRatedLoad(request.getRatedLoad() != null ? request.getRatedLoad() : 0);
					miningTruck.setModel(request.getModel() != null ? request.getModel() : "");
					miningTruck.setCreateTime(new Date());
					miningTruck.setDeleted(0);
					miningTruckList.add(miningTruck);

					request.setId(miningTruck.getId());
					arrayList.add(miningTruck.getId());

					String key = miningTruck.getTargetType() + "-" + miningTruck.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", miningTruck.getNumber());
					innerMap.put("targetType", miningTruck.getTargetType());
					innerMap.put("targetCategory", miningTruck.getCategory());
					innerMap.put("deptId", miningTruck.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}

					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(miningTruck, abstractTarget);
					abstractTarget.setName(miningTruck.getNumber());
					bdmAbstractTargets.add(abstractTarget);
				}
			}

			if (!miningTruckList.isEmpty()) {
				//将剩余的数据批量插入数据库
				MapperUtils.splitListByCapacity(miningTruckList, MapperUtils.DEFAULT_CAPACITY)
					.forEach(bdmMiningTrucks -> this.baseMapper.insertBatch(bdmMiningTrucks));

				MapperUtils.splitListByCapacity(bdmAbstractTargets, MapperUtils.DEFAULT_CAPACITY)
					.forEach(abstractTargetList -> this.abstractTargetService.insertBatch(abstractTargetList));

				// todo：Map<String, String> map这样写go那边解析有问题
				redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

				BdmMiningTruck last = miningTruckList.get(miningTruckList.size() - 1);
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.IMPORT);
				deviceInfo.setTargetId(last.getId());
				Set<Long> ids = miningTruckList.stream().map(BdmMiningTruck::getId).collect(Collectors.toSet());
				deviceInfo.setTargetIds(ids);
				deviceInfo.setTargetType(last.getTargetType());
				deviceInfo.setTargetCategory(last.getCategory());
				deviceInfo.setTargetName(last.getNumber());
				deviceInfo.setDeptId(last.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_mining_truck", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("矿用卡车信息更新消息发送到kafka失败", e);
				}

				QueryWrapper<BdmMiningTruck> queryWrapper = new QueryWrapper<>();
				if (!arrayList.isEmpty()) {
					MybatisPlusWrapperUitls.longListIn(queryWrapper, "id", arrayList);
					List<BdmMiningTruck> bdmMiningTrucks = baseMapper.selectList(queryWrapper);
					//messageClient
					List<BdmVehicle> vehicles = bdmMiningTrucks.stream().map(miningTruck -> BeanUtil.copy(miningTruck, BdmVehicle.class)).collect(Collectors.toList());
					try {
						messageClient.vehicleBatch(CommonConstant.OPER_TYPE_UPDATE, vehicles);
					} catch (Exception e) {
						log.error("消息发送到messageClient失败：" + e.getMessage());
					}
				}
			}
		}
		return duplicateRequests;
	}

	/**
	 * 批量更新所属机构
	 * @param batchUpdateRequest
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest) {
		int update = bdmMiningTruckMapper.batchUpdate(batchUpdateRequest.getIds(),batchUpdateRequest.getDeptId());
		if (update == 0) {
			return false;
		}
		//查询出所有的本次要更新的字段
		List<BdmMiningTruck> bdmMiningTruckList = bdmMiningTruckMapper.selectList(new LambdaQueryWrapper<BdmMiningTruck>().in(BdmMiningTruck::getId, batchUpdateRequest.getIds()));
		//转换
		List<BdmAbstractTarget> bdmAbstractTargetList = bdmMiningTruckList.stream().map(BdmAbstractTargetConverter::toBdmMiningTruck).collect(Collectors.toList());
		//更新主表
		abstractTargetService.saveOrUpdateBatch(bdmAbstractTargetList);
		//批量更新redis和kafka
		batchProcess(bdmMiningTruckList);
		if (update > 0){
			return true;
		}
		return false;
	}

	/**
	 * 批量更新redis
	 * 批量发送kafka
	 */
	private void batchProcess(List<BdmMiningTruck> bdmMiningTruckList){
		for (BdmMiningTruck truck : bdmMiningTruckList) {
			String key = truck.getTargetType() + "-" + truck.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("targetName", truck.getNumber());
			innerMap.put("targetType", truck.getTargetType());
			innerMap.put("targetCategory", truck.getCategory());
			innerMap.put("deptId", truck.getDeptId());
			Map<String, String> map = new HashMap<>();
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.OTHER);
			deviceInfo.setTargetId(truck.getId());
			deviceInfo.setTargetName(truck.getNumber());
			deviceInfo.setTargetCategory(truck.getCategory());
			deviceInfo.setTargetType(truck.getTargetType());
			deviceInfo.setDeptId(truck.getDeptId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_mining_truck", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("矿用卡车信息更新消息发送到kafka失败：" + e);
			}

			BdmVehicle vehicle = new BdmVehicle();
			BeanUtils.copyProperties(truck, vehicle);
			try {
				messageClient.vehicle(CommonConstant.OPER_TYPE_UPDATE, vehicle);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

	public List<MiningTruckRequest> getDuplicateRequests(List<MiningTruckRequest> list) {
		Map<String, Long> numberCountMap = list.stream()
			.map(MiningTruckRequest::getNumber)
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<String> duplicateNumbers = numberCountMap.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		return list.stream()
			.filter(miningTruckRequest -> duplicateNumbers.contains(miningTruckRequest.getNumber()))
			.peek(miningTruckRequest -> miningTruckRequest.setMsg("车辆编号重复"))
			.collect(Collectors.toList());
	}

}
