package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.ContainerRequest;
import com.xh.vdm.bi.vo.response.ContainerResponse;
import com.xh.vdm.biapi.entity.BdmContainer;
import org.springblade.core.mp.support.Query;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * @Description: 集装箱管理
 */
public interface BdmContainerService extends IService<BdmContainer> {

	/**
     * 分页查询
     *
     * @param container  筛选条件
     * @param query      分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<ContainerResponse> queryByPage(ContainerRequest container, Query query, DataAuthCE ceDataAuth);

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmContainer insert(ContainerRequest request);

	/**
	 * 修改数据
	 *
	 * @param bdmContainer 实例对象
	 * @return 实例对象
	 */
	BdmContainer update(ContainerRequest bdmContainer);

	/**
	 * 通过主键删除数据
	 *
	 * @param ids 主键
	 * @return 是否成功
	 */
	boolean deleteByIds(Long[] ids);

	/**
	 * 批量新增
	 *
	 * @param list
	 * @return 是否成功
	 */
	List<ContainerRequest> importExcel(List<ContainerRequest> list);


	Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest);
}
