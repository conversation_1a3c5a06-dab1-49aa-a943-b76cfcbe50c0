<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmDeviceLedgerMapper">

    <resultMap type="com.xh.vdm.biapi.entity.BdmDeviceLedger" id="BdmDeviceLedgerMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="uniqueId" column="unique_id" jdbcType="VARCHAR"/>
        <result property="imei" column="imei" jdbcType="VARCHAR"/>
        <result property="model" column="model" jdbcType="VARCHAR"/>
        <result property="vendor" column="vendor" jdbcType="VARCHAR"/>
        <result property="bdChipSn" column="bd_chip_sn" jdbcType="VARCHAR"/>
        <result property="deviceType" column="device_type" jdbcType="INTEGER"/>
        <result property="category" column="category" jdbcType="INTEGER"/>
        <result property="deviceNum" column="device_num" jdbcType="VARCHAR"/>
        <result property="storageState" column="storage_state" jdbcType="INTEGER"/>
        <result property="buytime" column="buytime" jdbcType="TIMESTAMP"/>
        <result property="entryTime" column="entry_time" jdbcType="TIMESTAMP"/>
        <result property="deliveryTime" column="delivery_time" jdbcType="TIMESTAMP"/>
        <result property="userDeptId" column="user_dept_id" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        unique_id,
        imei,
        model,
        vendor,
        bd_chip_sn,
        device_type,
        category,
        device_num,
        storage_state,
        buytime,
        entry_time,
        delivery_time,
        user_dept_id
    </sql>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bdm_device_ledger(unique_id, imei, model, vendor, bd_chip_sn, device_type, category, device_num,
        buytime, type, inputer)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.uniqueId}, #{entity.imei}, #{entity.model}, #{entity.vendor}, #{entity.bdChipSn},
            #{entity.deviceType}, #{entity.category}, #{entity.deviceNum}, #{entity.buytime}, #{entity.type},
            #{entity.inputer})
        </foreach>
    </insert>

    <update id="updateByIds">
        update bdm_device_ledger
        set storage_state = 1,
            user_dept_id = #{userDeptId},
            delivery_time = now(),
            outputer = #{userId}
        where id in
        <if test="ids != null and ids.size() > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

    <select id="queryAll" resultType="com.xh.vdm.biapi.entity.BdmDeviceLedger">
        SELECT l.*, d.dept_name, dc.activated AS back_status, u.account as inputerName, us.account as outputerName,ma.name as
        vendorName
        FROM bdm_device_ledger l
        left join blade_dept d on l.user_dept_id = d.id
        left join bdm_device_code dc on l.device_num = dc.device_num
        left join blade_user u on l.inputer = u.id
        left join blade_user us on l.outputer = us.id
        left join ${schema}.bdc_manufactor ma on ma.code = l.vendor
        <where>
            <if test="request.deviceNum != null and request.deviceNum != ''">
                AND l.device_num LIKE CONCAT('%', #{request.deviceNum}, '%')
            </if>
            <if test="request.uniqueId != null and request.uniqueId != ''">
                AND l.unique_id LIKE CONCAT('%', #{request.uniqueId}, '%')
            </if>
            <if test="request.model != null and request.model != ''">
                AND l.model LIKE CONCAT('%', #{request.model}, '%')
            </if>
            <if test="request.imei != null and request.imei != ''">
                AND l.imei LIKE CONCAT('%', #{request.imei}, '%')
            </if>
            <if test="request.bdChipSn != null and request.bdChipSn != ''">
                AND l.bd_chip_sn LIKE CONCAT('%', #{request.bdChipSn}, '%')
            </if>
            <if test="request.vendor != null and request.vendor != ''">
                AND ma.name LIKE CONCAT('%', #{request.vendor}, '%')
            </if>
            <if test="request.category != null">
                AND l.category = #{request.category}
            </if>
            <if test="request.storageState != null">
                AND l.storage_state = #{request.storageState}
            </if>
            <if test="request.deviceType != null">
                AND l.device_type = #{request.deviceType}
            </if>
            <if test="request.userDeptId != null">
                AND l.user_dept_id = #{request.userDeptId}
            </if>
            <if test="request.status != null and request.status == 1">
                AND dc.activated = 0
            </if>
        </where>
        order by l.delivery_time desc,l.entry_time desc
    </select>

    <update id="recycle">
        update bdm_device_ledger
        set storage_state = #{deviceLedger.storageState},
            user_dept_id  = #{deviceLedger.userDeptId},
            delivery_time = #{deviceLedger.deliveryTime}
        where id = #{deviceLedger.id}
    </update>

    <update id="updateByUniqueId">
        UPDATE bdm_device_ledger
        set delivery_time = #{deliveryTime},
            buytime = #{buytime},
            user_dept_id = #{userDeptId},
        <if test="null != uniqueId">unique_id = #{uniqueId},</if>
        <if test="null != imei">imei = #{imei},</if>
        <if test="null != model">model = #{model},</if>
        <if test="null != vendor">vendor = #{vendor},</if>
        <if test="null != bdChipSn">bd_chip_sn = #{bdChipSn},</if>
        <if test="null != deviceType">device_type = #{deviceType},</if>
        <if test="null != category">category = #{category},</if>
        <if test="null != deviceNum">device_num = #{deviceNum},</if>
        <if test="null != storageState">storage_state = #{storageState},</if>
        <if test="null != entryTime">entry_time = #{entryTime},</if>
        <if test="null != type">type = #{type},</if>
        <if test="null != inputer">inputer = #{inputer},</if>
        <if test="null != outputer">outputer = #{outputer}</if>
        WHERE  id = #{id}
    </update>

    <select id="selectDevice" resultType="java.lang.Object">
        SELECT unique_id
        FROM bdm_wearable_device
        WHERE unique_id = #{uniqueId}
          AND deleted = 0
        UNION
        SELECT unique_id
        FROM bdm_rnss_device
        WHERE unique_id = #{uniqueId}
          AND deleted = 0
        UNION
        SELECT unique_id
        FROM bdm_rdss_device
        WHERE unique_id = #{uniqueId}
          AND deleted = 0
        UNION
        SELECT unique_id
        FROM bdm_pnt_device
        WHERE unique_id = #{uniqueId}
          AND deleted = 0
        UNION
        SELECT unique_id
        FROM bdm_monit_device
        WHERE unique_id = #{uniqueId}
          AND deleted = 0
    </select>

    <select id="query" resultType="com.xh.vdm.biapi.entity.BdmDeviceLedger">
        SELECT l.*, d.dept_name, u.name as inputerName, us.name as outputerName,ma.name as
        vendorName
        FROM bdm_device_ledger l
        left join blade_dept d on l.user_dept_id = d.id
        left join blade_user u on l.inputer = u.id
        left join blade_user us on l.outputer = us.id
        left join ${schema}.bdc_manufactor ma on ma.code = l.vendor
        <if test="request.deviceType != null and request.deviceType == 1">
            left join bdm_rnss_device brd on brd.unique_id = l.unique_id and brd.deleted = 0
        </if>
        <if test="request.deviceType != null and request.deviceType == 2">
            left join bdm_wearable_device bwd on bwd.unique_id = l.unique_id and bwd.deleted = 0
        </if>
        <if test="request.deviceType != null and request.deviceType == 3">
            left join bdm_rdss_device rd on rd.unique_id = l.unique_id and rd.deleted = 0
        </if>
        <if test="request.deviceType != null and request.deviceType == 4">
            left join bdm_monit_device bmd on bmd.unique_id = l.unique_id and bmd.deleted = 0
        </if>
        <if test="request.deviceType != null and request.deviceType == 5">
            left join bdm_pnt_device bpd on bpd.unique_id = l.unique_id and bpd.deleted = 0
        </if>
        where 1 = 1
            <if test="request.deviceType != null and request.deviceType == 1">
                 and brd.unique_id is null
            </if>
            <if test="request.deviceType != null and request.deviceType == 2">
                 and bwd.unique_id is null
            </if>
            <if test="request.deviceType != null and request.deviceType == 3">
                and rd.unique_id is null
            </if>
            <if test="request.deviceType != null and request.deviceType == 4">
                 and bmd.unique_id is null
            </if>
            <if test="request.deviceType != null and request.deviceType == 5">
                 and bpd.unique_id is null
            </if>
            <if test="request.deviceNum != null and request.deviceNum != ''">
                AND l.device_num LIKE CONCAT('%', #{request.deviceNum}, '%')
            </if>
            <if test="request.uniqueId != null and request.uniqueId != ''">
                AND l.unique_id LIKE CONCAT('%', #{request.uniqueId}, '%')
            </if>
            <if test="request.model != null and request.model != ''">
                AND l.model LIKE CONCAT('%', #{request.model}, '%')
            </if>
            <if test="request.imei != null and request.imei != ''">
                AND l.imei LIKE CONCAT('%', #{request.imei}, '%')
            </if>
            <if test="request.bdChipSn != null and request.bdChipSn != ''">
                AND l.bd_chip_sn LIKE CONCAT('%', #{request.bdChipSn}, '%')
            </if>
            <if test="request.vendor != null and request.vendor != ''">
                AND ma.name LIKE CONCAT('%', #{request.vendor}, '%')
            </if>
            <if test="request.category != null">
                AND l.category = #{request.category}
            </if>
            <if test="request.storageState != null">
                AND l.storage_state = #{request.storageState}
            </if>
            <if test="request.deviceType != null">
                AND l.device_type = #{request.deviceType}
            </if>
            <if test="request.userDeptId != null">
                AND l.user_dept_id = #{request.userDeptId}
            </if>
        order by l.delivery_time desc,l.entry_time desc
    </select>

    <update id="update">
        UPDATE bdm_device_ledger
        set
        <if test="null != uniqueId">unique_id = #{uniqueId},</if>
        <if test="null != imei">imei = #{imei},</if>
        <if test="null != model">model = #{model},</if>
        <if test="null != vendor">vendor = #{vendor},</if>
        <if test="null != bdChipSn">bd_chip_sn = #{bdChipSn},</if>
        <if test="null != deviceType">device_type = #{deviceType},</if>
        <if test="null != category">category = #{category},</if>
        <if test="null != deviceNum">device_num = #{deviceNum},</if>
        <if test="null != storageState">storage_state = #{storageState},</if>
        <if test="null != entryTime">entry_time = #{entryTime},</if>
        <if test="null != userDeptId">user_dept_id = #{userDeptId},</if>
        <if test="null != type">type = #{type},</if>
        <if test="null != inputer">inputer = #{inputer},</if>
        <if test="null != outputer">outputer = #{outputer},</if>
        buytime = #{buytime}
        WHERE id = #{id}
    </update>

</mapper>

