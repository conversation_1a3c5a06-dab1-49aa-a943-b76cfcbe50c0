package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.RnssDeviceRequest;
import com.xh.vdm.bi.vo.request.VehicleTerminalRequest;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.RnssDeviceResponse;
import com.xh.vdm.bi.vo.response.WorkerBingResponse;
import com.xh.vdm.biapi.entity.BdmRnssDevice;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * @Description: 短报文终端管理
 */
public interface RnssDeviceService extends IService<BdmRnssDevice> {

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	RnssDeviceResponse queryById(Long id);

	/**
     * 分页查询
     *
     * @param request     筛选条件
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<RnssDeviceResponse> queryByPage(RnssDeviceRequest request, DataAuthCE ceDataAuth);

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmRnssDevice insert(RnssDeviceRequest request);


	BdmRnssDevice insertRnssDevice(RnssDeviceRequest request);

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmRnssDevice update(RnssDeviceRequest request);

	BdmRnssDevice updateRnssDevice(RnssDeviceRequest request);

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	boolean deleteById(Long id);

    boolean deleteByIds(Long[] ids);

	List<RnssDeviceRequest> importExcel(List<RnssDeviceRequest> list, Long userId);

    void updateByWorkerId(Long id, Integer targetType);

	void updateByVehicleId(Long id, Integer targetType);

	void connectWorkerTerminal(List<PersonTerminalRequest> personTerminalRequestList, Long id, Integer targetType, String targetName, Long deptId);

	void connectVehicleTerminal(List<VehicleTerminalRequest> personTerminalRequestList, Long id, Integer targetType, String targetName, Long deptId);

	List<PersonNoBingResponse> selectBindByWorkId(Long id, Integer targetType);

    void deleteByTargetIds(Long[] ids, Integer targetType);

    List<WorkerBingResponse> selectByWorkId(Long id, Integer targetType);

    int unbind(Long id, Integer targetType);

	int bindTarget(Long deviceId, Long targetId, Integer targetType,String targetName);

    long countByUserRole(DataAuthCE ceDataAuth);

	void updateDept(Long id, Integer targetType, Long deptId);
}
