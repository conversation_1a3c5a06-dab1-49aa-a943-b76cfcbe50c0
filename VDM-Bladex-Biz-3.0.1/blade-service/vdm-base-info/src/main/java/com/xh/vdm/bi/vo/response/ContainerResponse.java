package com.xh.vdm.bi.vo.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

/**
 * Description: 集装箱返参
 */
@Data
public class ContainerResponse {

	private Long id;
	@Compare("集装箱编号")
	private String number;
	@Compare("箱型")
	private Integer model;
	@Compare("尺寸，单位英尺")
	private Integer size;
	@Compare("所属部门id")
	private Long deptId;
	@Compare("总重，单位kg")
	private Float maxGross;
	@Compare("自重/皮重，单位kg")
	private Float tare;
	@Compare("载重/净重，单位kg")
	private Float net;
	@Compare("最大装货容积，单位m³")
	private Integer cuCap;
	@Compare("长度，单位mm")
	private Integer length;
	@Compare("高度，单位mm")
	private Integer height;
	@ExcelProperty(value = "创建时间",index = 5)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String createTime;

	private String updateTime;

	private Integer deleted;

	private Integer targetType;
	//所属机构名称
	private String deptName;
	//绑定终端类型
	private String terminalCategories;
	//绑定序列号
	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;
}
