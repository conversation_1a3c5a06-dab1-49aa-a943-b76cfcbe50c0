package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.TrainCargoBoxRequest;
import com.xh.vdm.bi.vo.response.TrainCargoBoxResponse;
import com.xh.vdm.biapi.entity.BdmTrainCargoBox;
import org.springblade.core.mp.support.Query;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmTrainCargoBox)表服务接口
 */
public interface BdmTrainCargoBoxService extends IService<BdmTrainCargoBox> {

	/**
     * 分页查询
     *
     * @param trainCargoBoxRequest 筛选条件
     * @param query                分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<TrainCargoBoxResponse> queryByPage(TrainCargoBoxRequest trainCargoBoxRequest, Query query, DataAuthCE ceDataAuth);

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmTrainCargoBox insert(TrainCargoBoxRequest request);

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmTrainCargoBox update(TrainCargoBoxRequest request);

	List<TrainCargoBoxRequest> importExcel(List<TrainCargoBoxRequest> list);

	Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest);
}
