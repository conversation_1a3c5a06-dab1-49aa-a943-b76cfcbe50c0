package com.xh.vdm.bi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xh.vdm.bi.entity.UserDeptRegulates;
import com.xh.vdm.bi.service.IUserDeptRegulatesService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 监管部门列表
 */
@RestController
@RequestMapping("/device/user/dept")
@Slf4j
public class UserDeptRegulatesController {

	@Resource
	private IUserDeptRegulatesService userDeptRegulatesService;

	@GetMapping("/regulates")
	public R<List<Long>> regulates(BladeUser user) {
		List<Long> list = userDeptRegulatesService.getBaseMapper()
			.selectList(new QueryWrapper<UserDeptRegulates>()
				.eq("user_id", user.getUserId())
			)
			.stream()
			.map(UserDeptRegulates::getDeptId)
			.collect(Collectors.toList());

		return R.data(list);
	}
}
