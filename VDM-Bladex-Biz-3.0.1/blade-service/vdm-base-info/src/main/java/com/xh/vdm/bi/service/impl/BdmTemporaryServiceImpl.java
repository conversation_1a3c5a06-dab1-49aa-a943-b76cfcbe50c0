package com.xh.vdm.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.converter.BdmAbstractTargetConverter;
import com.xh.vdm.bi.enums.DeviceTypeEnum;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.mapper.BdmTemporaryMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.*;
import com.xh.vdm.bi.vo.response.BdmTemporaryResponse;
import com.xh.vdm.bi.vo.response.ConnectResponse;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.interManager.feign.IMessageClient;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (BdmTemporary)表服务实现类
 */
@Service
public class BdmTemporaryServiceImpl extends ServiceImpl<BdmTemporaryMapper, BdmTemporary> implements BdmTemporaryService {
	@Resource
	private BdmTemporaryMapper bdmTemporaryMapper;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private WearableDeviceService wearableDeviceService;
	@Resource
	private RdssDeviceService rdssDeviceService;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private MonitDeviceService monitDeviceService;
	@Resource
	private PntDeviceService pntDeviceService;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private IMessageClient messageClient;
	@Resource
	private IBdmDeviceStatusService deviceStatusService;
	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private IBdmVirtualTargetService virtualTargetService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
     * 分页查询
     *
     * @param bdmTemporary 筛选条件
     * @param query        分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	@Override
	public IPage<BdmTemporaryResponse> queryByPage(BdmTemporaryRequest bdmTemporary, Query query, DataAuthCE ceDataAuth) {
		IPage<BdmTemporaryRequest> page = new Page<>(query.getCurrent(), query.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.bdmTemporaryMapper.queryByPage(bdmTemporary, page, response.getAccount(), response.getOrgList());
	}

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmTemporary insert(BdmTemporaryRequest request) {

		//不是组织机构里面的单位：外派人员填的身份证号
		if (request.getDeptId() == 0) {
			boolean result = judgeId(request.getWkno());
			if (!result) {
				throw new RuntimeException("身份证号填写不合法！");
			}
		}

		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.TEMPORARY_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		request.setId(targetId.nextId());
		request.setCreateTime(new Date());
		request.setCreateAccount(AuthUtil.getUserAccount());
		this.bdmTemporaryMapper.insertTemporary(request);

		BdmTemporary temporary = baseMapper.selectById(request.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(temporary, abstractTarget);
		abstractTarget.setNumber(temporary.getWkno());
		abstractTarget.setCreateAccount(AuthUtil.getUserAccount());
		abstractTargetService.save(abstractTarget);

		Map<String, String> map = new HashMap<>();
		String key = temporary.getTargetType() + "-" + temporary.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", temporary.getName() + "(" + temporary.getWkno() + ")");
		innerMap.put("targetType", temporary.getTargetType());
		innerMap.put("deptId", temporary.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setTargetId(temporary.getId());
		deviceInfo.setTargetName(temporary.getName());
		deviceInfo.setTargetType(temporary.getTargetType());
		deviceInfo.setDeptId(temporary.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_temporary", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("外派管理信息更新消息发送到kafka失败", e);
		}
		BdmWorker worker = new BdmWorker();
		BeanUtils.copyProperties(temporary, worker);
		try {
			messageClient.staff(CommonConstant.OPER_TYPE_ADD, worker);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());

		}
		return temporary;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmTemporary update(BdmTemporaryRequest request) {
		BdmTemporary temporaryInDB = baseMapper.selectById(request.getId());

		BdmTemporary bdmTemporary = new BdmTemporary();
		bdmTemporary.setId(request.getId());
		bdmTemporary.setName(request.getName() != null ? request.getName() : "");
		bdmTemporary.setWkno(request.getWkno() != null ? request.getWkno() : "");
		bdmTemporary.setTargetType(request.getTargetType() != null ? request.getTargetType() : TargetTypeEnum.TEMPORARY.getSymbol());
		bdmTemporary.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		bdmTemporary.setPost(request.getPost() != null ? request.getPost() : 11);
		bdmTemporary.setIndustry(request.getIndustry() != null ? request.getIndustry() : 0);
		bdmTemporary.setPhone(request.getPhone() != null ? request.getPhone() : "");
		bdmTemporary.setCompanyId(request.getCompanyId() != null ? request.getCompanyId() : 0);
		bdmTemporary.setCompany(request.getCompany() != null ? request.getCompany() : "");
		bdmTemporary.setValidFrom(request.getValidFrom() != null ? request.getValidFrom() : null);
		bdmTemporary.setValidTo(request.getValidTo() != null ? request.getValidTo() : null);
		bdmTemporary.setUpdateTime(new Date());
		this.bdmTemporaryMapper.update(bdmTemporary);

		BdmTemporary temporary = baseMapper.selectById(bdmTemporary.getId());
		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(temporary, abstractTarget);
		abstractTarget.setNumber(temporary.getWkno());
		abstractTargetService.updateById(abstractTarget);

		String key = temporary.getTargetType() + "-" + temporary.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", temporary.getName() + "(" + temporary.getWkno() + ")");
		innerMap.put("targetType", temporary.getTargetType());
		innerMap.put("deptId", temporary.getDeptId());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setTargetId(temporary.getId());
		deviceInfo.setTargetName(temporary.getName());
		deviceInfo.setTargetType(temporary.getTargetType());
		deviceInfo.setDeptId(temporary.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_temporary", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("外派管理信息更新消息发送到kafka失败", e);
		}

		BdmWorker worker = new BdmWorker();
		BeanUtils.copyProperties(temporary, worker);
		try {
			messageClient.staff(CommonConstant.OPER_TYPE_ADD, worker);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		//@zhouxw modify 2025-06-27 修改监管对象，终端不再同步更改组织
		/*if(!temporaryInDB.getDeptId().equals(temporary.getDeptId())){
			// 对于WearableDevice的更新
			wearableDeviceService.updateDept(temporary.getId(), temporary.getTargetType(), temporary.getDeptId());

			// 对于RdssDevice的更新
			rdssDeviceService.updateDept(temporary.getId(), temporary.getTargetType(), temporary.getDeptId());

			// 对于RnssDevice的更新
			rnssDeviceService.updateDept(temporary.getId(), temporary.getTargetType(), temporary.getDeptId());

			// 对于BdmAbstractDevice的更新
			abstractDeviceService.updateDept(temporary.getId(), temporary.getTargetType(), temporary.getDeptId());
		}*/

		return temporary;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {
		List<BdmTemporary> temporaryList = baseMapper.selectList(new QueryWrapper<BdmTemporary>().in("id", ids));

		boolean result = this.bdmTemporaryMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.TEMPORARY_TARGET_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + key);
			}
			this.wearableDeviceService.deleteByTargetIds(ids, TargetTypeEnum.TEMPORARY.getSymbol());
			this.rdssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.TEMPORARY.getSymbol());
			this.rnssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.TEMPORARY.getSymbol());

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.DELETE);
			Long lastId = ids[ids.length - 1];
			deviceInfo.setTargetId(lastId);
			deviceInfo.setTargetIds(CollUtil.newHashSet(ids));
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_temporary", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("外派管理信息更新消息发送到kafka失败", e);
			}
			List<BdmWorker> workers = temporaryList.stream().map(temporary -> BeanUtil.copy(temporary, BdmWorker.class)).collect(Collectors.toList());
			//messageClient
			try {
				messageClient.staffBatch(CommonConstant.OPER_TYPE_DELETE, workers);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
			//更新bdm_abstract_target
			abstractTargetService.deleteByIds(ids);
			//更新bdm_abstract_device
			abstractDeviceService.deleteByTargetIds(ids, TargetTypeEnum.TEMPORARY.getSymbol());
		}
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ConnectResponse connectTerminal(List<PersonTerminalRequest> list, Long id, Long deptId) {
		ConnectResponse res=new ConnectResponse();
		long rnssNum=list.stream().filter(val->val.getDeviceType().equals(DeviceTypeEnum.RNSS.getSymbol())).count();
		if (rnssNum>1){
			res.setCode(1);
			res.setMsg("外派最多只能绑定一个808终端");
			return res;
		}
		// todo bdm_virtual_target数据进行删除恢复
		BdmTemporary temporary = this.bdmTemporaryMapper.selectById(id);
		String targetName = temporary.getName() + "(" + temporary.getWkno() + ")";

		List<BdmAbstractDevice> abstractDevices = abstractDeviceService.getBaseMapper()
			.selectList(new QueryWrapper<BdmAbstractDevice>()
				.eq("target_id", id)
				.eq("target_type", TargetTypeEnum.TEMPORARY.getSymbol())
			);

		List<String> uniqueIdList = new ArrayList<>();
		if(!abstractDevices.isEmpty()){
			//bdm_device_status更新
			for (BdmAbstractDevice request:abstractDevices) {
				QueryWrapper<BdmDeviceStatus> wrapper = new QueryWrapper<>();
				wrapper.eq("device_id", request.getId());
				wrapper.eq("device_type", request.getDeviceType());
				wrapper.eq("target_id", request.getTargetId());
				wrapper.eq("target_type", request.getTargetType());
				deviceStatusService.remove(wrapper);

				BdmAbstractTarget target =abstractTargetService.getOne(new QueryWrapper<BdmAbstractTarget>().eq("number", request.getUniqueId()).eq("deleted", 0));
				if (target==null){
					res.setCode(2);
					res.setMsg("未查询到与该目标绑定的原抽象终端的待分配终端目标");
					return res;
				}

				if (request.getDeviceType().equals(DeviceTypeEnum.WEARABLE.getSymbol())){
					wearableDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.RNSS.getSymbol())){
					rnssDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.RDSS.getSymbol())){
					rdssDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.MONIT.getSymbol())){
					monitDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.PNT.getSymbol())){
					pntDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				abstractDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());

				uniqueIdList.add(request.getUniqueId());
			}

			// bdm_virtual_target数据 对abstractDevices 进行恢复
			virtualTargetService.updateBatch(uniqueIdList);
		}
		Set<Long> ids = abstractDevices.stream().map(BdmAbstractDevice::getId).collect(Collectors.toSet());

		if(list.isEmpty()){
//			//根据用户ID去清空原来绑定的终端数据
//			wearableDeviceService.updateByWorkerId(id, TargetTypeEnum.TEMPORARY.getSymbol());
//			rdssDeviceService.updateBatchByWorkerId(id, TargetTypeEnum.TEMPORARY.getSymbol());
//			rnssDeviceService.updateByWorkerId(id, TargetTypeEnum.TEMPORARY.getSymbol());
//			monitDeviceService.updateByDeviceId(id,TargetTypeEnum.TEMPORARY.getSymbol());
//			pntDeviceService.updateByDeviceId(id,TargetTypeEnum.TEMPORARY.getSymbol());
//			//bdm_abstract_device更新绑定关系
//			abstractDeviceService.unbinding(id, TargetTypeEnum.TEMPORARY.getSymbol());
		}else {
			//PersonTerminalRequest req=list.get(0);
			//后台需要判断设备是否被其他对象绑定，若已被绑定则提示操作失败
			for (PersonTerminalRequest req:list){
				BdmAbstractDevice bad=abstractDeviceService.getBadByUniqueId(req.getUniqueId());
				if (bad==null) {
					res.setCode(2);
					res.setMsg("设备序列号不存在");
					return res;
				}
				if (bad.getTargetId()>0&&bad.getTargetType()>0&&!bad.getTargetId().equals(id)){
					res.setCode(3);
					BdmAbstractTarget bat=abstractTargetService.getById(bad.getTargetId());
					String msg="设备已被其他对象绑定："+bat.getName()+"("+bat.getNumber()+")";
					res.setMsg(msg);
					return res;
				}

				if (DeviceTypeEnum.RDSS.getSymbol().equals(req.getDeviceType())) {
					rdssDeviceService.updateBatchByWorkerId(id, TargetTypeEnum.TEMPORARY.getSymbol());
					rdssDeviceService.updateBatch(list, id, TargetTypeEnum.TEMPORARY.getSymbol(), targetName, deptId);
				}
				if (DeviceTypeEnum.WEARABLE.getSymbol().equals(req.getDeviceType())) {
					wearableDeviceService.updateByWorkerId(id, TargetTypeEnum.TEMPORARY.getSymbol());
					wearableDeviceService.updateBatchByTerminalId(list, id, TargetTypeEnum.TEMPORARY.getSymbol(), targetName, deptId);
				}
				if (DeviceTypeEnum.RNSS.getSymbol().equals(req.getDeviceType())) {
					rnssDeviceService.updateByWorkerId(id, TargetTypeEnum.TEMPORARY.getSymbol());
					rnssDeviceService.connectWorkerTerminal(list, id, TargetTypeEnum.TEMPORARY.getSymbol(), targetName, deptId);
				}
				if (DeviceTypeEnum.MONIT.getSymbol().equals(req.getDeviceType())) {
					monitDeviceService.updateByDeviceId(id,TargetTypeEnum.TEMPORARY.getSymbol());
					List<FacilityTerminalRequest> pnts =new ArrayList<>();
					FacilityTerminalRequest pnt=new FacilityTerminalRequest();
					pnt.setId(req.getId());
					pnts.add(pnt);
					monitDeviceService.updateBatchByTerminalId(pnts, id, TargetTypeEnum.TEMPORARY.getSymbol(), deptId);
				}
				if (DeviceTypeEnum.PNT.getSymbol().equals(req.getDeviceType())) {
					pntDeviceService.updateByDeviceId(id,TargetTypeEnum.TEMPORARY.getSymbol());
					List<FacilityTerminalRequest> pnts =new ArrayList<>();
					FacilityTerminalRequest pnt=new FacilityTerminalRequest();
					pnt.setId(req.getId());
					pnts.add(pnt);
					pntDeviceService.updateBatchByTerminalId(pnts, id, TargetTypeEnum.TEMPORARY.getSymbol(), deptId);
				}
				ids.add(bad.getId());
			}

			//bdm_abstract_device更新绑定关系
			abstractDeviceService.unbinding(id, TargetTypeEnum.TEMPORARY.getSymbol());
			abstractDeviceService.bind(list, id, TargetTypeEnum.TEMPORARY.getSymbol(), targetName, deptId);

			List<String> uniqueIds = list.stream()
					.map(PersonTerminalRequest::getUniqueId)
					.collect(Collectors.toList());
			// bdm_virtual_target数据 对于list中的808进行删除。
			if(!uniqueIds.isEmpty()){
				virtualTargetService.updateByUniqueId(uniqueIds);
			}
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.BIND);
		deviceInfo.setTargetId(id);
		deviceInfo.setTargetName(temporary.getName());
		deviceInfo.setTargetType(temporary.getTargetType());
		deviceInfo.setDeviceIds(ids);

		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_temporary", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("外派管理信息更新消息发送到kafka失败", e);
		}
		res.setCode(0);
		return res;
	}

	@Override
	public IPage<PersonNoBingResponse> select(DeviceNoBindRequest deviceNoBindRequest, DataAuthCE ceDataAuth) {
		IPage<PersonNoBingResponse> responseIPage = new Page<>();
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		// bdm_abstract_device 数据进行分页查询
		responseIPage = abstractDeviceService.select(deviceNoBindRequest, response.getAccount(), response.getOrgList());
		return responseIPage;
	}

	@Override
	public List<PersonNoBingResponse> selectBind(DeviceNoBindRequest deviceNoBindRequest, Long id) {
		List<PersonNoBingResponse> resultList = abstractDeviceService.selectBind(id, TargetTypeEnum.TEMPORARY.getSymbol());
		return resultList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<BdmTemporaryRequest> importExcel(List<BdmTemporaryRequest> list) {
		List<BdmTemporaryRequest> duplicates = getDuplicates(list);
		List<BdmTemporaryRequest> requests = new ArrayList<>(list);
		// 去重后的数据（去除所有重复数据）
		requests.removeAll(duplicates);

		if (!requests.isEmpty()) {
			List<String> wknoList = requests.stream()
					.map(BdmTemporaryRequest::getWkno)
					.collect(Collectors.toList());
			//数据库的数据
			QueryWrapper<BdmTemporary> queryWrapper = new QueryWrapper<>();
			if (!wknoList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(queryWrapper, "wkno", wknoList);
			}
			queryWrapper.gt("DATE(valid_to)", LocalDate.now());
			queryWrapper.eq("deleted",0);
			queryWrapper.select("wkno");
			List<String> wknoExitList = this.bdmTemporaryMapper.selectList(queryWrapper)
					.stream()
					.map(BdmTemporary::getWkno)
					.collect(Collectors.toList());

			List<BdmTemporaryRequest> temporaryList = new ArrayList<>();

			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.TEMPORARY_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			Map<String, String> map = new HashMap<>();
			List<BdmAbstractTarget> bdmAbstractTargets = new ArrayList<>();

			for (BdmTemporaryRequest temporary : requests) {

//				if (temporary.getCompanyId() == 0) {
//					boolean result = judgeId(temporary.getWkno());
//					if (!result) {
//						temporary.setMsg("身份证号填写不合法");
//						duplicates.add(temporary);
//						continue;
//					}
//				}

				if (wknoExitList.contains(temporary.getWkno())) {
					temporary.setMsg("工卡号/身份证号已存在");
					duplicates.add(temporary);
				} else {
					temporary.setId(targetId.nextId());
					temporary.setName(temporary.getName() != null ? temporary.getName() : "");
					temporary.setWkno(temporary.getWkno() != null ? temporary.getWkno() : "");
					temporary.setTargetType(TargetTypeEnum.TEMPORARY.getSymbol());
					temporary.setDeptId(temporary.getDeptId() != null ? temporary.getDeptId() : 0);
					temporary.setPost(temporary.getPost() != null ? temporary.getPost() : 11);
					temporary.setIndustry(temporary.getIndustry() != null ? temporary.getIndustry() : 0);
					temporary.setPhone(temporary.getPhone() != null ? temporary.getPhone() : "");
					temporary.setCompanyId(temporary.getCompanyId() != null ? temporary.getCompanyId() : 0);
					temporary.setCompany(temporary.getCompany() != null ? temporary.getCompany() : "");
					temporary.setValidFrom(temporary.getValidFrom() != null ? temporary.getValidFrom() : null);
					temporary.setValidTo(temporary.getValidTo() != null ? temporary.getValidTo() : null);
					temporary.setCreateTime(new Date());
					temporary.setDeleted(0);
					temporaryList.add(temporary);

					String key = temporary.getTargetType() + "-" + temporary.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", temporary.getName() + "(" + temporary.getWkno() + ")");
					innerMap.put("targetType", temporary.getTargetType());
					innerMap.put("deptId", temporary.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}

					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(temporary, abstractTarget);
					abstractTarget.setNumber(temporary.getWkno());
					abstractTarget.setCategory(0);
					bdmAbstractTargets.add(abstractTarget);
				}
			}

			if (!temporaryList.isEmpty()) {
				MapperUtils.splitListByCapacity(temporaryList, MapperUtils.DEFAULT_CAPACITY)
					.forEach(bdmTemporaryList -> this.bdmTemporaryMapper.insertBatch(bdmTemporaryList));


				MapperUtils.splitListByCapacity(bdmAbstractTargets, MapperUtils.DEFAULT_CAPACITY)
					.forEach(abstractTargetList -> this.abstractTargetService.insertBatch(abstractTargetList));

				//todo：Map<String, String> map这样写go那边解析有问题
				redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

				BdmTemporaryRequest lastTemporary = temporaryList.get(temporaryList.size() - 1);
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.IMPORT);
				deviceInfo.setTargetId(lastTemporary.getId());
				deviceInfo.setTargetName(lastTemporary.getName());
				deviceInfo.setDeptId(lastTemporary.getDeptId());
				Set<Long> ids = temporaryList.stream().map(BdmTemporaryRequest::getId).collect(Collectors.toSet());
				deviceInfo.setTargetIds(ids);
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("外派管理信息更新消息发送到kafka失败", e);
				}
				List<BdmWorker> workers = temporaryList.stream().map(temporary -> BeanUtil.copy(temporary, BdmWorker.class)).collect(Collectors.toList());
				//messageClient
				try {
					messageClient.staffBatch(CommonConstant.OPER_TYPE_DELETE, workers);
				} catch (Exception e) {
					log.error("消息发送到messageClient失败：" + e.getMessage());

				}
			}
		}

		return duplicates;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest) {
		int update = bdmTemporaryMapper.batchUpdate(batchUpdateRequest.getIds(),batchUpdateRequest.getDeptId());
		if (update == 0) {
			return false;
		}
		//查询出所有的本次要更新的字段
		List<BdmTemporary> bdmTemporaryList = bdmTemporaryMapper.selectList(new LambdaQueryWrapper<BdmTemporary>().in(BdmTemporary::getId, batchUpdateRequest.getIds()));
		//转换
		List<BdmAbstractTarget> bdmAbstractTargetList = bdmTemporaryList.stream().map(BdmAbstractTargetConverter::toBdmTemporary).collect(Collectors.toList());
		//更新主表
		abstractTargetService.saveOrUpdateBatch(bdmAbstractTargetList);
		//批量更新redis和kafka
		batchProcess(bdmTemporaryList);
		if (update > 0){
			return true;
		}
		return false;
	}

	/**
	 * 批量更新redis
	 * 批量发送kafka
	 */
	private void batchProcess(List<BdmTemporary> bdmTemporaryList){
		for (BdmTemporary temporary : bdmTemporaryList) {
			String key = temporary.getTargetType() + "-" + temporary.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("targetName", temporary.getName() + "(" + temporary.getWkno() + ")");
			innerMap.put("targetType", temporary.getTargetType());
			innerMap.put("deptId", temporary.getDeptId());
			Map<String, String> map = new HashMap<>();
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.OTHER);
			deviceInfo.setTargetId(temporary.getId());
			deviceInfo.setTargetName(temporary.getName());
			deviceInfo.setTargetType(temporary.getTargetType());
			deviceInfo.setDeptId(temporary.getDeptId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_temporary", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("外派管理信息更新消息发送到kafka失败", e);
			}

			BdmWorker worker = new BdmWorker();
			BeanUtils.copyProperties(temporary, worker);
			try {
				messageClient.staff(CommonConstant.OPER_TYPE_ADD, worker);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

	/**
	 * 重复数据
	 *
	 * @param list
	 * @return
	 */
	public List<BdmTemporaryRequest> getDuplicates(List<BdmTemporaryRequest> list) {
		Map<String, Long> wknoCountMap = list.stream()
			.map(BdmTemporaryRequest::getWkno)
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<String> duplicateWknos = wknoCountMap.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		return list.stream()
			.filter(bdmTemporary -> duplicateWknos.contains(bdmTemporary.getWkno()))
			.peek(bdmTemporary -> bdmTemporary.setMsg("工卡号/身份证号重复"))
			.collect(Collectors.toList());
	}

	// 判断身份证号是否合法
	public static Boolean judgeId(String idNumber) {
		Boolean result = true;

		// 长度不等于 18 位 或 15位
		if (idNumber.length() != 18 && idNumber.length() != 15) {
			return false;
		}

		if (idNumber.length() == 18) {
			// 系数算法
			String tempId = getStr(idNumber, 0, 16);
			int[] coeff = {7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2};
			char[] end = {'1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'};
			int sum = 0;
			for (int i = 0; i < tempId.length(); i++) {
				int bye = tempId.charAt(i) - '0';
				sum += bye * coeff[i];
			}
			sum %= 11;

			if (end[sum] != getStr(idNumber, 17, 17).charAt(0)) {
				result = false;
			}
		}

		return result;
	}

	// 截取字符串的方法
	public static String getStr(String str, int a, int b) {
		b++;
		return str.substring(a, b);
	}

}
