package com.xh.vdm.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 定位模式
 */
@AllArgsConstructor
@Getter
public enum GnssModeEnum {
	/**
	 * 单北斗
	 */
	SINGLEBEIDOU(1, "单北斗"),
	/**
	 * 单GPS
	 */
	SINGLEGPS(2, "单GPS"),
	/**
	 * 北斗优先
	 */
	BEIDOUPRIORITY(3, "北斗优先"),
	/**
	 * 主用北斗
	 */
	MAINBEIDOU(4, "主用北斗"),
	/**
	 * 多模非主用北斗
	 */
	MUTILNONPRIMARYBEIDOU(5, "多模非主用北斗"),

	;

	/**
	 * key值
	 */
	private final Integer value;
	/**
	 * label
	 */
	private final String symbol;

}
