package com.xh.vdm.bi.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.enums.ClassCodeEnum;
import com.xh.vdm.bi.enums.SubClassCodeEnum;
import com.xh.vdm.bi.mapper.BdmDeviceClassMapper;
import com.xh.vdm.bi.service.BdmDeviceClassService;
import com.xh.vdm.biapi.entity.BdmDeviceClass;
import org.springblade.common.utils.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class BdmDeviceClassServiceImpl extends ServiceImpl<BdmDeviceClassMapper, BdmDeviceClass> implements BdmDeviceClassService {

	@Resource
	private BdmDeviceClassMapper bdmDeviceClassMapper;


	/**
	 * 保存终端设备类型
	 * @param classCode
	 * @param otherTypes
	 * @return
	 */
	@Override
	public String saveDeviceClass(String classCode, String otherTypes) {
		String subClassCode = null;
		if (StringUtils.isNotEmpty(otherTypes)) {
			BdmDeviceClass one = bdmDeviceClassMapper.selectOne(new LambdaQueryWrapper<BdmDeviceClass>().eq(BdmDeviceClass::getClassName, otherTypes));
			if (ObjectUtils.isEmpty(one)) {
				BdmDeviceClass deviceClass = new BdmDeviceClass();
				deviceClass.setId(IdWorker.getId());
				deviceClass.setCode(String.valueOf(IdWorker.getId()));
				deviceClass.setParentCode(classCode);
				deviceClass.setDeleted(0);
				deviceClass.setClassName(otherTypes);
				deviceClass.setCreateTime(new Date());
				deviceClass.setSource(Integer.valueOf(1));
				bdmDeviceClassMapper.insert(deviceClass);
				subClassCode = deviceClass.getCode();
			}
		}
		return subClassCode;
	}

	/**
	 * 拿到终端类型
	 *
	 * @param classCode    大类
	 * @param subClassCode 小类
	 * @return
	 */
	@Override
	public String getTableValue(String classCode, String subClassCode, String otherTypes) {

		if (StringUtils.isEmpty(otherTypes)) {
			BdmDeviceClass bdmDeviceClass = bdmDeviceClassMapper.selectOne(new LambdaQueryWrapper<BdmDeviceClass>().eq(BdmDeviceClass::getCode, subClassCode));
			otherTypes = bdmDeviceClass.getClassName();
		}
		// 参数校验
		if (StringUtils.isEmpty(classCode) || StringUtils.isEmpty(subClassCode)) {
			return "应用方向和终端类型不能为空！";
		}

		// 查询有效数据
		List<BdmDeviceClass> list = bdmDeviceClassMapper.selectList(
			new LambdaQueryWrapper<BdmDeviceClass>()
				.eq(BdmDeviceClass::getDeleted, 0)
				.eq(BdmDeviceClass::getSource, 1)
		);
		Boolean hasMatchingSubClass = false;

		// 判断是否存在匹配的子类描述
		for (BdmDeviceClass bdmDeviceClass : list) {
			if (bdmDeviceClass.getClassName().equals(otherTypes)) {
				hasMatchingSubClass = true;
				break;
			}
		}
		// 定义主要类别映射
		Map<String, String> mainClassMapping = new HashMap<>();
		mainClassMapping.put(ClassCodeEnum.WORKER_RNSS.getValue(), "bdm_rnss_device");
		mainClassMapping.put(ClassCodeEnum.CARRY_RNSS.getValue(), "bdm_rnss_device");
		mainClassMapping.put(ClassCodeEnum.ASSET_MANAGEMENT.getValue(), "bdm_rnss_device");
		mainClassMapping.put(ClassCodeEnum.SAFE_MONIT.getValue(), "bdm_monit_device");
		mainClassMapping.put(ClassCodeEnum.MAPPING_SURVEY.getValue(), "bdm_monit_device");
		mainClassMapping.put(ClassCodeEnum.EMERGENCY_TELECOMMUNICATIONS.getValue(), "bdm_rdss_device");
		mainClassMapping.put(ClassCodeEnum.TIME_FREQUENCY_SYNCHRONIZATION.getValue(), "bdm_pnt_device");
		mainClassMapping.put(ClassCodeEnum.PRECISE_CONTROL.getValue(), "bdm_rnss_device");
		mainClassMapping.put(ClassCodeEnum.INTELLIGENT_INSPECTION.getValue(), "bdm_rnss_device");

		// 定义特殊子类映射
		Map<String, Map<String, String>> specialSubClassMapping = new HashMap<>();

		Map<String, String> workerRnssMapping = new HashMap<>();
		workerRnssMapping.put(SubClassCodeEnum.OTHER.getValue(), "bdm_rnss_device");
		workerRnssMapping.put(SubClassCodeEnum.HIGH_PRECISION_BD_CARD.getValue(), "bdm_rnss_device");
		workerRnssMapping.put(SubClassCodeEnum.HIGH_PRECISION_SAFETY_HELMET.getValue(), "bdm_wearable_device");
		workerRnssMapping.put(SubClassCodeEnum.EXPLOSION_PROOF_SMART_WATCH.getValue(), "bdm_wearable_device");
		workerRnssMapping.put(SubClassCodeEnum.EXPLOSION_PROOF_WALKIE_TALKIE.getValue(), "bdm_rnss_device");
		workerRnssMapping.put(SubClassCodeEnum.EXPLOSION_PROOF_HANDHELD_TERMINAL.getValue(), "bdm_rnss_device");

		specialSubClassMapping.put(ClassCodeEnum.WORKER_RNSS.getValue(), workerRnssMapping);

		Map<String, String> carryRnssMapping = new HashMap<>();
		carryRnssMapping.put(SubClassCodeEnum.VEHICLE_COMMON_TERMINAL.getValue(), "bdm_rnss_device");
		carryRnssMapping.put(SubClassCodeEnum.VEHICLE_POSITIONING_TERMINAL.getValue(), "bdm_rnss_device");
		carryRnssMapping.put(SubClassCodeEnum.VEHICLE_VIDEO_POSITIONING_TERMINAL.getValue(), "bdm_rnss_device");
		carryRnssMapping.put(SubClassCodeEnum.VEHICLE_HIGH_PRECISION_TERMINAL.getValue(), "bdm_rnss_device");
		carryRnssMapping.put(SubClassCodeEnum.MINING_VEHICLE_ANTI_COLLISION_TERMINAL.getValue(), "bdm_rnss_device");

		specialSubClassMapping.put(ClassCodeEnum.CARRY_RNSS.getValue(), carryRnssMapping);


		Map<String, String> assetManagementMapping = new HashMap<>();
		assetManagementMapping.put(SubClassCodeEnum.ASSET_MANAGEMENT_COMMON.getValue(), "bdm_rnss_device");
		assetManagementMapping.put(SubClassCodeEnum.ASSET_MANAGEMENT_COMMON.getValue(), "bdm_rnss_device");
		assetManagementMapping.put(SubClassCodeEnum.ASSET_MANAGEMENT_COMMON.getValue(), "bdm_rnss_device");

		specialSubClassMapping.put(ClassCodeEnum.ASSET_MANAGEMENT.getValue(), assetManagementMapping);


		Map<String, String> safeMonitMapping = new HashMap<>();
		safeMonitMapping.put(SubClassCodeEnum.SAFETY_MONITORING_COMMON.getValue(), "bdm_monit_device");
		safeMonitMapping.put(SubClassCodeEnum.SPLIT_TYPE_MONITORING_RECEIVER.getValue(), "bdm_monit_device");
		safeMonitMapping.put(SubClassCodeEnum.INTEGRATED_MONITORING_RECEIVER.getValue(), "bdm_monit_device");
		safeMonitMapping.put(SubClassCodeEnum.REFERENCE_STATION_RECEIVER.getValue(), "bdm_monit_device");

		specialSubClassMapping.put(ClassCodeEnum.SAFE_MONIT.getValue(), safeMonitMapping);

		Map<String, String> mappingSurveyMapping = new HashMap<>();
		mappingSurveyMapping.put(SubClassCodeEnum.MAPPING_SURVEY_COMMON.getValue(), "bdm_monit_device");
		mappingSurveyMapping.put(SubClassCodeEnum.HIGH_PRECISION_RTK_DEVICE.getValue(), "bdm_monit_device");
		mappingSurveyMapping.put(SubClassCodeEnum.HIGH_PRECISION_SURVEY_UAV.getValue(), "bdm_monit_device");
		mappingSurveyMapping.put(SubClassCodeEnum.HIGH_PRECISION_INERTIAL_INTEGRATED_TERMINAL.getValue(), "bdm_monit_device");

		specialSubClassMapping.put(ClassCodeEnum.MAPPING_SURVEY.getValue(), mappingSurveyMapping);


		Map<String, String> emergencyTelecommunicationsMapping = new HashMap<>();
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.EMERGENCY_COMMUNICATION_COMMON.getValue(), "bdm_rdss_device");
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.BDS3_SHORT_MESSAGE_HANDHELD.getValue(), "bdm_rdss_device");
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.BDS3_DATA_TRANSMISSION_TERMINAL.getValue(), "bdm_rdss_device");
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.BDS_5G_SATELLITE_HANDHELD.getValue(), "bdm_rdss_device");
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.BDS3_DISTRESS_SAVING_TERMINAL.getValue(), "bdm_rdss_device");
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.BDS3_SHIPBORNE_TERMINAL.getValue(), "bdm_rdss_device");

		specialSubClassMapping.put(ClassCodeEnum.EMERGENCY_TELECOMMUNICATIONS.getValue(), emergencyTelecommunicationsMapping);

		Map<String, String> timeFrequencySynchronizationMapping = new HashMap<>();
		timeFrequencySynchronizationMapping.put(SubClassCodeEnum.TIME_FREQUENCY_SYNCHRONIZATION_COMMON.getValue(), "bdm_pnt_device");
		timeFrequencySynchronizationMapping.put(SubClassCodeEnum.POWER_TIME_SYNCHRONIZATION_DEVICE.getValue(), "bdm_pnt_device");
		timeFrequencySynchronizationMapping.put(SubClassCodeEnum.BDS_TIME_SERVICE_SERVER.getValue(), "bdm_pnt_device");
		timeFrequencySynchronizationMapping.put(SubClassCodeEnum.BDS_ANTI_SPOOFING_DEVICE.getValue(), "bdm_pnt_device");

		specialSubClassMapping.put(ClassCodeEnum.TIME_FREQUENCY_SYNCHRONIZATION.getValue(), timeFrequencySynchronizationMapping);

		Map<String, String> preciseControlMapping = new HashMap<>();
		preciseControlMapping.put(SubClassCodeEnum.TIME_FREQUENCY_SYNCHRONIZATION_COMMON.getValue(), "bdm_rnss_device");

		specialSubClassMapping.put(ClassCodeEnum.PRECISE_CONTROL.getValue(), preciseControlMapping);

		Map<String, String> intelligentInspectionMapping = new HashMap<>();
		intelligentInspectionMapping.put(SubClassCodeEnum.TIME_FREQUENCY_SYNCHRONIZATION_COMMON.getValue(), "bdm_rnss_device");

		specialSubClassMapping.put(ClassCodeEnum.INTELLIGENT_INSPECTION.getValue(), intelligentInspectionMapping);

		// 根据条件选择结果
		if (hasMatchingSubClass) {
			return mainClassMapping.getOrDefault(classCode, "");
		} else {
			Map<String, String> subMap = specialSubClassMapping.getOrDefault(classCode, Collections.emptyMap());
			return subMap.getOrDefault(subClassCode, "");
		}
	}
}
