package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.WearableDeviceRequest;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.WearableDeviceResponse;
import com.xh.vdm.bi.vo.response.WorkerBingResponse;
import com.xh.vdm.biapi.entity.BdmWearableDevice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 北斗穿戴实体管理
 */
public interface WearableDeviceMapper extends BaseMapper<BdmWearableDevice> {

	/**
	 * 分页查询
	 *
	 * @param page    分页条件
	 * @param request 筛选条件
	 * @param schema
	 * @return
	 */
	IPage<WearableDeviceResponse> queryAll(IPage page, @Param("request") WearableDeviceRequest request, @Param("account") String account, @Param("deptIds") String deptIds, @Param("schema") String schema);

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 影响行数
	 */
	void insertWear(BdmWearableDevice request);

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 影响行数
	 */
	int update(BdmWearableDevice request);

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 影响行数
	 */
	int deleteById(@Param("id") Long id);

	/**
	 * 通过主键删除数据
	 * @param ids
	 * @return
	 */
    int deleteByIds(@Param("ids") Long[] ids);

	void insertBatch(@Param("list") List<WearableDeviceRequest> list);

	void connectWorkerTerminal(@Param("request") PersonTerminalRequest request, @Param("id") Long id, @Param("targetType") Integer targetType, @Param("targetName") String targetName, @Param("deptId") Long deptId);

	void updateBatch(@Param("ids") List<Long> ids, @Param("id") Long id, @Param("targetType") Integer targetType, @Param("targetName") String targetName, @Param("deptId") Long deptId);
	IPage<PersonNoBingResponse> select(Page page, @Param("request") DeviceNoBindRequest request, @Param("account") String account, @Param("deptIds") String deptIds);

	List<WorkerBingResponse>  selectByWorkId(@Param("id") Long id, @Param("targetType") Integer targetType);

	void updateByWorkerId(@Param("id") Long id, @Param("targetType") Integer targetType);

	List<PersonNoBingResponse> selectBindByWorkId(@Param("id") Long id, @Param("targetType") Integer targetType);

	void deleteByTargetIds(@Param("ids") Long[] ids, @Param("targetType") Integer targetType);

	long countByUserRole(@Param("account") String account, @Param("deptIds") String deptIds);

    void updateDept(@Param("id") Long id, @Param("targetType") Integer targetType, @Param("deptId") Long deptId);

	int bindTarget(@Param("deviceId") Long deviceId,@Param("targetId") Long targetId, @Param("targetType") Integer targetType, @Param("targetName") String targetName);
}

