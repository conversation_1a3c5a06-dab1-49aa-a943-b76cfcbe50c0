package com.xh.vdm.bi.vo.request;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 基础设施绑定终端
 */
@Data
public class FacilityTerminalRequest {

	//终端类型
	private Integer category;
	//终端类别
	private Integer deviceType;
	//终端Id
	private Long id;
	//所属机构
	private Long deptId;
	//点串坐标，描述基础设施范围
	private String geometry;
	/**
	 * 定位纬度
	 */
	private BigDecimal latitude;

	/**
	 * 定位经度
	 */
	private BigDecimal longitude;

	private String deviceNum;

	private Integer iotProtocol;

	private String uniqueId;
}

