package com.xh.vdm.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 小类
 */
@AllArgsConstructor
@Getter
public enum SubClassCodeEnum {

	OTHER("1000", "其他"),
	HIGH_PRECISION_BD_CARD("1001", "北斗高精度定位卡"),
	HIGH_PRECISION_SAFETY_HELMET("1002", "北斗高精度智能安全帽"),
	EXPLOSION_PROOF_SMART_WATCH("1003", "北斗智能手表（防爆版）"),
	EXPLOSION_PROOF_WALKIE_TALKIE("1004", "北斗高精度对讲机（防爆版）"),
	EXPLOSION_PROOF_HANDHELD_TERMINAL("1005", "北斗高精度手持机（防爆版）"),

	VEHICLE_COMMON_TERMINAL("2000", "其他"),
	VEHICLE_POSITIONING_TERMINAL("2001", "普通车辆定位终端"),
	VEHICLE_VIDEO_POSITIONING_TERMINAL("2002", "普通车辆视频型定位终端"),
	VEHICLE_HIGH_PRECISION_TERMINAL("2003", "北斗高精度车载智能终端"),
	MINING_VEHICLE_ANTI_COLLISION_TERMINAL("2004", "矿卡防碰撞智能车载终端"),

	ASSET_MANAGEMENT_COMMON("3000", "其他"),
	CONTAINER_POSITIONING_TERMINAL("3001", "集装箱定位终端"),
	SOLAR_POWERED_ASSET_TRACKER("3002", "北斗物资定位终端（太阳能）"),

	SAFETY_MONITORING_COMMON("4000", "其他"),
	SPLIT_TYPE_MONITORING_RECEIVER("4001", "北斗监测接收机（分体式）"),
	INTEGRATED_MONITORING_RECEIVER("4002", "北斗监测接收机（一体式）"),
	REFERENCE_STATION_RECEIVER("4003", "北斗基准站接收机"),

	MAPPING_SURVEY_COMMON("5000", "其他"),
	HIGH_PRECISION_RTK_DEVICE("5001", "高精度RTK测量设备"),
	HIGH_PRECISION_SURVEY_UAV("5002", "北斗高精度测绘无人船"),
	HIGH_PRECISION_INERTIAL_INTEGRATED_TERMINAL("5003", "高精度卫惯组合终端"),

	EMERGENCY_COMMUNICATION_COMMON("6000", "其他"),
	BDS3_SHORT_MESSAGE_HANDHELD("6001", "北斗三号短报文手持终端"),
	BDS3_DATA_TRANSMISSION_TERMINAL("6002", "北斗三号数传终端"),
	BDS_5G_SATELLITE_HANDHELD("6003", "北斗短报文5G天通手持机"),
	BDS3_DISTRESS_SAVING_TERMINAL("6004", "北斗三号遇险救生终端"),
	BDS3_SHIPBORNE_TERMINAL("6005", "北斗三号短报文数传定位终端（船载）"),

	TIME_FREQUENCY_SYNCHRONIZATION_COMMON("7000", "其他"),
	POWER_TIME_SYNCHRONIZATION_DEVICE("7001", "电力时间同步设备"),
	BDS_TIME_SERVICE_SERVER("7002", "北斗授时服务器"),
	BDS_ANTI_SPOOFING_DEVICE("7003", "北斗防欺骗抗干扰卫星隔离装置"),

	PRECISE_CONTROL_COMMON("8000", "其他"),
	OTHER_DEVICE("9000", "其他");


	/**
	 * 标识
	 */
	private final String value;
	/**
	 * 状态
	 */
	private final String label;
}
