package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springblade.common.dept.DeptIdAware;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * Description: 集装箱入参
 */
@Data
@ExcelIgnoreUnannotated
public class ContainerRequest  implements DeptIdAware {

	/**
	 * 主键，唯一标识码
	 */
	private Long id;
	/**
	 * 集装箱编号，国际标准11位
	 */
	@NotNull(message = "集装箱编号不能为空")
	@ExcelProperty(value = "集装箱编号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String number;
	/**
	 * 箱型
	 */
	private Integer model;
	/**
	 * 尺寸，单位英尺
	 */
	private Integer size;
	/**
	 * 所属部门id
	 */
	private Long deptId;
	@Override
	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	/**
	 * 总重，单位kg
	 */
	private Float maxGross;
	/**
	 * 自重/皮重，单位kg
	 */
	private Float tare;
	/**
	 * 载重/净重，单位kg
	 */
	private Float net;
	/**
	 * 最大装货容积，单位m³
	 */
	private Float cuCap;
	/**
	 * 长度，单位mm
	 */
	private Integer length;
	/**
	 * 高度，单位mm
	 */
	private Integer height;
	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**
	 * 修改时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	/**
	 * 0-未删除，1-已删除
	 */
	private Integer deleted;
	/**
	 * 目标类别，0-未知，1-车辆，2-人员，3-基础设施、4-集装箱……
	 */
	private Integer targetType;

	/**
	 * 绑定序列号
	 */
	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;
	/**
	 * 导出动态表头中文名
	 */
	private List headNameList;

	/**
	 * 导出动态表头字段名
	 */
	private List columnNameList;

	/**
	 * 导出数据的id集合
	 */
	private List<Long> ids;

	//导入错误
	@ExcelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String msg;

	@ExcelIgnore
	private String createAccount;
}
