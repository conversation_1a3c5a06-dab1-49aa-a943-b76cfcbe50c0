package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springblade.common.dept.DeptIdAware;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 北斗工卡查询条件
 */
@Data
@ExcelIgnoreUnannotated
public class MonitDeviceRequest  implements DeptIdAware {

	/**
	 * id
	 */
	@Column(name = "id")
	private Long id;

	/**
	 * unique_id
	 */
	@ApiModelProperty(value = "序列号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "序列号")
	@ColumnWidth(22)
	private String uniqueId;

	/**
	 * imei
	 */
	@Column(name = "imei")
	private String imei;

	/**
	 * model
	 */
	@Column(name = "model")
	private String model;

	/**
	 * vendor
	 */
	@Column(name = "vendor")
	private String vendor;

	/**
	 * bd_chip_sn
	 */
	@Column(name = "bd_chip_sn")
	private String bdChipSn;

	/**
	 * device_type
	 */
	@Column(name = "device_type")
	private Integer deviceType;

	/**
	 * 特殊性，1-旧设备，2-新设备，3-特殊设备
	 */
	@Column(name = "specificity")
	private Integer specificity;

	/**
	 * dept_id
	 */
	@Column(name = "dept_id")
	private Long deptId;
	@Override
	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	/**
	 * target_id
	 */
	@Column(name = "target_id")
	private Long targetId;

	/**
	 * category
	 */
	@Column(name = "category")
	private Integer category;

	/**
	 * device_num
	 */
	@Column(name = "device_num")
	private String deviceNum;

	/**
	 * longitude
	 */
	@Column(name = "longitude")
	private BigDecimal longitude;

	/**
	 * latitude
	 */
	@Column(name = "latitude")
	private BigDecimal latitude;

	/**
	 * address
	 */
	@Column(name = "address")
	private String address;

	/**
	 * installdate
	 */
	@Column(name = "installdate")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date installdate;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**
	 * 修改时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	/**
	 * altitude
	 */
	@Column(name = "altitude")
	private Integer altitude;
	/**
	 * 应用场景
	 */
	@Column(name = "scenario")
	private Integer scenario;

	/**
	 * 应用方向/领域
	 */
	@Column(name = "domain")
	private Integer domain;
	/**
	 * 物联网卡号
	 */
	private String numbers;
	/**
	 * gnss_mode
	 */
	@Column(name = "gnss_mode")
	private Integer gnssMode;

	private Integer iotProtocol;

	private String terminalId;

	private Integer deleted;

	private String monitoringSrc;

	/**
	 * 错误信息
	 */
	@ApiModelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "错误信息")
	@ColumnWidth(22)
	private String msg;


	//页码
	private Integer current;
	//页大小
	private Integer size;

	/**	 导出动态表头中文名*/
	private List headNameList;

	/**	 导出动态表头字段名*/
	private List columnNameList;

	/**	 导出数据的id集合*/
	private List<Long> ids;

	@ExcelIgnore
	private String createAccount;

	/**
	 * 资产类型，1-固定资产，2-非固定资产
	 */
	@ExcelIgnore
	private Short assetType;
	/**
	 * 归属单位类型，1-内部单位，2-外部单位
	 */
	@ExcelIgnore
	private Short ownDeptType;
	/**
	 * 归属单位名称，当归属单位类型为2时有效，即外部单位名称
	 */
	@ExcelIgnore
	private String ownDeptName;
}
