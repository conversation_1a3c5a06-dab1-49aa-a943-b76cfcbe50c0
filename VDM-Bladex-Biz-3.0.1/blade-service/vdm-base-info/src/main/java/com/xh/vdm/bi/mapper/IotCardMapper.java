package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.IotCardRequest;
import com.xh.vdm.bi.vo.request.TerminalIotRequest;
import com.xh.vdm.bi.vo.response.BdmIotCardExportResponse;
import com.xh.vdm.bi.vo.response.IotCardResponse;
import com.xh.vdm.biapi.entity.BdmIotCard;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmIotCard)表数据库访问层
 */
public interface IotCardMapper extends BaseMapper<BdmIotCard> {

	/**
	 * 查询指定行数据
	 *
	 * @param request
	 * @param page
	 * @param
	 * @return 对象列表
	 */
	IPage<BdmIotCardExportResponse> queryAll(IPage page, @Param("request") IotCardRequest request, @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	BdmIotCard queryById(@Param("id") Long id);


	/**
	 * 新增数据
	 *
	 * @param bdmIotCard 实例对象
	 * @return 影响行数
	 */
	int insert(BdmIotCard bdmIotCard);

	/**
	 * 修改数据
	 *
	 * @param bdmIotCard 实例对象
	 * @return 影响行数
	 */
	int update(BdmIotCard bdmIotCard);

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 影响行数
	 */
	int deleteById(Long id);

	 int deleteByIds(Long[] ids);

	void updateIotCardByDeviceId(@Param("numbers") String numbers, @Param("id") Long id, @Param("deviceType") Integer deviceType);

	List<IotCardResponse> iotList(@Param("number") String number, @Param("account") String account, @Param("deptIds") String deptIds);

	void updateDeviceIdById(@Param("id") Long id, @Param("deviceType") Integer deviceType);

	void insertBatch(List<IotCardRequest> list);

	//更新记录
	void updateDeviceId(List<TerminalIotRequest> request);

	void deleteByDeviceIds(@Param("ids") Long[] ids, @Param("deviceType") Integer deviceType);

	List<String> selectAllNumbers(String number);

	List<String> findExistingNumbers(List<String> numbers);

	// 插入记录
	void insertDeviceId(@Param("request") List<TerminalIotRequest> request);

	List<String> queryIot(@Param("list") List<String> list, @Param("userId") Long userId);

	/**
	 * @description: 查询物联网卡信息（国能底座逻辑）
	 * @author: zhouxw
	 * @date: 2025-06-161 14:11:50
	 * @param: [list, account, deptArrayStr]
	 * @return: java.util.List<java.lang.String>
	 **/
	List<String> queryIotCE(@Param("list") List<String> list, @Param("account") String account, @Param("deptArray") String deptArrayStr);
}

