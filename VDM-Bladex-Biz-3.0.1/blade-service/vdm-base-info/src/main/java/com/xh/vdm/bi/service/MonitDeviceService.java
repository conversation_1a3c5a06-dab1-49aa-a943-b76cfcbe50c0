package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.FacilityTerminalRequest;
import com.xh.vdm.bi.vo.request.MonitDeviceRequest;
import com.xh.vdm.bi.vo.response.FacilityNoBingResponse;
import com.xh.vdm.bi.vo.response.MonitDeviceResponse;
import com.xh.vdm.biapi.entity.BdmMonitDevice;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * @Description: 北斗监测管理
 */
public interface MonitDeviceService extends IService<BdmMonitDevice> {

	/**
     * 分页查询
     *
     * @param monitDeviceRequest 筛选条件
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<MonitDeviceResponse> queryByPage(MonitDeviceRequest monitDeviceRequest, DataAuthCE ceDataAuth);

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	MonitDeviceResponse detail(Long id);

	/**
	 * 新增数据
	 *
	 * @param monitDeviceRequest 实例对象
	 * @return 实例对象
	 */
	BdmMonitDevice insert(MonitDeviceRequest monitDeviceRequest);

	BdmMonitDevice insertMonitDevice(MonitDeviceRequest monitDeviceRequest);

	/**
	 * 修改数据
	 *
	 * @param monitDeviceRequest 实例对象
	 * @return 实例对象
	 */
	BdmMonitDevice update(MonitDeviceRequest monitDeviceRequest);

	BdmMonitDevice updateMonitDevice(MonitDeviceRequest monitDeviceRequest);

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	boolean deleteById(Long id);

	boolean deleteByIds(Long[] ids);

	IPage<FacilityNoBingResponse> selectNoBind(DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds);

	List<FacilityNoBingResponse> selectBindByFacilityId(Long id, Integer targetType, DeviceNoBindRequest request);

	List<MonitDeviceRequest> importExcel(List<MonitDeviceRequest> list, Long userId);

	void updateByDeviceId(Long id, Integer targetType);

	void updateBatchByTerminalId(List<FacilityTerminalRequest> monits, Long id, Integer targetType, Long deptId);

	void deleteByTargetIds(Long[] ids);

    long countByUserRole(DataAuthCE ceDataAuth);

    void updateDept(Long id, Integer targetType, Long deptId);

	int bindTarget(Long deviceId, Long targetId, Integer targetType,String targetName);
}
