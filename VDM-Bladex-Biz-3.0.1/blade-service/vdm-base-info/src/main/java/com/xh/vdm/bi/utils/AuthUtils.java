package com.xh.vdm.bi.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.bi.entity.BladeDept;
import com.xh.vdm.bi.entity.UserDeptRegulates;
import com.xh.vdm.bi.service.IBladeDeptService;
import com.xh.vdm.bi.service.IUserDeptRegulatesService;
import org.apache.commons.collections4.CollectionUtils;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.system.cache.SysCache;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 用户权限
 */
@Component
public class AuthUtils {

	private static IBladeDeptService iBladeDeptService;
	private static IUserDeptRegulatesService iUserDeptRegulatesService;

	private static CETokenUtil ceTokenUtil;

	@Resource
	public void setIBladeDeptService(IBladeDeptService iBladeDeptService) {
		AuthUtils.iBladeDeptService = iBladeDeptService;
	}

	@Resource
	public void setIUserDeptRegulatesService(IUserDeptRegulatesService iUserDeptRegulatesService) {
		AuthUtils.iUserDeptRegulatesService = iUserDeptRegulatesService;
	}

	@Resource
	public void setCeTokenUtil(CETokenUtil ceTokenUtil) {
		AuthUtils.ceTokenUtil = ceTokenUtil;
	}

	public static R<String> isValidServiceRole(BladeUser user) {
		if ((user == null) || StringUtils.isBlank(user.getRoleName())) {
			return R.fail(ResultCode.FAILURE.getCode(), "用户登录信息异常。");
		}

		return R.success(ResultCode.SUCCESS);
	}

	/**
	 * 按照用户权限合并处理获取最终部门列表
	 *
	 * @param user   当前登录用户
	 * @param deptId 查询部门ID
	 * @return 用户权限与所查部门的本下级部门交集
	 */
	public static R<List<Long>> getFinalDeptList(BladeUser user, Long deptId) {
		if ((user == null) || (user.getUserId() == null) || StringUtils.isBlank(user.getAccount()) || StringUtils.isBlank(user.getDeptId()) || StringUtils.isBlank(user.getTenantId())) {
			return R.fail(ResultCode.FAILURE.getCode(), "用户登录信息异常。");
		}

		// 获取登录用户本下级部门ID列表
		String[] deptListStr = user.getDeptId().split(",");
		List<Long> list1 = new ArrayList<>();
		List<Long> tmp;
		for (String deptIdStr : deptListStr) {
			tmp = SysCache.getDeptChildIds(Long.parseLong(deptIdStr));
			if (CollectionUtils.isNotEmpty(tmp)) {
				list1.addAll(tmp);
			}
		}
		if (deptId == null) {
			return R.data(ResultCode.SUCCESS.getCode(), list1, "");
		}

		// 获取查询参数本下级部门ID列表
		List<Long> list2 = SysCache.getDeptChildIds(deptId);
		if ((list2 == null) || list2.isEmpty()) {
			return R.fail(ResultCode.FAILURE.getCode(), "所提供组织架构信息异常。");
		}

		// 取两个部门ID列表交集
		Collection<Long> res = CollectionUtils.intersection(list1, list2);
		if ((res == null) || res.isEmpty()) {
			return R.fail(ResultCode.FAILURE.getCode(), "没有所提供组织架构的权限。");
		}

		return R.data(ResultCode.SUCCESS.getCode(), (List<Long>) res, "");
	}

	/**
	 * 按照用户权限合并处理获取最终部门列表
	 *
	 * @param user 当前登录用户
	 * @return 用户权限 -- 获取所有监管部门
	 */
	public static R<List<Long>> getDeptList(BladeUser user) {
		List<Long> deptList = new ArrayList<>();
		// 超级管理员账户，查看所有部门
		if (AuthUtil.isAdministrator()) {
			return R.data(ResultCode.SUCCESS.getCode(), deptList, "");
		}
		if(!ceTokenUtil.isCELogin()){
			if (!AuthUtil.isAdministrator()) {
				deptList = iUserDeptRegulatesService.getBaseMapper()
					.selectList(new QueryWrapper<UserDeptRegulates>()
						.eq("user_id", user.getUserId())).stream()
					.map(UserDeptRegulates::getDeptId)
					.collect(Collectors.toList());
			}
			if (CollectionUtils.isEmpty(deptList)) {
				return R.fail("未能获取该用户的组织架构");
			}
		}

		return R.data(ResultCode.SUCCESS.getCode(), deptList, "");
	}
}
