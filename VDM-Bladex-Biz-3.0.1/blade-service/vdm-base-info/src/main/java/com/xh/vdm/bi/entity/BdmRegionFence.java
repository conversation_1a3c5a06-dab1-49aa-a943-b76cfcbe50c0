package com.xh.vdm.bi.entity;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (BdmRegionFence)实体类
 */
@Data
public class BdmRegionFence implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private String name;

	private String type;

	private String coordinate;

	private Long classifyId;

	private String deptIds;

	private Date createTime;

	private Date updateTime;

	private Long createUserId;

	private Integer isDel;

}

