package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.TrainCargoBoxRequest;
import com.xh.vdm.bi.vo.response.TrainCargoBoxResponse;
import com.xh.vdm.biapi.entity.BdmTrainCargoBox;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmTrainCargoBox)表数据库访问层
 */
public interface BdmTrainCargoBoxMapper extends BaseMapper<BdmTrainCargoBox> {

	/**
	 * 查询指定行数据
	 *
	 * @param page            分页对象
	 * @param request 查询条件
	 * @param account
	 * @return 对象列表
	 */
	IPage<TrainCargoBoxResponse> queryAll(@Param("request") TrainCargoBoxRequest request, @Param("page") IPage page, @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 新增数据
	 *
	 * @param trainCargoBox 实例对象
	 * @return 影响行数
	 */
	int insert(TrainCargoBoxRequest trainCargoBox);

	/**
	 * 批量新增数据（MyBatis原生foreach方法）
	 *
	 * @param entities List<BdmTrainCargoBox> 实例对象列表
	 * @return 影响行数
	 */
	int insertBatch(@Param("entities") List<BdmTrainCargoBox> entities);

	/**
	 * 修改数据
	 *
	 * @param trainCargoBox 实例对象
	 * @return 影响行数
	 */
	int update(BdmTrainCargoBox trainCargoBox);

	int batchUpdate(@Param("ids") List<Long> ids,@Param("deptId") Long deptId);

}

