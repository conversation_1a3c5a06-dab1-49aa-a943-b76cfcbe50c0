package com.xh.vdm.bi.mapper.bdCheck;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bi.entity.bdCheck.BdcTerminal;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface BdcTerminalMapper extends BaseMapper<BdcTerminal> {

	void updateFormalByDeviceSeq(@Param("deviceSeq") String deviceSeq);

	void updateFormalByDeviceSeqs(@Param("uniqueIdList") List<String> uniqueIdList);
}
