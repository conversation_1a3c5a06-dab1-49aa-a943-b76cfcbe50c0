package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.entity.BdmDeviceCode;
import com.xh.vdm.bi.vo.request.DeviceCodeRequest;
import org.springblade.core.mp.support.Query;

import java.util.List;

/**
 * (BdmDeviceCode)表服务接口
 */
public interface BdmDeviceCodeService extends IService<BdmDeviceCode> {

    void updateActivatedByDeviceNum(List<String> deviceNumList);

	void updateActivated(String deviceNum);

	IPage<BdmDeviceCode> select(DeviceCodeRequest deviceCodeRequest, Query query);

	List<String> selectDeviceNum();
}
