package com.xh.vdm.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.enums.*;
import com.xh.vdm.bi.mapper.WearableDeviceMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bi.utils.DeviceNumUtils;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.SnowflakeIdWorker;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.TerminalIotRequest;
import com.xh.vdm.bi.vo.request.WearableDeviceRequest;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.WearableDeviceResponse;
import com.xh.vdm.bi.vo.response.WorkerBingResponse;
import com.xh.vdm.biapi.entity.*;
import com.xh.vdm.interManager.feign.IMessageClient;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 北斗穿戴实体管理
 */
@Service
@Slf4j
public class WearableDeviceServiceImpl extends ServiceImpl<WearableDeviceMapper, BdmWearableDevice> implements WearableDeviceService {
	@Resource
	private WearableDeviceMapper wearableDeviceMapper;

	@Resource
	private CETokenUtil ceTokenUtil;
	@Resource
	private IotCardService iotCardService;
	@Resource
	private RedisTemplate redisTemplate;
	@Autowired
	private DeviceNumUtils deviceNumUtils;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private IBdcTerminalService terminalService;
	@Resource
	private BdmDeviceCodeService deviceCodeService;
	@Resource
	private BdmDeviceLedgerService deviceLedgerService;
	@Resource
	private IMessageClient messageClient;
	@Value("${current.schema}")
	String schema;
	@Resource
	private IBdmVirtualTargetService bdmVirtualTargetService;
	@Resource
	private IBdmAbstractDeviceService bdmAbstractDeviceService;
	@Resource
	private IBdmAbstractTargetService bdmAbstractTargetService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
	 * 分页查询
	 *
	 * @param request    筛选条件
	 * @param ceDataAuth
	 * @return 查询结果
	 */
	public IPage<WearableDeviceResponse> queryByPage(WearableDeviceRequest request, DataAuthCE ceDataAuth) {
		Page page = new Page();
		page.setCurrent(request.getCurrent());
		page.setSize(request.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.wearableDeviceMapper.queryAll(page, request, response.getAccount(), response.getOrgList(), schema);
	}

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmWearableDevice insert(WearableDeviceRequest request) {
		//判断是更新还是新增
		BdmWearableDevice bdmWearableDevice = new BdmWearableDevice();
		QueryWrapper<BdmWearableDevice> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("unique_id", request.getUniqueId());
		BdmWearableDevice wear = baseMapper.selectOne(queryWrapper);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		if (wear != null) {
			request.setId(wear.getId());
			if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			if (WearCategoryEnum.WATCH.getSymbol().equals(request.getCategory())) {
				request.setIotProtocol(IotProtocolEnum.MQTT.getCode());
			}
			request.setCreateTime(new Date());
			request.setDeleted(0);
			bdmWearableDevice = this.init(request);
			QueryWrapper<BdmVirtualTarget> wrapper = new QueryWrapper<>();
			wrapper.eq("number", bdmWearableDevice.getUniqueId());
			wrapper.last("LIMIT 1");
			BdmVirtualTarget latestRecord = bdmVirtualTargetService.getOne(wrapper);
			//同步到待分配终端对象实体表
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmWearableDevice, virtualTarget, "id");
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmWearableDevice.getDeviceNum());
			virtualTarget.setNumber(bdmWearableDevice.getUniqueId());
			if (latestRecord == null) {
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
				boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
				if (saveRes) {
					log.info("bdmVirtualTargetService漏掉的 新增成功");
				} else {
					log.info("bdmVirtualTargetService漏掉的 新增失败:{}", virtualTarget);
				}
			} else {
				virtualTarget.setId(latestRecord.getId());
				boolean updateRes = bdmVirtualTargetService.updateById(virtualTarget);
				if (updateRes) {
					log.info("bdmVirtualTargetService 更新成功");
				} else {
					log.info("bdmVirtualTargetService 更新失败:{}", virtualTarget);
				}
			}
			QueryWrapper<BdmAbstractTarget> batWrapper = new QueryWrapper<>();
			batWrapper.eq("number", bdmWearableDevice.getUniqueId());
			batWrapper.last("LIMIT 1");
			BdmAbstractTarget batLatestRecord = bdmAbstractTargetService.getOne(batWrapper);
			//同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmWearableDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmWearableDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmWearableDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			if (batLatestRecord == null) {
				boolean saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
				if (saveRes) {
					log.info("bdmAbstractTargetService漏掉的 新增成功");
				} else {
					log.info("bdmAbstractTargetService漏掉的 新增失败:{}", bdmAbstractTarget);
				}
			} else {
				boolean updateRes = bdmAbstractTargetService.updateById(bdmAbstractTarget);
				if (updateRes) {
					log.info("bdmAbstractTargetService 更新成功");
				} else {
					log.info("bdmAbstractTargetService 更新失败:{}", bdmAbstractTarget);
				}
			}
			bdmWearableDevice.setTargetId(virtualTarget.getId());
			baseMapper.update(bdmWearableDevice);
			//同步到抽象监控对象实体表
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(bdmWearableDevice, abstractDevice);
			abstractDevice.setTargetType(0);
			abstractDevice.setDeleted(0);
			bdmAbstractDeviceService.updateById(abstractDevice);
		} else {
			//旧设备、特殊设备生成赋码号
			if (BdmTypeEnum.OLD.getValue().equals(request.getSpecificity())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}
			if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
				R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
				if (r1.isSuccess()) {
					request.setDeviceNum(r1.getData());
				}
			}

			SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.WEAR_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			request.setId(idWorker.nextId());
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			if (WearCategoryEnum.WATCH.getSymbol().equals(request.getCategory())) {
				request.setIotProtocol(IotProtocolEnum.MQTT.getCode());
			}
			request.setCreateTime(new Date());
			bdmWearableDevice = this.init(request);
			String account = AuthUtil.getUserAccount();
			bdmWearableDevice.setCreateAccount(account);
			//同步到待分配终端对象实体表
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmWearableDevice, virtualTarget);
			virtualTarget.setId(targetId.nextId());
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmWearableDevice.getDeviceNum());
			virtualTarget.setNumber(bdmWearableDevice.getUniqueId());
			virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmVirtualTargetService.save(virtualTarget);
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmWearableDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmWearableDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmWearableDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			bdmAbstractTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmAbstractTargetService.save(bdmAbstractTarget);

			bdmWearableDevice.setTargetId(virtualTarget.getId());
			this.baseMapper.insertWear(bdmWearableDevice);
			//同步到抽象监控对象实体表
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(bdmWearableDevice, abstractDevice);
			abstractDevice.setTargetType(0);
			abstractDevice.setDeleted(0);
			abstractDevice.setCreateAccount(AuthUtil.getUserAccount());
			bdmAbstractDeviceService.saveDevice(abstractDevice);
		}
		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			//更新存在且未绑定的物联网卡信息
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), bdmWearableDevice.getId(), bdmWearableDevice.getDeviceType());
		}
		if (bdmWearableDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeq(request.getUniqueId());
			//更新bdm_device_code
			deviceCodeService.updateActivated(request.getDeviceNum());
		}
		BdmWearableDevice wearableDevice = getBaseMapper().selectById(bdmWearableDevice.getId());

		Map<String, String> map = new HashMap<>();
		String key = wearableDevice.getDeviceType() + "-" + wearableDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", wearableDevice.getUniqueId());
		innerMap.put("deviceNum", wearableDevice.getDeviceNum());
		innerMap.put("category", wearableDevice.getCategory());
		innerMap.put("deviceType", wearableDevice.getDeviceType());
		innerMap.put("deptId", wearableDevice.getDeptId());
		innerMap.put("iotProtocol", wearableDevice.getIotProtocol());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()) && !WearCategoryEnum.WATCH.getSymbol().equals(request.getCategory())) {
			Map<String, String> targetMap = new HashMap<>();
			String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + wearableDevice.getId();
			Map<String, Object> targetInnerMap = new HashMap<>();
			targetInnerMap.put("targetName", wearableDevice.getUniqueId());
			targetInnerMap.put("targetType", 0);
			targetInnerMap.put("deptId", wearableDevice.getDeptId());
			try {
				map.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targetMap);
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setDeviceId(wearableDevice.getId());
		deviceInfo.setDeviceType(wearableDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(wearableDevice.getUniqueId());
		deviceInfo.setDeviceModel(wearableDevice.getModel());
		deviceInfo.setDeviceNum(wearableDevice.getDeviceNum());
		deviceInfo.setDeptId(wearableDevice.getDeptId());
		deviceInfo.setSpecificity(wearableDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_wearable_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("穿戴式终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, wearableDevice.getDeviceType(), wearableDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		return wearableDevice;
	}

	@Override
	public BdmWearableDevice insertWearableDevice(WearableDeviceRequest request) {

		//判断是更新还是新增
		BdmWearableDevice bdmWearableDevice = new BdmWearableDevice();
		QueryWrapper<BdmWearableDevice> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("unique_id", request.getUniqueId());
		BdmWearableDevice wear = baseMapper.selectOne(queryWrapper);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		if (wear != null) {
			request.setId(wear.getId());
			request.setCreateTime(new Date());
			request.setDeleted(0);
			bdmWearableDevice = this.init(request);
			QueryWrapper<BdmVirtualTarget> wrapper = new QueryWrapper<>();
			wrapper.eq("number", bdmWearableDevice.getUniqueId());
			wrapper.last("LIMIT 1");
			BdmVirtualTarget latestRecord = bdmVirtualTargetService.getOne(wrapper);
			//同步到待分配终端对象实体表
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmWearableDevice, virtualTarget, "id");
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmWearableDevice.getDeviceNum());
			virtualTarget.setNumber(bdmWearableDevice.getUniqueId());
			if (latestRecord == null) {
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
				boolean saveRes = bdmVirtualTargetService.save(virtualTarget);
				if (saveRes) {
					log.info("bdmVirtualTargetService漏掉的 新增成功");
				} else {
					log.info("bdmVirtualTargetService漏掉的 新增失败:{}", virtualTarget);
				}
			} else {
				virtualTarget.setId(latestRecord.getId());
				boolean updateRes = bdmVirtualTargetService.updateById(virtualTarget);
				if (updateRes) {
					log.info("bdmVirtualTargetService 更新成功");
				} else {
					log.info("bdmVirtualTargetService 更新失败:{}", virtualTarget);
				}
			}
			QueryWrapper<BdmAbstractTarget> batWrapper = new QueryWrapper<>();
			batWrapper.eq("number", bdmWearableDevice.getUniqueId());
			batWrapper.last("LIMIT 1");
			BdmAbstractTarget batLatestRecord = bdmAbstractTargetService.getOne(batWrapper);
			//同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmWearableDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmWearableDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmWearableDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			if (batLatestRecord == null) {
				boolean saveRes = bdmAbstractTargetService.save(bdmAbstractTarget);
				if (saveRes) {
					log.info("bdmAbstractTargetService漏掉的 新增成功");
				} else {
					log.info("bdmAbstractTargetService漏掉的 新增失败:{}", bdmAbstractTarget);
				}
			} else {
				boolean updateRes = bdmAbstractTargetService.updateById(bdmAbstractTarget);
				if (updateRes) {
					log.info("bdmAbstractTargetService 更新成功");
				} else {
					log.info("bdmAbstractTargetService 更新失败:{}", bdmAbstractTarget);
				}
			}
			bdmWearableDevice.setTargetId(virtualTarget.getId());
			baseMapper.update(bdmWearableDevice);
		} else {
			SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.WEAR_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			request.setId(idWorker.nextId());
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			if (WearCategoryEnum.WATCH.getSymbol().equals(request.getCategory())) {
				request.setIotProtocol(IotProtocolEnum.MQTT.getCode());
			}
			request.setCreateTime(new Date());
			bdmWearableDevice = this.init(request);
			String account = AuthUtil.getUserAccount();
			bdmWearableDevice.setCreateAccount(account);
			//同步到待分配终端对象实体表
			BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
			BeanUtils.copyProperties(bdmWearableDevice, virtualTarget);
			virtualTarget.setId(null);
			virtualTarget.setTargetType(0);
			virtualTarget.setDeviceCode(bdmWearableDevice.getDeviceNum());
			virtualTarget.setNumber(bdmWearableDevice.getUniqueId());
			virtualTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmVirtualTargetService.save(virtualTarget);
			// 同步到抽象监控对象表
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			bdmAbstractTarget.setId(virtualTarget.getId());
			bdmAbstractTarget.setNumber(bdmWearableDevice.getUniqueId());
			bdmAbstractTarget.setName(bdmWearableDevice.getUniqueId());
			bdmAbstractTarget.setTargetType(0);
			bdmAbstractTarget.setDeptId(bdmWearableDevice.getDeptId());
			bdmAbstractTarget.setCategory(0);
			bdmAbstractTarget.setDeleted(0);
			bdmAbstractTarget.setCreateAccount(AuthUtil.getUserAccount());
			bdmAbstractTargetService.save(bdmAbstractTarget);

			bdmWearableDevice.setTargetId(virtualTarget.getId());
			bdmWearableDevice.setId(null);
			this.baseMapper.insert(bdmWearableDevice);
		}
		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			//更新存在且未绑定的物联网卡信息
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), bdmWearableDevice.getId(), bdmWearableDevice.getDeviceType());
		}
		if (bdmWearableDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeq(request.getUniqueId());
			//更新bdm_device_code
			deviceCodeService.updateActivated(request.getDeviceNum());
		}
		BdmWearableDevice wearableDevice = getBaseMapper().selectById(bdmWearableDevice.getId());

		Map<String, String> map = new HashMap<>();
		String key = wearableDevice.getDeviceType() + "-" + wearableDevice.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", wearableDevice.getUniqueId());
		innerMap.put("deviceNum", wearableDevice.getDeviceNum());
		innerMap.put("category", wearableDevice.getCategory());
		innerMap.put("deviceType", wearableDevice.getDeviceType());
		innerMap.put("deptId", wearableDevice.getDeptId());
		innerMap.put("iotProtocol", wearableDevice.getIotProtocol());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()) && !WearCategoryEnum.WATCH.getSymbol().equals(request.getCategory())) {
			Map<String, String> targetMap = new HashMap<>();
			String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + wearableDevice.getId();
			Map<String, Object> targetInnerMap = new HashMap<>();
			targetInnerMap.put("targetName", wearableDevice.getUniqueId());
			targetInnerMap.put("targetType", 0);
			targetInnerMap.put("deptId", wearableDevice.getDeptId());
			try {
				map.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targetMap);
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setDeviceId(wearableDevice.getId());
		deviceInfo.setDeviceType(wearableDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(wearableDevice.getUniqueId());
		deviceInfo.setDeviceModel(wearableDevice.getModel());
		deviceInfo.setDeviceNum(wearableDevice.getDeviceNum());
		deviceInfo.setDeptId(wearableDevice.getDeptId());
		deviceInfo.setSpecificity(wearableDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_wearable_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("穿戴式终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_ADD, wearableDevice.getDeviceType(), wearableDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		return wearableDevice;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmWearableDevice update(WearableDeviceRequest request) {
		if (WearCategoryEnum.WATCH.getSymbol().equals(request.getCategory())) {
			request.setIotProtocol(IotProtocolEnum.MQTT.getCode());
		}

		BdmWearableDevice bdmWearableDevice = this.init(request);
		this.wearableDeviceMapper.update(bdmWearableDevice);

		iotCardService.updateDeviceIdById(request.getId(), request.getDeviceType(), request.getNumbers());

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), request.getId(), request.getDeviceType());
		}

		BdmWearableDevice wearableDevice = getBaseMapper().selectById(bdmWearableDevice.getId());

		// 未绑定的非新设备更新抽象终端表 TODO 是否需要判断是否已经绑定
		//if (BdmTypeEnum.NEW.getValue().equals(wearableDevice.getSpecificity())) {
			BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
			BeanUtils.copyProperties(wearableDevice, abstractDevice);
			bdmAbstractDeviceService.updateById(abstractDevice);
		//}

		// 更新Redis缓存
		String key = wearableDevice.getDeviceType() + "-" + wearableDevice.getId();
		HashMap<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", wearableDevice.getUniqueId());
		innerMap.put("deviceNum", wearableDevice.getDeviceNum());
		innerMap.put("category", wearableDevice.getCategory());
		innerMap.put("deviceType", wearableDevice.getDeviceType());
		innerMap.put("deptId", wearableDevice.getDeptId());
		innerMap.put("iotProtocol", wearableDevice.getIotProtocol());
		HashMap<String, Object> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		// todo 这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setDeviceId(wearableDevice.getId());
		deviceInfo.setDeviceType(wearableDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(wearableDevice.getUniqueId());
		deviceInfo.setDeviceModel(wearableDevice.getModel());
		deviceInfo.setDeviceNum(wearableDevice.getDeviceNum());
		deviceInfo.setDeptId(wearableDevice.getDeptId());
		deviceInfo.setSpecificity(wearableDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_wearable_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("穿戴式终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_UPDATE, wearableDevice.getDeviceType(), wearableDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		return wearableDevice;
	}

	@Override
	public BdmWearableDevice updateWearableDevice(WearableDeviceRequest request) {
		if (WearCategoryEnum.WATCH.getSymbol().equals(request.getCategory())) {
			request.setIotProtocol(IotProtocolEnum.MQTT.getCode());
		}

		BdmWearableDevice bdmWearableDevice = this.init(request);
		this.wearableDeviceMapper.update(bdmWearableDevice);

		iotCardService.updateDeviceIdById(request.getId(), request.getDeviceType(), request.getNumbers());

		if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
			iotCardService.updateIotCardByDeviceId(request.getNumbers(), request.getId(), request.getDeviceType());
		}

		BdmWearableDevice wearableDevice = getBaseMapper().selectById(bdmWearableDevice.getId());

		// 更新Redis缓存
		String key = wearableDevice.getDeviceType() + "-" + wearableDevice.getId();
		HashMap<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", wearableDevice.getUniqueId());
		innerMap.put("deviceNum", wearableDevice.getDeviceNum());
		innerMap.put("category", wearableDevice.getCategory());
		innerMap.put("deviceType", wearableDevice.getDeviceType());
		innerMap.put("deptId", wearableDevice.getDeptId());
		innerMap.put("iotProtocol", wearableDevice.getIotProtocol());
		HashMap<String, Object> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		// todo 这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setDeviceId(wearableDevice.getId());
		deviceInfo.setDeviceType(wearableDevice.getDeviceType());
		deviceInfo.setDeviceUniqueId(wearableDevice.getUniqueId());
		deviceInfo.setDeviceModel(wearableDevice.getModel());
		deviceInfo.setDeviceNum(wearableDevice.getDeviceNum());
		deviceInfo.setDeptId(wearableDevice.getDeptId());
		deviceInfo.setSpecificity(wearableDevice.getSpecificity());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_wearable_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("穿戴式终端信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.terminal(CommonConstant.OPER_TYPE_UPDATE, wearableDevice.getDeviceType(), wearableDevice);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		return wearableDevice;
	}

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	@Override
	public boolean deleteById(Long id) {
		return this.wearableDeviceMapper.deleteById(id) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {
		QueryWrapper<BdmWearableDevice> wrapper = new QueryWrapper<>();
		wrapper.in("id", ids);
		List<BdmWearableDevice> list = this.wearableDeviceMapper.selectList(wrapper);

		List<String> deviceNumList = new ArrayList<>();
		List<String> uniqueIdList = new ArrayList<>();
		List<String> notWatchList = new ArrayList<>();

		if (!list.isEmpty()) {
			// 查询 deviceNum 列表
			deviceNumList = list.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
				.map(BdmWearableDevice::getDeviceNum)
				.collect(Collectors.toList());

			// 查询 uniqueId 列表
			uniqueIdList = list.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
				.map(BdmWearableDevice::getUniqueId)
				.collect(Collectors.toList());

			// 查询 uniqueId 列表
			notWatchList = list.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()) && !WearCategoryEnum.WATCH.getSymbol().equals(request.getCategory()))
				.map(BdmWearableDevice::getUniqueId)
				.collect(Collectors.toList());
		}

		boolean result = this.wearableDeviceMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.WEAR_DEVICE_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_DEVICE + ":" + key);
			}
			this.iotCardService.deleteByDeviceIds(ids, DeviceTypeEnum.WEARABLE.getSymbol());

			if (!uniqueIdList.isEmpty()) {
				// 跟新bdm_terminal
				terminalService.updateFormalByDeviceSeqs(uniqueIdList);
			}
			if (!deviceNumList.isEmpty()) {
				// 更新bdm_device_code
				deviceCodeService.updateActivatedByDeviceNum(deviceNumList);
			}

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.DELETE);
			Long lastId = ids[ids.length - 1];
			deviceInfo.setDeviceId(lastId);
			deviceInfo.setDeviceIds(CollUtil.newHashSet(ids));
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_wearable_device", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("穿戴式终端信息更新消息发送到kafka失败", e);
			}
			//messageClient
			try {
				List<Object> terminals = new ArrayList<>(list);
				messageClient.terminalBatch(CommonConstant.OPER_TYPE_DELETE, BaseInfoConstants.WEAR_DEVICE_TYPE, terminals);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}

			//同步更新抽象监控对象实体表
			bdmAbstractDeviceService.deleteByIds(ids);
			// 同步更新待分配终端对象实体表 删除
			if (!notWatchList.isEmpty()) {
				bdmVirtualTargetService.updateByUniqueId(notWatchList);

				// 同步更新待分配终端对象实体表
				QueryWrapper<BdmVirtualTarget> virtualTargetQueryWrapper = new QueryWrapper<>();
				if (!notWatchList.isEmpty()) {
					MybatisPlusWrapperUitls.longListIn(wrapper, "unique_id", notWatchList);
				}
				virtualTargetQueryWrapper.select("id");
				List<Long> idList = bdmVirtualTargetService.getBaseMapper()
					.selectList(virtualTargetQueryWrapper)
					.stream()
					.map(BdmVirtualTarget::getId)
					.collect(Collectors.toList());
				// 同步更新抽象监控对象实体表
				if (!idList.isEmpty()) {
					bdmAbstractTargetService.deleteByIds(idList.toArray(new Long[0]));
					for (Long id : idList) {
						String targetKey = BaseInfoConstants.VIRTUAL_TARGET_TYPE + "-" + id;
						redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + targetKey);
					}
				}
			}
		}
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void connectWorkerTerminal(PersonTerminalRequest request, Long id, Integer targetType, String targetName, Long deptId) {
		this.wearableDeviceMapper.connectWorkerTerminal(request, id, targetType, targetName, deptId);

		Map<String, String> map = new HashMap<>();
		String key = request.getDeviceType() + "-" + request.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("uniqueId", request.getUniqueId());
		innerMap.put("deviceNum", request.getDeviceNum());
		innerMap.put("category", request.getCategory());
		innerMap.put("deviceType", request.getDeviceType());
		innerMap.put("deptId", request.getDeptId());
		innerMap.put("iotProtocol", request.getIotProtocol());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			log.error(e.getMessage());
		}
		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateBatchByTerminalId(List<PersonTerminalRequest> requestList, Long id, Integer targetType, String targetName, Long deptId){
		List<Long> ids = requestList.stream().map(PersonTerminalRequest::getId).collect(Collectors.toList());
		if (!ids.isEmpty()) {
			this.wearableDeviceMapper.updateBatch(ids, id, targetType, targetName, deptId);
		}

		Map<String, String> map = new HashMap<>();
		for (PersonTerminalRequest request : requestList) {
			String key = request.getDeviceType() + "-" + request.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", request.getUniqueId());
			innerMap.put("deviceNum", request.getDeviceNum());
			innerMap.put("category", request.getCategory());
			innerMap.put("deviceType", request.getDeviceType());
			innerMap.put("deptId", request.getDeptId());
			innerMap.put("iotProtocol", request.getIotProtocol());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				log.error(e.getMessage());
			}
		}
		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
	}

	@Override
	public IPage<PersonNoBingResponse> select(DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds) {
		Page page = new Page();
		page.setCurrent(deviceNoBindRequest.getCurrent());
		page.setSize(deviceNoBindRequest.getSize());
		return this.wearableDeviceMapper.select(page, deviceNoBindRequest, account, deptIds);
	}

	@Override
	public List<WorkerBingResponse> selectByWorkId(Long id, Integer targetType) {
		return this.wearableDeviceMapper.selectByWorkId(id, targetType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateByWorkerId(Long id, Integer targetType) {
		this.wearableDeviceMapper.updateByWorkerId(id, targetType);
	}

	@Override
	public List<PersonNoBingResponse> selectBindByWorkId(Long id, Integer targetType) {
		return this.wearableDeviceMapper.selectBindByWorkId(id, targetType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<WearableDeviceRequest> importExcel(List<WearableDeviceRequest> list, Long userId) {
		List<WearableDeviceRequest> duplicateRequests = getDuplicateRequests(list);
		List<WearableDeviceRequest> requests = new ArrayList<>(list);
		// 移除duplicateRequests中的所有元素
		requests.removeIf(request -> duplicateRequests.contains(request));
		if (!requests.isEmpty()) {
			List<WearableDeviceRequest> filteredList = processRequests(requests, userId, duplicateRequests);
			if (!filteredList.isEmpty()) {
				List<Long> arrayList = updateDatabaseAndCache(filteredList);
				sendNotifications(filteredList, arrayList);
			}
		}
		return duplicateRequests;
	}

	/**
	 * 重复数据
	 *
	 * @param list
	 * @return
	 */
	public List<WearableDeviceRequest> getDuplicateRequests(List<WearableDeviceRequest> list) {
		// 映射并处理可能的null值
		List<String> numberList = list.stream()
			.map(WearableDeviceRequest::getNumbers)
			.map(Optional::ofNullable)
			.map(optional -> optional.orElse(null))
			.distinct()
			.collect(Collectors.toList());

		// 找出重复的物联网卡号
		List<String> duplicates = numberList.stream()
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
			.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		// 找出重复的序列号
		List<String> uniqueIdList = list.stream()
			.map(WearableDeviceRequest::getUniqueId)
			.collect(Collectors.toList());

		List<String> duplicateUniqueIds = uniqueIdList.stream()
			.filter(id -> Collections.frequency(uniqueIdList, id) > 1)
			.distinct()
			.collect(Collectors.toList());

		return list.stream()
			.filter(wearableDeviceRequest -> {
				String numbers = wearableDeviceRequest.getNumbers();
				boolean isDuplicateNumbers = numbers != null && duplicates.contains(numbers);
				boolean isDuplicateUniqueId = duplicateUniqueIds.contains(wearableDeviceRequest.getUniqueId());
				return isDuplicateNumbers || isDuplicateUniqueId;
			})
			.peek(wearableDeviceRequest -> {
				if (wearableDeviceRequest.getNumbers() != null && duplicates.contains(wearableDeviceRequest.getNumbers())) {
					wearableDeviceRequest.setMsg("导入的数据物联网卡号重复");
				}
				if (duplicateUniqueIds.contains(wearableDeviceRequest.getUniqueId())) {
					wearableDeviceRequest.setMsg("导入的数据序列号重复");
				}
			})
			.collect(Collectors.toList());
	}

	/**
	 * 数据处理
	 */
	private List<WearableDeviceRequest> processRequests(List<WearableDeviceRequest> requests, Long userId, List<WearableDeviceRequest> duplicateRequests) {
		// 按部门ID分组
		Map<Long, List<WearableDeviceRequest>> groupedByDept = requests.stream()
			.collect(Collectors.groupingBy(WearableDeviceRequest::getDeptId));

		List<WearableDeviceRequest> filterList = new ArrayList<>();

		for (Map.Entry<Long, List<WearableDeviceRequest>> entry : groupedByDept.entrySet()) {
			// TODO 终端赋码号不存在！和 序列号已存在！可连表查进行判断 检查输入的赋码号的合法性
			Map<String, BdmDeviceLedger> deviceLedgerMap = getDeviceLedgers(entry.getKey())
				.stream()
				.collect(Collectors.toMap(BdmDeviceLedger::getDeviceNum, ledger -> ledger));

			List<WearableDeviceRequest> requestsForDept = entry.getValue();

			// 过滤出该部门下设备编号存在于deviceLedgers中的请求
			List<WearableDeviceRequest> validRequestsForDept = requestsForDept.stream()
				.filter(request -> {
					if (!BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
						return true;
					} else {
						return isValidRequest(request, deviceLedgerMap);
					}
				})
				.collect(Collectors.toList());

			filterList.addAll(validRequestsForDept);

			//检查输入的赋码号的合法性
			requestsForDept.stream()
				.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()) && !deviceLedgerMap.containsKey(request.getDeviceNum()))
				.peek(request -> request.setMsg("终端赋码号不存在！"))
				.forEach(duplicateRequests::add);
		}

		if (!filterList.isEmpty()) {

			List<String> uniqueIdList = filterList.stream()
				.filter(Objects::nonNull)
				.map(WearableDeviceRequest::getUniqueId)
				.collect(Collectors.toList());

			QueryWrapper<BdmWearableDevice> wrapper = new QueryWrapper<>();
			wrapper.eq("deleted", 0);
			if (!uniqueIdList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(wrapper, "unique_id", uniqueIdList);
			}
			wrapper.select("unique_id");

			//序列号判重
			List<String> uniqueIds = baseMapper.selectList(wrapper)
				.stream()
				.map(BdmWearableDevice::getUniqueId)
				.collect(Collectors.toList());

			List<WearableDeviceRequest> resultList = filterList.stream()
				.filter(wearableDeviceRequest ->
					wearableDeviceRequest.getUniqueId() != null && !uniqueIds.contains(wearableDeviceRequest.getUniqueId()))
				.collect(Collectors.toList());

			filterList.stream()
				.filter(request -> !resultList.contains(request))
				.peek(request -> request.setMsg("序列号已存在！"))
				.forEach(duplicateRequests::add);

			filterList = resultList;

			if (!filterList.isEmpty()) {
				// 物联网卡验证
				List<String> numberedList = resultList.stream()
					.filter(Objects::nonNull)
					.map(WearableDeviceRequest::getNumbers)
					.collect(Collectors.toList());

				List<String> numberList = new ArrayList<>();

				if (!numberedList.isEmpty()) {

					log.info("[importExcel]终端信息导入，将要判断是否是底座登录");
					DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
					boolean isCE = ceTokenUtil.isCELogin();
					if(isCE){
						log.info("[importExcel]终端信息导入，国能底座登录");
						//如果是国能底座，则查询数据权限范围内的物联网卡信息
						String account = ceDataAuth.getAccount();
						String deptArrayStr = ceDataAuth.getOrgListStr();
						numberList = iotCardService.queryIotCE(numberedList, account, deptArrayStr);
						log.info("[importExcel]终端信息导入，获取数据权限范围内的物联网卡数量为：{}", numberList==null?0:numberList.size());
					}else{
						userId = AuthUtil.isAdministrator() ? null : userId;
						// 物联网卡验证
						numberList = iotCardService.queryIot(numberedList, userId);
					}
				}

				final List<String> finalNumberList = numberList;

				List<WearableDeviceRequest> filteredList = resultList.stream()
					.filter(wearableDeviceRequest ->
						wearableDeviceRequest.getNumbers() == null || finalNumberList.contains(wearableDeviceRequest.getNumbers()))
					.collect(Collectors.toList());

				// 将除了filteredList的数据加到duplicateRequests
				resultList.stream()
					.filter(request -> !filteredList.contains(request))
					.peek(request -> request.setMsg("该物联网卡号不存在或已绑定！"))
					.forEach(duplicateRequests::add);

				filterList = filteredList;
			}
		}
		return filterList;
	}

	/**
	 * 查询出库到本单位数据
	 */
	private List<BdmDeviceLedger> getDeviceLedgers(Long deptId) {
		return deviceLedgerService.getBaseMapper()
			.selectList(new QueryWrapper<BdmDeviceLedger>()
				.eq("device_type", BaseInfoConstants.WEAR_DEVICE_TYPE)
				.eq("user_dept_id", deptId));
	}

	/**
	 * 新设备给设置正确的赋码信息
	 */
	private boolean isValidRequest(WearableDeviceRequest request, Map<String, BdmDeviceLedger> targetMap) {
		BdmDeviceLedger deviceLedger = targetMap.get(request.getDeviceNum());
		if (deviceLedger != null) {
			request.setModel(deviceLedger.getModel());
			request.setImei(deviceLedger.getImei());
			request.setVendor(deviceLedger.getVendor());
			request.setUniqueId(deviceLedger.getUniqueId());
			request.setBdChipSn(deviceLedger.getBdChipSn());
			request.setDeviceType(deviceLedger.getDeviceType());
			request.setCategory(deviceLedger.getCategory());
			return true;
		}
		return false;
	}

	/**
	 * 更新数据库和进行数据redis缓存
	 */
	private List<Long> updateDatabaseAndCache(List<WearableDeviceRequest> filteredList) {
		SnowflakeIdWorker idWorker = new SnowflakeIdWorker(BaseInfoConstants.WEAR_DEVICE_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.VIRTUAL_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
		Map<String, BdmWearableDevice> wearMap = baseMapper.selectList(new QueryWrapper<BdmWearableDevice>().eq("deleted", 1))
			.stream()
			.collect(Collectors.toMap(BdmWearableDevice::getUniqueId, WearableDevice -> WearableDevice));
		//用于判断恢复被删除的设备时，判断是执行新增还是恢复操作
		Map<String, BdmVirtualTarget> allVirtualTargetMap = bdmVirtualTargetService.list()
			.stream()
			.collect(Collectors.toMap(BdmVirtualTarget::getNumber, VirtualTarget -> VirtualTarget, (existing, replacement) -> existing));
		Map<String, BdmAbstractTarget> allBatMap = bdmAbstractTargetService.list()
			.stream()
			.collect(Collectors.toMap(BdmAbstractTarget::getNumber, bat -> bat, (existing, replacement) -> existing));
		// 创建一个 Map 来存储所有的键值对
		List<TerminalIotRequest> terminalIotRequests = new ArrayList<>();
		Map<String, String> map = new HashMap<>();
		Map<String, String> targrtMap = new HashMap<>();
		List<Long> arrayList = new ArrayList<>();
		List<BdmVirtualTarget> virtualTargets = new ArrayList<>();
		List<BdmAbstractDevice> abstractDevices = new ArrayList<>();
		List<BdmAbstractTarget> abstractTargets = new ArrayList<>();

		// 在处理filteredList之前，收集所有新设备的序列号
		Set<String> newDeviceUniqueIds = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()) && !WearCategoryEnum.WATCH.getSymbol().equals(request.getCategory()))
			.map(WearableDeviceRequest::getUniqueId)
			.collect(Collectors.toSet());

		Map<String, BdmVirtualTarget> virtualTargetMap = new HashMap<>();
		// 一次性查询所有匹配的BdmVirtualTarget记录
		if (!newDeviceUniqueIds.isEmpty()) {
			List<BdmVirtualTarget> allVirtualTargets = bdmVirtualTargetService.list(
				new QueryWrapper<BdmVirtualTarget>().in("number", newDeviceUniqueIds).select("id").select("number")
			);
			// 将查询结果存储在一个Map中，以便后续快速查找
			virtualTargetMap = allVirtualTargets.stream().collect(Collectors.toMap(BdmVirtualTarget::getNumber, Function.identity()));
		}

		for (WearableDeviceRequest request : filteredList) {
			BdmWearableDevice device = wearMap.get(request.getUniqueId());
			// device为空新增，不为空修改
			long deviceId = (device == null) ? idWorker.nextId() : device.getId();
			request.setId(deviceId);
			request.setDeleted(0);
			//安全帽走808协议
			if (WearCategoryEnum.WATCH.getSymbol().equals(request.getCategory())) {
				request.setIotProtocol(IotProtocolEnum.MQTT.getCode());
			}
			//设备赋码
			if (device == null) {
				if (request.getSpecificity().equals(BdmTypeEnum.OLD.getValue())) {
					R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
					if (r1.isSuccess()) {
						request.setDeviceNum(r1.getData());
					}
				}
				if (BdmTypeEnum.SPECIFICITY.getValue().equals(request.getSpecificity())) {
					R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.S.name());
					if (r1.isSuccess()) {
						request.setDeviceNum(r1.getData());
					}
				}
			} else {
				request.setDeviceNum(device.getDeviceNum());
			}
			//新设备的定位模式单独处理
			if (BdmTypeEnum.NEW.getValue().equals(request.getSpecificity())) {
				request.setGnssMode(GnssModeEnum.SINGLEBEIDOU.getValue());
			}
			request.setCreateTime(new Date());
			BdmWearableDevice bdmWearableDevice = this.init(request);
			BdmAbstractTarget bdmAbstractTarget = new BdmAbstractTarget();
			if (device == null) {
				//同步到待分配终端对象实体表
				BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
				BeanUtils.copyProperties(bdmWearableDevice, virtualTarget, "id");
				virtualTarget.setId(targetId.nextId());
				virtualTarget.setTargetType(0);
				virtualTarget.setDeviceCode(bdmWearableDevice.getDeviceNum());
				virtualTarget.setNumber(bdmWearableDevice.getUniqueId());
				virtualTargets.add(virtualTarget);

				// TODO 同步到抽象监控对象实体表 abstractTargets
				bdmAbstractTarget.setId(virtualTarget.getId());
				bdmAbstractTarget.setNumber(bdmWearableDevice.getUniqueId());
				bdmAbstractTarget.setName(bdmWearableDevice.getUniqueId());
				bdmAbstractTarget.setTargetType(0);
				bdmAbstractTarget.setDeptId(bdmWearableDevice.getDeptId());
				bdmAbstractTarget.setCategory(0);
				bdmAbstractTarget.setDeleted(0);
				abstractTargets.add(bdmAbstractTarget);
				bdmWearableDevice.setTargetId(virtualTarget.getId());
				this.baseMapper.insertWear(bdmWearableDevice);
				//同步到抽象监控对象实体表
				BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
				BeanUtils.copyProperties(bdmWearableDevice, abstractDevice);
				abstractDevice.setTargetType(0);
				abstractDevice.setDeleted(0);
				abstractDevices.add(abstractDevice);
			} else {
				//同步到待分配终端对象实体表
				BdmVirtualTarget virtualTarget = new BdmVirtualTarget();
				BeanUtils.copyProperties(bdmWearableDevice, virtualTarget, "id");
				virtualTarget.setTargetType(0);
				virtualTarget.setDeviceCode(bdmWearableDevice.getDeviceNum());
				virtualTarget.setNumber(bdmWearableDevice.getUniqueId());
				if (allVirtualTargetMap.containsKey(device.getUniqueId())) {
					virtualTarget.setId(virtualTargetMap.get(bdmWearableDevice.getUniqueId()).getId());
					bdmVirtualTargetService.updateById(virtualTarget);
				} else {
					virtualTarget.setId(targetId.nextId());
					virtualTargets.add(virtualTarget);
				}
				// TODO 更新到抽象监控对象实体表
				bdmAbstractTarget.setNumber(bdmWearableDevice.getUniqueId());
				bdmAbstractTarget.setName(bdmWearableDevice.getUniqueId());
				bdmAbstractTarget.setTargetType(0);
				bdmAbstractTarget.setDeptId(bdmWearableDevice.getDeptId());
				bdmAbstractTarget.setCategory(0);
				bdmAbstractTarget.setDeleted(0);
				bdmAbstractTarget.setId(virtualTarget.getId());
				if (allBatMap.containsKey(device.getUniqueId())) {
					bdmAbstractTargetService.updateById(bdmAbstractTarget);
				} else {
					abstractTargets.add(bdmAbstractTarget);
				}
				bdmWearableDevice.setTargetId(virtualTarget.getId());
				baseMapper.update(bdmWearableDevice);
				//同步到抽象监控对象实体表
				BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
				BeanUtils.copyProperties(bdmWearableDevice, abstractDevice);
				abstractDevice.setTargetType(0);
				abstractDevice.setDeleted(0);
				bdmAbstractDeviceService.updateById(abstractDevice);
			}
			request.setId(bdmWearableDevice.getId());
			//messageClient
			arrayList.add(bdmWearableDevice.getId());
			//物联网卡
			if (request.getNumbers() != null && !request.getNumbers().isEmpty()) {
				TerminalIotRequest terminalIotRequest = new TerminalIotRequest();
				terminalIotRequest.setId(bdmWearableDevice.getId());
				terminalIotRequest.setNumber(request.getNumbers());
				terminalIotRequest.setDeviceType(bdmWearableDevice.getDeviceType());
				terminalIotRequests.add(terminalIotRequest);
			}
			//redis缓存
			String key = bdmWearableDevice.getDeviceType() + "-" + bdmWearableDevice.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", bdmWearableDevice.getUniqueId());
			innerMap.put("deviceNum", bdmWearableDevice.getDeviceNum());
			innerMap.put("category", bdmWearableDevice.getCategory());
			innerMap.put("deviceType", bdmWearableDevice.getDeviceType());
			innerMap.put("deptId", bdmWearableDevice.getDeptId());
			innerMap.put("iotProtocol", bdmWearableDevice.getIotProtocol());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}

			//待分配终端对象 缓存到 baseinfo_target
			if (bdmWearableDevice.getSpecificity().equals(BdmTypeEnum.NEW.getValue())) {
				String targetKey = TargetTypeEnum.ENDPOINTS.getSymbol() + "-" + bdmAbstractTarget.getId();
				Map<String, Object> targetInnerMap = new HashMap<>();
				targetInnerMap.put("targetName", bdmAbstractTarget.getNumber());
				targetInnerMap.put("targetType", bdmAbstractTarget.getTargetType());
				targetInnerMap.put("deptId", bdmAbstractTarget.getDeptId());
				try {
					targrtMap.put(targetKey, new ObjectMapper().writeValueAsString(targetInnerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
		}
		if (!virtualTargets.isEmpty()) {
			MapperUtils.splitListByCapacity(virtualTargets, MapperUtils.DEFAULT_CAPACITY)
				.forEach(virtualTargetList -> this.bdmVirtualTargetService.insertBatch(virtualTargetList));
		}
		if (!abstractDevices.isEmpty()) {
			MapperUtils.splitListByCapacity(abstractDevices, MapperUtils.DEFAULT_CAPACITY)
				.forEach(abstractDeviceList -> this.bdmAbstractDeviceService.insertBatch(abstractDeviceList));
		}
		// 新设备同步到抽象监控对象实体表
		if (!abstractTargets.isEmpty()) {
			MapperUtils.splitListByCapacity(abstractTargets, MapperUtils.DEFAULT_CAPACITY)
				.forEach(abstractTargetList -> this.bdmAbstractTargetService.insertBatch(abstractTargetList));
		}

		if (!map.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
		}
		if (!targrtMap.isEmpty()) {
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, targrtMap);
		}
		//物联网卡跟新
		if (!terminalIotRequests.isEmpty()) {
			this.iotCardService.updateDeviceId(terminalIotRequests);
		}

		// 查询 deviceNum 列表
		List<String> deviceNumList = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(WearableDeviceRequest::getDeviceNum)
			.collect(Collectors.toList());
		// 查询 uniqueId 列表
		List<String> uniqueIdList = filteredList.stream()
			.filter(request -> BdmTypeEnum.NEW.getValue().equals(request.getSpecificity()))
			.map(WearableDeviceRequest::getUniqueId)
			.collect(Collectors.toList());
		if (!uniqueIdList.isEmpty()) {
			//跟新bdm_terminal
			terminalService.updateFormalByDeviceSeqs(uniqueIdList);
		}
		if (!deviceNumList.isEmpty()) {
			//更新bdm_device_code
			deviceCodeService.updateActivatedByDeviceNum(deviceNumList);
		}
		return arrayList;
	}

	/**
	 * 发送消息
	 */
	private void sendNotifications(List<WearableDeviceRequest> filteredList, List<Long> arrayList) {
		WearableDeviceRequest request = filteredList.get(filteredList.size() - 1);
		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.IMPORT);
		deviceInfo.setDeviceId(request.getId());
		deviceInfo.setDeviceType(request.getDeviceType());
		deviceInfo.setDeviceModel(request.getModel());
		deviceInfo.setDeviceNum(request.getDeviceNum());
		deviceInfo.setDeviceUniqueId(request.getUniqueId());
		deviceInfo.setDeptId(request.getDeptId());
		Set<Long> ids = filteredList.stream().map(WearableDeviceRequest::getId).collect(Collectors.toSet());
		deviceInfo.setDeviceIds(ids);
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_wearable_device", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("穿戴式终端信息更新消息发送到kafka失败", e);
		}
		QueryWrapper<BdmWearableDevice> wrapper = new QueryWrapper<>();
		if (!arrayList.isEmpty()) {
			MybatisPlusWrapperUitls.longListIn(wrapper, "id", arrayList);
			List<BdmWearableDevice> wearableDevices = baseMapper.selectList(wrapper);
			//messageClient
			List<Object> terminals = new ArrayList<>(wearableDevices);
			try {
				messageClient.terminalBatch(CommonConstant.OPER_TYPE_ADD, BaseInfoConstants.WEAR_DEVICE_TYPE, terminals);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteByTargetIds(Long[] ids, Integer targetType) {
		this.wearableDeviceMapper.deleteByTargetIds(ids, targetType);
	}

	@Override
	public long countByUserRole(DataAuthCE ceDataAuth) {
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return baseMapper.countByUserRole(response.getAccount(), response.getOrgList());
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateDept(Long id, Integer targetType, Long deptId) {
		List<BdmWearableDevice> wearableDevices = baseMapper.selectList(
			new QueryWrapper<BdmWearableDevice>()
				.eq("target_id", id)
				.eq("target_type", targetType)
				.eq("deleted", 0)
				.select("id,unique_id,device_type,dept_id,category,device_num,iot_protocol")
		);
		if (!wearableDevices.isEmpty()) {
			baseMapper.updateDept(id, targetType, deptId);

			Map<String, String> map = new HashMap<>();
			for (BdmWearableDevice wearableDevice : wearableDevices) {
				String key = wearableDevice.getDeviceType() + "-" + wearableDevice.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("uniqueId", wearableDevice.getUniqueId());
				innerMap.put("deviceNum", wearableDevice.getDeviceNum());
				innerMap.put("category", wearableDevice.getCategory());
				innerMap.put("deviceType", wearableDevice.getDeviceType());
				innerMap.put("deptId", deptId);
				innerMap.put("iotProtocol", wearableDevice.getIotProtocol());
				try {
					map.put(key, new ObjectMapper().writeValueAsString(innerMap));
				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}
			}
			if (!map.isEmpty()) {
				redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);
			}
		}
	}


	/**
	 * 对象字段初始化
	 *
	 * @param request
	 * @return
	 */
	public BdmWearableDevice init(WearableDeviceRequest request) {
		BdmWearableDevice wearableDevice = new BdmWearableDevice();
		wearableDevice.setId(request.getId());
		wearableDevice.setUniqueId(request.getUniqueId() != null ? request.getUniqueId() : "");
		wearableDevice.setImei(request.getImei() != null ? request.getImei() : "");
		wearableDevice.setModel(request.getModel() != null ? request.getModel() : "");
		wearableDevice.setVendor(request.getVendor() != null ? request.getVendor() : "");
		wearableDevice.setBdChipSn(request.getBdChipSn() != null ? request.getBdChipSn() : "");
		wearableDevice.setDeviceType(DeviceTypeEnum.WEARABLE.getSymbol());
		wearableDevice.setSpecificity(request.getSpecificity() != null ? request.getSpecificity() : 0);
		wearableDevice.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		wearableDevice.setCategory(request.getCategory() != null ? request.getCategory() : 0);
		wearableDevice.setDeviceNum(request.getDeviceNum() != null ? request.getDeviceNum() : "");
		wearableDevice.setInstalldate(request.getInstalldate() != null ? request.getInstalldate() : null);
		wearableDevice.setCreateTime(request.getCreateTime() != null ? request.getCreateTime() : null);
		wearableDevice.setUpdateTime(request.getUpdateTime() != null ? request.getUpdateTime() : new Date());
		wearableDevice.setDeleted(request.getDeleted() != null ? request.getDeleted() : 0);
		wearableDevice.setScenario(request.getScenario() != null ? request.getScenario() : 0);
		wearableDevice.setDomain(request.getDomain() != null ? request.getDomain() : 0);
		wearableDevice.setGnssMode(request.getGnssMode() != null ? request.getGnssMode() : 0);
		wearableDevice.setIotProtocol(request.getIotProtocol() != null ? request.getIotProtocol() : IotProtocolEnum.JT808.getCode());
		wearableDevice.setTerminalId(request.getTerminalId() != null ? request.getTerminalId() : "");
		wearableDevice.setAssetType(request.getAssetType() != null ? request.getAssetType() : 0);
		wearableDevice.setOwnDeptType(request.getOwnDeptType() != null ? request.getOwnDeptType() : 0);
		wearableDevice.setOwnDeptName(request.getOwnDeptName() != null ? request.getOwnDeptName() : "");
		wearableDevice.setChannelNum(request.getChannelNum() != null ? request.getChannelNum() : 0);
		wearableDevice.setChannelIds(request.getChannelIds() != null ? request.getChannelIds() : "");
		return wearableDevice;
	}

	@Override
	public int bindTarget(Long deviceId, Long targetId, Integer targetType,String targetName){
		return this.wearableDeviceMapper.bindTarget(deviceId,targetId, targetType,targetName);
	}
}
