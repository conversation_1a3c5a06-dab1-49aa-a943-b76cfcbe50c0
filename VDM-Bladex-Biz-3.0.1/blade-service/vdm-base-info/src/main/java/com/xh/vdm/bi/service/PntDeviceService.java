package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.FacilityTerminalRequest;
import com.xh.vdm.bi.vo.request.PntDeviceRequest;
import com.xh.vdm.bi.vo.response.FacilityNoBingResponse;
import com.xh.vdm.bi.vo.response.PntDeviceResponse;
import com.xh.vdm.biapi.entity.BdmPntDevice;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * @Description: 北斗授时终端
 */
public interface PntDeviceService extends IService<BdmPntDevice> {

	/**
     * 分页查询
     *
     * @param request    筛选条件
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<PntDeviceResponse> queryByPage(PntDeviceRequest request, DataAuthCE ceDataAuth);

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	PntDeviceResponse queryById(Long id);

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmPntDevice insert(PntDeviceRequest request);


	BdmPntDevice insertPntDevice(PntDeviceRequest request);

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmPntDevice update(PntDeviceRequest request);

	BdmPntDevice updatePntDevice(PntDeviceRequest request);

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	boolean deleteById(Long id);

    boolean deleteByIds(Long[] ids);

	IPage<FacilityNoBingResponse> selectNoBind(DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds);

	List<FacilityNoBingResponse> selectBindByFacilityId(Long id, Integer targetType, DeviceNoBindRequest request);

	List<PntDeviceRequest> importExcel(List<PntDeviceRequest> list, Long userId);

	void updateByDeviceId(Long id, Integer targetType);

	void updateBatchByTerminalId(List<FacilityTerminalRequest> pnts, Long id, Integer targetType, Long deptId);

	void deleteByTargetIds(Long[] ids);

    long countByUserRole(DataAuthCE ceDataAuth);

    void updateDept(Long id, Integer targetType, Long deptId);

	int bindTarget(Long deviceId, Long targetId, Integer targetType,String targetName);
}
