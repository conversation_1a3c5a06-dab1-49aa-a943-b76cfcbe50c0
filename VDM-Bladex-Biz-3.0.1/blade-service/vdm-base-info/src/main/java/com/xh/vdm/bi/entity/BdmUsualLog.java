package com.xh.vdm.bi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * @description : 通用资源操作日志表
 */
@Data
@TableName("bdm_usual_log")
public class BdmUsualLog implements Serializable {

	private static final long serialVersionUID = 1L;
	/**
	 * id
	 */
	@TableId(type = IdType.AUTO)
	private Long id;

	/**
	 * user_id
	 */
	@Column(name="user_id")
	private Long userId;

	/**
	 * user_account
	 */
	@Column(name="user_account")
	private String userAccount;

	/**
	 * menu
	 */
	@Column(name="menu")
	private String menu;


	/**
	 * operation
	 */
	@Column(name="operation")
	private Integer operation;

	/**
	 * time
	 */
	@Column(name="time")
	private Date time;

	/**
	 * description
	 */
	private String  description;

	/**
	 * server_ip
	 */
	private String serverIp;

	/**
	 * dept_id
	 */
	private Long deptId;

	/**
	 * 操作记录ID
	 */
	@TableField(exist = false)
	private Long objectId;

	/**
	 * 被解绑终端ID
	 */
	@TableField(exist = false)
	private Long terminalId;
}
