package com.xh.vdm.bi.controller;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.service.BdmVisitorService;
import com.xh.vdm.bi.service.DeptId;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.utils.DictUtil;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.VisitorRequest;
import com.xh.vdm.bi.vo.response.ConnectResponse;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.VisitorResponse;
import com.xh.vdm.biapi.entity.BdmVisitor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springblade.common.annotation.Log;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.minio.MinioService;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.EasyExcelUtils;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.entity.DataAuthCE;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 访客人员管理
 */
@Slf4j
@RestController
@RequestMapping("/person/visitor")
@Validated
public class BdmVisitorController {
	/**
	 * 服务对象
	 */
	@Resource
	private BdmVisitorService bdmVisitorService;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private MinioService minioService;
	@Resource
	private DictUtil dictUtil;
	@Resource
	private CETokenUtil ceTokenUtil;

	/**
	 * 分页查询
	 *
	 * @param request 筛选条件
	 * @param query   分页对象
	 * @return 查询结果
	 */
	//pre_auth_test
	@PostMapping("/list")
	public R<IPage<VisitorResponse>> queryByPage(@RequestBody VisitorRequest request, Query query, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		IPage<VisitorResponse> page = this.bdmVisitorService.queryByPage(request, query, ceDataAuth);
		enrichDataWithDict(page.getRecords());
		return R.data(page);
	}

	/**
	 * 新增数据
	 *
	 * @param request 实体
	 * @return 新增结果
	 */
	//pre_auth_test
	@Log(menu = "访客管理", operation = Operation.INSERT, objectType = ObjectType.VISITOR)
	@PostMapping("/save")
	public R add(@Valid @RequestBody VisitorRequest request, BladeUser user) {
        LocalDate today = LocalDate.now();
        List<String> idNumbers = this.bdmVisitorService.getBaseMapper().selectList(
                        new QueryWrapper<BdmVisitor>()
                                .eq("deleted", 0)
                                .gt("DATE(valid_to)", today)
                                .eq("id_number", request.getIdNumber())
                                .select("id_number")
                )
                .stream()
                .map(BdmVisitor::getIdNumber)
                .collect(Collectors.toList());
        if (!idNumbers.isEmpty()) {
            return R.fail("身份证号已存在！");
        }
		try {
			BdmVisitor visitor = this.bdmVisitorService.insert(request);
			return R.data(ResultCode.SUCCESS.getCode(), visitor.getId().toString(), "新增成功");
		} catch (Exception e) {
			log.error(e.getMessage());
			return R.fail(e.getMessage());
		}
	}

	/**
	 * 编辑数据
	 * @param request 实体
	 * @return 编辑结果
	 */
	@Log(menu = "访客管理", operation = Operation.UPDATE, objectType = ObjectType.VISITOR)
	@PostMapping("/update")
	public R edit(@Valid @RequestBody VisitorRequest request, BladeUser user) {
        BdmVisitor visitor = this.bdmVisitorService.getById(request.getId());
        try {
            BdmVisitor bdmVisitor = this.bdmVisitorService.update(request);
            String result = new CompareUtils<BdmVisitor>().compare(visitor, bdmVisitor);
			return R.data(ResultCode.SUCCESS.getCode(), result, "编辑成功");
		} catch (Exception e) {
			log.error(e.getMessage());
			return R.fail("编辑失败");
		}
	}

	/**
	 * 批量更新所属机构
	 */
	@Log(menu = "访客管理", operation = Operation.UPDATE, objectType = ObjectType.VISITOR)
	@PostMapping("/batchUpdate")
	public R batchUpdate(@RequestBody BatchUpdateRequest batchUpdateRequest) {
		if (CollectionUtils.isEmpty(batchUpdateRequest.getIds())) {
			return R.fail("参数不能为空");
		}
		if (null == batchUpdateRequest.getDeptId()) {
			return R.fail("所属机构不能为空");
		}
		Boolean b = bdmVisitorService.batchUpdate(batchUpdateRequest);
		if (b){
			return R.data(ResultCode.SUCCESS.getCode(), "编辑成功");
		}else {
			return R.fail("操作失败");
		}

	}

	/**
	 * 删除数据
	 * @param ids 主键
	 * @return 删除是否成功
	 */
	@Log(menu = "访客管理", operation = Operation.DELETE, objectType = ObjectType.VISITOR)
	@GetMapping("/delete")
	public R deleteByIds(@RequestParam Long[] ids, BladeUser user) {
		Map<Long, Object> map = new HashMap<>();
		List<String> keys = new ArrayList<>();
		for (Long id : ids) {
			keys.add(BaseInfoConstants.VISITOR_TARGET_TYPE + "-" + id);
		}
		// 获取 Redis 数据
		List<Object> values = redisTemplate.opsForHash().multiGet(BaseInfoConstants.BASEINFO_TARGET, keys);
		// 处理获取到的值
		for (int i = 0; i < keys.size(); i++) {
			String key = keys.get(i);
			Object value = values.get(i);
			if (value != null) {
				String jsonValue = value.toString();
				BdmVisitor visitor = JSONObject.parseObject(jsonValue, BdmVisitor.class);

				int index = key.indexOf("-");
				if (index != -1) {
					String idValue = key.substring(index + 1);
					Long deptId = visitor.getDeptId();
					if (map.containsKey(deptId)) {
						String existingValues = (String) map.get(deptId);
						map.put(deptId, existingValues + "、" + idValue);
					} else {
						map.put(deptId, idValue);
					}
				}
			}
		}
		boolean result = this.bdmVisitorService.deleteByIds(ids);
		if (result) {
			return R.data(ResultCode.SUCCESS.getCode(), map, "删除成功");
		} else {
			return R.fail("删除失败");
		}
	}

	/**
	 * 绑定终端
	 *
	 * @param list 终端信息
	 * @param id   访客id
	 */
	@Log(menu = "访客管理", operation = Operation.BIND_OR_UNBIND, objectType = ObjectType.VISITOR)
	@PostMapping("/connect")
	public R<T> connectTerminal(@RequestBody List<PersonTerminalRequest> list, Long id, @DeptId Long deptId, BladeUser user) {
		if (id == null) {
			return R.fail("访客id不能为空");
		}
		ConnectResponse res = this.bdmVisitorService.connectTerminal(list, id, deptId);
		if (res.getCode()!=0) {
			return R.fail(ResultCode.FAILURE, res.getMsg());
		}
		return R.success("操作成功");
	}

	/**
	 * 查询未绑定终端列表
	 */
	//pre_auth_test
	@PostMapping("/select")
	public R<IPage<PersonNoBingResponse>> select(@RequestBody DeviceNoBindRequest deviceNoBindRequest, BladeUser user) {
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		IPage<PersonNoBingResponse> page = this.bdmVisitorService.select(deviceNoBindRequest, ceDataAuth);
		enrichDataByDict(page.getRecords());
		return R.data(page);
	}


	/**
	 * 查询已绑定终端列表
	 */
	@PostMapping("/selectBind")
	public R<List<PersonNoBingResponse>> selectBind(@RequestBody DeviceNoBindRequest deviceNoBindRequest, Long id) {
		List<PersonNoBingResponse> list = this.bdmVisitorService.selectBind(deviceNoBindRequest, id);
		enrichDataByDict(list);
		return R.data(list);
	}


	/**
	 * 导入
	 */
	@Log(menu = "访客管理", operation = Operation.IMPORT, objectType = ObjectType.VISITOR)
	@PostMapping("/importExcel")
	public R importExcel(@Valid @RequestBody List<VisitorRequest> list, BladeUser user) {
		if (list.isEmpty()) {
			return R.fail("数据为空");
		}
		List<VisitorRequest> result = this.bdmVisitorService.importExcel(list);
		if (result.isEmpty()) {
			Map<Long, StringBuilder> deptIdMap = new HashMap<>();
			for (VisitorRequest request : list) {
				Long deptId = request.getDeptId();
				Long id = request.getId();
				if (deptIdMap.containsKey(deptId)) {
					deptIdMap.get(deptId).append("、").append(id);
				} else {
					deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
				}
			}
			return R.data(ResultCode.SUCCESS.getCode(), deptIdMap, "导入成功");

		} else {
			String filePath = "";
			String menu = "访客信息管理";
			try {
				filePath = minioService.exportToMinIO(menu, result, VisitorRequest.class);
			} catch (IOException e) {
				log.error("导出错误数据失败");
			}
			List<VisitorRequest> filteredList = list.stream()
				.filter(item -> !result.contains(item))
				.collect(Collectors.toList());
			Map<Long, StringBuilder> deptIdMap = new HashMap<>();
			if (!filteredList.isEmpty()) {
				for (VisitorRequest request : filteredList) {
					Long deptId = request.getDeptId();
					Long id = request.getId();
					if (deptIdMap.containsKey(deptId)) {
						deptIdMap.get(deptId).append("、").append(id);
					} else {
						deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
					}
				}
			}
			return R.data(207, deptIdMap, filePath);
		}
	}

	/**
	 * 导出
	 */
	@PostMapping("/export")
	public R<String> export(@RequestBody VisitorRequest request, Query query, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		try {
			query.setCurrent(1);
			query.setSize(Integer.MAX_VALUE);
			IPage<VisitorResponse> list = this.bdmVisitorService.queryByPage(request, query, ceDataAuth);
			if (list.getRecords().isEmpty()) {
				return R.fail("没有数据可导出");
			}

			Map<String, String> map = new HashMap<>();
			//从字典中获取数据
			enrichDataWithDict(list.getRecords());
			for (VisitorResponse response : list.getRecords()) {
				String key = BaseInfoConstants.WORKER_TARGET_TYPE + "-" + response.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", response.getName() + "(" + response.getIdNumber() + ")");
				innerMap.put("targetType", response.getTargetType());
				innerMap.put("deptId", response.getDeptId());
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			}
			// todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			String menu = "访客信息管理";
			try {
				// 使用ByteArrayOutputStream创建Excel文件
				ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
				EasyExcelUtils.exportExcelToStream(
					menu,
					outputStream,
					(List) list.getRecords(),
					request.getHeadNameList(),
					request.getColumnNameList(),
					VisitorResponse.class
				);
				ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
				String minioFileUrl = minioService.uploadFile(menu, inputStream, outputStream.size());
				if (minioFileUrl != null) {
					return R.data(minioFileUrl);
				} else {
					return R.fail("文件上传失败");
				}
			} catch (Exception e) {
				log.info("文件上传失败 " + e.getMessage());
				return R.fail("文件上传失败");
			}
		} catch (Exception e) {
			log.error("导出失败", e);
			return R.fail("导出失败");
		}
	}

	private void enrichDataWithDict(List<VisitorResponse> records) {
		if (records.isEmpty()) {
			return;
		}
		Map<String, String> deviceTypeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_DEVICE_TYPE);
		Map<String, String> industryMap = dictUtil.getDictMap(BaseInfoConstants.BDM_WORKER_POST);

		for (VisitorResponse response : records) {
			response.setPostName(industryMap.getOrDefault(response.getPost(), null));
			response.setIndustryName(industryMap.getOrDefault(response.getIndustry(), null));
			if (response.getStatus() == 1) {
				response.setStatusName("生效");
			} else if (response.getStatus() == 2) {
				response.setStatusName("失效");
			} else {
				response.setStatusName("未生效");
			}
			if (StringUtils.isNotBlank(response.getTerminalCategories())) {
				String[] categories = response.getTerminalCategories().split(",");
				Set<String> uniqueCategories = new HashSet<>(Arrays.asList(categories));
				StringBuilder terminalCategories = new StringBuilder();
				for (String category : uniqueCategories) {
					String deviceType = deviceTypeMap.get(category);
					if (deviceType != null) {
						if (terminalCategories.length() > 0) {
							terminalCategories.append(", ");
						}
						terminalCategories.append(deviceType);
					}
				}
				response.setTerminalCategories(terminalCategories.toString());
			}
		}
	}

	private void enrichDataByDict(List<PersonNoBingResponse> records) {
		if (records.isEmpty()) {
			return;
		}
		Map<String, String> deviceTypeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_DEVICE_TYPE);

		for (PersonNoBingResponse response : records) {
			response.setCategoryName((response.getCategory() == null) ? "" : deviceTypeMap.getOrDefault(response.getCategory().toString(), ""));
			response.setDeviceTypeName((response.getDeviceType() == null) ? "" : deviceTypeMap.getOrDefault(response.getDeviceType().toString(), ""));
		}
	}

}

