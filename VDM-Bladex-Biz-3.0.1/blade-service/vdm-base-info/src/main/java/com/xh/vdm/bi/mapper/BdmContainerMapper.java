package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.ContainerRequest;
import com.xh.vdm.bi.vo.response.ContainerResponse;
import com.xh.vdm.biapi.entity.BdmContainer;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 集装箱管理
 */
public interface BdmContainerMapper extends BaseMapper<BdmContainer> {
	/**
	 * 查询指定行数据
	 *
	 * @param container 查询条件
	 * @param page     分页对象
	 * @return 对象列表
	 */
	IPage<ContainerResponse> queryByPage(@Param("container") ContainerRequest container, IPage page, @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 影响行数
	 */
	int insert(ContainerRequest request);

	/**
	 * 批量新增数据（MyBatis原生foreach方法）
	 *
	 * @param entities List<BdmContainer> 实例对象列表
	 * @return 影响行数
	 */
	int insertBatch(@Param("entities") List<BdmContainer> entities);

	/**
	 * 修改数据
	 *
	 * @param bdmContainer 实例对象
	 * @return 影响行数
	 */
	int update(BdmContainer bdmContainer);

	/**
	 * 通过主键删除数据
	 *
	 * @param ids 主键
	 * @return 影响行数
	 */
	int deleteByIds(Long[] ids);

	int batchUpdate(@Param("ids") List<Long> ids,@Param("deptId") Long deptId);
}

