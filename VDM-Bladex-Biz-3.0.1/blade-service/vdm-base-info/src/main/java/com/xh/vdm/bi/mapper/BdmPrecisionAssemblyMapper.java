package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.PrecisionAssemblyRequest;
import com.xh.vdm.bi.vo.response.PrecisionAssemblyResponse;
import com.xh.vdm.biapi.entity.BdmPrecisionAssembly;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description:
 */
public interface BdmPrecisionAssemblyMapper extends BaseMapper<BdmPrecisionAssembly> {

	/**
	 * 查询指定行数据
	 *
	 * @param request 查询条件
	 * @param page                 分页对象
	 * @param account
	 * @return 对象列表
	 */
	IPage<PrecisionAssemblyResponse> queryAll(@Param("request") PrecisionAssemblyRequest request, @Param("page") IPage page, @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 影响行数
	 */
	int insert(PrecisionAssemblyRequest request);

	/**
	 * 批量新增数据（MyBatis原生foreach方法）
	 *
	 * @param entities List<BdmPrecisionAssembly> 实例对象列表
	 * @return 影响行数
	 */
	int insertBatch(@Param("entities") List<BdmPrecisionAssembly> entities);

	/**
	 * 修改数据
	 *
	 * @param bdmPrecisionAssembly 实例对象
	 * @return 影响行数
	 */
	int update(BdmPrecisionAssembly bdmPrecisionAssembly);


	int batchUpdate(@Param("ids") List<Long> ids,@Param("deptId") Long deptId);

}

