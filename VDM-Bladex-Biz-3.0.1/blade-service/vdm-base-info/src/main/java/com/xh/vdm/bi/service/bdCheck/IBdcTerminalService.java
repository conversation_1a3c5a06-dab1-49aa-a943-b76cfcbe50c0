package com.xh.vdm.bi.service.bdCheck;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.entity.bdCheck.BdcTerminal;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IBdcTerminalService extends IService<BdcTerminal> {

	void updateFormalByDeviceSeq(@Param("deviceSeq") String deviceSeq);

	void updateFormalByDeviceSeqs(@Param("uniqueIdList") List<String> uniqueIdList);
}
