package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.dto.DeviceModelCount;
import com.xh.vdm.bi.dto.DeviceModelInfo;
import com.xh.vdm.bi.vo.request.*;
import com.xh.vdm.bi.vo.response.ConnectResponse;
import com.xh.vdm.bi.vo.response.FacilityNoBingResponse;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import com.xh.vdm.biapi.entity.BdmVirtualTarget;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmAbstractDevice)表服务接口
 */
public interface IBdmAbstractDeviceService extends IService<BdmAbstractDevice> {

	/**
	 * 通过主键逻辑删除
	 * @param ids
	 */
	void deleteByIds(Long[] ids);

	/**
	 * 批量插入
	 * @param abstractDeviceList
	 */
	void insertBatch(List<BdmAbstractDevice> abstractDeviceList);

	/**
	 * 新增单个
	 * @param abstractDevice
	 */
	void saveDevice(BdmAbstractDevice abstractDevice);

	/**
	 * 解除绑定
	 * @param id
	 * @param targetType
	 */
    void unbinding(Long id, Integer targetType);

	int bindTarget(Long deviceId, Long targetId, Integer targetType,String targetName);

	/**
	 * 绑定终端
	 * @param list
	 * @param id
	 * @param targetType
	 * @param targetName
	 * @param deptId
	 */
	void bind(List<PersonTerminalRequest> list, Long id, Integer targetType, String targetName, Long deptId);

	/**
	 * 绑定终端
	 * @param list
	 * @param id
	 * @param targetType
	 * @param targetName
	 * @param deptId
	 */
	void bindVehicle(List<VehicleTerminalRequest> list, Long id, Integer targetType, String targetName, Long deptId);

	/**
	 * 删除目标解绑抽象终端表
	 * @param ids
	 * @param targetType
	 */
	void deleteByTargetIds(Long[] ids, Integer targetType);

	/**
	 * 基础设施绑定终端
	 * @param list
	 * @param id
	 * @param targetType
	 * @param deptId
	 */
	void bindFacility(List<FacilityTerminalRequest> list, Long id, Integer targetType, Long deptId);

	void updateDept(Long id, Integer targetType, Long deptId);

	/**
	 * 基础设施分页查询未绑定终端
	 * @param deviceNoBindRequest
	 * @param account
	 * @return
	 */
    IPage<FacilityNoBingResponse> selectNoBind( DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds);

	/**
	 * 人员分页查询未绑定终端
	 * @param deviceNoBindRequest
	 * @param account
	 * @return
	 */
	IPage<PersonNoBingResponse> select(DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds);

	/**
	 * 人员查询绑定终端
	 * @param id
	 * @param targetType
	 * @return
	 */
	List<PersonNoBingResponse> selectBind(Long id, Integer targetType);

	/**
	 * 基础设施查询绑定终端
	 * @param id
	 * @param targetType
	 * @return
	 */
	List<FacilityNoBingResponse> selectBingByFacility(Long id, Integer targetType);
	void updateBatchByUniqueId(BdmVirtualTarget virtualTarget);

	/**
	 * 获取用户所监管的终端型号统计信息。
	 * @param auth 用户权限
	 * @return 终端型号统计信息
	 */
	List<DeviceModelInfo> getDeviceModelInfoByUserAuth(DataAuthCE auth);

	/**
	 * 获取用户所监管的中端型号统计信息。
	 * @param auth 用户权限
	 * @param models 设备型号列表
	 * @param district 行政区划编码
	 * @param deptId 指定部门id
	 * @param usage 使用类型
	 * @return 终端型号统计信息
	 */
	List<DeviceModelInfo> getDeviceModelInfoWith(DataAuthCE auth, List<String> models,
												 String district, Long deptId, Byte usage);

	/**
	 * 获取用户所监管的终端型号统计信息。
	 * @param auth 用户权限
	 * @return 终端型号统计信息
	 */
	List<DeviceModelCount> getDeviceModelCountByUserAuth(DataAuthCE auth);

	/**
	 * 获取用户所监管的终端型号统计信息。
	 * @param auth 用户权限
	 * @param models 设备型号列表
	 * @param district 行政区划编码
	 * @param deptId 指定部门id
	 * @param usage 使用类型
	 * @return 终端型号统计信息
	 */
	List<DeviceModelCount> getDeviceModelCountWith(DataAuthCE auth, List<String> models,
												   String district, Long deptId, Byte usage);

	List<BdmAbstractDevice> getListByUserRole(DataAuthCE ceDataAuth);

	/**
	 * 统计终端总数。
	 * @param ceDataAuth 账号权限信息
	 * @return 终端总数
	 */
	long countByUserRole(DataAuthCE ceDataAuth);

	BdmAbstractDevice getBadByUniqueId(String uniqueId);

	/**
	 * 统计终端总数。
	 * @param specificity 终端特殊性
	 * @param auth 用户权限
	 * @return 符合条件的终端总数
	 */
	long countByUserAuth(Byte specificity, DataAuthCE auth);

	/**
	 * 按条件查询终端id。
	 * @param specificity 终端特殊性
	 * @param auth 用户权限
	 * @param district 行政区划编码
	 * @param deptId 部门id
	 * @param usage 用法类型
	 * @return 符合条件的终端id列表
	 */
	List<Long> getDeviceIdsWith(Byte specificity, DataAuthCE auth, String district, Long deptId, Byte usage);

	/**
	 * 统计终端总数。
	 * @param specificity 终端特殊性
	 * @param auth 用户权限
	 * @return 符合条件的终端总数
	 */
	long countByUserWith(Byte specificity, DataAuthCE auth, String district, Long deptId, Byte usage);

	/**
	 * 统计终端总数（通过id列表）。
	 * @param specificity 终端特殊性
	 * @param deviceIds 终端id列表
	 * @return 符合条件的终端总数
	 */
	long countByDeviceIds(Byte specificity, String deviceIds);
}
