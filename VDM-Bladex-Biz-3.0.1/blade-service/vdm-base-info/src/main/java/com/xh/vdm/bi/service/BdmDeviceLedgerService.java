package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.BdmDeviceLedgerRequest;
import com.xh.vdm.biapi.entity.BdmDeviceLedger;
import org.springblade.core.mp.support.Query;

import java.util.List;

/**
 * (BdmDeviceLedger)表服务接口
 */
public interface BdmDeviceLedgerService extends IService<BdmDeviceLedger> {


	List<BdmDeviceLedger> insertBatch(List<BdmDeviceLedger> list, Integer kind, List<String> keySet, Long userId);

	long updateByIds(List<Long> ids, Long userDeptId, Long userId);

	List<BdmDeviceLedger> outImport(List<String> list);

	IPage<BdmDeviceLedger> queryAll(BdmDeviceLedgerRequest deviceLedgerRequest, Query query);

    void recycle(BdmDeviceLedger bdmDeviceLedger);

	boolean select(String uniqueId);

	boolean update(BdmDeviceLedger bdmDeviceLedger);
}
