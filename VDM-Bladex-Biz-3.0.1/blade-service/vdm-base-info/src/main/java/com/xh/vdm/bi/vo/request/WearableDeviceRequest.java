package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springblade.common.dept.DeptIdAware;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * Description: 北斗穿戴实体
 */
@Data
@ExcelIgnoreUnannotated
public class WearableDeviceRequest implements DeptIdAware {
	/**
	 * 主键，唯一标识码
	 */
	@Column(name = "id")
	private Long id;

	/**
	 * 设备id，也叫序列号
	 */
	@ApiModelProperty(value = "序列号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "序列号")
	@ColumnWidth(22)
	private String uniqueId;

	/**
	 * 国际移动设备识别码
	 */
	@Column(name = "imei")
	private String imei;

	/**
	 * 终端型号
	 */
	@Column(name = "model")
	private String model;

	/**
	 * 生产厂商名称
	 */
	@Column(name = "vendor")
	private String vendor;

	/**
	 * 北斗芯片序列号
	 */
	@Column(name = "bd_chip_sn")
	private String bdChipSn;

	/**
	 * 终端类别，1-北斗定位终
	 * 端，2-北斗穿戴式终端，3-北斗短
	 * 报文终端，4-北斗监测终端，5-北
	 * 斗授时终端
	 */
	@Column(name = "device_type")
	private Integer deviceType;

	/**
	 * 特殊性，1-日设备，2-新设备，3-特殊设备
	 */
	@Column(name = "specificity")
	private Integer specificity;

	/**
	 * 所属机构id
	 */
	@Column(name = "dept_id")
	private Long deptId;

	@Override
	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	/**
	 * 穿戴人员id
	 */
	@Column(name = "target_id")
	private Long targetId;

	/**
	 * 激活状态，0-未激活，1-已激活
	 */
	@Column(name = "activated")
	private Integer activated;

	/**
	 * 终端种类/功能类型，1-
	 * 手表/环，2-安全帽，...
	 */
	@Column(name = "category")
	private Integer category;

	/**
	 * 赋码值，16 位字符串
	 */
	@Column(name = "device_num")
	private String deviceNum;

	/**
	 * 安装时间
	 */
	@Column(name = "installdate")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date installdate;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**
	 * 修改时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	/**
	 * 物联网卡号
	 */
	private String number;

	/**
	 * 物联网卡号
	 */
	private String numbers;
	/**
	 * 应用场景
	 */
	@Column(name = "scenario")
	private Integer scenario;

	/**
	 * 应用方向/领域
	 */
	@Column(name = "domain")
	private Integer domain;

	/**
	 * gnss_mode
	 */
	@Column(name = "gnss_mode")
	private Integer gnssMode;

	private Integer iotProtocol;

	private String terminalId;

	private Integer deleted;

	/**
	 * 错误信息
	 */
	@ApiModelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "错误信息")
	@ColumnWidth(22)
	private String msg;

	//分页条件：当前页数
	private Integer current;
	//分页条件：每页大小
	private Integer size;

	/**
	 * 导出动态表头中文名
	 */
	private List headNameList;

	/**
	 * 导出动态表头字段名
	 */
	private List columnNameList;

	/**
	 * 导出数据的id集合
	 */
	private List<Long> ids;
	/**
	 * 资产类型，1-固定资产，2-非固定资产
	 */
	@ExcelIgnore
	private Short assetType;
	/**
	 * 归属单位类型，1-内部单位，2-外部单位
	 */
	@ExcelIgnore
	private Short ownDeptType;
	/**
	 * 归属单位名称，当归属单位类型为2时有效，即外部单位名称
	 */
	@ExcelIgnore
	private String ownDeptName;
	/**
	 * channel_num
	 */
	@Column(name = "channel_num")
	private Integer channelNum;
	private String channelIds;
}
