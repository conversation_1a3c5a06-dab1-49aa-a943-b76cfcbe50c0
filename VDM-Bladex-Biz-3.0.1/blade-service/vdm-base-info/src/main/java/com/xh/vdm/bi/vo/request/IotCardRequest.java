package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springblade.common.dept.DeptIdAware;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * (BdmIotCard)实体类
 */
@Data
@ExcelIgnoreUnannotated
public class IotCardRequest implements DeptIdAware {

	private Long id;
	//本机号
	@ApiModelProperty(value = "物联卡号码")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "物联卡号码")
	@ColumnWidth(22)
	private String number;
	//本机号
	private String cardNumber;
	//集成电路卡识别码
	private String iccid;
	//国际移动用户识别码
	private String imsi;
	//移动网络运营商，1-中国移动，2-中国电信，3-中国联通
	private Integer operator;
	//持卡人/客户名
	private String holder;
	//发卡日期
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date issueTime;
	//卡状态，1-已激活，2-已过期
	private Integer status;
	//激活日期
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date activationTime;
	//有效期
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date expire;
	//流量套餐，1-30M/月，2-50M/月,3-1G/月,4-2G/月,5-4G/月，10-10G/月，20-20G/月
	private Integer dataPlan;
	//流量包大小，单位 MB
	private Integer packetSize;
	//卡类型，1-定向流量卡, 2-公网物联网卡，3-电话卡
	private Integer category;
	//绑定的设备的 id
	private Long deviceId;
	//绑定的设备的类型
	private Integer deviceType;
	//所属机构
	private Long deptId;
	@Override
	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

	/**
	 * 导入失败的错误信息
	 */
	@ApiModelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "错误信息")
	@ColumnWidth(22)
	private String msg;

	private Integer current;
	private Integer size;

	/**	 导出动态表头中文名*/
	private List headNameList;

	/**	 导出动态表头字段名*/
	private List columnNameList;

	/**	 导出数据的id集合*/
	private List<Long> ids;

	@ExcelIgnore
	private String createAccount;

}

