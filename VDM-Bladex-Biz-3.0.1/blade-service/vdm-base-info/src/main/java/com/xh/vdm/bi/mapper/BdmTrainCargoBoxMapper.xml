<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmTrainCargoBoxMapper">

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.TrainCargoBoxResponse">
        select
        b.id,
        b.number,
        b.target_type,
        b.model,
        b.size,
        b.dept_id,
        b.max_gross,
        b.tare,
        b.net,
        b.cu_cap,
        b.length,
        b.height,
        to_char(b.create_time, 'YYYY-MM-DD HH24:MI:SS') as createTime,
        b.update_time,
        b.deleted,
        d.dept_name,
        rd.unique_id,
        rd.device_num,
        rd.category as terminalCategories
        from bdm_train_cargo_box b
        left join blade_dept d on b.dept_id = d.id
        left join bdm_rnss_device rd ON b.id = rd.target_id
        AND rd.deleted = 0 and rd.target_type = b.target_type
        where b.deleted = 0
            <if test="request.ids != null and request.ids.size() gt 0 ">
                and b.id in
                <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="request.ids == null or request.ids == ''">
                <if test="request.number != null and request.number != ''">
                    and b.number like concat('%', #{request.number}, '%')
                </if>
                <if test="request.deptId != null">
                    and b.dept_id = #{request.deptId}
                </if>
                <if test="request.uniqueId != null and request.uniqueId != ''">
                    and rd.unique_id like concat('%',#{request.uniqueId},'%')
                </if>
                <if test="request.deviceNum != null and request.deviceNum != ''">
                    and rd.device_num like concat('%',#{request.deviceNum},'%')
                </if>
                <if test="deptIds != null and deptIds != ''">
                    and d.id = any(${deptIds})
                </if>
                <if test="account != null and account != ''">
                    and b.create_account = #{account}
                </if>
            </if>
        order by b.create_time desc
    </select>

    <!--新增所有列-->
    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.xh.vdm.biapi.entity.BdmTrainCargoBox">
        INSERT INTO bdm_train_cargo_box
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != number and '' != number">
                number,
            </if>
            <if test="targetType != null">
                target_type,
            </if>
            <if test="model != null">
                model,
            </if>
            <if test="size != null">
                size,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="null != maxGross">
                max_gross,
            </if>
            <if test="null != tare">
                tare,
            </if>
            <if test="null != net">
                net,
            </if>
            <if test="null != cuCap">
                cu_cap,
            </if>
            <if test="length != null">
                length,
            </if>
            <if test="height != null">
                height,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted
            </if>
            <if test="null != createAccount">
                create_account
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != number and '' != number">
                #{number},
            </if>
            <if test="null != targetType">
                #{targetType},
            </if>
            <if test="null != model">
                #{model},
            </if>
            <if test="null != size">
                #{size},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != maxGross">
                #{maxGross},
            </if>
            <if test="null != tare">
                #{tare},
            </if>
            <if test="null != net">
                #{net},
            </if>
            <if test="null != cuCap">
                #{cuCap},
            </if>
            <if test="null != length">
                #{length},
            </if>
            <if test="null != height">
                #{height},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted}
            </if>
            <if test="null != createAccount">
                #{createAccount}
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bdm_train_cargo_box(id, number, model, size, dept_id, max_gross, tare, net, cu_cap, length,height)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.number},#{entity.model}, #{entity.size}, #{entity.deptId},
            #{entity.maxGross}, #{entity.tare}, #{entity.net}, #{entity.cuCap}, #{entity.length}, #{entity.height})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bdm_train_cargo_box
        <set>
            <if test="number != null">
                number = #{number},
            </if>
            <if test="targetType != null">
                target_type = #{targetType},
            </if>
            <if test="model != null">
                model = #{model},
            </if>
            <if test="size != null">
                size = #{size},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="maxGross != null">
                max_gross = #{maxGross},
            </if>
            <if test="tare != null">
                tare = #{tare},
            </if>
            <if test="net != null">
                net = #{net},
            </if>
            <if test="cuCap != null">
                cu_cap = #{cuCap},
            </if>
            <if test="length != null">
                length = #{length},
            </if>
            <if test="height != null">
                height = #{height},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted}
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="batchUpdate">
        UPDATE bdm_train_cargo_box
        <set>
            dept_id = #{deptId}
        </set>
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

</mapper>

