package com.xh.vdm.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 大类
 */
@AllArgsConstructor
@Getter
public enum ClassCodeEnum {

	/**
	 * 人员定位
	 */
	WORKER_RNSS("1", "人员定位"),
	/**
	 * 运载定位
	 */
	CARRY_RNSS("2", "运载定位"),

	/**
	 * 资产管理
	 */
	ASSET_MANAGEMENT("3", "资产管理"),

	/**
	 * 安全监测
	 */
	SAFE_MONIT("4", "安全监测"),


	/**
	 * 测绘勘探
	 */
	MAPPING_SURVEY("5", "测绘勘探"),



	/**
	 * 应急通信
	 */
	EMERGENCY_TELECOMMUNICATIONS("6", "应急通信"),


	/**
	 * 时频同步
	 */
	TIME_FREQUENCY_SYNCHRONIZATION("7", "时频同步"),



	/**
	 * 精密控制
	 */
	PRECISE_CONTROL("8", "精密控制"),



	/**
	 * 智能巡检
	 */
	INTELLIGENT_INSPECTION("9", "智能巡检");



	/**
	 * 标识
	 */
	private final String value;
	/**
	 * 状态
	 */
	private final String label;


}
