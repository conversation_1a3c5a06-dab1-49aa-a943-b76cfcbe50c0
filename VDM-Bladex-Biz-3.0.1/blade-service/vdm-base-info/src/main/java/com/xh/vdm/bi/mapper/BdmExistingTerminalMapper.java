package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.ExistingTerminalRequest;
import com.xh.vdm.bi.vo.response.ExistingTerminalResponse;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import org.apache.ibatis.annotations.Param;

public interface BdmExistingTerminalMapper extends BaseMapper<BdmAbstractDevice> {


	void saveDevice(BdmAbstractDevice abstractDevice);


	/**
	 * 查询指定行数据
	 *
	 * @param page    分页对象
	 * @param request 查询条件
	 * @return 对象列表
	 */
	IPage<ExistingTerminalResponse> queryAll(IPage page, @Param("request") ExistingTerminalRequest request, @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	ExistingTerminalResponse queryById(Long id);
}
