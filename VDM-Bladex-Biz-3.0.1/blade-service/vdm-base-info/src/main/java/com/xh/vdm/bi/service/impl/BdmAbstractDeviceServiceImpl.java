package com.xh.vdm.bi.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.dto.DeviceModelCount;
import com.xh.vdm.bi.dto.DeviceModelInfo;
import com.xh.vdm.bi.mapper.BdmAbstractDeviceMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.vo.request.*;
import com.xh.vdm.bi.vo.response.FacilityNoBingResponse;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import com.xh.vdm.biapi.entity.BdmVirtualTarget;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * (BdmAbstractDevice)表服务实现类
 */
@Service
public class BdmAbstractDeviceServiceImpl extends ServiceImpl<BdmAbstractDeviceMapper,BdmAbstractDevice> implements IBdmAbstractDeviceService {
	@Resource
	private DeptProcessingUtil deptProcessingUtil;
	@Override
	public void deleteByIds(Long[] ids) {
		baseMapper.deleteByIds(ids);
	}

	@Override
	public void insertBatch(List<BdmAbstractDevice> abstractDeviceList) {
		baseMapper.insertBatch(abstractDeviceList);
	}

	@Override
	public void saveDevice(BdmAbstractDevice abstractDevice) {
		baseMapper.saveDevice(abstractDevice);
	}

	@Override
	public void unbinding(Long id, Integer targetType) {
		baseMapper.unbinding(id, targetType);
	}

	@Override
	public int bindTarget(Long deviceId, Long targetId, Integer targetType,String targetName){
		return this.baseMapper.bindTarget(deviceId,targetId, targetType,targetName);
	}

	@Override
	public void bind(List<PersonTerminalRequest> list, Long id, Integer targetType, String targetName, Long deptId) {
		List<Long> ids = list.stream().map(PersonTerminalRequest::getId).collect(Collectors.toList());
		if (!ids.isEmpty()) {
			baseMapper.updateBatch(ids, id, targetType, targetName, deptId);
		}
	}

	@Override
	public void bindVehicle(List<VehicleTerminalRequest> list, Long id, Integer targetType, String targetName, Long deptId) {
		List<Long> ids = list.stream().map(VehicleTerminalRequest::getId).collect(Collectors.toList());
		if (!ids.isEmpty()) {
			baseMapper.updateBatch(ids, id, targetType, targetName, deptId);
		}
	}

	@Override
	public void deleteByTargetIds(Long[] ids, Integer targetType) {
		baseMapper.deleteByTargetIds(ids, targetType);
	}

	@Override
	public void bindFacility(List<FacilityTerminalRequest> list, Long id, Integer targetType, Long deptId) {
		List<Long> ids = list.stream().map(FacilityTerminalRequest::getId).collect(Collectors.toList());
		if (!ids.isEmpty()) {
			baseMapper.bindFacility(ids, id, targetType, deptId);
		}
	}

	@Override
	public void updateDept(Long id, Integer targetType, Long deptId) {
		baseMapper.updateDept(id, targetType, deptId);
	}

	@Override
	public IPage<FacilityNoBingResponse> selectNoBind(DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds) {
		Page page = new Page();
		page.setCurrent(deviceNoBindRequest.getCurrent());
		page.setSize(deviceNoBindRequest.getSize());
		return baseMapper.selectNoBind(page, deviceNoBindRequest, account, deptIds);
	}

	@Override
	public IPage<PersonNoBingResponse> select(DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds) {
		Page page = new Page();
		page.setCurrent(deviceNoBindRequest.getCurrent());
		page.setSize(deviceNoBindRequest.getSize());
		return baseMapper.select(page, deviceNoBindRequest, account, deptIds);
	}

	@Override
	public List<PersonNoBingResponse> selectBind(Long id, Integer targetType) {
		return baseMapper.selectBind(id, targetType);
	}

	@Override
	public List<FacilityNoBingResponse> selectBingByFacility(Long id, Integer targetType) {
		return baseMapper.selectBingByFacility(id, targetType);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public void updateBatchByUniqueId(BdmVirtualTarget virtualTarget) {
		baseMapper.updateBatchByUniqueId(virtualTarget);
	}

	@Override
	public List<DeviceModelInfo> getDeviceModelInfoByUserAuth(DataAuthCE auth) {
		AuthInfo response = deptProcessingUtil.handle(auth);
		return baseMapper.getDeviceModelInfoByUserAuth(response.getAccount(), response.getOrgList());
	}

	@Override
	public List<DeviceModelInfo> getDeviceModelInfoWith(DataAuthCE auth, List<String> models,
														String district, Long deptId, Byte usage) {
		// 如果未指定部门id
		String deptIds = null;
		String account = null;
		if (null == deptId) {
			AuthInfo response = deptProcessingUtil.handle(auth);
			account = response.getAccount();
			deptIds = response.getOrgList();
		}

		// 如果未指定行政区划
		if (null == district)
			return baseMapper.getDeviceModelInfoWith(account, deptIds, deptId, models, usage);

		return baseMapper.getDeviceModelInfoWithDistrict(account, deptIds, deptId, models, usage, district);
	}

	@Override
	public List<DeviceModelCount> getDeviceModelCountByUserAuth(DataAuthCE auth) {
		AuthInfo response = deptProcessingUtil.handle(auth);
		return baseMapper.getDeviceModelCountByUserAuth(response.getAccount(), response.getOrgList());
	}

	@Override
	public List<DeviceModelCount> getDeviceModelCountWith(DataAuthCE auth, List<String> models,
														  String district, Long deptId, Byte usage) {
		// 如果未指定部门id
		String deptIds = null;
		String account = null;
		if (null == deptId) {
			if (!AuthUtil.isAdministrator()) {
				AuthInfo response = deptProcessingUtil.handle(auth);
				account = response.getAccount();
				deptIds = response.getOrgList();
			}
		}

		// 如果未指定行政区划
		if (null == district)
			return baseMapper.getDeviceModelCountWith(account, deptIds, deptId, models, usage);

		return baseMapper.getDeviceModelCountWithDistrict(account, deptIds, deptId, models, usage, district);
	}

	@Transactional(rollbackFor = Exception.class)
	@Override
	public List<BdmAbstractDevice> getListByUserRole(DataAuthCE ceDataAuth) {
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return baseMapper.getListByUserRole(response.getAccount(), response.getOrgList());
	}

	@Override
	public long countByUserRole(DataAuthCE ceDataAuth) {
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return baseMapper.countByUserRole(response.getAccount(), response.getOrgList());
	}

	public BdmAbstractDevice getBadByUniqueId(String uniqueId){
		return baseMapper.getBadByUniqueId(uniqueId);
	}

	@Override
	public long countByUserAuth(Byte specificity, DataAuthCE auth) {
		AuthInfo response = deptProcessingUtil.handle(auth);
		return baseMapper.countByUserAuth(specificity, response.getAccount(), response.getOrgList());
	}

	@Override
	public List<Long> getDeviceIdsWith(Byte specificity, DataAuthCE auth, String district, Long deptId, Byte usage) {
		// 如果未指定部门id
		String deptIds = null;
		String account = null;
		if (null == deptId) {
			if (!AuthUtil.isAdministrator()) {
				AuthInfo response = deptProcessingUtil.handle(auth);
				account = response.getAccount();
				deptIds = response.getOrgList();
			}
		}

		// 未指定行政区划
		if (null == district)
			return baseMapper.getDeviceIdsWith(specificity, account, deptIds, deptId, usage);

		return baseMapper.getDeviceIdsWithDistrict(specificity, account, deptIds, deptId, usage, district);
	}

	@Override
	public long countByUserWith(Byte specificity, DataAuthCE auth, String district, Long deptId, Byte usage) {
		// 如果未指定部门id
		String deptIds = null;
		String account = null;
		if (null == deptId) {
			if (!AuthUtil.isAdministrator()) {
				AuthInfo response = deptProcessingUtil.handle(auth);
				account = response.getAccount();
				deptIds = response.getOrgList();
			}
		}

		// 未指定行政区划
		if (null == district)
			return baseMapper.countByUserWith(specificity, account, deptIds, deptId, usage);

		return baseMapper.countByUserWithDistrict(specificity, account, deptIds, deptId, usage, district);
	}

	@Override
	public long countByDeviceIds(Byte specificity, String deviceIds) {
		return baseMapper.countByDeviceIds(specificity, deviceIds);
	}
}






