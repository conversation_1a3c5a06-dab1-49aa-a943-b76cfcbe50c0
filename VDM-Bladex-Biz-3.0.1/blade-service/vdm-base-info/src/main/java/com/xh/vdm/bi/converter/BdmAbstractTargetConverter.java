package com.xh.vdm.bi.converter;

import com.xh.vdm.biapi.entity.*;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * 抽象监控对象实体对象转换
 */
@Data
public class BdmAbstractTargetConverter {

	public static BdmAbstractTarget toBdmWorker(BdmWorker worker) {
		if (worker == null) {
			return null;
		}
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(worker, abstractTarget);
		abstractTarget.setNumber(worker.getWkno());
		return abstractTarget;
	}

	public static BdmAbstractTarget toBdmVehicle(BdmVehicle bdmVehicle) {
		if (bdmVehicle == null) {
			return null;
		}
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(bdmVehicle, abstractTarget);
		abstractTarget.setName(bdmVehicle.getNumber());
		return abstractTarget;
	}

	public static BdmAbstractTarget toBdmMiningTruck(BdmMiningTruck bdmVehicle) {
		if (bdmVehicle == null) {
			return null;
		}
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(bdmVehicle, abstractTarget);
		abstractTarget.setName(bdmVehicle.getNumber());
		return abstractTarget;
	}


	public static BdmAbstractTarget toBdmFacility(BdmFacility facility) {
		if (facility == null) {
			return null;
		}
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(facility, abstractTarget);
		abstractTarget.setNumber(facility.getCode());
		return abstractTarget;
	}

	public static BdmAbstractTarget toBdmTemporary(BdmTemporary temporary) {
		if (temporary == null) {
			return null;
		}
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(temporary, abstractTarget);
		abstractTarget.setNumber(temporary.getWkno());
		return abstractTarget;
	}

	public static BdmAbstractTarget toBdmVisitor(BdmVisitor visitor) {
		if (visitor == null) {
			return null;
		}
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(visitor, abstractTarget);
		abstractTarget.setNumber(visitor.getIdNumber());
		return abstractTarget;
	}

	public static BdmAbstractTarget toBdmContainer(BdmContainer container) {
		if (container == null) {
			return null;
		}
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(container, abstractTarget);
		abstractTarget.setName(container.getNumber());
		return abstractTarget;
	}

	public static BdmAbstractTarget toBdmTrainCargoBox(BdmTrainCargoBox box) {
		if (box == null) {
			return null;
		}
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(box, abstractTarget);
		abstractTarget.setName(box.getNumber());
		return abstractTarget;
	}

	public static BdmAbstractTarget toBdmPrecisionAssembly(BdmPrecisionAssembly precisionAssembly) {
		if (precisionAssembly == null) {
			return null;
		}
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(precisionAssembly, abstractTarget);
		return abstractTarget;
	}

	public static BdmAbstractTarget toBdmShip(BdmShip ship) {
		if (ship == null) {
			return null;
		}
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(ship, abstractTarget);
		return abstractTarget;
	}

}
