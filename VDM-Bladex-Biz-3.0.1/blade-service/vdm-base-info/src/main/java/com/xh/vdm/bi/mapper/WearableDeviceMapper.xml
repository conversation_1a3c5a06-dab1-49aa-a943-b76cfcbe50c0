<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.WearableDeviceMapper">

    <sql id="Base_Column_List">
        wd.id,
        wd.unique_id,
        wd.imei,
        wd.model,
        wd.vendor,
        wd.bd_chip_sn,
        wd.device_type,
        wd.specificity,
        wd.dept_id,
        wd.target_id,
        wd.activated,
        wd.category,
        wd.device_num,
        wd.installdate,
        wd.create_time,
        wd.update_time,
        wd.deleted,
        wd.scenario,
        wd.domain,
        wd.gnss_mode,
        wd.terminal_id,
        d.dept_name
    </sql>

    <!--根据筛选条件查询数据-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.WearableDeviceResponse">
        SELECT
            wd.id, wd.unique_id, wd.imei, wd.model, wd.vendor,wd.iot_protocol,wd.target_type,
            wd.bd_chip_sn, wd.device_type, wd.specificity, wd.dept_id, wd.target_id,wd.terminal_id,
            wd.activated, wd.category, wd.device_num, wd.installdate, wd.scenario, wd.domain, d.dept_name,
            wd.gnss_mode,wd.asset_type,wd.own_dept_type,wd.own_dept_name,wd.channel_num,wd.channel_ids,ic."number" as numbers, ma.name as vendorName
        FROM bdm_wearable_device wd
        LEFT JOIN blade_dept d ON wd.dept_id = d.id
        LEFT JOIN bdm_worker wk ON wd.target_id = wk.id
        LEFT JOIN bdm_iot_card ic ON wd.id = ic.device_id and wd.device_type = ic.device_type
        LEFT JOIN ${schema}.bdc_manufactor ma ON ma.code = wd.vendor and ma.is_del = 0
        WHERE wd.deleted = 0
        <if test="request.ids != null and request.ids.size() gt 0 ">
            and wd.id in
            <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="request.ids == null or request.ids == ''">
            <if test="request.category != null">
                AND wd.category = #{request.category}
            </if>
            <if test="request.uniqueId != null and request.uniqueId != ''">
                AND wd.unique_id LIKE concat('%', #{request.uniqueId}, '%')
            </if>
            <if test="request.imei != null and request.imei != ''">
                AND wd.imei LIKE concat('%', #{request.imei}, '%')
            </if>
            <if test="request.number != null and request.number != ''">
                AND ic.number LIKE concat('%', #{request.number}, '%')
            </if>
            <if test="request.deviceNum != null and request.deviceNum != ''">
                and wd.device_num like concat('%', #{request.deviceNum}, '%')
            </if>
            <if test="request.specificity != null">
                AND wd.specificity = #{request.specificity}
            </if>
            <if test="request.deptId != null">
                AND wd.dept_id = #{request.deptId}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and d.id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and wd.create_account = #{account}
            </if>
        </if>
        ORDER BY wd.create_time DESC
    </select>

    <!--新增所有列-->
    <insert id="insertWear" useGeneratedKeys="true" keyColumn="id" keyProperty="id"
            parameterType="com.xh.vdm.biapi.entity.BdmWearableDevice">
        INSERT INTO bdm_wearable_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                unique_id,
            </if>
            <if test="null != imei and '' != imei">
                imei,
            </if>
            <if test="null != model and '' != model">
                model,
            </if>
            <if test="null != vendor and '' != vendor">
                vendor,
            </if>
            <if test="null != bdChipSn and '' != bdChipSn">
                bd_chip_sn,
            </if>
            <if test="null != deviceType">
                device_type,
            </if>
            <if test="null != specificity">
                specificity,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != targetId">
                target_id,
            </if>
            <if test="null != activated">
                activated,
            </if>
            <if test="null != category">
                category,
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                device_num,
            </if>
            <if test="null != installdate">
                installdate,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted,
            </if>
            <if test="null != scenario">
                scenario,
            </if>
            <if test="null != domain">
                domain,
            </if>
            <if test="null != gnssMode">
                gnss_mode,
            </if>
            <if test="null != iotProtocol">
                iot_protocol,
            </if>
            <if test="null != targetName and '' != targetName">
                target_name,
            </if>
            <if test="null != terminalId and '' != terminalId">
                terminal_id,
            </if>
            <if test="null != createAccount and '' != createAccount">
                create_account,
            </if>
            <if test="null != assetType">
                asset_type,
            </if>
            <if test="null != ownDeptType">
                own_dept_type,
            </if>
            <if test="null != ownDeptName">
                own_dept_name,
            </if>
            <if test="null != channelNum">
                channel_num,
            </if>
            <if test="null != channelIds">
                channel_ids
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                #{uniqueId},
            </if>
            <if test="null != imei and '' != imei">
                #{imei},
            </if>
            <if test="null != model and '' != model">
                #{model},
            </if>
            <if test="null != vendor and '' != vendor">
                #{vendor},
            </if>
            <if test="null != bdChipSn and '' != bdChipSn">
                #{bdChipSn},
            </if>
            <if test="null != deviceType">
                #{deviceType},
            </if>
            <if test="null != specificity">
                #{specificity},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != targetId">
                #{targetId},
            </if>
            <if test="null != activated">
                #{activated},
            </if>
            <if test="null != category">
                #{category},
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                #{deviceNum},
            </if>
            <if test="null != installdate">
                #{installdate},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted},
            </if>
            <if test="null != scenario">
                #{scenario},
            </if>
            <if test="null != domain">
                #{domain},
            </if>
            <if test="null != gnssMode">
                #{gnssMode},
            </if>
            <if test="null != iotProtocol">
                #{iotProtocol},
            </if>
            <if test="null != targetName and '' != targetName">
                #{targetName},
            </if>
            <if test="null != terminalId and '' != terminalId">
                #{terminalId},
            </if>
            <if test="null != createAccount and '' != createAccount">
                #{createAccount},
            </if>
            <if test="null != assetType">
                #{assetType},
            </if>
            <if test="null != ownDeptType">
                #{ownDeptType},
            </if>
            <if test="null != ownDeptName">
                #{ownDeptName},
            </if>
            <if test="null != channelNum">
                #{channelNum},
            </if>
            <if test="null != channelIds">
                #{channelIds}
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update" parameterType="com.xh.vdm.biapi.entity.BdmWearableDevice">
        UPDATE bdm_wearable_device
        <set>
            <if test="null != uniqueId">unique_id = #{uniqueId},</if>
            <if test="null != imei">imei = #{imei},</if>
            <if test="null != model">model = #{model},</if>
            <if test="null != vendor">vendor = #{vendor},</if>
            <if test="null != bdChipSn">bd_chip_sn = #{bdChipSn},</if>
            <if test="null != deviceType">device_type = #{deviceType},</if>
            <if test="null != specificity">specificity = #{specificity},</if>
            <if test="null != deptId">dept_id = #{deptId},</if>
            <if test="null != targetId">target_id = #{targetId},</if>
            <if test="null != activated">activated = #{activated},</if>
            <if test="null != category">category = #{category},</if>
            <if test="null != deviceNum">device_num = #{deviceNum},</if>
            <if test="installdate != null">installdate = #{installdate},</if>
            <if test="installdate == null">installdate = NULL,</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != deleted">deleted = #{deleted},</if>
            <if test="null != scenario">scenario = #{scenario},</if>
            <if test="null != domain">domain = #{domain},</if>
            <if test="null != gnssMode">gnss_mode = #{gnssMode},</if>
            <if test="null != iotProtocol">iot_protocol = #{iotProtocol},</if>
            <if test="null != targetName">target_name = #{targetName},</if>
            <if test="null != terminalId">terminal_id = #{terminalId},</if>
            <if test="null != channelNum">channel_num = #{channelNum},</if>
            <if test="null != channelIds">channel_ids = #{channelIds}</if>
        </set>
        WHERE id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
        update bdm_wearable_device
        set deleted     = 1,
            update_time = now()
        where id = #{id}
    </update>

    <update id="deleteByIds">
        update bdm_wearable_device
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO bdm_wearable_device (
        "id","unique_id", "imei", "model", "vendor", "bd_chip_sn","dept_id", "category", "installdate","create_time",
        "scenario", "domain", "specificity", "device_num", "gnss_mode","terminal_id"
        ) VALUES
        <foreach collection="list" item="device" separator=",">
            (
            #{device.id},  #{device.uniqueId}, #{device.imei}, #{device.model}, #{device.vendor}, #{device.bdChipSn},
            #{device.deptId}, #{device.category}, #{device.installdate}, now(), #{device.scenario}, #{device.domain},
            #{device.specificity}, #{device.deviceNum}, #{device.gnssMode}, #{device.terminalId}
            )
        </foreach>
    </insert>

    <update id="connectWorkerTerminal">
        update bdm_wearable_device
        set
            target_id = #{id},
            target_type = #{targetType},
            target_name = #{targetName}
        where id = #{request.id}
            and deleted = 0
    </update>

    <select id="select" resultType="com.xh.vdm.bi.vo.response.PersonNoBingResponse">
        SELECT
        w.id,
        w.unique_id,
        w.model,
        w.dept_id,
        w.category,
        w.device_type,
        w.device_num,
        w.iot_protocol,
        d.dept_name
        FROM bdm_wearable_device w
        LEFT JOIN blade_dept d on w.dept_id = d.id
        WHERE w.deleted = 0
        and w.target_id = 0
        and w.iot_protocol != 1
        and w.category = #{request.category}
        <if test="request.uniqueId != null and request.uniqueId != ''">
            AND w.unique_id LIKE concat('%', #{request.uniqueId}, '%')
        </if>
        <if test="request.deptId != null">
            AND w.dept_id = #{request.deptId}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and d.id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and w.create_account = #{account}
        </if>
    </select>

    <select id="selectByWorkId" resultType="com.xh.vdm.bi.vo.response.WorkerBingResponse">
        SELECT w.unique_id,
               w.model,
               w.dept_id,
               w.category,
               w.device_type,
               w.device_num,
               w.iot_protocol,
               d.dept_name
        FROM bdm_wearable_device w
                 LEFT JOIN blade_dept d on w.dept_id = d.id
        where w.target_id = #{id}
          and w.target_type = #{targetType}
        and w.deleted = 0
    </select>

    <update id="updateByWorkerId">
        update bdm_wearable_device
        set target_id = 0 , target_type = 0,target_name = ''
        where target_id = #{id}
          and target_type = #{targetType}
          and deleted = 0
    </update>

    <update id="updateBatch">
        update bdm_wearable_device
        set
        target_id = #{id},
        target_type = #{targetType},
        target_name = #{targetName},
        dept_id = #{deptId}
        where id in
        <if test="ids != null and ids.size() > 0">
            <foreach collection="ids" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectBindByWorkId" resultType="com.xh.vdm.bi.vo.response.PersonNoBingResponse">
        SELECT w.id,
               w.unique_id,
               w.model,
               w.dept_id,
               w.category,
               w.device_type,
               w.device_num,
               w.iot_protocol,
               d.dept_name
        FROM bdm_wearable_device w
        LEFT JOIN blade_dept d on w.dept_id = d.id
        where w.target_id = #{id}
          and w.target_type = #{targetType}
          AND w.deleted = 0
    </select>

    <update id="deleteByTargetIds">
        <if test="ids != null and ids.length > 0">
            update bdm_wearable_device
            set target_id = 0,target_type = 0,target_name = ''
            where target_type =  #{targetType}
            AND target_id in
            <foreach collection="ids" item="id" open="(" separator="," close=")" >
                #{id}
            </foreach>
        </if>
    </update>

    <select id="countByUserRole" resultType="java.lang.Long">
        select count(*) from bdm_wearable_device wd
        WHERE wd.deleted = 0
        <if test="deptIds != null and deptIds != ''">
            and wd.dept_id = any(${deptIds})
        </if>
    </select>

    <update id="updateDept">
        update bdm_wearable_device set dept_id = #{deptId} where target_id = #{id} and target_type = #{targetType}
    </update>

    <update id="bindTarget">
        update bdm_rnss_device
        set target_id = #{targetId}, target_type = #{targetType}, target_name = #{targetName}
        where id = #{deviceId}
    </update>

</mapper>

