<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmDeviceCodeMapper">

    <update id="updateActivatedByDeviceNum">
        update bdm_device_code
        set activated = 0
        where  device_num in
        <if test="deviceNumList != null and deviceNumList.size() > 0">
            <foreach collection="deviceNumList" item="deviceNum" open="(" separator="," close=")">
                #{deviceNum}
            </foreach>
        </if>
    </update>

    <update id="updateActivated">
        update bdm_device_code
        set activated = 1
        where  1=1
        <if test="deviceNum != null and deviceNum != ''">
            and device_num = #{deviceNum}
        </if>
    </update>

    <select id="select" resultType="com.xh.vdm.bi.entity.BdmDeviceCode">
        SELECT c.*,ma.name as manufacturerName
        FROM bdm_device_code c
        LEFT JOIN bdm_device_ledger l ON c.device_num = l.device_num
        left join ${schema}.bdc_manufactor ma on ma.code = c.vendor
        WHERE l.device_num IS NULL and ma.is_del=0
            <if test="request.deviceNum != null and request.deviceNum != ''">
                AND c.device_num LIKE CONCAT('%', #{request.deviceNum}, '%')
            </if>
            <if test="request.deviceSeq != null and request.deviceSeq != ''">
                AND c.serial LIKE CONCAT('%', #{request.deviceSeq}, '%')
            </if>
            <if test="request.deviceModel != null and request.deviceModel != ''">
                AND c.model LIKE CONCAT('%', #{request.deviceModel}, '%')
            </if>
            <if test="request.imei != null and request.imei != ''">
                AND c.imei LIKE CONCAT('%', #{request.imei}, '%')
            </if>
            <if test="request.chipSeq != null and request.chipSeq != ''">
                AND c.bd_chip_serial LIKE CONCAT('%', #{request.chipSeq}, '%')
            </if>
            <if test="request.manufacturer != null and request.manufacturer != ''">
                AND ma.name LIKE CONCAT('%', #{request.manufacturer}, '%')
            </if>
        order by c.create_time desc
    </select>

    <select id="selectDeviceNum" resultType="java.lang.String">
        SELECT c.device_num
        FROM bdm_device_code c
                 LEFT JOIN bdm_device_ledger l ON c.device_num = l.device_num
        WHERE l.device_num IS NULL
    </select>
</mapper>

