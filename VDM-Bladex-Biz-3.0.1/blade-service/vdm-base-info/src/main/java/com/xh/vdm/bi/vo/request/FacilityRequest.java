package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import org.springblade.common.dept.DeptIdAware;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 基础设施查询条件
 */
@Data
@ExcelIgnoreUnannotated
public class FacilityRequest  implements DeptIdAware {

	private Long id;
	//设施名称
	private String name;
	//设施类型
	private Integer category;
	//地址
	private String address;
	//目标类别
	private Integer targetType;
	//所属机构
	private Long deptId;
	@Override
	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	//点串坐标，描述基础设施范围
	private String geometry;

	@NotNull(message = "编号不能为空")
	@ApiModelProperty(value = "编号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "编号")
	@ColumnWidth(22)
	private String code;

	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;

	private Integer current;

	private Integer size;

	/**
	 * 错误信息
	 */
	@ApiModelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "错误信息")
	@ColumnWidth(22)
	private String msg;

	/**	 导出动态表头中文名*/
	private List headNameList;

	/**	 导出动态表头字段名*/
	private List columnNameList;

	/**	 导出数据的id集合*/
	private List<Long> ids;
}

