<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmVisitorMapper">

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryByPage" resultType="com.xh.vdm.bi.vo.response.VisitorResponse">
        SELECT
        v.id,v.name,v.post,v.phone,v.industry,v.dept_id,v.id_number,v.company,to_char(v.valid_from, 'YYYY-MM-DD') as validFrom,
        to_char(v.valid_to, 'YYYY-MM-DD') as validTo,v.create_time, v.target_type,d.dept_name,
        STRING_AGG(CAST(bad.category AS TEXT), ',') AS terminalCategories,
        CASE WHEN
        CURRENT_DATE > DATE(v.valid_to) THEN 2
        WHEN
        CURRENT_DATE BETWEEN DATE(v.valid_from) AND DATE(v.valid_to) THEN 1
        ELSE 0
        END AS status
        FROM bdm_visitor v
        LEFT JOIN blade_dept d ON v.dept_id = d.id
        LEFT JOIN bdm_abstract_device bad ON v.ID = bad.target_id AND bad.deleted = 0 and bad.target_type = v.target_type
        WHERE v.deleted = 0
        <if test="visitor.ids != null and visitor.ids.size() gt 0 ">
            and v.id in
            <foreach collection="visitor.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="visitor.ids == null or visitor.ids == ''">
            <if test="visitor.name != null and visitor.name != ''">
                and v.name like concat('%',#{visitor.name},'%')
            </if>
            <if test="visitor.post != null">
                and v.post = #{visitor.post}
            </if>
            <if test="visitor.industry != null">
                and v.industry = #{visitor.industry}
            </if>
            <if test="visitor.phone != null and visitor.phone != ''">
                and v.phone like concat('%',#{visitor.phone},'%')
            </if>
            <if test="visitor.idNumber != null and visitor.idNumber != ''">
                and v.id_number like concat('%',#{visitor.idNumber},'%')
            </if>
            <if test="visitor.deptId != null">
                and v.dept_id = #{visitor.deptId}
            </if>
            <if test="visitor.terminalType != null">
                and (bad.category = #{visitor.terminalType} or bad.device_type = #{visitor.terminalType})
            </if>
            <if test="visitor.uniqueId != null and visitor.uniqueId != ''">
                and bad.unique_id like concat('%',#{visitor.uniqueId},'%')
            </if>
            <if test="visitor.deviceNum != null and visitor.deviceNum != ''">
                and bad.device_num like concat('%',#{visitor.deviceNum},'%')
            </if>
            <if test="visitor.status == 0">
                and v.valid_from > now()
            </if>
            <if test="visitor.status == 1">
                and now() between v.valid_from and v.valid_to
            </if>
            <if test="visitor.status == 2">
                and now() > v.valid_to
            </if>
            <if test="deptIds != null and deptIds != ''">
                and d.id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and v.create_account = #{account}
            </if>
        </if>
        GROUP BY
        v.id,v.name,v.post,v.phone,v.industry,v.dept_id,v.id_number,v.company,v.valid_from,v.valid_to,v.create_time, v.target_type,d.dept_name
        ORDER BY v.create_time DESC
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO bdm_visitor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id">
                id,
            </if>
            <if test="null != name and '' != name">
                name,
            </if>
            <if test="null != idNumber and '' != idNumber">
                id_number,
            </if>
            <if test="null != targetType">
                target_type,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != post">
                post,
            </if>
            <if test="null != industry">
                industry,
            </if>
            <if test="null != phone and '' != phone">
                phone,
            </if>
            <if test="null != company and '' != company">
                company,
            </if>
            <if test="null != validFrom">
                valid_from,
            </if>
            <if test="null != validTo">
                valid_to,
            </if>
            <if test="null != createAccount">
                create_account
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id">
                #{id},
            </if>
            <if test="null != name and '' != name">
                #{name},
            </if>
            <if test="null != idNumber and '' != idNumber">
                #{idNumber},
            </if>
            <if test="null != targetType">
                #{targetType},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != post">
                #{post},
            </if>
            <if test="null != industry">
                #{industry},
            </if>
            <if test="null != phone and '' != phone">
                #{phone},
            </if>
            <if test="null != company and '' != company">
                #{company},
            </if>
            <if test="null != validFrom">
                #{validFrom},
            </if>
            <if test="null != validTo">
                #{validTo},
            </if>
            <if test="null != createAccount">
                #{createAccount,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <select id="selectIdNumberByPattern" resultType="java.lang.String">
        select id_number
        from bdm_visitor
        where id_number like concat('%', #{pattern}, '%')
        order by id desc
        limit 1
    </select>

    <update id="batchUpdate">
        UPDATE bdm_visitor
        <set>
            dept_id = #{deptId}
        </set>
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bdm_visitor(id,name, id_number, dept_id, post, industry, phone, company,valid_from, valid_to)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.name}, #{entity.idNumber}, #{entity.deptId}, #{entity.post},
            #{entity.industry}, #{entity.phone}, #{entity.company}, #{entity.validFrom},#{entity.validTo})
        </foreach>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bdm_visitor
        <set>
            <if test="name != null">
                name = #{name},
            </if>
            <if test="idNumber != null">
                id_number = #{idNumber},
            </if>
            <if test="targetType != null">
                target_type = #{targetType},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="post != null">
                post = #{post},
            </if>
            <if test="industry != null">
                industry = #{industry},
            </if>
            <if test="phone != null">
                phone = #{phone},
            </if>
            <if test="company != null">
                company = #{company},
            </if>
            <if test="validFrom != null">
                valid_from = #{validFrom},
            </if>
            <if test="validTo != null">
                valid_to = #{validTo},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted}
            </if>
        </set>
        where id = #{id}
    </update>

    <update id="deleteByIds">
        update bdm_visitor
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

</mapper>

