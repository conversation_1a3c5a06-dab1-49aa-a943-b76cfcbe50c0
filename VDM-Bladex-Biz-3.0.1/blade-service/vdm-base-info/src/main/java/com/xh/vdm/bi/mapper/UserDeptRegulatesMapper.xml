<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.UserDeptRegulatesMapper">


    <select id="query" resultType="com.xh.vdm.bi.vo.response.UserDeptRegulatesResponse">
        select udr.dept_id, d.dept_name
        from bdm_user_dept_regulates as udr
                 left join blade_dept as d on udr.dept_id = d.id
        where udr.user_id = #{userId}
    </select>
</mapper>
