package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.io.Serializable;
import java.util.List;

/**
 * (BdmTemporary)实体类
 */
@Data
@ExcelIgnoreUnannotated
public class BdmTemporaryRequest implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	@ExcelProperty(value = "人员姓名")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(12)
	@Compare("人员姓名")
	private String name;

	@NotNull(message = "工号/身份证号不能为空")
	@ExcelProperty(value = "工号/身份证号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(12)
	@Compare("工号/身份证号")
	private String wkno;

	private Integer targetType;
	@Compare("所属机构")
	private Long deptId;
	@Compare("从业类型")
	private Integer post;
	@Compare("岗位类型")
	private Integer industry;

	@Compare("联系电话")
	private String phone;

	private Long companyId;
	@Compare("所属公司名称")
	private String company;
	@JsonFormat(pattern = "yyyy-MM-dd")
	@Compare("有效日期")
	private Date validFrom;
	@JsonFormat(pattern = "yyyy-MM-dd")
	@Compare("终止日期")
	private Date validTo;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;
	/**
	 * 绑定终端类型
	 */
	@TableField(exist = false)
	private Integer terminalType;
	/**
	 * 绑定序列号
	 */
	@TableField(exist = false)
	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;
	/**
	 * 账号状态
	 */
	@TableField(exist = false)
	private Integer status;

	/**
	 * 错误信息
	 */
	@TableField(exist = false)
	@ExcelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(12)
	private String msg;

	/**	 导出动态表头中文名*/
	@TableField(exist = false)
	private List headNameList;

	/**	 导出动态表头字段名*/
	@TableField(exist = false)
	private List columnNameList;

	/**	 导出数据的id集合*/
	@TableField(exist = false)
	private List<Long> ids;

	private String createAccount;
}

