package com.xh.vdm.bi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 终端类别
 */
@AllArgsConstructor
@Getter
public enum DeviceTypeEnum {
    /**
     * 北斗定位终端
     */
    RNSS(1, "北斗定位终端"),
    /**
     * 北斗穿戴式终端
     */
    WEARABLE(2, "北斗穿戴式终端"),
    /**
     * 北斗短报文终端
     */
    RDSS(3, "北斗短报文终端"),
    /**
     * 北斗监测终端
     */
    MONIT(4, "北斗监测终端"),
    /**
     * 短报文终端
     */
    PNT(5, "北斗授时终端");

    /**
     * 标识
     */
    private final Integer symbol;
    /**
     * 状态
     */
    private final String source;
}
