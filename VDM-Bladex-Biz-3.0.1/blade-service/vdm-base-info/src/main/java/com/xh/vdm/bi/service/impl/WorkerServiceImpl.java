package com.xh.vdm.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.converter.BdmAbstractTargetConverter;
import com.xh.vdm.bi.enums.DeviceTypeEnum;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.mapper.WorkerMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.*;
import com.xh.vdm.bi.vo.response.ConnectResponse;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.WorkerResponse;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import com.xh.vdm.biapi.entity.BdmDeviceStatus;
import com.xh.vdm.biapi.entity.BdmWorker;
import com.xh.vdm.interManager.feign.IMessageClient;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (BdmWorker)表服务实现类
 */
@Service
@Slf4j
public class WorkerServiceImpl extends ServiceImpl<WorkerMapper, BdmWorker> implements WorkerService {
	@Resource
	private WorkerMapper workerMapper;
	@Resource
	private WearableDeviceService wearableDeviceService;
	@Resource
	private RdssDeviceService rdssDeviceService;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private MonitDeviceService monitDeviceService;
	@Resource
	private PntDeviceService pntDeviceService;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private IMessageClient messageClient;
	@Resource
	private IBdmDeviceStatusService deviceStatusService;
	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private IBdmVirtualTargetService virtualTargetService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
     * 分页查询
     *
     * @param workerRequest 筛选条件
     * @param ceDataAuth
     * @return
     */
	@Override
	public IPage<WorkerResponse> queryByPage(WorkerRequest workerRequest, DataAuthCE ceDataAuth) {
		Page page = new Page<>();
		page.setCurrent(workerRequest.getCurrent());
		page.setSize(workerRequest.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.workerMapper.queryAll(page, workerRequest, response.getAccount(), response.getOrgList());
	}

	/**
	 * 新增数据
	 *
	 * @param workerRequest 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmWorker insert(WorkerRequest workerRequest) {
		BdmWorker workerInDB = this.getBaseMapper().selectOne(new QueryWrapper<BdmWorker>().eq("wkno", workerRequest.getWkno()));
		BdmWorker bdmWorker = new BdmWorker();
		if (workerInDB != null) {
			if (workerInDB.getDeleted() == 0) {
				throw new RuntimeException("工卡号已存在: " + workerRequest.getWkno());
			} else {
				BeanUtils.copyProperties(workerRequest, bdmWorker, "id");
				bdmWorker.setUpdateTime(new Date());
				bdmWorker.setDeleted(0);
				bdmWorker.setId(workerInDB.getId());
				bdmWorker.setTargetType(TargetTypeEnum.WORKER.getSymbol());
				this.baseMapper.updateById(bdmWorker);

				//新增目标同步到bdm_abstract_target
				BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
				BeanUtils.copyProperties(bdmWorker, abstractTarget);
				abstractTarget.setNumber(bdmWorker.getWkno());
				abstractTargetService.updateById(abstractTarget);
			}
		} else {
			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.WORKER_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			BeanUtils.copyProperties(workerRequest, bdmWorker);
			bdmWorker.setId(targetId.nextId());
			bdmWorker.setTargetType(TargetTypeEnum.WORKER.getSymbol());
			bdmWorker.setCreateTime(new Date());
			bdmWorker.setUpdateTime(new Date());
			bdmWorker.setCreateAccount(AuthUtil.getUserAccount());
			this.workerMapper.insertWorker(bdmWorker);

			//新增目标同步到bdm_abstract_target
			BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
			BeanUtils.copyProperties(bdmWorker, abstractTarget);
			abstractTarget.setNumber(bdmWorker.getWkno());
			abstractTarget.setCreateAccount(AuthUtil.getUserAccount());
			abstractTargetService.save(abstractTarget);
		}

		BdmWorker worker = this.getBaseMapper().selectById(bdmWorker.getId());

		Map<String, String> map = new HashMap<>();
		String key = BaseInfoConstants.WORKER_TARGET_TYPE + "-" + worker.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", worker.getName() + "(" + worker.getWkno() + ")");
		innerMap.put("targetType", worker.getTargetType());
		innerMap.put("deptId", worker.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setTargetId(worker.getId());
		deviceInfo.setTargetName(worker.getName());
		deviceInfo.setTargetType(worker.getTargetType());
		deviceInfo.setDeptId(worker.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("人员信息更新消息发送到kafka失败", e);
		}

		//messageClient
		try {
			messageClient.staff(CommonConstant.OPER_TYPE_ADD, worker);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return worker;
	}

	/**
	 * 修改数据
	 *
	 * @param bdmWorker 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmWorker update(BdmWorker bdmWorker) {
		BdmWorker workerInDB = baseMapper.selectById(bdmWorker.getId());

		bdmWorker.setId(bdmWorker.getId());
		bdmWorker.setName(bdmWorker.getName() != null ? bdmWorker.getName() : "");
		bdmWorker.setTargetType(bdmWorker.getTargetType() != null ? bdmWorker.getTargetType() : TargetTypeEnum.WORKER.getSymbol());
		bdmWorker.setDeptId(bdmWorker.getDeptId() != null ? bdmWorker.getDeptId() : 0);
		bdmWorker.setPost(bdmWorker.getPost() != null ? bdmWorker.getPost() : 0);
		bdmWorker.setIndustry(bdmWorker.getIndustry() != null ? bdmWorker.getIndustry() : 0);
		bdmWorker.setPhone(bdmWorker.getPhone() != null ? bdmWorker.getPhone() : "");
		bdmWorker.setUpdateTime(new Date());
		bdmWorker.setWkno(bdmWorker.getWkno() != null ? bdmWorker.getWkno() : "");
		this.workerMapper.update(bdmWorker);

		BdmWorker worker = this.getBaseMapper().selectById(bdmWorker.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(worker, abstractTarget);
		abstractTarget.setNumber(worker.getWkno());
		abstractTargetService.updateById(abstractTarget);

		String key = BaseInfoConstants.WORKER_TARGET_TYPE + "-" + worker.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", worker.getName() + "(" + worker.getWkno() + ")");
		innerMap.put("targetType", worker.getTargetType());
		innerMap.put("deptId", worker.getDeptId());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setTargetId(worker.getId());
		deviceInfo.setTargetName(worker.getName());
		deviceInfo.setTargetType(worker.getTargetType());
		deviceInfo.setDeptId(worker.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("人员信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.staff(CommonConstant.OPER_TYPE_UPDATE, worker);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		//@zhouxw modify 2025-06-27 修改监管对象，终端不再同步更改组织
		// 对于已绑定的设备进行部门更新操作
		/*if (!workerInDB.getDeptId().equals(worker.getDeptId())) {
			// 对于WearableDevice的更新
			wearableDeviceService.updateDept(worker.getId(), worker.getTargetType(), worker.getDeptId());

			// 对于RdssDevice的更新
			rdssDeviceService.updateDept(worker.getId(), worker.getTargetType(), worker.getDeptId());

			// 对于RnssDevice的更新
			rnssDeviceService.updateDept(worker.getId(), worker.getTargetType(), worker.getDeptId());

			// 对于BdmAbstractDevice的更新
			abstractDeviceService.updateDept(worker.getId(), worker.getTargetType(), worker.getDeptId());
		}*/

		return worker;
	}

	/**
	 * 通过主键删除数据
	 *
	 * @param ids 主键
	 * @return 是否成功
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {
		List<BdmWorker> workers = baseMapper.selectList(new QueryWrapper<BdmWorker>().in("id", ids));
		boolean result = this.workerMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.WORKER_TARGET_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + key);
			}
			this.wearableDeviceService.deleteByTargetIds(ids, TargetTypeEnum.WORKER.getSymbol());
			this.rdssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.WORKER.getSymbol());
			this.rnssDeviceService.deleteByTargetIds(ids, TargetTypeEnum.WORKER.getSymbol());

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.DELETE);
			Long lastId = ids[ids.length - 1];
			deviceInfo.setTargetId(lastId);
			deviceInfo.setTargetIds(CollUtil.newHashSet(ids));
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("人员信息更新消息发送到kafka失败", e);
			}
			//messageClient
			try {
				messageClient.staffBatch(CommonConstant.OPER_TYPE_DELETE, workers);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
			//更新bdm_abstract_target
			abstractTargetService.deleteByIds(ids);
			//更新bdm_abstract_device
			abstractDeviceService.deleteByTargetIds(ids, TargetTypeEnum.WORKER.getSymbol());
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ConnectResponse connectTerminal(List<PersonTerminalRequest> list, Long id, Long deptId) {
		ConnectResponse res=new ConnectResponse();
		long rnssNum=list.stream().filter(val->val.getDeviceType().equals(DeviceTypeEnum.RNSS.getSymbol())).count();
		if (rnssNum>1){
			res.setCode(1);
			res.setMsg("人员最多只能绑定一个808终端");
			return res;
		}

		BdmWorker worker = this.workerMapper.selectById(id);
		String targetName = worker.getName() + "(" + worker.getWkno() + ")";

		//查询出已绑定的终端设备
		List<BdmAbstractDevice> abstractDevices = abstractDeviceService.getBaseMapper()
			.selectList(new QueryWrapper<BdmAbstractDevice>()
				.eq("target_id", id)
				.eq("target_type", TargetTypeEnum.WORKER.getSymbol())
			);

		List<String> uniqueIdList = new ArrayList<>();
		if(!abstractDevices.isEmpty()){
			//bdm_device_status更新
			for (BdmAbstractDevice request:abstractDevices) {
				QueryWrapper<BdmDeviceStatus> wrapper = new QueryWrapper<>();
				wrapper.eq("device_id", request.getId());
				wrapper.eq("device_type", request.getDeviceType());
				wrapper.eq("target_id", request.getTargetId());
				wrapper.eq("target_type", request.getTargetType());
				deviceStatusService.remove(wrapper);
				uniqueIdList.add(request.getUniqueId());

				BdmAbstractTarget target =abstractTargetService.getOne(new QueryWrapper<BdmAbstractTarget>().eq("number", request.getUniqueId()).eq("deleted", 0));
				if (target==null){
					res.setCode(2);
					res.setMsg("未查询到与该目标绑定的原抽象终端的待分配终端目标");
					return res;
				}

				if (request.getDeviceType().equals(DeviceTypeEnum.WEARABLE.getSymbol())){
					wearableDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.RNSS.getSymbol())){
					rnssDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.RDSS.getSymbol())){
					rdssDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.MONIT.getSymbol())){
					monitDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				if (request.getDeviceType().equals(DeviceTypeEnum.PNT.getSymbol())){
					pntDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
				}
				abstractDeviceService.bindTarget(request.getId(),target.getId(),target.getTargetType(),target.getName());
			}
			// bdm_virtual_target数据 对abstractDevices 进行恢复，(叶工加的，看不懂先留着)
			// 将deleted恢复置零，在待分配终端可以看到
			virtualTargetService.updateBatch(uniqueIdList);
		}
		if (list.isEmpty()) {
			//根据用户ID去清空原来绑定的终端数据
			//wearableDeviceService.updateByWorkerId(id, TargetTypeEnum.WORKER.getSymbol());
			//rdssDeviceService.updateBatchByWorkerId(id, TargetTypeEnum.WORKER.getSymbol());
			//rnssDeviceService.updateByWorkerId(id, TargetTypeEnum.WORKER.getSymbol());
			//monitDeviceService.updateByDeviceId(id,TargetTypeEnum.WORKER.getSymbol());
			//pntDeviceService.updateByDeviceId(id,TargetTypeEnum.WORKER.getSymbol());
			//bdm_abstract_device更新绑定关系
			//abstractDeviceService.unbinding(id, TargetTypeEnum.WORKER.getSymbol());
		} else {
			//PersonTerminalRequest req=list.get(0);
			for (PersonTerminalRequest req:list){
				//后台需要判断设备是否被其他对象绑定，若已被绑定则提示操作失败
				BdmAbstractDevice bad=abstractDeviceService.getBadByUniqueId(req.getUniqueId());
				if (bad==null) {
					res.setCode(2);
					res.setMsg("设备序列号不存在");
					return res;
				}
				if (bad.getTargetId()>0&&bad.getTargetType()>0&&!bad.getTargetId().equals(id)){
					res.setCode(3);
					BdmAbstractTarget bat=abstractTargetService.getById(bad.getTargetId());
					String msg="设备已被其他对象绑定："+bat.getName()+"("+bat.getNumber()+")";
					res.setMsg(msg);
					return res;
				}

				if (DeviceTypeEnum.RDSS.getSymbol().equals(req.getDeviceType())) {
					rdssDeviceService.updateBatchByWorkerId(id, TargetTypeEnum.WORKER.getSymbol());
					rdssDeviceService.updateBatch(list, id, TargetTypeEnum.WORKER.getSymbol(), targetName, deptId);
				}
				if (DeviceTypeEnum.WEARABLE.getSymbol().equals(req.getDeviceType())) {
					wearableDeviceService.updateByWorkerId(id, TargetTypeEnum.WORKER.getSymbol());
					wearableDeviceService.updateBatchByTerminalId(list, id, TargetTypeEnum.WORKER.getSymbol(), targetName, deptId);
				}
				if (DeviceTypeEnum.RNSS.getSymbol().equals(req.getDeviceType())) {
					rnssDeviceService.updateByWorkerId(id, TargetTypeEnum.WORKER.getSymbol());
					rnssDeviceService.connectWorkerTerminal(list, id, TargetTypeEnum.WORKER.getSymbol(), targetName, deptId);
				}
				if (DeviceTypeEnum.MONIT.getSymbol().equals(req.getDeviceType())) {
					monitDeviceService.updateByDeviceId(id,TargetTypeEnum.WORKER.getSymbol());
					List<FacilityTerminalRequest> pnts =new ArrayList<>();
					FacilityTerminalRequest pnt=new FacilityTerminalRequest();
					pnt.setId(req.getId());
					pnts.add(pnt);
					monitDeviceService.updateBatchByTerminalId(pnts, id, TargetTypeEnum.WORKER.getSymbol(), deptId);
				}
				if (DeviceTypeEnum.PNT.getSymbol().equals(req.getDeviceType())) {
					pntDeviceService.updateByDeviceId(id,TargetTypeEnum.WORKER.getSymbol());
					List<FacilityTerminalRequest> pnts =new ArrayList<>();
					FacilityTerminalRequest pnt=new FacilityTerminalRequest();
					pnt.setId(req.getId());
					pnts.add(pnt);
					pntDeviceService.updateBatchByTerminalId(pnts, id, TargetTypeEnum.WORKER.getSymbol(), deptId);
				}
			}

			//bdm_abstract_device更新绑定关系
			abstractDeviceService.unbinding(id, TargetTypeEnum.WORKER.getSymbol());
			abstractDeviceService.bind(list, id, TargetTypeEnum.WORKER.getSymbol(), targetName, deptId);

			List<String> uniqueIds = list.stream()
					.map(PersonTerminalRequest::getUniqueId)
					.collect(Collectors.toList());
			if(!uniqueIds.isEmpty()){
				virtualTargetService.updateByUniqueId(uniqueIds);
			}
		}

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.BIND);
		deviceInfo.setTargetId(worker.getId());
		deviceInfo.setTargetName(worker.getName());
		deviceInfo.setTargetType(worker.getTargetType());
		deviceInfo.setDeptId(worker.getDeptId());
		Set<Long> ids = abstractDevices.stream().map(BdmAbstractDevice::getId).collect(Collectors.toSet());
		Set<Long> idsList = list.stream().map(PersonTerminalRequest::getId).collect(Collectors.toSet());
		ids.addAll(idsList);
		deviceInfo.setDeviceIds(ids);

		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("人员信息更新消息发送到kafka失败", e);
		}
		res.setCode(0);
		return res;
	}

	@Override
	public IPage<PersonNoBingResponse> select(DeviceNoBindRequest deviceNoBindRequest, DataAuthCE ceDataAuth) {
		IPage<PersonNoBingResponse> responseIPage = new Page<>();
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		// bdm_abstract_device 数据进行分页查询
		responseIPage = abstractDeviceService.select(deviceNoBindRequest, response.getAccount(), response.getOrgList());
		return responseIPage;
	}

	@Override
	public List<PersonNoBingResponse> selectBind(DeviceNoBindRequest deviceNoBindRequest, Long id) {
		List<PersonNoBingResponse> resultList = abstractDeviceService.selectBind(id, TargetTypeEnum.WORKER.getSymbol());
		return resultList;
	}

	@Override
	public List<PersonNoBingResponse> terminalInfo(Long id) {
		List<PersonNoBingResponse> resultList = abstractDeviceService.selectBind(id, TargetTypeEnum.WORKER.getSymbol());
		return resultList;
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<WorkerRequest> importExcel(List<WorkerRequest> list) {
		//错误的的数据
		List<WorkerRequest> duplicateWorkerRequests = getDuplicateWorkerRequests(list);
		// 创建原始列表的副本以保留非重复的数据
		List<WorkerRequest> requests = new ArrayList<>(list);
		// 移除确定为重复的 WorkerRequest 对象
		requests.removeAll(duplicateWorkerRequests);

		if (!requests.isEmpty()) {
			List<String> wknoList = requests.stream()
				.map(WorkerRequest::getWkno)
				.collect(Collectors.toList());
			//数据库的数据
			QueryWrapper<BdmWorker> queryWrapper = new QueryWrapper<>();
			if (!wknoList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(queryWrapper, "wkno", wknoList);
			}
			Map<String, BdmWorker> workerMap = this.workerMapper.selectList(queryWrapper)
				.stream()
				.collect(Collectors.toMap(BdmWorker::getWkno, BdmWorker -> BdmWorker));

			Map<String, String> map = new HashMap<>();
			List<BdmWorker> workerList = new ArrayList<>();
			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.WORKER_TARGET_TYPE, BaseInfoConstants.ZERO, BaseInfoConstants.ZERO);
			List<Long> idList = new ArrayList<>();
			List<BdmAbstractTarget> bdmAbstractTargets = new ArrayList<>();

			for (WorkerRequest request : requests) {
				BdmWorker bdmWorker = workerMap.get(request.getWkno());

				if (bdmWorker != null) {
					if (bdmWorker.getDeleted() == 1) {
						BeanUtils.copyProperties(request, bdmWorker, "id");
						bdmWorker.setCreateTime(new Date());
						bdmWorker.setDeleted(0);
						bdmWorker.setTargetType(TargetTypeEnum.WORKER.getSymbol());
						this.workerMapper.updateById(bdmWorker);

						idList.add(bdmWorker.getId());
						request.setId(bdmWorker.getId());

						String key = BaseInfoConstants.WORKER_TARGET_TYPE + "-" + bdmWorker.getId();
						Map<String, Object> innerMap = new HashMap<>();
						innerMap.put("targetName", bdmWorker.getName() + "(" + bdmWorker.getWkno() + ")");
						innerMap.put("targetType", bdmWorker.getTargetType());
						innerMap.put("deptId", bdmWorker.getDeptId());
						try {
							map.put(key, new ObjectMapper().writeValueAsString(innerMap));
						} catch (JsonProcessingException e) {
							e.printStackTrace();
						}

						//新增目标同步到bdm_abstract_target
						BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
						BeanUtils.copyProperties(bdmWorker, abstractTarget);
						abstractTarget.setNumber(bdmWorker.getWkno());
						abstractTarget.setCategory(0);
						abstractTargetService.updateById(abstractTarget);
					} else {
						request.setMsg("工卡号已存在");
						duplicateWorkerRequests.add(request);
					}
				} else {
					BdmWorker worker = new BdmWorker();
					worker.setId(targetId.nextId());
					worker.setName(request.getName() != null ? request.getName() : "");
					worker.setTargetType(TargetTypeEnum.WORKER.getSymbol());
					worker.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
					worker.setPost(request.getPost() != null ? request.getPost() : 0);
					worker.setIndustry(request.getIndustry() != null ? request.getIndustry() : 0);
					worker.setPhone(request.getPhone() != null ? request.getPhone() : "");
					worker.setCreateTime(new Date());
					worker.setDeleted(0);
					worker.setWkno(request.getWkno() != null ? request.getWkno() : "");
					workerList.add(worker);

					request.setId(worker.getId());
					idList.add(worker.getId());

					String key = BaseInfoConstants.WORKER_TARGET_TYPE + "-" + worker.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", worker.getName() + "(" + worker.getWkno() + ")");
					innerMap.put("targetType", worker.getTargetType());
					innerMap.put("deptId", worker.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}

					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(worker, abstractTarget);
					abstractTarget.setNumber(worker.getWkno());
					abstractTarget.setCategory(0);
					bdmAbstractTargets.add(abstractTarget);
				}
			}

			if (!workerList.isEmpty()) {
				//将剩余的数据批量插入数据库
				MapperUtils.splitListByCapacity(workerList, MapperUtils.DEFAULT_CAPACITY)
					.forEach(workerRequestList -> this.workerMapper.insertBatch(workerRequestList));

				if (!map.isEmpty()) {
					redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
				}

				MapperUtils.splitListByCapacity(bdmAbstractTargets, MapperUtils.DEFAULT_CAPACITY)
					.forEach(abstractTargetList -> this.abstractTargetService.insertBatch(abstractTargetList));

				BdmWorker lastWorker = workerList.get(workerList.size() - 1);
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.IMPORT);
				deviceInfo.setTargetId(lastWorker.getId());
				deviceInfo.setTargetName(lastWorker.getName());
				deviceInfo.setDeptId(lastWorker.getDeptId());
				Set<Long> ids = workerList.stream().map(BdmWorker::getId).collect(Collectors.toSet());
				deviceInfo.setTargetIds(ids);
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("人员信息更新消息发送到kafka失败", e);
				}
				QueryWrapper<BdmWorker> wrapper = new QueryWrapper<>();
				if (!idList.isEmpty()) {
					MybatisPlusWrapperUitls.longListIn(wrapper, "id", idList);
					List<BdmWorker> workers = baseMapper.selectList(wrapper);
					//messageClient
					try {
						messageClient.staffBatch(CommonConstant.OPER_TYPE_ADD, workers);
					} catch (Exception e) {
						log.error("消息发送到messageClient失败：" + e.getMessage());
					}
				}
			}
		}
		return duplicateWorkerRequests;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest) {
		int update = workerMapper.batchUpdate(batchUpdateRequest.getIds(), batchUpdateRequest.getDeptId());
		if (update == 0) {
			return false;
		}
		//查询出所有的本次要更新的字段
		List<BdmWorker> bdmWorkers = workerMapper.selectList(new LambdaQueryWrapper<BdmWorker>().in(BdmWorker::getId, batchUpdateRequest.getIds()));
		//转换
		List<BdmAbstractTarget> bdmAbstractTargetList = bdmWorkers.stream().map(BdmAbstractTargetConverter::toBdmWorker).collect(Collectors.toList());
		//更新主表
		abstractTargetService.saveOrUpdateBatch(bdmAbstractTargetList);
		//批量更新redis和kafka
		batchProcess(bdmWorkers);
		if (update > 0){
			return true;
		}
		return false;
	}

	/**
	 * 批量更新redis
	 * 批量发送kafka
	 * @param bdmWorkers
	 */
	private void batchProcess(List<BdmWorker> bdmWorkers){
		for (BdmWorker worker : bdmWorkers) {
			String key = BaseInfoConstants.WORKER_TARGET_TYPE + "-" + worker.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("targetName", worker.getName() + "(" + worker.getWkno() + ")");
			innerMap.put("targetType", worker.getTargetType());
			innerMap.put("deptId", worker.getDeptId());
			Map<String, String> map = new HashMap<>();
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.OTHER);
			deviceInfo.setTargetId(worker.getId());
			deviceInfo.setTargetName(worker.getName());
			deviceInfo.setTargetType(worker.getTargetType());
			deviceInfo.setDeptId(worker.getDeptId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_worker", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("人员信息更新消息发送到kafka失败", e);
			}
			//messageClient
			try {
				messageClient.staff(CommonConstant.OPER_TYPE_UPDATE, worker);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

	/**
	 * 重复数据
	 *
	 * @param list
	 * @return
	 */
	public List<WorkerRequest> getDuplicateWorkerRequests(List<WorkerRequest> list) {
		Map<String, Long> wknoCountMap = list.stream()
			.map(WorkerRequest::getWkno)
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<String> duplicateWknos = wknoCountMap.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		return list.stream()
			.filter(workerRequest -> duplicateWknos.contains(workerRequest.getWkno()))
			.peek(workerRequest -> workerRequest.setMsg("工卡号重复"))
			.collect(Collectors.toList());
	}
}
