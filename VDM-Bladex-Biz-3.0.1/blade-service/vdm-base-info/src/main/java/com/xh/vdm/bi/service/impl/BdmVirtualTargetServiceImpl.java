package com.xh.vdm.bi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.mapper.BdmVirtualTargetMapper;
import com.xh.vdm.bi.service.IBdmAbstractDeviceService;
import com.xh.vdm.bi.service.IBdmVirtualTargetService;
import com.xh.vdm.biapi.entity.BdmVirtualTarget;
import org.apache.ibatis.annotations.Param;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.mp.support.Query;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * (BdmVirtualTarget)表服务实现类
 */
@Service
public class BdmVirtualTargetServiceImpl extends ServiceImpl<BdmVirtualTargetMapper, BdmVirtualTarget> implements IBdmVirtualTargetService {
	@Resource
	private BdmVirtualTargetMapper bdmVirtualTargetMapper;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
	 * 分页查询
	 *
	 * @param bdmVirtualTarget 筛选条件
	 * @param query            分页对象
	 * @param ceDataAuth
	 * @return 查询结果
	 */
	@Override
	public IPage<BdmVirtualTarget> queryByPage(BdmVirtualTarget bdmVirtualTarget, Query query, DataAuthCE ceDataAuth) {
		IPage page = new Page(query.getCurrent(),query.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.bdmVirtualTargetMapper.queryByPage(bdmVirtualTarget, page, response.getAccount(), response.getOrgList());
	}

	@Override
	public void insertBatch(List<BdmVirtualTarget> virtualTargetList) {
		baseMapper.insertBatch(virtualTargetList);
	}

    @Override
    public void updateByUniqueId(List<String> uniqueIds) {
        baseMapper.updateByUniqueId(uniqueIds);
    }

	@Override
	public void updateStatus(String uniqueId) {
		baseMapper.updateStatus(uniqueId);
	}

	@Override
	public void restoreStatus(Long targetId, Integer deleted) {
		baseMapper.restoreStatus(targetId,deleted);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateBatch(List<String> uniqueIdList) {
		baseMapper.updateBatch(uniqueIdList);
//		// TODO 设备绑定待分配
//		QueryWrapper<BdmVirtualTarget> queryWrapper = new QueryWrapper<>();
//		if (!uniqueIdList.isEmpty()) {
//			MybatisPlusWrapperUitls.longListIn(queryWrapper, "number", uniqueIdList);
//		}
//		List<BdmVirtualTarget> virtualTargets = baseMapper.selectList(queryWrapper);
//		if(!virtualTargets.isEmpty()){
//			for (BdmVirtualTarget virtualTarget : virtualTargets) {
//				bdmVirtualTargetMapper.updateBatchByUniqueId(virtualTarget);
//
//				abstractDeviceService.updateBatchByUniqueId(virtualTarget);
//			}
//		}
	}
}
