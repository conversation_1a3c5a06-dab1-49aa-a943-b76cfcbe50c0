package com.xh.vdm.bi.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.vo.response.DeviceRedisResponse;
import com.xh.vdm.bi.vo.response.TargetRedisResponse;
import com.xh.vdm.biapi.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 设备信息缓存初始化
 * 查询数据库的时候，分页查询，然后把分页的数据放到list里边；然后遍历list，只创建一个map对象，存list的数据，然后存到redis里边。重复这个过程
 */
@Component
@Slf4j
@RequestMapping("/terminalCache")
public class TerminalRedisController {
	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	@Resource
	private WorkerService workerService;
	@Resource
	private FacilityService facilityService;
	@Resource
	private BdmVisitorService bdmVisitorService;
	@Resource
	private IBdmVehicleService bdmVehicleService;
	@Resource
	private BdmTemporaryService bdmTemporaryService;
	@Resource
	private BdmContainerService containerService;
	@Resource
	private ITerminalRedisService terminalRedisService;
	@Resource
	private ObjectMapper objectMapper;

	private final Integer MAX_SIZE = 5000;

	@GetMapping("/refreshDeviceCache")
	public R<String> refreshDeviceCache(){
		try{
			this.initDevice();
			return R.success("刷新设备缓存成功");
		}catch (Exception e){
			log.error("刷新设备缓存失败",e);
			return R.fail("刷新设备缓存失败");
		}
	}

	/**
	 * baseinfo-device 缓存初始化
	 * 分页加载数据到redis中
	 */
	@PostConstruct
	public void initDevice() throws JsonProcessingException {
		redisTemplate.delete(BaseInfoConstants.BASEINFO_DEVICE);
		/**
		 说明：使用分页查询之后组装map，分批写入redis的方法不可行。分页查询之后，组装map，这样的多个map中包含重复数据，最终导致redis中的数据缺少
		 这里采用全部查询，然后分隔map，分批次写入redis的方法。
		 注意：不可以一次性写入redis太多数据，会导致其他连接redis的处理卡顿。这里共有20W数据，一次性写入redis耗时2.8秒，时间太长。
		 */
		Map<String,String> map = new HashMap<>();
		List<DeviceRedisResponse> list = terminalRedisService.selectDeviceAll();
		log.info("list的长度为： " + list.size());
		for (DeviceRedisResponse device : list) {
			String key = device.getDeviceType() + "-" + device.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("uniqueId", device.getUniqueId());
			innerMap.put("deviceNum", device.getDeviceNum());
			innerMap.put("category", device.getCategory());
			innerMap.put("deviceType", device.getDeviceType());
			innerMap.put("deptId", device.getDeptId());
			innerMap.put("iotProtocol", device.getIotProtocol());
			map.put(key, JSON.toJSONString(innerMap));
		}

		log.info("查询到的设备数量为 {}，将要写入缓存", map.size());

		int i = 1;
		Map<String,String> tmpMap = new HashMap<>();
		long startF = System.currentTimeMillis();
		for(String key : map.keySet()){
			if(i % MAX_SIZE == 0){
				long start = System.currentTimeMillis();
				redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, tmpMap);
				long end = System.currentTimeMillis();
				log.info("本批次写入设备缓存耗时：" + (end - start));
				tmpMap.clear();
			}
			tmpMap.put(key, map.get(key));
			i++;
		}
		//最后一个批次
		if(tmpMap != null && tmpMap.size() > 0){
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, tmpMap);
		}
		long endF = System.currentTimeMillis();
		log.info("设备缓存写入redis完成，共耗时：" + (endF - startF));
		tmpMap.clear();
		map.clear();
		tmpMap = null;
		map = null;
		System.gc();
	}

	/**
	 * baseinfo-target 缓存初始化
	 */
	@PostConstruct
	public void initTarget() throws JsonProcessingException {
		// TODO
		Map<String, String> map = new HashMap<>();
		List<TargetRedisResponse> list = new ArrayList<>();

		redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET);

		List<TargetRedisResponse> workerList = workerService.getBaseMapper().selectList(
				new QueryWrapper<BdmWorker>()
					.select("id", "name", "target_type", "wkno", "dept_id")
					.eq("deleted", 0)
			).stream()
			.map(bdmWorker -> {
				TargetRedisResponse response = new TargetRedisResponse();
				response.setId(bdmWorker.getId());
				response.setName(bdmWorker.getName() + "(" + bdmWorker.getWkno() + ")");
				response.setTargetType(bdmWorker.getTargetType());
				response.setDeptId(bdmWorker.getDeptId());
				return response;
			})
			.collect(Collectors.toList());

		if (!workerList.isEmpty()) {
			for (TargetRedisResponse device : workerList) {
				String key = device.getTargetType() + "-" + device.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", device.getName());
				innerMap.put("targetType", device.getTargetType());
				innerMap.put("targetCategory", device.getCategory());
				innerMap.put("deptId", device.getDeptId());
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			}
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
		}

		List<TargetRedisResponse> facilityList = facilityService.getBaseMapper().selectList(
				new QueryWrapper<BdmFacility>()
					.select("id", "name", "target_type", "category", "dept_id")
					.eq("deleted", 0)
			).stream()
			.map(bdmFacility -> {
				TargetRedisResponse response = new TargetRedisResponse();
				response.setId(bdmFacility.getId());
				response.setName(bdmFacility.getName());
				response.setTargetType(bdmFacility.getTargetType());
				response.setCategory(bdmFacility.getCategory());
				response.setDeptId(bdmFacility.getDeptId());
				return response;
			})
			.collect(Collectors.toList());
		if (!facilityList.isEmpty()) {
			for (TargetRedisResponse device : facilityList) {
				String key = device.getTargetType() + "-" + device.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", device.getName());
				innerMap.put("targetType", device.getTargetType());
				innerMap.put("targetCategory", device.getCategory());
				innerMap.put("deptId", device.getDeptId());
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			}
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
		}

		List<TargetRedisResponse> vehicleList = bdmVehicleService.getBaseMapper().selectList(
				new QueryWrapper<BdmVehicle>()
					.select("id", "number", "target_type", "category", "dept_id")
					.eq("deleted", 0)
			).stream()
			.map(bdmVehicle -> {
				TargetRedisResponse response = new TargetRedisResponse();
				response.setId(bdmVehicle.getId());
				response.setName(bdmVehicle.getNumber());
				response.setTargetType(bdmVehicle.getTargetType());
				response.setCategory(bdmVehicle.getCategory());
				response.setDeptId(bdmVehicle.getDeptId());
				return response;
			})
			.collect(Collectors.toList());
		if (!vehicleList.isEmpty()) {
			for (TargetRedisResponse device : vehicleList) {
				String key = device.getTargetType() + "-" + device.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", device.getName());
				innerMap.put("targetType", device.getTargetType());
				innerMap.put("targetCategory", device.getCategory());
				innerMap.put("deptId", device.getDeptId());
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			}
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
		}

		List<TargetRedisResponse> visitorList = bdmVisitorService.getBaseMapper().selectList(
				new QueryWrapper<BdmVisitor>()
					.select("id", "name", "target_type", "id_number", "dept_id")
					.eq("deleted", 0)
			).stream()
			.map(bdmVisitor -> {
				TargetRedisResponse response = new TargetRedisResponse();
				response.setId(bdmVisitor.getId());
				response.setName(bdmVisitor.getName() + "(" + bdmVisitor.getIdNumber() + ")");
				response.setTargetType(bdmVisitor.getTargetType());
				response.setDeptId(bdmVisitor.getDeptId());
				return response;
			})
			.collect(Collectors.toList());
		if (!visitorList.isEmpty()) {
			for (TargetRedisResponse device : visitorList) {
				String key = device.getTargetType() + "-" + device.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", device.getName());
				innerMap.put("targetType", device.getTargetType());
				innerMap.put("targetCategory", device.getCategory());
				innerMap.put("deptId", device.getDeptId());
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			}
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
		}

		List<TargetRedisResponse> temporaryList = bdmTemporaryService.getBaseMapper().selectList(
				new QueryWrapper<BdmTemporary>()
					.select("id", "name", "target_type", "wkno", "dept_id")
					.eq("deleted", 0)
			).stream()
			.map(bdmTemporary -> {
				TargetRedisResponse response = new TargetRedisResponse();
				response.setId(bdmTemporary.getId());
				response.setName(bdmTemporary.getName() + "(" + bdmTemporary.getWkno() + ")");
				response.setTargetType(bdmTemporary.getTargetType());
				response.setDeptId(bdmTemporary.getDeptId());
				return response;
			})
			.collect(Collectors.toList());
		if (!temporaryList.isEmpty()) {
			for (TargetRedisResponse device : temporaryList) {
				String key = device.getTargetType() + "-" + device.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", device.getName());
				innerMap.put("targetType", device.getTargetType());
				innerMap.put("targetCategory", device.getCategory());
				innerMap.put("deptId", device.getDeptId());
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			}
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
		}

		//添加集装箱
		List<TargetRedisResponse> containerList = containerService.getBaseMapper().selectList(
				new QueryWrapper<BdmContainer>()
					.select("id", "number", "target_type", "model", "dept_id")
					.eq("deleted", 0)
			).stream()
			.map(bdmContainer -> {
				TargetRedisResponse response = new TargetRedisResponse();
				response.setId(bdmContainer.getId());
				response.setName(bdmContainer.getNumber());
				response.setTargetType(bdmContainer.getTargetType());
				response.setDeptId(bdmContainer.getDeptId());
				return response;
			})
			.collect(Collectors.toList());
		if (!containerList.isEmpty()) {
			for (TargetRedisResponse device : containerList) {
				String key = device.getTargetType() + "-" + device.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", device.getName());
				innerMap.put("targetType", device.getTargetType());
				innerMap.put("targetCategory", device.getCategory());
				innerMap.put("deptId", device.getDeptId());
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			}
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
		}
	}
}

