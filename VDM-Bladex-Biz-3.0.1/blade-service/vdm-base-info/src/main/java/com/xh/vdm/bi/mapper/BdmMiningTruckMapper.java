package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.MiningTruckRequest;
import com.xh.vdm.bi.vo.response.MiningTruckResponse;
import com.xh.vdm.biapi.entity.BdmMiningTruck;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmMiningTruck)表数据库访问层
 */
public interface BdmMiningTruckMapper extends BaseMapper<BdmMiningTruck> {

	/**
	 * 查询指定行数据
	 *
	 * @param request 查询条件
	 * @param page           分页对象
	 * @param account
	 * @return 对象列表
	 */
	IPage<MiningTruckResponse> queryByPage(@Param("request") MiningTruckRequest request, @Param("page") IPage page, @Param("account") String account, @Param("deptIds") String deptIds);


	/**
	 * 修改数据
	 *
	 * @param miningTruck
	 */
	void update(BdmMiningTruck miningTruck);

	/**
	 * 批量删除
	 *
	 * @param ids
	 * @return
	 */
	int deleteByIds(@Param("ids") Long[] ids);


	void insertBatch(List<BdmMiningTruck> bdmMiningTrucks);

	int batchUpdate(@Param("ids") List<Long> ids,@Param("deptId") Long deptId);
}

