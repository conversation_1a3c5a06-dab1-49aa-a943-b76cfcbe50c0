package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bi.dto.DeviceModelCount;
import com.xh.vdm.bi.dto.DeviceModelInfo;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.TargetDeviceRequest;
import com.xh.vdm.bi.vo.response.FacilityNoBingResponse;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import com.xh.vdm.biapi.entity.BdmVirtualTarget;
import org.apache.ibatis.annotations.Param;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmAbstractDevice)表数据库访问层
 */
public interface BdmAbstractDeviceMapper extends BaseMapper<BdmAbstractDevice> {


    void deleteByIds(@Param("ids") Long[] ids);

	void insertBatch(@Param("entities") List<BdmAbstractDevice> abstractDeviceList);

	void saveDevice(BdmAbstractDevice abstractDevice);

	void unbinding(@Param("id") Long id, @Param("targetType") Integer targetType);

	int bindTarget(@Param("deviceId") Long deviceId,@Param("targetId") Long targetId, @Param("targetType") Integer targetType, @Param("targetName") String targetName);

	void updateBatch(@Param("ids") List<Long> ids, @Param("id") Long id, @Param("targetType") Integer targetType, @Param("targetName") String targetName, @Param("deptId") Long deptId);

	void deleteByTargetIds(@Param("ids") Long[] ids, @Param("targetType") Integer targetType);

	void bindFacility(@Param("ids") List<Long> ids,@Param("id") Long id, @Param("targetType") Integer targetType, @Param("deptId") Long deptId);

	void updateDept(@Param("id") Long id, @Param("targetType") Integer targetType, @Param("deptId") Long deptId);

    IPage<FacilityNoBingResponse> selectNoBind(@Param("page") Page page, @Param("request") DeviceNoBindRequest request, @Param("account") String account, @Param("deptIds") String deptIds);

	IPage<PersonNoBingResponse> select(@Param("page") Page page, @Param("request") DeviceNoBindRequest request, @Param("account") String account, @Param("deptIds") String deptIds);

	List<PersonNoBingResponse> selectBind(@Param("id") Long id, @Param("targetType") Integer targetType);

	List<FacilityNoBingResponse> selectBingByFacility(@Param("id") Long id, @Param("targetType") Integer targetType);

    void updateBatchByUniqueId(BdmVirtualTarget virtualTarget);

	/**
	 * 根据用户权限获取设备型号统计信息。
	 * @param account 用户账号
	 * @param deptIds 用户监管部门id列表
	 * @return 设备型号统计信息
	 */
	List<DeviceModelInfo> getDeviceModelInfoByUserAuth(@Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 根据一系列条件获取设备型号统计信息
	 * @param account 用户账号
	 * @param deptIds 用户监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param models 终端型号列表
	 * @param usage 使用类型
	 * @return 设备型号统计信息
	 */
	List<DeviceModelInfo> getDeviceModelInfoWith(@Param("account") String account, @Param("deptIds") String deptIds,
												 @Param("deptId") Long deptId,
												 @Param("models") List<String> models, @Param("usage") Byte usage);

	/**
	 * 根据一系列条件获取设备型号统计信息
	 * @param account 用户账号
	 * @param deptIds 用户监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param models 终端型号列表
	 * @param usage 使用类型
	 * @param district 行政区划编码
	 * @return 设备型号统计信息
	 */
	List<DeviceModelInfo> getDeviceModelInfoWithDistrict(@Param("account") String account, @Param("deptIds") String deptIds,
														 @Param("deptId") Long deptId, @Param("models") List<String> models,
														 @Param("usage") Byte usage, @Param("district") String district);

	/**
	 * 根据用户权限获取设备型号个数统计信息。
	 * @param account 用户账号
	 * @param deptIds 用户监管部门id列表
	 * @return 设备型号个数统计信息
	 */
	List<DeviceModelCount> getDeviceModelCountByUserAuth(@Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 根据用户权限获取设备型号个数统计信息。
	 * @param account 用户账号
	 * @param deptIds 用户监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param models 终端型号列表
	 * @param usage 使用类型
	 * @return 设备型号个数统计信息
	 */
	List<DeviceModelCount> getDeviceModelCountWith(@Param("account") String account, @Param("deptIds") String deptIds,
													 @Param("deptId") Long deptId, @Param("models") List<String> models,
													 @Param("usage") Byte usage);

	/**
	 * 根据用户权限获取设备型号个数统计信息。
	 * @param account 用户账号
	 * @param deptIds 用户监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param models 终端型号列表
	 * @param usage 使用类型
	 * @param district 行政区划编码
	 * @return 设备型号个数统计信息
	 */
	List<DeviceModelCount> getDeviceModelCountWithDistrict(@Param("account") String account, @Param("deptIds") String deptIds,
													 @Param("deptId") Long deptId, @Param("models") List<String> models,
													 @Param("usage") Byte usage, @Param("district") String district);

	List<BdmAbstractDevice> getListByUserRole(@Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 统计终端总数。
	 * @param account 账号
	 * @param deptIds 监管部门id列表
	 * @return 终端总数
	 */
	long countByUserRole(@Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 统计终端总数。
	 * @param specificity 终端特殊性
	 * @param account 账号
	 * @param deptIds 监管部门id列表
	 * @return 终端总数
	 */
	long countByUserAuth(@Param("specificity") Byte specificity, @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 按条件查询终端id。
	 * @param specificity 终端特殊性
	 * @param account 账号
	 * @param deptIds 监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param usage 使用类型
	 * @return 终端id列表
	 */
	List<Long> getDeviceIdsWith(@Param("specificity") Byte specificity, @Param("account") String account,
						 @Param("deptIds") String deptIds, @Param("deptId") Long deptId,
						 @Param("usage") Byte usage);

	/**
	 * 按条件查询终端id。
	 * @param specificity 终端特殊性
	 * @param account 账号
	 * @param deptIds 监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param usage 使用类型
	 * @param district 行政区划编码
	 * @return 终端id列表
	 */
	List<Long> getDeviceIdsWithDistrict(@Param("specificity") Byte specificity, @Param("account") String account,
								@Param("deptIds") String deptIds, @Param("deptId") Long deptId,
								@Param("usage") Byte usage, @Param("district") String district);

	/**
	 * 统计终端总数。
	 * @param specificity 终端特殊性
	 * @param account 账号
	 * @param deptIds 监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param usage 使用类型
	 * @return 终端总数
	 */
	long countByUserWith(@Param("specificity") Byte specificity, @Param("account") String account,
						 @Param("deptIds") String deptIds, @Param("deptId") Long deptId,
						 @Param("usage") Byte usage);

	/**
	 * 统计终端总数。
	 * @param specificity 终端特殊性
	 * @param account 账号
	 * @param deptIds 监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param usage 使用类型
	 * @param district 行政区划编码
	 * @return 终端总数
	 */
	long countByUserWithDistrict(@Param("specificity") Byte specificity, @Param("account") String account,
						 @Param("deptIds") String deptIds, @Param("deptId") Long deptId,
						 @Param("usage") Byte usage, @Param("district") String district);

	/**
	 * 统计终端总数（通过id列表）。
	 * @param specificity 终端特殊性
	 * @param deviceIds 终端id列表
	 * @return 符合条件的终端总数
	 */
	long countByDeviceIds(@Param("specificity") Byte specificity, @Param("deviceIds") String deviceIds);

	BdmAbstractDevice getBadByUniqueId(@Param("uniqueId") String uniqueId);

	void bindAbstractTarget(@Param("request") TargetDeviceRequest request);
}

