package com.xh.vdm.bi.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;

@Configuration
public class DeviceTargetChangeTopicKafkaConfig {

	@Primary
	@ConfigurationProperties(prefix = "spring.device-target-change-topic-kafka")
	@Bean("deviceTargetChangeTopicKafkaProperties")
	public KafkaProperties firstKafkaProperties () {
		return new KafkaProperties();
	}

	@Bean("deviceTargetChangeTopicKafkaListenerContainerFactory")
	public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>>
	firstKafkaListenerContainerFactory (@Autowired @Qualifier("deviceTargetChangeTopicKafkaProperties") KafkaProperties firstKafkaProperties) {
		ConcurrentKafkaListenerContainerFactory<Integer, String> factory =
			new ConcurrentKafkaListenerContainerFactory<>();
		factory.setConsumerFactory(this.firstConsumerFactory(firstKafkaProperties));
		factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
		factory.getContainerProperties().setMissingTopicsFatal(false);
		return factory;
	}

	private ConsumerFactory<? super Integer, ? super String> firstConsumerFactory (KafkaProperties firstKafkaProperties) {
		return new DefaultKafkaConsumerFactory<>(firstKafkaProperties.buildConsumerProperties());
	}
}
