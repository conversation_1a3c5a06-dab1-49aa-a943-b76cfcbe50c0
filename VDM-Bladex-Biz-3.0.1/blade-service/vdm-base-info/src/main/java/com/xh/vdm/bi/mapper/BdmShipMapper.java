package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.ShipRequest;
import com.xh.vdm.bi.vo.response.ShipResponse;
import com.xh.vdm.biapi.entity.BdmShip;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmShip)表数据库访问层
 */
public interface BdmShipMapper extends BaseMapper<BdmShip> {

	/**
	 * 查询指定行数据
	 *
	 * @param request  查询条件
	 * @param page     分页对象
	 * @param account
	 * @return 对象列表
	 */
	IPage<ShipResponse> queryAll(@Param("request")ShipRequest request, IPage page, @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 新增数据
	 *
	 * @param bdmShip 实例对象
	 * @return 影响行数
	 */
	int insert(ShipRequest bdmShip);

	/**
	 * 批量新增数据（MyBatis原生foreach方法）
	 *
	 * @param entities List<BdmShip> 实例对象列表
	 * @return 影响行数
	 */
	int insertBatch(@Param("entities") List<BdmShip> entities);

	/**
	 * 修改数据
	 *
	 * @param bdmShip 实例对象
	 * @return 影响行数
	 */
	int update(BdmShip bdmShip);

	int batchUpdate(@Param("ids") List<Long> ids,@Param("deptId") Long deptId);

}

