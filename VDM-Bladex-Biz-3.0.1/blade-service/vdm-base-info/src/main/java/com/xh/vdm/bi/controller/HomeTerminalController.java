package com.xh.vdm.bi.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xh.vdm.bi.dto.DeviceModelCount;
import com.xh.vdm.bi.dto.DeviceModelInfo;
import com.xh.vdm.bi.entity.BdmDevSte;
import com.xh.vdm.bi.entity.Track;
import com.xh.vdm.bi.mapper.BdmAbstractDeviceMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.vo.response.TerminalAllCountAndOnlineCount;
import com.xh.vdm.bi.vo.response.TerminalCountInfo;
import com.xh.vdm.bi.vo.response.TerminalCountResponse;
import com.xh.vdm.bi.vo.response.TerminalInfo;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.StringUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.entity.DataAuthCE;
import org.springblade.system.entity.DeptNode;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.*;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;


/**
 * @Description: 首页--终端查询控制层
 */
@RestController
@RequestMapping("/home/<USER>")
@Slf4j
public class HomeTerminalController {

	@Resource
	private MonitDeviceService monitDeviceService;
	@Resource
	private IBdmAbstractDeviceService bdmAbstractDeviceService;
	@Resource
	private PntDeviceService pntDeviceService;
	@Resource
	private RdssDeviceService rdssDeviceService;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private WearableDeviceService wearableDeviceService;
	@Resource
	private IBdmDeviceStatusService bdmDeviceStatusService;

	@Resource
	private IBladeDictBizService bladeDictBizService;
	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private CETokenUtil ceTokenUtil;


	@GetMapping("/count")
	public R<TerminalCountResponse> count(BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		TerminalCountResponse res = new TerminalCountResponse();

		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		long monitCount = monitDeviceService.countByUserRole(ceDataAuth);
		long pntCount = pntDeviceService.countByUserRole(ceDataAuth);
		long rdssCount = rdssDeviceService.countByUserRole(ceDataAuth);
		long rnssCount = rnssDeviceService.countByUserRole(ceDataAuth);
		long wearCount = wearableDeviceService.countByUserRole(ceDataAuth);

		res.setMonitCount(monitCount);
		res.setPntCount(pntCount);
		res.setRdssCount(rdssCount);
		res.setRnssCount(rnssCount);
		res.setWearCount(wearCount);
		long totalCount = monitCount + pntCount + rdssCount + rnssCount + wearCount;
		res.setTotalCount(totalCount);
		return R.data(res);
	}

	/**
	 * 新版统计终端计数接口。
	 * @param user 用户信息
	 * @return 终端统计信息
	 */
	@GetMapping("/countNew")
	public R<List<TerminalCountInfo>> countNew(BladeUser user) {
		// 1.获取账号监管的部门信息
		DataAuthCE auth = ceTokenUtil.getDataAuth();

		// 2.从业务字典中获取终端型号分类
		TerminalCountInfo countInfo = null;
		Map<String, TerminalCountInfo> countMap = new HashMap<>();
		Map<String, String> kvs = bladeDictBizService.select(CommonConstant.DICT_CODE_DEVICE_MODEL);
		// 3.检查终端型号分类是否有定义
		if (null == kvs || kvs.isEmpty()) {
			return R.fail("终端型号字典未定义！");
		}

		// 4.生成终端型号分类表
		for (Map.Entry<String, String> entry : kvs.entrySet()) {
			countInfo = new TerminalCountInfo();
			countInfo.setModelCode(entry.getKey());
			countInfo.setModelName(entry.getValue());
			countMap.put(entry.getKey(), countInfo);
		}

		// 5.按终端型号分组统计各型号终端总数（接入量）
		Map<String, Byte> deviceIdMap = new HashMap<>();
		List<DeviceModelInfo> modelInfos = bdmAbstractDeviceService.getDeviceModelInfoByUserAuth(auth);
		// 5.1.按型号生成接入量统计
		for (DeviceModelInfo info : modelInfos) {
			countInfo = countMap.get(info.getModel());
			if (null == countInfo)
				continue;

			countInfo.setAccessNum(info.getIds().size());

			// 构建终端id表，用于今日上线统计
			for (Long id : info.getIds()) {
				deviceIdMap.put(CommonConstant.RKEY_PREFIX_TODAY
					+ CommonConstant.RKEY_PREFIX_ONLINE+id, (byte) 0);
			}
		}

		// 6.查询终端状态表中的在线终端信息，统计在线数
		List<DeviceModelCount> counts = bdmDeviceStatusService.getDeviceModelOnlineByUserAuth(auth);
		for (DeviceModelCount count : counts) {
			countInfo = countMap.get(count.getModel());
			if (null == countInfo)
				continue;

			countInfo.setOnlineNum(count.getNumber());
		}

		// 7.获取今日上线缓存信息，统计今日上线数
		List<String> keys = getKeysWithTodayOnline();

		// 7.1.过滤掉非本用户权限的终端
		if (!CollectionUtils.isEmpty(keys)) {
			List<String> newKeys = new ArrayList<>();
			for (String key : keys) {
				if (deviceIdMap.containsKey(key))
					newKeys.add(key);
			}

			keys = newKeys;
		}

		// 7.2.获取今日上线缓存的值
		List<String> models = null;
		if (!CollectionUtils.isEmpty(keys))
			 models = stringRedisTemplate.opsForValue().multiGet(keys);

		if (!CollectionUtils.isEmpty(models)) {
			for (String model : models) {
				if (StringUtils.isEmpty(model))
					continue;

				if (null == countInfo || !StringUtils.equals(countInfo.getModelCode(), model))
					countInfo = countMap.get(model);

				if (null == countInfo)
					continue;

				countInfo.addTodayOnlineNum(1);
			}
		}

		// 给终端型号排序
		List<TerminalCountInfo> result = new ArrayList<>(countMap.values());
		result.sort(Comparator.comparing(TerminalCountInfo::getModelCode));

		return R.data(result);
	}

	/**
	 * 统计终端接入与在线情况。
	 * @param district 行政区划编码，为null时表示不需要按该条件过滤
	 * @param deptId 部门id，为null时表示不需要按该条件过滤
	 * @param usage 使用类型，为null时表示不需要按该条件过滤
	 * @param keyword 终端型号/名称关键字，为null时表示不需要按该条件过滤
	 * @return 统计结果
	 */
	@GetMapping("/v2/countNew")
	public R<List<TerminalCountInfo>> countNewV2(String district, Long deptId, Byte usage, String keyword) {
		// 1.获取账号监管的部门信息
		DataAuthCE auth = ceTokenUtil.getDataAuth();

		// 2.从业务字典中获取终端型号分类
		Map<String, TerminalCountInfo> countMap = new HashMap<>();
		Map<String, String> kvs = bladeDictBizService.selectDevModel(keyword);
		// 3.检查是否有匹配的终端型号
		if (null == kvs || kvs.isEmpty()) {
			return R.data(new ArrayList<>());
		}

		// 查询终端型号与终端（用途）类型映射表
//		Map<String, String> devMUMap = bladeDictBizService.select(CommonConstant.DICT_CODE_DEV_MODEL_USAGE_MAP);
		Map<String, String> devMUMap = bladeDictBizService.selectDevModelUsageMap(null != usage ? usage.toString() : null);

//		// 4.生成终端型号分类表
//		TerminalCountInfo countInfo = null;
//		List<String> models = new ArrayList<>();
//		for (Map.Entry<String, String> entry : kvs.entrySet()) {
//			countInfo = new TerminalCountInfo();
//			countInfo.setModelCode(entry.getKey());
//			countInfo.setModelName(entry.getValue());
//
//			// 加载设备类型
//			if (devMUMap.containsKey(entry.getKey()))
//				countInfo.setDeviceUsage(Byte.parseByte(devMUMap.get(entry.getKey())));
//
//			countMap.put(entry.getKey(), countInfo);
//
//			// 添加设备型号过滤参数
//			models.add(entry.getKey());
//		}

		// 4.根据过滤条件求交集，生成终端型号分类表
		TerminalCountInfo countInfo = null;
		List<String> models = new ArrayList<>();
		for (Map.Entry<String, String> entry : kvs.entrySet()) {
			countInfo = new TerminalCountInfo();
			countInfo.setModelCode(entry.getKey());
			countInfo.setModelName(entry.getValue());

			// 加载设备类型
			if (!devMUMap.containsKey(entry.getKey()))
				continue;

			countInfo.setDeviceUsage(Byte.parseByte(devMUMap.get(entry.getKey())));

			countMap.put(entry.getKey(), countInfo);

			// 添加设备型号过滤参数
			models.add(entry.getKey());
		}

		// 5.是否还有符合条件的终端型号
		if (models.isEmpty())
			return R.data(new ArrayList<>());

		// 6.按终端型号分组统计各型号终端总数（接入量）
		List<DeviceModelCount> counts = bdmAbstractDeviceService
			.getDeviceModelCountWith(auth, models, district, deptId, null);
		// 6.1.按型号生成接入量统计
		for (DeviceModelCount count : counts) {
			countInfo = countMap.get(count.getModel());
			if (null == countInfo)
				continue;

			countInfo.setAccessNum(count.getNumber());
		}

//		// 6.按终端型号分组统计各型号终端id列表（接入量）
//		List<DeviceModelInfo> infos = bdmAbstractDeviceService
//			.getDeviceModelInfoWith(auth, models, district, deptId, null);
//		// 6.1.按型号生成接入量统计
//		for (DeviceModelInfo info : infos) {
//			countInfo = countMap.get(info.getModel());
//			if (null == countInfo)
//				continue;
//
//			countInfo.setAccessNum(info.getIds().size());
//			countInfo.setDeviceIds(info.getIds());
//		}

//		// 7.查询终端状态表中的在线终端信息，统计在线数
//		List<DeviceModelCount> onlineCounts = bdmDeviceStatusService
//			.getDevOnlineCntWith(auth, models, district, deptId, null);
//		for (DeviceModelCount count : onlineCounts) {
//			countInfo = countMap.get(count.getModel());
//			if (null == countInfo)
//				continue;
//
//			countInfo.setOnlineNum(count.getNumber());
//		}

		// 7.查询终端状态表中的在线终端信息，统计在线数
		List<DeviceModelInfo> onlineInfos = bdmDeviceStatusService
			.getDevOnlineInfoWith(auth, models, district, deptId, null);
		for (DeviceModelInfo info : onlineInfos) {
			countInfo = countMap.get(info.getModel());
			if (null == countInfo)
				continue;

			countInfo.setOnlineNum(info.getIds().size());
			countInfo.setDeviceIds(info.getIds());
		}

		// 给终端型号排序
		List<TerminalCountInfo> result = new ArrayList<>(countMap.values());
		result.sort(Comparator.comparing(TerminalCountInfo::getModelCode));

		return R.data(result);
	}

	@GetMapping("/countOldDevice")
	public R<TerminalCountInfo> countOldDevice(BladeUser user) {
		// 1.获取账号监管的部门信息
		DataAuthCE auth = ceTokenUtil.getDataAuth();

		TerminalCountInfo info = new TerminalCountInfo();
		info.setModelCode("旧终端");
		info.setModelName("旧终端");

		// 2.获取旧终端接入量
		long count = bdmAbstractDeviceService.countByUserAuth((byte) 1, auth);
		info.setAccessNum((int) count);

		// 3.获取旧终端在线量
		count = bdmDeviceStatusService.countByUserAuth((byte)1, (byte)0, auth);
		info.setOnlineNum((int) count);

		// 4.获取所有今日上线终端key
		List<String> keys = getKeysWithTodayOnline();
		if (CollectionUtils.isEmpty(keys)) {
			return R.data(info);
		}

		// 提取设备id
		List<String> deviceIds = new ArrayList<>();
		StringBuilder builder = new StringBuilder("'{");
		for (String key : keys) {
			key = key.substring(key.lastIndexOf(":")+1);
			if (builder.length() > 2)
				builder.append(",");
			builder.append(key);
		}
		String idStr = builder.append("}'").toString();

		// 5.根据上线设备id查询旧设备数
		count = bdmAbstractDeviceService.countByDeviceIds((byte) 1, idStr);
		info.setTodayOnlineNum((int) count);

		return R.data(info);
	}

	@GetMapping("/v2/countOldDevice")
	public R<TerminalCountInfo> countOldDeviceV2(String district, Long deptId) {
		// 1.获取账号监管的部门信息
		DataAuthCE auth = ceTokenUtil.getDataAuth();

		TerminalCountInfo info = new TerminalCountInfo();
		info.setModelCode("旧终端");
		info.setModelName("旧终端");

//		// 2.获取应用场景
//		List<Integer> scenarios = null;
//		if (null != scenario)
//			scenarios = bladeDictBizService.getScenarios(scenario);

		// 3.获取旧终端接入量
		long count = bdmAbstractDeviceService.countByUserWith((byte) 1, auth, district, deptId, null);
		info.setAccessNum((int) count);

//		// 4.获取旧终端在线量
//		count = bdmDeviceStatusService.countByUserWith((byte)1, (byte)0, auth, district, deptId, null);
//		info.setOnlineNum((int) count);

		// 4.获取旧终端在线量
		List<Long> deviceIds = bdmDeviceStatusService.getDeviceIdsWith((byte)1, (byte)0, auth, district, deptId, null);
		info.setOnlineNum(deviceIds.size());
		info.setDeviceIds(deviceIds);

		return R.data(info);
	}

	@GetMapping("/countSpcDevice")
	public R<TerminalCountInfo> countSpcDevice(String district, Long deptId) {
		// 1.获取账号监管的部门信息
		DataAuthCE auth = ceTokenUtil.getDataAuth();

		TerminalCountInfo info = new TerminalCountInfo();
		info.setModelCode("特殊终端");
		info.setModelName("特殊终端");

//		// 2.获取应用场景
//		List<Integer> scenarios = null;
//		if (null != scenario)
//			scenarios = bladeDictBizService.getScenarios(scenario);

		// 3.获取特殊终端接入量
		long count = bdmAbstractDeviceService.countByUserWith((byte) 3, auth, district, deptId, null);
		info.setAccessNum((int) count);

//		// 4.获取特殊终端在线量
//		count = bdmDeviceStatusService.countByUserWith((byte)3, (byte)0, auth, district, deptId, null);
//		info.setOnlineNum((int) count);

		// 4.获取特殊终端在线量
		List<Long> deviceIds = bdmDeviceStatusService.getDeviceIdsWith((byte)3, (byte)0, auth, district, deptId, null);
		info.setOnlineNum(deviceIds.size());
		info.setDeviceIds(deviceIds);

		return R.data(info);
	}

	/**
	 * 获取今日上线终端的key列表。
	 * @return key列表
	 */
	private List<String> getKeysWithTodayOnline() {
		return stringRedisTemplate.execute((RedisCallback<List<String>>) connection -> {
			List<String> result = new ArrayList<>();
			ScanOptions options = ScanOptions.scanOptions().match(CommonConstant.RKEY_PREFIX_TODAY
				+ CommonConstant.RKEY_PREFIX_ONLINE + "*").count(1000).build(); // 1000是希望每次扫描返回的最大key数量，可根据需要调整
			Cursor<byte[]> cursor = connection.scan(options);
			while (cursor.hasNext()) {
				result.add(new String(cursor.next()));
			}
			return result;
		});
	}

	@GetMapping("/real/location")
	public R<Map<String, Object>> location(Integer deviceType, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		long time1 = System.currentTimeMillis();
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		long time2 = System.currentTimeMillis();
		log.info("终端今日上线，getDataAuth:{}",time2-time1);
		List<BdmAbstractDevice> deviceList = bdmAbstractDeviceService.getListByUserRole(ceDataAuth);
		List<Long> deviceIdList = new ArrayList<>();
		for (BdmAbstractDevice bad : deviceList) {
			deviceIdList.add(bad.getId());
		}

		// 今日上线总数
		Set<String> keys = stringRedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
			Set<String> result = new HashSet<>();
			Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(CommonConstant.TodayOnline + "*").count(1000).build());
			while (cursor.hasNext()) {
				result.add(new String(cursor.next()));
			}
			return result;
		});
		Set<String> onlineKeysSet = keys.stream()
			.map(onlineKey -> onlineKey.substring(CommonConstant.TodayOnline.length()))
			.collect(Collectors.toSet());
		Collection<Object> keyList = new ArrayList<>(onlineKeysSet);
		// 一次性批量获取所有keys对应的值
		Map<String, TerminalInfo> todayterminalInfoMap = new HashMap<>();
		List<Object> trackList = stringRedisTemplate.opsForHash().multiGet(CommonConstant.REDIS_CACHE_LAST_POST, keyList);
		for (Object obj : trackList) {
			// 可能有空的值
			if (null == obj)
				continue;
			Track track = JSON.parseObject(obj.toString(), Track.class);
			TerminalInfo terminalInfo = new TerminalInfo();
			terminalInfo.setLongitude(track.getLongitude());
			terminalInfo.setLatitude(track.getLatitude());
			terminalInfo.setLocTime(track.getLocTime());
			terminalInfo.setDeviceType(track.getDeviceType());
			terminalInfo.setDeptId(track.getDeptId());
			terminalInfo.setDeviceId(track.getDeviceId());
			todayterminalInfoMap.put(track.getDeviceId()+"", terminalInfo);
//			log.info("今日上线终端:key={},value={}", track.getDeviceId(), terminalInfo);
		}

		//获取在线总数
		Set<String> onlineMapKeysSet = new HashSet<>();
		Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(CommonConstant.RealDevSte);
		if (entries.size() > 0) {
			for (Map.Entry<Object, Object> entry : entries.entrySet()) {
				String deviceIdStr = entry.getKey().toString();
				onlineMapKeysSet.add(deviceIdStr);
//				log.info("在线终端:key={}",deviceIdStr);
			}
		}

		List<TerminalInfo> terminalInfoList = new ArrayList<>();
		//在线总数
		AtomicLong onlineCount = new AtomicLong();
		//今日上线总数
		AtomicLong linkCount = new AtomicLong();

		// 遍历keys对应的值，构建TerminalInfo对象并判断在线状态
		todayterminalInfoMap.forEach((key, terminalInfo) -> {
			if (onlineMapKeysSet.contains(key)) {
				terminalInfo.setTeState(1);
			} else {
				terminalInfo.setTeState(0);
			}
			if (deviceType == null || deviceType.equals(terminalInfo.getDeviceType())) {
				terminalInfoList.add(terminalInfo);
			}
		});
		List<TerminalInfo> filteredTerminalInfoList = new ArrayList<>();
		log.info(">>==terminalInfoList is " + JSON.toJSONString(terminalInfoList));
		log.info(">>==ceDataAuth's isSuper is " + (ceDataAuth == null ? "空的" : ceDataAuth.getIsSuper()));
		if (!ceDataAuth.getIsSuper()) {//非超级管理员
			if (ceDataAuth.getAccount() != null) {//国能用户
				for (TerminalInfo terminalInfo : terminalInfoList) {
					if (deviceIdList.contains(terminalInfo.getDeviceId())) {
						filteredTerminalInfoList.add(terminalInfo);
						linkCount.getAndIncrement();
					}
				}
				for (String onlineDevId : onlineMapKeysSet) {
					if (deviceIdList.contains(Long.valueOf(onlineDevId))) {
						onlineCount.getAndIncrement();
					}
				}
			} else {//本地用户
				for (TerminalInfo terminalInfo : terminalInfoList) {
					String deptIdStr = terminalInfo.getDeptId().toString();
					if (ceDataAuth.getOrgList().contains(deptIdStr)) {
						filteredTerminalInfoList.add(terminalInfo);
						linkCount.getAndIncrement();
					}
				}
				for (String onlineDevId : onlineMapKeysSet) {
					if (deviceIdList.contains(Long.valueOf(onlineDevId))) {
						onlineCount.getAndIncrement();
					}
				}
			}
		} else {
			for (TerminalInfo terminalInfo : terminalInfoList) {
				filteredTerminalInfoList.add(terminalInfo);
				linkCount.getAndIncrement();
			}
			for (String onlineDevId : onlineMapKeysSet) {
				if (deviceIdList.contains(Long.valueOf(onlineDevId))) {
					onlineCount.getAndIncrement();
				}
			}
		}

		Map<String, Object> map = new HashMap<>();
		map.put("terminalInfoList", filteredTerminalInfoList);
		long totalOnlineCount = bdmDeviceStatusService.countOnlineDevices(ceDataAuth);
		//在线总数
		map.put("terminalOnlineCount", totalOnlineCount);
		//map.put("terminalOnlineCount", onlineCount);
		//今日上线总数
		map.put("terminalLinkCount", linkCount.get());

		return R.data(map);
	}

	/**
	 * 查询终端总数和在线总数
	 * @return
	 */
	@GetMapping("/terminalAllCountAndOnlineCount")
	public R<TerminalAllCountAndOnlineCount> terminalAllCountAndOnlineCount(){
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		long totalOnlineCount = bdmDeviceStatusService.countOnlineDevices(ceDataAuth);
		TerminalAllCountAndOnlineCount countBean = new TerminalAllCountAndOnlineCount();
		countBean.setTerminalOnlineCount(totalOnlineCount);

		//2.终端总数
		long totalCount = bdmAbstractDeviceService.countByUserRole(ceDataAuth);
		countBean.setAllTerminalCount(totalCount);
		return R.data(countBean);
	}
}

