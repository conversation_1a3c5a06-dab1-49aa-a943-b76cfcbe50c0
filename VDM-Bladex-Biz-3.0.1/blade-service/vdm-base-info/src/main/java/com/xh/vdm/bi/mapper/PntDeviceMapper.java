package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.FacilityTerminalRequest;
import com.xh.vdm.bi.vo.request.PntDeviceRequest;
import com.xh.vdm.bi.vo.response.FacilityNoBingResponse;
import com.xh.vdm.bi.vo.response.PntDeviceResponse;
import com.xh.vdm.biapi.entity.BdmPntDevice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 北斗授时终端
 */
public interface PntDeviceMapper extends BaseMapper<BdmPntDevice> {

	/**
	 * 查询指定行数据
	 *
	 * @param page    分页对象
	 * @param request 查询条件
	 * @param schema
	 * @return 对象列表
	 */
	IPage<PntDeviceResponse> queryAll(IPage page, @Param("request") PntDeviceRequest request, @Param("account") String account, @Param("deptIds") String deptIds, @Param("schema") String schema);

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	PntDeviceResponse queryById(Long id);

	/**
	 * 新增数据
	 *
	 * @param bdmPntDevice 实例对象
	 * @return 影响行数
	 */
	void insertPnt(BdmPntDevice bdmPntDevice);

	/**
	 * 修改数据
	 *
	 * @param bdmPntDevice 实例对象
	 * @return 影响行数
	 */
	int update(BdmPntDevice bdmPntDevice);

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 影响行数
	 */
	int deleteById(Long id);

	int deleteByIds(Long[] ids);

	IPage<FacilityNoBingResponse> selectNoBind(Page page, @Param("request") DeviceNoBindRequest request, @Param("account") String account, @Param("deptIds") String deptIds);

	List<FacilityNoBingResponse> selectBindByFacilityId(@Param("id") Long id, @Param("targetType") Integer targetType, @Param("request") DeviceNoBindRequest request);

    void insertBatch(List<PntDeviceRequest> list);

	void updateByDeviceId(@Param("id") Long id, @Param("targetType") Integer targetType);

	void updateBatchByTerminalId(@Param("pnt") FacilityTerminalRequest pnt, @Param("id") Long id, @Param("targetType") Integer targetType, @Param("deptId") Long deptId);

	void deleteByTargetIds(Long[] ids);

    long countByUserRole(@Param("account") String account, @Param("deptIds") String deptIds);

	void updateDept(@Param("id") Long id, @Param("targetType") Integer targetType, @Param("deptId") Long deptId);

	int bindTarget(@Param("deviceId") Long deviceId,@Param("targetId") Long targetId, @Param("targetType") Integer targetType, @Param("targetName") String targetName);
}

