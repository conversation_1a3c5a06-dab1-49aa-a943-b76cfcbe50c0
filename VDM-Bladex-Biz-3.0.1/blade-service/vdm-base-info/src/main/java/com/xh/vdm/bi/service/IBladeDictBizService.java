package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.entity.BladeDictBiz;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 业务字典表 服务类
 * </p>
 */
public interface IBladeDictBizService extends IService<BladeDictBiz> {

	Map<String, String> select(String code);

	/**
	 * 查询设备型号。
	 * @param keyword 关键字
	 * @return 符合条件的设备型号和名称键值对
	 */
	Map<String, String> selectDevModel(String keyword);

	/**
	 * 查询终端型号与用途映射。
	 * @param usage 用途类型
	 * @return 符合条件的键值对
	 */
	Map<String, String> selectDevModelUsageMap(String usage);

	/**
	 * 获取指定的应用场景。
	 * @param scenario （一级）应用场景
	 * @return 自身及下级应用场景
	 */
	List<Integer> getScenarios(Integer scenario);

	List<Map<String, Object>> deviceType(String bdmDeviceType,Long parentId);
}
