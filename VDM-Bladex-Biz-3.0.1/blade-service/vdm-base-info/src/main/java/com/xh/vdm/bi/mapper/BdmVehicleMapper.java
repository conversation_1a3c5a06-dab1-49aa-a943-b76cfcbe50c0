package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bi.vo.request.VehicleRequest;
import com.xh.vdm.bi.vo.response.VehicleResponse;
import com.xh.vdm.biapi.entity.BdmVehicle;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 */
public interface BdmVehicleMapper extends BaseMapper<BdmVehicle> {

	IPage<VehicleResponse> queryAll(Page page, @Param("request") VehicleRequest request, @Param("account") String account, @Param("deptIds") String deptIds);

	int insertVehicle(BdmVehicle bdmVehicle);

	int update(BdmVehicle bdmVehicle);

	int deleteByIds(@Param("ids") Long[] ids);

	void insertBatch(List<BdmVehicle> list);

	List<String> selectAllNumbers(String number);

	int batchUpdate(@Param("ids") List<Long> ids,@Param("deptId") Long deptId);
}
