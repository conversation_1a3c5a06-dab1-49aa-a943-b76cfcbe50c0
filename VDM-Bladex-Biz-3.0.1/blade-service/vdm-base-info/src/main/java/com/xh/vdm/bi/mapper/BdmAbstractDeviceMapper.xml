<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmAbstractDeviceMapper">

    <insert id="saveDevice">
        INSERT INTO bdm_abstract_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id and '' != id">
                id,
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                unique_id,
            </if>
            <if test="null != deviceType and '' != deviceType">
                device_type,
            </if>
            <if test="null != deptId and '' != deptId">
                dept_id,
            </if>
            <if test="null != category and '' != category">
                category,
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                device_num,
            </if>
            <if test="null != targetId and '' != targetId">
                target_id,
            </if>
            <if test="null != targetType and '' != targetType">
                target_type,
            </if>
            <if test="null != deleted and '' != deleted">
                deleted,
            </if>
            <if test="null != iotProtocol and '' != iotProtocol">
                iot_protocol,
            </if>
            <if test="null != model and '' != model">
                model,
            </if>
            <if test="null != specificity and '' != specificity">
                specificity
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id and '' != id">
                #{id},
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                #{uniqueId},
            </if>
            <if test="null != deviceType and '' != deviceType">
                #{deviceType},
            </if>
            <if test="null != deptId and '' != deptId">
                #{deptId},
            </if>
            <if test="null != category and '' != category">
                #{category},
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                #{deviceNum},
            </if>
            <if test="null != targetId and '' != targetId">
                #{targetId},
            </if>
            <if test="null != targetType and '' != targetType">
                #{targetType},
            </if>
            <if test="null != deleted and '' != deleted">
                #{deleted},
            </if>
            <if test="null != iotProtocol and '' != iotProtocol">
                #{iotProtocol},
            </if>
            <if test="null != model and '' != model">
                #{model},
            </if>
            <if test="null != specificity and '' != specificity">
                #{specificity}
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bdm_abstract_device
        <set>
            <if test="uniqueId != null and uniqueId != ''">
                unique_id = #{uniqueId},
            </if>
            <if test="deviceType != null">
                device_type = #{deviceType},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId},
            </if>
            <if test="category != null">
                category = #{category},
            </if>
            <if test="deviceNum != null and deviceNum != ''">
                device_num = #{deviceNum},
            </if>
            <if test="targetId != null">
                target_id = #{targetId},
            </if>
            <if test="targetType != null">
                target_type = #{targetType},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="iotProtocol != null">
                iot_protocol = #{iotProtocol}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键逻辑删除-->
    <update id="deleteByIds">
        update bdm_abstract_device
        set deleted = 1
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into bdm_abstract_device(id,unique_id, device_type, dept_id, category, device_num, target_id, target_type,
         iot_protocol,model,specificity)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id}, #{entity.uniqueId}, #{entity.deviceType}, #{entity.deptId}, #{entity.category}, #{entity.deviceNum},
            #{entity.targetId}, #{entity.targetType}, #{entity.iotProtocol},#{entity.model},#{entity.specificity})
        </foreach>
    </insert>

    <update id="unbinding">
        update bdm_abstract_device
        set target_id = 0, target_type = 0
        where target_id = #{id} and target_type = #{targetType}
    </update>

    <update id="bindTarget">
        update bdm_abstract_device
        set target_id = #{targetId}, target_type = #{targetType}
        where id = #{deviceId}
    </update>

    <update id="updateBatch">
        update bdm_abstract_device
        set
            target_id = #{id},
            target_type = #{targetType}
        where id in
        <if test="ids != null and ids.size() > 0">
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="deleteByTargetIds">
        <if test="ids != null and ids.length > 0">
            update bdm_abstract_device
            set target_id = 0, target_type = 0
            where target_type =  #{targetType}
            and target_id in
            <foreach collection="ids" item="id" index="i" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </update>

    <update id="bindFacility">
        update bdm_abstract_device
        set
            target_id = #{id},
            target_type = #{targetType},
            dept_id = #{deptId}
        where id in
        <if test="ids != null and ids.size() > 0">
            <foreach collection="ids" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <update id="updateDept">
        update bdm_abstract_device set dept_id = #{deptId} where target_id = #{id} and target_type = #{targetType}
    </update>

    <!-- TODO 本人权限-->
    <select id="selectNoBind" resultType="com.xh.vdm.bi.vo.response.FacilityNoBingResponse">
        SELECT
        t.id,
        t.unique_id,
        t.dept_id,
        t.category,
        t.device_type,
        t.device_num,
        t.iot_protocol,
        d.dept_name
        FROM bdm_abstract_device t
        LEFT JOIN blade_dept d on t.dept_id = d.id
        WHERE t.deleted = 0
        and t.target_id = 0
        and t.device_type = 4 or t.device_type = 5
        <if test="request.uniqueId != null and request.uniqueId != ''">
            AND t.unique_id LIKE concat('%', #{request.uniqueId}, '%')
        </if>
        <if test="request.deptId != null">
            AND t.dept_id = #{request.deptId}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and d.id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and t.create_account = #{account}
        </if>
    </select>

    <!-- TODO 本人权限-->
    <select id="select" resultType="com.xh.vdm.bi.vo.response.PersonNoBingResponse">
        SELECT
        t.id,
        t.unique_id,
        t.dept_id,
        t.category,
        t.device_type,
        t.device_num,
        t.iot_protocol,
        t.specificity,
        d.dept_name
        FROM bdm_abstract_device t
        LEFT JOIN blade_dept d on t.dept_id = d.id
        WHERE t.deleted = 0
        and t.target_type = 0
        <if test="request.deviceType != null">
            AND t.device_type = #{request.deviceType}
        </if>
        <if test="request.category != null">
            AND t.category = #{request.category}
        </if>
        <if test="request.uniqueId != null and request.uniqueId != ''">
            AND t.unique_id LIKE concat('%', #{request.uniqueId}, '%')
        </if>
        <if test="request.deptId != null">
            AND t.dept_id = #{request.deptId}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and d.id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and t.create_account = #{account}
        </if>
    </select>

    <select id="selectBind" resultType="com.xh.vdm.bi.vo.response.PersonNoBingResponse">
        SELECT
        bad.id,
        bad.unique_id,
        bad.dept_id,
        bad.category,
        bad.device_type,
        bad.device_num,
        bad.iot_protocol,
        d.dept_name
        FROM bdm_abstract_device bad
        LEFT JOIN blade_dept d on bad.dept_id = d.id
        where bad.target_id = #{id}
        and bad.target_type = #{targetType}
    </select>

<!--    <select id="selectBingByFacility" resultType="com.xh.vdm.bi.vo.response.FacilityNoBingResponse">-->
<!--        SELECT-->
<!--        bad.id,-->
<!--        bad.unique_id,-->
<!--        bad.dept_id,-->
<!--        bad.category,-->
<!--        bad.device_type,-->
<!--        bad.device_num,-->
<!--        bad.iot_protocol,-->
<!--        d.dept_name-->
<!--        FROM bdm_abstract_device bad-->
<!--        LEFT JOIN blade_dept d on bad.dept_id = d.id-->
<!--        where bad.target_id = #{id}-->
<!--        and bad.target_type = #{targetType}-->
<!--    </select>-->
    <select id="selectBingByFacility" resultType="com.xh.vdm.bi.vo.response.FacilityNoBingResponse">
        SELECT
            bad.id,
            bad.unique_id,
            bad.dept_id,
            bad.category,
            bad.device_type,
            bad.device_num,
            bad.iot_protocol,
            d.dept_name,
            CASE
                WHEN bad.device_type = 4 THEN md.longitude
                WHEN bad.device_type = 5 THEN pd.longitude
                ELSE NULL
                END AS longitude,
            CASE
                WHEN bad.device_type = 4 THEN md.latitude
                WHEN bad.device_type = 5 THEN pd.latitude
                ELSE NULL
                END AS latitude
        FROM
            bdm_abstract_device bad
                LEFT JOIN
            blade_dept d ON bad.dept_id = d.id
                LEFT JOIN
            bdm_monit_device md ON bad.id = md.id AND bad.device_type = 4
                LEFT JOIN
            bdm_pnt_device pd ON bad.id = pd.id AND bad.device_type = 5
        WHERE
            bad.target_id = #{id}
          AND bad.target_type = #{targetType}
    </select>

    <update id="updateBatchByUniqueId">
        update bdm_wearable_device
        set target_id = #{id} , target_type = 0
        where unique_id = #{number}
    </update>

    <resultMap id="deviceModelInfo" type="com.xh.vdm.bi.dto.DeviceModelInfo">
<!--        <result property="id" column="id" javaType="long" jdbcType="BIGINT"/>-->
        <result property="model" column="model" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="ids" column="ids" jdbcType="ARRAY" typeHandler="com.xh.vdm.bi.handler.ListTypeHandler"/>
    </resultMap>

    <resultMap id="deviceModelCount" type="com.xh.vdm.bi.dto.DeviceModelCount">
        <result property="model" column="model" javaType="java.lang.String" jdbcType="VARCHAR"/>
        <result property="number" column="cnt" javaType="int" jdbcType="INTEGER"/>
    </resultMap>

    <select id="getDeviceModelInfoByUserAuth" resultMap="deviceModelInfo">
        select model, array_agg(id) as ids from bdm_abstract_device bad
        WHERE bad.deleted = 0 and bad.model != ''
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and create_account = #{account}
        </if>
        group by model order by model;
    </select>

    <select id="getDeviceModeInfoWith" resultMap="deviceModelInfo">
        select bad.model, array_agg(bad.id) as ids from bdm_abstract_device bad
        LEFT JOIN blade_dept bdd ON bad.dept_id = bdd.id
        WHERE bad.deleted = 0
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (bad.dept_Id = #{deptId} or bdd.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
        <choose>
            <when test="models != null and models.size() > 0">
                and bad.model in
                <foreach collection="models" item="model" open="(" separator="," close=")">
                    #{model}
                </foreach>
            </when>
            <otherwise>
                and bad.model != ''
            </otherwise>
        </choose>
<!--        <if test="scenarios != null and scenarios.size() > 0">-->
<!--            and bad.scenario in-->
<!--            <foreach collection="scenarios" item="scenario" open="(" separator="," close=")">-->
<!--                #{scenario}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="usage != null">
            and bad.usage = #{usage}
        </if>
        group by bad.model order by bad.model;
    </select>

    <select id="getDeviceModelInfoWithDistrict" resultMap="deviceModelInfo">
        select bad.model, array_agg(bad.id) as ids from bdm_abstract_device bad
        LEFT JOIN bdm_device_status bds ON bad.id = bds.device_id
        LEFT JOIN blade_dept bdd ON bad.dept_id = bdd.id
        WHERE bad.deleted = 0
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (bad.dept_Id = #{deptId} or bdd.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
        <choose>
            <when test="models != null and models.size() > 0">
                and bad.model in
                <foreach collection="models" item="model" open="(" separator="," close=")">
                    #{model}
                </foreach>
            </when>
            <otherwise>
                and bad.model != ''
            </otherwise>
        </choose>
<!--        <if test="scenarios != null and scenarios.size() > 0">-->
<!--            and bad.scenario in-->
<!--            <foreach collection="scenarios" item="scenario" open="(" separator="," close=")">-->
<!--                #{scenario}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="usage != null">
            and bad.usage = #{usage}
        </if>
        <if test="district != null and district != ''">
            and bds.district LIKE concat(#{district}, '%')
        </if>
        group by bad.model order by bad.model;
    </select>

    <select id="getDeviceModelCountByUserAuth" resultMap="deviceModelCount">
        select model, count(id) as cnt from bdm_abstract_device bad
        WHERE bad.deleted = 0 and bad.model != ''
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and create_account = #{account}
        </if>
        group by model;
    </select>

    <select id="getDeviceModelCountWith" resultMap="deviceModelCount">
        select model, count(id) as cnt from bdm_abstract_device bad
        LEFT JOIN blade_dept bdd ON bad.dept_id = bdd.id
        WHERE bad.deleted = 0
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (bad.dept_Id = #{deptId} or bdd.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
        <choose>
            <when test="models != null and models.size() > 0">
                and bad.model in
                <foreach collection="models" item="model" open="(" separator="," close=")">
                    #{model}
                </foreach>
            </when>
            <otherwise>
                and bad.model != ''
            </otherwise>
        </choose>
<!--        <if test="scenarios != null and scenarios.size() > 0">-->
<!--            and bad.scenario in-->
<!--            <foreach collection="scenarios" item="scenario" open="(" separator="," close=")">-->
<!--                #{scenario}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="usage != null">
            and bad.usage = #{usage}
        </if>
        group by model;
    </select>

    <select id="getDeviceModelCountWithDistrict" resultMap="deviceModelCount">
        select bad.model, count(bad.id) as cnt from bdm_abstract_device bad
        LEFT JOIN bdm_device_status bds ON bad.id = bds.device_id
        LEFT JOIN blade_dept bdd ON bad.dept_id = bdd.id
        WHERE bad.deleted = 0
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (bad.dept_Id = #{deptId} or bdd.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
        <choose>
            <when test="models != null and models.size() > 0">
                and bad.model in
                <foreach collection="models" item="model" open="(" separator="," close=")">
                    #{model}
                </foreach>
            </when>
            <otherwise>
                and bad.model != ''
            </otherwise>
        </choose>
<!--        <if test="scenarios != null and scenarios.size() > 0">-->
<!--            and bad.scenario in-->
<!--            <foreach collection="scenarios" item="scenario" open="(" separator="," close=")">-->
<!--                #{scenario}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="usage != null">
            and bad.usage = #{usage}
        </if>
        <if test="district != null and district != ''">
            and bds.district LIKE concat(#{district}, '%')
        </if>
        group by bad.model;
    </select>

    <select id="getListByUserRole" resultType="com.xh.vdm.biapi.entity.BdmAbstractDevice">
        select * from bdm_abstract_device bad
        WHERE bad.deleted = 0
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and create_account = #{account}
        </if>
    </select>

    <select id="countByUserRole" resultType="java.lang.Long">
        select count(*) from bdm_abstract_device bad
        WHERE bad.deleted = 0
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
    </select>

    <select id="countByUserAuth" resultType="java.lang.Long">
        select count(*) from bdm_abstract_device bad
        WHERE bad.deleted = 0
        <if test="specificity != null">
            and bad.specificity = #{specificity}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
    </select>

    <select id="getDeviceIdsWith" resultType="java.lang.Long">
        select bad.id from bdm_abstract_device bad
        LEFT JOIN blade_dept bdd ON bad.dept_id = bdd.id
        WHERE bad.deleted = 0
        <if test="specificity != null">
            and bad.specificity = #{specificity}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (bad.dept_Id = #{deptId} or bdd.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
        <if test="usage != null">
            and bad.usage = #{usage}
        </if>
    </select>

    <select id="getDeviceIdsWithDistrict" resultType="java.lang.Long">
        select bad.id from bdm_abstract_device bad
        LEFT JOIN bdm_device_status bds ON bad.id = bds.device_id
        LEFT JOIN blade_dept bdd ON bad.dept_id = bdd.id
        WHERE bad.deleted = 0
        <if test="specificity != null">
            and bad.specificity = #{specificity}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (bad.dept_Id = #{deptId} or bdd.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
        <if test="usage != null">
            and bad.usage = #{usage}
        </if>
        <if test="district != null and district != ''">
            and bds.district LIKE concat(#{district}, '%')
        </if>
    </select>

    <select id="countByUserWith" resultType="java.lang.Long">
        select count(*) from bdm_abstract_device bad
        LEFT JOIN blade_dept bdd ON bad.dept_id = bdd.id
        WHERE bad.deleted = 0
        <if test="specificity != null">
            and bad.specificity = #{specificity}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (bad.dept_Id = #{deptId} or bdd.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
<!--        <if test="scenarios != null and scenarios.size() > 0">-->
<!--            and bad.scenario in-->
<!--            <foreach collection="scenarios" item="scenario" open="(" separator="," close=")">-->
<!--                #{scenario}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="usage != null">
            and bad.usage = #{usage}
        </if>
    </select>

    <select id="countByUserWithDistrict" resultType="java.lang.Long">
        select count(*) from bdm_abstract_device bad
        LEFT JOIN bdm_device_status bds ON bad.id = bds.device_id
        LEFT JOIN blade_dept bdd ON bad.dept_id = bdd.id
        WHERE bad.deleted = 0
        <if test="specificity != null">
            and bad.specificity = #{specificity}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="deptId != null">
            and (bad.dept_Id = #{deptId} or bdd.ancestors LIKE concat('%', #{deptId}, '%'))
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
<!--        <if test="scenarios != null and scenarios.size() > 0">-->
<!--            and bad.scenario in-->
<!--            <foreach collection="scenarios" item="scenario" open="(" separator="," close=")">-->
<!--                #{scenario}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="usage != null">
            and bad.usage = #{usage}
        </if>
        <if test="district != null and district != ''">
            and bds.district LIKE concat(#{district}, '%')
        </if>
    </select>

    <select id="countByDeviceIds" resultType="java.lang.Long">
        select count(*) from bdm_abstract_device bad
        WHERE bad.deleted = 0
        <if test="specificity != null">
            and bad.specificity = #{specificity}
        </if>
        <if test="deviceIds != null and deviceIds != ''">
            and bad.id = any(${deviceIds})
        </if>
    </select>

    <select id="getBadByUniqueId" resultType="com.xh.vdm.biapi.entity.BdmAbstractDevice">
        select * from bdm_abstract_device bad
        WHERE bad.deleted = 0 and bad.unique_id = #{uniqueId}
    </select>

    <update id="bindAbstractTarget">
        update bdm_abstract_device
        set  target_id= #{request.targetId} , target_type = #{request.targetType}
        where deleted = 0 and unique_id = #{request.uniqueId}
    </update>

</mapper>

