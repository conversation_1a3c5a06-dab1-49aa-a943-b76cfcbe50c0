package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bi.dto.DeviceModelCount;
import com.xh.vdm.bi.dto.DeviceModelInfo;
import com.xh.vdm.biapi.entity.BdmDeviceStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmDeviceLink)表数据库访问层
 */
@Mapper
public interface BdmDeviceStatusMapper extends BaseMapper<BdmDeviceStatus> {

	/**
	 * 统计在线终端个数。
	 * @param account 账号
	 * @param deptIds 监管部门id列表
	 * @return 在线终端数
	 */
	long countOnlineDevices(@Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 获取用户监管的设备型号在线统计信息。
	 * @param account 用户账号
	 * @param deptIds 用户监管的部门id列表
	 * @return 按型号分类的终端在线数
	 */
	List<DeviceModelCount> getDeviceModelOnlineByUserAuth(@Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 获取用户监管的设备型号在线统计信息。
	 * @param account 用户账号
	 * @param deptIds 用户监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param models 终端型号列表
	 * @param usage 使用类型
	 * @param district 行政区划编码
	 * @return 按型号分类的终端在线数
	 */
	List<DeviceModelInfo> getDevOnlineInfoWith(@Param("account") String account, @Param("deptIds") String deptIds,
											   @Param("deptId") Long deptId, @Param("models") List<String> models,
											   @Param("usage") Byte usage, @Param("district") String district);

	/**
	 * 获取用户监管的设备型号在线统计数量。
	 * @param account 用户账号
	 * @param deptIds 用户监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param models 终端型号列表
	 * @param usage 使用类型
	 * @param district 行政区划编码
	 * @return 按型号分类的终端在线数
	 */
	List<DeviceModelCount> getDevOnlineCntWith(@Param("account") String account, @Param("deptIds") String deptIds,
													@Param("deptId") Long deptId, @Param("models") List<String> models,
													@Param("usage") Byte usage, @Param("district") String district);

	/**
	 * 统计终端总数。
	 * @param specificity 终端特殊性
	 * @param online 0-在线，1-下线
	 * @param account 账号
	 * @param deptIds 监管部门id列表
	 * @return 符合条件的终端总数
	 */
	long countByUserAuth(@Param("specificity") Byte specificity, @Param("online") Byte online,
						 @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 统计指定条件下的终端id列表。
	 * @param specificity 终端特殊性
	 * @param online 0-在线，1-下线
	 * @param account 账号
	 * @param deptIds 监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param usage 使用类型
	 * @param district 行政区划编码
	 * @return 符合条件的终端id列表
	 */
	List<Long> getDeviceIdsWith(@Param("specificity") Byte specificity, @Param("online") Byte online,
						 @Param("account") String account, @Param("deptIds") String deptIds,
						 @Param("deptId") Long deptId, @Param("usage") Byte usage, @Param("district") String district);

	/**
	 * 统计终端总数。
	 * @param specificity 终端特殊性
	 * @param online 0-在线，1-下线
	 * @param account 账号
	 * @param deptIds 监管部门id列表
	 * @param deptId 指定部门id(与参数deptIds互斥)
	 * @param usage 使用类型
	 * @param district 行政区划编码
	 * @return 符合条件的终端总数
	 */
	long countByUserWith(@Param("specificity") Byte specificity, @Param("online") Byte online,
						 @Param("account") String account, @Param("deptIds") String deptIds,
						 @Param("deptId") Long deptId, @Param("usage") Byte usage, @Param("district") String district);
}

