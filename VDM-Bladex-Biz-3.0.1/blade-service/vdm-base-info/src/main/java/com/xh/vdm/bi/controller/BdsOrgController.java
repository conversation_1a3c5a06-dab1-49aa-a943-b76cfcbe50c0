package com.xh.vdm.bi.controller;

import com.xh.vdm.bi.service.gnProd.IBdsOrgService;
import com.xh.vdm.bi.vo.response.gnProd.BdsOrgVO;
import org.springblade.common.utils.StringUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/gnProd")
@Validated
public class BdsOrgController {
	@Resource
	private IBdsOrgService bdsOrgService;

	/**
	 * 组织树查找。
	 * @param sectors 产业板块列表
	 * @return 符合条件的组织树
	 */
	@GetMapping(value = "/treeWith",produces = "application/json;charset=UTF-8")
	public R<List<BdsOrgVO>> treeWith(@RequestParam("sectors") int[] sectors, BladeUser user) {
		if (null == user || StringUtils.isEmpty(user.getAccount()))
			return R.fail("非法请求：账号信息缺失！");

		List<String> secs = null;
		if (null != sectors && sectors.length > 0) {
			secs = new ArrayList<>();
			for (Integer i : sectors) {
				secs.add(i.toString());
			}
		}

		List<BdsOrgVO> tree = bdsOrgService.getBdsOrgWith(secs);
		return R.data(tree);
	}
}
