package com.xh.vdm.bi.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.service.IBdmVirtualTargetService;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.utils.DictUtil;
import com.xh.vdm.biapi.entity.BdmVirtualTarget;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.entity.DataAuthCE;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description: 待分配终端管理
 */
@RestController
@RequestMapping("/virtual/target")
public class BdmVirtualTargetController {
	/**
	 * 服务对象
	 */
	@Resource
	private IBdmVirtualTargetService bdmVirtualTargetService;
	@Resource
	private CETokenUtil ceTokenUtil;

	@Resource
	private DictUtil dictUtil;

	/**
	 * 分页查询
	 *
	 * @param bdmVirtualTarget 筛选条件
	 * @param query      分页对象
	 * @return 查询结果
	 */
	//pre_auth_test
	@PostMapping("/list")
	public R<IPage<BdmVirtualTarget>> queryByPage(@RequestBody BdmVirtualTarget bdmVirtualTarget, Query query, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		IPage<BdmVirtualTarget> page = this.bdmVirtualTargetService.queryByPage(bdmVirtualTarget, query, ceDataAuth);
		enrichByDict(page.getRecords());
		return R.data(page);
	}
	private void enrichByDict(List<BdmVirtualTarget> records) {
		if (records.isEmpty()) {
			return;
		}
		Map<String, String> deviceTypeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_DEVICE_TYPE);
		for (BdmVirtualTarget response : records) {
			response.setCategoryName((response.getCategory() == null) ? "" : deviceTypeMap.getOrDefault(response.getCategory().toString(), ""));
		}
	}

}

