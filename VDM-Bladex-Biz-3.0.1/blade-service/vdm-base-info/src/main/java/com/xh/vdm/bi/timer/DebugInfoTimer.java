package com.xh.vdm.bi.timer;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;

//@Component
@Slf4j
public class DebugInfoTimer {

	//@Scheduled(cron="*/5 * * * * ?")
	public void showThreadInfo(){
		ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
		long threadCount = threadMXBean.getThreadCount();
		long daemonThreadCount = threadMXBean.getDaemonThreadCount();
		long peakThreadCount = threadMXBean.getPeakThreadCount();
		long totalStartedThreadCount = threadMXBean.getTotalStartedThreadCount();
		log.info("daemon线程数量：" + daemonThreadCount);
		log.info("峰值线程数量为：" + peakThreadCount);
		log.info("启动的线程总数为："+totalStartedThreadCount);
		log.info("当前线程数量: " + threadCount);
		long[] threadIds = threadMXBean.getAllThreadIds();
		ThreadInfo[] threadInfos = threadMXBean.getThreadInfo(threadIds);
		int runnableCount = 0;
		int blockedCount = 0;
		int waitingCount = 0;
		int timeWaitingCount = 0;
		int terminatedCount = 0;
		for (ThreadInfo info : threadInfos) {
			if (info != null && info.getThreadState() == Thread.State.RUNNABLE) {
				runnableCount++;
			}
			if (info != null && info.getThreadState() == Thread.State.BLOCKED) {
				blockedCount++;
			}
			if (info != null && info.getThreadState() == Thread.State.WAITING) {
				waitingCount++;
			}
			if (info != null && info.getThreadState() == Thread.State.TIMED_WAITING) {
				timeWaitingCount++;
			}
			if (info != null && info.getThreadState() == Thread.State.TERMINATED) {
				terminatedCount++;
			}
		}
		log.info("running状态的线程数量："+runnableCount);
		log.info("blocked状态的线程数量："+blockedCount);
		log.info("waiting状态的线程数量："+waitingCount);
		log.info("time_waiting状态的线程数量："+timeWaitingCount);
		log.info("terminated状态的线程数量："+terminatedCount);
	}
}
