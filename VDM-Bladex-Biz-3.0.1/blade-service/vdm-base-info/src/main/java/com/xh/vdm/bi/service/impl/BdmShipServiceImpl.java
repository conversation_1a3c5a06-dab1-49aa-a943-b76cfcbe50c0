package com.xh.vdm.bi.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.converter.BdmAbstractTargetConverter;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.mapper.BdmShipMapper;
import com.xh.vdm.bi.service.BdmShipService;
import com.xh.vdm.bi.service.IBdmAbstractDeviceService;
import com.xh.vdm.bi.service.IBdmAbstractTargetService;
import com.xh.vdm.bi.service.RnssDeviceService;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.ShipRequest;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.ShipResponse;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import com.xh.vdm.biapi.entity.BdmShip;
import com.xh.vdm.interManager.feign.IMessageClient;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (BdmShip)表服务实现类
 */
@Service
public class BdmShipServiceImpl extends ServiceImpl<BdmShipMapper, BdmShip> implements BdmShipService {
	@Resource
	private BdmShipMapper bdmShipMapper;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private IMessageClient messageClient;
	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
     * 分页查询
     *
     * @param shipRequest 筛选条件
     * @param query       分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	@Override
	public IPage<ShipResponse> queryByPage(ShipRequest shipRequest, Query query, DataAuthCE ceDataAuth) {
		IPage<ShipResponse> page = new Page<>(query.getCurrent(), query.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.bdmShipMapper.queryAll(shipRequest, page, response.getAccount(), response.getOrgList());
	}

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmShip insert(ShipRequest request) {

		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker( BaseInfoConstants.SHIP_TARGET_TYPE, BaseInfoConstants.ZERO,BaseInfoConstants.ZERO);
		request.setId(targetId.nextId());
		request.setCreateTime(new Date());
		request.setCreateAccount(AuthUtil.getUserAccount());
		this.bdmShipMapper.insert(request);

		BdmShip ship = this.getBaseMapper().selectById(request.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(ship, abstractTarget);
		abstractTargetService.save(abstractTarget);

		Map<String, String> map = new HashMap<>();
		String key = ship.getTargetType() + "-" + ship.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", ship.getNumber());
		innerMap.put("targetType", ship.getTargetType());
		innerMap.put("targetCategory", ship.getCategory());
		innerMap.put("deptId", ship.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setTargetId(ship.getId());
		deviceInfo.setTargetName(ship.getNumber());
		deviceInfo.setDeptId(ship.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_ship", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("货船信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.ship(CommonConstant.OPER_TYPE_ADD, ship);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return ship;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmShip update(ShipRequest request) {
		BdmShip shipInDB = this.getBaseMapper().selectById(request.getId());

		BdmShip bdmShip = new BdmShip();
		bdmShip.setId(request.getId());
		bdmShip.setNumber(request.getNumber() != null ? request.getNumber() : "");
		bdmShip.setTargetType(request.getTargetType() != null ? request.getTargetType() : TargetTypeEnum.SHIP.getSymbol());
		bdmShip.setCategory(request.getCategory() != null ? request.getCategory() : 0);
		bdmShip.setName(request.getName() != null ? request.getName() : "");
		bdmShip.setNameEn(request.getNameEn() != null ? request.getNameEn() : "");
		bdmShip.setMmsi(request.getMmsi() != null ? request.getMmsi() : "");
		bdmShip.setImoNumber(request.getImoNumber() != null ? request.getImoNumber() : "");
		bdmShip.setCallSign(request.getCallSign() != null ? request.getCallSign() : "");
		bdmShip.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		bdmShip.setMaxGross(request.getMaxGross() != null ? request.getMaxGross() : 0.0f);
		bdmShip.setNet(request.getNet() != null ? request.getNet() : 0.0f);
		bdmShip.setDisplcement(request.getDisplcement() != null ? request.getDisplcement() : 0.0f);
		bdmShip.setLength(request.getLength() != null ? request.getLength() : 0);
		bdmShip.setBreadth(request.getBreadth() != null ? request.getBreadth() : 0);
		bdmShip.setDepth(request.getDepth() != null ? request.getDepth() : 0);
		bdmShip.setDraught(request.getDraught() != null ? request.getDraught() : 0);
		bdmShip.setCruiseSpeed(request.getCruiseSpeed() != null ? request.getCruiseSpeed() : 0.0f);
		bdmShip.setUpdateTime(new Date());
		this.bdmShipMapper.update(bdmShip);

		BdmShip ship = this.getBaseMapper().selectById(bdmShip.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(ship, abstractTarget);
		abstractTargetService.updateById(abstractTarget);

		Map<String, String> map = new HashMap<>();
		String key = ship.getTargetType() + "-" + ship.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", ship.getNumber());
		innerMap.put("targetType", ship.getTargetType());
		innerMap.put("targetCategory", ship.getCategory());
		innerMap.put("deptId", ship.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setTargetId(ship.getId());
		deviceInfo.setTargetName(ship.getNumber());
		deviceInfo.setDeptId(ship.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_ship", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("货船信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.ship(CommonConstant.OPER_TYPE_UPDATE, ship);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		//@zhouxw modify 2025-06-27 修改监管对象，终端不再同步更改组织
		/*if(shipInDB.getDeptId().equals(ship.getDeptId())){
			// 对于RnssDevice的更新
			rnssDeviceService.updateDept(ship.getId(),ship.getTargetType(),ship.getDeptId());

			// 对于BdmAbstractDevice的更新
			abstractDeviceService.updateDept(ship.getId(),ship.getTargetType(),ship.getDeptId());
		}*/

		return ship;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<ShipRequest> importExcel(List<ShipRequest> list) {
		//重复的的数据
		List<ShipRequest> duplicates = getDuplicateRequests(list);
		List<ShipRequest> requests = new ArrayList<>(list);
		// 去重后的数据（去除所有重复数据）
		requests.removeAll(duplicates);

		if (!requests.isEmpty()) {
			List<String> numberList = requests.stream()
					.map(ShipRequest::getNumber)
					.collect(Collectors.toList());
			//数据库的数据
			QueryWrapper<BdmShip> queryWrapper = new QueryWrapper<>();
			if (!numberList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(queryWrapper, "number", numberList);
			}
			queryWrapper.eq("deleted",0);
			queryWrapper.select("number");
			List<String> numberExitList = this.bdmShipMapper.selectList(queryWrapper)
					.stream()
					.map(BdmShip::getNumber)
					.collect(Collectors.toList());

			Map<String, String> map = new HashMap<>();
			List<BdmShip> ships = new ArrayList<>();
			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.SHIP_TARGET_TYPE, BaseInfoConstants.ZERO,BaseInfoConstants.ZERO);
			List<Long> arrayList = new ArrayList<>();
			List<BdmAbstractTarget> bdmAbstractTargets = new ArrayList<>();

			for (ShipRequest request : requests) {
				if (numberExitList.contains(request.getNumber())) {
					request.setMsg("船舶编号已存在");
					duplicates.add(request);
				} else {
					BdmShip ship = new BdmShip();
					ship.setId(targetId.nextId());
					ship.setNumber(request.getNumber() != null ? request.getNumber() : "");
					ship.setTargetType(TargetTypeEnum.SHIP.getSymbol());
					ship.setCategory(request.getCategory() != null ? request.getCategory() : 0);
					ship.setName(request.getName() != null ? request.getName() : "");
					ship.setNameEn(request.getNameEn() != null ? request.getNameEn() : "");
					ship.setMmsi(request.getMmsi() != null ? request.getMmsi() : "");
					ship.setImoNumber(request.getImoNumber() != null ? request.getImoNumber() : "");
					ship.setCallSign(request.getCallSign() != null ? request.getCallSign() : "");
					ship.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
					ship.setMaxGross(request.getMaxGross() != null ? request.getMaxGross() : 0.0f);
					ship.setNet(request.getNet() != null ? request.getNet() : 0.0f);
					ship.setDisplcement(request.getDisplcement() != null ? request.getDisplcement() : 0.0f);
					ship.setLength(request.getLength() != null ? request.getLength() : 0);
					ship.setBreadth(request.getBreadth() != null ? request.getBreadth() : 0);
					ship.setDepth(request.getDepth() != null ? request.getDepth() : 0);
					ship.setDraught(request.getDraught() != null ? request.getDraught() : 0);
					ship.setCruiseSpeed(request.getCruiseSpeed() != null ? request.getCruiseSpeed() : 0.0f);
					ship.setCreateTime(new Date());
					ship.setDeleted(0);
					ships.add(ship);

					request.setId(ship.getId());
					arrayList.add(ship.getId());

					String key = ship.getTargetType() + "-" + ship.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", ship.getNumber());
					innerMap.put("targetType", ship.getTargetType());
					innerMap.put("targetCategory", ship.getCategory());
					innerMap.put("deptId", ship.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}

					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(ship, abstractTarget);
					bdmAbstractTargets.add(abstractTarget);
				}
			}

			if (!ships.isEmpty()) {
				//将剩余的数据批量插入数据库
				MapperUtils.splitListByCapacity(ships, MapperUtils.DEFAULT_CAPACITY)
					.forEach(shipList -> this.bdmShipMapper.insertBatch(shipList));

				MapperUtils.splitListByCapacity(bdmAbstractTargets, MapperUtils.DEFAULT_CAPACITY)
					.forEach(abstractTargetList -> this.abstractTargetService.insertBatch(abstractTargetList));

				//redis缓存
				if(!map.isEmpty()){
					redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
				}

				//kafka推送
				BdmShip last = ships.get(ships.size() - 1);
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.IMPORT);
				deviceInfo.setTargetId(last.getId());
				Set<Long> ids = ships.stream().map(BdmShip::getId).collect(Collectors.toSet());
				deviceInfo.setTargetIds(ids);
				deviceInfo.setTargetType(last.getTargetType());
				deviceInfo.setTargetName(last.getNumber());
				deviceInfo.setDeptId(last.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_ship", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("货船信息更新消息发送到kafka失败", e);
				}

				//messageClient推送
				QueryWrapper<BdmShip> wrapper = new QueryWrapper<>();
				if (!arrayList.isEmpty()) {
					MybatisPlusWrapperUitls.longListIn(wrapper, "id", arrayList);
					List<BdmShip> shipList = baseMapper.selectList(wrapper);
					messageClient.shipBatch(CommonConstant.OPER_TYPE_ADD, shipList);
				}
			}
		}
		return duplicates;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest) {
		int update = bdmShipMapper.batchUpdate(batchUpdateRequest.getIds(),batchUpdateRequest.getDeptId());
		if (update == 0) {
			return false;
		}
		//查询出所有的本次要更新的字段
		List<BdmShip> bdmShipList = bdmShipMapper.selectList(new LambdaQueryWrapper<BdmShip>().in(BdmShip::getId, batchUpdateRequest.getIds()));
		//转换
		List<BdmAbstractTarget> bdmAbstractTargetList = bdmShipList.stream().map(BdmAbstractTargetConverter::toBdmShip).collect(Collectors.toList());
		//更新主表
		abstractTargetService.saveOrUpdateBatch(bdmAbstractTargetList);
		//批量更新redis和kafka
		batchProcess(bdmShipList);
		if (update > 0){
			return true;
		}
		return false;
	}

	/**
	 * 批量更新redis
	 * 批量发送kafka
	 */
	private void batchProcess(List<BdmShip> bdmShipList){
		for (BdmShip ship : bdmShipList) {
			Map<String, String> map = new HashMap<>();
			String key = ship.getTargetType() + "-" + ship.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("targetName", ship.getNumber());
			innerMap.put("targetType", ship.getTargetType());
			innerMap.put("targetCategory", ship.getCategory());
			innerMap.put("deptId", ship.getDeptId());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.OTHER);
			deviceInfo.setTargetId(ship.getId());
			deviceInfo.setTargetName(ship.getNumber());
			deviceInfo.setDeptId(ship.getDeptId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_ship", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("货船信息更新消息发送到kafka失败", e);
			}
			//messageClient
			try {
				messageClient.ship(CommonConstant.OPER_TYPE_UPDATE, ship);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

	public List<ShipRequest> getDuplicateRequests(List<ShipRequest> list) {
		Map<String, Long> numberCountMap = list.stream()
			.map(ShipRequest::getNumber)
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<String> duplicateNumbers = numberCountMap.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		return list.stream()
			.filter(ship -> duplicateNumbers.contains(ship.getNumber()))
			.peek(ship -> ship.setMsg("船舶编号重复"))
			.collect(Collectors.toList());
	}
}
