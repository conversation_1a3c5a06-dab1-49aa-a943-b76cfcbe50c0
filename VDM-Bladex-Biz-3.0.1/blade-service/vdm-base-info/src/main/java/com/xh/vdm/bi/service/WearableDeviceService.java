package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.WearableDeviceRequest;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.WearableDeviceResponse;
import com.xh.vdm.bi.vo.response.WorkerBingResponse;
import com.xh.vdm.biapi.entity.BdmWearableDevice;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * @Description: 北斗穿戴实体管理
 */
public interface WearableDeviceService extends IService<BdmWearableDevice> {

	/**
	 * 分页查询
	 *
	 * @param request 筛选条件
	 * @param ceDataAuth
	 * @return 查询结果
	 */
	IPage<WearableDeviceResponse> queryByPage(WearableDeviceRequest request, DataAuthCE ceDataAuth);

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmWearableDevice insert(WearableDeviceRequest request);

	BdmWearableDevice insertWearableDevice(WearableDeviceRequest request);

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	BdmWearableDevice update(WearableDeviceRequest request);

	BdmWearableDevice updateWearableDevice(WearableDeviceRequest request);

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	boolean deleteById(Long id);

	/**
	 * 通过主键删除数据
	 * @param ids
	 * @return
	 */
	boolean deleteByIds(Long[] ids);

	void connectWorkerTerminal(PersonTerminalRequest personTerminalRequest, Long id, Integer targetType, String targetName, Long deptId);

	void updateBatchByTerminalId(List<PersonTerminalRequest> requestList, Long id, Integer targetType, String targetName, Long deptId);

	IPage<PersonNoBingResponse> select(DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds);

	List<WorkerBingResponse>  selectByWorkId(Long id, Integer targetType);

    void updateByWorkerId(Long id, Integer targetType);

	List<PersonNoBingResponse> selectBindByWorkId(Long id, Integer targetType);

	List<WearableDeviceRequest> importExcel(List<WearableDeviceRequest> list, Long userId);

    void deleteByTargetIds(Long[] ids, Integer targetType);

    long countByUserRole(DataAuthCE ceDataAuth);

	void updateDept(Long id, Integer targetType, Long deptId);

	int bindTarget(Long deviceId, Long targetId, Integer targetType,String targetName);
}
