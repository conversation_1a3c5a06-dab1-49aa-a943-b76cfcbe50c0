package com.xh.vdm.bi.entity.bdCheck;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(schema = "bd_check")
public class BdcTerminal implements Serializable {

	private static final long serialVersionUID = 1L;

	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 终端类别
	 * 设备类别（M：模组，T：终端）
	 */
	private String deviceCate;

	/**
	 * 终端类型
	 * 设备类型（B：基础设备，N：导航定位设备，C：短报文通信设备，T：授时设备，D：探测监测设备，M：定位模组）
	 */
	private String deviceType;

	//设备编号
	private String deviceNo;

	//序列号
	private String deviceSeq;

	//imei号
	private String imei;

	//北斗芯片序列号
	private String chipSeq;

	//sim号
	private String sim;

	//设备赋码值
	private String deviceNum;

	//设备赋码签名
	private String deviceNumSign;

	//终端型号
	private String deviceModel;

	//设备厂商
	private String manufacturer;

	//通信协议
	private String protocol;

	//检测结果（0：未检测，1：合格，2：不合格）
	private Integer testResult;

	//接口检测描述
	private String testResMessage;

	// 入网状态（0：未正式入网，1：已正式入网）
	@TableField("formal")
	private Byte formal;

	// 赋码结果（0：未赋码，1：成功，2：失败）
	@TableField("code_result")
	private Integer codeResult;

	// 赋码结果描述
	@TableField("code_res_message")
	private String codeResMessage;

	// 赋码时间
	@TableField("code_time")
	private Date codeTime;

	// 赋码所使用的赋码机
	@TableField("code_machine")
	private String codeMachine;

	//所属企业id
	@JsonSerialize(using = ToStringSerializer.class)
	private Long companyId;

	//对应报告id
	@JsonSerialize(using = ToStringSerializer.class)
	private Long reportId;


	/**
	 * 北斗检测识别结果：0 未开始或检测中   1 通过    2 不通过
	 */
	private String checkResult;

	//终端检测结果描述
	private String checkResMessage;

	//检测数据总量
	private Long checkTotalDataCount;

	//检测为北斗数据总量
	@TableField("check_bd_data_count")
	private Long checkBDDataCount;

	//检测为非北斗数据总量
	@TableField("check_non_bd_count")
	private Long checkNonBDCount;

	//检测开始时间
	@TableField("check_start_date")
	private Date checkStartDate;

	//检测结束时间
	@TableField("check_end_date")
	private Date checkEndDate;

	//是否参与检测 0 不参与检测   1 参与检测
	@TableField("is_in_check")
	private Integer isInCheck;

	//检测时间（接口协议检测）
	private Date testTime;

	//是否删除
	private Integer isDel;

	private Date createTime;

	private Date updateTime;


}
