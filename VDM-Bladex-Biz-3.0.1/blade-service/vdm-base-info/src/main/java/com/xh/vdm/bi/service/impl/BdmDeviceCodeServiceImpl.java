package com.xh.vdm.bi.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.entity.BdmDeviceCode;
import com.xh.vdm.bi.mapper.BdmDeviceCodeMapper;
import com.xh.vdm.bi.service.BdmDeviceCodeService;
import com.xh.vdm.bi.vo.request.DeviceCodeRequest;
import org.springblade.core.mp.support.Query;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * (BdmDeviceCode)表服务实现类
 */
@Service
public class BdmDeviceCodeServiceImpl extends ServiceImpl<BdmDeviceCodeMapper, BdmDeviceCode> implements BdmDeviceCodeService {
	@Resource
	private BdmDeviceCodeMapper bdmDeviceCodeMapper;
	@Value("${current.schema}")
	String schema;

    @Override
	@Transactional(rollbackFor = Exception.class)
    public void updateActivatedByDeviceNum(List<String> deviceNumList) {
		this.bdmDeviceCodeMapper.updateActivatedByDeviceNum(deviceNumList);
    }

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateActivated(String deviceNum) {
		this.bdmDeviceCodeMapper.updateActivated(deviceNum);
	}

	@Override
	public IPage<BdmDeviceCode> select(DeviceCodeRequest deviceCodeRequest, Query query) {
		IPage<BdmDeviceCode> page = new Page<>(query.getCurrent(),query.getSize());
		return this.bdmDeviceCodeMapper.select(deviceCodeRequest,page,schema);
	}

	@Override
	public List<String> selectDeviceNum() {
		return this.bdmDeviceCodeMapper.selectDeviceNum();
	}
}
