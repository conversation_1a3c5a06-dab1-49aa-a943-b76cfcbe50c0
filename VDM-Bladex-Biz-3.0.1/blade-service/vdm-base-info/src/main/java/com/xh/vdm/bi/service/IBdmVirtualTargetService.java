package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.biapi.entity.BdmVirtualTarget;
import org.springblade.core.mp.support.Query;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmVirtualTarget)表服务接口
 */
public interface IBdmVirtualTargetService extends IService<BdmVirtualTarget> {

	/**
     * 分页查询
     *
     * @param bdmVirtualTarget 筛选条件
     * @param query            分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<BdmVirtualTarget> queryByPage(BdmVirtualTarget bdmVirtualTarget, Query query, DataAuthCE ceDataAuth);

	void insertBatch(List<BdmVirtualTarget> virtualTargetList);

	/**
	 * 根据序列号批量逻辑删除
	 * @param uniqueIds
	 */
    void updateByUniqueId(List<String> uniqueIds);

	void updateStatus(String uniqueId);

	void restoreStatus(Long targetId,Integer deleted);

	/**
	 * 根据序列号批量恢复
	 * @param uniqueIdList
	 */
	void updateBatch(List<String> uniqueIdList);
}
