package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * (BdmVisitor)实体类
 */
@Data
@ExcelIgnoreUnannotated
public class VisitorRequest implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	@ExcelProperty(value = "姓名")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String name;

	@NotNull(message = "身份证号不能为空")
	@ExcelProperty(value = "身份证号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String idNumber;

	private Integer targetType;

	private Long deptId;


	private Integer post;

	private Integer industry;

	private String phone;

	private String company;

	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date validFrom;
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date validTo;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;
	/**
	 * 绑定终端类型
	 */
	private Integer terminalType;
	/**
	 * 绑定序列号
	 */
	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;
	/**
	 * 账号状态
	 */
	private Integer status;

	@ExcelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String msg;

	/**	 导出动态表头中文名*/
	private List headNameList;

	/**	 导出动态表头字段名*/
	private List columnNameList;

	/**	 导出数据的id集合*/
	private List<Long> ids;

	@ExcelIgnore
	private String createAccount;
}

