<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmMiningTruckMapper">

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryByPage" resultType="com.xh.vdm.bi.vo.response.MiningTruckResponse">
        select
        mt.id,
        mt.number,
        mt.category,
        mt.target_type,
        mt.dept_id,
        mt.vin,
        mt.max_power,
        mt.manufacturer,
        mt.rated_load,
        mt.model,
        mt.create_time,
        mt.update_time,
        mt.deleted,
        d.dept_name,
        array_to_string(ARRAY ( SELECT UNNEST ( ARRAY_AGG ( rd.category ) ) ), ',' ) AS terminalCategories,
        rd.unique_id,
        rd.device_num
        from bdm_mining_truck mt
        left join blade_dept d on mt.dept_id = d.id
        left join bdm_rnss_device rd on mt.id = rd.target_id AND rd.deleted = 0 and rd.target_type = mt.target_type
        where mt.deleted = 0
        <if test="request.ids != null and request.ids.size() gt 0 ">
            and mt.id in
            <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="request.ids == null or request.ids == ''">
            <if test="request.number != null and request.number != ''">
                and mt.number like concat('%',#{request.number},'%')
            </if>
            <if test="request.category != null">
                and mt.category = #{request.category}
            </if>
            <if test="request.targetType != null">
                and mt.target_type = #{request.targetType}
            </if>
            <if test="request.deptId != null">
                and mt.dept_id = #{request.deptId}
            </if>
            <if test="request.vin != null and request.vin != ''">
                and mt.vin like concat('%',#{request.vin},'%')
            </if>
            <if test="request.maxPower != null">
                and mt.max_power = #{request.maxPower}
            </if>
            <if test="request.manufacturer != null and request.manufacturer != ''">
                and mt.manufacturer like concat('%',#{request.manufacturer},'%')
            </if>
            <if test="request.ratedLoad != null">
                and mt.rated_load = #{request.ratedLoad}
            </if>
            <if test="request.model != null and request.model != ''">
                and mt.model like concat('%',#{request.model},'%')
            </if>
            <if test="request.uniqueId != null and request.uniqueId != ''">
                and rd.unique_id like concat('%',#{request.uniqueId},'%')
            </if>
            <if test="request.deviceNum != null and request.deviceNum != ''">
                and rd.device_num like concat('%',#{request.deviceNum},'%')
            </if>
            <if test="deptIds != null and deptIds != ''">
                and d.id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and mt.create_account = #{account}
            </if>
        </if>
        GROUP BY
        mt.id,mt.number,mt.category,mt.target_type,mt.dept_id,mt.vin,mt.max_power,mt.manufacturer,mt.rated_load,mt.model,
        mt.create_time,mt.update_time,mt.deleted,d.dept_name,rd.unique_id,rd.device_num
        order by create_time desc
    </select>

    <update id="update">
        UPDATE bdm_mining_truck
        <set>
            <if test="null != number">number = #{number},</if>
            <if test="null != category">category = #{category},</if>
            <if test="null != targetType">target_type = #{targetType},</if>
            <if test="null != deptId">dept_id = #{deptId},</if>
            <if test="null != vin">vin = #{vin},</if>
            <if test="null != maxPower">max_power = #{maxPower},</if>
            <if test="null != manufacturer">manufacturer = #{manufacturer},</if>
            <if test="null != ratedLoad">rated_load = #{ratedLoad},</if>
            <if test="null != model">model = #{model},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != deleted">deleted = #{deleted}</if>
        </set>
        WHERE id = #{id}
    </update>


    <update id="deleteByIds">
        update bdm_mining_truck
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

    <insert id="insertBatch">
        insert into bdm_mining_truck( id,number,category,dept_id,create_time,vin,max_power,manufacturer,rated_load,model)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id}, #{entity.number}, #{entity.category}, #{entity.deptId}, now(), #{entity.vin}, #{entity.maxPower},
            #{entity.manufacturer}, #{entity.ratedLoad}, #{entity.model})
        </foreach>
    </insert>

    <update id="batchUpdate">
        UPDATE bdm_mining_truck
        <set>
            dept_id = #{deptId}
        </set>
        <where>
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </update>

</mapper>

