<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.gnProd.BdsOrgMapper">
    <resultMap id="bdsOrgLite" type="com.xh.vdm.bi.entity.gnProd.BdsOrgLite">
        <result property="id" column="id"/>
        <result property="parentId" column="pid"/>
        <result property="orgCode" column="org_code"/>
        <result property="orgName" column="org_name"/>
    </resultMap>

    <select id="getBdsOrgWith" resultMap="bdsOrgLite">
        select id,pid,org_code,org_name from bds_org where delete_flag = '0'
        <if test="orgCodes != null and orgCodes != ''">
            and org_code = any(${orgCodes})
        </if>
        <if test="sectors != null and sectors.size() > 0">
            and industrial_sector in
            <foreach collection="sectors" item="sector" separator="," open="(" close=")">
                #{sector}
            </foreach>
        </if>
    </select>
</mapper>
