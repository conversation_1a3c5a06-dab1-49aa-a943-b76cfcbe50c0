package com.xh.vdm.bi.vo.request;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;

/**
 * (BdmDeviceLedger)实体类
 */
@Data
public class BdmDeviceLedgerRequest implements Serializable {
	@Compare("终端赋码号")
	private String deviceNum;
	@Compare("序列号")
	private String uniqueId;
	@Compare("终端型号")
	private String model;
	@Compare("IMEI号")
	private String imei;
	@Compare("北斗芯片序列号")
	private String bdChipSn;
	@Compare("生产厂商")
	private String vendor;
	@Compare("终端类型")
	private Integer category;
	@Compare("管理状态")
	private Integer storageState;
	@Compare("使用单位")
	private Long userDeptId;
	@Compare("终端类别")
	private Integer deviceType;

	/**
	 * 1 -- 非出入库管理请求
	 */
	private Integer type;

	private Integer status;
}

