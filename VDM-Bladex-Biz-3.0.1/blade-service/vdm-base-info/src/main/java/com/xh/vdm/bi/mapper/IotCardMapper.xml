<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.IotCardMapper">

    <resultMap type="com.xh.vdm.biapi.entity.BdmIotCard" id="BdmIotCardMap">
        <result property="id" column="id"/>
        <result property="number" column="number"/>
        <result property="cardNumber" column="card_number"/>
        <result property="iccid" column="iccid"/>
        <result property="imsi" column="imsi"/>
        <result property="operator" column="operator"/>
        <result property="holder" column="holder"/>
        <result property="issueTime" column="issue_time"/>
        <result property="status" column="status"/>
        <result property="activationTime" column="activation_time"/>
        <result property="expire" column="expire"/>
        <result property="dataPlan" column="data_plan"/>
        <result property="packetSize" column="packet_size"/>
        <result property="category" column="category"/>
        <result property="deviceId" column="device_id"/>
        <result property="deviceType" column="device_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="deleted" column="deleted"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>

    <sql id="Base_Column_List">
        bic.id,
        bic.number,
        bic.card_number,
        bic.iccid,
        bic.imsi,
        bic.operator,
        bic.holder,
        bic.issue_time,
        bic.status,
        bic.activation_time,
        bic.expire,
        bic.data_plan,
        bic.packet_size,
        bic.category,
        bic.device_id,
        bic.device_type,
        bic.create_time,
        bic.update_time,
        bic.deleted,
        bic.dept_id,
        bd.dept_name
    </sql>

    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.BdmIotCardExportResponse">
        select
        <include refid="Base_Column_List"/>
        from bdm_iot_card bic
        left join blade_dept bd on bic.dept_id = bd.id
        where bic.deleted = 0
        <if test="request.ids != null and request.ids.size() gt 0 ">
            and bic.id in
            <foreach collection="request.ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="request.ids == null or request.ids == ''">
            <if test="request.number != null and request.number != ''">
                and bic.number like concat('%',#{request.number},'%')
            </if>
            <if test="request.iccid != null and request.iccid != ''">
                and bic.iccid like concat('%',#{request.iccid},'%')
            </if>
            <if test="deptIds != null and deptIds != ''">
                and bic.dept_id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and bic.create_account = #{account}
            </if>
        </if>
        order by create_time desc
    </select>

    <!--查询单个-->
    <select id="queryById" resultType="com.xh.vdm.bi.vo.response.BdmIotCardExportResponse">
        select
        <include refid="Base_Column_List"/>
        from bdm_iot_card bic
        left join blade_dept bd on bic.dept_id = bd.id
        where bic.id = #{id}
    </select>

    <!--新增所有列-->
    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO bdm_iot_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != number and '' != number">
                number,
            </if>
            <if test="null != cardNumber and '' != cardNumber">
                card_number,
            </if>
            <if test="null != iccid and '' != iccid">
                iccid,
            </if>
            <if test="null != imsi and '' != imsi">
                imsi,
            </if>
            <if test="null != operator">
                operator,
            </if>
            <if test="null != holder and '' != holder">
                holder,
            </if>
            <if test="null != issueTime">
                issue_time,
            </if>
            <if test="null != status">
                status,
            </if>
            <if test="null != activationTime">
                activation_time,
            </if>
            <if test="null != expire">
                expire,
            </if>
            <if test="null != dataPlan">
                data_plan,
            </if>
            <if test="null != packetSize">
                packet_size,
            </if>
            <if test="null != category">
                category,
            </if>
            <if test="null != deviceId">
                device_id,
            </if>
            <if test="null != deviceType">
                device_type,
            </if>
            <if test="null != createTime">
                create_time,
            </if>
            <if test="null != updateTime">
                update_time,
            </if>
            <if test="null != deleted">
                deleted,
            </if>
            <if test="null != deptId">
                dept_id,
            </if>
            <if test="null != virtuality">
                virtuality,
            </if>
            <if test="null != createAccount">
                create_account
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != number and '' != number">
                #{number},
            </if>
            <if test="null != cardNumber and '' != cardNumber">
                #{cardNumber},
            </if>
            <if test="null != iccid and '' != iccid">
                #{iccid},
            </if>
            <if test="null != imsi and '' != imsi">
                #{imsi},
            </if>
            <if test="null != operator">
                #{operator},
            </if>
            <if test="null != holder and '' != holder">
                #{holder},
            </if>
            <if test="null != issueTime">
                #{issueTime},
            </if>
            <if test="null != status">
                #{status},
            </if>
            <if test="null != activationTime">
                #{activationTime},
            </if>
            <if test="null != expire">
                #{expire},
            </if>
            <if test="null != dataPlan">
                #{dataPlan},
            </if>
            <if test="null != packetSize">
                #{packetSize},
            </if>
            <if test="null != category">
                #{category},
            </if>
            <if test="null != deviceId">
                #{deviceId},
            </if>
            <if test="null != deviceType">
                #{deviceType},
            </if>
            <if test="null != createTime">
                #{createTime},
            </if>
            <if test="null != updateTime">
                #{updateTime},
            </if>
            <if test="null != deleted">
                #{deleted},
            </if>
            <if test="null != deptId">
                #{deptId},
            </if>
            <if test="null != virtuality">
                #{virtuality},
            </if>
            <if test="null != createAccount">
                #{createAccount,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update bdm_iot_card
        <set>
            <if test="number != null">
                number = #{number},
            </if>
            <if test="null != cardNumber">
                card_number = #{cardNumber},
            </if>
            <if test="iccid != null">
                iccid = #{iccid},
            </if>
            <if test="imsi != null">
                imsi = #{imsi},
            </if>
            <if test="operator != null">
                operator = #{operator},
            </if>
            <if test="holder != null">
                holder = #{holder},
            </if>
            <if test="issueTime != null">
                issue_time = #{issueTime},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="activationTime != null">
                activation_time = #{activationTime},
            </if>
            <if test="expire != null">
                expire = #{expire},
            </if>
            <if test="dataPlan != null">
                data_plan = #{dataPlan},
            </if>
            <if test="packetSize != null">
                packet_size = #{packetSize},
            </if>
            <if test="category != null">
                category = #{category},
            </if>
            <if test="deviceId != null">
                device_id = #{deviceId},
            </if>
            <if test="deviceType != null">
                device_type = #{deviceType},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="deleted != null">
                deleted = #{deleted},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId}
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <update id="deleteById">
        update bdm_iot_card
        set deleted     = 1,
            update_time = now()
        where id = #{id}
    </update>

    <delete id="deleteByIds">
        update bdm_iot_card
        set deleted = 1, update_time = now()
        where id in
        <if test="ids != null and ids.length > 0">
            <foreach collection="ids" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </delete>

    <update id="updateIotCardByDeviceId">
        update bdm_iot_card
        set device_id =#{id}, device_type = #{deviceType}
        where
            deleted = 0
            <if test="numbers != null and numbers != ''">
                and number = #{numbers}
            </if>
    </update>

    <!-- TODO 本人权限-->
    <select id="iotList" resultType="com.xh.vdm.bi.vo.response.IotCardResponse">
        select bic.id, bic.number
        from bdm_iot_card bic
        where bic.device_id = 0
        and bic.deleted = 0
        <if test="number != null and number != ''">
            and bic.number like concat('%',#{number},'%')
        </if>
        <if test="deptIds != null and deptIds != ''">
            and bic.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and bic.create_account = #{account}
        </if>

    </select>

    <update id="updateDeviceIdById">
        update bdm_iot_card
        set device_id   = 0,
            device_type = 0
        where device_type = #{deviceType}
          and device_id = #{id}
          and deleted =0
    </update>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO bdm_iot_card (
        "number","card_number", "iccid", "imsi", "operator", "holder", "issue_time", "status",
        "activation_time", "expire", "data_plan", "packet_size", "create_time", "dept_id"
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.number},#{item.cardNumber},#{item.iccid},#{item.imsi},#{item.operator},#{item.holder},
            #{item.issueTime},#{item.status},#{item.activationTime},#{item.expire},#{item.dataPlan},
            #{item.packetSize},now(),#{item.deptId}
            )
        </foreach>
    </insert>

    <update id="updateDeviceId">
        <foreach collection="request" item="item" open="" close="" separator=";">
            update bdm_iot_card
            <set>
                <if test="item.id != null">
                    device_id = #{item.id},
                </if>
                <if test="item.deviceType != null">
                    device_type = #{item.deviceType}
                </if>
            </set>
            where deleted = 0
            and number = #{item.number}
        </foreach>
    </update>

    <insert id="insertDeviceId">
        INSERT INTO bdm_iot_card (number, device_id, device_type, virtuality)
        VALUES
        <foreach collection="request" item="item" separator="," open="" close="">
            (
            <if test="null != item.number and '' != item.number">
                #{item.number},
            </if>
            <if test="null != item.id">
                #{item.id},
            </if>
            <if test="null != item.deviceType">
                #{item.deviceType},
            </if>
            <if test="null != item.virtuality">
                #{item.virtuality}
            </if>
            )
        </foreach>
    </insert>


    <update id="deleteByDeviceIds">
        <if test="ids != null and ids.length > 0">
            update bdm_iot_card
            set device_id = 0,device_type = 0
            where device_type = #{deviceType}
            and device_id in
            <foreach collection="ids" item="id" index="i" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </update>

    <select id="selectAllNumbers" resultType="java.lang.String">
        SELECT number
        FROM bdm_iot_card
        <where>
            <if test="null != number">
                and number != #{number}
            </if>
        </where>
    </select>

    <select id="findExistingNumbers" resultType="String">
        SELECT number
        FROM bdm_iot_card
        WHERE number IN
        <foreach collection="list" item="number" open="(" close=")" separator=",">
            #{number}
        </foreach>
    </select>

    <select id="queryIot" resultType="java.lang.String">
        SELECT bic.number
        FROM bdm_iot_card bic
        <if test="userId != null">
            join bdm_user_dept_regulates usr on usr.dept_id = bic.dept_id and usr.user_id = #{userId}
        </if>
        WHERE bic.number IN
        <foreach collection="list" item="number" open="(" close=")" separator=",">
            #{number}
        </foreach>
    </select>

    <select id="queryIotCE" resultType="java.lang.String">
        SELECT bic.number
        FROM bdm_iot_card bic
        where deleted = 0
        <if test="account != null and account != ''">
            and create_account = #{account}
        </if>
        <if test="account == null or account == ''">
            and dept_id = any(${deptArray})
        </if>

    </select>

</mapper>

