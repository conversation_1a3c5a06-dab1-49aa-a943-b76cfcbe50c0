package com.xh.vdm.bi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.mapper.IotCardMapper;
import com.xh.vdm.bi.service.IotCardService;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.vo.request.IotCardRequest;
import com.xh.vdm.bi.vo.request.TerminalIotRequest;
import com.xh.vdm.bi.vo.response.BdmIotCardExportResponse;
import com.xh.vdm.bi.vo.response.IotCardResponse;
import com.xh.vdm.biapi.entity.BdmIotCard;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (BdmIotCard)表服务实现类
 */
@Service
public class IotCardServiceImpl extends ServiceImpl<IotCardMapper, BdmIotCard> implements IotCardService {
	@Resource
	private IotCardMapper iotCardMapper;

	private static final int BATCH_SIZE = 10000;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
     * 分页查询
     *
     * @param iotCardRequest 筛选条件
     * @param ceDataAuth
     * @return 查询结果
     */
	@Override
	public IPage<BdmIotCardExportResponse> queryByPage(IotCardRequest iotCardRequest, DataAuthCE ceDataAuth) {
		Page page = new Page();
		page.setSize(iotCardRequest.getSize());
		page.setCurrent(iotCardRequest.getCurrent());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.iotCardMapper.queryAll(page, iotCardRequest, response.getAccount(), response.getOrgList());
	}

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	@Override
	public BdmIotCard queryById(Long id) {
		return this.iotCardMapper.queryById(id);
	}

	/**
	 * 新增数据f
	 *
	 * @param iotCardRequest 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmIotCard insert(IotCardRequest iotCardRequest) {
		List<String> numbers = this.iotCardMapper.selectAllNumbers(null);
		BdmIotCard bdmIotCard = new BdmIotCard();

		if (numbers.contains(iotCardRequest.getNumber())) {
			BdmIotCard iotCard = this.getBaseMapper().selectOne(new QueryWrapper<BdmIotCard>().eq("number", iotCardRequest.getNumber()));

			if (iotCard.getDeleted() == 0) {
				throw new RuntimeException("物联网卡号已存在: " + iotCardRequest.getNumber());
			} else {
				BeanUtils.copyProperties(iotCardRequest, bdmIotCard,"id");
				bdmIotCard.setCreateTime(new Date());
				bdmIotCard.setDeleted(0);
				bdmIotCard.setId(iotCard.getId());
				this.baseMapper.updateById(bdmIotCard);
			}
		} else {
			BeanUtils.copyProperties(iotCardRequest, bdmIotCard);
			bdmIotCard.setCreateTime(new Date());
			bdmIotCard.setCreateAccount(AuthUtil.getUserAccount());
			this.iotCardMapper.insert(bdmIotCard);
		}

		BdmIotCard iotCard = getBaseMapper().selectById(bdmIotCard.getId());
		return iotCard;
	}

	/**
	 * 修改数据
	 *
	 * @param iotCardRequest 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmIotCard update(IotCardRequest iotCardRequest) {
		List<String> numbers = this.iotCardMapper.selectAllNumbers(iotCardRequest.getNumber());
		if (numbers.contains(iotCardRequest.getNumber())) {
			throw new RuntimeException("物联网卡号已存在: " + iotCardRequest.getNumber());
		}
		if (iotCardRequest.getPacketSize() == null) {
			iotCardRequest.setPacketSize(0);
		}
		BdmIotCard bdmIotCard = new BdmIotCard();
		BeanUtils.copyProperties(iotCardRequest, bdmIotCard);
		bdmIotCard.setUpdateTime(new Date());
		bdmIotCard.setNumber(bdmIotCard.getNumber() != null ? bdmIotCard.getNumber() : "");
		bdmIotCard.setIccid(bdmIotCard.getIccid() != null ? bdmIotCard.getIccid() : "");
		bdmIotCard.setImsi(bdmIotCard.getImsi() != null ? bdmIotCard.getImsi() : "");
		bdmIotCard.setOperator(bdmIotCard.getOperator() != null ? bdmIotCard.getOperator() : 1);
		bdmIotCard.setHolder(bdmIotCard.getHolder() != null ? bdmIotCard.getHolder() : "");
		bdmIotCard.setStatus(bdmIotCard.getStatus() != null ? bdmIotCard.getStatus() : 1);
		bdmIotCard.setDataPlan(bdmIotCard.getDataPlan() != null ? bdmIotCard.getDataPlan() : 1);
		bdmIotCard.setPacketSize(bdmIotCard.getPacketSize() != null ? bdmIotCard.getPacketSize() : 0);
		bdmIotCard.setCategory(bdmIotCard.getCategory() != null ? bdmIotCard.getCategory() : 1);
		bdmIotCard.setDeptId(bdmIotCard.getDeptId() != null ? bdmIotCard.getDeptId() : 0);
		this.iotCardMapper.update(bdmIotCard);

		BdmIotCard iotCard = getBaseMapper().selectById(bdmIotCard.getId());
		return iotCard;
	}

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	@Override
	public boolean deleteById(Long id) {
		return this.iotCardMapper.deleteById(id) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {
		return this.iotCardMapper.deleteByIds(ids) > 0;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateIotCardByDeviceId(String numbers, Long id, Integer deviceType) {
		this.iotCardMapper.updateIotCardByDeviceId(numbers, id, deviceType);
	}

	@Override
	public List<IotCardResponse> iotList(String number, DataAuthCE ceDataAuth) {
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.iotCardMapper.iotList(number, response.getAccount(), response.getOrgList());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateDeviceIdById(Long id, Integer deviceType, String numbers) {
		this.iotCardMapper.updateDeviceIdById( id, deviceType);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<IotCardRequest> importExcel(List<IotCardRequest> list) {
		//重复的的数据
		List<IotCardRequest> duplicateIotCardRequests = getDuplicateIotCardRequests(list);
		// 去重后的数据（去除所有重复数据）
		List<IotCardRequest> requests = list.stream()
			.filter(Objects::nonNull)
			.collect(Collectors.collectingAndThen(
				Collectors.groupingBy(IotCardRequest::getNumber, Collectors.toList()),
				map -> map.values().stream()
					.filter(values -> values.size() == 1)
					.flatMap(List::stream)
					.collect(Collectors.toList())
			));

		if (!requests.isEmpty()) {
			List<String> stringList = requests.stream().map(IotCardRequest::getNumber).collect(Collectors.toList());
			//数据库的数据
			QueryWrapper<BdmIotCard> wrapper = new QueryWrapper<>();
			if (!stringList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(wrapper, "number", stringList);
			}
			Map<String, BdmIotCard> cardMap = baseMapper.selectList(wrapper)
				.stream()
				.collect(Collectors.toMap(BdmIotCard::getNumber, card -> card));

			List<IotCardRequest> iotCardList = new ArrayList<>();
			for (IotCardRequest request : requests) {
				BdmIotCard card = cardMap.get(request.getNumber());
				if (card != null) {
					if (card.getDeleted() == 1) {
						BeanUtils.copyProperties(request,card,"id");
						card.setCreateTime(new Date());
						card.setDeleted(0);
						this.iotCardMapper.updateById(card);
						request.setId(card.getId());
					} else {
						request.setMsg("物联网卡号已存在");
						duplicateIotCardRequests.add(request);
					}
				} else {
					request.setNumber(request.getNumber() != null ? request.getNumber() : "");
					request.setIccid(request.getIccid() != null ? request.getIccid() : "");
					request.setImsi(request.getImsi() != null ? request.getImsi() : "");
					request.setOperator(request.getOperator() != null ? request.getOperator() : 1);
					request.setHolder(request.getHolder() != null ? request.getHolder() : "");
					request.setStatus(request.getStatus() != null ? request.getStatus() : 1);
					request.setDataPlan(request.getDataPlan() != null ? request.getDataPlan() : 1);
					request.setPacketSize(request.getPacketSize() != null ? request.getPacketSize() : 0);
					request.setCategory(request.getCategory() != null ? request.getCategory() : 1);
					request.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
					iotCardList.add(request);
				}
			}
			if (!iotCardList.isEmpty()) {
				MapperUtils.splitListByCapacity(iotCardList, MapperUtils.DEFAULT_CAPACITY)
					.forEach(bdmIotCards -> this.iotCardMapper.insertBatch(bdmIotCards));
			}
		}
		return duplicateIotCardRequests;
	}

	public List<IotCardRequest> getDuplicateIotCardRequests(List<IotCardRequest> list) {
		Map<String, Long> numberCountMap = list.stream()
			.map(IotCardRequest::getNumber)
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<String> duplicateNumber = numberCountMap.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		return list.stream()
			.filter(request -> duplicateNumber.contains(request.getNumber()))
			.peek(request -> request.setMsg("物联网卡号重复"))
			.collect(Collectors.toList());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateDeviceId(List<TerminalIotRequest> terminalIotRequests) {
		iotCardMapper.updateDeviceId(terminalIotRequests);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteByDeviceIds(Long[] ids, Integer deviceType) {
		this.iotCardMapper.deleteByDeviceIds(ids, deviceType);
	}

    @Override
    public List<String> queryIot(List<String> numberedList, Long userId) {
		List<String> allValidNumbers = new ArrayList<>();
		List<List<String>> batches = splitIntoBatches(numberedList, BATCH_SIZE);
		for (List<String> batch : batches) {
			List<String> batchValidNumbers = baseMapper.queryIot(batch, userId);
			allValidNumbers.addAll(batchValidNumbers);
		}
		return allValidNumbers;
    }

	@Override
	public List<String> queryIotCE(List<String> numberedList, String account, String deptArrayStr) {
		List<String> allValidNumbers = new ArrayList<>();
		List<List<String>> batches = splitIntoBatches(numberedList, BATCH_SIZE);
		for (List<String> batch : batches) {
			List<String> batchValidNumbers = baseMapper.queryIotCE(batch, account, deptArrayStr);
			allValidNumbers.addAll(batchValidNumbers);
		}
		return allValidNumbers;
	}

	private List<List<String>> splitIntoBatches(List<String> list, int batchSize) {
		List<List<String>> batches = new ArrayList<>();
		for (int i = 0; i < list.size(); i += batchSize) {
			batches.add(list.subList(i, Math.min(i + batchSize, list.size())));
		}
		return batches;
	}
}
