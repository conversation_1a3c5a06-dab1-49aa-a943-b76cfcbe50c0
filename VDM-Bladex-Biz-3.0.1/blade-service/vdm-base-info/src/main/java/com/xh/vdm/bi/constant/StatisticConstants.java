package com.xh.vdm.bi.constant;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2018/11/15.
 * @description 常用变量
 */
public class StatisticConstants {
    public static final Integer SUCCESS_CODE = 0;
    public static final Integer FAILED_CODE = -1;

    public static final String SUCCESS_DESC = "成功";
    public static final String FAIL_DESC = "失败";
    public static final Integer FAILED_PERMISSION = 100;
    public static final Integer TOKEN_ERR_CODE = -2;
    /**
     * 权限错误
     */
    public static final Integer PERMISSION_ERR_CODE = -3;
    /**
     * 网关转发错误
     */
    public static final Integer GATE_NET_ERR_CODE = 500;
    public static final Integer EX_TOKEN_ERROR_CODE = 40101;
    public static final Integer EX_USER_INVALID_CODE = 40102;
    public static final Integer EX_CLIENT_INVALID_CODE = 40131;
    public static final Integer EX_CLIENT_FORBIDDEN_CODE = 40331;
    public static final Integer EX_OTHER_CODE = 500;
    public static final String CONTEXT_KEY_USER_ID = "currentUserId";
    public static final String CONTEXT_KEY_USERNAME = "currentUserName";
    public static final String CONTEXT_KEY_USER_NAME = "currentUser";
    public static final String JWT_KEY_USER_ID = "userId";
    public static final String JWT_KEY_NAME = "name";
    public static final String JWT_KEY_IS_ADMIN = "isAdmin";
    public static final Short SUPER_MAN = 1;
    public static final Integer IS_ADMING_ABLE = 1;
    public static final Integer IS_ADMING_DISABLE = 0;
    public static final Integer IS_USER_ABLE = 1;
    public static final Integer IS_USER_DISABLE = 0;
    public static final Short IS_LOGIN_ABLE = 1;
    public static final Short IS_LOGIN_DISABLE = 0;
    public static final Short IS_TOKEN_ABLE = 1;
    public static final Short IS_TOKEN_DISABLE = 2;
    public static final String REDIS_USER_INFO = "USER_INFO:";
    public static final String BUILDING_ID_CODE = "building_id";
    public static final String FLOOR_CODE = "floor";
    public static final String IMG_URL_CODE = "owner_img_url";
    public static final String LABEL_NAME_CODE = "name";

    public static final Integer PERSON_FAILED_CODE = -100;

    /**
     * 菜单树根节点ID
     */
    public static final Long MENU_TREE_ROOT_ID = 0L;

    /**
     * 经纬度的名称
     */
    public static final String LONGITUDE_NAME = "longitude";
    public static final String LATITUDE_NAME = "latitude";


    //****************经度范围*************************
    public static final double LONGITUDE_MIN_VALUE = 73.55;
    public static final double LONGITUDE_MAX_VALUE = 135.083;

    //****************纬度范围***************************
    public static final double LATITUDE_MIN_VALUE = 3.85;
    public static final double LATITUDE_MAX_VALUE = 53.55;

    //****************速度范围*****************************
    public static final int SPEED_MIN_VALUE = 0;
    public static final int SPEED_MAX_VALUE = 160;

    //****************海拔范围**************************
    public static final int ALTITUDE_MIN_VALUE = -200;
    public static final int ALTITUDE_MAX_VALUE = 6500;


    //*****************车牌号字段名称******************
    public static final String LICENCE_PLATE = "licence_plate";
    public static final String LICENCE_COLOR_CODE = "licence_color_code";

    //*****************数据质量表名***********************
    public static final String LOCATION_QUALITY_TABLE = "location_quality";


    //*****************车辆在线率中间表名*********************************
    public static final String CACHE_DB_VEHICLE_ONLINE_RATE = "cache_vehicle_online_rate";


    //*****************轨迹完整率表名************************
    //指标统计表
    public static final String LOCATION_COMPLETE_TABLE = "stat_complete";
    //数据临时表
    public static final String LOCATION_COMPLETE_DATA_TEMP_TABLE = "tmp_stat_complete_data";
    //数据临时模板表
    public static final String LOCATION_COMPLETE_DATA_TEMPLATE_TABLE = "template_stat_complete_data";

    public static final String LOCATION_COMPLETE_DATA_TEMP_LICENCE_PLATE = "tmp_complete_exist_licence_plate";



    //********************漂移率表名*****************************
    //指标统计表
    public static final String LOCATION_DRIFT_TABLE = "stat_drift";
    //数据临时表
    public static final String LOCATION_DRIFT_DATA_TEMP_TABLE = "tmp_stat_drift_data";
    //数据临时模板表
    public static final String LOCATION_DRIFT_DATA_TEMPLATE_TABLE = "template_stat_drift_data";

    public static final String LOCATION_DRIFT_DATA_TEMP_LICENCE_PLATE = "tmp_drift_exist_licence_plate";


    //*********************字典 Dict_Type****************************
    public static final String DICT_TYPE_COLOR = "licence_color";  //车牌颜色


    public static final String DICT_TYPE_ONLINE_OFFLINE = "7"; //终端状态(车辆在线、离线状态)



    //**********************删除标记***************
    public static final int IS_DELETED = 1; //删除
    public static final int IN_USE = 0; //在用


    //**********************跑批任务类型****************************
    public static final String TASK_LOCATION_QUALITY = "LOCATION_QUALITY"; //数据质量定时任务
    public static final String TASK_LOCATION_COMPLETE = "LOCATION_COMPLETE"; //轨迹完整率
    public static final String TASK_LOCATION_DRIFT = "LOCATION_DRIFT"; //漂移率

	public static final String TASK_VEH_ONLINE_DAY = "VEH_ONLINE_DURATION_DAY"; //车辆每日在线时长
    public static final String TASK_STAT_SEND_EMAIL_DAY = "STAT_SEND_EMAIL_DAY"; //统计日报并发送到客户邮箱
    public static final String TASK_STAT_SEND_EMAIL_MONTH = "STAT_SEND_EMAIL_MONTH"; //统计月报并发送到客户邮箱

    public static final String TASK_DB_CACHE_SEG_LIMIT_SPEED_MAP = "STAT_DB_CACHE_SEG_LIMIT_SPEED_MAP"; //车辆地图超速

    public static final String TASK_DB_CACHE_SEG_LIMIT_SPEED_TERMINAL = "STAT_DB_CACHE_SEG_LIMIT_SPEED_TERMINAL"; //车辆地图超速

    public static final String TASK_DB_CACHE_VEHICLE_ONLINE_OFFLINE = "TASK_DB_CACHE_VEHICLE_ONLINE_OFFLINE"; //车辆上下线

    public static final String TASK_DB_CACHE_SECURITY_INFO = "TASK_DB_CACHE_SECURITY_INFO"; //车辆安全信息记录

    public static final String TASK_DB_CACHE_VEHICLE_ONLINE_RATE = "TASK_DB_CACHE_VEHICLE_ONLINE_RATE"; //车辆在线率

    public static final String TASK_DB_CACHE_STAT_DRIFT_RATE = "TASK_DB_CACHE_STAT_DRIFT_RATE"; //车辆漂移率（考核管理）


	public static final String TASK_INDEX_VEH_RUNNING_STATE = "TASK_INDEX_VEH_RUNNING_STATE"; //车辆运行综合情况统计


	public static final String TASK_INDEX_ALARM_HANDLE_COUNT_30_DAY = "TASK_INDEX_ALARM_HANDLE_COUNT_30_DAY"; //30天内报警数和处理数统计

    //**********************跑批任务执行结果************************
    public static final String TASK_EXECUTE_RESULT_SUCCESS = "SUCCESS";  //执行成功
    public static final String TASK_EXECUTE_RESULT_FAIL = "FAIL";  //执行失败


    //**********************通用常量**************************
    public static final String COMMON_TRUE = "TRUE";
    public static final String COMMON_FALSE = "FALSE";


    //**********************redis前缀********************
    public static final String TASK_EXECUTING_PREFIX = "TASK_EXECUTING:"; //跑批任务执行标记

    public static final String VEHICLE_COLOR_VALUE = "VEHICLE_COLOR_VALUE";

    public static final String VEHICLE_ONLINE_OFFLINE_STATE = "VEHICLE_ONLINE_OFFLINE_STATE";


    //**********************车辆行业类型**************************
    public static final int VEHICLE_USE_TYPE_DANGER = 30;  //危险品车辆
    public static final int VEHICLE_USE_TYPE_DANGER_SUB1 = 31; //危险品子类
    public static final int VEHICLE_USE_TYPE_DANGER_SUB2 = 32; //危险品子类

    public static final int VEHICLE_USE_TYPE_REGULAR = 11 ; //班线

    public static final int VEHICLE_USE_TYPE_CHARTERED = 12; //包车

    //************************邮件类型**********************************
    //日报
    public static final int EMAIL_TYPE_DAY = 1;
    //月报
    public static final int EMAIL_TYPE_MONTH = 2;

    //************************邮件发送类型**************************************
    //定时自动发送
    public static final int EMAIL_SEND_TYPE_SCHEDULE = 1;
    //手动调用接口发送
    public static final int EMAIL_SEND_TYPE_INTERFACE = 2;
    //手动页面发送（一般是重新发送）
    public static final int EMAIL_SEND_TYPE_PAGE = 3;

    //************************邮件发送结果*****************************************
    //邮件发送成功
    public static final int EMAIL_SEND_SUCCESS = 1;
    //邮件发送失败
    public static final int EMAIL_SEND_FAIL = 2;


    //************************统计报表--车辆在线统计***************************************
    //车辆在线天数
    public static final String ONLINE_NUM = "online_num";

	public static final String OFFLINE_NUM = "offline_num";

    //平均在线率
    public static final String AVERAGE_ONLINE_RATE = "total_online_rate";

    //在线率情况
    public static final String DAY_NUM = "day_num";

    //总在线时长
    public static final String TOTAL_ONLINE_TIME = "totalOnlineTime";

    public static final String ONLINE_RATE_MONTH = "month";
    public static final String ONLINE_RATE_LICENCE_PLATE = "licencePlate";
    public static final String ONLINE_RATE_LICENCE_COLOR = "licenceColorCode";

    //********************************公用线程池****************************************
    public static ThreadPoolExecutor threadPool = new ThreadPoolExecutor(10, 20, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(5));


	//********************车辆行驶时长表名****************************
	public static final String VEHICLE_TRAVEL_DURATION_TEMPLATE_TABLE = "stat_veh_travel_duration";


	//********************驾驶员驾驶时长表名****************************
	public static final String DRIVER_DRIVE_DURATION_TEMPLATE_TABLE = "stat_drive_status";

	//********************驾驶员驾驶时段表****************************
	public static final String DRIVER_DRIVER_DURATION_TEMPLATE_TABLE = "stat_driver_duration";

	//********************停止点表名**************************************
	public static final String STOP_POINT_TEMPLATE_TABLE = "stat_stop_point";

	//**********************跑批任务类型****************************
	public static final String TASK_VEHICLE_TRAVEL_DURATION = "VEHICLE_TRAVEL_DURATION"; //车辆行驶时长

	public static final String TASK_DRIVER_DRIVE_DURATION = "DRIVER_DRIVE_DURATION"; //驾驶员驾驶时长

	public static final String TASK_STOP_POINT = "STOP_POINT"; //停止点


	//*********************统计每小时车辆在线时长类型**************************
	//按照小时更新
	public static final String HOUR = "hour";
	//全部更新
	public static final String ALL = "all";

	//******************************平台角色**************************************
	public static final String ROLE_SERVER = "server"; //服务商
	public static final String ROLE_THIRD = "third"; //第三方服务商
	public static final String ROLE_COMPANY = "company"; //企业

	//******************************用户类型***************************************
	public static final String USER_TYPE_SERVER = "1";  //服务商
	public static final String USER_TYPE_THIRD = "2";  //第三方服务商
	public static final String USER_TYPE_COMPANY = "3"; //企业


	//*************************报警类型*********************************************

	public static final String ALARM_TYPE_KEY = "alarm_type";

	public static final Integer ALARM_KEY_EMERGENCY = 81;  //紧急报警
	public static final Integer ALARM_KEY_ADAS = 85;  //ADAS报警

	public static final Integer ALARM_KEY_DSM = 86;  //ADAS报警

	public static final Integer ALARM_KEY_INTENSE = 87;  //激烈驾驶报警


}
