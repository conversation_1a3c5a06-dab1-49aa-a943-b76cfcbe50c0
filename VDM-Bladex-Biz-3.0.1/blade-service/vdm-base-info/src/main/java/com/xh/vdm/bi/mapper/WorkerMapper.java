package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.WorkerRequest;
import com.xh.vdm.bi.vo.response.WorkerResponse;
import com.xh.vdm.biapi.entity.BdmWorker;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmWorker)表数据库访问层
 */
public interface WorkerMapper extends BaseMapper<BdmWorker> {

	/**
	 * 查询指定行数据
	 *
	 * @param request 查询条件
	 * @param page  分页对象
	 * @return 对象列表
	 */
	IPage<WorkerResponse> queryAll(IPage page, @Param("request") WorkerRequest request, @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 新增数据
	 *
	 * @param bdmWorker 实例对象
	 * @return 影响行数
	 */
	int insertWorker(BdmWorker bdmWorker);

	/**
	 * 修改数据
	 *
	 * @param bdmWorker 实例对象
	 * @return 影响行数
	 */
	int update(BdmWorker bdmWorker);

	/**
	 * 批量新增数据
	 *
	 * @param list List<BdmWorker> 实例对象列表
	 * @return 影响行数
	 */
	int insertBatch(@Param("list") List<BdmWorker> list);

	/**
	 * 通过主键删除数据
	 *
	 * @param ids 主键
	 * @return 影响行数
	 */
	int deleteByIds(Long[] ids);


	int batchUpdate(@Param("ids") List<Long> ids,@Param("deptId") Long deptId);
}

