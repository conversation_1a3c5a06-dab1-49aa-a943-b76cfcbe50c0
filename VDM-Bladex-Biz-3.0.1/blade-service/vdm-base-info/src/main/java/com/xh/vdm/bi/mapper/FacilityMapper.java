package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.FacilityRequest;
import com.xh.vdm.bi.vo.response.FacilityResponse;
import com.xh.vdm.biapi.entity.BdmFacility;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmFacility)表数据库访问层
 */
public interface FacilityMapper extends BaseMapper<BdmFacility> {

	/**
	 * 查询指定行数据
	 *
	 * @param request 查询条件
	 * @return 对象列表
	 */
	IPage<FacilityResponse> queryAll(IPage page, @Param("request") FacilityRequest request, @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 新增数据
	 *
	 * @param bdmFacility 实例对象
	 * @return 影响行数
	 */
	void insertFacility(BdmFacility bdmFacility);

	/**
	 * 修改数据
	 *
	 * @param bdmFacility 实例对象
	 * @return 影响行数
	 */
	void update(BdmFacility bdmFacility);

	/**
	 * 批量新增数据（MyBatis原生foreach方法）
	 *
	 * @param list List<FacilityResponse> 实例对象列表
	 * @return 影响行数
	 */
	int insertBatch(@Param("list") List<BdmFacility> list);

	/**
	 * 通过主键删除数据
	 *
	 * @param ids 主键
	 * @return 影响行数
	 */
	int deleteByIds(Long[] ids);

	int batchUpdate(@Param("ids") List<Long> ids,@Param("deptId") Long deptId);

}

