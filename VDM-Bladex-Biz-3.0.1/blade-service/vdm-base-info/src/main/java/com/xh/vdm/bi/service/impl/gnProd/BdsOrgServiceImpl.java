package com.xh.vdm.bi.service.impl.gnProd;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.entity.gnProd.BdsOrg;
import com.xh.vdm.bi.entity.gnProd.BdsOrgLite;
import com.xh.vdm.bi.mapper.gnProd.BdsOrgMapper;
import com.xh.vdm.bi.service.gnProd.IBdsOrgService;
import com.xh.vdm.bi.vo.response.gnProd.BdsOrgVO;
import org.springblade.common.constant.RedisConstant;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.DataAuthCE;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class BdsOrgServiceImpl extends ServiceImpl<BdsOrgMapper, BdsOrg> implements IBdsOrgService {
	@Resource
	private CETokenUtil ceTokenUtil;
	@Resource
	private RedisTemplate<String, List<BdsOrgLite>> bdsOrgVORedisTemplate;

	@Override
	public List<BdsOrgVO> getBdsOrgWith(List<String> sectors) {
		// 1.获取用户账号信息
		String key = AuthUtil.getUserAccount();

		// 1.1.获取产业板块编码
		if (null != sectors && !sectors.isEmpty()) {
			Collections.sort(sectors);
			key = key + "-" + String.join("-", sectors);
		}

		// 2.先直接从缓存中查询
		List<BdsOrgLite> bdsOrgLites = bdsOrgVORedisTemplate
			.opsForValue().get(RedisConstant.VALUE_GN_USER_SECTOR_ORG+key);

		// 3.缓存没有时从数据库中查询
		if (null == bdsOrgLites || bdsOrgLites.isEmpty()) {
			String orgListStr = null;
			// 非超级管理员账户，查看所监管部门
			if (!AuthUtil.isAdministrator()) {
				DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
				orgListStr = dataAuthCE.getOrgListStr();
			}

			// 3.1.根据用户权限和产业板块过滤符合条件的组织部门
			bdsOrgLites = baseMapper.getBdsOrgWith(orgListStr, sectors);

			// 3.2.将查询结果写入redis缓存，并指定有效期
			if (null != bdsOrgLites && !bdsOrgLites.isEmpty()) {
				bdsOrgVORedisTemplate.opsForValue().set(
					RedisConstant.VALUE_GN_USER_SECTOR_ORG+key, bdsOrgLites, 5, TimeUnit.MINUTES);
			}
		}

		// 为空时直接返回
		if (null == bdsOrgLites || bdsOrgLites.isEmpty())
			return new ArrayList<>();

		BdsOrgVO bdsOrgVO = null;
		Map<String, BdsOrgVO> bdsOrgVOMap = new HashMap<>();
		for (BdsOrgLite bdsOrgLite : bdsOrgLites) {
			bdsOrgVO = new BdsOrgVO();
			bdsOrgVO.setId(bdsOrgLite.getId());
			bdsOrgVO.setParentId(bdsOrgLite.getParentId());
			bdsOrgVO.setOrgCode(bdsOrgLite.getOrgCode());
			bdsOrgVO.setOrgName(bdsOrgLite.getOrgName());
			bdsOrgVOMap.put(bdsOrgVO.getId(), bdsOrgVO);
		}

		// 4.重构组织树
		List<BdsOrgVO> roots = new ArrayList<>();
		for (BdsOrgVO bdsOrg : bdsOrgVOMap.values()) {
			addToRoot(bdsOrgVOMap, bdsOrg, roots);
		}

		return roots;
	}

	/**
	 * 添加到部门树。
	 * @param bdsOrgVOMap 参与树构建的部门节点
	 * @param bdsOrgVO 当前部门节点
	 * @param roots 树列表
	 */
	private void addToRoot(Map<String, BdsOrgVO> bdsOrgVOMap, BdsOrgVO bdsOrgVO, List<BdsOrgVO> roots) {
		// 节点已访问过
		if (bdsOrgVO.isMarked())
			return ;

		// 将节点标记为已访问
		bdsOrgVO.marked();

		// 获取父节点
		BdsOrgVO parent = bdsOrgVOMap.get(bdsOrgVO.getParentId());
		if (null == parent) {
			roots.add(bdsOrgVO);
			return;
		}

		parent.getChildren().add(bdsOrgVO);

		addToRoot(bdsOrgVOMap, parent, roots);
	}
}
