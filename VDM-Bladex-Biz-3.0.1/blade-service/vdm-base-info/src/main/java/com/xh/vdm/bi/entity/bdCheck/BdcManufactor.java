/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.bi.entity.bdCheck;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
@ApiModel(value = "BdcManufactor对象", description = "BdcManufactor对象")
@TableName(schema = "bd_check")
public class BdcManufactor implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 厂商编号
     */
    @ApiModelProperty(value = "厂商编号")
    private String code;
    /**
     * 厂商名称
     */
    @ApiModelProperty(value = "厂商名称")
    private String name;
    /**
     * 厂商地址
     */
    @ApiModelProperty(value = "厂商地址")
    private String address;
    /**
     * 厂商联系人
     */
    @ApiModelProperty(value = "厂商联系人")
    private String contactPerson;
    /**
     * 厂商联系电话
     */
    @ApiModelProperty(value = "厂商联系电话")
    private String contactPhone;
    /**
     * 是否删除  0：有效     1：删除
     */
    @ApiModelProperty(value = "是否删除  0：有效     1：删除")
    private Integer isDel;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
