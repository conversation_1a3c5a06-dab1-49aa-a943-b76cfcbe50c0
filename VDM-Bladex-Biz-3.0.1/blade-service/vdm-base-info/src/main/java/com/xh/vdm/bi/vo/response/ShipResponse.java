package com.xh.vdm.bi.vo.response;

import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;

/**
 * @Description: 货船管理返参
 */
@Data
public class ShipResponse implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	@Compare("船舶编号")
	private String number;

	private Integer targetType;
	@Compare("船舶类型")
	private Integer category;
	@Compare("船舶名称")
	private String name;
	@Compare("船舶英文名称")
	private String nameEn;
	@Compare("水上移动业务识别码")
	private String mmsi;
	@Compare("国际海事船舶识别码")
	private String imoNumber;
	@Compare("船舶呼号")
	private String callSign;
	@Compare("所属部门id")
	private Long deptId;
	@Compare("总重")
	private  Float maxGross;
	@Compare("载重/净重")
	private Float net;
	@Compare("排水量")
	private Float displcement;
	@Compare("型长")
	private Integer length;
	@Compare("型宽")
	private Integer breadth;
	@Compare("型深")
	private Integer depth;
	@Compare("吃水")
	private Integer draught;
	@Compare("航速")
	private Float cruiseSpeed;

	private String createTime;

	private String updateTime;

	private Integer deleted;

	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;

	private String terminalCategories;

	private String deptName;
}

