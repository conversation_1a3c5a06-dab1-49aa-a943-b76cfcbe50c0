package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.FacilityRequest;
import com.xh.vdm.bi.vo.request.FacilityTerminalRequest;
import com.xh.vdm.bi.vo.response.FacilityNoBingResponse;
import com.xh.vdm.bi.vo.response.FacilityResponse;
import com.xh.vdm.biapi.entity.BdmFacility;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmFacility)表服务接口
 */
public interface FacilityService extends IService<BdmFacility> {

	/**
     * 分页查询
     *
     * @param facilityRequest 筛选条件
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<FacilityResponse> queryByPage(FacilityRequest facilityRequest, DataAuthCE ceDataAuth);

	/**
	 * 新增数据
	 *
	 * @param facilityRequest 实例对象
	 * @return 实例对象
	 */
	BdmFacility insert(FacilityRequest facilityRequest);

	/**
	 * 修改数据
	 *
	 * @param facilityRequest 实例对象
	 * @return 实例对象
	 */
	BdmFacility update(FacilityRequest facilityRequest);

	/**
	 * 通过主键删除数据
	 *
	 * @param ids 主键
	 * @return 是否成功
	 */
	boolean deleteByIds(Long[] ids);

    boolean connectTerminal(List<FacilityTerminalRequest> list, Long id, Long deptId);

	IPage<FacilityNoBingResponse> selectNoBind(DeviceNoBindRequest deviceNoBindRequest, DataAuthCE ceDataAuth);

	List<FacilityNoBingResponse> terminalInfo(Long id);

	List<FacilityNoBingResponse> selectBind(DeviceNoBindRequest deviceNoBindRequest, Long id);

    List<FacilityRequest> importExcel(List<FacilityRequest> list);

	Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest);
}
