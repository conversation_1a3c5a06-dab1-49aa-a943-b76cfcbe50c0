<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.BdmExistingTerminalMapper">

    <insert id="saveDevice" keyProperty="id" useGeneratedKeys="true">
        INSERT INTO bdm_abstract_device
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != id and '' != id">
                id,
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                unique_id,
            </if>
            <if test="null != deviceType and '' != deviceType">
                device_type,
            </if>
            <if test="null != deptId and '' != deptId">
                dept_id,
            </if>
            <if test="null != category and '' != category">
                category,
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                device_num,
            </if>
            <if test="null != targetId and '' != targetId">
                target_id,
            </if>
            <if test="null != targetType and '' != targetType">
                target_type,
            </if>
            <if test="null != deleted and '' != deleted">
                deleted,
            </if>
            <if test="null != iotProtocol and '' != iotProtocol">
                iot_protocol,
            </if>
            <if test="null != model and '' != model">
                model,
            </if>
            <if test="null != specificity and '' != specificity">
                specificity,
            </if>
            <if test="null != manufacturerName and '' != manufacturerName">
                manufacturer_name,
            </if>
            <if test="null != imei and '' != imei">imei,</if>
            <if test="null != classCode and '' != classCode">class_code,</if>
            <if test="null != subClassCode and '' != subClassCode">sub_class_code,</if>
            <if test="null != longitude">longitude,</if>
            <if test="null != latitude">latitude,</if>
            <if test="null != deviceAddr and '' != deviceAddr">device_addr,</if>
            <if test="null != inNetType">in_net_type,</if>
            <if test="null != inNetProvider and '' != inNetProvider">in_net_provider,</if>
            <if test="null != bdCardNumber and '' != bdCardNumber">bd_card_number,</if>
            <if test="null != contact and '' != contact">contact,</if>
            <if test="null != contactPhone and '' != contactPhone">contact_phone,</if>
            <if test="null != iotNumber and '' != iotNumber">iot_number,</if>
            <if test="null != deviceNo and '' != deviceNo">device_no,</if>
            <if test="null != deviceSource">device_source</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != id and '' != id">
                #{id},
            </if>
            <if test="null != uniqueId and '' != uniqueId">
                #{uniqueId},
            </if>
            <if test="null != deviceType and '' != deviceType">
                #{deviceType},
            </if>
            <if test="null != deptId and '' != deptId">
                #{deptId},
            </if>
            <if test="null != category and '' != category">
                #{category},
            </if>
            <if test="null != deviceNum and '' != deviceNum">
                #{deviceNum},
            </if>
            <if test="null != targetId and '' != targetId">
                #{targetId},
            </if>
            <if test="null != targetType and '' != targetType">
                #{targetType},
            </if>
            <if test="null != deleted and '' != deleted">
                #{deleted},
            </if>
            <if test="null != iotProtocol and '' != iotProtocol">
                #{iotProtocol},
            </if>
            <if test="null != model and '' != model">
                #{model},
            </if>
            <if test="null != specificity and '' != specificity">
                #{specificity},
            </if>
            <if test="null != manufacturerName and '' != manufacturerName">
                #{manufacturerName},
            </if>
            <if test="null != imei and '' != imei">#{imei},</if>
            <if test="null != classCode and '' != classCode">#{classCode},</if>
            <if test="null != subClassCode and '' != subClassCode">#{subClassCode},</if>
            <if test="null != longitude">#{longitude},</if>
            <if test="null != latitude">#{latitude},</if>
            <if test="null != deviceAddr and '' != deviceAddr">#{deviceAddr},</if>
            <if test="null != inNetType">#{inNetType},</if>
            <if test="null != inNetProvider and '' != inNetProvider">#{inNetProvider},</if>
            <if test="null != bdCardNumber and '' != bdCardNumber">#{bdCardNumber},</if>
            <if test="null != contact and '' != contact">#{contact},</if>
            <if test="null != contactPhone and '' != contactPhone">#{contactPhone},</if>
            <if test="null != iotNumber and '' != iotNumber">#{iotNumber},</if>
            <if test="null != deviceNo and '' != deviceNo">#{deviceNo},</if>
            <if test="null != deviceSource">#{deviceSource}</if>
        </trim>
    </insert>



    <!-- TODO 本人权限-->
    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.bi.vo.response.ExistingTerminalResponse">
        SELECT
        t1.ID,
        t1.device_type,
        t1.model,
        t1.unique_id,
        t1.imei,
        t1.specificity,
        t1.dept_id,
        t1.target_id,
        t1.target_type,
        t1.category,
        t1.device_num,
        t1.channel_num,
        t2.dept_name,
        t1.class_code,
        t1.sub_class_code,
        t1.contact,
        t1.contact_phone,
        t1.device_source,
        t1.longitude,
        t1.latitude,
        t1.device_addr,
        t1.in_net_type,
        t1.in_net_provider,
        t1.bd_card_number,
        t1.iot_number,
        t1.device_no,
        t2.dept_name,
        t1.manufacturer_name,
        CASE

        WHEN t3.installdate IS NOT NULL THEN
        t3.installdate
        WHEN t4.installdate IS NOT NULL THEN
        t4.installdate
        WHEN t5.installdate IS NOT NULL THEN
        t5.installdate
        WHEN t6.installdate IS NOT NULL THEN
        t6.installdate
        WHEN t7.installdate IS NOT NULL THEN
        t7.installdate ELSE NULL
        END AS installdate
        FROM
        bdm_abstract_device t1
        LEFT JOIN blade_dept t2 ON t1.dept_id = t2.
        ID LEFT JOIN bdm_rnss_device t3 ON t1.unique_id = t3.unique_id
        LEFT JOIN bdm_wearable_device t4 ON t1.unique_id = t4.unique_id
        LEFT JOIN bdm_monit_device t5 ON t1.unique_id = t5.unique_id
        LEFT JOIN bdm_rdss_device t6 ON t1.unique_id = t6.unique_id
        LEFT JOIN bdm_pnt_device t7 ON t1.unique_id = t7.unique_id
        WHERE t1.deleted = 0 AND t1.device_source = 1
        <if test="request.classCode != null and request.classCode != ''">
            AND t1.class_code = #{request.classCode}
        </if>
        <if test="request.subClassCode != null and request.subClassCode != ''">
            AND t1.sub_class_code = #{request.subClassCode}
        </if>
        <if test="request.uniqueId != null and request.uniqueId != ''">
            AND t1.unique_id LIKE CONCAT('%', #{request.uniqueId}, '%')
        </if>
        <if test="request.contact != null and request.contact != ''">
            AND t1.contact LIKE CONCAT('%', #{request.contact}, '%')
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            AND t1.dept_id = #{request.deptId}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and t2.id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and t1.create_account = #{account}
        </if>
        ORDER BY t1.id DESC
    </select>

    <sql id="Base_Column_List">
            t1.ID,
            t1.device_type,
            t1.model,
            t1.unique_id,
            t1.imei,
            t1.specificity,
            t1.dept_id,
            t1.target_id,
            t1.target_type,
            t1.category,
            t1.device_num,
            t1.channel_num,
            t2.dept_name,
            t1.class_code,
            t1.sub_class_code,
            t1.contact,
            t1.contact_phone,
            t1.device_source,
            t1.longitude,
            t1.latitude,
            t1.device_addr,
            t1.in_net_type,
            t1.in_net_provider,
            t1.bd_card_number,
            t1.iot_number,
            t1.device_no,
            t2.dept_name,
            t1.manufacturer_name,
            CASE
                WHEN t3.installdate IS NOT NULL THEN
                    t3.installdate
                WHEN t4.installdate IS NOT NULL THEN
                    t4.installdate
                WHEN t5.installdate IS NOT NULL THEN
                    t5.installdate
                WHEN t6.installdate IS NOT NULL THEN
                    t6.installdate
                WHEN t7.installdate IS NOT NULL THEN
                    t7.installdate ELSE NULL
                END AS installdate
    </sql>
    <!--查询单个-->
    <select id="queryById" resultType="com.xh.vdm.bi.vo.response.ExistingTerminalResponse">
        SELECT <include refid="Base_Column_List" />
        FROM
        bdm_abstract_device t1
        LEFT JOIN blade_dept t2 ON t1.dept_id = t2.
        ID LEFT JOIN bdm_rnss_device t3 ON t1.unique_id = t3.unique_id
        LEFT JOIN bdm_wearable_device t4 ON t1.unique_id = t4.unique_id
        LEFT JOIN bdm_monit_device t5 ON t1.unique_id = t5.unique_id
        LEFT JOIN bdm_rdss_device t6 ON t1.unique_id = t6.unique_id
        LEFT JOIN bdm_pnt_device t7 ON t1.unique_id = t7.unique_id
        WHERE t1.deleted = 0 AND t1.device_source = 1
        and t1.id = #{id}
    </select>

</mapper>

