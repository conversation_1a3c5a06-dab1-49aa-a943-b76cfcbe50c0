package com.xh.vdm.bi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.entity.BdmDeviceCode;
import com.xh.vdm.bi.mapper.BdmDeviceLedgerMapper;
import com.xh.vdm.bi.service.BdmDeviceCodeService;
import com.xh.vdm.bi.service.BdmDeviceLedgerService;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.vo.request.BdmDeviceLedgerRequest;
import com.xh.vdm.biapi.entity.BdmDeviceLedger;
import org.springblade.core.mp.support.Query;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * (BdmDeviceLedger)表服务实现类
 */
@Service
public class BdmDeviceLedgerServiceImpl extends ServiceImpl<BdmDeviceLedgerMapper, BdmDeviceLedger> implements BdmDeviceLedgerService {

	@Resource
	private BdmDeviceLedgerMapper bdmDeviceLedgerMapper;
	@Resource
	private BdmDeviceCodeService deviceCodeService;
	@Value("${current.schema}")
	String schema;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<BdmDeviceLedger> insertBatch(List<BdmDeviceLedger> list, Integer kind, List<String> keySet, Long userId) {
		List<BdmDeviceLedger> duplicates = new ArrayList<>();

		if (kind == 0) {
			BdmDeviceLedger ledger = list.get(0);
			BdmDeviceLedger deviceLedger = baseMapper.selectOne(new QueryWrapper<BdmDeviceLedger>().eq("unique_id", ledger.getUniqueId()));
			if(deviceLedger == null){
				ledger.setUniqueId(ledger.getUniqueId() != null ? ledger.getUniqueId() : "");
				ledger.setImei(ledger.getImei() != null ? ledger.getImei() : "");
				ledger.setModel(ledger.getModel() != null ? ledger.getModel() : "");
				ledger.setVendor(ledger.getVendor() != null ? ledger.getVendor() : "");
				ledger.setBdChipSn(ledger.getBdChipSn() != null ? ledger.getBdChipSn() : "");
				ledger.setDeviceType(ledger.getDeviceType() != null ? ledger.getDeviceType() : 0);
				ledger.setCategory(ledger.getCategory() != null ? ledger.getCategory() : 0);
				ledger.setDeviceNum(ledger.getDeviceNum() != null ? ledger.getDeviceNum() : "");
				ledger.setStorageState(ledger.getStorageState() != null ? ledger.getStorageState() : 0);
				ledger.setType(ledger.getType() != null ? ledger.getType() : "");
				ledger.setBuytime(ledger.getBuytime() != null ? ledger.getBuytime() : new Date());
				ledger.setInputer(userId);
				this.bdmDeviceLedgerMapper.insertBatch(list);
			}else {
				deviceLedger.setUniqueId(ledger.getUniqueId() != null ? ledger.getUniqueId() : "");
				deviceLedger.setImei(ledger.getImei() != null ? ledger.getImei() : "");
				deviceLedger.setModel(ledger.getModel() != null ? ledger.getModel() : "");
				deviceLedger.setVendor(ledger.getVendor() != null ? ledger.getVendor() : "");
				deviceLedger.setBdChipSn(ledger.getBdChipSn() != null ? ledger.getBdChipSn() : "");
				deviceLedger.setDeviceType(ledger.getDeviceType() != null ? ledger.getDeviceType() : 0);
				deviceLedger.setCategory(ledger.getCategory() != null ? ledger.getCategory() : 0);
				deviceLedger.setDeviceNum(ledger.getDeviceNum() != null ? ledger.getDeviceNum() : "");
				deviceLedger.setStorageState(ledger.getStorageState() != null ? ledger.getStorageState() : 0);
				deviceLedger.setType(ledger.getType() != null ? ledger.getType() : "");
				deviceLedger.setBuytime(ledger.getBuytime());
				deviceLedger.setInputer(userId);
				baseMapper.updateById(deviceLedger);
				ledger.setId(deviceLedger.getId());
			}
		} else {
			//重复数据
			duplicates = getDuplicates(list);

			List<BdmDeviceLedger> requests = list.stream()
				.filter(Objects::nonNull)
				.collect(Collectors.collectingAndThen(
					Collectors.groupingBy(BdmDeviceLedger::getDeviceNum, Collectors.toList()),
					map -> map.values().stream()
						.filter(values -> values.size() == 1)
						.flatMap(List::stream)
						.collect(Collectors.toList())
				));
			List<BdmDeviceLedger> deviceLedgers = new ArrayList<>();
			if (!requests.isEmpty()) {
				Map<String, BdmDeviceLedger> deviceNumMap = this.bdmDeviceLedgerMapper.selectList(new QueryWrapper<>())
					.stream()
					.collect(Collectors.toMap(BdmDeviceLedger::getUniqueId, Function.identity()));

				Map<String, BdmDeviceCode> deviceCodeMap = this.deviceCodeService.getBaseMapper()
					.selectList(new QueryWrapper<>())
					.stream()
					.collect(Collectors.toMap(BdmDeviceCode::getDeviceNum, Function.identity()));

				for (BdmDeviceLedger request : requests) {
					if (keySet != null && keySet.contains(request.getDeviceNum())) {
						BdmDeviceCode deviceCode = deviceCodeMap.get(request.getDeviceNum());

						BdmDeviceLedger deviceLedger = deviceNumMap.get(deviceCode.getSerial());
						if (deviceLedger != null) {
							//1、该序列号已录入，请先删除终端，回库后再重新入库！
							List<Object> b = baseMapper.selectDevice(deviceLedger.getUniqueId());
							if(!b.isEmpty()){
								request.setErrorMsg("该序列号因重复赋码导致重复录入，请先删除终端，回库后再重新入库！");
								duplicates.add(request);
							}
							if (deviceLedger.getStorageState() == 0) {
								//更新操作
								request.setId(deviceLedger.getId());
								request.setUniqueId(deviceCode.getSerial() != null ? deviceCode.getSerial() : "");
								request.setImei(deviceCode.getImei() != null ? deviceCode.getImei() : "");
								request.setModel(deviceCode.getModel() != null ? deviceCode.getModel() : "");
								request.setVendor(deviceCode.getVendor() != null ? deviceCode.getVendor() : "");
								request.setBdChipSn(deviceCode.getBdChipSerial() != null ? deviceCode.getBdChipSerial() : "");
								request.setDeviceType(request.getDeviceType() != null ? request.getDeviceType() : 0);
								request.setCategory(request.getCategory() != null ? request.getCategory() : 0);
								request.setDeviceNum(request.getDeviceNum() != null ? request.getDeviceNum() : "");
								request.setStorageState(request.getStorageState() != null ? request.getStorageState() : 0);
								request.setType(deviceCode.getKind() != null ? deviceCode.getKind() : "");
								request.setBuytime(request.getBuytime());
								request.setInputer(userId);
								request.setDeliveryTime(null);
								request.setOutputer(0L);
								request.setUserDeptId(0L);
								baseMapper.updateByUniqueId(request);
							} else {
								//错误信息记录
								request.setErrorMsg("该序列号已出库，请先回库再重新入库！");
								duplicates.add(request);
							}
						} else {
							//在bdm_device_code但不在bdm_device_ledger：新增
							request.setUniqueId(deviceCode.getSerial() != null ? deviceCode.getSerial() : "");
							request.setImei(deviceCode.getImei() != null ? deviceCode.getImei() : "");
							request.setModel(deviceCode.getModel() != null ? deviceCode.getModel() : "");
							request.setVendor(deviceCode.getVendor() != null ? deviceCode.getVendor() : "");
							request.setBdChipSn(deviceCode.getBdChipSerial() != null ? deviceCode.getBdChipSerial() : "");
							request.setDeviceType(request.getDeviceType() != null ? request.getDeviceType() : 1);
							request.setCategory(request.getCategory() != null ? request.getCategory() : 11);
							request.setDeviceNum(request.getDeviceNum() != null ? request.getDeviceNum() : "");
							request.setStorageState(request.getStorageState() != null ? request.getStorageState() : 0);
							request.setType(deviceCode.getKind() != null ? deviceCode.getKind() : "");
							request.setBuytime(request.getBuytime());
							request.setInputer(userId);
							deviceLedgers.add(request);
						}
					} else {
						request.setErrorMsg("终端赋码号不存在或已入库！");
						duplicates.add(request);
					}
				}
			}
			if (!deviceLedgers.isEmpty()) {
				MapperUtils.splitListByCapacity(deviceLedgers, MapperUtils.DEFAULT_CAPACITY)
					.forEach(ledgerList -> this.bdmDeviceLedgerMapper.insertBatch(ledgerList));
			}

		}
		return duplicates;
	}

	public List<BdmDeviceLedger> getDuplicates(List<BdmDeviceLedger> list) {
		Map<String, Long> map = list.stream()
			.map(BdmDeviceLedger::getDeviceNum)
			.filter(Objects::nonNull)

			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<String> deviceNumList = map.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		return list.stream()
			.filter(deviceLedger -> deviceNumList.contains(deviceLedger.getDeviceNum()))
			.peek(deviceLedger -> deviceLedger.setErrorMsg("终端赋码号重复"))
			.collect(Collectors.toList());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public long updateByIds(List<Long> ids, Long userDeptId, Long userId) {
		return this.bdmDeviceLedgerMapper.updateByIds(ids, userDeptId, userId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<BdmDeviceLedger> outImport(List<String> list) {

		List<String> keySet = this.bdmDeviceLedgerMapper.selectList(new QueryWrapper<BdmDeviceLedger>().eq("storage_state", 0))
			.stream()
			.map(BdmDeviceLedger::getDeviceNum)
			.collect(Collectors.toList());

		List<String> deviceNumList = new ArrayList<>();

		if (list.size() > 1) {
			for (String item : list) {
				if (keySet.contains(item) && !deviceNumList.contains(item)) {
					deviceNumList.add(item);
				}
			}
		} else {
			if (keySet.contains(list.get(0))) {
				deviceNumList.add(list.get(0));
			}
		}
		List<BdmDeviceLedger> ledgerList = new ArrayList<>();
		if (!deviceNumList.isEmpty()) {
			QueryWrapper<BdmDeviceLedger> queryWrapper = new QueryWrapper<>();

			queryWrapper.in("device_num", deviceNumList);

			ledgerList = this.bdmDeviceLedgerMapper.selectList(queryWrapper);
		}

		return ledgerList;
	}

	@Override
	public IPage<BdmDeviceLedger> queryAll(BdmDeviceLedgerRequest deviceLedgerRequest, Query query) {
		IPage<BdmDeviceLedger> page = new Page<>(query.getCurrent(), query.getSize());
		IPage<BdmDeviceLedger> iPage ;
		if(deviceLedgerRequest.getStatus() != null && deviceLedgerRequest.getStatus() == 1){
			iPage  = this.bdmDeviceLedgerMapper.query(deviceLedgerRequest,page,schema);
		}else {
			iPage = this.bdmDeviceLedgerMapper.queryAll(deviceLedgerRequest, page, schema);
			if (!iPage.getRecords().isEmpty()){
				for (BdmDeviceLedger ledger:iPage.getRecords()) {
					List<Object> b = baseMapper.selectDevice(ledger.getUniqueId());
					if(!b.isEmpty()){
						ledger.setBackStatus(1);
					}else {
						ledger.setBackStatus(0);
					}
				}
			}
		}
		return iPage;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void recycle(BdmDeviceLedger bdmDeviceLedger) {
		this.bdmDeviceLedgerMapper.recycle(bdmDeviceLedger);
	}
	@Override
	public boolean select(String uniqueId) {
		List<Object> b = baseMapper.selectDevice(uniqueId);
		if (!b.isEmpty()){
			return true;
		}else {
			return false;
		}
	}

	@Override
	public boolean update(BdmDeviceLedger bdmDeviceLedger) {
		return baseMapper.update(bdmDeviceLedger);
	}
}
