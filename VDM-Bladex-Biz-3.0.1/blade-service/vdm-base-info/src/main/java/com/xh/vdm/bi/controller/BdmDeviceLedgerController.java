package com.xh.vdm.bi.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.entity.BdmDeviceCode;
import com.xh.vdm.bi.service.BdmDeviceCodeService;
import com.xh.vdm.bi.service.BdmDeviceLedgerService;
import com.xh.vdm.bi.utils.DictUtil;
import com.xh.vdm.bi.vo.request.BdmDeviceLedgerRequest;
import com.xh.vdm.bi.vo.request.DeviceLedgerRequest;
import com.xh.vdm.bi.vo.response.BdmDeviceLedgerCount;
import com.xh.vdm.biapi.entity.BdmDeviceLedger;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.annotation.Log;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.minio.MinioService;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 设备台账
 */
@Slf4j
@RestController
@RequestMapping("/device/ledger")
@Validated
public class BdmDeviceLedgerController {
	/**
	 * 服务对象
	 */
	@Resource
	private BdmDeviceLedgerService bdmDeviceLedgerService;
	@Resource
	private BdmDeviceCodeService deviceCodeService;
	@Resource
	private MinioService minioService;
	@Resource
	private DictUtil dictUtil;

	/**
	 * 入库 -- 单个录入/批量录入
	 * 0 -- 新增 1-- 导入
	 */
	@Log(menu = "设备台账", operation = Operation.INLET, objectType = ObjectType.LEDGER)
	@PostMapping("/enter")
	public R enter(@RequestBody List<BdmDeviceLedger> list, BladeUser user, Integer kind) {
		if (list.isEmpty()) {
			return R.fail("数据为空");
		}
		List<String> keySet = new ArrayList<>();
		//新增校验
		if (kind == 0 && list.size() == 1) {
			BdmDeviceLedger deviceLedger = list.get(0);
			//1.已出库录入情况提示语：该序列号已录入，请先删除终端，回库后再重新入库
			boolean result = this.bdmDeviceLedgerService.select(deviceLedger.getUniqueId());
			if (result) {
				return R.fail("该序列号因重复赋码导致重复录入，请先删除终端，回库后再重新入库！");
			}
			BdmDeviceLedger ledger = this.bdmDeviceLedgerService.getOne(new QueryWrapper<BdmDeviceLedger>().eq("unique_id", deviceLedger.getUniqueId()));
			//2.出库未录入情况提示语：该序列号已出库，请先回库再重新入库
			if (ledger != null && ledger.getStorageState() == 1) {
				return R.fail("该序列号已出库，请先回库再重新入库！");
			}
		}
		//导入
		if (kind == 1) {
			keySet = deviceCodeService.selectDeviceNum();
		}
		List<BdmDeviceLedger> duplicates = this.bdmDeviceLedgerService.insertBatch(list, kind, keySet, user.getUserId());
		if (duplicates.isEmpty()) {
			List<Long> result = list.stream().map(BdmDeviceLedger::getId).collect(Collectors.toList());
			return R.data(ResultCode.SUCCESS.getCode(), result.toString(), "入库成功");
		} else {
			String filePath = "";
			String menu = "台账管理";
			try {
				filePath = minioService.exportToMinIO(menu, list, BdmDeviceLedger.class);
			} catch (Exception e) {
				log.error("导出错误数据失败",e);
			}
			List<BdmDeviceLedger> filteredList = list.stream()
				.filter(item -> !duplicates.contains(item))
				.collect(Collectors.toList());
			Map<Long, StringBuilder> deptIdMap = new HashMap<>();
			if (!filteredList.isEmpty()) {
				for (BdmDeviceLedger request : filteredList) {
					Long deptId = 0L;
					Long id = request.getId();
					if (deptIdMap.containsKey(deptId)) {
						deptIdMap.get(deptId).append("、").append(id);
					} else {
						deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
					}
				}
			}
			return R.data(207, deptIdMap, filePath);
		}
	}


	/**
	 * 出库 -- 导入出库数据
	 */
	@GetMapping("/outImport")
	public R<List<BdmDeviceLedger>> outImport(@RequestParam String deviceNumString) {
		List<String> list = Func.toStrList(deviceNumString);
		List<BdmDeviceLedger> ledgerList = this.bdmDeviceLedgerService.outImport(list);
		enrichDataWithDict(ledgerList);
		return R.data(ledgerList);
	}

	/**
	 * 出库 -- 单个出库/批量出库
	 * 0-入库，1-出库
	 */
	@Log(menu = "设备台账", operation = Operation.OUTLET, objectType = ObjectType.LEDGER)
	@PostMapping("/out")
	public R out(@RequestBody DeviceLedgerRequest request, BladeUser user) {
		QueryWrapper<BdmDeviceLedger> wrapper = new QueryWrapper<>();
		if (request.getIds() != null && !request.getIds().isEmpty()) {
			MybatisPlusWrapperUitls.longListIn(wrapper, "id", request.getIds());
		}
		List<BdmDeviceLedger> bdmDeviceLedgers = this.bdmDeviceLedgerService.getBaseMapper().selectList(wrapper);

		List<String> stringList = bdmDeviceLedgers.stream().map(BdmDeviceLedger::getUniqueId).collect(Collectors.toList());
		QueryWrapper<BdmDeviceCode> queryWrapper = new QueryWrapper<>();
		if (!stringList.isEmpty()) {
			MybatisPlusWrapperUitls.longListIn(queryWrapper, "serial",stringList);
		}
		List<BdmDeviceCode> bdmDeviceCodes = deviceCodeService.getBaseMapper().selectList(queryWrapper);
		Map<String, BdmDeviceCode>  collect = bdmDeviceCodes.stream().collect(Collectors.toMap(BdmDeviceCode::getSerial, BdmDeviceCode -> BdmDeviceCode));

		for (BdmDeviceLedger ledger:bdmDeviceLedgers) {
			BdmDeviceCode deviceCode = collect.get(ledger.getUniqueId());
			if(deviceCode != null && !isDeviceCodeMatching(ledger, deviceCode)){
					return R.fail(ledger.getUniqueId() + " 该终端已经进行重新赋码，请先重新入库");
			}
		}

		if (request.getIds().isEmpty()) {
			return R.fail("请选择出库数据");
		}
		long count = this.bdmDeviceLedgerService.updateByIds(request.getIds(), request.getUserDeptId(), user.getUserId());
		if (count > 0) {
			return R.data(ResultCode.SUCCESS.getCode(), request.getIds().toString(), "出库成功");
		}
		return R.fail("操作失败");
	}

	private boolean isDeviceCodeMatching(BdmDeviceLedger ledger, BdmDeviceCode deviceCode) {
		return deviceCode.getVendor().equals(ledger.getVendor())
			&& deviceCode.getModel().equals(ledger.getModel())
			&& deviceCode.getSerial().equals(ledger.getUniqueId())
			&& deviceCode.getImei().equals(ledger.getImei())
			&& deviceCode.getBdChipSerial().equals(ledger.getBdChipSn())
			&& deviceCode.getKind().equals(ledger.getType())
			&& deviceCode.getDeviceNum().equals(ledger.getDeviceNum());
	}

	/**
	 * 出入库管理 -- 分页查询
	 */
	@PostMapping("/page")
	public R<IPage<BdmDeviceLedger>> page(@RequestBody BdmDeviceLedgerRequest deviceLedgerRequest, Query query) {
		IPage<BdmDeviceLedger> page = bdmDeviceLedgerService.queryAll(deviceLedgerRequest, query);
		enrichDataWithDict(page.getRecords());
		return R.data(page);
	}

	/**
	 * 出入库管理 -- 详情
	 */
	@GetMapping("/detail/{id}")
	public R<BdmDeviceLedger> detail(@PathVariable Long id) {
		return R.data(this.bdmDeviceLedgerService.getById(id));
	}

	/**
	 * 出入库管理 -- 统计总数、出库和入库数量
	 */
	@GetMapping("/count")
	public R<BdmDeviceLedgerCount> count() {
		BdmDeviceLedgerCount ledgerCount = new BdmDeviceLedgerCount();
		Long totalCount = this.bdmDeviceLedgerService.count();
		Long outCount = this.bdmDeviceLedgerService.getBaseMapper().selectCount(new QueryWrapper<BdmDeviceLedger>().eq("storage_state", 1));
		ledgerCount.setTotalCount(totalCount);
		ledgerCount.setOutCount(outCount);
		ledgerCount.setCenterCount(totalCount - outCount);
		return R.data(ledgerCount);
	}

	/**
	 * 出入库管理 -- 编辑（在库状态才可编辑）
	 */
	@Log(menu = "设备台账", operation = Operation.UPDATE, objectType = ObjectType.LEDGER)
	@PostMapping("/edit")
	public R edit(@RequestBody BdmDeviceLedger bdmDeviceLedger, BladeUser user) {
		BdmDeviceLedger deviceLedger = this.bdmDeviceLedgerService.getOne(new QueryWrapper<BdmDeviceLedger>().eq("device_num", bdmDeviceLedger.getDeviceNum()));
		if (deviceLedger != null && !Objects.equals(deviceLedger.getId(), bdmDeviceLedger.getId())) {
			return R.fail("该终端赋码号已入库！");
		}
		BdmDeviceLedger ledger = this.bdmDeviceLedgerService.getById(bdmDeviceLedger.getId());
		bdmDeviceLedger.setUniqueId(bdmDeviceLedger.getUniqueId() != null ? bdmDeviceLedger.getUniqueId() : "");
		bdmDeviceLedger.setImei(bdmDeviceLedger.getImei() != null ? bdmDeviceLedger.getImei() : "");
		bdmDeviceLedger.setModel(bdmDeviceLedger.getModel() != null ? bdmDeviceLedger.getModel() : "");
		bdmDeviceLedger.setVendor(bdmDeviceLedger.getVendor() != null ? bdmDeviceLedger.getVendor() : "");
		bdmDeviceLedger.setBdChipSn(bdmDeviceLedger.getBdChipSn() != null ? bdmDeviceLedger.getBdChipSn() : "");
		bdmDeviceLedger.setDeviceType(bdmDeviceLedger.getDeviceType() != null ? bdmDeviceLedger.getDeviceType() : 1);
		bdmDeviceLedger.setCategory(bdmDeviceLedger.getCategory() != null ? bdmDeviceLedger.getCategory() : 11);
		bdmDeviceLedger.setDeviceNum(bdmDeviceLedger.getDeviceNum() != null ? bdmDeviceLedger.getDeviceNum() : "");
		bdmDeviceLedger.setStorageState(bdmDeviceLedger.getStorageState() != null ? bdmDeviceLedger.getStorageState() : 0);
		bdmDeviceLedger.setType(bdmDeviceLedger.getType() != null ? bdmDeviceLedger.getType() : "B");
		boolean result = this.bdmDeviceLedgerService.update(bdmDeviceLedger);
		if (result) {
			String resultData = new CompareUtils<BdmDeviceLedger>().compare(ledger, bdmDeviceLedger);
			return R.data(ResultCode.SUCCESS.getCode(), resultData, "编辑成功");
		}
		return R.fail("操作失败");
	}

	/**
	 * 回库（出库状态才可回库）
	 */
	@Log(menu = "设备台账", operation = Operation.RETURN_BACK, objectType = ObjectType.LEDGER)
	@PostMapping("/recycle")
	public R recycle(@RequestBody BdmDeviceLedger bdmDeviceLedger, BladeUser user) {
		bdmDeviceLedger.setUserDeptId(0L);
		bdmDeviceLedger.setStorageState(0);
		bdmDeviceLedger.setDeliveryTime(null);
		this.bdmDeviceLedgerService.recycle(bdmDeviceLedger);
		return R.data(ResultCode.SUCCESS.getCode(), bdmDeviceLedger.getId().toString(), "回库成功");
	}

	private void enrichDataWithDict(List<BdmDeviceLedger> records) {
		if (records.isEmpty()) {
			return;
		}
		Map<String, String> deviceTypeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_DEVICE_TYPE);
		Map<String, String> manageStatusMap = dictUtil.getDictMap(BaseInfoConstants.MANAGE_STATUS);
		for (BdmDeviceLedger response : records) {
			response.setCategoryName((response.getCategory() == null) ? "" : deviceTypeMap.getOrDefault(response.getCategory().toString(), ""));
			response.setCategoryName((response.getCategory() == null) ? "" : deviceTypeMap.getOrDefault(response.getCategory().toString(), ""));
			response.setBackStatusName((response.getBackStatus() == null) ? "" : manageStatusMap.getOrDefault(response.getBackStatus().toString(), ""));
		}
	}

}

