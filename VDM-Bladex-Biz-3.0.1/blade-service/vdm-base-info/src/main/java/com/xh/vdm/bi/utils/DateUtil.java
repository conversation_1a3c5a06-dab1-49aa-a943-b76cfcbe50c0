package com.xh.vdm.bi.utils;

import com.alibaba.fastjson.JSON;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Description: 日期工具类
 * @Author: zhouxw
 * @Date: 2022/11/9 9:07 AM
 */
public class DateUtil {

    //考虑多线程使用的情况，使用ThreadLocal
    public static final ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    public static final ThreadLocal<SimpleDateFormat> sdfHolderShort = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));
	public static final ThreadLocal<SimpleDateFormat> sdfHolderShortNoLine = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd"));

    public static final ThreadLocal<SimpleDateFormat> sdfHolderMonth = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM"));

    /**
     * @description: 根据给定的时间戳（精确到秒）获取位于当前天的第几个小时
     * 注意：10:05 应属于 11 点，所以，会返回 11
     * 注意：如果超过了23点，会返回24；超过了0点，会返回1
     * @author: zhouxw
     * @date: 2022/11/9 9:11 AM
     * @param: [secondTimestamp]
     * @return: int
     **/
    public static int getHour(long secondTimestamp){
        Date date = new Date();
        date.setTime(secondTimestamp * 1000);
        String dateStr = sdfHolder.get().format(date);
        String hourStr = dateStr.substring(11,13);
        return Integer.parseInt(hourStr.trim())+1;
    }


	/**
	 * 根据时间戳查询小时列表，包含首尾
	 * 比如 输入 2023-08-24 22:05:00 和 2023-08-24 23:05:00 会返回  ['22','23','24']
	 * @param startSecondTimestamp
	 * @param endSecondTimestamp
	 * @return MM
	 */
	public static List<String> getHourList(long startSecondTimestamp, long endSecondTimestamp){
		Date date = new Date();
		long time = startSecondTimestamp;
		List<String> hourList = new ArrayList<>();
		while(time <= endSecondTimestamp){
			date.setTime(time*1000);
			String dateStr = sdfHolder.get().format(date);
			String hour = dateStr.substring(11,13);
			hourList.add(hour);
			time += 3600;
			date.setTime(time);
		}
		//因为包含尾，此处添加后边的时间
		int nextHour = getHour(endSecondTimestamp);
		String nextHourStr = nextHour<10?"0"+nextHour:""+nextHour;
		if(!hourList.contains(nextHourStr)){
			hourList.add(nextHourStr);
		}
		return hourList;
	}

    /**
     * @description: 获取给定时间戳所在的整点时间戳
     * 如给定 2022-10-08 10:25:30 的时间戳，那么就返回 2022-10-08 10:00:00 的时间戳，精确到 秒
     * @author: zhouxw
     * @date: 2022/11/9 9:39 AM
     * @param: [secondTimestamp]
     * @return: long
     **/
    public static long getSecondTimestampAtHour(long secondTimestamp) throws ParseException {
        Date date = new Date();
        date.setTime(secondTimestamp * 1000);
        String dateStr = sdfHolder.get().format(date);
        dateStr = dateStr.substring(0 , 13) + ":00:00";
        long timestamp = sdfHolder.get().parse(dateStr).getTime() / 1000;
        return timestamp;
    }

	/**
	 * 获取当前时间戳，精确到秒
	 * @return
	 */
	public static long getSecondTimestamp(){
		Date date = new Date();
		return date.getTime()/1000;
	}

    /**
     * @description: 查询两个给定的时间戳(精确到秒)相差几个月
     * 包含首尾，所以，最后需要多加1
     * @author: zhouxw
     * @date: 2022/11/9 4:36 PM
     * @param: [startSecondTimestamp, endSecondTimestamp]
     * @return: int
     **/
    public static int monthCountBetweenSecondTimestamp(long startSecondTimestamp , long endSecondTimestamp){
        Date startDate = new Date();
        startDate.setTime(startSecondTimestamp * 1000);
        String startStr = sdfHolder.get().format(startDate).substring( 0 , 7 );
        Date endDate = new Date();
        endDate.setTime(endSecondTimestamp * 1000);
        String endStr = sdfHolder.get().format(endDate).substring( 0 , 7 );
        int startYear = Integer.parseInt(startStr.split("-")[0]);
        int startMonth = Integer.parseInt(startStr.split("-")[1]);
        int endYear = Integer.parseInt(endStr.split("-")[0]);
        int endMonth = Integer.parseInt(endStr.split("-")[1]);
        return (endYear - startYear) * 12 + (endMonth - startMonth) + 1;
    }


    /**
     * @description: 查询两个给定的时间戳(精确到秒)相差几年
     * 包含首尾，所以，最后需要多加1
     * @author: zhouxw
     * @date: 2022/11/9 4:36 PM
     * @param: [startSecondTimestamp, endSecondTimestamp]
     * @return: int
     **/
    public static int yearCountBetweenSecondTimestamp(long startSecondTimestamp , long endSecondTimestamp){
        Date startDate = new Date();
        startDate.setTime(startSecondTimestamp * 1000);
        String startStr = sdfHolder.get().format(startDate).substring( 0 , 7 );
        Date endDate = new Date();
        endDate.setTime(endSecondTimestamp * 1000);
        String endStr = sdfHolder.get().format(endDate).substring( 0 , 7 );
        int startYear = Integer.parseInt(startStr.split("-")[0]);
        int endYear = Integer.parseInt(endStr.split("-")[0]);
        return (endYear - startYear);
    }


    /**
     * @description: 查询两个给定的时间戳(精确到秒)相差几天
     * 包含首尾，所以，最后需要多加1
     * @author: zhouxw
     * @date: 2022/11/9 4:36 PM
     * @param: [startSecondTimestamp, endSecondTimestamp]
     * @return: int
     **/
    public static int getDayCountBetweenSecondTimestamp(long startSecondTimestamp , long endSecondTimestamp) throws ParseException {
        //判断两个时间戳是否是同一个月
        Date startDate = new Date();
        startDate.setTime(startSecondTimestamp * 1000);
        String startStr = sdfHolder.get().format(startDate).substring( 0 , 7 );
        Date endDate = new Date();
        endDate.setTime(endSecondTimestamp * 1000);
        String endStr = sdfHolder.get().format(endDate).substring( 0 , 7 );
        if(startStr.equals(endStr)){
            //如果是同一个月
            return Integer.parseInt(sdfHolder.get().format(endDate).substring( 8 , 10 )) - Integer.parseInt(sdfHolder.get().format(startDate).substring( 8 , 10 ));
        }else{
            //如果不是同一个月
            Calendar cal = Calendar.getInstance();
            cal.setTime(startDate);
            int count = 0;
            while(getDayFirstSecondTimestamp(cal.getTimeInMillis() / 1000) < getDayFirstSecondTimestamp(endSecondTimestamp)){
                count++;
                cal.add(Calendar.DAY_OF_MONTH , 1);
            }
            return count;
        }

    }



    /**
     * @description: 根据给定的时间戳获取当月最后的时间戳（精确到秒）
     * @author: zhouxw
     * @date: 2022/11/9 5:21 PM
     * @param: [secondTimestamp]
     * @return: long
     **/
    public static long getMonthLastSecondTimestamp(long secondTimestamp){
        Calendar cal = Calendar.getInstance();
        Date startDate = new Date();
        startDate.setTime(secondTimestamp * 1000);
        cal.setTime(startDate);
        cal.set(Calendar.DAY_OF_MONTH , cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY , cal.getActualMaximum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE , cal.getActualMaximum(Calendar.MINUTE));
        cal.set(Calendar.SECOND , cal.getActualMaximum(Calendar.SECOND));
        long lastSecondTimestamp = cal.getTimeInMillis() / 1000;
        return lastSecondTimestamp;
    }

	/**
	 * 根据给定的月份字符串获取当月最后的时间戳（精确到秒）
	 * @param month yyyy-MM
	 * @return
	 * @throws Exception
	 */
	public static long getMonthLastSecondTimestamp(String month) throws Exception{
		Calendar cal = Calendar.getInstance();
		Date startDate = sdfHolderMonth.get().parse(month);
		cal.setTime(startDate);
		cal.set(Calendar.DAY_OF_MONTH , cal.getActualMaximum(Calendar.DAY_OF_MONTH));
		cal.set(Calendar.HOUR_OF_DAY , cal.getActualMaximum(Calendar.HOUR_OF_DAY));
		cal.set(Calendar.MINUTE , cal.getActualMaximum(Calendar.MINUTE));
		cal.set(Calendar.SECOND , cal.getActualMaximum(Calendar.SECOND));
		long lastSecondTimestamp = cal.getTimeInMillis() / 1000;
		return lastSecondTimestamp;
	}


	/**
	 * 获取指定月份最后一天的日期
	 * @param month yyy-MM
	 * @return yyyy-MMM-dd
	 * @throws Exception
	 */
	public static String getMonthLastDateStr(String month) throws Exception{
		Date date = sdfHolderMonth.get().parse(month);
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		String dateStr = month + "-" + cal.getActualMaximum(Calendar.DAY_OF_MONTH);
		return dateStr;
	}

    /**
     * @description: 根据给定的时间戳获取当月最初的时间戳（精确到秒）
     * @author: zhouxw
     * @date: 2022/11/14 6:47 PM
     * @param: [secondTimestamp]
     * @return: long
     **/
    public static long getMonthFirstSecondTimestamp(long secondTimestamp){
        Calendar cal = Calendar.getInstance();
        Date startDate = new Date();
        startDate.setTime(secondTimestamp * 1000);
        cal.setTime(startDate);
        cal.set(Calendar.DAY_OF_MONTH , cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY , cal.getActualMinimum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE , cal.getActualMinimum(Calendar.MINUTE));
        cal.set(Calendar.SECOND , cal.getActualMinimum(Calendar.SECOND));
        long lastSecondTimestamp = cal.getTimeInMillis() / 1000;
        return lastSecondTimestamp;
    }

	/**
	 * 根据给定的月份字符串获取当月最初的时间戳（精确到秒）
	 * @param month yyyy-MM
	 * @return
	 * @throws Exception
	 */
	public static long getMonthFirstSecondTimestamp(String month) throws Exception {
		Calendar cal = Calendar.getInstance();
		Date startDate = sdfHolderMonth.get().parse(month);
		cal.setTime(startDate);
		cal.set(Calendar.DAY_OF_MONTH , cal.getActualMinimum(Calendar.DAY_OF_MONTH));
		cal.set(Calendar.HOUR_OF_DAY , cal.getActualMinimum(Calendar.HOUR_OF_DAY));
		cal.set(Calendar.MINUTE , cal.getActualMinimum(Calendar.MINUTE));
		cal.set(Calendar.SECOND , cal.getActualMinimum(Calendar.SECOND));
		long lastSecondTimestamp = cal.getTimeInMillis() / 1000;
		return lastSecondTimestamp;
	}

    /**
     * @description: 根据给定的时间戳获取当天最开始的时间戳（精确到秒）
     * @author: zhouxw
     * @date: 2022/11/14 6:48 PM
     * @param: [secondTimestamp 精确到秒的时间戳]
     * @return: long
     **/
    public static long getDayFirstSecondTimestamp(long secondTimestamp) throws ParseException {
        Date date = new Date();
        date.setTime(secondTimestamp * 1000);
        String dateStr = sdfHolder.get().format(date).substring(0,10)+" 00:00:00";
        long firstSecondTimestamp = sdfHolder.get().parse(dateStr).getTime() / 1000;
        return firstSecondTimestamp;
    }

	/**
	 * 获取当日开始时间戳
	 * @return
	 * @throws Exception
	 */
	public static long getDayFirstSecondTimestamp() throws Exception {
		Date date = new Date();
		String dateStr = sdfHolder.get().format(date).substring(0,10)+" 00:00:00";
		long firstSecondTimestamp = sdfHolder.get().parse(dateStr).getTime() / 1000;
		return firstSecondTimestamp;
	}

	/**
	 * 获取当日开始时间戳
	 * @param dateStr yyyy-MM-dd
	 * @return
	 * @throws Exception
	 */
	public static long getDayFirstSecondTimestamp(String dateStr) throws Exception {
		Date date = sdfHolderShort.get().parse(dateStr);
		long firstSecondTimestamp = date.getTime() / 1000;
		return firstSecondTimestamp;
	}

    /**
     * @description: 根据给定的时间戳获取当天最后的时间戳（精确到秒）
     * @author: zhouxw
     * @date: 2022/11/14 6:48 PM
     * @param: [secondTimestamp]
     * @return: long
     **/
    public static long getDayLastSecondTimestamp(long secondTimestamp) throws ParseException {
        Date date = new Date();
        date.setTime(secondTimestamp * 1000);
        String dateStr = sdfHolder.get().format(date).substring(0,10)+" 23:59:59";
        long lastSecondTimestamp = sdfHolder.get().parse(dateStr).getTime() / 1000;
        return lastSecondTimestamp;
    }

	/**
	 * 查询今日的最后时间戳（秒）
	 * @return
	 * @throws ParseException
	 */
	public static long getDayLastSecondTimestamp() throws ParseException {
		Date date = new Date();
		String dateStr = sdfHolder.get().format(date).substring(0,10)+" 23:59:59";
		long lastSecondTimestamp = sdfHolder.get().parse(dateStr).getTime() / 1000;
		return lastSecondTimestamp;
	}

    /**
     * @description: 获取上个月的月份日期表示
     * 返回格式 yyyy-MM
     * @author: zhouxw
     * @date: 2022/11/16 8:56 AM
     * @param: []
     * @return: java.lang.String
     **/
    public static String getLastMonthString(){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH , -1);
        return cal.get(Calendar.YEAR) + "-" + (cal.get(Calendar.MONTH) + 1);
    }

    /**
     * @description: 获取指定日期的上个月的月份
     * 返回格式 yyyy-MM
     * @author: zhouxw
     * @date: 2022/11/28 12:50 AM
     * @param: [month: yyyy-MM]
     * @return: java.lang.String
     **/
    public static String getLastMonthString(String month) throws Exception{
        Date date = sdfHolderMonth.get().parse(month);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH , -1);
        String monthPre = cal.get(Calendar.YEAR) + "-" + ((cal.get(Calendar.MONTH ) + 1)<10?"0"+(cal.get(Calendar.MONTH ) + 1):(cal.get(Calendar.MONTH ) + 1));
        return monthPre;
    }

    /**
     * @description: 获取前一年相同月份的字符串表示
     * 如指定月份为 2022-09 ，则返回 2021-09
     * @author: zhouxw
     * @date: 2022/11/28 1:00 AM
     * @param: [month: yyyy-MM]
     * @return: java.lang.String
     **/
    public static String getLastYearMonthString(String month) throws Exception{
        Date date = sdfHolderMonth.get().parse(month);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.YEAR , -1);
        String monthPre = cal.get(Calendar.YEAR) + "-" + ((cal.get(Calendar.MONTH ) + 1)<10?"0"+(cal.get(Calendar.MONTH ) + 1):(cal.get(Calendar.MONTH ) + 1));
        return monthPre;
    }

    /**
     * @description: 获取指定月份的天数
     * @author: zhouxw
     * @date: 2022/11/28 1:25 AM
     * @param: [month：yyyy-MM]
     * @return: int
     **/
    public static int getDaysCountInMonth(String month) throws Exception{
        Date date = sdfHolderMonth.get().parse(month);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int days = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        return days;
    }

    /**
     * @description: 获取给定日期的字符串表示
     * 返回字符串格式： yyyy-MM-dd
     * @author: zhouxw
     * @date: 2022/11/27 12:34 AM
     * @param: [secondTimestamp]
     * @return: java.lang.String
     **/
    public static String getDateString(long secondTimestamp){
        Date date = new Date();
        date.setTime(secondTimestamp * 1000);
        return sdfHolderShort.get().format(date);
    }

	/**
	 * @description: 根据秒时间戳获取日期
	 * @author: zhouxw
	 * @date: 2023-08-237 18:11:40
	 * @param: [secondTimestamp]
	 * @return: java.util.Date
	 **/
	public static Date getDateBySecondTimestamp(long secondTimestamp){
		Date date = new Date();
		date.setTime(secondTimestamp * 1000);
		return date;
	}

	/**
	 * 根据时间戳获取时间
	 * @param secondTimestamp
	 * @return yyyy-MM-dd hh:mm:ss
	 */
	public static String getDateTimeString(long secondTimestamp){
		Date date = new Date();
		date.setTime(secondTimestamp * 1000);
		return sdfHolder.get().format(date);
	}

	/**
	 * @description: 获取当前日期的字符串表示
	 * 返回字符串格式： yyyy-MM-dd
	 * @author: zhouxw
	 * @date: 2022/11/27 12:34 AM
	 * @param: [secondTimestamp]
	 * @return: java.lang.String
	 **/
	public static String getDateString(){
		Date date = new Date();
		return sdfHolderShort.get().format(date);
	}

    /**
     * @description: 获取下个月第一天的日期
     * 日期格式：yyyy-MM-dd
     * @author: zhouxw
     * @date: 2022/11/21 8:45 AM
     * @param: [secondTimestampInThisMonth]
     * @return: java.lang.String
     **/
    public static String getNextMonthFirstDay(long secondTimestampInThisMonth){
        long nextMonthFirstSecond = getMonthLastSecondTimestamp(secondTimestampInThisMonth) + 1;
        return sdfHolderShort.get().format(nextMonthFirstSecond * 1000);
    }


    /**
     * @description: 获取指定时间段内的日期列表
     * @author: zhouxw
     * @date: 2023-03-68 15:40:42
     * @param: [startDate：开始日期 yyyy-MM-dd , endDate：结束日期 yyyy-MM-dd]
     * @return: 日期列表，包含首尾, yyyy-MM-dd
     **/
    public static List<String> getDateList(String startDate, String endDate) throws Exception{
        Date startDateTime = sdfHolderShort.get().parse(startDate);
        Date endDateTime = sdfHolderShort.get().parse(endDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDateTime);
        List<String> dateList = new ArrayList<>();
        long tmpDateMillTIme = calendar.getTimeInMillis();
        while(tmpDateMillTIme <= endDateTime.getTime()){
            int month = calendar.get(Calendar.MONTH)+1;
            int date = calendar.get(Calendar.DATE);
            String monthStr = "";
            String dateStr = "";
            if(month < 10){
                monthStr = "0"+month;
            }else{
                monthStr = month + "";
            }
            if(date < 10){
                dateStr = "0" + date;
            }else{
                dateStr = date + "";
            }
            dateList.add(calendar.get(Calendar.YEAR)+"-"+monthStr+"-"+dateStr);
            calendar.add(Calendar.DATE, 1);
            tmpDateMillTIme = calendar.getTimeInMillis();
        }
        return dateList;
    }

	/**
	 * 根据开始时间戳和结束时间戳获取日期列表
	 * 不包含 endSecondTimestamp 所在天
	 * @param startSecondTimestamp
	 * @param endSecondTimestamp
	 * @return yyyy-MM-dd
	 * @throws Exception
	 */
	public static List<String> getDateList(long startSecondTimestamp, long endSecondTimestamp) throws Exception{
		Date startDateTime = new Date();
		Date endDateTime = new Date();
		startDateTime.setTime(startSecondTimestamp * 1000);
		endDateTime.setTime(endSecondTimestamp * 1000);
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(startDateTime);
		List<String> dateList = new ArrayList<>();
		long tmpDateMillTIme = calendar.getTimeInMillis();
		while(tmpDateMillTIme <= endDateTime.getTime()){
			int month = calendar.get(Calendar.MONTH)+1;
			int date = calendar.get(Calendar.DATE);
			String monthStr = "";
			String dateStr = "";
			if(month < 10){
				monthStr = "0"+month;
			}else{
				monthStr = month + "";
			}
			if(date < 10){
				dateStr = "0" + date;
			}else{
				dateStr = date + "";
			}
			dateList.add(calendar.get(Calendar.YEAR)+"-"+monthStr+"-"+dateStr);
			calendar.add(Calendar.DATE, 1);
			tmpDateMillTIme = calendar.getTimeInMillis();
		}
		return dateList;
	}

    /**
     * @description: 获取指定时间段内的月份列表
     * @author: zhouxw
     * @date: 2023-03-68 15:40:42
     * @param: [startDate：开始日期 yyyy-MM-dd , endDate：结束日期 yyyy-MM-dd]
     * @return: 月份列表，包含首尾, yyyy-MM
     **/
    public static List<String> getMonthList(String startDate, String endDate) throws Exception{
        Date startDateTime = sdfHolderShort.get().parse(startDate);
        Date endDateTime = sdfHolderShort.get().parse(endDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDateTime);
        List<String> dateList = new ArrayList<>();
        long tmpDateMillTIme = calendar.getTimeInMillis();
        while(tmpDateMillTIme <= endDateTime.getTime()){
            int month = calendar.get(Calendar.MONTH)+1;
            String monthStr = "";
            if(month < 10){
                monthStr = "0" + month;
            }else{
                monthStr = month + "";
            }
            dateList.add(calendar.get(Calendar.YEAR) + "-" + monthStr);
            calendar.add(Calendar.MONTH, 1);
            tmpDateMillTIme = calendar.getTimeInMillis();
        }
        return dateList;
    }

    /**
     * @description: 获取指定时间段内的月份列表
     * @author: zhouxw
     * @date: 2023-03-68 15:40:42
     * @param: [startSecondTimestamp：开始时间，精确到秒的时间戳 , endSecondTimestamp：结束时间，精确到秒的时间戳]
     * @return: 月份列表，包含首尾, yyyy-MM
     **/
    public static List<String> getMonthList(long startSecondTimestamp, long endSecondTimestamp) throws Exception{
        Date startDateTime = new Date(startSecondTimestamp * 1000);
        Date endDateTime = new Date(endSecondTimestamp * 1000);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDateTime);
        List<String> dateList = new ArrayList<>();
        long tmpDateMillTIme = calendar.getTimeInMillis();
        int monthCount = monthCountBetweenSecondTimestamp(startSecondTimestamp, endSecondTimestamp);
        for(int i = 0 ; i < monthCount; i++){
            int month = calendar.get(Calendar.MONTH)+1;
            String monthStr = "";
            if(month < 10){
                monthStr = "0" + month;
            }else{
                monthStr = month + "";
            }
            dateList.add(calendar.get(Calendar.YEAR)+"-"+monthStr);
            calendar.add(Calendar.MONTH, 1);
        }
        return dateList;
    }


	/**
	 * @description: 获取 指定日期 指定月份 之后的日期
	 * 如果不指定日期，则默认为当前时间
	 * @author: zhouxw
	 * @date: 2023-07-205 21:34:29
	 * @param: [date, monthCount]
	 * @return: java.util.Date
	 **/
	public static Date getDateAfterMonth(Date date, int monthCount) throws Exception{
		if(date == null){
			date = new Date();
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, monthCount);
		return calendar.getTime();
	}


	/**
	 * @description: 获取 指定日期 指定天数 之后的日期
	 * 如果不指定日期，则默认为当前时间
	 * @author: zhouxw
	 * @date: 2023-07-205 21:34:29
	 * @param: [date, monthCount]
	 * @return: java.util.Date
	 **/
	public static Date getDateAfterDay(Date date, int dayCount) throws Exception{
		if(date == null){
			date = new Date();
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, dayCount);
		return calendar.getTime();
	}

	/**
	 * @description: 获取 指定日期 指定天数 之前的日期
	 * 如果不指定日期，则默认为当前时间
	 * @author: zhouxw
	 * @date: 2023-07-205 21:34:29
	 * @param: [date, monthCount]
	 * @return: java.util.Date
	 **/
	public static Date getDateBeforeDay(Date date, int dayCount) throws Exception{
		if(date == null){
			date = new Date();
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, -1 * dayCount);
		return calendar.getTime();
	}

	/**
	 * 根据日期字符串获取日期
	 * @param dateString
	 * @return
	 * @throws Exception
	 */
	public static Date getDateByDateString(String dateString) throws Exception{
		return sdfHolder.get().parse(dateString);
	}

	/**
	 * @description: 获取 指定日期 指定天数 之前的日期时间戳，精确到秒
	 * 如果不指定日期，则默认为当前时间
	 * @author: zhouxw
	 * @date: 2023-07-205 21:34:29
	 * @param: [date, monthCount]
	 * @return: java.util.Date
	 **/
	public static long getSecondTimeStampBeforeDay(Date date, int dayCount) throws Exception{
		if(date == null){
			date = new Date();
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, -1 * dayCount);
		return calendar.getTime().getTime()/1000;
	}

	/**
	 * @description: 获取 指定日期 指定天数 之前的日期
	 * 如果不指定日期，则默认为当前时间
	 * @author: zhouxw
	 * @date: 2023-07-205 21:34:29
	 * @param: [date, monthCount]
	 * @return: yyyy-MM-dd
	 **/
	public static String getDateBeforeDayStr(Date date, int dayCount) throws Exception{
		Date dateBefore = getDateBeforeDay(date, dayCount);
		String dateStr = sdfHolderShort.get().format(dateBefore);
		return dateStr;
	}

	/**
	 * @description: 获取 指定日期 指定天数 之前的日期
	 * 如果不指定日期，则默认为当前时间
	 * @author: zhouxw
	 * @date: 2023-07-205 21:34:29
	 * @param: [dateStr yyyy-MM-dd, monthCount]
	 * @return: yyyy-MM-dd
	 **/
	public static String getDateBeforeDayStr(String dateStr, int dayCount) throws Exception{
		Date date = sdfHolderShort.get().parse(dateStr);
		Date dateBefore = getDateBeforeDay(date, dayCount);
		String dateS = sdfHolderShort.get().format(dateBefore);
		return dateS;
	}


	/**
	 * 根据输入的秒数，转换成 xx天xx小时xx分钟xx秒的格式字符串
	 * @param second
	 * @return
	 */
	public static String getFormatDateString(long second){
		long daySeconds = 24 * 3600;
		long hourSeconds = 3600;
		long minuteSeconds = 60;
		long dayCount = second / daySeconds;
		long hourCount = (second - dayCount * daySeconds) / hourSeconds;
		long minuteCount = (second - dayCount * daySeconds - hourCount * hourSeconds) / minuteSeconds;
		long secondCount = second - dayCount * daySeconds - hourCount * hourSeconds - minuteCount * minuteSeconds;
		StringBuffer sb = new StringBuffer();
		if(dayCount > 0){
			sb.append(dayCount).append("天");
		}
		if(hourCount > 0){
			sb.append(hourCount).append("小时");
		}
		if(minuteCount > 0){
			sb.append(minuteCount).append("分钟");
		}
		sb.append(secondCount).append("秒");
		return sb.toString();
	}

	/**
	 * 格式化日期格式
	 * yyyy-MM-dd 转为 xxxx年xx月xx日
	 * @param dateStr
	 * @return
	 */
	public static String formatDateFromYMD(String dateStr){
		try{
			DateUtil.sdfHolderShort.get().parse(dateStr);
		}catch (Exception e){
			//日期格式不正确，原样返回
			return dateStr;
		}
		String[] dateArray = dateStr.split("-");
		return dateArray[0]+"年"+dateArray[1]+"月"+dateArray[2]+"日";
	}


}
