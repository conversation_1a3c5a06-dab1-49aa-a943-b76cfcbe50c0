package com.xh.vdm.bi.service.impl;

import com.xh.vdm.bi.mapper.BdmRegionFenceMapper;
import com.xh.vdm.bi.service.BdmRegionFenceService;
import com.xh.vdm.bi.vo.response.BdmRegionFenceResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * (BdmRegionFence)表服务实现类
 */
@Service
public class BdmRegionFenceServiceImpl implements BdmRegionFenceService {
	@Resource
	private BdmRegionFenceMapper bdmRegionFenceMapper;

	/**
	 * 分页查询
	 *
	 * @return 查询结果
	 */
	@Override
	public List<BdmRegionFenceResponse> queryAll() {
		return bdmRegionFenceMapper.queryAll();
	}
}
