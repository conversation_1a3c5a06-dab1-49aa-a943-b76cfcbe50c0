package com.xh.vdm.bi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.mapper.BdmAbstractTargetMapper;
import com.xh.vdm.bi.service.IBdmAbstractTargetService;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * (BdmAbstractTarget)表服务实现类
 */
@Service
public class BdmAbstractTargetServiceImpl extends ServiceImpl<BdmAbstractTargetMapper, BdmAbstractTarget> implements IBdmAbstractTargetService {

    @Override
	@Transactional(rollbackFor = Exception.class)
    public void deleteByIds(Long[] ids) {
        baseMapper.deleteByIds(ids);
    }

    @Override
	@Transactional(rollbackFor = Exception.class)
    public void insertBatch(List<BdmAbstractTarget> abstractTargetList) {
        baseMapper.insertBatch(abstractTargetList);
    }

	@Override
	public void restoreStatus(Long targetId,Integer deleted){
		baseMapper.restoreStatus(targetId,deleted);
	}

}
