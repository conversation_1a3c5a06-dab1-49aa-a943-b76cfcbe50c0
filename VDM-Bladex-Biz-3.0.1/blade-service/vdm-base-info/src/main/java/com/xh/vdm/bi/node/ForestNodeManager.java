package com.xh.vdm.bi.node;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ForestNodeManager<T extends INode<T>> {
	private final ImmutableMap<String, T> nodeMap;
	private final Map<String, Object> parentIdMap = Maps.newHashMap();

	public ForestNodeManager(List<T> nodes) {
		this.nodeMap = Maps.uniqueIndex(nodes, INode::getId);
	}

	public INode<T> getTreeNodeAt(String id) {
		return this.nodeMap.getOrDefault(id, null);
	}

	public void addParentId(String parentId) {
		this.parentIdMap.put(parentId, "");
	}

	public List<T> getRoot() {
		List<T> roots = new ArrayList<>();
		this.nodeMap.forEach((key, node) -> {
			if (Objects.equals(node.getParentId(), "0") || this.parentIdMap.containsKey(node.getId())) {
				roots.add(node);
			}
		});
		return roots;
	}

	public List<T> getRoots(List<String> rootIds) {
		List<T> roots = new ArrayList<>();
		this.nodeMap.forEach((key, node) -> {
			if (rootIds.contains(node.getId())) {
				roots.add(node);
			}
		});
		return roots;
	}
}
