package com.xh.vdm.bi.controller;

import com.xh.vdm.bi.service.BdmRegionFenceService;
import com.xh.vdm.bi.vo.response.BdmRegionFenceResponse;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * (BdmRegionFence)表控制层
 */
@RestController
@RequestMapping("/region/fence")
public class BdmRegionFenceController {
	/**
	 * 服务对象
	 */
	@Resource
	private BdmRegionFenceService bdmRegionFenceService;

	/**
	 * 查询全部电子围栏区域
	 * @return 查询结果
	 */
	@GetMapping("/list")
	public R<List<BdmRegionFenceResponse>> queryAll() {
		return R.data(this.bdmRegionFenceService.queryAll());
	}

}

