<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.DeviceNumMapper">

    <select id="selectByPattern" resultType="java.lang.String">
        SELECT MAX(device_num) AS max_device_num
        FROM (
                 SELECT device_num
                 FROM bdm_monit_device
                 WHERE device_num LIKE CONCAT('%', #{pattern}, '%')
                   AND (
                         (#{productType} = 'O' AND specificity = 1)
                         OR (#{productType} = 'S' AND specificity = 3)
                     )

                 UNION ALL

                 SELECT device_num
                 FROM bdm_pnt_device
                 WHERE device_num LIKE CONCAT('%', #{pattern}, '%')
                   AND (
                         (#{productType} = 'O' AND specificity = 1)
                         OR (#{productType} = 'S' AND specificity = 3)
                     )

                 UNION ALL

                 SELECT device_num
                 FROM bdm_rdss_device
                 WHERE device_num LIKE CONCAT('%', #{pattern}, '%')
                   AND (
                         (#{productType} = 'O' AND specificity = 1)
                         OR (#{productType} = 'S' AND specificity = 3)
                     )

                 UNION ALL

                 SELECT device_num
                 FROM bdm_rnss_device
                 WHERE device_num LIKE CONCAT('%', #{pattern}, '%')
                   AND (
                         (#{productType} = 'O' AND specificity = 1)
                         OR (#{productType} = 'S' AND specificity = 3)
                     )

                 UNION ALL

                 SELECT device_num
                 FROM bdm_wearable_device
                 WHERE device_num LIKE CONCAT('%', #{pattern}, '%')
                   AND (
                         (#{productType} = 'O' AND specificity = 1)
                         OR (#{productType} = 'S' AND specificity = 3)
                     )
             ) AS combined_devices;
    </select>
</mapper>

