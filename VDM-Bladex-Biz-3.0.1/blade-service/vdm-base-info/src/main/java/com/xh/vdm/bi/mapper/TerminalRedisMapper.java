package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.response.DeviceRedisResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 缓存查询
 */
public interface TerminalRedisMapper{

	List<DeviceRedisResponse> selectDeviceForPage(@Param("start") int start, @Param("pageSize") int pageSize);

	List<DeviceRedisResponse> selectDeviceAll();
}

