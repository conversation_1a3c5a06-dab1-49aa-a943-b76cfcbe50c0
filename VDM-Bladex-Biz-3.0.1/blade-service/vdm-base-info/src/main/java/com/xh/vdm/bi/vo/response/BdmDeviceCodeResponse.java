package com.xh.vdm.bi.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BdmDeviceCodeResponse implements Serializable {

	private String deviceNum;
	@JsonProperty("manufacturer")
	private String vendor;
	@JsonProperty("deviceModel")
	private String model;
	@JsonProperty("deviceSeq")
	private String serial;
	@JsonProperty("imei")
	private String imei;
	@JsonProperty("chipSeq")
	private String bdChipSerial;
	@JsonProperty("deviceCate")
	private String kind;
}
