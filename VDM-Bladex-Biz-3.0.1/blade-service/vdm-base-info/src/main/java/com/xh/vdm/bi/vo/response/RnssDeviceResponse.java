package com.xh.vdm.bi.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * @description : 北斗定位终端返参
 */
@Data
@ExcelIgnoreUnannotated
public class RnssDeviceResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@Column(name="id")
	@ExcelIgnore
	private Long id;

	/**
	 * unique_id
	 */
	@Column(name="unique_id")
	@ExcelProperty(value = "序列号",index = 5)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String uniqueId;

	/**
	 * imei
	 */
	@Column(name="imei")
	@ExcelProperty(value = "IMEI",index = 9)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String imei;

	/**
	 * model
	 */
	@Column(name="model")
	@ExcelProperty(value = "终端型号",index = 3)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String model;

	/**
	 * vendor
	 */
	@Column(name="vendor")
	@ExcelProperty(value = "厂商名称",index = 2)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String vendor;

	private String vendorName;

	/**
	 * bd_chip_sn
	 */
	@Column(name="bd_chip_sn")
	@ExcelProperty(value = "北斗芯片序列号",index = 11)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String bdChipSn;

	/**
	 * device_type
	 */
	@Column(name="device_type")
	@ExcelIgnore
	private Integer deviceType;

	@ExcelProperty(value = "终端类型", index = 1)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String categoryName;

	/**
	 * specificity
	 */
	@Column(name="specificity")
	@ExcelProperty(value = "设备", index = 6)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(12)
	private String specificity;

	private String specificityName;

	/**
	 * dept_id
	 */
	@Column(name="dept_id")
	@ExcelIgnore
	@JsonSerialize(using = ToStringSerializer.class)
	private Long deptId;

	/**
	 * target_id
	 */
	@Column(name="target_id")
	@ExcelIgnore
	private Long targetId;

	/**
	 * target_type
	 */
	@Column(name="target_type")
	@ExcelIgnore
	private Integer targetType;

	/**
	 * target_name
	 */
	@Column(name="target_name")
	private String targetName;

	/**
	 * activated
	 */
	@Column(name="activated")
	@ExcelIgnore
	private Integer activated;

	/**
	 * category
	 */
	@Column(name="category")
	@ExcelProperty(value = "终端名称",index = 4)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String category;
	private String deviceName;

	/**
	 * device_num
	 */
	@Column(name="device_num")
	@ExcelProperty(value = "终端赋码号",index = 8)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String deviceNum;

	/**
	 * channel_num
	 */
	@Column(name="channel_num")
	@ExcelProperty(value = "视频通道个数",index = 13)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private Integer channelNum;

	/**
	 * installdate
	 */
	@Column(name="installdate")
	@ExcelIgnore
	private Date installdate;

	/**
	 * create_time
	 */
	@Column(name="create_time")
	@ExcelIgnore
	private Date createTime;

	/**
	 * update_time
	 */
	@Column(name="update_time")
	@ExcelIgnore
	private Date updateTime;

	/**
	 * deleted
	 */
	@Column(name="deleted")
	@ExcelIgnore
	private Integer deleted;

	/**
	 * 机构名称
	 */
	@ExcelProperty(value = "所属机构",index = 12)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String deptName;

	/**
	 * 物联网卡
	 */
	@ExcelProperty(value = "物联网卡",index = 10)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	public String numbers;

	/**
	 * 应用场景
	 */
	@Column(name="scenario")
	private Integer scenario;
	/**
	 * 应用方向/领域
	 */
	@Column(name="domain")
	private Integer domain;

	/**
	 * 定位模式
	 */
	@Column(name="gnss_mode")
	@ExcelProperty(value = "定位模式", index = 7)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(12)
	private String gnssMode;

	private String gnssModeName;

	private Integer iotProtocol;

	private String terminalId;

	private Short assetType;
	private Short ownDeptType;
	private String ownDeptName;
}

