package com.xh.vdm.bi.utils;

import com.xh.vdm.bi.mapper.*;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Description: 旧设备和特殊设备自动赋码
 */
@Component
public class DeviceNumUtils {
	@Resource
	private DeviceNumMapper deviceNumMapper;
	/**
	 * 4位赋码机编号
	 */
	private  String MACHINE_CODING = "CE00";

	/**
	 * 4位序列号
	 */
	private  String SERIAL = "00001";

	/**
	 * 最大序列号
	 */
	private  final Integer MAX_SERIAL = 99999;

	/**
	 * 赋码值 = 1位产品类别标识 + 4位赋码机编号 + 6位年月日赋码日期 + 5位序列号
	 * 数量超过9999台时，将赋码机编号加1，即赋码机编号变为CE01
	 *
	 * @param productType     1位产品类别标识（M：模组，T：终端，O：旧终端，S：特殊终端）
	 * @return
	 */
	public R<String> deviceNumProducer(String productType) {

		// 6位年月日赋码日期
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
		String pattern = dateTimeFormatter.format(LocalDateTime.now()).substring(2);

		//旧 / 特殊 设备：判断当天是否已经在数据库生成过赋码值
		String deviceNumInDB = "";
		// 获取五个表中 S 或 O 的最大赋码值
		deviceNumInDB = deviceNumMapper.selectByPattern(pattern, productType);
		if (deviceNumInDB != null && !deviceNumInDB.isEmpty()) {
			int serialNumber = Integer.parseInt(deviceNumInDB.substring(11)) + 1;
			SERIAL = String.format("%05d", serialNumber);
		} else {
			SERIAL = "00001";
		}

		String deviceNum = "";

		if (Integer.parseInt(SERIAL) >= MAX_SERIAL) {
			int machineNumber = Integer.parseInt(MACHINE_CODING.substring(2)) + 1;
			MACHINE_CODING = "CE" + String.format("%02d", machineNumber);
			SERIAL = "00001";
			deviceNum = productType  + MACHINE_CODING + pattern + SERIAL;
		} else {
			deviceNum = productType  + MACHINE_CODING + pattern + SERIAL;
			int serialNumber = Integer.parseInt(SERIAL) + 1;
			SERIAL = String.format("%05d", serialNumber);
		}

		return R.data(ResultCode.SUCCESS.getCode(), deviceNum, "");
	}


}
