package com.xh.vdm.bi.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.enums.*;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.utils.DictUtil;
import com.xh.vdm.bi.vo.request.*;
import com.xh.vdm.bi.vo.response.ExistingTerminalResponse;
import com.xh.vdm.bi.vo.response.ExportExistingResponse;
import com.xh.vdm.bi.vo.response.ExportExistingTerminalResponse;
import com.xh.vdm.biapi.entity.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springblade.common.annotation.Log;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.minio.MinioService;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.EasyExcelUtils;
import org.springblade.common.utils.StringUtils;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * @Description: 存量终端管理
 */
@RestController
@RequestMapping("/device/existingTerminal")
@Slf4j
@Api(value = "存量终端管理", tags = "存量终端管理接口")
public class BdmExistingTerminalController {

    @Resource
    private CETokenUtil ceTokenUtil;

    @Resource
    private BdmExistingTerminalService bdmExistingTerminalService;

    @Resource
    private DictUtil dictUtil;

    @Resource
    private IotCardService iotCardService;

    //定位终端
    @Resource
    private RnssDeviceService rnssDeviceService;

    //穿戴式终端
    @Resource
    private WearableDeviceService wearableDeviceService;

    //监测终端
    @Resource
    private MonitDeviceService monitDeviceService;

    //短报文终端
    @Resource
    private RdssDeviceService rdssDeviceService;

    //授时终端
    @Resource
    private PntDeviceService pntDeviceService;

    //设备大类小类
    @Resource
    private BdmDeviceClassService bdmDeviceClassService;

    @Resource
    private IBdmDeviceStatusService deviceStatusService;

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private MinioService minioService;

    @PostMapping("/list")
    @ApiOperation(value = "分页", notes = "分页")
    public R<IPage<ExistingTerminalResponse>> queryByPage(@RequestBody ExistingTerminalRequest request, BladeUser user) {
        log.info("rnssDevice>>将要执行数据处理");
        R<String> r0 = AuthUtils.isValidServiceRole(user);
        if (!r0.isSuccess()) {
            return R.fail(r0.getCode(), r0.getMsg());
        }
        //获取数据权限
        DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
        IPage<ExistingTerminalResponse> page = this.bdmExistingTerminalService.queryByPage(request, ceDataAuth);
		//数据类型转换
        enrichDataWithDict(page.getRecords());
        return R.data(page);
    }

    /**
     * 通过主键查询单条数据
     *
     * @param id 主键
     * @return 单条数据
     */
    @ApiOperation(value = "通过主键查询单条数据", notes = "通过主键查询单条数据")
    @GetMapping("/queryById")
    public R<ExistingTerminalResponse> queryById(@RequestParam Long id) {
        return R.data(this.bdmExistingTerminalService.queryById(id));
    }


    @ApiOperation(value = "新增存量终端", notes = "新增存量终端")
    @Log(menu = "存量终端管理", operation = Operation.INSERT, objectType = ObjectType.EXISTINGTERMINAL)
    @PostMapping("/save")
    public R add(@RequestBody ExistingTerminalRequest request) {
        List<BdmAbstractDevice> list = bdmExistingTerminalService.list(new LambdaQueryWrapper<BdmAbstractDevice>()
                .eq(BdmAbstractDevice::getUniqueId, request.getUniqueId())
                .eq(BdmAbstractDevice::getDeleted, 0));
        if (CollectionUtils.isNotEmpty(list)) {
            return R.fail("序列号已存在！");
        }
		String subClassCode = bdmDeviceClassService.saveDeviceClass(request.getClassCode(), request.getOtherTypes());
        if (StringUtils.isNotEmpty(subClassCode)) {
            request.setSubClassCode(subClassCode);
        }
        //将数据拆分到五张子表中
		String tableValue = bdmDeviceClassService.getTableValue(request.getClassCode(), request.getSubClassCode(), request.getOtherTypes());
        log.info("tableValue: {}", tableValue);
        BdmAbstractDevice bdmAbstractDevice = bdmExistingTerminalService.insertAbstract(request, tableValue);
        //存储五张子类表数据
        addDevice(tableValue, request);
        return R.data(ResultCode.SUCCESS.getCode(), bdmAbstractDevice.getId(), "新增成功");
    }


    /**
     * 修改存量终端
     *
     * @param request
     * @param user
     * @return
     */
    @Log(menu = "存量终端管理", operation = Operation.UPDATE, objectType = ObjectType.EXISTINGTERMINAL)
    @PostMapping("/update")
    public R edit(@RequestBody ExistingTerminalRequest request, BladeUser user) {
        // 对于不是新终端的序列号要校验唯一
        if (request.getSpecificity() != null && request.getSpecificity() != BdmTypeEnum.NEW.getValue()) {
            BdmAbstractDevice device = bdmExistingTerminalService.getOne(new LambdaQueryWrapper<BdmAbstractDevice>()
                    .eq(BdmAbstractDevice::getUniqueId, request.getUniqueId())
                    .ne(BdmAbstractDevice::getId, request.getId())
                    .eq(BdmAbstractDevice::getDeleted, 0));
            if (device != null) {
                return R.fail("序列号已存在！");
            }
        }

        BdmAbstractDevice device = this.bdmExistingTerminalService.getById(request.getId());

        List<String> list = iotCardService.getBaseMapper()
                .selectList(new QueryWrapper<BdmIotCard>()
                        .eq("deleted", 0)
                        .eq("device_id", request.getId())
                        .eq("device_type", request.getDeviceType()))
                .stream()
                .map(BdmIotCard::getNumber)
                .collect(Collectors.toList());
        device.setIotNumber(list.toString());

        BdmAbstractDevice bdmAbstractDevice = new BdmAbstractDevice();
        BeanUtils.copyProperties(request, bdmAbstractDevice);
        bdmAbstractDevice.setIotNumber(request.getIotNumber());
        //将数据拆分到五张子表中
        String tableValue = bdmDeviceClassService.getTableValue(request.getClassCode(), request.getSubClassCode(), request.getOtherTypes());
        log.info("tableValue: {}", tableValue);
		//历史中的子表是那张表
		BdmAbstractDevice one = bdmExistingTerminalService.getOne(new LambdaQueryWrapper<BdmAbstractDevice>().eq(BdmAbstractDevice::getId, request.getId()));
		String oldValue = bdmDeviceClassService.getTableValue(one.getClassCode(), one.getSubClassCode(), null);
		BdmAbstractDevice abstractDevice = bdmExistingTerminalService.updateAbstract(request, tableValue);
        if (abstractDevice == null) {
            return R.fail("编辑失败");
        }
		if (tableValue.equals(oldValue)) {
			//存储五张子类表数据
			updateDevice(tableValue, request);
		}else {
			//不相等，说明编辑的时候修改了应用方向和终端类型，改变了原有的表
			deleteDevice(oldValue, one.getUniqueId());
			//存储五张子类表数据
			addDevice(tableValue, request);
		}
        String compareResult = new CompareUtils<BdmAbstractDevice>().compare(device, abstractDevice);
        return R.data(ResultCode.SUCCESS.getCode(), compareResult, "编辑成功");
    }

    /**
     * 批量删除存量终端和子表数据
     *
     * @param ids 主键
     * @return 删除是否成功
     */
    @PostMapping("/batchDelete")
    public R deleteById(@RequestBody List<Long> ids) {
        //查询一下，然后调用删除子表
        List<BdmAbstractDevice> list = bdmExistingTerminalService.list(new LambdaQueryWrapper<BdmAbstractDevice>()
                .in(BdmAbstractDevice::getId, ids));
        if (CollectionUtils.isEmpty(list)) {
            return R.fail("未能查询到数据");
        }
        for (BdmAbstractDevice bdmAbstractDevice : list) {
            String tableValue = bdmDeviceClassService.getTableValue(bdmAbstractDevice.getClassCode(), bdmAbstractDevice.getSubClassCode(), bdmAbstractDevice.getOtherTypes());
            log.info("tableValue: {}", tableValue);
            //删除五张子类表数据
            R r = deleteDevice(tableValue, bdmAbstractDevice.getUniqueId());
            bdmExistingTerminalService.deleteById(bdmAbstractDevice.getId());
        }
        return R.data(ResultCode.SUCCESS.getCode(), "删除成功");
    }


    /**
     * 新增五种终端
     *
     * @param type
     * @param existingTerminalRequest
     */
    private void addDevice(String type, ExistingTerminalRequest existingTerminalRequest) {
        switch (type) {
            case "bdm_rnss_device":
                //拼接子接口对象
                RnssDeviceRequest reques = initRnssDevice(existingTerminalRequest);
                rnssDeviceService.insertRnssDevice(reques);
                break;
            case "bdm_wearable_device":
                WearableDeviceRequest wearableDeviceRequest = initWearable(existingTerminalRequest);
                wearableDeviceService.insertWearableDevice(wearableDeviceRequest);
                break;
            case "bdm_monit_device":
                MonitDeviceRequest monitDeviceRequest = initMonit(existingTerminalRequest);
                monitDeviceService.insertMonitDevice(monitDeviceRequest);
                break;
            case "bdm_rdss_device":
                RdssDeviceRequest rdssDeviceRequest = initRdss(existingTerminalRequest);
                rdssDeviceService.insertRdssDevice(rdssDeviceRequest);
                break;
            case "bdm_pnt_device":
                PntDeviceRequest pntDeviceRequest = initPnt(existingTerminalRequest);
                pntDeviceService.insertPntDevice(pntDeviceRequest);
                break;
            default:
                break;
        }
    }

    /**
     * 修改五种终端
     *
     * @param type
     * @param existingTerminalRequest
     */
    private void updateDevice(String type, ExistingTerminalRequest existingTerminalRequest) {
        switch (type) {
            case "bdm_rnss_device":
                //拼接子接口对象
                RnssDeviceRequest rnssDeviceRequest = initRnssDevice(existingTerminalRequest);
                BdmRnssDevice rnssDevice = rnssDeviceService.getOne(new LambdaUpdateWrapper<BdmRnssDevice>().eq(BdmRnssDevice::getUniqueId, existingTerminalRequest.getUniqueId()));
                if (ObjectUtils.isNotEmpty(rnssDevice)) {
                    rnssDeviceRequest.setId(rnssDevice.getId());
                    rnssDeviceService.updateRnssDevice(rnssDeviceRequest);
                }else {
					rnssDeviceService.insertRnssDevice(rnssDeviceRequest);
				}
                break;
            case "bdm_wearable_device":
                WearableDeviceRequest wearableDeviceRequest = initWearable(existingTerminalRequest);
                BdmWearableDevice wearableDevice = wearableDeviceService.getOne(new LambdaUpdateWrapper<BdmWearableDevice>().eq(BdmWearableDevice::getUniqueId, existingTerminalRequest.getUniqueId()));
                if (ObjectUtils.isNotEmpty(wearableDevice)) {
                    wearableDeviceRequest.setId(wearableDevice.getId());
                    wearableDeviceService.updateWearableDevice(wearableDeviceRequest);
                }else {
					wearableDeviceService.insertWearableDevice(wearableDeviceRequest);
				}
                break;
            case "bdm_monit_device":
                MonitDeviceRequest monitDeviceRequest = initMonit(existingTerminalRequest);
                BdmMonitDevice monitDevice = monitDeviceService.getOne(new LambdaUpdateWrapper<BdmMonitDevice>().eq(BdmMonitDevice::getUniqueId, existingTerminalRequest.getUniqueId()));
                if (ObjectUtils.isNotEmpty(monitDevice)) {
                    monitDeviceRequest.setId(monitDevice.getId());
                    monitDeviceService.updateMonitDevice(monitDeviceRequest);
                }else {
					monitDeviceService.insertMonitDevice(monitDeviceRequest);
				}
                break;
            case "bdm_rdss_device":
                RdssDeviceRequest rdssDeviceRequest = initRdss(existingTerminalRequest);
                BdmRdssDevice rdssDevice = rdssDeviceService.getOne(new LambdaUpdateWrapper<BdmRdssDevice>().eq(BdmRdssDevice::getUniqueId, existingTerminalRequest.getUniqueId()));
                if (ObjectUtils.isNotEmpty(rdssDevice)) {
                    rdssDeviceRequest.setId(rdssDevice.getId());
                    rdssDeviceService.updateRdssDevice(rdssDeviceRequest);
                }else {
					rdssDeviceService.insertRdssDevice(rdssDeviceRequest);
				}
                break;
            case "bdm_pnt_device":
                PntDeviceRequest pntDeviceRequest = initPnt(existingTerminalRequest);
                BdmPntDevice pntDevice = pntDeviceService.getOne(new LambdaUpdateWrapper<BdmPntDevice>().eq(BdmPntDevice::getUniqueId, existingTerminalRequest.getUniqueId()));
                if (ObjectUtils.isNotEmpty(pntDevice)) {
                    pntDeviceRequest.setId(pntDevice.getId());
                    pntDeviceService.updatePntDevice(pntDeviceRequest);
                }else {
					pntDeviceService.insertPntDevice(pntDeviceRequest);
				}
                break;
            default:
                break;
        }
    }


    /**
     * 删除子表
     *
     * @param type
     * @param uniqueId
     * @return
     */
    private R deleteDevice(String type, String uniqueId) {
        log.info("删除子表: type={}, uniqueId={}",
                type != null ? type : "null",
                uniqueId != null ? uniqueId : "null");
        Long deviceId = null;
        switch (type) {
            case "bdm_rnss_device":
                BdmRnssDevice rnssDevice = rnssDeviceService.getOne(new LambdaUpdateWrapper<BdmRnssDevice>().eq(BdmRnssDevice::getUniqueId, uniqueId));
                if (ObjectUtils.isNotEmpty(rnssDevice)) {
                    deviceId = rnssDevice.getId();
                    List<BdmRnssDevice> bdmRnssDevices = rnssDeviceService.list(new LambdaQueryWrapper<BdmRnssDevice>().in(BdmRnssDevice::getId, deviceId))
                            .stream()
                            .filter(bdmRnssDevice -> bdmRnssDevice.getTargetType() != 0)
                            .filter(bdmRnssDevice -> bdmRnssDevice.getTargetId() != 0)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(bdmRnssDevices)) {
                        return R.fail(ResultCode.FAILURE, "请先解绑人员或车辆");
                    }
                    rnssDeviceService.deleteById(deviceId);
                }
                break;
            case "bdm_wearable_device":
                BdmWearableDevice wearableDevice = wearableDeviceService.getOne(new LambdaUpdateWrapper<BdmWearableDevice>().eq(BdmWearableDevice::getUniqueId, uniqueId));
                if (ObjectUtils.isNotEmpty(wearableDevice)) {
                    deviceId = wearableDevice.getId();
                    List<BdmWearableDevice> bdmWearableDevices = wearableDeviceService.list(new LambdaQueryWrapper<BdmWearableDevice>().eq(BdmWearableDevice::getId, deviceId))
                            .stream()
                            .filter(bdmWearableDevice -> bdmWearableDevice.getTargetType() != 0)
                            .filter(bdmWearableDevice -> bdmWearableDevice.getTargetId() != 0)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(bdmWearableDevices)) {
                        return R.fail(ResultCode.FAILURE, "先解绑人员或基础设施");
                    }
                    wearableDeviceService.deleteById(wearableDevice.getId());
                }
                break;
            case "bdm_monit_device":
                BdmMonitDevice monitDevice = monitDeviceService.getOne(new LambdaUpdateWrapper<BdmMonitDevice>().eq(BdmMonitDevice::getUniqueId, uniqueId));
                if (ObjectUtils.isNotEmpty(monitDevice)) {
                    deviceId = monitDevice.getId();
                    List<BdmMonitDevice> bdmMonitDevices = monitDeviceService.list(new LambdaQueryWrapper<BdmMonitDevice>().eq(BdmMonitDevice::getId, deviceId))
                            .stream()
                            .filter(bdmMonitDevice -> bdmMonitDevice.getTargetType() != 0)
                            .filter(bdmMonitDevice -> bdmMonitDevice.getTargetId() != 0)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(bdmMonitDevices)) {
                        return R.fail(ResultCode.FAILURE, "先解绑人员或基础设施");
                    }
                    monitDeviceService.deleteById(monitDevice.getId());
                }
                break;
            case "bdm_rdss_device":
                BdmRdssDevice rdssDevice = rdssDeviceService.getOne(new LambdaUpdateWrapper<BdmRdssDevice>().eq(BdmRdssDevice::getUniqueId, uniqueId));
                if (ObjectUtils.isNotEmpty(rdssDevice)) {
                    deviceId = rdssDevice.getId();
                    List<BdmRdssDevice> bdmRdssDevices = rdssDeviceService.list(new LambdaQueryWrapper<BdmRdssDevice>().eq(BdmRdssDevice::getId, deviceId))
                            .stream()
                            .filter(bdmRdssDevice -> bdmRdssDevice.getTargetType() != 0)
                            .filter(bdmRdssDevice -> bdmRdssDevice.getTargetId() != 0)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(bdmRdssDevices)) {
                        return R.fail(ResultCode.FAILURE, "先解绑人员或基础设施");
                    }
                    rdssDeviceService.deleteById(rdssDevice.getId());
                }
                break;
            case "bdm_pnt_device":
                BdmPntDevice pntDevice = pntDeviceService.getOne(new LambdaUpdateWrapper<BdmPntDevice>().eq(BdmPntDevice::getUniqueId, uniqueId));
                if (ObjectUtils.isNotEmpty(pntDevice)) {
                    deviceId = pntDevice.getId();
                    List<BdmPntDevice> pntDeviceList = pntDeviceService.list(new LambdaQueryWrapper<BdmPntDevice>().eq(BdmPntDevice::getId, deviceId))
                            .stream()
                            .filter(bdmPntDevice -> bdmPntDevice.getTargetType() != 0)
                            .filter(bdmPntDevice -> bdmPntDevice.getTargetId() != 0).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(pntDeviceList)) {
                        return R.fail(ResultCode.FAILURE, "先解绑人员或基础设施");
                    }
                    pntDeviceService.deleteById(pntDevice.getId());
                }
                break;
            default:
                break;
        }
        QueryWrapper<BdmDeviceStatus> wrapper = new QueryWrapper<>();
        wrapper.eq("device_id", deviceId);
        deviceStatusService.remove(wrapper);
        return R.data(ResultCode.SUCCESS.getCode(), true, "删除成功");
    }


    /**
     * 定位终端
     *
     * @param existingTerminalRequest
     * @return
     */
    private RnssDeviceRequest initRnssDevice(ExistingTerminalRequest existingTerminalRequest) {
        RnssDeviceRequest rnssDeviceRequest = new RnssDeviceRequest();
        BeanUtils.copyProperties(existingTerminalRequest, rnssDeviceRequest);
		rnssDeviceRequest.setTerminalId(existingTerminalRequest.getDeviceNo());
		rnssDeviceRequest.setId(null);
        return rnssDeviceRequest;
    }

    /**
     * 穿戴终端
     *
     * @param existingTerminalRequest
     * @return
     */
    private WearableDeviceRequest initWearable(ExistingTerminalRequest existingTerminalRequest) {
        WearableDeviceRequest wearableDeviceRequest = new WearableDeviceRequest();
        BeanUtils.copyProperties(existingTerminalRequest, wearableDeviceRequest);
		wearableDeviceRequest.setTerminalId(existingTerminalRequest.getDeviceNo());
		wearableDeviceRequest.setId(null);
        return wearableDeviceRequest;
    }


    /**
     * 监测终端
     *
     * @param existingTerminalRequest
     * @return
     */
    private MonitDeviceRequest initMonit(ExistingTerminalRequest existingTerminalRequest) {
        MonitDeviceRequest monitDeviceRequest = new MonitDeviceRequest();
        BeanUtils.copyProperties(existingTerminalRequest, monitDeviceRequest);
		monitDeviceRequest.setTerminalId(existingTerminalRequest.getDeviceNo());
		monitDeviceRequest.setId(null);
        return monitDeviceRequest;
    }

    /**
     * 短报文终端
     *
     * @param existingTerminalRequest
     * @return
     */
    private RdssDeviceRequest initRdss(ExistingTerminalRequest existingTerminalRequest) {
        RdssDeviceRequest rdssDeviceRequest = new RdssDeviceRequest();
        BeanUtils.copyProperties(existingTerminalRequest, rdssDeviceRequest);
		rdssDeviceRequest.setTerminalId(existingTerminalRequest.getDeviceNo());
		rdssDeviceRequest.setId(null);
        return rdssDeviceRequest;
    }

    /**
     * 授时终端
     *
     * @param existingTerminalRequest
     * @return
     */
    private PntDeviceRequest initPnt(ExistingTerminalRequest existingTerminalRequest) {
        PntDeviceRequest pntDeviceRequest = new PntDeviceRequest();
        BeanUtils.copyProperties(existingTerminalRequest, pntDeviceRequest);
		pntDeviceRequest.setTerminalId(existingTerminalRequest.getDeviceNo());
		pntDeviceRequest.setId(null);
        return pntDeviceRequest;
    }

    /**
     * 查询设备大类列表
     *
     * @return
     */
    @GetMapping("/listMainDeviceClasses")
    public R<List<DeviceClassRequest>> listMainDeviceClasses() {
        List<BdmDeviceClass> list = bdmDeviceClassService.list(new LambdaQueryWrapper<BdmDeviceClass>().eq(BdmDeviceClass::getParentCode, "0"));
        if (CollectionUtils.isEmpty(list)) {
            return R.fail("未能查询到大类信息");
        }
        List<DeviceClassRequest> deviceClassRequestList = list.stream().map(DeviceClassRequest::dtoToVo).collect(Collectors.toList());
        return R.data(deviceClassRequestList);
    }


    /**
     * 查询子类
     *
     * @param code
     * @return
     */
    @GetMapping("/listSubClassesByMainClass")
    public R<List<DeviceClassRequest>> listSubClassesByMainClass(@RequestParam String code) {
        List<BdmDeviceClass> list = bdmDeviceClassService.list(new LambdaQueryWrapper<BdmDeviceClass>().eq(BdmDeviceClass::getParentCode, code));
        if (CollectionUtils.isEmpty(list)) {
            return R.fail("未能查询到子类信息");
        }
        List<DeviceClassRequest> deviceClassRequestList = list.stream().map(DeviceClassRequest::dtoToVo).collect(Collectors.toList());
        return R.data(deviceClassRequestList);
    }


    /**
     * 导出数据
     */
    @PostMapping("/export")
    public R<String> export(@RequestBody ExistingTerminalRequest request, BladeUser user) {
        R<String> r0 = AuthUtils.isValidServiceRole(user);
        if (!r0.isSuccess()) {
            return R.fail(r0.getCode(), r0.getMsg());
        }
        DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
        try {
            request.setCurrent(1);
            request.setSize(Integer.MAX_VALUE);
            IPage<ExistingTerminalResponse> list = this.bdmExistingTerminalService.queryByPage(request, ceDataAuth);
            if (list.getRecords().isEmpty()) {
                return R.fail("没有数据可导出");
            }
            Map<String, String> map = new HashMap<>();
            //数据转换
            enrichDataWithDict(list.getRecords());
            for (ExistingTerminalResponse response : list.getRecords()) {
                String key = BaseInfoConstants.RNSS_DEVICE_TYPE + "-" + response.getId();
                Map<String, Object> innerMap = new HashMap<>();
                innerMap.put("uniqueId", response.getUniqueId());
                innerMap.put("deviceNum", response.getDeviceNum());
                innerMap.put("category", response.getCategory());
                innerMap.put("deviceType", response.getDeviceType());
                innerMap.put("deptId", response.getDeptId());
                innerMap.put("iotProtocol", response.getIotProtocol());
                map.put(key, new ObjectMapper().writeValueAsString(innerMap));
            }

            redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_DEVICE, map);

            List<ExportExistingResponse> exportExistingResponseList = list.getRecords().stream().map(ExportExistingResponse::voToDto).collect(Collectors.toList());

            List<String> headNameList = Arrays.asList("应用方向", "终端类型", "序列号", "IMEI", "经度", "纬度", "设备安装位置", "入网方式", "入网运营商", "北斗卡号", "联系人", "联系方式", "终端型号", "物联网卡号", "终端编号", "厂商名称","视频通道个数");
            List<String> columnNameList = Arrays.asList("classCode", "subClassCode", "uniqueId", "imei", "longitude", "latitude", "deviceAddr", "inNetTypeName", "inNetProvider", "bdCardNumber", "contact", "contactPhone", "model", "iotNumber", "deviceNo", "manufacturerName","channelNum");
            String menu = "存量终端管理";
            try {
                // 使用ByteArrayOutputStream创建Excel文件
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                EasyExcelUtils.exportExcelToStream(
                        menu,
                        outputStream,
                        exportExistingResponseList,
                        headNameList,
                        columnNameList,
                        ExportExistingResponse.class
                );
                ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
                log.info("准备上传到minio服务器上");
                String minioFileUrl = minioService.uploadFile(menu, inputStream, outputStream.size());
                if (minioFileUrl != null) {
                    return R.data(minioFileUrl);
                } else {
                    return R.fail("文件上传失败");
                }
            } catch (Exception e) {
                log.info("文件上传失败 " + e.getMessage());
                return R.fail("文件上传失败");
            }
        } catch (Exception e) {
            log.error("导出失败", e);
            return R.fail("导出失败");
        }
    }

	/**
	 * 数据类型转换
	 * @param records
	 */
    private void enrichDataWithDict(List<ExistingTerminalResponse> records) {
        if (records.isEmpty()) {
            return;
        }

        Map<String, String> classMap = new HashMap<>();
        for (ClassCodeEnum type : ClassCodeEnum.values()) {
            classMap.put(type.getValue(), type.getLabel());
        }

        List<BdmDeviceClass> list = bdmDeviceClassService.list(new LambdaQueryWrapper<BdmDeviceClass>().ne(BdmDeviceClass::getParentCode, "0"));

        Map<String, String> subMap = new HashMap<>();
        for (BdmDeviceClass type : list) {
            subMap.put(type.getCode(), type.getClassName());
        }

        Map<Integer, String> netWorkMap = new HashMap<>();
        for (NetworkAccessTypeEnum type : NetworkAccessTypeEnum.values()) {
            netWorkMap.put(type.getCode(), type.getName());
        }

        Map<String, String> networkOperatorMap = new HashMap<>();
        for (NetworkOperatorEnum type : NetworkOperatorEnum.values()) {
            networkOperatorMap.put(type.getCode(), type.getName());
        }

        for (ExistingTerminalResponse response : records) {
            response.setClassCode(classMap.getOrDefault(String.valueOf(response.getClassCode()), null));
            response.setSubClassCode(subMap.getOrDefault(response.getSubClassCode(), null));
            response.setInNetTypeName(netWorkMap.getOrDefault(response.getInNetType(), null));
            response.setInNetProvider(networkOperatorMap.getOrDefault(response.getInNetProvider(), null));
        }
    }

    /**
     * 导入数据
     */
    @Log(menu = "存量终端管理", operation = Operation.IMPORT, objectType = ObjectType.EXISTINGTERMINAL)
    @PostMapping("/import")
	public R importExcel(@RequestParam("file") MultipartFile file,
						 @RequestParam Long deptId,
						 BladeUser user) {
		// 初始化返回结果和数据列表
		List<ExportExistingTerminalResponse> dataList;
		try {
			// 1. 验证文件有效性并读取数据
			dataList = readExcelData(file);
			if (dataList.isEmpty()) {
				return R.fail("数据为空");
			}

			// 2. 验证用户权限
			R<String> authResult = AuthUtils.isValidServiceRole(user);
			if (!authResult.isSuccess()) {
				return R.fail(authResult.getCode(), authResult.getMsg());
			}

			// 3. 处理导入业务逻辑
			Map<List<ExistingTerminalRequest>, List<ExportExistingTerminalResponse>> resultMap
				= bdmExistingTerminalService.importExcel(dataList, user.getUserId(), deptId);

			// 4. 处理导入结果
			return processImportResult(resultMap);

		} catch (Exception e) {
			log.error("导入Excel数据异常", e);
			return R.fail("导入失败：" + e.getMessage());
		}
	}

	/**
	 * 读取Excel文件数据
	 */
	private List<ExportExistingTerminalResponse> readExcelData(MultipartFile file) throws IOException {
		List<ExportExistingTerminalResponse> dataList = new ArrayList<>();

		try (InputStream inputStream = file.getInputStream();
			 Workbook workbook = new XSSFWorkbook(inputStream)) {
			Sheet sheet = workbook.getSheetAt(0);
			// 验证表格是否为空
			if (sheet.getLastRowNum() == 0) {
				log.info("表格不能为空");
				throw new RuntimeException("表格不能为空");
			}

			// 读取表头并验证必要列
			Map<String, Integer> headerMap = readHeader(sheet.getRow(0));
			validateRequiredColumns(headerMap);

			// 从第2行开始读取数据
			for (int i = 1; i <= sheet.getLastRowNum(); i++) {
				Row row = sheet.getRow(i);
				if (row == null) continue;

				ExportExistingTerminalResponse data = new ExportExistingTerminalResponse();
				populateDataFromRow(row, headerMap, data);
				dataList.add(data);
			}
		}

		return dataList;
	}

	/**
	 * 读取Excel表头
	 */
	private Map<String, Integer> readHeader(Row headerRow) {
		Map<String, Integer> headerMap = new HashMap<>();
		for (int i = 0; i < headerRow.getLastCellNum(); i++) {
			String header = getCellValueAsString(headerRow.getCell(i));
			headerMap.put(header, i);
		}
		return headerMap;
	}

	/**
	 * 验证必要列是否存在
	 */
	private R validateRequiredColumns(Map<String, Integer> headerMap) {
		// 定义必要列及其错误提示
		Map<String, String> requiredColumns = new HashMap<>();
		requiredColumns.put("应用方向", "Excel文件缺少必要的列（应用方向）");
		requiredColumns.put("终端类型", "Excel文件缺少必要的列（终端类型）");
		requiredColumns.put("其他的终端类型名称", "Excel文件缺少必要的列（其他的终端类型名称）");
		requiredColumns.put("序列号", "Excel文件缺少必要的列（序列号）");
		requiredColumns.put("IMEI", "Excel文件缺少必要的列（IMEI）");
		requiredColumns.put("经度", "Excel文件缺少必要的列（经度）");
		requiredColumns.put("纬度", "Excel文件缺少必要的列（纬度）");
		requiredColumns.put("设备安装位置", "Excel文件缺少必要的列（设备安装位置）");
		requiredColumns.put("入网方式", "Excel文件缺少必要的列（入网方式）");
		requiredColumns.put("入网运营商", "Excel文件缺少必要的列（入网运营商）");
		requiredColumns.put("北斗卡号", "Excel文件缺少必要的列（北斗卡号）");
		requiredColumns.put("联系人", "Excel文件缺少必要的列（联系人）");
		requiredColumns.put("联系方式", "Excel文件缺少必要的列（联系方式）");
		requiredColumns.put("终端型号", "Excel文件缺少必要的列（终端型号）");
		requiredColumns.put("物联网卡", "Excel文件缺少必要的列（物联网卡号）");
		requiredColumns.put("终端编号", "Excel文件缺少必要的列（终端编号）");
		requiredColumns.put("厂商名称", "Excel文件缺少必要的列（厂商名称）");
		requiredColumns.put("安装日期", "Excel文件缺少必要的列（安装日期）");
		requiredColumns.put("视频通道数", "Excel文件缺少必要的列（视频通道个数）");

		// 检查必要列是否存在
		for (Map.Entry<String, String> entry : requiredColumns.entrySet()) {
			if (!headerMap.containsKey(entry.getKey())) {
				return R.fail(entry.getValue());
			}
		}
		return R.success("校验通过");
	}

	/**
	 * 从行数据填充对象
	 */
	private void populateDataFromRow(Row row, Map<String, Integer> headerMap, ExportExistingTerminalResponse data) {
		// 读取应用方向
		setCellValue(row, headerMap, "应用方向", data::setClassCode);

		// 读取终端类型
		setCellValue(row, headerMap, "终端类型", data::setSubClassCode);

		// 读取其他终端类型
		setCellValue(row, headerMap, "其他的终端类型名称", data::setOtherTypes);

		// 读取序列号
		setCellValue(row, headerMap, "序列号", data::setUniqueId);

		// 读取IMEI
		setCellValue(row, headerMap, "IMEI", data::setImei);

		// 读取经度
		setNumericCellValue(row, headerMap, "经度", data::setLongitude);

		// 读取纬度
		setNumericCellValue(row, headerMap, "纬度", data::setLatitude);

		// 读取设备安装位置
		setCellValue(row, headerMap, "设备安装位置", data::setDeviceAddr);

		// 读取入网方式
		setCellValue(row, headerMap, "入网方式", data::setInNetTypeName);

		// 读取入网服务商
		setCellValue(row, headerMap, "入网运营商", data::setInNetProvider);

		// 读取北斗卡号
		setCellValue(row, headerMap, "北斗卡号", data::setBdCardNumber);

		// 读取联系人
		setCellValue(row, headerMap, "联系人", data::setContact);

		// 读取联系方式
		setCellValue(row, headerMap, "联系方式", data::setContactPhone);

		// 读取终端型号
		setCellValue(row, headerMap, "终端型号", data::setModel);

		// 读取物联网卡号
		setCellValue(row, headerMap, "物联网卡", data::setIotNumber);

		// 读取终端编号
		setCellValue(row, headerMap, "终端编号", data::setDeviceNo);

		// 读取厂商名称
		setCellValue(row, headerMap, "厂商名称", data::setManufacturerName);

		// 读取安装日期
		setCellValue(row, headerMap, "安装日期", data::setInstalldateName);

		// 读取视频通道个数
		setIntegerCellValue(row, headerMap, "视频通道数", data::setChannelNum);
	}

	/**
	 * 设置字符串类型单元格值
	 */
	private <T> void setCellValue(Row row, Map<String, Integer> headerMap, String columnName, Consumer<String> setter) {
		Integer index = headerMap.get(columnName);
		if (index != null) {
			String value = getCellValueAsString(row.getCell(index));
			setter.accept(value);
		}
	}

	/**
	 * 设置数值类型单元格值
	 */
	private <T> void setNumericCellValue(Row row, Map<String, Integer> headerMap, String columnName, Consumer<Double> setter) {
		Integer index = headerMap.get(columnName);
		if (index != null) {
			Cell cell = row.getCell(index);
			if (cell != null) {
				try {
					setter.accept(cell.getNumericCellValue());
				} catch (IllegalStateException e) {
					String value = getCellValueAsString(cell);
					if (value != null && !value.isEmpty()) {
						try {
							setter.accept(Double.parseDouble(value));
						} catch (NumberFormatException ex) {
							// 保持默认值，不做处理
						}
					}
				}
			}
		}
	}

	/**
	 * 设置整数类型单元格值
	 */
	private <T> void setIntegerCellValue(Row row, Map<String, Integer> headerMap, String columnName, Consumer<Integer> setter) {
		Integer index = headerMap.get(columnName);
		if (index != null) {
			Cell cell = row.getCell(index);
			if (cell != null) {
				try {
					setter.accept((int) cell.getNumericCellValue());
				} catch (IllegalStateException e) {
					String value = getCellValueAsString(cell);
					if (value != null && !value.isEmpty()) {
						try {
							setter.accept(Integer.parseInt(value));
						} catch (NumberFormatException ex) {
							// 保持默认值，不做处理
						}
					}
				}
			}
		}
	}

	/**
	 * 设置日期类型单元格值
	 */
	private <T> void setDateCellValue(Row row, Map<String, Integer> headerMap, String columnName, Consumer<Date> setter) {
		Integer index = headerMap.get(columnName);
		if (index != null) {
			Cell cell = row.getCell(index);
			if (cell != null) {
				try {
					setter.accept(cell.getDateCellValue());
				} catch (IllegalStateException e) {
					String value = getCellValueAsString(cell);
					if (value != null && !value.isEmpty()) {
						// 日期格式处理
						try {
							SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
							setter.accept(sdf.parse(value));
						} catch (ParseException ex) {
							try {
								SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
								setter.accept(sdf.parse(value));
							} catch (ParseException ex2) {
								// 保持默认值，不做处理
							}
						}
					}
				}
			}
		}
	}

	/**
	 * 处理导入结果
	 */
	private R processImportResult(Map<List<ExistingTerminalRequest>, List<ExportExistingTerminalResponse>> resultMap) {
		if (resultMap.isEmpty()) {
			return R.fail("导入处理结果为空");
		}

		Map.Entry<List<ExistingTerminalRequest>, List<ExportExistingTerminalResponse>> firstEntry
			= resultMap.entrySet().iterator().next();
		List<ExistingTerminalRequest> firstKey = firstEntry.getKey();
		List<ExportExistingTerminalResponse> firstValue = firstEntry.getValue();

		if (firstKey.size() == firstValue.size()) {
			for (ExistingTerminalRequest export : firstKey) {
				ExistingTerminalRequest toSave = new ExistingTerminalRequest();
				BeanUtil.copyProperties(export, toSave);
				addDevice(export.getTableValue(), toSave);
			}
			return R.data(ResultCode.SUCCESS.getCode(), "导入成功");
		}

		String menu = "存量终端管理";
		try {
			String filePath = minioService.exportToMinIO(menu, firstValue, ExportExistingTerminalResponse.class);
			return R.data(207, filePath);
		} catch (Exception e) {
			log.error("导出错误数据失败", e);
			return R.fail("导出错误数据失败");
		}
	}

    private String getCellValueAsString(Cell cell) {
        if (cell == null) return "";

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf((int) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }


}
