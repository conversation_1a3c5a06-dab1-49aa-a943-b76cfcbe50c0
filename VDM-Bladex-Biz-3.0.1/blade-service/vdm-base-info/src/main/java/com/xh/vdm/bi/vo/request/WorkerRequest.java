package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.common.dept.DeptIdAware;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Description: 人员信息入参
 */
@Data
@ExcelIgnoreUnannotated
public class WorkerRequest  implements DeptIdAware {

	private Long id;

	//姓名
	private String name;
	//目标类别
	private Integer targetType;
	//所属机构
	private Long deptId;
	@Override
	public Long getDeptId() {
		return deptId;
	}
	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	//岗位类别
	private Integer post;
	//从业类型
	private Integer industry;
	// 联系电话
	private String  phone;

	@NotNull(message = "工卡号不能为空")
	@ApiModelProperty(value = "工卡号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "工卡号")
	@ColumnWidth(22)
	private String wkno;

	private Integer deviceType;

	private Integer category;
	/**
	 * 绑定终端类型
	 */
	private Integer terminalType;
	/**
	 * 绑定序列号
	 */
	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;

	/**
	 * 导入失败的错误信息
	 */
	@ApiModelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "错误信息")
	@ColumnWidth(22)
	private String msg;


	private Integer current;

	private Integer size;

	// 非业务参数，导出时，由前端额外传入。
	private List<String> headNameList;

	// 非业务参数，导出时，由前端额外传入。
	private List<String> columnNameList;

	//导出时，id集合
	private List<Long> ids;

}

