package com.xh.vdm.bi.vo.response.gnProd;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xh.vdm.bi.node.INode;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 国能组织信息视图。
 */
@Data
public class BdsOrgVO implements INode<BdsOrgVO> {
	/** 组织id */
	private String id;
	/** 父组织id */
	private String parentId;
	/** 组织代码 */
	private String orgCode;
	/** 组织名称 */
	private String orgName;
	/** 子节点 */
	private List<BdsOrgVO> children;
	/** 已访问标记 */
	@JsonIgnore
	private boolean marked;

	@Override
	public List<BdsOrgVO> getChildren() {
		if (this.children == null) {
			this.children = new ArrayList<>();
		}
		return this.children;
	}

	public Boolean isMarked() {
		return marked;
	}

	public void marked() {
		marked = true;
	}
}
