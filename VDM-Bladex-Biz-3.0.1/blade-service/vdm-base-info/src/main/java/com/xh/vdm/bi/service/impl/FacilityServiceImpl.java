package com.xh.vdm.bi.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.converter.BdmAbstractTargetConverter;
import com.xh.vdm.bi.enums.DeviceTypeEnum;
import com.xh.vdm.bi.enums.IotProtocolEnum;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.mapper.FacilityMapper;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.FacilityRequest;
import com.xh.vdm.bi.vo.request.FacilityTerminalRequest;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.FacilityNoBingResponse;
import com.xh.vdm.bi.vo.response.FacilityResponse;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import com.xh.vdm.biapi.entity.BdmFacility;
import com.xh.vdm.interManager.feign.IMessageClient;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * (BdmFacility)表服务实现类
 */
@Service
@Slf4j
public class FacilityServiceImpl extends ServiceImpl<FacilityMapper, BdmFacility> implements FacilityService {
	@Resource
	private FacilityMapper facilityMapper;
	@Resource
	private MonitDeviceService monitDeviceService;
	@Resource
	private PntDeviceService pntDeviceService;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private IMessageClient messageClient;
	@Resource
	private IBdmDeviceStatusService deviceStatusService;
	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private IBdmVirtualTargetService virtualTargetService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
     * 分页查询
     *
     * @param facilityRequest 筛选条件
     * @param ceDataAuth
     * @return 查询结果
     */
	@Override
	public IPage<FacilityResponse> queryByPage(FacilityRequest facilityRequest, DataAuthCE ceDataAuth) {
		Page page = new Page<>();
		page.setSize(facilityRequest.getSize());
		page.setCurrent(facilityRequest.getCurrent());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.facilityMapper.queryAll(page, facilityRequest, response.getAccount(), response.getOrgList());
	}

	/**
	 * 新增数据
	 *
	 * @param facilityRequest 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmFacility insert(FacilityRequest facilityRequest) {

		BdmFacility bdmFacility = new BdmFacility();
		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.FACILITY_TARGET_TYPE, BaseInfoConstants.ZERO,BaseInfoConstants.ZERO);
		BeanUtils.copyProperties(facilityRequest, bdmFacility);
		bdmFacility.setId(targetId.nextId());
		bdmFacility.setCreateTime(new Date());
		bdmFacility.setCreateAccount(AuthUtil.getUserAccount());
		this.facilityMapper.insertFacility(bdmFacility);

		BdmFacility facility = getBaseMapper().selectById(bdmFacility.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(facility, abstractTarget);
		abstractTarget.setNumber(facility.getCode());
		abstractTarget.setCreateAccount(AuthUtil.getUserAccount());
		abstractTargetService.save(abstractTarget);

		Map<String, String> map = new HashMap<>();
		String key = facility.getTargetType() + "-" + facility.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", facility.getName());
		innerMap.put("targetType", facility.getTargetType());
		innerMap.put("deptId", facility.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setTargetId(facility.getId());
		deviceInfo.setTargetName(facility.getName());
		deviceInfo.setTargetCategory(facility.getCategory());
		deviceInfo.setDeptId(facility.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_facility", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("设施管理信息更新消息发送到kafka失败", e.getMessage());
		}

		//messageClient
		try {
			messageClient.facility(CommonConstant.OPER_TYPE_ADD, facility);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		return facility;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmFacility update(FacilityRequest request) {

		BdmFacility facilityInDB = this.facilityMapper.selectById(request.getId());

		BdmFacility bdmFacility = new BdmFacility();
		bdmFacility.setId(request.getId());
		bdmFacility.setName(request.getName() != null ? request.getName() : "");
		bdmFacility.setCategory(request.getCategory() != null ? request.getCategory() : 0);
		bdmFacility.setAddress(request.getAddress() != null ? request.getAddress() : "");
		bdmFacility.setTargetType(request.getTargetType() != null ? request.getTargetType() : TargetTypeEnum.FACILITY.getSymbol());
		bdmFacility.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		bdmFacility.setUpdateTime(new Date());
		bdmFacility.setGeometry(request.getGeometry() != null ? request.getGeometry() : "");
		bdmFacility.setCode(request.getCode() != null ? request.getCode() : "");
		this.facilityMapper.update(bdmFacility);

		BdmFacility facility = this.facilityMapper.selectById(request.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(facility, abstractTarget);
		abstractTarget.setNumber(facility.getCode());
		abstractTargetService.updateById(abstractTarget);

		String key = facility.getTargetType() + "-" + facility.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", facility.getName());
		innerMap.put("targetType", facility.getTargetType());
		innerMap.put("deptId", facility.getDeptId());
		Map<String, String> map = new HashMap<>();
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setTargetId(facility.getId());
		deviceInfo.setTargetName(facility.getName());
		deviceInfo.setTargetType(facility.getTargetType());
		deviceInfo.setTargetCategory(facility.getCategory());
		deviceInfo.setDeptId(facility.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_facility", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("设施管理信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.facility(CommonConstant.OPER_TYPE_UPDATE, facility);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		//@zhouxw modify 2025-06-27 修改监管对象，终端不再同步更改组织
		/*if(!facilityInDB.getDeptId().equals(facility.getDeptId())){
			// 对于MntDevice的更新
			pntDeviceService.updateDept(facility.getId(),facility.getTargetType(),facility.getDeptId());

			// 对于MonitDevic的更新
			monitDeviceService.updateDept(facility.getId(),facility.getTargetType(),facility.getDeptId());

			// 对于BdmAbstractDevice的更新
			abstractDeviceService.updateDept(facility.getId(),facility.getTargetType(),facility.getDeptId());
		}*/

		return facility;
	}

	/**
	 * 通过主键删除数据
	 *
	 * @param ids 主键
	 * @return 是否成功
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean deleteByIds(Long[] ids) {
		List<BdmFacility> facilities = baseMapper.selectList(new QueryWrapper<BdmFacility>().in("id", ids));
		boolean result = this.facilityMapper.deleteByIds(ids) > 0;
		if (result) {
			for (Long id : ids) {
				String key = BaseInfoConstants.FACILITY_TARGET_TYPE + "-" + id;
				redisTemplate.delete(BaseInfoConstants.BASEINFO_TARGET + ":" + key);
			}
			this.monitDeviceService.deleteByTargetIds(ids);
			this.pntDeviceService.deleteByTargetIds(ids);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.DELETE);
			Long lastId = ids[ids.length - 1];
			deviceInfo.setTargetId(lastId);
			deviceInfo.setTargetIds(CollUtil.newHashSet(ids));
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_facility", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("设施管理信息更新消息发送到kafka失败", e);
			}
			//messageClient
			try {
				messageClient.facilityBatch(CommonConstant.OPER_TYPE_DELETE, facilities);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
			//更新bdm_abstract_target
			abstractTargetService.deleteByIds(ids);
			//更新bdm_abstract_device
			abstractDeviceService.deleteByTargetIds(ids, TargetTypeEnum.FACILITY.getSymbol());
		}
		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean connectTerminal(List<FacilityTerminalRequest> list, Long id, Long deptId) {
		List<BdmAbstractDevice> abstractDevices = abstractDeviceService.getBaseMapper()
			.selectList(new QueryWrapper<BdmAbstractDevice>()
				.eq("target_id", id)
				.eq("target_type", TargetTypeEnum.TEMPORARY.getSymbol())
				.select("id,target_type,target_id,device_type,unique_id")
			);
//
//		List<String> uniqueIdList = new ArrayList<>();
//		if(!abstractDevices.isEmpty()){
//			//bdm_device_status更新
//			for (BdmAbstractDevice request:abstractDevices) {
//				QueryWrapper<BdmDeviceStatus> wrapper = new QueryWrapper<>();
//				wrapper.eq("device_id", request.getId());
//				wrapper.eq("device_type",request.getDeviceType());
//				wrapper.eq("target_id", request.getTargetId());
//				wrapper.eq("target_type", request.getTargetType());
//				deviceStatusService.remove(wrapper);
//
//				uniqueIdList.add(request.getUniqueId());
//			}
//
//			// bdm_virtual_target数据 对abstractDevices 进行恢复
//			virtualTargetService.updateBatch(uniqueIdList);
//		}

		//根据用户ID去清空原来绑定的终端数据
		monitDeviceService.updateByDeviceId(id,TargetTypeEnum.FACILITY.getSymbol());
		pntDeviceService.updateByDeviceId(id,TargetTypeEnum.FACILITY.getSymbol());
		//bdm_abstract_device更新绑定关系
		abstractDeviceService.unbinding(id,TargetTypeEnum.FACILITY.getSymbol());

		if(!list.isEmpty()){
			//新增要绑定的终端
			List<FacilityTerminalRequest> monits = list.stream()
				.filter(w -> DeviceTypeEnum.MONIT.getSymbol().equals(w.getDeviceType()))
				.collect(Collectors.toList());
			if (!monits.isEmpty()) {
				monitDeviceService.updateBatchByTerminalId(monits, id, TargetTypeEnum.FACILITY.getSymbol(), deptId);
			}

			List<FacilityTerminalRequest> pnts = list.stream()
				.filter(w -> DeviceTypeEnum.PNT.getSymbol().equals(w.getDeviceType()))
				.collect(Collectors.toList());
			if (!pnts.isEmpty()) {
				pntDeviceService.updateBatchByTerminalId(pnts, id, TargetTypeEnum.FACILITY.getSymbol(), deptId);
			}

			abstractDeviceService.bindFacility(list, id, TargetTypeEnum.FACILITY.getSymbol(), deptId);

			List<String> uniqueIds = list.stream()
					.filter(request -> IotProtocolEnum.JT808.getCode().equals(request.getIotProtocol()))
					.map(FacilityTerminalRequest::getUniqueId)
					.collect(Collectors.toList());
			// bdm_virtual_target数据 对于list中的808进行删除。
			if(!uniqueIds.isEmpty()){
				virtualTargetService.updateByUniqueId(uniqueIds);
			}
		}

		BdmFacility facility = baseMapper.selectById(id);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.BIND);
		deviceInfo.setTargetId(facility.getId());
		deviceInfo.setTargetName(facility.getName());
		deviceInfo.setTargetType(facility.getTargetType());
		deviceInfo.setTargetCategory(facility.getCategory());
		deviceInfo.setDeptId(facility.getDeptId());
		Set<Long> ids = abstractDevices.stream().map(BdmAbstractDevice::getId).collect(Collectors.toSet());
		Set<Long> idList = list.stream().map(FacilityTerminalRequest::getId).collect(Collectors.toSet());
		ids.addAll(idList);
		deviceInfo.setDeviceIds(ids);
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_facility", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("设施管理信息更新消息发送到kafka失败", e);
		}

		return true;
	}

	@Override
	public IPage<FacilityNoBingResponse> selectNoBind(DeviceNoBindRequest deviceNoBindRequest, DataAuthCE ceDataAuth) {
		IPage<FacilityNoBingResponse> responseIPage = new Page<>();
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		if (deviceNoBindRequest.getDeviceType() == null) {
			// bdm_abstract_target数据进行分页查询
			responseIPage = abstractDeviceService.selectNoBind(deviceNoBindRequest, response.getAccount(), response.getOrgList());
		}
		if (DeviceTypeEnum.MONIT.getSymbol().equals(deviceNoBindRequest.getDeviceType())) {
			responseIPage = monitDeviceService.selectNoBind(deviceNoBindRequest, response.getAccount(), response.getOrgList());
		} else if (DeviceTypeEnum.PNT.getSymbol().equals(deviceNoBindRequest.getDeviceType())) {
			responseIPage = pntDeviceService.selectNoBind(deviceNoBindRequest, response.getAccount(), response.getOrgList());
		}
		return responseIPage;
	}

	@Override
	public List<FacilityNoBingResponse> terminalInfo(Long id) {
		List<FacilityNoBingResponse> resultList = abstractDeviceService.selectBingByFacility(id, TargetTypeEnum.FACILITY.getSymbol());
		return resultList;
	}

	@Override
	public List<FacilityNoBingResponse> selectBind(DeviceNoBindRequest deviceNoBindRequest, Long id) {
		List<FacilityNoBingResponse> resultList = abstractDeviceService.selectBingByFacility(id, TargetTypeEnum.FACILITY.getSymbol());
		return resultList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<FacilityRequest> importExcel(List<FacilityRequest> facilityRequestList) {
		List<FacilityRequest> duplicateRequests = getDuplicateRequests(facilityRequestList);
		List<FacilityRequest> requests = new ArrayList<>(facilityRequestList);
		// 去重后的数据（去除所有重复数据）
		requests.removeAll(duplicateRequests);

		if (!requests.isEmpty()) {
			List<String> codeList = requests.stream()
					.map(FacilityRequest::getCode)
					.collect(Collectors.toList());
			//数据库的数据
			QueryWrapper<BdmFacility> queryWrapper = new QueryWrapper<>();
			if (!codeList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(queryWrapper, "code", codeList);
			}
			queryWrapper.eq("deleted",0);
			queryWrapper.select("code");
			List<String> codeExitList = this.facilityMapper.selectList(queryWrapper)
					.stream()
					.map(BdmFacility::getCode)
					.collect(Collectors.toList());

			List<BdmFacility> facilityList = new ArrayList<>();
			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.FACILITY_TARGET_TYPE, BaseInfoConstants.ZERO,BaseInfoConstants.ZERO);
			Map<String, String> map = new HashMap<>();
			List<BdmAbstractTarget> bdmAbstractTargets = new ArrayList<>();
			List<Long> arrayList = new ArrayList<>();

			for (FacilityRequest request : requests) {
				if (codeExitList.contains(request.getCode())) {
					request.setMsg("基础设施编码已存在");
					duplicateRequests.add(request);
				} else {
					BdmFacility facility = new BdmFacility();
					facility.setId(targetId.nextId());
					facility.setName(request.getName() != null ? request.getName() : "");
					facility.setCategory(request.getCategory() != null ? request.getCategory() : 0);
					facility.setAddress(request.getAddress() != null ? request.getAddress() : "");
					facility.setTargetType(TargetTypeEnum.FACILITY.getSymbol());
					facility.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
					facility.setCreateTime(new Date());
					facility.setDeleted(0);
					facility.setGeometry(request.getGeometry() != null ? request.getGeometry() : "");
					facility.setCode(request.getCode() != null ? request.getCode() : "");
					facilityList.add(facility);

					request.setId(facility.getId());
					arrayList.add(facility.getId());

					String key = facility.getTargetType() + "-" + facility.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", facility.getName());
					innerMap.put("targetType", facility.getTargetType());
					innerMap.put("deptId", facility.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}

					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(facility, abstractTarget);
					abstractTarget.setNumber(facility.getCode());
					bdmAbstractTargets.add(abstractTarget);
				}
			}

			if (!facilityList.isEmpty()) {
				//将剩余的数据批量插入数据库
				MapperUtils.splitListByCapacity(facilityList, MapperUtils.DEFAULT_CAPACITY)
					.forEach(facilities -> this.facilityMapper.insertBatch(facilities));

				MapperUtils.splitListByCapacity(bdmAbstractTargets, MapperUtils.DEFAULT_CAPACITY)
					.forEach(abstractTargetList -> this.abstractTargetService.insertBatch(abstractTargetList));

				if(!map.isEmpty()){
					redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);
				}

				BdmFacility lastFacility = facilityList.get(facilityList.size() - 1);
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.IMPORT);
				deviceInfo.setTargetId(lastFacility.getId());
				Set<Long> ids = facilityList.stream().map(BdmFacility::getId).collect(Collectors.toSet());
				deviceInfo.setTargetIds(ids);

				deviceInfo.setTargetCategory(lastFacility.getCategory());
				deviceInfo.setTargetName(lastFacility.getName());
				deviceInfo.setDeptId(lastFacility.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_facility", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("设施管理信息更新消息发送到kafka失败", e);
				}
				QueryWrapper<BdmFacility> wrapper = new QueryWrapper<>();
				if (!arrayList.isEmpty()) {
					MybatisPlusWrapperUitls.longListIn(wrapper, "id", arrayList);
					List<BdmFacility> facilities = baseMapper.selectList(wrapper);
					//messageClient
					try {
						messageClient.facilityBatch(CommonConstant.OPER_TYPE_ADD, facilities);
					} catch (Exception e) {
						log.error("消息发送到messageClient失败：" + e.getMessage());
					}
				}
			}
		}
		return duplicateRequests;
	}

	/**
	 * 批量更新所属机构
	 * @param batchUpdateRequest
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest) {
		int update = facilityMapper.batchUpdate(batchUpdateRequest.getIds(),batchUpdateRequest.getDeptId());
		if (update == 0) {
			return false;
		}
		//查询出所有的本次要更新的字段
		List<BdmFacility> bdmFacilityList = facilityMapper.selectList(new LambdaQueryWrapper<BdmFacility>().in(BdmFacility::getId, batchUpdateRequest.getIds()));
		//转换
		List<BdmAbstractTarget> bdmAbstractTargetList = bdmFacilityList.stream().map(BdmAbstractTargetConverter::toBdmFacility).collect(Collectors.toList());
		//更新主表
		abstractTargetService.saveOrUpdateBatch(bdmAbstractTargetList);
		//批量更新redis和kafka
		batchProcess(bdmFacilityList);
		if (update > 0){
			return true;
		}
		return false;
	}

	/**
	 * 批量更新redis
	 * 批量发送kafka
	 */
	private void batchProcess(List<BdmFacility> bdmFacilityList){
		for (BdmFacility facility : bdmFacilityList) {
			String key = facility.getTargetType() + "-" + facility.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("targetName", facility.getName());
			innerMap.put("targetType", facility.getTargetType());
			innerMap.put("deptId", facility.getDeptId());
			Map<String, String> map = new HashMap<>();
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			//todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.OTHER);
			deviceInfo.setTargetId(facility.getId());
			deviceInfo.setTargetName(facility.getName());
			deviceInfo.setTargetType(facility.getTargetType());
			deviceInfo.setTargetCategory(facility.getCategory());
			deviceInfo.setDeptId(facility.getDeptId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_facility", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("设施管理信息更新消息发送到kafka失败", e);
			}
			//messageClient
			try {
				messageClient.facility(CommonConstant.OPER_TYPE_UPDATE, facility);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

	/**
	 * 重复数据
	 *
	 * @param list
	 * @return
	 */
	public List<FacilityRequest> getDuplicateRequests(List<FacilityRequest> list) {
		// 1. 统计每个 code 出现的次数
		Map<String, Long> countMap = list.stream()
			.collect(Collectors.groupingBy(
				FacilityRequest::getCode,
				Collectors.counting()));

		// 2. 过滤出重复的 code
		List<String> duplicates = countMap.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		// 3. 将含有重复 code 的 FacilityRequest 对象添加到 duplicateRequests 列表中
		return list.stream()
			.filter(facilityRequest -> duplicates.contains(facilityRequest.getCode()))
			.peek(facilityRequest -> facilityRequest.setMsg("导入的数据基础设施编码重复"))
			.collect(Collectors.toList());
	}

}
