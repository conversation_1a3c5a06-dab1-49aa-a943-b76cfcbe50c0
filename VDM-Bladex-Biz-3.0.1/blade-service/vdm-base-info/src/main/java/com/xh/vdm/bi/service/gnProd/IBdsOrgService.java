package com.xh.vdm.bi.service.gnProd;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.entity.gnProd.BdsOrg;
import com.xh.vdm.bi.vo.response.gnProd.BdsOrgVO;

import java.util.List;

/**
 * 国能组织服务接口。
 */
public interface IBdsOrgService extends IService<BdsOrg> {
	/**
	 * 获取指定产业板块下的组织。
	 * @param sectors 产业板块列表
	 * @return 符合条件的组织列表
	 */
	List<BdsOrgVO> getBdsOrgWith(List<String> sectors);
}
