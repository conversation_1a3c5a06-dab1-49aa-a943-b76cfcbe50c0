package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.dept.DeptIdAware;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 *	矿用卡车管理入参
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ExcelIgnoreUnannotated
public class MiningTruckRequest implements DeptIdAware {

	private Long id;

	/**
	 * 车牌编号
	 */
	@NotNull(message = "车牌编号不能为空")
	@ApiModelProperty(value = "车牌编号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "车牌编号")
	@ColumnWidth(22)
	private String number;
	/**
	 * 车辆类型
	 */
	private Integer category;
	/**
	 * 目标类别
	 */
	private Integer targetType;
	/**
	 * 所属机构
	 */
	private Long deptId;

	@Override
	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}
	/**
	 * 车架号
	 */
	private String vin;
	/**
	 * 最大马力，单位kw
	 */
	private Float maxPower;
	/**
	 * 制造商
	 */
	private String manufacturer;
	/**
	 * 额定载重，单位t
	 */
	private Float ratedLoad;
	/**
	 * 车辆型号
	 */
	private String model;

	/**
	 * 导入失败的错误信息
	 */
	@ApiModelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = "错误信息")
	@ColumnWidth(22)
	@TableField(exist = false)
	private String msg;
	@TableField(exist = false)
	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;
	/**	 导出动态表头中文名*/
	@TableField(exist = false)
	private List headNameList;
	/**	 导出动态表头字段名*/
	@TableField(exist = false)
	private List columnNameList;
	/**	 导出数据的id集合*/
	private List<Long> ids;
}
