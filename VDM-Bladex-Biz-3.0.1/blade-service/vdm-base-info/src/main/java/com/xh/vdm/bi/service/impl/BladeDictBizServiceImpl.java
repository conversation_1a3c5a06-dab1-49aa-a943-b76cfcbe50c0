package com.xh.vdm.bi.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.entity.BladeDictBiz;
import com.xh.vdm.bi.mapper.BladeDictBizMapper;
import com.xh.vdm.bi.service.IBladeDictBizService;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 业务字典表 服务实现类
 * </p>
 */
@Service
public class BladeDictBizServiceImpl extends ServiceImpl<BladeDictBizMapper, BladeDictBiz> implements IBladeDictBizService {

	@Resource
	private BladeDictBizMapper bladeDictBizMapper;

	@Override
	public  Map<String, String> select(String code) {
//		List<BladeDictBiz> list = bladeDictBizMapper.selectList(
//			new QueryWrapper<BladeDictBiz>().eq("code",code)
//				.ne("parent_id", 0).eq("is_deleted", 0));
		List<BladeDictBiz> list = bladeDictBizMapper.getDictBizList(code);
		Map<String, String> map = new HashMap<>();
		if (list != null && !list.isEmpty()) {
			for (BladeDictBiz dict : list) {
				map.put(dict.getDictKey(), dict.getDictValue());
			}
		}
		return map;
	}

	@Override
	public Map<String, String> selectDevModel(String keyword) {
		QueryWrapper<BladeDictBiz> wrapper = new QueryWrapper<BladeDictBiz>()
			.eq("tenant_id", CommonConstant.DEFAULT_TENANT_ID)
			.eq("code", CommonConstant.DICT_CODE_DEVICE_MODEL)
			.gt("parent_id", 0).eq("is_deleted", 0);

		// 添加关键字模糊查找
		if (StringUtil.isNotBlank(keyword)) {
			wrapper.nested(i -> i.like("dict_key", keyword).or().like("dict_value", keyword));
		}

		List<BladeDictBiz> list = bladeDictBizMapper.selectList(wrapper);

		Map<String, String> map = new HashMap<>();
		if (list != null && !list.isEmpty()) {
			for (BladeDictBiz dict : list) {
				map.put(dict.getDictKey(), dict.getDictValue());
			}
		}
		return map;
	}

	@Override
	public Map<String, String> selectDevModelUsageMap(String usage) {
		QueryWrapper<BladeDictBiz> wrapper = new QueryWrapper<BladeDictBiz>()
			.eq("tenant_id", CommonConstant.DEFAULT_TENANT_ID)
			.eq("code", CommonConstant.DICT_CODE_DEV_MODEL_USAGE_MAP)
			.gt("parent_id", 0).eq("is_deleted", 0);

		// 添加关键字模糊查找
		if (StringUtil.isNotBlank(usage)) {
			wrapper.eq("dict_value", usage);
		}

		List<BladeDictBiz> list = bladeDictBizMapper.selectList(wrapper);

		Map<String, String> map = new HashMap<>();
		if (list != null && !list.isEmpty()) {
			for (BladeDictBiz dict : list) {
				map.put(dict.getDictKey(), dict.getDictValue());
			}
		}
		return map;
	}

	@Override
	public List<Integer> getScenarios(Integer scenario) {
		QueryWrapper<BladeDictBiz> wrapper = new QueryWrapper<BladeDictBiz>()
			.eq("tenant_id", CommonConstant.DEFAULT_TENANT_ID)
			.eq("code", CommonConstant.DICT_CODE_SCENARIO)
			.gt("parent_id", 0).eq("is_deleted", 0)
			.likeRight("dict_key", String.valueOf(scenario));

		List<BladeDictBiz> list = bladeDictBizMapper.selectList(wrapper);

		List<Integer> result = new ArrayList<>();
		if (list != null && !list.isEmpty()) {
			for (BladeDictBiz dict : list) {
				result.add(Integer.parseInt(dict.getDictKey()));
			}
		}
		return result;
	}

	@Override
	public List<Map<String, Object>> deviceType(String bdmDeviceType, Long parentId) {
		List<Map<String, Object>> resultList = new ArrayList<>();
		List<BladeDictBiz> list = bladeDictBizMapper.selectList(new QueryWrapper<BladeDictBiz>()
			.eq("code", bdmDeviceType)
			.eq("parent_id", parentId)
			.eq("is_deleted",0)
		);
		for (BladeDictBiz bladeDictBiz : list) {
			Map<String, Object> resultMap = new HashMap<>();
			resultMap.put("value", bladeDictBiz.getDictKey());
			resultMap.put("label", bladeDictBiz.getDictValue());
			List<Map<String, Object>> children = deviceType(bdmDeviceType, bladeDictBiz.getId());
			if (!children.isEmpty()) {
				resultMap.put("children", children);
			}
			resultList.add(resultMap);
		}
		return resultList;
	}
}
