package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.MiningTruckRequest;
import com.xh.vdm.bi.vo.response.MiningTruckResponse;
import com.xh.vdm.biapi.entity.BdmMiningTruck;
import org.springblade.core.mp.support.Query;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmMiningTruck)表服务接口
 */
public interface IBdmMiningTruckService extends IService<BdmMiningTruck> {

	/**
     * 分页查询
     *
     * @param request    筛选条件
     * @param query      分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<MiningTruckResponse> queryByPage(MiningTruckRequest request, Query query, DataAuthCE ceDataAuth);

	/**
	 * 新增数据
	 *
	 * @param request
	 * @return
	 */
	BdmMiningTruck insert(MiningTruckRequest request);

	/**
	 * 修改数据
	 *
	 * @param request
	 * @return
	 */
	BdmMiningTruck update(MiningTruckRequest request);

	/**
	 * 删除数据
	 *
	 * @param ids
	 * @return
	 */
	boolean deleteByIds(Long[] ids);

	/**
	 * 导入--批量插入
	 *
	 * @param list
	 * @return
	 */
	List<MiningTruckRequest> importExcel(List<MiningTruckRequest> list);


	Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest);
}
