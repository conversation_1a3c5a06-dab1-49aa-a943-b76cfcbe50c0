package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.RdssDeviceRequest;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.RdssDeviceResponse;
import com.xh.vdm.bi.vo.response.WorkerBingResponse;
import com.xh.vdm.biapi.entity.BdmRdssDevice;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * @Description: 短报文终端管理
 */
public interface RdssDeviceService extends IService<BdmRdssDevice> {

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	RdssDeviceResponse queryById(Long id);

	/**
     * 分页查询
     *
     * @param rdssDeviceService 筛选条件
     * @param ceDataAuth
     * @return 查询结果
     */
	IPage<RdssDeviceResponse> queryByPage(RdssDeviceRequest rdssDeviceService, DataAuthCE ceDataAuth);

	/**
	 * 新增数据
	 *
	 * @param rdssDeviceService 实例对象
	 * @return 实例对象
	 */
	BdmRdssDevice insert(RdssDeviceRequest rdssDeviceService);

	BdmRdssDevice insertRdssDevice(RdssDeviceRequest rdssDeviceService);

	/**
	 * 修改数据
	 *
	 * @param rdssDeviceService 实例对象
	 * @return 实例对象
	 */
	BdmRdssDevice update(RdssDeviceRequest rdssDeviceService);

	BdmRdssDevice updateRdssDevice(RdssDeviceRequest rdssDeviceService);

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 是否成功
	 */
	boolean deleteById(Long id);

    boolean deleteByIds(Long[] ids);

	IPage<PersonNoBingResponse> select(DeviceNoBindRequest deviceNoBindRequest, String account, String deptIds);

	List<RdssDeviceRequest> importExcel(List<RdssDeviceRequest> list, Long userId);

	void updateBatchByWorkerId(Long id, Integer targetType);

	void updateBatch(List<PersonTerminalRequest> rdssList, Long id, Integer targetType, String targetName, Long deptId);

	List<PersonNoBingResponse> selectBindByWorkId(Long id, Integer targetType);

	List<WorkerBingResponse> selectByWorkId(Long id, Integer targetType);

	void deleteByTargetIds(Long[] ids, Integer targetType);

    long countByUserRole(DataAuthCE ceDataAuth);

    void updateDept(Long id, Integer targetType, Long deptId);

	int bindTarget(Long deviceId, Long targetId, Integer targetType,String targetName);
}
