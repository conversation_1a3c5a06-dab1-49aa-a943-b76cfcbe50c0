package com.xh.vdm.bi.vo.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *	车辆实体返参
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ExcelIgnoreUnannotated
public class VehicleResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 车牌编号
     */
	@ExcelProperty(value = "车辆编号",index = 1)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
    private String number;
	/**
	 * 车辆类型
	 */
	@ExcelProperty(value = "车辆类型",index = 4)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String category;
	private String categoryName;
	/**
	 * 目标类别
	 */
	@ExcelProperty(value = "监控对象",index = 2)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String targetType;
	private String targetTypeName;
	/**
	 * 所属机构
	 */
	private Long deptId;

	@ExcelProperty(value = "所属机构",index = 3)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String deptName;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 创建时间
     */
	@ExcelProperty(value = "创建时间",index = 7)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
    private String createTime;
    /**
     * 删除标记：0-未删除，1-已删除
     */
    private Integer deleted;

	@ExcelProperty(value = "绑定终端类型",index = 5)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String  terminalCategories;

	@ExcelProperty(value = "绑定序列号",index = 6)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;
	/**
	 * 车架号
	 */
	private String vin;
	/**
	 * 最大马力，单位kw
	 */
	private Float maxPower;
	/**
	 * 制造商
	 */
	private String manufacturer;
	/**
	 * 额定载重，单位t
	 */
	private Float ratedLoad;
	/**
	 * 车辆型号
	 */
	private String model;
}
