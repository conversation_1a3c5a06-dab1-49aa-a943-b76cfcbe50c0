package com.xh.vdm.bi.aop;

import cn.hutool.core.util.ObjectUtil;
import com.xh.vdm.bi.entity.BdmUsualLog;
import com.xh.vdm.bi.enums.DeviceTypeEnum;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.service.BdmUsualLogService;
import com.xh.vdm.bi.vo.request.TargetDeviceRequest;
import com.xh.vdm.bi.vo.request.VehicleTerminalRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springblade.common.annotation.Log;
import org.springblade.common.dept.DeptIdAware;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.IPUtils;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 操作日志记录处理
 */
@Slf4j
@Aspect
@Component
public class LogAspect {
	@Resource
	private BdmUsualLogService bulUsualLogService;

	private static final Pattern ID_PATTERN = Pattern.compile("id=(\\d+)");

	/**
	 * 处理完请求后执行
	 *
	 * @param joinPoint 切点
	 */
	@AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
	public void doAfterReturning(JoinPoint joinPoint, Log controllerLog, Object jsonResult) {
		BladeUser user = new BladeUser();
		Object[] args = joinPoint.getArgs();
		// 遍历参数数组，查找 BladeUser 类型的参数
		for (Object arg : args) {
			if (arg instanceof BladeUser) {
				user = (BladeUser) arg;
				break;
			}
		}
		// 如果没有找到 BladeUser，您可以选择抛出异常或记录日志
		if (user == null) {
			log.warn("未找到 BladeUser 参数");
		}
		handleLog(joinPoint, controllerLog, null, jsonResult, user);
	}

	/**
	 * 拦截异常操作
	 *
	 * @param joinPoint 切点
	 * @param e         异常
	 */
	@AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
	public void doAfterThrowing(JoinPoint joinPoint, Log controllerLog, Exception e) {
		BladeUser user = new BladeUser();
		Object[] args = joinPoint.getArgs();
		// 遍历参数数组，查找 BladeUser 类型的参数
		for (Object arg : args) {
			if (arg instanceof BladeUser) {
				user = (BladeUser) arg;
				break;
			}
		}
		// 如果没有找到 BladeUser，您可以选择抛出异常或记录日志
		if (user == null) {
			log.warn("未找到 BladeUser 参数");
		}
		handleLog(joinPoint, controllerLog, e, null, user);
	}

	protected void handleLog(final JoinPoint joinPoint, Log controllerLog, final Exception e, Object jsonResult, BladeUser user) {

		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		try {

			// *========数据库日志=========*//
			BdmUsualLog usualLog = new BdmUsualLog();
			usualLog.setUserId(user.getUserId());
			usualLog.setUserAccount(user.getAccount());
			// 获取登录者的IP地址
			String ipAddress = IPUtils.getClientIpAddr(request);
			// 设置IP地址
			usualLog.setServerIp(ipAddress);

			// 处理设置注解上的参数
			getControllerMethodDescription(joinPoint, controllerLog, usualLog, jsonResult);
			if (e == null && jsonResult != null && ((R) jsonResult).isSuccess()) {
				if (!usualLog.getOperation().equals(Operation.DELETE.ordinal()) &&
					!usualLog.getOperation().equals(Operation.IMPORT.ordinal())) {
					// 数据保存到数据库
					bulUsualLogService.getBaseMapper().insert(usualLog);
				}
			}
			if(e == null && usualLog.getOperation().equals(Operation.IMPORT.ordinal())){
				// 数据保存到数据库
				bulUsualLogService.getBaseMapper().insert(usualLog);
			}
		} catch (Exception exp) {
			// 记录本地异常日志
			log.error("异常信息:{}", exp.getMessage());
			exp.printStackTrace();
		}
	}

	/**
	 * 获取注解中对方法的描述信息 用于Controller层注解
	 *
	 * @param log      日志
	 * @param usualLog 操作日志
	 * @throws Exception
	 */
	public void getControllerMethodDescription(JoinPoint joinPoint, Log log, BdmUsualLog usualLog, Object jsonResult) throws Exception {
		// 设置operation动作
		usualLog.setOperation(log.operation().ordinal());
		Object[] args = joinPoint.getArgs();
		// 遍历参数数组，查找类型的参数
		TargetDeviceRequest req=new TargetDeviceRequest();
		for (Object arg : args) {
			if (arg instanceof TargetDeviceRequest) {
				req = (TargetDeviceRequest) arg;
				break;
			}
		}
		// 设置操作菜单项
		if (req.getTargetType()!=null){
			if (TargetTypeEnum.VEHICLE.getSymbol().equals(req.getTargetType())){
				usualLog.setMenu("车辆信息管理");
			}
			if (TargetTypeEnum.FACILITY.getSymbol().equals(req.getTargetType())){
				usualLog.setMenu("基础设施管理");
			}
			if (TargetTypeEnum.RECEIVER.getSymbol().equals(req.getTargetType())){
				usualLog.setMenu("集装箱管理");
			}
			if (TargetTypeEnum.TRUCK.getSymbol().equals(req.getTargetType())){
				usualLog.setMenu("矿用卡车管理");
			}
			if (TargetTypeEnum.SHIP.getSymbol().equals(req.getTargetType())){
				usualLog.setMenu("货船管理");
			}
			if (TargetTypeEnum.BOX.getSymbol().equals(req.getTargetType())){
				usualLog.setMenu("铁路货车车厢管理");
			}
			if (TargetTypeEnum.PRECISION.getSymbol().equals(req.getTargetType())){
				usualLog.setMenu("精密装备管理");
			}
		}else {
			usualLog.setMenu(log.menu());
		}
		// 是否需要保存request，参数和值
		if (log.isSaveRequestData()) {
			// 获取参数的信息，传入到数据库中。
			setRequestValue(joinPoint, usualLog, log.excludeParamNames());
		}

		// 是否需要保存response，参数和值
		if (log.isSaveResponseData() && ObjectUtil.isNotNull(jsonResult)) {
			String jsonResultString = jsonResult.toString();
			// 提取msg字段的值
			int dataIndex = jsonResultString.indexOf("data=") + "data=".length();
			int msgIndex = jsonResultString.indexOf(", msg=");

			if (!usualLog.getOperation().equals(Operation.DELETE.ordinal())) {
				String msg = jsonResultString.substring(dataIndex, msgIndex).trim();
				if (usualLog.getOperation().equals(Operation.INSERT.ordinal())) {
					//新增的信息记录
					usualLog.setDescription(usualLog.getMenu() + "新增了一条ID为" + msg + "的数据");
				} else if (usualLog.getOperation().equals(Operation.UPDATE.ordinal())) {
					int startIndex = jsonResultString.indexOf("data");
					String msgText = jsonResultString.substring(startIndex);
					if (msgText.contains("[")) {
						// 使用正则表达式匹配所有方括号内的内容
						String regex = "\\[(.*?)\\]";
						Pattern pattern = Pattern.compile(regex);
						Matcher matcher = pattern.matcher(msgText);
						StringBuilder msgBuilder = new StringBuilder();
						while (matcher.find()) {
							msgBuilder.append(matcher.group(0)).append(" ");
						}
						String editMsg = msgBuilder.toString().trim();
						//修改的信息记录
						usualLog.setDescription(usualLog.getMenu() + "修改了一条ID为" + usualLog.getObjectId() + "的数据，修改的内容为：" + editMsg);
					}
				} else if (usualLog.getOperation().equals(Operation.INLET.ordinal())) {
					usualLog.setDescription(usualLog.getMenu() + "入库了ID为" + msg + "的数据");
				} else if (usualLog.getOperation().equals(Operation.OUTLET.ordinal())) {
					usualLog.setDescription(usualLog.getMenu() + "出库了ID为" + msg + "的数据");
				} else if (usualLog.getOperation().equals(Operation.RETURN_BACK.ordinal())) {
					usualLog.setDescription(usualLog.getMenu() + "回库了一条ID为" + msg + "的数据");
				} else if (usualLog.getOperation().equals(Operation.BIND_OR_UNBIND.ordinal())) {
					usualLog.setDescription(usualLog.getMenu() + "ID为" + usualLog.getTerminalId() + "绑定/解绑了数据");
				} else if (usualLog.getOperation().equals(Operation.IMPORT.ordinal())) {
					String msgText = jsonResultString.substring(dataIndex + 1, msgIndex - 1).trim();
					msgText = msgText.substring(1, msgText.length() - 1);
					String[] pairs = msgText.split(",");
					for (String pair : pairs) {
						// 按等号分割键和值
						String[] keyValue = pair.split("=");
						if (keyValue.length < 2) {
							continue;
						}
						usualLog.setDeptId(Long.valueOf(keyValue[0].trim()));
						usualLog.setDescription(usualLog.getMenu() + "导入了ID为" + keyValue[1].trim() + "的数据");
						bulUsualLogService.getBaseMapper().insert(usualLog);
					}
				}
			} else {
				String msg = jsonResultString.substring(dataIndex + 1, msgIndex - 1).trim();

				msg = msg.substring(1, msg.length() - 1);
				String[] pairs = msg.split(",");
				for (String pair : pairs) {
					// 按等号分割键和值
					String[] keyValue = pair.split("=");
					if (keyValue.length < 2) {
						continue;
					}
					usualLog.setDeptId(Long.valueOf(keyValue[0].trim()));
					usualLog.setDescription(usualLog.getMenu() + "删除了ID为" + keyValue[1].trim() + "的数据");
					bulUsualLogService.getBaseMapper().insert(usualLog);
				}
			}
		}
	}

	/**
	 * 获取请求的参数，放到log中
	 *
	 * @param usualLog 操作日志
	 * @throws Exception 异常
	 */
	private void setRequestValue(JoinPoint joinPoint, BdmUsualLog usualLog, String[] excludeParamNames) throws Exception {

		HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
		Object[] args = joinPoint.getArgs();

		String deptIdStr = request.getParameter("deptId");
		if (deptIdStr == null) {
			// 绑定/解绑终端
			if (args.length > 0) {
				for (Object arg : args) {
					if((usualLog.getOperation().equals(Operation.UPDATE.ordinal()))){
						// 修改获取id ,排除删除时候获取id
						Matcher matcher = ID_PATTERN.matcher(arg.toString());
						if (matcher.find()) {
							String id = matcher.group(1);
							usualLog.setObjectId(Long.parseLong(id));
							break;
						}
					}

					if (arg instanceof DeptIdAware) {
						Long deptId = ((DeptIdAware) arg).getDeptId();
						usualLog.setDeptId(deptId);
						break;
					}
				}
			}
		} else {
			// 绑定/解绑
			Long deptId = Long.parseLong(deptIdStr);
			usualLog.setDeptId(deptId);
			String idStr = request.getParameter("id");
			Long terminalId = idStr != null ? Long.parseLong(idStr) : null;
			usualLog.setTerminalId(terminalId);
		}
	}

}
