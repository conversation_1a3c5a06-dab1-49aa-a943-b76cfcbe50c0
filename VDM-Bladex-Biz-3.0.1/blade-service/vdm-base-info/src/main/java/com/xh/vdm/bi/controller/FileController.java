package com.xh.vdm.bi.controller;

import com.xh.vdm.bi.vo.request.DownloadRequest;
import lombok.extern.log4j.Log4j;
import org.springblade.common.minio.MinioService;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;

/**
 * @Description: 文件管理
 */
@Log4j
@RestController
@RequestMapping("/base/file")
public class FileController {
	@Resource
	private MinioService minioService;
	@PostMapping("/up")
	public R Up(@RequestParam("file") MultipartFile file) {
		if (file.isEmpty()) {
			return R.fail("请选择文件！");
		}
		try {
			String originalFilename = file.getOriginalFilename();
			InputStream inputStream = file.getInputStream();
			String objectName = minioService.getStreamObjectName(originalFilename, inputStream, file.getSize());
			if (objectName != null) {
				return R.data(objectName);
			} else {
				return R.fail("文件上传失败");
			}
		} catch (IOException e) {
			e.printStackTrace();
			return R.fail("文件上传失败！");
		}
	}
	@PostMapping("/download")
	public R Download(@RequestBody DownloadRequest param) {
		if (param.getPath().length()==0){
			return R.fail("参数错误！");
		}
		String url= minioService.getPresignedObjectUrl(param.getPath());
		return R.data(url);
	}
}
