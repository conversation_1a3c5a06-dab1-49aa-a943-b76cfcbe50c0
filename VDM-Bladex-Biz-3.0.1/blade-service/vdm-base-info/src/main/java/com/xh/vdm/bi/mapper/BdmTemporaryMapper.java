package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.BdmTemporaryRequest;
import com.xh.vdm.bi.vo.response.BdmTemporaryResponse;
import com.xh.vdm.biapi.entity.BdmTemporary;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmTemporary)表数据库访问层
 */
public interface BdmTemporaryMapper extends BaseMapper<BdmTemporary> {

	/**
	 * 查询指定行数据
	 *
	 * @param bdmTemporary 查询条件
	 * @param page 分页对象
	 * @param account
	 * @return 对象列表
	 */
	IPage<BdmTemporaryResponse> queryByPage(@Param("bdmTemporary") BdmTemporaryRequest bdmTemporary, @Param("page") IPage<BdmTemporaryRequest> page, @Param("account") String account, @Param("deptIds") String deptIds);

	/**
	 * 新增数据
	 *
	 * @param bdmTemporary 实例对象
	 * @return 影响行数
	 */
	int insertTemporary(BdmTemporaryRequest bdmTemporary);

	/**
	 * 批量新增数据（MyBatis原生foreach方法）
	 *
	 * @param entities List<BdmTemporary> 实例对象列表
	 * @return 影响行数
	 */
	int insertBatch(@Param("entities") List<BdmTemporaryRequest> entities);

	/**
	 * 修改数据
	 *
	 * @param bdmTemporary 实例对象
	 * @return 影响行数
	 */
	int update(BdmTemporary bdmTemporary);

	int deleteByIds(@Param("ids") Long[] ids);

	int batchUpdate(@Param("ids") List<Long> ids,@Param("deptId") Long deptId);

}

