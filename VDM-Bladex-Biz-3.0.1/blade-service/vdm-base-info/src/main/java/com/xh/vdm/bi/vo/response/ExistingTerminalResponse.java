package com.xh.vdm.bi.vo.response;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.baomidou.mybatisplus.core.toolkit.BeanUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xh.vdm.bi.vo.request.ExistingTerminalRequest;
import lombok.Data;
import org.springblade.core.tool.utils.BeanUtil;

import javax.persistence.Column;
import java.util.Date;

/**
 * @description : 存量终端接收和返回参数
 */
@Data
@ExcelIgnoreUnannotated
public class ExistingTerminalResponse {

	private Long id;

	/** 大类，应用方向，取分类表中的code */
	@ExcelProperty(value = "应用方向",index = 1)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String classCode;

	/** 小类，取分类表中的code */
	@ExcelProperty(value = "终端类型",index = 2)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String subClassCode;

	//序列号
	@Column(name="unique_id")
	@ExcelProperty(value = "序列号",index = 3)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String uniqueId;

	//IMEI  定位设备必填，其他类型非必填
	@ExcelProperty(value = "IMEI",index = 4)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String imei;


	/** 安装位置经度 */
	@ExcelProperty(value = "经度",index = 5)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private Double longitude;

	/** 安装位置纬度 */
	@ExcelProperty(value = "纬度",index = 6)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private Double latitude;


	/** 安装位置描述 */
	@ExcelProperty(value = "设备安装位置",index = 7)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String deviceAddr;


	/** 入网方式 */
	private Integer inNetType;

	@ExcelProperty(value = "入网方式",index = 8)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String inNetTypeName;

	/** 入网服务商 */
	@ExcelProperty(value = "入网运营商",index = 9)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String inNetProvider;


	/** 北斗卡号 */
	@ExcelProperty(value = "北斗卡号",index = 10)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String bdCardNumber;

	/** 联系人 */
	@ExcelProperty(value = "联系人",index = 11)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String contact;

	/** 联系方式 */
	@ExcelProperty(value = "联系方式",index = 12)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String contactPhone;


	//终端型号
	@ExcelProperty(value = "终端型号",index = 13)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String model;


	/** 物联网卡号 */
	@ExcelProperty(value = "物联网卡号",index = 14)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String iotNumber;

	/** 终端编号 */
	@ExcelProperty(value = "终端编号",index = 15)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String deviceNo;


	/**
	 * vendor
	 */
	@Column(name="vendor")
	@ExcelProperty(value = "厂商名称",index = 16)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String manufacturerName;

	/**
	 * 安装日期
	 */
	@Column(name="installdate")
	@ExcelProperty(value = "安装日期",index = 17)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private Date installdate;



	//视频通道个数
	@ExcelProperty(value = "视频通道个数",index = 18)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private Integer channelNum;


	//终端类型
	private Integer deviceType;

	//所属部门  归属单位
	private Long deptId;

	//终端种类/功能类型
	private Integer category;

	//赋码值，16位字符串
	private String deviceNum;

	//监控对象id
	private Long targetId;

	//监控对象类型
	private Integer targetType;

	//0-未删除，1-已删除
	private Integer deleted;

	//物联网协议，1-JT/T808，2-MQTT，3-SNMP，...  默认1
	private Integer iotProtocol;

	//创建人，对应国能登录账号（也是工号）
	private String createAccount;

	//特殊性，0-未知，1-旧设备，2-新设备，3-特殊设备  默认1
	private Integer specificity;

	/** 0:新终端 1:存量终端 */
	private Integer deviceSource;

	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**
	 * 修改时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	//其他终端类型名称
	private String otherTypes;

	/**
	 * 所属机构
	 */
	private String deptName;



}
