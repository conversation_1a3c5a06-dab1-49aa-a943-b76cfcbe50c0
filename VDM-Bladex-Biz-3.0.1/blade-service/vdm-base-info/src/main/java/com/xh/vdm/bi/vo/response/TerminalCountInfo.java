package com.xh.vdm.bi.vo.response;

import lombok.Data;

import java.util.List;

/**
 * 终端计数统计信息。
 */
@Data
public class TerminalCountInfo {
	/** 终端类型 */
	private byte deviceUsage;
	/** 型号编码 */
	private String modelCode;
	/** 型号名称 */
	private String modelName;
	/** 接入量 */
	private int accessNum;
	/** 在线量 */
	private int onlineNum;
	/** 今日上线量 */
	private int todayOnlineNum;
	/** 终端id列表 */
	private List<Long> deviceIds;

	/**
	 * 增加今日上线量。
	 * @param value 增量
	 */
	public void addTodayOnlineNum(int value) {
		todayOnlineNum += value;
	}
}
