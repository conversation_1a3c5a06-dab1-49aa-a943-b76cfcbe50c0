package com.xh.vdm.bi.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bi.mapper.TerminalRedisMapper;
import com.xh.vdm.bi.service.ITerminalRedisService;
import com.xh.vdm.bi.vo.response.DeviceRedisResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 缓存查询
 */
@Service
public class TerminalRedisServiceImpl implements ITerminalRedisService {

	@Resource
	private TerminalRedisMapper terminalRedisMapper;

	@Override
	public List<DeviceRedisResponse> selectDeviceForPage(Integer current, Integer size) {
		int start = (current - 1) * size;
		return terminalRedisMapper.selectDeviceForPage(start, size);
	}

	@Override
	public List<DeviceRedisResponse> selectDeviceAll() {
		return terminalRedisMapper.selectDeviceAll();
	}
}
