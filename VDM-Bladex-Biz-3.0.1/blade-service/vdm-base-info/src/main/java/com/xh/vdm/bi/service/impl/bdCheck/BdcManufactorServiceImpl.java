package com.xh.vdm.bi.service.impl.bdCheck;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.entity.bdCheck.BdcManufactor;
import com.xh.vdm.bi.mapper.bdCheck.BdcManufactorMapper;
import com.xh.vdm.bi.service.bdCheck.IBdcManufactorService;
import com.xh.vdm.bi.vo.response.bdcheck.BdcManufactorResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Slf4j
@Service
public class BdcManufactorServiceImpl extends ServiceImpl<BdcManufactorMapper, BdcManufactor> implements IBdcManufactorService {

	@Resource
	private BdcManufactorMapper manufactorMapper;

	@Override
	public List<BdcManufactorResponse> selectList() {
		return manufactorMapper.selectList();
	}
}
