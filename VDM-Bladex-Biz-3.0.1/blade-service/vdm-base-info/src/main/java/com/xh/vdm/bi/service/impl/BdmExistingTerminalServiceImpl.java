package com.xh.vdm.bi.service.impl;


import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.enums.*;
import com.xh.vdm.bi.mapper.BdmDeviceClassMapper;
import com.xh.vdm.bi.mapper.BdmExistingTerminalMapper;
import com.xh.vdm.bi.mapper.IotCardMapper;
import com.xh.vdm.bi.service.BdmExistingTerminalService;
import com.xh.vdm.bi.utils.DeviceNumUtils;
import com.xh.vdm.bi.utils.ObjectFilterUtil;
import com.xh.vdm.bi.vo.request.*;
import com.xh.vdm.bi.vo.response.ExistingTerminalResponse;
import com.xh.vdm.bi.vo.response.ExportExistingTerminalResponse;
import com.xh.vdm.biapi.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hpsf.Array;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 存量终端服务类
 */
@Service
@Slf4j
public class BdmExistingTerminalServiceImpl extends ServiceImpl<BdmExistingTerminalMapper, BdmAbstractDevice> implements BdmExistingTerminalService {

	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	@Resource
	private BdmExistingTerminalMapper bdmExistingTerminalMapper;


	@Resource
	private DeviceNumUtils deviceNumUtils;

	@Resource
	private BdmDeviceClassMapper bdmDeviceClassMapper;

	@Resource
	private IotCardMapper iotCardMapper;

	@Override
	public IPage<ExistingTerminalResponse> queryByPage(ExistingTerminalRequest existingTerminalRequest, DataAuthCE ceDataAuth) {
		Page page = new Page();
		page.setCurrent(existingTerminalRequest.getCurrent());
		page.setSize(existingTerminalRequest.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.bdmExistingTerminalMapper.queryAll(page, existingTerminalRequest, response.getAccount(), response.getOrgList());
	}

	@Override
	public ExistingTerminalResponse queryById(Long id) {
		return this.bdmExistingTerminalMapper.queryById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmAbstractDevice insertAbstract(ExistingTerminalRequest request, String tableValue) {
		// 初始化请求参数
		request.setDeviceType(deviceType(tableValue));
		request.setSpecificity(1); // 旧设备标识
		request.setCategory(getCategory(request.getClassCode(), request.getSubClassCode(), request.getOtherTypes()));
		// 构建基础设备对象
		BdmAbstractDevice abstractDevice = this.init(request);
		// 检查唯一性：根据uniqueId查找记录
		LambdaQueryWrapper<BdmAbstractDevice> queryWrapper = new LambdaQueryWrapper<>();
		queryWrapper.eq(BdmAbstractDevice::getUniqueId, request.getUniqueId());
		// 尝试获取已存在的记录
		BdmAbstractDevice existingDevice = bdmExistingTerminalMapper.selectOne(queryWrapper);
		if (existingDevice != null) {
			// 存在则更新：使用ID设置，执行更新
			abstractDevice.setId(existingDevice.getId());
			boolean updateSuccess = bdmExistingTerminalMapper.updateById(abstractDevice) > 0;
			log.info("bdm_abstract_device表更新操作 - ID: {}, 结果: {}",
				existingDevice.getId(), updateSuccess ? "成功" : "失败");
		} else {
			// 不存在则插入：明确设置ID为null，确保生成新ID
			abstractDevice.setId(null);
			bdmExistingTerminalMapper.insert(abstractDevice);
			log.info("bdm_abstract_device表插入操作 - uniqueId: {}, 结果: 成功", request.getUniqueId());
		}

		return abstractDevice;
	}

	@Override
	public BdmAbstractDevice updateAbstract(ExistingTerminalRequest existingTerminalRequest, String tableValue) {
		existingTerminalRequest.setDeviceType(deviceType(tableValue));
		BdmAbstractDevice bdmAbstractDevice = this.init(existingTerminalRequest);
		bdmExistingTerminalMapper.updateById(bdmAbstractDevice);
		return bdmAbstractDevice;
	}

	@Override
	public Boolean deleteById(Long id) {
		return this.bdmExistingTerminalMapper.deleteById(id) > 0;
	}

	/**
	 * 导入
	 *
	 * @param list
	 * @param getUserId
	 * @param deptId
	 * @return
	 */
	@Override
	public Map<List<ExistingTerminalRequest>, List<ExportExistingTerminalResponse>> importExcel(List<ExportExistingTerminalResponse> list, Long getUserId, Long deptId) {
		Map<List<ExistingTerminalRequest>, List<ExportExistingTerminalResponse>> map = new HashMap<>();
		// 过滤空对象
		List<ExportExistingTerminalResponse> filteredList = ObjectFilterUtil.filterEmptyObjects(list);
		//校验导入数据的合法性
		List<ExportExistingTerminalResponse> duplicateRequests = getDuplicateRequests(filteredList);
		List<ExportExistingTerminalResponse> requestList = duplicateRequests.stream()
			.filter(item -> item.getMsg() == null || item.getMsg().trim().isEmpty())
			.collect(Collectors.toList());
		List<ExistingTerminalRequest> existingTerminalRequestList = new ArrayList<>();
		if (duplicateRequests.size() == requestList.size()) {
			enrichDataWithDict(requestList);

			// 查询有效数据
			List<BdmDeviceClass> bdmDeviceClassList = bdmDeviceClassMapper.selectList(
				new LambdaQueryWrapper<BdmDeviceClass>()
					.eq(BdmDeviceClass::getDeleted, 0)
					.eq(BdmDeviceClass::getSource, 1)
			);

			for (ExportExistingTerminalResponse dto : requestList) {
				ExistingTerminalRequest existingTerminalRequest = new ExistingTerminalRequest();
				BeanUtil.copyProperties(dto, existingTerminalRequest);
				if (dto.getSubClassCode().equals("其他")) {
					//保存设备分类信息
					String s = saveBdmDeviceClass(dto.getClassCode(), dto.getOtherTypes(), bdmDeviceClassList);
					if (StringUtils.isNotEmpty(s)) {
						existingTerminalRequest.setSubClassCode(s);
					}
				}
				String tableValue = getTableValue(existingTerminalRequest.getClassCode(),
					existingTerminalRequest.getSubClassCode(), existingTerminalRequest.getOtherTypes());
				existingTerminalRequest.setDeptId(deptId);
				try {
					existingTerminalRequest.setInstalldate(convert(dto.getInstalldateName()));
				} catch (Exception e) {
					log.error(e.getMessage(), e);
				}

				BdmAbstractDevice bdmAbstractDevice = insertAbstract(existingTerminalRequest, tableValue);
				BeanUtil.copyProperties(bdmAbstractDevice, existingTerminalRequest);
				existingTerminalRequest.setTableValue(tableValue);
				existingTerminalRequest.setId(null);
				existingTerminalRequestList.add(existingTerminalRequest);
			}
		}
		map.put(existingTerminalRequestList, duplicateRequests);
		return map;
	}

	private String saveBdmDeviceClass(String classCode, String otherTypes, List<BdmDeviceClass> bdmDeviceClassList) {
		String subClassCode = null;
		if (CollectionUtils.isNotEmpty(bdmDeviceClassList)) {
			boolean hasFirstEdition = bdmDeviceClassList.stream()
				.anyMatch(item -> otherTypes.equals(item.getClassName()));
			if (!hasFirstEdition) {
				BdmDeviceClass deviceClass = new BdmDeviceClass();
				deviceClass.setId(IdWorker.getId());
				deviceClass.setCode(String.valueOf(IdWorker.getId()));
				deviceClass.setParentCode(classCode);
				deviceClass.setDeleted(0);
				deviceClass.setClassName(otherTypes);
				deviceClass.setCreateTime(new Date());
				deviceClass.setSource(Integer.valueOf(1));
				bdmDeviceClassMapper.insert(deviceClass);
				subClassCode = deviceClass.getCode();
			}
		}
		return subClassCode;

	}


	/**
	 * 校验
	 *
	 * @param list
	 * @return
	 */
	public List<ExportExistingTerminalResponse> getDuplicateRequests(List<ExportExistingTerminalResponse> list) {
		//必填字段必须有值
		for (ExportExistingTerminalResponse request : list) {
			if (request.getClassCode().contains("必填")) {
				request.setMsg("请删除当前行提示数据");
				continue;
			}
			if (request.getClassCode().contains("示例")) {
				request.setMsg("请删除当前行示例数据");
				continue;
			}
			if (StringUtils.isEmpty(request.getClassCode())) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；应用方向不能为空");
				} else {
					request.setMsg("应用方向不能为空");
				}
			}
			if (StringUtils.isEmpty(request.getSubClassCode())) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；终端类型不能为空");
				} else {
					request.setMsg("终端类型不能为空");
				}
			}

			if (StringUtils.isEmpty(request.getUniqueId())) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；序列号不能为空");
				} else {
					request.setMsg("序列号不能为空");
				}
			}

			if (StringUtils.isEmpty(request.getContact())) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；联系人不能为空");
				} else {
					request.setMsg("联系人不能为空");
				}
			}
			if (StringUtils.isEmpty(request.getContactPhone())) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；联系方式不能为空");
				} else {
					request.setMsg("联系方式不能为空");
				}
			}

			// 统计所有iotNumber的出现次数，排除空字符串
			Map<String, Long> iotNumberCountMap = list.stream()
				.map(ExportExistingTerminalResponse::getIotNumber)
				.filter(Objects::nonNull)              // 过滤null值
				.filter(iot -> !iot.isEmpty())         // 过滤空字符串
				.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

			// 找出重复的iotNumber
			Set<String> duplicateIotNumbers = iotNumberCountMap.entrySet().stream()
				.filter(entry -> entry.getValue() > 1)
				.map(Map.Entry::getKey)
				.collect(Collectors.toSet());

			// 统计所有uniqueId的出现次数
			Map<String, Long> uniqueIdCountMap = list.stream()
				.map(ExportExistingTerminalResponse::getUniqueId)
				.filter(Objects::nonNull)
				.filter(iot -> !iot.isEmpty())         // 过滤空字符串
				.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

			// 找出重复的uniqueId
			Set<String> duplicateUniqueIds = uniqueIdCountMap.entrySet().stream()
				.filter(entry -> entry.getValue() > 1)
				.map(Map.Entry::getKey)
				.collect(Collectors.toSet());

			//获取集合中不重复不为空的所有 序列号
			List<String> uniqueIdList = list.stream()
				.map(ExportExistingTerminalResponse::getUniqueId)
				.filter(uniqueId -> uniqueId != null && !uniqueId.trim().isEmpty()) // 过滤空值和空白字符串
				.distinct() // 去重
				.collect(Collectors.toList());

			//用所有序列号进行查询，查询到数据说明导入的序列号已经存在
			List<String> oldUniqueIdList = bdmExistingTerminalMapper.selectList(
					new LambdaQueryWrapper<BdmAbstractDevice>()
						.eq(BdmAbstractDevice::getDeleted, 0)
						.in(BdmAbstractDevice::getUniqueId, uniqueIdList)
						.select(BdmAbstractDevice::getUniqueId) // 只查询type字段
				)
				.stream()
				.map(BdmAbstractDevice::getUniqueId) // 从实体中提取type值
				.collect(Collectors.toList()); // 转换为List<String>

			//拿到所有的不重复不为空的物联网卡号
			List<String> iotNumberList = list.stream()
				.map(ExportExistingTerminalResponse::getIotNumber)
				.filter(iotNumber -> iotNumber != null && !iotNumber.trim().isEmpty()) // 过滤空值和空白字符串
				.distinct() // 去重
				.collect(Collectors.toList());

			String iotNumber = request.getIotNumber();
			String uniqueId = request.getUniqueId();

			if (iotNumber != null && duplicateIotNumbers.contains(iotNumber)) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；导入的数据物联网卡号重复");
				} else {
					request.setMsg("导入的数据物联网卡号重复");
				}
			}

			if (uniqueId != null && duplicateUniqueIds.contains(uniqueId)) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；导入的数据序列号重复");
				} else {
					request.setMsg("导入的数据序列号重复");
				}
			}

			if (oldUniqueIdList.contains(request.getUniqueId())) {
				if (request.getMsg() != null && !request.getMsg().isEmpty()) {
					request.setMsg(request.getMsg() + "；序列号已存在!");
				} else {
					request.setMsg("序列号已存在!");
				}
			}
			if (StringUtils.isNotEmpty(request.getInstalldateName())) {
				if (!isValidDate(request.getInstalldateName())) {
					if (request.getMsg() != null && !request.getMsg().isEmpty()) {
						request.setMsg(request.getMsg() + "；日期格式不正确");
					} else {
						request.setMsg("日期格式不正确!");
					}
				}
			}

		}
		return list;
	}


	public Date convert(String dateStr) throws ParseException {
		if (StringUtils.isEmpty(dateStr)) {
			return null;
		}
		// 定义日期格式
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		// 解析字符串为Date
		return sdf.parse(dateStr);
	}

	private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

	public static boolean isValidDate(String dateStr) {
		try {
			// 严格校验日期（例如：不会把2023-02-30自动转换为2023-03-02）
			LocalDate.parse(dateStr, FORMATTER);
			return true;
		} catch (DateTimeParseException e) {
			return false;
		}
	}


	/**
	 * 对象字段初始化
	 *
	 * @param request
	 * @return
	 */
	public BdmAbstractDevice init(ExistingTerminalRequest request) {
		BdmAbstractDevice abstractDevice = new BdmAbstractDevice();
		abstractDevice.setId(request.getId() != null ? request.getId() : null);
		abstractDevice.setUniqueId(request.getUniqueId() != null ? request.getUniqueId() : "");
		abstractDevice.setImei(request.getImei() != null ? request.getImei() : "");
		abstractDevice.setModel(request.getModel() != null ? request.getModel() : "");
		abstractDevice.setSpecificity(request.getSpecificity() != null ? request.getSpecificity() : 1);
		abstractDevice.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		R<String> r1 = deviceNumUtils.deviceNumProducer(ProductTypeEnum.O.name());
		if (r1.isSuccess()) {
			abstractDevice.setDeviceNum(r1.getData());
		}
		abstractDevice.setChannelNum(request.getChannelNum() != null ? request.getChannelNum() : 0);
		abstractDevice.setDeleted(request.getDeleted() != null ? request.getDeleted() : 0);
		abstractDevice.setIotProtocol(request.getIotProtocol() != null ? request.getIotProtocol() : IotProtocolEnum.JT808.getCode());
		abstractDevice.setClassCode(request.getClassCode() != null ? request.getClassCode() : "");
		abstractDevice.setSubClassCode(request.getSubClassCode() != null ? request.getSubClassCode() : "");
		abstractDevice.setLongitude(request.getLongitude() != null ? request.getLongitude() : null);
		abstractDevice.setLatitude(request.getLatitude() != null ? request.getLatitude() : null);
		abstractDevice.setDeviceAddr(request.getDeviceAddr() != null ? request.getDeviceAddr() : "");
		abstractDevice.setInNetProvider(request.getInNetProvider() != null ? request.getInNetProvider() : "");
		abstractDevice.setBdCardNumber(request.getBdCardNumber() != null ? request.getBdCardNumber() : "");
		abstractDevice.setContact(request.getContact() != null ? request.getContact() : "");
		abstractDevice.setContactPhone(request.getContactPhone() != null ? request.getContactPhone() : "");
		abstractDevice.setIotNumber(request.getIotNumber() != null ? request.getIotNumber() : "");
		abstractDevice.setDeviceNo(request.getDeviceNo() != null ? request.getDeviceNo() : "");
		abstractDevice.setDeviceSource(request.getDeviceSource() != null ? request.getDeviceSource() : 1);
		abstractDevice.setTargetId(request.getTargetId() != null ? request.getTargetId() : null);
		abstractDevice.setTargetType(request.getTargetType() != null ? request.getTargetType() : 0);
		abstractDevice.setCreateAccount(AuthUtil.getUserAccount());
		abstractDevice.setDeviceType(request.getDeviceType() != null ? request.getDeviceType() : 0);
		abstractDevice.setCategory(request.getCategory() != null ? request.getCategory() : 0);
		abstractDevice.setManufacturerName(request.getManufacturerName() != null ? request.getManufacturerName() : "");
		if (request.getInNetType() != null) {
			abstractDevice.setInNetType(request.getInNetType());
		} else if (StringUtils.isNotEmpty(request.getInNetTypeName())) {
			Map<String, Integer> netWorkMap = new HashMap<>();
			for (NetworkAccessTypeEnum type : NetworkAccessTypeEnum.values()) {
				netWorkMap.put(type.getName(), type.getCode());
			}
			abstractDevice.setInNetType(netWorkMap.getOrDefault(abstractDevice.getInNetType(), null));
		} else {
			abstractDevice.setInNetType(0);
		}

		return abstractDevice;
	}


	/**
	 * 终端类别, 1-北斗定位终端，2-北斗穿戴式终端，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端
	 *
	 * @param type
	 * @return
	 */
	private Integer deviceType(String type) {
		switch (type) {
			case "bdm_rnss_device":
				return 1;
			case "bdm_wearable_device":
				return 2;
			case "bdm_monit_device":
				return 3;
			case "bdm_rdss_device":
				return 4;
			case "bdm_pnt_device":
				return 5;
			default:
				return 0;
		}
	}

	/**
	 * 五种终端类型对应下面的子类
	 *
	 * @return
	 */
	private Integer getCategory(String classCode, String subClassCode, String otherTypes) {
		// 查询有效数据
		if (StringUtils.isEmpty(otherTypes)) {
			BdmDeviceClass bdmDeviceClass = bdmDeviceClassMapper.selectOne(new LambdaQueryWrapper<BdmDeviceClass>().eq(BdmDeviceClass::getCode, subClassCode));
			otherTypes = bdmDeviceClass.getClassName();
		}
		// 查询有效数据
		List<BdmDeviceClass> list = bdmDeviceClassMapper.selectList(
			new LambdaQueryWrapper<BdmDeviceClass>()
				.eq(BdmDeviceClass::getDeleted, 0)
				.eq(BdmDeviceClass::getSource, 1)
		);
		Boolean hasMatchingSubClass = false;

		// 判断是否存在匹配的子类描述
		for (BdmDeviceClass bdmDeviceClass : list) {
			if (bdmDeviceClass.getClassName().equals(otherTypes)) {
				hasMatchingSubClass = true;
				break;
			}
		}

		Map<String, Integer> otherMap = new HashMap<>();
		otherMap.put("1", 100);
		otherMap.put("2", 100);
		otherMap.put("3", 100);
		otherMap.put("4", 400);
		otherMap.put("5", 400);
		otherMap.put("6", 300);
		otherMap.put("7", 500);
		otherMap.put("8", 100);
		otherMap.put("9", 100);


		Map<String, Integer> subMap = new HashMap<>();
		subMap.put("1000", 100);
		subMap.put("1001", 107);
		subMap.put("1002", 202);
		subMap.put("1003", 201);
		subMap.put("1004", 113);
		subMap.put("1005", 113);
		subMap.put("2000", 100);
		subMap.put("2001", 101);
		subMap.put("2002", 102);
		subMap.put("2003", 103);
		subMap.put("2004", 105);
		subMap.put("3000", 100);
		subMap.put("3001", 106);
		subMap.put("3002", 114);
		subMap.put("4000", 400);
		subMap.put("4001", 401);
		subMap.put("4002", 401);
		subMap.put("4003", 402);
		subMap.put("5000", 400);
		subMap.put("5001", 400);
		subMap.put("5002", 400);
		subMap.put("5003", 400);
		subMap.put("6000", 300);
		subMap.put("6001", 301);
		subMap.put("6002", 302);
		subMap.put("6003", 301);
		subMap.put("6004", 303);
		subMap.put("6005", 300);
		subMap.put("7000", 500);
		subMap.put("7001", 503);
		subMap.put("7002", 501);
		subMap.put("7003", 500);
		subMap.put("8000", 100);
		subMap.put("9000", 100);

		// 根据条件选择结果
		if (hasMatchingSubClass) {
			return otherMap.getOrDefault(classCode, null);
		} else {
			return subMap.getOrDefault(subClassCode, null);
		}

	}

	/**
	 * 拿到终端类型
	 *
	 * @param classCode    大类
	 * @param subClassCode 小类
	 * @return
	 */
	@Override
	public String getTableValue(String classCode, String subClassCode, String otherTypes) {
		if (StringUtils.isEmpty(otherTypes)) {
			BdmDeviceClass bdmDeviceClass = bdmDeviceClassMapper.selectOne(new LambdaQueryWrapper<BdmDeviceClass>().eq(BdmDeviceClass::getCode, subClassCode));
			otherTypes = bdmDeviceClass.getClassName();
		}
		// 参数校验
		if (StringUtils.isEmpty(classCode) || StringUtils.isEmpty(subClassCode)) {
			return "应用方向和终端类型不能为空！";
		}

		// 查询有效数据
		List<BdmDeviceClass> list = bdmDeviceClassMapper.selectList(
			new LambdaQueryWrapper<BdmDeviceClass>()
				.eq(BdmDeviceClass::getDeleted, 0)
				.eq(BdmDeviceClass::getSource, 1)
		);
		Boolean hasMatchingSubClass = false;

		// 判断是否存在匹配的子类描述
		for (BdmDeviceClass bdmDeviceClass : list) {
			if (bdmDeviceClass.getClassName().equals(otherTypes)) {
				hasMatchingSubClass = true;
				break;
			}
		}
		// 定义主要类别映射
		Map<String, String> mainClassMapping = new HashMap<>();
		mainClassMapping.put(ClassCodeEnum.WORKER_RNSS.getValue(), "bdm_rnss_device");
		mainClassMapping.put(ClassCodeEnum.CARRY_RNSS.getValue(), "bdm_rnss_device");
		mainClassMapping.put(ClassCodeEnum.ASSET_MANAGEMENT.getValue(), "bdm_rnss_device");
		mainClassMapping.put(ClassCodeEnum.SAFE_MONIT.getValue(), "bdm_monit_device");
		mainClassMapping.put(ClassCodeEnum.MAPPING_SURVEY.getValue(), "bdm_monit_device");
		mainClassMapping.put(ClassCodeEnum.EMERGENCY_TELECOMMUNICATIONS.getValue(), "bdm_rdss_device");
		mainClassMapping.put(ClassCodeEnum.TIME_FREQUENCY_SYNCHRONIZATION.getValue(), "bdm_pnt_device");
		mainClassMapping.put(ClassCodeEnum.PRECISE_CONTROL.getValue(), "bdm_rnss_device");
		mainClassMapping.put(ClassCodeEnum.INTELLIGENT_INSPECTION.getValue(), "bdm_rnss_device");

		// 定义特殊子类映射
		Map<String, Map<String, String>> specialSubClassMapping = new HashMap<>();

		Map<String, String> workerRnssMapping = new HashMap<>();
		workerRnssMapping.put(SubClassCodeEnum.OTHER.getValue(), "bdm_rnss_device");
		workerRnssMapping.put(SubClassCodeEnum.HIGH_PRECISION_BD_CARD.getValue(), "bdm_rnss_device");
		workerRnssMapping.put(SubClassCodeEnum.HIGH_PRECISION_SAFETY_HELMET.getValue(), "bdm_wearable_device");
		workerRnssMapping.put(SubClassCodeEnum.EXPLOSION_PROOF_SMART_WATCH.getValue(), "bdm_wearable_device");
		workerRnssMapping.put(SubClassCodeEnum.EXPLOSION_PROOF_WALKIE_TALKIE.getValue(), "bdm_rnss_device");
		workerRnssMapping.put(SubClassCodeEnum.EXPLOSION_PROOF_HANDHELD_TERMINAL.getValue(), "bdm_rnss_device");

		specialSubClassMapping.put(ClassCodeEnum.WORKER_RNSS.getValue(), workerRnssMapping);

		Map<String, String> carryRnssMapping = new HashMap<>();
		carryRnssMapping.put(SubClassCodeEnum.VEHICLE_COMMON_TERMINAL.getValue(), "bdm_rnss_device");
		carryRnssMapping.put(SubClassCodeEnum.VEHICLE_POSITIONING_TERMINAL.getValue(), "bdm_rnss_device");
		carryRnssMapping.put(SubClassCodeEnum.VEHICLE_VIDEO_POSITIONING_TERMINAL.getValue(), "bdm_rnss_device");
		carryRnssMapping.put(SubClassCodeEnum.VEHICLE_HIGH_PRECISION_TERMINAL.getValue(), "bdm_rnss_device");
		carryRnssMapping.put(SubClassCodeEnum.MINING_VEHICLE_ANTI_COLLISION_TERMINAL.getValue(), "bdm_rnss_device");

		specialSubClassMapping.put(ClassCodeEnum.CARRY_RNSS.getValue(), carryRnssMapping);


		Map<String, String> assetManagementMapping = new HashMap<>();
		assetManagementMapping.put(SubClassCodeEnum.ASSET_MANAGEMENT_COMMON.getValue(), "bdm_rnss_device");
		assetManagementMapping.put(SubClassCodeEnum.ASSET_MANAGEMENT_COMMON.getValue(), "bdm_rnss_device");
		assetManagementMapping.put(SubClassCodeEnum.ASSET_MANAGEMENT_COMMON.getValue(), "bdm_rnss_device");

		specialSubClassMapping.put(ClassCodeEnum.ASSET_MANAGEMENT.getValue(), assetManagementMapping);


		Map<String, String> safeMonitMapping = new HashMap<>();
		safeMonitMapping.put(SubClassCodeEnum.SAFETY_MONITORING_COMMON.getValue(), "bdm_monit_device");
		safeMonitMapping.put(SubClassCodeEnum.SPLIT_TYPE_MONITORING_RECEIVER.getValue(), "bdm_monit_device");
		safeMonitMapping.put(SubClassCodeEnum.INTEGRATED_MONITORING_RECEIVER.getValue(), "bdm_monit_device");
		safeMonitMapping.put(SubClassCodeEnum.REFERENCE_STATION_RECEIVER.getValue(), "bdm_monit_device");

		specialSubClassMapping.put(ClassCodeEnum.SAFE_MONIT.getValue(), safeMonitMapping);

		Map<String, String> mappingSurveyMapping = new HashMap<>();
		mappingSurveyMapping.put(SubClassCodeEnum.MAPPING_SURVEY_COMMON.getValue(), "bdm_monit_device");
		mappingSurveyMapping.put(SubClassCodeEnum.HIGH_PRECISION_RTK_DEVICE.getValue(), "bdm_monit_device");
		mappingSurveyMapping.put(SubClassCodeEnum.HIGH_PRECISION_SURVEY_UAV.getValue(), "bdm_monit_device");
		mappingSurveyMapping.put(SubClassCodeEnum.HIGH_PRECISION_INERTIAL_INTEGRATED_TERMINAL.getValue(), "bdm_monit_device");

		specialSubClassMapping.put(ClassCodeEnum.MAPPING_SURVEY.getValue(), mappingSurveyMapping);


		Map<String, String> emergencyTelecommunicationsMapping = new HashMap<>();
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.EMERGENCY_COMMUNICATION_COMMON.getValue(), "bdm_rdss_device");
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.BDS3_SHORT_MESSAGE_HANDHELD.getValue(), "bdm_rdss_device");
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.BDS3_DATA_TRANSMISSION_TERMINAL.getValue(), "bdm_rdss_device");
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.BDS_5G_SATELLITE_HANDHELD.getValue(), "bdm_rdss_device");
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.BDS3_DISTRESS_SAVING_TERMINAL.getValue(), "bdm_rdss_device");
		emergencyTelecommunicationsMapping.put(SubClassCodeEnum.BDS3_SHIPBORNE_TERMINAL.getValue(), "bdm_rdss_device");

		specialSubClassMapping.put(ClassCodeEnum.EMERGENCY_TELECOMMUNICATIONS.getValue(), emergencyTelecommunicationsMapping);

		Map<String, String> timeFrequencySynchronizationMapping = new HashMap<>();
		timeFrequencySynchronizationMapping.put(SubClassCodeEnum.TIME_FREQUENCY_SYNCHRONIZATION_COMMON.getValue(), "bdm_pnt_device");
		timeFrequencySynchronizationMapping.put(SubClassCodeEnum.POWER_TIME_SYNCHRONIZATION_DEVICE.getValue(), "bdm_pnt_device");
		timeFrequencySynchronizationMapping.put(SubClassCodeEnum.BDS_TIME_SERVICE_SERVER.getValue(), "bdm_pnt_device");
		timeFrequencySynchronizationMapping.put(SubClassCodeEnum.BDS_ANTI_SPOOFING_DEVICE.getValue(), "bdm_pnt_device");

		specialSubClassMapping.put(ClassCodeEnum.TIME_FREQUENCY_SYNCHRONIZATION.getValue(), timeFrequencySynchronizationMapping);

		Map<String, String> preciseControlMapping = new HashMap<>();
		preciseControlMapping.put(SubClassCodeEnum.TIME_FREQUENCY_SYNCHRONIZATION_COMMON.getValue(), "bdm_rnss_device");

		specialSubClassMapping.put(ClassCodeEnum.PRECISE_CONTROL.getValue(), preciseControlMapping);

		Map<String, String> intelligentInspectionMapping = new HashMap<>();
		intelligentInspectionMapping.put(SubClassCodeEnum.TIME_FREQUENCY_SYNCHRONIZATION_COMMON.getValue(), "bdm_rnss_device");

		specialSubClassMapping.put(ClassCodeEnum.INTELLIGENT_INSPECTION.getValue(), intelligentInspectionMapping);

		// 根据条件选择结果
		if (hasMatchingSubClass) {
			return mainClassMapping.getOrDefault(classCode, "");
		} else {
			Map<String, String> subMap = specialSubClassMapping.getOrDefault(classCode, Collections.emptyMap());
			return subMap.getOrDefault(subClassCode, "");
		}
	}


	private void enrichDataWithDict(List<ExportExistingTerminalResponse> records) {
		if (records.isEmpty()) {
			return;
		}

		Map<String, String> classMap = new HashMap<>();
		for (ClassCodeEnum type : ClassCodeEnum.values()) {
			classMap.put(type.getLabel(), type.getValue());
		}

		List<BdmDeviceClass> list = bdmDeviceClassMapper.selectList(new LambdaQueryWrapper<BdmDeviceClass>().ne(BdmDeviceClass::getParentCode, "0"));
		Map<String, String> subMap = new HashMap<>();
		for (BdmDeviceClass type : list) {
			subMap.put(type.getClassName(), type.getCode());
		}

		Map<String, Integer> netWorkMap = new HashMap<>();
		for (NetworkAccessTypeEnum type : NetworkAccessTypeEnum.values()) {
			netWorkMap.put(type.getName(), type.getCode());
		}

		Map<String, String> networkOperatorMap = new HashMap<>();
		for (NetworkOperatorEnum type : NetworkOperatorEnum.values()) {
			networkOperatorMap.put(type.getName(), type.getCode());
		}

		for (ExportExistingTerminalResponse response : records) {
			response.setClassCode(classMap.getOrDefault(String.valueOf(response.getClassCode()), null));
			if (response.getSubClassCode().equals("其他") && StringUtils.isNotEmpty(response.getOtherTypes())) {
				if (StringUtils.isNotEmpty(subMap.get(response.getOtherTypes()))) {
					response.setSubClassCode(subMap.getOrDefault(response.getSubClassCode(), null));
				}
			} else {
				response.setSubClassCode(subMap.getOrDefault(response.getSubClassCode(), null));
			}
			response.setInNetType(netWorkMap.getOrDefault(response.getInNetTypeName(), null));
			response.setInNetProvider(networkOperatorMap.getOrDefault(response.getInNetProvider(), null));
		}
	}
}
