package com.xh.vdm.bi.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bi.vo.request.ExistingTerminalRequest;
import com.xh.vdm.bi.vo.response.ExistingTerminalResponse;
import com.xh.vdm.bi.vo.response.ExportExistingTerminalResponse;
import com.xh.vdm.biapi.entity.BdmAbstractDevice;
import org.springblade.entity.DataAuthCE;

import java.util.List;
import java.util.Map;


public interface BdmExistingTerminalService extends IService<BdmAbstractDevice> {

	IPage<ExistingTerminalResponse> queryByPage(ExistingTerminalRequest existingTerminalRequest, DataAuthCE ceDataAuth);

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	ExistingTerminalResponse queryById(Long id);


	BdmAbstractDevice insertAbstract(ExistingTerminalRequest existingTerminalRequest, String tableValue);

	BdmAbstractDevice updateAbstract(ExistingTerminalRequest existingTerminalRequest, String tableValue);


	Boolean deleteById(Long id);

	/**
	 * 导入
	 */
	Map<List<ExistingTerminalRequest>,List<ExportExistingTerminalResponse>> importExcel(List<ExportExistingTerminalResponse> list, Long getUserId, Long deptId);


	String getTableValue(String classCode, String subClassCode, String otherTypes);
}
