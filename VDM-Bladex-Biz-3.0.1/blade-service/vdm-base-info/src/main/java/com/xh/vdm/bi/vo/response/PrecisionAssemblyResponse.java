package com.xh.vdm.bi.vo.response;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 精密装备管理返参
 */
@Data
public class PrecisionAssemblyResponse implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private String number;

	private Integer targetType;

	private String name;

	private String model;

	private String manufacturer;

	private Long deptId;

	private String createTime;

	private String updateTime;

	private Integer deleted;

	private String uniqueId;
	/**
	 * 绑定赋码编号
	 */
	private String deviceNum;

	private String terminalCategories;

	private String deptName;

}

