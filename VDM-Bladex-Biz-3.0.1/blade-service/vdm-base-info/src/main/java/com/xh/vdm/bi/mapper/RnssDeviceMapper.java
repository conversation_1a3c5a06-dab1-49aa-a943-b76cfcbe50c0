package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bi.vo.request.PersonTerminalRequest;
import com.xh.vdm.bi.vo.request.RnssDeviceRequest;
import com.xh.vdm.bi.vo.request.VehicleTerminalRequest;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.RnssDeviceResponse;
import com.xh.vdm.bi.vo.response.WorkerBingResponse;
import com.xh.vdm.biapi.entity.BdmRnssDevice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 短报文终端管理
 */
public interface RnssDeviceMapper extends BaseMapper<BdmRnssDevice> {

	/**
	 * 查询指定行数据
	 *
	 * @param page    分页对象
	 * @param request 查询条件
	 * @param schema
	 * @return 对象列表
	 */
	IPage<RnssDeviceResponse> queryAll(IPage page, @Param("request") RnssDeviceRequest request, @Param("account") String account, @Param("deptIds") String deptIds, @Param("schema") String schema);

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	RnssDeviceResponse queryById(Long id);

	/**
	 * 新增数据
	 *
	 * @param bdmRnssDevice 实例对象
	 * @return 影响行数
	 */
	void insertRnss(BdmRnssDevice bdmRnssDevice);

	/**
	 * 修改数据
	 *
	 * @param bdmRdssDevice 实例对象
	 * @return 影响行数
	 */
	int update(BdmRnssDevice bdmRdssDevice);

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 影响行数
	 */
	int deleteById(Long id);

	int deleteByIds(Long[] ids);

    void insertBatch(List<RnssDeviceRequest> list);

	void updateByWorkerId(@Param("id") Long id, @Param("targetType") Integer targetType);

	void updateByVehicleId(@Param("id") Long id, @Param("targetType") Integer targetType);

	void connectWorkerTerminal(@Param("request") PersonTerminalRequest request, @Param("id") Long id, @Param("targetType") Integer targetType, @Param("targetName") String targetName, @Param("deptId") Long deptId);

	void connectVehicleTerminal(@Param("request") VehicleTerminalRequest request, @Param("id") Long id, @Param("targetType") Integer targetType, @Param("targetName") String targetName, @Param("deptId") Long deptId);

	List<PersonNoBingResponse> selectBindByWorkId(@Param("id") Long id, @Param("targetType") Integer targetType);

	void deleteByTargetIds(@Param("ids") Long[] ids, @Param("targetType") Integer targetType);

	List<WorkerBingResponse> selectByWorkId(@Param("id") Long id, @Param("targetType") Integer targetType);

	int unbind(@Param("id") Long id, @Param("targetType") Integer targetType);

	int bindTarget(@Param("deviceId") Long deviceId,@Param("targetId") Long targetId, @Param("targetType") Integer targetType, @Param("targetName") String targetName);

    long countByUserRole(@Param("account") String account, @Param("deptIds") String deptIds);

	void updateDept(@Param("id") Long id, @Param("targetType") Integer targetType, @Param("deptId") Long deptId);
}

