package com.xh.vdm.bi.vo.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class TaskParam {
	@J<PERSON>NField(name = "device_id")
	@JsonProperty("device_id")
	private Long deviceId;

	@JSONField(name = "device_type")
	@JsonProperty("device_type")
	private Integer deviceType;

	@JSONField(name = "target_id")
	@JsonProperty("target_id")
	private Long targetId;

	@JSONField(name = "target_type")
	@JsonProperty("target_type")
	private Integer targetType;
}
