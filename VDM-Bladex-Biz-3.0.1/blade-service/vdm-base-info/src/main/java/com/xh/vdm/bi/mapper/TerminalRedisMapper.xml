<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bi.mapper.TerminalRedisMapper">

    <select id="selectDeviceForPage" resultType="com.xh.vdm.bi.vo.response.DeviceRedisResponse">
        select
            "id",
            "unique_id",
            "device_type",
            "category",
            "device_num",
            "dept_id",
            "iot_protocol"
        from bdm_abstract_device
        where deleted = 0
        limit #{pageSize} offset #{start}
    </select>

    <select id="selectDeviceAll" resultType="com.xh.vdm.bi.vo.response.DeviceRedisResponse">
        select
            "id",
            "unique_id",
            "device_type",
            "category",
            "device_num",
            "dept_id",
            "iot_protocol"
        from bdm_abstract_device
        where deleted = 0
    </select>

    <!--<select id="selectList" resultType="com.xh.vdm.bi.vo.response.DeviceRedisResponse">
        select
            "id",
            "unique_id",
            "device_type",
            "category",
            "device_num",
            "dept_id",
            "iot_protocol"
        from bdm_wearable_device
        where deleted = 0
        union all
        select
            "id",
            "unique_id",
            "device_type",
            "category",
            "device_num",
            "dept_id",
            "iot_protocol"
        from bdm_rnss_device
        where deleted = 0
        union all
        select
            "id",
            "unique_id",
            "device_type",
            "category",
            "device_num",
            "dept_id",
            "iot_protocol"
        from bdm_rdss_device
        where deleted = 0
        union all
        select
            "id",
            "unique_id",
            "device_type",
            "category",
            "device_num",
            "dept_id",
            "iot_protocol"
        from bdm_pnt_device
        where deleted = 0
        union all
        select
            "id",
            "unique_id",
            "device_type",
            "category",
            "device_num",
            "dept_id",
            "iot_protocol"
        from bdm_monit_device
        where deleted = 0
    </select>-->
</mapper>

