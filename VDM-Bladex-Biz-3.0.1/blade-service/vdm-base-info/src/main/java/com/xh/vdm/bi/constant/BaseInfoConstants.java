package com.xh.vdm.bi.constant;

/**
 * @description 常用变量
 */
public class BaseInfoConstants {

	/**
	 * 数据中心id、服务器编号 初始值
	 */
	public static final Long ZERO = 0L;

    /**
     * 菜单树根节点ID
     */
    public static final Long MENU_TREE_ROOT_ID = 0L;


    //*********************字典 Dict_Type****************************
	//从业类型
	public static final String BDM_WORKER_POST = "bdm_worker_post";
	//终端类别
	public static final String BDM_DEVICE_TYPE = "bdm_device_type";
	//入网测试设备类型
	public static final String TEST_DEVICE_TYPE = "test_device_type";
	//管理状态
	public static final String MANAGE_STATUS = "manage_status";

	//设施类型
	public static final String FACILITY_TYPE = "facility_type";

	//卡类型
	public static final String IOTCARD_CATEGORY = "bdm_iotcard_category";

	//运营商
	public static final String OPERATOR = "bdm_iotcard_operator";

	//流量套餐
	public static final String DATA_PLAN = "bdm_iotcard_data_plan";

	//卡状态
	public static final String IOTCARD_STATUS = "bdm_iotcard_status";

	//目标类别
	public static final String BDM_WORKER_TYPE = "bdm_woker_type";

	//车辆类别
	public static final String BDM_CAR_CATEGORY = "bdm_car_category";
	//矿车 车辆类型
	public static final String BDM_TRUCK_CATEGORY = "bdm_truck_category";

	//北斗设备类型
	public static final String BDM_TYPE = "bdm_type";

	//定位模式
	public static final String BDM_GNSS_MODE = "bdm_gnss_mode";

	//北斗卡等级
	public static final String BDM_BDCARD_LEVEL = "bdm_bdcard_level";
	// 授时终端参考源输入
	public static final String BDM_TIME_REF_SOURCE = "bdm_time_ref_source";

	// 授时时钟输出信号
	public static final String BDM_TIME_CLOCK_SIGNAL = "bdm_time_clock_signal";

	// 箱型
	public static final String BOX_TYPE = "box_type";

	//车厢型号
	public static final String CARRIAGE_MODEL = "carriage_model";


	//********************* 终端类别 ****************************
	public static final Integer PNT_DEVICE_TYPE = 5;
	public static final Integer WEAR_DEVICE_TYPE = 2;
	public static final Integer RDSS_DEVICE_TYPE = 3;
	public static final Integer MONIT_DEVICE_TYPE = 4;
	public static final Integer RNSS_DEVICE_TYPE = 1;


	//********************* 目标类别 ****************************
	//待分配终端对象
	public static final Integer VIRTUAL_TARGET_TYPE = 0;
	public static final Integer VEHICLE_TARGET_TYPE = 1;
	public static final Integer WORKER_TARGET_TYPE = 2;
	public static final Integer FACILITY_TARGET_TYPE = 3;
	public static final Integer CONTAINER_TARGET_TYPE = 4;
	public static final Integer TEMPORARY_TARGET_TYPE = 5;
	public static final Integer VISITOR_TARGET_TYPE = 6;
	public static final Integer SHIP_TARGET_TYPE = 7;
	public static final Integer BOX_TARGET_TYPE = 8;
	public static final Integer PRECISION_TARGET_TYPE = 9;
	public static final Integer TRUCK_TARGET_TYPE = 10;


	//********************* redis缓存 ****************************
	public static final String BASEINFO_DEVICE = "baseinfo_device";

	public static final String BASEINFO_TARGET = "baseinfo_target";
}
