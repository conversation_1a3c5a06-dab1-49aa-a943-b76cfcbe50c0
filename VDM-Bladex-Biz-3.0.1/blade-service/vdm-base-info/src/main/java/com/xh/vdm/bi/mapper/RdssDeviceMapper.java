package com.xh.vdm.bi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bi.vo.request.DeviceNoBindRequest;
import com.xh.vdm.bi.vo.request.RdssDeviceRequest;
import com.xh.vdm.bi.vo.response.PersonNoBingResponse;
import com.xh.vdm.bi.vo.response.RdssDeviceResponse;
import com.xh.vdm.bi.vo.response.WorkerBingResponse;
import com.xh.vdm.biapi.entity.BdmRdssDevice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description: 短报文终端管理
 */
public interface RdssDeviceMapper extends BaseMapper<BdmRdssDevice> {

	/**
	 * 查询指定行数据
	 *
	 * @param page    分页对象
	 * @param request 查询条件
	 * @param schema
	 * @return 对象列表
	 */
	IPage<RdssDeviceResponse> queryAll(IPage page, @Param("request") RdssDeviceRequest request, @Param("account") String account, @Param("deptIds") String deptIds, @Param("schema") String schema);

	/**
	 * 通过ID查询单条数据
	 *
	 * @param id 主键
	 * @return 实例对象
	 */
	RdssDeviceResponse queryById(Long id);

	/**
	 * 新增数据
	 *
	 * @param bdmRdssDevice 实例对象
	 * @return 影响行数
	 */
	int insertRdss(BdmRdssDevice bdmRdssDevice);

	/**
	 * 修改数据
	 *
	 * @param bdmRdssDevice 实例对象
	 * @return 影响行数
	 */
	int update(BdmRdssDevice bdmRdssDevice);

	/**
	 * 通过主键删除数据
	 *
	 * @param id 主键
	 * @return 影响行数
	 */
	int deleteById(Long id);

	int deleteByIds(Long[] ids);

	IPage<PersonNoBingResponse> select(Page page, @Param("request") DeviceNoBindRequest request, @Param("account") String account, @Param("deptIds") String deptIds);

    void insertBatch(List<RdssDeviceRequest> list);

	void updateBatchByWorkerId(@Param("id") Long id, @Param("targetType") Integer targetType);

	void updateBatch(@Param("ids") List<Long> ids, @Param("id") Long id, @Param("targetType") Integer targetType, @Param("targetName") String targetName, @Param("deptId") Long deptId);

	List<PersonNoBingResponse> selectBindByWorkId(@Param("id") Long id, @Param("targetType") Integer targetType);

	List<WorkerBingResponse> selectByWorkId(@Param("id") Long id, @Param("targetType") Integer targetType);

	void deleteByTargetIds(@Param("ids") Long[] ids, @Param("targetType") Integer targetType);

    long countByUserRole(@Param("account") String account, @Param("deptIds") String deptIds);

	void updateDept(@Param("id") Long id, @Param("targetType") Integer targetType, @Param("deptId") Long deptId);

	int bindTarget(@Param("deviceId") Long deviceId,@Param("targetId") Long targetId, @Param("targetType") Integer targetType, @Param("targetName") String targetName);
}

