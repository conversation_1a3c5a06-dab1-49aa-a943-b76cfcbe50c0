package com.xh.vdm.bi.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.converter.BdmAbstractTargetConverter;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.mapper.BdmPrecisionAssemblyMapper;
import com.xh.vdm.bi.service.BdmPrecisionAssemblyService;
import com.xh.vdm.bi.service.IBdmAbstractDeviceService;
import com.xh.vdm.bi.service.IBdmAbstractTargetService;
import com.xh.vdm.bi.service.RnssDeviceService;
import com.xh.vdm.bi.utils.MapperUtils;
import com.xh.vdm.bi.utils.TargetSnowflakeIdWorker;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.PrecisionAssemblyRequest;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.PrecisionAssemblyResponse;
import com.xh.vdm.biapi.entity.BdmAbstractTarget;
import com.xh.vdm.biapi.entity.BdmPrecisionAssembly;
import com.xh.vdm.interManager.feign.IMessageClient;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.DeptProcessingUtil;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.AuthInfo;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 */
@Service
public class BdmPrecisionAssemblyServiceImpl extends ServiceImpl<BdmPrecisionAssemblyMapper, BdmPrecisionAssembly> implements BdmPrecisionAssemblyService {
	@Resource
	private BdmPrecisionAssemblyMapper precisionAssemblyMapper;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private IMessageClient messageClient;
	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private DeptProcessingUtil deptProcessingUtil;

	/**
     * 分页查询
     *
     * @param precisionAssemblyRequest 筛选条件
     * @param query                    分页对象
     * @param ceDataAuth
     * @return 查询结果
     */
	@Override
	public IPage<PrecisionAssemblyResponse> queryByPage(PrecisionAssemblyRequest precisionAssemblyRequest, Query query, DataAuthCE ceDataAuth) {
		IPage page = new Page<>(query.getCurrent(), query.getSize());
		AuthInfo response = deptProcessingUtil.handle(ceDataAuth);
		return this.precisionAssemblyMapper.queryAll(precisionAssemblyRequest, page, response.getAccount(), response.getOrgList());
	}

	/**
	 * 新增数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmPrecisionAssembly insert(PrecisionAssemblyRequest request) {

		TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.PRECISION_TARGET_TYPE, BaseInfoConstants.ZERO,BaseInfoConstants.ZERO);
		request.setId(targetId.nextId());
		request.setCreateTime(new Date());
		request.setCreateAccount(AuthUtil.getUserAccount());
		this.precisionAssemblyMapper.insert(request);

		BdmPrecisionAssembly precisionAssembly = this.getBaseMapper().selectById(request.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(precisionAssembly, abstractTarget);
		abstractTargetService.save(abstractTarget);

		Map<String, String> map = new HashMap<>();
		String key = precisionAssembly.getTargetType() + "-" + precisionAssembly.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", precisionAssembly.getNumber());
		innerMap.put("targetType", precisionAssembly.getTargetType());
		innerMap.put("deptId", precisionAssembly.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		//todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.INSERT);
		deviceInfo.setTargetId(precisionAssembly.getId());
		deviceInfo.setTargetName(precisionAssembly.getNumber());
		deviceInfo.setDeptId(precisionAssembly.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_precision_assembly", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("精密装备信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.precision(CommonConstant.OPER_TYPE_ADD, precisionAssembly);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}
		return precisionAssembly;
	}

	/**
	 * 修改数据
	 *
	 * @param request 实例对象
	 * @return 实例对象
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public BdmPrecisionAssembly update(PrecisionAssemblyRequest request) {
		BdmPrecisionAssembly assemblyInDB = this.baseMapper.selectById(request.getId());

		BdmPrecisionAssembly assembly = new BdmPrecisionAssembly();
		assembly.setId(request.getId());
		assembly.setNumber(request.getNumber() != null ? request.getNumber() : "");
		assembly.setTargetType(request.getTargetType() != null ? request.getTargetType() : TargetTypeEnum.PRECISION.getSymbol());
		assembly.setName(request.getName() != null ? request.getName() : "");
		assembly.setModel(request.getModel() != null ? request.getModel() : "");
		assembly.setManufacturer(request.getManufacturer() != null ? request.getManufacturer() : "");
		assembly.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
		assembly.setUpdateTime(new Date());
		this.precisionAssemblyMapper.update(assembly);

		BdmPrecisionAssembly precisionAssembly = this.baseMapper.selectById(request.getId());

		//新增目标同步到bdm_abstract_target
		BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
		BeanUtils.copyProperties(precisionAssembly, abstractTarget);
		abstractTargetService.updateById(abstractTarget);

		Map<String, String> map = new HashMap<>();
		String key = precisionAssembly.getTargetType() + "-" + precisionAssembly.getId();
		Map<String, Object> innerMap = new HashMap<>();
		innerMap.put("targetName", precisionAssembly.getNumber());
		innerMap.put("targetType", precisionAssembly.getTargetType());
		innerMap.put("deptId", precisionAssembly.getDeptId());
		try {
			map.put(key, new ObjectMapper().writeValueAsString(innerMap));
		} catch (JsonProcessingException e) {
			e.printStackTrace();
		}
		// todo：Map<String, String> map这样写go那边解析有问题
		redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

		DeviceInfo deviceInfo = new DeviceInfo();
		deviceInfo.setOperation(Operation.UPDATE);
		deviceInfo.setTargetId(precisionAssembly.getId());
		deviceInfo.setTargetName(precisionAssembly.getNumber());
		deviceInfo.setDeptId(precisionAssembly.getDeptId());
		try {
			this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_precision_assembly", JSON.toJSONString(deviceInfo));
		} catch (Exception e) {
			log.error("精密装备信息更新消息发送到kafka失败", e);
		}
		//messageClient
		try {
			messageClient.precision(CommonConstant.OPER_TYPE_UPDATE, precisionAssembly);
		} catch (Exception e) {
			log.error("消息发送到messageClient失败：" + e.getMessage());
		}

		//@zhouxw modify 2025-06-27 修改监管对象，终端不再同步更改组织
		/*if(!assemblyInDB.getDeptId().equals(precisionAssembly.getDeptId())){
			// 对于RnssDevice的更新
			rnssDeviceService.updateDept(precisionAssembly.getId(),precisionAssembly.getTargetType(),precisionAssembly.getDeptId());

			// 对于BdmAbstractDevice的更新
			abstractDeviceService.updateDept(precisionAssembly.getId(),precisionAssembly.getTargetType(),precisionAssembly.getDeptId());
		}*/

		return precisionAssembly;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public List<PrecisionAssemblyRequest> importExcel(List<PrecisionAssemblyRequest> list) {
		//重复的的数据
		List<PrecisionAssemblyRequest> duplicates = getDuplicateRequests(list);
		List<PrecisionAssemblyRequest> requests = new ArrayList<>(list);
		// 去重后的数据（去除所有重复数据）
		requests.removeAll(duplicates);

		if (!requests.isEmpty()) {
			List<String> numberList = requests.stream()
					.map(PrecisionAssemblyRequest::getNumber)
					.collect(Collectors.toList());
			//数据库的数据
			QueryWrapper<BdmPrecisionAssembly> queryWrapper = new QueryWrapper<>();
			if (!numberList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(queryWrapper, "number", numberList);
			}
			queryWrapper.eq("deleted",0);
			queryWrapper.select("number");
			List<String> numberExitList = this.precisionAssemblyMapper.selectList(queryWrapper)
					.stream()
					.map(BdmPrecisionAssembly::getNumber)
					.collect(Collectors.toList());

			List<BdmPrecisionAssembly> precisionAssemblies = new ArrayList<>();
			TargetSnowflakeIdWorker targetId = new TargetSnowflakeIdWorker(BaseInfoConstants.PRECISION_TARGET_TYPE, BaseInfoConstants.ZERO,BaseInfoConstants.ZERO);
			List<Long> arrayList = new ArrayList<>();
			List<BdmAbstractTarget> bdmAbstractTargets = new ArrayList<>();
			Map<String, String> map = new HashMap<>();

			for (PrecisionAssemblyRequest request : requests) {
				if (numberExitList.contains(request.getNumber())) {
					request.setMsg("装备编号已存在");
					duplicates.add(request);
				} else {
					BdmPrecisionAssembly precisionAssembly = new BdmPrecisionAssembly();
					precisionAssembly.setId(targetId.nextId());
					precisionAssembly.setNumber(request.getNumber() != null ? request.getNumber() : "");
					precisionAssembly.setTargetType(TargetTypeEnum.PRECISION.getSymbol());
					precisionAssembly.setName(request.getName() != null ? request.getName() : "");
					precisionAssembly.setModel(request.getModel() != null ? request.getModel() : "");
					precisionAssembly.setManufacturer(request.getManufacturer() != null ? request.getManufacturer() : "");
					precisionAssembly.setDeptId(request.getDeptId() != null ? request.getDeptId() : 0);
					precisionAssembly.setCreateTime(new Date());
					precisionAssembly.setDeleted(0);
					precisionAssemblies.add(precisionAssembly);
					request.setId(precisionAssembly.getId());
					arrayList.add(precisionAssembly.getId());

					String key = BaseInfoConstants.PRECISION_TARGET_TYPE + "-" + precisionAssembly.getId();
					Map<String, Object> innerMap = new HashMap<>();
					innerMap.put("targetName", precisionAssembly.getNumber());
					innerMap.put("targetType", precisionAssembly.getTargetType());
					innerMap.put("deptId", precisionAssembly.getDeptId());
					try {
						map.put(key, new ObjectMapper().writeValueAsString(innerMap));
					} catch (JsonProcessingException e) {
						e.printStackTrace();
					}

					BdmAbstractTarget abstractTarget = new BdmAbstractTarget();
					BeanUtils.copyProperties(precisionAssembly, abstractTarget);
					abstractTarget.setCategory(0);
					bdmAbstractTargets.add(abstractTarget);
				}
			}

			if (!precisionAssemblies.isEmpty()) {
				// 将剩余的数据批量插入数据库
				MapperUtils.splitListByCapacity(precisionAssemblies, MapperUtils.DEFAULT_CAPACITY)
					.forEach(precisionAssemblyList -> this.precisionAssemblyMapper.insertBatch(precisionAssemblyList));

				MapperUtils.splitListByCapacity(bdmAbstractTargets, MapperUtils.DEFAULT_CAPACITY)
					.forEach(abstractTargetList -> this.abstractTargetService.insertBatch(abstractTargetList));

				//todo：Map<String, String> map这样写go那边解析有问题
				redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

				BdmPrecisionAssembly last = precisionAssemblies.get(precisionAssemblies.size() - 1);
				DeviceInfo deviceInfo = new DeviceInfo();
				deviceInfo.setOperation(Operation.IMPORT);
				deviceInfo.setTargetId(last.getId());
				Set<Long> ids = precisionAssemblies.stream().map(BdmPrecisionAssembly::getId).collect(Collectors.toSet());
				deviceInfo.setTargetIds(ids);
				deviceInfo.setTargetType(last.getTargetType());
				deviceInfo.setTargetName(last.getNumber());
				deviceInfo.setDeptId(last.getDeptId());
				try {
					this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_precision_assembly", JSON.toJSONString(deviceInfo));
				} catch (Exception e) {
					log.error("精密装备信息更新消息发送到kafka失败", e);
				}
				QueryWrapper<BdmPrecisionAssembly> wrapper = new QueryWrapper<>();
				if (!arrayList.isEmpty()) {
					MybatisPlusWrapperUitls.longListIn(wrapper, "id", arrayList);
					List<BdmPrecisionAssembly> assemblyList = baseMapper.selectList(wrapper);
					//messageClient
					try {
						messageClient.precisionBatch(CommonConstant.OPER_TYPE_ADD, assemblyList);
					} catch (Exception e) {
						log.error("消息发送到messageClient失败：" + e.getMessage());
					}
				}
			}
		}
		return duplicates;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Boolean batchUpdate(BatchUpdateRequest batchUpdateRequest) {
		int update = precisionAssemblyMapper.batchUpdate(batchUpdateRequest.getIds(),batchUpdateRequest.getDeptId());
		if (update == 0) {
			return false;
		}
		//查询出所有的本次要更新的字段
		List<BdmPrecisionAssembly> bdmPrecisionAssemblyList = precisionAssemblyMapper.selectList(new LambdaQueryWrapper<BdmPrecisionAssembly>().in(BdmPrecisionAssembly::getId, batchUpdateRequest.getIds()));
		//转换
		List<BdmAbstractTarget> bdmAbstractTargetList = bdmPrecisionAssemblyList.stream().map(BdmAbstractTargetConverter::toBdmPrecisionAssembly).collect(Collectors.toList());
		//更新主表
		abstractTargetService.saveOrUpdateBatch(bdmAbstractTargetList);
		//批量更新redis和kafka
		batchProcess(bdmPrecisionAssemblyList);
		if (update > 0){
			return true;
		}
		return false;
	}

	/**
	 * 批量更新redis
	 * 批量发送kafka
	 */
	private void batchProcess(List<BdmPrecisionAssembly> bdmPrecisionAssemblyList){
		for (BdmPrecisionAssembly precisionAssembly : bdmPrecisionAssemblyList) {
			Map<String, String> map = new HashMap<>();
			String key = precisionAssembly.getTargetType() + "-" + precisionAssembly.getId();
			Map<String, Object> innerMap = new HashMap<>();
			innerMap.put("targetName", precisionAssembly.getNumber());
			innerMap.put("targetType", precisionAssembly.getTargetType());
			innerMap.put("deptId", precisionAssembly.getDeptId());
			try {
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			}
			// todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.OTHER);
			deviceInfo.setTargetId(precisionAssembly.getId());
			deviceInfo.setTargetName(precisionAssembly.getNumber());
			deviceInfo.setDeptId(precisionAssembly.getDeptId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_precision_assembly", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("精密装备信息更新消息发送到kafka失败", e);
			}
			//messageClient
			try {
				messageClient.precision(CommonConstant.OPER_TYPE_UPDATE, precisionAssembly);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}
		}
	}

	public List<PrecisionAssemblyRequest> getDuplicateRequests(List<PrecisionAssemblyRequest> list) {
		Map<String, Long> numberCountMap = list.stream()
			.map(PrecisionAssemblyRequest::getNumber)
			.filter(Objects::nonNull)
			.collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<String> duplicateNumbers = numberCountMap.entrySet().stream()
			.filter(entry -> entry.getValue() > 1)
			.map(Map.Entry::getKey)
			.collect(Collectors.toList());

		return list.stream()
			.filter(precisionAssembly -> duplicateNumbers.contains(precisionAssembly.getNumber()))
			.peek(precisionAssembly -> precisionAssembly.setMsg("装备编号重复"))
			.collect(Collectors.toList());
	}
}
