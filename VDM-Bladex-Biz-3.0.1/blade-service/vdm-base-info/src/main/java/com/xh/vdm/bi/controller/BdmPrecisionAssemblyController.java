package com.xh.vdm.bi.controller;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xh.vdm.bi.constant.BaseInfoConstants;
import com.xh.vdm.bi.enums.TargetTypeEnum;
import com.xh.vdm.bi.service.*;
import com.xh.vdm.bi.utils.AuthUtils;
import com.xh.vdm.bi.utils.DictUtil;
import com.xh.vdm.bi.vo.request.BatchUpdateRequest;
import com.xh.vdm.bi.vo.request.PrecisionAssemblyRequest;
import com.xh.vdm.bi.vo.response.DeviceInfo;
import com.xh.vdm.bi.vo.response.PrecisionAssemblyResponse;
import com.xh.vdm.biapi.entity.BdmDeviceStatus;
import com.xh.vdm.biapi.entity.BdmPrecisionAssembly;
import com.xh.vdm.biapi.entity.BdmRnssDevice;
import com.xh.vdm.interManager.feign.IMessageClient;
import lombok.extern.log4j.Log4j;
import org.springblade.common.annotation.Log;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.Constants;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.minio.MinioService;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.EasyExcelUtils;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 精密装备管理
 */
@Log4j
@RestController
@RequestMapping("/precision/assembly")
public class BdmPrecisionAssemblyController {
	/**
	 * 服务对象
	 */
	@Resource
	private BdmPrecisionAssemblyService bdmPrecisionAssemblyService;
	@Resource
	private RedisTemplate redisTemplate;
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;
	@Resource
	private MinioService minioService;
	@Resource
	private RnssDeviceService rnssDeviceService;
	@Resource
	private IBdmDeviceStatusService deviceStatusService;
	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;
	@Resource
	private IMessageClient messageClient;
	@Resource
	private IBdmAbstractTargetService abstractTargetService;
	@Resource
	private IBdmVirtualTargetService virtualTargetService;
	@Resource
	private CETokenUtil ceTokenUtil;
	@Resource
	private DictUtil dictUtil;

	/**
	 * 分页查询
	 *
	 * @param precisionAssemblyRequest 筛选条件
	 * @param query                    分页对象
	 * @return 查询结果
	 */
	//pre_auth_test
	@PostMapping("/list")
	public R<IPage<PrecisionAssemblyResponse>> queryByPage(@RequestBody PrecisionAssemblyRequest precisionAssemblyRequest, Query query, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		IPage<PrecisionAssemblyResponse> page = this.bdmPrecisionAssemblyService.queryByPage(precisionAssemblyRequest, query, ceDataAuth);
		enrichDataWithDict(page.getRecords());
		return R.data(page);
	}

	/**
	 * 新增数据
	 *
	 * @param request 实体
	 * @return 新增结果
	 */
	//pre_auth_test
	@Log(menu = "精密装备管理", operation = Operation.INSERT, objectType = ObjectType.PRECISION)
	@PostMapping("/save")
	public R add(@Valid @RequestBody PrecisionAssemblyRequest request, BladeUser user) {
		List<String> numbers = bdmPrecisionAssemblyService.getBaseMapper()
				.selectList(new QueryWrapper<BdmPrecisionAssembly>()
						.select("number")
						.eq("deleted", 0)
						.eq("number",request.getNumber()))
				.stream().map(BdmPrecisionAssembly::getNumber)
				.collect(Collectors.toList());
		if (!numbers.isEmpty()) {
			return R.fail("装备编号已存在！");
		}
		try {
			BdmPrecisionAssembly assembly = this.bdmPrecisionAssemblyService.insert(request);
			return R.data(ResultCode.SUCCESS.getCode(), assembly.getId().toString(), "新增成功");
		} catch (Exception e) {
			log.info(e.getMessage());
			return R.fail(e.getMessage());
		}
	}

	/**
	 * 编辑数据
	 *
	 * @param request 实体
	 * @return 编辑结果
	 */
	@Log(menu = "精密装备管理", operation = Operation.UPDATE, objectType = ObjectType.PRECISION)
	@PostMapping("/update")
	public R edit(@Valid @RequestBody PrecisionAssemblyRequest request, BladeUser user) {
		BdmPrecisionAssembly precisionAssembly = bdmPrecisionAssemblyService.getById(request.getId());
		try {
			BdmPrecisionAssembly bdmPrecisionAssembly = new BdmPrecisionAssembly();
			BeanUtils.copyProperties(request, bdmPrecisionAssembly);

			this.bdmPrecisionAssemblyService.update(request);
			String result = new CompareUtils<BdmPrecisionAssembly>().compare(precisionAssembly, bdmPrecisionAssembly);
			return R.data(ResultCode.SUCCESS.getCode(), result, "编辑成功");
		} catch (Exception e) {
			log.info("操作失败" + e.getMessage());
			return R.fail("操作失败 " + e.getMessage());
		}
	}

	@Log(menu = "精密装备管理", operation = Operation.UPDATE, objectType = ObjectType.PRECISION)
	@PostMapping("/batchUpdate")
	public R batchUpdate(@RequestBody BatchUpdateRequest batchUpdateRequest) {
		if (CollectionUtils.isEmpty(batchUpdateRequest.getIds())) {
			return R.fail("参数不能为空");
		}
		if (null == batchUpdateRequest.getDeptId()) {
			return R.fail("所属机构不能为空");
		}

		Boolean b = bdmPrecisionAssemblyService.batchUpdate(batchUpdateRequest);
		if (b){
			return R.data(ResultCode.SUCCESS.getCode(), "编辑成功");
		}else {
			return R.fail("操作失败");
		}

	}

	/**
	 * 删除数据
	 *
	 * @param ids 主键
	 * @return 删除是否成功
	 */
	@Log(menu = "精密装备管理", operation = Operation.DELETE, objectType = ObjectType.PRECISION)
	@GetMapping("/delete")
	public R deleteByIds(@RequestParam String ids, BladeUser user) {
		List<BdmPrecisionAssembly> list = this.bdmPrecisionAssemblyService.listByIds(Func.toLongList(ids));
		list.forEach(item -> {
			item.setUpdateTime(new Date());
			item.setDeleted(1);
		});
		boolean result = this.bdmPrecisionAssemblyService.updateBatchById(list);
		if (result) {
			List<Long> idList = Func.toLongList(ids);
			Long[] idsArray = idList.toArray(new Long[0]);

			rnssDeviceService.deleteByTargetIds(idsArray,TargetTypeEnum.PRECISION.getSymbol());

			List<String> keys = new ArrayList<>();
			for (Long id : idList) {
				String key = BaseInfoConstants.PRECISION_TARGET_TYPE + "-" + id;
				keys.add(key);
			}
			Map<Long, Object> map = new HashMap<>();
			// 获取 Redis 数据
			List<Object> values = redisTemplate.opsForHash().multiGet(BaseInfoConstants.BASEINFO_TARGET, keys);
			// 处理获取到的值
			for (int i = 0; i < keys.size(); i++) {
				String key = keys.get(i);
				Object value = values.get(i);
				if (value != null) {
					String jsonValue = value.toString();
					BdmPrecisionAssembly precisionAssembly = JSONObject.parseObject(jsonValue, BdmPrecisionAssembly.class);

					int index = key.indexOf("-");
					if (index != -1) {
						String idValue = key.substring(index + 1);
						Long deptId = precisionAssembly.getDeptId();
						if (map.containsKey(deptId)) {
							String existingValues = (String) map.get(deptId);
							map.put(deptId, existingValues + "、" + idValue);
						} else {
							map.put(deptId, idValue);
						}
					}
				}
			}

			redisTemplate.delete(keys.stream()
				.map(key -> BaseInfoConstants.BASEINFO_TARGET + ":" + key)
				.collect(Collectors.toSet()));

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.DELETE);
			Long lastId = idList.get(idList.size() - 1);
			deviceInfo.setTargetId(lastId);
			deviceInfo.setTargetIds(CollUtil.newHashSet(idList));
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_precision_assembly", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("精密装备信息更新消息发送到kafka失败", e);
			}

			try {
				//messageClient
				//messageClient.precisionBatch(CommonConstant.OPER_TYPE_DELETE, list);
				messageClient.precisionBatch(CommonConstant.OPER_TEST, list);
			} catch (Exception e) {
				log.error("消息发送到messageClient失败：" + e.getMessage());
			}

			//更新bdm_abstract_target
			abstractTargetService.deleteByIds(idsArray);
			//更新bdm_abstract_device
			abstractDeviceService.deleteByTargetIds(idsArray, TargetTypeEnum.PRECISION.getSymbol());
			return R.data(ResultCode.SUCCESS.getCode(), map, "删除成功");
		}
		return R.fail("操作失败");
	}

	/**
	 * 导出
	 */
	@PostMapping("/export")
	public R<String> export(@RequestBody PrecisionAssemblyRequest precisionAssemblyRequest, Query query, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		DataAuthCE ceDataAuth = ceTokenUtil.getDataAuth();
		try {
			query.setCurrent(1);
			query.setSize(Integer.MAX_VALUE);
			IPage<PrecisionAssemblyResponse> list = this.bdmPrecisionAssemblyService.queryByPage(precisionAssemblyRequest, query, ceDataAuth);
			if (list.getRecords().isEmpty()) {
				return R.fail("没有数据可导出");
			}

			Map<String, String> map = new HashMap<>();
			//从字典中获取数据
			enrichDataWithDict(list.getRecords());
			for (PrecisionAssemblyResponse response : list.getRecords()) {
				String key = BaseInfoConstants.PRECISION_TARGET_TYPE + "-" + response.getId();
				Map<String, Object> innerMap = new HashMap<>();
				innerMap.put("targetName", response.getNumber());
				innerMap.put("targetType", response.getTargetType());
				innerMap.put("deptId", response.getDeptId());
				map.put(key, new ObjectMapper().writeValueAsString(innerMap));
			}
			// todo：Map<String, String> map这样写go那边解析有问题
			redisTemplate.opsForHash().putAll(BaseInfoConstants.BASEINFO_TARGET, map);

			String menu = "精密装备管理";
			try {
				// 使用ByteArrayOutputStream创建Excel文件
				ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
				EasyExcelUtils.exportExcelToStream(
					menu,
					outputStream,
					(List) list.getRecords(),
					precisionAssemblyRequest.getHeadNameList(),
					precisionAssemblyRequest.getColumnNameList(),
					PrecisionAssemblyResponse.class
				);
				ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
				String minioFileUrl = minioService.uploadFile(menu, inputStream, outputStream.size());
				if (minioFileUrl != null) {
					return R.data(minioFileUrl);
				} else {
					return R.fail("文件上传失败");
				}
			} catch (Exception e) {
				log.info("文件上传失败 " + e.getMessage());
				return R.fail("文件上传失败");
			}
		} catch (Exception e) {
			log.error("导出失败", e);
			return R.fail("导出失败");
		}
	}

	/**
	 * 导入数据
	 */
	@Log(menu = "精密装备管理", operation = Operation.IMPORT, objectType = ObjectType.PRECISION)
	@PostMapping("/importExcel")
	public R importExcel(@Valid @RequestBody List<PrecisionAssemblyRequest> list, BladeUser user) {
		if (list != null && list.isEmpty()) {
			return R.fail("数据为空");
		}
		List<PrecisionAssemblyRequest> result = this.bdmPrecisionAssemblyService.importExcel(list);
		if (result != null && result.isEmpty()) {
			Map<Long, StringBuilder> deptIdMap = new HashMap<>();
			for (PrecisionAssemblyRequest request : list) {
				Long deptId = request.getDeptId();
				Long id = request.getId();
				if (deptIdMap.containsKey(deptId)) {
					deptIdMap.get(deptId).append("、").append(id);
				} else {
					deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
				}
			}
			return R.data(ResultCode.SUCCESS.getCode(), deptIdMap, "导入成功");
		} else {
			String filePath = "";
			String menu = "精密装备管理";
			try {
				filePath = minioService.exportToMinIO(menu, result, PrecisionAssemblyRequest.class);
			} catch (Exception e) {
				log.error("导出错误数据失败");
			}
			List<PrecisionAssemblyRequest> filteredList = list.stream()
				.filter(item -> !result.contains(item))
				.collect(Collectors.toList());
			Map<Long, StringBuilder> deptIdMap = new HashMap<>();
			if (!filteredList.isEmpty()) {
				for (PrecisionAssemblyRequest request : filteredList) {
					Long deptId = request.getDeptId();
					Long id = request.getId();
					if (deptIdMap.containsKey(deptId)) {
						deptIdMap.get(deptId).append("、").append(id);
					} else {
						deptIdMap.put(deptId, new StringBuilder(String.valueOf(id)));
					}
				}
			}
			return R.data(207, deptIdMap, filePath);
		}
	}

	private void enrichDataWithDict(List<PrecisionAssemblyResponse> records) {
		if (records.isEmpty()) {
			return;
		}
		Map<String, String> deviceTypeMap = dictUtil.getDictMap(BaseInfoConstants.BDM_DEVICE_TYPE);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

		for (PrecisionAssemblyResponse response : records) {
			Date date = null;
			try {
				date = sdf.parse(response.getCreateTime());
			} catch (ParseException e) {
				throw new RuntimeException(e);
			}
			response.setCreateTime(sdf.format(date));
			if (StringUtils.isNotBlank(response.getTerminalCategories())) {
				String[] categories = response.getTerminalCategories().split(",");
				if (categories.length > 0) {
					response.setTerminalCategories(deviceTypeMap.get(categories[0]));
				}
			}
		}
	}

	/**
	 * 解除绑定
	 */
	@GetMapping("/unbind")
	@Transactional
	@Log(menu = "精密装备管理", operation = Operation.BIND_OR_UNBIND, objectType = ObjectType.PRECISION)
	public R unbind(@RequestParam Long id,@RequestParam Integer targetType, BladeUser user, @DeptId Long deptId){
		BdmRnssDevice device = rnssDeviceService.getOne(new QueryWrapper<BdmRnssDevice>().eq("target_id", id).eq("target_type", targetType).eq("deleted", 0));
		int row = rnssDeviceService.unbind(id, targetType);
		if (row > 0){
			QueryWrapper<BdmDeviceStatus> wrapper = new QueryWrapper<>();
			wrapper.eq("device_id", device.getId());
			wrapper.eq("device_type",device.getDeviceType());
			wrapper.eq("target_id",id);
			wrapper.eq("target_type",targetType);
			deviceStatusService.remove(wrapper);

			DeviceInfo deviceInfo = new DeviceInfo();
			deviceInfo.setOperation(Operation.UNBIND);
			deviceInfo.setTargetId(id);
			deviceInfo.setTargetType(targetType);
			deviceInfo.setDeviceId(device.getId());
			try {
				this.kafkaTemplate.send(Constants.DEVICE_TARGET_CHANGE_TOPIC, "bdm_precision_assembly", JSON.toJSONString(deviceInfo));
			} catch (Exception e) {
				log.error("精密装备管理解绑更新消息发送到kafka失败", e);
			}
			abstractDeviceService.unbinding(id, TargetTypeEnum.PRECISION.getSymbol());
			//恢复待分配终端对象记录
			virtualTargetService.updateStatus(device.getUniqueId());

			return R.success("解绑成功");
		}
		return R.fail("解绑失败");
	}

}

