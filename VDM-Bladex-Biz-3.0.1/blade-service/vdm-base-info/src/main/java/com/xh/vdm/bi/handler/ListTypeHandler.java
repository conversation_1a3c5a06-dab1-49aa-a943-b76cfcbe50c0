package com.xh.vdm.bi.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeException;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URL;
import java.sql.*;
import java.time.*;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class ListTypeHandler extends BaseTypeHandler<Object> {
	/** java类型与jdbc类型映射表 */
	private static final ConcurrentHashMap<Class<?>, String> STANDARD_MAPPING = new ConcurrentHashMap<>();

	@Override
	public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
		if (parameter instanceof Array) {
			ps.setArray(i, (Array)parameter);
		} else if (parameter instanceof List) {
			Object[] objects = ((List<?>) parameter).toArray();
			Class<?> componentType = parameter.getClass().getComponentType();
			String arrayTypeName = this.resolveTypeName(componentType);
			Array array = ps.getConnection().createArrayOf(arrayTypeName, objects);
			ps.setArray(i, array);
			array.free();
		} else {
			if (!parameter.getClass().isArray()) {
				throw new TypeException("ArrayType Handler requires SQL array or java array parameter and does not support type " + parameter.getClass());
			}

			Class<?> componentType = parameter.getClass().getComponentType();
			String arrayTypeName = this.resolveTypeName(componentType);
			Array array = ps.getConnection().createArrayOf(arrayTypeName, (Object[])((Object[])parameter));
			ps.setArray(i, array);
			array.free();
		}
	}

	protected String resolveTypeName(Class<?> type) {
		return STANDARD_MAPPING.getOrDefault(type, JdbcType.JAVA_OBJECT.name());
	}

	@Override
	public Object getNullableResult(ResultSet rs, String columnName) throws SQLException {
		return extractList(rs.getArray(columnName));
	}

	@Override
	public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
		return extractList(rs.getArray(columnIndex));
	}

	@Override
	public Object getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
		return extractList(cs.getArray(columnIndex));
	}

	protected Object extractList(Array array) throws SQLException {
		if (array == null) {
			return null;
		} else {
			Object result = array.getArray();
			array.free();
			return Arrays.asList((Object[]) result);
		}
	}

	static {
		STANDARD_MAPPING.put(BigDecimal.class, JdbcType.NUMERIC.name());
		STANDARD_MAPPING.put(BigInteger.class, JdbcType.BIGINT.name());
		STANDARD_MAPPING.put(Boolean.TYPE, JdbcType.BOOLEAN.name());
		STANDARD_MAPPING.put(Boolean.class, JdbcType.BOOLEAN.name());
		STANDARD_MAPPING.put(byte[].class, JdbcType.VARBINARY.name());
		STANDARD_MAPPING.put(Byte.TYPE, JdbcType.TINYINT.name());
		STANDARD_MAPPING.put(Byte.class, JdbcType.TINYINT.name());
		STANDARD_MAPPING.put(Calendar.class, JdbcType.TIMESTAMP.name());
		STANDARD_MAPPING.put(Date.class, JdbcType.DATE.name());
		STANDARD_MAPPING.put(java.util.Date.class, JdbcType.TIMESTAMP.name());
		STANDARD_MAPPING.put(Double.TYPE, JdbcType.DOUBLE.name());
		STANDARD_MAPPING.put(Double.class, JdbcType.DOUBLE.name());
		STANDARD_MAPPING.put(Float.TYPE, JdbcType.REAL.name());
		STANDARD_MAPPING.put(Float.class, JdbcType.REAL.name());
		STANDARD_MAPPING.put(Integer.TYPE, JdbcType.INTEGER.name());
		STANDARD_MAPPING.put(Integer.class, JdbcType.INTEGER.name());
		STANDARD_MAPPING.put(LocalDate.class, JdbcType.DATE.name());
		STANDARD_MAPPING.put(LocalDateTime.class, JdbcType.TIMESTAMP.name());
		STANDARD_MAPPING.put(LocalTime.class, JdbcType.TIME.name());
		STANDARD_MAPPING.put(Long.TYPE, JdbcType.BIGINT.name());
		STANDARD_MAPPING.put(Long.class, JdbcType.BIGINT.name());
		STANDARD_MAPPING.put(OffsetDateTime.class, JdbcType.TIMESTAMP_WITH_TIMEZONE.name());
		STANDARD_MAPPING.put(OffsetTime.class, JdbcType.TIME_WITH_TIMEZONE.name());
		STANDARD_MAPPING.put(Short.class, JdbcType.SMALLINT.name());
		STANDARD_MAPPING.put(String.class, JdbcType.VARCHAR.name());
		STANDARD_MAPPING.put(Time.class, JdbcType.TIME.name());
		STANDARD_MAPPING.put(Timestamp.class, JdbcType.TIMESTAMP.name());
		STANDARD_MAPPING.put(URL.class, JdbcType.DATALINK.name());
	}
}
