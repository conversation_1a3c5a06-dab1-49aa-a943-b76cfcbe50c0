package com.xh.vdm.bi.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bi.entity.BdmUsualLog;
import com.xh.vdm.bi.mapper.BdmUsualLogMapper;
import com.xh.vdm.bi.service.BdmUsualLogService;
import com.xh.vdm.bi.vo.response.MenuOperationsResponse;
import com.xh.vdm.bi.vo.response.UsualLogTreeResponse;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * (BdmUsualLog)表服务实现类
 */
@Service
public class BdmUsualLogServiceImpl extends ServiceImpl<BdmUsualLogMapper, BdmUsualLog> implements BdmUsualLogService {


	@Override
	public List<UsualLogTreeResponse> test() {
		List<UsualLogTreeResponse> list = baseMapper.test();
		return buildTree(list);
	}
	// 构建树形结构
	private List<UsualLogTreeResponse> buildTree(List<UsualLogTreeResponse> flatList) {
		Map<Long, UsualLogTreeResponse> map = new HashMap<>();

		for (UsualLogTreeResponse node : flatList) {
			map.put(node.getMenuId(), node);
		}
		List<UsualLogTreeResponse> tree = new ArrayList<>();

		List<MenuOperationsResponse> operationsList = baseMapper.selectOperations();
		Map<String, MenuOperationsResponse> menuMap = new HashMap<>();
		for (MenuOperationsResponse menuOperationsResponse:operationsList) {
			menuMap.put(menuOperationsResponse.getMenu(),menuOperationsResponse);
		}
		// 获取 menuMap 的所有键
		Set<String> menuKeys = menuMap.keySet();




		for (UsualLogTreeResponse node : flatList) {
			if (node.getParentId() == 0) {
				tree.add(node);
			} else {
				UsualLogTreeResponse parent = map.get(node.getParentId());
				if (parent != null) {
					parent.getChildren().add(node);
				}
				// 如果当前节点的 menuName 在 menuMap 中，添加 operations 作为下一级
				if (menuKeys.contains(node.getMenuName())) {
					MenuOperationsResponse operationsResponse = menuMap.get(node.getMenuName());
					String[] stringArray = operationsResponse.getOperations().split(",");

					// 遍历操作数组并创建子节点
					for (String operation : stringArray) {
						UsualLogTreeResponse operationNode = new UsualLogTreeResponse();
						// 可以根据实际情况设置 ID
						operationNode.setMenuId(null);
						// 设置为当前菜单的 ID
						operationNode.setParentId(node.getMenuId());
						// 设置操作名称
						operationNode.setMenuName(operation.trim());
						// 初始化子节点列表
						operationNode.setChildren(new ArrayList<>());
						// 将操作节点添加到当前菜单的 children 中
						node.getChildren().add(operationNode);
					}
				}
			}
		}
		return tree;
	}
}
