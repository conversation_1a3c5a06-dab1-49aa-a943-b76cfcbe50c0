package com.xh.vdm.bi.vo.request;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;
import java.util.Date;

/**
 * (BdmTrainCargoBox)实体类
 */
@Data
@ExcelIgnoreUnannotated
public class BdmTrainCargoBoxRequest implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	@Compare("货车车厢编号")
	@ExcelProperty(value = "货车车厢编号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(12)
	private String number;

	private Integer targetType;
	@Compare("箱型")
	private Integer model;
	@Compare("尺寸")
	private Integer size;
	@Compare("所属部门id")
	private Long deptId;
	@Compare("总重")
	private Float maxGross;
	@Compare("自重/皮重")
	private Float tare;
	@Compare("载重/净重")
	private Float net;
	@Compare("最大装货容积")
	private Float cuCap;
	@Compare("长度")
	private Integer length;
	@Compare("高度")
	private Integer height;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	/**
	 * 错误信息
	 */
	@TableField(exist = false)
	@ExcelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(12)
	private String msg;

}

