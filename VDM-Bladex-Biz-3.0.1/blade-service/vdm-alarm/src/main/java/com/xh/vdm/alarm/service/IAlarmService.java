package com.xh.vdm.alarm.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.alarm.vo.request.AlarmRequest;
import com.xh.vdm.alarm.vo.response.AlarmLocationResponse;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.entity.Alarm;
import org.springblade.system.vo.DictTreeNodeVO;

import java.util.List;
import java.util.Set;

public interface IAlarmService extends IService<Alarm> {

	/**
	 * @description: 根据部门id查询今日报警数量（包含本部门以及子部门、所有部门账户关联的外部车辆）
	 * @author: zhouxw
	 * @date: 2023-06-164 21:27:36
	 * @param: [deptId]
	 * @return: long
	 **/
	long findTodayTotalCountByDeptId(long deptId, long userId) throws Exception;


	/**
	 * @description: 根据部门id查询今日报警处理数量（包含本部门以及子部门、所有部门账户关联的外部车辆）
	 * @author: zhouxw
	 * @date: 2023-06-164 21:27:36
	 * @param: [deptId 本部门id；roleType 用户角色类型 1 服务商  2 第三方服务商 3 企业]
	 * @return: long
	 **/
	long findTodayTotalHandleCountByDeptId(long deptId, long userId, String roleType) throws Exception;


	/**
	 * @description: 查询实时报警总数（一个小时内）
	 * @author: zhouxw
	 * @date: 2023-06-165 10:23:21
	 * @param: []
	 * @return: long
	 **/
	long findTodayRealTimeAlarmTotalCount(long deptId, long userId) throws Exception;


	/**
	 * @description: 查询实时报警处理总数（一个小时内）
	 * @author: zhouxw
	 * @date: 2023-06-165 10:23:21
	 * @param: []
	 * @return: long
	 **/
	long findRealTimeAlarmHandleTotalCount(long deptId, long userId, String roleType) throws Exception;


	/**
	 * @description: 根据指定报警类型查询实时报警数（一个小时内）
	 * @author: zhouxw
	 * @date: 2023-06-165 10:23:21
	 * @param: []
	 * @return: long
	 **/
	long findTodayRealTimeAlarmCount(long deptId, long userId, List<Integer> alarmTypes) throws Exception;

	/**
	 * @description: 根据指定报警类型查询实时报警处理数（一个小时内）
	 * @author: zhouxw
	 * @date: 2023-06-165 10:23:21
	 * @param: []
	 * @return: long
	 **/
	long findRealTimeAlarmHandleCount(long deptId, long userId, List<Integer> alarmTypes, String roleType) throws Exception;




	/**
	 * @description: 获取报警类型
	 * @author: zhouxw
	 * @date: 2023-06-166 15:27:16
	 * @param: [code, key]
	 * @return: java.lang.String
	 **/
	Set<DictTreeNodeVO> getChildrenFlatAlarmTypes (String code, String key) ;


	/**
	 * @description: 获取报警类型
	 * @author: zhouxw
	 * @date: 2023-06-166 15:27:16
	 * @param: [code, key]
	 * @return: java.lang.String
	 **/
	Set<DictTreeNodeVO> getChildrenFlatAlarmTypesByTenantId (String code, String key, String tenantId) ;

	// 告警数
	int getNumAlarm (AlarmRequest request, List<Long> deviceIdList);


	// 告警设备数
	int getNumAlarmDevice (AlarmRequest request);

	// 按告警类型分组的告警数
	JSONObject getNumAlarmGroupByType (AlarmRequest request);

	// 分页列表
	IPage<Alarm> getAlarmPage (AlarmRequest request, Query query);

	// 定位点列表
	List<AlarmLocationResponse> getLocationListFromAlarm (Alarm alarm);

	// 告警附件列表
	List<AlarmLocationResponse> getFileFromAlarm (Alarm alarm);

	// 处理
	R<String> dealAlarm (AlarmRequest request, BladeUser user) throws Exception;

	/**
	 * 获取告警相关的终端id
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws Exception
	 */
	List<Long> getAlarmDeviceIdList(Long startTime, Long endTime, List<String> deptIdList) throws Exception;

	/**
	 * 根据id列表查询告警列表
	 * @param ids
	 * @return
	 * @throws Exception
	 */
	List<Alarm> findAlarmListByIds(List<Long> ids) throws Exception;

	Alarm getAlarmById(Long id);
}
