package com.xh.vdm.alarm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.alarm.entity.BdmFacility;
import com.xh.vdm.alarm.mapper.FacilityMapper;
import com.xh.vdm.alarm.service.FacilityService;
import com.xh.vdm.alarm.vo.response.TargetResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * (BdmFacility)表服务实现类
 */
@Service
@Slf4j
public class FacilityServiceImpl extends ServiceImpl<FacilityMapper, BdmFacility> implements FacilityService {

	@Resource
	private FacilityMapper facilityMapper;

	@Override
	public List<TargetResponse> selectTargetByDeptId(Long deptId) {
		return facilityMapper.selectTargetByDeptId(deptId);
	}
}
