package com.xh.vdm.alarm.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Configuration
public class AlarmThreadPoolConfig {

    /**
     * SpEL expression for default core pool size.
     * Default: 2 * number of available processors.
     */
    private static final String DEFAULT_CORE_POOL_SIZE = "#{T(java.lang.Integer).valueOf(2 * T(java.lang.Runtime).getRuntime().availableProcessors())}";

    /**
     * SpEL expression for default max pool size.
     * Default: 4 * number of available processors.
     */
    private static final String DEFAULT_MAX_POOL_SIZE = "#{T(java.lang.Integer).valueOf(4 * T(java.lang.Runtime).getRuntime().availableProcessors())}";

    @Value("${blade.alarm.pool.core-size:" + DEFAULT_CORE_POOL_SIZE + "}")
    private int corePoolSize;

    @Value("${blade.alarm.pool.max-size:" + DEFAULT_MAX_POOL_SIZE + "}")
    private int maxPoolSize;

    @Value("${blade.alarm.pool.keep-alive:120}")
    private long keepAliveTime;

    @Bean(name = "alarmServiceExecutor")
    public ThreadPoolExecutor alarmServiceExecutor() {
        return new ThreadPoolExecutor(
            corePoolSize,
            maxPoolSize,
            keepAliveTime,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );
    }
} 