<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xh.vdm.alarm.mapper.DealAlarmOptionMapper">

    <select id="getOptionByDept" resultType="com.xh.vdm.alarm.entity.DealAlarmOption">
        select * from bdm_deal_alarm_option where dept_id = #{deptId}
    </select>

    <select id="getOptionByTypeLevel" resultType="com.xh.vdm.alarm.entity.DealAlarmOption">
        select * from bdm_deal_alarm_option where dept_id = #{deptId} and alarm_type = #{alarmType} and alarm_level = #{alarmLevel}
    </select>

    <insert id="setDealRule">
        insert into bdm_deal_alarm_option (
            dept_id, alarm_type, alarm_level, daily_report, monthly_report,
            server_auto_deal, server_tts_audio, server_terminal_screen, server_urgent, server_content,
            third_auto_deal, third_tts_audio, third_terminal_screen, third_urgent, third_content
        ) values
        <foreach collection="optionList" item="option" index="i" separator=",">
            (#{option.deptId}, #{option.alarmType}, #{option.alarmLevel}, #{option.dailyReport}, #{option.monthlyReport},
            #{option.serverAutoDeal}, #{option.serverTtsAudio}, #{option.serverTerminalScreen}, #{option.serverUrgent}, #{option.serverContent},
            #{option.thirdAutoDeal}, #{option.thirdTtsAudio}, #{option.thirdTerminalScreen}, #{option.thirdUrgent}, #{option.thirdContent})
        </foreach>
        on conflict(id) do update set daily_report = #{option.daily_report}, monthly_report = #{option.monthly_report}, server_auto_deal = #{option.server_auto_deal},
            server_tts_audio = #{option.server_tts_audio}, server_terminal_screen = #{option.server_terminal_screen}, server_urgent = #{option.server_urgent},
            server_content = #{option.server_content}, third_tts_audio = #{option.third_tts_audio}, third_terminal_screen = #{option.third_terminal_screen},
            third_urgent = #{option.third_urgent}, third_content = #{option.third_content}
    </insert>

    <delete id="removeDealRule">
        delete from bdm_deal_alarm_option where dept_id in
        <foreach collection="deptList" item="deptId" index="i" open="(" close=")" separator=",">
            #{deptId}
        </foreach>
    </delete>

</mapper>
