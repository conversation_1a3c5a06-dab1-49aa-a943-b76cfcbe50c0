package com.xh.vdm.alarm.util;

import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.Dept;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.user.entity.BdmVehicle;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class AlarmBusiUtil {

	@Resource
	private IUserClient userClient;

	@Resource
	private ISysClient sysClient;

	/**
	 * 获取登录用户关联的车辆列表
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	public List<BdmVehicle> getVehicleListByUserId(Long userId) throws Exception {
		//用户关联的车辆
		List<BdmVehicle> vehicleList = null;
		R<List<BdmVehicle>> userVehicleRes = userClient.findVehiclesByUserId(userId);
		if(userVehicleRes == null || !userVehicleRes.isSuccess()){
			throw new Exception("查询用户关联车辆信息feign接口失败");
		}
		vehicleList = userVehicleRes.getData();
		return vehicleList;
	}

	/**
	 * 获取用户关联的车辆id列表
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	public List<Integer> getVehicleIdList(Long userId) throws Exception{
		List<BdmVehicle> vehicleList =  getVehicleListByUserId(userId);
		List<Integer> vehicleIds = new ArrayList<>();
		vehicleList.forEach(item -> {
			vehicleIds.add(item.getId());
		});
		return vehicleIds;
	}

	/**
	 * 查询 当前部门及子部门 的部门id
	 * @return
	 * @throws Exception
	 */
	public List<Long> getChildrenAndSelfDeptId(Long deptId) throws Exception{
		List<Long> list = new ArrayList<>();
		R<List<Dept>> resDept = sysClient.getDeptChild(deptId);
		if(resDept == null || !resDept.isSuccess()){
			throw new Exception("查询部门feign接口失败");
		}
		for(Dept d : resDept.getData()){
			list.add(d.getId());
		}
		list.add(deptId);
		return list;
	}
}
