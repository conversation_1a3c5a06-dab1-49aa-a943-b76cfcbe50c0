package com.xh.vdm.alarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

// 告警附件
@Data
@TableName(value = "bdm_dsm_data_sh", autoResultMap = true)
public class BdmDsmDataSh implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String licencePlate;

    private String phone;

    private Integer alarmType;

    private Integer alarmFlag;

    private Integer alarmLevel;

    private String fileName;

    private String terminalId;

    private Date time;

    private Integer formatId;

    private Integer appendNum;

    private Integer other;

    private String driveMp4;

    private String bin;





	// 附件ID
	@TableField(value = "unique_id")
	private String uniqueId;

	// 附件图片1
	@TableField(value = "jpg1")
	private String jpg1;

	// 附件图片2
	@TableField(value = "jpg2")
	private String jpg2;

	// 附件图片3
	@TableField(value = "jpg3")
	private String jpg3;

	// 附件视频
	@TableField(value = "mp4")
	private String mp4;
}
