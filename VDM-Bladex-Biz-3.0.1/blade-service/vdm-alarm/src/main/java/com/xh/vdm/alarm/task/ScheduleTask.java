package com.xh.vdm.alarm.task;

import com.xh.vdm.alarm.constant.AlarmConstant;
import com.xh.vdm.alarm.entity.BdmAlarmDeviceId;
import com.xh.vdm.alarm.service.IBdmAlarmDeviceIdService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtil;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class ScheduleTask {


	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private IBdmAlarmDeviceIdService bdmAlarmDeviceIdService;
	/**
	 * 保存redis中存储的告警的终端id
	 * 每天2点执行，保存前一天的redis中的数据
	 */
	@Scheduled(cron="0 0 2 * * ?")
	public void saveAlarmDeviceIdFromRedis(){

		try{
			//为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
			Object executingFlag = stringRedisTemplate.opsForValue().get(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SAVE_ALARM_DEVICE_ID_FROM_REDIS);
			if(executingFlag != null && CommonConstant.COMMON_TRUE.equals(executingFlag.toString())){
				//如果已经有跑批程序在执行，则不再执行
				log.info("[车辆行驶时长统计]跑批任务已经在执行，请等当前执行程序完成后再试");
				return ;
			}

			//获取执行权限之后，添加执行标记（有效期为1个小时）
			synchronized (this){
				stringRedisTemplate.opsForValue().set(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SAVE_ALARM_DEVICE_ID_FROM_REDIS , CommonConstant.COMMON_TRUE , 1 , TimeUnit.HOURS);
			}

			//1.读取redis中前一天的发生告警的终端数据
			String dateStr = "";
			try {
				Date yestoday = DateUtil.getDateBeforeDay(new Date(), 1);
				dateStr = DateUtil.getDateString(yestoday.getTime()/1000);
			}catch (Exception e){
				log.error("查询前一天的日期失败",e);
				return ;
			}

			//2.组合redis key
			//keys中保存的是根据正则表达式查询出来的所有key
			Set<String> keys = new HashSet<>();

			String keyPrefix = AlarmConstant.CACHE_PREFIX_ALARM_DEVICE + dateStr.replace("-","") + ":";
			Set<String> set = stringRedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
				Set<String> keysTmp = new HashSet<>();
				Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(keyPrefix+"*").count(1000).build());
				while (cursor.hasNext()) {
					keysTmp.add(new String(cursor.next()));
				}
				return keysTmp;
			});
			keys.addAll(set);

			//3.查询redis，获取指定时间段、指定部门发生告警的终端号列表
			Set<String> sets = new HashSet<>();
			List<BdmAlarmDeviceId> adList = new ArrayList<>();
			for(String k : keys){
				String date = k.split(":")[2];
				String deptId = k.split(":")[3];
				BdmAlarmDeviceId ad = new BdmAlarmDeviceId();
				ad.setDate(date);
				ad.setDeptId(Long.parseLong(deptId));
				ad.setCreateTime(new Date());

				//对每一个key进行操作
				Set<String> s = stringRedisTemplate.opsForSet().members(k);
				StringBuffer sb = new StringBuffer();
				for(String str : s){
					sb.append(str).append(",");
				}
				if(sb.length() > 0){
					ad.setDeviceIdList(sb.substring(0, sb.length()-1));
				}
				adList.add(ad);
			}

			//4.保存到数据库中
			bdmAlarmDeviceIdService.saveBatch(adList);
			log.info("定时任务【保存redis中存储的告警的终端id】处理成功");

		}catch (Exception e){
			log.error("定时任务【保存redis中存储的告警的终端id】处理失败：",e);
		}finally {
			//执行完毕后，解除执行锁定
			synchronized (this){
				stringRedisTemplate.delete(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SAVE_ALARM_DEVICE_ID_FROM_REDIS );
			}
		}
	}


}
