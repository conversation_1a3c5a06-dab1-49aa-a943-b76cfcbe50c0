<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xh.vdm.alarm.mapper.ImpalaAlarmMapper">

    <select id="getAlarmCountByDeptIdAndDuration" resultType="long">
        select (dc.num + bc.num) count from
            (select count(*) num from alarms where alarm_level != 0 and dept_id in (
                <foreach collection="deptIds" item="deptId" separator=",">
                    #{deptId}
                </foreach>
            ) and (is_wrong != 1 or is_wrong is null) and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}) dc,
            (
                <if test="vehicleIds != null and vehicleIds.size() > 0">
                    select count(*) num from alarms
                    where alarm_level != 0
                    and vehicle_id in (
                    <foreach collection="vehicleIds" item="vehicleId" separator=",">
                        #{vehicleId}
                    </foreach>
                    )
                    and (is_wrong != 1 or is_wrong is null) and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
                </if>
                <if test="vehicleIds == null or vehicleIds.size() == 0">
                    select 0 num
                </if>
            ) bc
    </select>


    <select id="getAlarmCountByDeptIdAndDurationAndType" resultType="long">
        select (dc.num + bc.num) count from
        (select count(*) num from alarms where alarm_level != 0 and dept_id in (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId}
            </foreach>
        ) and (is_wrong != 1 or is_wrong is null) and alarm_time >= #{startTime} and alarm_time &lt; #{endTime} and alarm_type in (
        <foreach collection="alarmTypes" item="alarmType" separator=",">
            #{alarmType}
        </foreach>
        )) dc,
        (
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            select count(*) num from alarms
            where alarm_level != 0
            and vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
            and (is_wrong != 1 or is_wrong is null)
            and alarm_time >= #{startTime}
            and alarm_time &lt; #{endTime}
            and alarm_type in (
            <foreach collection="alarmTypes" item="alarmType" separator=",">
                #{alarmType}
            </foreach>
            )
        </if>
        <if test="vehicleIds == null or vehicleIds.size() == 0">
            select 0 num
        </if>
        ) bc
    </select>


    <select id="getAlarmHandleCountByDeptIdAndDuration" resultType="long">
        select (dc.num + bc.num) count from
        (select count(*) num from alarms where alarm_level != 0 and dept_id in (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId}
            </foreach>
        ) and (is_wrong != 1 or is_wrong is null) and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
        <!-- 服务商 -->
        <if test="roleType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="roleType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="roleType == 3">
            and company_state = 1
        </if>
        ) dc,
        (
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            select count(*) num from alarms
            where 1 = 1
            and vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
            and (is_wrong != 1 or is_wrong is null) and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
            <!-- 服务商 -->
            <if test="roleType == 1">
                and server_state = 1
            </if>
            <!-- 第三方 -->
            <if test="roleType == 2">
                and third_state = 1
            </if>
            <!-- 企业 -->
            <if test="roleType == 3">
                and company_state = 1
            </if>
        </if>
        <if test="vehicleIds == null or vehicleIds.size() == 0">
            select 0 num
        </if>
        ) bc
    </select>

</mapper>
