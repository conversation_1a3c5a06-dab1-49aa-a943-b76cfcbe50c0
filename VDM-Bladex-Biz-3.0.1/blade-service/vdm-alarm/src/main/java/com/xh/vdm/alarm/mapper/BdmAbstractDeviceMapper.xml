<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.alarm.mapper.BdmAbstractDeviceMapper">

    <select id="getAccessDeviceList" resultType="com.xh.vdm.alarm.entity.BdmAbstractDevice">
        select *
        from bdm_abstract_device
        where 1 = 1
        <if test="deptIds != null and deptIds != ''">
            and dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and create_account = #{account}
        </if>

    </select>

    <select id="getAccessDeviceIdList" resultType="long">
        select id
        from bdm_abstract_device
        where 1 = 1
        <if test="deptIds != null and deptIds != ''">
            and dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and create_account = #{account}
        </if>

    </select>

    <select id="getDeviceIdListByCondition" resultType="long">
        select id
        from bdm_abstract_device
        where 1 = 1
        <if test="uniqueId != null and uniqueId != ''">
            and unique_id = like #{uniqueId}
        </if>
        <if test="category != null">
            and category = #{category}
        </if>
        and deleted = 0
    </select>

</mapper>

