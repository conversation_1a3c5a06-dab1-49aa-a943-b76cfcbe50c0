package com.xh.vdm.alarm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.alarm.dto.DealAlarmOptionCache;
import com.xh.vdm.alarm.entity.DealAlarmOption;
import com.xh.vdm.alarm.service.IDealAlarmOptionService;
import com.xh.vdm.alarm.vo.request.ByDeptIdRequest;
import com.xh.vdm.alarm.vo.request.SetDealRuleRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.system.cache.SysCache;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.BeanUtils;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "实时报警", protocols = "http", produces = "application/json", consumes = "application/json")
@RestController
@RequestMapping("/alarm/realtime")
@Slf4j
public class RealTimeAlarmController {

	@Resource
	private KafkaTemplate<String, Object> kafkaTemplate;

	@Resource
	private IDictBizClient dictBizClient;

	@Resource(name = "DealAlarmOptionService")
	private IDealAlarmOptionService dealAlarmOptionService;

	/**
	 * 按照用户权限合并处理获取最终部门列表
	 *
	 * @param user 当前登录用户
	 * @param deptId  查询部门ID
	 * @return 用户权限与所查部门的本下级部门交集
	 */
	private R<List<Long>> getFinalDeptList (BladeUser user, Long deptId) {
		if ((user == null) || (user.getUserId() == null) || StringUtils.isBlank(user.getAccount()) || StringUtils.isBlank(user.getDeptId()) || StringUtils.isBlank(user.getTenantId())) {
			return R.fail(ResultCode.FAILURE.getCode(), "用户登录信息异常。");
		}

		// 获取登录用户本下级部门ID列表
		String[] deptListStr = user.getDeptId().split(",");
		List<Long> list1 = new ArrayList<>();
		List<Long> tmp;
		for (String deptIdStr : deptListStr) {
			tmp = SysCache.getDeptChildIds(Long.parseLong(deptIdStr));
			if (CollectionUtils.isNotEmpty(tmp)) {
				list1.addAll(tmp);
			}
		}
		if (deptId == null) {
			return R.data(ResultCode.SUCCESS.getCode(), list1, "");
		}

		// 获取查询参数本下级部门ID列表
		List<Long> list2 = SysCache.getDeptChildIds(deptId);
		if ((list2 == null) || list2.isEmpty()) {
			return R.fail(ResultCode.FAILURE.getCode(), "所提供组织架构信息异常。");
		}

		// 取两个部门ID列表交集
		Collection<Long> res = CollectionUtils.intersection(list1, list2);
		if ((res == null) || res.isEmpty()) {
			return R.fail(ResultCode.FAILURE.getCode(), "没有所提供组织架构的权限。");
		}

		return R.data(ResultCode.SUCCESS.getCode(), (List<Long>) res, "");
	}

	@ApiOperation(value = "实时报警自动处理规则设置", httpMethod = "POST")
	@PostMapping("deal-rule/set")
	public R<String> setDealRule (@Validated @RequestBody SetDealRuleRequest request, BladeUser user) {
		R<List<Long>> r1 = this.getFinalDeptList(user, null);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		Collection<Long> deptList = CollectionUtils.intersection(request.getDeptList(), r1.getData());
		if (CollectionUtils.isEmpty(deptList)) {
			return R.fail(ResultCode.FAILURE.getCode(), "没有所提供企业的操作权限。");
		}

		R<List<DictBiz>> r2 = this.dictBizClient.getList(CommonConstant.DICT_ALARM_LEVEL);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}
		if (CollectionUtils.isEmpty(r2.getData())) {
			return R.fail(ResultCode.FAILURE.getCode(), "报警等级获取异常。");
		}

		List<String> alarmLevelList = r2.getData().parallelStream().map(DictBiz::getDictKey).collect(Collectors.toList());
		JSONObject ruleMap = request.getRuleMap();
		List<DealAlarmOption> optionList = new ArrayList<>();
		for (long deptId : deptList) {
			for (String alarmType : ruleMap.keySet()) {
				for (String alarmLevel : alarmLevelList) {
					if ((!alarmType.matches("\\d+")) || (!alarmLevel.matches("\\d+"))) {
						return R.fail(ResultCode.FAILURE.getCode(), "不正确的报警类型（" + alarmType + "）或报警等级（" + alarmLevel + "）");
					}

					DealAlarmOption option = new DealAlarmOption();
					option.setDeptId(deptId);
					option.setAlarmType(alarmType);
					option.setAlarmLevel(alarmLevel);
					option.setDailyReport((byte) 0);
					option.setMonthlyReport((byte) 0);
					option.setServerAutoDeal((byte) 0);
					option.setServerTtsAudio((byte) 0);
					option.setServerTerminalScreen((byte) 0);
					option.setServerUrgent((byte) 0);
					option.setServerContent("");
					option.setThirdAutoDeal((byte) 0);
					option.setThirdTtsAudio((byte) 0);
					option.setThirdTerminalScreen((byte) 0);
					option.setThirdUrgent((byte) 0);
					option.setThirdContent("");
					JSONObject alarmTypeRule = JSON.parseObject(JSON.toJSONString(ruleMap.get(alarmType)));
					Object dailyReport = alarmTypeRule.get("daily_report");
					Object monthlyReport = alarmTypeRule.get("monthly_report");
					Object serverAutoDeal = alarmTypeRule.get("server_auto_deal");
					Object thirdAutoDeal = alarmTypeRule.get("third_auto_deal");
					if ((dailyReport != null) && (Byte.parseByte(dailyReport.toString()) != ((byte) 0))) {
						option.setDailyReport((byte) 1);
					}
					if ((monthlyReport != null) && (Byte.parseByte(monthlyReport.toString()) != ((byte) 0))) {
						option.setMonthlyReport((byte) 1);
					}
					if ((serverAutoDeal != null) && (Byte.parseByte(serverAutoDeal.toString()) != ((byte) 0))) {
						option.setServerAutoDeal((byte) 1);
					}
					if ((thirdAutoDeal != null) && (Byte.parseByte(thirdAutoDeal.toString()) != ((byte) 0))) {
						option.setThirdAutoDeal((byte) 1);
					}

					Object o = alarmTypeRule.get("server" + alarmLevel);
					if ((serverAutoDeal != null) && (Byte.parseByte(serverAutoDeal.toString()) != 0) && (o != null)) {
						JSONObject alarmLevelRule = JSON.parseObject(JSON.toJSONString(o));
						Object serverTtsAudio = alarmLevelRule.get("server_tts_audio");
						Object serverTerminalScreen = alarmLevelRule.get("server_terminal_screen");
						Object serverUrgent = alarmLevelRule.get("server_urgent");
						Object serverContent = alarmLevelRule.get("server_content");
						if ((serverTtsAudio != null) && (Byte.parseByte(serverTtsAudio.toString()) != ((byte) 0))) {
							option.setServerTtsAudio((byte) 1);
						}
						if ((serverTerminalScreen != null) && (Byte.parseByte(serverTerminalScreen.toString()) != ((byte) 0))) {
							option.setServerTerminalScreen((byte) 1);
						}
						if ((serverUrgent != null) && (Byte.parseByte(serverUrgent.toString()) != ((byte) 0))) {
							option.setServerUrgent((byte) 1);
						}
						if ((serverContent != null) && StringUtils.isNotBlank(serverContent.toString())) {
							option.setServerContent(serverContent.toString());
						}
					}

					o = alarmTypeRule.get("third" + alarmLevel);
					if ((thirdAutoDeal != null) && (Byte.parseByte(thirdAutoDeal.toString()) != 0) && (o != null)) {
						JSONObject alarmLevelRule = JSON.parseObject(JSON.toJSONString(o));
						Object thirdTtsAudio = alarmLevelRule.get("third_tts_audio");
						Object thirdTerminalScreen = alarmLevelRule.get("third_terminal_screen");
						Object thirdUrgent = alarmLevelRule.get("third_urgent");
						Object thirdContent = alarmLevelRule.get("third_content");
						if ((thirdTtsAudio != null) && (Byte.parseByte(thirdTtsAudio.toString()) != ((byte) 0))) {
							option.setThirdTtsAudio((byte) 1);
						}
						if ((thirdTerminalScreen != null) && (Byte.parseByte(thirdTerminalScreen.toString()) != ((byte) 0))) {
							option.setThirdTerminalScreen((byte) 1);
						}
						if ((thirdUrgent != null) && (Byte.parseByte(thirdUrgent.toString()) != ((byte) 0))) {
							option.setThirdUrgent((byte) 1);
						}
						if ((thirdContent != null) && StringUtils.isNotBlank(thirdContent.toString())) {
							option.setThirdContent(thirdContent.toString());
						}
					}

					optionList.add(option);
					DealAlarmOptionCache cache = new DealAlarmOptionCache();
					BeanUtils.copyProperties(option, cache);
					DealAlarmOptionCache.dealAlarmOptionMap.put(deptId + "-" + alarmType + "-" + alarmLevel, cache);
				}
			}
		}

		this.dealAlarmOptionService.removeDealRule(deptList);
		this.dealAlarmOptionService.setDealRule(optionList);
		this.kafkaTemplate.send("refresh_deal_alarm_option", "refresh");
		return R.success(ResultCode.SUCCESS, "");
	}

	@ApiOperation(value = "实时报警自动处理规则列表", httpMethod = "POST")
	@PostMapping("deal-rule/get")
	public R<JSONObject> getDealRule (@Validated @RequestBody ByDeptIdRequest request, BladeUser user) {
		R<List<Long>> r1 = this.getFinalDeptList(user, null);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		Long deptId = request.getDeptId();
		if (deptId == null) {
			String[] deptListStr = user.getDeptId().split(",");
			deptId = Long.parseLong(deptListStr[0]);
		} else if (!r1.getData().contains(deptId)) {
			return R.fail(ResultCode.FAILURE.getCode(), "没有查看该单位数据的权限。");
		}

		List<DealAlarmOption> optionList = this.dealAlarmOptionService.getOptionByDept(deptId);
		JSONObject optionMap = new JSONObject();
		for (DealAlarmOption option : optionList) {
			JSONObject alarmTypeRule;
			String alarmType = option.getAlarmType();
			String alarmLevel = option.getAlarmLevel();
			if (!optionMap.containsKey(alarmType)) {
				alarmTypeRule = new JSONObject();
				alarmTypeRule.put("daily_report", option.getDailyReport());
				alarmTypeRule.put("monthly_report", option.getMonthlyReport());
				alarmTypeRule.put("server_auto_deal", option.getServerAutoDeal());
				alarmTypeRule.put("third_auto_deal", option.getThirdAutoDeal());
				optionMap.put(alarmType, alarmTypeRule);
			}

			alarmTypeRule = JSON.parseObject(JSON.toJSONString(optionMap.get(alarmType)));
			JSONObject alarmLevelRule = new JSONObject();
			alarmLevelRule.put("server_tts_audio", option.getServerTtsAudio());
			alarmLevelRule.put("server_terminal_screen", option.getServerTerminalScreen());
			alarmLevelRule.put("server_urgent", option.getServerUrgent());
			alarmLevelRule.put("server_content", option.getServerContent());
			alarmTypeRule.put("server" + alarmLevel, alarmLevelRule);
//			alarmTypeRule = JSON.parseObject(JSON.toJSONString(optionMap.get(alarmType)));
			alarmLevelRule = new JSONObject();
			alarmLevelRule.put("third_tts_audio", option.getThirdTtsAudio());
			alarmLevelRule.put("third_terminal_screen", option.getThirdTerminalScreen());
			alarmLevelRule.put("third_urgent", option.getThirdUrgent());
			alarmLevelRule.put("third_content", option.getThirdContent());
			alarmTypeRule.put("third" + alarmLevel, alarmLevelRule);
			optionMap.put(alarmType, alarmTypeRule);
		}

		return R.data(ResultCode.SUCCESS.getCode(), optionMap, "");
	}
}
