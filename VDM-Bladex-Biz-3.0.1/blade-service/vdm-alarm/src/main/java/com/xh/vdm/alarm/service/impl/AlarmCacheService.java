package com.xh.vdm.alarm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.alarm.entity.BdmAbstractDevice;
import com.xh.vdm.alarm.entity.BdmAbstractTarget;
import com.xh.vdm.alarm.mapper.BdmAbstractDeviceMapper;
import com.xh.vdm.alarm.mapper.BdmAbstractTargetMapper;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.DictCodeConstant;
import org.springblade.common.constant.DictKeyConstant;
import org.springblade.common.constant.RedisConstant;
import org.springblade.core.tool.api.R;
import org.springblade.entity.Alarm;
import org.springblade.system.entity.DeptNode;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.user.entity.BaseUser;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * 告警缓存服务 - 优化缓存查询性能
 */
@Slf4j
@Service
public class AlarmCacheService {

    @Resource
    private RedisTemplate<String, DeptNode> deptNodeRedisTemplate;

    @Resource
    private RedisTemplate<String, BaseUser> userRedisTemplate;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private IDictBizClient dictBizClient;

    @Resource
    private BdmAbstractDeviceMapper abstractDeviceMapper;

    @Resource
    private BdmAbstractTargetMapper abstractTargetMapper;

    private final ExecutorService cacheExecutor = Executors.newFixedThreadPool(
        Runtime.getRuntime().availableProcessors()
    );

    // 缓存字典数据，避免重复查询
    private final Map<String, Map<String, String>> dictCache = new ConcurrentHashMap<>();
    private volatile long lastDictCacheTime = 0;
    private static final long DICT_CACHE_EXPIRE_TIME = 5 * 60 * 1000; // 5分钟

    /**
     * 批量获取告警相关的所有缓存数据
     */
    public AlarmCacheData batchGetAlarmCacheData(List<Alarm> alarmList, String tenantId) {
        if (CollectionUtils.isEmpty(alarmList)) {
            return new AlarmCacheData();
        }

        long startTime = System.currentTimeMillis();

        // 提取所有需要查询的ID
        Set<Long> deptIds = alarmList.stream().map(Alarm::getDeptId).collect(Collectors.toSet());
        Set<Long> handlerIds = alarmList.stream().map(Alarm::getHandler).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> deviceIds = alarmList.stream().map(Alarm::getDeviceId).collect(Collectors.toSet());
        Set<Long> targetIds = alarmList.stream().map(Alarm::getTargetId).collect(Collectors.toSet());

        // 并行查询所有缓存数据
        CompletableFuture<Map<Long, DeptNode>> deptFuture = CompletableFuture.supplyAsync(() ->
            batchGetDeptNodes(deptIds, tenantId), cacheExecutor);

        CompletableFuture<Map<Long, BaseUser>> userFuture = CompletableFuture.supplyAsync(() ->
            batchGetBaseUsers(handlerIds, tenantId), cacheExecutor);

        CompletableFuture<Map<Long, BdmAbstractDevice>> deviceFuture = CompletableFuture.supplyAsync(() ->
            batchGetDevices(deviceIds), cacheExecutor);

        CompletableFuture<Map<Long, BdmAbstractTarget>> targetFuture = CompletableFuture.supplyAsync(() ->
            batchGetTargets(targetIds), cacheExecutor);

        CompletableFuture<Map<String, Map<String, String>>> dictFuture = CompletableFuture.supplyAsync(() ->
            batchGetDictMaps(), cacheExecutor);

        try {
            // 等待所有异步操作完成
            CompletableFuture.allOf(deptFuture, userFuture, deviceFuture, targetFuture, dictFuture).join();

            AlarmCacheData cacheData = new AlarmCacheData();
            cacheData.setDeptMap(deptFuture.get());
            cacheData.setUserMap(userFuture.get());
            cacheData.setDeviceMap(deviceFuture.get());
            cacheData.setTargetMap(targetFuture.get());
            cacheData.setDictMaps(dictFuture.get());

            long endTime = System.currentTimeMillis();
            log.info("批量缓存查询耗时: {}ms", endTime - startTime);

            return cacheData;
        } catch (Exception e) {
            log.error("批量获取缓存数据失败", e);
            return new AlarmCacheData();
        }
    }

    /**
     * 批量获取部门信息
     */
    private Map<Long, DeptNode> batchGetDeptNodes(Set<Long> deptIds, String tenantId) {
        if (CollectionUtils.isEmpty(deptIds)) {
            return new HashMap<>();
        }

        try {
            List<DeptNode> deptList = deptNodeRedisTemplate.<Long, DeptNode>opsForHash()
                .multiGet(RedisConstant.HASH_DEPT_NODE + tenantId, deptIds);

            Map<Long, DeptNode> deptMap = new HashMap<>();
            for (DeptNode dept : deptList) {
                if (dept != null) {
                    deptMap.put(dept.id, dept);
                }
            }
            return deptMap;
        } catch (Exception e) {
            log.error("批量获取部门信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 批量获取用户信息
     */
    private Map<Long, BaseUser> batchGetBaseUsers(Set<Long> userIds, String tenantId) {
        if (CollectionUtils.isEmpty(userIds)) {
            return new HashMap<>();
        }

        try {
            List<BaseUser> userList = userRedisTemplate.<Long, BaseUser>opsForHash()
                .multiGet(RedisConstant.HASH_BASE_USER + tenantId, userIds);

            Map<Long, BaseUser> userMap = new HashMap<>();
            for (BaseUser user : userList) {
                if (user != null) {
                    userMap.put(user.id, user);
                }
            }
            return userMap;
        } catch (Exception e) {
            log.error("批量获取用户信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 批量获取设备信息
     */
    private Map<Long, BdmAbstractDevice> batchGetDevices(Set<Long> deviceIds) {
        if (CollectionUtils.isEmpty(deviceIds)) {
            return new HashMap<>();
        }

        try {
            List<BdmAbstractDevice> deviceList = abstractDeviceMapper.selectList(
                Wrappers.lambdaQuery(BdmAbstractDevice.class)
                    .in(BdmAbstractDevice::getId, deviceIds)
                    .select(BdmAbstractDevice::getId, BdmAbstractDevice::getUniqueId,
                           BdmAbstractDevice::getDeviceNum, BdmAbstractDevice::getIotProtocol,
                           BdmAbstractDevice::getCategory, BdmAbstractDevice::getDeviceType)
            );

            return deviceList.stream().collect(Collectors.toMap(
                BdmAbstractDevice::getId, device -> device, (existing, replacement) -> existing));
        } catch (Exception e) {
            log.error("批量获取设备信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 批量获取目标信息
     */
    private Map<Long, BdmAbstractTarget> batchGetTargets(Set<Long> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) {
            return new HashMap<>();
        }

        try {
            List<BdmAbstractTarget> targetList = abstractTargetMapper.selectList(
                Wrappers.lambdaQuery(BdmAbstractTarget.class)
                    .in(BdmAbstractTarget::getId, targetIds)
                    .select(BdmAbstractTarget::getId, BdmAbstractTarget::getName,
                           BdmAbstractTarget::getTargetType)
            );

            return targetList.stream().collect(Collectors.toMap(
                BdmAbstractTarget::getId, target -> target, (existing, replacement) -> existing));
        } catch (Exception e) {
            log.error("批量获取目标信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 批量获取字典映射（带缓存）
     */
    private Map<String, Map<String, String>> batchGetDictMaps() {
        long currentTime = System.currentTimeMillis();

        // 检查缓存是否过期
        if (currentTime - lastDictCacheTime < DICT_CACHE_EXPIRE_TIME && !dictCache.isEmpty()) {
            return new HashMap<>(dictCache);
        }

        synchronized (this) {
            // 双重检查
            if (currentTime - lastDictCacheTime < DICT_CACHE_EXPIRE_TIME && !dictCache.isEmpty()) {
                return new HashMap<>(dictCache);
            }

            Map<String, Map<String, String>> result = new HashMap<>();

            try {
                // 并行获取所有字典数据
                CompletableFuture<Map<String, String>> targetTypeFuture = CompletableFuture.supplyAsync(() ->
                    getDictMap(DictCodeConstant.TARGET_TYPE, DictKeyConstant.DEFAULT_ROOT_KEY), cacheExecutor);

                CompletableFuture<Map<String, String>> deviceTypeFuture = CompletableFuture.supplyAsync(() ->
                    getDictMap(DictCodeConstant.DEVICE_TYPE, DictKeyConstant.DEFAULT_ROOT_KEY), cacheExecutor);

                CompletableFuture<Map<String, String>> alarmTypeFuture = CompletableFuture.supplyAsync(() ->
                    getDictMapWithTenant(DictCodeConstant.ALARM_TYPE, CommonConstant.DEFAULT_TENANT_ID), cacheExecutor);

                CompletableFuture<Map<String, String>> alarmLevelFuture = CompletableFuture.supplyAsync(() ->
                    getDictMap(DictCodeConstant.ALARM_LEVEL, DictKeyConstant.DEFAULT_ROOT_KEY), cacheExecutor);

                CompletableFuture<Map<String, String>> alarmSourceFuture = CompletableFuture.supplyAsync(() ->
                    getDictMap(DictCodeConstant.ALARM_SOURCE, DictKeyConstant.DEFAULT_ROOT_KEY), cacheExecutor);

                CompletableFuture<Map<String, String>> handleMeasuresFuture = CompletableFuture.supplyAsync(() ->
                    getDictMap(DictCodeConstant.HANDLE_MEASURES, DictKeyConstant.DEFAULT_ROOT_KEY), cacheExecutor);

                CompletableFuture.allOf(targetTypeFuture, deviceTypeFuture, alarmTypeFuture,
                                      alarmLevelFuture, alarmSourceFuture, handleMeasuresFuture).join();

                result.put("targetType", targetTypeFuture.get());
                result.put("deviceType", deviceTypeFuture.get());
                result.put("alarmType", alarmTypeFuture.get());
                result.put("alarmLevel", alarmLevelFuture.get());
                result.put("alarmSource", alarmSourceFuture.get());
                result.put("handleMeasures", handleMeasuresFuture.get());

                // 更新缓存
                dictCache.clear();
                dictCache.putAll(result);
                lastDictCacheTime = currentTime;

            } catch (Exception e) {
                log.error("批量获取字典数据失败", e);
            }

            return result;
        }
    }

    private Map<String, String> getDictMap(String code, String key) {
        try {
            R<Map<String, String>> result = dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(code, key);
            return (result.isSuccess() && result.getData() != null) ? result.getData() : new HashMap<>();
        } catch (Exception e) {
            log.error("获取字典数据失败: code={}, key={}", code, key, e);
            return new HashMap<>();
        }
    }

    private Map<String, String> getDictMapWithTenant(String code, String tenantId) {
        try {
            R<Map<String, String>> result = dictBizClient.getDictTreeFlatByCodeWithSelfMapTenant(code, tenantId);
            return (result.isSuccess() && result.getData() != null) ? result.getData() : new HashMap<>();
        } catch (Exception e) {
            log.error("获取字典数据失败: code={}, tenantId={}", code, tenantId, e);
            return new HashMap<>();
        }
    }

    /**
     * 缓存数据容器
     */
    public static class AlarmCacheData {
        private Map<Long, DeptNode> deptMap = new HashMap<>();
        private Map<Long, BaseUser> userMap = new HashMap<>();
        private Map<Long, BdmAbstractDevice> deviceMap = new HashMap<>();
        private Map<Long, BdmAbstractTarget> targetMap = new HashMap<>();
        private Map<String, Map<String, String>> dictMaps = new HashMap<>();

        // Getters and Setters
        public Map<Long, DeptNode> getDeptMap() { return deptMap; }
        public void setDeptMap(Map<Long, DeptNode> deptMap) { this.deptMap = deptMap; }

        public Map<Long, BaseUser> getUserMap() { return userMap; }
        public void setUserMap(Map<Long, BaseUser> userMap) { this.userMap = userMap; }

        public Map<Long, BdmAbstractDevice> getDeviceMap() { return deviceMap; }
        public void setDeviceMap(Map<Long, BdmAbstractDevice> deviceMap) { this.deviceMap = deviceMap; }

        public Map<Long, BdmAbstractTarget> getTargetMap() { return targetMap; }
        public void setTargetMap(Map<Long, BdmAbstractTarget> targetMap) { this.targetMap = targetMap; }

        public Map<String, Map<String, String>> getDictMaps() { return dictMaps; }
        public void setDictMaps(Map<String, Map<String, String>> dictMaps) { this.dictMaps = dictMaps; }
    }
}
