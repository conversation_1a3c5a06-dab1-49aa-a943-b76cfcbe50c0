package com.xh.vdm.alarm.constant;

// 告警处理状态
public enum AlarmHandleState {

	HANDLE_STATE_PEND("待处理", (byte) 0),
	HANDLE_STATE_DONE("已处理", (byte) 1),
	HANDLE_STATE_WRONG("误报", (byte) 2);

	private String key;
	private byte value;

	AlarmHandleState (String key, byte value) {
		this.setKey(key);
		this.setValue(value);
	}

	public String getKey () {
		return this.key;
	}

	public void setKey (String key) {
		this.key = key;
	}

	public byte getValue () {
		return this.value;
	}

	public void setValue (byte value) {
		this.value = value;
	}

	public static AlarmHandleState getByValue (byte value) {
		AlarmHandleState[] stateList = values();
		for (AlarmHandleState state : stateList) {
			if (state.getValue() == value) {
				return state;
			}
		}

		return null;
	}

	public static String getKeyByValue (byte value) {
		AlarmHandleState[] stateList = values();
		for (AlarmHandleState state : stateList) {
			if (state.getValue() == value) {
				return state.getKey();
			}
		}

		return "";
	}
}
