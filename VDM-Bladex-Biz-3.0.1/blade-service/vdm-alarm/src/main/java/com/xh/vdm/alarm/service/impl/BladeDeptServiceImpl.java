package com.xh.vdm.alarm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.alarm.entity.BladeDept;
import com.xh.vdm.alarm.mapper.BladeDeptMapper;
import com.xh.vdm.alarm.service.IBladeDeptService;
import com.xh.vdm.alarm.vo.response.BladeDeptDeviceResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 机构表 服务实现类
 */
@Service
public class BladeDeptServiceImpl extends ServiceImpl<BladeDeptMapper, BladeDept> implements IBladeDeptService {

	@Resource
	private BladeDeptMapper dladeDeptMapper;

	@Override
	public BladeDeptDeviceResponse selectDept(Long deptId) {
		return dladeDeptMapper.selectDept(deptId);
	}
}
