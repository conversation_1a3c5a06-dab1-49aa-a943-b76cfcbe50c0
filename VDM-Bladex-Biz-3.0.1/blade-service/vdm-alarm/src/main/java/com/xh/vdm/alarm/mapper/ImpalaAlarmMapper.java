package com.xh.vdm.alarm.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.alarm.entity.ImpalaAlarm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("impala")
public interface ImpalaAlarmMapper extends BaseMapper<ImpalaAlarm> {

	/**
	 * @description: 根据部门id和时间段获取报警数量（本节点及子节点）
	 * 不包含误报
	 * @author: zhouxw
	 * @date: 2023-06-164 21:05:12
	 * @param: [deptIds 本节点及子节点的deptId]
	 * @return: long
	 **/
	long getAlarmCountByDeptIdAndDuration(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime);


	/**
	 * @description: 根据部门id和时间段、报警类型获取报警数量（本节点及子节点）
	 * 不包含误报
	 * @author: zhouxw
	 * @date: 2023-06-164 21:05:12
	 * @param: [deptIds 本节点及子节点的deptId； alarmTypes 报警类型，多个类型中间使用英文逗号分隔；startTime 开始时间，精确到秒的时间戳，时间范围包含开始时间； endTime 结束时间，精确到秒的时间戳，时间范围不包含结束时间；]
	 * @return: long
	 **/
	long getAlarmCountByDeptIdAndDurationAndType(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("alarmTypes") List<Integer> alarmTypes);


	/**
	 * @description: 根据部门id和时间段获取报警处理数量（本节点及子节点）
	 * 不包含误报
	 * @author: zhouxw
	 * @date: 2023-06-164 21:05:12
	 * @param: [deptIds 本节点及子节点的deptId; roleType 1 服务商  2 第三方  3 企业]
	 * @return: long
	 **/
	//todo 实现方式有问题，一个账号可能会有多个角色，既是服务商，又是第三方服务商
	long getAlarmHandleCountByDeptIdAndDuration(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> userId, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("roleType") String roleType);
}
