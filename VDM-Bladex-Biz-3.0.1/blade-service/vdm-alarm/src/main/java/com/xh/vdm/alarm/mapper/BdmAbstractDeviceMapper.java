package com.xh.vdm.alarm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.alarm.entity.BdmAbstractDevice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmAbstractDevice)表数据库访问层
 */
public interface BdmAbstractDeviceMapper extends BaseMapper<BdmAbstractDevice> {

	/**
	 * 获取可以访问的终端列表
	 * @param deptIds
	 * @param account
	 * @return
	 */
	List<BdmAbstractDevice> getAccessDeviceList(@Param("deptIds") String deptIds, @Param("account") String account);

	/**
	 * 获取有权限访问的终端id
	 * @param deptIds
	 * @param account
	 * @return
	 */
	List<Long> getAccessDeviceIdList(@Param("deptIds") String deptIds, @Param("account") String account);

	/**
	 * 根据条件查询终端id列表
	 * @param uniqueId
	 * @param category
	 * @return
	 */
	List<Long> getDeviceIdListByCondition(@Param("uniqueId") String uniqueId, @Param("category") Integer category);

}

