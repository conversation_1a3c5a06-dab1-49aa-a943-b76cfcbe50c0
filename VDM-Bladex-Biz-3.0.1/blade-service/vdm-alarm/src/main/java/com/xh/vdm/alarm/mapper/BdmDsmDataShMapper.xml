<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xh.vdm.alarm.mapper.BdmDsmDataShMapper">

    <select id="getAttachFromUniqueIdList" resultType="com.xh.vdm.alarm.entity.BdmDsmDataSh">
        select unique_id, jpg1, jpg2, jpg3, mp4 from bdm_dsm_data_sh where unique_id in
        <foreach collection="attachIdList" item="attachId" separator="," open="(" close=")">
            #{attachId}
        </foreach>
    </select>
</mapper>
