package com.xh.vdm.alarm.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 告警终端id表，用于保存每天每个部门发生告警的终端id列表。可用于快速统计发生告警的终端
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmAlarmDeviceId implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 日期
     */
    private String date;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 发生告警的终端id列表，中间以英文逗号分隔
     */
    private String deviceIdList;

    /**
     * 数据创建时间
     */
    private Date createTime;


}
