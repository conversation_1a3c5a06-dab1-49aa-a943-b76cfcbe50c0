package com.xh.vdm.alarm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.alarm.entity.BdmDsmDataSh;
import com.xh.vdm.alarm.vo.response.AlarmLocationResponse;
import org.springblade.entity.Alarm;

import java.util.List;
import java.util.Map;

public interface IBdmDsmDataShService extends IService<BdmDsmDataSh> {

	// 为报警列表中的每个报警，获取预期附件数及实际附件数。
	Map<Long, Map<String, Integer>> getNumAttachForAlarmList (List<Alarm> alarmList);

	List<AlarmLocationResponse> getFileByAlarm(Alarm alarm);
}
