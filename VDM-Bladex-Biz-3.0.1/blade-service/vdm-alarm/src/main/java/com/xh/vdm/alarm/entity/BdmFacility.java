package com.xh.vdm.alarm.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 基础设施管理实体
 */
@Data
public class BdmFacility implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private String name;

	private Integer category;

	private String address;

	private Integer targetType;

	private Long deptId;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	private String geometry;
}

