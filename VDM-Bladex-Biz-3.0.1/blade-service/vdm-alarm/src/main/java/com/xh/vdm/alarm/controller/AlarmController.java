package com.xh.vdm.alarm.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.alarm.constant.AlarmHandleState;
import com.xh.vdm.alarm.entity.BdmAbstractDevice;
import com.xh.vdm.alarm.entity.BdmAbstractTarget;
import com.xh.vdm.alarm.mapper.AlarmMapper;
import com.xh.vdm.alarm.mapper.BdmAbstractDeviceMapper;
import com.xh.vdm.alarm.mapper.BdmAbstractTargetMapper;
import com.xh.vdm.alarm.service.impl.AlarmCacheService;
import com.xh.vdm.alarm.service.IAlarmService;
import com.xh.vdm.alarm.service.IBdmDsmDataShService;
import com.xh.vdm.alarm.service.IUserDeptRegulatesService;
import com.xh.vdm.alarm.vo.request.AlarmRequest;
import com.xh.vdm.alarm.vo.request.group.AlarmDetailGroup;
import com.xh.vdm.alarm.vo.request.group.AlarmListGroup;
import com.xh.vdm.alarm.vo.request.group.DealAlarmGroup;
import com.xh.vdm.alarm.vo.response.AlarmListResponse;
import com.xh.vdm.alarm.vo.response.AlarmLocationResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.DictKeyConstant;
import org.springblade.common.minio.MinioService;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.EasyExcelUtils;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.entity.Alarm;
import org.springblade.entity.DataAuthCE;
import org.springblade.entity.Location;
import org.springblade.system.entity.DeptNode;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.user.entity.BaseUser;
import org.springblade.system.user.entity.UserDeptRegulates;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;


@Slf4j
@Api(tags = "告警", protocols = "http", produces = "application/json", consumes = "application/json")
@RestController
@RequestMapping("/alarm")
public class AlarmController {

	private final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(
		Runtime.getRuntime().availableProcessors() * 2,
		Runtime.getRuntime().availableProcessors() * 4,
		60, TimeUnit.SECONDS,
		new LinkedBlockingQueue<>(1000),
		Executors.defaultThreadFactory(),
		new ThreadPoolExecutor.CallerRunsPolicy()
	);

	@Resource
	private RedisTemplate<String, DeptNode> deptNodeRedisTemplate;


	@Resource
	private RedisTemplate<String, BaseUser> userRedisTemplate;

	@Resource
	private RedisTemplate<String, String> redisTemplate;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private IDictBizClient dictBizClient;

	@Resource
	private MinioService minioService;

	@Resource(name = "AlarmService")
	private IAlarmService alarmService;

	@Resource
	private AlarmMapper alarmMapper;

	@Resource(name = "BdmDsmDataShService")
	private IBdmDsmDataShService bdmDsmDataShService;

	@Resource
	private IUserDeptRegulatesService userDeptRegulatesService;

	@Resource
	private CETokenUtil ceTokenUtil;

	@Resource
	private BdmAbstractDeviceMapper abstractDeviceMapper;

	@Resource
	private BdmAbstractTargetMapper abstractTargetMapper;

	@Resource
	private AlarmCacheService alarmCacheService;

	// 明确用户能访问的组织架构。
	private List<Long> getUserDeptList (BladeUser user) throws Exception {
		if (AuthUtil.isAdministrator()) {
			return null;
		}

		List<UserDeptRegulates> tmpList = this.userDeptRegulatesService.list(
			Wrappers.lambdaQuery(UserDeptRegulates.class).eq(UserDeptRegulates::getUserId, user.getUserId())
		);
		if(ceTokenUtil.isCELogin()){
			//如果是国能登录，则不再判断监管机构
			DataAuthCE data = ceTokenUtil.getDataAuth();
			if(data == null){
				log.error("未获取国能数据权限");
				throw new Exception("用户未登录或登录失效");
			}else{
				List<String> orgStrList = data.getOrgList();
				List<Long> orgList =  orgStrList.stream().map(Long::parseLong).collect(Collectors.toList());
				return orgList;
			}

		}else{
			//如果是位置平台登录
			if (CollectionUtils.isEmpty(tmpList)) {
				throw new Exception("未能获取该用户的组织架构。");
			}
			return tmpList.parallelStream().map(UserDeptRegulates::getDeptId).collect(Collectors.toList());
		}


	}

	// 为请求体补充数据权限相关参数。
	private R<Object> addDataRangeForRequest (AlarmRequest request, BladeUser user) throws Exception {
		if ((user == null) || (user.getUserId() == null) || StringUtils.isBlank(user.getTenantId())) {
			return R.fail(ResultCode.FAILURE, "用户信息异常。");
		}

		request.setDeptList(this.getUserDeptList(user));
		return R.success(ResultCode.SUCCESS, "");
	}


	/**
	 *  把Alarm转为AlarmListResponse，以返回给前端使用。
	 *  优化版本：使用批量缓存查询提升性能
	 * @param alarmList
	 * @param tenantId
	 * @return
	 */
	private List<AlarmListResponse> formAlarmListResponse (List<Alarm> alarmList, String tenantId) {
		// 避免告警个数为0时抛出异常
		if (alarmList.isEmpty()) {
			return new ArrayList<>();
		}

		long start = System.currentTimeMillis();

		// 使用优化的缓存服务批量获取所有缓存数据
		AlarmCacheService.AlarmCacheData cacheData = alarmCacheService.batchGetAlarmCacheData(alarmList, tenantId);

		long cacheEnd = System.currentTimeMillis();
		log.info(">>>>优化后缓存查询耗时：" + (cacheEnd - start));

		// 从缓存数据中获取映射表
		Map<Long, DeptNode> deptMap = cacheData.getDeptMap();
		Map<Long, BaseUser> userMap = cacheData.getUserMap();
		Map<Long, BdmAbstractDevice> deviceMap = cacheData.getDeviceMap();
		Map<Long, BdmAbstractTarget> targetMap = cacheData.getTargetMap();
		Map<String, Map<String, String>> dictMaps = cacheData.getDictMaps();

		Map<String, String> targetTypeMap = dictMaps.getOrDefault("targetType", new HashMap<>());
		Map<String, String> deviceTypeMap = dictMaps.getOrDefault("deviceType", new HashMap<>());
		Map<String, String> alarmTypeMap = dictMaps.getOrDefault("alarmType", new HashMap<>());
		Map<String, String> alarmLevelMap = dictMaps.getOrDefault("alarmLevel", new HashMap<>());
		Map<String, String> alarmSourceMap = dictMaps.getOrDefault("alarmSource", new HashMap<>());
		Map<String, String> handleMeasuresMap = dictMaps.getOrDefault("handleMeasures", new HashMap<>());

		// 获取附件信息
		long start2 = System.currentTimeMillis();
		Map<Long, Map<String, Integer>> alarmIdMapNumAttach = this.bdmDsmDataShService.getNumAttachForAlarmList(alarmList);
		long end2 = System.currentTimeMillis();
		log.info(">>>>查询数据库耗时:" + (end2 - start2));
		List<AlarmListResponse> responseList = new ArrayList<>();
		for (Alarm alarm : alarmList) {
			AlarmListResponse response = new AlarmListResponse();
			response.setId(alarm.getId());
			response.setTargetType(alarm.getTargetType());
			response.setTargetTypeName(targetTypeMap.getOrDefault(alarm.getTargetType().toString(), ""));
			response.setTargetId(alarm.getTargetId());
			response.setTargetName("");
			response.setDeviceType(alarm.getDeviceType());
			response.setDeviceTypeName(deviceTypeMap.getOrDefault(alarm.getDeviceType().toString(), ""));
			response.setDeviceId(alarm.getDeviceId());
			response.setUniqueId("");
			response.setDeviceNum("");
			response.setAlarmType(alarmTypeMap.getOrDefault(alarm.getType().toString(), ""));
			response.setAlarmLevel(alarmLevelMap.getOrDefault(alarm.getLevel().toString(), ""));
			response.setAlarmSource(alarmSourceMap.getOrDefault(alarm.getSource().toString(), ""));
			response.setAlarmComplete(alarm.getCompleted());
			response.setStartAddr(alarm.getStartAddr());
			response.setStartTime((alarm.getStartTime() <= 0) ? "" : this.format.format(new Date(alarm.getStartTime() * 1000)));
			response.setEndAddr(alarm.getEndAddr());
			response.setEndTime((alarm.getEndTime() <= 0) ? "" : this.format.format(new Date(alarm.getEndTime() * 1000)));
			response.setNumAttachExpect(0);
			response.setNumAttachReal(0);
			response.setNumAttach("0/0");
			response.setRuleName("");
			response.setIotProtocol((byte) 0);
			response.setHandleState(AlarmHandleState.getKeyByValue(alarm.getHandleState()));
			response.setHandleMeasures("");
			response.setHandleContent(alarm.getHandleContent());
			response.setHandler("");
			response.setHandleTime((alarm.getHandleTime() <= 0) ? "" : this.format.format(new Date(alarm.getHandleTime() * 1000)));
			response.setSpeed(alarm.getSpeed());

			DeptNode dept = deptMap.get(alarm.getDeptId());
			response.setDeptName(((dept == null) || StringUtils.isBlank(dept.name)) ? "" : dept.name);
			if (alarm.getHandler() != null) {
				BaseUser user = userMap.get(alarm.getHandler());
				response.setHandler(((user == null) || StringUtils.isBlank(user.name)) ? "" : user.name);
			}

			String auxiliary = alarm.getAuxiliary();
			if (StringUtils.isNotBlank(auxiliary)) {
				JSONObject attach = JSON.parseObject(auxiliary);
				if (attach != null) {
					attach.getOrDefault("attachment_num", 0);
					response.setRuleName(attach.getOrDefault("rule_name", "").toString());
				}
			}

			// 设置目标信息
			BdmAbstractTarget target = targetMap.get(alarm.getTargetId());
			if (target != null) {
				response.setTargetName(target.getName());
			}

			// 设置设备信息
			BdmAbstractDevice device = deviceMap.get(alarm.getDeviceId());
			if (device != null) {
				response.setUniqueId(device.getUniqueId());
				response.setDeviceNum(device.getDeviceNum());
				response.setIotProtocol(device.getIotProtocol()==null?0:device.getIotProtocol().byteValue());
				response.setDeviceCate(device.getCategory()==null?0:device.getCategory());
				response.setDeviceCateName(deviceTypeMap.getOrDefault(device.getCategory()+"", ""));
			}

			if (alarmIdMapNumAttach.containsKey(alarm.getId())) {
				Map<String, Integer> tmp = alarmIdMapNumAttach.get(alarm.getId());
				response.setNumAttachExpect(tmp.getOrDefault("num_attach_expect", 0));
				response.setNumAttachReal(tmp.getOrDefault("num_attach_real", 0));
				response.setNumAttach(response.getNumAttachReal() + "/" + response.getNumAttachExpect());
			}

			String handleMeasuresListStr = alarm.getHandleMeasures();
			if (StringUtils.isNotBlank(handleMeasuresListStr)) {
				String[] handleMeasuresList = handleMeasuresListStr.split(",");
				StringBuffer sb = new StringBuffer();
				for (String handleMeasures : handleMeasuresList) {
					sb.append(handleMeasuresMap.getOrDefault(handleMeasures, "")).append("、");
				}

				// 把最后的"、"去掉。
				response.setHandleMeasures(sb.substring(0, sb.length() - "、".length()));
			}

			responseList.add(response);
		}

		return responseList;
	}

	/**
	 * 导出专用的告警列表响应构建方法
	 * 优化版本：使用批量缓存查询提升性能
	 * @param alarmList
	 * @param tenantId
	 * @return
	 */
	private List<AlarmListResponse> formAlarmListResponseForExport (List<Alarm> alarmList, String tenantId) {
		// 避免告警个数为0时抛出异常
		if (alarmList.isEmpty()) {
			return new ArrayList<>();
		}

		long start = System.currentTimeMillis();

		// 使用优化的缓存服务批量获取所有缓存数据
		AlarmCacheService.AlarmCacheData cacheData = alarmCacheService.batchGetAlarmCacheData(alarmList, tenantId);

		long cacheEnd = System.currentTimeMillis();
		log.info(">>>>导出优化后缓存查询耗时：" + (cacheEnd - start));

		// 从缓存数据中获取映射表
		Map<Long, DeptNode> deptMap = cacheData.getDeptMap();
		Map<Long, BaseUser> userMap = cacheData.getUserMap();
		Map<Long, BdmAbstractDevice> deviceMap = cacheData.getDeviceMap();
		Map<Long, BdmAbstractTarget> targetMap = cacheData.getTargetMap();
		Map<String, Map<String, String>> dictMaps = cacheData.getDictMaps();

		Map<String, String> targetTypeMap = dictMaps.getOrDefault("targetType", new HashMap<>());
		Map<String, String> deviceTypeMap = dictMaps.getOrDefault("deviceType", new HashMap<>());
		Map<String, String> alarmTypeMap = dictMaps.getOrDefault("alarmType", new HashMap<>());
		Map<String, String> alarmLevelMap = dictMaps.getOrDefault("alarmLevel", new HashMap<>());
		Map<String, String> alarmSourceMap = dictMaps.getOrDefault("alarmSource", new HashMap<>());
		Map<String, String> handleMeasuresMap = dictMaps.getOrDefault("handleMeasures", new HashMap<>());

		// 获取附件信息
		long start2 = System.currentTimeMillis();
		Map<Long, Map<String, Integer>> alarmIdMapNumAttach = this.bdmDsmDataShService.getNumAttachForAlarmList(alarmList);
		long end2 = System.currentTimeMillis();
		log.info(">>>>导出查询数据库耗时:" + (end2 - start2));
		List<AlarmListResponse> responseList = new ArrayList<>();
		for (Alarm alarm : alarmList) {
			AlarmListResponse response = new AlarmListResponse();
			response.setId(alarm.getId());
			response.setTargetType(alarm.getTargetType());
			response.setTargetTypeName(targetTypeMap.getOrDefault(alarm.getTargetType().toString(), ""));
			response.setTargetId(alarm.getTargetId());
			response.setTargetName("");
			response.setDeviceType(alarm.getDeviceType());
			response.setDeviceTypeName(deviceTypeMap.getOrDefault(alarm.getDeviceType().toString(), ""));
			response.setDeviceId(alarm.getDeviceId());
			response.setUniqueId("");
			response.setDeviceNum("");
			response.setAlarmType(alarmTypeMap.getOrDefault(alarm.getType().toString(), ""));
			response.setAlarmLevel(alarmLevelMap.getOrDefault(alarm.getLevel().toString(), ""));
			response.setAlarmSource(alarmSourceMap.getOrDefault(alarm.getSource().toString(), ""));
			response.setAlarmComplete(alarm.getCompleted());
			response.setStartAddr(alarm.getStartAddr());
			response.setStartTime((alarm.getStartTime() <= 0) ? "" : this.format.format(new Date(alarm.getStartTime() * 1000)));
			response.setEndAddr(alarm.getEndAddr());
			response.setEndTime((alarm.getEndTime() <= 0) ? "" : this.format.format(new Date(alarm.getEndTime() * 1000)));
			response.setNumAttachExpect(0);
			response.setNumAttachReal(0);
			response.setNumAttach("0/0");
			response.setRuleName("");
			response.setIotProtocol((byte) 0);
			response.setHandleState(AlarmHandleState.getKeyByValue(alarm.getHandleState()));
			response.setHandleMeasures("");
			response.setHandleContent(alarm.getHandleContent());
			response.setHandler("");
			response.setHandleTime((alarm.getHandleTime() <= 0) ? "" : this.format.format(new Date(alarm.getHandleTime() * 1000)));

			DeptNode dept = deptMap.get(alarm.getDeptId());
			response.setDeptName(((dept == null) || StringUtils.isBlank(dept.name)) ? "" : dept.name);
			if (alarm.getHandler() != null) {
				BaseUser user = userMap.get(alarm.getHandler());
				response.setHandler(((user == null) || StringUtils.isBlank(user.name)) ? "" : user.name);
			}

			String auxiliary = alarm.getAuxiliary();
			if (StringUtils.isNotBlank(auxiliary)) {
				JSONObject attach = JSON.parseObject(auxiliary);
				if (attach != null) {
					attach.getOrDefault("attachment_num", 0);
					response.setRuleName(attach.getOrDefault("rule_name", "").toString());
				}
			}

			// 设置目标信息
			BdmAbstractTarget target = targetMap.get(alarm.getTargetId());
			if (target != null) {
				response.setTargetName(target.getName());
			}

			// 设置设备信息
			BdmAbstractDevice device = deviceMap.get(alarm.getDeviceId());
			if (device != null) {
				response.setUniqueId(device.getUniqueId());
				response.setDeviceNum(device.getDeviceNum());
				response.setIotProtocol(device.getIotProtocol()==null?0:device.getIotProtocol().byteValue());
				response.setDeviceCate(device.getCategory()==null?0:device.getCategory());
				response.setDeviceCateName(deviceTypeMap.getOrDefault(device.getCategory()+"", ""));
			}

			if (alarmIdMapNumAttach.containsKey(alarm.getId())) {
				Map<String, Integer> tmp = alarmIdMapNumAttach.get(alarm.getId());
				response.setNumAttachExpect(tmp.getOrDefault("num_attach_expect", 0));
				response.setNumAttachReal(tmp.getOrDefault("num_attach_real", 0));
				response.setNumAttach(response.getNumAttachReal() + "/" + response.getNumAttachExpect());
			}

			String handleMeasuresListStr = alarm.getHandleMeasures();
			if (StringUtils.isNotBlank(handleMeasuresListStr)) {
				String[] handleMeasuresList = handleMeasuresListStr.split(",");
				StringBuffer sb = new StringBuffer();
				for (String handleMeasures : handleMeasuresList) {
					sb.append(handleMeasuresMap.getOrDefault(handleMeasures, "")).append("、");
				}

				// 把最后的"、"去掉。
				response.setHandleMeasures(sb.substring(0, sb.length() - "、".length()));
			}

			responseList.add(response);
		}

		return responseList;
	}

	@ApiOperation(value = "告警数", httpMethod = "POST")
	@PostMapping("/num")
	public R<Integer> getNumAlarm (@Validated(AlarmListGroup.class) @RequestBody AlarmRequest request, BladeUser user) {
		try {
			R<Object> r = this.addDataRangeForRequest(request, user);
			if (!r.isSuccess()) {
				return R.fail(r.getCode(), r.getMsg());
			}
			long start1 = System.currentTimeMillis();
			DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
			long end1 = System.currentTimeMillis();
			log.info("查询CE数据权限耗时：" + (end1 - start1));
			if(StringUtils.isEmpty(dataAuthCE.getOrgListStr()) && StringUtils.isEmpty(dataAuthCE.getAccount())){
				return R.data(0);
			}
			List<Long> deviceList = alarmService.getAlarmDeviceIdList(request.getStartTime(), request.getEndTime(), dataAuthCE.getOrgList());

			//5.查询相关终端的告警信息
			long start2 = System.currentTimeMillis();
			if(deviceList == null || deviceList.size() < 1){
				return R.data(0);
			}
			long end2 = System.currentTimeMillis();
			log.info("第一个查询耗时：" + (end2 - start2));
			long start3 = System.currentTimeMillis();
			//优化：impala的查询中，in最多支持10000个参数，且无法修改。经测试，in 10000个参数耗时500ms左右。
			//位置平台中，共有20W终端，使用union拼接sql执行效率低。因为这里只需要查询总数，所以，可以使用多线程 + in 10000个参数的方式进行查询
			//对组织列表按照10000每组进行拆分
			int pageSize = 999;
			int groupCount = deviceList.size() / pageSize + (deviceList.size()%pageSize==0?0:1);
			CountDownLatch countDownLatch = new CountDownLatch(groupCount);
			AtomicLong totalCount = new AtomicLong();
			for(int i = 0 ; i < groupCount-1; i++){
				List<Long> dList = deviceList.subList((i * pageSize), (i + 1) * pageSize);
				threadPool.submit(() -> {
					long startTimeT = System.currentTimeMillis();
					int num = this.alarmService.getNumAlarm(request, dList);
					long endTimeT = System.currentTimeMillis();
					log.info("单次执行查询耗时：" + (endTimeT - startTimeT));
					totalCount.addAndGet(num);
					countDownLatch.countDown();
				});
			}
			//执行最后一批的查询
			if(groupCount == 1 || deviceList.size() / pageSize > 0){
				List<Long> dList = deviceList.subList((groupCount-1)*pageSize, deviceList.size());
				int num = this.alarmService.getNumAlarm(request, dList);
				totalCount.addAndGet(num);
				countDownLatch.countDown();
			}
			countDownLatch.await();
			long end3 = System.currentTimeMillis();
			log.info("第二个查询耗时：" + (end3 - start3));
			return R.data((int)totalCount.get());
		} catch (Exception e) {
			log.error("fail get num alarm: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "获取告警数异常：" + e.getMessage());
		}
	}


	private Set<String> getSetMembers(Set<String> keys) {
		// 获取Redis连接
		RedisConnection connection = stringRedisTemplate.getConnectionFactory().getConnection();
		try {
			// 创建pipeline
			connection.openPipeline();

			// 在pipeline中添加SMEMBERS命令
			for(String key : keys){
				connection.sMembers(stringRedisTemplate.getStringSerializer().serialize(key));
			}

			// 执行pipeline
			List<Object> results = connection.closePipeline();
			Set<String> set = new HashSet<>();
			// 解析结果，这里只有一个命令的返回值，所以取第一个元素
			if (!results.isEmpty()) {
				for(Object obj : results){
					LinkedHashSet<byte[]> links = (LinkedHashSet<byte[]>) obj;
					for(byte[] b : links){
						String str = new String(b);
						set.add(str);
					}
				}
				return set;
			}
		} finally {
			// 关闭连接
			connection.close();
		}
		return null;
	}



	@ApiOperation(value = "告警设备数", httpMethod = "POST")
	@PostMapping("/device/num")
	public R<Integer> getNumAlarmDevice (@Validated(AlarmListGroup.class) @RequestBody AlarmRequest request, BladeUser user) {
		try {
			R<Object> r = this.addDataRangeForRequest(request, user);
			if (!r.isSuccess()) {
				return R.fail(r.getCode(), r.getMsg());
			}

			return R.data(this.alarmService.getNumAlarmDevice(request));
		} catch (Exception e) {
			log.error("fail get num alarm device: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "获取告警设备数异常：" + e.getMessage());
		}
	}

	@ApiOperation(value = "按告警类型分组的告警数", httpMethod = "POST")
	@PostMapping("/num-total-group-by-type")
	public R<JSONObject> getNumTotalGroupByType (BladeUser user) {
		try {
			AlarmRequest request = new AlarmRequest();
			long currentTime = System.currentTimeMillis() / 1000;
			request.setStartTime(DateUtil.getDayFirstSecondTimestampNoLine(currentTime));
			request.setEndTime(DateUtil.getDayLastSecondTimestamp(currentTime));
			List<Short> alarmTypeList = new ArrayList<>();
			alarmTypeList.add(Short.parseShort(DictKeyConstant.DEFAULT_ROOT_KEY));
			request.setAlarmTypeList(alarmTypeList);
			R<Object> r = this.addDataRangeForRequest(request, user);
			if (!r.isSuccess()) {
				return R.fail(r.getCode(), r.getMsg());
			}

			return R.data(this.alarmService.getNumAlarmGroupByType(request));
		} catch (Exception e) {
			log.error("fail get num total alarm group by type: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "按告警类型分组的告警数，获取异常：" + e.getMessage());
		}
	}

	@ApiOperation(value = "按告警类型分组的未处理告警数", httpMethod = "POST")
	@PostMapping("/num-pend-group-by-type")
	public R<JSONObject> getNumPendGroupByType (BladeUser user) {
		try {
			AlarmRequest request = new AlarmRequest();
			long currentTime = System.currentTimeMillis() / 1000;
			request.setStartTime(DateUtil.getDayFirstSecondTimestampNoLine(currentTime));
			request.setEndTime(DateUtil.getDayLastSecondTimestamp(currentTime));
			List<Short> alarmTypeList = new ArrayList<>();
			alarmTypeList.add(Short.parseShort(DictKeyConstant.DEFAULT_ROOT_KEY));
			request.setAlarmTypeList(alarmTypeList);
			request.setHandleState(AlarmHandleState.HANDLE_STATE_PEND.getValue());
			R<Object> r = this.addDataRangeForRequest(request, user);
			if (!r.isSuccess()) {
				return R.fail(r.getCode(), r.getMsg());
			}

			return R.data(this.alarmService.getNumAlarmGroupByType(request));
		} catch (Exception e) {
			log.error("fail get num pend alarm group by type: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "按告警类型分组的未处理告警数，获取异常：" + e.getMessage());
		}
	}

	@ApiOperation(value = "分页列表", httpMethod = "POST")
	@PostMapping("/page")
	public R<IPage<AlarmListResponse>> getAlarmPage (@Validated(AlarmListGroup.class) @RequestBody AlarmRequest request, BladeUser user, Query query) {
		try {
			if (request.getDeptId()!=null&&request.getDeptId()!=0){
				request.setDeptList(Collections.singletonList(request.getDeptId()));
			}
			long start1 = System.currentTimeMillis();
			IPage<Alarm> tmp = this.alarmService.getAlarmPage(request, query);
			IPage<AlarmListResponse> res = new Page<>();
			BeanUtils.copyProperties(tmp, res);
			if (tmp.getTotal() <= 0) {
				return R.data(res);
			}
			long end1 = System.currentTimeMillis();
			log.info(">>>>查询分页耗时：" + (end1 - start1));
			long start2 = System.currentTimeMillis();
			res.setRecords(this.formAlarmListResponse(tmp.getRecords(), user.getTenantId()));
			long end2 = System.currentTimeMillis();
			log.info(">>>>设置结果耗时：" +(end2 - start2));
			return R.data(res);
		} catch (Exception e) {
			log.error("fail get alarm page: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "获取告警分页列表异常：" + e.getMessage());
		}
	}

	@ApiOperation(value = "导出", httpMethod = "POST")
	@PostMapping("/export")
	public R<String> exportAlarm (@Validated(AlarmListGroup.class) @RequestBody AlarmRequest request, BladeUser user) {
		try {
			R<Object> r = this.addDataRangeForRequest(request, user);
			if (!r.isSuccess()) {
				return R.fail(r.getCode(), r.getMsg());
			}

			Query query = new Query();
			query.setCurrent(1);
			query.setSize(100000000);
			request.setExport(true);
			IPage<Alarm> page = this.alarmService.getAlarmPage(request, query);
			if (page.getTotal() <= 0) {
				return R.fail(ResultCode.FAILURE, "没有符合条件的数据。");
			}

			String title = "告警信息列表";
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			EasyExcelUtils.exportExcelToStream(
				title,
				outputStream,
				this.formAlarmListResponseForExport(page.getRecords(), user.getTenantId()),
				request.getHeadNameList(),
				request.getColumnNameList(),
				AlarmListResponse.class
			);

			ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
			String url = this.minioService.uploadFile(title, inputStream, outputStream.size());
			if (StringUtils.isBlank(url)) {
				return R.fail(ResultCode.FAILURE, "导出告警异常。");
			} else {
				return R.data(url);
			}
		} catch (Exception e) {
			log.error("fail export alarm: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "导出告警失败：" + e.getMessage());
		}
	}

	@ApiOperation(value = "详情", httpMethod = "POST")
	@PostMapping("/info")
	public R<AlarmListResponse> getAlarmInfo (@Validated(AlarmDetailGroup.class) @RequestBody AlarmRequest request, BladeUser user) {
		try {
			R<Object> r = this.addDataRangeForRequest(request, user);
			if (!r.isSuccess()) {
				return R.fail(r.getCode(), r.getMsg());
			}

			Query query = new Query();
			query.setCurrent(1);
			query.setSize(1);
			//IPage<Alarm> tmpPage = this.alarmService.getAlarmPage(request, query);
			Alarm alarm = this.alarmService.getAlarmById(request.getIdList().get(0));
			if (alarm==null) {
				return R.fail(ResultCode.FAILURE, "对应告警不存在。");
			}
			Long endTime=alarm.getEndTime()==null?System.currentTimeMillis()/1000:alarm.getEndTime();
			//查询速度
			List<Location> locationList=alarmMapper.getLocationListFromAlarm(alarm.getStartTime(),endTime,alarm.getTargetType(),alarm.getTargetId(),alarm.getDeviceType(),alarm.getDeviceId());
			Location lastLocation = locationList.get(locationList.size() - 1);
			Double speed=lastLocation.getSpeed();
			alarm.setSpeed(speed);
			List<Alarm> list=new ArrayList<>();
			list.add(alarm);
			return R.data(this.formAlarmListResponse(list, user.getTenantId()).get(0));
		} catch (Exception e) {
			log.error("fail get alarm info: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "获取告警详情异常：" + e.getMessage());
		}
	}

	@ApiOperation(value = "定位点列表", httpMethod = "POST")
	@PostMapping("/location/list")
	public R<List<AlarmLocationResponse>> getLocationListFromAlarm (@Validated(AlarmDetailGroup.class) @RequestBody AlarmRequest request, BladeUser user) {
		try {
			R<Object> r = this.addDataRangeForRequest(request, user);
			if (!r.isSuccess()) {
				return R.fail(r.getCode(), r.getMsg());
			}

			Query query = new Query();
			query.setCurrent(1);
			query.setSize(1);
//			IPage<Alarm> tmpPage = this.alarmService.getAlarmPage(request, query);
//			if (tmpPage.getTotal() <= 0) {
//				return R.data(new ArrayList<>());
//			}
//			return R.data(this.alarmService.getLocationListFromAlarm(tmpPage.getRecords().get(0)));
			Alarm alarm = this.alarmService.getAlarmById(request.getIdList().get(0));
			if (alarm==null) {
				return R.fail(ResultCode.FAILURE, "对应告警不存在。");
			}
			return R.data(this.alarmService.getLocationListFromAlarm(alarm));
		} catch (Exception e) {
			log.error("fail get location list from alarm: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "获取告警定位点列表异常：" + e.getMessage());
		}
	}

	@ApiOperation(value = "告警附件", httpMethod = "POST")
	@PostMapping("/location/file")
	public R<List<AlarmLocationResponse>> getFileFromAlarm (@Validated(AlarmDetailGroup.class) @RequestBody AlarmRequest request, BladeUser user) {
		try {
			R<Object> r = this.addDataRangeForRequest(request, user);
			if (!r.isSuccess()) {
				return R.fail(r.getCode(), r.getMsg());
			}
			Alarm alarm=new Alarm();
			alarm.setId(request.getIdList().get(0));
			return R.data(this.alarmService.getFileFromAlarm(alarm));
		} catch (Exception e) {
			log.error("fail get location file from alarm: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "获取告警附件列表异常：" + e.getMessage());
		}
	}
	@ApiOperation(value = "处理", httpMethod = "POST")
	@PostMapping("/deal")
	public R<String> dealAlarm (@Validated(DealAlarmGroup.class) @RequestBody AlarmRequest request, BladeUser user) {
		try {
			return this.alarmService.dealAlarm(request, user);
		} catch (Exception e) {
			log.error("fail deal alarm: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "告警处理异常：" + e.getMessage());
		}
	}
}
