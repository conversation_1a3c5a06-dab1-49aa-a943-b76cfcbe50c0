<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xh.vdm.alarm.mapper.AlarmMapper">

    <select id="getAlarmHandleCountByDeptIdAndDuration" resultType="long">
        select (dc.num + bc.num) count from
        (select count(*) num from bdm_security where alarm_level != 0 and dept_id in (${deptIds}) and (is_wrong != 1 or is_wrong is null) and alarm_time >= to_timestamp(#{startTimeStr},'yyyy-MM-dd hh24:mi:ss') and alarm_time &lt; to_timestamp(#{endTimeStr},'yyyy-MM-dd hh24:mi:ss')
        <!-- 服务商 -->
        <if test="roleType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="roleType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="roleType == 3">
            and company_state = 1
        </if>
        ) dc,
        (select count(*) num from bdm_security bs, blade_users_vehicles bud
        where bs.vehicle_id = bud.vehicle_id and bud.user_id = #{userId} and (is_wrong != 1 or is_wrong is null) and alarm_time >= to_timestamp(#{startTimeStr},'yyyy-MM-dd hh24:mi:ss') and alarm_time &lt; to_timestamp(#{endTimeStr},'yyyy-MM-dd hh24:mi:ss')
        <!-- 服务商 -->
        <if test="roleType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="roleType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="roleType == 3">
            and company_state = 1
        </if>

        ) bc
    </select>

    <select id="getAlarmHandleCount" resultType="long">
        select (dc.num + bc.num) count from
        (select count(*) num from bdm_security where dept_id in (${deptIds}) and (is_wrong != 1 or is_wrong is null) and alarm_time >= to_timestamp(#{startTimeStr},'yyyy-MM-dd hh24:mi:ss') and alarm_time &lt; to_timestamp(#{endTimeStr},'yyyy-MM-dd hh24:mi:ss') and alarm_type in (
        <foreach collection="alarmTypes" item="alarmType" separator=",">
            #{alarmType}
        </foreach>
        )
        <!-- 服务商 -->
        <if test="roleType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="roleType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="roleType == 3">
            and company_state = 1
        </if>
        ) dc,
        (select count(*) num from bdm_security bs, blade_users_vehicles bud
        where bs.vehicle_id = bud.vehicle_id and bud.user_id = #{userId} and (is_wrong != 1 or is_wrong is null) and alarm_time >= to_timestamp(#{startTimeStr},'yyyy-MM-dd hh24:mi:ss') and alarm_time &lt; to_timestamp(#{endTimeStr},'yyyy-MM-dd hh24:mi:ss') and alarm_type in (
        <foreach collection="alarmTypes" item="alarmType" separator=",">
            #{alarmType}
        </foreach>
        )
        <!-- 服务商 -->
        <if test="roleType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="roleType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="roleType == 3">
            and company_state = 1
        </if>
        ) bc
    </select>

    <sql id="dataRangeCondition">
        <if test="deptListGroup != null and deptListGroup.size() gt 0 ">
            <foreach collection="deptListGroup" item="deptList" separator=" or " open="and (" close=")">
                dept_id in <foreach collection="deptList" item="dept" separator="," open="(" close=")">#{dept}</foreach>
            </foreach>
        </if>
    </sql>

    <sql id="alarmRequestCondition">
        <if test="request.idList != null and request.idList.size() gt 0">
            and id in
            <foreach collection="request.idList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        <if test="request.deviceType != null">
            and device_type = #{request.deviceType}
        </if>
        <if test="request.deviceId != null">
            and device_id = #{request.deviceId}
        </if>
        <if test="deptIdListGroup != null and deptIdListGroup.size() gt 0 ">
            <foreach collection="deptIdListGroup" item="deptIdList" separator=" or " open="and (" close=")">
                dept_id in <foreach collection="deptIdList" item="deptId" separator="," open="(" close=")">#{deptId}</foreach>
            </foreach>
        </if>
        <if test="request.alarmTypeList != null and request.alarmTypeList.size() gt 0">
            and type in
            <foreach collection="request.alarmTypeList" item="alarmType" separator="," open="(" close=")">
                #{alarmType}
            </foreach>
        </if>
        <if test="request.alarmLevelList != null and request.alarmLevelList.size() gt 0">
            and level in
            <foreach collection="request.alarmLevelList" item="alarmLevel" separator="," open="(" close=")">
                #{alarmLevel}
            </foreach>
        </if>
        <if test="request.alarmSource != null">
            and source = #{request.alarmSource}
        </if>
        <if test="request.startTime != null">
            and start_time &gt;= #{request.startTime}
        </if>
        <if test="request.endTime != null">
            and start_time &lt;= #{request.endTime}
        </if>
        <if test="request.handleState != null">
            and handle_state = #{request.handleState}
        </if>
        <if test="request.handler != null">
            and handler = #{request.handler}
        </if>
    </sql>

    <select id="getNumAlarm" resultType="int">
        select count(1) from alarm where 1 = 1
        <include refid="dataRangeCondition" />
        <include refid="alarmRequestCondition" />
        <if test="deviceIdList != null and deviceIdList.size() gt 0 ">
            and device_id in
            <foreach collection="deviceIdList" item="deviceId" index="index" open="(" close=")" separator=",">
                #{deviceId}
            </foreach>
        </if>
    </select>

    <select id="getAlarmDeviceList" resultType="java.util.Map">
        select device_type, device_id, count(1) as num_alarm from alarm where 1 = 1
        <include refid="dataRangeCondition"/>
        <include refid="alarmRequestCondition" />
        group by device_type, device_id
    </select>

    <select id="getNumAlarmGroupByType" resultType="java.util.Map">
        select type as alarm_type, count(1) as num_alarm from alarm where 1 = 1
        <include refid="dataRangeCondition" />
        <include refid="alarmRequestCondition" />
        group by type
    </select>

    <select id="getAlarmTypeAndCount" resultType="com.xh.vdm.alarm.vo.response.AlarmTypeAndCountVO">
        select type as alarm_type, count(1) as num_alarm from alarm where 1 = 1
        <if test="deviceIdListGroup != null and deviceIdListGroup.size() > 0">
            and (
            <foreach collection="deviceIdListGroup" item="deviceIdList" separator="or" open="(" close=")">
                device_id in
                <foreach collection="deviceIdList" item="deviceId" separator="," open="(" close=")">
                   #{deviceId}
                </foreach>
            </foreach>
            )
        </if>
        <if test="request.deviceType != null">
            and device_type = #{request.deviceType}
        </if>
        <if test="request.deviceId != null">
            and device_id = #{request.deviceId}
        </if>
        <if test="request.alarmTypeList != null and request.alarmTypeList.size() gt 0">
            and type in
            <foreach collection="request.alarmTypeList" item="alarmType" separator="," open="(" close=")">
                #{alarmType}
            </foreach>
        </if>
        <if test="request.alarmLevelList != null and request.alarmLevelList.size() gt 0">
            and level in
            <foreach collection="request.alarmLevelList" item="alarmLevel" separator="," open="(" close=")">
                #{alarmLevel}
            </foreach>
        </if>
        <if test="request.alarmSource != null">
            and source = #{request.alarmSource}
        </if>
        <if test="request.startTime != null">
            and start_time &gt;= #{request.startTime}
        </if>
        <if test="request.endTime != null">
            and start_time &lt;= #{request.endTime}
        </if>
        <if test="request.handleState != null">
            and handle_state = #{request.handleState}
        </if>
        group by type
    </select>

    <select id="getAlarmList" resultType="org.springblade.entity.Alarm">
        select * from alarm where 1 = 1
        <include refid="dataRangeCondition" />
        <include refid="alarmRequestCondition" />
        <if test="deviceIdList != null and deviceIdList.size() gt 0 ">
            and device_id in
            <foreach collection="deviceIdList" item="deviceId" index="index" open="(" close=")" separator=",">
                <if test="index != 0 and index % 999 == 0">
                    #{deviceId}) or device_id in (
                </if>
                #{deviceId}
            </foreach>
        </if>
        order by start_time desc
        limit ${size} offset ${(current - 1) * size}
    </select>

    <select id="getLocationListFromAlarm" resultType="org.springblade.entity.Location">
        select * from locations
        where time &gt;= #{startTime} and time &lt;= #{endTime}
        and target_type = #{targetType} and target_id = #{targetId}
        and device_type = #{deviceType} and device_id = #{deviceId}
    </select>

    <select id="getAlarmListByIds" resultType="org.springblade.entity.Alarm">
        <foreach collection="idsList" separator=" union " item="idList">
            select * from alarm
            where id in
            <foreach collection="idList" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </foreach>
    </select>

    <select id="getAlarmById" resultType="org.springblade.entity.Alarm">
        select * from alarm
        where id=  #{id}
    </select>

</mapper>
