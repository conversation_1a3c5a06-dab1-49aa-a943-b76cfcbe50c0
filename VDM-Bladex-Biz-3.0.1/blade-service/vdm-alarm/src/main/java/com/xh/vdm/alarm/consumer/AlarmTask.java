package com.xh.vdm.alarm.consumer;

import cn.hutool.http.HttpRequest;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class AlarmTask {

	@Value("${go-api.http.service}")
	private String goApiHttpService;

	@Value("${go-api.http.header.account}")
	private String goApiHttpHeaderAccount;

	@Value("${go-api.http.header.tenantId}")
	private String goApiHttpHeaderTenantId;

	@Value("${go-api.http.url.send_msg}")
	private String goApiHttpUrlSendMsg;

	@Resource
	private NacosServiceManager nacosServiceManager;

	@Resource
	private NacosDiscoveryProperties nacosDiscoveryProperties;

	// 告警处理时的指令下发终端
	@KafkaListener(
		containerFactory = "terminalCmdDeliverKafkaListenerContainerFactory",
		batch = "true",
		topics = {"GuoNeng_terminal_cmd_deliver"}
	)
	public void sendCmdToTerminal (List<ConsumerRecord<String, String>> consumerRecords, Acknowledgment acknowledgment) {
		try {
			if (CollectionUtils.isEmpty(consumerRecords)) {
				return;
			}

			NamingService namingService = this.nacosServiceManager.getNamingService(this.nacosDiscoveryProperties.getNacosProperties());
			Instance instance = namingService.selectOneHealthyInstance(this.goApiHttpService, this.nacosDiscoveryProperties.getGroup());
			if (instance == null) {
				log.error("fail get host for go-regulatorycenter from nacos when send cmd to terminal");
				return;
			}

			String host = instance.getIp() + ":" + instance.getPort();
			String url = host + this.goApiHttpUrlSendMsg;
			Map<String, String> header = new HashMap<>();
			header.put("account", this.goApiHttpHeaderAccount);
			header.put("tenantId", this.goApiHttpHeaderTenantId.split("_")[1]);
			String reqBodyStr;
			String respBodyStr;
			JSONObject responseBody;
			for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
				reqBodyStr = consumerRecord.value();
				log.info("start request for send cmd to terminal, url: {}, request body: {}", url, reqBodyStr);
				respBodyStr = HttpRequest.post(url).headerMap(header, false).body(reqBodyStr).execute().body();
				log.info("request done for send cmd to terminal, url: {}, request body: {}, response body: {}", url, reqBodyStr, respBodyStr);
				responseBody = JSON.parseObject(respBodyStr);
				if ((responseBody == null) || (responseBody.get("code") == null) || (responseBody.get("msg") == null)) {
					log.error("invalid response when send cmd to terminal, url: {}, request body: {}, response body: {}",
						url, reqBodyStr, respBodyStr);
				} else if (Integer.parseInt(responseBody.get("code").toString()) != 0) {
					log.error("fault response when send cmd to terminal, url: {}, request body: {}, response body: {}, msg: {}",
						url, reqBodyStr, respBodyStr, responseBody.get("msg"));
				}
			}

			// 有点类似InnoDB事务的提交，如果消费了消息，但消息处理过程出现异常而退出，则消息可被回滚到列队中。只有执行到这步，相当于提交，消息队列才正式认为该消息已被消费。
			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("fail send cmd to terminal: {}", e.getMessage(), e);
		}
	}
}
