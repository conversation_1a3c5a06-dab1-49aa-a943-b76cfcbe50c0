package com.xh.vdm.alarm.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.alarm.entity.DealAlarmOption;

import java.util.Collection;
import java.util.List;

public interface IDealAlarmOptionService extends IService<DealAlarmOption> {

	List<DealAlarmOption> getOptionByDept (long deptId);

	DealAlarmOption getOptionByTypeLevel (long deptId, short alarmType, short alarmLevel);

	void setDealRule (List<DealAlarmOption> optionList);

	void removeDealRule (Collection<Long> deptList);
}
