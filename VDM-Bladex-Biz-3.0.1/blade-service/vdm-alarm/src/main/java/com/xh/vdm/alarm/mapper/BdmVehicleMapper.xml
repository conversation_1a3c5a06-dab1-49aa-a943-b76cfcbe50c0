<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.alarm.mapper.BdmVehicleMapper">

    <select id="selectTargetByDeptId" resultType="com.xh.vdm.alarm.vo.response.TargetResponse">
        select CONCAT(target_type, '_', id) AS id, number as name, id as targetId, target_type from bdm_vehicle_new where deleted = 0 and dept_id = #{deptId}
    </select>
</mapper>
