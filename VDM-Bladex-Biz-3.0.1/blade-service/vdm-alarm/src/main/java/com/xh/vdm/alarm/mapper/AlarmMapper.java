package com.xh.vdm.alarm.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.alarm.vo.request.AlarmRequest;
import com.xh.vdm.alarm.vo.response.AlarmTypeAndCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.entity.Alarm;
import org.springblade.entity.Location;

import java.util.List;
import java.util.Map;

@Mapper
@DS("impala")
public interface AlarmMapper extends BaseMapper<Alarm> {

	/**
	 * @description: 根据部门id和时间段获取报警处理数量（本节点及子节点）
	 * 不包含误报
	 * @author: zhouxw
	 * @date: 2023-06-164 21:05:12
	 * @param: [deptIds 本节点及子节点的deptId; roleType 1 服务商  2 第三方  3 企业]
	 * @return: long
	 **/
	long getAlarmHandleCountByDeptIdAndDuration(@Param("deptIds") String deptIds, @Param("userId") long userId, @Param("startTimeStr") String startTimeStr, @Param("endTimeStr") String endTimeStr, @Param("roleType") String roleType);

	/**
	 * @description: 查询报警处理数量（本节点及子节点）
	 * 不包含误报
	 * @author: zhouxw
	 * @date: 2023-06-164 21:05:12
	 * @param: [deptIds 本节点及子节点的deptId； alarmTypes 报警类型，多个类型中间使用英文逗号分隔；startTime 开始时间，精确到秒的时间戳，时间范围包含开始时间； endTime 结束时间，精确到秒的时间戳，时间范围不包含结束时间；roleType 角色类型，服务商、第三方、企业]
	 * @return: long
	 **/
	long getAlarmHandleCount(@Param("deptIds") String deptIds, @Param("userId") long userId, @Param("startTimeStr") String startTimeStr, @Param("endTimeStr") String endTimeStr, @Param("alarmTypes") List<Integer> alarmTypes, @Param("roleType") String roleType);

	// 告警数
	int getNumAlarm (
		@Param("request") AlarmRequest request,
		@Param("deviceIdList") List<Long> deviceIdList,
		@Param("deptListGroup") List<List<Long>> deptListGroup,
		@Param("deptIdListGroup") List<List<Long>> deptIdListGroup
	);

	// 告警设备列表
	List<Map<String, Object>> getAlarmDeviceList (
		@Param("request") AlarmRequest request,
		@Param("deptListGroup") List<List<Long>> deptListGroup,
		@Param("deptIdListGroup") List<List<Long>> deptIdListGroup
	);

	// 按告警类型分组的告警数
	List<Map<String, Object>> getNumAlarmGroupByType (
		@Param("request") AlarmRequest request,
		@Param("deptListGroup") List<List<Long>> deptListGroup,
		@Param("deptIdListGroup") List<List<Long>> deptIdListGroup
	);

	/**
	 * 查询指定了序列号列表的告警数量
	 * @param request
	 * @param deviceIdListGroup
	 * @return
	 */
	List<AlarmTypeAndCountVO> getAlarmTypeAndCount(@Param("request") AlarmRequest request, @Param("deviceIdListGroup") List<List<Long>> deviceIdListGroup);


	// 告警列表
	List<Alarm> getAlarmList (
		@Param("request") AlarmRequest request,
		@Param("deviceIdList") List<Long> deviceIdList,
		@Param("deptListGroup") List<List<Long>> deptListGroup,
		@Param("deptIdListGroup") List<List<Long>> deptIdListGroup,
		@Param("current") long current,
		@Param("size") long size
	);

	// 告警定位点列表
	List<Location> getLocationListFromAlarm (
		@Param("startTime") long startTime,
		@Param("endTime") long endTime,
		@Param("targetType") byte targetType,
		@Param("targetId") long targetId,
		@Param("deviceType") byte deviceType,
		@Param("deviceId") long deviceId
	);

	/**
	 * 根据id列表查询告警信息
	 * @param idsList id的分组，每组最多10000个告警id
	 * @return
	 */
	List<Alarm> getAlarmListByIds(@Param("idsList") List<List<Long>> idsList);

	Alarm getAlarmById(@Param("id") Long ids);
}
