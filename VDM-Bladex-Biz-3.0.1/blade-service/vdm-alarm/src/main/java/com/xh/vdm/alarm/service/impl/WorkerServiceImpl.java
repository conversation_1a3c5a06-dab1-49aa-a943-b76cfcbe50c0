package com.xh.vdm.alarm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.alarm.entity.BdmWorker;
import com.xh.vdm.alarm.mapper.WorkerMapper;
import com.xh.vdm.alarm.service.WorkerService;
import com.xh.vdm.alarm.vo.response.TargetResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * (BdmWorker)表服务实现类
 */
@Service
@Slf4j
public class WorkerServiceImpl extends ServiceImpl<WorkerMapper, BdmWorker> implements WorkerService {

	@Resource
	private WorkerMapper workerMapper;

	@Override
	public List<TargetResponse> selectTargetByDeptId(Long deptId) {
		return workerMapper.selectTargetByDeptId(deptId);
	}
}
