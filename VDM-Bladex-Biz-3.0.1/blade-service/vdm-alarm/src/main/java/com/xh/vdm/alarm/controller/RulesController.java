package com.xh.vdm.alarm.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xh.vdm.alarm.service.*;
import com.xh.vdm.alarm.vo.response.BladeDeptDeviceResponse;
import com.xh.vdm.alarm.vo.response.TargetResponse;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.system.user.entity.UserDeptRegulates;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Description: 配置中心--规则管理
 */
@RestController
@RequestMapping("/config/rules")
public class RulesController {

	@Resource
	private IUserDeptRegulatesService userDeptRegulatesService;
	@Resource
	private IBladeDeptService bladeDeptService;
	@Resource
	private FacilityService facilityService;
	@Resource
	private IBdmVehicleService iBdmVehicleService;
	@Resource
	private WorkerService workerService;



	@GetMapping("/deptDevice")
	public R<List<BladeDeptDeviceResponse>> deptDevice(BladeUser user){

		List<UserDeptRegulates> userDeptRegulatesList = userDeptRegulatesService.list(new QueryWrapper<UserDeptRegulates>().eq("user_id", user.getUserId()));

		List<BladeDeptDeviceResponse> list = new ArrayList<>();

		if (userDeptRegulatesList.size() > 0){
			for (UserDeptRegulates userDept: userDeptRegulatesList) {
				BladeDeptDeviceResponse bladeDeptDeviceResponse = new BladeDeptDeviceResponse();

				bladeDeptDeviceResponse = bladeDeptService.selectDept(userDept.getDeptId());

				List<TargetResponse> facilityList = facilityService.selectTargetByDeptId(Long.parseLong(user.getDeptId()));
				List<TargetResponse> vehicleList = iBdmVehicleService.selectTargetByDeptId(Long.parseLong(user.getDeptId()));
				List<TargetResponse> workerList = workerService.selectTargetByDeptId(Long.parseLong(user.getDeptId()));
				List<TargetResponse> devices = Stream.of(facilityList, vehicleList, workerList)
					.flatMap(List::stream)
					.collect(Collectors.toList());

				bladeDeptDeviceResponse.setChildren(devices);
				list.add(bladeDeptDeviceResponse);
			}
		}

		return R.data(list);
	}

}
