package com.xh.vdm.alarm.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;

@ApiModel(value = "请求体：通过单位ID获取数据")
@Data
public class ByDeptIdRequest {

	@JsonProperty("dept_id")
	@ApiModelProperty(name = "dept_id", value = "单位ID", example = "1", required = false)
	@DecimalMin(value = "1", message = "单位ID不正确。")
	private Long deptId;
}
