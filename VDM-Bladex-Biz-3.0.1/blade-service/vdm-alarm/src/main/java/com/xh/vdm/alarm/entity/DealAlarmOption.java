package com.xh.vdm.alarm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "bdm_deal_alarm_option", autoResultMap = true)
public class DealAlarmOption implements Serializable {

	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;

	@TableField(value = "dept_id")
	private Long deptId;

	@TableField(value = "alarm_type")
	private String alarmType;

	@TableField(value = "alarm_level")
	private String alarmLevel;

	@TableField(value = "daily_report")
	private Byte dailyReport;

	@TableField(value = "monthly_report")
	private Byte monthlyReport;

	@TableField(value = "server_auto_deal")
	private Byte serverAutoDeal;

	@TableField(value = "server_tts_audio")
	private Byte serverTtsAudio;

	@TableField(value = "server_terminal_screen")
	private Byte serverTerminalScreen;

	@TableField(value = "server_urgent")
	private Byte serverUrgent;

	@TableField(value = "server_content")
	private String serverContent;

	@TableField(value = "third_auto_deal")
	private Byte thirdAutoDeal;

	@TableField(value = "third_tts_audio")
	private Byte thirdTtsAudio;

	@TableField(value = "third_terminal_screen")
	private Byte thirdTerminalScreen;

	@TableField(value = "third_urgent")
	private Byte thirdUrgent;

	@TableField(value = "third_content")
	private String thirdContent;
}
