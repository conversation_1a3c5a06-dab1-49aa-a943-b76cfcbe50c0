package com.xh.vdm.alarm.vo.response;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 目标
 * </p>
 */
@Data
public class TargetResponse implements Serializable {

	private static final long serialVersionUID = 1L;

	private String id;

	/**
	 * 终端号
	 */
	private String name;

	/**
	 * 主键，唯一标识码
	 */
	private Long targetId;

	/**
	 * 终端类别，1-北斗定位终
	 * 端，2-北斗穿戴式终端，3-北斗短
	 * 报文终端，4-北斗监测终端，5-北
	 * 斗授时终端
	 */
	private Integer targetType;

	private String type = "target";
}
