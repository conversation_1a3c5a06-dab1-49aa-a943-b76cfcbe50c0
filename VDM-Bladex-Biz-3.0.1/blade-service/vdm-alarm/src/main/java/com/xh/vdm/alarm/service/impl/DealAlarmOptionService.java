package com.xh.vdm.alarm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.alarm.entity.DealAlarmOption;
import com.xh.vdm.alarm.mapper.DealAlarmOptionMapper;
import com.xh.vdm.alarm.service.IDealAlarmOptionService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

@Service("DealAlarmOptionService")
public class DealAlarmOptionService extends ServiceImpl<DealAlarmOptionMapper, DealAlarmOption> implements IDealAlarmOptionService {

	@Override
	public List<DealAlarmOption> getOptionByDept (long deptId) {
		return this.baseMapper.getOptionByDept(deptId);
	}

	@Override
	public DealAlarmOption getOptionByTypeLevel (long deptId, short alarmType, short alarmLevel) {
		return this.baseMapper.getOptionByTypeLevel(deptId, alarmType, alarmLevel);
	}

	@Override
	public void setDealRule (List<DealAlarmOption> optionList) {
		this.baseMapper.setDealRule(optionList);
	}

	@Override
	public void removeDealRule (Collection<Long> deptList) {
		this.baseMapper.removeDealRule(deptList);
	}
}

