package com.xh.vdm.alarm.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "返回体：告警列表")
@Data
public class AlarmListResponse {

	@JsonProperty("id")
	@ApiModelProperty(name = "id", value = "告警ID", example = "1", required = true)
	private Long id;

	@JsonProperty("target_type")
	@ApiModelProperty(name = "target_type", value = "目标类型（类型值与名称的映射，详见blade_dict_biz表code=bdm_target_type的记录）", example = "1", required = true)
	private Byte targetType;

	@JsonProperty("target_type_name")
	@ApiModelProperty(name = "target_type_name", value = "目标类型名称", example = "人员", required = true)
	private String targetTypeName;

	@JsonProperty("target_id")
	@ApiModelProperty(name = "target_id", value = "目标ID", example = "1", required = true)
	private Long targetId;

	@JsonProperty("target_name")
	@ApiModelProperty(name = "target_name", value = "目标名称", example = "张三", required = true)
	private String targetName;

	@JsonProperty("device_type")
	@ApiModelProperty(name = "device_type", value = "设备类别（类型值与名称的映射，详见blade_dict_biz表code=bdm_device_type的记录）", example = "1", required = true)
	private Byte deviceType;

	@JsonProperty("device_type_name")
	@ApiModelProperty(name = "device_type_name", value = "设备类别名称", example = "北斗定位终端", required = true)
	private String deviceTypeName;

	@JsonProperty("device_cate")
	@ApiModelProperty(name = "device_cate", value = "设备类型（类别值与名称的映射，详见blade_dict_biz表code=bdm_device_type的记录）", example = "101", required = true)
	private Integer deviceCate;

	@JsonProperty("device_cate_name")
	@ApiModelProperty(name = "device_cate_name", value = "设备类型名称", example = "北斗车载终端", required = true)
	private String deviceCateName;

	@JsonProperty("device_id")
	@ApiModelProperty(name = "device_id", value = "设备ID", example = "1", required = true)
	private Long deviceId;

	@JsonProperty("unique_id")
	@ApiModelProperty(name = "unique_id", value = "设备号", example = "24012700094", required = true)
	private String uniqueId;

	@JsonProperty("device_num")
	@ApiModelProperty(name = "device_num", value = "设备赋码号", example = "TNCE012404190004", required = true)
	private String deviceNum;

	@JsonProperty("dept_name")
	@ApiModelProperty(name = "dept_name", value = "单位名称", example = "海格", required = true)
	private String deptName;

	@JsonProperty("alarm_type")
	@ApiModelProperty(name = "alarm_type", value = "告警类型", example = "生理疲劳", required = true)
	private String alarmType;

	@JsonProperty("alarm_level")
	@ApiModelProperty(name = "alarm_level", value = "告警等级", example = "1级", required = true)
	private String alarmLevel;

	@JsonProperty("alarm_source")
	@ApiModelProperty(name = "alarm_source", value = "告警来源", example = "平台", required = true)
	private String alarmSource;

	@JsonProperty("alarm_complete")
	@ApiModelProperty(name = "alarm_complete", value = "告警结束状态（0：未结束，1：已结束）", example = "0", required = true)
	private Byte alarmComplete;

	@JsonProperty("start_addr")
	@ApiModelProperty(name = "start_addr", value = "开始地址", example = "广东省广州市黄埔区联和街道阅阳一街8号大壮国际广场G1栋", required = true)
	private String startAddr;

	@JsonProperty("start_time")
	@ApiModelProperty(name = "start_time", value = "开始时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-06-27 08:00:00", required = true)
	private String startTime;

	@JsonProperty("end_addr")
	@ApiModelProperty(name = "end_addr", value = "结束地址", example = "广东省广州市黄埔区联和街道阅阳一街8号大壮国际广场G1栋", required = true)
	private String endAddr;

	@JsonProperty("end_time")
	@ApiModelProperty(name = "end_time", value = "结束时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-06-27 08:00:00", required = true)
	private String endTime;

	@JsonProperty("num_attach_expect")
	@ApiModelProperty(name = "num_attach_expect", value = "预期附件数", example = "4", required = true)
	private Integer numAttachExpect;

	@JsonProperty("num_attach_real")
	@ApiModelProperty(name = "num_attach_real", value = "实际附件数", example = "1", required = true)
	private Integer numAttachReal;

	@JsonProperty("rule_name")
	@ApiModelProperty(name = "rule_name", value = "告警规则", example = "疲劳规则", required = true)
	private String ruleName;

	@JsonProperty("iot_protocol")
	@ApiModelProperty(name = "iot_protocol", value = "物联网协议（1：JT/T808，2：MQTT，3：SNMP）", example = "1", required = true)
	private Byte iotProtocol;

	@JsonProperty("handle_state")
	@ApiModelProperty(name = "handle_state", value = "处理状态", example = "未处理", required = true)
	private String handleState;

	@JsonProperty("handle_measures")
	@ApiModelProperty(name = "handle_measures", value = "处理措施", example = "语音播报、显示屏展示", required = true)
	private String handleMeasures;

	@JsonProperty("handle_content")
	@ApiModelProperty(name = "handle_content", value = "处理内容", example = "您已疲劳，请停车休息！", required = true)
	private String handleContent;

	@JsonProperty("handler")
	@ApiModelProperty(name = "handler", value = "处理人", example = "李四", required = true)
	private String handler;

	@JsonProperty("handle_time")
	@ApiModelProperty(name = "handle_time", value = "处理时间", example = "处理时间（格式：yyyy-MM-dd HH:mm:ss）", required = true)
	private String handleTime;

	// 仅导出用
	private String numAttach;

	private Double speed;
}
