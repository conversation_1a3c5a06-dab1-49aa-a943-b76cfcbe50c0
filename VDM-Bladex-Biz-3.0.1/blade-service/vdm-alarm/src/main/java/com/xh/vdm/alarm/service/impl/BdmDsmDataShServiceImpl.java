package com.xh.vdm.alarm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.alarm.entity.BdmDsmDataSh;
import com.xh.vdm.alarm.mapper.BdmDsmDataShMapper;
import com.xh.vdm.alarm.service.IBdmDsmDataShService;
import com.xh.vdm.alarm.vo.response.AlarmLocationResponse;
import org.springblade.common.minio.MinioService;
import org.springblade.entity.Alarm;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service("BdmDsmDataShService")
public class BdmDsmDataShServiceImpl extends ServiceImpl<BdmDsmDataShMapper, BdmDsmDataSh> implements IBdmDsmDataShService {
	@Resource
	private MinioService minioService;

	@Override
	public Map<Long, Map<String, Integer>> getNumAttachForAlarmList (List<Alarm> alarmList) {
		Map<Long, Map<String, Integer>> res = new HashMap<>();
		if (CollectionUtils.isEmpty(alarmList)) {
			return res;
		}

		Map<String, Long> attachIdMapAlarmId = new HashMap<>();
		for (Alarm alarm : alarmList) {
			Map<String, Integer> tmp = new HashMap<>();
			tmp.put("num_attach_expect", 0);
			tmp.put("num_attach_real", 0);
			String auxiliary = alarm.getAuxiliary();
			if (StringUtils.isBlank(auxiliary)) {
				continue;
			}

			JSONObject jo = JSON.parseObject(auxiliary);
			if ((jo == null) || (!jo.containsKey("attachments")) || (jo.get("attachments") == null)) {
				continue;
			}

			JSONArray ja = JSON.parseArray(jo.get("attachments").toString());
			if ((ja == null) || ja.isEmpty()) {
				continue;
			}
			for (Object o : ja) {
				JSONObject attach = (JSONObject) o;
				attachIdMapAlarmId.put(attach.getOrDefault("attachment_id", "").toString(), alarm.getId());
				tmp.put(
					"num_attach_expect",
					tmp.get("num_attach_expect") + Integer.parseInt(attach.getOrDefault("attachment_num", 0).toString())
				);
			}

			res.put(alarm.getId(), tmp);
		}

		Set<String> attachIdSet = attachIdMapAlarmId.keySet();
		if (CollectionUtils.isEmpty(attachIdSet)) {
			return res;
		}

		List<BdmDsmDataSh> attachList = this.baseMapper.getAttachFromUniqueIdList(attachIdSet.parallelStream().collect(Collectors.toList()));
		if (CollectionUtils.isEmpty(attachList)) {
			return res;
		}
		for (BdmDsmDataSh attach : attachList) {
			long alarmId = attachIdMapAlarmId.get(attach.getUniqueId());
			int numAttachReal = res.get(alarmId).get("num_attach_real");
			numAttachReal += ((StringUtils.isNotBlank(attach.getJpg1()) && attach.getJpg1().contains("/")) ? 1 : 0);
			numAttachReal += ((StringUtils.isNotBlank(attach.getJpg2()) && attach.getJpg2().contains("/")) ? 1 : 0);
			numAttachReal += ((StringUtils.isNotBlank(attach.getJpg3()) && attach.getJpg3().contains("/")) ? 1 : 0);
			numAttachReal += ((StringUtils.isNotBlank(attach.getMp4()) && attach.getMp4().contains("/")) ? 1 : 0);
			res.get(alarmId).put("num_attach_real", numAttachReal);
		}

		return res;
	}

	@Override
	public List<AlarmLocationResponse> getFileByAlarm (Alarm alarm) {
		List<AlarmLocationResponse> res=new ArrayList<>();
		String auxiliary = alarm.getAuxiliary();
		JSONObject jo = JSON.parseObject(auxiliary);
		if ((jo == null) || (!jo.containsKey("attachments")) || (jo.get("attachments") == null)) {
			return res;
		}
		JSONArray ja = JSON.parseArray(jo.get("attachments").toString());
		if ((ja == null) || ja.isEmpty()) {
			return res;
		}
		Map<String, Long> attachIdMapAlarmId = new HashMap<>();
		for (Object o : ja) {
			JSONObject attach = (JSONObject) o;
			attachIdMapAlarmId.put(attach.getOrDefault("attachment_id", "").toString(), alarm.getId());
		}
		Set<String> attachIdSet = attachIdMapAlarmId.keySet();
		if (CollectionUtils.isEmpty(attachIdSet)) {
			return res;
		}
		List<BdmDsmDataSh> attachList = this.baseMapper.getAttachFromUniqueIdList(attachIdSet.parallelStream().collect(Collectors.toList()));
		if (CollectionUtils.isEmpty(attachList)) {
			return res;
		}
		for (BdmDsmDataSh attach : attachList) {
			AlarmLocationResponse alr=new AlarmLocationResponse();
			if (StringUtils.isNotBlank(attach.getJpg1()) && attach.getJpg1().contains("/")){
				String url=minioService.getPresignedObjectUrl(attach.getJpg1());
				alr.setImg1(url);
			}
			if (StringUtils.isNotBlank(attach.getJpg2()) && attach.getJpg2().contains("/")){
				String url=minioService.getPresignedObjectUrl(attach.getJpg2());
				alr.setImg2(url);
			}
			if (StringUtils.isNotBlank(attach.getJpg3()) && attach.getJpg3().contains("/")){
				String url=minioService.getPresignedObjectUrl(attach.getJpg3());
				alr.setImg3(url);
			}
			if (StringUtils.isNotBlank(attach.getMp4()) && attach.getMp4().contains("/")){
				String url=minioService.getPresignedObjectUrl(attach.getMp4());
				alr.setMp4(url);
			}
			alr.setAppendixId(attach.getUniqueId());
			res.add(alr);
		}
		return res;
	}

}
