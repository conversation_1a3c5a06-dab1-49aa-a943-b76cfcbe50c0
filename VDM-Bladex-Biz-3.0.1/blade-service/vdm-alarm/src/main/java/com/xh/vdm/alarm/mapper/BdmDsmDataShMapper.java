package com.xh.vdm.alarm.mapper;

import com.xh.vdm.alarm.entity.BdmDsmDataSh;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BdmDsmDataShMapper extends BaseMapper<BdmDsmDataSh> {

	// 通过附件ID获取附件
	List<BdmDsmDataSh> getAttachFromUniqueIdList (@Param("attachIdList") List<String> attachIdList);
}
