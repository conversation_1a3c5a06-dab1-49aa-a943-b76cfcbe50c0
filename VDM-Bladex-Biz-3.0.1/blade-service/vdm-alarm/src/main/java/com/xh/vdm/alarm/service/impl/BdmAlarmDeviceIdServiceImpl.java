package com.xh.vdm.alarm.service.impl;

import com.xh.vdm.alarm.entity.BdmAlarmDeviceId;
import com.xh.vdm.alarm.mapper.BdmAlarmDeviceIdMapper;
import com.xh.vdm.alarm.service.IBdmAlarmDeviceIdService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 告警终端id表，用于保存每天每个部门发生告警的终端id列表。可用于快速统计发生告警的终端 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-07
 */
@Service
public class BdmAlarmDeviceIdServiceImpl extends ServiceImpl<BdmAlarmDeviceIdMapper, BdmAlarmDeviceId> implements IBdmAlarmDeviceIdService {

}
