package com.xh.vdm.alarm.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;

@Configuration
public class TerminalCmdDeliverKafkaConfig {

	// 改：prefix的值、Bean的名称、方法名
	@ConfigurationProperties(prefix = "spring.terminal-cmd-deliver-kafka")
	@Bean("terminalCmdDeliverKafkaProperties")
	@Primary
	public KafkaProperties terminalCmdDeliverKafkaProperties() {
		return new KafkaProperties();
	}

	// 改：Bean的名称、Qualifier的名称
	@Bean("terminalCmdDeliverKafkaListenerContainerFactory")
	public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>>
	firstKafkaListenerContainerFactory(@Autowired @Qualifier("terminalCmdDeliverKafkaProperties") KafkaProperties firstKafkaProperties) {
		ConcurrentKafkaListenerContainerFactory<Integer, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
		factory.setConsumerFactory(this.firstConsumerFactory(firstKafkaProperties));
		factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
		factory.getContainerProperties().setMissingTopicsFatal(false);
		factory.getContainerProperties().setGroupId(firstKafkaProperties.getConsumer().getGroupId());
		return factory;
	}

	private ConsumerFactory<? super Integer, ? super String> firstConsumerFactory (KafkaProperties firstKafkaProperties) {
		return new DefaultKafkaConsumerFactory<>(firstKafkaProperties.buildConsumerProperties());
	}
}
