package com.xh.vdm.alarm.runner;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.alarm.dto.DealAlarmOptionCache;
import com.xh.vdm.alarm.entity.DealAlarmOption;
import com.xh.vdm.alarm.service.IDealAlarmOptionService;
import org.springframework.beans.BeanUtils;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@Component
public class DealAlarmOptionRunner implements CommandLineRunner {

	@Resource(name = "DealAlarmOptionService")
	private IDealAlarmOptionService dealAlarmOptionService;

	@Override
	public void run (String... args) {
		List<DealAlarmOption> optionList = this.dealAlarmOptionService.list();
		DealAlarmOptionCache.dealAlarmOptionMap = new HashMap<>();
		if (CollectionUtils.isEmpty(optionList)) {
			return;
		}
		for (DealAlarmOption option : optionList) {
			if ((option.getDeptId() == null) || StringUtils.isBlank(option.getAlarmType()) || StringUtils.isBlank(option.getAlarmLevel())) {
				continue;
			}

			DealAlarmOptionCache cache = new DealAlarmOptionCache();
			BeanUtils.copyProperties(option, cache);
			DealAlarmOptionCache.dealAlarmOptionMap.put(option.getDeptId() + "-" + option.getAlarmType() + "-" + option.getAlarmLevel(), cache);
		}
	}


	//todo 每次启动时，需要查询大数据中近15天发生告警的终端，添加到redis中。
}
