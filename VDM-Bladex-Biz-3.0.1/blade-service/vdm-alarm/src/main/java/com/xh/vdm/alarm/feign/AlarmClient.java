package com.xh.vdm.alarm.feign;

import com.xh.vdm.alarm.constant.AlarmConstant;
import com.xh.vdm.alarm.entity.AlarmInfo;
import com.xh.vdm.alarm.service.IAlarmService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.system.vo.DictTreeNodeVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * @Description: 报警feign服务端
 * @Author: zhouxw
 * @Date: 2023/6/13 20:50
 */
@RestController
@Slf4j
public class AlarmClient implements IAlarmClient{

	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(20, 50, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

	@Resource
	private IAlarmService alarmService;

	private AtomicLong totalAlarmCount;

	/**
	 * @description: 查询今日报警总数
	 * @author: zhouxw
	 * @date: 2023-06-164 20:51:56
	 * @param: [deptId]
	 * @return: org.springblade.core.tool.api.R<java.lang.Long>
	 **/
	@GetMapping(TODAY_ALARM_COUNT)
	@Override
	public R<Long> getTodayAlarmCount(@RequestParam("deptId") Long deptId, @RequestParam("userId") Long userId) {
		try{
			long count = alarmService.findTodayTotalCountByDeptId(deptId, userId);
			return R.data(count);
		}catch (Exception e){
			log.error("统计今日报警数量失败",e);
			return R.fail("统计今日报警数量失败");
		}
	}


	/**
	 * @description: 查询今日报警处理总数
	 * @author: zhouxw
	 * @date: 2023-06-164 20:51:56
	 * @param: [deptId]
	 * @return: org.springblade.core.tool.api.R<java.lang.Long>
	 **/
	@GetMapping(TODAY_ALARM_HANDLE_COUNT)
	@Override
	public R<Long> getTodayAlarmHandleCount(@RequestParam("deptId") Long deptId, @RequestParam("userId") Long userId, @RequestParam("roleType") String roleType) {
		try{
			long count = alarmService.findTodayTotalHandleCountByDeptId(deptId, userId, roleType);
			return R.data(count);
		}catch (Exception e){
			log.error("统计今日报警数量失败",e);
			return R.fail("统计今日报警数量失败");
		}
	}




	/**
	 * @description: 统计今日实时报警数
	 * @author: zhouxw
	 * @date: 2023-06-165 10:38:39
	 * @param: [deptId, userId]
	 * @return: org.springblade.core.tool.api.R<java.lang.Long>
	 **/
	@GetMapping(TODAY_REALTIME_ALARM_COUNT)
	public R<Long> getRealTimeAlarmCount(@RequestParam("deptId") Long deptId, @RequestParam("userId") Long userId){
		try{
			long count = alarmService.findTodayRealTimeAlarmTotalCount(deptId, userId);
			return R.data(count);
		}catch (Exception e){
			log.error("统计实时报警数量失败",e);
			return R.fail("统计实时报警数量失败");
		}
	}

	/**
	 * @description: 统计实时报警情况
	 * @author: zhouxw
	 * @date: 2023-06-165 20:40:56
	 * @param: [deptId, userId]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.alarm.entity.AlarmInfo>
	 **/
	@Override
	@GetMapping(TODAY_REALTIME_ALARM_INFO)
	public R<AlarmInfo> getAlarmInfo(Long deptId, Long userId) {
		String tenantId = AuthUtil.getTenantId();
		//*****************************报警信息************************************************
		//查询报警处理数
		AlarmInfo info = new AlarmInfo();
		AtomicLong totalCount = new AtomicLong();
		CountDownLatch countDownLatch = new CountDownLatch(4);
		//1.今日报警总数
		threadPool.submit(() -> {
			long start1 = System.currentTimeMillis();
			try {
				totalCount.set(alarmService.findTodayTotalCountByDeptId(deptId, userId));
			}catch (Exception e){
				log.error("查询今日报警总数失败",e);
			}
			long end1 = System.currentTimeMillis();
			log.info("time 1 : "+ (end1 - start1));
			countDownLatch.countDown();;
		});

		//2.今日实时报警总数
		threadPool.submit(() -> {
			long start2 = System.currentTimeMillis();
			long realTimeCount = 0;
			try {
				realTimeCount = alarmService.findTodayRealTimeAlarmTotalCount(deptId, userId);
			}catch (Exception e){
				log.error("查询今日实时报警数失败",e);
			}
			info.setRealTimeAlarmTotalCount(realTimeCount);
			long end2 = System.currentTimeMillis();
			log.info("time2: "+(end2 - start2));
			countDownLatch.countDown();
		});

		//3.超速实时报警
		threadPool.submit(() -> {
			long start3 = System.currentTimeMillis();
			long overSpeedRealTimeCount = 0;
			try{

				//获取超速报警类型
				Set<DictTreeNodeVO> set = alarmService.getChildrenFlatAlarmTypes(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED+"");
				List<Integer> list = new ArrayList<>();
				for(DictTreeNodeVO vo : set){
					if(vo != null && vo.getDictKey() != null){
						list.add(Integer.parseInt(vo.getDictKey()));
					}
				}
				//添加超速父类型
				list.add((int)AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED);
				overSpeedRealTimeCount = alarmService.findTodayRealTimeAlarmCount(deptId, userId, list);
			}catch (Exception e){
				log.error("查询超速实时报警数失败",e);
			}
			log.info("超速实时报警数 is" + overSpeedRealTimeCount);
			info.setRealTimeOverSpeedCount(overSpeedRealTimeCount);
			long end3 = System.currentTimeMillis();
			log.info("time3 : "+(end3 - start3));
			countDownLatch.countDown();
		});

		//4.疲劳实时报警
		long start4 = System.currentTimeMillis();
		long tiredRealTimeCount = 0;
		try{
			//获取疲劳报警类型
			List<Integer> list = new ArrayList<>();
			//疲劳驾驶报警（终端）
			list.add((int)AlarmConstant.DICT_ALARM_TYPE_TIRED_TERMINAL);
			//疲劳驾驶报警（平台）
			list.add((int)AlarmConstant.DICT_ALARM_TYPE_TIRED_PLATFORM);
			tiredRealTimeCount = alarmService.findTodayRealTimeAlarmCount(deptId, userId, list);
		}catch (Exception e){
			log.error("查询疲劳实时报警数失败",e);
		}
		log.info("疲劳实时报警数 is" + tiredRealTimeCount);
		info.setRealTimeTiredCount(tiredRealTimeCount);
		long end4 = System.currentTimeMillis();
		log.info("time4: " + (end4 - start4));

		//5.夜间行驶实时报警
		long start5 = System.currentTimeMillis();
		long nightRealTimeCount = 0;
		try{
			//获取夜间行驶报警类型
			List<Integer> list = new ArrayList<>();
			//夜间行驶报警
			list.add((int)AlarmConstant.DICT_ALARM_TYPE_NIGHT);
			nightRealTimeCount = alarmService.findTodayRealTimeAlarmCount(deptId, userId, list);
		}catch (Exception e){
			log.error("查询夜间行驶实时报警数失败",e);
		}
		log.info("夜间行驶实时报警数 is" + nightRealTimeCount);
		info.setRealTimeNightCount(nightRealTimeCount);
		long end5 = System.currentTimeMillis();
		log.info("time5 : "+ (end5 - start5));


		//6.主动安全实时报警
		AtomicLong activeRealTimeCount = new AtomicLong();
		threadPool.submit(() -> {
			long start6 = System.currentTimeMillis();
			try{
				//获取主动安全报警类型：高级驾驶辅助系统类、驾驶员状态监测类、激烈驾驶类
				//高级驾驶辅助系统类
				List<Integer> listADAS = new ArrayList<>();
				Set<DictTreeNodeVO> setADAS = alarmService.getChildrenFlatAlarmTypesByTenantId(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_ADAS+"", tenantId);
				for(DictTreeNodeVO vo : setADAS){
					if(vo != null && vo.getDictKey() != null){
						listADAS.add(Integer.parseInt(vo.getDictKey()));
					}
				}
				listADAS.add((int)AlarmConstant.DICT_ALARM_TYPE_ADAS);
				long adasRealTimeCount = alarmService.findTodayRealTimeAlarmCount(deptId, userId, listADAS);

				//驾驶员状态监测类
				List<Integer> listDSM = new ArrayList<>();
				Set<DictTreeNodeVO> setDSM = alarmService.getChildrenFlatAlarmTypesByTenantId(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_DSM+"", tenantId);
				for(DictTreeNodeVO vo : setDSM){
					if(vo != null && vo.getDictKey() != null){
						listDSM.add(Integer.parseInt(vo.getDictKey()));
					}
				}
				listDSM.add((int)AlarmConstant.DICT_ALARM_TYPE_DSM);
				long dsmRealTimeCount = alarmService.findTodayRealTimeAlarmCount(deptId, userId, listDSM);

				//激烈驾驶类
				List<Integer> listIntenseDriving = new ArrayList<>();
				Set<DictTreeNodeVO> setIntenseDriving = alarmService.getChildrenFlatAlarmTypesByTenantId(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_INTENSE_DRIVING+"", tenantId);
				for(DictTreeNodeVO vo : setIntenseDriving){
					if(vo != null && vo.getDictKey() != null){
						listIntenseDriving.add(Integer.parseInt(vo.getDictKey()));
					}
				}
				listIntenseDriving.add((int)AlarmConstant.DICT_ALARM_TYPE_INTENSE_DRIVING);
				long intenseDrivingRealTimeCount = alarmService.findTodayRealTimeAlarmCount(deptId, userId, listIntenseDriving);
				long end6 = System.currentTimeMillis();
				log.info("time6: "+(end6 - start6));


				//主动安全实时报警数量
				activeRealTimeCount.set(adasRealTimeCount + dsmRealTimeCount + intenseDrivingRealTimeCount);
			}catch (Exception e){
				log.error("查询主动安全实时报警数失败",e);
			}
			log.info("主动安全实时报警数 is" + activeRealTimeCount);
			countDownLatch.countDown();
		});

		try {
			countDownLatch.await();
		} catch (Exception e) {
			log.info("异步统计报警信息，等待解锁失敗",e);
		}
		info.setTodayTotalAlarmCount(totalCount.get());
		info.setRealTimeAcitveCount(activeRealTimeCount.get());
		return R.data(info);

	}


	/**
	 * @description: 统计今日累计报警情况
	 * @author: zhouxw
	 * @date: 2023-06-165 20:40:56
	 * @param: [deptId, userId]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.alarm.entity.AlarmInfo>
	 **/
	@Override
	@GetMapping(TODAY_ALARM_INFO)
	public R<AlarmInfo> getTodayAlarmInfo(Long deptId, Long userId) {

		String tenantId = AuthUtil.getTenantId();

		//*****************************报警信息************************************************
		//查询报警处理数
		AlarmInfo info = new AlarmInfo();
		final long[] totalCount = {0};
		long start1 = System.currentTimeMillis();
		CountDownLatch countDownLatch = new CountDownLatch(8);
		//1.今日报警总数
		threadPool.submit(() -> {
			try {
				totalCount[0] = alarmService.findTodayTotalCountByDeptId(deptId, userId);
			}catch (Exception e){
				log.error("查询今日报警总数失败",e);
			}finally {
				countDownLatch.countDown();
			}
			info.setTodayTotalAlarmCount(totalCount[0]);
			long end1 = System.currentTimeMillis();
			log.info("-=-=time1 : "+(end1 - start1));
		});

		//2.今日实时报警总数
		threadPool.submit(() -> {
			long start2 = System.currentTimeMillis();
			long realTimeCount = 0;
			try {
				realTimeCount = alarmService.findTodayRealTimeAlarmTotalCount(deptId, userId);
			}catch (Exception e){
				log.error("查询今日实时报警数失败",e);
			}finally {
				countDownLatch.countDown();
			}
			info.setRealTimeAlarmTotalCount(realTimeCount);
			long end2 = System.currentTimeMillis();
			log.info("-=-=time2: "+(end2 - start2));
		});

		//3.超速实时报警
		threadPool.submit(() -> {
			long start3 = System.currentTimeMillis();
			long overSpeedRealTimeCount = 0;
			try{

				//获取超速报警类型
				Set<DictTreeNodeVO> set = alarmService.getChildrenFlatAlarmTypesByTenantId(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED+"", tenantId);
				List<Integer> list = new ArrayList<>();
				for(DictTreeNodeVO vo : set){
					list.add(Integer.parseInt(vo.getDictKey()));
				}
				//添加超速父类型
				list.add((int)AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED);
				overSpeedRealTimeCount = alarmService.findTodayRealTimeAlarmCount(deptId, userId, list);
			}catch (Exception e){
				log.error("查询超速报警数失败",e);
			}finally {
				countDownLatch.countDown();
			}
			log.info("超速报警数 is" + overSpeedRealTimeCount);
			info.setRealTimeOverSpeedCount(overSpeedRealTimeCount);
			long end3 = System.currentTimeMillis();
			log.info("-=-=time3: "+(end3 - start3));
		});


		//4.疲劳实时报警
		threadPool.submit(() -> {
			long start4 = System.currentTimeMillis();
			long tiredRealTimeCount = 0;
			try{
				//获取疲劳报警类型
				List<Integer> list = new ArrayList<>();
				//疲劳驾驶报警（终端）
				list.add((int)AlarmConstant.DICT_ALARM_TYPE_TIRED_TERMINAL);
				//疲劳驾驶报警（平台）
				list.add((int)AlarmConstant.DICT_ALARM_TYPE_TIRED_PLATFORM);
				tiredRealTimeCount = alarmService.findTodayRealTimeAlarmCount(deptId, userId, list);
			}catch (Exception e){
				log.error("查询疲劳报警数失败",e);
			}finally{
				countDownLatch.countDown();
			}
			log.info("疲劳报警数 is" + tiredRealTimeCount);
			info.setRealTimeTiredCount(tiredRealTimeCount);
			long end4 = System.currentTimeMillis();
			log.info("-=-=time4: "+(end4 - start4));
		});

		//5.夜间行驶报警
		threadPool.submit(() -> {
			long start5 = System.currentTimeMillis();
			long nightRealTimeCount = 0;
			try{
				//获取夜间行驶报警类型
				List<Integer> list = new ArrayList<>();
				//夜间行驶报警
				list.add((int)AlarmConstant.DICT_ALARM_TYPE_NIGHT);
				nightRealTimeCount = alarmService.findTodayRealTimeAlarmCount(deptId, userId, list);
			}catch (Exception e){
				log.error("查询夜间行驶报警数失败",e);
			}finally{
				countDownLatch.countDown();
			}
			log.info("夜间行驶报警数 is" + nightRealTimeCount);
			info.setRealTimeNightCount(nightRealTimeCount);
			long end5 = System.currentTimeMillis();
			log.info("-=-=time5: "+(end5 - start5));
		});

		//6.主动安全实时报警
		long activeRealTimeCount = 0;
			//获取主动安全报警类型：高级驾驶辅助系统类、驾驶员状态监测类、激烈驾驶类
			//高级驾驶辅助系统类
			AtomicLong adasRealTimeCount = new AtomicLong();
			threadPool.submit(() -> {
				long start6 = System.currentTimeMillis();
				List<Integer> listADAS = new ArrayList<>();
				Set<DictTreeNodeVO> setADAS = alarmService.getChildrenFlatAlarmTypesByTenantId(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_ADAS+"", tenantId);
				for(DictTreeNodeVO vo : setADAS){
					listADAS.add(Integer.parseInt(vo.getDictKey()));
				}
				listADAS.add((int)AlarmConstant.DICT_ALARM_TYPE_ADAS);
				try {
					adasRealTimeCount.set(alarmService.findTodayRealTimeAlarmCount(deptId, userId, listADAS));
				} catch (Exception e) {
					log.info("查询adas实时报警失败",e);
				}finally {
					countDownLatch.countDown();
				}
				long end6 = System.currentTimeMillis();
				log.info("-=-=time6: "+(end6 - start6));
			});

			//驾驶员状态监测类
			AtomicLong dsmRealTimeCount = new AtomicLong();
			threadPool.submit(()->{
				long start7 = System.currentTimeMillis();
				List<Integer> listDSM = new ArrayList<>();
				Set<DictTreeNodeVO> setDSM = alarmService.getChildrenFlatAlarmTypesByTenantId(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_DSM+"", tenantId);
				for(DictTreeNodeVO vo : setDSM){
					listDSM.add(Integer.parseInt(vo.getDictKey()));
				}
				listDSM.add((int)AlarmConstant.DICT_ALARM_TYPE_ADAS);
				try {
					dsmRealTimeCount.set(alarmService.findTodayRealTimeAlarmCount(deptId, userId, listDSM));
				} catch (Exception e) {
					log.error("查询dsm报错失败",e);
				}finally {
					countDownLatch.countDown();
				}
				long end7 = System.currentTimeMillis();
				log.info("-=-=time7: "+(end7 - start7));
			});


			//激烈驾驶类
			AtomicLong intenseDrivingRealTimeCount = new AtomicLong();
			threadPool.submit(()->{
				long start8 = System.currentTimeMillis();
				List<Integer> listIntenseDriving = new ArrayList<>();
				Set<DictTreeNodeVO> setIntenseDriving = alarmService.getChildrenFlatAlarmTypesByTenantId(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_INTENSE_DRIVING+"", tenantId);
				for(DictTreeNodeVO vo : setIntenseDriving){
					listIntenseDriving.add(Integer.parseInt(vo.getDictKey()));
				}
				listIntenseDriving.add((int)AlarmConstant.DICT_ALARM_TYPE_ADAS);
				try {
					intenseDrivingRealTimeCount.set(alarmService.findTodayRealTimeAlarmCount(deptId, userId, listIntenseDriving));
				} catch (Exception e) {
					log.error("查询激烈驾驶失败",e);
				}finally {
					countDownLatch.countDown();
				}
				long end8 = System.currentTimeMillis();
				log.info("-=-=time8: "+(end8 - start8));
			});

		try {
			countDownLatch.await();
		} catch (Exception e) {
			log.error("countdownlatch 等待解锁失败",e);
		}

		//主动安全实时报警数量
		activeRealTimeCount = adasRealTimeCount.get() + dsmRealTimeCount.get() + intenseDrivingRealTimeCount.get();
		info.setRealTimeAcitveCount(activeRealTimeCount);
		return R.data(info);

	}




	/**
	 * @description: 统计报警处理数量情况
	 * @author: zhouxw
	 * @date: 2023-06-165 20:40:56
	 * @param: [deptId, userId]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.alarm.entity.AlarmInfo>
	 **/
	@Override
	@GetMapping(TODAY_REALTIME_ALARM_HANDLE_INFO)
	public R<AlarmInfo> getAlarmHandleInfo(Long deptId, Long userId, String roleType) {

		//*****************************报警处理信息************************************************

		//报警处理信息
		AlarmInfo info = new AlarmInfo();

		//1.今日报警处理总数
		long totalCount = 0;
		try {
			totalCount = alarmService.findTodayTotalHandleCountByDeptId(deptId, userId, roleType);
		}catch (Exception e){
			log.error("查询今日报警总数失败",e);
		}
		info.setTodayTotalAlarmCount(totalCount);

		//2.今日实时报警处理总数
		long realTimeCount = 0;
		try {
			realTimeCount = alarmService.findRealTimeAlarmHandleTotalCount(deptId, userId, roleType);
		}catch (Exception e){
			log.error("查询今日实时报警数失败",e);
		}
		info.setRealTimeAlarmTotalCount(realTimeCount);

		//3.超速实时报警
		long overSpeedRealTimeCount = 0;
		try{
			//获取超速报警类型
			Set<DictTreeNodeVO> set = alarmService.getChildrenFlatAlarmTypes(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED+"");
			List<Integer> list = new ArrayList<>();
			for(DictTreeNodeVO vo : set){
				list.add(Integer.parseInt(vo.getDictKey()));
			}
			//添加疲劳驾驶父类型
			list.add((int)AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED);
			overSpeedRealTimeCount = alarmService.findRealTimeAlarmHandleCount(deptId, userId, list, roleType);
		}catch (Exception e){
			log.error("查询超速实时报警数失败",e);
		}
		log.info("超速实时报警数 is" + overSpeedRealTimeCount);
		info.setRealTimeOverSpeedHandleCount(overSpeedRealTimeCount);

		//4.疲劳实时报警
		long tiredRealTimeCount = 0;
		try{
			//获取疲劳报警类型
			List<Integer> list = new ArrayList<>();
			//疲劳驾驶报警（终端）
			list.add((int)AlarmConstant.DICT_ALARM_TYPE_TIRED_TERMINAL);
			//疲劳驾驶报警（平台）
			list.add((int)AlarmConstant.DICT_ALARM_TYPE_TIRED_PLATFORM);
			tiredRealTimeCount = alarmService.findRealTimeAlarmHandleCount(deptId, userId, list, roleType);
		}catch (Exception e){
			log.error("查询疲劳实时报警数失败",e);
		}
		log.info("疲劳实时报警数 is" + tiredRealTimeCount);
		info.setRealTimeTiredHandleCount(tiredRealTimeCount);

		//5.夜间行驶实时报警
		long nightRealTimeCount = 0;
		try{
			//获取夜间行驶报警类型
			List<Integer> list = new ArrayList<>();
			//夜间行驶报警
			list.add((int)AlarmConstant.DICT_ALARM_TYPE_NIGHT);
			nightRealTimeCount = alarmService.findRealTimeAlarmHandleCount(deptId, userId, list, roleType);
		}catch (Exception e){
			log.error("查询夜间行驶实时报警数失败",e);
		}
		log.info("夜间行驶实时报警数 is" + nightRealTimeCount);
		info.setRealTimeNightHandleCount(nightRealTimeCount);


		//6.主动安全实时报警处理情况
		long activeRealTimeCount = 0;
		try{
			//获取主动安全报警类型：高级驾驶辅助系统类、驾驶员状态监测类、激烈驾驶类
			//高级驾驶辅助系统类
			List<Integer> listADAS = new ArrayList<>();
			Set<DictTreeNodeVO> setADAS = alarmService.getChildrenFlatAlarmTypes(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED+"");
			for(DictTreeNodeVO vo : setADAS){
				listADAS.add(Integer.parseInt(vo.getDictKey()));
			}
			listADAS.add((int)AlarmConstant.DICT_ALARM_TYPE_ADAS);
			long adasRealTimeCount = alarmService.findRealTimeAlarmHandleCount(deptId, userId, listADAS, roleType);

			//驾驶员状态监测类
			List<Integer> listDSM = new ArrayList<>();
			Set<DictTreeNodeVO> setDSM = alarmService.getChildrenFlatAlarmTypes(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED+"");
			for(DictTreeNodeVO vo : setDSM){
				listDSM.add(Integer.parseInt(vo.getDictKey()));
			}
			listDSM.add((int)AlarmConstant.DICT_ALARM_TYPE_ADAS);
			long dsmRealTimeCount = alarmService.findRealTimeAlarmHandleCount(deptId, userId, listDSM, roleType);

			//激烈驾驶类
			List<Integer> listIntenseDriving = new ArrayList<>();
			Set<DictTreeNodeVO> setIntenseDriving = alarmService.getChildrenFlatAlarmTypes(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED+"");
			for(DictTreeNodeVO vo : setIntenseDriving){
				listIntenseDriving.add(Integer.parseInt(vo.getDictKey()));
			}
			listIntenseDriving.add((int)AlarmConstant.DICT_ALARM_TYPE_ADAS);
			long intenseDrivingRealTimeCount = alarmService.findRealTimeAlarmHandleCount(deptId, userId, listIntenseDriving, roleType);

			//主动安全实时报警数量
			activeRealTimeCount = adasRealTimeCount + dsmRealTimeCount + intenseDrivingRealTimeCount;

			info.setRealTimeAcitveHandleCount(activeRealTimeCount);
		}catch (Exception e){
			log.error("查询主动安全实时报警处理数失败",e);
		}
		log.info("主动安全实时报警处理数 is" + activeRealTimeCount);
		return R.data(info);

	}


}
