package com.xh.vdm.alarm.vo.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.alarm.constant.AlarmHandleState;
import com.xh.vdm.alarm.vo.request.group.AlarmDetailGroup;
import com.xh.vdm.alarm.vo.request.group.AlarmListGroup;
import com.xh.vdm.alarm.vo.request.group.DealAlarmGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springblade.common.annotation.Enum;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(value = "请求体：告警")
@Data
public class AlarmRequest {

	@JsonProperty("ids")
	@ApiModelProperty(name = "ids", value = "告警ID列表", example = "[1, ...]", required = true)
	@NotEmpty(message = "告警ID列表为空。", groups = {AlarmDetailGroup.class, DealAlarmGroup.class})
	private List<Long> idList;

	@JsonProperty("device_type")
	@ApiModelProperty(name = "device_type", value = "设备类别（类型值与名称的映射，详见blade_dict_biz表code=bdm_device_type的记录）", example = "1", required = true)
	@DecimalMin(value = "1", message = "设备类别不正确。", groups = {AlarmListGroup.class})
	private Byte deviceType;

	//终端类型
	@JsonProperty("device_cate")
	private String deviceCate;

	@JsonProperty("device_id")
	@ApiModelProperty(name = "device_id", value = "设备ID", example = "1", required = true)
	@DecimalMin(value = "1", message = "设备ID不正确。", groups = {AlarmListGroup.class})
	private Long deviceId;

	//序列号
	@JsonProperty("unique_id")
	private String uniqueId;

	//赋码号
	@JsonProperty("device_num")
	private String deviceNum;

	@JsonProperty("dept_id")
	@ApiModelProperty(name = "dept_id", value = "单位ID", example = "", required = true)
	private Long deptId;

	@JsonProperty("dept_id_list")
	@ApiModelProperty(name = "dept_id_list", value = "单位ID列表", example = "[1, ...]", required = true)
	private List<Long> deptIdList;

	@JsonProperty("special_alarm_type")
	@ApiModelProperty(name = "special_alarm_type", value = "是否特殊报警类型（0：否，非0：是）", example = "0", required = true)
	private Byte specialAlarmType;

	@JsonProperty("alarm_type_list")
	@ApiModelProperty(name = "alarm_type_list", value = "告警类型列表（每个元素的类型值与名称的映射，详见blade_dict_biz表code=alarm_type的记录）", example = "[1, ...]", required = true)
	private List<Short> alarmTypeList;

	@JsonProperty("alarm_level_list")
	@ApiModelProperty(name = "alarm_level_list", value = "告警等级列表（每个元素的等级值与名称的映射，详见blade_dict_biz表code=alarm_level的记录）", example = "[1, ...]", required = true)
	private List<Byte> alarmLevelList;

	@JsonProperty("alarm_source")
	@ApiModelProperty(name = "alarm_source", value = "告警来源（来源值与名称的映射，详见blade_dict_biz表code=alarm_origin的记录）", example = "1", required = true)
	@DecimalMin(value = "0", message = "告警来源不正确。", groups = {AlarmListGroup.class})
	private Byte alarmSource;

	@JsonProperty("start_time")
	@ApiModelProperty(name = "start_time", value = "开始时间", example = "1715234368", required = true)
	@DecimalMin(value = "0", message = "开始时间不正确。", groups = {AlarmListGroup.class})
	private Long startTime;

	@JsonProperty("end_time")
	@ApiModelProperty(name = "end_time", value = "结束时间", example = "1715234368", required = true)
	@DecimalMin(value = "0", message = "结束时间不正确。", groups = {AlarmListGroup.class})
	private Long endTime;

	@JsonProperty("handle_state")
	@ApiModelProperty(name = "handle_state", value = "处理状态（0：未处理，1：已处理，2：误报）", example = "0", required = true)
	@NotNull(message = "处理状态为空。", groups = {DealAlarmGroup.class})
	@Enum(target = AlarmHandleState.class, message = "处理状态不正确。", groups = {AlarmListGroup.class, DealAlarmGroup.class})
	private Byte handleState;

	@JsonProperty("handle_measures_list")
	@ApiModelProperty(name = "handle_measures_list", value = "处理措施列表（每个元素为1个措施，措施值与名称的映射，详见blade_dict_biz表code=handle_measures的记录）", example = "[1, ...]", required = true)
	private List<Byte> handleMeasuresList;

	@JsonProperty("handle_content")
	@ApiModelProperty(name = "handle_content", value = "处理内容", example = "您已疲劳，请停车休息。", required = true)
	private String handleContent;

	@JsonProperty("handler")
	@ApiModelProperty(name = "handler", value = "处理人")
	private String handler;

	// 非前端参数，后端形成，用于确定用户能访问的单位ID列表。
	private List<Long> deptList;

	// 非业务参数，导出时，由前端额外传入。
	@JsonProperty("head_name_list")
	private List<String> headNameList;

	// 非业务参数，导出时，由前端额外传入。
	@JsonProperty("column_name_list")
	private List<String> columnNameList;

	@JsonIgnore
	private boolean isExport = false;
}
