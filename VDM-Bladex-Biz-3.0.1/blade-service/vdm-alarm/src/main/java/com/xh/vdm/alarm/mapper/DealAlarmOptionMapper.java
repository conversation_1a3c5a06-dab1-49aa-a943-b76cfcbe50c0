package com.xh.vdm.alarm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.alarm.entity.DealAlarmOption;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface DealAlarmOptionMapper extends BaseMapper<DealAlarmOption> {

	List<DealAlarmOption> getOptionByDept (@Param("deptId") long deptId);

	DealAlarmOption getOptionByTypeLevel (@Param("deptId") long deptId, @Param("alarmType") short alarmType, @Param("alarmLevel") short alarmLevel);

	void setDealRule (@Param("optionList") List<DealAlarmOption> optionList);

	void removeDealRule (@Param("deptList") Collection<Long> deptList);
}
