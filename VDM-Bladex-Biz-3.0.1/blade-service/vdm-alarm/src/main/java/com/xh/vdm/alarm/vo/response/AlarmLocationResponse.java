package com.xh.vdm.alarm.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "返回体：告警定位点")
@Data
public class AlarmLocationResponse {

	@JsonProperty("time")
	@ApiModelProperty(name = "time", value = "时间（秒数）", example = "1715615999", required = true)
	private Long time;

	@JsonProperty("longitude")
	@ApiModelProperty(name = "longitude", value = "经度", example = "113.4531947602", required = true)
	private Double longitude;

	@JsonProperty("latitude")
	@ApiModelProperty(name = "latitude", value = "纬度", example = "23.1553043695", required = true)
	private Double latitude;

	@JsonProperty("appendix_id")
	@ApiModelProperty(name = "appendix_id", value = "附件ID", example = "1", required = true)
	private String appendixId;

	@JsonProperty("img1")
	@ApiModelProperty(name = "img1", value = "附件图片1", example = "http://www.baidu.com", required = true)
	private String img1;

	@JsonProperty("img2")
	@ApiModelProperty(name = "img2", value = "附件图片2", example = "http://www.baidu.com", required = true)
	private String img2;

	@JsonProperty("img3")
	@ApiModelProperty(name = "img3", value = "附件图片3", example = "http://www.baidu.com", required = true)
	private String img3;

	@JsonProperty("mp4")
	@ApiModelProperty(name = "mp4", value = "附件视频", example = "http://www.baidu.com", required = true)
	private String mp4;

	@JsonProperty("speed")
	@ApiModelProperty(name = "speed", value = "速度", example = "30.5", required = true)
	private Double speed;
}
