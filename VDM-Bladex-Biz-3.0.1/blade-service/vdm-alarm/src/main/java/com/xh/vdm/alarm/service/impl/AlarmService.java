package com.xh.vdm.alarm.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.alarm.constant.AlarmConstant;
import com.xh.vdm.alarm.constant.AlarmHandleState;
import com.xh.vdm.alarm.entity.BdmAbstractDevice;
import com.xh.vdm.alarm.entity.BdmDsmDataSh;
import com.xh.vdm.alarm.mapper.AlarmMapper;
import com.xh.vdm.alarm.mapper.BdmAbstractDeviceMapper;
import com.xh.vdm.alarm.mapper.BdmDsmDataShMapper;
import com.xh.vdm.alarm.mapper.ImpalaAlarmMapper;
import com.xh.vdm.alarm.service.IAlarmService;
import com.xh.vdm.alarm.service.IBdmDsmDataShService;
import com.xh.vdm.alarm.util.AlarmBusiUtil;
import com.xh.vdm.alarm.vo.request.AlarmRequest;
import com.xh.vdm.alarm.vo.response.AlarmLocationResponse;
import com.xh.vdm.alarm.vo.response.AlarmTypeAndCountVO;
import com.xh.vdm.base.vo.SendMsgToTerminalRequest;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.DictCodeConstant;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.DateUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.entity.Alarm;
import org.springblade.entity.DataAuthCE;
import org.springblade.entity.Location;
import org.springblade.system.entity.Dept;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.vo.DictTreeNodeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Slf4j
@Service("AlarmService")
public class AlarmService extends ServiceImpl<AlarmMapper, Alarm> implements IAlarmService {

	@Resource
	private AlarmBusiUtil alarmBusiUtil;

	@Resource
	private ImpalaAlarmMapper impalaAlarmMapper;

	@Resource
	private ISysClient sysClient;

	@Resource
	private AlarmMapper alarmMapper;

	@Resource
	private CETokenUtil ceTokenUtil;
	@Resource(name = "BdmDsmDataShService")
	private IBdmDsmDataShService bdmDsmDataShService;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private BdmAbstractDeviceMapper abstractDeviceMapper;

	@Resource(name = "alarmServiceExecutor")
	private ThreadPoolExecutor threadPool;

	/**
	 * @description: 根据部门id查询今日报警数量（包含本部门以及子部门、所有部门账户关联的外部车辆）
	 * 查询 impala
	 * @author: zhouxw
	 * @date: 2023-06-164 21:27:36
	 * @param: [deptId]
	 * @return: long
	 **/
	public long findTodayTotalCountByDeptId(long deptId, long userId) throws Exception{

		//暂定：先查询所有部门中的车辆，然后查询本账号关联的外部的车辆，
		//1.查询子部门信息
		List<Long> deptIds = alarmBusiUtil.getChildrenAndSelfDeptId(deptId);

		//2.查询账号关联的车辆
		List<Integer> vehicleIds = alarmBusiUtil.getVehicleIdList(userId);

		//3.查询本部门及子部门、关联车辆的报警数
		//今日日期
		Date date = new Date();
		long startTime = DateUtil.getDayFirstSecondTimestampNoLine(date.getTime()/1000);
		long endTime = DateUtil.getDayLastSecondTimestamp(date.getTime()/1000);
		//查询impala
		long totalCount = impalaAlarmMapper.getAlarmCountByDeptIdAndDuration(deptIds, vehicleIds, startTime, endTime);
		return totalCount;
	}

	/**
	 * @description: 根据部门id查询今日报警处理数量（包含本部门以及子部门、所有部门账户关联的外部车辆）
	 * 查询 impala
	 * @author: zhouxw
	 * @date: 2023-06-164 21:27:36
	 * @param: [deptId 本部门id；roleType 用户角色类型 1 服务商  2 第三方服务商 3 企业]
	 * @return: long
	 **/
	public long findTodayTotalHandleCountByDeptId(long deptId, long userId, String roleType) throws Exception{

		//暂定：先查询所有部门中的车辆，然后查询本账号关联的外部的车辆，
		//1.查询子部门信息
		List<Long> deptIds = alarmBusiUtil.getChildrenAndSelfDeptId(deptId);

		//2.查询账号关联的车辆
		List<Integer> vehicleIds = alarmBusiUtil.getVehicleIdList(userId);


		//3.查询本部门及子部门、关联车辆的报警数
		//今日日期
		Date date = new Date();
		long startTime = DateUtil.getDayFirstSecondTimestampNoLine(date.getTime()/1000);
		long endTime = DateUtil.getDayLastSecondTimestamp(date.getTime()/1000) + 1;
		long totalCount = impalaAlarmMapper.getAlarmHandleCountByDeptIdAndDuration(deptIds, vehicleIds, startTime, endTime, roleType);
		return totalCount;
	}

	/**
	 * @description: 查询实时报警总数（一个小时内）
	 * 查询impala
	 * @author: zhouxw
	 * @date: 2023-06-165 10:23:21
	 * @param: []
	 * @return: long
	 **/
	public long findTodayRealTimeAlarmTotalCount(long deptId, long userId) throws Exception{
		//1.查询子部门信息
		List<Long> deptIds = alarmBusiUtil.getChildrenAndSelfDeptId(deptId);

		//2.查询账号关联的车辆
		List<Integer> vehicleIds = alarmBusiUtil.getVehicleIdList(userId);

		//3.查询本部门及子部门、关联车辆的实时报警数
		Date date = new Date();
		long startTime = (date.getTime() - 3600 * 1000) / 1000;
		long endTime = date.getTime() / 1000;
		long totalCount = impalaAlarmMapper.getAlarmCountByDeptIdAndDuration(deptIds, vehicleIds, startTime, endTime);
		return totalCount;
	}


	/**
	 * @description: 查询实时报警处理总数（一个小时内）
	 * @author: zhouxw
	 * @date: 2023-06-165 10:23:21
	 * @param: []
	 * @return: long
	 **/
	public long findRealTimeAlarmHandleTotalCount(long deptId, long userId, String roleType) throws Exception{
		//1.查询子部门信息
		StringBuffer deptIds = new StringBuffer();
		try{
			R<List<Dept>> res = sysClient.getDeptChild(deptId);
			if(res == null || res.getData() == null){
				log.error("查询部门信息失败");
				throw new Exception("未查询到部门信息");
			}
			List<Dept> list = res.getData();
			for(Dept d : list){
				deptIds.append(d.getId()).append(",");
			}
		}catch (Exception e){
			log.error("查询子部门信息失败",e);
			throw new Exception("未查询到部门信息");
		}

		//2.拼装本部门
		deptIds.append(deptId).append(",");
		String deptIdsStr = deptIds.toString().substring(0, deptIds.length() - 1);

		//3.查询本部门及子部门、关联车辆的实时报警数
		Date date = new Date();
		long startTime = (date.getTime() - 3600 * 1000) / 1000;
		long endTime = date.getTime() / 1000;
		Date start = new Date();
		Date end = new Date();
		start.setTime(startTime * 1000);
		end.setTime(endTime * 1000);
		String startTimeStr = DateUtil.sdfHolder.get().format(start);
		String endTimeStr = DateUtil.sdfHolder.get().format(end);
		long totalCount = alarmMapper.getAlarmHandleCountByDeptIdAndDuration(deptIdsStr, userId, startTimeStr, endTimeStr, roleType);
		return totalCount;
	}

	/**
	 * @description: 根据指定报警类型查询实时报警数（一个小时内）
	 * 查询impala
	 * @author: zhouxw
	 * @date: 2023-06-165 10:23:21
	 * @param: []
	 * @return: long
	 **/
	public long findTodayRealTimeAlarmCount(long deptId, long userId, List<Integer> alarmTypes) throws Exception{
		//1.查询子部门信息
		List<Long> deptIds = alarmBusiUtil.getChildrenAndSelfDeptId(deptId);

		//2.查询账号关联的车辆
		List<Integer> vehicleIds = alarmBusiUtil.getVehicleIdList(userId);

		//3.查询本部门及子部门、关联车辆的实时超速报警数
		Date date = new Date();
		long startTime = (date.getTime() - 3600 * 1000) / 1000;
		long endTime = date.getTime() / 1000;
		long totalCount = impalaAlarmMapper.getAlarmCountByDeptIdAndDurationAndType(deptIds, vehicleIds, startTime, endTime, alarmTypes);
		return totalCount;
	}

	/**
	 * @description: 根据指定报警类型查询实时报警处理数（一个小时内）
	 * @author: zhouxw
	 * @date: 2023-06-165 10:23:21
	 * @param: []
	 * @return: long
	 **/
	public long findRealTimeAlarmHandleCount(long deptId, long userId, List<Integer> alarmTypes, String roleType) throws Exception{
		//1.查询子部门信息
		StringBuffer deptIds = new StringBuffer();
		try{
			R<List<Dept>> res = sysClient.getDeptChild(deptId);
			if(res == null || res.getData() == null){
				log.error("查询部门信息失败");
				throw new Exception("未查询到部门信息");
			}
			List<Dept> list = res.getData();
			for(Dept d : list){
				deptIds.append(d.getId()).append(",");
			}
		}catch (Exception e){
			log.error("查询子部门信息失败",e);
			throw new Exception("未查询到部门信息");
		}

		//2.拼装本部门
		deptIds.append(deptId).append(",");
		String deptIdsStr = deptIds.toString().substring(0, deptIds.length() - 1);

		//3.查询本部门及子部门、关联车辆的实时超速报警数
		Date date = new Date();
		long startTime = (date.getTime() - 3600 * 1000) / 1000;
		long endTime = date.getTime() / 1000;
		Date start = new Date();
		Date end = new Date();
		start.setTime(startTime * 1000);
		end.setTime(endTime * 1000);
		String startTimeStr = DateUtil.sdfHolder.get().format(start);
		String endTimeStr = DateUtil.sdfHolder.get().format(end);
		long handleCount = alarmMapper.getAlarmHandleCount(deptIdsStr, userId, startTimeStr, endTimeStr, alarmTypes, roleType);
		return handleCount;
	}


	/**
	 * @description: 获取报警类型
	 * @author: zhouxw
	 * @date: 2023-06-166 15:27:16
	 * @param: [code, key]
	 * @return: java.lang.String
	 **/
	public Set<DictTreeNodeVO> getChildrenFlatAlarmTypes (String code, String key) {
		//查找所有子报警类型
		DictTreeNodeVO overSpeedTreeNode = null;
		String tenantId = AuthUtil.getTenantId();
		log.info(">> getChildrenFlatAlarmTypes tenantId is {}", tenantId);
		try{
			R<DictTreeNodeVO> res = dictBizClient.getDictTreeByCodeAndKeyCacheTenant(code, key, tenantId);
			if(res == null || res.getData() == null){
				log.error("未查询到子类型");
				overSpeedTreeNode = null;
			}else{
				overSpeedTreeNode = res.getData();
			}
		}catch (Exception e){
			log.error("查询子类型失败",e);
			overSpeedTreeNode = null;
		}
		log.info(">> getChildrenFlatAlarmTypes code={} key={} 获取到的结果为{}", code, key , JSON.toJSONString(overSpeedTreeNode));
		Set<DictTreeNodeVO> set = new HashSet<>();
		if(overSpeedTreeNode != null){
			set = getDictChildFlat(overSpeedTreeNode, set);
		}
		return set;
	}



	/**
	 * @description: 获取报警类型
	 * @author: zhouxw
	 * @date: 2023-06-166 15:27:16
	 * @param: [code, key]
	 * @return: java.lang.String
	 **/
	public Set<DictTreeNodeVO> getChildrenFlatAlarmTypesByTenantId (String code, String key, String tenantId) {
		//查找所有子报警类型
		DictTreeNodeVO overSpeedTreeNode = null;
		log.info(">> getChildrenFlatAlarmTypes tenantId is {}", tenantId);
		try{
			R<DictTreeNodeVO> res = dictBizClient.getDictTreeByCodeAndKeyCacheTenant(code, key, tenantId);
			if(res == null || res.getData() == null){
				log.error("未查询到子类型");
				overSpeedTreeNode = null;
			}else{
				overSpeedTreeNode = res.getData();
			}
		}catch (Exception e){
			log.error("查询子类型失败",e);
			overSpeedTreeNode = null;
		}
		log.info(">> getChildrenFlatAlarmTypes code={} key={} 获取到的结果为{}", code, key , JSON.toJSONString(overSpeedTreeNode));
		Set<DictTreeNodeVO> set = new HashSet<>();
		if(overSpeedTreeNode != null){
			set = getDictChildFlat(overSpeedTreeNode, set);
		}
		return set;
	}

	/**
	 * @description: 递归遍历字典
	 * @author: zhouxw
	 * @date: 2023-06-165 20:17:17
	 * @param: [vo, list]
	 * @return: java.util.List<org.springblade.system.vo.DictTreeNodeVO>
	 **/
	private Set<DictTreeNodeVO> getDictChildFlat(DictTreeNodeVO vo, Set<DictTreeNodeVO> set){
		if(vo != null ){
			if(vo.getChildren() != null && vo.getChildren().size() > 0){
				List<DictTreeNodeVO> tmpList = vo.getChildren();
				for(DictTreeNodeVO v : tmpList){
					set.addAll(getDictChildFlat(v, set));
				}
			}else{
				set.add(vo);
			}
		}
		return set;
	}
	@Resource
	private KafkaTemplate<String, String> kafkaTemplate;

	@Resource
	private IDictBizClient dictBizClient;

	@Resource
	private BdmDsmDataShMapper bdmDsmDataShMapper;

	// 组织架构分组
	private List<List<Long>> getDeptIdListGroup (List<Long> input) {
		List<List<Long>> deptIdListGroup = new ArrayList<>();
		if (CollectionUtils.isEmpty(input)) {
			return  deptIdListGroup;
		}

		int num = input.size();
		List<Long> deptIdList = null;
		for (int i = 0; i < num; ++i) {
			if (i % 1000 == 0) {
				if (CollectionUtils.isNotEmpty(deptIdList)) {
					deptIdListGroup.add(deptIdList);
				}

				deptIdList = new ArrayList<>();
			}

			deptIdList.add(input.get(i));
		}
		if (CollectionUtils.isNotEmpty(deptIdList)) {
			deptIdListGroup.add(deptIdList);
		}

		return deptIdListGroup;
	}

	// 明确告警类型列表
	private List<Short> getFinalAlarmTypeList (List<Short> alarmTypeList, boolean special) {
		R<List<DictBiz>> r;
		Set<Short> alarmTypeSet = new HashSet<>();
		for (Short alarmType : alarmTypeList) {
			if (special) {
				r = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelf(DictCodeConstant.ALARM_TYPE_SPECIAL, alarmType.toString());
			} else {
				r = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelf(DictCodeConstant.ALARM_TYPE, alarmType.toString());
			}
			if ((!r.isSuccess()) || CollectionUtils.isEmpty(r.getData())) {
				continue;
			}
			for (DictBiz dict : r.getData()) {
				alarmTypeSet.add(Short.parseShort(dict.getDictKey()));
			}
		}

		return alarmTypeSet.parallelStream().collect(Collectors.toList());
	}

	// 按字典code为alarm_type_code的告警类型分组，生成分组告警数量。
	// 对于code为alarm_type_special的字典层级而言，此处也还是定死的，如果层级数有变动，估计还得改代码……
	private void formNumAlarmGroup (List<AlarmTypeAndCountVO> tmpList, JSONObject res) {
		Map<String, Integer> tmpMap = new HashMap<>();
		for (AlarmTypeAndCountVO tmp : tmpList) {
			tmpMap.put(
				tmp.getAlarmType(),
				tmp.getNumAlarm().intValue()
			);
		}

		R<List<DictTreeNodeVO>> r = this.dictBizClient.getDictTreeByCodeCache(DictCodeConstant.ALARM_TYPE_SPECIAL);
		if ((!r.isSuccess()) || CollectionUtils.isEmpty(r.getData())) {
			return;
		}

		Map<String, Map<String, Integer>> map = new HashMap<>();
		for (DictTreeNodeVO vo : r.getData()) {
			if (CollectionUtils.isEmpty(vo.getChildren())) {
				continue;
			}

			Map<String, Integer> numAlarmGroup = new HashMap<>();
			for (DictTreeNodeVO specialAlarmType : vo.getChildren()) {
				String k = specialAlarmType.getDictKey();
				if (!numAlarmGroup.containsKey(k)) {
					numAlarmGroup.put(k, 0);
				}
				if (CollectionUtils.isEmpty(specialAlarmType.getChildren())) {
					numAlarmGroup.put(k, numAlarmGroup.get(k) + tmpMap.getOrDefault(k, 0));
				} else {
					for (DictTreeNodeVO alarmType : specialAlarmType.getChildren()) {
						numAlarmGroup.put(k, numAlarmGroup.get(k) + tmpMap.getOrDefault(alarmType.getDictKey(), 0));
					}
				}
			}

			map.put(vo.getDictKey(), numAlarmGroup);
		}
		for (String k : map.keySet()) {
			List<JSONObject> joList = new ArrayList<>();
			JSONObject jo = new JSONObject();
			jo.put("alarm_type", k);
			jo.put("num_alarm", 0);
			joList.add(jo);
			Map<String, Integer> numAlarmGroup = map.get(k);
			for (String specialAlarmType : numAlarmGroup.keySet()) {
				joList.get(0).put(
					"num_alarm",
					Integer.parseInt(joList.get(0).get("num_alarm").toString()) + numAlarmGroup.get(specialAlarmType)
				);
				jo = new JSONObject();
				jo.put("alarm_type", specialAlarmType);
				jo.put("num_alarm", numAlarmGroup.get(specialAlarmType));
				joList.add(jo);
			}

			res.put(k, joList);
		}
	}

	@Override
	public int getNumAlarm (AlarmRequest request, List<Long> deviceIdList) {
		return this.baseMapper.getNumAlarm(
			request,deviceIdList,
			this.getDeptIdListGroup(request.getDeptList()),
			this.getDeptIdListGroup(request.getDeptIdList())
		);
	}

	@Override
	public int getNumAlarmDevice (AlarmRequest request) {
		long startTime = request.getStartTime();
		long endTime = request.getEndTime();
		List<String> deptIdList = ceTokenUtil.getDataAuth().getOrgList();
		List<Long> deviceIdList = new ArrayList<>();
		try {
			deviceIdList = getAlarmDeviceIdList(startTime, endTime, deptIdList);
		}catch (Exception e){
			log.error("查询发生告警的终端id列表失败",e);
			return 0;
		}
		return deviceIdList.size();
	}

	@Override
	public JSONObject getNumAlarmGroupByType (AlarmRequest request) {
		JSONObject res = new JSONObject();
		request.setAlarmTypeList(this.getFinalAlarmTypeList(request.getAlarmTypeList(), true));
		long startTime = request.getStartTime();
		long endTime = request.getEndTime();
		List<String> deptIdList = ceTokenUtil.getDataAuth().getOrgList();
		List<Long> deviceIdList = new ArrayList<>();
		try {
			deviceIdList = getAlarmDeviceIdList(startTime, endTime, deptIdList);
		}catch (Exception e){
			log.error("查询发生了告警的设备id列表失败",e);
		}
		//对设备进行分组
		List<List<Long>> listGroup = new ArrayList<>();
		List<Long> tmpList = new ArrayList<>();
		for(int i = 0 ; i < deviceIdList.size(); i++){
			tmpList.add(deviceIdList.get(i));
			if(i > 0 && i % 1000 == 0){
				listGroup.add(tmpList);
				tmpList = new ArrayList<>();
			}
		}
		//添加最后一批
		if(tmpList.size() > 0){
			listGroup.add(tmpList);
		}
		List<AlarmTypeAndCountVO> resList = baseMapper.getAlarmTypeAndCount(request, listGroup);

		if (CollectionUtils.isEmpty(resList)) {
			return res;
		}
		this.formNumAlarmGroup(resList, res);
		return res;
	}

	private int getNumAlarmParallel(AlarmRequest request, List<Long> deviceIdList) {
		List<List<Long>> deptListGroup = this.getDeptIdListGroup(request.getDeptList());
		List<List<Long>> deptIdListGroup = this.getDeptIdListGroup(request.getDeptIdList());

		if (CollectionUtils.isEmpty(request.getDeptList())) {
			return this.baseMapper.getNumAlarm(request, deviceIdList, deptListGroup, deptIdListGroup);
		}

		List<Future<Integer>> futures = new ArrayList<>();
		for (List<Long> chunk : deptListGroup) {
			futures.add(threadPool.submit(() ->
				this.baseMapper.getNumAlarm(request, deviceIdList, Collections.singletonList(chunk), deptIdListGroup)
			));
		}

		int total = 0;
		for (Future<Integer> f : futures) {
			try {
				total += f.get();
			} catch (Exception e) {
				log.error("Failed to get num alarm from thread", e);
			}
		}
		return total;
	}

	private List<Alarm> getAlarmListParallel(AlarmRequest request, List<Long> deviceIdList, IPage<Alarm> page) {
		List<List<Long>> deptListGroup = this.getDeptIdListGroup(request.getDeptList());
		List<List<Long>> deptIdListGroup = this.getDeptIdListGroup(request.getDeptIdList());

		if (CollectionUtils.isEmpty(request.getDeptList())) {
			return this.baseMapper.getAlarmList(request, deviceIdList, deptListGroup, deptIdListGroup, page.getCurrent(), page.getSize());
		}

		List<Future<List<Alarm>>> futures = new ArrayList<>();


		for (List<Long> chunk : deptListGroup) {
			futures.add(threadPool.submit(() ->
				this.baseMapper.getAlarmList(request, deviceIdList, Collections.singletonList(chunk), deptIdListGroup, 1, page.getSize())
			));
		}

		List<Alarm> allAlarms = new ArrayList<>();
		for (Future<List<Alarm>> f : futures) {
			try {
				allAlarms.addAll(f.get());
			} catch (Exception e) {
				log.error("Failed to get alarm list from thread", e);
			}
		}

		// 默认按照开始时间倒序排序，需要根据实际情况调整
		allAlarms.sort(Comparator.comparing(Alarm::getStartTime).reversed());

		long offset = (page.getCurrent() - 1) * page.getSize();
		return allAlarms.stream().skip(offset).limit(page.getSize()).collect(Collectors.toList());
	}

	@Override
	public IPage<Alarm> getAlarmPage (AlarmRequest request, Query query) {
		long startA = System.currentTimeMillis();
		long start1 = System.currentTimeMillis();
		if (CollectionUtils.isNotEmpty(request.getAlarmTypeList()) && (request.getSpecialAlarmType() != null)) {
			request.setAlarmTypeList(
				this.getFinalAlarmTypeList(
					request.getAlarmTypeList(),
					!request.getSpecialAlarmType().equals((byte) 0)
				)
			);
		}
		long end1 = System.currentTimeMillis();
		log.info(">>==查询告警类型耗时：" +(end1 - start1));

		//使用多线程提高查询效率
		CountDownLatch countDownLatch = new CountDownLatch(3);

		//根据终端号查询终端id
		List<Long> aDeviceIdList = new ArrayList<>();
		try{
			long start2 = System.currentTimeMillis();
			QueryWrapper<BdmAbstractDevice> badQw=new QueryWrapper<>();
			String uniqueIdLike = "";
			if(!StringUtils.isEmpty(request.getUniqueId())){
				uniqueIdLike = "%" + request.getUniqueId() + "%";
			}
			Integer category = null;
			if (!StringUtils.isEmpty(request.getDeviceCate())){
				category = Integer.parseInt(request.getDeviceCate());
			}
			List<Long> aList = abstractDeviceMapper.getDeviceIdListByCondition(uniqueIdLike, category);
			aDeviceIdList.addAll(aList);

//			if(!StringUtils.isEmpty(request.getUniqueId())){
//				//支持模糊查询，根据给定的uniqueId查询可以定位到的终端
//				List<BdmAbstractDevice> aList = abstractDeviceMapper.selectList(Wrappers.lambdaQuery(BdmAbstractDevice.class)
//					.like(BdmAbstractDevice::getUniqueId, "%" + request.getUniqueId() + "%"));
//				for(BdmAbstractDevice d : aList){
//					aDeviceIdList.add(d.getId());
//				}
//			}
			long end2 = System.currentTimeMillis();
			log.info(">>==根据终端号查询终端id耗时：" +(end2 - start2));
		}catch (Exception e){
			log.error("根据终端号查询终端id出错",e);
		}finally {
			countDownLatch.countDown();
		}

		//根据赋码号查询终端id
		List<Long> deviceNumDeviceIdList = new ArrayList<>();
		try{
			long start3 = System.currentTimeMillis();
			if(!StringUtils.isEmpty(request.getDeviceNum())){
				//支持模糊查询，根据给定的 deviceNum 查询可以定位到的终端
				List<BdmAbstractDevice> aList = abstractDeviceMapper.selectList(Wrappers.lambdaQuery(BdmAbstractDevice.class)
					.like(BdmAbstractDevice::getDeviceNum, "%" + request.getDeviceNum() + "%"));
				for(BdmAbstractDevice d : aList){
					deviceNumDeviceIdList.add(d.getId());
				}
			}
			long end3 = System.currentTimeMillis();
			log.info(">>==根据赋码号查询终端耗时："+(end3 - start3));
		}catch (Exception e){
			log.error("根据赋码号查询终端id出错",e);
		}finally{
			countDownLatch.countDown();
		}

		IPage<Alarm> page = Condition.getPage(query);
		if ((request.getStartTime() != null) && (request.getEndTime() != null) && (request.getStartTime() > request.getEndTime())) {
			page.setTotal(0);
			return page;
		}

		//查询当前用户可以检索的 device_id
		List<Long> deviceIdList = new ArrayList<>();
		try{
			long start4 = System.currentTimeMillis();
			DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
			try {
				deviceIdList = getAlarmDeviceIdList(request.getStartTime(), request.getEndTime(), dataAuthCE.getOrgList());
			}catch (Exception e){
				log.error("获取报警相关的终端id失败",e);
			}
			long end4 = System.currentTimeMillis();
			log.info(">>==查询数据权限下的deviceId耗时：" +(end4 - start4));
		}catch (Exception e){
			log.error("查询当前用户可以检索的设备id出错",e);
		}finally {
			countDownLatch.countDown();;
		}
		try {
			countDownLatch.await();
		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		}
		long endA = System.currentTimeMillis();
		log.info(">>==第一部分耗时：" +(endA - startA));
		long startB = System.currentTimeMillis();
		//根据赋码号和根据序列号查询的结果取交集
		List<Long> deviceIdArray = new ArrayList<>();
		if(aDeviceIdList.size() > 0 && deviceNumDeviceIdList.size() > 0){
			//如果同时根据赋码号和序列号查询
			aDeviceIdList.retainAll(deviceNumDeviceIdList);
			deviceIdArray.addAll(aDeviceIdList);
		}else{
			if(aDeviceIdList.size() > 0){
				deviceIdArray.addAll(aDeviceIdList);
			}else{
				deviceIdArray.addAll(deviceNumDeviceIdList);
			}
		}

		//如果指定了要查询的终端，则只查询相应终端的数据
		if(deviceIdArray.size() > 0){
			deviceIdList.retainAll(deviceIdArray);
		}

		if(CollectionUtils.isEmpty(deviceIdList)){
			//如果没有终端发生告警，或者没有权限查询终端，或者根据指定的终端没有发生告警
			//注意：此处要保证redis中发生告警的终端信息与大数据中一致
			page.setTotal(0);
			return page;
		}

		// 并行查询总数和分页数据
		try {
			if (!request.isExport()) {
				// 并行查询总数
				long start5 = System.currentTimeMillis();
				int total = this.getNumAlarmParallel(request, deviceIdList);
				page.setTotal(total);

				if (page.getTotal() <= 0) {
					return page;
				}
				long end5 = System.currentTimeMillis();
				log.info(">>==查询告警数量耗时：" + (end5 - start5));
			}

			// 并行查询分页数据
			long start6 = System.currentTimeMillis();
			List<Alarm> pageRecords = this.getAlarmListParallel(request, deviceIdList, page);
			page.setRecords(pageRecords);
			if (request.isExport()) {
				page.setTotal(page.getRecords().size());
			}
			long end6 = System.currentTimeMillis();
			log.info(">>==查询告警耗时：" + (end6 - start6));
		} catch (Exception e) {
			log.error("查询告警分页数据出错", e);
		}

		long endB = System.currentTimeMillis();
		log.info(">>==第二部分耗时："+(endB - startB));
		return page;
	}


	public List<Long> getAlarmDeviceIdList(Long startTime, Long endTime, List<String> deptIdList) throws Exception{
		//优化查询效率
		//1.获取查询日期列表
		List<String> dateList = DateUtil.getDateList(startTime, endTime);

		//2.根据前缀查询redis中的key
		long start1 = System.currentTimeMillis();
		Set<String> keys = new HashSet<>();
		for(String date : dateList){
			String keyPrefix = AlarmConstant.CACHE_PREFIX_ALARM_DEVICE + date.replace("-","") + ":";
			Set<String> set = stringRedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
				Set<String> keysTmp = new HashSet<>();
				Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(keyPrefix+"*").count(1000).build());
				while (cursor.hasNext()) {
					keysTmp.add(new String(cursor.next()));
				}
				return keysTmp;
			});
			keys.addAll(set);


		}
		long end1 = System.currentTimeMillis();
		log.info(">>==【告警分页查询】根据前缀查询redis中的key 耗时："+(end1 - start1));

		//3.添加数据权限
		//只保留在authDeptIds中存在的deptId
		Set<String> set = new HashSet<>();
		for(String key : keys){
			String deptId = key.split(":")[3];
			if(deptIdList.contains(deptId)){
				set.add(key);
			}
		}

		//4.批量查询redis，获取指定时间段、指定部门发生告警的终端号列表
		long start2 = System.currentTimeMillis();
		Set<String> sets = new HashSet<>();
		long start10 = System.currentTimeMillis();
		sets = getSetMembers(set);
		long end10 = System.currentTimeMillis();
		log.info("redis 查询 发生报警的设备耗时：" + (end10 - start10));
		//类型转换
		List<Long> deviceList = new ArrayList<>();
		if(sets != null){
			for(String bs : sets){
				deviceList.add(Long.parseLong(bs));
			}
		}
		long end2 = System.currentTimeMillis();
		log.info(">>==【告警分页查询】查询发生告警的终端序列号列表耗时：" + (end2 - start2));
		return deviceList;
	}


	private Set<String> getSetMembers(Set<String> keys) {
		// 获取Redis连接
		RedisConnection connection = stringRedisTemplate.getConnectionFactory().getConnection();
		try {
			// 创建pipeline
			connection.openPipeline();

			// 在pipeline中添加SMEMBERS命令
			for(String key : keys){
				connection.sMembers(stringRedisTemplate.getStringSerializer().serialize(key));
			}

			// 执行pipeline
			List<Object> results = connection.closePipeline();
			Set<String> set = new HashSet<>();
			// 解析结果，这里只有一个命令的返回值，所以取第一个元素
			if (!results.isEmpty()) {
				for(Object obj : results){
					LinkedHashSet<byte[]> links = (LinkedHashSet<byte[]>) obj;
					for(byte[] b : links){
						String str = new String(b);
						set.add(str);
					}
				}
				return set;
			}
		} finally {
			// 关闭连接
			connection.close();
		}
		return null;
	}

	@Override
	public List<AlarmLocationResponse> getLocationListFromAlarm (Alarm alarm) {
		List<AlarmLocationResponse> responseList = new ArrayList<>();
		List<Location> locationList = this.baseMapper.getLocationListFromAlarm(
			alarm.getStartTime() - (10 * 60),
			((alarm.getEndTime() == null) || (alarm.getEndTime() <= 0)) ? (System.currentTimeMillis() / 1000) : (alarm.getEndTime() + (10 * 60)),
			alarm.getTargetType(),
			alarm.getTargetId(),
			alarm.getDeviceType(),
			alarm.getDeviceId()
		);
		if (CollectionUtils.isEmpty(locationList)) {
			log.error("no location found from alarm: {}", alarm);
			return responseList;
		}

		Map<Long, String> locationTimeMapAttachId = new HashMap<>();
		String auxiliary = alarm.getAuxiliary();
		if (!StringUtils.isBlank(auxiliary)) {
			JSONObject jo = JSON.parseObject(auxiliary);
			if ((jo != null) && jo.containsKey("attachments") && (jo.get("attachments") != null)) {
				JSONArray ja = JSON.parseArray(jo.get("attachments").toString());
				if ((ja != null) && (!ja.isEmpty())) {
					for (Object o : ja) {
						JSONObject attach = (JSONObject) o;
						locationTimeMapAttachId.put(
							Long.parseLong(attach.getOrDefault("loc_time", 0).toString()),
							attach.getOrDefault("attachment_id", "").toString()
						);
					}
				}
			}
		}
		for (Location location : locationList) {
			AlarmLocationResponse response = new AlarmLocationResponse();
			BeanUtils.copyProperties(location, response);
			response.setAppendixId(locationTimeMapAttachId.getOrDefault(location.getTime(), ""));
			response.setImg1("");
			response.setImg2("");
			response.setImg3("");
			response.setMp4("");
			if (StringUtils.isNotBlank(response.getAppendixId())) {
				List<String> attachIdList = new ArrayList<>();
				attachIdList.add(response.getAppendixId());
				List<BdmDsmDataSh> attachList = this.bdmDsmDataShMapper.getAttachFromUniqueIdList(attachIdList);
				if (CollectionUtils.isNotEmpty(attachList)) {
					BdmDsmDataSh attach = attachList.get(0);
					response.setImg1((StringUtils.isNotBlank(attach.getJpg1()) && attach.getJpg1().contains("/")) ? attach.getJpg1() : "");
					response.setImg2((StringUtils.isNotBlank(attach.getJpg2()) && attach.getJpg2().contains("/")) ? attach.getJpg2() : "");
					response.setImg3((StringUtils.isNotBlank(attach.getJpg3()) && attach.getJpg3().contains("/")) ? attach.getJpg3() : "");
					response.setMp4((StringUtils.isNotBlank(attach.getMp4()) && attach.getMp4().contains("/")) ? attach.getMp4() : "");
				}
			}

			responseList.add(response);
		}

		return responseList;
	}

	@Override
	public List<AlarmLocationResponse> getFileFromAlarm (Alarm alarm) {
		 alarm = baseMapper.getAlarmById(alarm.getId());
		 return this.bdmDsmDataShService.getFileByAlarm(alarm);
	}

	@Override
	public R<String> dealAlarm (AlarmRequest request, BladeUser user) throws Exception{

		//根据传过来的告警id查询告警信息
		List<Alarm> alarmList = findAlarmListByIds(request.getIdList());

		long start4 = System.currentTimeMillis();
		long currentTime = System.currentTimeMillis() / 1000;
		SendMsgToTerminalRequest sendMsgToTerminalRequest = new SendMsgToTerminalRequest();
		sendMsgToTerminalRequest.setAlarmList(new ArrayList<>());
		for (Alarm alarm : alarmList) {
			Alarm alarmForKudu = new Alarm();
			alarmForKudu.setId(alarm.getId());
			alarmForKudu.setTargetType(alarm.getTargetType());
			alarmForKudu.setTargetId(alarm.getTargetId());
			alarmForKudu.setDeviceType(alarm.getDeviceType());
			alarmForKudu.setDeviceId(alarm.getDeviceId());
			alarmForKudu.setStartTime(alarm.getStartTime());
			if (request.getHandleState().equals(AlarmHandleState.HANDLE_STATE_DONE.getValue())) {
				SendMsgToTerminalRequest.Alarm alarmForTerminal = new SendMsgToTerminalRequest.Alarm();
				alarmForTerminal.setAlarmId(alarm.getId());
				alarmForTerminal.setDeviceType(alarm.getDeviceType());
				alarmForTerminal.setDeviceId(alarm.getDeviceId());
				sendMsgToTerminalRequest.getAlarmList().add(alarmForTerminal);

				alarmForKudu.setHandleState(AlarmHandleState.HANDLE_STATE_DONE.getValue());
				alarmForKudu.setHandleContent(StringUtils.isBlank(request.getHandleContent()) ? "" : request.getHandleContent());
				alarmForKudu.setHandleMeasures("");
				if (CollectionUtils.isNotEmpty(request.getHandleMeasuresList())) {
					alarmForKudu.setHandleMeasures(
						org.apache.commons.lang3.StringUtils.join(
							request.getHandleMeasuresList().toArray(),
							","
						)
					);
				}
			} else if (request.getHandleState().equals(AlarmHandleState.HANDLE_STATE_WRONG.getValue())) {
				alarmForKudu.setHandleState(AlarmHandleState.HANDLE_STATE_WRONG.getValue());
				alarmForKudu.setHandleContent("确认误报");
			} else {
				continue;
			}

			alarmForKudu.setHandler(user.getUserId());
			alarmForKudu.setHandleTime(currentTime);
			this.kafkaTemplate.send("GuoNeng_alarm", "handle", JSON.toJSONString(alarmForKudu));
		}
		long end4 = System.currentTimeMillis();
		log.info("告警信息入kudu（kafka）处理耗时：" + (end4 - start4));
		long start5 = System.currentTimeMillis();
		if (
			CollectionUtils.isNotEmpty(request.getHandleMeasuresList()) &&
			CollectionUtils.isNotEmpty(sendMsgToTerminalRequest.getAlarmList())
		) {
			sendMsgToTerminalRequest.setAudio((byte) 0);
			sendMsgToTerminalRequest.setScreen((byte) 0);
			sendMsgToTerminalRequest.setUrgent((byte) 0);
			sendMsgToTerminalRequest.setContent(StringUtils.isBlank(request.getHandleContent()) ? "" : request.getHandleContent());
			for (byte handleMeasures : request.getHandleMeasuresList()) {
				switch (handleMeasures) {
					case AlarmConstant.HANDLE_MEASURES_AUDIO:
						sendMsgToTerminalRequest.setAudio((byte) 1);
						break;
					case AlarmConstant.HANDLE_MEASURES_SCREEN:
						sendMsgToTerminalRequest.setScreen((byte) 1);
						break;
					case AlarmConstant.HANDLE_MEASURES_URGENT:
						sendMsgToTerminalRequest.setUrgent((byte) 1);
						break;
					default:
						break;
				}
			}

			this.kafkaTemplate.send("GuoNeng_terminal_cmd_deliver", JSON.toJSONString(sendMsgToTerminalRequest));
		}
		long end5 = System.currentTimeMillis();
		log.info("告警数据发送到终端（kafka）耗时：" + (end5 - start5));

		return R.data("处理成功！本次操作之前已处理的告警将自动跳过。");
	}

	@Override
	public List<Alarm> findAlarmListByIds(List<Long> ids) throws Exception {
		if(ids == null || ids.size() < 1){
			return null;
		}
		//对id进行分组，每组最多10000个id
		List<List<Long>> group = new ArrayList<>();
		int groupSize = 1;
		if(ids.size() < 10000){
			group.add(ids);
		}else{
			groupSize = ids.size() % 10000 > 0 ? (ids.size() / 10000 + 1) : (ids.size() / 10000);
			for(int i = 0; i < groupSize ; i++){
				int start = i * 10000;
				int end = (i + 1) * 10000 - 1;
				List<Long> tmpList = ids.subList(start, end);
				group.add(tmpList);
			}
			//最后一组
			group.add(ids.subList((groupSize - 1) * 10000, ids.size()));
		}
		List<Alarm> list = baseMapper.getAlarmListByIds(group);
		return list;
	}
	@Override
	public Alarm getAlarmById(Long id){
		Alarm alarm=baseMapper.getAlarmById(id);
		return alarm;
	}
}
