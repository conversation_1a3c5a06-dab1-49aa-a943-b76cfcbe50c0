package com.xh.vdm.alarm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.alarm.entity.BdmVehicle;
import com.xh.vdm.alarm.mapper.BdmVehicleMapper;
import com.xh.vdm.alarm.service.IBdmVehicleService;
import com.xh.vdm.alarm.vo.response.TargetResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 */
@Service
public class BdmVehicleServiceImpl extends ServiceImpl<BdmVehicleMapper, BdmVehicle> implements IBdmVehicleService {

	@Resource
	private BdmVehicleMapper bdmVehicleMapper;

	@Override
	public List<TargetResponse> selectTargetByDeptId(Long deptId) {
		return bdmVehicleMapper.selectTargetByDeptId(deptId);
	}
}
