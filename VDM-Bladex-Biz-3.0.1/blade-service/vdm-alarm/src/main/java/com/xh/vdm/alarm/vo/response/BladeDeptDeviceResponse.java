package com.xh.vdm.alarm.vo.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 机构 --终端关系
 * </p>
 */
@Data
public class BladeDeptDeviceResponse implements Serializable {

	private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;

    /**
     * 部门名
     */
    private String name;

	private String type = "dept";

	/**
	 * 终端信息
	 */
	private List<TargetResponse> children;

}
