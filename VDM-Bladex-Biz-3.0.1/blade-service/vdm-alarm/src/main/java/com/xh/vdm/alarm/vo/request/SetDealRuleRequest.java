package com.xh.vdm.alarm.vo.request;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel(value = "请求体：实时报警自动处理规则设置")
@Data
public class SetDealRuleRequest {

	@JsonProperty("dept_list")
	@ApiModelProperty(name = "dept_list", value = "单位ID列表", example = "[1, ...]", required = true)
	@NotEmpty(message = "单位ID列表为空。")
	private List<Long> deptList;

	@JsonProperty("rule_map")
	@ApiModelProperty(name = "rule_map", value = "规则映射表", example = "[{}, ...]", required = true)
	@NotNull(message = "规则映射表为空。")
	private JSONObject ruleMap;
}
