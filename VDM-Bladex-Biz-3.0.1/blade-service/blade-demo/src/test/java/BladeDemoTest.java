import com.xh.vdm.bd.DemoApplication;
import com.xh.vdm.bd.service.INoticeService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springblade.core.test.BladeBootTest;
import org.springblade.core.test.BladeSpringExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Blade单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(BladeSpringExtension.class)
@SpringBootTest(classes = DemoApplication.class)
@BladeBootTest(appName = "blade-demo", profile = "test", enableLoader = true)
public class BladeDemoTest {

	@Autowired
	private INoticeService noticeService;

	@Test
	public void contextLoads() {
		long count = noticeService.count();
		System.out.println("notice数量：[" + count + "] 个");
	}

}
