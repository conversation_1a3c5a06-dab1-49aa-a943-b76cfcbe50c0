#服务器端口
server:
  port: 8200

#数据源配置
#spring:
#  datasource:
#    url: ${blade.datasource.dev.url}
#    username: ${blade.datasource.dev.username}
#    password: ${blade.datasource.dev.password}

spring:
  #排除DruidDataSourceAutoConfigure
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      druid:
        #通用校验配置
        validation-query: select 1
        #启用sql日志拦截器
        proxy-filters:
          - sqlLogInterceptor
      #设置默认的数据源或者数据源组,默认值即为master
      primary: master
      datasource:
        master:
          druid:
            #独立校验配置
            validation-query: select 1
            #oracle校验
            #validation-query: select 1 from dual
          url: ${blade.datasource.demo.master.url}
          username: ${blade.datasource.demo.master.username}
          password: ${blade.datasource.demo.master.password}
        slave:
          druid:
            #独立校验配置
            validation-query: select 1
            #oracle校验
            #validation-query: select 1 from dual
          url: ${blade.datasource.demo.slave.url}
          username: ${blade.datasource.demo.slave.username}
          password: ${blade.datasource.demo.slave.password}

  # mqtt，经过项目中实际使用，发现前缀为mqtt时，代码中无法正确读取变量的值，存在缓存，所以这里更改前缀为mqtt-gn
  mqtt-gn:
    username: emqx # 用户名
    password: emq@@2024 # 密码
    hostUrl: tcp://************:1883 # ip+端口号;mqtts协议的话ip前面的tpc要改成ssl,端口好像是8883
    #说明：因为多个客户端同时连接mqtt时，如果clientId相同，会连接出错，无法连接。为了保证集群启动，clientId需要在启动时指定
    #clientId: client20 # 客户端名称(随便起名，强调注意:多个客户端同时连接mqtt的时候，客户端名称一样会导致出错一直重连,解决办法:名称不能命名一样的)
    defaultTopic: test1,test2,test3 # 订阅主题多个主题
    timeout: 100 # 超时时间 （单位：秒）
    keepalive: 60 # 心跳 （单位：秒）
    enabled: true # 是否使能mqtt功能
    millis: 300 # 间隔多长时间发布一组数据 (毫秒)
  main:
    allow-circular-references: true



mqtt:
  hostUrl: tcp://************:1883
  username: emqx
  password: emq@@2024
  cleanSession: true
  reconnect: true
  timeout: 100
  keepAlive: 100
  defaultTopic: test1 #多个topic前缀之间，使用 , 进行分隔
  isOpen: true
  qos: 0

#默认对象存储配置
oss:
  enabled: true
  name: minio
  tenant-mode: false
  endpoint: http://***********:9009
  access-key: 6qZ5NdRqsSuqJhEKVQKW
  secret-key: yWlHNzFzzunE1MjbiNJNZS0ic2Gb6bpGUT10h3z3
  bucket-name: guoneng
