package com.xh.vdm.bd.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.bd.entity.Menu;
import com.xh.vdm.bd.service.IMenuService;

import com.xh.vdm.bd.vo.ButtonAuthEntity;
import com.xh.vdm.bd.vo.DataAuthEntity;

import org.springblade.common.utils.StringUtils;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.WebUtil;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * 国能权限模拟接口
 */
@RestController
@RequestMapping("/gnAuthMock")
public class GNDataAuthMockController {

	@Resource
	private IMenuService menuService;

	/**
	 * 模拟国能功能权限/按钮权限
	 * @param systemCode 系统编码，位置平台为 bdswz
	 * @param workSpaceCode 工作空间配置，可配置，位置平台为 BDS_WZ
	 * @return
	 */
	@GetMapping("/api/system/permission/getUserPermissionByToken")
	public Map<String,Object> getButtonAuthGnMock(String systemCode, String workSpaceCode){
		Map<String,Object> resMap = new HashMap<>();
		//1.校验token
		String token = WebUtil.getRequest().getHeader("X-Access-Token");
		if(StringUtils.isEmpty(token)){
			token = WebUtil.getRequest().getHeader("x-access-token");
		}
		if(StringUtils.isEmpty(token)){
			resMap.put("code",401);
			resMap.put("message","token为空或不存在，清重新登录");
			resMap.put("success", false);
			resMap.put("timestamp", new Date().getTime());
			return resMap;
		}
		//2.组织返回数据
		//查询所有的按钮，如果需要测试按钮的访问权限，可以在位置平台的菜单管理中删除和新增按钮来测试接口的返回是否有相应的变化
		List<Menu> buttonList = menuService.list(Wrappers.lambdaQuery(Menu.class)
			.eq(Menu::getCategory, 2)
			.eq(Menu::getIsDeleted, 0));
		if(buttonList == null || buttonList.size() < 1){
			//如果没有查询到按钮
			resMap.put("code",200);
			resMap.put("message","操作成功");
			resMap.put("success", true);
			resMap.put("timestamp", new Date().getTime());
			resMap.put("result",new ArrayList<>());
			return resMap;
		}
		List<ButtonAuthEntity> buttonAuthList = new ArrayList<>();
		for(Menu menu : buttonList){
			ButtonAuthEntity button = new ButtonAuthEntity();
			button.setComponentCode(menu.getCode());
			button.setComponentUrl(menu.getCode());
			button.setComponentName(menu.getName());
			buttonAuthList.add(button);
		}
		resMap.put("code",200);
		resMap.put("message","操作成功");
		resMap.put("success", true);
		resMap.put("timestamp", new Date().getTime());
		resMap.put("result",buttonAuthList);
		return resMap;
	}


	/**
	 * 模拟国能数据权限接口
	 * @param param 查询参数
	 * @return
	 */
	@PostMapping("/api/system/sysOrg/getOrgCodeListByParentCode")
	public Map<String,Object> getDataAuthGnMock(@RequestBody Map<String,String> param){
		Map<String,Object> resMap = new HashMap<>();
		//1.校验token
		String token = WebUtil.getRequest().getHeader("X-Access-Token");
		if(StringUtils.isEmpty(token)){
			token = WebUtil.getRequest().getHeader("x-access-token");
		}
		if(StringUtils.isEmpty(token)){
			resMap.put("code",401);
			resMap.put("message","token为空或不存在，清重新登录");
			resMap.put("success", false);
			resMap.put("timestamp", new Date().getTime());
			return resMap;
		}
		//2.组织返回数据
		String mockType = token;
		if(StringUtils.isEmpty(mockType) || !("1".equals(mockType.trim()) || "2".equals(mockType.trim()) ||"3".equals(mockType.trim()))){
			mockType = "2";
		}
		//人员 mockType = 1
		if("1".equals(mockType)){
			DataAuthEntity result = new DataAuthEntity();
			result.setAuthOrgList(new ArrayList<>());
			result.setAuthType(5);
			result.setOperation(1);
			resMap.put("code",200);
			resMap.put("message","操作成功");
			resMap.put("success", true);
			resMap.put("timestamp", new Date().getTime());
			resMap.put("result",result);
			return resMap;
		}
		//组织，包含 mockType = 2
		if("2".equals(mockType)){
			DataAuthEntity result = new DataAuthEntity();
			List<String> orgList = new ArrayList<>();
			orgList.add("1123598813738675201");
			orgList.add("1810652820437864448");
			orgList.add("1810652820639191040");
			result.setAuthOrgList(orgList);
			result.setAuthType(1);
			result.setOperation(1);
			resMap.put("code",200);
			resMap.put("message","操作成功");
			resMap.put("success", true);
			resMap.put("timestamp", new Date().getTime());
			resMap.put("result",result);
			return resMap;
		}
		//组织，不包含 mockType = 3
		if("3".equals(mockType)){
			DataAuthEntity result = new DataAuthEntity();
			List<String> orgList = new ArrayList<>();
			orgList.add("1123598813738675201");
			orgList.add("1810652820437864448");
			orgList.add("1810652820639191040");
			result.setAuthOrgList(orgList);
			result.setAuthType(1);
			result.setOperation(2);
			resMap.put("code",200);
			resMap.put("message","操作成功");
			resMap.put("success", true);
			resMap.put("timestamp", new Date().getTime());
			resMap.put("result",result);
			return resMap;
		}
		resMap.put("code",400);
		resMap.put("message","操作失败，未知错误");
		resMap.put("success", false);
		resMap.put("timestamp", new Date().getTime());
		return resMap;
	}

	/**
	 * 国能校验token模拟接口
	 * @return
	 */
	@GetMapping("/mockCheckCEToken")
	public Map mockCheckCEToken(){
		String token = StringUtils.isEmpty(WebUtil.getHeader("Ce-Auth"))?WebUtil.getHeader("ce-auth"):WebUtil.getHeader("Ce-Auth");
		Map<String,Object> map = new HashMap<>();
		if(StringUtils.isEmpty(token)){
			Map<String,String> param = new HashMap<>();
			param.put("loginName","ce");
			map.put("success","true");
			map.put("result",param);
		}else{
			map.put("success","false");
			map.put("result",null);
		}
		return map;
	}

}
