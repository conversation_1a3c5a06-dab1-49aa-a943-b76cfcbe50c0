/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.bd.controller;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.bd.entity.Notice;
import com.xh.vdm.bd.feign.INoticeClient;
import com.xh.vdm.bd.props.DemoProperties;
import io.minio.http.HttpUtils;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.aspectj.weaver.ast.Not;
import org.springblade.core.cache.utils.CacheUtil;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Demo控制器
 *
 * <AUTHOR>
 */
@RefreshScope
@RestController
@RequestMapping("/demo")
@RequiredArgsConstructor
@Api(value = "配置接口", tags = "即时刷新配置")
public class DemoController {


	/**
	 * 需要导入blade-demo-dev.yaml文件至nacos
	 */
	@Value("${demo.name}")
	private String name;

	private final DemoProperties properties;


	//private final IBaseInfoClient client;


	private final INoticeClient noticeClient;


	private final RedisTemplate redisTemplate;


	/**
	 * @description: baseinfo go服务 feign client 测试
	 * @author: zhouxw
	 * @date: 2023-06-158 18:52:15
	 * @param: []
	 * @return: org.springblade.core.tool.api.R<java.util.List<com.xh.vdm.bd.entity.Person>>
	 **/
	/*@GetMapping("/getDriverList")
	public R<List<Person>> getDriverList(){
		R<List<Person>> list = client.getDriver(1, 10);
		return list;
	}*/


	@GetMapping("/notice")
	public R<List<Notice>> getNotice(){
		R<List<Notice>> list = noticeClient.top(1);
		return list;
	}



	@GetMapping("name")
	public String getName() {
		return name;
	}

	@GetMapping("name-by-props")
	public String getNameByProps() {
		return properties.getName();
	}


	@GetMapping("/testRedis")
	public String testRedis(){
		List<Long> deptIdList = (List) CacheUtil.get("blade:sys", "deptChildIds:id:", 1123598813738675201L, List.class);

		return JSON.toJSONString(deptIdList);

	}


	/*public static void main(String[] args) {
		//String url = "http://***********:8083/bd/location?is_filter=0&licence_plate=粤A05533&licence_color=1&start_time=1689350310&end_time=1689350510";
		Integer vehicleId = null;
		long vId = Long.parseLong(vehicleId+"");
	}*/


}
