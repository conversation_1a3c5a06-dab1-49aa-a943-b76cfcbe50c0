package com.xh.vdm.bd.controller;

import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.boot.file.LocalFile;
import org.springblade.core.oss.MinioTemplate;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.resource.feign.IOssClient;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;

/**
 * UploadController
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/notice/upload")
@Api(value = "对象存储接口", tags = "oss上传测试")
@Slf4j
public class UploadController extends BladeController {

	private final MinioTemplate minioTemplate;


	private IOssClient ossClient;

	/**
	 * MinIO客户端
	 */
	private final MinioClient client;



	/**
	 * minio上传demo
	 *
	 * @param file 上传文件
	 * @return BladeFile
	 */
	@SneakyThrows
	@PostMapping("put-object")
	public R<BladeFile> putMinioObject(@RequestParam MultipartFile file) {
		BladeFile bladeFile = minioTemplate.putFile(file);
		return R.data(bladeFile);
	}

	/**
	 * 上传本地文件
	 *
	 * @param file 上传文件
	 * @return LocalFile
	 */
	@SneakyThrows
	@PostMapping("put-local-object")
	public R<LocalFile> putLocalObject(@RequestParam MultipartFile file) {
		LocalFile localFile = getFile(file);
		localFile.transfer();
		return R.data(localFile);
	}


	/**
	 * 上传本地文件，指定名称
	 *
	 * @return LocalFile
	 */
	@SneakyThrows
	@PostMapping("/put-local-object-name")
	public R<BladeFile> putLocalObjectName() {
		String filePath = "e:/1.txt";
		//如果指定文件名字，需要防止文件名称重复，可以添加时间戳或者日期，或者uuid等
		R<BladeFile> res = ossClient.putFile("测试.txt", new File(filePath));
		return res;
	}

}
