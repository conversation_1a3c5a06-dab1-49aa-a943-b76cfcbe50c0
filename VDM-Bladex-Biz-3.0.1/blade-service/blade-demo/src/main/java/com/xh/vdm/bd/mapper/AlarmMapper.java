package com.xh.vdm.bd.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bd.entity.Alarm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.datascope.annotation.DataAuth;
import org.springblade.core.datascope.enums.DataScopeEnum;

import java.util.List;

public interface AlarmMapper extends BaseMapper<Alarm> {

	@DataAuth(column = "dept_id", type = DataScopeEnum.OWN_DEPT)
	List<Alarm> getAllAlarm();
}
