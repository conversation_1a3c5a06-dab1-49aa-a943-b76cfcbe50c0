package com.xh.vdm.bd.config;

import org.springblade.core.swagger.EnableSwagger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.ViewControllerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * <AUTHOR> chen * @version V1.0 * @Package com.dc.config * @date 2018/1/16 17:33 * @Description: 主要用途：开启在线接口文档和添加相关配置
 */
@Configuration
@EnableSwagger
//@Profile({"dev", "test"})
public class SwaggerConfig extends WebMvcConfigurerAdapter {
	@Value("${swagger.enable:true}")
	private Boolean enable;

	//@Bean

	public Docket createRestApi() {
		return new Docket(DocumentationType.SWAGGER_2)
			.enable(enable)
			.apiInfo(apiInfo())
			.select()
			.apis(RequestHandlerSelectors.basePackage("com.dc.controller"))
			.paths(PathSelectors.any())
			//.paths(PathSelectors.none())
			.build();

	}

	private ApiInfo apiInfo() {
		return new ApiInfoBuilder()
			.title("auth系统数据接口文档")
			.description("此系统为新架构Api说明文档")
			.termsOfServiceUrl("")
			.contact(new Contact("陈永佳 chen867647213 @ 163. com", "", "https://blog.csdn.net/Mrs_chens"))
			.version(" 1.0")
			.build();
	}

	/**
	 * swagger ui资源映射
	 *
	 * @param registry
	 */
	@Override
	public void addResourceHandlers(ResourceHandlerRegistry registry) {
		registry.addResourceHandler("swagger - ui.html")
			.addResourceLocations("classpath:/META - INF / resources /");
		registry.addResourceHandler(" / webjars/**")
			.addResourceLocations("classpath:/META-INF/resources/webjars/");
	}

	/**
	 * swagger-ui.html路径映射，浏览器中使用/api-docs访问
	 *
	 * @param registry
	 */
	@Override
	public void addViewControllers(ViewControllerRegistry registry) {
		registry.addRedirectViewController(" / api - docs", "/swagger - ui.html");
	}

}
