package com.xh.vdm.bd.generator;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.InjectionConfig;
import com.baomidou.mybatisplus.generator.config.*;
import com.baomidou.mybatisplus.generator.config.po.TableInfo;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

@Slf4j
public class CodeGenerator {

    private static  String DRIVER = "";
    private static  String URL = "";
	private static String SCHEMANAME = "";
    private static  String USERNAME = "";
    private static  String PASSWORD = "";

    //***********************************需要配置的地方start***********************************************

    //表名(多个表名中间用 , 分隔)
    private static  String TABLE_NAME = "";
    //模块名
    private static  String MODULE_NAME = "";
    //代码生成的根目录
    private static  String BASE_DIR = "";

    //***********************************需要配置的地方end***********************************************************************




    /*public static void main(String[] args) {
        generate();
    }*/

    public static void generate(){

        //配置初始化
        init();
        log.info("配置初始化完成");
        System.out.println("配置初始化完成");

        // 代码生成器
        AutoGenerator mpg = new AutoGenerator();

        // 全局配置
        GlobalConfig gc = new GlobalConfig();
        final String projectPath = System.getProperty("user.dir");
        gc.setOutputDir(projectPath + System.getProperty("file.separator") +MODULE_NAME + "/src/main/java");
        gc.setAuthor("vdm");
        gc.setOpen(false);
        gc.setKotlin(false);
        //gc.setSwagger2(true); //实体属性 Swagger2 注解
        mpg.setGlobalConfig(gc);



        // 数据源配置
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setUrl(URL);
        dsc.setSchemaName(SCHEMANAME);
        dsc.setDriverName(DRIVER);
        dsc.setUsername(USERNAME);
        dsc.setPassword(PASSWORD);
		//指定数据库类型：postgresql
		dsc.setDbType(DbType.POSTGRE_SQL);
        mpg.setDataSource(dsc);

        // 包配置
        final PackageConfig pc = new PackageConfig();
        //自己进行的设置，因为前边直接对 outputdir 进行了全路径设置，这里就不需要再设置moudleName了，否则目录就会多一层
        pc.setModuleName("");
        pc.setParent(BASE_DIR);
        mpg.setPackageInfo(pc);

        // 自定义配置
        InjectionConfig cfg = new InjectionConfig() {
            @Override
            public void initMap() {
                // to do nothing
            }
        };

        // 如果模板引擎是 freemarker
        String templatePath = "/templates/mapper.xml.ftl";
        // 如果模板引擎是 velocity
        //String templatePath = "/templates/mapper.xml.vm";

        // 自定义输出配置
        List<FileOutConfig> focList = new ArrayList<>();
        // 自定义配置会被优先输出
        focList.add(new FileOutConfig(templatePath) {
            @Override
            public String outputFile(TableInfo tableInfo) {
                // 自定义输出文件名 ， 如果你 Entity 设置了前后缀、此处注意 xml 的名称会跟着发生变化！！
                //注意，这里的 单独/ 才表示划分目录级别，否则会作为文件夹名称的一部分
                return projectPath + "/" +MODULE_NAME + "/" + "/src/main/java/" + "/"+BASE_DIR.replaceAll("\\.","/")+"/"+"mapper"
                        + "/" + tableInfo.getEntityName() + "Mapper" + StringPool.DOT_XML;
            }
        });
        /*
        cfg.setFileCreate(new IFileCreate() {
            @Override
            public boolean isCreate(ConfigBuilder configBuilder, FileType fileType, String filePath) {
                // 判断自定义文件夹是否需要创建
                checkDir("调用默认方法创建的目录，自定义目录用");
                if (fileType == FileType.MAPPER) {
                    // 已经生成 mapper 文件判断存在，不想重新生成返回 false
                    return !new File(filePath).exists();
                }
                // 允许生成模板文件
                return true;
            }
        });
        */
        cfg.setFileOutConfigList(focList);
        mpg.setCfg(cfg);

        // 配置模板
        TemplateConfig templateConfig = new TemplateConfig();

        // 配置自定义输出模板
        //指定自定义模板路径，注意不要带上.ftl/.vm, 会根据使用的模板引擎自动识别
        // templateConfig.setEntity("templates/entity2.java");
        // templateConfig.setService();
        // templateConfig.setController();
        templateConfig.setController("templates/controller.java");

        templateConfig.setXml(null);
        mpg.setTemplate(templateConfig);

        // 策略配置
        StrategyConfig strategy = new StrategyConfig();
        strategy.setNaming(NamingStrategy.underline_to_camel);
        strategy.setColumnNaming(NamingStrategy.underline_to_camel);
        //strategy.setSuperEntityClass("你自己的父类实体,没有就不用设置!");
        strategy.setEntityLombokModel(true);
        strategy.setRestControllerStyle(true);
        // 公共父类
        //strategy.setSuperControllerClass("你自己的父类控制器,没有就不用设置!");
        // 写于父类中的公共字段
        //strategy.setSuperEntityColumns("id");
        String[] tables = new String[TABLE_NAME.split(",").length];
        for(int i = 0 ; i < tables.length ; i++){
            tables[i] = TABLE_NAME.split(",")[i].trim();
        }
        strategy.setInclude(tables);
        strategy.setControllerMappingHyphenStyle(true);
        strategy.setTablePrefix(pc.getModuleName() + "_");
        mpg.setStrategy(strategy);
        //使用freemarker引擎
        mpg.setTemplateEngine(new FreemarkerTemplateEngine());
        log.info("即将进行代码生成");
        System.out.println("即将进行代码生成");
        mpg.execute();
        log.info("代码生成完成");
        System.out.println("代码生成完成");
    }


    private static void init(){
        //读取配置文件
        Resource resource = new ClassPathResource("/generator.properties");
        Properties props = new Properties();
        try{
            props.load(resource.getInputStream());
        }catch(Exception e){
            e.printStackTrace();
        }

        DRIVER = props.getProperty("driver");
        URL = props.getProperty("url");
		SCHEMANAME = props.getProperty("schemaName");
        USERNAME = props.getProperty("username");
        PASSWORD = props.getProperty("password");
        TABLE_NAME = props.getProperty("table_names");
        MODULE_NAME = props.getProperty("module_name");
        BASE_DIR = props.getProperty("base_dir");

    }

    public static void main(String[] args) {
        CodeGenerator.generate();
    }
}
