package com.xh.vdm.bd.controller;

import com.xh.vdm.bd.mqtt.MqttSendClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/mqtt")
@Slf4j
public class MqttController {

	@Resource
	private MqttSendClient client;

	@GetMapping("/push")
	public String push(String message){
		try {
			client.publish(false, "test1", message);
			return "发送消息成功";
		}catch (Exception e){
			log.error("发送消息失败",e);
			return "发送消息失败"+e.getMessage();
		}
	}
}
