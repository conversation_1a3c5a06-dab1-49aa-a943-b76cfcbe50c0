package com.xh.vdm.bd.controller;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.base.entity.Alarms;
import com.xh.vdm.base.feign.IAlarmInfoClient;
import com.xh.vdm.bd.entity.Notice;
import com.xh.vdm.bd.feign.INoticeClient;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: feign client 调用测试
 * @Author: zhouxw
 * @Date: 2023/6/7 20:19
 */
@RequestMapping("/feignTest")
@RestController
@Slf4j
public class FeignTest {


	/*@Resource
	private IBaseInfoClient client;*/

	@Resource
	private IAlarmInfoClient alarmInfoClient;

	@Resource
	private INoticeClient noticeClient;


	@Resource
	private IUserClient userClient;

	@GetMapping("/notice")
	public R<List<Notice>> getNotice(){
		R<List<Notice>> list = noticeClient.top(1);
		return list;
	}

	@GetMapping("/sendAlarm")
	public R sendAlarm(){
		Alarms alarms = new Alarms();
		List<Long> ids = new ArrayList<>();
		ids.add(1L);
		alarms.setAlarm(ids);
		try {
			R res = alarmInfoClient.sendAlarm(alarms);
			if(res == null ){
				log.error("调用[go security] feign接口失败");
			}
			int code = res.getCode();
			if(code == 0){
				log.info("调用[go security] feign接口成功");
				return res;
			}else{
				log.info("调用[go security] feign接口失败");
			}

		}catch (Exception e){
			log.error("调用[go security] feign接口失败",e);
		}

		return R.data("调用feign接口失败");
	}


	@GetMapping("/testUsername")
	public R<String> testUsername(Long userId){
		return userClient.findUsernameByUserIdCache(userId);
	}
}
