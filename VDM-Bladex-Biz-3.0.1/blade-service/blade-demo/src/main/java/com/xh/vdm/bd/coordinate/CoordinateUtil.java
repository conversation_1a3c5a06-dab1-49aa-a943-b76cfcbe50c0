package com.xh.vdm.bd.coordinate;

import lombok.extern.slf4j.Slf4j;
import org.locationtech.proj4j.*;

import java.math.BigDecimal;

/**
 * 坐标系转换工具类
 * CGCS2000坐标系：俗称大地2000坐标系，北斗3号默认的坐标系，位置平台中存储坐标系使用CGCS2000。
 * WGS84坐标系：俗称84坐标系，是GPS导航系统使用的坐标系，也是全球通用的坐标系。
 * GCJ-02坐标系：俗称火星坐标系，是中国国测局坐标系，高德地图、QQ地图使用GCJ02坐标系
 * BD-09坐标系：百度坐标系，百度地图上使用的坐标系，在GCJ02坐标系系统上进行二次加密。
 *
 * 特殊说明：
 * CGCS2000有两种表示方法：
 * 1.地理坐标系，也就是俗称的大地坐标系，用于地理定位，结果是经纬度。
 * 2.平面坐标系，用于三维地图绘制，结果是xy。
 * 位置平台中，只涉及经纬度，所以使用的是CGCS2000地理坐标系。
 */
@Slf4j
public class CoordinateUtil {

	public static double pi = 3.1415926535897932384626;
	public static double x_pi = 3.14159265358979324 * 3000.0 / 180.0;
	public static double a = 6378245.0;
	public static double ee = 0.00669342162296594323;


	//public static void main(String[] args) {

		//CGCS2000平面坐标系转WGS84平面坐标系
		//CGCS2000XYZ_to_WGS84XYZ(0,0,0);

		/*System.out.println("84 to 2000 xy");
		double[] a = {116.4074,39.9042};
		double[] a1 = WGS84_to_CGCS2000(a[0], a[1]);
		System.out.println("84坐标系原始坐标：[" + a[0] + "," + a[1] +"]");
		System.out.println("84 转 2000 结果：["  + a1[0] + "," + a1[1] +","+ a1[2] +"]");

		System.out.println("2000 xy 转 2000 坐标系");
		CGCS2000XY_to_CGCS2000LonLat(a1[0], a1[1]);*/

		//2000平面坐标系 转 2000地理坐标系
		//CGCS2000XY_to_CGCS2000LonLat();

		/*//84 转 2000
		double[] a = {115.78401094464135,39.89041333160656};
		double[] a1 = WGS84_to_CGCS2000(a[0], a[1]);
		System.out.println("84坐标系原始坐标：[" + a[0] + "," + a[1] +"]");
		System.out.println("84 转 2000 结果：["  + a1[0] + "," + a1[1] +","+ a1[2] +"]");

		//2000 转 84
		double[] a2 = CGCS2000_to_WGS84(a1[0], a1[1], a1[2]);
		System.out.println("2000 转 84 结果：[" + a2[0] + "," + a2[1] +"]");

		//84 转 火星
		double[] a3 = WGS84_to_GCJ02(a2[0], a2[1]);
		System.out.println("84 转 火星 结果：[" + a3[0] + "," + a3[1] +"]");

		//2000 转 火星
		double[] a4 = CGCS2000_to_GCJ02(a1[0], a1[1], a1[2]);
		System.out.println("2000 转 火星 结果：[" + a4[0] + "," + a4[1] +"]");*/
	//}

	/**
	 * WGS84坐标 转 CGCS2000坐标（经纬度）
	 * @param longitude
	 * @param latitude
	 * @return
	 */
	public static double[] WGS84_to_CGCS2000(double longitude, double latitude) throws Exception{
		try {
			double lat = latitude; // 纬度
			double lon = longitude; // 经度

			// 创建CRS工厂和坐标转换工厂
			CRSFactory crsFactory = new CRSFactory();
			CoordinateTransformFactory ctFactory = new CoordinateTransformFactory();

			// 定义WGS84地理坐标的CRS（EPSG:4326）
			CoordinateReferenceSystem wgs84Crs = crsFactory.createFromName("EPSG:4326");

			// 定义CGCS2000地理坐标的CRS（EPSG:4490）
			CoordinateReferenceSystem cgcs2000Crs = crsFactory.createFromName("EPSG:4490");

			// 创建坐标转换器
			CoordinateTransform trans = ctFactory.createTransform(wgs84Crs, cgcs2000Crs);

			// 定义WGS84坐标
			ProjCoordinate wgs84Coord = new ProjCoordinate(lon, lat);

			// 进行坐标转换
			ProjCoordinate cgcs2000Coord = new ProjCoordinate();
			trans.transform(wgs84Coord, cgcs2000Coord);

			// 输出转换后的CGCS2000坐标
			//System.out.println("CGCS2000 Longitude (degrees): " + cgcs2000Coord.x);
			//System.out.println("CGCS2000 Latitude (degrees): " + cgcs2000Coord.y);
			return new double[]{cgcs2000Coord.x, cgcs2000Coord.y};
		} catch (Exception e) {
			log.error("WGS84 转 CGCS2000失败",e);
			throw new Exception("WGS84 转 CGCS2000失败: "+e.getMessage());
		}
	}

	/**
	 * CGCS2000 xyz 转 WGS84 xyz
	 * @param x
	 * @param y
	 * @param z
	 * @return
	 */
	public static double[] CGCS2000XYZ_to_WGS84XYZ(double x, double y, double z){
		// 创建 CRS 工厂
		CRSFactory crsFactory = new CRSFactory();

		// 定义 CGCS2000 和 WGS84 的 proj4j 字符串
		String cgcs2000Def = "+proj=geocent +ellps=GRS80 +units=m +no_defs";
		String wgs84Def = "+proj=geocent +ellps=WGS84 +units=m +no_defs";

		// 创建 CGCS2000 和 WGS84 的坐标参考系统
		CoordinateReferenceSystem cgcs2000 = crsFactory.createFromParameters("CGCS2000", cgcs2000Def);
		CoordinateReferenceSystem wgs84 = crsFactory.createFromParameters("WGS84", wgs84Def);

		// 创建坐标转换工厂
		CoordinateTransformFactory ctFactory = new CoordinateTransformFactory();

		// 创建从 CGCS2000 到 WGS84 的坐标转换
		CoordinateTransform transform = ctFactory.createTransform(cgcs2000, wgs84);

		// CGCS2000 中的 XYZ 坐标
		ProjCoordinate sourceCoord = new ProjCoordinate(4465048.87, 543181.66, 4565939.49);

		// 执行转换
		ProjCoordinate targetCoord = new ProjCoordinate();
		transform.transform(sourceCoord, targetCoord);

		// 输出转换后的 WGS84 中的 XYZ 坐标
		System.out.println("WGS84 X: " + targetCoord.x);
		System.out.println("WGS84 Y: " + targetCoord.y);
		System.out.println("WGS84 Z: " + targetCoord.z);
		return new double[]{targetCoord.x, targetCoord.y, targetCoord.z};
	}



	/**
	 * CGCS2000平面坐标 转 CGCS2000物理坐标（x、y）
	 * 未充分测试
	 * 未正式使用
	 * @param x
	 * @param y
	 * @return
	 */
	public static double[] CGCS2000XY_to_CGCS2000LonLat(double x, double y){
		// 计算分带号
		int zone = (int) Math.floor((x / 1000000.0) + 0.5);

		// 创建CRS工厂和坐标转换工厂
		CRSFactory crsFactory = new CRSFactory();
		CoordinateTransformFactory ctFactory = new CoordinateTransformFactory();

		// 定义CGCS2000高斯-克吕格投影的CRS（以计算出的分带号为例）
		String proj4Str = "+proj=tmerc +lat_0=0 +lon_0=" + (zone * 3) + " +k=1 +x_0=500000 +y_0=0 +ellps=GRS80 +units=m +no_defs";
		CoordinateReferenceSystem srcCrs = crsFactory.createFromParameters("EPSG:4549", proj4Str); // 使用示例EPSG代码，需要根据实际分带号调整

		// 定义CGCS2000地理坐标的CRS（EPSG:4490）
		CoordinateReferenceSystem dstCrs = crsFactory.createFromName("EPSG:4490");

		// 创建坐标转换器
		CoordinateTransform trans = ctFactory.createTransform(srcCrs, dstCrs);

		// 定义高斯-克吕格平面坐标
		ProjCoordinate srcCoord = new ProjCoordinate(x, y);

		// 进行坐标转换
		ProjCoordinate dstCoord = new ProjCoordinate();
		trans.transform(srcCoord, dstCoord);

		// 输出转换后的地理坐标
		//System.out.println("Latitude (degrees): " + dstCoord.y);
		//System.out.println("Longitude (degrees): " + dstCoord.x);
		return new double[]{dstCoord.x, dstCoord.y};
	}


	/**
	 * WGS84 转 CGCS2000平面坐标系
	 *
	 * @param longitude WGS84经度值
	 * @param latitude WGS84纬度值
	 * @return 第一个参数 经度， 第二个参数 纬度， 第三个参数 3度经度分带号（一般39表示中国区域）
	 */
	public static double[] WGS84_to_CGCS2000Plat(double longitude, double latitude) {
		CRSFactory crsFactory = new CRSFactory();
		CoordinateReferenceSystem WGS84 = crsFactory.createFromName("epsg:4326");
		// 根据经度范围确定转换标准,具体EPSG定义参考https://epsg.io
		String degree3EPSG = "epsg:4528";
		if (longitude < 76.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 25
			degree3EPSG = "epsg:4513";
		} else if (longitude >= 76.3 && longitude < 79.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 26
			degree3EPSG = "epsg:4514";
		} else if (longitude >= 79.3 && longitude < 82.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 27
			degree3EPSG = "epsg:4515";
		} else if (longitude >= 82.3 && longitude < 85.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 28
			degree3EPSG = "epsg:4516";
		} else if (longitude >= 85.3 && longitude < 88.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 29
			degree3EPSG = "epsg:4517";
		} else if (longitude >= 88.3 && longitude < 91.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 30
			degree3EPSG = "epsg:4518";
		} else if (longitude >= 91.3 && longitude < 94.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 31
			degree3EPSG = "epsg:4519";
		} else if (longitude >= 94.3 && longitude < 97.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 32
			degree3EPSG = "epsg:4520";
		} else if (longitude >= 97.3 && longitude < 100.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 33
			degree3EPSG = "epsg:4521";
		} else if (longitude >= 100.3 && longitude < 103.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 34
			degree3EPSG = "epsg:4522";
		} else if (longitude >= 103.3 && longitude < 106.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 35
			degree3EPSG = "epsg:4523";
		} else if (longitude >= 106.3 && longitude < 109.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 36
			degree3EPSG = "epsg:4524";
		} else if (longitude >= 109.3 && longitude < 112.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 37
			degree3EPSG = "epsg:4525";
		} else if (longitude >= 112.3 && longitude < 115.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 38
			degree3EPSG = "epsg:4526";
		} else if (longitude >= 115.3 && longitude < 118.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 39
			degree3EPSG = "epsg:4527";
		} else if (longitude >= 118.3 && longitude < 121.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 40
			degree3EPSG = "epsg:4528";
		} else if (longitude >= 121.3 && longitude < 124.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 41
			degree3EPSG = "epsg:4529";
		} else if (longitude >= 124.3 && longitude < 127.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 42
			degree3EPSG = "epsg:4530";
		} else if (longitude >= 127.3 && longitude < 130.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 43
			degree3EPSG = "epsg:4531";
		} else if (longitude >= 130.3 && longitude < 133.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 44
			degree3EPSG = "epsg:4532";
		} else if (longitude >= 133.3) {
			// CGCS2000 / 3-degree Gauss-Kruger zone 45
			degree3EPSG = "epsg:4533";
		}
		CoordinateReferenceSystem CGCS2000 = crsFactory.createFromName(degree3EPSG);
		// 定义转换Factory
		CoordinateTransformFactory ctFactory = new CoordinateTransformFactory();
		CoordinateTransform wgs84ToCGCS2000 = ctFactory.createTransform(WGS84, CGCS2000);
		// 转换结果数据定义
		ProjCoordinate result = new ProjCoordinate();
		// 转换方法调用
		// 输出转换值: result.x前两位表示的3度经度分带的中国区域内编号, result.x->longitude, result.y->latitude
		wgs84ToCGCS2000.transform(new ProjCoordinate(longitude, latitude), result);
		String lonStr = BigDecimal.valueOf(result.x) + "";
		//3度经度分带号，一般39表示中国区域
		double num = Double.parseDouble(lonStr.substring(0,2));
		double lon = Double.parseDouble(lonStr.substring(2));
		return new double[]{lon, BigDecimal.valueOf(result.y).doubleValue(), num};
	}

	/**
	 * 大地2000坐标系(CGCS2000) 转 WGS84
	 * @param x CGCS2000经度值
	 * @param y CGCS2000纬度值
	 * @param num CGCS2000 3度分带号
	 * @return x,y WGS84经度值,WGS84纬度值
	 */
	public static double[] CGCS2000_to_WGS84(double x, double y, double num) {
		String degree3No = num+"";
		CRSFactory crsFactory = new CRSFactory();
		CoordinateReferenceSystem WGS84 = crsFactory.createFromName("epsg:4326");
		// 根据精度范围换算使用坐标标准
		String degree3EPSG = "epsg:4528";
		switch (degree3No) {
			case "25":
				degree3EPSG = "epsg:4513";
				break;
			case "26":
				degree3EPSG = "epsg:4514";
				break;
			case "27":
				degree3EPSG = "epsg:4515";
				break;
			case "28":
				degree3EPSG = "epsg:4516";
				break;
			case "29":
				degree3EPSG = "epsg:4517";
				break;
			case "30":
				degree3EPSG = "epsg:4518";
				break;
			case "31":
				degree3EPSG = "epsg:4519";
				break;
			case "32":
				degree3EPSG = "epsg:4520";
				break;
			case "33":
				degree3EPSG = "epsg:4521";
				break;
			case "34":
				degree3EPSG = "epsg:4522";
				break;
			case "35":
				degree3EPSG = "epsg:4523";
				break;
			case "36":
				degree3EPSG = "epsg:4524";
				break;
			case "37":
				degree3EPSG = "epsg:4525";
				break;
			case "38":
				degree3EPSG = "epsg:4526";
				break;
			case "39":
				degree3EPSG = "epsg:4527";
				break;
			case "40":
				degree3EPSG = "epsg:4528";
				break;
			case "41":
				degree3EPSG = "epsg:4529";
				break;
			case "42":
				degree3EPSG = "epsg:4530";
				break;
			case "43":
				degree3EPSG = "epsg:4531";
				break;
			case "44":
				degree3EPSG = "epsg:4532";
				break;
			case "45":
				degree3EPSG = "epsg:4533";
				break;
		}
		CoordinateReferenceSystem CGCS2000 = crsFactory.createFromName(degree3EPSG);
		CoordinateTransformFactory ctFactory = new CoordinateTransformFactory();
		// 转换区别实际就这一行创建转换器的代码,前后顺序变了
		CoordinateTransform cgcs2000ToWGS84 = ctFactory.createTransform(CGCS2000, WGS84);
		ProjCoordinate result = new ProjCoordinate();
		String lonStr = (int)num + "" + x;
		double lon = Double.parseDouble(lonStr);
		cgcs2000ToWGS84.transform(new ProjCoordinate(lon, y), result);
		return new double[] {BigDecimal.valueOf(result.x).doubleValue(),BigDecimal.valueOf(result.y).doubleValue()};
	}

	/**
	 * 大地2000坐标系(CGCS2000) 转 火星坐标系（GCJ-02）
	 * 思路：大地2000先转84，然后再由84转火星
	 * @param lon 大地2000坐标系经度值，前两位为3度分带号，后边的是经度值
	 * @param lat 大地2000坐标系纬度值
	 * @return
	 */
	public static double[] CGCS2000_to_GCJ02(double lon, double lat, double num){
		//1.大地2000转84坐标系
		double[] wgs84 = CGCS2000_to_WGS84(lon, lat, num);
		//2.84坐标系转火星坐标系
		double[] gcj02 = WGS84_to_GCJ02(wgs84[0], wgs84[1]);
		return gcj02;
	}



	/**
	 * WGS84 转 火星坐标系 (GCJ-02)
	 *
	 * @param lat
	 * @param lon
	 * @return
	 */
	public static double[] WGS84_to_GCJ02(double lon, double lat) {
		if (outOfChina(lat, lon)) {
			return new double[]{lat, lon};
		}
		double dLat = transformLat(lon - 105.0, lat - 35.0);
		double dLon = transformLon(lon - 105.0, lat - 35.0);
		double radLat = lat / 180.0 * pi;
		double magic = Math.sin(radLat);
		magic = 1 - ee * magic * magic;
		double sqrtMagic = Math.sqrt(magic);
		dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
		dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
		double mgLat = lat + dLat;
		double mgLon = lon + dLon;
		return new double[]{mgLon,mgLat};
	}

	/**
	 * 火星坐标系 (GCJ-02) 转 WGS84
	 */
	public static double[] GCJ02_to_WGS84(double lon, double lat) {
		double[] gps = transform(lat, lon);
		double lontitude = lon * 2 - gps[1];
		double latitude = lat * 2 - gps[0];
		return new double[]{lontitude,latitude};
	}

	/**
	 * 火星坐标系 (GCJ-02) 转 百度坐标系 (BD-09)
	 *
	 * @param lat
	 * @param lon
	 */
	public static double[] GCJ02_to_BD09(double lon, double lat) {
		double x = lon, y = lat;
		double z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);
		double theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);
		double tempLon = z * Math.cos(theta) + 0.0065;
		double tempLat = z * Math.sin(theta) + 0.006;
		double[] gps = {tempLon, tempLat};
		return gps;
	}

	/**
	 * * 百度坐标系 (BD-09) 转 火星坐标系 (GCJ-02)
	 */
	public static double[] BD09_to_GCJ02(double lon, double lat) {
		double x = lon - 0.0065, y = lat - 0.006;
		double z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
		double theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
		double tempLon = z * Math.cos(theta);
		double tempLat = z * Math.sin(theta);
		double[] gps = {tempLat, tempLon};
		return gps;
	}

	/**
	 * WGS84 转 BD-09
	 *
	 * @param lat
	 * @param lon
	 * @return
	 */
	public static double[] WGS84_to_BD09(double lon, double lat) {
		double[] gcj02 = WGS84_to_GCJ02(lat, lon);
		double[] bd09 = GCJ02_to_BD09(gcj02[0], gcj02[1]);
		return bd09;
	}

	/**
	 * BD-09 转 WGS84
	 *
	 * @param lat
	 * @param lon
	 * @return
	 */
	public static double[] BD09_To_WGS84(double lon, double lat){
		double[] gcj02 = BD09_to_GCJ02(lat, lon);
		double[] gps84 = GCJ02_to_WGS84(gcj02[0], gcj02[1]);
		//保留小数点后六位
		gps84[0] = retain6(gps84[0]);
		gps84[1] = retain6(gps84[1]);
		return gps84;
	}

	/**保留小数点后六位
	 * @param num
	 * @return
	 */
	private static double retain6(double num) {
		String result = String.format("%.6f", num);
		return Double.valueOf(result);
	}

	private static boolean outOfChina(double lat, double lon) {
		if (lon < 72.004 || lon > 137.8347)
			return true;
		return lat < 0.8293 || lat > 55.8271;
	}

	private static double transformLat(double x, double y) {
		double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y
			+ 0.2 * Math.sqrt(Math.abs(x));
		ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(y * pi) + 40.0 * Math.sin(y / 3.0 * pi)) * 2.0 / 3.0;
		ret += (160.0 * Math.sin(y / 12.0 * pi) + 320 * Math.sin(y * pi / 30.0)) * 2.0 / 3.0;
		return ret;
	}

	private static double transformLon(double x, double y) {
		double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1
			* Math.sqrt(Math.abs(x));
		ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(x * pi) + 40.0 * Math.sin(x / 3.0 * pi)) * 2.0 / 3.0;
		ret += (150.0 * Math.sin(x / 12.0 * pi) + 300.0 * Math.sin(x / 30.0
			* pi)) * 2.0 / 3.0;
		return ret;
	}

	private static double[] transform(double lat, double lon) {
		if (outOfChina(lat, lon)) {
			return new double[]{lat, lon};
		}
		double dLat = transformLat(lon - 105.0, lat - 35.0);
		double dLon = transformLon(lon - 105.0, lat - 35.0);
		double radLat = lat / 180.0 * pi;
		double magic = Math.sin(radLat);
		magic = 1 - ee * magic * magic;
		double sqrtMagic = Math.sqrt(magic);
		dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);
		dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);
		double mgLat = lat + dLat;
		double mgLon = lon + dLon;
		return new double[]{mgLat, mgLon};
	}

}
