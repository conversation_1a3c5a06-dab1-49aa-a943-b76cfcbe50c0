package com.xh.vdm.bd.config;

import com.xh.vdm.bd.mqtt.MqttAcceptClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Configuration
public class MqttConfig {

	@Autowired
	private MqttAcceptClient mqttAcceptClient;

	/**
	 * 订阅mqtt
	 *
	 * @return
	 */
	@Conditional(MqttCondition.class)
	@Bean
	public MqttAcceptClient getMqttPushClient() {
		mqttAcceptClient.connect();
		return mqttAcceptClient;
	}
}
