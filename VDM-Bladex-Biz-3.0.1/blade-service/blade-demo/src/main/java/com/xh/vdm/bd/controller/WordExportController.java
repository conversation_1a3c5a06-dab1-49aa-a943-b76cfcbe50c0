package com.xh.vdm.bd.controller;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.xh.vdm.bd.entity.Alarm;
import com.xh.vdm.bd.entity.AlarmList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/7/12 10:49
 */
@RestController
public class WordExportController {

	@Autowired
	private ResourceLoader resourceLoader;

	/**
	 * @description: 简单导出
	 * @author: zhouxw
	 * @date: 2023-07-193 19:56:56
	 * @param: [response]
	 * @return: void
	 **/
	@GetMapping("/exportSimple")
	public String exportSimple(HttpServletResponse response) {
		//1.组装数据
		Map<String, Object> params = assertMap();
		//2.加载模板文件
		//模板文件
		String templatePath = "c:/tmp/word_model.docx";
		//文件名称，防止冲突
		String fileName = System.currentTimeMillis() + ".docx";
		//本地临时文件
		String tmpPath = "c:/tmp/file/" + fileName;
		//代理地址，nginx上使用的代理地址
		String proxyPath = "/proxy/file/doc/";
		String resFileUrl = proxyPath + fileName;

		XWPFTemplate template = null;
		FileOutputStream fos = null;
		try {
			//3.将模板文件写入到根目录
			//4.编译模板，渲染数据
			template = XWPFTemplate.compile(templatePath).render(params);
			//5.写入到指定目录位置
			fos = new FileOutputStream(tmpPath);
			template.write(fos);
			return resFileUrl;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if(fos != null){
				try {
					fos.flush();
					fos.close();
				}catch (Exception e){}
			}
			if(template != null){
				try{
					template.close();
				}catch (Exception e){}
			}
		}
		return "";
	}


	@GetMapping("/preview")
	public void preview(HttpServletResponse response) {
		String percentEncodedFileName = null;
		try {
			percentEncodedFileName = percentEncode("test.docx");
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		}
		//1.组装数据
		Map<String, Object> params = assertMap();
		//2.加载模板文件
		String templatePath = "c:/tmp/word_model.docx";
		String fileName = System.currentTimeMillis() + ".docx";
		String tmpPath = "c:/tmp/file/" + fileName;
		try {
			//3.将模板文件写入到根目录
			//4.编译模板，渲染数据
			XWPFTemplate template = XWPFTemplate.compile(templatePath).render(params);
			//5.写入到指定目录位置

			StringBuilder contentDispositionValue = new StringBuilder();
			contentDispositionValue.append("attachment; filename=").append(percentEncodedFileName).append(";").append("filename*=").append("utf-8''").append(percentEncodedFileName);
			response.addHeader("Access-Control-Allow-Origin", "*");
			response.addHeader("Access-Control-Expose-Headers", "Content-Disposition,download-filename");
			response.setHeader("Content-disposition", contentDispositionValue.toString());
			response.setHeader("download-filename", percentEncodedFileName);

			FileOutputStream fos = new FileOutputStream(tmpPath);
			template.write(response.getOutputStream());

			fos.flush();
			fos.close();
			template.close();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			//7.删除临时文件
			/*File file = new File(tmpPath);
			file.delete();*/
			/*File copyFile = new File(templatePath);
			copyFile.delete();*/
		}
	}

	/**
	 * @description: 动态表格导出
	 * @author: zhouxw
	 * @date: 2023-07-193 20:49:54
	 * @param: [response]
	 * @return: void
	 **/
	@GetMapping("/dynamicTable")
	public void dynamicTable(HttpServletResponse response) {
		//1.组装数据
		List<Alarm> list = new ArrayList<>();
		for(int i = 0 ; i < 10; i++){
			Alarm alarm = new Alarm();
			alarm.setId((long)i);
			alarm.setAppealResult("处理结果"+i);
			list.add(alarm);
		}
		AlarmList alarmList = new AlarmList();
		alarmList.setList(list);

		//2.加载模板文件
		String templatePath = "c:/tmp/word_table_model.docx";
		//3.获取临时文件
		String fileName = System.currentTimeMillis() + ".docx";
		String tmpPath = "c:/tmp/file/" + fileName;
		try {
			//4.编译模板，渲染数据
			LoopRowTableRenderPolicy hackLoopTableRenderPolicy = new LoopRowTableRenderPolicy();
			Configure config =
				Configure.builder().bind("list", hackLoopTableRenderPolicy).build();
			XWPFTemplate template = XWPFTemplate.compile(templatePath, config).render(alarmList);
			//5.写入到指定目录位置
			FileOutputStream fos = new FileOutputStream(tmpPath);
			template.write(fos);
			fos.flush();
			fos.close();
			template.close();
			//6.提供下载
			down(response, tmpPath, fileName);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			//7.删除临时文件
			File file = new File(tmpPath);
			file.delete();
			File copyFile = new File(templatePath);
			copyFile.delete();
		}
	}







	private Map<String, Object> assertMap() {
		Map<String, Object> params = new HashMap<>();
		params.put("name", "hb");
		params.put("date", "2023-07-08");
		params.put("reason", "发生超速");
		params.put("alarmType","超速");
		params.put("dealType","警告");
		return params;
	}



	private void down(HttpServletResponse response, String filePath, String realFileName) {
		String percentEncodedFileName = null;
		try {
			percentEncodedFileName = percentEncode(realFileName);
		} catch (UnsupportedEncodingException e) {
			throw new RuntimeException(e);
		}
		StringBuilder contentDispositionValue = new StringBuilder();
		contentDispositionValue.append("attachment; filename=").append(percentEncodedFileName).append(";").append("filename*=").append("utf-8''").append(percentEncodedFileName);
		response.addHeader("Access-Control-Allow-Origin", "*");
		response.addHeader("Access-Control-Expose-Headers", "Content-Disposition,download-filename");
		response.setHeader("Content-disposition", contentDispositionValue.toString());
		response.setHeader("download-filename", percentEncodedFileName);
		try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(filePath));
			 // 输出流
			 BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream());) {
			byte[] buff = new byte[1024];
			int len = 0;
			while ((len = bis.read(buff)) > 0) {
				bos.write(buff, 0, len);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	/**
	 * 百分号编码工具方法
	 * @param s 需要百分号编码的字符串
	 * @return 百分号编码后的字符串
	 */
	public static String percentEncode(String s) throws UnsupportedEncodingException {
		String encode = URLEncoder.encode(s, StandardCharsets.UTF_8.toString());
		return encode.replaceAll("\\+", "%20");
	}


}
