package com.xh.vdm.tp.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xh.vdm.tp.dto.ContainerLocationFromYilian;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@Api(tags = "集装箱", protocols = "http", produces = "application/json", consumes = "application/json")
@RestController
@RequestMapping("/container")
public class ContainerController {

	@Resource
	private KafkaTemplate<String, Object> kafkaTemplate;

	@ApiOperation(value = "推送定位数据", httpMethod = "POST")
	@PostMapping("/location/push")
	public R<String> pushLocation (@RequestBody ContainerLocationFromYilian request) {
		log.info("recv container location: {}", request);
		try {
			this.kafkaTemplate.send("ContainerLocTopic", JSONObject.parseObject(JSON.toJSONString(request)));
		} catch (Exception e) {
			log.error("fail push container location to kafka: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}

		return R.success(ResultCode.SUCCESS, "done");
	}
}
