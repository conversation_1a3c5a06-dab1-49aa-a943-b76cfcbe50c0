package com.xh.vdm.tp.dto;

import lombok.Data;

// 集装箱定位
@Data
public class ContainerLocationFromYilian {

	// 记录ID
	private Long id;

	// 客户ID
	private Integer clientId;

	// 系统分配的用户名
	private String clientCode;

	// 设备型号
	private String type;

	// 设备ID
	private String deviceId;

	// SIM卡号/ICCID号码
	private String cardNum;

	// 产商序号
	private String vendor;

	// 版本号
	public String version;

	// 定位时间（秒数）
	private Long createTime;

	// 上报时间（格式：yyyy-MM-dd HH:ii:ss）
	private String reportDate;

	// 更新时间（秒数）
	private Long updateTime;

	// 经度
	private String lon;

	// 经度标记（0：东经，1：西经）
	private String lonMark;

	// 纬度
	private String lat;

	// 纬度标记（0：南纬，1：北纬）
	private String latMark;

	// 海拔
	private String height;

	// 地址
	private String address;

	// 速度
	private String speed;

	// 方向
	private String direction;

	// 定位有效性（1：有效，0：无效）
	private String locate;

	// 状态（位图）
	private String allStatus;

	// ACC状态（0：关，1：开）
	private String acc;

	// 是否发生碰撞（0：没有，1：有）
	private String crash;

	// 主机拆卸报警（0：正常，1：报警）
	private String disMain;

	// 从机拆卸报警（0：正常，1：报警）
	private String disSub;

	// 门状态（0：关闭，1：打开）
	private String doorStatus;

	// 门开关总计数
	private String doorCountValue;

	// 载重状态（0：空载，1：重载）
	private String loadStatus;

	// 定位方式（00：GPS/北斗定位，01：基站定位，02：LBS定位，其它：无定位）
	private String locateMode;

	// 供电模式（0：备用电池，1：正常供电）
	private String power;

	// 主机电量
	private String mainBatValue;

	// 从机低电量报警（0：正常，1：报警）
	private String subBattery;

	// 从机电量
	private String subBatValue;

	// 电池低电压报警（0：正常，1：报警）
	private String voltage;

	// 电池电压
	private String volValue;

	// 卫星发送状态（0：默认，1：卫星发送失败帧）
	private String satellite;

	// 唤醒源（0：RTC唤醒，1：Gsensor唤醒，2：拆除唤醒，3~5：预留）
	private String wakeSrc;

	// 加速度
	private String accValue;

	// 基站编号
	private String cid;

	// 湿度
	private String humValue;

	// 模块信号值
	private String sigValue;

	// 预留字段1
	private String reserve1;

	// 预留字段2
	private String reserve2;
}
