/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.wrapper;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.cloud.client.BladeCloudApplication;
import org.springblade.core.launch.BladeApplication;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;

import javax.annotation.Resource;
import java.lang.annotation.*;
import java.util.List;

/**
 * Demo启动器
 *
 * <AUTHOR>
 */
//@BladeCloudApplication
@EnableDiscoveryClient
@EnableFeignClients({"org.springblade"})
@SpringBootApplication(excludeName = {"org.springblade.core.tool.config.MessageConfiguration"})
@ComponentScan(excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {org.springblade.core.tool.config.MessageConfiguration.class}))
@Slf4j
public class WrapperApplication {

	@Value("${application.name}")
	private static String applicationName;

	public static void main (String[] args) {
		String applicationNameFinal = "";
		//国能docker发布环境，从docker run -e 中传入参数
		String applicationNameDocker = System.getenv("applicationName");
		if(!StringUtils.isEmpty(applicationNameDocker)){
			applicationNameFinal = applicationNameDocker;
		}else{
			//星航测试环境：从java -jar启动时传入参数
			if(args.length > 0){
				applicationNameFinal = args[0]+"-wrapper";
			}
		}
		System.setProperty("finalApplicationName", applicationNameFinal);
		BladeApplication.run(applicationNameFinal, WrapperApplication.class, args);
	}
}

