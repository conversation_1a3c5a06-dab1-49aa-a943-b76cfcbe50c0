package com.xh.vdm.wrapper.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 负载均衡配置属性
 *
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "blade.loadbalancer")
public class LoadBalancerProperties {

    /**
     * 是否启用负载均衡
     */
    private boolean enabled = false;

    /**
     * 优先调用的IP段配置
     */
    private List<String> priorIpPattern = new ArrayList<>();

    /**
     * 灰度版本
     */
    private String version;
}
