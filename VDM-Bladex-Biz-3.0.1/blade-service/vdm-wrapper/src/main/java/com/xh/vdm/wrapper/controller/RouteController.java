package com.xh.vdm.wrapper.controller;

import cn.hutool.http.HttpRequest;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.wrapper.config.LoadBalancerProperties;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.util.HtmlUtils;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

@Controller
@RequestMapping("")
@Slf4j
public class RouteController {

	@Autowired
	private NacosServiceManager nacosServiceManager;

	@Autowired
	private NacosDiscoveryProperties nacosDiscoveryProperties;

	@Autowired(required = false)
	private LoadBalancerProperties loadBalancerProperties;

	@PostConstruct
	public void init() {
		log.info("LoadBalancerProperties initialized: {}", loadBalancerProperties);
		if (loadBalancerProperties != null) {
			log.info("Prior IP patterns: {}", loadBalancerProperties.getPriorIpPattern());
		}
	}

	@RequestMapping("/**")
	@ResponseBody
	//@PreAuth("permissionAll()")
	public Object toGoAPI (HttpServletRequest request, BladeUser user) {
		Object response = getResponse(request, null, user);
		return response;
	}

	/**
	 * 从nacos上获取服务实例ip和端口
	 * @return
	 */
	private String getServiceInstance(){
		String instanceName = System.getProperty("finalApplicationName");
		String finalName = "go-" + instanceName.split("-")[0];
		NamingService namingService = nacosServiceManager.getNamingService(nacosDiscoveryProperties.getNacosProperties());
		Instance instance = null;
		try {
			// 如果配置了优先IP模式，则优先选择匹配的实例
			if (loadBalancerProperties != null && loadBalancerProperties.getPriorIpPattern() != null && !loadBalancerProperties.getPriorIpPattern().isEmpty()) {
				List<Instance> allInstances = namingService.getAllInstances(finalName, nacosDiscoveryProperties.getGroup());
				instance = selectPriorInstance(allInstances);
			}

			// 如果没有找到优先实例，则使用默认的负载均衡策略
			if (instance == null) {
				instance = namingService.selectOneHealthyInstance(finalName, nacosDiscoveryProperties.getGroup());
			}
		} catch (NacosException e) {
			e.printStackTrace();
		}
		return instance.getIp() + ":" + instance.getPort();
	}

	/**
	 * 根据优先IP配置选择实例
	 * @param instances 所有可用实例
	 * @return 匹配优先IP模式的实例，如果没有匹配的则返回null
	 */
	private Instance selectPriorInstance(List<Instance> instances) {
		if (instances == null || instances.isEmpty()) {
			return null;
		}

		// 遍历所有实例，查找匹配优先IP模式的实例
		for (Instance instance : instances) {
			if (instance.isHealthy() && matchesPriorIpPattern(instance.getIp())) {
				return instance;
			}
		}

		return null;
	}

	/**
	 * 检查IP是否匹配优先IP模式
	 * @param ip 要检查的IP地址
	 * @return 是否匹配
	 */
	private boolean matchesPriorIpPattern(String ip) {
		if (loadBalancerProperties == null || loadBalancerProperties.getPriorIpPattern() == null || loadBalancerProperties.getPriorIpPattern().isEmpty()) {
			return false;
		}

		for (String pattern : loadBalancerProperties.getPriorIpPattern()) {
			if (StringUtils.isBlank(pattern)) {
				continue;
			}

			// 将通配符模式转换为正则表达式
			String regex = pattern.replace(".", "\\.")
							     .replace("*", ".*");

			if (Pattern.matches(regex, ip)) {
				return true;
			}
		}

		return false;
	}


	public Object getResponse(HttpServletRequest request,String uri, BladeUser user){
		//1.请求校验
		if(StringUtils.isEmpty(uri)){
			uri = request.getRequestURI();
		}
		if (StringUtils.isBlank(uri)) {
			return R.fail(ResultCode.FAILURE.getCode(), "请求URI为空。");
		}

		String paramStr = request.getQueryString();
		String method = request.getMethod();
		Map<String, String> headerMap = this.getRequestHeader(request);
		String bodyStr;
		try {
			bodyStr = this.getRequestBody(request);
		} catch (IOException e) {
			e.printStackTrace();
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}

		//2.获取用户信息（添加到header中）
		/*if(user == null){
			log.error("用户未登录，或未授权");
			return R.fail("请求失败：用户未授权");
		}*/
		if(user != null){
			log.info("用户信息为 "+JSON.toJSONString(user));
			String account = user.getAccount();
			String clientId = user.getClientId();
			String deptId = user.getDeptId();
			Long userId = user.getUserId();
			String tenantId = user.getTenantId();
			String roleId = user.getRoleId();
			String roleName = user.getRoleName();
			String postId = user.getPostId();
			headerMap.put("account", account);
			headerMap.put("clientId", clientId);
			headerMap.put("deptId", deptId);
			headerMap.put("userId", userId+"");
			headerMap.put("tenantId", tenantId);
			headerMap.put("roleId", roleId);
			headerMap.put("roleName", roleName);
			headerMap.put("postId", postId);
		}

		//3.执行请求发送
		String url = getServiceInstance() + uri + (StringUtils.isBlank(paramStr) ? "" : ("?" + paramStr));
		log.info("将要发送请求，url="+url);
		log.debug("method = " + method);
		log.debug("header = "+ JSON.toJSONString(headerMap));
		log.debug("body = "+bodyStr);

		String res;
		switch (method) {
			case "GET":
				res = HttpRequest.get(url)
					.headerMap(headerMap, true)
					.body(bodyStr)
					.execute().body();
				break;
			case "POST":
				res = HttpRequest.post(url)
					.headerMap(headerMap, true)
					.body(bodyStr)
					.execute().body();
				break;
			case "PUT":
				res = HttpRequest.put(url)
					.headerMap(headerMap, true)
					.body(bodyStr)
					.execute().body();
				break;
			case "DELETE":
				res = HttpRequest.delete(url)
					.headerMap(headerMap, true)
					.body(bodyStr)
					.execute().body();
				break;
			default:
				return R.fail(ResultCode.FAILURE.getCode(), "请求方法不正确。");
		}

		JSONObject jo = JSON.parseObject(res);
		if (jo == null) {
			return R.fail(ResultCode.FAILURE.getCode(), "返回体为空。");
		}
		if(jo.get("data")==null){
			//对null的特殊处理
			Map<String, Object> map = new HashMap<>();
			map.put("code",(Integer.parseInt(jo.get("code").toString()) == 0) ? ResultCode.SUCCESS.getCode() : ResultCode.FAILURE.getCode());
			map.put("success", (Integer.parseInt(jo.get("code").toString()) == 0) ? true : false);
			map.put("msg",jo.get("msg").toString());
			map.put("data",null);
			return map;
		}else {
			R<Object> response = new R<>();
			response.setCode((Integer.parseInt(jo.get("code").toString()) == 0) ? ResultCode.SUCCESS.getCode() : ResultCode.FAILURE.getCode());
			response.setSuccess((Integer.parseInt(jo.get("code").toString()) == 0) ? true : false);
			response.setMsg(jo.get("msg").toString());
			log.info("data域的值为：{}", jo.get("data"));
			response.setData(jo.get("data"));
			return response;
		}
	}



	private Map<String, String> getRequestHeader (HttpServletRequest request) {
		Map<String, String> headerMap = new HashMap<>();
		Enumeration<String> headerNameList = request.getHeaderNames();
		if (headerNameList == null) {
			return headerMap;
		}
		while (headerNameList.hasMoreElements()) {
			String headerName = headerNameList.nextElement();
			if (StringUtils.isBlank(headerName)) {
				continue;
			}

			String headerValue = request.getHeader(headerName);
			headerMap.put(headerName, headerValue);
		}

		return headerMap;
	}

	private String getRequestBody (HttpServletRequest request) throws IOException {
		StringBuilder sb = new StringBuilder();
		BufferedReader br = request.getReader();
		String line;
		while ((line = br.readLine()) != null) {
			sb.append(line);
		}

		br.close();
		return HtmlUtils.htmlUnescape(sb.toString());
	}
}
