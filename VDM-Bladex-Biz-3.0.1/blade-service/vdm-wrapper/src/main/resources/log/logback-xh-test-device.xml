<?xml version="1.0" encoding="UTF-8"?>

<configuration>
    <!-- 日志存放路径 -->
    <property name="log.path" value="logs" />
    <!-- 日志输出格式 -->
    <property name="log.pattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{20} [%thread] [%method, %line] => %msg%n" />
    <!-- 日志控制台输出格式 -->
    <property name="log.console.pattern" value="%red(%d{yyyy-MM-dd HH:mm:ss.SSS}) %highlight(%-5level) %boldMagenta(%logger{20}) %green([%thread]) %yellow([%method, %line]) => %msg%n" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.console.pattern}</pattern>
        </encoder>
    </appender>

    <!-- INFO级别输出 -->
    <appender name="INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件名 -->
        <file>${log.path}/info.log</file>
        <!-- 基于时间和文件大小的滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 切割日志文件命名 -->
            <fileNamePattern>${log.path}/info/%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- 切割日志文件大小 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 日志文件保留时间（单位：天） -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 日志级别 -->
            <level>INFO</level>
            <!-- 匹配级别时：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配级别时：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- DEBUG级别输出 -->
    <appender name="DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件名 -->
        <file>${log.path}/debug.log</file>
        <!-- 基于时间和文件大小的滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 切割日志文件命名 -->
            <fileNamePattern>${log.path}/debug/%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- 切割日志文件大小 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 日志文件保留时间（单位：天） -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 日志级别 -->
            <level>DEBUG</level>
            <!-- 匹配级别时：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配级别时：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- ERROR级别输出 -->
    <appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 文件名 -->
        <file>${log.path}/error.log</file>
        <!-- 基于时间和文件大小的滚动策略 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- 切割日志文件命名 -->
            <fileNamePattern>${log.path}/error/%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <!-- 切割日志文件大小 -->
            <maxFileSize>10MB</maxFileSize>
            <!-- 日志文件保留时间（单位：天） -->
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 日志级别 -->
            <level>ERROR</level>
            <!-- 匹配级别时：接收（记录） -->
            <onMatch>ACCEPT</onMatch>
            <!-- 不匹配级别时：拒绝（不记录） -->
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!--系统操作日志-->
    <root level="info">
        <!-- 全局日志级别，INFO以上输出到控制台 -->
        <appender-ref ref="CONSOLE"/>
        <!-- appender将会添加到logger -->
        <appender-ref ref="INFO"/>
        <appender-ref ref="DEBUG"/>
        <appender-ref ref="ERROR"/>
    </root>

    <logger name="net.sf.ehcache" level="INFO"/>
    <logger name="druid.sql" level="INFO"/>


    <!-- MyBatis log configure -->
    <logger name="com.apache.ibatis" level="INFO"/>
    <logger name="org.mybatis.spring" level="INFO"/>
    <logger name="java.sql.Connection" level="INFO"/>
    <logger name="java.sql.Statement" level="INFO"/>
    <logger name="java.sql.PreparedStatement" level="INFO"/>

    <!-- 减少部分debug日志 -->
    <logger name="druid.sql" level="INFO"/>
    <logger name="org.apache.shiro" level="INFO"/>
    <logger name="org.mybatis.spring" level="INFO"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.springframework.context" level="WARN"/>
    <logger name="org.springframework.beans" level="WARN"/>
    <logger name="com.baomidou.mybatisplus" level="INFO"/>
    <logger name="org.apache.ibatis.io" level="INFO"/>
    <logger name="org.apache.velocity" level="INFO"/>
    <logger name="org.eclipse.jetty" level="INFO"/>
    <logger name="io.undertow" level="INFO"/>
    <logger name="org.xnio.nio" level="INFO"/>
    <logger name="org.thymeleaf" level="INFO"/>
    <logger name="springfox.documentation" level="INFO"/>
    <logger name="org.hibernate.validator" level="INFO"/>
    <logger name="com.netflix.loadbalancer" level="INFO"/>
    <logger name="com.netflix.hystrix" level="INFO"/>
    <logger name="com.netflix.zuul" level="INFO"/>
    <logger name="de.codecentric" level="INFO"/>
    <!-- cache INFO -->
    <logger name="net.sf.ehcache" level="INFO"/>
    <logger name="org.springframework.cache" level="INFO"/>
    <!-- cloud -->
    <logger name="org.apache.http" level="INFO"/>
    <logger name="com.netflix.discovery" level="INFO"/>
    <logger name="com.netflix.eureka" level="INFO"/>
    <!-- 业务日志 -->
    <Logger name="org.springblade" level="INFO"/>
    <Logger name="org.springblade.core.tenant" level="INFO"/>
    <Logger name="org.springblade.core.version" level="INFO"/>

    <!-- 减少nacos日志 -->
    <logger name="com.alibaba.nacos" level="ERROR"/>
</configuration>
