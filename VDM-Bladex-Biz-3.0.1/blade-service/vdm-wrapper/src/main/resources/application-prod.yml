# wrapper 和 go 是一一对应的，每个go服务都会对应一个唯一的wrapper服务，所以，这里ip是写固定的
go-api:
  host: http://***********:8882

blade:
  security:
    skip-url: /**
  jackson:
    null-to-empty: false

#数据源配置
spring:
  datasource:
    url: ${blade.datasource.prod.url}
    username: ${blade.datasource.prod.username}
    password: ${blade.datasource.prod.password}

  autoconfigure:
    exclude: org.springblade.core.tool.config.MessageConfiguration
