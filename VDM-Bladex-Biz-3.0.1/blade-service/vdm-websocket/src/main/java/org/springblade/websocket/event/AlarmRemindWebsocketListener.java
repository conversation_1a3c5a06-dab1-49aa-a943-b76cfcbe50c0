package org.springblade.websocket.event;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

// websocket连接监听器：告警通知
@Slf4j
@Component
@ServerEndpoint("/ws/alarmRemind/push/{userCode}")
public class AlarmRemindWebsocketListener {

	// 需要建立连接，以推送通知的userCode的列表。
	private static CopyOnWriteArraySet<String> userCodeSet = new CopyOnWriteArraySet<>();

	// 一个账号（以userId为标识）可能会同时在多个地方登录，每个登录态（以userCode为标识）都应接收到消息，因此，需要维护从userId到userCode的映射。
	// private static ConcurrentHashMap<String, List<String>> userCodeMap = new ConcurrentHashMap<>();
	private static Map<String, Set<String>> userCodeMap = new HashMap<>();

	// 当前所有连接的会话，以userCode为key。
	private static ConcurrentHashMap<String, Session> sessionPool = new ConcurrentHashMap<>();

	// 所有AlarmRemindWebsocketListener对象的集合。
	// 虽然@Component默认是单例模式的，但SpringBoot还是会为每个websocket连接，初始化一个bean，所以可以用一个静态set保存起来，此处用concurrent包的线程安全Set。
	private static CopyOnWriteArraySet<AlarmRemindWebsocketListener> webSockets = new CopyOnWriteArraySet<>();

	// 超管用户ID列表
	private static CopyOnWriteArraySet<Long> adminIdSet = new CopyOnWriteArraySet<>();

	// 当前连接的userCode
	private String userCode;

	// 当前连接的会话
	private Session session;

	// 往userCodeSet添加userCode
	public void addUserCodeSet (String userCode) {
		AlarmRemindWebsocketListener.userCodeSet.add(userCode);
	}

	// 添加从userId到userCode的映射
	public void putUserCodeMap (String userId, String userCode) {
		if (!AlarmRemindWebsocketListener.userCodeMap.containsKey(userId)) {
			AlarmRemindWebsocketListener.userCodeMap.put(userId, new HashSet<>());
		}

		AlarmRemindWebsocketListener.userCodeMap.get(userId).add(userCode);
	}

	// 往adminIdSet添加adminId
	public void addAdminIdSet (Long adminId) {
		AlarmRemindWebsocketListener.adminIdSet.add(adminId);
	}

	// 连接建立成功时调用。
	@OnOpen
	public void onOpen (Session session, @PathParam(value="userCode") String userCode) {
		log.info("websocket conn built for send alarm remind, userCode: {}", userCode);
		try {
			if (StringUtils.isBlank(userCode)) {
				log.error("empty userCode when onOpen for send alarm remind");
				return;
			}

			this.session = session;
			this.userCode = userCode;
			AlarmRemindWebsocketListener.sessionPool.put(userCode, session);
			AlarmRemindWebsocketListener.webSockets.add(this);
		} catch (Exception e) {
			log.error("fail callback when websocket conn built for send alarm remind, err: {}", e.getMessage(), e);
		}
	}

	// 连接关闭成功时调用。
	@OnClose
	public void onClose () {
		try {
			AlarmRemindWebsocketListener.webSockets.remove(this);
			AlarmRemindWebsocketListener.sessionPool.remove(this.userCode);
			AlarmRemindWebsocketListener.userCodeMap.remove(this.userCode);
			AlarmRemindWebsocketListener.userCodeSet.remove(this.userCode);
		} catch (Exception e) {
			log.error("fail callback when websocket conn close for send alarm remind, err: {}", e.getMessage(), e);
		}
	}

	// 收到客户端消息时调用。
	@OnMessage
	public void onMessage (String msg) {
		System.out.println("receive from client when send alarm remind, content: " + msg);
	}

	// 推送消息出错时调用。
	@OnError
	public void onError (Session session, Throwable e) {
		log.error("fail push when send alarm remind, session: {}, err: {}", session, e.getMessage(), e);
	}

	// 向指定用户推送通知
	public void sendMsgToUser (List<Long> userList, String msg) {
		Set<Long> userSet = new HashSet<>(userList);
		if (CollectionUtils.isNotEmpty(AlarmRemindWebsocketListener.adminIdSet)) {
			userSet.addAll(AlarmRemindWebsocketListener.adminIdSet);
		}
		for (long userId : userSet) {
			Set<String> userCodeList = AlarmRemindWebsocketListener.userCodeMap.get(String.valueOf(userId));
			if (CollectionUtils.isEmpty(userCodeList)) {
				continue;
			}
			for (String userCode : userCodeList) {
				Session session = AlarmRemindWebsocketListener.sessionPool.get(userCode);
				if ((session == null) || (!session.isOpen())) {
					continue;
				}
				try {
					log.info("start send alarm remind, userId: {}, userCode: {}, msg: {}", userId, userCode, msg);
					session.getAsyncRemote().sendText(msg);
					log.info("send alarm remind done, userId: {}, userCode: {}, msg: {}", userId, userCode, msg);
				} catch (Exception e) {
					log.error("fail send alarm remind: {}, userId: {}, userCode: {}, err: {}",
						msg, userId, userCode, e.getMessage(), e);
				}
			}
		}
	}
}
