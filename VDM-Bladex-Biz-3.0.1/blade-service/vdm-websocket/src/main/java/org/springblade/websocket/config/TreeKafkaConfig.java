package org.springblade.websocket.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;

import java.util.Map;

/**
 * 树结构Kafka消费者配置
 */
@Configuration
public class TreeKafkaConfig {
	// 改：prefix的值、Bean的名称、方法名
	@ConfigurationProperties(prefix = "spring.tree-kafka")
	@Bean("treeKafkaProperties")
	public KafkaProperties locationKafkaProperties() {
		return new KafkaProperties();
	}
	// 改：Bean的名称、Qualifier的名称
	@Bean("treeKafkaListenerContainerFactory")
	public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>>
	treeKafkaListenerContainerFactory(@Autowired @Qualifier("treeKafkaProperties") KafkaProperties firstKafkaProperties) {
		ConcurrentKafkaListenerContainerFactory<Integer, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
		factory.setConsumerFactory(this.firstConsumerFactory(firstKafkaProperties));
		factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
		factory.getContainerProperties().setMissingTopicsFatal(false);
		factory.getContainerProperties().setGroupId(firstKafkaProperties.getConsumer().getGroupId());

		return factory;
	}

	private ConsumerFactory<? super Integer, ? super String> firstConsumerFactory (KafkaProperties firstKafkaProperties) {
		Map<String, Object> configs = firstKafkaProperties.buildConsumerProperties();
		return new DefaultKafkaConsumerFactory<>(configs);
	}

}
