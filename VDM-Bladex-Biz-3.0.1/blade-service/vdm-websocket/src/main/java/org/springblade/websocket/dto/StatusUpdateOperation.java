package org.springblade.websocket.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 状态更新操作数据结构
 * 用于在树构建期间缓存设备状态更新
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StatusUpdateOperation {
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 状态字段名（online、fusionState、acc）
     */
    private String fieldName;
    
    /**
     * 新的状态值
     */
    private Integer newValue;
    
    /**
     * 更新时间戳
     */
    private long timestamp;
    
    /**
     * 生成缓存字段key
     * 格式：deviceId:fieldName
     */
    public String getCacheKey() {
        return deviceId + ":" + fieldName;
    }
    
    /**
     * 创建状态更新操作
     * @param deviceId 设备ID
     * @param fieldName 字段名
     * @param newValue 新值
     * @return 状态更新操作对象
     */
    public static StatusUpdateOperation create(String deviceId, String fieldName, Integer newValue) {
        return new StatusUpdateOperation(deviceId, fieldName, newValue, System.currentTimeMillis());
    }
}
