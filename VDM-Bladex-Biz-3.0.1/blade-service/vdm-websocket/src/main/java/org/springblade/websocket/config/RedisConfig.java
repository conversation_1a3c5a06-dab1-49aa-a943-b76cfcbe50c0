package org.springblade.websocket.config;

import cn.hutool.core.util.StrUtil;
import io.github.dengliming.redismodule.redisearch.client.RediSearchClient;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettucePoolingClientConfiguration;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;

// 假设您的自定义配置属性类是这个名字
// import com.yourproject.config.TreeRedisProperties;

@Configuration
public class RedisConfig {

	// ===================================================================
	// 第一个 Redis 数据源 (主数据源)
	// ===================================================================

	/**
	 * 创建主要的 Redis 连接工厂，使用 Spring Boot 默认配置。
	 * 标记为 @Primary，当没有指定 @Qualifier 时，默认注入此 Bean。
	 */
	@Bean("mainRedisConnectionFactory")
	@Primary
	public LettuceConnectionFactory mainRedisConnectionFactory(RedisProperties redisProperties) {
		RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
		config.setHostName(redisProperties.getHost());
		config.setPort(redisProperties.getPort());
		config.setDatabase(redisProperties.getDatabase());
		if (redisProperties.getPassword() != null) {
			config.setPassword(redisProperties.getPassword());
		}
		return new LettuceConnectionFactory(config);
	}

	/**
	 * 创建主要的 RedisTemplate，用于操作对象 (Value 为 Object)。
	 * 它使用主连接工厂。
	 */
	@Bean("redisTemplate")
	@Primary
	public RedisTemplate<String, Object> redisTemplate(
		@Qualifier("mainRedisConnectionFactory") RedisConnectionFactory connectionFactory) {
		RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
		redisTemplate.setConnectionFactory(connectionFactory);

		StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
		GenericJackson2JsonRedisSerializer genericJackson2JsonRedisSerializer = new GenericJackson2JsonRedisSerializer();

		redisTemplate.setKeySerializer(stringRedisSerializer);
		redisTemplate.setValueSerializer(genericJackson2JsonRedisSerializer);
		redisTemplate.setHashKeySerializer(stringRedisSerializer);
		redisTemplate.setHashValueSerializer(genericJackson2JsonRedisSerializer);
		redisTemplate.afterPropertiesSet();
		return redisTemplate;
	}


	// ===================================================================
	// 第二个 Redis 数据源 (tree 数据源)
	// ===================================================================

	/**
	 * 使用自定义的 TreeRedisProperties 创建第二个 Redis 连接工厂。
	 * 注意 Bean 的名称是 "treeLettuceConnectionFactory"，非常明确。
	 * @param redisProperties 自定义的配置属性对象
	 * @return LettuceConnectionFactory
	 */
	@Bean("treeLettuceConnectionFactory")
	public LettuceConnectionFactory treeLettuceConnectionFactory(TreeRedisProperties redisProperties) {
		RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
		config.setHostName(redisProperties.getHost());
		config.setPort(redisProperties.getPort());
		config.setDatabase(redisProperties.getDatabase());
		if (redisProperties.getPassword() != null) {
			config.setPassword(redisProperties.getPassword());
		}

		GenericObjectPoolConfig<Object> poolConfig = new GenericObjectPoolConfig<>();
		TreeRedisProperties.Pool poolProps = redisProperties.getPool();
		if (poolProps != null) {
			poolConfig.setMaxTotal(poolProps.getMaxActive());
			poolConfig.setMaxIdle(poolProps.getMaxIdle());
			poolConfig.setMinIdle(poolProps.getMinIdle());
			poolConfig.setMaxWait(Duration.ofMillis(poolProps.getMaxWait()));
		}

		LettucePoolingClientConfiguration clientConfig = LettucePoolingClientConfiguration.builder()
			.commandTimeout(Duration.ofMillis(redisProperties.getTimeout()))
			.poolConfig(poolConfig)
			.build();

		LettuceConnectionFactory factory = new LettuceConnectionFactory(config, clientConfig);
		// 调用 afterPropertiesSet() 是一个好习惯，以确保工厂被正确初始化。
		factory.afterPropertiesSet();
		return factory;
	}

	/**
	 * 创建第二个 RedisTemplate，用于操作字符串 (Value 为 String)。
	 * 【重要】这里直接注入 "treeLettuceConnectionFactory"，移除了中间的别名 Bean。
	 * @param treeConnectionFactory 通过 @Qualifier 精确指定第二个连接工厂
	 * @return RedisTemplate
	 */
	@Bean("treeRedisTemplate")
	public RedisTemplate<String, String> treeRedisTemplate(
		@Qualifier("treeLettuceConnectionFactory") RedisConnectionFactory treeConnectionFactory) {
		RedisTemplate<String, String> template = new RedisTemplate<>();
		template.setConnectionFactory(treeConnectionFactory);

		StringRedisSerializer stringSerializer = new StringRedisSerializer();
		template.setKeySerializer(stringSerializer);
		template.setValueSerializer(stringSerializer);
		template.setHashKeySerializer(stringSerializer);
		template.setHashValueSerializer(stringSerializer);
		template.afterPropertiesSet();
		return template;
	}

	/**
	 * 创建 RedissonClient，用于分布式锁等功能
	 */
	@Bean(destroyMethod = "shutdown")
	public RedissonClient redissonClient(TreeRedisProperties redisProperties) {
		Config config = new Config();
		String redisAddress = String.format("redis://%s:%s",
			redisProperties.getHost(),
			redisProperties.getPort());

		String password = redisProperties.getPassword();
		if (StrUtil.isBlank(password)) {
			password = null;
		}
		config.useSingleServer()
			.setAddress(redisAddress)
			.setPassword(password)
			.setDatabase(redisProperties.getDatabase())
			.setConnectionMinimumIdleSize(redisProperties.getPool().getMinIdle())
			.setConnectionPoolSize(redisProperties.getPool().getMaxActive())
			.setConnectTimeout((int) redisProperties.getTimeout())
			.setTimeout((int) redisProperties.getTimeout());

		return Redisson.create(config);
	}

	/**
	 * 创建 RediSearchClient，并明确指定它使用第二个数据源的连接工厂。
	 */
	@Bean(destroyMethod = "shutdown")
	public RediSearchClient rediSearchClient(
		@Qualifier("treeLettuceConnectionFactory") LettuceConnectionFactory connectionFactory) {
		// RediSearch 官方推荐使用 RedissonClient 来集成
		Config config = new Config();
		String redisAddress = String.format("redis://%s:%s",
			connectionFactory.getHostName(),
			connectionFactory.getPort());

		config.useSingleServer()
			.setAddress(redisAddress)
			.setPassword(connectionFactory.getPassword())
			.setDatabase(connectionFactory.getDatabase());
		return new RediSearchClient(config);
	}




	@Bean
	public RedisScript<Long> clearKeysByPatternScript() {
		DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
		redisScript.setLocation(new ClassPathResource("lua/clear_keys_by_pattern.lua"));
		redisScript.setResultType(Long.class);
		return redisScript;
	}

	@Bean
	public RedisScript<String> getTreeByCreatorScript() {
		DefaultRedisScript<String> redisScript = new DefaultRedisScript<>();
		redisScript.setLocation(new ClassPathResource("lua/get_tree_by_creator.lua"));
		redisScript.setResultType(String.class);
		return redisScript;
	}



	@Bean
	public RedisScript<String> getDeptTreeWithExpandedNodesScript() {
		DefaultRedisScript<String> redisScript = new DefaultRedisScript<>();
		redisScript.setLocation(new ClassPathResource("lua/get_dept_tree_with_expanded_nodes.lua"));
		redisScript.setResultType(String.class);
		return redisScript;
	}

	@Bean
	public RedisScript<Long> updateDeviceStatusScript() {
		DefaultRedisScript<Long> redisScript = new DefaultRedisScript<>();
		redisScript.setLocation(new ClassPathResource("lua/update_device_online_status.lua"));
		redisScript.setResultType(Long.class);
		return redisScript;
	}



}
