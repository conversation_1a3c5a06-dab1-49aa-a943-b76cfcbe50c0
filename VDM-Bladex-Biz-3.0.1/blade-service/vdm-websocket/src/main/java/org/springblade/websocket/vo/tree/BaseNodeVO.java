package org.springblade.websocket.vo.tree;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.Data;

import java.util.List;

@Data
@JsonTypeInfo(
	use = JsonTypeInfo.Id.NAME,
	include = JsonTypeInfo.As.EXISTING_PROPERTY,
	property = "type",
	visible = true
)
@JsonSubTypes({
	@JsonSubTypes.Type(value = DeptNodeVO.class, name = "dept"),
	@JsonSubTypes.Type(value = DeviceTypeNodeVO.class, name = "device_type"),
	@JsonSubTypes.Type(value = TargetNodeVO.class, name = "target"),
	@JsonSubTypes.Type(value = DeviceNodeVO.class, name = "device"),
	@JsonSubTypes.Type(value = ChannelNodeVO.class, name = "channel")
})
public class BaseNodeVO {

	private String id;
	@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
	private Long deptId;
	private String name;
	private String type;
	private String parentId;
	private List<BaseNodeVO> children;

	/**
	 * 是否已展开（前端状态标识）
	 */
	@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
	private Boolean expanded;

	/**
	 * 是否叶子节点
	 */
	// private Boolean leaf ;

}
