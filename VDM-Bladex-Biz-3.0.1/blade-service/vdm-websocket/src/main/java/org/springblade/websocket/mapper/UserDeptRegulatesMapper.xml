<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.websocket.mapper.UserDeptRegulatesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userDeptRegulatesResultMap" type="org.springblade.websocket.vo.UserDeptRegulatesVO">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="dept_id" property="deptId"/>
    </resultMap>


    <select id="selectUserDeptRegulatesPage" resultMap="userDeptRegulatesResultMap">
        select * from bdm_user_dept_regulates where is_deleted = 0
    </select>

</mapper>
