package org.springblade.websocket.dto.tree;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部门节点
 */
@Data
@EqualsAndHashCode(callSuper = true) // 在比较时包含父类的字段
public class DeptNode extends BaseNode {

	/**
     * 该部门下终端设备的总数
	 */
	private Long total = 0L;

	/**
     * 该部门下在线终端设备的总数
	 */
	private Long onlineNum = 0L;

	/**
	 * 该部门直属（不含子部门）的终端设备总数
	 */
	private Long selfTotal = 0L;

	/**
	 * 该部门直属（不含子部门）的在线终端设备总数
	 */
	private Long selfOnlineNum = 0L;
}
