/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.websocket.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
@TableName("bdm_user_dept_regulates")
@ApiModel(value = "UserDeptRegulates对象", description = "UserDeptRegulates对象")
public class UserDeptRegulates implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，唯一标识码
     */
    @ApiModelProperty(value = "主键，唯一标识码")
    private Long id;
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;
    /**
     * 被监管部门ID
     */
    @ApiModelProperty(value = "被监管部门ID")
    private Long deptId;


}
