package org.springblade.websocket.util;

import java.math.BigDecimal;

/**
 * @Description: 数学计算相关工具类
 * @Author: zhouxw
 * @Date: 2022/9/6 5:24 PM
 */
public class MathUtil {

    /**
     * @description: 两数相除，四舍五入保留小数
     * @author: zhouxw
     * @date: 2022/9/6 5:28 PM
     * @param: [part 分子, all 分母, decimalCount 小数点位数]
     * @return: double
     **/
    public static double divideRoundDouble(double part , double all , int decimalCount){
        double rate = (double) part / all;
        BigDecimal b = new BigDecimal(rate);
        double decimal = b.setScale(decimalCount, BigDecimal.ROUND_HALF_UP).doubleValue();
        return decimal;
    }

    /**
     * @description: 对指定的小数，四舍五入保留小数
     * @author: zhouxw
     * @date: 2022/9/6 5:36 PM
     * @param: [d, decimalCount]
     * @return: double
     **/
    public static double roundDouble (double d , int decimalCount){
        BigDecimal b = new BigDecimal(d);
        double decimal = b.setScale(decimalCount, BigDecimal.ROUND_HALF_UP).doubleValue();
        return decimal;
    }

    /**
     * @description: 数据格式化为百分数形式
     * @author: zhouxw
     * @date: 2022/9/22 5:26 PM
     * @param: [data 数据   decimalCount 小数位数]
     * @return: java.lang.String
     **/
    public static String formatToPercent(double data , int decimalCount){
        data = data * 100;
        data = roundDouble(data , decimalCount + 2);
        String percentString = data + "%";
        return percentString;
    }


}
