/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.websocket.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.websocket.entity.PntDevice;
import org.springblade.websocket.vo.PntDeviceVO;
import org.springblade.websocket.service.IPntDeviceService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@RestController
@AllArgsConstructor
@RequestMapping("vdm-websocket/pntdevice")
@Api(value = "", tags = "接口")
public class PntDeviceController extends BladeController {

	private IPntDeviceService pntDeviceService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperation(value = "详情", notes = "传入pntDevice")
	public R<PntDevice> detail(PntDevice pntDevice) {
		PntDevice detail = pntDeviceService.getOne(Condition.getQueryWrapper(pntDevice));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperation(value = "分页", notes = "传入pntDevice")
	public R<IPage<PntDevice>> list(PntDevice pntDevice, Query query) {
		IPage<PntDevice> pages = pntDeviceService.page(Condition.getPage(query), Condition.getQueryWrapper(pntDevice));
		return R.data(pages);
	}

	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperation(value = "分页", notes = "传入pntDevice")
	public R<IPage<PntDeviceVO>> page(PntDeviceVO pntDevice, Query query) {
		IPage<PntDeviceVO> pages = pntDeviceService.selectPntDevicePage(Condition.getPage(query), pntDevice);
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperation(value = "新增", notes = "传入pntDevice")
	public R save(@Valid @RequestBody PntDevice pntDevice) {
		return R.status(pntDeviceService.save(pntDevice));
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperation(value = "修改", notes = "传入pntDevice")
	public R update(@Valid @RequestBody PntDevice pntDevice) {
		return R.status(pntDeviceService.updateById(pntDevice));
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperation(value = "新增或修改", notes = "传入pntDevice")
	public R submit(@Valid @RequestBody PntDevice pntDevice) {
		return R.status(pntDeviceService.saveOrUpdate(pntDevice));
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(pntDeviceService.removeByIds(Func.toLongList(ids)));
	}


}
