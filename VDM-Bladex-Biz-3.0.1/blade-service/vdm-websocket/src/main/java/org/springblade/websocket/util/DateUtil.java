package org.springblade.websocket.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @Description: 日期工具类
 * @Author: zhouxw
 * @Date: 2022/11/9 9:07 AM
 */
public class DateUtil {

    //考虑多线程使用的情况，使用ThreadLocal
    public static final ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    public static final ThreadLocal<SimpleDateFormat> sdfHolderShort = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    public static final ThreadLocal<SimpleDateFormat> sdfHolderMonth = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM"));

    /**
     * @description: 根据给定的时间戳（精确到秒）获取位于当前天的第几个小时
     * 注意：10:05 应属于 11 点，所以，会返回 11
     * 注意：如果超过了23点，会返回24；超过了0点，会返回1
     * @author: zhouxw
     * @date: 2022/11/9 9:11 AM
     * @param: [secondTimestamp]
     * @return: int
     **/
    public static int getHour(long secondTimestamp){
        Date date = new Date();
        date.setTime(secondTimestamp * 1000);
        String dateStr = sdfHolder.get().format(date);
        String hourStr = dateStr.substring(11,13);
        return Integer.parseInt(hourStr)+1;
    }

    /**
     * @description: 获取给定时间戳所在的整点时间戳
     * 如给定 2022-10-08 10:25:30 的时间戳，那么就返回 2022-10-08 10:00:00 的时间戳，精确到 秒
     * @author: zhouxw
     * @date: 2022/11/9 9:39 AM
     * @param: [secondTimestamp]
     * @return: long
     **/
    public static long getSecondTimestampAtHour(long secondTimestamp) throws ParseException {
        Date date = new Date();
        date.setTime(secondTimestamp * 1000);
        String dateStr = sdfHolder.get().format(date);
        dateStr = dateStr.substring(0 , 13) + ":00:00";
        long timestamp = sdfHolder.get().parse(dateStr).getTime() / 1000;
        return timestamp;
    }

    /**
     * @description: 查询两个给定的时间戳(精确到秒)相差几个月
     * 包含首尾，所以，最后需要多加1
     * @author: zhouxw
     * @date: 2022/11/9 4:36 PM
     * @param: [startSecondTimestamp, endSecondTimestamp]
     * @return: int
     **/
    public static int monthCountBetweenSecondTimestamp(long startSecondTimestamp , long endSecondTimestamp){
        Date startDate = new Date();
        startDate.setTime(startSecondTimestamp * 1000);
        String startStr = sdfHolder.get().format(startDate).substring( 0 , 7 );
        Date endDate = new Date();
        endDate.setTime(endSecondTimestamp * 1000);
        String endStr = sdfHolder.get().format(endDate).substring( 0 , 7 );
        int startYear = Integer.parseInt(startStr.split("-")[0]);
        int startMonth = Integer.parseInt(startStr.split("-")[1]);
        int endYear = Integer.parseInt(endStr.split("-")[0]);
        int endMonth = Integer.parseInt(endStr.split("-")[1]);
        return (endYear - startYear) * 12 + (endMonth - startMonth) + 1;
    }


    /**
     * @description: 查询两个给定的时间戳(精确到秒)相差几年
     * 包含首尾，所以，最后需要多加1
     * @author: zhouxw
     * @date: 2022/11/9 4:36 PM
     * @param: [startSecondTimestamp, endSecondTimestamp]
     * @return: int
     **/
    public static int yearCountBetweenSecondTimestamp(long startSecondTimestamp , long endSecondTimestamp){
        Date startDate = new Date();
        startDate.setTime(startSecondTimestamp * 1000);
        String startStr = sdfHolder.get().format(startDate).substring( 0 , 7 );
        Date endDate = new Date();
        endDate.setTime(endSecondTimestamp * 1000);
        String endStr = sdfHolder.get().format(endDate).substring( 0 , 7 );
        int startYear = Integer.parseInt(startStr.split("-")[0]);
        int endYear = Integer.parseInt(endStr.split("-")[0]);
        return (endYear - startYear);
    }


    /**
     * @description: 查询两个给定的时间戳(精确到秒)相差几天
     * 包含首尾，所以，最后需要多加1
     * @author: zhouxw
     * @date: 2022/11/9 4:36 PM
     * @param: [startSecondTimestamp, endSecondTimestamp]
     * @return: int
     **/
    public static int getDayCountBetweenSecondTimestamp(long startSecondTimestamp , long endSecondTimestamp) throws ParseException {
        //判断两个时间戳是否是同一个月
        Date startDate = new Date();
        startDate.setTime(startSecondTimestamp * 1000);
        String startStr = sdfHolder.get().format(startDate).substring( 0 , 7 );
        Date endDate = new Date();
        endDate.setTime(endSecondTimestamp * 1000);
        String endStr = sdfHolder.get().format(endDate).substring( 0 , 7 );
        if(startStr.equals(endStr)){
            //如果是同一个月
            return Integer.parseInt(sdfHolder.get().format(endDate).substring( 8 , 10 )) - Integer.parseInt(sdfHolder.get().format(startDate).substring( 8 , 10 ));
        }else{
            //如果不是同一个月
            Calendar cal = Calendar.getInstance();
            cal.setTime(startDate);
            int count = 0;
            while(getDayFirstSecondTimestamp(cal.getTimeInMillis() / 1000) < getDayFirstSecondTimestamp(endSecondTimestamp)){
                count++;
                cal.add(Calendar.DAY_OF_MONTH , 1);
            }
            return count;
        }

    }



    /**
     * @description: 根据给定的时间戳获取当月最后的时间戳（精确到秒）
     * @author: zhouxw
     * @date: 2022/11/9 5:21 PM
     * @param: [secondTimestamp]
     * @return: long
     **/
    public static long getMonthLastSecondTimestamp(long secondTimestamp){
        Calendar cal = Calendar.getInstance();
        Date startDate = new Date();
        startDate.setTime(secondTimestamp * 1000);
        cal.setTime(startDate);
        cal.set(Calendar.DAY_OF_MONTH , cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY , cal.getActualMaximum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE , cal.getActualMaximum(Calendar.MINUTE));
        cal.set(Calendar.SECOND , cal.getActualMaximum(Calendar.SECOND));
        long lastSecondTimestamp = cal.getTimeInMillis() / 1000;
        return lastSecondTimestamp;
    }

    /**
     * @description: 根据给定的时间戳获取当月最初的时间戳（精确到秒）
     * @author: zhouxw
     * @date: 2022/11/14 6:47 PM
     * @param: [secondTimestamp]
     * @return: long
     **/
    public static long getMonthFirstSecondTimestamp(long secondTimestamp){
        Calendar cal = Calendar.getInstance();
        Date startDate = new Date();
        startDate.setTime(secondTimestamp * 1000);
        cal.setTime(startDate);
        cal.set(Calendar.DAY_OF_MONTH , cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.HOUR_OF_DAY , cal.getActualMinimum(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE , cal.getActualMinimum(Calendar.MINUTE));
        cal.set(Calendar.SECOND , cal.getActualMinimum(Calendar.SECOND));
        long lastSecondTimestamp = cal.getTimeInMillis() / 1000;
        return lastSecondTimestamp;
    }

    /**
     * @description: 根据给定的时间戳获取当天最开始的时间戳（精确到秒）
     * @author: zhouxw
     * @date: 2022/11/14 6:48 PM
     * @param: [secondTimestamp 精确到秒的时间戳]
     * @return: long
     **/
    public static long getDayFirstSecondTimestamp(long secondTimestamp) throws ParseException {
        Date date = new Date();
        date.setTime(secondTimestamp * 1000);
        String dateStr = sdfHolder.get().format(date).substring(0,10)+" 00:00:00";
        long firstSecondTimestamp = sdfHolder.get().parse(dateStr).getTime() / 1000;
        return firstSecondTimestamp;
    }


    /**
     * @description: 根据给定的时间戳获取当天最后的时间戳（精确到秒）
     * @author: zhouxw
     * @date: 2022/11/14 6:48 PM
     * @param: [secondTimestamp]
     * @return: long
     **/
    public static long getDayLastSecondTimestamp(long secondTimestamp) throws ParseException {
        Date date = new Date();
        date.setTime(secondTimestamp * 1000);
        String dateStr = sdfHolder.get().format(date).substring(0,10)+" 23:59:59";
        long lastSecondTimestamp = sdfHolder.get().parse(dateStr).getTime() / 1000;
        return lastSecondTimestamp;
    }

    /**
     * @description: 获取上个月的月份日期表示
     * 返回格式 yyyy-MM
     * @author: zhouxw
     * @date: 2022/11/16 8:56 AM
     * @param: []
     * @return: java.lang.String
     **/
    public static String getLastMonthString(){
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.MONTH , -1);
        return cal.get(Calendar.YEAR) + "-" + (cal.get(Calendar.MONTH) + 1);
    }

    /**
     * @description: 获取指定日期的上个月的月份
     * 返回格式 yyyy-MM
     * @author: zhouxw
     * @date: 2022/11/28 12:50 AM
     * @param: [month: yyyy-MM]
     * @return: java.lang.String
     **/
    public static String getLastMonthString(String month) throws Exception{
        Date date = sdfHolderMonth.get().parse(month);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH , -1);
        String monthPre = cal.get(Calendar.YEAR) + "-" + ((cal.get(Calendar.MONTH ) + 1)<10?"0"+(cal.get(Calendar.MONTH ) + 1):(cal.get(Calendar.MONTH ) + 1));
        return monthPre;
    }

    /**
     * @description: 获取前一年相同月份的字符串表示
     * 如指定月份为 2022-09 ，则返回 2021-09
     * @author: zhouxw
     * @date: 2022/11/28 1:00 AM
     * @param: [month: yyyy-MM]
     * @return: java.lang.String
     **/
    public static String getLastYearMonthString(String month) throws Exception{
        Date date = sdfHolderMonth.get().parse(month);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.YEAR , -1);
        String monthPre = cal.get(Calendar.YEAR) + "-" + ((cal.get(Calendar.MONTH ) + 1)<10?"0"+(cal.get(Calendar.MONTH ) + 1):(cal.get(Calendar.MONTH ) + 1));
        return monthPre;
    }

    /**
     * @description: 获取指定月份的天数
     * @author: zhouxw
     * @date: 2022/11/28 1:25 AM
     * @param: [month：yyyy-MM]
     * @return: int
     **/
    public static int getDaysCountInMonth(String month) throws Exception{
        Date date = sdfHolderMonth.get().parse(month);
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int days = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        return days;
    }

    /**
     * @description: 获取给定日期的字符串表示
     * 返回字符串格式： yyyy-MM-dd
     * @author: zhouxw
     * @date: 2022/11/27 12:34 AM
     * @param: [secondTimestamp]
     * @return: java.lang.String
     **/
    public static String getDateString(long secondTimestamp){
        Date date = new Date();
        date.setTime(secondTimestamp * 1000);
        return sdfHolderShort.get().format(date);
    }

	/**
	 * @description: 获取当前日期的字符串表示
	 * 返回字符串格式： yyyy-MM-dd
	 * @author: zhouxw
	 * @date: 2022/11/27 12:34 AM
	 * @param: [secondTimestamp]
	 * @return: java.lang.String
	 **/
	public static String getDateString(){
		Date date = new Date();
		return sdfHolderShort.get().format(date);
	}

    /**
     * @description: 获取下个月第一天的日期
     * 日期格式：yyyy-MM-dd
     * @author: zhouxw
     * @date: 2022/11/21 8:45 AM
     * @param: [secondTimestampInThisMonth]
     * @return: java.lang.String
     **/
    public static String getNextMonthFirstDay(long secondTimestampInThisMonth){
        long nextMonthFirstSecond = getMonthLastSecondTimestamp(secondTimestampInThisMonth) + 1;
        return sdfHolderShort.get().format(nextMonthFirstSecond * 1000);
    }


    /**
     * @description: 获取指定时间段内的日期列表
     * @author: zhouxw
     * @date: 2023-03-68 15:40:42
     * @param: [startDate：开始日期 yyyy-MM-dd , endDate：结束日期 yyyy-MM-dd]
     * @return: 日期列表，包含首尾, yyyy-MM-dd
     **/
    public static List<String> getDateList(String startDate, String endDate) throws Exception{
        Date startDateTime = sdfHolderShort.get().parse(startDate);
        Date endDateTime = sdfHolderShort.get().parse(endDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDateTime);
        List<String> dateList = new ArrayList<>();
        long tmpDateMillTIme = calendar.getTimeInMillis();
        while(tmpDateMillTIme <= endDateTime.getTime()){
            int month = calendar.get(Calendar.MONTH)+1;
            int date = calendar.get(Calendar.DATE);
            String monthStr = "";
            String dateStr = "";
            if(month < 10){
                monthStr = "0"+month;
            }else{
                monthStr = month + "";
            }
            if(date < 10){
                dateStr = "0" + date;
            }else{
                dateStr = date + "";
            }
            dateList.add(calendar.get(Calendar.YEAR)+"-"+monthStr+"-"+dateStr);
            calendar.add(Calendar.DATE, 1);
            tmpDateMillTIme = calendar.getTimeInMillis();
        }
        return dateList;
    }

    /**
     * @description: 获取指定时间段内的月份列表
     * @author: zhouxw
     * @date: 2023-03-68 15:40:42
     * @param: [startDate：开始日期 yyyy-MM-dd , endDate：结束日期 yyyy-MM-dd]
     * @return: 月份列表，包含首尾, yyyy-MM
     **/
    public static List<String> getMonthList(String startDate, String endDate) throws Exception{
        Date startDateTime = sdfHolderShort.get().parse(startDate);
        Date endDateTime = sdfHolderShort.get().parse(endDate);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDateTime);
        List<String> dateList = new ArrayList<>();
        long tmpDateMillTIme = calendar.getTimeInMillis();
        while(tmpDateMillTIme <= endDateTime.getTime()){
            int month = calendar.get(Calendar.MONTH)+1;
            String monthStr = "";
            if(month < 10){
                monthStr = "0" + month;
            }else{
                monthStr = month + "";
            }
            dateList.add(calendar.get(Calendar.YEAR) + "-" + monthStr);
            calendar.add(Calendar.MONTH, 1);
            tmpDateMillTIme = calendar.getTimeInMillis();
        }
        return dateList;
    }

    /**
     * @description: 获取指定时间段内的月份列表
     * @author: zhouxw
     * @date: 2023-03-68 15:40:42
     * @param: [startSecondTimestamp：开始时间，精确到秒的时间戳 , endSecondTimestamp：结束时间，精确到秒的时间戳]
     * @return: 月份列表，包含首尾, yyyy-MM
     **/
    public static List<String> getMonthList(long startSecondTimestamp, long endSecondTimestamp) throws Exception{
        Date startDateTime = new Date(startSecondTimestamp * 1000);
        Date endDateTime = new Date(endSecondTimestamp * 1000);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDateTime);
        List<String> dateList = new ArrayList<>();
        long tmpDateMillTIme = calendar.getTimeInMillis();
        int monthCount = monthCountBetweenSecondTimestamp(startSecondTimestamp, endSecondTimestamp);
        for(int i = 0 ; i < monthCount; i++){
            int month = calendar.get(Calendar.MONTH)+1;
            String monthStr = "";
            if(month < 10){
                monthStr = "0" + month;
            }else{
                monthStr = month + "";
            }
            dateList.add(calendar.get(Calendar.YEAR)+"-"+monthStr);
            calendar.add(Calendar.MONTH, 1);
        }
        return dateList;
    }


	/**
	 * @description: 获取 指定日期 指定月份 之后的日期
	 * 如果不指定日期，则默认为当前时间
	 * @author: zhouxw
	 * @date: 2023-07-205 21:34:29
	 * @param: [date, monthCount]
	 * @return: java.util.Date
	 **/
	public static Date getDateAfterMonth(Date date, int monthCount) throws Exception{
		if(date == null){
			date = new Date();
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MONTH, monthCount);
		return calendar.getTime();
	}


	/**
	 * @description: 获取 指定日期 指定天数 之后的日期
	 * 如果不指定日期，则默认为当前时间
	 * @author: zhouxw
	 * @date: 2023-07-205 21:34:29
	 * @param: [date, monthCount]
	 * @return: java.util.Date
	 **/
	public static Date getDateAfterDay(Date date, int dayCount) throws Exception{
		if(date == null){
			date = new Date();
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, dayCount);
		return calendar.getTime();
	}

	/**
	 * @description: 获取 指定日期 指定天数 之前的日期
	 * 如果不指定日期，则默认为当前时间
	 * @author: zhouxw
	 * @date: 2023-07-205 21:34:29
	 * @param: [date, monthCount]
	 * @return: java.util.Date
	 **/
	public static Date getDateBeforeDay(Date date, int dayCount) throws Exception{
		if(date == null){
			date = new Date();
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.DATE, -1 * dayCount);
		return calendar.getTime();
	}

	/**
	 * @description: 获取 指定日期 指定天数 之前的日期
	 * 如果不指定日期，则默认为当前时间
	 * @author: zhouxw
	 * @date: 2023-07-205 21:34:29
	 * @param: [date, monthCount]
	 * @return: java.util.Date
	 **/
	public static String getDateBeforeDayStr(Date date, int dayCount) throws Exception{
		Date dateBefore = getDateBeforeDay(date, dayCount);
		String dateStr = sdfHolderShort.get().format(dateBefore);
		return dateStr;
	}




/*

    public static void main(String[] args) throws Exception {
        Date date = new Date();
		String dateBefore = getDateBeforeDayStr(date, 10);
		System.out.println(dateBefore);
    }
*/




}
