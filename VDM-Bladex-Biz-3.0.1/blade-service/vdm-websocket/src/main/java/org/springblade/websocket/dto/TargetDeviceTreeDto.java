package org.springblade.websocket.dto;

import lombok.Data;

/**
 * 终端树数据
 */
@Data
public class TargetDeviceTreeDto {
    /**
     * 终端id
     */
    private Long id;
    /**
     * 监控对象id
     */
    private Long targetId;

    /**
     * 监控对象名称
     */
    private String name;

    /**
     * 创建账户
     */
    private String createAccount;

    /**
     * 终端种类/功能类型
     * 1-定位终端，2-视频终端，3-智能终端，4-工卡，5-集装箱追踪器等
     */
    private Integer category;

    /**
     * 终端类别
     * 1-北斗定位终端，2-北斗穿戴式终端，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端
     */
    private Integer deviceType;

    /**
     * 所属部门ID
     */
    private Long deptId;

    /**
     * 视频通道个数（视频终端和智能终端特有）
     */
    private Integer channelNum;

    /**
     * 在线状态
     * 0-上线，1-下线
     */
    private Integer online;

    /**
     * 融合状态（运动状态）
     * 0-静止，1-运动
     */
    private Integer fusionState;

    /**
     * 设备唯一标识码（终端号）
     */
    private String uniqueId;

    /**
     * 型号
     */
    private String model;

    /**
     * ACC状态(0-关闭,1-开启)
     */
    private Integer acc = -1;
}
