package org.springblade.websocket.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 位置数据事件DTO
 * 用于ce.comms.fct.location.0消息
 */
@Data
public class LocationEvent {

	@JsonProperty("device_id")
	private String deviceId;        // 设备ID

	@JsonProperty("device_type")
	private Integer deviceType;       // 设备类型

	@JsonProperty("device_model")
	private String deviceModel;   // 设备型号

	@JsonProperty("device_no")
	private String deviceNo;      // 设备编号，原终端手机号

	@JsonProperty("device_unique_id")
	private String deviceUniqueId;

	@JsonProperty("target_id")
	private String targetId;        // 目标ID

	@JsonProperty("target_type")
	private Integer targetType;       // 目标类型

	@JsonProperty("target_name")
	private String targetName;    // 目标名称

	@JsonProperty("device_num")
	private String deviceNum;     // 赋码字段

	@JsonProperty("dept_id")
	private Long deptId;          // 监控对象所属部门id

	@JsonProperty("Longitude")
	private Double Longitude;     // 经度

	@JsonProperty("latitude")
	private Double latitude;      // 纬度

	@JsonProperty("altitude")
	private Integer altitude;         // 高程

	@JsonProperty("speed")
	private Float speed;          // 卫星速度，单位km/h

	@JsonProperty("bearing")
	private Integer bearing;          // 方位角

	@JsonProperty("alarm_flag")
	private Long alarmFlag;       // 告警标识

	@JsonProperty("state_flag")
	private Long stateFlag;       // 状态标识

	@JsonProperty("loc_time")
	private Long locTime;         // 定位时间

	@JsonProperty("recv_time")
	private Long recvTime;        // 接收数据时间

	@JsonProperty("valid")
	private Short valid;          // 定位有效性，0-无效，1-有效 2-纠偏点 3-漂移点（非法点）

	@JsonProperty("pos_system")
	private Short posSystem;

	@JsonProperty("mileage")
	private Float mileage;        // 终端上传里程 OBD

	@JsonProperty("real_speed")
	private Float realSpeed;      // 行驶记录仪速度 OBD

	@JsonProperty("io_status")
	private Integer ioStatus;         // IO状态位 OBD

	@JsonProperty("oil_mass")
	private Float oilMass;        // 油量 OBD

	@JsonProperty("gnss_num")
	private Byte gnssNum;         // GNSS定位卫星数

	@JsonProperty("wireless")
	private Byte wireless;        // 无线通信网络信号强度

	@JsonProperty("expand_signal")
	private Long expandSignal;    // 扩展车辆信号状态位

	@JsonProperty("temperature")
	private String temperature;   // 温度

	@JsonProperty("batch")
	private Byte batch;           // 0-位置信息，1-批量位置信息

	// @JsonProperty("extra")
	// private Map<String, Object> extra;  // 扩展字段

	@JsonProperty("create_time")
	@JsonSerialize(using = LocalDateTimeSerializer.class)
	@JsonDeserialize(using = LocalDateTimeDeserializer.class)
	private LocalDateTime createTime;   // 创建时间

}
