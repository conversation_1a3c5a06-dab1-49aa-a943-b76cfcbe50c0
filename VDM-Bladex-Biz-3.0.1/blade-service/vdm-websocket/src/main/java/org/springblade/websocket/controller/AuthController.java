package org.springblade.websocket.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.PermissionType;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.entity.DataAuthCE;
import org.springblade.websocket.constant.RedisConstant;
import org.springblade.websocket.event.AlarmRemindWebsocketListener;
import org.springblade.websocket.event.DeviceOnOffWebsocketListener;
import org.springblade.websocket.event.LocationTrackWebsocketListener;
import org.springblade.websocket.event.TreeWebsocketListener;
import org.springblade.websocket.service.IBdmAbstractDeviceService;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;

@Slf4j
@Api(tags = "websocket连接权限", protocols = "http", produces = "application/json", consumes = "application/json")
@RestController
@RequestMapping("/authWS")
public class AuthController {

	// websocket连接监听器：设备上下线通知
	@Resource
	private DeviceOnOffWebsocketListener deviceOnOffWebsocketListener;

	// websocket连接监听器：告警通知
	@Resource
	private AlarmRemindWebsocketListener alarmRemindWebsocketListener;

	// websocket连接监听器：位置跟踪
	@Resource
	private LocationTrackWebsocketListener locationTrackWebsocketListener;

	// websocket连接监听器：终端树
	@Resource
	private TreeWebsocketListener treeWebsocketListener;

	@Resource
	private IBdmAbstractDeviceService abstractDeviceService;


	@Resource
	private CETokenUtil ceTokenUtil;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@ApiOperation(value = "用户鉴权与建立连接", httpMethod = "GET")
	@GetMapping("/authCode")
	public R<String> authCode() {
		Long userId = AuthUtil.getUserId();
		String userIdStr = String.valueOf(userId);
		boolean isAdmin = ceTokenUtil.isAdmin();
		String userCode = UUID.randomUUID().toString().replace("-", "");
		log.info("create userCode: {} for userId: {} when auth and build websocket conn", userCode, userId);

		// 设备上下线通知
		this.deviceOnOffWebsocketListener.addUserCodeSet(userCode);
		this.deviceOnOffWebsocketListener.putUserCodeMap(userIdStr, userCode);
		if (isAdmin) {
			this.deviceOnOffWebsocketListener.addAdminIdSet(userId);
		}

		// 告警通知
		this.alarmRemindWebsocketListener.addUserCodeSet(userCode);
		this.alarmRemindWebsocketListener.putUserCodeMap(userIdStr, userCode);
		if (isAdmin) {
			this.alarmRemindWebsocketListener.addAdminIdSet(userId);
		}

		// 位置跟踪
		this.locationTrackWebsocketListener.addUserCodeSet(userCode);
		this.locationTrackWebsocketListener.putUserCodeMap(userIdStr, userCode);

		if (isAdmin) {
			this.locationTrackWebsocketListener.addAdminIdSet(userId);
		}

		// 树结构
		this.treeWebsocketListener.addUserCodeSet(userCode);
		TreeWebsocketListener.USER_CODE_BLADE_USER_MAP.put(userCode, AuthUtil.getUser());
		if (isAdmin) {
			this.treeWebsocketListener.addAdminIdSet(userId);
		}

		if (isAdmin) {
			// 如果是超级管理员或者管理员，记录下userCode，后期全部推送
			stringRedisTemplate.opsForSet().add(RedisConstant.KEY_ADMIN_USER_CODE_SET, userCode);
			TreeWebsocketListener.USER_CODE_PERMISSION_TYPE_MAP.put(userCode,PermissionType.ADMIN);
		} else {
			// 如果是国能底座登录或者admin登录
			// 添加逻辑 @zhouxw 因为国能底座登录时，数据权限是外部传入的，没有使用 user_dept_regulates，所以这里做特殊处理，记录国能的数据权限中终端与userCode的映射
			List<Long> deviceIdList = new ArrayList<>();
			// @zhouxw 兼容非底座非admin
			// if(ceTokenUtil.isCELogin()){
			DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
			String dataType = dataAuthCE.getGnDataAuthType();
			String createAccount = "";
			String deptIdArray = "";
			if (dataType.equals(DataAuthCE.CE_DATA_AUTH_TYPE_SELF)) {
				// 如果是本人
				createAccount = dataAuthCE.getAccount();
				TreeWebsocketListener.USER_CODE_PERMISSION_TYPE_MAP.put(userCode,PermissionType.USER);
			} else {
				// 如果是组织
				deptIdArray = ceTokenUtil.getDataAuth().getOrgListStr();
				TreeWebsocketListener.USER_CODE_PERMISSION_TYPE_MAP.put(userCode,PermissionType.DEPT);
				TreeWebsocketListener.USER_CODE_DEPT_SET_MAP.put(userCode,new HashSet<>(ceTokenUtil.getDataAuth().getOrgList()));
			}
			try {
				deviceIdList = abstractDeviceService.findDeivceIdList(deptIdArray, createAccount);
			} catch (Exception e) {
				log.error("根据国能数据权限，查询deviceId列表失败", e);
			}
			// 记录deviceNum与userCode的映射关系，方便后续推送
			List<Long> finalDeviceIdList = deviceIdList;
			stringRedisTemplate.executePipelined(new SessionCallback<Object>() {
				@Override
				public Object execute(RedisOperations operations) {
					for (Long deviceId : finalDeviceIdList) {
						operations.opsForSet().add(RedisConstant.KEY_DEVICE_Id_USER_CODE_PREFIX + deviceId, userCode);
					}
					return null;
				}
			});
			// }
		}

		return R.data(userCode);
	}
}
