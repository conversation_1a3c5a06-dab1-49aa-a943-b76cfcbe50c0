package org.springblade.websocket.event;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.entity.Location;
import org.springblade.websocket.constant.RedisConstant;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

// websocket连接监听器：位置跟踪
@Slf4j
@Component
@ServerEndpoint("/ws/locationTrack/push/{userCode}")
public class LocationTrackWebsocketListener {

	// 需要建立连接，以推送消息的userCode的列表。
	private static CopyOnWriteArraySet<String> userCodeSet = new CopyOnWriteArraySet<>();

	// 一个账号（以userId为标识）可能会同时在多个地方登录，每个登录态（以userCode为标识）都应接收到消息，因此，需要维护从userId到userCode的映射。
	// private static ConcurrentHashMap<String, List<String>> userCodeMap = new ConcurrentHashMap<>();
	private static Map<String, Set<String>> userCodeMap = new HashMap<>();

	// 每个登录态（以userCode为标识）对应的设备列表（每个设备信息包括设备类型、设备ID）
	// private static ConcurrentHashMap<String, List<String>> deviceMap = new ConcurrentHashMap<>();
	private static Map<String, Set<String>> deviceMap = new HashMap<>();

	// 当前所有连接的会话，以userCode为key。
	private static ConcurrentHashMap<String, Session> sessionPool = new ConcurrentHashMap<>();

	// 所有LocationTrackWebsocketListener对象的集合。
	// 虽然@Component默认是单例模式的，但SpringBoot还是会为每个websocket连接，初始化一个bean，所以可以用一个静态set保存起来，此处用concurrent包的线程安全Set。
	private static CopyOnWriteArraySet<LocationTrackWebsocketListener> webSockets = new CopyOnWriteArraySet<>();

	// 超管用户ID列表
	private static CopyOnWriteArraySet<Long> adminIdSet = new CopyOnWriteArraySet<>();

	// 当前连接的userCode
	private String userCode;

	// 当前连接的会话
	private Session session;

	@Resource
	private RedisTemplate<String, Object> redisTmp;

	// 在listener内仅凭@Autowired或@Resource，似乎无法注入RedisTemplate。
	public static RedisTemplate<String, Object> redisTemplate;

	@PostConstruct
	public void getRedisTemplate () {
		LocationTrackWebsocketListener.redisTemplate = this.redisTmp;
	}

	// 往userCodeSet添加userCode
	public void addUserCodeSet (String userCode) {
		LocationTrackWebsocketListener.userCodeSet.add(userCode);
	}

	public Set<String> getUserCodeSetByUserId(String userId){
		return LocationTrackWebsocketListener.userCodeMap.get(userId);
	}

	// 添加从userId到userCode的映射
	public void putUserCodeMap (String userId, String userCode) {
		if (!LocationTrackWebsocketListener.userCodeMap.containsKey(userId)) {
			LocationTrackWebsocketListener.userCodeMap.put(userId, new HashSet<>());
		}

		LocationTrackWebsocketListener.userCodeMap.get(userId).add(userCode);
	}

	// 往adminIdSet添加adminId
	public void addAdminIdSet (Long adminId) {
		LocationTrackWebsocketListener.adminIdSet.add(adminId);
	}

	// 连接建立成功时调用。
	@OnOpen
	public void onOpen (Session session, @PathParam(value="userCode") String userCode) {
		log.info("websocket conn built for send location track, userCode: {}", userCode);
		try {
			if (StringUtils.isBlank(userCode)) {
				log.error("empty userCode when onOpen for send location track");
				return;
			}

			this.session = session;
			this.userCode = userCode;
			LocationTrackWebsocketListener.sessionPool.put(userCode, session);
			LocationTrackWebsocketListener.webSockets.add(this);
		} catch (Exception e) {
			log.error("fail callback when websocket conn built for send location track, err: {}", e.getMessage(), e);
		}
	}

	// 连接关闭成功时调用。
	@OnClose
	public void onClose () {
		try {
			LocationTrackWebsocketListener.webSockets.remove(this);
			LocationTrackWebsocketListener.sessionPool.remove(this.userCode);
			LocationTrackWebsocketListener.deviceMap.remove(this.userCode);
			LocationTrackWebsocketListener.userCodeMap.remove(this.userCode);
			LocationTrackWebsocketListener.userCodeSet.remove(this.userCode);
			//删除redis中保存的数据
			redisTemplate.opsForSet().remove(RedisConstant.KEY_ADMIN_USER_CODE_SET, this.userCode);
			Set<String> keys = redisTemplate.execute((RedisCallback<Set<String>>) connection -> {
				Set<String> result = new HashSet<>();
				Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(RedisConstant.KEY_DEVICE_Id_USER_CODE_PREFIX + "*").count(1000).build());
				while (cursor.hasNext()) {
					result.add(new String(cursor.next()));
				}
				return result;
			});
			redisTemplate.executePipelined(new SessionCallback<Object>() {
				@Override
				public Object execute(RedisOperations operations) {
					if(keys != null && keys.size() > 0){
						for (String key : keys) {
							try {
								String deviceId = key.split(":")[1];
								operations.opsForSet().remove(RedisConstant.KEY_DEVICE_Id_USER_CODE_PREFIX + deviceId, userCode);
							}catch (Exception e){
								log.error("车辆跟踪，删除deviceId-userCode缓存失败",e);
							}
						}
					}
					return null;
				}
			});

		} catch (Exception e) {
			log.error("fail callback when websocket conn close for send location track, err: {}", e.getMessage(), e);
		}
	}

	// 收到客户端消息时调用。
	@OnMessage
	public void onMessage (String msg) {
		log.info("locationTrack>>websocket，收到前端发送的消息{}", msg);
		if (!LocationTrackWebsocketListener.deviceMap.containsKey(this.userCode)) {
			LocationTrackWebsocketListener.deviceMap.put(this.userCode, new HashSet<>());
		}

		LocationTrackWebsocketListener.deviceMap.get(this.userCode).add(msg);

		// 如果是来自公务车集成页面中，客户端推过来的请求，数据中会包含设备对应的用户ID，需要把这个对应关系缓存起来。
		JSONObject jo = JSON.parseObject(msg);
		if ((jo == null) || (!jo.containsKey("device_type")) || (!jo.containsKey("device_id"))) {
			log.error("invalid device when on message for send location track, msg: {}, userCode: {}", msg, this.userCode);
			return;
		}

		if (jo.containsKey("user_id")) {
			log.info("locationTrack>>websocket，公务车页面传过来的参数，将要向redis中添加内容 key = {}", RedisConstant.SET_OFFICIAL_DEVICE_USER + jo.get("device_type") + "-" + jo.get("device_id"), " value = {}", jo.get("user_id"));
			LocationTrackWebsocketListener.redisTemplate.opsForSet().add(
				RedisConstant.SET_OFFICIAL_DEVICE_USER + jo.get("device_type") + "-" + jo.get("device_id"),
				jo.get("user_id")
			);
		}
	}

	// 推送消息出错时调用。
	@OnError
	public void onError (Session session, Throwable e) {
		log.error("fail push when send location track, session: {}, err: {}", session, e.getMessage(), e);
	}

	// 向指定用户推送消息
	public void sendMsgToUser (List<Long> userList, String msg) {
		Location location = JSON.parseObject(msg, Location.class);
		if ((location == null) || (location.getDeviceType() == null) || (location.getDeviceId() == null)) {
			log.error("invalid msg when send location track: {}, userList: {}", msg, userList);
			return;
		}

		Set<Long> userSet = new HashSet<>(userList);
		if (CollectionUtils.isNotEmpty(LocationTrackWebsocketListener.adminIdSet)) {
			userSet.addAll(LocationTrackWebsocketListener.adminIdSet);
		}
		for (long userId : userSet) {
			Set<String> userCodeList = LocationTrackWebsocketListener.userCodeMap.get(String.valueOf(userId));
			if (CollectionUtils.isEmpty(userCodeList)) {
				continue;
			}
			for (String userCode : userCodeList) {
				Session session = LocationTrackWebsocketListener.sessionPool.get(userCode);
				if ((session == null) || (!session.isOpen())) {
					continue;
				}

				Set<String> deviceList = LocationTrackWebsocketListener.deviceMap.getOrDefault(userCode, new HashSet<>());
				for (String device : deviceList) {
					JSONObject jo = JSON.parseObject(device);
					if ((jo == null) || (!jo.containsKey("device_type")) || (!jo.containsKey("device_id"))) {
						log.error("invalid device when send location track: {}, userId: {}, userCode: {}, device: {}",
							msg, userId, userCode, device);
						continue;
					}

					byte deviceType = Byte.parseByte(jo.get("device_type").toString());
					long deviceId = Long.parseLong(jo.get("device_id").toString());
					if (location.getDeviceType().equals(deviceType) && location.getDeviceId().equals(deviceId)) {
						try {
							log.info("start send location track, userId: {}, userCode: {}, msg: {}", userId, userCode, msg);
							session.getAsyncRemote().sendText(msg);
							log.info("send location track done, userId: {}, userCode: {}, msg: {}", userId, userCode, msg);
						} catch (Exception e) {
							log.error("fail send location track: {}, userId: {}, userCode: {}, err: {}",
								msg, userId, userCode, e.getMessage(), e);
						}
					}
				}
			}
		}
	}


	/**
	 * 通过userCode获取session进行推送
	 * @param userCodes
	 * @param message
	 */
	public void sendMessageByUserCodes(Set<String> userCodes, String message){
		if(userCodes == null || userCodes.size() < 1){
			return ;
		}
		log.info("locationTrack>>将要推送到"+userCodes.size()+"个用户session中");
		for(String userCode : userCodes){
			Session session = LocationTrackWebsocketListener.sessionPool.get(userCode.toString());
			if(session != null){
				try {
					session.getAsyncRemote().sendText(message);
					log.info("locationTrack>>推送完成");
				}catch (Exception e){
					log.error("locationTrack>>执行推送动作失败", e);
					continue;
				}
			}

		}
	}

}
