/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.websocket.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.websocket.entity.RnssDevice;
import org.springblade.websocket.vo.RnssDeviceVO;
import org.springblade.websocket.service.IRnssDeviceService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@RestController
@AllArgsConstructor
@RequestMapping("vdm-websocket/rnssdevice")
@Api(value = "", tags = "接口")
public class RnssDeviceController extends BladeController {

	private IRnssDeviceService rnssDeviceService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperation(value = "详情", notes = "传入rnssDevice")
	public R<RnssDevice> detail(RnssDevice rnssDevice) {
		RnssDevice detail = rnssDeviceService.getOne(Condition.getQueryWrapper(rnssDevice));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperation(value = "分页", notes = "传入rnssDevice")
	public R<IPage<RnssDevice>> list(RnssDevice rnssDevice, Query query) {
		IPage<RnssDevice> pages = rnssDeviceService.page(Condition.getPage(query), Condition.getQueryWrapper(rnssDevice));
		return R.data(pages);
	}

	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperation(value = "分页", notes = "传入rnssDevice")
	public R<IPage<RnssDeviceVO>> page(RnssDeviceVO rnssDevice, Query query) {
		IPage<RnssDeviceVO> pages = rnssDeviceService.selectRnssDevicePage(Condition.getPage(query), rnssDevice);
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperation(value = "新增", notes = "传入rnssDevice")
	public R save(@Valid @RequestBody RnssDevice rnssDevice) {
		return R.status(rnssDeviceService.save(rnssDevice));
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperation(value = "修改", notes = "传入rnssDevice")
	public R update(@Valid @RequestBody RnssDevice rnssDevice) {
		return R.status(rnssDeviceService.updateById(rnssDevice));
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperation(value = "新增或修改", notes = "传入rnssDevice")
	public R submit(@Valid @RequestBody RnssDevice rnssDevice) {
		return R.status(rnssDeviceService.saveOrUpdate(rnssDevice));
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(rnssDeviceService.removeByIds(Func.toLongList(ids)));
	}


}
