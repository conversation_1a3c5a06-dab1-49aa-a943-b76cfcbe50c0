package org.springblade.websocket.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "返回体：告警通知")
@Data
public class AlarmRemindResponse {

	@JsonProperty("id")
	@ApiModelProperty(name = "id", value = "告警ID", example = "1", required = true)
	private Long id;

	@JsonProperty("target_name")
	@ApiModelProperty(name = "target_name", value = "目标名称", example = "粤A12345", required = true)
	private String targetName;

	@JsonProperty("unique_id")
	@ApiModelProperty(name = "unique_id", value = "序列号", example = "21124021522222255", required = true)
	private String uniqueId;

	@JsonProperty("device_type")
	@ApiModelProperty(name = "device_type", value = "设备类型（类型值与名称的映射，详见blade_dict_biz表code=bdm_device_type的记录）", example = "1", required = true)
	private Byte deviceType;

	@JsonProperty("type")
	@ApiModelProperty(name = "type", value = "告警类型（类型值与名称的映射，详见blade_dict_biz表code=alarm_type的记录）", example = "1", required = true)
	private Short type;

	@JsonProperty("level")
	@ApiModelProperty(name = "level", value = "告警等级（等级值与名称的映射，详见blade_dict_biz表code=alarm_level的记录）", example = "1", required = true)
	private Byte level;

	@JsonProperty("start_lon")
	@ApiModelProperty(name = "start_lon", value = "开始经度", example = "113.4531947602", required = true)
	private Double startLon;

	@JsonProperty("start_lat")
	@ApiModelProperty(name = "start_lat", value = "开始纬度", example = "23.1553043695", required = true)
	private Double startLat;

	@JsonProperty("start_addr")
	@ApiModelProperty(name = "start_addr", value = "开始地址", example = "广东省广州市黄埔区联和街道阅阳一街8号大壮国际广场", required = true)
	private String startAddr;

	@JsonProperty("start_time")
	@ApiModelProperty(name = "start_time", value = "开始时间（秒数）", example = "1717072620", required = true)
	private Long startTime;
}
