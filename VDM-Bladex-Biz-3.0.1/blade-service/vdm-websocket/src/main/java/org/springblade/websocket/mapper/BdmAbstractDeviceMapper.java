package org.springblade.websocket.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.websocket.entity.BdmAbstractDevice;

import java.util.List;

/**
 * (BdmAbstractDevice)表数据库访问层
 */
public interface BdmAbstractDeviceMapper extends BaseMapper<BdmAbstractDevice> {

	/**
	 * 根据给定的组织和创建账户查询deviceId
	 * @param deptIdArray
	 * @return
	 */
	List<Long> getDeviceIdList(@Param("deptIdArray") String deptIdArray, @Param("createAccount") String createAccount);
}

