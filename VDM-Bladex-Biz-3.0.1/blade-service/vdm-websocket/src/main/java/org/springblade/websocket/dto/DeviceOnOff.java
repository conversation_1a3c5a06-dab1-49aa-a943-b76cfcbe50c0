package org.springblade.websocket.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

// 消息体：设备上下线
@Data
public class DeviceOnOff {

	// 目标类型（类型值与名称的映射，详见blade_dict_biz表code=target_type的记录）
	@JsonProperty("target_type")
	private Byte targetType;

	// 目标ID
	@JsonProperty("target_id")
	private Long targetId;

	// 目标名称
	@JsonProperty("target_name")
	private String targetName;

	// 设备类型（类型值与名称的映射，详见blade_dict_biz表code=bdm_device_type的记录）
	@JsonProperty("device_type")
	private Byte deviceType;

	// 设备ID
	@JsonProperty("device_id")
	private Long deviceId;

	// 设备编号
	@JsonProperty("unique_id")
	private String uniqueId;

	// sim号
	@JsonProperty("phone")
	private String phone;

	// 设备赋码
	@JsonProperty("device_num")
	private String deviceNum;

	// 时间
	@JsonProperty("time")
	private String time;

	// 上下线状态（0：上线，1：下线）
	@JsonProperty("on_off_line")
	private Byte onOffLine;
}
