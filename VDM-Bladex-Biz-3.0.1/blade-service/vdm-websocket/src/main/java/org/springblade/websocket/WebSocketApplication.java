package org.springblade.websocket;

import org.mybatis.spring.annotation.MapperScan;
import org.springblade.common.constant.ApplicationConstant;
import org.springblade.core.cloud.client.BladeCloudApplication;
import org.springblade.core.launch.BladeApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

/**
 * WebSocketApplication
 *
 * <AUTHOR>
 */
@BladeCloudApplication
@EnableWebSocket
@EnableScheduling
@MapperScan("org.springblade.**.mapper")
public class WebSocketApplication {

	public static void main(String[] args) {
		String suffix = "";
		if(args != null && args.length > 0){
			for(String s : args){
				if(s.contains("suffix")){
					String param = s.split("=")[1].trim();
					suffix = "-" + param;
				}
			}
		}
		BladeApplication.run(ApplicationConstant.APPLICATION_WEBSOCKET+suffix, WebSocketApplication.class, args);
	}

}
