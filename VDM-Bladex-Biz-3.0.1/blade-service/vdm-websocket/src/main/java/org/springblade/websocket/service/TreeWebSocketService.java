package org.springblade.websocket.service;

import org.springblade.entity.Location;
import org.springblade.websocket.dto.DeviceOnOff;

/**
 * 树结构WebSocket推送服务接口
 */
public interface TreeWebSocketService {

    /**
     * 推送终端状态变化消息
     * @param event 终端状态事件
     */
    void pushOnlionChange(DeviceOnOff event);

    /**
     * 推送ACC状态变化消息
     * @param event 位置事件（包含ACC状态）
     */
    void pushAccStatusChange(Location event);

    /**
     * 推送设备变化消息
     * @param deviceId 设备ID
     * @param changeType 变化类型：ADD, UPDATE, DELETE
     */
    void pushDeviceChange(Long deviceId, String changeType);

    /**
     * 推送统计数据变化消息
     * @param deptId 部门ID
     * @param totalChange 总数变化
     * @param onlineChange 在线数变化
     */
    void pushStatisticsChange(String deptId, Long totalChange, Long onlineChange);
}
