package org.springblade.websocket.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.RedisConstant;
import org.springblade.entity.Alarm;
import org.springblade.websocket.event.AlarmRemindWebsocketListener;
import org.springblade.websocket.vo.response.AlarmRemindResponse;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

// 从kafka消费消息，并把数据推送给websocket，数据内容：告警通知
@Slf4j
@Component
@EnableKafka
public class AlarmRemindWebsocketConsumer extends WebsocketConsumer {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	@Resource
	private AlarmRemindWebsocketListener alarmRemindWebsocketListener;

	@KafkaListener(
		containerFactory = "alarmRemindKafkaListenerContainerFactory",
		topics = {"GuoNeng_alarm"},
		batch = "true"
	)
	public void push (List<ConsumerRecord<String, String>> consumerRecords, Acknowledgment acknowledgment) {
		try {
			if (CollectionUtils.isEmpty(consumerRecords)) {
				log.error("empty list when consume alarm remind");
				return;
			}

			Map<Object, Object> targetMap = this.redisTemplate.opsForHash().entries(RedisConstant.HASH_BASE_INFO_TARGET);
			for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
				String k = consumerRecord.key();
				if (StringUtils.isBlank(k) || (!k.equals(CommonConstant.KAFKA_ALARM_MSG_KEY_ADD))) {
					continue;
				}

				String s = consumerRecord.value();
				Alarm alarm = JSON.parseObject(s, Alarm.class);
				if (StringUtils.isBlank(s) || (alarm == null) || (alarm.getDeviceType() == null) || (alarm.getDeviceId() == null)) {
					log.error("invalid element when consume alarm remind");
					continue;
				}

				List<Long> userList = this.getUserListByDevice(alarm.getDeviceType().toString(), alarm.getDeviceId());
				if (CollectionUtils.isEmpty(userList)) {
					log.error("no user found when consume alarm remind, element: {}", alarm);
					continue;
				}

				AlarmRemindResponse alarmRemindResponse = new AlarmRemindResponse();
				BeanUtils.copyProperties(alarm, alarmRemindResponse);
				alarmRemindResponse.setTargetName("");
				String targetKey = alarm.getTargetType() + "-" + alarm.getTargetId();
				if (targetMap.containsKey(targetKey)) {
					Object target = targetMap.get(targetKey);
					if (target != null) {
						JSONObject jo = JSON.parseObject(target.toString());
						alarmRemindResponse.setTargetName(jo.getOrDefault("targetName", "").toString());
					}
				}
				String deviceKey=alarm.getDeviceType()+"-"+alarm.getDeviceId();
				Object devObj= this.redisTemplate.opsForHash().get(RedisConstant.HASH_BASE_INFO_DEVICE,deviceKey);
				if (devObj!=null){
					JSONObject jo = JSON.parseObject(devObj.toString());
					alarmRemindResponse.setUniqueId(jo.getOrDefault("uniqueId", "").toString());
				}

				this.alarmRemindWebsocketListener.sendMsgToUser(userList, JSON.toJSONString(alarmRemindResponse));
			}

			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("fail consume and push alarm remind, err: {}", e.getMessage(), e);
		}
	}
}
