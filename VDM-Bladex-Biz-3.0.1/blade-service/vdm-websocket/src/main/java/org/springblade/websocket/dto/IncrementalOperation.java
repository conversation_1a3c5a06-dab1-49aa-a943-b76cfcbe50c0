package org.springblade.websocket.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 增量操作数据结构
 * 用于在树构建期间缓存增量操作
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IncrementalOperation {
    
    /**
     * 操作类型：ADD, UPDATE, DELETE, BIND, UNBIND, IMPORT
     */
    private String operationType;
    
    /**
     * 设备信息
     */
    private DeviceInfo deviceInfo;
    
    /**
     * 操作时间戳
     */
    private long timestamp;
    
    /**
     * 操作唯一标识，用于去重
     */
    private String operationId;
    
    /**
     * 操作来源（用于调试和追踪）
     */
    private String source;
    
    /**
     * 创建增量操作
     * @param operationType 操作类型
     * @param deviceInfo 设备信息
     * @param source 操作来源
     * @return 增量操作对象
     */
    public static IncrementalOperation create(String operationType, DeviceInfo deviceInfo, String source) {
        return new IncrementalOperation(
            operationType,
            deviceInfo,
            System.currentTimeMillis(),
            java.util.UUID.randomUUID().toString(),
            source
        );
    }
}
