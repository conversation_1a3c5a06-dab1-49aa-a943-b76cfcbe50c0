package org.springblade.websocket.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;

@Configuration
public class LocationTrackKafkaConfig {

	// 改：prefix的值、Bean的名称、方法名
	@ConfigurationProperties(prefix = "spring.location-track-kafka")
	@Bean("locationTrackKafkaProperties")
	public KafkaProperties locationTrackKafkaProperties() {
		return new KafkaProperties();
	}

	// 改：Bean的名称、Qualifier的名称
	@Bean("locationTrackKafkaListenerContainerFactory")
	public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>>
	firstKafkaListenerContainerFactory(@Autowired @Qualifier("locationTrackKafkaProperties") KafkaProperties firstKafkaProperties) {
		ConcurrentKafkaListenerContainerFactory<Integer, String> factory = new ConcurrentKafkaListenerContainerFactory<>();
		factory.setConsumerFactory(this.firstConsumerFactory(firstKafkaProperties));
		factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
		factory.getContainerProperties().setMissingTopicsFatal(false);
		factory.getContainerProperties().setGroupId(firstKafkaProperties.getConsumer().getGroupId());
		return factory;
	}

	private ConsumerFactory<? super Integer, ? super String> firstConsumerFactory (KafkaProperties firstKafkaProperties) {
		return new DefaultKafkaConsumerFactory<>(firstKafkaProperties.buildConsumerProperties());
	}
}
