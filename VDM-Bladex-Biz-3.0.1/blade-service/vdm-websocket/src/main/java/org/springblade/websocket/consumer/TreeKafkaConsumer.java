package org.springblade.websocket.consumer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springblade.common.constant.DeviceStatusConstant;
import org.springblade.common.enums.Operation;
import org.springblade.entity.Location;
import org.springblade.websocket.cache.LocalDeviceStatusCacheManager;
import org.springblade.websocket.constant.RedisConstant;
import org.springblade.websocket.dto.DeviceInfo;
import org.springblade.websocket.dto.DeviceOnOff;
import org.springblade.websocket.dto.DeviceStatusInfo;
import org.springblade.websocket.dto.tree.DeviceNode;
import org.springblade.websocket.event.TreeWebsocketListener;
import org.springblade.websocket.service.TreeService;
import org.springblade.websocket.service.TreeWebSocketService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 终端部门树增量数据Kafka消费者
 * 处理终端变化、状态变化和位置数据消息
 */
@Slf4j
@Component
public class TreeKafkaConsumer {

	@Resource
	private TreeService treeService;

	@Resource
	private TreeWebSocketService treeWebSocketService;
	@Resource
	private TreeWebsocketListener treeWebsocketListener;

	@Resource
	private LocalDeviceStatusCacheManager localCacheManager;
	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Value("${acc-show.models}")
	private Set<String> models;

	/**
	 * 位置数据时间过滤阈值（秒）- 3分钟
	 */
	private static final long LOCATION_TIME_FILTER_THRESHOLD_SECONDS = 180L;

	/**
	 * 消费终端、监控对象数据变化消息
	 * 主题：device_target_change_topic
	 * 处理：新增、编辑、删除操作
	 */
	@KafkaListener(
		topics = "device_target_change_topic",
		containerFactory = "treeKafkaListenerContainerFactory",
		batch = "true"
	)
	public void consumeDeviceTargetChange(List<ConsumerRecord<String, String>> records,
										  Acknowledgment acknowledgment) {
		try {
			if (CollectionUtils.isEmpty(records)) {
				log.warn("接收到空的终端、监控对象变化消息列表");
				return;
			}

			log.info("开始处理终端、监控对象变化消息，数量: {}", records.size());

			for (ConsumerRecord<String, String> record : records) {
				try {
					String message = record.value();
					// 时间过滤：检查消息时间是否超过3分钟
					long currentTimeMillis = System.currentTimeMillis();
					long messageTimeMillis = record.timestamp();
					long timeDifferenceSeconds = (currentTimeMillis - messageTimeMillis) / 1000;

					if (timeDifferenceSeconds > LOCATION_TIME_FILTER_THRESHOLD_SECONDS) {
						log.debug("终端、监控对象变化消息时间过期，跳过处理: timestamp={}, currentTime={}, 时间差={}秒",
							messageTimeMillis, currentTimeMillis, timeDifferenceSeconds);
						continue;
					}
					DeviceInfo deviceInfo = JSON.parseObject(message, DeviceInfo.class);

					// 推断操作类型（如果没有明确指定）
					Operation operation = deviceInfo.getOperation();

					log.info("处理终端、监控对象变化事件: deviceId={}, targetId={}, operation={}",
						deviceInfo.getDeviceId(), deviceInfo.getTargetId(), operation);

					switch (operation) {
						case INSERT:
							treeService.handleAdd(deviceInfo);
							break;
						case UPDATE:
							treeService.handleUpdate(deviceInfo);
							break;
						case DELETE:
							treeService.handleDelete(deviceInfo);
							break;
						case BIND:
							treeService.handleDeviceBind(deviceInfo);
							break;
						case UNBIND:
							treeService.handleDeviceUnbind(deviceInfo);
							break;
						case IMPORT:
							treeService.handleImport(deviceInfo);
							break;
						default:
							log.warn("未知的操作类型: {}", operation);
					}
				} catch (Exception e) {
					log.error("处理单条终端、监控对象变化消息失败: {}", record.value(), e);
				}
			}

			// 手动提交offset
			acknowledgment.acknowledge();
			log.info("终端、监控对象变化消息处理完成");

		} catch (Exception e) {
			log.error("处理终端、监控对象变化消息批次失败", e);
			// 不提交offset，让消息重新消费
		}
	}


	/**
	 * 消费终端状态变化消息
	 * 主题：terminalstatus
	 * 处理：上线、下线状态变化
	 */
	@KafkaListener(
		topics = "terminalstatus",
		containerFactory = "treeKafkaListenerContainerFactory",
		batch = "true"
	)
	public void consumeTerminalStatus(List<ConsumerRecord<String, String>> records,
									  Acknowledgment acknowledgment) {
		try {
			if (CollectionUtils.isEmpty(records)) {
				log.warn("接收到空的终端状态消息列表");
				return;
			}

			log.info("开始处理终端状态消息，数量: {}", records.size());

			for (ConsumerRecord<String, String> record : records) {
				try {
					String message = record.value();

					// 时间过滤：检查消息时间是否超过3分钟
					long currentTimeMillis = System.currentTimeMillis();
					long messageTimeMillis = record.timestamp();
					long timeDifferenceSeconds = (currentTimeMillis - messageTimeMillis) / 1000;

					if (timeDifferenceSeconds > LOCATION_TIME_FILTER_THRESHOLD_SECONDS) {
						log.debug("终端状态消息时间过期，跳过处理: timestamp={}, currentTime={}, 时间差={}秒",
							messageTimeMillis, currentTimeMillis, timeDifferenceSeconds);
						continue;
					}

					DeviceOnOff deviceOnOff = JSON.parseObject(message, DeviceOnOff.class);

					log.info("处理终端状态事件: targetId={}, deviceId={}, deviceOnOff={}",
						deviceOnOff.getTargetId(), deviceOnOff.getDeviceId(), deviceOnOff.getOnOffLine());

					// 获取当前上线状态信息
					DeviceNode deviceNode = treeService.queryDeviceNodeByIndex(deviceOnOff.getDeviceId());

					if (deviceNode != null) {
						Integer currentStatus = deviceNode.getOnline();
						Integer newValue = Integer.valueOf(deviceOnOff.getOnOffLine());

						// 检查上线状态是否变化
						if (!Objects.equals(currentStatus, newValue)) {
							// 更新上线状态
							boolean success = treeService.updateDeviceState(
								deviceNode,
								DeviceStatusInfo.FIELD_ONLINE,
								newValue
							);

							if (success) {
								log.info("终端上线状态更新成功: deviceId={}, {} -> {}",
									deviceOnOff.getDeviceId(), currentStatus, deviceOnOff.getOnOffLine());

								// 推送到前端
								pushOnlionChange(deviceOnOff);
							} else {
								log.warn("终端上线状态更新失败: deviceId={}", deviceOnOff.getDeviceId());
							}
						} else {
							log.debug("终端上线状态未发生变化，跳过更新: deviceId={}, action={}",
								deviceOnOff.getDeviceId(), deviceOnOff.getOnOffLine());
						}
					}

				} catch (Exception e) {
					log.error("处理单条终端状态消息失败: {}", record.value(), e);
				}
			}

			// 手动提交offset
			acknowledgment.acknowledge();
			log.info("终端状态消息处理完成");

		} catch (Exception e) {
			log.error("处理终端状态消息批次失败", e);
			// 不提交offset，让消息重新消费
		}
	}

	private void pushOnlionChange(DeviceOnOff deviceOnOff) {
		try {
			// 根据设备id查询用户
			log.info("推送终端状态变化: deviceId={}, status={}",
				deviceOnOff.getDeviceId(), deviceOnOff.getOnOffLine());
			String deviceIdStr = deviceOnOff.getDeviceId() + "";
			Set<String> userCodeSet = new HashSet<>();
			Set<String> set = stringRedisTemplate.opsForSet().members(RedisConstant.KEY_DEVICE_Id_USER_CODE_PREFIX + deviceIdStr);
			if (CollUtil.isNotEmpty(set)) {
				userCodeSet.addAll(set);
			}
			//同时推送给admin
			Set<String> adminSet = stringRedisTemplate.opsForSet().members(RedisConstant.KEY_ADMIN_USER_CODE_SET);
			if (CollUtil.isNotEmpty(adminSet)) {
				userCodeSet.addAll(adminSet);
			}
			for (String userCode : userCodeSet) {
				String msg = treeService.getDynamicTreeForWebsocket(userCode);
				treeWebsocketListener.sendMsgToUser(userCode, msg);
			}



			// 推送给所有连接的客户端
			// deviceOnOffWebsocketListener.sendAllMessage(wsMessage.toJSONString());

			log.info("终端状态变化推送完成: deviceId={}", deviceOnOff.getDeviceId());

		} catch (Exception e) {
			log.error("推送终端状态变化失败: deviceId={}", deviceOnOff.getDeviceId(), e);
		}
	}

	/**
	 * 消费位置数据消息
	 * 主题：ce.comms.fct.location.0
	 * 处理：ACC状态变化和运动状态变化
	 */
	@KafkaListener(topics = "ce.comms.fct.location.0",
		containerFactory = "treeKafkaListenerContainerFactory",
		batch = "true")
	public void consumeLocationData(List<ConsumerRecord<String, String>> records,
									Acknowledgment acknowledgment) {
		try {
			if (CollectionUtils.isEmpty(records)) {
				log.warn("接收到空的位置数据消息列表");
				return;
			}

			for (ConsumerRecord<String, String> record : records) {
				try {
					String message = record.value();
					Location locationEvent = JSON.parseObject(message, Location.class);

					// 1. 时间过滤：检查定位时间是否超过3分钟
					if (!isLocationTimeValid(locationEvent)) {
						log.debug("位置数据时间过期，跳过处理: deviceId={}, locTime={}, currentTime={}",
							locationEvent.getDeviceId(), locationEvent.getTime(), System.currentTimeMillis() / 1000);
						continue;
					}

					// 2. 处理运动状态变化（集成本地缓存优化）
					processMotionStateChangeOptimized(locationEvent);

					// 3. 处理ACC状态变化（仅限特定终端型号，集成本地缓存优化）
					if (models.contains(locationEvent.getDeviceModel())) {
						processAccStatusChangeOptimized(locationEvent);
					}

				} catch (Exception e) {
					log.error("处理单条位置数据消息失败: {}", record.value(), e);
				}
			}

			// 手动提交offset
			acknowledgment.acknowledge();

		} catch (Exception e) {
			log.error("处理位置数据消息批次失败", e);
			// 不提交offset，让消息重新消费
		}
	}

	/**
	 * 检查位置数据时间是否有效
	 *
	 * @param locationEvent 位置事件
	 * @return true-时间有效，false-时间过期
	 */
	private boolean isLocationTimeValid(Location locationEvent) {
		if (locationEvent == null || locationEvent.getTime() == null) {
			log.warn("位置数据或定位时间为空，跳过时间验证");
			return false;
		}

		long locationTimeSeconds = locationEvent.getTime();
		long currentTimeSeconds = System.currentTimeMillis() / 1000;
		long timeDifferenceSeconds = currentTimeSeconds - locationTimeSeconds;

		// 如果时间差超过阈值，认为数据过期
		if (timeDifferenceSeconds > LOCATION_TIME_FILTER_THRESHOLD_SECONDS) {
			log.info("位置数据时间过期: deviceId={}, 定位时间={}, 当前时间={}, 时间差={}秒",
				locationEvent.getDeviceId(), locationTimeSeconds, currentTimeSeconds, timeDifferenceSeconds);
			return false;
		}

		return true;
	}

	/**
	 * 处理运动状态变化（优化版本，集成本地缓存）
	 *
	 * @param locationEvent 位置事件
	 */
	private void processMotionStateChangeOptimized(Location locationEvent) {
		try {
			Double speed = locationEvent.getSpeed();
			// 根据速度计算运动状态：速度>0为运动(1)，否则为静止(0)
			Integer newFusionState = (speed != null && speed > 0) ? DeviceStatusConstant.FUSION_STATE_MOVE : DeviceStatusConstant.FUSION_STATE_STOP;

			// 1. 首先检查本地缓存中的状态是否有变化
			if (!localCacheManager.isFusionStateChanged(locationEvent.getDeviceId(), newFusionState)) {
				log.debug("本地缓存显示运动状态未变化，跳过处理: deviceId={}, fusionState={}",
					locationEvent.getDeviceId(), newFusionState);
				return;
			}

			// 2. 本地缓存显示状态有变化，进一步检查Redis中的状态
			DeviceNode deviceNode = treeService.queryDeviceNodeByIndex(locationEvent.getDeviceId());

			if (deviceNode != null) {
				Integer currentFusionState = deviceNode.getFusionState();

				// 3. 检查Redis中的运动状态是否真的有变化
				if (!Objects.equals(currentFusionState, newFusionState)) {
					log.info("处理运动状态事件: targetId={}, deviceId={}, speed={}, fusionState={} -> {}",
						locationEvent.getTargetId(), locationEvent.getDeviceId(),
						locationEvent.getSpeed(), currentFusionState, newFusionState);

					// 4. 更新运动状态
					boolean success = treeService.updateDeviceState(
						deviceNode,
						DeviceStatusInfo.FIELD_FUSION_STATE,
						newFusionState
					);

					if (success) {
						log.info("终端运动状态更新成功: deviceId={}, {} -> {}",
							locationEvent.getDeviceId(), currentFusionState, newFusionState);

						// 5. 更新本地缓存
						localCacheManager.updateDeviceStatusCache(
							locationEvent.getDeviceId(), newFusionState, null, locationEvent.getDeviceModel());

						// 6. 推送运动状态变化到前端
						treeWebSocketService.pushDeviceChange(locationEvent.getDeviceId(), "MOTION_STATE_CHANGE");
					} else {
						log.warn("终端运动状态更新失败: deviceId={}", locationEvent.getDeviceId());
					}
				} else {
					log.debug("Redis中运动状态未发生变化，更新本地缓存: deviceId={}, fusionState={}",
						locationEvent.getDeviceId(), newFusionState);
					// 更新本地缓存以保持一致性
					localCacheManager.updateDeviceStatusCache(
						locationEvent.getDeviceId(), newFusionState, null, locationEvent.getDeviceModel());
				}
			}
		} catch (Exception e) {
			log.error("处理运动状态变化失败: deviceId={}", locationEvent.getDeviceId(), e);
		}
	}


	/**
	 * 处理ACC状态变化（优化版本，集成本地缓存）
	 *
	 * @param location 位置事件
	 */
	private void processAccStatusChangeOptimized(Location location) {
		try {
			Integer stateFlag = location.getStatus();
			if (stateFlag != null) {
				Integer accStatus = stateFlag & 1;

				// 1. 首先检查本地缓存中的ACC状态是否有变化
				if (!localCacheManager.isAccStatusChanged(location.getDeviceId(), accStatus)) {
					log.debug("本地缓存显示ACC状态未变化，跳过处理: deviceId={}, accStatus={}",
						location.getDeviceId(), accStatus);
					return;
				}

				// 2. 本地缓存显示状态有变化，进一步检查Redis中的状态
				DeviceNode deviceNode = treeService.queryDeviceNodeByIndex(location.getDeviceId());

				if (deviceNode != null) {
					Integer currentAccStatus = deviceNode.getAcc();

					// 3. 检查Redis中的ACC状态是否真的有变化
					if (!Objects.equals(currentAccStatus, accStatus)) {
						log.info("处理ACC状态事件: targetId={}, deviceId={}, accStatus={} -> {}",
							location.getTargetId(), location.getDeviceId(),
							currentAccStatus, accStatus);

						// 4. 更新ACC状态
						boolean success = treeService.updateDeviceState(
							deviceNode,
							DeviceStatusInfo.FIELD_ACC,
							accStatus
						);

						if (success) {
							log.info("终端ACC状态更新成功: deviceId={}, {} -> {}",
								location.getDeviceId(), currentAccStatus, accStatus);

							// 5. 更新本地缓存
							localCacheManager.updateDeviceStatusCache(
								location.getDeviceId(), null, accStatus, location.getDeviceModel());

							// 6. 推送ACC状态变化到前端
							treeWebSocketService.pushAccStatusChange(location);
						} else {
							log.warn("终端ACC状态更新失败: deviceId={}", location.getDeviceId());
						}
					} else {
						log.debug("Redis中ACC状态未发生变化，更新本地缓存: deviceId={}, accStatus={}",
							location.getDeviceId(), accStatus);
						// 更新本地缓存以保持一致性
						localCacheManager.updateDeviceStatusCache(
							location.getDeviceId(), null, accStatus, location.getDeviceModel());
					}
				}
			}
		} catch (Exception e) {
			log.error("处理ACC状态变化失败: deviceId={}", location.getDeviceId(), e);
		}
	}

}
