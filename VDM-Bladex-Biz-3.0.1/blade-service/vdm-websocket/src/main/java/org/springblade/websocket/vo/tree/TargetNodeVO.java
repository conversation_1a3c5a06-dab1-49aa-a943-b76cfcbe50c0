package org.springblade.websocket.vo.tree;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 监控对象节点 (例如 "京a12345")
 * 监控对象节点不包含统计字段，仅作为逻辑分组节点。
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TargetNodeVO extends DeviceNodeVO {


	/**
	 * 终端总数
	 */
	@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
	private Long total;

	/**
	 * 在线终端数
	 */
	@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
	private Long onlineNum;

}
