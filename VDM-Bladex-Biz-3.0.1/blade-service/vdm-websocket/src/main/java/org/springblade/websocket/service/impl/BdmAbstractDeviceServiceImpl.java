package org.springblade.websocket.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springblade.websocket.entity.BdmAbstractDevice;
import org.springblade.websocket.mapper.BdmAbstractDeviceMapper;
import org.springblade.websocket.service.IBdmAbstractDeviceService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * (BdmAbstractDevice)表服务实现类
 */
@Service
public class BdmAbstractDeviceServiceImpl extends ServiceImpl<BdmAbstractDeviceMapper, BdmAbstractDevice> implements IBdmAbstractDeviceService {

	@Override
	public List<Long> findDeivceIdList(String deptIdArray, String createAccount) throws Exception {
		return baseMapper.getDeviceIdList(deptIdArray, createAccount);
	}
}






