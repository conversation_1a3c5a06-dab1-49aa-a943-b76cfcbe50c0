package org.springblade.websocket.consumer;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.DictKeyConstant;
import org.springblade.websocket.entity.*;
import org.springblade.websocket.service.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

// 从kafka消费消息，并把数据推送给websocket，公共逻辑。
@Slf4j
public class WebsocketConsumer {

	@Resource
	private IRnssDeviceService rnssDeviceService;

	@Resource
	private IWearableDeviceService wearableDeviceService;

	@Resource
	private IRdssDeviceService rdssDeviceService;

	@Resource
	private IMonitDeviceService monitDeviceService;

	@Resource
	private IPntDeviceService pntDeviceService;

	@Resource
	private IUserDeptRegulatesService userDeptRegulatesService;

	// 根据设备类型与设备ID，查询所属单位ID。
	protected long getDeptIdByDevice (String deviceType, Long deviceId) {
		switch (deviceType) {
			case DictKeyConstant.DEVICE_TYPE_LOCATE:
				RnssDevice rnssDevice = this.rnssDeviceService.getOne(
					Wrappers.lambdaQuery(RnssDevice.class)
						.eq(RnssDevice::getDeviceType, Short.parseShort(deviceType))
						.eq(RnssDevice::getId, deviceId)
				);
				return ((rnssDevice == null) || (rnssDevice.getDeptId() == null)) ? 0 : rnssDevice.getDeptId();
			case DictKeyConstant.DEVICE_TYPE_WEAR:
				WearableDevice wearableDevice = this.wearableDeviceService.getOne(
					Wrappers.lambdaQuery(WearableDevice.class)
						.eq(WearableDevice::getDeviceType, Short.parseShort(deviceType))
						.eq(WearableDevice::getId, deviceId)
				);
				return ((wearableDevice == null) || (wearableDevice.getDeptId() == null)) ? 0 : wearableDevice.getDeptId();
			case DictKeyConstant.DEVICE_TYPE_SHORT:
				RdssDevice rdssDevice = this.rdssDeviceService.getOne(
					Wrappers.lambdaQuery(RdssDevice.class)
						.eq(RdssDevice::getDeviceType, Short.parseShort(deviceType))
						.eq(RdssDevice::getId, deviceId)
				);
				return ((rdssDevice == null) || (rdssDevice.getDeptId() == null)) ? 0 : rdssDevice.getDeptId();
			case DictKeyConstant.DEVICE_TYPE_MONITOR:
				MonitDevice monitDevice = this.monitDeviceService.getOne(
					Wrappers.lambdaQuery(MonitDevice.class)
						.eq(MonitDevice::getDeviceType, Short.parseShort(deviceType))
						.eq(MonitDevice::getId, deviceId)
				);
				return ((monitDevice == null) || (monitDevice.getDeptId() == null)) ? 0 : monitDevice.getDeptId();
			case DictKeyConstant.DEVICE_TYPE_TIME:
				PntDevice pntDevice = this.pntDeviceService.getOne(
					Wrappers.lambdaQuery(PntDevice.class)
						.eq(PntDevice::getDeviceType, Short.parseShort(deviceType))
						.eq(PntDevice::getId, deviceId)
				);
				return ((pntDevice == null) || (pntDevice.getDeptId() == null)) ? 0 : pntDevice.getDeptId();
			default:
				return 0;
		}
	}

	// 根据设备类型与设备ID，查询所属用户ID列表。
	protected List<Long> getUserListByDevice (String deviceType, Long deviceId) {
		List<Long> userList = new ArrayList<>();
		long deptId = this.getDeptIdByDevice(deviceType, deviceId);
		if (deptId <= 0) {
			log.error("no dept found when get user list by device, deviceType: {}, deviceId: {}", deviceType, deviceId);
			return userList;
		}

		List<UserDeptRegulates> userDeptList = this.userDeptRegulatesService.list(
			Wrappers.lambdaQuery(UserDeptRegulates.class)
				.eq(UserDeptRegulates::getDeptId, deptId)
		);
		if (CollectionUtils.isEmpty(userDeptList)) {
			log.error("no user found when get user list by device, deviceType: {}, deviceId: {}, deptId: {}", deviceType, deviceId, deptId);
			return userList;
		}

		return userDeptList.parallelStream().map(UserDeptRegulates::getUserId).collect(Collectors.toList());
	}
}
