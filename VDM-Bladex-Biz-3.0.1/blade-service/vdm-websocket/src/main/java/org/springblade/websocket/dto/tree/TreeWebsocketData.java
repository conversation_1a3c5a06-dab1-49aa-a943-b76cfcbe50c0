package org.springblade.websocket.dto.tree;

import lombok.Data;

@Data
public class TreeWebsocketData<T> {
	public static final String TYPE_ONLINE_CHANGE = "ONLINE_CHANGE";

	public static final String TYPE_ACC_CHANGE = "ACC_CHANGE";

	public static final String TYPE_FUSION_STATE_CHANGE = "FUSION_STATE_CHANGE";

	public static final String TYPE_DEVICE_TARGET_CHANGE =  "DEVICE_TARGET_CHANGE";

	/**
	 * 类型
	 */
	private String type;
	/**
	 * 数据
	 */
	private T data;
	/**
	 * 时间戳
	 */
	private Long timestamp;
}
