/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package org.springblade.websocket.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.websocket.entity.RdssDevice;
import org.springblade.websocket.vo.RdssDeviceVO;
import org.springblade.websocket.service.IRdssDeviceService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@RestController
@AllArgsConstructor
@RequestMapping("vdm-websocket/rdssdevice")
@Api(value = "", tags = "接口")
public class RdssDeviceController extends BladeController {

	private IRdssDeviceService rdssDeviceService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperation(value = "详情", notes = "传入rdssDevice")
	public R<RdssDevice> detail(RdssDevice rdssDevice) {
		RdssDevice detail = rdssDeviceService.getOne(Condition.getQueryWrapper(rdssDevice));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperation(value = "分页", notes = "传入rdssDevice")
	public R<IPage<RdssDevice>> list(RdssDevice rdssDevice, Query query) {
		IPage<RdssDevice> pages = rdssDeviceService.page(Condition.getPage(query), Condition.getQueryWrapper(rdssDevice));
		return R.data(pages);
	}

	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperation(value = "分页", notes = "传入rdssDevice")
	public R<IPage<RdssDeviceVO>> page(RdssDeviceVO rdssDevice, Query query) {
		IPage<RdssDeviceVO> pages = rdssDeviceService.selectRdssDevicePage(Condition.getPage(query), rdssDevice);
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperation(value = "新增", notes = "传入rdssDevice")
	public R save(@Valid @RequestBody RdssDevice rdssDevice) {
		return R.status(rdssDeviceService.save(rdssDevice));
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperation(value = "修改", notes = "传入rdssDevice")
	public R update(@Valid @RequestBody RdssDevice rdssDevice) {
		return R.status(rdssDeviceService.updateById(rdssDevice));
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperation(value = "新增或修改", notes = "传入rdssDevice")
	public R submit(@Valid @RequestBody RdssDevice rdssDevice) {
		return R.status(rdssDeviceService.saveOrUpdate(rdssDevice));
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(rdssDeviceService.removeByIds(Func.toLongList(ids)));
	}


}
