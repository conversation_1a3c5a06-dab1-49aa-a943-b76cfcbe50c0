package org.springblade.websocket.event;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.websocket.service.TreeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

// websocket连接监听器：设备上下线通知
@Slf4j
@Component
@ServerEndpoint("/ws/tree/push/{userCode}")
public class TreeWebsocketListener {

	@Autowired
	private TreeService treeService;

	/**
	 * 用户权限类型map
	 */
	public static final Map<String, String> USER_CODE_PERMISSION_TYPE_MAP = new HashMap<>();
	/**
	 * 用户部门权限map
	 */
	public static final Map<String, Set<String>> USER_CODE_DEPT_SET_MAP = new HashMap<>();
	/**
	 * 用户信息map
	 */
	public static final Map<String, BladeUser> USER_CODE_BLADE_USER_MAP = new HashMap<>();
	/**
	 * 用户展开节点map
	 */
	public static final Map<String, Set<String>> USER_CODE_EXPANDED_NODE_ID_SET_MAP = new HashMap<>();


	// 需要建立连接，以推送通知的userCode的列表。
	private static CopyOnWriteArraySet<String> userCodeSet = new CopyOnWriteArraySet<>();

	// 一个账号（以userId为标识）可能会同时在多个地方登录，每个登录态（以userCode为标识）都应接收到消息，因此，需要维护从userId到userCode的映射。
	// private static ConcurrentHashMap<String, List<String>> userCodeMap = new ConcurrentHashMap<>();
	private static Map<String, Set<String>> userCodeMap = new HashMap<>();

	// 当前所有连接的会话，以userCode为key。
	private static ConcurrentHashMap<String, Session> sessionPool = new ConcurrentHashMap<>();

	// 所有DeviceOnOffWebsocketListener对象的集合。
	// 虽然@Component默认是单例模式的，但SpringBoot还是会为每个websocket连接，初始化一个bean，所以可以用一个静态set保存起来，此处用concurrent包的线程安全Set。
	private static CopyOnWriteArraySet<TreeWebsocketListener> webSockets = new CopyOnWriteArraySet<>();

	// 超管用户ID列表
	private static CopyOnWriteArraySet<Long> adminIdSet = new CopyOnWriteArraySet<>();

	// 当前连接的userCode
	private String userCode;

	// 当前连接的会话
	private Session session;

	// 往userCodeSet添加userCode
	public void addUserCodeSet(String userCode) {
		TreeWebsocketListener.userCodeSet.add(userCode);
	}

	// 添加从userId到userCode的映射
	public void putUserCodeMap(String userId, String userCode) {
		if (!TreeWebsocketListener.userCodeMap.containsKey(userId)) {
			TreeWebsocketListener.userCodeMap.put(userId, new HashSet<>());
		}

		TreeWebsocketListener.userCodeMap.get(userId).add(userCode);
	}


	// 往adminIdSet添加adminId
	public void addAdminIdSet(Long adminId) {
		TreeWebsocketListener.adminIdSet.add(adminId);
	}

	// 连接建立成功时调用。
	@OnOpen
	public void onOpen(Session session, @PathParam(value = "userCode") String userCode) {
		log.info("websocket conn built for send device online or offline msg, userCode: {}", userCode);
		try {
			if (StringUtils.isBlank(userCode)) {
				log.error("empty userCode when onOpen for send device online or offline msg");
				return;
			}

			this.session = session;
			this.userCode = userCode;
			TreeWebsocketListener.sessionPool.put(userCode, session);
			TreeWebsocketListener.webSockets.add(this);
		} catch (Exception e) {
			log.error("fail callback when websocket conn built for send device online or offline msg, err: {}", e.getMessage(), e);
		}
	}

	// 连接关闭成功时调用。
	@OnClose
	public void onClose() {
		try {
			TreeWebsocketListener.webSockets.remove(this);
			TreeWebsocketListener.sessionPool.remove(this.userCode);
			TreeWebsocketListener.userCodeMap.remove(this.userCode);
			TreeWebsocketListener.USER_CODE_BLADE_USER_MAP.remove(this.userCode);
			TreeWebsocketListener.userCodeSet.remove(this.userCode);
		} catch (Exception e) {
			log.error("fail callback when websocket conn close for send device online or offline msg, err: {}", e.getMessage(), e);
		}
	}

	// 收到客户端消息时调用。
	@OnMessage
	public void onMessage(String msg) {
		List<String> expandedNodeIdList = JSON.parseArray(msg, String.class);
		HashSet<String> value = new HashSet<>(expandedNodeIdList);
		USER_CODE_EXPANDED_NODE_ID_SET_MAP.put(userCode, value);
		treeService.getDynamicTreeForWebsocket(userCode);
	}

	// 推送消息出错时调用。
	@OnError
	public void onError(Session session, Throwable e) {
		log.error("fail push when send device online or offline msg, session: {}, err: {}", session, e.getMessage(), e);
	}

	// 向指定用户推送通知
	public void sendMsgToUser(String userCode, String msg) {
		Session session = TreeWebsocketListener.sessionPool.get(userCode);
		if ((session == null) || (!session.isOpen())) {
			return;
		}
		try {
			session.getAsyncRemote().sendText(msg);
		} catch (Exception e) {

		}
	}
}
