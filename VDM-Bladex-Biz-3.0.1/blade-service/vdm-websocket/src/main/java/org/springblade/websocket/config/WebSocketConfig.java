package org.springblade.websocket.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

@Configuration
public class WebSocketConfig implements WebSocketMessageBrokerConfigurer {

	/**
	 * 	注入ServerEndpointExporter，
	 * 	这个bean会自动注册到使用了@ServerEndpoint注解的WebsocketListener
	 */
	@Bean
	public ServerEndpointExporter serverEndpointExporter () {
		return new ServerEndpointExporter();
	}
}
