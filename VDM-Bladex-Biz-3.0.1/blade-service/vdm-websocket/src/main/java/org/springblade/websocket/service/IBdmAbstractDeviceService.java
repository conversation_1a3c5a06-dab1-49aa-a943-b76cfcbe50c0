package org.springblade.websocket.service;


import com.baomidou.mybatisplus.extension.service.IService;
import org.springblade.websocket.entity.BdmAbstractDevice;

import java.util.List;

/**
 * (BdmAbstractDevice)表服务接口
 */
public interface IBdmAbstractDeviceService extends IService<BdmAbstractDevice> {

	/**
	 * 根据数据权限查询deviceId列表
	 * @param deptIdArray
	 * @param createAccount
	 * @return
	 * @throws Exception
	 */
	List<Long> findDeivceIdList(String deptIdArray, String createAccount) throws Exception;
}
