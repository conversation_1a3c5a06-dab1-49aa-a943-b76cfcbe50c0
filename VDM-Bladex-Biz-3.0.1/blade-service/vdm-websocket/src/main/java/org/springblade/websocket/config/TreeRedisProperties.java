package org.springblade.websocket.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "tree.redis")
@Data
public class TreeRedisProperties {

	private String host;
	private int port;
	private int database;
	private String password;
	private long timeout; // ms
	/**
	 * 连接池配置
	 */
	private Pool pool;


	/**
	 * 内部静态类，用于映射连接池配置
	 */
	@Data
	public static class Pool {
		/**
		 * 连接池中的最大连接数。-1 表示无限制。
		 */
		private int maxActive = 8;

		/**
		 * 连接池中的最大空闲连接。
		 */
		private int maxIdle = 8;

		/**
		 * 连接池中的最小空闲连接。
		 */
		private int minIdle = 0;

		/**
		 * 当连接池耗尽时，客户端等待新连接的最长时间（毫秒）。-1 表示无限期等待。
		 */
		private long maxWait = -1L;

	}
}
