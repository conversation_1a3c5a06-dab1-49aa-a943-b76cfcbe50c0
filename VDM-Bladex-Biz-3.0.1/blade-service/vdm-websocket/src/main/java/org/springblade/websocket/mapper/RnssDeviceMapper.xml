<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.websocket.mapper.RnssDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="rnssDeviceResultMap" type="org.springblade.websocket.vo.RnssDeviceVO">
        <id column="id" property="id"/>
        <result column="unique_id" property="uniqueId"/>
        <result column="imei" property="imei"/>
        <result column="model" property="model"/>
        <result column="vendor" property="vendor"/>
        <result column="bd_chip_sn" property="bdChipSn"/>
        <result column="device_type" property="deviceType"/>
        <result column="specificity" property="specificity"/>
        <result column="dept_id" property="deptId"/>
        <result column="target_id" property="targetId"/>
        <result column="target_type" property="targetType"/>
        <result column="target_name" property="targetName"/>
        <result column="activated" property="activated"/>
        <result column="category" property="category"/>
        <result column="device_num" property="deviceNum"/>
        <result column="channel_num" property="channelNum"/>
        <result column="installdate" property="installdate"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="deleted" property="deleted"/>
        <result column="scenario" property="scenario"/>
        <result column="domain" property="domain"/>
        <result column="gnss_mode" property="gnssMode"/>
    </resultMap>


    <select id="selectRnssDevicePage" resultMap="rnssDeviceResultMap">
        select * from bdm_rnss_device where is_deleted = 0
    </select>

</mapper>
