package org.springblade.websocket.cache;

import lombok.Data;

import java.io.Serializable;

/**
 * 设备状态本地缓存对象
 * 用于减少Redis查询，提高位置数据处理性能
 */
@Data
public class DeviceStatusCache implements Serializable {

    /**
     * 设备运动状态（融合状态）
     * 0-静止，1-运动
     */
    private Integer fusionState;

    /**
     * 设备ACC状态
     * 0-关闭，1-开启
     */
    private Integer accStatus;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 最后更新时间（毫秒时间戳）
     */
    private Long lastUpdateTime;

    /**
     * 缓存创建时间（毫秒时间戳）
     */
    private Long createTime;

    /**
     * 默认构造函数
     */
    public DeviceStatusCache() {
        this.createTime = System.currentTimeMillis();
        this.lastUpdateTime = this.createTime;
    }

    /**
     * 构造函数
     * @param fusionState 运动状态
     * @param accStatus ACC状态
     * @param deviceModel 设备型号
     */
    public DeviceStatusCache(Integer fusionState, Integer accStatus, String deviceModel) {
        this();
        this.fusionState = fusionState;
        this.accStatus = accStatus;
        this.deviceModel = deviceModel;
    }

    /**
     * 更新运动状态
     * @param fusionState 新的运动状态
     */
    public void updateFusionState(Integer fusionState) {
        this.fusionState = fusionState;
        this.lastUpdateTime = System.currentTimeMillis();
    }

    /**
     * 更新ACC状态
     * @param accStatus 新的ACC状态
     */
    public void updateAccStatus(Integer accStatus) {
        this.accStatus = accStatus;
        this.lastUpdateTime = System.currentTimeMillis();
    }

    /**
     * 检查缓存是否过期
     * @param timeoutMillis 超时时间（毫秒）
     * @return true-已过期，false-未过期
     */
    public boolean isExpired(long timeoutMillis) {
        return System.currentTimeMillis() - this.createTime > timeoutMillis;
    }

    /**
     * 检查运动状态是否发生变化
     * @param newFusionState 新的运动状态
     * @return true-状态有变化，false-状态无变化
     */
    public boolean isFusionStateChanged(Integer newFusionState) {
        if (this.fusionState == null && newFusionState == null) {
            return false;
        }
        if (this.fusionState == null || newFusionState == null) {
            return true;
        }
        return !this.fusionState.equals(newFusionState);
    }

    /**
     * 检查ACC状态是否发生变化
     * @param newAccStatus 新的ACC状态
     * @return true-状态有变化，false-状态无变化
     */
    public boolean isAccStatusChanged(Integer newAccStatus) {
        if (this.accStatus == null && newAccStatus == null) {
            return false;
        }
        if (this.accStatus == null || newAccStatus == null) {
            return true;
        }
        return !this.accStatus.equals(newAccStatus);
    }

    @Override
    public String toString() {
        return "DeviceStatusCache{" +
                "fusionState=" + fusionState +
                ", accStatus=" + accStatus +
                ", deviceModel='" + deviceModel + '\'' +
                ", lastUpdateTime=" + lastUpdateTime +
                ", createTime=" + createTime +
                '}';
    }
}
