<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.websocket.mapper.TreeMapper">
<sql id="selectDeviceNodeSql">
    select a.id                       as targetId,
           a.name,
           b.create_account,
           b.id,
           b.category,
           b.device_type,
           b.dept_id,
           b.channel_num,
           COALESCE(c."kinestate", 0) as fusion_state,
           COALESCE(c."action", 1)    as online,
           b.unique_id,
           b.model
    from bdm_abstract_target a
             inner join bdm_abstract_device b on a.id = b.target_id
             left join bdm_device_status c on b.id = c.device_id
    where b.deleted = 0
</sql>

    <select id="selectDeviceNode" resultType="org.springblade.websocket.dto.TargetDeviceTreeDto">
        <include refid="selectDeviceNodeSql"/>
        order by b.dept_id, b.device_type, b.category, a.name;
    </select>

    <select id="selectDeviceNodeByDeviceId" resultType="org.springblade.websocket.dto.TargetDeviceTreeDto">
        <include refid="selectDeviceNodeSql"/>
        and b.id =#{deviceId}
    </select>
</mapper>

