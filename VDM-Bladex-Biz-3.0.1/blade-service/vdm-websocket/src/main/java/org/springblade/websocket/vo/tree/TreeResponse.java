package org.springblade.websocket.vo.tree;

import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 动态树展开请求DTO
 *
 * <AUTHOR> Assistant
 * @since 2024-12-21
 */
@Data
public class TreeResponse {

    /**
     * 展开的节点ID列表
     */
    private List<BaseNodeVO> tree =  new ArrayList<>();

	/**
	 * 展开的节点ID列表
	 */
	private Set<String> expandedNodeIds = new HashSet<>();
}
