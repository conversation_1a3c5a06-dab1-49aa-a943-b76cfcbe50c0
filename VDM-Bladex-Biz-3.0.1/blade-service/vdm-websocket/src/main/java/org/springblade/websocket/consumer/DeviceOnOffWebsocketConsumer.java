package org.springblade.websocket.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springblade.websocket.dto.DeviceOnOff;
import org.springblade.websocket.event.DeviceOnOffWebsocketListener;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

// 从kafka消费消息，并把数据推送给websocket，数据内容：设备上下线通知
@Slf4j
@Component
@EnableKafka
public class DeviceOnOffWebsocketConsumer extends WebsocketConsumer {

	@Resource
	private DeviceOnOffWebsocketListener deviceOnOffWebsocketListener;

	@KafkaListener(
		containerFactory = "deviceOnOffKafkaListenerContainerFactory",
		batch = "true",
		topics = {"terminalstatus"}
	)
	public void push (List<ConsumerRecord<String, String>> consumerRecords, Acknowledgment acknowledgment) {
		try {
			if (CollectionUtils.isEmpty(consumerRecords)) {
				log.error("empty list when consume device online or offline msg");
				return;
			}
			for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
				String s = consumerRecord.value();
				DeviceOnOff deviceOnOff = JSON.parseObject(s, DeviceOnOff.class);
				if (
					StringUtils.isBlank(s) ||
					(deviceOnOff == null) ||
					(deviceOnOff.getDeviceType() == null) ||
					(deviceOnOff.getDeviceId() == null)
				) {
					log.error("invalid element when consume device online or offline msg");
					continue;
				}

				List<Long> userList = this.getUserListByDevice(deviceOnOff.getDeviceType().toString(), deviceOnOff.getDeviceId());
				if (CollectionUtils.isEmpty(userList)) {
					log.error("no user found when consume device online or offline msg, element: {}", deviceOnOff);
					continue;
				}

				this.deviceOnOffWebsocketListener.sendMsgToUser(userList, JSON.toJSONString(deviceOnOff));
			}

			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("fail consume and push device online or offline msg, err: {}", e.getMessage(), e);
		}
	}
}
