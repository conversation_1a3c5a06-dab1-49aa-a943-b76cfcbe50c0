<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.websocket.mapper.BdmAbstractDeviceMapper">

    <select id="getDeviceIdList" resultType="long">
        select id from bdm_abstract_device
        where 1 = 1
        <if test="deptIdArray != null and deptIdArray != ''">
            and dept_id = any(${deptIdArray})
        </if>
        <if test="createAccount != null and createAccount != ''">
            and create_count = #{createAccount}
        </if>
    </select>

</mapper>

