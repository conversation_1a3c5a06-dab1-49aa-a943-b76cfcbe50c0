package org.springblade.websocket.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 设备状态本地缓存管理器
 * 用于减少Redis查询，提高位置数据处理性能
 */
@Slf4j
@Component
public class LocalDeviceStatusCacheManager {

    /**
     * 本地缓存存储
     * Key: deviceId, Value: DeviceStatusCache
     */
    private final ConcurrentHashMap<Long, DeviceStatusCache> deviceStatusCache = new ConcurrentHashMap<>();

    /**
     * 缓存过期时间（毫秒）- 30分钟
     */
    private static final long CACHE_EXPIRE_TIME_MILLIS = 30 * 60 * 1000L;

    /**
     * 清理任务执行间隔（分钟）
     */
    private static final long CLEANUP_INTERVAL_MINUTES = 10L;

    /**
     * 定时清理任务执行器
     */
    private ScheduledExecutorService cleanupExecutor;

    /**
     * 初始化缓存管理器
     */
    @PostConstruct
    public void init() {
        // 启动定时清理任务
        cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "LocalDeviceStatusCache-Cleanup");
            thread.setDaemon(true);
            return thread;
        });

        cleanupExecutor.scheduleWithFixedDelay(
                this::cleanupExpiredCache,
                CLEANUP_INTERVAL_MINUTES,
                CLEANUP_INTERVAL_MINUTES,
                TimeUnit.MINUTES
        );

        log.info("本地设备状态缓存管理器初始化完成，缓存过期时间: {}分钟，清理间隔: {}分钟",
                CACHE_EXPIRE_TIME_MILLIS / 60000, CLEANUP_INTERVAL_MINUTES);
    }

    /**
     * 获取设备状态缓存
     * @param deviceId 设备ID
     * @return 设备状态缓存，如果不存在或已过期则返回null
     */
    public DeviceStatusCache getDeviceStatusCache(Long deviceId) {
        if (deviceId == null) {
            return null;
        }

        DeviceStatusCache cache = deviceStatusCache.get(deviceId);
        if (cache != null && cache.isExpired(CACHE_EXPIRE_TIME_MILLIS)) {
            // 缓存已过期，移除并返回null
            deviceStatusCache.remove(deviceId);
            log.debug("设备状态缓存已过期并移除: deviceId={}", deviceId);
            return null;
        }

        return cache;
    }

    /**
     * 更新设备状态缓存
     * @param deviceId 设备ID
     * @param fusionState 运动状态
     * @param accStatus ACC状态
     * @param deviceModel 设备型号
     */
    public void updateDeviceStatusCache(Long deviceId, Integer fusionState, Integer accStatus, String deviceModel) {
        if (deviceId == null) {
            return;
        }

        DeviceStatusCache cache = deviceStatusCache.get(deviceId);
        if (cache == null) {
            // 创建新的缓存对象
            cache = new DeviceStatusCache(fusionState, accStatus, deviceModel);
            deviceStatusCache.put(deviceId, cache);
            log.debug("创建设备状态缓存: deviceId={}, cache={}", deviceId, cache);
        } else {
            // 更新现有缓存
            if (fusionState != null) {
                cache.updateFusionState(fusionState);
            }
            if (accStatus != null) {
                cache.updateAccStatus(accStatus);
            }
            if (deviceModel != null) {
                cache.setDeviceModel(deviceModel);
            }
            log.debug("更新设备状态缓存: deviceId={}, cache={}", deviceId, cache);
        }
    }

    /**
     * 检查运动状态是否发生变化
     * @param deviceId 设备ID
     * @param newFusionState 新的运动状态
     * @return true-状态有变化或缓存不存在，false-状态无变化
     */
    public boolean isFusionStateChanged(Long deviceId, Integer newFusionState) {
        DeviceStatusCache cache = getDeviceStatusCache(deviceId);
        if (cache == null) {
            // 缓存不存在，认为状态有变化
            return true;
        }
        return cache.isFusionStateChanged(newFusionState);
    }

    /**
     * 检查ACC状态是否发生变化
     * @param deviceId 设备ID
     * @param newAccStatus 新的ACC状态
     * @return true-状态有变化或缓存不存在，false-状态无变化
     */
    public boolean isAccStatusChanged(Long deviceId, Integer newAccStatus) {
        DeviceStatusCache cache = getDeviceStatusCache(deviceId);
        if (cache == null) {
            // 缓存不存在，认为状态有变化
            return true;
        }
        return cache.isAccStatusChanged(newAccStatus);
    }

    /**
     * 移除设备状态缓存
     * @param deviceId 设备ID
     */
    public void removeDeviceStatusCache(Long deviceId) {
        if (deviceId != null) {
            DeviceStatusCache removed = deviceStatusCache.remove(deviceId);
            if (removed != null) {
                log.debug("移除设备状态缓存: deviceId={}", deviceId);
            }
        }
    }

    /**
     * 清理过期的缓存
     */
    private void cleanupExpiredCache() {
        try {
            int beforeSize = deviceStatusCache.size();
            long currentTime = System.currentTimeMillis();
            
            deviceStatusCache.entrySet().removeIf(entry -> {
                DeviceStatusCache cache = entry.getValue();
                return cache.isExpired(CACHE_EXPIRE_TIME_MILLIS);
            });
            
            int afterSize = deviceStatusCache.size();
            int removedCount = beforeSize - afterSize;
            
            if (removedCount > 0) {
                log.info("清理过期设备状态缓存完成: 清理前={}, 清理后={}, 移除数量={}", 
                        beforeSize, afterSize, removedCount);
            }
        } catch (Exception e) {
            log.error("清理过期设备状态缓存失败", e);
        }
    }

    /**
     * 获取缓存统计信息
     * @return 缓存大小
     */
    public int getCacheSize() {
        return deviceStatusCache.size();
    }

    /**
     * 清空所有缓存
     */
    public void clearAllCache() {
        int size = deviceStatusCache.size();
        deviceStatusCache.clear();
        log.info("清空所有设备状态缓存: 清理数量={}", size);
    }

    /**
     * 销毁缓存管理器
     */
    public void destroy() {
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        clearAllCache();
        log.info("本地设备状态缓存管理器已销毁");
    }
}
