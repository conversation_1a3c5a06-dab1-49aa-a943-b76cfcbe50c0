package org.springblade.websocket.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springblade.core.tool.utils.Func;
import org.springblade.entity.Location;
import org.springblade.websocket.constant.RedisConstant;
import org.springblade.websocket.event.LocationTrackWebsocketListener;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

// 从kafka消费消息，并把数据推送给websocket，数据内容：位置跟踪
@Slf4j
@Component
@EnableKafka
public class LocationTrackWebsocketConsumer extends WebsocketConsumer {

	@Resource
	private LocationTrackWebsocketListener locationTrackWebsocketListener;

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@KafkaListener(
		containerFactory = "locationTrackKafkaListenerContainerFactory",
		topics = {"GuoNeng_track_control_position"},
		batch = "true"
	)
	public void push (List<ConsumerRecord<String, String>> consumerRecords, Acknowledgment acknowledgment) {
		try {
			if (CollectionUtils.isEmpty(consumerRecords)) {
				log.error("empty list when consume location track");
				return;
			}
			for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
				String s = consumerRecord.value();
				Location location = JSON.parseObject(s, Location.class);
				if (StringUtils.isBlank(s) || (location == null) || (location.getDeviceType() == null) || (location.getDeviceId() == null)) {
					log.error("invalid element when consume location track");
					continue;
				}

				/*List<Long> userList;
				String k = RedisConstant.SET_OFFICIAL_DEVICE_USER + location.getDeviceType() + "-" + location.getDeviceId();
				if (Boolean.TRUE.equals(this.redisTemplate.hasKey(k))) {
					Set<Object> userSet = this.redisTemplate.opsForSet().members(k);
					if (CollectionUtils.isEmpty(userSet)) {
						log.info("no user found from cache when consume location track, element: {}", location);
						//continue;
					}

					userList = new ArrayList<>();
					for (Object user : userSet) {
						userList.add(Long.parseLong(user.toString()));
					}
				} else {
					userList = this.getUserListByDevice(location.getDeviceType().toString(), location.getDeviceId());
					if (CollectionUtils.isEmpty(userList)) {
						log.info("no user found from DB when consume location track, element: {}", location);
						//continue;
					}
				}

				this.locationTrackWebsocketListener.sendMsgToUser(userList, JSON.toJSONString(location));

*/
				//之前的数据权限无法在国能底座登录时使用，因为国能底座登录时账号管理的部门从外部传入，而不是保存在 user_dept_regulates中。
				//这里改为适配国能底座登录以及位置平台admin登录
				String deviceIdStr = location.getDeviceId() + "";
				Set<String> userCodeSet = stringRedisTemplate.opsForSet().members(RedisConstant.KEY_DEVICE_Id_USER_CODE_PREFIX + deviceIdStr);

				//公务车推送
				//获取当前用户对应的userId
				String deviceTypeStr = location.getDeviceType() + "";
				String key = RedisConstant.SET_OFFICIAL_DEVICE_USER + deviceTypeStr + "-" + deviceIdStr;
				Set<Object> set = LocationTrackWebsocketListener.redisTemplate.opsForSet().members(key);

				//根据userId找到userCode
				for(Object userId : set){
					if(userId != null){
						String userIdStr = userId.toString();
						Set<String> ucs = this.locationTrackWebsocketListener.getUserCodeSetByUserId(userIdStr);
						if(userCodeSet != null && ucs != null && ucs.size() > 0){
							userCodeSet.addAll(ucs);
						}
					}
				}

				log.info("locationTrack>>获取用户将要推送的userCode", JSON.toJSONString(userCodeSet));
				if(userCodeSet != null){
					//同时推送给admin
					Set<String> adminSet = stringRedisTemplate.opsForSet().members(RedisConstant.KEY_ADMIN_USER_CODE_SET);
					if(adminSet != null && adminSet.size() > 0){
						for(String as : adminSet){
							userCodeSet.add(as.toString());
						}
					}
					log.info("locationTrack>>将要推送");
					locationTrackWebsocketListener.sendMessageByUserCodes(userCodeSet, JSON.toJSONString(location));
				}

			}

			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("fail consume and push location track, err: {}", e.getMessage(), e);
		}
	}
}
