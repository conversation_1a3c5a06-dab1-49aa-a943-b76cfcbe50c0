package org.springblade.websocket.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springblade.entity.Location;
import org.springblade.websocket.dto.DeviceOnOff;
import org.springblade.websocket.event.TreeWebsocketListener;
import org.springblade.websocket.service.TreeWebSocketService;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 树结构WebSocket推送服务实现
 */
@Slf4j
@Service
public class TreeWebSocketServiceImpl implements TreeWebSocketService {

    @Resource
    private TreeWebsocketListener treeWebsocketListener;

	@Resource
	private StringRedisTemplate stringRedisTemplate;


	@Override
    public void pushOnlionChange(DeviceOnOff deviceOnOff) {
        try {
			// 根据设备id查询用户
            log.info("推送终端状态变化: deviceId={}, status={}",
                deviceOnOff.getDeviceId(), deviceOnOff.getOnOffLine());

            // 构建WebSocket消息
            JSONObject wsMessage = new JSONObject();
            wsMessage.put("type", "TERMINAL_STATUS_CHANGE");
            wsMessage.put("data", deviceOnOff);
            wsMessage.put("timestamp", System.currentTimeMillis());

            // 推送给所有连接的客户端
            // deviceOnOffWebsocketListener.sendAllMessage(wsMessage.toJSONString());

            log.info("终端状态变化推送完成: deviceId={}", deviceOnOff.getDeviceId());

        } catch (Exception e) {
            log.error("推送终端状态变化失败: deviceId={}", deviceOnOff.getDeviceId(), e);
        }
    }

    @Override
    public void pushAccStatusChange(Location event) {
        try {
            // log.info("推送ACC状态变化: deviceId={}, accStatus={}",
            //     event.getDeviceId(), event.getAccStatus());

            // 构建WebSocket消息
            JSONObject wsMessage = new JSONObject();
            // wsMessage.put("type", "TERMINAL_ACC_CHANGE");
            // wsMessage.put("data", new JSONObject()
            //     .fluentPut("deviceId", event.getDeviceId())
            //     .fluentPut("uniqueId", event.getUniqueId())
            //     .fluentPut("deviceType", event.getDeviceType())
            //     .fluentPut("accStatus", event.getAccStatus())
            //     .fluentPut("locTime", event.getLocTime()));
            // wsMessage.put("timestamp", System.currentTimeMillis());

            // 推送给所有连接的客户端
            // deviceOnOffWebsocketListener.sendAllMessage(wsMessage.toJSONString());

            log.info("ACC状态变化推送完成: deviceId={}", event.getDeviceId());

        } catch (Exception e) {
            log.error("推送ACC状态变化失败: deviceId={}", event.getDeviceId(), e);
        }
    }

    @Override
    public void pushDeviceChange(Long deviceId, String changeType) {
        try {
            log.info("推送设备变化: deviceId={}, changeType={}", deviceId, changeType);

            // 构建WebSocket消息
            JSONObject wsMessage = new JSONObject();
            wsMessage.put("type", "DEVICE_CHANGE");
            wsMessage.put("data", new JSONObject()
                .fluentPut("deviceId", deviceId)
                .fluentPut("changeType", changeType));
            wsMessage.put("timestamp", System.currentTimeMillis());

            // 推送给所有连接的客户端
            // deviceOnOffWebsocketListener.sendAllMessage(wsMessage.toJSONString());

            log.info("设备变化推送完成: deviceId={}", deviceId);

        } catch (Exception e) {
            log.error("推送设备变化失败: deviceId={}", deviceId, e);
        }
    }

    @Override
    public void pushStatisticsChange(String deptId, Long totalChange, Long onlineChange) {
        try {
            log.info("推送统计数据变化: deptId={}, totalChange={}, onlineChange={}",
                deptId, totalChange, onlineChange);

            // 构建WebSocket消息
            JSONObject wsMessage = new JSONObject();
            wsMessage.put("type", "STATISTICS_CHANGE");
            wsMessage.put("data", new JSONObject()
                .fluentPut("deptId", deptId)
                .fluentPut("totalChange", totalChange)
                .fluentPut("onlineChange", onlineChange));
            wsMessage.put("timestamp", System.currentTimeMillis());

            // 推送给所有连接的客户端
            // deviceOnOffWebsocketListener.sendAllMessage(wsMessage.toJSONString());

            log.info("统计数据变化推送完成: deptId={}", deptId);

        } catch (Exception e) {
            log.error("推送统计数据变化失败: deptId={}", deptId, e);
        }
    }
}
