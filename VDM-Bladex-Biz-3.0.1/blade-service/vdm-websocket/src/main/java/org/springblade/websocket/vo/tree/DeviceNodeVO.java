package org.springblade.websocket.vo.tree;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 逻辑终端/设备节点 (例如 "终端a")
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeviceNodeVO extends BaseNodeVO {
    /**
     * 监控对象id
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
	private Long targetId;

	/**
	 * 监控对象id
	 */
	@JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
	private Long deviceId;

    /**
     * 创建账户
     */
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
	private String createAccount;

    /**
     * 终端种类/功能类型
     * 1-定位终端，2-视频终端，3-智能终端，4-工卡，5-集装箱追踪器等
     */
    private Integer category;

    /**
     * 终端类别
     * 1-北斗定位终端，2-北斗穿戴式终端，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端
     */
    private Integer deviceType;


    /**
     * 视频通道个数（视频终端和智能终端特有）
     */
    private Integer channelNum;

    /**
     * 在线状态
     * 0-离线，1-在线
     */
    private Integer online;

    /**
     * 融合状态（运动状态）
     * 1-静止，2-运动
     */
    private Integer fusionState;

    /**
     * 设备唯一标识码（终端号）
     */
    private String uniqueId;

    /**
     * ACC状态(0-关闭,1-开启)
     */
    private Integer acc ;
}
