-- KEYS[1]: 用户ID
--
-- 获取指定用户创建的所有设备及其所有祖先节点，
-- 然后构建并返回一个层级化的树形结构。
--
-- 返回: 一个代表树根节点（或多个根节点）的JSON数组字符串。

local userId = KEYS[1]
if not userId or userId == '' then
    return '[]'
end

-- 获取树形数据的当前活动版本
local currentVersionKey = "tree:current_version"
local currentVersion = redis.call('GET', currentVersionKey)
if not currentVersion then
    currentVersion = "v1" -- 默认版本
end

local node_key_prefix = "tree:" .. currentVersion .. ":node:"
local creator_index_key = "tree:" .. currentVersion .. ":user_devices:" .. userId

-- 1. 获取该用户创建的所有设备的ID
local deviceIds = redis.call("SMEMBERS", creator_index_key)
if #deviceIds == 0 then
    return '[]'
end

local nodes_map = {}
local seen_ids = {}

-- 2. 从每个设备出发，向上遍历至根节点，并收集所有唯一的祖先节点。
--    将它们存储在一个 map (Lua table) 中以便快速查找。
for _, deviceId in ipairs(deviceIds) do
    local current_id = deviceId
    -- 从每个设备开始向上追溯
    while current_id and current_id ~= '0' and not seen_ids[current_id] do
        seen_ids[current_id] = true
        local node_key = node_key_prefix .. current_id
        local node_data_flat = redis.call("HGETALL", node_key)

        if #node_data_flat > 0 then
            local node = {}
            for i = 1, #node_data_flat, 2 do
                node[node_data_flat[i]] = node_data_flat[i + 1]
            end

            -- 为构建树形结构做准备
            node.children = {}
            nodes_map[node.id] = node

            -- 移动到父节点，进行下一次迭代
            local  type = node['type']
            local  parentType
            if type == 'device' then
                current_id = 'target:' .. node['parentId']
            elseif type == 'target' then
                current_id = 'device_type:' .. node['parentId']
            elseif type == 'device_type' then
                current_id = 'dept:' .. node['parentId']
            else
                current_id = 'dept:' .. node['parentId']
            end
        else
            -- 如果找不到节点数据，则停止这条路径的追溯
            current_id = nil
        end
    end
end

-- 3. 组装树形结构。
--    遍历收集到的所有节点，并将子节点链接到它们的父节点上。
local tree = {}
for id, node in pairs(nodes_map) do
    local parentId = node.parentId
    if parentId and parentId ~= "0" and nodes_map[parentId] then
        -- 如果此节点有父节点，并且父节点也在我们的 map 中，则将其加为子节点
        local parent_node = nodes_map[parentId]
        table.insert(parent_node.children, node)
    else
        -- 这是一个根节点（因为它的父节点是'0'或不在此次获取的数据中）
        table.insert(tree, node)
    end
end

-- 4. (最终修正步骤) 处理所有叶子节点（即children列表为空的节点）
--    以防止空的 table {} 被错误地编码为 JSON 对象 {}
for id, node in pairs(nodes_map) do
    if #node.children == 0 then
        node.children = nil
    end
end


-- 4. 将完整构建好的树以JSON字符串的形式返回
return cjson.encode(tree)
