-- 根据模式批量删除Redis key的Lua脚本
-- 参数：
-- KEYS[1]: 要删除的key模式 (如 "tree:node:*")
-- ARGV[1]: 每次SCAN的数量 (默认1000)
-- ARGV[2]: 每批删除的数量 (默认1000)

local pattern = KEYS[1]
local scan_count = tonumber(ARGV[1]) or 1000
local batch_size = tonumber(ARGV[2]) or 1000

local cursor = "0"
local total_deleted = 0
local keys_to_delete = {}

-- 循环SCAN直到遍历完所有匹配的key
repeat
    -- 执行SCAN命令
    local scan_result = redis.call("SCAN", cursor, "MATCH", pattern, "COUNT", scan_count)
    cursor = scan_result[1]
    local keys = scan_result[2]
    
    -- 将找到的key添加到待删除列表
    for i = 1, #keys do
        table.insert(keys_to_delete, keys[i])
        
        -- 如果达到批次大小，执行删除
        if #keys_to_delete >= batch_size then
            local deleted = redis.call("DEL", unpack(keys_to_delete))
            total_deleted = total_deleted + deleted
            keys_to_delete = {} -- 清空列表
        end
    end
    
until cursor == "0"

-- 删除剩余的key
if #keys_to_delete > 0 then
    local deleted = redis.call("DEL", unpack(keys_to_delete))
    total_deleted = total_deleted + deleted
end

return total_deleted
