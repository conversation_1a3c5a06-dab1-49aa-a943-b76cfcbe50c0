--- get_dept_tree_with_expanded_nodes.lua (优化版本)
-- 获取部门树结构，并展开指定节点的全部子节点
-- 返回全量部门树，对于expandedNodeIds中的节点，还会返回其全部子节点（包括非部门节点）
--
-- KEYS[1]: expandedNodeIds的数量
-- KEYS[2...n]: expandedNodeIds列表 (e.g., "dept_100", "category_200")

-- 1. 获取当前活跃版本
local currentVersionKey = "tree:current_version"
local currentVersion = redis.call('GET', currentVersionKey)
if not currentVersion then
    currentVersion = "v1" -- 默认版本
end

-- 2. 使用版本化的key前缀
local nodeKeyPrefix = "tree:" .. currentVersion .. ":node:"
local childrenKeyPrefix = "tree:" .. currentVersion .. ":children:"

local virtualRootId = "dept:0" -- Hardcoded virtual root ID

-- 向上查找父节点路径的函数
local function getParentPath(nodeId, visited)
    if visited[nodeId] then
        return {} -- 防止循环引用
    end
    visited[nodeId] = true

    local nodeKey = nodeKeyPrefix .. nodeId
    local nodeData = redis.call('HGETALL', nodeKey)
    if not nodeData or #nodeData == 0 then
        return {}
    end

    -- 将数组转换为哈希表
    local node = {}
    for i = 1, #nodeData, 2 do
        node[nodeData[i]] = nodeData[i + 1]
    end

    local parentPath = {}

    -- 如果有父节点且不是虚拟根节点，继续向上查找
    if node.parentId and node.parentId ~= virtualRootId then
        local parentVisited = {}
        for k, v in pairs(visited) do
            parentVisited[k] = v
        end
        local upperPath = getParentPath(node.parentId, parentVisited)
        for _, parentId in ipairs(upperPath) do
            table.insert(parentPath, parentId)
        end
        -- 直接使用父节点ID，保持与传入参数格式一致
        table.insert(parentPath, node.parentId)
    end

    return parentPath
end

-- 解析expandedNodeIds并扩展父节点路径
local expandedCount = tonumber(KEYS[1]) or 0
local expandedNodeIds = {}

-- 首先收集原始的expandedNodeIds
local originalExpandedIds = {}
local originalExpandedIdsList = {} -- 用于最终返回的expandedNodeIds列表
for i = 2, expandedCount + 1 do
    if KEYS[i] then
        originalExpandedIds[KEYS[i]] = true
        expandedNodeIds[KEYS[i]] = true
        table.insert(originalExpandedIdsList, KEYS[i])
    end
end

-- 为每个原始展开节点查找并添加其父节点路径
for nodeId, _ in pairs(originalExpandedIds) do
    local visited = {}
    local parentPath = getParentPath(nodeId, visited)
    for _, parentId in ipairs(parentPath) do
        expandedNodeIds[parentId] = true
        table.insert(originalExpandedIdsList, parentId)
    end
end

-- 前向声明
local getFullSubtree
local getDirectChildNode
local buildNodeFromData

-- 通用节点构建函数（优化：避免重复代码）
buildNodeFromData = function(node)
    local result = {
        id = node.id,
        name = node.name,
        type = node.type,
        parentId = node.parentId
    }

    -- 根据节点类型添加特定字段
    if node.type == 'dept' then
        result.deptId = node.deptId
        result.total = tonumber(node.total) or 0
        result.onlineNum = tonumber(node.onlineNum) or 0
        result.selfTotal = tonumber(node.selfTotal) or 0
        result.selfOnlineNum = tonumber(node.selfOnlineNum) or 0
    elseif node.type == 'device' then
        result.targetId = node.targetId
        result.createAccount = node.createAccount
        result.category = tonumber(node.category)
        result.deviceType = tonumber(node.deviceType)
        result.deptId = node.deptId
        result.channelNum = tonumber(node.channelNum)
        result.online = tonumber(node.online)
        result.fusionState = tonumber(node.fusionState)
        result.uniqueId = node.uniqueId
        result.acc = tonumber(node.acc)
    elseif node.type == 'target' then
        result.deptId = node.deptId
        result.createAccount = node.createAccount
        result.category = tonumber(node.category)
        result.deviceType = tonumber(node.deviceType)
        result.channelNum = tonumber(node.channelNum)
        result.online = tonumber(node.online)
        result.fusionState = tonumber(node.fusionState)
        result.uniqueId = node.uniqueId
        result.acc = tonumber(node.acc)
        result.total = tonumber(node.total) or 0
        result.onlineNum = tonumber(node.onlineNum) or 0
    elseif node.type == 'device_type' then
        result.deptId = node.deptId
        if node.total then
            result.total = tonumber(node.total) or 0
        end
        if node.onlineNum then
            result.onlineNum = tonumber(node.onlineNum) or 0
        end
    end

    return result
end

-- 获取直接子节点的函数（不递归，只获取节点本身）
getDirectChildNode = function(nodeId)
    local nodeKey = nodeKeyPrefix .. nodeId

    -- 优化：直接使用HGETALL，无需先检查EXISTS
    local nodeData = redis.call('HGETALL', nodeKey)
    if not nodeData or #nodeData == 0 then
        return nil
    end

    -- 将数组转换为哈希表
    local node = {}
    for i = 1, #nodeData, 2 do
        node[nodeData[i]] = nodeData[i + 1]
    end

    -- 使用通用构建函数
    return buildNodeFromData(node)
end



-- 获取完整子树的函数（包含所有类型的节点）
getFullSubtree = function(nodeId, visited)
    if visited[nodeId] then
        return nil -- 防止循环引用
    end
    visited[nodeId] = true

    local nodeKey = nodeKeyPrefix .. nodeId

    -- 优化：直接使用HGETALL，无需先检查EXISTS
    local nodeData = redis.call('HGETALL', nodeKey)
    if not nodeData or #nodeData == 0 then
        return nil
    end

    -- 将数组转换为哈希表
    local node = {}
    for i = 1, #nodeData, 2 do
        node[nodeData[i]] = nodeData[i + 1]
    end

    -- 使用通用构建函数
    local result = buildNodeFromData(node)

    -- 处理子节点
    local childrenKey = childrenKeyPrefix .. nodeId
    local childrenIds = redis.call('ZRANGE', childrenKey, 0, -1)

    -- 优化：只有存在子节点时才创建数组
    if #childrenIds > 0 then
        local childrenNodes = {}
        for _, childId in ipairs(childrenIds) do
            local childNode = getFullSubtree(childId, visited)
            if childNode then
                table.insert(childrenNodes, childNode)
            end
        end

        -- 只有当子节点列表不为空时，才将children字段加入结果中
        if #childrenNodes > 0 then
            result.children = childrenNodes
        end
    end

    return result
end

-- 递归获取部门树的函数（只包含部门节点）
local function getDeptTree(nodeId, visited)
    if visited[nodeId] then
        return nil -- 防止循环引用
    end
    visited[nodeId] = true

    local nodeKey = nodeKeyPrefix .. nodeId

    -- 优化：直接使用HGETALL，无需先检查EXISTS
    local nodeData = redis.call('HGETALL', nodeKey)
    if not nodeData or #nodeData == 0 then
        return nil
    end

    -- 将数组转换为哈希表
    local node = {}
    for i = 1, #nodeData, 2 do
        node[nodeData[i]] = nodeData[i + 1]
    end

    -- 只处理部门类型的节点
    if node.type ~= 'dept' then
        return nil
    end

    -- 优化：预先检查是否展开，避免重复查找
    local isExpanded = expandedNodeIds[nodeId] == true

    -- 构建子节点列表
    local childrenKey = childrenKeyPrefix .. nodeId
    local childrenIds = redis.call('ZRANGE', childrenKey, 0, -1)

    -- 优化：只有存在子节点时才处理
    local childrenNodes = {}
    if #childrenIds > 0 then
        -- 优化：批量获取子节点类型，减少Redis调用
        local childTypes = {}
        for _, childId in ipairs(childrenIds) do
            local childNodeKey = nodeKeyPrefix .. childId
            childTypes[childId] = redis.call('HGET', childNodeKey, 'type')
        end

        for _, childId in ipairs(childrenIds) do
            local childType = childTypes[childId]

            if childType == 'dept' then
                -- 部门节点：递归处理
                local childNode = getDeptTree(childId, visited)
                if childNode then
                    table.insert(childrenNodes, childNode)
                end
            elseif isExpanded then
                -- 非部门节点（终端类型节点）：只有当前节点被展开时才包含
                -- 优化：预先检查子节点是否展开
                local isChildExpanded = expandedNodeIds[childId] == true
                local childNode
                if isChildExpanded then
                    childNode = getFullSubtree(childId, visited)
                else
                    childNode = getDirectChildNode(childId)
                end
                if childNode then
                    table.insert(childrenNodes, childNode)
                end
            end
        end
    end

    -- 使用通用构建函数
    local result = buildNodeFromData(node)

    -- 只有当子节点列表不为空时，才将children字段加入结果中
    if #childrenNodes > 0 then
        result.children = childrenNodes
    end

    return result
end



-- 主逻辑: 始终从虚拟根节点开始构建完整的部门树
local visited = {}
local finalResult = {}

-- 直接从虚拟根的子节点开始
local childrenKey = childrenKeyPrefix .. virtualRootId
local childrenIds = redis.call('ZRANGE', childrenKey, 0, -1)

-- 优化：只有存在子节点时才处理
if #childrenIds > 0 then
    for _, childId in ipairs(childrenIds) do
        local childNode = getDeptTree(childId, visited)
        if childNode then
            table.insert(finalResult, childNode)
        end
    end
end

-- 构建最终返回的TreeResponse结构
local treeResponse = {
    tree = finalResult,
    expandedNodeIds = originalExpandedIdsList
}

-- 返回TreeResponse结构的JSON
return cjson.encode(treeResponse)
