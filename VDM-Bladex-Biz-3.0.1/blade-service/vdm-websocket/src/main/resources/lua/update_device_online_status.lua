--- 更新设备在线状态脚本

-- KEYS[1]: versionedNodeKey (完整的版本化节点键，如 tree:v1:node:device_123 或 tree:v1:node:target_456)
-- ARGV[1]: newOnlineStatus (1表示离线，0表示在线)
-- ARGV[2]: currentVersion (当前版本号，如 v1)
--
-- 原子性地更新设备或目标的状态，并将在线数量变化
-- 向上传播到树形层级结构中，支持版本控制
--
-- 返回值:
-- 1 表示状态已更新
-- 0 表示状态未变化或节点不存在
-- -1 表示出错（如节点数据损坏）

local versionedNodeKey = KEYS[1]
local newStatus = ARGV[1]
local currentVersion = ARGV[2]

-- 1. 检查节点是否存在
if redis.call('EXISTS', versionedNodeKey) == 0 then
    return 0
end

-- 2. 获取当前在线状态和父节点ID
local nodeData = redis.call('HMGET', versionedNodeKey, 'online','parentId','deptId')
local currentStatus = nodeData[1]
local parentId = nodeData[2]
local deptId = nodeData[3]

local deptNodeId = 'dept:' .. deptId

if not currentStatus then
    -- 节点哈希存在但缺少'online'字段
    return 0 -- 或者作为错误处理
end

-- 3. 如果状态没有变化，则不做任何操作
if currentStatus == newStatus then
    return 0
end

-- 4. 确定在线数量的增量值
local increment = (tonumber(newStatus) == 0) and 1 or -1

-- 5. 更新节点的在线状态
redis.call('HSET', versionedNodeKey, 'online', newStatus)

-- 6. 向上遍历树结构并更新父节点的在线数量
while parentId and parentId ~= 'null' and parentId ~= '' do
    -- 构造版本化的父节点键
    local versionedParentKey = "tree:" .. currentVersion .. ":node:" .. parentId

    if redis.call('EXISTS', versionedParentKey) == 0 then
        -- 父节点不存在，停止传播
        break
    end

    if deptNodeId == parentId then
        -- 增加直属部门的在线数量
        redis.call('HINCRBY', versionedParentKey, 'selfOnlineNum', increment)
    end

    -- 增加父节点的在线数量
    redis.call('HINCRBY', versionedParentKey, 'onlineNum', increment)

    -- 获取下一个父节点的ID
    nodeData = redis.call('HMGET', versionedParentKey, 'parentId')
    parentId = nodeData[1]
end


return 1
