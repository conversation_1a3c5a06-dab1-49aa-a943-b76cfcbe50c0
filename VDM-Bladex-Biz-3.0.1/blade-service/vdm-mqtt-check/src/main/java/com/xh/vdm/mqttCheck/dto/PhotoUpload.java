package com.xh.vdm.mqttCheck.dto;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PhotoUpload {
    private String userNumber;         // 用户工号
    private String deviceNumber;       // 设备号
    private String longitude;          // 经度
    private String latitude;           // 纬度
    private String altitude;           // 海拔
    private int speed;                 // 速度
    private String direction;          // 方向
    private String timestamp;          // 当前时间 (Unix时间戳)
    private byte[] fileObject;         // 上传文件对象
    private int crcChecksum;           // CRC 校验
}
