package com.xh.vdm.mqttCheck.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *Cat1手表后台发布文本数据
 * 示例1，填写所有字段，自定义标题
 * cd,1,1680106907,祝大家国庆节快乐！,紧急会议,发送人
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DataNotify {
    /**
     *帧类型名
     *cd
     */
    private String fr;
    /**
     *优先级：3>2>1
     * 数据优先级：当消息同时到达手表时，按照数据的重要度显示。（SOS>其他告警>业务数据（本字段）>其他）。
     */
    private Integer pri;
    /**
     *时间（精确到秒）
     */
    private Integer ct;
    /**
     *文本数据
     * 文本中使用\,代替,
     */
    private String msg;
    /**
     *标题
     * 默认为【通知】
     */
    private String title;
    /**
     *发送人
     * 缺省时，手表UI不展示发送人的
     */
    private String sender;
    /**
     * 终端唯一编号
     */
	@JsonProperty("device_id")
	@JSONField(name = "device_id")
    private String deviceId;

	@JsonProperty("device_no")
	@JSONField(name = "device_no")
    private String deviceNo;

	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private Date dateTime;
}
