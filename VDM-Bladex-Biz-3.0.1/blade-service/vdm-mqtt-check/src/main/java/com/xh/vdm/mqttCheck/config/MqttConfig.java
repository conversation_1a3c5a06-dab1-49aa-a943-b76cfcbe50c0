package com.xh.vdm.mqttCheck.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/5/5 8:41
 */
@Configuration
@Data
@ConfigurationProperties(prefix = "mqtt")
public class MqttConfig {
    private String username;
    private String password;
    private String broker;
    private Topic topic;
    private String clientId;
    private int messageInterval = 360;
    private int qos;

    public Topic getTopic() {
        return topic;
    }

    @Setter
    @Getter
    public static class Topic {
        private Subscription subscription;
        private Publish publish;
    }

    @Setter
    @Getter
    public static class Subscription {
        //赋码参数上报
        private String  deviceNumReport;
        //终端鉴权
        private String deviceAuth;
        //实时位置上报
        private String positionReport;
        //补传定位数据
        private String positionBatch;
    }

    @Setter
    @Getter
    public static class Publish {
        //赋码参数查询
        private String deviceNumQuery;
        //终端赋码
        private String deviceNumSet;
        //终端鉴权结果
        private String deviceAuthResult;
        //天气下发
        private String weatherSend;
        //数据下发
        private String dataSend;
        //模式频率控制
        private String frequencyControl;
        //远程参数配置
        private String remoteParamSet;
        //星历文件更新
        private String starUpdate;
    }
}
