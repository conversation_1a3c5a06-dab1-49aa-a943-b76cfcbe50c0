package com.xh.vdm.mqttCheck.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.mqttCheck.entity.BdcTerminal;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface BdcTerminalMapper extends BaseMapper<BdcTerminal> {
    @Select("select * from bd_check.bdc_terminal where is_del=0")
    List<BdcTerminal> queryAll();
}

