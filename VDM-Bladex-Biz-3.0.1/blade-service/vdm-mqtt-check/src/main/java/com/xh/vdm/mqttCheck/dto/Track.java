package com.xh.vdm.mqttCheck.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;
import java.util.HashMap;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Track {
	@JSONField(name = "device_id")
    private long deviceId;

	@JSONField(name = "device_type")
    private int deviceType;

	@JSONField(name = "device_model")
    private String deviceModel;

	@JSONField(name = "device_no")
    private String deviceNo;

	@JSONField(name = "device_unique_id")
    private String deviceUniqueId;

	@JSONField(name = "target_id")
    private long targetId;

	@JSONField(name = "target_type")
    private int targetType;

	@JSONField(name = "target_name")
    private String targetName;

	@JSONField(name = "device_num")
    private String deviceNum;

	@JSONField(name = "dept_id")
    private long deptId;

	@JSONField(name = "longitude")
    private double longitude;

	@JSONField(name = "latitude")
    private double latitude;

	@JSONField(name = "altitude")
    private int altitude;

	@JSONField(name = "speed")
    private double speed;

	@JSONField(name = "bearing")
    private int bearing;

	@JSONField(name = "alarm_flag")
    private long alarmFlag;

	@JSONField(name = "loc_time")
    private long locTime;

	@JSONField(name = "recv_time")
    private long recvTime;

	@JSONField(name = "valid")
    private int valid;

	@JSONField(name = "batch")
    private int batch;

	@JSONField(name = "aux_str")
    private String auxStr;

	@JSONField(name = "auxs_normal")
    private HashMap<String, Object> auxsNormal;

	@JSONField(name = "loc_time_f",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private Date locTimeF;

	@JSONField(name = "recv_time_f",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private Date recvTimeF;
}

