package com.xh.vdm.mqttCheck.mqtt.handler.watch;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttCheck.config.MqttConfig;
import com.xh.vdm.mqttCheck.constant.CommEnum;
import com.xh.vdm.mqttCheck.constant.CommonConstant;
import com.xh.vdm.mqttCheck.dto.*;
import com.xh.vdm.mqttCheck.entity.BdcTerminal;
import com.xh.vdm.mqttCheck.kafka.KafkaProducerService;
import com.xh.vdm.mqttCheck.mqtt.client.IMqttClient;
import com.xh.vdm.mqttCheck.mqtt.handler.IMqttMessageHandlerV5;
import com.xh.vdm.mqttCheck.mqtt.handler.MessageHandlerAbst;
import com.xh.vdm.mqttCheck.util.StringUtil;
import com.xh.vdm.mqttCheck.util.TopicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.xh.vdm.mqttCheck.config.WearableWorkerCache.BdcTerminalMap;
import static com.xh.vdm.mqttCheck.util.CoordinateTransform.wgs84ToGcj02;

@Slf4j
@Component
public class ReportDataMessageHandler extends MessageHandlerAbst implements IMqttMessageHandlerV5 {

    /**
     * 设备号位置
     */
    private final int  deviceIndex;
    private static String deviceId;
    @Autowired
    StringRedisTemplate redisTemplate;
    @Autowired
    KafkaProducerService kafkaProducerService;

    public ReportDataMessageHandler(List<IMqttClient> messageClientList, MqttConfig mqttConfig) {
        super();
        this.setSubTopic(mqttConfig.getTopic().getSubscription().getPositionReport());
        this.deviceIndex = Arrays.asList(subTopic.split(CommonConstant.MqttConstant.SLASH)).indexOf(CommonConstant.MqttConstant.PLUS_SIGN);
        messageClientList.forEach(client -> {
            addClient(client.version(), client);
            client.addMessageHandler(this);
        });
    }

    @Override
    public void process(String topic, String msg) {
        deviceId = topic.split(CommonConstant.MqttConstant.SLASH)[deviceIndex];
        List<RealTimeDataReportMsg> dtoList = analysisMessageToList(msg);
        log.info("ReportDataMessageHandler process received deviceId：{}, msg: {}", deviceId, JSON.toJSONString(dtoList));
    }
    private List<RealTimeDataReportMsg> analysisMessageToList(String msg) {
        List<RealTimeDataReportMsg> list = new ArrayList<>();
        String[] params = msg.split(CommonConstant.MqttConstant.SEPARATOR);
        if (params.length<14){
            log.error("analysisMessageToList [ERROR] 位置推送消息参数小于14个:{}",params.length);
            return list;
        }
        RealTimeDataReportMsg dto=new RealTimeDataReportMsg();
        dto.setCt(params[0] == null? 0L: Long.parseLong(params[0]));
        dto.setLng(params[1] == null? 0F:Double.parseDouble(params[1]));
        dto.setLat(params[2] == null? 0F:Double.parseDouble(params[2]));
        dto.setAlt(params[3] == null? 0F:Double.parseDouble(params[3]));
        dto.setSpd(params[4] == null? 0F:Double.parseDouble(params[4]));
        dto.setDir(params[5] == null? 0F:Double.parseDouble(params[5]));
        dto.setNloc(params[6] == null? 0:(int)Long.parseLong(params[6]));
        dto.setTyp(params[7] == null? 0:(int)Long.parseLong(params[7]));
        dto.setBat(params[8] == null? 0:(int)Long.parseLong(params[8]));
        dto.setAlm(params[9] == null? 0:(int)Long.parseLong(params[9]));
        dto.setHr(params[10] == null? 0:(int)Long.parseLong(params[10]));
        dto.setSpo2(params[11] == null? 0:(int)Long.parseLong(params[11]));
        dto.setBluetooth(params[12] == null? 0:(int)Long.parseLong(params[12]));
        dto.setEvent(params[13] == null? 0:(int)Long.parseLong(params[13]));
		List<Satellite> sateList=new ArrayList<>();
		if (params.length==15){
			dto.setSateHexDes(params[14] == null? "":params[14]);
			sateList= analysisSatesInfo(dto.getSateHexDes());
		}
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceId, CommEnum.PositionReport.getKey());
        NetCheck netCheck=new NetCheck(msg,"通过",new Date(dto.getCt()*1000));
        redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck),30*60, TimeUnit.SECONDS);
		sendCheckReportDataToKafka(dto,sateList);
        list.add(dto);
        return list;
    }
    public void sendCheckReportDataToKafka(RealTimeDataReportMsg msg, List<Satellite> sateList){
        Track track=new Track();
        BdcTerminal bdcTerminal=BdcTerminalMap.get(deviceId);
        if (bdcTerminal==null){
            log.error("BdcTerminalMap 中没有获取到设备终端信息:{}",deviceId);
        }else{
			track.setDeviceModel(bdcTerminal.getDeviceModel());
			track.setDeviceNum(bdcTerminal.getDeviceNum());
		}
        track.setDeviceNo(deviceId);
        track.setDeviceUniqueId(deviceId);
        track.setLocTime(msg.getCt());
        track.setRecvTime(System.currentTimeMillis()/1000);
        track.setLocTimeF(new Date(msg.getCt()*1000));
        track.setRecvTimeF(new Date(System.currentTimeMillis()));
        track.setLongitude(msg.getLng());
        track.setLatitude(msg.getLat());
        track.setAltitude(msg.getAlt().intValue());
        track.setSpeed(msg.getSpd());
        track.setBearing(msg.getDir().intValue());
        track.setAlarmFlag(msg.getAlm());
        track.setValid(msg.getNloc());
        TrackAux trackAux=new TrackAux();
        trackAux.setBluetooth(msg.getBluetooth());
        trackAux.setBloodOxygen(msg.getSpo2());
        trackAux.setBatteryLevel(msg.getBat());
        trackAux.setHeartRate(msg.getHr());
        trackAux.setLocationType(msg.getTyp());
        trackAux.setEventType(msg.getEvent());
        track.setAuxStr(JSON.toJSONString(trackAux));
        HashMap<String, Object> auxMap=new HashMap<>();
        auxMap.put("255",sateList);
        track.setAuxsNormal(auxMap);
        //经纬坐标转换
//        if (msg.getTyp()==0){
//            double[] cors=wgs84ToGcj02(msg.getLat(),msg.getLng());
//            track.setLatitude(cors[0]);
//            track.setLongitude(cors[1]);
//        }
        kafkaProducerService.sendCheckPositionReportMessage(JSON.toJSONString(track),track.getDeviceUniqueId());
    }

    public static List<Satellite> analysisSatesInfo(String satesStr){
        List<Satellite> list = new ArrayList<>();
        List<String> listSateStr=StringUtil.splitString(satesStr,14);
        for (String s : listSateStr) {
            list.add(analysisSateInfo(s));
        }
        return list;
    }
    public static Satellite analysisSateInfo(String sateStr){
        Satellite sate=new Satellite();
        if (sateStr.length()!=14){
            return sate;
        }
        sate.setType(Integer.parseInt(sateStr.substring(0,2),16));
        sate.setId(Integer.parseInt(sateStr.substring(2,4),16));
        sate.setElevation(Integer.parseInt(sateStr.substring(4,6),16));
        sate.setAzimuth(Integer.parseInt(sateStr.substring(6,10),16));
        sate.setRatio(Integer.parseInt(sateStr.substring(10,12),16));
        sate.setFlag(Integer.parseInt(sateStr.substring(12,14),16));
        return sate;
    }

    @Override
    public boolean match(String topic) {
        return TopicUtils.math(subTopic, topic, deviceIndex);
    }
}
