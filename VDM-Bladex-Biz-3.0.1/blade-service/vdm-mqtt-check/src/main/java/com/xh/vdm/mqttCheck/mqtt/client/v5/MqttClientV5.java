package com.xh.vdm.mqttCheck.mqtt.client.v5;

import com.xh.vdm.mqttCheck.config.MqttConfig;
import com.xh.vdm.mqttCheck.constant.CommonConstant;
import com.xh.vdm.mqttCheck.mqtt.client.IMqttClient;
import com.xh.vdm.mqttCheck.mqtt.handler.IMqttMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.client.IMqttAsyncClient;
import org.eclipse.paho.mqttv5.client.IMqttToken;
import org.eclipse.paho.mqttv5.client.MqttAsyncClient;
import org.eclipse.paho.mqttv5.client.MqttConnectionOptions;
import org.eclipse.paho.mqttv5.client.persist.MqttDefaultFilePersistence;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.eclipse.paho.mqttv5.common.MqttSubscription;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class MqttClientV5 implements IMqttClient {

    private final IMqttAsyncClient mqttClient;

    private final List<MqttSubscription> subscriptionList = new ArrayList<>();

    private final MessageCallbackV5 messageCallbackV5;

    public MqttClientV5(MqttConfig mqttConfig) throws MqttException {
        log.info("MqttClientV5 init");
        MqttDefaultFilePersistence persistence = new MqttDefaultFilePersistence();
        MqttConnectionOptions connOpts = new MqttConnectionOptions();
        connOpts.setUserName(mqttConfig.getUsername());
        connOpts.setPassword(mqttConfig.getPassword().getBytes());
        connOpts.setCleanStart(false);
        connOpts.setAutomaticReconnect(true);
        connOpts.setKeepAliveInterval(60);
        IMqttAsyncClient mqttAsyncClient = new MqttAsyncClient(mqttConfig.getBroker(),
                mqttConfig.getClientId() + CommonConstant.MqttConstant.COLON + version(),
                persistence);

        IMqttToken token = mqttAsyncClient.connect(connOpts);
        token.waitForCompletion();
        MqttConfig.Subscription subscription = mqttConfig.getTopic().getSubscription();
        int qos = mqttConfig.getQos();
        addSubscriptionList(qos, subscription.getDeviceNumReport());
        addSubscriptionList(qos, subscription.getDeviceAuth());
        addSubscriptionList(qos, subscription.getPositionReport());
        addSubscriptionList(qos, subscription.getPositionBatch());
        this.messageCallbackV5 = new MessageCallbackV5(this);
        this.mqttClient = mqttAsyncClient;
        mqttAsyncClient.setCallback(messageCallbackV5);
        subscribe();
    }

    @Override
    public String version() {
        return CommonConstant.MqttConstant.MQTT_VERSION_5;
    }

    @Override
    public boolean isConnected() {
        return mqttClient.isConnected();
    }

    @Override
    public boolean reconnect() {
        try {
            if (!mqttClient.isConnected()) {
                mqttClient.reconnect();
            }
            return true;
        } catch (MqttException e) {
            log.error("reconnect error", e);
        }
        return false;
    }

    @Override
    public boolean publishMessage(String topic, byte[] payload, int qos, boolean retained) {
        if (topic == null || topic.trim().length() == 0) {
            throw new IllegalArgumentException("publishMessage topic can not be null or empty");
        }
        try {
            mqttClient.publish(topic, payload, qos, retained);
            return true;
        } catch (MqttException e) {
            log.error("publishMessage error topic -> [{}] ", topic, e);
        }
        return false;
    }

    @Override
    public void addMessageHandler(IMqttMessageHandler messageHandler) {
        messageCallbackV5.addMessageHandler(messageHandler);
    }

    @PreDestroy
    private void destroy() {
        try {
            log.warn("MqttClientV5 destroy");
            mqttClient.disconnect();
            mqttClient.close();
        } catch (MqttException e) {
            log.error("MqttClientV5 destroy error", e);
        }
    }

    public IMqttToken subscribe() throws MqttException {
        if (mqttClient == null) {
            log.error("mqttClient is null");
            return null;
        }

        if (subscriptionList.isEmpty()) {
            log.error("subscriptionList is empty");
            return null;
        }
        return mqttClient.subscribe(this.subscriptionList.toArray(new MqttSubscription[0]));
    }

    private void addSubscriptionList(int qos, String topic) {
        if (topic == null || topic.trim().length() == 0) {
            return;
        }
        subscriptionList.add(new MqttSubscription(topic, qos));
    }

}
