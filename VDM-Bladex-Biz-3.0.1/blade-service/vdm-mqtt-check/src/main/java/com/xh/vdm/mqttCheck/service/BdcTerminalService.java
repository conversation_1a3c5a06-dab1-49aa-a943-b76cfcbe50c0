package com.xh.vdm.mqttCheck.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.mqttCheck.entity.BdcTerminal;
import com.xh.vdm.mqttCheck.mapper.BdcTerminalMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class BdcTerminalService extends ServiceImpl<BdcTerminalMapper, BdcTerminal> {
    public BdcTerminal queryByDeviceNo(String deviceNo){
        return this.query().eq("device_no",deviceNo).eq("is_del",0).one();
    }

    public List<BdcTerminal> getAllTerminals(){
        return this.baseMapper.queryAll();
    }

    public List<BdcTerminal> getWearableTerminals(){
        return this.query().eq("protocol","MQTT").eq("is_del",0).list();
    }

    public List<BdcTerminal> getTerminalsByReportId(Long reportId){
        return this.query().eq("report_id",reportId).eq("is_del",0).list();
    }
    public BdcTerminal queryByDeviceNum(String deviceNum){
        return this.query().eq("device_num",deviceNum).eq("is_del",0).one();
    }

    public boolean updateTestResultBySim(String deviceNo,Integer result,String testMsg){
        UpdateWrapper<BdcTerminal> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("test_result", result);
        updateWrapper.set("test_res_message", testMsg);
        updateWrapper.set("test_time", new Date());
        updateWrapper.eq("device_no", deviceNo).eq("is_del", 0);
        return update(updateWrapper);
    }
}
