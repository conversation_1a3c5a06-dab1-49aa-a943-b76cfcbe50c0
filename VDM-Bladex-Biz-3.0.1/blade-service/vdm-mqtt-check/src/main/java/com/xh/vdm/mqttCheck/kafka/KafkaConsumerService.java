package com.xh.vdm.mqttCheck.kafka;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttCheck.config.WearableWorkerCache;
import com.xh.vdm.mqttCheck.entity.BdcTerminal;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KafkaConsumerService {
    @Autowired
    WearableWorkerCache wearableWorkerCache;
    //@KafkaListener(topics = "device_target_change_check_topic", groupId = "bdc_terminal_change_watch")
	@KafkaListener(topics = "device_target_change_check_topic", groupId = "#{'bdc_terminal_change_watch_' + T(java.net.InetAddress).getLocalHost().getHostAddress().replace('.', '_')}")
    public void listenBdcTerminal(String msg) {
        BdcTerminal bdcTerminal=JSON.parseObject(msg,BdcTerminal.class);
        log.info("kafka listenBdcTerminal listen receive:{},{}",bdcTerminal.getSim(),msg);
        wearableWorkerCache.initBdcTerminalCache();
    }
}

