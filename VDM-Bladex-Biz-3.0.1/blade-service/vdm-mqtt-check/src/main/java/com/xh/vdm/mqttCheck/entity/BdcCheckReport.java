package com.xh.vdm.mqttCheck.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import java.util.Date;

@Data
@TableName("bd_check.bdc_check_report")
public class BdcCheckReport {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("batch_no")
    private String batchNo;

    @TableField("company_id")
    private Long companyId;

    @TableField("check_start_time")
    private Date checkStartTime;

    @TableField("check_end_time")
    private Date checkEndTime;

    @TableField("report_time")
    private Date reportTime;

    @TableField("check_type")
    private String checkType;

    @TableField("check_address")
    private String checkAddress;

    @TableField("check_base")
    private String checkBase;

    @TableField("check_method")
    private String checkMethod;

    @TableField("check_result")
    private Short checkResult;

    @TableField("check_res_message")
    private String checkResMessage;

    @TableField("edit_person")
    private String editPerson;

    @TableField("audit_person")
    private String auditPerson;

    @TableField("approve_person")
    private String approvePerson;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("state")
    private String state;

    @TableField("check_process")
    private String checkProcess;

    @TableField("terminal_type")
    private String terminalType;

    @TableField("terminal_model")
    private String terminalModel;

    @TableField("report_file")
    private String reportFile;

    @TableField("report_no")
    private String reportNo;

    @TableField("terminal_cate")
    private String terminalCate;

    @TableField("test_result")
    private Short testResult;

    @TableField("test_time")
    private Date testTime;

    @TableField("send_date")
    private Date sendDate;
}

