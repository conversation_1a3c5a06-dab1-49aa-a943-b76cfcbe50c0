package com.xh.vdm.mqttCheck.constant;

public interface CommonConstant {
    interface MqttConstant {
        int QOS_0 = 0;
        int QOS_1 = 1;
        int QOS_2 = 2;
        String FRAME_HEADER_EB90 ="EB90";
        String FRAME_HEADER_EB91 ="EB91";
        String FRAME_HEADER_EB92 ="EB92";
        String SEPARATOR = ",";
        String PLUS_SIGN = "+";
        String SLASH = "/";
        String COLON = ":";
        String EMPTY = "";
        String POINT = ".";
        String PLACEHOLDER = "%s";
        String MQTT_VERSION_3_1_1 = "3.1.1";
        String MQTT_VERSION_5 = "5.0";
    }

    interface RedisConstant {
        String MQTT_WATCH="MQTT:DEVICE_NUM:WATCH:";
        String NET_CHECK="MQTT:NET_CHECK:WATCH:";


    }
    interface LocConstant {
        String LOC_WATCH="MQTT:LOC:WATCH:";
    }

}
