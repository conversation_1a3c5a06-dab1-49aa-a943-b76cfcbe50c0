package com.xh.vdm.mqttCheck.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 终端鉴权结果
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceAuthResult {
    /**
     * 鉴权结果
     * 0-成功，1-设备类别标识错误，2-生产厂商编号错误，3-产品型号错误，4-产品序列号错误，5-IMEI号错误，6-北斗芯片序列号错误，7-赋码值错误，8-其他错误，9-设备未录入
     */
    byte result;
    /**
     * 终端唯一编号
     */
	@JsonProperty("device_id")
	@JSONField(name = "device_id")
    private String deviceId;

	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private Date dateTime;
}
