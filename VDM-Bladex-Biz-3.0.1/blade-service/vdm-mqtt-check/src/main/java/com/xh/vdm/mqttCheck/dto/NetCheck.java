package com.xh.vdm.mqttCheck.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NetCheck {
    @JsonProperty("content")
    private String content;
    @JsonProperty("result")
    private String result;
	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private Date dateTime;
}
