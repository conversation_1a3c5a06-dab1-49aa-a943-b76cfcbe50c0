package com.xh.vdm.mqttCheck.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *Cat1手表后台下发最新天气信息（根据手表最后上传的位置）
 * 示例3：雷电暴雨预警3-4级，风向:西南，预计发生于2023.5.4
 * wx,21,1680106907,8~10,西南,2023.5.4
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WeatherNotifyMsg {
    /**
     *帧类型名
     *wx
     */
    private String fr;
    /**
     *天气编号：0晴、1阴、2雨 3、4雪、11台风蓝色预警、12台风黄色预警、13台风橙色预警、14台风红色预警、21雷电暴雨、31地震预警
     */
    private Integer wxt;
    /**
     *时间（精确到秒）
     */
    private Integer ct;
    /**
     *等级范围如：8~10
     */
    private String lvl;
    /**
     *风向如：西南
     */
    private String dir;
    /**
     *预计发生时间如：2023.5.4
     */
    private String pred;
    /**
     * 终端唯一编号
     */
	@JsonProperty("device_id")
	@JSONField(name = "device_id")
    private String deviceId;

	@JsonProperty("date_time")
	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private Date dateTime;
}
