package com.xh.vdm.mqttCheck.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StartCheckParam {
    @JsonProperty("device_no")
    private String deviceNo;
    @JsonProperty("ids")
    private List<String> idList;
}
