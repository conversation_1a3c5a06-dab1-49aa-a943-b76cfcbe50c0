package com.xh.vdm.mqttCheck.controller;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xh.vdm.mqttCheck.config.MqttConfig;
import com.xh.vdm.mqttCheck.config.WearableWorkerCache;
import com.xh.vdm.mqttCheck.constant.CommEnum;
import com.xh.vdm.mqttCheck.constant.CommonConstant;
import com.xh.vdm.mqttCheck.dto.*;
import com.xh.vdm.mqttCheck.entity.BdcTerminal;
import com.xh.vdm.mqttCheck.mqtt.client.v3.MqttClientV3;
import com.xh.vdm.mqttCheck.service.BdcCheckReportService;
import com.xh.vdm.mqttCheck.service.BdcTerminalService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/mqtt")
public class SendController {

	@Value("${custom.bd-check-addr}")
	private String bdCheckAddr;
    String payloadStr;
    Integer checkResult;
    Integer reportResult;
    @Resource
    MqttConfig mqttConfig;
    @Resource
    MqttClientV3 mqttClientV3;
    @Autowired
    StringRedisTemplate redisTemplate;
    @Autowired
    BdcTerminalService bdcTerminalService;
    @Autowired
    BdcCheckReportService bdcCheckReportService;
    @Autowired
    WearableWorkerCache wearableWorkerCache;

    @PostMapping("/send/weather")
    public HttpParamResponse WeatherSend(@RequestBody WeatherNotifyMsg param) {
        log.info("WeatherNotifyMsg input param is {}", param);
        String paramStr=String.format("%s,%d,%d,%s,%s,%s",param.getFr(),param.getWxt(),param.getCt(),param.getLvl(),param.getDir(),param.getPred());
        String setTopic =mqttConfig.getTopic().getPublish().getWeatherSend().replace(CommonConstant.MqttConstant.PLACEHOLDER,param.getDeviceId());
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,param.getDeviceId(), CommEnum.WeatherSend.getKey());
        NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
        redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        return DeviceNumController.getHttpParamResponse(paramStr, setTopic, mqttClientV3);
    }

    @PostMapping("/send/data")
    public HttpParamResponse DataSend(@RequestBody DataNotify param) {
        log.info("WeatherNotifyMsg input param is {}", param);
        String paramStr=String.format("%s,%d,%d,%s,%s,%s",param.getFr(),param.getPri(),param.getCt(),param.getMsg(),param.getTitle(),param.getSender());
        String setTopic =mqttConfig.getTopic().getPublish().getDataSend().replace(CommonConstant.MqttConstant.PLACEHOLDER,param.getDeviceId());
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,param.getDeviceId(), CommEnum.DataSend.getKey());
        NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
        redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        return DeviceNumController.getHttpParamResponse(paramStr, setTopic, mqttClientV3);
    }

    @PostMapping("/send/frequency")
    public HttpParamResponse FrequencyControl(@RequestBody ModeFrequencyControl param) {
        log.info("FrequencyControl input param is {}", param);
        String paramStr=String.format("%s,%s,%d,%d,%s",param.getFr(),param.getTyp(),param.getMo(),param.getSts(),param.getFrq());
        String setTopic =mqttConfig.getTopic().getPublish().getFrequencyControl().replace(CommonConstant.MqttConstant.PLACEHOLDER,param.getDeviceId());
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,param.getDeviceId(), CommEnum.FrequencyControl.getKey());
        NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
        redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        return DeviceNumController.getHttpParamResponse(paramStr, setTopic, mqttClientV3);
    }

    @PostMapping("/send/remoteparamset")
    public HttpParamResponse RemoteParamSet(@RequestBody RemoteParamSet param) {
        log.info("RemoteParamSet input param is {}", param);
        String paramStr=String.format("%s,%d,%d,%d",param.getFrameType(),param.getCategory(),param.getSwitchStatus(),param.getTimestamp());
        String setTopic =mqttConfig.getTopic().getPublish().getRemoteParamSet().replace(CommonConstant.MqttConstant.PLACEHOLDER,param.getDeviceId());
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,param.getDeviceId(), CommEnum.RemoteParamSet.getKey());
        NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
        redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        return DeviceNumController.getHttpParamResponse(paramStr, setTopic, mqttClientV3);
    }

    @PostMapping("/send/updatestar")
    public HttpParamResponse UpdateStarCalendar(@RequestBody StarCalendarUpdate param) {
        log.info("UpdateStarCalendar input param is {}", param);
        String paramStr=String.format("%s%s", Hex.encodeHexString(param.getEphemerisFile()),Integer.toHexString(param.getCrcChecksum()));
        String setTopic =mqttConfig.getTopic().getPublish().getStarUpdate();
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,CommEnum.DataSend.getKey(), CommEnum.DataSend.getKey());
        NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
        redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        return DeviceNumController.getHttpParamResponse(paramStr, setTopic, mqttClientV3);
    }

    @PostMapping("/send/startcheck")
    public HttpParamResponse StartCheck(@RequestBody StartCheckParam param) throws IOException, URISyntaxException {
        log.info("StartCheck input param is {}", param);
        //开始进行协议检测
        HashMap<String, NetCheck> map = new HashMap<>();
        String deviceNo=param.getDeviceNo();
        //终端赋码
        payloadStr=String.format("%s,%s","TNCE012404090001","0");
        String topic =mqttConfig.getTopic().getPublish().getDeviceNumSet().replace(CommonConstant.MqttConstant.PLACEHOLDER,deviceNo);
        boolean deviceNumSetRes=mqttClientV3.publishMessage(topic,payloadStr.getBytes(StandardCharsets.UTF_8),2,false);
        if (deviceNumSetRes){
            String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceNo, CommEnum.DeviceNumSet.getKey());
            NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
            redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        }
        //赋码参数查询
        payloadStr= String.format("%s","device_num_query_test");
        String queryTopic =mqttConfig.getTopic().getPublish().getDeviceNumQuery().replace(CommonConstant.MqttConstant.PLACEHOLDER,deviceNo);
        boolean queryRes=mqttClientV3.publishMessage(queryTopic,payloadStr.getBytes(StandardCharsets.UTF_8),2,false);
        if (queryRes){
            String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceNo, CommEnum.DeviceNumQuery.getKey());
            NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
            redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        }
        //天气下发
        payloadStr= String.format("%s,%d,%d,%s,%s,%s","wx",31,System.currentTimeMillis()/1000,"8-10","西南","2026.6.4");
        String weatherTopic =mqttConfig.getTopic().getPublish().getWeatherSend().replace(CommonConstant.MqttConstant.PLACEHOLDER,deviceNo);
        boolean weatherRes=mqttClientV3.publishMessage(weatherTopic,payloadStr.getBytes(StandardCharsets.UTF_8),2,false);
        if (weatherRes){
            String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceNo, CommEnum.WeatherSend.getKey());
            NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
            redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        }
        //数据下发
        payloadStr= String.format("%s,%d,%d,%s,%s,%s","cd",3,System.currentTimeMillis()/1000,"学习强国","学习强国","发送人");
        String dataTopic =mqttConfig.getTopic().getPublish().getDataSend().replace(CommonConstant.MqttConstant.PLACEHOLDER,deviceNo);
        boolean dataRes=mqttClientV3.publishMessage(dataTopic,payloadStr.getBytes(StandardCharsets.UTF_8),2,false);
        if (dataRes){
            String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceNo, CommEnum.DataSend.getKey());
            NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
            redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        }
        //模式频率控制
        payloadStr= String.format("%s,%s,%s,%s,%s","","","","","");
        String freTopic =mqttConfig.getTopic().getPublish().getFrequencyControl().replace(CommonConstant.MqttConstant.PLACEHOLDER,deviceNo);
        boolean freRes=mqttClientV3.publishMessage(freTopic,payloadStr.getBytes(StandardCharsets.UTF_8),2,false);
        if (freRes){
            String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceNo, CommEnum.FrequencyControl.getKey());
            NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
            redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        }
        //远程参数配置
        payloadStr= String.format("%s,%d,%d,%d","",1,1,System.currentTimeMillis()/1000);
        String remoteTopic =mqttConfig.getTopic().getPublish().getRemoteParamSet().replace(CommonConstant.MqttConstant.PLACEHOLDER,deviceNo);
        boolean remoteRes=mqttClientV3.publishMessage(remoteTopic,payloadStr.getBytes(StandardCharsets.UTF_8),2,false);
        if (remoteRes){
            String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceNo, CommEnum.RemoteParamSet.getKey());
            NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
            redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        }
        //星历文件更新
        byte[] byteArray = {0x01, 0x2A, (byte) 0x2F};
        payloadStr= String.format("%s%s", Hex.encodeHexString(byteArray),Integer.toHexString(48));
        String starTopic =mqttConfig.getTopic().getPublish().getStarUpdate();
        boolean starRes=mqttClientV3.publishMessage(starTopic,payloadStr.getBytes(StandardCharsets.UTF_8),0,false);
        if (starRes){
            String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceNo,CommEnum.StarFileUpdate.getKey());
            NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
            redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        }
        checkResult=1;
        for (String protocol:param.getIdList()) {
            CommEnum ce=CommEnum.getByKey(protocol);
            if (ce==null) {
                NetCheck netCheck =new NetCheck();
                netCheck.setResult("不通过");
                map.put(protocol,netCheck);
                checkResult=2;
                continue;
            }
            String ceValue=ce.getValue();
            String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceNo, protocol);
            if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))) {
                String redisValue = redisTemplate.opsForValue().get(redisKey);
                try{
                    NetCheck netCheck = JSONObject.parseObject(redisValue, NetCheck.class);
                    map.put(ceValue,netCheck);
                }catch(Exception e){
                    log.info("Exception 获取redis的值parseObject报错:{}",e.getMessage());
                    NetCheck netCheck =new NetCheck();
                    netCheck.setResult("不通过");
                    map.put(ceValue,netCheck);
                    checkResult=2;
                }
            }else {
                NetCheck netCheck =new NetCheck();
                netCheck.setResult("不通过");
                map.put(ceValue,netCheck);
                checkResult=2;
            }
        }
        BdcTerminal terminal=bdcTerminalService.queryByDeviceNo(deviceNo);
        boolean terminalRes=bdcTerminalService.updateTestResultBySim(deviceNo,checkResult, JSON.toJSONString(map));
        if (Boolean.FALSE.equals(terminalRes)){
            return new HttpParamResponse(1,"updateTestResultBySim fail",map);
        }
        List<BdcTerminal> terminals=bdcTerminalService.getTerminalsByReportId(terminal.getReportId());
        reportResult=1;
        for (BdcTerminal ter:terminals) {
            if (ter.getTestResult()!=1){
                reportResult=0;
                break;
            }
        }
        boolean reportRes=bdcCheckReportService.updateTestResultById(reportResult,terminal.getReportId());
        if (Boolean.FALSE.equals(reportRes)){
            return new HttpParamResponse(2,"updateTestResultById fail",map);
        }
        String bdCheckUrl=String.format("http://%s/check/startBDCheck?ids=%d", bdCheckAddr,terminal.getReportId());
        log.info("bdCheckUrl:{}",bdCheckUrl);
        //创建httpClient实例
        CloseableHttpClient client = HttpClients.createDefault();
        //创建一个uri对象
        URIBuilder uriBuilder = new URIBuilder(bdCheckUrl);
        HttpGet httpGet = new HttpGet(uriBuilder.build());
        CloseableHttpResponse response = client.execute(httpGet);
        //获取响应实体, 响应内容
        HttpEntity entity = response.getEntity();
        //通过EntityUtils中的toString方法将结果转换为字符串
        String entityStr = EntityUtils.toString(entity);
        response.close();
        client.close();
        HashMap<String, String> resMap = new HashMap<>();
        resMap.put("北斗检测结果",entityStr);
        return new HttpParamResponse(200,"ok",resMap);
    }
}
