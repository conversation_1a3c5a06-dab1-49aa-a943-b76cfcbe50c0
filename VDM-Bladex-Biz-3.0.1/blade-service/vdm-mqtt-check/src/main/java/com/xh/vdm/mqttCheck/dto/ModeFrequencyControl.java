package com.xh.vdm.mqttCheck.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *Cat1手表后台修改手表的各模式的频率（自定义）
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ModeFrequencyControl {
    /**
     *帧类型名
     *crf
     */
    @JsonProperty("fr")
    private String fr;
    /**
     * 1日常计步
     * 2日常心率
     * 3心率风险
     * 4血氧
     * 5血氧风险
     * 6日常体温
     * 7体温风险
     * 8日常GPS位置
     * 9日常蓝牙信标位置
     * 10信标危险区域
     * 11日常睡眠
     * 12久坐提醒
     * 13脱腕
     */
    @JsonProperty("typ")
    private String typ;
    /**
     *模式：
     * 1演示模式
     * 2下班模式
     * 3工作模式
     * 4普通模式
     * 5守护模式
     */
    @JsonProperty("mo")
    private Integer mo;
    /**
     *状态：
     * 1室内移动
     * 2室外移动
     * 3静止
     * 4脱腕
     */
    @JsonProperty("sts")
    private Integer sts;
    /**
     *采样频率（typ=1/2/3/4/5/6/7/8/9/10）、
     * 时间段（typ=11/12）、
     * 脱腕（typ=13）、
     * 上报频率（typ=14）
     * 采样频率（单位秒）：
     * 数值为正整数n时，n秒1次。
     * 数值为0时，不上报。
     * 数值为-1时，实时腕表提醒。
     * 数值为-2时，实时触发上报。
     * 数值为-3时，实时腕表提醒并实时触发上报
     * 数值为-4时，常开
     * 时间段：
     * 第一段开始时间-第一段结束时间_第二段开始时间-第二段结束时间
     * 12:00-13:00_23:00-6:00
     */
    @JsonProperty("frq")
    private String frq;
    /**
     * 终端唯一编号
     */
	@JSONField(name = "device_id")
    @JsonProperty("device_id")
    private String deviceId;

	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private Date dateTime;
}
