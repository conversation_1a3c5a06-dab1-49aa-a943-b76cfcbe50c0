package com.xh.vdm.mqttCheck.config;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttCheck.entity.BdcTerminal;
import com.xh.vdm.mqttCheck.service.BdcTerminalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class WearableWorkerCache {
    public static ConcurrentHashMap<String, BdcTerminal> BdcTerminalMap = new ConcurrentHashMap<>();
    @Autowired
    BdcTerminalService bdcTerminalService;

    @PostConstruct
    public void initBdcTerminalCacheConstruct(){
        initBdcTerminalCache();
    }

    public void initBdcTerminalCache(){
        List<BdcTerminal> list = bdcTerminalService.getWearableTerminals();
        ConcurrentHashMap<String, BdcTerminal> map = new ConcurrentHashMap<>();
        for(BdcTerminal ter:list){
            map.put(ter.getDeviceNo(),ter);
        }
        BdcTerminalMap = map;
        log.info("initBdcTerminalCache:{},{}",BdcTerminalMap.size(), JSON.toJSONString(BdcTerminalMap));
    }
}
