package com.xh.vdm.mqttCheck.controller;

import com.xh.vdm.mqttCheck.config.MqttConfig;
import com.xh.vdm.mqttCheck.constant.CommonConstant;
import com.xh.vdm.mqttCheck.dto.DeviceAuthResult;
import com.xh.vdm.mqttCheck.dto.HttpParamResponse;
import com.xh.vdm.mqttCheck.mqtt.client.v3.MqttClientV3;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

@Slf4j
@RestController
@RequestMapping("/mqtt")
public class DeviceAuthController {
    @Resource
    MqttConfig mqttConfig;

    @Resource
    MqttClientV3 mqttClientV3;
    @PostMapping("/deviceauth/result")
    public HttpParamResponse DeviceAuthResult(@RequestBody DeviceAuthResult param) {
        log.info("DeviceAuthResult input param is {}", param);
        String payloadStr=String.valueOf(param.getResult());
        String authTopic =mqttConfig.getTopic().getPublish().getDeviceAuthResult().replace(CommonConstant.MqttConstant.PLACEHOLDER,param.getDeviceId());
        boolean res=mqttClientV3.publishMessage(authTopic,payloadStr.getBytes(StandardCharsets.UTF_8),0,false);
        HttpParamResponse resp=new HttpParamResponse(200,"ok",res);
        if (!res){
            resp.setCode(1);
            resp.setMsg("fail");
        }
        return resp;
    }
}
