package com.xh.vdm.mqttCheck.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchPointsReportMsg {
    /**
     * 帧头
     * 固定为帧头 EB 90
     */
	@JSONField(name = "frame_head")
    private Integer frameHead;
    /**
     * 有效数据位
     * 二进制读取，由低到高位通过1和0表达数据是否传输
     * 1、全部数据都要：
     * 0011 1111（0x3F）
     * 2、仅时间戳+经纬高：
     * 0000 0000（0x00）
     * 时间戳+经纬高+速度（第1个spd位为1）：
     * 0000 0001（0x01）
     * 3、时间戳+经纬高+报警（第3个alm报警位为1）：
     * 0000 0100（0x04）
     * 4、时间戳+经纬高+蓝牙信标（第6个bluetooth蓝牙信标编号为1）+报警（第3个alm报警位为1）：
     * 0010 0100（24）
     */
	@JSONField(name = "valid_bit")
    private Byte validBit;
    /**
     * 时间戳，秒精度
     */
    private Integer ct;
    /**
     * 纬度，单位度，至少精确到小数点后6位
     * 1、经度，单位度（小数点后6位）×106
     * 2、不同坐标下，也可以为x轴坐标，单位m
     * 3、typ为4 蓝牙信标时，此项可为空
     */
    private Integer lat;
    /**
     * 经度，单位度，至少精确到小数点后6位
     * 1、纬度，单位度（小数点后6位）×106
     * 2、不同坐标下，也可以为y轴坐标，单位m
     * 3、typ为4 蓝牙信标时，此项可为空
     */
    private Integer lng;
    /**
     * 海拔，单位m，保留1位小数
     * 海拔，单位dm（分米）
     * 不同坐标下，也可以为z轴坐标
     */
    private Short alt;
    /**
     * 定位类型
     * 0 WGS84坐标（无偏，不加密）（默认值）
     * 1 GCJ02坐标（偏移，加密）
     * 2 LBS定位
     * 3 惯导
     * 4 蓝牙信标
     */
	@JSONField(name = "loc_type")
    private Integer locType;
    /**
     * 序号1
     * 单位km/h 范围：0~255
     */
	@JSONField(name = "speed")
    private Integer speed;
    /**
     * 序号2
     * 方向
     * 单位度（小数点后2位）×102
     * 正北为0(0.00)，
     * 正东为9000d(90.0)，
     * 正南为18000d(180.00)，
     * 正西为27000d(270.00)，
     * 范围：0~35999d即 0~8C9F
     */
	@JSONField(name = "bearing")
    private Integer bearing;
    /**
     * 序号3
     * bit0:0-正常，1-SOS报警
     * bit1:0-佩戴，1-未佩戴
     * bit2:0-正常，1-心率异常报警
     * bit3:0-正常，1-血氧异常提醒
     * bit4:0-正常，1-区域报警
     * bit5:0-正常，1-离开区域报警
     * bit6:0-正常，1-进入蓝牙信标报警
     * bit7:0-正常，1-离开蓝牙信标报警
     * bit8:0-正常，1-跌落（撞击）报警
     * bit9:0-正常，1-静默报警
     * bit10:0-正常，1-近电报警
     * bit11:0-正常，1-登高报警
     * bit12:0-正常，1-温度报警
     * bit13:0-正常，1-体温报警
     * bit14:0-正常，1-脱帽报警
     * bit[15:31]:保留
     */
	@JSONField(name = "alarm")
    private Integer alarm;
    /**
     * 序号4
     * 心率数据
     * 0~255
     */
	@JSONField(name = "hr")
    private Integer hr;
    /**
     * 序号5
     * 脉搏血氧饱和度
     * 0~100
     */
	@JSONField(name = "spo2")
    private Integer spo2;
    /**
     * 序号6
     * 蓝牙信标编号
     * 蓝牙信标数据由6字节MAC地址。例子：EC26CA8464B0AC63对应EC:26:CA:84:64:B0
     */
	@JSONField(name = "bluetooth")
    private String bluetooth;
    /**
     * 序号7
     * 事件定义类型：
     * 1 休眠
     * 2 开机
     * 3 手动关机
     */
	@JSONField(name = "event")
    private Integer event;
    /**
     * 序号8
     * 预留
     */
	@JSONField(name = "obligate")
    private Integer obligate;

	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private Date dateTime;
}
