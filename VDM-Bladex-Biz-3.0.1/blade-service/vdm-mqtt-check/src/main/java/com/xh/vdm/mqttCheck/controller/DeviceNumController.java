package com.xh.vdm.mqttCheck.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xh.vdm.mqttCheck.config.MqttConfig;
import com.xh.vdm.mqttCheck.config.WearableWorkerCache;
import com.xh.vdm.mqttCheck.constant.CommEnum;
import com.xh.vdm.mqttCheck.constant.CommonConstant;
import com.xh.vdm.mqttCheck.dto.*;
import com.xh.vdm.mqttCheck.kafka.KafkaProducerService;
import com.xh.vdm.mqttCheck.mqtt.client.v3.MqttClientV3;
import com.xh.vdm.mqttCheck.mqtt.handler.watch.ReportDataMessageHandler;
import com.xh.vdm.mqttCheck.service.BdcTerminalService;
import com.xh.vdm.mqttCheck.service.RedisService;
import com.xh.vdm.mqttCheck.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;

import static com.xh.vdm.mqttCheck.mqtt.handler.watch.ReportDataMessageHandler.analysisSateInfo;
import static com.xh.vdm.mqttCheck.mqtt.handler.watch.ReportDataMessageHandler.analysisSatesInfo;

@Slf4j
@RestController
@RequestMapping("/mqtt")
public class DeviceNumController {
    @Resource
    MqttConfig mqttConfig;

    @Resource
    MqttClientV3 mqttClientV3;

    @Autowired
    RedisService redisService;
    @Autowired
    KafkaProducerService kafkaProducerService;
    @Autowired
    BdcTerminalService bdcTerminalService;
    @Autowired
    StringRedisTemplate redisTemplate;
    @Autowired
    ReportDataMessageHandler reportDataMessageHandler;
    @Autowired
    WearableWorkerCache wearableWorkerCache;
    static HttpParamResponse getHttpParamResponse(String payloadStr, String setTopic, MqttClientV3 mqttClientV3) {
        boolean res= mqttClientV3.publishMessage(setTopic,payloadStr.getBytes(StandardCharsets.UTF_8),0,false);
        HttpParamResponse resp=new HttpParamResponse(200,"ok",res);
        if (!res){
            resp.setCode(1);
            resp.setMsg("fail");
        }
        return resp;
    }
    @PostConstruct
    public void initTest(){
        kafkaProducerService.sendMessage("TEST kafkaProducerService sendMessage  123456789");
        redisService.test();
        //log.info("TEST bdcTerminalService queryBySim:{}",bdcTerminalService.queryByDeviceNo("04B01022D0137061").toString());
        String str="00275000EF0D0100104B00D9090000064400D80E0000093700D5000000173200851101001B2200E3100100275000EF1E0100173200852001001B2200E31E01";
        log.info("TEST Integer.parseInt:{}",Integer.parseInt("80",16));
        for (String s : StringUtil.splitString(str,14)) {
            log.info("TEST analysisSateInfo:{},{}", s,analysisSateInfo(s));
        }
        log.info("TEST analysisSatesInfo:{}", analysisSatesInfo(str));
    }
    @PostMapping("/devicenum/set")
    public HttpParamResponse DeviceNumSet(@RequestBody DeviceNumSet param) {
        log.info("DeviceNumSet input param is {}", param);
        String payloadStr=param.getDeviceNum()+","+param.getDeviceSign();
        String setTopic =mqttConfig.getTopic().getPublish().getDeviceNumSet().replace(CommonConstant.MqttConstant.PLACEHOLDER,param.getDeviceNo());
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,param.getDeviceNo(), CommEnum.DeviceNumSet.getKey());
        redisTemplate.opsForValue().set(redisKey,payloadStr);
        return getHttpParamResponse(payloadStr, setTopic, mqttClientV3);
    }

    @PostMapping("/devicenum/queryparam")
    public HttpParamResponse DeviceNumQuery(@RequestBody DeviceNumQuery param) {
        log.info("DeviceNumQuery input param is {}", param);
        String payloadStr= param.getCmdFlag();
        String queryTopic =mqttConfig.getTopic().getPublish().getDeviceNumQuery().replace(CommonConstant.MqttConstant.PLACEHOLDER,param.getDeviceNo());
        boolean res=mqttClientV3.publishMessage(queryTopic,payloadStr.getBytes(StandardCharsets.UTF_8),2,false);
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,param.getDeviceNo(), CommEnum.DeviceNumQuery.getKey());
        NetCheck netCheck=new NetCheck(payloadStr,"通过",new Date());
        redisTemplate.opsForValue().set(redisKey, JSON.toJSONString(netCheck));
        if (Boolean.FALSE.equals(redisTemplate.hasKey(CommonConstant.RedisConstant.MQTT_WATCH+param.getDeviceNo()))) {
            try {
                // 暂停当前线程执行5秒 (5000毫秒)
                Thread.sleep(5000);
            } catch (InterruptedException e) {
                // 处理被打断的异常
                System.out.println("Thread was interrupted");
            }
        }
        String deviceNumRedis = redisTemplate.opsForValue().get(CommonConstant.RedisConstant.MQTT_WATCH+param.getDeviceNo());
        DeviceNumReport obj = JSONObject.parseObject(deviceNumRedis, DeviceNumReport.class);
        HttpParamResponse resp=new HttpParamResponse(200,"ok",obj);
        if (!res){
            resp.setCode(1);
            resp.setMsg("fail");
        }
        return resp;
    }
}
