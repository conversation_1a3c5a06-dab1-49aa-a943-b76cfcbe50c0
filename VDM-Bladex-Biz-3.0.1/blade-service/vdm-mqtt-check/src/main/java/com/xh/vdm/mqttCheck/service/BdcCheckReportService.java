package com.xh.vdm.mqttCheck.service;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.mqttCheck.entity.BdcCheckReport;
import com.xh.vdm.mqttCheck.entity.BdcTerminal;
import com.xh.vdm.mqttCheck.mapper.BdcCheckReportMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class BdcCheckReportService extends ServiceImpl<BdcCheckReportMapper, BdcCheckReport> {
    public boolean updateTestResultById(Integer result,Long reportId){
        UpdateWrapper<BdcCheckReport> updateWrapper = new UpdateWrapper<>();
        updateWrapper.set("test_result", result);
        updateWrapper.set("test_time", new Date());
        updateWrapper.set("check_process", 1);
        updateWrapper.eq("id", reportId);
        return update(updateWrapper);
    }
}
