package com.xh.vdm.mqttCheck.mqtt.handler.watch;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttCheck.config.MqttConfig;
import com.xh.vdm.mqttCheck.config.WearableWorkerCache;
import com.xh.vdm.mqttCheck.constant.CommEnum;
import com.xh.vdm.mqttCheck.constant.CommonConstant;
import com.xh.vdm.mqttCheck.constant.ModeTypeEnum;
import com.xh.vdm.mqttCheck.dto.DeviceAuthUpload;
import com.xh.vdm.mqttCheck.dto.NetCheck;
import com.xh.vdm.mqttCheck.entity.BdcTerminal;
import com.xh.vdm.mqttCheck.entity.BdmCodingMachine;
import com.xh.vdm.mqttCheck.mqtt.client.IMqttClient;
import com.xh.vdm.mqttCheck.mqtt.client.v3.MqttClientV3;
import com.xh.vdm.mqttCheck.mqtt.handler.IMqttMessageHandlerV5;
import com.xh.vdm.mqttCheck.mqtt.handler.MessageHandlerAbst;
import com.xh.vdm.mqttCheck.service.BdcCodingMachineService;
import com.xh.vdm.mqttCheck.service.BdcTerminalService;
import com.xh.vdm.mqttCheck.util.SM2Util;
import com.xh.vdm.mqttCheck.util.TopicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.xh.vdm.mqttCheck.config.WearableWorkerCache.BdcTerminalMap;

@Slf4j
@Component
public class DeviceAuthMsgHandler extends MessageHandlerAbst implements IMqttMessageHandlerV5{
    /**
     * 设备号位置
     */
    private final int  deviceIndex;
    private String deviceId;
    @Resource
    MqttConfig mqttConfig;
    @Resource
    MqttClientV3 mqttClientV3;
    @Autowired
    BdcCodingMachineService bdcCodingMachineService;
    @Autowired
    BdcTerminalService bdcTerminalService;
    @Autowired
    StringRedisTemplate redisTemplate;
    @Autowired
    WearableWorkerCache wearableWorkerCache;

    public DeviceAuthMsgHandler(List<IMqttClient> messageClientList, MqttConfig mqttConfig) {
        super();
        this.setSubTopic(mqttConfig.getTopic().getSubscription().getDeviceAuth());
        this.deviceIndex = Arrays.asList(subTopic.split(CommonConstant.MqttConstant.SLASH)).indexOf(CommonConstant.MqttConstant.PLUS_SIGN);
        messageClientList.forEach(client -> {
            addClient(client.version(), client);
            client.addMessageHandler(this);
        });
    }

    @Override
    public void process(String topic, String msg) {
        deviceId = topic.split(CommonConstant.MqttConstant.SLASH)[deviceIndex];
        List<DeviceAuthUpload> dtoList = analysisMessageToList(msg);
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceId, CommEnum.DeviceAuth.getKey());
        NetCheck netCheck=new NetCheck(msg,"通过",new Date());
        redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        log.info("DeviceAuthMsgHandler process received deviceId：{}, msg: {}", deviceId, JSON.toJSONString(dtoList));
    }

    private List<DeviceAuthUpload> analysisMessageToList(String msg) {
        List<DeviceAuthUpload> list = new ArrayList<>();
        String[] params = msg.split(",");
        if (params.length<2){
            return list;
        }
        DeviceAuthUpload dto=new DeviceAuthUpload();
        dto.setDeviceNum(params[0]);
        dto.setDeviceSign(params[1]);
        int result = 0;
        String authTopic =mqttConfig.getTopic().getPublish().getDeviceAuthResult().replace(CommonConstant.MqttConstant.PLACEHOLDER,deviceId);
        mqttClientV3.publishMessage(authTopic,String.valueOf(result).getBytes(StandardCharsets.UTF_8),0,false);
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceId, CommEnum.DeviceAuthResult.getKey());
        NetCheck netCheck=new NetCheck(msg,"通过",new Date());
        redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        list.add(dto);
        return list;
    }

    public int checkAuth(DeviceAuthUpload deviceAuthUpload) {
        BdcTerminal bdcTerminal=BdcTerminalMap.get(deviceId);
        if (bdcTerminal==null){
            return 9;
        }
        if (bdcTerminal.getCodeResult()!=1){
            //赋码检测阶段
            return 0;
        }
        String machineNumber=deviceAuthUpload.getDeviceNum().substring(2, 6);
        BdmCodingMachine bdmCodingMachine=bdcCodingMachineService.queryByNumber(machineNumber);
        if (bdmCodingMachine==null){
            return 10;
        }
        try{
            String  deviceNumSign = SM2Util.decrypt(deviceAuthUpload.getDeviceSign(), "00" + bdmCodingMachine.getPrivateKey(), ModeTypeEnum.BASE_MODE);
            log.info("赋码签名解密后:{}",deviceNumSign);
            String[] deviceNumSignArr=deviceNumSign.split(":");
            if (deviceNumSignArr.length!=7){
                return 8;
            }
            if (!deviceNumSignArr[0].equals(bdcTerminal.getDeviceCate())){
                return 1;
            }
            if (!deviceNumSignArr[1].equals(bdcTerminal.getManufacturer())){
                return 2;
            }
            if (!deviceNumSignArr[2].equals(bdcTerminal.getDeviceModel())){
                return 3;
            }
            if (!deviceNumSignArr[3].equals(bdcTerminal.getDeviceSeq())){
                return 4;
            }
            if (!deviceNumSignArr[4].equals(bdcTerminal.getImei())){
                return 5;
            }
            if (!deviceNumSignArr[5].equals(bdcTerminal.getChipSeq())){
                return 6;
            }
            if (!deviceNumSignArr[6].equals(bdcTerminal.getDeviceNum())){
                return 7;
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        log.info("checkAuth 鉴权成功，刷新入网设备缓存map:{},{},{}",bdcTerminal.getDeviceNo(),bdcTerminal.getSim(), bdcTerminal.getCodeResult());
        return 0;
    }
    @Override
    public boolean match(String topic) {
        return TopicUtils.math(subTopic, topic, deviceIndex);
    }
}
