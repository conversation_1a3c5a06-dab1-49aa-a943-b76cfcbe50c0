package com.xh.vdm.mqttCheck.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("bd_check.bdm_coding_machine")
public class BdmCodingMachine {

    @TableId(value = "number")
    private String number;

    @TableField("password")
    private String password;

    @TableField("private_key")
    private String privateKey;

    @TableField("public_key")
    private String publicKey;

    @TableField("vendor")
    private String vendor;

    @TableField("create_time")
    private Date createTime;

    @TableField("disabled")
    private Short disabled;
}

