package com.xh.vdm.mqttCheck.util;

import com.xh.vdm.mqttCheck.constant.CommonConstant;
import org.springframework.util.StringUtils;

public class TopicUtils {

    /**
     * @param topic       监听主题
     * @param targetTopic 匹配主题
     * @param index       通配符位置
     * @return 是否订阅主题
     */
    public static boolean math(String topic, String targetTopic, int index) {
        if (StringUtils.isEmpty(topic) || StringUtils.isEmpty(targetTopic)) {
            return false;
        }
        String[] matchTopicArr = targetTopic.split(CommonConstant.MqttConstant.SLASH);
        String[] topicArr = topic.split(CommonConstant.MqttConstant.SLASH);
        if (matchTopicArr.length != topicArr.length) {
            return false;
        }
        for (int i = 0; i < matchTopicArr.length; i++) {
            if (i == index) {
                continue;
            }
            if (Boolean.FALSE.equals(matchTopicArr[i].equals(topicArr[i]))) {
                return false;
            }
        }
        return true;
    }
}
