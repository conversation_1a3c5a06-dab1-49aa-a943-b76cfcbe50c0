package com.xh.vdm.mqttCheck.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.mqttCheck.entity.BdmCodingMachine;
import com.xh.vdm.mqttCheck.mapper.BdmCodingMachineMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class BdcCodingMachineService extends ServiceImpl<BdmCodingMachineMapper, BdmCodingMachine> {
    public BdmCodingMachine queryByNumber(String number){
        return this.query().eq("number",number).one();
    }
}
