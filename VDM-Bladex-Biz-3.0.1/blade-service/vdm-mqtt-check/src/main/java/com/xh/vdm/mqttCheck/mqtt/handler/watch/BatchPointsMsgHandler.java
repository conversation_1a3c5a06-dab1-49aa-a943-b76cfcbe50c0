package com.xh.vdm.mqttCheck.mqtt.handler.watch;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttCheck.config.MqttConfig;
import com.xh.vdm.mqttCheck.constant.CommEnum;
import com.xh.vdm.mqttCheck.constant.CommonConstant;
import com.xh.vdm.mqttCheck.dto.BatchPointsReportMsg;
import com.xh.vdm.mqttCheck.dto.NetCheck;
import com.xh.vdm.mqttCheck.kafka.KafkaProducerService;
import com.xh.vdm.mqttCheck.mqtt.client.IMqttClient;
import com.xh.vdm.mqttCheck.mqtt.handler.IMqttMessageHandlerV5;
import com.xh.vdm.mqttCheck.mqtt.handler.MessageHandlerAbst;
import com.xh.vdm.mqttCheck.util.TopicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class BatchPointsMsgHandler extends MessageHandlerAbst implements IMqttMessageHandlerV5 {
    /**
     * 设备号位置
     */
    private final int deviceIndex;
    private static String deviceId;
    @Autowired
    KafkaProducerService kafkaProducerService;
    @Autowired
    StringRedisTemplate redisTemplate;

    public BatchPointsMsgHandler(List<IMqttClient> messageClientList, MqttConfig mqttConfig) {
        super();
        super.setSubTopic(mqttConfig.getTopic().getSubscription().getPositionBatch());
        this.deviceIndex = Arrays.asList(subTopic.split(CommonConstant.MqttConstant.SLASH)).indexOf(CommonConstant.MqttConstant.PLUS_SIGN);
        messageClientList.forEach(client -> {
            addClient(client.version(), client);
            client.addMessageHandler(this);
        });
    }

    @Override
    public void process(String topic, String msg) {
        deviceId = topic.split(CommonConstant.MqttConstant.SLASH)[deviceIndex];
        List<BatchPointsReportMsg> dtoList = new ArrayList<>();
        if (!msg.startsWith(CommonConstant.MqttConstant.FRAME_HEADER_EB90)){
            log.error("BatchPointsMsgHandler not startsWith EB90:{}",msg);
            return;
        }
        String[] posArr=msg.split(CommonConstant.MqttConstant.FRAME_HEADER_EB90);
        for (String pos:posArr) {
            String posWithFrame=CommonConstant.MqttConstant.FRAME_HEADER_EB90+pos;
            BatchPointsReportMsg posMsg=analysisSplitMessageToPos(posWithFrame);
            log.info("BatchPointsMsgHandler Split EB90:{},{}",posWithFrame,posMsg);
            dtoList.add(posMsg);
        }
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceId, CommEnum.PositionBatch.getKey());
        NetCheck netCheck=new NetCheck(msg,"通过",new Date());
        redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        log.info("BatchPointsMsgHandler received deviceId：{}, msg: {}", deviceId, JSON.toJSONString(dtoList));
    }

    private BatchPointsReportMsg analysisSplitMessageToPos(String msg) {
        BatchPointsReportMsg dto=new BatchPointsReportMsg();
        if (msg.length()<36){
            return dto;
        }
        dto.setFrameHead(Integer.parseInt(msg.substring(0,4),16));
        dto.setValidBit(Byte.parseByte(msg.substring(4,6),16));
        dto.setCt(Integer.parseInt(msg.substring(6,14),16));
        dto.setLat(Integer.parseInt(msg.substring(14,22),16));
        dto.setLng(Integer.parseInt(msg.substring(22,30),16));
        dto.setAlt(Short.parseShort(msg.substring(30,34),16));
        dto.setLocType(Integer.parseInt(msg.substring(34,36),16));
        switch (dto.getValidBit()){
            case 0x7f:
                //全部数据都要
                if (msg.length()<70){
                    return dto;
                }
                dto.setSpeed(Integer.parseInt(msg.substring(36,38),16));
                dto.setBearing(Integer.parseInt(msg.substring(38,42),16));
                dto.setAlarm(Integer.parseInt(msg.substring(42,50),16));
                dto.setHr(Integer.parseInt(msg.substring(50,52),16));
                dto.setSpo2(Integer.parseInt(msg.substring(52,54),16));
                dto.setBluetooth(msg.substring(54,66));
                dto.setEvent(Integer.parseInt(msg.substring(66,68),16));
                dto.setObligate(Integer.parseInt(msg.substring(68,70),16));
                break;
            case 0x00:
                //仅时间戳+经纬高
                break;
            case 0x01:
                //时间戳+经纬高+速度（第1个spd位为1）
                dto.setSpeed(Integer.parseInt(msg.substring(36,38),16));
                break;
            case 0x04:
                //时间戳+经纬高+报警（第3个alm报警位为1）
                dto.setAlarm(Integer.parseInt(msg.substring(36,38),16));
                break;
            case 0x24:
                //时间戳+经纬高+蓝牙信标（第6个bluetooth蓝牙信标编号为1）+报警（第3个alm报警位为1）
                dto.setBluetooth(msg.substring(36,48));
                dto.setAlarm(Integer.parseInt(msg.substring(48,50),16));
                break;
            default:
                return dto;
        }
        return dto;
    }
    @Override
    public boolean match(String topic) {
        return TopicUtils.math(subTopic, topic, deviceIndex);
    }
}
