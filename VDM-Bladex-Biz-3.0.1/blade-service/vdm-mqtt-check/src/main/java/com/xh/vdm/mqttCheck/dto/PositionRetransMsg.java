package com.xh.vdm.mqttCheck.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PositionRetransMsg {
    /**
     *帧头
     *固定为帧头EB90
     */
    private short frame;
    /**
     *有效数据位
     *二进制读取，由低到高位通过1和0表达数据是否传输
     * 1、全部数据都要：
     * 0011 1111（0x3F）
     * 2、仅时间戳+经纬高：
     * 0000 0000（0x00）
     * 时间戳+经纬高+速度（第1个spd位为1）：
     * 0000 0001（0x01）
     * 3、时间戳+经纬高+报警（第3个alm报警位为1）：
     * 0000 0100（0x04）
     * 4、时间戳+经纬高+蓝牙信标（第6个bluetooth蓝牙信标编号为1）+报警（第3个alm报警位为1）：
     * 0010 0100（24）
     */
    private byte validBits;
    /**
     * 时间戳，秒精度
     */
    private Long ct;
    /**
     * 经度，单位度，至少精确到小数点后6位
     * 不同坐标下，也可以为x轴坐标，单位m
     * typ为4 蓝牙信标时，此项可为空
     */
    private Double lng;
    /**
     * 纬度，单位度，至少精确到小数点后6位
     * 不同坐标下，也可以为y轴坐标，单位m
     * typ为4 蓝牙信标时，此项可为空
     */
    private Double lat;
    /**
     * 海拔，单位m，保留1位小数
     * 不同坐标下，也可以为z轴坐标，单位m
     */
    private Double alt;
    /**
     * 定位类型
     * 0 WGS84坐标（无偏，不加密）（默认值）
     * 1 GCJ02坐标（偏移，加密）
     * 2 LBS定位
     * 3 惯导
     * 4 蓝牙信标
     * 5 wifi定位
     */
    private Integer typ;
    /**
     * 速度，单位km/h
     */
    private Double spd;
    /**
     * 方向
     */
    private Double dir;
    /**
     * bit0:0-正常，1-SOS报警
     * bit1:0-佩戴，1-未佩戴
     * bit2:0-正常，1-心率异常报警
     * bit3:0-正常，1-血氧异常提醒
     * bit4:0-正常，1-区域报警
     * bit5:0-正常，1-离开区域报警
     * bit6:0-正常，1-进入蓝牙信标报警
     * bit7:0-正常，1-离开蓝牙信标报警
     * bit8:0-正常，1-跌落（撞击）报警
     * bit9:0-正常，1-静默报警
     * bit10:0-正常，1-近电报警
     * bit11:0-正常，1-登高报警
     * bit12:0-正常，1-温度报警
     * bit13:0-正常，1-体温报警
     * bit14:0-正常，1-脱帽报警
     * bit[15:31]:保留
     */
    private Integer alm;
    /**
     *心率数据heartRate
     */
    private Integer hr;
    /**
     * 搏血氧饱和度pulse oxygen saturation (SPO2)
     */
    private Integer spo2;
    /**
     *蓝牙信标数据由6字节MAC地址+1字节信号强度+1字节电量组成，多组数据间用分号“;”分割
     */
    private String bluetooth;
    /**
     * 事件定义类型：
     * 1 休眠
     * 2 开机
     * 3 手动关机
     */
    private Integer event;
    /**
     * 卫星信息
     */
    private String sateHexDes;

	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private Date dateTime;
}
