package com.xh.vdm.mqttCheck.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@TableName("bd_check.bdc_terminal")
public class BdcTerminal {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("device_cate")
    private String deviceCate;

    @TableField("device_type")
    private String deviceType;

    @TableField("device_no")
    private String deviceNo;

    @TableField("device_seq")
    private String deviceSeq;

    @TableField("imei")
    private String imei;

    @TableField("chip_seq")
    private String chipSeq;

    @TableField("sim")
    private String sim;

    @TableField("device_num")
    private String deviceNum;

    @TableField("device_num_sign")
    private String deviceNumSign;

    @TableField("device_model")
    private String deviceModel;

    @TableField("manufacturer")
    private String manufacturer;

    @TableField("protocol")
    private String protocol;

    @TableField("test_result")
    private Short testResult;

    @TableField("code_result")
    private Short codeResult;

    @TableField("company_id")
    private Long companyId;

    @TableField("report_id")
    private Long reportId;

    @TableField("check_result")
    private String checkResult;

    @TableField("check_res_message")
    private String checkResMessage;

    @TableField("check_total_data_count")
    private Long checkTotalDataCount;

    @TableField("check_bd_data_count")
    private Long checkBdDataCount;

    @TableField("check_non_bd_count")
    private Long checkNonBdCount;

    @TableField("check_start_date")
    private Date checkStartDate;

    @TableField("check_end_date")
    private Date checkEndDate;

    @TableField("test_time")
    private Date testTime;

    @TableField("code_time")
    private Date codeTime;

    @TableField("is_del")
    private Integer isDel;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    @TableField("is_in_check")
    private Short isInCheck;

    @TableField("test_res_message")
    private String testResMessage;
}

