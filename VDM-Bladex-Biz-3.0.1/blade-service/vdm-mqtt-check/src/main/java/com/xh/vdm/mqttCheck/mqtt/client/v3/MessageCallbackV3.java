package com.xh.vdm.mqttCheck.mqtt.client.v3;

import com.xh.vdm.mqttCheck.mqtt.handler.IMqttMessageHandler;
import com.xh.vdm.mqttCheck.mqtt.handler.IMqttMessageHandlerV3;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * v3 消息回调
 * <AUTHOR>
 * @date 2024/4/12
 */
@Slf4j
public class MessageCallbackV3 implements MqttCallback {

    private final Map<Class<? extends IMqttMessageHandler>, IMqttMessageHandler> messageHandlers = new HashMap<>();

    private final MqttClientV3 mqttClient;

    public MessageCallbackV3(MqttClientV3 mqttClient) {
        this.mqttClient = mqttClient;
    }

    @Override
    public void connectionLost(Throwable throwable) {
        log.error("connectionLost error", throwable);
    }

    /**
     * 接收到消息
     * @param topic
     * @param message
     * @return void
     */
    @Override
    public void messageArrived(String topic, MqttMessage message) throws Exception {
        String msg = new String(message.getPayload(), StandardCharsets.UTF_8);
        log.debug("messageArrived topic = {} message = {}", topic, msg);
    }

    /**
     * 消息发送完成后通知
     * 不关注消息发送的结果 这里留空
     * @param token
     * @return void
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        if (log.isDebugEnabled()) {
//            try {
//                log.debug("deliveryComplete topic ->[{}]  msg [{}]  complete->[{}];", Arrays.toString(token.getTopics()), new String(token.getMessage().getPayload()),token.isComplete());
//            } catch (MqttException e) {
//                log.error("deliveryComplete error", e);
//            }
        }
    }


    /**
     * 添加消息处理器
     */
    public void addMessageHandler(IMqttMessageHandler messageHandler) {
        if (messageHandler == null) {
            return;
        }
        if (messageHandler instanceof IMqttMessageHandlerV3) {
            messageHandlers.put(messageHandler.getClass(), messageHandler);
        }
    }
}
