package com.xh.vdm.mqttCheck.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RemoteParamSet {
    /**
     *固定为 crc
     */
    @JsonProperty("frame_type")
	@JSONField(name = "frame_type")
    private String frameType;  // 帧类型
    /**
     *1数据下发、2天气下发、3SOS告警、4危险区域告警、5体征异常提醒、6 单北斗、7 录制
     */
    @JsonProperty("category")
    private int category;       // 类别
    /**
     *1开启、0关闭
     */
    @JsonProperty("switch_status")
	@JSONField(name = "switch_status")
    private int switchStatus;   // 开启/关闭状态
    /**
     *时间戳
     */
    @JsonProperty("timestamp")
    private Integer timestamp;  // 时间戳，可为空
    /**
     * 终端唯一编号
     */
    @JsonProperty("device_id")
	@JSONField(name = "device_id")
    private String deviceId;

	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
    private Date dateTime;
}
