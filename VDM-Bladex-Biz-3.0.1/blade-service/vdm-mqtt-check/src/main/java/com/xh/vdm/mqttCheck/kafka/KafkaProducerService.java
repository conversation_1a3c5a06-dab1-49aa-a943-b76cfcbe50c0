package com.xh.vdm.mqttCheck.kafka;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class KafkaProducerService {
    private static final String TOPIC = "my_topic";
    //正式入网之前的定位点topic
    private static final String LOC_CHECK_TOPIC = "TerminalCheckTopic";

    @Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(String message) {
        kafkaTemplate.send(TOPIC, message);
    }

    public void sendCheckPositionReportMessage(String message,String key){
        log.info("sendCheckPositionReportMessage:{}",message);
        kafkaTemplate.send(LOC_CHECK_TOPIC,key, message);
    }
}
