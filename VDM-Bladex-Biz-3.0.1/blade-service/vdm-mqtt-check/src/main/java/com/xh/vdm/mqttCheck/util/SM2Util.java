package com.xh.vdm.mqttCheck.util;

import com.xh.vdm.mqttCheck.constant.ModeTypeEnum;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.AsymmetricCipherKeyPair;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.generators.ECKeyPairGenerator;
import org.bouncycastle.crypto.params.*;
import org.bouncycastle.math.ec.ECPoint;
import org.bouncycastle.pqc.math.linearalgebra.ByteUtils;
import org.bouncycastle.util.encoders.Hex;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;

/**
 * SM2椭圆曲线公钥密码算法（非对称算法）是我国自主设计的密码算法。
 * 包括SM2-1椭圆曲线数字签名算法、SM2-2椭圆曲线密钥交换协议、SM2-3椭圆曲线公钥加密算法，分别用于实现数字签名、密钥协商、数据加密等功能。
 * SM2算法与RSA算法不同的是，SM2算法是基于椭圆曲线上点群离散对数难题，相对于RSA算法，256位的SM2密码强度比2048位的RSA密码强度要高。
 */
public class SM2Util {

	private static final String GM_NAME_CURVE = "sm2p256v1";

	private static final String ALGORITHM = "SHA1PRNG";

	// 获取椭圆曲线
	public static synchronized ECDomainParameters getECDomainParameters () {
		X9ECParameters sm2ECParameters = GMNamedCurves.getByName(SM2Util.GM_NAME_CURVE);
		return new ECDomainParameters(sm2ECParameters.getCurve(), sm2ECParameters.getG(), sm2ECParameters.getN());
	}

	// 生成密钥对
	public static Map<String, String> createKeyPair () throws NoSuchAlgorithmException {
		ECKeyPairGenerator keyPairGenerator = new ECKeyPairGenerator();
		keyPairGenerator.init(new ECKeyGenerationParameters(SM2Util.getECDomainParameters(), SecureRandom.getInstance(SM2Util.ALGORITHM)));
		AsymmetricCipherKeyPair keyPair = keyPairGenerator.generateKeyPair();
		BigInteger bigInteger = ((ECPrivateKeyParameters) keyPair.getPrivate()).getD();
		// 公钥开头的02或03表示压缩公钥，04表示未压缩公钥，04时，可去掉开头的04。
		ECPoint point = ((ECPublicKeyParameters) keyPair.getPublic()).getQ();
		Map<String, String> map = new HashMap<>();
		map.put("private_key", ByteUtils.toHexString(bigInteger.toByteArray()));
		map.put("public_key", ByteUtils.toHexString(point.getEncoded(false)));
		return map;
	}

	/**
	 * 加密
	 * @param plainText 明文字符串
	 * @param publicKey 公钥
	 * @param modeType 加密模式（base：标准模式[C1C3C2]，bc：BC模式[C1C2C3]）
	 */
	public static String encrypt (String plainText, String publicKey, ModeTypeEnum modeType) throws InvalidCipherTextException {
		ECDomainParameters domainParameters = SM2Util.getECDomainParameters();
		// 提取公钥点
		ECPoint point = domainParameters.getCurve().decodePoint(ByteUtils.fromHexString(publicKey));
		// 公钥开头的02或03表示压缩公钥，04表示未压缩公钥，04时，可去掉开头的04。
		ECPublicKeyParameters publicKeyParameters = new ECPublicKeyParameters(point, domainParameters);
		SM2Engine sm2Engine = new SM2Engine(modeType.getMode());
		sm2Engine.init(true, new ParametersWithRandom(publicKeyParameters, new SecureRandom()));
		byte[] plainByte = plainText.getBytes(StandardCharsets.UTF_8);
		return ByteUtils.toHexString(sm2Engine.processBlock(plainByte, 0, plainByte.length));
	}

	/**
	 * 解密
	 * @param cipherText 密文字符串
	 * @param privateKey 私钥
	 * @param modeType 加密模式（base：标准模式[C1C3C2]，bc：BC模式[C1C2C3]）
	 */
	public static String decrypt (String cipherText, String privateKey, ModeTypeEnum modeType) throws InvalidCipherTextException {
		BigInteger bigInteger = new BigInteger(privateKey, 16);
		ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(bigInteger, SM2Util.getECDomainParameters());
		SM2Engine sm2Engine = new SM2Engine(modeType.getMode());
		sm2Engine.init(false, privateKeyParameters);
		byte[] cipherByte = Hex.decode(cipherText);
		return new String(sm2Engine.processBlock(cipherByte, 0, cipherByte.length), StandardCharsets.UTF_8);
	}
}
