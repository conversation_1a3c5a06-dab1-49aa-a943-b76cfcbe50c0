package com.xh.vdm.mqttCheck.mqtt.handler.watch;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttCheck.config.MqttConfig;
import com.xh.vdm.mqttCheck.constant.CommEnum;
import com.xh.vdm.mqttCheck.constant.CommonConstant;
import com.xh.vdm.mqttCheck.dto.DeviceNumReport;
import com.xh.vdm.mqttCheck.dto.NetCheck;
import com.xh.vdm.mqttCheck.mqtt.client.IMqttClient;
import com.xh.vdm.mqttCheck.mqtt.handler.IMqttMessageHandlerV5;
import com.xh.vdm.mqttCheck.mqtt.handler.MessageHandlerAbst;
import com.xh.vdm.mqttCheck.util.TopicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class DeviceNumReportMsgHandler extends MessageHandlerAbst implements IMqttMessageHandlerV5 {
    /**
     * 设备号位置
     */
    private final int deviceIndex;
    private String deviceId;
    @Autowired
    StringRedisTemplate redisTemplate;
    @Resource
    MqttConfig mqttConfig;
    public DeviceNumReportMsgHandler(List<IMqttClient> messageClientList, MqttConfig mqttConfig) {
        super();
        this.setSubTopic(mqttConfig.getTopic().getSubscription().getDeviceNumReport());
        this.deviceIndex = Arrays.asList(subTopic.split(CommonConstant.MqttConstant.SLASH)).indexOf(CommonConstant.MqttConstant.PLUS_SIGN);
        messageClientList.forEach(client -> {
            addClient(client.version(), client);
            client.addMessageHandler(this);
        });
    }

    @Override
    public void process(String topic, String msg) {
        deviceId = topic.split(CommonConstant.MqttConstant.SLASH)[deviceIndex];
        List<DeviceNumReport> dtoList = analysisMessageToList(msg);
        String redisKey = String.format("%s%s:%s",CommonConstant.RedisConstant.NET_CHECK,deviceId, CommEnum.DeviceNumReport.getKey());
        NetCheck netCheck=new NetCheck(msg,"通过",new Date());
        redisTemplate.opsForValue().set(redisKey,JSON.toJSONString(netCheck));
        log.info("DeviceNumReportMsgHandler process received deviceId：{}, msg: {}", deviceId, JSON.toJSONString(dtoList));
    }

    private List<DeviceNumReport> analysisMessageToList(String msg) {
        List<DeviceNumReport> list = new ArrayList<>();
        String[] params = msg.split(",");
        DeviceNumReport dto=new DeviceNumReport();
        dto.setDeviceCategory(params[0]);
        dto.setManufacturerId(params[1]);
        dto.setProductModel(params[2]);
        dto.setProductSerialNumber(params[3]);
        dto.setImei(params[4]);
        dto.setBdChipSerialNumber(params[5]);
        saveDeviceNum(dto);
        list.add(dto);
        return list;
    }

    private void saveDeviceNum(DeviceNumReport deviceNumReport){
        redisTemplate.opsForValue().set(CommonConstant.RedisConstant.MQTT_WATCH+deviceId,JSON.toJSONString(deviceNumReport));
    }

    @Override
    public boolean match(String topic) {
        return TopicUtils.math(subTopic, topic, deviceIndex);
    }
}
