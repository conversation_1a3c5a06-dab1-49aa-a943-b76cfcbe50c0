package com.xh.vdm.mqttCheck.util;

import com.xh.vdm.mqttCheck.dto.Satellite;

import java.util.ArrayList;
import java.util.List;

public class StringUtil {
    public static List<String> splitString(String input, int length) {
        int index = 0;
        List<String> list= new ArrayList<>();
        while (index < input.length()) {
            list.add(input.substring(index, Math.min(index + length, input.length())));
            index += length;
        }
        return list;
    }
}
