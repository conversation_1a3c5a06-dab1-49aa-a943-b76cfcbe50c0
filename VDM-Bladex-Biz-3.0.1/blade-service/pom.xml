<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springblade</groupId>
        <artifactId>BladeX-Biz</artifactId>
        <version>*******.RELEASE</version>
    </parent>

    <artifactId>blade-service</artifactId>
    <name>${project.artifactId}</name>
    <version>*******.RELEASE</version>
    <packaging>pom</packaging>
    <description>BladeX 微服务集合</description>

    <modules>
        <module>blade-demo</module>
        <module>vdm-big-data</module>
        <module>vdm-alarm</module>
        <module>vdm-wrapper</module>
        <module>vdm-statistic</module>
        <module>vdm-websocket</module>
        <module>vdm-third-platform</module>
        <module>vdm-base-info</module>
        <module>vdm-bd-check</module>
        <module>vdm-inter-manager</module>
        <module>vdm-inter-auth</module>
        <module>vdm-mqtt-check</module>
        <module>vdm-mqtt-official</module>
    </modules>

    <dependencies>
        <!--<dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-biz-common</artifactId>
        </dependency>-->
        <!--<dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-tenant</artifactId>
        </dependency>-->
        <!--<dependency>
            <groupId>org.springblade</groupId>
            <artifactId>blade-starter-api-crypto</artifactId>
        </dependency>-->
    </dependencies>

</project>
