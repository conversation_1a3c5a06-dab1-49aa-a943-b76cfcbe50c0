# wrapper 和 go 是一一对应的，每个go服务都会对应一个唯一的wrapper服务，所以，这里ip是写固定的
go-api:
  host: http://***********:8882

blade:
  security:
    skip-url: /**
  jackson:
    null-to-empty: false

#数据源配置
spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          #生产环境
          #    url: **************************************************************************************************************************************************************************************************
          #测试环境
          #url: **************************************************************************************************************************************************************************************************
          #开发环境
          url: ${blade.datasource.dev.url}
          username: ${blade.datasource.dev.username}
          password: ${blade.datasource.dev.password}
          driver-class-name: com.mysql.cj.jdbc.Driver
          hikari:
            auto-commit: true
            pool-name: DatebookHikariCP
            minimum-idle: 5
            maximum-pool-size: 80
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 400000
            # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 500000
            # 数据库连接超时时间,默认30秒，即30000。配置3s。
            connection-timeout: 60000
            connection-test-query: SELECT 1
            validation-timeout: 10000
        location:
          #测试环境
          #url: ***************************************************************************************************************************************************************************************************
          #开发环境：
          url: ***********************************
          username:
          password:
          driver-class-name: com.cloudera.impala.jdbc41.Driver
          hikari:
            auto-commit: true
            pool-name: DatebookHikariCP
            minimum-idle: 5
            maximum-pool-size: 80
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 400000
            # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 500000
            # 数据库连接超时时间,默认30秒，即30000。配置3s。
            connection-timeout: 60000
            connection-test-query: SELECT 1
            validation-timeout: 10000

        impala:
          driver-class-name: com.cloudera.impala.jdbc41.Driver
          url: ***********************************
          username:
          password:
          # Druid连接池配置
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            # 初始化
            initial-size: 30
            # 最大
            max-wait: 200
            # 最小
            min-idle: 3
            # 最大连接等待超时时间
            max-active: 60000
            # 周期性剔除长时间呆在池子里未被使用的空闲连接, 1 min 一次,单位毫秒
            time-between-eviction-runs-millis: 60000
            # 配置一个连接在池中最小生存的时间,单位是毫秒
            min-evictable-idle-time-millis: 300000
            # 设置连接在池中最大存活时长，超过上限才会被清理
            max-evictable-idle-time-millis: 600000
            # 验证连接是否可用，使用的SQL语句
            validation-query: SELECT 'x'
            # 连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
            test-while-idle: false
            # 借出连接时不要测试，否则很影响性能
            test-on-borrow: false
            # 指明是否在归还到池中前进行检验
            test-on-return: false

    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      auto-commit: true
      pool-name: DatebookHikariCP
      minimum-idle: 5
      maximum-pool-size: 80
      # 空闲连接存活最大时间，默认600000（10分钟）
      idle-timeout: 400000
      # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
      max-lifetime: 500000
      # 数据库连接超时时间,默认30秒，即30000。配置3s。
      connection-timeout: 60000
      connection-test-query: SELECT 1
      validation-timeout: 10000



  autoconfigure:
    exclude: org.springblade.core.tool.config.MessageConfiguration


#kudu表
kudu:
  master:
    addr: "vdm1:7051"
  table_name: "impala::pos_gn.locations"
