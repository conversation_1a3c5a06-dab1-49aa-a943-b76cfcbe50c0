#mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath:com/xh/**/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.xh.**.entity
  #configuration:
    #这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl



blade:
  security:
    #skip-url: /**
    #jackson配置
    jackson:
      #null自动转空值
      null-to-empty: false
      #大数字自动转字符串
      big-num-to-string: true
      #支持text文本请求,与报文加密同时开启
      support-text-plain: false

#数据源配置
spring:
  datasource:
    url: ${blade.datasource.dev.url}
    username: ${blade.datasource.dev.username}
    password: ${blade.datasource.dev.password}

  autoconfigure:
    exclude: org.springblade.core.tool.config.MessageConfiguration





