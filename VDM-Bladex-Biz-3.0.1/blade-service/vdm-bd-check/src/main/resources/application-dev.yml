

#数据源配置
spring:
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          #开发环境
          url: ${blade.datasource.dev.url}
          username: ${blade.datasource.dev.username}
          password: ${blade.datasource.dev.password}
          driver-class-name: org.postgresql.Driver
          hikari:
            auto-commit: true
            pool-name: DatebookHikariCP
            minimum-idle: 5
            maximum-pool-size: 80
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 400000
            # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 500000
            # 数据库连接超时时间,默认30秒，即30000。配置3s。
            connection-timeout: 60000
            connection-test-query: SELECT 1
            validation-timeout: 10000
        bdCheck:
          #开发环境
          url: **************************************************************
          username: ${blade.datasource.dev.username}
          password: ${blade.datasource.dev.password}
          driver-class-name: org.postgresql.Driver
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            auto-commit: true
            pool-name: DatebookHikariCP
            minimum-idle: 5
            maximum-pool-size: 80
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 400000
            # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 500000
            # 数据库连接超时时间,默认30秒，即30000。配置3s。
            connection-timeout: 60000
            connection-test-query: SELECT 1
            validation-timeout: 10000
        impala:
          driver-class-name: com.cloudera.impala.jdbc41.Driver
          url: ****************************************
          username:
          password:
          # 连接池配置
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            auto-commit: true
            pool-name: DatebookHikariCP
            minimum-idle: 5
            maximum-pool-size: 80
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 400000
            # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 500000
            # 数据库连接超时时间,默认30秒，即30000。配置3s。
            connection-timeout: 60000
            connection-test-query: SELECT 1
            validation-timeout: 10000
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      auto-commit: true
      pool-name: DatebookHikariCP
      minimum-idle: 5
      maximum-pool-size: 80
      # 空闲连接存活最大时间，默认600000（10分钟）
      idle-timeout: 400000
      # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
      max-lifetime: 500000
      # 数据库连接超时时间,默认30秒，即30000。配置3s。
      connection-timeout: 60000
      connection-test-query: SELECT 1
      validation-timeout: 10000



  autoconfigure:
    exclude: org.springblade.core.tool.config.MessageConfiguration

  redis:
    ##redis 单机环境配置
    host: ***********
    port: 20142
    password: xh123456
    database: 0
    ssl:
      enabled: false


  #北斗检测--实时检测--定位数据
  bd-check-vehicle-terminal:
    bootstrap-servers: ***********:21002,*************:21002,*************:21022
    # 生产者配置
    producer:
      retries: 1
      acks: 1
      batch-size: 1024
      #properties:
      #linger.ms: 0
      #partitioner.class: com.xh.vdm.CustomPartioner
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      #value-serializer: org.apache.kafka.common.serialization.StringSerializer
      #添加受信任的bean目录
      properties:
        spring:
          json:
            trusted:
              packages: "com.springblade.vdm.bdCheck"

    #消费者配置
    consumer:
      auto-offset-reset: latest
      key-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      group-id: position_push_group_real
      properties:
        spring.deserializer.key.delegate.class: org.apache.kafka.common.serialization.StringDeserializer
        spring.deserializer.value.delegate.class: org.springframework.kafka.support.serializer.JsonDeserializer
        spring.json.value.default.type: com.xh.vdm.bdCheck.entity.Location
        session:
          timeout:
            ms: 120000
        request:
          timeout:
            ms: 180000
        #添加受信任的bean目录
        spring:
          json:
            trusted:
              packages: "com.springblade.vdm.bdCheck"

      enable-auto-commit: false
      auto:
        commit:
          interval:
            ms: 1000
      #value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 10000


    listener:
      missing-topics-fatal: false
      type: batch
      ack-mode: manual_immediate




  #北斗检测--实时检测--检测结果
  bd-check-realtime-result:
    bootstrap-servers: ***********:21002,*************:21002,*************:21022
    # 生产者配置
    producer:
      retries: 1
      acks: 1
      batch-size: 1024
      #properties:
      #linger.ms: 0
      #partitioner.class: com.xh.vdm.CustomPartioner
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      #value-serializer: org.apache.kafka.common.serialization.StringSerializer
      #添加受信任的bean目录
      properties:
        spring:
          json:
            trusted:
              packages: "com.xh.vdm.bdCheck"

    #消费者配置
    consumer:
      auto-offset-reset: latest
      key-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      group-id: TerminalLocCheckResult_group
      properties:
        spring.deserializer.key.delegate.class: org.apache.kafka.common.serialization.StringDeserializer
        spring.deserializer.value.delegate.class: org.springframework.kafka.support.serializer.JsonDeserializer
        spring.json.value.default.type: com.xh.vdm.bdCheck.entity.Location
        session:
          timeout:
            ms: 120000
        request:
          timeout:
            ms: 180000
        #添加受信任的bean目录
        spring:
          json:
            trusted:
              packages: "com.xh.vdm.bdCheck"

      enable-auto-commit: false
      auto:
        commit:
          interval:
            ms: 1000
      #value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 10000


    listener:
      missing-topics-fatal: false
      type: batch
      ack-mode: manual_immediate


  #北斗检测--识别检测--定位数据
  bd-check-terminal:
    bootstrap-servers: ***********:21002,*************:21002,*************:21022
    # 生产者配置
    producer:
      retries: 1
      acks: 1
      batch-size: 1024
      #properties:
      #linger.ms: 0
      #partitioner.class: com.xh.vdm.CustomPartioner
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      #value-serializer: org.apache.kafka.common.serialization.StringSerializer
      #添加受信任的bean目录
      properties:
        spring:
          json:
            trusted:
              packages: "com.springblade.vdm.bdCheck"

    #消费者配置
    consumer:
      auto-offset-reset: latest
      key-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      group-id: position_push_group_real
      properties:
        spring.deserializer.key.delegate.class: org.apache.kafka.common.serialization.StringDeserializer
        spring.deserializer.value.delegate.class: org.springframework.kafka.support.serializer.JsonDeserializer
        spring.json.value.default.type: com.xh.vdm.bdCheck.entity.bdCheck.BDCheckLocations
        session:
          timeout:
            ms: 120000
        request:
          timeout:
            ms: 180000
        #添加受信任的bean目录
        spring:
          json:
            trusted:
              packages: "com.springblade.vdm.bdCheck"

      enable-auto-commit: false
      auto:
        commit:
          interval:
            ms: 1000
      #value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 10000


    listener:
      missing-topics-fatal: false
      type: batch
      ack-mode: manual_immediate



  #北斗检测--识别检测--保存数据到kudu
  bd-check-result:
    bootstrap-servers: ***********:21002,*************:21002,*************:21022
    # 生产者配置
    producer:
      retries: 1
      acks: 1
      batch-size: 1024
      #properties:
      #linger.ms: 0
      #partitioner.class: com.xh.vdm.CustomPartioner
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      #value-serializer: org.apache.kafka.common.serialization.StringSerializer
      #添加受信任的bean目录
      properties:
        spring:
          json:
            trusted:
              packages: "com.springblade.vdm.bdCheck"

    #消费者配置
    consumer:
      auto-offset-reset: earliest
      key-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      group-id: TerminalCheckResult_group_30
      properties:
        spring.deserializer.key.delegate.class: org.apache.kafka.common.serialization.StringDeserializer
        spring.deserializer.value.delegate.class: org.springframework.kafka.support.serializer.JsonDeserializer
        spring.json.value.default.type: com.xh.vdm.bdCheck.entity.bdCheck.BDCheckLocationsKudu
        session:
          timeout:
            ms: 120000
        request:
          timeout:
            ms: 180000
        #添加受信任的bean目录
        spring:
          json:
            trusted:
              packages: "com.springblade.vdm.bdCheck"

      enable-auto-commit: false
      auto:
        commit:
          interval:
            ms: 1000
      #value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 100


    listener:
      missing-topics-fatal: false
      type: batch
      ack-mode: manual_immediate



  #临时-最终位置点
  tmp-latest-loc:
    latest-loc:
      enable: true
    bootstrap-servers: ***********:21002,*************:21002,*************:21022
    # 生产者配置
    producer:
      retries: 1
      acks: 1
      batch-size: 1024
      #properties:
      #linger.ms: 0
      #partitioner.class: com.xh.vdm.CustomPartioner
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      #value-serializer: org.apache.kafka.common.serialization.StringSerializer
      #添加受信任的bean目录
      properties:
        spring:
          json:
            trusted:
              packages: "com.xh.vdm.bdCheck"

    #消费者配置
    consumer:
      auto-offset-reset: latest
      key-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.ErrorHandlingDeserializer
      group-id: TmpLatestLoc_local_group
      properties:
        spring.deserializer.key.delegate.class: org.apache.kafka.common.serialization.StringDeserializer
        spring.deserializer.value.delegate.class: org.springframework.kafka.support.serializer.JsonDeserializer
        spring.json.value.default.type: com.xh.vdm.bdCheck.entity.Location
        session:
          timeout:
            ms: 120000
        request:
          timeout:
            ms: 180000
        #添加受信任的bean目录
        spring:
          json:
            trusted:
              packages: "com.xh.vdm.bdCheck"

      enable-auto-commit: false
      auto:
        commit:
          interval:
            ms: 1000
      #value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      max-poll-records: 10000
    listener:
      missing-topics-fatal: false
      type: batch
      ack-mode: manual_immediate






#kudu表
kudu:
  master:
    addr: "vdm1:7051"
  #kudu批量处理提交时，一批中的数据条数
  batch-size: 1
  table_name: "impala::pos_gn.bd_check_real_result"
  check_table_name: "impala::pos_gn.bd_check_locations"



#ftp配置
ftp:
  host: ftp2.csno-tarc.cn
  port: 21
  username: pub
  password: tarc
  path: /brdc/
  localTmpPath: C:\Users\<USER>\Desktop\tmp


#北斗识别报告
bd-check:
  enable: true #如果开启，则入网时，会检测每个定位点；如果关闭，则入网检测结果会固定设置为通过
  template-path: E:\tmp\vdm-gn\template\北斗识别报告模板-表达式.docx
  file-path: E:\tmp\vdm-gn\file\
  proxy-path: /report/bd-check/



