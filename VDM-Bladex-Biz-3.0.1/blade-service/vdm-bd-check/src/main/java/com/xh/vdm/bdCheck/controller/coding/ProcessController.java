package com.xh.vdm.bdCheck.controller.coding;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bdCheck.constant.FormalEnum;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcManufactor;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcManufactorService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bdCheck.vo.coding.request.ProcessRequest;
import com.xh.vdm.bdCheck.vo.coding.request.SyncFromMachineRequest;
import com.xh.vdm.bdCheck.vo.coding.request.group.*;
import com.xh.vdm.bdCheck.vo.coding.response.PassListResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.DictCodeConstant;
import org.springblade.common.constant.DictKeyConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags = "赋码流程", protocols = "http", produces = "application/json", consumes = "application/json")
@RestController
@RequestMapping("/coding/process")
public class ProcessController {

	@Resource
	private IDictBizClient dictBizClient;

	@Resource
	private IBdcTerminalService bdcTerminalService;

	@Resource
	private IBdcManufactorService bdcManufactorService;

	// 对可赋码设备部分字段进行转义，以返回给前端使用。
	private void formPassListResponse (List<PassListResponse> responseList) {
		Map<String, String> deviceTypeMap = new HashMap<>();
		R<Map<String, String>> rdt = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(
			DictCodeConstant.TEST_DEVICE_TYPE,
			DictKeyConstant.DEFAULT_ROOT_KEY
		);
		if (rdt.isSuccess() && (rdt.getData() != null) && (!rdt.getData().isEmpty())) {
			deviceTypeMap = rdt.getData();
		}

		Map<String, String> codeResultMap = new HashMap<>();
		R<Map<String, String>> rcr = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(
			DictCodeConstant.CODE_RESULT,
			DictKeyConstant.DEFAULT_ROOT_KEY
		);
		if (rcr.isSuccess() && (rcr.getData() != null) && (!rcr.getData().isEmpty())) {
			codeResultMap = rcr.getData();
		}

		List<BdcManufactor> manufacturerList = this.bdcManufactorService.list();
		Map<String, String> manufacturerMap = new HashMap<>();
		for (BdcManufactor manufacturer : manufacturerList) {
			manufacturerMap.put(manufacturer.getCode(), manufacturer.getName());
		}
		for (PassListResponse response : responseList) {
			response.setDeviceType(deviceTypeMap.getOrDefault(response.getDeviceType(), ""));
			response.setManufacturer(manufacturerMap.getOrDefault(response.getManufacturer(), ""));
			response.setCodeResult(codeResultMap.getOrDefault(response.getCodeResult(), ""));
			response.setFormalStr(FormalEnum.getValueByKey(response.getFormal()));
		}
	}

	@ApiOperation(value = "可赋码设备的分页列表", httpMethod = "POST")
	@PostMapping("/pass/page")
	public R<IPage<PassListResponse>> getPassPage (@Validated(PassListGroup.class) @RequestBody ProcessRequest request, Query query) {
		try {
			IPage<PassListResponse> res = this.bdcTerminalService.getPassPage(request, query);
			if (res.getTotal() <= 0) {
				return R.data(res);
			}

			this.formPassListResponse(res.getRecords());
			return R.data(res);
		} catch (Exception e) {
			log.error("fail get pass page: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "可赋码设备的分页列表获取异常：" + e.getMessage());
		}
	}

	@ApiOperation(value = "参数检查", httpMethod = "POST")
	@PostMapping("/param/check")
	public R<Map<String, Map<String, Object>>> checkCodeParam (@Validated(CheckParamGroup.class) @RequestBody ProcessRequest request) {
		try {
			return R.data(this.bdcTerminalService.checkCodeParam(request));
		} catch (Exception e) {
			log.error("fail check code param: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "赋码参数检查异常：" + e.getMessage());
		}
	}

	@ApiOperation(value = "生成", httpMethod = "POST")
	@PostMapping("/form")
	public R<Map<String, String>> formDeviceNum (@Validated(FormDeviceNumGroup.class) @RequestBody ProcessRequest request) {
		try {
			return this.bdcTerminalService.formDeviceNum(request);
		} catch (Exception e) {
			log.error("fail form device num: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "生成赋码失败：" + e.getMessage());
		}
	}

	@ApiOperation(value = "下发", httpMethod = "POST")
	@PostMapping("/deliver")
	public R<String> deliverDeviceNum (@Validated(DeliverDeviceNumGroup.class) @RequestBody ProcessRequest request) {
		try {
			return this.bdcTerminalService.deliverDeviceNum(request);
		} catch (Exception e) {
			log.error("fail deliver device num: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "下发赋码失败：" + e.getMessage());
		}
	}

	@ApiOperation(value = "来自赋码机的赋码结果同步至平台", httpMethod = "POST")
	@PostMapping("/sync/from-machine")
	public R<List<String>> syncFromCodingMachine (@Validated(SyncFromMachineGroup.class) @RequestBody SyncFromMachineRequest request) {
		try {
			this.bdcTerminalService.syncFromCodingMachine(request);
			return R.success("");
		} catch (Exception e) {
			log.error("fail sync from coding machine: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "来自赋码机的赋码结果同步至平台失败：" + e.getMessage());
		}
	}

	@ApiOperation(value = "一键批量", httpMethod = "POST")
	@PostMapping("/batch/complete")
	public R<String> batchCompleteCoding (@Validated(BatchCompleteProcessGroup.class) @RequestBody ProcessRequest request) {
		try {
			return this.bdcTerminalService.batchCompleteCoding(request);
		} catch (Exception e) {
			log.error("fail batch complete code process: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "批量赋码流程执行失败：" + e.getMessage());
		}
	}
}
