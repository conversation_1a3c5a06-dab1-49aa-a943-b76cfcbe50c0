<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bdCheck.mapper.BdmSatelliteMapper">

    <insert id="saveOrUpdateSatellite">
        INSERT INTO bdm_satellite (key, flag, num, year, mon, day, hour, min, sec, a0, a1, a2, iode, crs, deltan, m0,
                                   cuc, e, cus, root_a, toe, cic, omega0, cis, i0, crc, omega, omegadot, idot, data,
                                   bdt, adot, sv, hs, tgd, isc, sow, iodc, deltan0dot, sattype, create_time, batch_id)
        values (#{satellite.key,jdbcType=VARCHAR}, #{satellite.flag}, #{satellite.num}, #{satellite.year},
                #{satellite.mon}, #{satellite.day}, #{satellite.hour}, #{satellite.min}, #{satellite.sec},
                #{satellite.a0}, #{satellite.a1}, #{satellite.a2}, #{satellite.iode}, #{satellite.crs},
                #{satellite.deltan}, #{satellite.m0}, #{satellite.cuc}, #{satellite.e}, #{satellite.cus},
                #{satellite.root_a}, #{satellite.toe}, #{satellite.cic}, #{satellite.omega0}, #{satellite.cis},
                #{satellite.i0}, #{satellite.crc}, #{satellite.omega}, #{satellite.omegadot}, #{satellite.idot},
                #{satellite.data}, #{satellite.bdt}, #{satellite.adot}, #{satellite.sv}, #{satellite.hs},
                #{satellite.tgd}, #{satellite.isc}, #{satellite.sow}, #{satellite.iodc}, #{satellite.deltan0dot},
                #{satellite.sattype}, #{satellite.create_time}, #{satellite.batch_id})
        SELECT key, flag, num, year, mon, day, hour, min, sec, a0, a1, a2, iode, crs, deltan, m0, cuc, e, cus, root_a, toe, cic, omega0, cis, i0, crc, omega, omegadot, idot, data, bdt, adot, sv, hs, tgd, isc, sow, iodc, deltan0dot, sattype, create_time, batch_id
        FROM bdm_satellite
        ON CONFLICT (key)
        DO UPDATE SET
            flag=#{satellite.flag},
            num=#{satellite.num},
            year =#{satellite.year},
            mon=#{satellite.mon},
            day =#{satellite.day},
            hour =#{satellite.hour},
            min=#{satellite.min},
            sec=#{satellite.sec},
            a0=#{satellite.a0},
            a1=#{satellite.a1},
            a2=#{satellite.a2},
            iode=#{satellite.iode},
            crs=#{satellite.crs},
            deltan=#{satellite.deltan},
            m0=#{satellite.m0},
            cuc=#{satellite.cuc},
            e=#{satellite.e},
            cus=#{satellite.cus},
            root_a=#{satellite.root_a},
            toe=#{satellite.toe},
            cic=#{satellite.cic},
            omega0=#{satellite.omega0},
            cis=#{satellite.cis},
            i0=#{satellite.i0},
            crc=#{satellite.crc},
            omega=#{satellite.omega},
            omegadot=#{satellite.omegadot},
            idot=#{satellite.idot},
            data =#{satellite.data},
            bdt=#{satellite.bdt},
            adot=#{satellite.adot},
            sv=#{satellite.sv},
            hs=#{satellite.hs},
            tgd=#{satellite.tgd},
            isc=#{satellite.isc},
            sow=#{satellite.sow},
            iodc=#{satellite.iodc},
            deltan0dot=#{satellite.deltan0dot},
            sattype=#{satellite.sattype},
            create_time=#{satellite.create_time},
            batch_id=#{satellite.batch_id}
    </insert>

    <select id="getAllSatelliteInDateList" resultType="com.xh.vdm.bdCheck.entity.BdmSatellite">
        select *
        from bdm_satellite bs
        where num = #{num}
        and concat(year, mon, day) in
        <foreach collection="dateList" item="date" separator="," open="(" close=")">
            #{date}
        </foreach>
        order by year desc , mon desc ,day desc ,hour desc;
    </select>

    <select id="getAllSatelliteInDateListNoNum" resultType="com.xh.vdm.bdCheck.entity.BdmSatellite">
        select *
        from bdm_satellite bs
        where concat(year, mon, day) in
        <foreach collection="dateList" item="date" separator="," open="(" close=")">
            #{date}
        </foreach>
        order by year desc , mon desc ,day desc ,hour desc;
    </select>

    <select id="selectSateDataList" resultType="com.xh.vdm.bdCheck.entity.BdmSatellite">
        SELECT *,
               TO_TIMESTAMP(CONCAT(year, '-', mon, '-', day, ' ', hour, ':', min, ':', sec), 'YYYY-MM-DD HH24:MI:SS') AS locTime
        FROM "bdm_satellite"
        WHERE "year" = (SELECT MAX("year") FROM "bdm_satellite")
          AND "mon" = (SELECT MAX("mon") FROM "bdm_satellite" WHERE "year" = (SELECT MAX("year") FROM "bdm_satellite"))
          AND "day"::integer = (SELECT MAX("day")::integer FROM "bdm_satellite" WHERE "year" = (SELECT MAX("year") FROM "bdm_satellite")
                    AND "mon" = (SELECT MAX("mon") FROM "bdm_satellite" WHERE "year" = (SELECT MAX("year") FROM "bdm_satellite"))
                   ) - 1
        ORDER BY num, "day", "hour" ASC;
    </select>

    <select id="selectNewestSatellite" parameterType="string" resultType="com.xh.vdm.bdCheck.entity.BdmSatellite">
        select * from bdm_satellite
        where key in (
            select max(key)
            from bdm_satellite bs
            where create_time > to_date(#{daysBeforeStr},'yyyy-MM-dd')
            group by num )
        order by num
    </select>
</mapper>
