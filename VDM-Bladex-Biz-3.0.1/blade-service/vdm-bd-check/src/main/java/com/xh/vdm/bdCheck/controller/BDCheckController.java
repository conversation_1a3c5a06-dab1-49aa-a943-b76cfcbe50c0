package com.xh.vdm.bdCheck.controller;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bdCheck.entity.BDCheckRealResult;
import com.xh.vdm.bdCheck.entity.BdmTerminal;
import com.xh.vdm.bdCheck.entity.BladeDept;
import com.xh.vdm.bdCheck.service.IBDCheckRealResultService;
import com.xh.vdm.bdCheck.service.IBdmTerminalService;
import com.xh.vdm.bdCheck.service.IBladeDeptService;
import com.xh.vdm.bdCheck.task.BDCheckTask;
import com.xh.vdm.bdCheck.vo.BDCheckCountVO;
import com.xh.vdm.bdCheck.vo.BDCheckDateResult;
import com.xh.vdm.bdCheck.vo.BDCheckRealRequest;
import com.xh.vdm.bdCheck.vo.BDCheckRealStatResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 北斗实时检测结果
 */
@RestController
@RequestMapping("/bdCheck")
@Slf4j
public class BDCheckController {

	@Resource
	private IBDCheckRealResultService realResultService;

	@Resource
	private IBdmTerminalService terminalService;

	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(20, 50, 120, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

	@Resource
	private IBladeDeptService deptService;

	@Resource
	private BDCheckTask task;

	/**
	 * 北斗终端实时检测
	 *
	 * @param request
	 * @return
	 */
	@PostMapping("/real/realStat")
	public R<IPage<BDCheckRealStatResponse>> bdCheckRealStat(@RequestBody BDCheckRealRequest request, Query query) {
		long methodStartTime = System.currentTimeMillis();
		log.info("开始执行bdCheckRealStat查询 - 请求参数: {}, 分页: current={}, size={}", request, query.getCurrent(), query.getSize());

		IPage<BDCheckRealStatResponse> resPage = new Page<>(query.getCurrent(), query.getSize());

		try {
			// 1.查询终端信息
			long terminalQueryStart = System.currentTimeMillis();
			IPage<BdmTerminal> page = terminalService.findTerminalPage(request, query);
			log.info("步骤1-查询终端信息完成: {}ms, 查询到{}条记录", System.currentTimeMillis() - terminalQueryStart, page != null ? page.getRecords().size() : 0);
			if (page == null || page.getRecords() == null || page.getRecords().size() < 1) {
				return R.data(null);
			}
			resPage.setTotal(page.getTotal());

			// 使用优化后的批量并行处理方式
			List<BdmTerminal> terminalList = page.getRecords();
			List<BDCheckRealStatResponse> resList = bdCheckRealStatOptimized(terminalList);

			// 对结果集按照deviceNum进行排序
			Collections.sort(resList, Comparator.comparing(BDCheckRealStatResponse::getUniqueId));
			resPage.setRecords(resList);

			long totalTime = System.currentTimeMillis() - methodStartTime;
			log.info("bdCheckRealStat查询完成，总耗时: {}ms, 处理设备数: {}", totalTime, terminalList.size());
		} catch (Exception e) {
			log.error("操作失败", e);
			return R.fail("操作失败");
		}
		return R.data(resPage);
	}

	/**
	 * 优化后的批量并行处理方法
	 * 按步骤并行化，每个步骤处理所有设备
	 */
	private List<BDCheckRealStatResponse> bdCheckRealStatOptimized(List<BdmTerminal> terminalList) throws Exception {
		long startTime = System.currentTimeMillis();

		// 提取所有设备号和部门ID
		List<String> deviceNums = terminalList.stream().map(BdmTerminal::getDeviceNum).collect(Collectors.toList());
		Set<Long> deptIds = terminalList.stream().map(BdmTerminal::getDeptId).filter(Objects::nonNull).collect(Collectors.toSet());

		// 创建结果映射
		Map<String, BDCheckRealStatResponse> responseMap = new ConcurrentHashMap<>();

		// 初始化响应对象
		for (BdmTerminal terminal : terminalList) {
			BDCheckRealStatResponse response = new BDCheckRealStatResponse();
			response.setTerminalType(terminal.getTerminalType());
			response.setDeviceNum(terminal.getDeviceNum());
			response.setUniqueId(terminal.getUniqueId());
			response.setTerminalModel(terminal.getTerminalModel());
			responseMap.put(terminal.getDeviceNum(), response);
		}

		// 使用CompletableFuture并行执行各个步骤
		CompletableFuture<Void> step1Future = CompletableFuture.runAsync(() -> {
			try {
				batchProcessDateAndNearCounts(deviceNums, responseMap);
			} catch (Exception e) {
				log.error("批量处理日期和近期统计失败", e);
			}
		}, threadPool);

		CompletableFuture<Void> step2Future = CompletableFuture.runAsync(() -> {
			try {
				batchFindRealCheckAllCount(deviceNums, responseMap);
			} catch (Exception e) {
				log.error("批量处理总量统计失败", e);
			}
		}, threadPool);
		CompletableFuture<Void> step3Future = CompletableFuture.runAsync(() -> {
			try {
				batchFindRealCheckNonBDCount(deviceNums, responseMap);
			} catch (Exception e) {
				log.error("批量处理总量统计失败", e);
			}
		}, threadPool);

		CompletableFuture<Void> step4Future = CompletableFuture.runAsync(() -> {
			try {
				batchProcessDeptInfo(terminalList, deptIds, responseMap);
			} catch (Exception e) {
				log.error("批量处理部门信息失败", e);
			}
		}, threadPool);

		// 等待所有步骤完成
		CompletableFuture.allOf(step1Future, step2Future, step3Future, step4Future).join();

		List<BDCheckRealStatResponse> result = new ArrayList<>(responseMap.values());
		long totalTime = System.currentTimeMillis() - startTime;
		log.info("优化后的批量处理完成，总耗时: {}ms, 处理设备数: {}", totalTime, terminalList.size());

		return result;
	}

	/**
	 * 批量处理日期和近期统计数据
	 */
	private void batchProcessDateAndNearCounts(List<String> deviceNums, Map<String, BDCheckRealStatResponse> responseMap) throws Exception {
		long stepStart = System.currentTimeMillis();

		// 批量获取日期信息和近期统计
		List<BDCheckDateResult> checkDateResultList = new ArrayList<>();

		// 步骤1: 批量获取日期列表
		if (!BDCheckTask.statRunningFlag) {
			// 从缓存中批量获取 - 优化：移除for循环，直接批量处理
			try {
				for (String deviceNum : deviceNums) {
					List<String> dateList = BDCheckTask.BD_CHECK_DATE_NEAR.get(deviceNum);
					String lastDate = calculateLastDate(dateList);
					BDCheckDateResult bdCheckDateResult = new BDCheckDateResult(deviceNum, lastDate);
					checkDateResultList.add(bdCheckDateResult);
				}
			} catch (Exception e) {
				log.error("从缓存批量获取日期列表失败", e);
			}
		} else {
			// 批量查询数据库 - 优化：直接使用批量查询，无需for循环
			try {
				Map<String, List<String>> deviceDateListMap = realResultService.batchFindRealCheckDateByDeviceNum(deviceNums);
				for (String deviceNum : deviceNums) {
					List<String> dateList = deviceDateListMap.get(deviceNum);

					String lastDate = calculateLastDate(dateList);
					BDCheckDateResult bdCheckDateResult = new BDCheckDateResult(deviceNum, lastDate);
					checkDateResultList.add(bdCheckDateResult);
				}
			} catch (Exception e) {
				log.error("批量查询设备日期列表失败", e);
			}
		}

		log.info("批量获取日期信息完成: {}ms, 处理设备数: {}", System.currentTimeMillis() - stepStart, deviceNums.size());

		// 步骤2&3: 批量查询近期统计数据（合并查询）- 优化：使用已合并的查询方法
		long nearCountStart = System.currentTimeMillis();
		try {
			// 批量查询最近三天总校验量和非北斗数据量（合并查询）
			Map<String, BDCheckCountVO> nearCountsMap = realResultService.batchFindRealCheckCountAndNonBDCountGE(checkDateResultList);

			// 设置结果
			for (String deviceNum : deviceNums) {
				BDCheckRealStatResponse response = responseMap.get(deviceNum);
				BDCheckCountVO countVO = nearCountsMap.get(deviceNum);
				if (countVO != null) {
					response.setTotalCountNear(countVO.getTotalCount() != null ? countVO.getTotalCount() : 0L);
					response.setTotalNonDBCountNear(countVO.getNonBdCount() != null ? countVO.getNonBdCount() : 0L);
				} else {
					response.setTotalCountNear(0L);
					response.setTotalNonDBCountNear(0L);
				}
			}
		} catch (Exception e) {
			log.error("批量查询近期统计失败", e);
		}

		log.info("批量处理近期统计完成: {}ms, 处理设备数: {}", System.currentTimeMillis() - nearCountStart, deviceNums.size());
	}

	/**
	 * 批量查询总检测数据量
	 */
	private void batchFindRealCheckAllCount(List<String> deviceNums, Map<String, BDCheckRealStatResponse> responseMap) throws Exception {
		long stepStart = System.currentTimeMillis();

		try {
			// 批量查询总检测数据量
			Map<String, Long> totalCountMap = realResultService.batchFindRealCheckAllCount(deviceNums);

			// 设置结果
			for (String deviceNum : deviceNums) {
				BDCheckRealStatResponse response = responseMap.get(deviceNum);
				response.setTotalCount(totalCountMap.getOrDefault(deviceNum, 0L));
			}
		} catch (Exception e) {
			log.error("批量查询总量统计失败", e);
		}

		log.info("批量查询总检测数据量: {}ms, 处理设备数: {}", System.currentTimeMillis() - stepStart, deviceNums.size());
	}

	/**
	 * 批量查询总非北斗数据量
	 */
	private void batchFindRealCheckNonBDCount(List<String> deviceNums, Map<String, BDCheckRealStatResponse> responseMap) throws Exception {
		long stepStart = System.currentTimeMillis();

		try {
			// 批量查询总非北斗数据量
			Map<String, Long> totalNonBDCountMap = realResultService.batchFindRealCheckNonBDCount(deviceNums);

			// 设置结果
			for (String deviceNum : deviceNums) {
				BDCheckRealStatResponse response = responseMap.get(deviceNum);
				response.setTotalNonDBCount(totalNonBDCountMap.getOrDefault(deviceNum, 0L));
			}
		} catch (Exception e) {
			log.error("批量查询总量统计失败", e);
		}

		log.info("批量查询总非北斗数据量: {}ms, 处理设备数: {}", System.currentTimeMillis() - stepStart, deviceNums.size());
	}

	/**
	 * 批量处理部门信息
	 */
	private void batchProcessDeptInfo(List<BdmTerminal> terminalList, Set<Long> deptIds, Map<String, BDCheckRealStatResponse> responseMap) throws Exception {
		long stepStart = System.currentTimeMillis();

		// 批量查询部门信息
		Map<Long, String> deptNameMap = new HashMap<>();
		if (!deptIds.isEmpty()) {
			try {
				List<BladeDept> deptList = deptService.listByIds(deptIds);
				for (BladeDept dept : deptList) {
					deptNameMap.put(dept.getId(), dept.getDeptName());
				}
			} catch (Exception e) {
				log.error("批量查询部门信息失败", e);
			}
		}

		// 设置部门名称
		for (BdmTerminal terminal : terminalList) {
			BDCheckRealStatResponse response = responseMap.get(terminal.getDeviceNum());
			String deptName = deptNameMap.get(terminal.getDeptId());
			response.setDeptName(deptName != null ? deptName : "未知");
		}

		log.info("批量处理部门信息完成: {}ms, 处理设备数: {}", System.currentTimeMillis() - stepStart, terminalList.size());
	}

	/**
	 * 计算最后日期 - 获取倒数第三个日期
	 */
	private String calculateLastDate(List<String> dateList) {
		String lastDate = "";
		if (dateList != null && !dateList.isEmpty()) {
			if (dateList.size() < 3) {
				lastDate = dateList.get(dateList.size() - 1);
			} else {
				// 取倒数第三个，dateList按日期降序排列，索引2就是倒数第三个
				lastDate = dateList.get(2);
			}
		}
		return lastDate;
	}

	/**
	 * 北斗实时检测详情--最新数据
	 *
	 * @param deviceNum 赋码号
	 * @return
	 */
	@GetMapping("/real/newest")
	public R<IPage<BDCheckRealResult>> bdRealCheckNewestData(String deviceNum, Query query) {
		IPage<BDCheckRealResult> page = null;
		if (StringUtils.isEmpty(deviceNum)) {
			return R.fail("赋码号不能为空");
		}
		try {
			page = realResultService.findRealResultPage(deviceNum, query);
		} catch (Exception e) {
			log.error("查询实时北斗检测结果失败", e);
			return R.fail("查询失败");
		}
		return R.data(page);
	}

	/**
	 * 北斗实时检测详情--非北斗信息
	 *
	 * @param deviceNum 赋码号
	 * @return
	 */
	@GetMapping("/real/nonBD")
	public R<IPage<BDCheckRealResult>> bdRealCheckNonBDData(String deviceNum, Query query) {
		IPage<BDCheckRealResult> page = null;
		if (StringUtils.isEmpty(deviceNum)) {
			return R.fail("赋码号不能为空");
		}
		try {
			page = realResultService.findRealNonBDPage(deviceNum, query);
		} catch (Exception e) {
			log.error("查询实时北斗检测结果非单北斗信息失败", e);
			return R.fail("查询失败");
		}
		return R.data(page);
	}

}
