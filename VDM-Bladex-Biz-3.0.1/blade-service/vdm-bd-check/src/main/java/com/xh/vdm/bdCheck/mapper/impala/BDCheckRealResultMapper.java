package com.xh.vdm.bdCheck.mapper.impala;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bdCheck.entity.BDCheckRealResult;
import com.xh.vdm.bdCheck.vo.BDCheckAllCountResult;
import com.xh.vdm.bdCheck.vo.BDCheckCountResult;
import com.xh.vdm.bdCheck.vo.BDCheckDateResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-20
 */
@DS("impala")
public interface BDCheckRealResultMapper extends BaseMapper<BDCheckRealResult> {

	/**
	 * 分页查询实时检测结果
	 * @param deviceNum
	 * @return
	 */
	IPage<BDCheckRealResult> getRealResultPage(@Param("deviceNum") String deviceNum, IPage<BDCheckRealResult> page);

	/**
	 * 分页查询实时检测非北斗数据
	 * @param deviceNum
	 * @return
	 */
	IPage<BDCheckRealResult> getRealNonBDPage(@Param("deviceNum") String deviceNum, IPage<BDCheckRealResult> page);

	/**
	 * 查询实时检测表中所有的终端赋码号
	 * @return
	 */
	List<String> getRealCheckAllDeviceNum();

	/**
	 * 根据赋码号查询所有在线检测日期
	 * @param deviceNum
	 * @return
	 */
	List<String> getRealCheckAllDateByDeviceNum(@Param("deviceNum") String deviceNum);

	/**
	 * 批量查询所有在线检测日期
	 * @param deviceNums 设备号列表
	 * @return 结果列表，包含设备号和日期
	 */
	List<BDCheckDateResult> batchGetRealCheckAllDateByDeviceNum(@Param("deviceNums") List<String> deviceNums);

	/**
	 * 查询给定日期之后的实时检测数据量（包含给定日期）
	 * @param deviceNum 赋码号
	 * @param dateStr 给定日期 yyyy-MM-dd
	 * @return
	 */
	Long getRealCheckCountGE(@Param("deviceNum") String deviceNum, @Param("dateStr") String dateStr);

	/**
	 * 查询给定日期之后的实时检测非单北斗数据量（包含给定日期）
	 * @param deviceNum 赋码号
	 * @param dateStr 给定日期 yyyy-MM-dd
	 * @return
	 */
	Long getRealCheckNonBDCountGE(@Param("deviceNum") String deviceNum, @Param("dateStr") String dateStr);

	/**
	 * 查询终端总北斗检测数据量（包括北斗和非单北斗）
	 * 通过查询所有有效定位点实现
	 * @param deviceNum
	 * @return
	 */
	Long getRealCheckAllCount(@Param("deviceNum") String deviceNum);

	/**
	 * 批量查询给定日期之后的实时检测数据量（合并查询总量和非北斗量）
	 * @param checkDateResultList
	 * @return 结果列表，包含设备号、总量和非北斗量
	 */
	List<BDCheckCountResult> batchGetRealCheckCountGE(@Param("checkDateResultList") List<BDCheckDateResult> checkDateResultList);

	/**
	 * 批量查询终端总北斗检测数据量
	 * @param deviceNums 设备号列表
	 * @return 结果列表，包含设备号和数量
	 */
	List<BDCheckAllCountResult> batchGetRealCheckAllCount(@Param("deviceNums") List<String> deviceNums);

	/**
	 * 批量查询总非北斗数据量
	 * @param deviceNums 设备号列表
	 * @return 结果列表，包含设备号和非北斗数量
	 */
	List<BDCheckAllCountResult> batchGetRealCheckNonBDCount(@Param("deviceNums") List<String> deviceNums);
}
