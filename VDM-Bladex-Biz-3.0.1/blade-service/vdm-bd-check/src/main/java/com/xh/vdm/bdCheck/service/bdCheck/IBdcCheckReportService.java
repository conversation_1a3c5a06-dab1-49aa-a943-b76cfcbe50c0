package com.xh.vdm.bdCheck.service.bdCheck;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCheckReport;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bdCheck.vo.bdCheck.BDCReportRequest;
import org.springblade.core.mp.support.Query;

/**
 * <p>
 * 检测报告表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
public interface IBdcCheckReportService extends IService<BdcCheckReport> {

	/**
	 * 分页查询检测报告信息
	 * @param request
	 * @param query
	 * @return
	 */
	IPage<BdcCheckReport> findCheckReportPage(BDCReportRequest request, Query query) throws Exception;

	/**
	 * 重置检测信息状态
	 * @param reportId
	 * @throws Exception
	 */
	void resetCheckState(Long reportId) throws Exception;

}
