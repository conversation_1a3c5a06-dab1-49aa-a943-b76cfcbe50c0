package com.xh.vdm.bdCheck.config;

import org.apache.kudu.client.KuduClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.net.InetAddress;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/5/26 16:56
 */
@Configuration
public class KuduConfig {


	@Value("${kudu.master.addr}")
	private String kuduMasterAddr;

	@Bean
	public KuduClient kuduClient(){
		KuduClient client = new KuduClient.KuduClientBuilder(kuduMasterAddr)
			.defaultSocketReadTimeoutMs(6000).build();
		return client;
	}

}
