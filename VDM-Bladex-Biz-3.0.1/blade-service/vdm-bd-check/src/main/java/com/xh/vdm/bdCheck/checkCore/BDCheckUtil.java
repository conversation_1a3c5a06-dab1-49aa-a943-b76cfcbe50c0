package com.xh.vdm.bdCheck.checkCore;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.bdCheck.dto.BDCheckResAndMessage;
import com.xh.vdm.bdCheck.entity.BdmSatellite;
import com.xh.vdm.bdCheck.entity.Location;
import com.xh.vdm.bdCheck.entity.TerminalSatelliteData;
import com.xh.vdm.bdCheck.service.IBdmSatelliteService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 北斗检测工具类
 * 采用B1I/B3I的星历数据和公式计算卫星的x、y、z
 */
@Slf4j
@Component
public class BDCheckUtil {

	public static double d2r = 3.14159265358979 / 180;

	@Resource
	private RedisTemplate<String,Object> redisTemplate;

	@Resource
	private IBdmSatelliteService satelliteService;

	//用于存储星历数据, key: yyyy-MM-dd  value: 星历的key-星历对象
	private static ConcurrentHashMap<String,Map<String,BdmSatellite>> satelliteMap = new ConcurrentHashMap<>();

	//用于存储星历数据计时，每组星历数据（每天的星历数据），最多保留两个小时
	private static ConcurrentHashMap<String,Long> satelliteTimestampMap = new ConcurrentHashMap<>();

	//试验星，是北斗卫星，但是在星历数据中不包含
	public static List<String> TEST_SATE_LIST = new ArrayList<>();
	{
		TEST_SATE_LIST.add("31");
		TEST_SATE_LIST.add("56");
		TEST_SATE_LIST.add("57");
		TEST_SATE_LIST.add("58");
		TEST_SATE_LIST.add("61");
		TEST_SATE_LIST.add("62");
		TEST_SATE_LIST.add("48");
		TEST_SATE_LIST.add("50");
	}

	//卫星类型，不同卫星类型采用不同的公式
	//todo 后续应该从字典中获取
	public static Map<String,String> SATE_TYPE = new HashMap<>();
	{
		SATE_TYPE.put("01","GEO");
		SATE_TYPE.put("02","GEO");
		SATE_TYPE.put("03","GEO");
		SATE_TYPE.put("04","GEO");
		SATE_TYPE.put("05","GEO");
		SATE_TYPE.put("06","IGSO");
		SATE_TYPE.put("07","IGSO");
		SATE_TYPE.put("08","IGSO");
		SATE_TYPE.put("09","IGSO");
		SATE_TYPE.put("10","IGSO");
		SATE_TYPE.put("11","MEO");
		SATE_TYPE.put("12","MEO");
		SATE_TYPE.put("13","IGSO");
		SATE_TYPE.put("14","MEO");
		SATE_TYPE.put("16","IGSO");
		SATE_TYPE.put("19","MEO");
		SATE_TYPE.put("20","MEO");
		SATE_TYPE.put("21","MEO");
		SATE_TYPE.put("22","MEO");
		SATE_TYPE.put("23","MEO");
		SATE_TYPE.put("24","MEO");
		SATE_TYPE.put("25","MEO");
		SATE_TYPE.put("26","MEO");
		SATE_TYPE.put("27","MEO");
		SATE_TYPE.put("28","MEO");
		SATE_TYPE.put("29","MEO");
		SATE_TYPE.put("30","MEO");
		SATE_TYPE.put("31","IGSO");
		SATE_TYPE.put("32","MEO");
		SATE_TYPE.put("33","MEO");
		SATE_TYPE.put("34","MEO");
		SATE_TYPE.put("35","MEO");
		SATE_TYPE.put("36","MEO");
		SATE_TYPE.put("37","MEO");
		SATE_TYPE.put("38","IGSO");
		SATE_TYPE.put("39","IGSO");
		SATE_TYPE.put("40","IGSO");
		SATE_TYPE.put("41","MEO");
		SATE_TYPE.put("42","MEO");
		SATE_TYPE.put("43","MEO");
		SATE_TYPE.put("44","MEO");
		SATE_TYPE.put("45","MEO");
		SATE_TYPE.put("46","MEO");
		SATE_TYPE.put("56","IGSO");
		SATE_TYPE.put("57","MEO");
		SATE_TYPE.put("58","MEO");
		SATE_TYPE.put("59","GEO");
		SATE_TYPE.put("60","GEO");
		SATE_TYPE.put("61","GEO");
		SATE_TYPE.put("62","GEO");
		SATE_TYPE.put("48","MEO");
		SATE_TYPE.put("50","MEO");

	}

	//*****************卫星轨道分组***********************************
	//1.MEO
	public static List<String> MEO_A = new ArrayList<>();
	public static List<String> MEO_B = new ArrayList<>();
	public static List<String> MEO_C = new ArrayList<>();
	{
		MEO_A.add("23");
		MEO_A.add("37");
		MEO_A.add("46");
		MEO_A.add("36");
		MEO_A.add("26");
		MEO_A.add("24");
		MEO_A.add("25");
		MEO_A.add("45");
		MEO_B.add("14");
		MEO_B.add("33");
		MEO_B.add("41");
		MEO_B.add("32");
		MEO_B.add("20");
		MEO_B.add("48");
		MEO_B.add("19");
		MEO_B.add("22");
		MEO_B.add("21");
		MEO_B.add("50");
		MEO_B.add("42");
		MEO_C.add("28");
		MEO_C.add("43");
		MEO_C.add("11");
		MEO_C.add("34");
		MEO_C.add("12");
		MEO_C.add("44");
		MEO_C.add("35");
		MEO_C.add("27");
		MEO_C.add("30");
		MEO_C.add("29");
	}




	public BDCheckResAndMessage bdCheck(Location location){
		BDCheckResAndMessage bds = new BDCheckResAndMessage();
		//数据校验
 		if(location == null || location.getLocTime() == null || location.getLongitude() == null || location.getLatitude() == null ){
			bds.setResult(false);
			bds.setMessage("定位数据有误");
			return bds;
		}
		if( location.getAuxsNormal() == null || location.getAuxsNormal().get(CommonConstant.AUXS_STATELLITE_KEY) == null){
			bds.setResult(false);
			bds.setMessage("未包含卫星数据");
			return bds;
		}

		/**
		 * 注意：北斗识别，不需要做坐标系的转换。终端传上来的数据是CGCS2000，北斗星历数据计算公式中使用的也是CGCS2000
		 */
		//1.获取定位点中的经度、纬度、高程、定位时间
		Double longitude = location.getLongitude();
		Double latitude = location.getLatitude();
		Long altitude = location.getAltitude();
		Long locTime = location.getLocTime();
		//输入角度转化为弧度
		double[] rcvPositionNEU = new double[3];
		rcvPositionNEU[0] = latitude  * d2r;// 纬度
		rcvPositionNEU[1] =  longitude * d2r;// 经度
		rcvPositionNEU[2] = altitude;// 高程
		LocalDateTime dateTime = LocalDateTime.ofEpochSecond(locTime, 0, ZoneOffset.UTC);//定位时间
		//计算在本周第几天
		int tt = calculationTT(dateTime);

		//获取定位点中的星历数据
		List<TerminalSatelliteData> satelliteData = new ArrayList<>();
		try {
			satelliteData = JSONArray.parseArray(JSON.toJSONString(location.getAuxsNormal().get(CommonConstant.AUXS_STATELLITE_KEY)), TerminalSatelliteData.class);
		}catch (Exception e){
			log.error("从定位信息中解析星历数据失败",e);
			bds.setResult(false);
			bds.setMessage("卫星数据错误");
			return bds;
		}

		//从本地缓存中获取星历数据（星历数据只保存两个小时）
		String dateStr = DateUtil.getDateString(locTime);
		String year = dateStr.substring(0,4);
		String month = dateStr.substring(5,7);
		String day = dateStr.substring(8,10);
		String dayStr = dateStr.replace(" ","").replace("-","").substring(0,8);
		Map<String,BdmSatellite> satelliteDataMap = satelliteMap.get(dateStr);
		//判断星历数据有效期
		Long timestamp = satelliteTimestampMap.get(dateStr);
		if(timestamp != null && System.currentTimeMillis() - timestamp > 2 * 3600 * 1000){
			//如果数据过期，则删除数据
			satelliteMap.remove(dateStr);
			satelliteTimestampMap.remove(dateStr);
		}
		if(satelliteDataMap == null || satelliteDataMap.size() < 1){
			//如果没有从本地缓存中获取星历数据，则查询数据库
			List<BdmSatellite> list = satelliteService.findSatelliteByDay(dateStr);
			if(list == null || list.size() < 1){
				//如果不存在当天的星历数据，则查询前三天内的数据（三天之前的会损失经度）
				//todo zhouxw 2024-06-21 应该使用3天内的数据，但是因为北斗办的ftp挂了，这里为了平台能够正常运行，临时设置为10天
				int days = 3;
				try {
					Date beforeDay = DateUtil.getDateBeforeDay(DateUtil.transForDate(locTime), days);
					List<String> dateArray = DateUtil.getDateList(beforeDay, new Date());
					List<String> dateArrayNoLine = new ArrayList<>();
					dateArray.forEach(date -> {
						String dateNoLine = date.replace("-","");
						dateArrayNoLine.add(dateNoLine);
					});
					list = satelliteService.findAllSatelliteInDateList(dateArrayNoLine);
				} catch (Exception e) {
					log.error("获取星历数据失败",e);
					bds.setMessage("获取星历数据失败");
					bds.setResult(false);
					return bds;
				}
			}
			//整理数据，保存到本地缓存中
			Map<String,BdmSatellite> map = new LinkedHashMap<>();
			for(BdmSatellite sate : list){
				map.put(sate.getKey(), sate);
			}

			satelliteMap.put(dateStr, map);
			satelliteDataMap = map;
			//记录有效期
			satelliteTimestampMap.put(dateStr, System.currentTimeMillis());
		}
		//对所有观测到的星都进行判断
		for (int i = 0; i < satelliteData.size(); i++) {
			//卫星编号
			int id = satelliteData.get(i).getId();
			int ratio = satelliteData.get(i).getRatio();
			int flag = satelliteData.get(i).getFlag();
			int idOri = id;
			/**
			 * 旧版本协议中，北斗卫星编号范围是 201 ~ 260；新版本协议中，北斗卫星编号范围是 01 ~ 60
			 * 此处要进行兼容：当星号范围大于200时，卫星编号减去200；当星号小于200时，卫星编号使用原编号
			 */
			if(id > 200){
				id = id - 200;
			}

			//优化：不对0号卫星进行判断
			if(id == 0){
				continue;
			}


			if(TEST_SATE_LIST.contains(id+"")){
				//如果是北斗试验星，则不需要验证具体星历，直接认为通过
				continue;
			}

			//优化：modify by zhouxw 2024-11-26，不对没有用于定位的卫星进行检测
			if(flag == 0){
				continue;
			}

			//优化：不对信噪比小于30的卫星进行检测
			if(ratio < 30){
				//如果信噪比小于30，表示信号不好，俯仰角和方位角不准，过滤掉，不做校验
				continue;
			}


			//星号判断，非北斗卫星不进行后续校验
			String idStr = "";
			if(id < 10){
				idStr = "0" + id;
			}else{
				idStr = id + "";
			}
			if(!SATE_TYPE.containsKey(idStr)){
				bds.setMessage("该星[" + idOri + "]不是北斗卫星");
				bds.setResult(false);
				return bds;
			}

			String key = calculationKey(dateTime, id);
			//星历数据
			BdmSatellite sateInDB = satelliteDataMap.get(key);
			/*if(satelliteData.get(i).getId() == 9){
				log.info("调试：9号星");
			}*/
			if(sateInDB == null ){
				if(sateInDB == null){
					log.debug("本地缓存中没有存储该星[{}]数据，将要获取有效内最新的数据", id);
					for(String k : satelliteDataMap.keySet()){
						String numTmp = k.substring(0,3);
						String num = key.substring(0,3);
						if(num.equals(numTmp)){
							sateInDB = satelliteDataMap.get(k);
							//因为是排了序的，从最新的开始，所以找到之后，就不再找后边的了
							break;
						}
					}
				}
			}
			if(sateInDB == null){
				//如果星历数据仍然是空，则查询数据库
				//如果数据库中未保存该星的星历数据，则不执行后续对比操作，认为是北斗定位
				log.debug("未保存该星【"+(idOri)+"】星历数据");
				bds.setResult(false);
				bds.setMessage("北斗星历数据中不包含该星["+(idOri)+"]数据，请联系系统管理员");
				return bds;
			}
			//计算测站XYZ坐标
			double[] calculation = calculation(sateInDB, tt, rcvPositionNEU);
			//俯仰角
			double elevation = calculation[0];
			//方位角
			double azimuth = calculation[1];

			//北斗识别
			//todo 设置的阈值为3，但是因为北斗办的ftp挂掉了，数据是4天之前的，经度差很多，所以，这里临时将阈值设置为20
			double limit = 3D;

			//判断俯仰角
			boolean elevationFlag = true;

			if(satelliteData.get(i).getElevation() >= elevation){
				boolean f1 = true;
				if(satelliteData.get(i).getElevation() - elevation > limit){
					f1 = false;
				}
				boolean f2 = true;
				if(elevation + 360 - satelliteData.get(i).getElevation() > limit){
					f2 = false;
				}
				elevationFlag = f1 | f2;
			}else{
				boolean f1 = true;
				if(elevation - satelliteData.get(i).getElevation() > limit){
					f1 = false;
				}
				boolean f2 = true;
				if(satelliteData.get(i).getElevation() + 360 - elevation > limit){
					f2 = false;
				}
				elevationFlag = f1 | f2;
			}
			boolean azimuthFlag = true;
			if(satelliteData.get(i).getAzimuth() >= azimuth){
				boolean f1 = true;
				if(satelliteData.get(i).getAzimuth() - azimuth > limit){
					f1 = false;
				}
				boolean f2 = true;
				if(azimuth + 360 - satelliteData.get(i).getAzimuth() > limit){
					f2 = false;
				}
				azimuthFlag = f1 | f2;
			}else{
				boolean f1 = true;
				if(azimuth - satelliteData.get(i).getAzimuth() > limit){
					f1 = false;
				}
				boolean f2 = true;
				if(satelliteData.get(i).getAzimuth() + 360 - azimuth > limit){
					f2 = false;
				}
				azimuthFlag = f1 | f2;
			}

			if( !(elevationFlag && azimuthFlag) ){
				log.info("北斗识别不通过：deviceNo={}, deviceNum={}, 卫星id={}, 计算值 elevation={} azimuth={}，上传值 elevation={} azimuth={}", location.getDeviceNo(), location.getDeviceNum(), idOri,satelliteData.get(i).getElevation(), satelliteData.get(i).getAzimuth(), elevation, azimuth);
				bds.setResult(false);
				bds.setMessage("疑似非单北斗定位，不合规数据为：" + JSON.toJSONString(satelliteData.get(i))+"，定位数据为："+JSON.toJSONString(location)+"，具体不合规数据：deviceNo="+location.getDeviceNo()+", deviceNum="+location.getDeviceNum()+", 卫星id="+idOri+", 上传值 elevation="+satelliteData.get(i).getElevation()+" azimuth="+satelliteData.get(i).getAzimuth()+"，计算值 elevation="+elevation+" azimuth="+azimuth);
				return bds;
			}
		}
		bds.setResult(true);
		bds.setMessage("北斗定位");
		return bds;
	}

	/**
	 * 获取星历数据key
	 * @param dateTime 定位时间
	 * @param num 卫星号
	 * @return
	 */
	private static String calculationKey(LocalDateTime dateTime ,int num ) {
		// 计算在本周第几天
		int year = dateTime.getYear();
		int month = dateTime.getMonth().getValue();
		int day = dateTime.getDayOfMonth();
		int hour = dateTime.getHour();

		String numStr = "";
		if(num < 10){
			numStr = "0"+num;
		}else{
			numStr = num+"";
		}

		// 卫星XYZ坐标
		String mon = month + "";
		if (month <= 9) {
			mon = "0" + mon;
		}
		String dd = day + "";
		if (day <= 9) {
			dd = "0" + dd;
		}
		String ho = hour + "";
		if (hour <= 9) {
			ho = "0" + ho;
		}
		String str = "C" +numStr+ year + mon + dd + ho;
		return str;
	}

	public static int calculationTT(LocalDateTime dateTime) {
		// 计算在本周第几天
		int dayOfWeek = dateTime.getDayOfWeek().getValue();
		int hour1 = dateTime.getHour();
		int minute = dateTime.getMinute();
		int second = dateTime.getSecond();
		int tt = dayOfWeek * 24 * 60 * 60 + hour1 * 60 * 60 + minute * 60 + second;
		return tt;
	}

	/**
	 * 计算观测点观测卫星时的俯仰角和方位角
	 * [0]俯仰角
	 * [1]方位角
	 * @param bdCheck
	 * @param tt
	 * @param rcvPositionNEU
	 * @return
	 */
	private static double[]  calculation(BdmSatellite bdCheck,int tt ,double[] rcvPositionNEU){
		//if(bdCheck.getNum().equals("44")){
		//	log.info("here is ok");
		//}
		double d2r = 3.14159265358979 / 180;
		double r2d = 180 / 3.14159265358979;
		double[] satPosition = xyzB1I(bdCheck, new BigDecimal(tt));
		double[][] orientation = getGroundStationOrientation(rcvPositionNEU);
		// 计算测站XYZ坐标
		double[] rcvPosition = NEU2XYZ(rcvPositionNEU);
		//新程序  计算测站XYZ坐标
		double[] azimuthElevation = getAzimuthElevation(orientation, rcvPosition, satPosition);
		return azimuthElevation;
	}

	/**
	 * 计算方位角与俯仰角
	 * @param orientation
	 * @param receiverPosition
	 * @param satellitePosition
	 * @return 0：俯仰角  1：方位角
	 */
	private static double[] getAzimuthElevation(double[][] orientation, double[] receiverPosition, double[] satellitePosition) {
		double Pi = 3.14159265358979;
		double r2d = 180 / 3.14159265358979;
		int i;
		double relativeLoS[] = new double[3];
		double distance = 0;
		for (i = 0; i < 3; i++) {
			distance += (satellitePosition[i] - receiverPosition[i]) * (satellitePosition[i] - receiverPosition[i]);
		}
		distance = Math.sqrt(distance);
		for (i = 0; i < 3; i++) {
			relativeLoS[i] = ((satellitePosition[0] - receiverPosition[0]) * orientation[i][0] + (satellitePosition[1] - receiverPosition[1]) * orientation[i][1] + (satellitePosition[2] - receiverPosition[2]) * orientation[i][2]) / distance;
		}

		double azimuth = Math.atan2(relativeLoS[1], relativeLoS[0]);
		if (azimuth < 0) {
			azimuth += 2 * Pi;
		}
		double elevation = Math.asin(relativeLoS[2]);
		double[] doubles = new double[2];

		//俯仰角会出现负数的情况，咨询曹总后，说需要取绝对值
		doubles[0] = Math.abs(elevation*r2d);
		doubles[1] = Math.abs(azimuth*r2d);
		return doubles;
	}

	/**
	 * 给定经纬度、高程计算xyz
	 * @param positionNEU
	 * @return
	 */
	public static double[] NEU2XYZ(double[] positionNEU) {
		double[] positionXYZ = new double[3];
		final double a = 6378137.0;
		//const double  a = SBAS_EARTH_RADIUS;
		final double f = 1 / 298.257223563;
		final double b = a * (1 - f);
		final double e2 = (a * a - b * b) / (a * a);
		double N;
		N = a / Math.sqrt(1 - e2 * Math.sin(positionNEU[0]) * Math.sin(positionNEU[0]));
		positionXYZ[0] = (N + positionNEU[2]) * Math.cos(positionNEU[0]) * Math.cos(positionNEU[1]);
		positionXYZ[1] = (N + positionNEU[2]) * Math.cos(positionNEU[0]) * Math.sin(positionNEU[1]);
		positionXYZ[2] = ((1 - e2) * N + positionNEU[2]) * Math.sin(positionNEU[0]);
		return positionXYZ;
	}

	private static double[][] getGroundStationOrientation(double[] positionNEU) {
		double orientation[][];
		orientation = new double[3][];
		orientation[0] = new double[3];
		orientation[1] = new double[3];
		orientation[2] = new double[3];
		// Local coordinate frame

		// North
		orientation[0][0] = -Math.sin(positionNEU[0]) * Math.cos(positionNEU[1]);
		orientation[0][1] = -Math.sin(positionNEU[0]) * Math.sin(positionNEU[1]);
		orientation[0][2] = Math.cos(positionNEU[0]);

		// East
		orientation[1][0] = -Math.sin(positionNEU[1]);
		orientation[1][1] = Math.cos(positionNEU[1]);
		orientation[1][2] = 0;

		// Up (Zenith)
		orientation[2][0] = Math.cos(positionNEU[0]) * Math.cos(positionNEU[1]);
		orientation[2][1] = Math.cos(positionNEU[0]) * Math.sin(positionNEU[1]);
		orientation[2][2] = Math.sin(positionNEU[0]);

		return orientation;

	}


	/**
	 * 计算卫星的xyz坐标计算
	 * sate 卫星数据
	 * t 定位时间
	 */
	public static double[] xyzB1I(BdmSatellite sate, BigDecimal t) {
		BigDecimal miu = new BigDecimal(398600441800000l);
		BigDecimal omegaedot = new BigDecimal(7.292115 / 100000);
		double pai = 3.1415926535898;


		//计算长半轴 A
		BigDecimal A = new BigDecimal(sate.getRootA()).multiply(new BigDecimal(sate.getRootA()));

		//计算卫星平均角速度 n0
		BigDecimal pow = A.pow(3);
		BigDecimal divide = miu.divide(pow, MathContext.DECIMAL128);
		BigDecimal n0 = sqrt(divide);

		//计算观测历元到参考历元的时间差 tk
		BigDecimal toe = new BigDecimal(sate.getToe());
		BigDecimal tk = t.subtract(toe);

		//改正平均角速度
		BigDecimal n = n0.add(new BigDecimal(sate.getDeltan()));

		//计算平近点角 Mk
		BigDecimal M0 = new BigDecimal(sate.getM0());
		BigDecimal multiply3 = n.multiply(tk);
		BigDecimal Mk = M0.add(multiply3);

		//迭代计算偏近点角 Ek
		BigDecimal e = new BigDecimal(sate.getE());
		BigDecimal ek = iteration(e, Mk);

		//计算真近点角  sinvk  cosvk
		BigDecimal one = new BigDecimal(1);
		BigDecimal subtract3 = one.subtract(e.pow(2));
		BigDecimal bigDecimal1 = sin(ek);
		BigDecimal multiply = sqrt(subtract3).multiply(bigDecimal1);

		//计算纬度幅角
		BigDecimal cos = new BigDecimal(Math.cos(ek.doubleValue()));
		BigDecimal subtract1 = cos.subtract(e);
		BigDecimal vk = new BigDecimal(Math.atan2(multiply.doubleValue(), subtract1.doubleValue()));
		BigDecimal omega = new BigDecimal(sate.getOmega());
		BigDecimal peik = vk.add(omega);

		//tmp 参数准备
		BigDecimal cus = new BigDecimal(sate.getCus());
		BigDecimal crs = new BigDecimal(sate.getCrs());
		BigDecimal cis = new BigDecimal(sate.getCis());
		BigDecimal cuc = new BigDecimal(sate.getCuc());
		BigDecimal crc = new BigDecimal(sate.getCrc());
		BigDecimal cic = new BigDecimal(sate.getCic());

		//纬度幅角改正项
		//径向改正项
		//轨道倾角改正项
		BigDecimal bigDecimal2 = new BigDecimal(2);
		BigDecimal peik2 = peik.multiply(bigDecimal2);
		BigDecimal sinpeik2 = sin(peik2);
		BigDecimal cospeik2 = cos(peik2);
		BigDecimal deltauk = cus.multiply(sinpeik2).add(cuc.multiply(cospeik2));
		BigDecimal deltark = crs.multiply(sinpeik2).add(crc.multiply(cospeik2));
		BigDecimal deltaik = cis.multiply(sinpeik2).add(cic.multiply(cospeik2));

		//计算改正后的纬度幅角
		//计算改正后的径向
		//计算改正后的轨道倾角
		BigDecimal multiply5 = e.multiply(cos);
		BigDecimal subtract2 = one.subtract(multiply5);
		BigDecimal uk = peik.add(deltauk);
		BigDecimal rk = A.multiply(subtract2).add(deltark);
		BigDecimal i0 = new BigDecimal(sate.getI0());
		BigDecimal idot = new BigDecimal(sate.getIdot());
		BigDecimal multiply6 = idot.multiply(tk);
		BigDecimal ik = i0.add(multiply6).add(deltaik);

		//计算卫星在轨道平面内的坐标
		BigDecimal cosuk = cos(uk);
		BigDecimal sinuk = sin(uk);
		BigDecimal xk = rk.multiply(cosuk);
		BigDecimal yk = rk.multiply(sinuk);

		//卫星号
		String num = sate.getNum();
		String type = SATE_TYPE.get(num);
		double[] res = new double[3];
		if("MEO".equals(type) || "IGSO".equals(type)){
			//计算历元升交点经度（地固系）
			BigDecimal omega0 = new BigDecimal(sate.getOmega0());
			BigDecimal omegadot = new BigDecimal(sate.getOmegadot());
			BigDecimal subtract4 = omegadot.subtract(omegaedot);
			BigDecimal multiply7 = subtract4.multiply(tk);
			BigDecimal multiply8 = omegaedot.multiply(toe);
			BigDecimal add = omega0.add(multiply7);
			BigDecimal omegak = add.subtract(multiply8);

			//计算MEO/IGSO卫星在BDCS坐标系中的坐标
			BigDecimal multiply9 = xk.multiply(cos(omegak));
			BigDecimal multiply10 = yk.multiply(cos(ik)).multiply(sin(omegak));
			BigDecimal Xk = multiply9.subtract(multiply10);
			BigDecimal multiply11 = xk.multiply(sin(omegak));
			BigDecimal multiply12 = yk.multiply(cos(ik)).multiply(cos(omegak));
			BigDecimal Yk = multiply11.add(multiply12);
			BigDecimal Zk = yk.multiply(sin(ik));
			res[0] = Xk.doubleValue();
			res[1] = Yk.doubleValue();
			res[2] = Zk.doubleValue();
		}else if("GEO".equals(type)){
			//计算历元升交点经度（惯性系）
			BigDecimal omega0 = new BigDecimal(sate.getOmega0());
			BigDecimal omegadot = new BigDecimal(sate.getOmegadot());
			BigDecimal multiply7 = omegadot.multiply(tk);
			BigDecimal multiply8 = omegaedot.multiply(toe);
			BigDecimal omegak = omega0.add(multiply7).subtract(multiply8);

			//计算GEO卫星在自定义坐标系中的坐标
			BigDecimal multiply9 = xk.multiply(cos(omegak));
			BigDecimal multiply10 = yk.multiply(cos(ik)).multiply(sin(omegak));
			BigDecimal XGK = multiply9.subtract(multiply10);
			BigDecimal multiply11 = xk.multiply(sin(omegak));
			BigDecimal multiply12 = yk.multiply(cos(ik)).multiply(cos(omegak));
			BigDecimal YGK = multiply11.add(multiply12);
			BigDecimal ZGK = yk.multiply(sin(ik));

			//计算GEO卫星在BDCS坐标系中的坐标
			BigDecimal tmpA = omegaedot.multiply(tk);
			// 角度
			double degrees = -5;
			// 将角度转换为弧度
			double radians = Math.toRadians(degrees);
			BigDecimal tmpB = new BigDecimal(radians);

			BigDecimal Xk = cos(tmpA).multiply(XGK).add(sin(tmpA).multiply(cos(tmpB)).multiply(YGK)).add(sin(tmpA).multiply(sin(tmpB).multiply(ZGK)));
			BigDecimal Yk = (new BigDecimal(-1).multiply(sin(tmpA))).multiply(XGK).add(cos(tmpA).multiply(cos(tmpB).multiply(YGK))).add(cos(tmpA).multiply(sin(tmpB)).multiply(ZGK));
			BigDecimal Zk = (new BigDecimal(-1).multiply(sin(tmpB)).multiply(YGK)).add(cos(tmpB).multiply(ZGK));
			res[0] = Xk.doubleValue();
			res[1] = Yk.doubleValue();
			res[2] = Zk.doubleValue();
		}

		return res;
	}

	/**
	 * 北斗坐标系转地心惯性系
	 * @param x x坐标
	 * @param y y坐标
	 * @param z z坐标
	 */
	public static double[] b1IToE(BdmSatellite sate, BigDecimal t, double x, double y, double z) {
		BigDecimal omegaedot = new BigDecimal(7.292115 / 100000);
		BigDecimal pai = new BigDecimal(3.1415926535898);
		BigDecimal A = new BigDecimal(sate.getRootA()).multiply(new BigDecimal(sate.getRootA()));
		BigDecimal toe = new BigDecimal(sate.getToe());
		BigDecimal tk = new BigDecimal(t.toString()).subtract(toe);
		String num = sate.getNum();
		String type = SATE_TYPE.get(num);
		double[] res = new double[3];

		if ("MEO".equals(type) || "IGSO".equals(type)) {
			BigDecimal omega0 = new BigDecimal(sate.getOmega0());
			BigDecimal omegadot = new BigDecimal(sate.getOmegadot());
			BigDecimal subtract4 = omegadot.subtract(omegaedot);
			BigDecimal multiply7 = subtract4.multiply(tk);
			BigDecimal multiply8 = omegaedot.multiply(toe);
			BigDecimal add = omega0.add(multiply7);
			BigDecimal omegak = add.subtract(multiply8);
			BigDecimal cos_omegak = new BigDecimal(Math.cos(omegak.doubleValue()));
			BigDecimal sin_omegak = new BigDecimal(Math.sin(omegak.doubleValue()));
			BigDecimal cos_i0 = new BigDecimal(Math.cos(new BigDecimal(sate.getI0()).doubleValue()));
			BigDecimal sin_i0 = new BigDecimal(Math.sin(new BigDecimal(sate.getI0()).doubleValue()));
			BigDecimal Xk = new BigDecimal(x).multiply(cos_omegak).subtract(new BigDecimal(y).multiply(cos_i0).multiply(sin_omegak));
			BigDecimal Yk = new BigDecimal(x).multiply(sin_omegak).add(new BigDecimal(y).multiply(cos_i0).multiply(cos_omegak));
			BigDecimal Zk = new BigDecimal(y).multiply(sin_i0);
			res[0] = Xk.doubleValue();
			res[1] = Yk.doubleValue();
			res[2] = Zk.doubleValue();
		} else if ("GEO".equals(type)) {
			BigDecimal omega0 = new BigDecimal(sate.getOmega0());
			BigDecimal omegadot = new BigDecimal(sate.getOmegadot());
			BigDecimal multiply7 = omegadot.multiply(tk);
			BigDecimal multiply8 = omegaedot.multiply(toe);
			BigDecimal omegak = omega0.add(multiply7).subtract(multiply8);
			BigDecimal cos_omegak = new BigDecimal(Math.cos(omegak.doubleValue()));
			BigDecimal sin_omegak = new BigDecimal(Math.sin(omegak.doubleValue()));
			BigDecimal cos_i0 = new BigDecimal(Math.cos(new BigDecimal(sate.getI0()).doubleValue()));
			BigDecimal sin_i0 = new BigDecimal(Math.sin(new BigDecimal(sate.getI0()).doubleValue()));
			BigDecimal XGK = new BigDecimal(x).multiply(cos_omegak).subtract(new BigDecimal(y).multiply(cos_i0).multiply(sin_omegak));
			BigDecimal YGK = new BigDecimal(x).multiply(sin_omegak).add(new BigDecimal(y).multiply(cos_i0).multiply(cos_omegak));
			BigDecimal ZGK = new BigDecimal(y).multiply(sin_i0);
			double degrees = -5;
			double radians = Math.toRadians(degrees);
			BigDecimal tmpA = omegaedot.multiply(tk);
			BigDecimal tmpB = new BigDecimal(radians);
			BigDecimal cos_tmpA = new BigDecimal(Math.cos(tmpA.doubleValue()));
			BigDecimal sin_tmpA = new BigDecimal(Math.sin(tmpA.doubleValue()));
			BigDecimal cos_tmpB = new BigDecimal(Math.cos(tmpB.doubleValue()));
			BigDecimal sin_tmpB = new BigDecimal(Math.sin(tmpB.doubleValue()));
			BigDecimal Xk = cos_tmpA.multiply(XGK).add(sin_tmpA.multiply(cos_tmpB).multiply(YGK)).add(sin_tmpA.multiply(sin_tmpB).multiply(ZGK));
			BigDecimal Yk = sin_tmpA.negate().multiply(XGK).add(cos_tmpA.multiply(cos_tmpB).multiply(YGK)).add(cos_tmpA.multiply(sin_tmpB).multiply(ZGK));
			BigDecimal Zk = sin_tmpB.negate().multiply(YGK).add(cos_tmpB.multiply(ZGK));
			res[0] = Xk.doubleValue();
			res[1] = Yk.doubleValue();
			res[2] = Zk.doubleValue();
		}
		return res;
	}



	private static BigDecimal sin(BigDecimal e) {
		return new BigDecimal(Math.sin(e.doubleValue()));
	}

	private static BigDecimal cos(BigDecimal e) {
		return new BigDecimal(Math.cos(e.doubleValue()));
	}

	private static BigDecimal iteration(BigDecimal e, BigDecimal mk) {
		BigDecimal e0 = mk;
		while (true) {
			double sin = Math.sin(e0.doubleValue());
			BigDecimal multiply = e.multiply(new BigDecimal(sin));
			BigDecimal ek = mk.add(multiply);
			e0 = ek;
			double sin2 = Math.sin(e0.doubleValue());
			BigDecimal multiply2 = e.multiply(new BigDecimal(sin));
			BigDecimal ek2 = mk.add(multiply);
			BigDecimal subtract = ek2.subtract(e0);
			if (subtract.doubleValue() < 1e-8) {
				return e0;
			}
		}
	}

	private static double iteration3(double e, double mk) {
		double e0 = mk;
		while (true) {
			double ek = mk + e * Math.sin(e0);
			e0 = ek;
			if (mk + e * Math.sin(e0) - e0 < 1e-8) {
				return e0;
			}
		}
	}

	private static BigDecimal sqrt(BigDecimal num) {
		if (num.compareTo(BigDecimal.ZERO) < 0) {
			return BigDecimal.ZERO;
		}
		BigDecimal x = num.divide(new BigDecimal("2"), MathContext.DECIMAL128);
		while (x.subtract(x = sqrtIteration(x, num)).abs().compareTo(new BigDecimal("0.0000000000000000000001")) > 0) ;
		return x;
	}

	private static BigDecimal sqrtIteration(BigDecimal x, BigDecimal n) {
		return x.add(n.divide(x, MathContext.DECIMAL128)).divide(new BigDecimal("2"), MathContext.DECIMAL128);
	}



	// 火星坐标系转WGS84坐标系的近似偏移量
	public static Map<String,Double> gcj02ToWgs84(double latitude, double longitude) {
		double dLat = transformLat(longitude - 105.0, latitude - 35.0);
		double dLon = transformLon(longitude - 105.0, latitude - 35.0);
		double radLat = latitude / 180.0 * Math.PI;
		double magic = Math.sin(radLat);
		magic = 1 - 0.00669342162296594323 * magic * magic;
		double sqrtMagic = Math.sqrt(magic);
		dLat = (dLat * 180.0) / ((6378245.0 * (1 - 0.00669342162296594323)) / (magic * sqrtMagic) * Math.PI);
		dLon = (dLon * 180.0) / (6378245.0 / sqrtMagic * Math.cos(radLat) * Math.PI);
		latitude -= dLat;
		longitude -= dLon;
		Map<String,Double> map = new HashMap<>();
		map.put("latitude", latitude);
		map.put("longitude",longitude);
		//System.out.println("WGS84 Coordinates: Lat=" + latitude + ", Lon=" + longitude);
		return map;
	}

	private static double transformLat(double x, double y) {
		double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
		ret += (20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(y * Math.PI) + 40.0 * Math.sin(y / 3.0 * Math.PI)) * 2.0 / 3.0;
		ret += (160.0 * Math.sin(y / 12.0 * Math.PI) + 320 * Math.sin(y * Math.PI / 30.0)) * 2.0 / 3.0;
		return ret;
	}

	private static double transformLon(double x, double y) {
		double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
		ret += (20.0 * Math.sin(6.0 * x * Math.PI) + 20.0 * Math.sin(2.0 * x * Math.PI)) * 2.0 / 3.0;
		ret += (20.0 * Math.sin(x * Math.PI) + 40.0 * Math.sin(x / 3.0 * Math.PI)) * 2.0 / 3.0;
		ret += (150.0 * Math.sin(x / 12.0 * Math.PI) + 300.0 * Math.sin(x / 30.0 * Math.PI)) * 2.0 / 3.0;
		return ret;
	}


	/*public static void main(String[] args) {
		// 火星坐标系坐标（示例：北京天安门）
		double gcjLat = 39.904199;
		double gcjLon = 116.407396;

		// 转换为WGS84坐标系
		gcj02ToWgs84(gcjLat, gcjLon);
	}*/

	/*
	   xyz坐标转经纬度，高程
	 */
//	public static double[] xyzToLatLonAlt(double x, double y, double z) {
//		double a = 6378137.0; // 地球长半轴
//		double f = 1 / 298.257223563; // 扁率
//		double e2 = f * (2 - f); // 第一偏心率的平方
//		//地球短半轴的长度
//		double b = a * (1 - f);
//
//		double p = Math.sqrt(x * x + y * y); // 地球椭球面上的投影距离
//		double theta = Math.atan2(z * a, p * b); // 经度
//		double lon = Math.atan2(y, x); // 经度
//		double lat = Math.atan2(z + e2 * b * Math.sin(theta) * Math.sin(theta) * Math.sin(theta), p - e2 * a * Math.cos(theta) * Math.cos(theta) * Math.cos(theta)); // 纬度
//		double N = a / Math.sqrt(1 - e2 * Math.sin(lat) * Math.sin(lat)); // 曲率半径
//		double alt = p / Math.cos(lat) - N; // 高程
//
//		double[] result = new double[3];
//		result[0] = Math.toDegrees(lat); // 纬度
//		result[1] = Math.toDegrees(lon); // 经度
//		result[2] = alt; // 高程
//		return result;
//	}

	/**
	 * xyz转经纬度高程
	 * @param x x坐标
	 * @param y y坐标
	 * @param z z坐标
	 * @return 包含纬度、经度和高程的数组
	 */
	public static double[] xyzToLatLonAlt(double x, double y, double z) {
		double a = 6378137.0; // 地球长半轴
		double f = 1 / 298.257223563; // 扁率
		double e2 = f * (2 - f); // 第一偏心率的平方
		double b = a * (1 - f); // 地球短半轴

		double p = Math.sqrt(x * x + y * y); // 地球椭球面上的投影距离
		double theta = Math.atan2(z * a, p * b); // 经度

		// 计算经度
		double lon = Math.atan2(y, x);

		// 迭代计算纬度和高程
		double lat = Math.atan(z / (p * (1 - f) * Math.sin(theta))); // 初始纬度值
		double alt = 0; // 初始化高程

		do {
			double N = a / Math.sqrt(1 - e2 * Math.sin(lat) * Math.sin(lat)); // 曲率半径
			alt = p / Math.cos(lat) - N; // 计算高程
			double latNew = Math.atan(z / p / (1 - e2 * N / (N + alt))); // 使用迭代法更新纬度
			if (Math.abs(lat - latNew) < 1e-12) {
				break; // 当纬度变化小于阈值时，退出循环
			}
			lat = latNew; // 更新纬度
		} while (true);

		double[] result = new double[3];
		result[0] = Math.toDegrees(lat); // 纬度
		result[1] = Math.toDegrees(lon); // 经度
		result[2] = alt; // 高程（使用最后一次迭代的高程值）
		return result;
	}

	/*public static void main(String[] args) {

		double a1 = 1;
		double b1 = 359;

		double a2 = 359;
		double b2 = 5;

		double limit = 3;

		boolean a2Flag = true;
		if(a1 >= a2){
			boolean f1 = true;
			if(a1 - a2 > limit){
				f1 = false;
			}
			boolean f2 = true;
			if(a2 + 360 - a1 > limit){
				f2 = false;
			}
				a2Flag = f1 | f2;
		}else{
			boolean f1 = true;
			if(a2 - a1 > limit){
				f1 = false;
			}
			boolean f2 = true;
			if(a1 + 360 - a2 > limit){
				f2 = false;
			}
			a2Flag = f1 | f2;
		}
		boolean b2Flag = true;
		if(b1 >= b2){
			boolean f1 = true;
			if(b1 - b2 > limit){
				f1 = false;
			}
			boolean f2 = true;
			if(b2 + 360 - b1 > limit){
				f2 = false;
			}
			b2Flag = f1 | f2;
		}else{
			boolean f1 = true;
			if(b2 - b1 > limit){
				f1 = false;
			}
			boolean f2 = true;
			if(b1 + 360 - b2 > limit){
				f2 = false;
			}
			b2Flag = f1 | f2;
		}

		if( !(a2Flag && b2Flag) ){
			log.info("北斗识别不通过：deviceNo={}, deviceNum={}, 卫星id={}, 计算值 elevation={} azimuth={}，上传值 elevation={} azimuth={}");
		}else{
			log.info("验证通过");
		}
	}*/

}
