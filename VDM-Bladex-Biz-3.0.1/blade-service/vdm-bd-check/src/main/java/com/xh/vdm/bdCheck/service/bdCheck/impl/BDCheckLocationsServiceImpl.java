package com.xh.vdm.bdCheck.service.bdCheck.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bdCheck.entity.bdCheck.BDCheckLocations;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;
import com.xh.vdm.bdCheck.mapper.bdCheck.BdcTerminalMapper;
import com.xh.vdm.bdCheck.mapper.impala.BDCheckLocationsMapper;
import com.xh.vdm.bdCheck.service.bdCheck.IBDCheckLocationsService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcTerminalService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 北斗识别检测
 * 查询impala
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Service
public class BDCheckLocationsServiceImpl extends ServiceImpl<BDCheckLocationsMapper, BDCheckLocations> implements IBDCheckLocationsService {

	@Override
	public List<String> findCheckDateList(String terminalNo) throws Exception {
		return baseMapper.getCheckDateList(terminalNo);
	}
}
