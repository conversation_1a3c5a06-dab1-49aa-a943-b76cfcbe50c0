package com.xh.vdm.bdCheck.vo.coding.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.bdCheck.vo.coding.request.group.SyncFromMachineGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@ApiModel(value = "请求体：来自赋码机的赋码结果同步至平台")
@Data
public class SyncFromMachineRequest {

	@JsonProperty("list")
	@ApiModelProperty(name = "list", value = "结果列表", example = "[{}, ...]", required = true)
	@NotEmpty(message = "结果列表为空。", groups = {SyncFromMachineGroup.class})
	private List<ProcessRequest> list;
}
