package com.xh.vdm.bdCheck.checkCore;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.MathContext;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class BDCheckService {


    /*public static void main(String[] args) throws IOException {
        Map<String, BDCheck> readxingli = readxingli("C:/tmp/bd/tarc3660.20b_cnav");
        double d2r = 3.14159265358979 / 180;
        double r2d = 180 / 3.14159265358979;
        File f1 = new File("C:/tmp/bd/计算数据结果.txt");
        FileOutputStream fos = new FileOutputStream(f1);
        if (f1.exists() == false) {
            f1.getParentFile().mkdirs();
        }

        int num = 0;
        //读文本
        System.out.println("  文件开始读取==========");
        File file = new File("C:/tmp/bd/计算数据.txt");
        LineIterator it = FileUtils.lineIterator(file, "UTF-8");
        while (it.hasNext()) {
            num++;
            String ss = "";
            String line  = it.nextLine();
            String[] split = line.split("\\$");
            // 输入角度转化为弧度，纬度
            double[] rcvPositionNEU = new double[3];
            rcvPositionNEU[0] = Double.parseDouble(split[4])  * d2r;// 输入角度转化为弧度，纬度
            rcvPositionNEU[1] =  Double.parseDouble(split[3])* d2r;// 经度
            rcvPositionNEU[2] = Integer.parseInt(split[5]);// 高程
            LocalDateTime dateTime = LocalDateTime.ofEpochSecond(Integer.parseInt(split[2]), 0, ZoneOffset.UTC);//定位时间
//            计算在本周第几天
            int tt = calculationTT(dateTime);
            // for 循环
            String result = "";
            String result2 = "";
            int effective = 0;
            int invalid = 0;
            String[] bdDetail = split[1].split(";");
            for (int i = 0; i < bdDetail.length; i++) {
                String[] split1 = bdDetail[i].split(",");
                if(Integer.parseInt(split1[0]) >=19  && Integer.parseInt(split1[0]) !=31){
                    String key = calculationKey(dateTime,Integer.parseInt(split1[0]));
                    BDCheck bdCheck = readxingli.get(key);
                    if(bdCheck == null ){
                        System.out.println(bdCheck);
                    }
//                    计算测站XYZ坐标
                    double[] calculation = calculation(bdCheck, tt, rcvPositionNEU);
                    result = Integer.parseInt(split1[0]) + ","+calculation[0]+ ","+calculation[1]+ ";";
                    result2+=result;
                    if(Double.parseDouble(split1[1]) - calculation[0] >3 || Double.parseDouble(split1[2]) - calculation[1] >3){
                        invalid++;
                    }else {
                        effective++;
                    }
                }
            }
            fos.write((line + "$" + result2+ "$" + effective + "$" + invalid ).getBytes());
            fos.write("\r\n".getBytes());// 写入一个换行
            if(num%1000 == 0){
                System.out.println(num);
            }
        }
    }*/

    private static int calculationTT(LocalDateTime dateTime) {
        // 计算在本周第几天
        int dayOfWeek = dateTime.getDayOfWeek().getValue();
        int hour1 = dateTime.getHour();
        int minute = dateTime.getMinute();
        int second = dateTime.getSecond();
        int tt = dayOfWeek * 24 * 60 * 60 + hour1 * 60 * 60 + minute * 60 + second;
        return tt;
    }
    private static String calculationKey(LocalDateTime dateTime ,int num ) {
        // 计算在本周第几天
        int year = dateTime.getYear();
        int month = dateTime.getMonth().getValue();
        int day = dateTime.getDayOfMonth();
        int hour = dateTime.getHour();
        // 卫星XYZ坐标
        String mon = month + "";
        if (month <= 9) {
            mon = "0" + mon;
        }
        String dd = day + "";
        if (day <= 9) {
            dd = "0" + dd;
        }
        String ho = hour + "";
        if (hour <= 9) {
            ho = "0" + ho;
        }
        String str = "C" +num+ year + mon + dd + ho;
        return str;
    }


    static double[]  calculation(BDCheck bdCheck,int tt ,double[] rcvPositionNEU){
        double d2r = 3.14159265358979 / 180;
        double r2d = 180 / 3.14159265358979;
        double[] satPosition = xyz(bdCheck, new BigDecimal(tt));
        double[][] orientation = getGroundStationOrientation(rcvPositionNEU);
        // 计算测站XYZ坐标
        double[] rcvPosition = NEU2XYZ(rcvPositionNEU);
        //新程序  计算测站XYZ坐标
        double[] azimuthElevation = getAzimuthElevation(orientation, rcvPosition, satPosition);
//        System.out.println(azimuthElevation[0]*r2d); //俯仰角
//        System.out.println(azimuthElevation[1]*r2d); //方位角
        return azimuthElevation;
    }




    static double[] BLH_TO_LXYZ(double BLHO[], double XYZ[]) {
        double LXYZ[] = new double[3];
        double XYZO[] = new double[3];
        double H[][];

        H = new double[3][];
        H[0] = new double[3];
        H[1] = new double[3];
        H[2] = new double[3];


        double dX = 0.0;
        double dY = 0.0;
        double dZ = 0.0;
        double B_U = 0.0;
        double L_U = 0.0;
//	double H_U = 0.0;

        int i;

        XYZO = BLH_TO_XYZ(BLHO);

        dX = XYZ[0] - XYZO[0];
        dY = XYZ[1] - XYZO[1];
        dZ = XYZ[2] - XYZO[2];

        B_U = BLHO[0];
        L_U = BLHO[1];
//	H_U = BLHO[2];
        H[0][0] = -Math.sin(B_U) * Math.cos(L_U);
        H[0][1] = -Math.sin(B_U) * Math.sin(L_U);
        H[0][2] = Math.cos(B_U);
        H[1][0] = -Math.sin(L_U);
        H[1][1] = Math.cos(L_U);
        H[1][2] = 0;
        H[2][0] = Math.cos(B_U) * Math.cos(L_U);
        H[2][1] = Math.cos(B_U) * Math.sin(L_U);
        H[2][2] = Math.sin(B_U);
        for (i = 0; i < 3; i++) {
            LXYZ[i] = H[i][0] * dX + H[i][1] * dY + H[i][2] * dZ;
        }
        return LXYZ;
    }

    static double[] BLH_TO_XYZ(double dBLH[]) {
        double[] dXYZ = new double[3];
        double DUTOPI = 3.14159265358979 / 180;
        double AE = 6378137.0;
        double f = 1 / 298.257223563;
        double b = AE * (1 - f);
        double e2 = (AE * AE - b * b) / (AE * AE);

        double N = 0.0;
        double B = 0.0;
        double L = 0.0;
        double H = 0.0;

//角度转换为弧度
        B = dBLH[0];
        L = dBLH[1];
        H = dBLH[2];
//待求点卯酉圈曲率半径
        N = AE / Math.sqrt(1.0 - e2 * Math.sin(B) * Math.sin(B));
//转换大地坐标到直角坐标
        dXYZ[0] = (N + H) * Math.cos(B) * Math.cos(L);
        dXYZ[1] = (N + H) * Math.cos(B) * Math.sin(L);
        dXYZ[2] = (N * (1.0 - e2) + H) * Math.sin(B);
        return dXYZ;
    }


    private static double[] getAzimuthElevation(double[][] orientation, double[] receiverPosition, double[] satellitePosition) {
        double Pi = 3.14159265358979;
        double r2d = 180 / 3.14159265358979;
        int i;
        double relativeLoS[] = new double[3];
        double distance = 0;
        for (i = 0; i < 3; i++) {
            distance += (satellitePosition[i] - receiverPosition[i]) * (satellitePosition[i] - receiverPosition[i]);
        }
        distance = Math.sqrt(distance);
        for (i = 0; i < 3; i++) {
            relativeLoS[i] = ((satellitePosition[0] - receiverPosition[0]) * orientation[i][0] + (satellitePosition[1] - receiverPosition[1]) * orientation[i][1] + (satellitePosition[2] - receiverPosition[2]) * orientation[i][2]) / distance;
        }

        double azimuth = Math.atan2(relativeLoS[1], relativeLoS[0]);
        if (azimuth < 0) {
            azimuth += 2 * Pi;
        }
        double elevation = Math.asin(relativeLoS[2]);
        double[] doubles = new double[2];

        doubles[0] = elevation*r2d;
        doubles[1] = azimuth*r2d;
        return doubles;
    }

    public static double[] NEU2XYZ(double[] positionNEU) {
        double[] positionXYZ = new double[3];
        final double a = 6378137.0;
        //const double  a = SBAS_EARTH_RADIUS;
        final double f = 1 / 298.257223563;
        final double b = a * (1 - f);
        final double e2 = (a * a - b * b) / (a * a);
        double N;
        N = a / Math.sqrt(1 - e2 * Math.sin(positionNEU[0]) * Math.sin(positionNEU[0]));
        positionXYZ[0] = (N + positionNEU[2]) * Math.cos(positionNEU[0]) * Math.cos(positionNEU[1]);
        positionXYZ[1] = (N + positionNEU[2]) * Math.cos(positionNEU[0]) * Math.sin(positionNEU[1]);
        positionXYZ[2] = ((1 - e2) * N + positionNEU[2]) * Math.sin(positionNEU[0]);
        return positionXYZ;
    }

    public static double[][] getGroundStationOrientation(double[] positionNEU) {
        double orientation[][];
        orientation = new double[3][];
        orientation[0] = new double[3];
        orientation[1] = new double[3];
        orientation[2] = new double[3];
        // Local coordinate frame

        // North
        orientation[0][0] = -Math.sin(positionNEU[0]) * Math.cos(positionNEU[1]);
        orientation[0][1] = -Math.sin(positionNEU[0]) * Math.sin(positionNEU[1]);
        orientation[0][2] = Math.cos(positionNEU[0]);

        // East
        orientation[1][0] = -Math.sin(positionNEU[1]);
        orientation[1][1] = Math.cos(positionNEU[1]);
        orientation[1][2] = 0;

        // Up (Zenith)
        orientation[2][0] = Math.cos(positionNEU[0]) * Math.cos(positionNEU[1]);
        orientation[2][1] = Math.cos(positionNEU[0]) * Math.sin(positionNEU[1]);
        orientation[2][2] = Math.sin(positionNEU[0]);

        return orientation;

    }

    ;

    //计算当前周和周内秒
    public static TTime ymd2bdt(LocalDateTime dateTime) {
        TTime tTime = new TTime();
        double djul;
        int mjd;
        int year, month;
        int I, K;
        year = dateTime.getYear();
        month = dateTime.getMonth().getValue() - 1;
        if (month <= 2) {
            year = year - 1;
            month = month - 1;
        }
        I = year / 100;
        K = 2 - I + I / 4;
        djul = (365.25) * year - 365.25 * year % 1.0 - 679006.0;
        djul = djul + Math.floor(30.6001 * (month + 1)) + dateTime.getDayOfMonth() + K;
        mjd = (int) Math.floor(djul);
        tTime.setWeek((mjd - 53736) / 7);
        tTime.setTow(((mjd - 53736) % 7) * 86400 + dateTime.getHour() * 3600 + dateTime.getMinute() * 60 + dateTime.getSecond());
        return tTime;
    }

    public static BigDecimal unixtomonse(BigDecimal t) {
        int i = t.intValue();
        boolean flag = true;
        while (flag) {
            if (i > 604800) {
                i = i - 604800;
            } else {
                flag = false;
            }
        }
        return new BigDecimal(i);

    }


    //  xyz坐标计算
    public static double[] xyz2(BDCheck bdCheck, BigDecimal t) {
        BigDecimal miu = new BigDecimal(398600441800000l);
        BigDecimal omegaedot = new BigDecimal(7.292115 / 100000);
        double pai = 3.1415926535898;
        BigDecimal aref = new BigDecimal(0);
//        BigDecimal aref = new BigDecimal(42162200);
        Map<String, String> map = new HashMap<>();
        map.put("31", "31");
        map.put("38", "38");
        map.put("39", "39");
        map.put("40", "40");
        map.put("56", "56");
        map.put("59", "59");
        map.put("60", "60");
        map.put("61", "61");
        BigDecimal num = new BigDecimal(bdCheck.getNum());
        if (map.get(num.stripTrailingZeros().toPlainString()) != null) {
            aref = new BigDecimal(42162200);
        } else {
            aref = new BigDecimal(27906100);
        }
        //1608462063

        BigDecimal toe = new BigDecimal(bdCheck.getToe());
        //计算tk
        BigDecimal tk2 = t.subtract(toe);
        BigDecimal can1 = new BigDecimal(302400);
        BigDecimal can2 = new BigDecimal(-302400);
        BigDecimal can3 = new BigDecimal(604800);
        if (tk2.compareTo(can1) == 1) {
            tk2 = tk2.subtract(can3);
        } else if (tk2.compareTo(can2) == -1) {
            tk2 = tk2.add(can3);
        }

        // 计算tk
        double tk = tk2.doubleValue();
        //计算a0
        double deltaa = Double.parseDouble(bdCheck.getDeltaa());
        double a0 = aref.doubleValue() + deltaa;
        //计算ak
        double adot = Double.parseDouble(bdCheck.getAdot());
        double ak = a0 + adot * tk;
        //计算n0
        double n0 = Math.sqrt(miu.doubleValue() / Math.pow(a0, 3));
        //计算deltana
        double deltan0 = Double.parseDouble(bdCheck.getDeltan0());
        double deltan0dot = Double.parseDouble(bdCheck.getDeltan0dot());
        double deltana = deltan0 + 1 / 2 * deltan0dot * tk;
        //计算na
        double na = n0 + deltana;
        //计算mk
        double m0 = Double.parseDouble(bdCheck.getM0());
        double mk = m0 + na * tk;
        //计算ek
        double e = Double.parseDouble(bdCheck.getE());
        double ek = iteration3(e, mk);
        //计算vk
        double sinvk = (Math.sqrt(1 - Math.pow(e, 2)) * Math.sin(ek)) / (1 - e * Math.cos(ek));
        double vk1 = Math.asin(sinvk);
        double cosvk = (Math.cos(ek) - e) / (1 - e * Math.cos(ek));
        double vk2 = Math.acos(cosvk);

        double vk3 = Math.atan2((Math.sqrt(1 - Math.pow(e, 2)) * Math.sin(ek)), (Math.cos(ek) - e));

        double[] bb = new double[3];
//        bb[0] = Xk.doubleValue();
//        bb[1] = Yk.doubleValue();
//        bb[2] = Zk.doubleValue();
//        System.out.println("Xk=" + Xk + ",Yk=" + Yk + ",Zk=" + Zk);
        return bb;
    }


    //  xyz坐标计算
    public static double[] xyz(BDCheck bdCheck, BigDecimal t) {
        BigDecimal miu = new BigDecimal(398600441800000l);
        BigDecimal omegaedot = new BigDecimal(7.292115 / 100000);
        double pai = 3.1415926535898;
        BigDecimal aref = new BigDecimal(0);
//        BigDecimal aref = new BigDecimal(42162200);
        Map<String, String> map = new HashMap<>();
        map.put("31", "31");
        map.put("38", "38");
        map.put("39", "39");
        map.put("40", "40");
        map.put("56", "56");
        map.put("59", "59");
        map.put("60", "60");
        map.put("61", "61");
        BigDecimal num = new BigDecimal(bdCheck.getNum());
        if (map.get(num.stripTrailingZeros().toPlainString()) != null) {
            aref = new BigDecimal(42162200);
        } else {
            aref = new BigDecimal(27906100);
        }
        //1608462063
//        System.out.println("miu=" + miu);
//        System.out.println("omegaedot=" + omegaedot);
//        System.out.println("aref=" + aref);

        BigDecimal toe = new BigDecimal(bdCheck.getToe());
//        System.out.println("toe=" + toe);
        //计算tk
        BigDecimal tk = t.subtract(toe);

        BigDecimal can1 = new BigDecimal(302400);
        BigDecimal can2 = new BigDecimal(-302400);
        BigDecimal can3 = new BigDecimal(604800);
        if (tk.compareTo(can1) == 1) {
            tk = tk.subtract(can3);
        } else if (tk.compareTo(can2) == -1) {
            tk = tk.add(can3);
        }
//        System.out.println("tk=" + tk);
        //计算a0
        BigDecimal deltaa = new BigDecimal(bdCheck.getDeltaa());
        BigDecimal a0 = aref.add(deltaa);
//        System.out.println("deltaa=" + deltaa);
//        System.out.println("a0=" + a0);
        //计算ak
        BigDecimal adot = new BigDecimal(bdCheck.getAdot());
        BigDecimal multiply1 = adot.multiply(tk);
        BigDecimal ak = a0.add(multiply1);
//        System.out.println("adot=" + adot);
//        System.out.println("ak=" + ak);
        //计算n0
        BigDecimal pow = a0.pow(3);
        BigDecimal divide = miu.divide(pow, MathContext.DECIMAL128);
        BigDecimal n0 = sqrt(divide);
//        System.out.println("n0=" + n0);
        //计算 deltana
        BigDecimal deltan0dot = new BigDecimal(bdCheck.getDeltan0dot());
        BigDecimal deltan0 = new BigDecimal(bdCheck.getDeltan0());
//        System.out.println("deltan0dot=" + deltan0dot);
//        System.out.println("deltan0=" + deltan0);
        BigDecimal cc = new BigDecimal(0.5);
        BigDecimal multiply2 = cc.multiply(deltan0dot).multiply(tk);
        BigDecimal deltana = deltan0.add(multiply2);
//        System.out.println("deltana=" + deltana);
        //计算na
        BigDecimal na = n0.add(deltana);
//        System.out.println("na=" + na);
        //计算mk
        BigDecimal m0 = new BigDecimal(bdCheck.getM0());
        BigDecimal multiply3 = na.multiply(tk);
        BigDecimal mk = m0.add(multiply3);
//        System.out.println("m0=" + m0);
//        System.out.println("mk=" + mk);
//        if(mk.doubleValue()<0){
//            mk = mk.add(new BigDecimal(360));
//        }
        //迭代计算ek
        BigDecimal e = new BigDecimal(bdCheck.getE());
        BigDecimal ek = iteration(e, mk);
//        System.out.println("ek=" + ek);
        //计算真近点角  sinvk  cosvk
        BigDecimal one = new BigDecimal(1);
        BigDecimal subtract3 = one.subtract(e.pow(2));
        BigDecimal bigDecimal1 = sin(ek);
        BigDecimal multiply = sqrt(subtract3).multiply(bigDecimal1);


        BigDecimal cos = new BigDecimal(Math.cos(ek.doubleValue()));
        BigDecimal subtract1 = cos.subtract(e);

        BigDecimal multiply5 = e.multiply(cos);
        BigDecimal subtract2 = one.subtract(multiply5);
        BigDecimal cosvk = subtract1.divide(subtract2, MathContext.DECIMAL128);
        BigDecimal vk2 = new BigDecimal(Math.acos(cosvk.doubleValue()));
        BigDecimal divide1 = multiply.divide(subtract2, MathContext.DECIMAL128);
        BigDecimal vk3 = new BigDecimal(Math.asin(divide1.doubleValue()));

        BigDecimal vk = new BigDecimal(Math.atan2(multiply.doubleValue(), subtract1.doubleValue()));

//        System.out.println("vk=" + vk);
        BigDecimal omega = new BigDecimal(bdCheck.getOmega());
        BigDecimal cus = new BigDecimal(bdCheck.getCus());
        BigDecimal crs = new BigDecimal(bdCheck.getCrs());
        BigDecimal cis = new BigDecimal(bdCheck.getCis());
        BigDecimal cuc = new BigDecimal(bdCheck.getCuc());
        BigDecimal crc = new BigDecimal(bdCheck.getCrc());
        BigDecimal cic = new BigDecimal(bdCheck.getCic());
//        System.out.println("omega=" + omega);
//        System.out.println("cus=" + cus);
//        System.out.println("crs=" + crs);
//        System.out.println("cis=" + cis);
//        System.out.println("cuc=" + cuc);
//        System.out.println("crc=" + crc);
//        System.out.println("cic=" + cic);
        BigDecimal peik = vk.add(omega);
        //   计算纬度幅角改正项  计算径向距离改正项  计算轨道倾角改正项
        BigDecimal bigDecimal2 = new BigDecimal(2);
        BigDecimal peik2 = peik.multiply(bigDecimal2);
//        double sinpeik = Math.sin(peik2.doubleValue());
//        double cospeik = Math.cos(peik2.doubleValue());
        BigDecimal sinpeik2 = sin(peik2);
        BigDecimal cospeik2 = cos(peik2);
        BigDecimal deltauk = cus.multiply(sinpeik2).add(cuc.multiply(cospeik2));
        BigDecimal deltark = crs.multiply(sinpeik2).add(crc.multiply(cospeik2));
        BigDecimal deltaik = cis.multiply(sinpeik2).add(cic.multiply(cospeik2));
//        System.out.println("deltauk=" + deltauk);
//        System.out.println("deltark=" + deltark);
//        System.out.println("deltaik=" + deltaik);
        //  计算改正后纬度幅角  计算改正后径向距离  计算改正后轨道倾角
        BigDecimal uk = peik.add(deltauk);
        BigDecimal rk = ak.multiply(subtract2).add(deltark);
        BigDecimal i0 = new BigDecimal(bdCheck.getI0());
        BigDecimal idot = new BigDecimal(bdCheck.getIdot());
        BigDecimal multiply6 = idot.multiply(tk);
        BigDecimal ik = i0.add(multiply6).add(deltaik);
//        System.out.println("uk=" + uk);
//        System.out.println("rk=" + rk);
//        System.out.println("i0=" + i0);
//        System.out.println("idot=" + idot);
//        System.out.println("ik=" + ik);
        //计算卫星在轨道平面内的坐标
        BigDecimal cosuk = cos(uk);
        BigDecimal sinuk = sin(uk);
        BigDecimal xk = rk.multiply(cosuk);
        BigDecimal yk = rk.multiply(sinuk);
//        System.out.println("xk=" + xk);
//        System.out.println("yk=" + yk);
        //计算改正后的升交点经度
        BigDecimal omega0 = new BigDecimal(bdCheck.getOmega0());
        BigDecimal omegadot = new BigDecimal(bdCheck.getOmegadot());
        BigDecimal subtract4 = omegadot.subtract(omegaedot);
        BigDecimal multiply7 = subtract4.multiply(tk);
        BigDecimal multiply8 = omegaedot.multiply(toe);
        BigDecimal add = omega0.add(multiply7);
        BigDecimal omegak = add.subtract(multiply8);
//        System.out.println("omega0=" + omega0);
//        System.out.println("omegadot=" + omegadot);
//        System.out.println("omegak=" + omegak);
        //计算MEO/IGSO卫星在BDCS坐标系中的坐标
        BigDecimal multiply9 = xk.multiply(cos(omegak));
        BigDecimal multiply10 = yk.multiply(cos(ik)).multiply(sin(omegak));
        BigDecimal Xk = multiply9.subtract(multiply10);
        BigDecimal multiply11 = xk.multiply(sin(omegak));
        BigDecimal multiply12 = yk.multiply(cos(ik)).multiply(cos(omegak));
        BigDecimal Yk = multiply11.add(multiply12);
        BigDecimal Zk = yk.multiply(sin(ik));
//        System.out.println("Xk=" + Xk);
//        System.out.println("Yk=" + Yk);
//        System.out.println("Zk=" + Zk);
        double[] bb = new double[3];
        bb[0] = Xk.doubleValue();
        bb[1] = Yk.doubleValue();
        bb[2] = Zk.doubleValue();
//        System.out.println("Xk=" + Xk + ",Yk=" + Yk + ",Zk=" + Zk);
        return bb;
    }

    private static BigDecimal sin(BigDecimal e) {
        return new BigDecimal(Math.sin(e.doubleValue()));
    }

    private static BigDecimal cos(BigDecimal e) {
        return new BigDecimal(Math.cos(e.doubleValue()));
    }

    private static BigDecimal iteration(BigDecimal e, BigDecimal mk) {
        BigDecimal e0 = mk;
        while (true) {
            double sin = Math.sin(e0.doubleValue());
            BigDecimal multiply = e.multiply(new BigDecimal(sin));
            BigDecimal ek = mk.add(multiply);
            e0 = ek;
            double sin2 = Math.sin(e0.doubleValue());
            BigDecimal multiply2 = e.multiply(new BigDecimal(sin));
            BigDecimal ek2 = mk.add(multiply);
            BigDecimal subtract = ek2.subtract(e0);
            if (subtract.doubleValue() < 1e-8) {
                return e0;
            }
        }
    }

    private static double iteration3(double e, double mk) {
        double e0 = mk;
        while (true) {
            double ek = mk + e * Math.sin(e0);
            e0 = ek;
            if (mk + e * Math.sin(e0) - e0 < 1e-8) {
                return e0;
            }
        }
    }

    private static BigDecimal iteration2(BigDecimal e, BigDecimal mk) {
        BigDecimal ek = mk;
        for (int i = 0; i < 3; i++) {
            double sin = Math.sin(ek.doubleValue());
            BigDecimal bigDecimal = new BigDecimal(sin);
            BigDecimal multiply = e.multiply(bigDecimal);
            ek = mk.add(multiply);
        }
        return ek;
    }

    public static BigDecimal sqrt(BigDecimal num) {
        if (num.compareTo(BigDecimal.ZERO) < 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal x = num.divide(new BigDecimal("2"), MathContext.DECIMAL128);
        while (x.subtract(x = sqrtIteration(x, num)).abs().compareTo(new BigDecimal("0.0000000000000000000001")) > 0) ;
        return x;
    }

    private static BigDecimal sqrtIteration(BigDecimal x, BigDecimal n) {
        return x.add(n.divide(x, MathContext.DECIMAL128)).divide(new BigDecimal("2"), MathContext.DECIMAL128);
    }


    /**
     * <AUTHOR>
     * @Version 1.0
     * @comp cttic
     * @Exception
     * @Date 2021/8/11 14:25
     * 读取星历文件
     */
    public static HashMap<String, BDCheck> readxingli(String filename) {
        System.out.println("---------------//////");
        String line = "";
        JSONObject jsonObject = null;
        try {
            System.out.println("  文件开始读取==========");
            File file = new File(filename);
            LineIterator it = null;

            it = FileUtils.lineIterator(file, "UTF-8");

            HashMap<String, BDCheck> stringListHashMap = new HashMap<String, BDCheck>();
            int i = 1;
            //备注：
            //*）为了适用不同的编译器，浮点数指数和尾数之间可以使用字母 E，e，D。但占两位的指数是必
            //须满足的；
            //**）信息发射时间需根据 BROADCAST ORBIT – 5 中的周数，加、减 604800。若此项未知，则置为
            //0.9999E9；
            //***) 以 semi-circles 和 semi-circles/sec 为单位的角及其倒数需转化为 radians；
            //****) GAL week 是连续的数，GALweek=GSTweek+1024+n*4096；
            //*****) 如果 bit0 或 bit2 被置“1”，则 E1B DVS&HS, E5b DVS& HS 和 BGDs 均有效，
            // 如果 bit1 被置“1”，则 E5a DVS&HS 和 BGD E5a/E1 有效；
            // 非有效参数置“0”或忽略。
            //******）对于 BDS 卫星，Aref=27906100m（MEO），Aref=42162200m（IGSO/GEO）。
            BDCheck bdCheck = new BDCheck();
            while (it.hasNext()) {

//                C19 2020 12 31 00 00 00 0.673263915814E-03 0.136557432029E-10 0.000000000000E+00
//                0.120000000000E+02-0.325976562500E+02 0.331388803674E-08 0.244359783722E+01
//               -0.168941915035E-05 0.588934170082E-03 0.115428119898E-04 0.774570312500E+02
//                0.345600000000E+06-0.124797224999E-06 0.587511217800E+00-0.679865479469E-07
//                0.965160676782E+00 0.129816406250E+03-0.119152079341E+01-0.632062042207E-08
//                0.584488632012E-09 0.100000000000E+01 0.782000000000E+03-0.826692581177E-02
//                0.190794956800E+10 0.000000000000E+00 0.116415321827E-07-0.465661287308E-09
//                0.345726000000E+06 0.120000000000E+02 0.786732338082E-13 0.300000000000E+01
                line = it.nextLine();

                if (i % 8 == 1) {
                    String seconds = "";
                    String sec = "";
                    String sec2 = "";
                    String flag = "";
                    //卫星标志
                    String flag2 = line.substring(0, 1);
                    bdCheck.setFlag(flag2);
                    //卫星号
                    String num = line.substring(1, 3);
                    bdCheck.setNum(num);
                    // 年
                    String year = line.substring(4, 8);
                    bdCheck.setYear(year);
                    // 月
                    String month = line.substring(9, 11);
                    bdCheck.setMon(month);
                    // 日
                    String day = line.substring(12, 14);
                    bdCheck.setDay(day);
                    // 时
                    String hour = line.substring(15, 17);
                    bdCheck.setHour(hour);
                    // 分
                    String minute = line.substring(18, 20);
                    bdCheck.setMin(minute);
                    // 秒
                    String se = line.substring(21, 23);
                    bdCheck.setSec(se);
                    // a0 卫星钟偏系数(seconds)
                    flag = line.substring(23, 24);
                    if (flag.equals("-")) {
                        seconds = line.substring(23, 42);
                    } else {
                        seconds = line.substring(24, 42);
                    }
                    bdCheck.setA0(seconds);
                    // a1 卫星钟漂系数(sec/sec)
                    flag = line.substring(42, 43);
                    if (flag.equals("-")) {
                        sec = line.substring(42, 61);
                    } else {
                        sec = line.substring(43, 61);
                    }
                    bdCheck.setA1(sec);
                    // a2 卫星钟漂移率系数 (sec/sec2)
                    flag = line.substring(61, 62);
                    if (flag.equals("-")) {
                        sec2 = line.substring(61);
                    } else {
                        sec2 = line.substring(62);
                    }
                    bdCheck.setA2(sec2);
                } else if (i % 8 == 2) {
                    String substring = line.substring(4, 5);
                    // -IODE/AODE 星历参数版本号/星历数据龄期 IODE: 对应 B1C、B2a 的导航电文 AODE: 对应 B1I、B2I、B3I 的导航电文
                    //-Crs 轨道半径的正弦调和改正项的振幅 (meters)
                    //-Delta n0 参考时刻卫星平均角速度与计算值之差 (radians/sec)
                    //-M0 参考时刻的平近点角 (radians)
                    String two_one = "";
                    String two_two = "";
                    String two_the = "";
                    String two_fou = "";
                    if (substring.equals("-")) {
                        two_one = line.substring(4, 23);
                    } else {
                        two_one = line.substring(5, 23);
                    }
                    substring = line.substring(23, 24);
                    if (substring.equals("-")) {
                        two_two = line.substring(23, 42);
                    } else {
                        two_two = line.substring(24, 42);
                    }
                    substring = line.substring(42, 43);
                    if (substring.equals("-")) {
                        two_the = line.substring(42, 61);
                    } else {
                        two_the = line.substring(43, 61);
                    }
                    substring = line.substring(61, 62);
                    if (substring.equals("-")) {
                        two_fou = line.substring(61);
                    } else {
                        two_fou = line.substring(62);
                    }
                    bdCheck.setIode(two_one);
                    bdCheck.setCrs(two_two);
                    bdCheck.setDeltan0(two_the);
                    bdCheck.setM0(two_fou);
                } else if (i % 8 == 3) {
                    String substring = line.substring(4, 5);
                    //-Cuc 纬度幅角的余弦调和改正项的振幅(radians)
                    //-e 偏心率
                    //-Cus 纬度幅角的正弦调和改正项的振幅(radians)
                    //-ΔA/sqrt(A) 参考时刻长半轴相对于参考值的偏差/长半轴的平方根   (meters)/sqrt(m)) ΔA: 对应 B1C、B2a 的导航电文 sqrt(A): 对应 B1I、B2I、B3I 的导航电文
                    String two_one = "";
                    String two_two = "";
                    String two_the = "";
                    String two_fou = "";
                    if (substring.equals("-")) {
                        two_one = line.substring(4, 23);
                    } else {
                        two_one = line.substring(5, 23);
                    }
                    substring = line.substring(23, 24);
                    if (substring.equals("-")) {
                        two_two = line.substring(23, 42);
                    } else {
                        two_two = line.substring(24, 42);
                    }
                    substring = line.substring(42, 43);
                    if (substring.equals("-")) {
                        two_the = line.substring(42, 61);
                    } else {
                        two_the = line.substring(43, 61);
                    }
                    substring = line.substring(61, 62);
                    if (substring.equals("-")) {
                        two_fou = line.substring(61);
                    } else {
                        two_fou = line.substring(62);
                    }
                    bdCheck.setCuc(two_one);
                    bdCheck.setE(two_two);
                    bdCheck.setCus(two_the);
                    bdCheck.setDeltaa(two_fou);
                } else if (i % 8 == 4) {
                    String substring = line.substring(4, 5);
                    //-Toe 星历参考时刻 (sec,BDT)
                    //-Cic 轨道倾角的余弦调和改正项的振幅(radians)
                    //-OMEGA0 周历元零时刻计算的升交点经度(radians)
                    //-Cis 轨道倾角的正弦调和改正项的振幅(radians)
                    String two_one = "";
                    String two_two = "";
                    String two_the = "";
                    String two_fou = "";
                    if (substring.equals("-")) {
                        two_one = line.substring(4, 23);
                    } else {
                        two_one = line.substring(5, 23);
                    }
                    substring = line.substring(23, 24);
                    if (substring.equals("-")) {
                        two_two = line.substring(23, 42);
                    } else {
                        two_two = line.substring(24, 42);
                    }
                    substring = line.substring(42, 43);
                    if (substring.equals("-")) {
                        two_the = line.substring(42, 61);
                    } else {
                        two_the = line.substring(43, 61);
                    }
                    substring = line.substring(61, 62);
                    if (substring.equals("-")) {
                        two_fou = line.substring(61);
                    } else {
                        two_fou = line.substring(62);
                    }
                    bdCheck.setToe(two_one);
                    bdCheck.setCic(two_two);
                    bdCheck.setOmega0(two_the);
                    bdCheck.setCis(two_fou);
                } else if (i % 8 == 5) {
                    String substring = line.substring(4, 5);
                    //-i0 参考时刻的轨道倾角 (radians)
                    //-Crc 轨道半径的余弦调和改正项的振幅 (meters)
                    //-omega 近地点幅角 (radians)
                    //-OMEGA DOT 升交点赤经变化率(radians/sec)
                    String two_one = "";
                    String two_two = "";
                    String two_the = "";
                    String two_fou = "";
                    if (substring.equals("-")) {
                        two_one = line.substring(4, 23);
                    } else {
                        two_one = line.substring(5, 23);
                    }
                    substring = line.substring(23, 24);
                    if (substring.equals("-")) {
                        two_two = line.substring(23, 42);
                    } else {
                        two_two = line.substring(24, 42);
                    }
                    substring = line.substring(42, 43);
                    if (substring.equals("-")) {
                        two_the = line.substring(42, 61);
                    } else {
                        two_the = line.substring(43, 61);
                    }
                    substring = line.substring(61, 62);
                    if (substring.equals("-")) {
                        two_fou = line.substring(61);
                    } else {
                        two_fou = line.substring(62);
                    }
                    bdCheck.setI0(two_one);
                    bdCheck.setCrc(two_two);
                    bdCheck.setOmega(two_the);
                    bdCheck.setOmegadot(two_fou);
                } else if (i % 8 == 6) {
                    String substring = line.substring(4, 5);
                    //-IDOT 轨道倾角变化率 (radians/sec)
                    //-Data 电文来源 (FLOATINTEGER)  Bit 0 “1”: 来自 B1C 的 B-CNAV1  Bit 1 “1”: 来自 B2b 的 B-CNAV1  Bit 2 “1”: 来自 B2a 的 B-CNAV2
                    // Bit 3 “1”: 来自 B2I 播发  Bit 4 “1”: 来自 B1I 播发  Bit 5 “1”: 来自 B3I 播发  或该字段为零，标示来自 B1I 播发
                    //-BDT Week# BDS 的整周计数
                    //-A DOT/留空 长半轴变化率 (meters/sec) A DOT: 对应 B1C、B2a 的导航电文 留空： 对应 B1I、B2I、B3I 的导航电文
                    String two_one = "";
                    String two_two = "";
                    String two_the = "";
                    String two_fou = "";
                    if (substring.equals("-")) {
                        two_one = line.substring(4, 23);
                    } else {
                        two_one = line.substring(5, 23);
                    }
                    substring = line.substring(23, 24);
                    if (substring.equals("-")) {
                        two_two = line.substring(23, 42);
                    } else {
                        two_two = line.substring(24, 42);
                    }
                    substring = line.substring(42, 43);
                    if (substring.equals("-")) {
                        two_the = line.substring(42, 61);
                    } else {
                        two_the = line.substring(43, 61);
                    }
                    substring = line.substring(61, 62);
                    if (substring.equals("-")) {
                        two_fou = line.substring(61);
                    } else {
                        two_fou = line.substring(62);
                    }
                    bdCheck.setIdot(two_one);
                    bdCheck.setData(two_two);
                    bdCheck.setBdt(two_the);
                    bdCheck.setAdot(two_fou);
                } else if (i % 8 == 7) {
                    String substring = line.substring(4, 5);
                    //-空间信号精度指数及空间信号监测精度指数
                    ///SV accuracy(FLOATINTEGER)
                    // 空间信号精度指数及空间信号监测精度指 数：对应 B1C、B2a 的导航电文
                    // bit0-bit3: SISMAI，空间信号监测
                    //精度指数
                    //bit4-bit8: SISAIoe，卫星轨道的切
                    //向和法向误差精度指数
                    //bit9-bit19:top，数据预测的周内时刻
                    //bit20-bit24: SISAIocb，卫星轨道的
                    //径向及卫星钟固定偏
                    //差精度指数
                    //bit25-bit27: SISAIoc1，卫星钟频偏精度指数
                    // bit28-bit30: SISAIoc2，卫星钟频漂
                    //精度指数
                    //SV accuracy：对应 B1I、B2I、B3I 的
                    //导航电文的 URAI


                    //-HS/SatH1 卫星健康状态:(FLOATINTEGER)


                    //-TGD/TGD1 (seconds) TGD: 对应 B1C、B2a 的导航电文
                    // 导航电文来自 B1C,此字段 TGD_B1Cp
                    //导航电文来自 B2a,此字段为 TGD_B2ap
                    // TGD1: 对应 B1I、B2I、B3I 的导航电文的
                    //TGD1（B1/B3）

                    //-ISC/TGD2 (seconds)
                    //ISC：对应 B1C、B2a、B2b 的导航电文
                    // 导航电文来自 B1C,此字段为 ISC_B1Cd
                    //导航电文来自 B2a,此字段为 ISC_B2ad
                    // TGD2：对应 B1I、B2I、B3I 的导航电文的
                    //TGD2（B2/B3）
                    String two_one = "";
                    String two_two = "";
                    String two_the = "";
                    String two_fou = "";
                    if (substring.equals("-")) {
                        two_one = line.substring(4, 23);
                    } else {
                        two_one = line.substring(5, 23);
                    }
                    substring = line.substring(23, 24);
                    if (substring.equals("-")) {
                        two_two = line.substring(23, 42);
                    } else {
                        two_two = line.substring(24, 42);
                    }
                    substring = line.substring(42, 43);
                    if (substring.equals("-")) {
                        two_the = line.substring(42, 61);
                    } else {
                        two_the = line.substring(43, 61);
                    }
                    substring = line.substring(61, 62);
                    if (substring.equals("-")) {
                        two_fou = line.substring(61);
                    } else {
                        two_fou = line.substring(62);
                    }
                    bdCheck.setSv(two_one);
                    bdCheck.setHs(two_two);
                    bdCheck.setTgd(two_the);
                    bdCheck.setIsc(two_fou);
                } else if (i % 8 == 0) {
                    String substring = line.substring(4, 5);
                    //-信息的发射时间（BDT 的周内秒）**）
                    //对于 B-CNAV1 电文，此字段由子帧 1 播 发的 HOW（周内小时计数）和 SOH（小时
                    //内秒计数）计算而来，即 HOW*3600+SOH
                    //对于 B-CNAV2 电文，此字段为 B-CNAV2
                    //信息类型 10 中播发的 SOW
                    //对于 B1I、B2I、B3I 的导航电文，此字
                    //段为 D1 或 D2 导航电文子帧 1 中播发的
                    //SOW

                    //-IODC/AODC:钟差参数版本号/时钟数据龄期
                    //IODC:对应 B1C、B2a 的导航电文
                    //AODC:对应 B1I、B2I、B3I 的导航电文

                    //-Delta n0 DOT 参考时刻卫星平均角速度与
                    //计算值之差的变化率
                    // (radians/sec2)

                    //-SatType/留空 卫星轨道类型
                    //SatType: 对应 B1C、B2a 的导航电
                    //文,1:GEO,2:IGSO,3:MEO
                    //留空： 对应 B1I、B2I、B3I 的导航电文
                    String two_one = "";
                    String two_two = "";
                    String two_the = "";
                    String two_fou = "";
                    if (substring.equals("-")) {
                        two_one = line.substring(4, 23);
                    } else {
                        two_one = line.substring(5, 23);
                    }
                    substring = line.substring(23, 24);
                    if (substring.equals("-")) {
                        two_two = line.substring(23, 42);
                    } else {
                        two_two = line.substring(24, 42);
                    }
                    substring = line.substring(42, 43);
                    if (substring.equals("-")) {
                        two_the = line.substring(42, 61);
                    } else {
                        two_the = line.substring(43, 61);
                    }
                    substring = line.substring(61, 62);
                    if (substring.equals("-")) {
                        two_fou = line.substring(61);
                    } else {
                        two_fou = line.substring(62);
                    }
                    bdCheck.setSow(two_one);
                    bdCheck.setIodc(two_two);
                    bdCheck.setDeltan0dot(two_the);
                    bdCheck.setSattype(two_fou);
                    // key   C192020122012  C 星号 年 月 日 时
                    if (bdCheck.getData().equals("0.100000000000E+01") && bdCheck.getMin().equals("00")) {
                        stringListHashMap.put(bdCheck.getFlag() + bdCheck.getNum() + bdCheck.getYear() + bdCheck.getMon() + bdCheck.getDay() + bdCheck.getHour(), bdCheck);
//                        System.out.println(bdCheck.getFlag() + bdCheck.getNum() + bdCheck.getYear() + bdCheck.getMon() + bdCheck.getDay() + bdCheck.getHour() + bdCheck.getNum());
                        bdCheck = new BDCheck();
                    }
                }
                i++;
            }
            return stringListHashMap;


        } catch (Exception e) {
            e.printStackTrace();
        } finally {
        }
        return null;
    }

    /**
     * @Author: wangkun
     * @Date : 2016/1/21 13:43
     * @Description : 字符串时间转换为Unix时间戳
     */
    public static String date2TimeStamp(String dateString) throws ParseException {
        Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateString);
        long unixTimestamp = date.getTime() / 1000;
        return String.valueOf(unixTimestamp);
    }

}
