<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bdCheck.mapper.bdCheck.BdcCheckReportMapper">

    <select id="getCheckReportPage" resultType="com.xh.vdm.bdCheck.entity.bdCheck.BdcCheckReport">
        select r.*
        from bdc_check_report r
        <if test="request.deviceSeq != null">
            join bdc_terminal t on t.report_id=r.id and t.device_seq=#{request.deviceSeq} and t.is_del=0
        </if>
        where r.state = 'U'
        <if test="request.companyId != null and request.companyId != ''">
            and r.company_id = #{request.companyId,jdbcType=BIGINT}
        </if>
        <if test="request.terminalType != null and request.terminalType != ''">
            and r.terminal_type = #{request.terminalType,jdbcType=VARCHAR}
        </if>
        <if test="request.terminalModel != null and request.terminalModel != ''">
            and r.terminal_model = #{request.terminalModel,jdbcType=VARCHAR}
        </if>
        <if test="request.checkProcess != null and request.checkProcess != ''">
            and r.check_process = #{request.checkProcess,jdbcType=VARCHAR}
        </if>
        <if test="request.checkResult != null">
            and r.check_result = #{request.checkResult}
        </if>
        <if test="request.batchNo != null and request.batchNo != ''">
            and r.batch_no = #{request.batchNo,jdbcType=VARCHAR}
        </if>
        order by r.create_time desc
    </select>

    <update id="resetReportCheckState" parameterType="long">
        update bdc_check_report
            set
                check_start_time = null,
                check_end_time = null,
                check_result = 0,
                check_res_message = null,
                report_time = null,
                check_process = '0',
                test_result = null,
                report_file = null,
                test_time = null
            where id = #{reportId}
    </update>
</mapper>
