package com.xh.vdm.bdCheck.entity;

import lombok.Data;

/**
 * 星历数据头信息-电离层矫正参数
 * 这里只考虑消息头数据正文信息，从第四行开始
 */
@Data
public class BdmSatelliteHeader {

	//星号
	private String num;

	//校正类型
	/*
	GAL  = Galileo     ai0 – ai2
	GPSA = GPS      alpha0 - alpha3
	GPSB = GPS      beta0 - beta3
	BDS1= BDS   alpha1- alpha3
	BDS2= BDS   alpha4- alpha6
	BDS3= BDS   alpha7- alpha9
	BDSA = BDS alpha0 - alpha3
	BDSB = BDS beta0 - beta3
	QZSA = QZS alpha0 - alpha3
	QZSB = QZS beta0 - beta3
	IRNA = IRNSS alpha0 - alpha30
	IRNB = IRNSS beta0 - beta3
	 */
	private String type;

	//参数
	/*
	GPS: alpha0-alpha3 or beta0-beta3
	GAL: ai0, ai1, ai2, 空格
	BDS: 对于B1I、B2I、B3I频点为
	alpha0-alpha3 or beta0-beta3
	对于B1C、B2a、B2b频点
	alpha1-alpha3, 空格
	or alpha4-alpha6, 空格
	or alpha7-alpha9, 空格
	IRNA：IRNSS alpha0 - alpha3
	IRNB：IRNSS beta0 - beta3
	 */
	private String data1;
	private String data2;
	private String data3;
	private String data4;

	//时间标记
	/*
	此字段仅应用于BDS导航星历文
	件，是Toc对应的时间）用24个字母表
	示一天内的24个小时，a：第一小时：
	00h-01h；f=b：第二小时：01h
	02h；…f=x：第24小时：23h-24h
	 */
	private String timeFlag;

	//备注信息：值恒定 IONOSPHERIC
	private String comment1;

	//备注信息：值恒定 CORR
	private String comment2;




}
