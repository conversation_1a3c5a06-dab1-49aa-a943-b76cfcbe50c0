package com.xh.vdm.bdCheck.event;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * 全部终端推送监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@ServerEndpoint("/ws/bdCheck/pushAll/{auth_code}")
public class AllTerminalWSEventListener {

	//与某个客户端的连接会话，需要通过它来给客户端发送数据
	private Session session;
	/**
	 * 用户ID
	 */
	private String userCode;


	//concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
	//虽然@Component默认是单例模式的，但springboot还是会为每个websocket连接初始化一个bean，所以可以用一个静态set保存起来。
	//  注：底下WebSocket是当前类名
	private static CopyOnWriteArraySet<AllTerminalWSEventListener> webSockets =new CopyOnWriteArraySet<>();
	// 用来存在线连接用户信息
	private static ConcurrentHashMap<String,Session> sessionPool = new ConcurrentHashMap<String,Session>();

	private static CopyOnWriteArraySet<String> userCodes = new CopyOnWriteArraySet<>();


	public void putUserCode(String userCode){
		userCodes.add(userCode);
	}

	/**
	 * 链接成功调用的方法
	 * 传入终端赋码
	 */
	@OnOpen
	public void onOpen(Session session, @PathParam(value="auth_code")String userCode) {
		log.info("全部终端北斗识别推送，userCode is {}", userCode);
		log.info("userCodes中的内容为");
		userCodes.forEach(item -> {
			log.info(item);
		});
		try {
			if(!userCodes.contains(userCode.trim())){
				//如果用户还未登录，则不创建session
				log.error("用户未登录，不创建 websocket session");
				return ;
			}
			this.session = session;
			this.userCode = userCode;
			webSockets.add(this);
			sessionPool.put(userCode, session);
			log.info("【websocket消息】有新的连接，总数为:"+webSockets.size());
		} catch (Exception e) {
			log.error("创建websocket连接失败",e);
		}
	}

	/**
	 * 链接关闭调用的方法
	 */
	@OnClose
	public void onClose() {
		try {
			webSockets.remove(this);
			sessionPool.remove(this.userCode);
			log.info("【websocket消息】连接断开，总数为:"+webSockets.size());
		} catch (Exception e) {
		}
	}
	/**
	 * 收到客户端消息后调用的方法
	 *
	 * @param message
	 */
	@OnMessage
	public void onMessage(String message) {
		//log.info("【websocket消息】收到客户端消息:"+message);
	}

	/** 发送错误时的处理
	 * @param session
	 * @param error
	 */
	@OnError
	public void onError(Session session, Throwable error) {

		log.error("推送数据错误,原因:"+error.getMessage());
		error.printStackTrace();
	}

	// 此为广播消息
	public void sendAllMessage(String message) {
		log.info("【websocket消息】广播消息:"+message);
		for(AllTerminalWSEventListener webSocket : webSockets) {
			try {
				if(webSocket.session.isOpen()) {
					webSocket.session.getAsyncRemote().sendText(message);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	// 此为单点消息
	public void sendOneMessage(String userCode, String message) {
		log.info("[SingleTerminalWS]将要执行数据推送");
		Session session = sessionPool.get(userCode);
		log.info("[SingleTerminalWS]session is {}",session);
		if (session != null && session.isOpen()) {
			try {
				log.info("【websocket消息】 单点消息:"+message);
				session.getAsyncRemote().sendText(message);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	// 此为单点消息(多人)
	public void sendMoreMessage(String[] userIds, String message) {
		for(String userId:userIds) {
			Session session = sessionPool.get(userId);
			if (session != null&&session.isOpen()) {
				try {
					log.info("【websocket消息】 单点消息:"+message);
					session.getAsyncRemote().sendText(message);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}
}
