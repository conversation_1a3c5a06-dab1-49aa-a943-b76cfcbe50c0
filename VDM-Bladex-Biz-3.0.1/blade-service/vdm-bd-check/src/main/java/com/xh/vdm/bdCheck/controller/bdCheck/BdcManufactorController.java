/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.bdCheck.controller.bdCheck;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcManufactor;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;
import com.xh.vdm.bdCheck.entity.coding.Machine;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcManufactorService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bdCheck.service.coding.IMachineService;
import com.xh.vdm.bdCheck.vo.bdCheck.BdcManufactorVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.annotation.Log;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springblade.entity.DataAuthCE;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 厂商管理
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@RestController
@AllArgsConstructor
@RequestMapping("bd-check/bdcmanufactor")
@Api(value = "", tags = "接口")
@Slf4j
public class BdcManufactorController extends BladeController {

	private IBdcManufactorService bdcManufactorService;


	private IBdcTerminalService terminalService;

	@Resource
	private IMachineService machineService;

	@Resource
	private CETokenUtil ceTokenUtil;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperation(value = "详情", notes = "传入bdcManufactor")
	public R<BdcManufactor> detail(BdcManufactor bdcManufactor) {
		BdcManufactor detail = bdcManufactorService.getOne(Condition.getQueryWrapper(bdcManufactor));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperation(value = "分页", notes = "传入bdcManufactor")
	public R<IPage<BdcManufactor>> list(BdcManufactor bdcManufactor, Query query) {
		LambdaQueryWrapper<BdcManufactor> wrapper = Wrappers.lambdaQuery(BdcManufactor.class)
			.eq(BdcManufactor::getIsDel, 0)
			.orderByDesc(BdcManufactor::getUpdateTime);
		DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
		if (!StringUtils.isEmpty(bdcManufactor.getName())) {
			wrapper.like(BdcManufactor::getName, "%" + bdcManufactor.getName() + "%");
		}
		if (!StringUtils.isEmpty(bdcManufactor.getCode())) {
			wrapper.like(BdcManufactor::getCode, "%" + bdcManufactor.getCode() + "%");
		}
		IPage<BdcManufactor> pages = bdcManufactorService.page(Condition.getPage(query), wrapper);
		return R.data(pages);
	}

	/**
	 * 自定义分页
	 */
	@GetMapping("/page")
	@ApiOperation(value = "分页", notes = "传入bdcManufactor")
	public R<IPage<BdcManufactorVO>> page(BdcManufactorVO bdcManufactor, Query query) {
		IPage<BdcManufactorVO> pages = bdcManufactorService.selectBdcManufactorPage(Condition.getPage(query), bdcManufactor);
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@Log(menu = "厂商信息管理", operation = Operation.INSERT, objectType = ObjectType.MANUFACTURER)
	@PostMapping("/save")
	@ApiOperation(value = "新增", notes = "传入bdcManufactor")
	public R save(@Valid @RequestBody BdcManufactor bdcManufactor, BladeUser user) {
		//判断终端编号是否已经存在
		String code = bdcManufactor.getCode();
		String name = bdcManufactor.getName();
		if (StringUtils.isEmpty(code)) {
			return R.fail("厂商编号不能为空");
		}
		//校验厂商编号是否已经存在
		long count = bdcManufactorService.count(Wrappers.lambdaQuery(BdcManufactor.class)
			.eq(BdcManufactor::getCode, code)
			.eq(BdcManufactor::getIsDel, 0));
		if (count > 0) {
			return R.fail("该厂商编号[" + code + "]已存在");
		}
		//校验厂商名称是否已经存在
		long countName = bdcManufactorService.count(Wrappers.lambdaQuery(BdcManufactor.class)
			.eq(BdcManufactor::getName, name)
			.eq(BdcManufactor::getIsDel, 0));
		if (countName > 0) {
			return R.fail("该厂商名称[" + name + "]已存在");
		}
		bdcManufactor.setIsDel(0);
		Date date = new Date();
		bdcManufactor.setCreateTime(date);
		bdcManufactor.setUpdateTime(date);
		boolean result = bdcManufactorService.save(bdcManufactor);
		if (result) {
			return R.data(ResultCode.SUCCESS.getCode(), bdcManufactor.getId().toString(),"新增成功");
		}
		return R.status(result);

	}

	/**
	 * 修改
	 */
	@Log(menu = "厂商信息管理", operation = Operation.UPDATE, objectType = ObjectType.MANUFACTURER)
	@PostMapping("/update")
	@ApiOperation(value = "修改", notes = "传入bdcManufactor")
	public R update(@Valid @RequestBody BdcManufactor bdcManufactor, BladeUser user) {
		//判断终端编号是否已经存在
		String code = bdcManufactor.getCode();
		String name = bdcManufactor.getName();
		if (StringUtils.isEmpty(code)) {
			return R.fail("厂商编号不能为空");
		}
		//查找修改之前的厂商名称和厂商编号
		Long id = bdcManufactor.getId();
		BdcManufactor mInDB = bdcManufactorService.getById(id);
		String codeInDB = mInDB.getCode();
		String nameInDB = mInDB.getName();

		//校验厂商编号是否已经存在
		long count = bdcManufactorService.count(Wrappers.lambdaQuery(BdcManufactor.class)
			.eq(BdcManufactor::getCode, code)
			.eq(BdcManufactor::getIsDel, 0));
		if (!code.equals(codeInDB) && count > 0) {
			return R.fail("该厂商编号[" + code + "]已存在");
		}
		//校验厂商名称是否已经存在
		long countName = bdcManufactorService.count(Wrappers.lambdaQuery(BdcManufactor.class)
			.eq(BdcManufactor::getName, name)
			.eq(BdcManufactor::getIsDel, 0));
		if (!name.equals(nameInDB) && countName > 0) {
			return R.fail("该厂商名称[" + name + "]已存在");
		}

		try {
			bdcManufactorService.updateManufacturer(bdcManufactor);

			String result = new CompareUtils<BdcManufactor>().compare(mInDB, bdcManufactor);
			return R.data(ResultCode.SUCCESS.getCode(), result,"编辑成功");
		} catch (Exception e) {
			log.error("更新厂商编号失败", e);
			return R.fail("操作失败：" + e.getMessage());
		}
	}

	/**
	 * 删除
	 */
	@Log(menu = "厂商信息管理", operation = Operation.DELETE, objectType = ObjectType.MANUFACTURER)
	@PostMapping("/remove")
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids, BladeUser user) {
		try {
			//逻辑删除
			if (StringUtils.isEmpty(ids)) {
				return R.fail("没有指定要删除的数据");
			}

			List<Long> list = Func.toLongList(ids);
			List<BdcManufactor> mfList = this.bdcManufactorService.list(
				Wrappers.lambdaQuery(BdcManufactor.class).in(BdcManufactor::getId, list)
			);
			if (CollectionUtils.isEmpty(mfList)) {
				return R.fail("未能获得要删除的厂商记录。");
			}

			//查看是否已经有终端绑定该厂商，如果有，则不允许删除该厂商信息
			//判断入网终端管理
			List<BdcTerminal> terminalList = terminalService.findTerminalListByManufacturerIds(list);
			if (terminalList != null && terminalList.size() > 0) {
				return R.fail("选择的厂商已被终端绑定，请先到入网终端管理页面删除终端信息");
			}

			//获取厂商编号
			List<BdcManufactor> manufactor = bdcManufactorService.listByIds(list);
			List<String> vendors = new ArrayList<>();
			for(BdcManufactor f : manufactor){
				vendors.add(f.getCode());
			}

			//判断北斗资源管理
			long bdTerminalCount = terminalService.findBDTerminalCountByVendor(vendors);
			if(bdTerminalCount > 0){
				return R.fail("选择的厂商已被终端绑定，请先到设备信息管理模块删除终端信息");
			}
			//判断是否已经绑定赋码机
			if (
				this.machineService.count(
					Wrappers.lambdaQuery(Machine.class).in(
						Machine::getVendor, mfList.parallelStream().map(BdcManufactor::getCode).collect(Collectors.toList())
					)
				) > 0
			) {
				return R.fail("选择的厂商含有赋码机，请先前往终端入网管理->赋码机管理，删除所选厂商名下的赋码机。");
			}


			List<BdcManufactor> mList = new ArrayList<>();
			for (Long id : list) {
				BdcManufactor mf = new BdcManufactor();
				mf.setId(id);
				mf.setIsDel(1);
				mList.add(mf);
			}
			return R.status(bdcManufactorService.updateBatchById(mList));
		} catch (Exception e) {
			log.error("删除厂商信息失败", e);
			return R.fail("删除失败" + e.getMessage());
		}
	}


}
