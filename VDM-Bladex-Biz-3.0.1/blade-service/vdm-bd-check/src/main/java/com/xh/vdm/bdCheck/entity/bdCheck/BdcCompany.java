package com.xh.vdm.bdCheck.entity.bdCheck;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.utils.compare.Compare;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(schema = "bd_check")
public class BdcCompany implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 企业名称
     */
	@Compare("企业名称")
    private String companyName;

    /**
     * 对应组织机构中的机构id
     */
	@Compare("所属机构")
    private Long deptId;

    /**
     * 企业地址
     */
	@Compare("地址")
    private String address;

    /**
     * 状态：U 生效     E 无效
     */
    private String state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

	//联系人
	@Compare("联系人")
	private String contactPerson;

	//联系电话
	@Compare("联系电话")
	private String contactPhone;

}
