package com.xh.vdm.bdCheck.vo;

import lombok.Data;

/**
 * 北斗检测数量结果
 * 用于批量查询设备检测数量的返回结果（包含总量和非北斗量）
 * 
 * <AUTHOR>
 * @since 2024-04-20
 */
@Data
public class BDCheckCountResult {

    /**
     * 设备号
     */
    private String deviceNum;

    /**
     * 总检测数量
     */
    private Long totalCount;

    /**
     * 非北斗检测数量
     */
    private Long nonBdCount;

    public BDCheckCountResult() {
    }

    public BDCheckCountResult(String deviceNum, Long totalCount, Long nonBdCount) {
        this.deviceNum = deviceNum;
        this.totalCount = totalCount;
        this.nonBdCount = nonBdCount;
    }
}
