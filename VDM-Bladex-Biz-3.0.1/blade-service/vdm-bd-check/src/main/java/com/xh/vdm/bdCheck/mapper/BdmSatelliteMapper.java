package com.xh.vdm.bdCheck.mapper;

import com.xh.vdm.bdCheck.entity.BdmSatellite;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 星历数据，年表，从中国卫星导航系统管理办公室测试评估研究中心下载的广播星历数据 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
public interface BdmSatelliteMapper extends BaseMapper<BdmSatellite> {

	/**
	 * 存在即更新卫星星历数据
	 * @return
	 */
	void saveOrUpdateSatellite(@Param("satellite") BdmSatellite satellite);

	/**
	 * 获取指定时间内的所有星历数据
	 * 按照星历中的日期排序
	 * @param dateList [yyyyMMdd, yyyyMMdd...]
	 * @param num 星号
	 * @return
	 */
	List<BdmSatellite> getAllSatelliteInDateList(@Param("num") String num, @Param("dateList") List<String> dateList);

	/**
	 * 获取指定时间内的所有星历数据
	 * 按照星历中的日期排序
	 * @param dateList [yyyyMMdd, yyyyMMdd...]
	 * @return
	 */
	List<BdmSatellite> getAllSatelliteInDateListNoNum(@Param("dateList") List<String> dateList);

	List<BdmSatellite> selectSateDataList();

	/**
	 * 查询指定天以来最新的星历数据
	 * @param daysBeforeStr
	 * @return
	 */
	List<BdmSatellite> selectNewestSatellite(String daysBeforeStr);
}
