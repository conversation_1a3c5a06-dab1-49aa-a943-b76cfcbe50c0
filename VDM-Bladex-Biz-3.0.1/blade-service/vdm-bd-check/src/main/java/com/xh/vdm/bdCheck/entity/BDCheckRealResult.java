package com.xh.vdm.bdCheck.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 终端实时检测结果
 * 入网运行之后的北斗检测
 * impala中的表
 * 主键：赋码号 + 经度 + 纬度 + 定位时间
 */
@Data
@TableName("bd_check_real_result")
public class BDCheckRealResult {

	//赋码号
	private String deviceNum;

	//经度
	private Double longitude;

	//纬度
	private Double latitude;

	//定位时间
	private Long locTime;

	//终端类型
	private String terminalType;

	//终端型号
	private String terminalModel;

	//北斗检测识别结果：0 未开始或检测中   1 通过    2 不通过
	private String checkRes;

	//所属单位
	private String deptName;

	//检测结果说明
	private String checkResMessage;

	//定位数据上报的卫星数据
	private String satelliteData;

	//检测日期
	private String dateStr;

	//创建时间
	private Long createTime;

	//更新时间
	private Long updateTime;
}
