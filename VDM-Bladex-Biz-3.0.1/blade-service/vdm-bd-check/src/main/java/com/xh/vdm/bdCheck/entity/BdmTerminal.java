package com.xh.vdm.bdCheck.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmTerminal implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * jtt808协议头的终端手机号，终端的唯一标识
     */
    private String phone;

    /**
     * 终端类型
     */
    private String terminalType;

    /**
     * 终端型号
     */
    private String terminalModel;

    /**
     * 终端厂商编号
     */
    private String vendorId;

    /**
     * 终端注册时间
     */
    private Date registerTime;

    /**
     * 下拉框，标准
     */
    private String localStandards;

    /**
     * 下拉框
     */
    private String terminalFunctionType;

    /**
     * 终端编号
     */
    private String terminalNo;

    /**
     * 是否主动安全设备
     */
    private Integer isAi;

    /**
     * 是否绑定车牌号：0-否，1-是
     */
    private Integer unBindFlag;

    /**
     * 鉴权码
     */
    private String token;

    private String remark;

    /**
     * 绑定的SIM卡信息，对应bdm_teminal_sim表的主键id字段
     */
    private Long terminalSimId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 删除标记：0-未删除，1-已删除
     */
    private Integer isDel;

    /**
     * 创建用户Id
     */
    private Long createUserId;

    private String terminalId;

    private String serial;

    private String imsi;

    private String imei;

    private Integer channelNum;

    private Long deptId;
	//终端识别检测阶段，不会有该值
    private String deviceNum;

    private String uniqueId;


}
