package com.xh.vdm.bdCheck.vo.bdCheck;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.dept.DeptIdAware;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(schema = "bd_check")
public class BdcCompanyRequest implements DeptIdAware {

	private Long id;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 对应组织机构中的机构id
     */
    private Long deptId;
	@Override
	public Long getDeptId() {
		return deptId;
	}

	public void setDeptId(Long deptId) {
		this.deptId = deptId;
	}

    /**
     * 企业地址
     */
    private String address;

	//联系人
	private String contactPerson;

	//联系电话
	private String contactPhone;

}
