package com.xh.vdm.bdCheck.vo.bdCheck;

import lombok.Data;

/**
 * 北斗识别检测 终端检测数据
 */
@Data
public class BDCTerminalCheckData {

	//终端编号
	private String terminalNo;
	//终端类型
	private String terminalType;
	//终端型号
	private String terminalModel;
	//北斗检测结果
	private Integer checkResult;
	//北斗检测结果描述
	private String checkResMessage;
	//接口检测结果
	private Integer testResult;
	//接口检测结果描述
	private String testResMessage;
	//检测数据总数
	private Long checkDataCount;
	//检测为北斗数据总数
	private Long checkBDDataCount;
	//检测为非北斗数据总数
	private Long checkNonBDDataCount;
	//检测天数（工作日）
	private String checkDayCount;
	//sim卡号
	private String sim;
	//是否参与检测
	private Integer isInCheck;
	//协议
	private String protocol;

}
