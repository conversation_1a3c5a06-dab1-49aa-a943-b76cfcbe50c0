<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bdCheck.mapper.terminal.RnssDeviceMapper">

    <sql id="base_dept">
        <if test="userId != null">
            AND EXISTS (
            SELECT 1
            FROM bdm_user_dept_regulates usr
            WHERE usr.dept_id = ${tableAlias}.dept_id
            AND usr.user_id = #{userId}
            )
        </if>
    </sql>


    <select id="countTerminal" resultType="java.lang.Long">
        select (select count(*) cnt from bdm_rnss_device rn where rn.deleted = 0 AND rn.specificity = 2
        <include refid="base_dept">
            <property name="tableAlias" value="rn"/>
        </include>)
        + (select count(*) cnt from bdm_pnt_device pn where pn.deleted = 0 AND pn.specificity = 2
        <include refid="base_dept">
            <property name="tableAlias" value="pn"/>
        </include>)
        + (select count(*) cnt from bdm_rdss_device rd where rd.deleted = 0 AND rd.specificity = 2
        <include refid="base_dept">
            <property name="tableAlias" value="rd"/>
        </include>)
        + (select count(*) cnt from bdm_monit_device mo where mo.deleted = 0 AND mo.specificity = 2
        <include refid="base_dept">
            <property name="tableAlias" value="mo"/>
        </include>)
        + (select count(*) cnt from bdm_wearable_device we where we.deleted = 0 AND we.specificity = 2
        <include refid="base_dept">
            <property name="tableAlias" value="we"/>
        </include>);
    </select>
</mapper>
