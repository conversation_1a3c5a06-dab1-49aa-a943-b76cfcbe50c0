package com.xh.vdm.bdCheck.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bdCheck.entity.BDCheckRealResult;
import com.xh.vdm.bdCheck.vo.BDCheckCountVO;

import com.xh.vdm.bdCheck.vo.BDCheckDateResult;
import org.springblade.core.mp.support.Query;

import java.util.List;
import java.util.Map;

/**
 * 北斗终端实时检测
 * <AUTHOR>
 * @since 2024-04-20
 */
public interface IBDCheckRealResultService extends IService<BDCheckRealResult> {

	/**
	 * 分页查询实时检测结果
	 * @param deviceNum
	 * @return
	 * @throws Exception
	 */
	IPage<BDCheckRealResult> findRealResultPage(String deviceNum, Query query) throws Exception;

	/**
	 * 分页查询实时检测非北斗结果
	 * @param deviceNum
	 * @return
	 * @throws Exception
	 */
	IPage<BDCheckRealResult> findRealNonBDPage(String deviceNum, Query query) throws Exception;

	/**
	 * 查询实时检测结果表中所有的赋码号
	 * @return
	 * @throws Exception
	 */
	List<String> findRealCheckAllDeviceNum() throws Exception;

	/**
	 * 根据赋码号查询终端所有在线日期
	 * @param deviceNum
	 * @return
	 * @throws Exception
	 */
	List<String> findRealCheckDateByDeviceNum(String deviceNum) throws Exception;

	/**
	 * 批量查询终端所有在线日期
	 * @param deviceNums 设备号列表
	 * @return 设备号和日期列表的映射
	 * @throws Exception
	 */
	Map<String, List<String>> batchFindRealCheckDateByDeviceNum(List<String> deviceNums) throws Exception;

	/**
	 * 查询给定日期之后的实时检测数据量（包含给定日期）
	 * @param deviceNum
	 * @param dateStr
	 * @return
	 * @throws Exception
	 */
	Long findRealCheckCountGE(String deviceNum, String dateStr) throws Exception;

	/**
	 * 查询给定日期之后的实时检测非单北斗数据量（包含给定日期）
	 * @param deviceNum
	 * @param dateStr
	 * @return
	 * @throws Exception
	 */
	Long findRealCheckNonBDCountGE(String deviceNum, String dateStr) throws Exception;

	/**
	 * 查询终端总的检测数量
	 * 实际是查询总有效定位点数量
	 * @param deviceNum
	 * @return
	 * @throws Exception
	 */
	Long findRealCheckAllCount(String deviceNum) throws Exception;



	/**
	 * 批量查询给定日期之后的实时检测数据量（合并查询总量和非北斗量）
	 * 推荐使用此方法，性能最优
	 *
	 * @param checkDateResultList 设备号和日期的映射
	 * @return 包含总量和非北斗量的映射，key为设备号，value为BDCheckCountVO对象
	 * @throws Exception
	 */
	Map<String, BDCheckCountVO> batchFindRealCheckCountAndNonBDCountGE(List<BDCheckDateResult> checkDateResultList) throws Exception;

	/**
	 * 批量查询终端总的检测数量
	 * @param deviceNums 设备号列表
	 * @return 设备号和数量的映射
	 * @throws Exception
	 */
	Map<String, Long> batchFindRealCheckAllCount(List<String> deviceNums) throws Exception;

	/**
	 * 批量查询总非北斗数据量
	 * @param deviceNums 设备号列表
	 * @return 设备号和数量的映射
	 * @throws Exception
	 */
	Map<String, Long> batchFindRealCheckNonBDCount(List<String> deviceNums) throws Exception;
}
