package com.xh.vdm.bdCheck.service.coding;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bdCheck.entity.coding.Machine;
import com.xh.vdm.bdCheck.vo.coding.request.MachineRequest;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;

import java.security.NoSuchAlgorithmException;
import java.text.ParseException;

public interface IMachineService extends IService<Machine> {

	// 分页列表
	IPage<Machine> getCodingMachinePage (MachineRequest request, Query query) throws ParseException;

	// 添加
	R<String> addCodingMachine (MachineRequest request) throws NoSuchAlgorithmException;

	// 更新
	R<String> editCodingMachine (MachineRequest request);

	// 登录
	String loginCodingMachine (MachineRequest request) throws Exception;
}
