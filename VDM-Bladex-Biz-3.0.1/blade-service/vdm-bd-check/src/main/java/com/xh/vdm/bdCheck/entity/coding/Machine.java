package com.xh.vdm.bdCheck.entity.coding;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;
import java.util.Date;

// 赋码机
@Data
@TableName(value = "bd_check.bdm_coding_machine", autoResultMap = true)
public class Machine implements Serializable {

	// 赋码机编号
	@TableField(value = "number")
	@Compare("赋码机编号")
	private String number;

	// 赋码机登录密码（密文）
	@TableField(value = "password")
	private String password;

	// SM2私钥
	@TableField(value = "private_key")
	private String privateKey;

	// SM2公钥
	@TableField(value = "public_key")
	private String publicKey;

	// 使用赋码机的设备所属厂商编号
	@TableField(value = "vendor")
	@Compare("终端生产厂商")
	private String vendor;

	// 创建时间（格式：yyyy-MM-dd HH:mm:ss）
	@TableField(value = "create_time")
	@Compare("创建日期")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date createTime;

	// 是否禁用（0：否，1：是）
	@TableField(value = "disabled")
	@Compare("状态")
	private Byte disabled;
}
