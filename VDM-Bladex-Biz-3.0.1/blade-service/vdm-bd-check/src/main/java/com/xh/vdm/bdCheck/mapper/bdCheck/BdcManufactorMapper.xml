<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bdCheck.mapper.bdCheck.BdcManufactorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="bdcManufactorResultMap" type="com.xh.vdm.bdCheck.entity.bdCheck.BdcManufactor">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="address" property="address"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="is_del" property="isDel"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <select id="selectBdcManufactorPage" resultMap="bdcManufactorResultMap">
        select * from bdc_manufactor where is_deleted = 0
    </select>

</mapper>
