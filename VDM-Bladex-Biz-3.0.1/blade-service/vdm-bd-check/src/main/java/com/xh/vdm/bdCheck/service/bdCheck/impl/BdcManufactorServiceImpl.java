/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.bdCheck.service.bdCheck.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcManufactor;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;
import com.xh.vdm.bdCheck.mapper.bdCheck.BdcManufactorMapper;
import com.xh.vdm.bdCheck.mapper.bdCheck.BdcTerminalMapper;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcManufactorService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bdCheck.vo.bdCheck.BdcManufactorVO;
import org.springblade.core.tool.api.R;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Service
public class BdcManufactorServiceImpl extends ServiceImpl<BdcManufactorMapper, BdcManufactor> implements IBdcManufactorService {

	@Resource
	private IBdcTerminalService terminalService;

	@Override
	public IPage<BdcManufactorVO> selectBdcManufactorPage(IPage<BdcManufactorVO> page, BdcManufactorVO bdcManufactor) {
		return page.setRecords(baseMapper.selectBdcManufactorPage(page, bdcManufactor));
	}

	@Transactional
	@Override
	public void updateManufacturer(BdcManufactor manufactor) {
		//1.更新终端信息中的厂商编号
		//获取目前的厂商编号
		BdcManufactor manufactorInDB = getById(manufactor.getId());
		String code = manufactorInDB.getCode();
		List<BdcTerminal> terminalList = terminalService.list(Wrappers.lambdaQuery(BdcTerminal.class)
			.eq(BdcTerminal::getManufacturer, code)
			.eq(BdcTerminal::getIsDel, 0));
		Date updateTime = new Date();
		terminalList.forEach(item -> {
			item.setManufacturer(manufactor.getCode());
			item.setUpdateTime(updateTime);
		});
		//更新终端信息
		terminalService.updateBatchById(terminalList);

		//2.更新厂商信息
		manufactor.setUpdateTime(new Date());
		updateById(manufactor);
	}
}
