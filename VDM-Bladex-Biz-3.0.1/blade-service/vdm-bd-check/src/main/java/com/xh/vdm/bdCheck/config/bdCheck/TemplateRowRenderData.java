package com.xh.vdm.bdCheck.config.bdCheck;

import com.deepoove.poi.data.CellRenderData;
import com.deepoove.poi.data.ParagraphRenderData;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.style.CellStyle;
import com.deepoove.poi.data.style.ParagraphStyle;
import com.deepoove.poi.data.style.RowStyle;
import com.deepoove.poi.data.style.Style;
import com.xh.vdm.bdCheck.vo.bdCheck.BDCheckTerminalExportVO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Data
public class TemplateRowRenderData {

	/**
	 * 管控分类措施
	 */
	private List<RowRenderData> typeRowRenderDataList;

	private RowStyle rowStyle;

	public TemplateRowRenderData(List<BDCheckTerminalExportVO> inventoryVos) {
		// 初始化样式
		initStyle();
		// 初始化动态数据
		initData(inventoryVos);
	}

	private void initStyle() {
		// 字体样式
		Style style = new Style("宋体", 10);

		// 段落样式
		ParagraphStyle paragraphStyle = new ParagraphStyle();
		paragraphStyle.setDefaultTextStyle(style);
		// ps:这里才是字体居中对齐
		paragraphStyle.setAlign(ParagraphAlignment.CENTER);

		// 表格样式
		CellStyle cellStyle = new CellStyle();
		// ps:表格也需要居中，否则字体不在正中间，会偏上
		cellStyle.setVertAlign(XWPFTableCell.XWPFVertAlign.CENTER);
		cellStyle.setDefaultParagraphStyle(paragraphStyle);

		// 行样式
		this.rowStyle = new RowStyle();
		rowStyle.setDefaultCellStyle(cellStyle);
	}

	private void initData(List<BDCheckTerminalExportVO> inventoryVos) {
		// 管控分类
		List<RowRenderData> newTypeRowRenderDataList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(inventoryVos)) {
			for (BDCheckTerminalExportVO vo : inventoryVos) {
				// 共有14列
				List<CellRenderData> cellDataList = new ArrayList<>();
				cellDataList.add(new CellRenderData().addParagraph(new ParagraphRenderData().addText(vo.getTerminalNo())));
				cellDataList.add(new CellRenderData().addParagraph(new ParagraphRenderData().addText(vo.getCheckDate())));
				cellDataList.add(new CellRenderData().addParagraph(new ParagraphRenderData().addText(vo.getCheckTotalDataCount()+"")));
				cellDataList.add(new CellRenderData().addParagraph(new ParagraphRenderData().addText(vo.getCheckBDDataCount()+"")));
				cellDataList.add(new CellRenderData().addParagraph(new ParagraphRenderData().addText(vo.getCheckNonBDCount()+"")));
				cellDataList.add(new CellRenderData().addParagraph(new ParagraphRenderData().addText(vo.getLocTime())));
				cellDataList.add(new CellRenderData().addParagraph(new ParagraphRenderData().addText(vo.getLocCheckRes())));
				//cellDataList.add(new CellRenderData().addParagraph(new ParagraphRenderData().addText(vo.getLocCheckResMessage())));
				RowRenderData rowRenderData = new RowRenderData();
				// 样式
				rowRenderData.setRowStyle(rowStyle);
				rowRenderData.setCells(cellDataList);
				newTypeRowRenderDataList.add(rowRenderData);
			}
			this.typeRowRenderDataList = newTypeRowRenderDataList;
		}else{
			this.typeRowRenderDataList = Collections.emptyList();
		}
	}
}
