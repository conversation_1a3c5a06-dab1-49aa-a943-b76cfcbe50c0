package com.xh.vdm.bdCheck.vo;

import lombok.Data;

/**
 * 北斗检测总数量结果
 * 用于批量查询设备总检测数量的返回结果
 * 
 * <AUTHOR>
 * @since 2024-04-20
 */
@Data
public class BDCheckAllCountResult {

    /**
     * 设备号
     */
    private String deviceNum;

    /**
     * 总数量值
     */
    private Long countValue;

    public BDCheckAllCountResult() {
    }

    public BDCheckAllCountResult(String deviceNum, Long countValue) {
        this.deviceNum = deviceNum;
        this.countValue = countValue;
    }
}
