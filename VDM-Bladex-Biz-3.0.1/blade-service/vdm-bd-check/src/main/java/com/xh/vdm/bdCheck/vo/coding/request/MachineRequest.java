package com.xh.vdm.bdCheck.vo.coding.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.bdCheck.vo.coding.request.group.AddMachineGroup;
import com.xh.vdm.bdCheck.vo.coding.request.group.EditMachineGroup;
import com.xh.vdm.bdCheck.vo.coding.request.group.LoginMachineGroup;
import com.xh.vdm.bdCheck.vo.coding.request.group.MachineListGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

@ApiModel(value = "请求体：赋码机")
@Data
public class MachineRequest {

	@JsonProperty("code_machine_num")
	@ApiModelProperty(name = "code_machine_num", value = "赋码机编号", example = "CE01", required = true)
	@NotEmpty(message = "赋码机编号为空。", groups = {AddMachineGroup.class, EditMachineGroup.class, LoginMachineGroup.class})
	@Pattern(regexp = "[A-Z]{2}\\d{2}", message = "赋码机编号不正确。", groups = {AddMachineGroup.class, EditMachineGroup.class, LoginMachineGroup.class})
	private String codeMachineNum;

	@JsonProperty("password")
	@ApiModelProperty(name = "password", value = "赋码机登录密码（密文）", example = "21232f297a57a5a743894a0e4a801fc3", required = true)
	@NotEmpty(message = "赋码机登录密码为空。", groups = {AddMachineGroup.class, LoginMachineGroup.class})
	@Pattern(regexp = "\\w{32}", message = "赋码机登录密码不正确。", groups = {AddMachineGroup.class, EditMachineGroup.class, LoginMachineGroup.class})
	private String password;

	@JsonProperty("manufacturer")
	@ApiModelProperty(name = "manufacturer", value = "使用赋码机的设备所属厂商编号", example = "9999", required = true)
	@NotEmpty(message = "所属厂商编号为空。", groups = {AddMachineGroup.class})
	@Pattern(regexp = "\\d{4}", message = "所属厂商编号不正确。", groups = {MachineListGroup.class, AddMachineGroup.class, EditMachineGroup.class})
	private String manufacturer;

	@JsonProperty("start_time")
	@ApiModelProperty(name = "start_time", value = "开始时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-06-27 08:00:00", required = true)
	@Pattern(regexp = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}", message = "开始时间不正确。", groups = {MachineListGroup.class})
	private String startTime;

	@JsonProperty("end_time")
	@ApiModelProperty(name = "end_time", value = "结束时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-06-27 08:00:00", required = true)
	@Pattern(regexp = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}", message = "结束时间不正确。", groups = {MachineListGroup.class})
	private String endTime;

	@JsonProperty("enable")
	@ApiModelProperty(name = "enable", value = "启用状态（0：未启用，1：已启用）", example = "2024-06-27", required = true)
	private Byte enable;
}
