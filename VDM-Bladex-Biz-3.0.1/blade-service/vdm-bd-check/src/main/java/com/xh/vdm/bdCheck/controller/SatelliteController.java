package com.xh.vdm.bdCheck.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.JsonObject;
import com.xh.vdm.bdCheck.checkCore.BDCheckUtil;
import com.xh.vdm.bdCheck.checkCore.SatelliteDateService;
import com.xh.vdm.bdCheck.entity.BdmSatellite;
import com.xh.vdm.bdCheck.service.IBdmSatelliteService;
import com.xh.vdm.bdCheck.util.CoordinateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.StringUtils;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;

@RestController
@RequestMapping("/satellite")
@Slf4j
public class SatelliteController {


	@Resource
	private IBdmSatelliteService satelliteService;

	@Resource
	private SatelliteDateService sateService;

	/**
	 * 手动更新北斗星历数据
	 * 星历文件在ftp的 /brdc/2024/tarcxxxx.24b
	 * @param year
	 * @param fileName
	 * @return
	 */
	@GetMapping("/updateSatellite")
	public R<String> updateSatellite(String year, String fileName){
		try{
			if(sateService.handleSatelliteDate(year, fileName)){
				return R.success("手动更新星历数据成功");
			}else{
				return R.fail("手动更新星历数据失败");
			}
		}catch (Exception e){
			log.error("手动更新星历数据失败",e);
			return R.fail("手动更新星历数据失败，出现异常: " + e.getMessage());
		}
	}

	/**
	 * 获取星历数据
	 * @return
	 */
	@GetMapping("/getSatelliteData")
	public R<Map<String,Object>> getSatelliteData(){
		List<Map<String,Object>> resList = new ArrayList<>();
		Map<String,Object> resMap = new HashMap<>();
		try{
			//J2000历元开始时间
			String jDateTime = "2000-01-01 12:00:00";
			//1.查询星历数据
			//取近2天的数据
			String dateStr = DateUtil.getDateString();
			String dateStrYes = DateUtil.getDateString((new Date().getTime() - 24 * 3600 * 1000)/1000);
			String dateStrYes2 = DateUtil.getDateString((new Date().getTime() - 48 * 3600 * 1000)/1000);
			String year = dateStr.split("-")[0];
			String month = dateStr.split("-")[1];
			String day = dateStr.split("-")[2];
			String yearYes = dateStrYes.split("-")[0];
			String monthYes = dateStrYes.split("-")[1];
			String dayYes = dateStrYes.split("-")[2];
			String yearYes2 = dateStrYes2.split("-")[0];
			String monthYes2 = dateStrYes2.split("-")[1];
			String dayYes2 = dateStrYes2.split("-")[2];
			//查询当天的数据
			List<BdmSatellite> sates = satelliteService.list(Wrappers.lambdaQuery(BdmSatellite.class)
				.eq(BdmSatellite::getYear,year)
				.eq(BdmSatellite::getMon, month)
				.eq(BdmSatellite::getDay, day)
				.orderByAsc(BdmSatellite::getNum)
				.orderByAsc(BdmSatellite::getMon)
				.orderByAsc(BdmSatellite::getDay)
				.orderByAsc(BdmSatellite::getHour)
			);
			//查询前一天的数据
			List<BdmSatellite> satesYes = satelliteService.list(Wrappers.lambdaQuery(BdmSatellite.class)
				.eq(BdmSatellite::getYear,yearYes)
				.eq(BdmSatellite::getMon, monthYes)
				.eq(BdmSatellite::getDay, dayYes)
				.orderByAsc(BdmSatellite::getNum)
				.orderByAsc(BdmSatellite::getMon)
				.orderByAsc(BdmSatellite::getDay)
				.orderByAsc(BdmSatellite::getHour)
			);
			//查询前2天的数据
			List<BdmSatellite> satesYes2 = satelliteService.list(Wrappers.lambdaQuery(BdmSatellite.class)
				.eq(BdmSatellite::getYear,yearYes2)
				.eq(BdmSatellite::getMon, monthYes2)
				.eq(BdmSatellite::getDay, dayYes2)
				.orderByAsc(BdmSatellite::getNum)
				.orderByAsc(BdmSatellite::getMon)
				.orderByAsc(BdmSatellite::getDay)
				.orderByAsc(BdmSatellite::getHour)
			);
			//查询前3天的数据
			List<BdmSatellite> satesYes3 = satelliteService.list(Wrappers.lambdaQuery(BdmSatellite.class)
				.eq(BdmSatellite::getYear,yearYes2)
				.eq(BdmSatellite::getMon, monthYes2)
				.eq(BdmSatellite::getDay, dayYes2)
				.orderByAsc(BdmSatellite::getNum)
				.orderByAsc(BdmSatellite::getMon)
				.orderByAsc(BdmSatellite::getDay)
				.orderByAsc(BdmSatellite::getHour)
			);

			List<BdmSatellite> list = new ArrayList<>();
			list.addAll(satesYes3);
			list.addAll(satesYes2);
			list.addAll(satesYes);
			list.addAll(sates);

			//2.获取每颗星的运行时段
			//按照星号进行分组
			Map<String,List<BdmSatellite>> sateGroup = new HashMap<>();
			for(BdmSatellite s : list){
				String num = s.getNum();
				List<BdmSatellite> tmpList = sateGroup.get(num);
				if(tmpList == null){
					tmpList = new ArrayList<>();
				}
				tmpList.add(s);
				sateGroup.put(num, tmpList);
			}
			//对每组数据计算时间参数
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			Map<String,List<Map<String,String>>> timeMap = new HashMap<>();
			for(String num : sateGroup.keySet()){
				//todo1 调试
				/*if("38".equals(num)){
					log.info("here is 38");
				}*/
				List<BdmSatellite> listTmp = sateGroup.get(num);
				List<Map<String,String>> timeList = new ArrayList<>();
				for(int i = 0; i < listTmp.size() - 1; i++){
					BdmSatellite start = listTmp.get(i);
					BdmSatellite end = listTmp.get(i + 1);
					String startDateStr = start.getYear()+"-"+start.getMon()+"-"+start.getDay()+" "+start.getHour()+":"+start.getMin()+":"+start.getSec();
					String endDateStr = end.getYear()+"-"+end.getMon()+"-"+end.getDay()+" "+end.getHour()+":"+end.getMin()+":"+end.getSec();
					//为了模拟实时的状态，这里需要加一天
					String startTimeStr = formatDate(DateUtil.getDateBeforeDay(sdf.parse(startDateStr), -1));
					String endTimeStr = formatDate(DateUtil.getDateBeforeDay(sdf.parse(endDateStr), -1));
					String interval = startTimeStr + "/" + endTimeStr;
					String epoch = endTimeStr.substring(0,19)+"Z";
					Map<String,String> tmpMap = new HashMap<>();
					tmpMap.put("interval", interval);
					tmpMap.put("epoch",epoch);
					timeList.add(tmpMap);
				}
				timeMap.put(num,timeList);
			}

			//3.获取每颗星的位置信息
			Map<String,List<String[]>> locMap = new HashMap<>();
			for(String num : sateGroup.keySet()){
				List<BdmSatellite> sateList = sateGroup.get(num);
				if(sateList == null || sateList.size() < 1){
					continue;
				}
				//todo1 调试
				/*if("38".equals(num)){
					log.info("here is 38");
				}*/
				BdmSatellite start = sateList.get(0);
				String startDateStr = start.getYear()+"-"+start.getMon()+"-"+start.getDay()+" "+start.getHour()+":"+start.getMin()+":"+start.getSec();
				long startTime = sdf.parse(startDateStr).getTime()/1000;
				List<String[]> locList = new ArrayList<>();
				for(int i = 0; i < sateList.size(); i++){
					BdmSatellite s = sateList.get(i);
					//星历数据转xyz
					String locDateStr = s.getYear() + "-" + s.getMon() +"-" + s.getDay() + " " + s.getHour() + ":" + s.getMin() + ":" + s.getSec();
					Date locDate = DateUtil.sdfHolder.get().parse(locDateStr);
					LocalDateTime dateTime = LocalDateTime.ofEpochSecond(locDate.getTime()/1000, 0, ZoneOffset.UTC);//定位时间
					//计算在本周第几天
					int tt = BDCheckUtil.calculationTT(dateTime);
					double[] satPosition = BDCheckUtil.xyzB1I(s, new BigDecimal(tt));
					//CGCS2000 xyz 转 WGS84 xyz
					//double[] pos = CGCS20000XYZ_to_CECS(satPosition[0],satPosition[1],satPosition[2]);

					//CGCS2000转ECI
					String locTimeStr = s.getYear()+"-"+s.getMon()+"-"+s.getDay()+" "+s.getHour()+":"+s.getMin()+":"+s.getSec();
					double second = DateUtil.sdfHolder.get().parse(locTimeStr).getTime()/1000;
					double[] pos = CoordinateUtil.convertCGCS2000ToECI(satPosition[0],satPosition[1],satPosition[2], second);

					//double[] pos = satPosition;
					String endDateStr = s.getYear()+"-"+s.getMon()+"-"+s.getDay()+" "+s.getHour()+":"+s.getMin()+":"+s.getSec();
					long endTime = sdf.parse(endDateStr).getTime()/1000;
					String[] arr = {(endTime - startTime)+"",String.format("%.18f", pos[0]),String.format("%.18f", pos[1]), String.format("%.18f", pos[2])};
					locList.add(arr);
				}
				locMap.put(num, locList);
			}




			//todo1 调试
			/*List<String> numList = new ArrayList<>();
			numList.add("23");
			numList.add("37");
			numList.add("46");
			numList.add("36");
			numList.add("26");
			numList.add("24");
			numList.add("25");
			numList.add("14");
			numList.add("33");
			numList.add("41");
			numList.add("32");
			numList.add("20");
			numList.add("48");
			numList.add("19");
			numList.add("22");
			numList.add("21");
			numList.add("50");
			numList.add("42");
			numList.add("28");
			numList.add("43");
			numList.add("11");
			numList.add("34");
			numList.add("12");
			numList.add("44");
			numList.add("35");
			numList.add("27");
			numList.add("30");
			numList.add("45");
			numList.add("29");*/

			//轨道数据修正
			//卫星会定期进行轨道修正，根据星历数据在Cesium上进行数据展示时，
			//会明显看到轨迹的高低变化，展示起来很不好看。
			//这里对轨道数据进行修正，使卫星始终展示在一条轨道上
			//经过数据比对和实际图像校验，MEO、GEO不会产生图像上的变轨现象，所以只对IGSO进行处理
			//处理方式：IGSO每24小时绕地球一圈，当出现变轨时，变轨之后的卫星位置使用上一轮次时的位置，所以始终使用同一轨道
			for(String num : locMap.keySet()){
				if(BDCheckUtil.SATE_TYPE.get(num) == null || !BDCheckUtil.SATE_TYPE.get(num).equals("IGSO")){
					continue;
				}
				//每颗卫星都识别和纠正
				//记录卫星在某时刻的位置（一共查询三天，只用记录上一轮次的位置）
				Map<String, String[]> map = new HashMap<>();
				boolean isChange = false;
				for(int i = 1; i < locMap.get(num).size(); i++ ){
					//识别变轨：
					String[] loc1 = locMap.get(num).get(i-1);
					String[] loc2 = locMap.get(num).get(i);
					double dis = calcDistance(loc1, loc2);
					String hour = timeMap.get(num).get(i-1).get("epoch").replace("T"," ").replace("Z","").substring(11,13);

					log.info("num={}, type={}, distance={}, time={}",num, BDCheckUtil.SATE_TYPE.get(num), dis, timeMap.get(num).get(i-1).get("epoch"));
					if(i > 2){
						long timePre = DateUtil.sdfHolder.get().parse(timeMap.get(num).get(i-1).get("epoch").replace("T"," ").replace("Z","")).getTime();
						long timeNow = DateUtil.sdfHolder.get().parse(timeMap.get(num).get(i-2).get("epoch").replace("T"," ").replace("Z","")).getTime();
						if(dis > 1.2E7 && (timeNow-timePre) <= 3600){
							//如果卫星发生了变轨，那么之后的每颗卫星都使用之前的位置
							isChange = true;
						}
						if(isChange /*&& num.equals("38")*/){
							String[] arr = map.get(hour);
							if(arr != null){
								loc2[1] = arr[1];
								loc2[2] = arr[2];
								loc2[3] = arr[3];
							}
						}
					}
					map.put(hour, loc2);
				}
			}


			//4.整合数据
			for(String num : sateGroup.keySet()){
				String type = BDCheckUtil.SATE_TYPE.get(num);
				if(StringUtils.isEmpty(type)){
					//如果不包含该星，则不返回该星的数据
					continue;
				}
				//xyz数据转czml
				Map<String,Object> map = new HashMap<>();
				map.put("id", num);
				map.put("type",BDCheckUtil.SATE_TYPE.get(num));
				//todo1 调试
				//只选择MEO
				/*if(!BDCheckUtil.SATE_TYPE.get(num).equals("GEO")){
					continue;
				}*/
				/*if(numList.contains(num)){
					continue;
				}*/
				/*if(!num.equals("08")){
					continue;
				}*/


				map.put("leadTime", timeMap.get(num));
				map.put("cartesian", locMap.get(num));
				resList.add(map);
			}
			resMap.put("data", resList);
		}catch (Exception e){
			log.error("查询星历数据失败",e);
			return R.fail("查询星历数据失败");
		}
		return R.data(resMap);
	}

	/**
	 * 计算两组坐标之间的距离
	 * @param arr1
	 * @param arr2
	 * @return
	 */
	private double calcDistance(String[] arr1, String[] arr2){
		double x1 = Double.parseDouble(arr1[1]);
		double y1 = Double.parseDouble(arr1[2]);
		double z1 = Double.parseDouble(arr1[3]);
		double x2 = Double.parseDouble(arr2[1]);
		double y2 = Double.parseDouble(arr2[2]);
		double z2 = Double.parseDouble(arr2[3]);
		return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2) + Math.pow(z2 - z1, 2));
	}


	private String formatDate(Date date){
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'");
		//sdf.setTimeZone(TimeZone.getTimeZone("UTC")); // 设置时区为UTC

		// 将Date对象格式化为ISO 8601字符串
		String iso8601String = sdf.format(date);
		return iso8601String;
	}


	/**
	 * 查询测试数据
	 * @return
	 */
	@GetMapping("/getData")
	public String getData(){
		JsonObject json = sateService.generateCzml();
		return json.toString();
	}


}
