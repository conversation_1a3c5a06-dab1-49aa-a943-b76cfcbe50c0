package com.xh.vdm.bdCheck.consumer.bdCheck;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.utils.StringUtils;
import com.xh.vdm.bdCheck.checkCore.BDCheckUtil;
import com.xh.vdm.bdCheck.dto.BDCheckResAndMessage;
import com.xh.vdm.bdCheck.entity.Location;
import com.xh.vdm.bdCheck.entity.TerminalSatelliteData;
import com.xh.vdm.bdCheck.entity.bdCheck.BDCheckLocations;
import com.xh.vdm.bdCheck.entity.bdCheck.BDCheckLocationsKudu;
import com.xh.vdm.bdCheck.event.AllTerminalWSEventListener;
import com.xh.vdm.bdCheck.event.SingleTerminalWSEventListener;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bdCheck.vo.bdCheck.BDCheckResultResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description: 终端识别检测消费者，推送消息
 * 从kafka读取数据推送到websocket
 * @Author: zhouxw
 * @Date: 2023/5/18 11:24
 */
@Component
@EnableKafka
@Slf4j
public class TerminalCheckResultWSConsumer {

	@Resource
	private SingleTerminalWSEventListener stl;

	@Resource
	private AllTerminalWSEventListener atl;

	@Resource
	private BDCheckUtil bdCheckUtil;

	@Value("${bd-check.enable:true}")
	private boolean bdCheckEnable;

	//本地缓存deptName, key: deviceName  value: deptName
	private ConcurrentHashMap<String,String> companyNameMap = new ConcurrentHashMap<>();
	//记录缓存日期（2个小时）
	private ConcurrentHashMap<String,Long> companyNameTimestampMap = new ConcurrentHashMap<>();

	@Resource
	private IBdcTerminalService terminalService;

	@Resource
	private KafkaTemplate kafkaTemplate;


	/**
	 * @description: 读取kafka推送到websocket
	 * @author: zhouxw
	 * @date: 2023-05-138 11:30:27
	 * @param: [record]
	 * @return: void
	 **/
	@KafkaListener( containerFactory = "bdCheckTerminalListenerContainerFactory", batch = "true", topics = {"TerminalCheckTopic"})
	public void push(List<ConsumerRecord<String, String>> consumerRecords, Acknowledgment acknowledgment){

		try{
			log.info("[BD_CHECK_TERMINAL]反序列化的数据为：{}", consumerRecords);
			if (CollectionUtils.isEmpty(consumerRecords)) {
				return;
			}
			log.info("[BD_CHECK_TERMINAL]开始消费数据");
			for(int  i = 0 ; i < consumerRecords.size() ; i++){
				//1.获取topic中的数据，将数据通过websocket推送到前端
				String dataStr = consumerRecords.get(i).value();
				if(StringUtils.isEmpty(dataStr)){
					continue;
				}
				BDCheckLocations ln = JSON.parseObject(dataStr, BDCheckLocations.class);
				/*if(ln.getDeviceNo() == null){
					log.info(JSON.toJSONString(ln));
				}*/
				/*if(ln.getDeviceNo().equals("04B01022D0137025") || ln.getDeviceNo().equals("04B01022D0138829")){
					log.info(JSON.toJSONString(ln));
				}*/
				if(ln == null){
					continue;
				}

				//只处理有效数据
				if(ln.getValid() != 1){
					continue;
				}


				//获取终端编号
				String deviceNo = ln.getDeviceNo();
				if(StringUtils.isEmpty(deviceNo)){
					//如果终端赋码号是空，则不再推送数据
					continue;
				}

				//2.检测定位点
				Location loc = new Location();
				BeanUtils.copyProperties(ln, loc);
				loc.setLongitude(ln.getLongitude().doubleValue());
				loc.setLatitude(ln.getLatitude().doubleValue());
				//经纬度处理
				double lat = loc.getLatitude();
				double lon = loc.getLongitude();
				if(lat < 1 || lon < 1){
					//如果是未定位状态，则不判断该定位点
					continue;
				}
				BDCheckResAndMessage bds = bdCheckUtil.bdCheck(loc);

				//3.整理推送数据
				BDCheckResultResponse response = new BDCheckResultResponse();
				response.setTerminalType(CommonConstant.TERMINAL_TYPE_VEHICLE);
				response.setLongitude(ln.getLongitude().doubleValue());
				response.setLatitude(ln.getLatitude().doubleValue());
				if(bdCheckEnable){
					//如果开启了北斗检测，则真实检测每个定位点
					//1:北斗定位   0:疑似非北斗定位
					response.setCheckRes(bds.getResult()?"1":"0");
					response.setCheckResMessage(bds.getMessage());
				}else{
					//如果没有开启北斗检测，则检测结果固定设置为通过
					//1:北斗定位   0:疑似非北斗定位
					response.setCheckRes("1");
					response.setCheckResMessage("调试模式，跳过北斗检测，设置为通过");
				}
				response.setDeviceNo(deviceNo);
				response.setLocTime(DateUtil.getDateTimeString(ln.getLocTime()));

				//todo 添加星历数据，正常其概况下，只有在检测为北斗终端的时候返回星历数据，此处为了前端测试效果
				if(!(ln.getAuxsNormal() == null || ln.getAuxsNormal().get(CommonConstant.AUXS_STATELLITE_KEY) == null)){
					List<TerminalSatelliteData> sateList = JSON.parseArray(JSON.toJSONString(ln.getAuxsNormal().get(CommonConstant.AUXS_STATELLITE_KEY)), TerminalSatelliteData.class);
					response.setSatellites(sateList);
				}else{
					response.setSatellites(null);
				}

				//从本地缓存取deptName
				String companyName = companyNameMap.get(deviceNo);
				if(StringUtils.isEmpty(companyName)){
					//如果缓存中获取不到，则查询数据库
					String dName = terminalService.findCompanyNameByTerminalNo(deviceNo);
					companyName = dName;
					if(!StringUtils.isEmpty(companyName)){
						companyNameMap.put(deviceNo, companyName);
						companyNameTimestampMap.put(deviceNo, System.currentTimeMillis());
					}
				}else{
					//判断本地缓存是否过期，如果过期，则删除缓存
					Long timestamp = companyNameTimestampMap.get(deviceNo);
					if(timestamp == null || System.currentTimeMillis() - timestamp > 2 * 3600 * 1000){
						companyNameMap.remove(deviceNo);
						if(timestamp != null){
							companyNameTimestampMap.remove(deviceNo);
						}
					}
				}
				if(StringUtils.isEmpty(companyName)){
					response.setCompanyName("");
				}else{
					response.setCompanyName(companyName);
				}

				//4.保存定位点的北斗检测数据，发送到kafka
				//4.1 构建结果数据
				BDCheckLocationsKudu result = new BDCheckLocationsKudu();
				BeanUtils.copyProperties(ln, result);
				BeanUtils.copyProperties(response, result);
				result.setDateStr(DateUtil.getDateString(ln.getLocTime()));
				result.setAuxsNormal(ln.getAuxsNormal()==null?"":JSON.toJSONString(ln.getAuxsNormal()));

				//4.2 发送到kafka
				try {
					kafkaTemplate.send(CommonConstant.TOPIC_TERMINAL_CHECK_RESULT, result);
				}catch (Exception e){
					log.error("北斗识别检测结果发送到kafka失败",e);
				}

				//执行数据推送
				log.debug("[BD_CHECK_VEHICLE_TERMINAL]将要推送数据:{}",JSON.toJSONString(response));
				//todo 北斗识别检测的 ws 推送需要改造，暂时先不推送，先调试数据存储
				//执行单终端数据推送
				//stl.sendMessageByDeviceNum(deviceNo, JSON.toJSONString(response));
				//执行全终端数据推送
				//atl.sendAllMessage(JSON.toJSONString(response));
			}

			log.info("kafka vehicle terminal 处理消息成功，共{}条", consumerRecords.size());
			// 手动提交offset
			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("[BD_CHECK_VEHICLE_TERMINAL]消息消费失败 :" + e.getMessage(), e);
		}
	}

}
