<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bdCheck.mapper.impala.BDCheckLocationsMapper">

    <select id="getCheckDateList" resultType="string">
        select distinct date_str
        from bd_check_locations
        where device_no = #{terminalNo}
    </select>
</mapper>
