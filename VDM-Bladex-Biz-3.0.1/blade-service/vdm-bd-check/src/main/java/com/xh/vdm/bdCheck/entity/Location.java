package com.xh.vdm.bdCheck.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 轨迹表：新，在动态监控平台中使用此对象
 * <AUTHOR>
 * @date 2022/1/20 14:50
 */
@Data
public class Location {

	//设备ID
	@JsonProperty("device_id")
	private Long deviceId;

	//设备类型
	@JsonProperty("device_type")
	private Integer deviceType;

	//设备型号
	@JsonProperty("device_model")
	private String deviceModel;

	//设备编号
	//终端入网之前进行测试时，使用该字段进行表示
	@JsonProperty("device_no")
  	private String deviceNo;

	//目标ID
	@JsonProperty("target_id")
	private Long targetId;

	//目标类型
	@JsonProperty("target_type")
	private Integer targetType;

	//目标名称
	@JsonProperty("target_name")
	private String targetName;

	//赋码号
	@JsonProperty("device_num")
	private String deviceNum;

	//经度
	private Double longitude;

	//纬度
	private Double latitude;

	//高程
	private Long altitude;

	//卫星速度，单位km/h
	private Double speed;

	//方位角
	private Long bearing;


	//告警标志
	@JsonProperty("alarm_flag")
	private Long alarmFlag;


	//状态标志
	@JsonProperty("state_flag")
	private Long stateFlag;

	//定位时间
	@JsonProperty("loc_time")
	private Long locTime;


	//接收时间
	@JsonProperty("recv_time")
	private Long recvTime;

	// 定位有效性，0-无效，1-有效   2-纠偏点   3-漂移点（非法点）
	private Integer valid;

	//终端上传里程
	private Double mileage;

	//GNSS定位卫星数
	private Integer gnssNum;

	//无线通信网络信号强度
	private Integer wireless;

	//行驶记录仪速度
	@JsonProperty("recv_speed")
	private Double realSpeed;

	// 扩展车辆信号状态位
	@JsonProperty("expand_signal")
	private Long expandSignal;

	//IO状态位
	@JsonProperty("io_status")
	private Long ioStatus;

	//温度
	private String temperature;

	//0-位置上报，1-定位信息批量上传,正常批量汇报，2-定位信息批量上传,盲区补报
	private Integer batch;

	//附加信息
	@JsonProperty("aux_str")
	private String auxStr;

	//终端使用的星历数据
	@JsonProperty("auxs_normal")
	private Map<Integer,Object> auxsNormal;

	//告警唯一Id
	@JsonProperty("dsm_unique_id")
	private String dsmUniqueId;

	//在线状态0-离线，1-在线
	@JsonProperty("te_state")
	private Integer teState;

	@JsonProperty("loc_time_f")
	private Date locTimeF;

	@JsonProperty("recv_time_f")
	private Date recvTimeF;

	@JsonProperty("off_line_time")
	private Date offLineTime;

}
