package com.xh.vdm.bdCheck.checkCore;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonPrimitive;
import com.xh.vdm.bdCheck.entity.BdmSatellite;
import com.xh.vdm.bdCheck.entity.BdmSatelliteHeader;
import com.xh.vdm.bdCheck.service.IBdmSatelliteService;
import com.xh.vdm.bdCheck.util.FtpUtil;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.net.ftp.FTPClient;
import org.springblade.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 星历数据处理
 */
@Service
@Slf4j
public class SatelliteDateService {


	@Value("${minioGJD.BucketName}")
	private String bucketName;

	@Value("${minioGJD.Endpoint}")
	private String endpoint;

	@Value("${minioGJD.AccessKey}")
	private String accessKey;

	@Value("${minioGJD.SecretKey}")
	private String secretKey;

	@Value("${minioGJD.proxy-prefix}")
	private String minioProxyPrefix;

	private MinioClient minioClient;

	@Resource
	private IBdmSatelliteService satelliteService;

	@Resource
	private FtpUtil ftpUtil;

	@Value("${ftp.localTmpPath}")
	private String ftpLocationTmpPath;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	//ftp目录
	@Value("${ftp.path}")
	private String path;

	@Resource
	private BDCheckUtil bdCheckUtil;

	@PostConstruct
	public void init() {
		minioClient = MinioClient.builder()
			.endpoint(endpoint)
			.credentials(accessKey, secretKey)
			.build();
	}


	/**
	 * 定时更新最新的星历数据
	 * 每1个小时执行一次
	 */
	@Scheduled(cron = "0 0 0/1 * * ?")
	public void handleSatelliteSchedule() {

		try{
			//为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
			Object executingFlag = stringRedisTemplate.opsForValue().get(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.UPDATE_BD_SATELLITES_DATA);
			if(executingFlag != null && CommonConstant.COMMON_TRUE.equals(executingFlag.toString())){
				//如果已经有跑批程序在执行，则不再执行
				log.info("[车辆行驶时长统计]跑批任务已经在执行，请等当前执行程序完成后再试");
				return ;
			}

			//获取执行权限之后，添加执行标记（有效期为1个小时）
			synchronized (this){
				stringRedisTemplate.opsForValue().set(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.UPDATE_BD_SATELLITES_DATA , CommonConstant.COMMON_TRUE , 1 , TimeUnit.HOURS);
			}


			Calendar cal = Calendar.getInstance();
			String year = "" + cal.get(Calendar.YEAR);
			boolean res = handleSatelliteDate(year, null);
			if (res) {
				log.info("定时更新星历数据成功");
			} else {
				log.info("定时更新星历数据失败");
			}
		}catch (Exception e){
			log.error("定时任务【定时更新星历数据成功】处理失败，", e);
		}finally {
			//执行完毕后，解除执行锁定
			synchronized (this){
				stringRedisTemplate.delete(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.UPDATE_BD_SATELLITES_DATA );
			}
		}

	}


	/**
	 * 处理星历数据
	 * 下载星历文件，从星历文件中解析星历数据，保存到数据库
	 * 保存到数据库中，存在即更新
	 *
	 * @param year     指定年份
	 * @param fileName 指定文件名称，可以不填
	 */
	public boolean handleSatelliteDate(String year, String fileName) {

		String localFilePath = downloadSatelliteFileFromMinio(year, fileName);

		//2.解析星历文件
		if (StringUtils.isEmpty(localFilePath)) {
			//如果下载星历数据不成功
			log.info("星历数据文件下载不成功，下载文件为空");
			return false;
		}
		Map<String, BdmSatellite> satelliteMap = new HashMap<>();
		try {
			satelliteMap = readSatelliteData(localFilePath);
		} catch (Exception e) {
			log.error("解析星历数据失败", e);
			return false;
		}

		//3.保存星历数据
		try {
			//设置批次号
			Collection<BdmSatellite> set = satelliteMap.values();
			long batchId = System.currentTimeMillis();
			set.forEach(item -> {
				item.setBatchId(batchId);
			});
			List<BdmSatellite> list = new ArrayList<>();
			list.addAll(set);
			satelliteService.saveOrUpdateSatelliteBatch(list);
		} catch (Exception e) {
			log.error("保存星历数据失败", e);
			return false;
		}

		//4.todo 添加生成卫星CZML的逻辑


		//5.删除本地临时文件
		File file = new File(localFilePath);
		if (file != null && file.isFile()) {
			file.delete();
		}
		return true;
	}


	private String downloadSatelliteFileFromMinio(String year, String fileName){
		//1.下载星历文件（minio），下载最新的
		FTPClient client = null;
		String localFilePath = "";
		String pathF = "";
		try {
			//1.1 创建ftp连接
			client = ftpUtil.connectFtp();
			//1.2 下载文件到本地
			String fileNameF = "";
			if (StringUtils.isEmpty(fileName)) {
				log.info("未指定要更新的文件，将更新最新的文件");
				//如果不指定文件,则下载最新的文件
				//因为ftp中有两类文件，这里只使用tarc开头的文件
				List<String> fileList = ftpUtil.getFileNameList(client, path + year);
				log.info("查询ftp中的文件，共查询到文件{}个", fileList==null?0:fileList.size());
				//取文件名称最大的
				Collections.sort(fileList, (fileName1, fileName2) -> fileName2.compareTo(fileName1));
				if (fileList == null || fileList.size() < 1) {
					log.error("下载星历数据，ftp上未查询到文件");
					return null;
				}
				String newestFile = fileList.get(0);
				fileNameF = newestFile;
				log.info("将要更新的文件的名称为：{}", fileNameF);
			} else {
				//如果指定文件,则下载指定的文件
				fileNameF = fileName;
				log.info("指定了要更新的文件，文件名称为：{}", fileNameF);
			}
			pathF = path + year;
			log.info("将要下载星历文件");
			localFilePath = ftpUtil.downloadFile(client, pathF, fileNameF, ftpLocationTmpPath);
			log.info("下载星历数据{}成功", pathF + fileNameF);
		} catch (Exception e) {
			log.error("操作ftp下载星历数据失败", e);
			return null;
		} finally {
			if (client != null) {
				ftpUtil.closeFtpClient(client);
			}
		}
		return localFilePath;
	}

	/**
	 * @description: 通过ftp下载星历数据
	 * @author: zhouxw
	 * @date: 2025-06-162 17:58:16
	 * @param: [year, fileName]
	 * @return: java.lang.String
	 **/
	private String downloadSatelliteFileFromFtp(String year, String fileName){
		//1.下载星历文件（ftp），下载最新的
		FTPClient client = null;
		String localFilePath = "";
		String pathF = "";
		try {
			//1.1 创建ftp连接
			client = ftpUtil.connectFtp();
			//1.2 下载文件到本地
			String fileNameF = "";
			if (StringUtils.isEmpty(fileName)) {
				log.info("未指定要更新的文件，将更新最新的文件");
				//如果不指定文件,则下载最新的文件
				//因为ftp中有两类文件，这里只使用tarc开头的文件
				List<String> fileList = ftpUtil.getFileNameList(client, path + year);
				log.info("查询ftp中的文件，共查询到文件{}个", fileList==null?0:fileList.size());
				//取文件名称最大的
				Collections.sort(fileList, (fileName1, fileName2) -> fileName2.compareTo(fileName1));
				if (fileList == null || fileList.size() < 1) {
					log.error("下载星历数据，ftp上未查询到文件");
					return null;
				}
				String newestFile = fileList.get(0);
				fileNameF = newestFile;
				log.info("将要更新的文件的名称为：{}", fileNameF);
			} else {
				//如果指定文件,则下载指定的文件
				fileNameF = fileName;
				log.info("指定了要更新的文件，文件名称为：{}", fileNameF);
			}
			pathF = path + year;
			log.info("将要下载星历文件");
			localFilePath = ftpUtil.downloadFile(client, pathF, fileNameF, ftpLocationTmpPath);
			log.info("下载星历数据{}成功", pathF + fileNameF);
		} catch (Exception e) {
			log.error("操作ftp下载星历数据失败", e);
			return null;
		} finally {
			if (client != null) {
				ftpUtil.closeFtpClient(client);
			}
		}
		return localFilePath;
	}

	/**
	 * 读取星历数据头信息
	 * @param filePath
	 * @return key = num + "~" + timeFlag + "~" + type;
	 */
	public static Map<String, BdmSatelliteHeader> readSatelliteHeader(String filePath){
		LineIterator it = null;
		Map<String,BdmSatelliteHeader> map = new HashMap<>();
		File file = null;
		try{
			//1.加载文件
			file = new File(filePath);
			it = FileUtils.lineIterator(file, "UTF-8");
			int index = 0;
			//2.读取消息头信息
			//从第四行开始读取
			while(it.hasNext()){
				if(index < 3){
					it.nextLine();
					index++;
					continue;
				}
				String content = it.nextLine();
				if(content.contains("END OF HEADER")){
					//结束数据读取
					break;
				}
				if(StringUtils.isEmpty(content)){
					continue;
				}
				String[] array = content.split("\\s+");
				if(array.length == 8){
					String[] arr = new String[9];
					arr[0] = array[0];
					arr[1] = array[1];
					arr[2] = array[2];
					arr[3] = array[3];
					arr[4] = "";
					arr[5] = array[4];
					arr[6] = array[5];
					arr[7] = array[6];
					arr[8] = array[7];
					array = arr;
				}
				//校正类型
				String type = array[0].trim();
				//参数
				String data1 = array[1].trim();
				String data2 = array[2].trim();
				String data3 = array[3].trim();
				String data4 = array[4].trim();
				//时间标记
				String timeFlag = array[5].trim();
				//星号
				String num = array[6].trim();
				//备注
				String comment1 = array[7].trim();
				String comment2 = array[8].trim();

				//整理数据
				String key = num + "~" + timeFlag + "~" + type;
				BdmSatelliteHeader header = new BdmSatelliteHeader();
				header.setNum(num);
				header.setType(type);
				header.setData1(data1);
				header.setData2(data2);
				header.setData3(data3);
				header.setData4(data4);
				header.setTimeFlag(timeFlag);
				header.setComment1(comment1);
				header.setComment2(comment2);
				map.put(key, header);

				index ++;
			}
			return map;
		}catch (Exception e){
			log.error("读取星历数据头信息失败",e);
			return null;
		}finally {
			if(it != null){
				try {
					it.close();
				} catch (IOException e) {
					throw new RuntimeException(e);
				}
			}
		}
	}


	public static Map<String, BdmSatellite> readSatelliteData(String fileName) {
		System.out.println("---------------//////");
		String line = "";
		JSONObject jsonObject = null;
		LineIterator it = null;
		try {
			System.out.println("  文件开始读取==========");
			File file = new File(fileName);
			it = FileUtils.lineIterator(file, "UTF-8");

			Map<String, BdmSatellite> stringListHashMap = new HashMap<>();
			int i = 1;
			BdmSatellite bdCheck = new BdmSatellite();
			boolean isData = false;
			while (it.hasNext()) {
				line = it.nextLine();
				String linePureData = line.trim();
				if (!linePureData.startsWith("C") && !isData) {
					continue;
				}
				isData = true;
				if (i % 8 == 1) {
					String seconds = "";
					String sec = "";
					String sec2 = "";
					String flag = "";
					//卫星标志
					String flag2 = line.substring(0, 1);
					bdCheck.setFlag(flag2);
					//卫星号
					String num = line.substring(1, 3);
					bdCheck.setNum(num);
					// 年
					String year = line.substring(4, 8);
					bdCheck.setYear(year);
					// 月
					String month = line.substring(9, 11);
					bdCheck.setMon(month);
					// 日
					String day = line.substring(12, 14);
					bdCheck.setDay(day);
					// 时
					String hour = line.substring(15, 17);
					bdCheck.setHour(hour);
					// 分
					String minute = line.substring(18, 20);
					bdCheck.setMin(minute);
					// 秒
					String se = line.substring(21, 23);
					bdCheck.setSec(se);
					// a0 卫星钟偏系数(seconds)
					flag = line.substring(23, 24);
					if (flag.equals("-")) {
						seconds = line.substring(23, 42);
					} else {
						seconds = line.substring(24, 42);
					}
					bdCheck.setA0(seconds);
					// a1 卫星钟漂系数(sec/sec)
					flag = line.substring(42, 43);
					if (flag.equals("-")) {
						sec = line.substring(42, 61);
					} else {
						sec = line.substring(43, 61);
					}
					bdCheck.setA1(sec);
					// a2 卫星钟漂移率系数 (sec/sec2)
					flag = line.substring(61, 62);
					if (flag.equals("-")) {
						sec2 = line.substring(61);
					} else {
						sec2 = line.substring(62);
					}
					bdCheck.setA2(sec2);
				} else if (i % 8 == 2) {
					String substring = line.substring(4, 5);
					String two_one = "";
					String two_two = "";
					String two_the = "";
					String two_fou = "";
					if (substring.equals("-")) {
						two_one = line.substring(4, 23);
					} else {
						two_one = line.substring(5, 23);
					}
					substring = line.substring(23, 24);
					if (substring.equals("-")) {
						two_two = line.substring(23, 42);
					} else {
						two_two = line.substring(24, 42);
					}
					substring = line.substring(42, 43);
					if (substring.equals("-")) {
						two_the = line.substring(42, 61);
					} else {
						two_the = line.substring(43, 61);
					}
					substring = line.substring(61, 62);
					if (substring.equals("-")) {
						two_fou = line.substring(61);
					} else {
						two_fou = line.substring(62);
					}
					bdCheck.setIode(two_one);
					bdCheck.setCrs(two_two);
					bdCheck.setDeltan(two_the);
					bdCheck.setM0(two_fou);
				} else if (i % 8 == 3) {
					String substring = line.substring(4, 5);
					String two_one = "";
					String two_two = "";
					String two_the = "";
					String two_fou = "";
					if (substring.equals("-")) {
						two_one = line.substring(4, 23);
					} else {
						two_one = line.substring(5, 23);
					}
					substring = line.substring(23, 24);
					if (substring.equals("-")) {
						two_two = line.substring(23, 42);
					} else {
						two_two = line.substring(24, 42);
					}
					substring = line.substring(42, 43);
					if (substring.equals("-")) {
						two_the = line.substring(42, 61);
					} else {
						two_the = line.substring(43, 61);
					}
					substring = line.substring(61, 62);
					if (substring.equals("-")) {
						two_fou = line.substring(61);
					} else {
						two_fou = line.substring(62);
					}
					bdCheck.setCuc(two_one);
					bdCheck.setE(two_two);
					bdCheck.setCus(two_the);
					bdCheck.setRootA(two_fou);
				} else if (i % 8 == 4) {
					String substring = line.substring(4, 5);
					String two_one = "";
					String two_two = "";
					String two_the = "";
					String two_fou = "";
					if (substring.equals("-")) {
						two_one = line.substring(4, 23);
					} else {
						two_one = line.substring(5, 23);
					}
					substring = line.substring(23, 24);
					if (substring.equals("-")) {
						two_two = line.substring(23, 42);
					} else {
						two_two = line.substring(24, 42);
					}
					substring = line.substring(42, 43);
					if (substring.equals("-")) {
						two_the = line.substring(42, 61);
					} else {
						two_the = line.substring(43, 61);
					}
					substring = line.substring(61, 62);
					if (substring.equals("-")) {
						two_fou = line.substring(61);
					} else {
						two_fou = line.substring(62);
					}
					bdCheck.setToe(two_one);
					bdCheck.setCic(two_two);
					bdCheck.setOmega0(two_the);
					bdCheck.setCis(two_fou);
				} else if (i % 8 == 5) {
					String substring = line.substring(4, 5);
					String two_one = "";
					String two_two = "";
					String two_the = "";
					String two_fou = "";
					if (substring.equals("-")) {
						two_one = line.substring(4, 23);
					} else {
						two_one = line.substring(5, 23);
					}
					substring = line.substring(23, 24);
					if (substring.equals("-")) {
						two_two = line.substring(23, 42);
					} else {
						two_two = line.substring(24, 42);
					}
					substring = line.substring(42, 43);
					if (substring.equals("-")) {
						two_the = line.substring(42, 61);
					} else {
						two_the = line.substring(43, 61);
					}
					substring = line.substring(61, 62);
					if (substring.equals("-")) {
						two_fou = line.substring(61);
					} else {
						two_fou = line.substring(62);
					}
					bdCheck.setI0(two_one);
					bdCheck.setCrc(two_two);
					bdCheck.setOmega(two_the);
					bdCheck.setOmegadot(two_fou);
				} else if (i % 8 == 6) {
					String substring = line.substring(4, 5);
					String two_one = "";
					String two_two = "";
					String two_the = "";
					String two_fou = "";
					if (substring.equals("-")) {
						two_one = line.substring(4, 23);
					} else {
						two_one = line.substring(5, 23);
					}
					substring = line.substring(23, 24);
					if (substring.equals("-")) {
						two_two = line.substring(23, 42);
					} else {
						two_two = line.substring(24, 42);
					}
					substring = line.substring(42, 43);
					if (substring.equals("-")) {
						two_the = line.substring(42, 61);
					} else {
						two_the = line.substring(43, 61);
					}
					substring = line.substring(61, 62);
					if (substring.equals("-")) {
						two_fou = line.substring(61);
					} else {
						two_fou = line.substring(62);
					}
					bdCheck.setIdot(two_one);
					bdCheck.setData(two_two);
					bdCheck.setBdt(two_the);
					bdCheck.setAdot(two_fou);
				} else if (i % 8 == 7) {
					String substring = line.substring(4, 5);
					String two_one = "";
					String two_two = "";
					String two_the = "";
					String two_fou = "";
					if (substring.equals("-")) {
						two_one = line.substring(4, 23);
					} else {
						two_one = line.substring(5, 23);
					}
					substring = line.substring(23, 24);
					if (substring.equals("-")) {
						two_two = line.substring(23, 42);
					} else {
						two_two = line.substring(24, 42);
					}
					substring = line.substring(42, 43);
					if (substring.equals("-")) {
						two_the = line.substring(42, 61);
					} else {
						two_the = line.substring(43, 61);
					}
					substring = line.substring(61, 62);
					if (substring.equals("-")) {
						two_fou = line.substring(61);
					} else {
						two_fou = line.substring(62);
					}
					bdCheck.setSv(two_one);
					bdCheck.setHs(two_two);
					bdCheck.setTgd(two_the);
					bdCheck.setIsc(two_fou);
				} else if (i % 8 == 0) {
					String substring = line.substring(4, 5);

					String two_one = "";
					String two_two = "";
					if (substring.equals("-")) {
						two_one = line.substring(4, 23);
					} else {
						two_one = line.substring(5, 23);
					}
					substring = line.substring(23, 24);
					if (substring.equals("-")) {
						two_two = line.substring(23, 42);
					} else {
						two_two = line.substring(24, 42);
					}
					bdCheck.setSow(two_one);
					bdCheck.setIodc(two_two);
					String key = bdCheck.getFlag() + bdCheck.getNum() + bdCheck.getYear() + bdCheck.getMon() + bdCheck.getDay() + bdCheck.getHour();
					stringListHashMap.put(key, bdCheck);
					bdCheck.setKey(key);
					bdCheck.setCreateTime(new Date());
					bdCheck = new BdmSatellite();
				}
				i++;
			}
			return stringListHashMap;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (it != null) {
				try {
					it.close();
				} catch (Exception e) {
				}
			}
		}
		return null;
	}

	/**
	 * 从数据库中获取星历数据
	 * 星历数据转 xyz 坐标
	 * xyz 转 经纬度、高程
	 * 生成 CZML 数据
	 */
	public JsonObject generateCzml() {
		List<BdmSatellite> list = satelliteService.dataInfo();
		JsonObject czmlObject = new JsonObject();
		JsonArray czmlArray = new JsonArray();
		Map<String, List<JsonObject>> positionsMap = new HashMap<>();
		Integer seconds = 3600;

		try {
			for (int i = 0; i < list.size(); i++) {
				BdmSatellite satellite = list.get(i);
				String id = satellite.getNum();

				double[] xyzB1I = BDCheckUtil.xyzB1I(satellite, new BigDecimal(BDCheckUtil.calculationTT(LocalDateTime.ofInstant(satellite.getLocTime().toInstant(), ZoneOffset.UTC))));
				double[] b1IToE = BDCheckUtil.b1IToE(satellite, new BigDecimal(BDCheckUtil.calculationTT(LocalDateTime.ofInstant(satellite.getLocTime().toInstant(), ZoneOffset.UTC))), xyzB1I[0], xyzB1I[1], xyzB1I[2]);
				double[] xyzToLatLonAlt = BDCheckUtil.xyzToLatLonAlt(xyzB1I[0], xyzB1I[1], xyzB1I[2]);

				JsonArray cartographicDegrees = new JsonArray();
				cartographicDegrees.add(xyzToLatLonAlt[0]);
				cartographicDegrees.add(xyzToLatLonAlt[1]);
				cartographicDegrees.add(xyzToLatLonAlt[2]);

				JsonArray cartesianDegrees = new JsonArray();
				int index = positionsMap.containsKey(id) ? positionsMap.get(id).size() : 0;
				cartesianDegrees.add(seconds * index);
				cartesianDegrees.add(b1IToE[0]);
				cartesianDegrees.add(b1IToE[1]);
				cartesianDegrees.add(b1IToE[2]);

				JsonObject czmlItem = new JsonObject();
				czmlItem.addProperty("id", id);
				czmlItem.add("position", cartographicDegrees);
				czmlItem.add("cartesian", cartesianDegrees);
				czmlItem.addProperty("locTime", satellite.getLocTime().toString());

				if (!positionsMap.containsKey(id)) {
					positionsMap.put(id, new ArrayList<>());
				}
				positionsMap.get(id).add(czmlItem);
			}

			DateTimeFormatter originalDateFormat = DateTimeFormatter.ofPattern("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
			DateTimeFormatter targetDateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSSSS'Z'").withZone(ZoneOffset.UTC);

			for (Map.Entry<String, List<JsonObject>> entry : positionsMap.entrySet()) {
				List<JsonObject> positions = entry.getValue();
				JsonArray leadTimeArray = new JsonArray();
				JsonArray trailTimeArray = new JsonArray();
				JsonArray positionsArray = new JsonArray();
				JsonArray cartesianArray = new JsonArray();

				for (int i = 1; i < positions.size(); i++) {
					JsonObject currentPos = positions.get(i);
					JsonObject prevPos = positions.get(i - 1);

					String currentLocTime = currentPos.get("locTime").getAsString();
					String prevLocTime = prevPos.get("locTime").getAsString();

					try {
						LocalDateTime currentDateTime = LocalDateTime.parse(currentLocTime, originalDateFormat);
						ZonedDateTime currentZonedDateTime = ZonedDateTime.ofInstant(currentDateTime.toInstant(ZoneOffset.UTC), ZoneId.of("UTC"));
						String currentLocalDateStr = targetDateFormat.format(currentZonedDateTime.withZoneSameInstant(ZoneOffset.UTC));

						LocalDateTime prevDateTime = LocalDateTime.parse(prevLocTime, originalDateFormat);
						ZonedDateTime prevZonedDateTime = ZonedDateTime.ofInstant(prevDateTime.toInstant(ZoneOffset.UTC), ZoneId.of("UTC"));
						String prevLocDateStr = targetDateFormat.format(prevZonedDateTime.withZoneSameInstant(ZoneOffset.UTC));

						JsonObject leadTimeObj = new JsonObject();
						leadTimeObj.addProperty("interval", prevLocDateStr + "/" + currentLocalDateStr);
						leadTimeObj.addProperty("epoch", formatInstantToISO8601(prevZonedDateTime.toInstant()));
						leadTimeArray.add(leadTimeObj);

						JsonObject trailTimeObj = new JsonObject();
						trailTimeObj.addProperty("interval", prevLocDateStr + "/" + currentLocalDateStr);
						trailTimeObj.addProperty("epoch", formatInstantToISO8601(prevZonedDateTime.toInstant()));
						trailTimeArray.add(trailTimeObj);

						positionsArray.add(prevPos.get("position").getAsJsonArray());

						cartesianArray.add(prevPos.get("cartesian").getAsJsonArray());


						if (i == (positions.size() - 1)) {
							positionsArray.add(currentPos.get("position").getAsJsonArray());

							cartesianArray.add(currentPos.get("cartesian").getAsJsonArray());

						}
					} catch (DateTimeParseException e) {
						log.info("日期解析失败：" + e.getMessage());
					}
				}

				JsonObject czmlItem = new JsonObject();
				czmlItem.addProperty("id", entry.getKey());
				czmlItem.add("type", new JsonPrimitive(BDCheckUtil.SATE_TYPE.get(entry.getKey())));
				czmlItem.add("leadTime", leadTimeArray);
				czmlItem.add("trailTime", trailTimeArray);
				czmlItem.add("positions", positionsArray);
				czmlItem.add("cartesian", cartesianArray);
				czmlArray.add(czmlItem);
			}
		} catch (Exception e) {
			log.info("日期解析失败：" + e.getMessage());
		}

		czmlObject.add("data", czmlArray);
		return czmlObject;
	}

	public static String formatInstantToISO8601(Instant instant) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
			.withZone(ZoneId.of("UTC"));
		return formatter.format(instant);
	}

}
