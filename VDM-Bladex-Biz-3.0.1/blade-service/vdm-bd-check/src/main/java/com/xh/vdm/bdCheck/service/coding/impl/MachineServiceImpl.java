package com.xh.vdm.bdCheck.service.coding.impl;

import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bdCheck.constant.BdCheckConstant;
import com.xh.vdm.bdCheck.entity.coding.Machine;
import com.xh.vdm.bdCheck.mapper.coding.MachineMapper;
import com.xh.vdm.bdCheck.service.coding.IMachineService;
import com.xh.vdm.bdCheck.util.SM2Util;
import com.xh.vdm.bdCheck.vo.coding.request.MachineRequest;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.stereotype.Service;

import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

@Slf4j
@Service("MachineService")
public class MachineServiceImpl extends ServiceImpl<MachineMapper, Machine> implements IMachineService {

	private final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	private byte transEnableToDisabled (byte enable) {
		if (enable == BdCheckConstant.CODING_MACHINE_ENABLE_NO) {
			return BdCheckConstant.CODING_MACHINE_DISABLED_YES;
		}

		return BdCheckConstant.CODING_MACHINE_DISABLED_NO;
	}

	@Override
	public IPage<Machine> getCodingMachinePage (MachineRequest request, Query query) throws ParseException {
		IPage<Machine> page = Condition.getPage(query);
		LambdaQueryWrapper<Machine> wrapper = Wrappers.lambdaQuery(Machine.class);
		wrapper.orderByDesc(Machine::getCreateTime);
		if (StringUtils.isNotBlank(request.getCodeMachineNum())) {
			wrapper.like(Machine::getNumber, request.getCodeMachineNum());
		}
		if (StringUtils.isNotBlank(request.getManufacturer())) {
			wrapper.eq(Machine::getVendor, request.getManufacturer());
		}
		if (StringUtils.isNotBlank(request.getStartTime())) {
			wrapper.ge(Machine::getCreateTime, this.format.parse(request.getStartTime()));
		}
		if (StringUtils.isNotBlank(request.getEndTime())) {
			wrapper.le(Machine::getCreateTime, this.format.parse(request.getEndTime()));
		}
		if (request.getEnable() != null) {
			wrapper.eq(Machine::getDisabled, this.transEnableToDisabled(request.getEnable()));
		}

		this.page(page, wrapper);
		return page;
	}

	@Override
	public R<String> addCodingMachine (MachineRequest request) throws NoSuchAlgorithmException {
		LambdaQueryWrapper<Machine> wrapper = Wrappers.lambdaQuery(Machine.class);
		wrapper.eq(Machine::getNumber, request.getCodeMachineNum());
		if (this.count(wrapper) > 0) {
			return R.fail(ResultCode.FAILURE, "赋码机编号已存在。");
		}

		// Go在使用SM2的公私钥时，会认为公钥开头的04、私钥开头的00已经去掉，因此，这里要先把04、00去掉再存入数据库。
		// 但Java这边，坑爹的SM2Util生成的私钥，时而会在开头带上00（共66个字符串），时而又不带（共64个字符串）。公钥暂时还是乖乖的固定130个字符且以04开头。
		// 为了对付SM2Util这种坑爹设定，故做以下处理。
		Map<String, String> keyPair = SM2Util.createKeyPair();
		String privateKey = keyPair.get("private_key");
		String publicKey = keyPair.get("public_key");
		int privateKeySize = privateKey.length();
		int publicKeySize = publicKey.length();
		if ((privateKeySize < 64) || (privateKeySize > 66) || (privateKeySize == 65)) {
			log.error("invalid size of private key when add coding machine: {}", privateKey);
			return R.fail(ResultCode.FAILURE, "赋码机私钥位数异常。");
		}
		if ((privateKeySize == 66) && (!privateKey.startsWith("00"))) {
			log.error("invalid private key when add coding machine: {}", privateKey);
			return R.fail(ResultCode.FAILURE, "赋码机私钥格式异常。");
		}
		if ((privateKeySize == 66) && privateKey.startsWith("00")) {
			privateKey = privateKey.substring(2);
		}
		if ((publicKeySize < 128) || (publicKeySize > 130) || (publicKeySize == 129)) {
			log.error("invalid size of public key when add coding machine: {}", publicKey);
			return R.fail(ResultCode.FAILURE, "赋码机公钥位数异常。");
		}
		if ((publicKeySize == 130) && (!publicKey.startsWith("04"))) {
			log.error("invalid public key when add coding machine: {}", publicKey);
			return R.fail(ResultCode.FAILURE, "赋码机公钥格式异常。");
		}
		if ((publicKeySize == 130) && publicKey.startsWith("04")) {
			publicKey = publicKey.substring(2);
		}

		Date date = new Date();
		Machine machine = new Machine();
		machine.setNumber(request.getCodeMachineNum());
		machine.setPassword(SecureUtil.md5(request.getPassword() + this.format.format(date)));
		machine.setPrivateKey(privateKey);
		machine.setPublicKey(publicKey);
		machine.setVendor(request.getManufacturer());
		machine.setCreateTime(date);
		machine.setDisabled(BdCheckConstant.CODING_MACHINE_DISABLED_NO);
		this.save(machine);
		return R.data(ResultCode.SUCCESS.getCode(), machine.getNumber(),"添加成功。");
	}

	@Override
	public R<String> editCodingMachine (MachineRequest request) {
		LambdaQueryWrapper<Machine> wrapper = Wrappers.lambdaQuery(Machine.class);
		wrapper.eq(Machine::getNumber, request.getCodeMachineNum());
		Machine tmp = this.getOne(wrapper);
		if ((tmp == null) || (tmp.getCreateTime() == null)) {
			return R.fail(ResultCode.FAILURE, "赋码机信息获取异常。");
		}

		Machine machine = new Machine();
		if (StringUtils.isNotBlank(request.getPassword())) {
			machine.setPassword(SecureUtil.md5(request.getPassword() + this.format.format(tmp.getCreateTime())));
		}
		if (StringUtils.isNotBlank(request.getManufacturer())) {
			machine.setVendor(request.getManufacturer());
		}
		if (request.getEnable() != null) {
			machine.setDisabled(this.transEnableToDisabled(request.getEnable()));
		}

		this.update(machine, wrapper);
		return R.data(ResultCode.SUCCESS.getCode(), new CompareUtils<Machine>().compare(tmp, machine),"更新成功。");
	}

	@Override
	public String loginCodingMachine (MachineRequest request) throws Exception {
		Machine machine = this.getOne(
			Wrappers.lambdaQuery(Machine.class)
				.eq(Machine::getNumber, request.getCodeMachineNum())
				.eq(Machine::getDisabled, BdCheckConstant.CODING_MACHINE_DISABLED_NO)
		);
		if (machine == null) {
			throw new Exception("赋码机不存在或已禁用。");
		}
		if (
			machine.getPassword().equals(
				SecureUtil.md5(request.getPassword() + this.format.format(machine.getCreateTime()))
			)
		) {
			return machine.getPublicKey();
		} else {
			throw new Exception("赋码机登录密码不正确。");
		}
	}
}
