package com.xh.vdm.bdCheck.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 星历数据 , xyz数据 , 经纬度高程
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmSatelliteResponse implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
	 * 主键
     * 星历数据标识，方便后续取星历数据
     */
	@TableId
    private String key;

    /**
     * 卫星标志，C
     */
    private String flag;

    /**
     * 卫星号
     */
    private String num;

    private String year;

    private String mon;

    /**
     * 日
     */
    private String day;

    /**
     * 时
     */
    private String hour;

    private String min;

    /**
     * 秒
     */
    private String sec;

    /**
     * 卫星钟偏系数(seconds)
     */
    private String a0;

    /**
     * 卫星钟漂系数(sec/sec)
     */
    private String a1;

    /**
     * 卫星钟漂移率系数 (sec/sec2)
     */
    private String a2;

    /**
     * 星历参数版本号：对应 B1C、B2a 的导航电文
     */
    private String iode;

    /**
     * 轨道半径的正弦调和改正项的振幅 (meters)
     */
    private String crs;

    /**
     * 参考时刻卫星平均角速度与计算值之差 (radians/sec)
     */
    private String deltan;

    /**
     * 参考时刻的平近点角 (radians)
     */
    private String m0;

    /**
     * 纬度幅角的余弦调和改正项的振幅(radians)
     */
    private String cuc;

    /**
     * 偏心率
     */
    private String e;

    /**
     * 纬度幅角的正弦调和改正项的振幅(radians)
     */
    private String cus;

    /**
     * 参考时刻长半轴相对于参考值的偏差：对应 BIC/B3C 的导航电文
	 * 计算公式中的 根号A
     */
    private String rootA;

    /**
     * 星历参考时刻 (sec,BDT)
     */
    private String toe;

    /**
     * 轨道倾角的余弦调和改正项的振幅(radians)
     */
    private String cic;

    /**
     * 周历元零时刻计算的升交点经度(radians)
     */
    private String omega0;

    /**
     * 轨道倾角的正弦调和改正项的振幅(radians)
     */
    private String cis;

    /**
     * 参考时刻的轨道倾角 (radians)
     */
    private String i0;

    /**
     * 轨道半径的余弦调和改正项的振幅 (meters)
     */
    private String crc;

    /**
     * 近地点幅角 (radians)
     */
    private String omega;

    /**
     * 升交点赤经变化率(radians/sec)
     */
    private String omegadot;

    /**
     * 轨道倾角变化率 (radians/sec)
     */
    private String idot;

    /**
     * 电文来源 (FLOATINTEGER)  Bit 0 “1”: 来自 B1C 的 B-CNAV1  Bit 1 “1”: 来自 B2b 的 B-CNAV1  Bit 2 “1”: 来自 B2a 的 B-CNAV2  Bit 3 “1”: 来自 B2I 播发  Bit 4 “1”: 来自 B1I 播发  Bit 5 “1”: 来自 B3I 播发  或该字段为零，标示来自 B1I 播发
     */
    private String data;

    /**
     * BDS 的整周计数
     */
    private String bdt;

    /**
     * DOT/留空 长半轴变化率 (meters/sec) A DOT: 对应 B1C、B2a 的导航电文 留空： 对应 B1I、B2I、B3I 的导航电文
     */
    private String adot;

    /**
     * 空间信号精度指数及空间信号监测精度指数
     */
    private String sv;

    /**
     * 卫星健康状态
     */
    private String hs;

    /**
     * -TGD/TGD1 (seconds) TGD: 对应 B1C、B2a 的导航电文 导航电文来自 B1C,此字段 TGD_B1Cp 导航电文来自 B2a,此字段为 TGD_B2ap TGD1: 对应 B1I、B2I、B3I 的导航电文的TGD1（B1/B3）
     */
    private String tgd;

    /**
     * -ISC/TGD2 (seconds) ISC：对应 B1C、B2a、B2b 的导航电文 导航电文来自 B1C,此字段为 ISC_B1Cd 导航电文来自 B2a,此字段为 ISC_B2ad TGD2：对应 B1I、B2I、B3I 的导航电文的 TGD2（B2/B3）
     */
    private String isc;

    /**
     * -信息的发射时间（BDT 的周内秒）**）对于 B-CNAV1 电文，此字段由子帧 1 播 发的 HOW（周内小时计数）和 SOH（小时内秒计数）计算而来，即 HOW*3600+SOH对于 B-CNAV2 电文，此字段为 B-CNAV2信息类型 10 中播发的 SOW对于 B1I、B2I、B3I 的导航电文，此字段为 D1 或 D2 导航电文子帧 1 中播发的SOW
     */
    private String sow;

    /**
     * -IODC/AODC:钟差参数版本号/时钟数据龄期IODC:对应 B1C、B2a 的导航电文AODC:对应 B1I、B2I、B3I 的导航电文
     */
    private String iodc;

    /**
     * -Delta n0 DOT 参考时刻卫星平均角速度与计算值之差的变化率(radians/sec2)
     */
    private String deltan0dot;

    /**
     * -SatType/留空 卫星轨道类型SatType: 对应 B1C、B2a 的导航电文,1:GEO,2:IGSO,3:MEO留空： 对应 B1I、B2I、B3I 的导航电文
     */
    private String sattype;

	private Date createTime;
	//入库批次号（当前时间戳）
	private Long batchId;

	@TableField(exist = false)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date locTime;

	//经度
	private Double longitude;
	//纬度
	private Double latitude;
	//高程
	private Double altitude;
	//x坐标
	private Double xDate;
	//y坐标
	private Double yDate;
	//z坐标
	private Double zDate;


}
