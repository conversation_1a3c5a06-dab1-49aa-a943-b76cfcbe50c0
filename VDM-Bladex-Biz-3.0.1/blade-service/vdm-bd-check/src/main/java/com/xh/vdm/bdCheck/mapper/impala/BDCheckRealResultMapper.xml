<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bdCheck.mapper.impala.BDCheckRealResultMapper">

    <select id="getRealResultPage" resultType="com.xh.vdm.bdCheck.entity.BDCheckRealResult">
        select *
        from bd_check_real_result
        where device_num = #{deviceNum,jdbcType=VARCHAR}
        order by create_time desc
    </select>

    <select id="getRealNonBDPage" resultType="com.xh.vdm.bdCheck.entity.BDCheckRealResult">
        select *
        from bd_check_real_result
        where device_num = #{deviceNum,jdbcType=VARCHAR}
            and check_res = '0'
        order by create_time desc
    </select>

    <select id="getRealCheckAllDeviceNum" resultType="string">
        select distinct device_num from bd_check_real_result
    </select>

    <select id="getRealCheckAllDateByDeviceNum" resultType="string">
        select distinct date_str from bd_check_real_result
        where device_num = #{deviceNum,jdbcType=VARCHAR}
        order by date_str desc
    </select>

    <select id="batchGetRealCheckAllDateByDeviceNum" resultType="com.xh.vdm.bdCheck.vo.BDCheckDateResult">
        select device_num, date_str
        from bd_check_real_result
        where device_num in
        <foreach collection="deviceNums" item="deviceNum" open="(" close=")" separator=",">
            #{deviceNum}
        </foreach>
        group by device_num, date_str
        order by device_num, date_str desc
    </select>

    <select id="getRealCheckCountGE" resultType="long">
        select count(*) from bd_check_real_result
        where date_str >= #{dateStr}
          and device_num = #{deviceNum,jdbcType=VARCHAR}
    </select>

    <select id="getRealCheckNonBDCountGE" resultType="long">
        select count(*) from bd_check_real_result
        where date_str >= #{dateStr}
          and device_num = #{deviceNum,jdbcType=VARCHAR}
        and check_res = '0'
    </select>

    <select id="getRealCheckAllCount" resultType="long">
        select count(*) from locations
        where valid = 1
        and device_num = #{deviceNum,jdbcType=VARCHAR}
    </select>

    <!-- 优化版本：使用GROUP BY替代UNION，性能更优，确保结果一致性 -->
    <select id="batchGetRealCheckCountGE" resultType="com.xh.vdm.bdCheck.vo.BDCheckCountResult">
        <foreach collection="checkDateResultList" item="item" index="idx" separator="UNION ALL">
            SELECT device_num,
            count(*) as total_count,
            sum(case
            when check_res = '0' then 1
            else 0
            end) as non_bd_count
            FROM bd_check_real_result
            WHERE device_num = #{item.deviceNum,jdbcType=VARCHAR} and date_str>#{item.dateStr}
            GROUP BY device_num
        </foreach>
    </select>

    <select id="batchGetRealCheckAllCount" resultType="com.xh.vdm.bdCheck.vo.BDCheckAllCountResult">
        select device_num, count(*) as count_value
        from locations
        where valid = 1
        and device_num in
        <foreach collection="deviceNums" item="deviceNum" open="(" close=")" separator=",">
            #{deviceNum}
        </foreach>
        group by device_num
    </select>

    <select id="batchGetRealCheckNonBDCount" resultType="com.xh.vdm.bdCheck.vo.BDCheckAllCountResult">
        select device_num, count(*) as count_value
        from bd_check_real_result
        where check_res = '0'
        and device_num in
        <foreach collection="deviceNums" item="deviceNum" open="(" close=")" separator=",">
            #{deviceNum}
        </foreach>
        group by device_num
    </select>
</mapper>
