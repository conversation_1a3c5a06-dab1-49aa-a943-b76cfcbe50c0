package com.xh.vdm.bdCheck.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BladeDept implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 父主键
     */
    private Long parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 部门类型
     */
    private Integer deptCategory;

    /**
     * 部门名
     */
    private String deptName;

    /**
     * 部门全称
     */
    private String fullName;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否已删除
     */
    private Integer isDeleted;

    /**
     * 统一社会信用代码
     */
    private String creditCode;

    /**
     * 经营许可证字号
     */
    private String busiCertificate;

    /**
     * 经营期限开始
     */
    private LocalDate statrtDate;

    /**
     * 经营期限结束
     */
    private LocalDate endDate;

    /**
     * 安全主要负责人
     */
    private String securityPerson;

    /**
     * 负责人电话
     */
    private String securityPhone;

    /**
     * 营运类型
     */
    private Integer operateType;

    /**
     * 所在地
     */
    private String region;

    /**
     * 监控员
     */
    private String monitorPerson;

    /**
     * 监控电话
     */
    private String monitorPhone;

    /**
     * 法定代表人
     */
    private String legalPerson;

    /**
     * 联系电话
     */
    private String legalPhone;

    /**
     * 所属行业
     */
    private String trade;

    /**
     * 上级单位
     */
    private String tradeDept;

    /**
     * 行业主管部门
     */
    private String superDept;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 企业官网
     */
    private String netAddress;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 作者
     */
    private String author;

    /**
     * 注册地址
     */
    private String registeredAddr;

    /**
     * 行政区划
     */
    private String district;

    /**
     * 联系电话
     */
    private String telephone;

    /**
     * 联系邮箱
     */
    private String email;


}
