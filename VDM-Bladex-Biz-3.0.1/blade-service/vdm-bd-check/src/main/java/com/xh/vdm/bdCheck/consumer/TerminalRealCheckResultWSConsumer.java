package com.xh.vdm.bdCheck.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.utils.StringUtils;
import com.xh.vdm.bdCheck.checkCore.BDCheckUtil;
import com.xh.vdm.bdCheck.dto.BDCheckResAndMessage;
import com.xh.vdm.bdCheck.entity.Location;
import com.xh.vdm.bdCheck.entity.BDCheckRealResult;
import com.xh.vdm.bdCheck.entity.TerminalSatelliteData;
import com.xh.vdm.bdCheck.event.AllTerminalWSEventListener;
import com.xh.vdm.bdCheck.event.SingleTerminalWSEventListener;
import com.xh.vdm.bdCheck.service.IBdmTerminalService;
import com.xh.vdm.bdCheck.util.CheckCacheUtil;
import com.xh.vdm.bdCheck.vo.BDCheckRealResultResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description: 终端实时北斗检测消费者，推送消息
 * 从kafka读取数据推送到websocket
 * @Author: zhouxw
 * @Date: 2023/5/18 11:24
 */
@Component
@EnableKafka
@Slf4j
public class TerminalRealCheckResultWSConsumer {

	@Resource
	private SingleTerminalWSEventListener stl;

	@Resource
	private AllTerminalWSEventListener atl;

	@Resource
	private BDCheckUtil bdCheckUtil;

	@Resource
	private CheckCacheUtil checkCacheUtil;

	private static Map<String,String> deviceTypeMap = new HashMap<>();

	//本地缓存deptName, key: deviceName  value: deptName
	private ConcurrentHashMap<String,String> deptNameMap = new ConcurrentHashMap<>();
	//记录缓存日期（2个小时）
	private ConcurrentHashMap<String,Long> deptNameTimestampMap = new ConcurrentHashMap<>();

	@Resource
	private IBdmTerminalService terminalService;

	@Resource
	private KafkaTemplate kafkaTemplate;


	//全量推送检测结果时的间隔时长
	//如果每个定位点都推送到前端，浏览器会卡死。此处设置每个终端推送的时间间隔，抽稀推送。后续终端数量增多时，可以考虑提高该配置
	@Value("${check-result.push-all.duration:300}")
	private Integer duration;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	/**
	 * @description: 读取kafka推送到websocket
	 * @author: zhouxw
	 * @date: 2023-05-138 11:30:27
	 * @param: [record]
	 * @return: void
	 **/
	@KafkaListener(containerFactory = "bdCheckVehicleTerminalListenerContainerFactory", batch = "true", topics = {"ce.comms.fct.location.0"})
	public void push(List<ConsumerRecord<String, Location>> consumerRecords, Acknowledgment acknowledgment){

		try{
			log.debug("[BD_CHECK_VEHICLE_TERMINAL]反序列化的数据为：{}", consumerRecords);
			if (CollectionUtils.isEmpty(consumerRecords)) {
				return;
			}
			log.debug("[BD_CHECK_VEHICLE_TERMINAL]开始消费数据");
			for(int  i = 0 ; i < consumerRecords.size() ; i++){
				//1.获取topic中的数据，将数据通过websocket推送到前端
				Location ln = consumerRecords.get(i).value();
				if(ln == null){
					continue;
				}

				//只处理有效数据
				if(ln.getValid() != 1){
					continue;
				}


				//获取赋码号
				//终端传上来的定位点topic中应当包含赋码号
				String deviceNum = ln.getDeviceNum();
				if(StringUtils.isEmpty(deviceNum)){
					//如果终端赋码号是空，则不再推送数据
					continue;
				}

				//2.检测定位点
				//经纬度处理
				double lat = ln.getLatitude();
				double lon = ln.getLongitude();
				if(lat < 1 || lon < 1){
					//如果是未定位状态，则不判断该定位点
					continue;
				}
				BDCheckResAndMessage bds = bdCheckUtil.bdCheck(ln);

				//3.整理推送数据
				BDCheckRealResultResponse response = new BDCheckRealResultResponse();
				//设置终端类型
				if(deviceTypeMap == null || deviceTypeMap.size() < 1){
					deviceTypeMap = checkCacheUtil.getTerminalTypeMap();
				}
				response.setTerminalType(deviceTypeMap.get(ln.getDeviceType()+""));
				response.setLongitude(ln.getLongitude());
				response.setLatitude(ln.getLatitude());
				//1:北斗定位   0:疑似非北斗定位
				response.setCheckRes(bds.getResult()?"1":"0");
				response.setCheckResMessage(bds.getMessage());
				response.setDeviceNum(deviceNum);
				response.setLocTime(DateUtil.getDateTimeString(ln.getLocTime()));

				//todo 添加星历数据，正常其概况下，只有在检测为北斗终端的时候返回星历数据，此处为了前端测试效果
				if(!(ln.getAuxsNormal() == null || ln.getAuxsNormal().get(CommonConstant.AUXS_STATELLITE_KEY) == null)){
					List<TerminalSatelliteData> sateList = JSON.parseArray(JSON.toJSONString(ln.getAuxsNormal().get(CommonConstant.AUXS_STATELLITE_KEY)), TerminalSatelliteData.class);
					response.setSatellites(sateList);
				}else{
					response.setSatellites(null);
				}

				//从本地缓存取deptName
				String deptName = deptNameMap.get(deviceNum);
				if(StringUtils.isEmpty(deptName)){
					//如果缓存中获取不到，则查询数据库
					String dName = terminalService.findDeptNameByDeviceNum(deviceNum);
					deptName = dName;
					if(!StringUtils.isEmpty(deptName)){
						deptNameMap.put(deviceNum, deptName);
						deptNameTimestampMap.put(deviceNum, System.currentTimeMillis());
					}
				}else{
					//判断本地缓存是否过期，如果过期，则删除缓存
					Long timestamp = deptNameTimestampMap.get(deviceNum);
					if(timestamp == null || System.currentTimeMillis() - timestamp > 2 * 3600 * 1000){
						deptNameMap.remove(deviceNum);
						if(timestamp != null){
							deptNameTimestampMap.remove(deviceNum);
						}
					}
				}
				if(StringUtils.isEmpty(deptName)){
					response.setDeptName("");
				}else{
					response.setDeptName(deptName);
				}

				//4.保存定位点的北斗检测数据，发送到kafka
				//4.1 构建结果数据
				BDCheckRealResult result = new BDCheckRealResult();
				result.setDeviceNum(deviceNum);
				result.setTerminalType(response.getTerminalType());
				log.info("推送，设置终端类型：resonse.getTermianlType = {}，ln.getTerminalType ={}", response.getTerminalType(), ln.getDeviceType());
				result.setTerminalModel(ln.getDeviceModel());
				result.setLongitude(ln.getLongitude());
				result.setLatitude(ln.getLatitude());
				result.setDeptName(response.getDeptName());
				result.setCheckRes(response.getCheckRes());
				result.setCheckResMessage(response.getCheckResMessage());
				List<TerminalSatelliteData> list = response.getSatellites();
				result.setSatelliteData(list==null||list.size()<1?"":JSON.toJSONString(list));
				result.setLocTime(ln.getLocTime());
				result.setDateStr(DateUtil.getDateString(ln.getLocTime()));

				//4.2 发送到kafka
				try {
					kafkaTemplate.send(CommonConstant.TOPIC_TERMINAL_LOC_CHECK_RESULT, result);
				}catch (Exception e){
					log.error("终端实时检测结果发送到kafka失败",e);
				}

				//执行数据推送
				log.debug("[BD_CHECK_VEHICLE_TERMINAL]将要推送数据:{}",JSON.toJSONString(response));
				//执行单终端数据推送
				stl.sendMessageByDeviceNum(deviceNum, JSON.toJSONString(response));


				//执行全终端数据推送
				Long deviceId = ln.getDeviceId();
				if(canPushWhenPushAll(deviceId)){
					atl.sendAllMessage(JSON.toJSONString(response));
				}
			}

			log.info("kafka vehicle terminal 处理消息成功，共{}条", consumerRecords.size());
			// 手动提交offset
			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("[BD_CHECK_VEHICLE_TERMINAL]消息消费失败 :" + e.getMessage(), e);
		}
	}


	private boolean canPushWhenPushAll(Long deviceId){
		Object timestampO = stringRedisTemplate.opsForHash().get(CommonConstant.CACHE_MAP_PUSH_ALL_DURATION, deviceId+"");
		try{
			if(timestampO != null){
				long lastPushTimestamp = Long.parseLong(timestampO.toString());
				//如果距离上次推送超过了设置的延迟时间，则可以推送
				return System.currentTimeMillis() / 1000 - lastPushTimestamp > duration;
			}else{
				return true;
			}
		}catch (Exception e){
			log.error("北斗检测结果全量推送：从缓存中获取上次推送时间失败，将要执行本次推送",e);
			return true;
		}
	}

}
