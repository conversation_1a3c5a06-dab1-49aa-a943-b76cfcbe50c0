package com.xh.vdm.bdCheck.service.impl;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bdCheck.entity.BDCheckRealResult;
import com.xh.vdm.bdCheck.mapper.impala.BDCheckRealResultMapper;
import com.xh.vdm.bdCheck.service.IBDCheckRealResultService;
import com.xh.vdm.bdCheck.vo.BDCheckAllCountResult;
import com.xh.vdm.bdCheck.vo.BDCheckCountResult;
import com.xh.vdm.bdCheck.vo.BDCheckCountVO;
import com.xh.vdm.bdCheck.vo.BDCheckDateResult;
import org.springblade.core.mp.support.Query;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 北斗终端实时检测
 *
 * <AUTHOR>
 * @since 2024-04-20
 */
@Service
public class BDCheckRealResultServiceImpl extends ServiceImpl<BDCheckRealResultMapper, BDCheckRealResult> implements IBDCheckRealResultService {

	@Override
	public IPage<BDCheckRealResult> findRealResultPage(String deviceNum, Query query) throws Exception {
		IPage<BDCheckRealResult> page = new Page<>(query.getCurrent(), query.getSize());
		return baseMapper.getRealResultPage(deviceNum, page);
	}

	@Override
	public IPage<BDCheckRealResult> findRealNonBDPage(String deviceNum, Query query) throws Exception {
		IPage<BDCheckRealResult> page = new Page<>(query.getCurrent(), query.getSize());
		return baseMapper.getRealNonBDPage(deviceNum, page);
	}

	@Override
	public List<String> findRealCheckAllDeviceNum() throws Exception {
		return baseMapper.getRealCheckAllDeviceNum();
	}

	@Override
	public List<String> findRealCheckDateByDeviceNum(String deviceNum) throws Exception {
		return baseMapper.getRealCheckAllDateByDeviceNum(deviceNum);
	}

	@Override
	public Map<String, List<String>> batchFindRealCheckDateByDeviceNum(List<String> deviceNums) throws Exception {
		if(deviceNums.isEmpty()) {
			return new HashMap<>();
		}

		Map<String, List<String>> resultMap = new HashMap<>();

		// 批量查询所有设备的日期
		List<BDCheckDateResult> results = baseMapper.batchGetRealCheckAllDateByDeviceNum(deviceNums);

		// 按设备号分组
		for(BDCheckDateResult result : results) {
			String deviceNum = result.getDeviceNum();
			String dateStr = result.getDateStr();

			resultMap.computeIfAbsent(deviceNum, k -> new ArrayList<>()).add(dateStr);
		}

		// 确保所有设备都有结果，即使是空列表
		for(String deviceNum : deviceNums) {
			resultMap.putIfAbsent(deviceNum, new ArrayList<>());
		}

		// 对每个设备的日期列表按降序排序
		for(List<String> dateList : resultMap.values()) {
			dateList.sort(Collections.reverseOrder());
		}

		return resultMap;
	}

	@Override
	public Long findRealCheckCountGE(String deviceNum, String dateStr) throws Exception {
		return baseMapper.getRealCheckCountGE(deviceNum, dateStr);
	}

	@Override
	public Long findRealCheckNonBDCountGE(String deviceNum, String dateStr) throws Exception {
		return baseMapper.getRealCheckNonBDCountGE(deviceNum, dateStr);
	}

	@Override
	public Long findRealCheckAllCount(String deviceNum) throws Exception {
		return baseMapper.getRealCheckAllCount(deviceNum);
	}



	@Override
	public Map<String, BDCheckCountVO> batchFindRealCheckCountAndNonBDCountGE(List<BDCheckDateResult> checkDateResultList) throws Exception {
		if(checkDateResultList.isEmpty()) {
			return new HashMap<>();
		}

		// 优化：使用合并查询，一次性获取总数和非北斗数
		List<BDCheckCountResult> results = baseMapper.batchGetRealCheckCountGE(checkDateResultList);
		Map<String, BDCheckCountVO> resultMap = new HashMap<>();

		for(BDCheckCountResult result : results) {
			String deviceNum = result.getDeviceNum();
			Long totalCount = result.getTotalCount() != null ? result.getTotalCount() : 0L;
			Long nonBdCount = result.getNonBdCount() != null ? result.getNonBdCount() : 0L;

			BDCheckCountVO countVO = new BDCheckCountVO(totalCount, nonBdCount);
			resultMap.put(deviceNum, countVO);
		}

		// 确保所有设备都有结果
		for(BDCheckDateResult checkDateResult : checkDateResultList) {
			if(!resultMap.containsKey(checkDateResult.getDeviceNum())) {
				BDCheckCountVO countVO = new BDCheckCountVO(0L, 0L);
				resultMap.put(checkDateResult.getDeviceNum(), countVO);
			}
		}

		return resultMap;
	}

	@Override
	public Map<String, Long> batchFindRealCheckAllCount(List<String> deviceNums) throws Exception {
		if(deviceNums.isEmpty()) {
			return new HashMap<>();
		}

		List<BDCheckAllCountResult> results = baseMapper.batchGetRealCheckAllCount(deviceNums);
		Map<String, Long> resultMap = new HashMap<>();

		for(BDCheckAllCountResult result : results) {
			String deviceNum = result.getDeviceNum();
			Long count = result.getCountValue() != null ? result.getCountValue() : 0L;
			resultMap.put(deviceNum, count);
		}

		// 确保所有设备都有结果
		for(String deviceNum : deviceNums) {
			resultMap.putIfAbsent(deviceNum, 0L);
		}

		return resultMap;
	}

	@Override
	public Map<String, Long> batchFindRealCheckNonBDCount(List<String> deviceNums) throws Exception {
		if(deviceNums.isEmpty()) {
			return new HashMap<>();
		}

		// 使用批量查询替代for循环，提高性能
		List<BDCheckAllCountResult> results = baseMapper.batchGetRealCheckNonBDCount(deviceNums);
		Map<String, Long> resultMap = new HashMap<>();

		// 使用GROUP BY的结果构建Map
		for(BDCheckAllCountResult result : results) {
			String deviceNum = result.getDeviceNum();
			Long count = result.getCountValue() != null ? result.getCountValue() : 0L;
			resultMap.put(deviceNum, count);
		}

		// 确保所有设备都有结果，即使是0
		for(String deviceNum : deviceNums) {
			resultMap.putIfAbsent(deviceNum, 0L);
		}

		return resultMap;
	}
}
