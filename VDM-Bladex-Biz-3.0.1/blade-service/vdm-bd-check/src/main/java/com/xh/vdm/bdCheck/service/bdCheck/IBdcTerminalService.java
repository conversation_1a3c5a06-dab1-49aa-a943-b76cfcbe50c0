package com.xh.vdm.bdCheck.service.bdCheck;

import com.alibaba.nacos.api.exception.NacosException;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCheckReport;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bdCheck.vo.coding.request.ProcessRequest;
import com.xh.vdm.bdCheck.vo.coding.request.SyncFromMachineRequest;
import com.xh.vdm.bdCheck.vo.coding.response.PassListResponse;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;

import java.sql.SQLException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IBdcTerminalService extends IService<BdcTerminal> {

	/**
	 * 根据终端编号查询所属企业名称
	 * @param terminalNo
	 * @return
	 * @throws Exception
	 */
	String findCompanyNameByTerminalNo(String terminalNo) throws Exception;

	/**
	 * 根据终端编号获取检测信息
	 * @param terminalNo
	 * @return
	 * @throws Exception
	 */
	BdcCheckReport findReportByTerminalNo(String terminalNo) throws Exception;



	// 可赋码设备的分页列表
	IPage<PassListResponse> getPassPage (ProcessRequest request, Query query);

	// 参数检查
	Map<String, Map<String, Object>> checkCodeParam (ProcessRequest request);

	// 生成
	R<Map<String, String>> formDeviceNum (ProcessRequest request) throws ParseException, InvalidCipherTextException;

	// 下发
	R<String> deliverDeviceNum (ProcessRequest request) throws SQLException, NacosException;

	// 来自赋码机的赋码结果同步至平台
	void syncFromCodingMachine (SyncFromMachineRequest request) throws SQLException, ParseException;

	// 一键批量
	R<String> batchCompleteCoding (ProcessRequest request) throws SQLException, NacosException;



	/**
	 * 查询给定的设备编号中已经存在的设备编号
	 * @param deviceNoList
	 * @return
	 * @throws Exception
	 */
	List<String> findExistDeviceNo(String deviceNoList) throws Exception;

	/**
	 * 根据给定的厂商id列表查询已经绑定了的终端（入网终端管理）
	 * @param manufacturerIds
	 * @return
	 * @throws Exception
	 */
	List<BdcTerminal> findTerminalListByManufacturerIds(List<Long> manufacturerIds) throws Exception;


	/**
	 * 根据给定的厂商编号列表查询已经绑定的北斗终端数量（北斗资源管理）
	 * @param manufacturerIds
	 * @return
	 */
	long findBDTerminalCountByVendor(List<String> vendors);




	void updateByDeviceSeq(BdcTerminal bdcTerminal);

	/**
	 * 根据赋码号查询uniqueId
	 * @param deviceNum
	 * @return
	 */
	String findUniqueIdByDeviceNum(String deviceNum);

	/**
	 * 根据赋码号查询deviceId
	 * @param deviceNum
	 * @return
	 */
	Long findDeviceIdByDeviceNum(String deviceNum);

}
