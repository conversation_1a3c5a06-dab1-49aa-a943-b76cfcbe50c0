package com.xh.vdm.bdCheck.controller.bdCheck;

import com.xh.vdm.bdCheck.entity.BdmSatellite;
import com.xh.vdm.bdCheck.service.IBdmSatelliteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

import static com.xh.vdm.bdCheck.checkCore.SatelliteDateService.readSatelliteData;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2025/6/11 19:15
 */
@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

	@Resource
	private IBdmSatelliteService satelliteService;

	@GetMapping("/testSatelliteDate")
	public R<String> testSatelliteData(){
		//用于测试解析星历数据入库
		String localFilePath = "c:/tmp/brdm1610.25p";
		//2.解析星历文件
		if (StringUtils.isEmpty(localFilePath)) {
			//如果下载星历数据不成功
			log.info("星历数据文件下载不成功，下载文件为空");
			return R.fail("星历");
		}
		Map<String, BdmSatellite> satelliteMap = new HashMap<>();
		try {
			satelliteMap = readSatelliteData(localFilePath);
		} catch (Exception e) {
			log.error("解析星历数据失败", e);
			return R.data("解析星历数据失败：" + e.getMessage());
		}

		//3.保存星历数据
		try {
			//设置批次号
			Collection<BdmSatellite> set = satelliteMap.values();
			long batchId = System.currentTimeMillis();
			set.forEach(item -> {
				item.setBatchId(batchId);
			});
			List<BdmSatellite> list = new ArrayList<>();
			list.addAll(set);
			satelliteService.saveOrUpdateSatelliteBatch(list);
		} catch (Exception e) {
			log.error("保存星历数据失败", e);
			return R.fail("保存星历数据失败："+e.getMessage());
		}

		return R.success("北斗星历数据处理成功");
	}
}
