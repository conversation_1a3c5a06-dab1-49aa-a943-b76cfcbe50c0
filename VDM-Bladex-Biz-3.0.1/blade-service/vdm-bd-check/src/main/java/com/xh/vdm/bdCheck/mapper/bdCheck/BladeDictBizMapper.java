package com.xh.vdm.bdCheck.mapper.bdCheck;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bdCheck.entity.bdCheck.BladeDictBiz;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 业务字典表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-09
 */
public interface BladeDictBizMapper extends BaseMapper<BladeDictBiz> {

	List<String> getProfessionList (@Param("professionId") Long professionId);
}
