package com.xh.vdm.bdCheck.service.bdCheck.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.bdCheck.entity.bdCheck.BDCheckLocations;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCheckReport;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;
import com.xh.vdm.bdCheck.mapper.bdCheck.BdcCheckReportMapper;
import com.xh.vdm.bdCheck.mapper.bdCheck.BdcTerminalMapper;
import com.xh.vdm.bdCheck.mapper.impala.BDCheckLocationsMapper;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcCheckReportService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bdCheck.vo.bdCheck.BDCReportRequest;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 检测报告表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@Service
public class BdcCheckReportServiceImpl extends ServiceImpl<BdcCheckReportMapper, BdcCheckReport> implements IBdcCheckReportService {


	@Resource
	private BdcTerminalMapper terminalMapper;

	@Resource
	private BDCheckLocationsMapper locationsMapper;


	@Override
	public IPage<BdcCheckReport> findCheckReportPage(BDCReportRequest request, Query query) throws Exception {
		IPage<BdcCheckReport> page = Condition.getPage(query);
		return baseMapper.getCheckReportPage(request, page);
	}

	@Override
	public void resetCheckState(Long reportId) throws Exception {

		//1.重置report状态
		baseMapper.resetReportCheckState(reportId);

		//2.重置终端状态
		terminalMapper.resetTerminalByReportId(reportId);

		//3.删除大数据中的检测数据
		//查询用于检测的终端
		List<BdcTerminal> terminalList = terminalMapper.selectList(Wrappers.lambdaQuery(BdcTerminal.class)
			.eq(BdcTerminal::getIsInCheck,1)
			.eq(BdcTerminal::getIsDel,0)
			.eq(BdcTerminal::getReportId, reportId));
		List<String> deviceNoList = new ArrayList<>();
		terminalList.forEach(item -> {
			deviceNoList.add(item.getDeviceNo());
		});
		locationsMapper.delete(Wrappers.lambdaQuery(BDCheckLocations.class)
			.in(BDCheckLocations::getDeviceNo, deviceNoList));
	}
}
