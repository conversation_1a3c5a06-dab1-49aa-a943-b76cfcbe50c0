package com.xh.vdm.bdCheck.controller;

import com.alibaba.druid.util.StringUtils;
import com.xh.vdm.bdCheck.checkCore.BDCheckUtil;
import com.xh.vdm.bdCheck.checkCore.SatelliteDateService;
import com.xh.vdm.bdCheck.entity.BdmSatellite;
import com.xh.vdm.bdCheck.entity.BdmSatelliteHeader;
import com.xh.vdm.bdCheck.service.IBdmSatelliteService;
import com.xh.vdm.bdCheck.util.FtpUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.springblade.common.utils.DateUtil;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;

@RestController
@RequestMapping("/agnss")
@Slf4j
public class AGNSSController {


	@Resource
	private IBdmSatelliteService satelliteService;

	@Resource
	private FtpUtil ftpUtil;

	//ftp目录
	@Value("${ftp.path}")
	private String path;

	@Value("${ftp.localTmpPath}")
	private String ftpLocationTmpPath;

	@GetMapping("/toAGNSS")
	public R<String> toAGNSS(){
		try{
			//1.下载星历文件

			//2.解析星历数据，保存为对象

			//3.取每颗星最新的数据


			//1.读取最新星历数据
			List<BdmSatellite> list = satelliteService.findNewestSatellite(3);
			File file = new File("E:\\tmp\\HG_BDS.hdb");
			PrintWriter pw = new PrintWriter(new FileOutputStream(file));
			List<String> resList = new ArrayList<>();
			//2.按照 AGNSS 格式写入文件
			for(int i = 0; i < list.size(); i++){
				BdmSatellite b = list.get(i);
				StringBuffer sb = new StringBuffer();
				//语句头
				sb.append("$BDALE").append(",");
				//语句总数
				sb.append(list.size()).append(",");
				//语句号
				sb.append(i).append(",");
				//卫星类别
				sb.append("BD").append(",");
				//卫星号PRN
				sb.append(b.getNum()).append(",");
				//星历参考时刻
				//todo 转换格式
				sb.append(formatData(b.getToe())).append(",");
				//卫星轨道类别
				String type = BDCheckUtil.SATE_TYPE.get(b.getNum());
				if(type.equals("GEO")){
					sb.append("1").append(",");
				}else{
					sb.append("2").append(",");
				}
				//长半轴的平方根
				sb.append(formatData(b.getRootA())).append(",");
				//卫星平均运动速率与计算值之差
				//todo 待确认
				sb.append(formatData(b.getDeltan())).append(",");
				//参考时刻的平近点角
				sb.append(formatData(b.getM0())).append(",");
				//偏心率
				sb.append(formatData(b.getE())).append(",");
				//近地点幅角
				sb.append(formatData(b.getOmega())).append(",");
				//按参考时间计算的升交点经度
				sb.append(formatData(b.getOmega0())).append(",");
				//参考时刻的轨道倾角
				sb.append(formatData(b.getI0())).append(",");
				//升交点赤经变化率
				sb.append(formatData(b.getOmegadot())).append(",");
				//轨道倾角变化率IDOT
				sb.append(formatData(b.getIdot())).append(",");
				//轨道倾角的正弦调和改正项的振幅Cis
				sb.append(formatData(b.getCis())).append(",");
				//轨道倾角的余弦调和改正项的振幅Cic
				sb.append(formatData(b.getCic())).append(",");
				//轨道半径的正弦调和改正项的振幅Crs
				sb.append(formatData(b.getCrs())).append(",");
				//轨道半径的余弦调和改正项的振幅Crc
				sb.append(formatData(b.getCrc())).append(",");
				//纬度幅角的正弦调和改正项的振幅Cus
				sb.append(formatData(b.getCus())).append(",");
				//纬度幅角的余弦调和改正项的振幅Cuc
				sb.append(formatData(b.getCuc())).append(",");
				//钟差参数参考时刻toc
				//todo 确认是否是秒数
				String dateStr = b.getYear()+"-"+b.getMon()+"-"+b.getDay()+" "+b.getHour()+":"+b.getMin()+":"+b.getSec();
				Date date = DateUtil.sdfHolder.get().parse(dateStr);
				sb.append(date.getTime()/1000).append(",");
				//卫星钟偏差系数a0
				sb.append(formatData(b.getA0())).append(",");
				//卫星钟漂移系数a1
				sb.append(formatData(b.getA1())).append(",");
				//卫星钟漂移率系数a2
				sb.append(formatData(b.getA2())).append(",");
				//电离层参数
				//todo RENIX文件中不包含，定为0
				for(int j = 0 ; j < 7; j++){
					sb.append(0).append(",");
				}
				sb.append(0);
				//指令结束符
				sb.append("*");
				//校验和（取$和*之间的部分取异或，不包含$和*）
				String data = sb.substring(1,sb.length()-1);
				sb.append(calculateXORValue(data));
				pw.println(sb);
			}
			pw.flush();
			pw.close();

		}catch (Exception e){
			log.error("转换AGNSS格式失败",e);
			return R.fail("转换AGNSS格式失败，" + e.getMessage());
		}
		return R.success("转换AGNSS格式成功");
	}




	/**
	 * 处理星历数据
	 * 下载星历文件，从星历文件中解析星历数据，保存到数据库
	 * 保存到数据库中，存在即更新
	 *
	 * 已创建了单独的工程用于处理 AGNSS数据转换，查看数据处理工程目录
	 *
	 * @param year     指定年份
	 * @param fileName 指定文件名称，可以不填
	 */
	@GetMapping("/handleToAGNSS")
	public R<String> handleToAGNSS(String year, String fileName) {
		if(StringUtils.isEmpty(year)){
			year = "2024";
		}
		//1.下载星历文件（ftp），下载最新的
		FTPClient client = null;
		String localFilePath = "";
		String pathF = "";
		try {
			//1.1 创建ftp连接
			client = ftpUtil.connectFtp();
			//1.2 下载文件到本地
			String fileNameF = "";
			if (StringUtils.isEmpty(fileName)) {
				//如果不指定文件,则下载最新的文件
				//因为ftp中有两类文件，这里只使用tarc开头的文件
				List<String> fileList = ftpUtil.getFileNameList(client, path + year);
				//取文件名称最大的
				Collections.sort(fileList, (fileName1, fileName2) -> fileName2.compareTo(fileName1));
				if (fileList == null || fileList.size() < 1) {
					log.error("下载星历数据，ftp上未查询到文件");
					return R.fail("处理星历文件失败");
				}
				String newestFile = fileList.get(0);
				fileNameF = newestFile;
			} else {
				//如果指定文件,则下载指定的文件
				fileNameF = fileName;
			}
			pathF = path + year;
			localFilePath = ftpUtil.downloadFile(client, pathF, fileNameF, ftpLocationTmpPath);
			log.info("下载星历数据{}成功", pathF + fileNameF);
		} catch (Exception e) {
			log.error("操作ftp下载星历数据失败", e);
			return R.fail("处理星历文件失败");
		} finally {
			if (client != null) {
				ftpUtil.closeFtpClient(client);
			}
		}

		//2.解析星历头信息
		Map<String, BdmSatelliteHeader> satelliteHeaderMap;
		//num - 电离层参数列表
		Map<String,List<String>> paramResMap = new HashMap<>();
		try {
			//2.1 读取星历头数据
			satelliteHeaderMap = SatelliteDateService.readSatelliteHeader(localFilePath);
			//2.2 取每颗星最新的数据
			//num + "~" + timeFlag
			List<String> headerKeyPrefixList = new ArrayList<>();
			//num - timeFlag
			Map<String,String> keyMap = new HashMap<>();
			//num - num~timeFlag~type
			Map<String,Set<String>> paramMap = new HashMap<>();
			for(String key : satelliteHeaderMap.keySet()){
				if(StringUtils.isEmpty(key)){
					continue;
				}
				String num = key.split("~")[0];
				String timeFlag = key.split("~")[1];
				String type = key.split("~")[2];
				String tFlag = keyMap.get(num);
				if(StringUtils.isEmpty(tFlag)){
					//如果数据为空，则进行初始化
					keyMap.put(num, timeFlag);
					Set<String> set = paramMap.get(num);
					if(set == null || set.size() < 1){
						set = new HashSet<>();
					}
					set.add(num+"~"+timeFlag+"~"+type);
					paramMap.put(num, set);
				}else{
					//如果数据不为空，则比较时间标记，只存储最新的数据
					if(timeFlag.compareTo(tFlag) > 0){
						keyMap.put(num, timeFlag);
						Set<String> set = paramMap.get(num);
						if(set == null || set.size() < 1){
							//如果set为空，则初始化
							set = new HashSet<>();
						}else{
							//如果set不为空，则删除非当前 num+"~"+timeFlag的数据
							Set<String> tmpSet = new HashSet<>();
							for(String s : set){
								if(s.startsWith(num + "~" + timeFlag)){
									tmpSet.add(s);
								}
							}
							set = tmpSet;
						}
						set.add(num+"~"+timeFlag+"~"+type);
						paramMap.put(num, set);
					}
				}
			}
			//拼接电离层参数
			for(String num : paramMap.keySet()){
				List<String> paramList = new ArrayList<>();
				//将所有的电离层参数合并起来
				Set<String> set = paramMap.get(num);
				for(String key : set){
					BdmSatelliteHeader header = satelliteHeaderMap.get(key);
					paramList.add(header.getData1());
					paramList.add(header.getData2());
					paramList.add(header.getData3());
					paramList.add(header.getData4());
				}
				paramResMap.put(num, paramList);
			}
		} catch (Exception e) {
			log.error("解析星历数据失败", e);
			return R.fail("处理星历文件失败");
		}


		//3.解析星历数据信息
		if (StringUtils.isEmpty(localFilePath)) {
			//如果下载星历数据不成功
			log.info("星历数据文件下载不成功，下载文件为空");
			return R.fail("处理星历文件失败");
		}
		Map<String, BdmSatellite> satelliteMap = new HashMap<>();
		File file = new File("E:\\tmp\\HG_BDS.hdb");
		PrintWriter pw = null;
		try {
			pw = new PrintWriter(new FileOutputStream(file));
			//key = flag + num + year + mon + day + hour;
			satelliteMap = SatelliteDateService.readSatelliteData(localFilePath);
			//查找最新的数据
			//按照卫星号分组
			Map<String,List<BdmSatellite>> sateMap = new HashMap<>();
			for(String key : satelliteMap.keySet()){
				//todo 暂时只对B1I和B3I进行处理
				BdmSatellite sate = satelliteMap.get(key);
				if(!StringUtils.isEmpty(sate.getData()) && (sate.getData().charAt(4) == '1' || sate.getData().charAt(5) == '1')){
					continue;
				}
				String k = key.substring(0,3);
				List<BdmSatellite> list = sateMap.get(k);
				if(list == null || list.size() < 0){
					list = new ArrayList<>();
				}
				list.add(satelliteMap.get(key));
				sateMap.put(k, list);
			}
			//取分组中最新的数据
			//num-星历对象
			Map<String,BdmSatellite> sateDataMap = new HashMap<>();
			for(String num : sateMap.keySet()){
				String maxKey = "";
				for(BdmSatellite sate : sateMap.get(num)){
					String key = sate.getFlag() + sate.getNum() + sate.getYear() + sate.getMon() + sate.getDay() + sate.getHour();
					if(key.compareTo(maxKey) > 0){
						maxKey = key;
					}
				}
				sateDataMap.put(num, satelliteMap.get(maxKey));
			}
			//对最新的数据进行解析和拼接
			int index = 0;
			for(String num : sateDataMap.keySet()){
				BdmSatellite b = sateDataMap.get(num);
				StringBuffer sb = new StringBuffer();
				//语句头
				sb.append("$BDALE").append(",");
				//语句总数
				sb.append(sateDataMap.size()).append(",");
				//语句号
				sb.append(index).append(",");
				//卫星类别
				sb.append("BD").append(",");
				//卫星号PRN
				sb.append(b.getNum()).append(",");
				//星历参考时刻
				//todo 转换格式
				sb.append(formatData(b.getToe())).append(",");
				//卫星轨道类别
				String type = BDCheckUtil.SATE_TYPE.get(b.getNum());
				if(type.equals("GEO")){
					sb.append("1").append(",");
				}else{
					sb.append("2").append(",");
				}
				//长半轴的平方根
				sb.append(formatData(b.getRootA())).append(",");
				//卫星平均运动速率与计算值之差
				//todo 待确认
				sb.append(formatData(b.getDeltan())).append(",");
				//参考时刻的平近点角
				sb.append(formatData(b.getM0())).append(",");
				//偏心率
				sb.append(formatData(b.getE())).append(",");
				//近地点幅角
				sb.append(formatData(b.getOmega())).append(",");
				//按参考时间计算的升交点经度
				sb.append(formatData(b.getOmega0())).append(",");
				//参考时刻的轨道倾角
				sb.append(formatData(b.getI0())).append(",");
				//升交点赤经变化率
				sb.append(formatData(b.getOmegadot())).append(",");
				//轨道倾角变化率IDOT
				sb.append(formatData(b.getIdot())).append(",");
				//轨道倾角的正弦调和改正项的振幅Cis
				sb.append(formatData(b.getCis())).append(",");
				//轨道倾角的余弦调和改正项的振幅Cic
				sb.append(formatData(b.getCic())).append(",");
				//轨道半径的正弦调和改正项的振幅Crs
				sb.append(formatData(b.getCrs())).append(",");
				//轨道半径的余弦调和改正项的振幅Crc
				sb.append(formatData(b.getCrc())).append(",");
				//纬度幅角的正弦调和改正项的振幅Cus
				sb.append(formatData(b.getCus())).append(",");
				//纬度幅角的余弦调和改正项的振幅Cuc
				sb.append(formatData(b.getCuc())).append(",");
				//钟差参数参考时刻toc
				//todo 确认是否是秒数
				String dateStr = b.getYear()+"-"+b.getMon()+"-"+b.getDay()+" "+b.getHour()+":"+b.getMin()+":"+b.getSec();
				Date date = DateUtil.sdfHolder.get().parse(dateStr);
				sb.append(date.getTime()/1000).append(",");
				//卫星钟偏差系数a0
				sb.append(formatData(b.getA0())).append(",");
				//卫星钟漂移系数a1
				sb.append(formatData(b.getA1())).append(",");
				//卫星钟漂移率系数a2
				sb.append(formatData(b.getA2())).append(",");
				//电离层参数
				List<String> paramList = paramResMap.get(num);
				if(paramList == null || paramList.size() < 1){
					for(int j = 0 ; j < 7; j++){
						sb.append(0).append(",");
					}
					sb.append(0);
				}else {
					for (int j = 0; j < paramList.size()-1; j++) {
						sb.append(formatData(paramList.get(j))).append(",");
					}
					sb.append(formatData(paramList.get(paramList.size()-1)));
				}

				//指令结束符
				sb.append("*");
				//校验和（取$和*之间的部分取异或，不包含$和*）
				String data = sb.substring(1,sb.length()-1);
				sb.append(calculateXORValue(data));
				pw.println(sb);
				index++;
			}

		} catch (Exception e) {
			log.error("解析星历数据失败", e);
			return R.fail("处理星历文件失败");
		}finally {
			if(pw != null){
				pw.flush();
				pw.close();
			}
		}


		//5.删除本地临时文件
		File fileO = new File(localFilePath);
		if (fileO != null && fileO.isFile()) {
			fileO.delete();
		}
		return R.success("处理星历文件成功");
	}



	/**
	 * 格式化数值
	 * @param num
	 * @return
	 */
	private String formatData(String num){
		if(StringUtils.isEmpty(num)){
			num = "0";
		}
		//todo 格式化的方式不太好
		DecimalFormat df = new DecimalFormat("#,##0.000000000000000000000000000000");
		BigDecimal data = new BigDecimal(num);
		if(data.compareTo(new BigDecimal(0)) == 0){
			//如果是0，则直接返回0
			return "0";
		}
		String dataStr = df.format(new BigDecimal(num));
		dataStr = dataStr.replaceAll(",","");
		//去掉数据中多余的0
		int index = dataStr.length();
		for(int i = dataStr.length()-1; i > -1; i--){
			char c = dataStr.charAt(i);
			if(c != '0' && c != '.'){
				index = i;
				break;
			}
		}
		dataStr = dataStr.substring(0, index + 1);
		return dataStr;
	}

	/**
	 * 字符串异或操作
	 * @param str
	 * @return
	 */
	public static String calculateXORValue(String str) {
		int xorValue = 0;
		for (int i = 0; i < str.length(); i++) {
			// 对每个字符的ASCII值进行异或操作
			xorValue ^= str.charAt(i);
		}
		String xorValueStr = Integer.toHexString(xorValue);
		return xorValueStr;
	}
}
