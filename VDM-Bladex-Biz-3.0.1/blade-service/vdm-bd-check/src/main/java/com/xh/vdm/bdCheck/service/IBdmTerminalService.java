package com.xh.vdm.bdCheck.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bdCheck.entity.BdmTerminal;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bdCheck.vo.BDCheckRealRequest;
import org.springblade.core.mp.support.Query;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-20
 */
public interface IBdmTerminalService extends IService<BdmTerminal> {

	/**
	 * 根据赋码值查询设备归属单位名称
	 * @param deviceNum
	 * @return
	 * @throws Exception
	 */
	String findDeptNameByDeviceNum(String deviceNum) throws Exception;

	/**
	 * 检查终端赋码号是否存在。
	 * @param deviceNum 终端赋码号
	 * @return true-存在，false-不存在
	 * @throws Exception
	 */
	boolean isExistDeviceNum(String deviceNum) throws Exception;

	/**
	 * 查询终端信息
	 * @param request
	 * @return
	 * @throws Exception
	 */
	List<BdmTerminal> findTerminal(BDCheckRealRequest request) throws Exception;

	/**
	 * 分页查询终端
	 * @param request
	 * @param query
	 * @return
	 * @throws Exception
	 */
	IPage<BdmTerminal> findTerminalPage(BDCheckRealRequest request, Query query) throws Exception;
}
