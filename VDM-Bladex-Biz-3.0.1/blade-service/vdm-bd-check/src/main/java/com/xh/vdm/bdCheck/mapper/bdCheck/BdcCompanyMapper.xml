<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bdCheck.mapper.bdCheck.BdcCompanyMapper">

    <select id="getCompanyPage" resultType="com.xh.vdm.bdCheck.entity.bdCheck.BdcCompany">
        select *
        from bdc_company
        where state = 'U'
        <if test="company.id != null">
            and id = #{company.id}
        </if>
        <if test="company.deptId != null">
            and dept_id = #{company.deptId}
        </if>
        <if test="company.companyName != null and company.companyName != ''">
            and company_name like concat('%', #{company.companyName}, '%')
        </if>
        <if test="company.address != null and company.address != ''">
            and address like concat('%', #{company.address}, '%')
        </if>
        <if test="company.contactPerson != null and company.contactPerson != ''">
            and contact_person = #{company.contactPerson}
        </if>
        <if test="company.contactPhone != null and company.contactPhone != ''">
            and contact_phone = #{company.contactPhone}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and dept_id = any(${deptIds})
        </if>
        order by create_time desc
    </select>
</mapper>
