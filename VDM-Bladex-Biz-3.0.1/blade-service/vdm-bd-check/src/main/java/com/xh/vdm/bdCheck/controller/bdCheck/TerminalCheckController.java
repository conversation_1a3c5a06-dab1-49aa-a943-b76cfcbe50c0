package com.xh.vdm.bdCheck.controller.bdCheck;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bdCheck.entity.bdCheck.*;
import com.xh.vdm.bdCheck.service.IBladeDeptService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcCheckReportService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcCompanyService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bdCheck.vo.bdCheck.*;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.annotation.Log;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

/**
 * 北斗识别检测-终端信息
 */
@RestController
@RequestMapping("/terminalCheck")
@Slf4j
public class TerminalCheckController {

	@Resource
	private IBdcTerminalService terminalService;

	@Resource
	private IBladeDeptService deptService;

	@Resource
	private IBdcCheckReportService reportService;

	@Resource
	private IBdcCompanyService companyService;


	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<BdcTerminal> detail(BdcTerminal terminal) {
		BdcTerminal detail = terminalService.getOne(Condition.getQueryWrapper(terminal));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	public R<IPage<ReportTerminalResponse>> list(BDCReportRequest request, Query query) {
		try{
			//1.根据条件分页查询报告数据
			IPage<BdcCheckReport> reportPage = reportService.findCheckReportPage(request, query);
			if(reportPage == null || reportPage.getRecords() == null || reportPage.getRecords().size() < 1){
				return R.data(null);
			}
			List<ReportTerminalResponse> resList = new ArrayList<>();
			//存储企业信息
			Map<Long,String> map = new HashMap<>();

			//2.查询每个报告对应的终端检测信息
			for(BdcCheckReport report : reportPage.getRecords()){
				ReportTerminalResponse res = new ReportTerminalResponse();
				BeanUtils.copyProperties(report, res);
				long reportId = report.getId();
				//2.1 查询报告关联的终端
				LambdaQueryWrapper<BdcTerminal> wrapper =Wrappers.lambdaQuery(BdcTerminal.class)
					.eq(BdcTerminal::getReportId, reportId)
					.eq(BdcTerminal::getIsDel,0)
					.orderByDesc(BdcTerminal::getCreateTime)
					.orderByDesc(BdcTerminal::getId);
				if (request.getDeviceSeq()!=null){
					wrapper.like(BdcTerminal::getDeviceSeq, "%" + request.getDeviceSeq() + "%");
				}
				List<BdcTerminal> termianlList = terminalService.list(wrapper);
				//2.2 查询终端的检测数据
				//如果终端已经检测完成，则数据量查询终端表中的；否则，统计kudu表中的数据
				List<BdcTerminalResponse> tList = new ArrayList<>();
				for(BdcTerminal terminal : termianlList){
					BdcTerminalResponse response = new BdcTerminalResponse();
					BeanUtils.copyProperties(terminal, response);
					tList.add(response);
				}
				res.setTerminalCheckData(tList);
				resList.add(res);

				//2.3 查询企业名称
				Long companyId = res.getCompanyId();
				String companyName = map.get(companyId);
				if(com.alibaba.druid.util.StringUtils.isEmpty(companyName)){
					BdcCompany company = companyService.getOne(Wrappers.lambdaQuery(BdcCompany.class).eq(BdcCompany::getId, companyId));
					res.setCompanyName(company==null?"":company.getCompanyName());
				}
			}

			//3.组合数据
			IPage<ReportTerminalResponse> resPage = new Page<>();
			BeanUtils.copyProperties(reportPage, resPage);
			resPage.setRecords(resList);
			return R.data(resPage);
		}catch (Exception e){
			log.error("[北斗识别检测]查询检测数据失败",e);
			return R.fail("查询失败");
		}
	}


	/**
	 * 新增
	 */
	@Log(menu = "入网终端管理", operation = Operation.INSERT, objectType = ObjectType.TERMINALCHECK)
	@PostMapping("/save")
	public R save(@Valid @RequestBody BdcTerminalRequest request, BladeUser user) {
		BdcTerminal terminal = new BdcTerminal();
		BeanUtil.copyProperties(request, terminal);
		terminal.setDeviceNo(terminal.getDeviceSeq());
		terminal.setIsDel(0);
		terminal.setCreateTime(new Date());
		boolean result = terminalService.save(terminal);
		if (result) {
			return R.data(ResultCode.SUCCESS.getCode(), terminal.getId().toString(),"新增成功");
		}
		return R.status(result);
	}

	/**
	 * 修改
	 */
	@Log(menu = "入网终端管理", operation = Operation.UPDATE, objectType = ObjectType.TERMINALCHECK)
	@PostMapping("/update")
	public R update(@Valid @RequestBody BdcTerminalRequest request, BladeUser user) {
		try{
			BdcTerminal terminalDB = terminalService.getById(request.getId());
			BdcTerminal terminal = new BdcTerminal();
			BeanUtil.copy(request, terminal);
			terminal.setUpdateTime(new Date());
			boolean flag = terminalService.updateById(terminal);
			log.info("执行结果为："+flag);
			if(flag){
				String result = new CompareUtils<BdcTerminal>().compare(terminalDB, terminal);
				//数据更改成功后，向
				return R.data(ResultCode.SUCCESS.getCode(), result,"编辑成功");
			}else{
				return R.fail("操作失败");
			}
		}catch (Exception e){
			log.error("操作失败", e);
			return R.fail("操作失败");
		}
	}


	/**
	 * 删除
	 */
	@Log(menu = "入网终端管理", operation = Operation.DELETE, objectType = ObjectType.TERMINALCHECK)
	@GetMapping("/remove")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids, BladeUser user) {
		//逻辑删除
		List<Long> list = Func.toLongList(ids);
		List<BdcTerminal> comList = new ArrayList<>();
		for(Long id : list){
			BdcTerminal terminal = new BdcTerminal();
			terminal.setId(id);
			terminal.setUpdateTime(new Date());
			terminal.setIsDel(1);
			comList.add(terminal);
		}
		return R.status(terminalService.updateBatchById(comList));
	}


}
