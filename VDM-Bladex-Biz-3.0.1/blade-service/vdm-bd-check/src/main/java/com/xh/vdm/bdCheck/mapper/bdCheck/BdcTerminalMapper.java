package com.xh.vdm.bdCheck.mapper.bdCheck;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCheckReport;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;
import com.xh.vdm.bdCheck.vo.coding.request.ProcessRequest;
import com.xh.vdm.bdCheck.vo.coding.response.PassListResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@DS("bdCheck")
public interface BdcTerminalMapper extends BaseMapper<BdcTerminal> {

	/**
	 * 根据终端编号查询所属企业名称
	 * @param terminalNo
	 * @return
	 */
	String getCompanyNameByTerminalNo(String terminalNo);

	/**
	 * 根据终端编号获取检测信息
	 * @param terminalNo
	 * @return
	 */
	BdcCheckReport getReportByTerminalNo(String terminalNo);

	// 可赋码设备的分页列表
	List<PassListResponse> getPassPage (@Param("request") ProcessRequest request, IPage<PassListResponse> page);

	// 根据device_no获取可赋码设备列表
	List<BdcTerminal> getPassListByDeviceNo (@Param("deviceNoList") List<String> deviceNoList);

	// 根据设备序列号，获取设备数量。
	int getCountByDeviceSeq (@Param("deviceSeq") String deviceSeq);

	// 根据设备赋码号，获取设备数量。
	int getCountByDeviceNum (@Param("deviceNum") String deviceNum);

	// 往vdm.bdm_device_code添加记录
	void addDeviceNum (@Param("testDevice") BdcTerminal testDevice);

	// 根据设备序列号，更新vdm.bdm_device_code记录。
	void editByDeviceSeq (@Param("testDevice") BdcTerminal testDevice);

	// 根据设备赋码号，更新vdm.bdm_device_code记录。
	void editByDeviceNum(@Param("testDevice") BdcTerminal testDevice);



	/**
	 * 重置终端检测状态
	 * @param reportId
	 */
	void resetTerminalByReportId(Long reportId);

	/**
	 * 查询给定的终端编号中已经存在的终端编号
	 * @param deviceNoList 字符串数组的格式，'{123,123,123}'
	 * @return
	 */
	List<String> getExistDeviceNo(String deviceNoList);

	/**
	 * 根据给定的厂商id列表查询已经绑定了的终端列表
	 * @param manufacturerIds
	 * @return
	 */
	List<BdcTerminal> getTerminalListByManufacturerIds(List<Long> manufacturerIds);

	void updateByDeviceSeq(BdcTerminal bdcTerminal);

	/**
	 * 根据赋码号查询uniqueId
	 * @param deviceNum
	 * @return
	 */
	@DS("master")
	String getUniqueIdByDeviceNum(String deviceNum);

	/**
	 * 根据赋码号查询deviceId
	 * @param deviceNum
	 * @return
	 */
	@DS("master")
	Long getDeviceIdByDeviceNum(String deviceNum);

}
