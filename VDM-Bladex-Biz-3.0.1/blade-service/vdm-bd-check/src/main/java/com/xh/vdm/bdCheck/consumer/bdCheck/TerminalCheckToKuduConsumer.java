package com.xh.vdm.bdCheck.consumer.bdCheck;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.bdCheck.entity.BDCheckRealResult;
import com.xh.vdm.bdCheck.entity.bdCheck.BDCheckLocations;
import com.xh.vdm.bdCheck.entity.bdCheck.BDCheckLocationsKudu;
import lombok.extern.slf4j.Slf4j;
import org.apache.kudu.client.*;
import org.springblade.common.utils.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @Description: 从北斗识别检测结果topic读取数据，写入到kudu中，每条信息包含定位数据和识别结果数据
 * topic：TerminalCheckResult  kudu表：pos_gn.bd_check_locations
 * 从kafka读取数据入Kudu，使用 Kudu 原生 API
 * @Author: zhouxw
 * @Date: 2023/5/18 11:24
 */
@Component
@EnableKafka
@Slf4j
public class TerminalCheckToKuduConsumer {


	private ExecutorService threadPool = Executors.newFixedThreadPool(10);

	@Resource
	private KuduClient client;

	@Value("${kudu.check_table_name}")
	private String tableName;

	@Value("${kudu.batch-size}")
	private Long batchSize;

	/**
	 * @description: 读取kafka入kudu
	 * @author: zhouxw
	 * @date: 2023-05-138 11:30:27
	 * @param: [record]
	 * @return: void
	 **/
	@KafkaListener(containerFactory = "bdCheckResultListenerContainerFactory", topics = {"TerminalCheckResult"},  batch = "true")
	public void receiveToKudu(List<BDCheckLocationsKudu> consumerRecords, Acknowledgment acknowledgment){
		//入数据到kudu中
		try{
			if (CollectionUtils.isEmpty(consumerRecords)) {
				return;
			}
			KuduTable table = client.openTable(tableName);
			KuduSession kuduSession = client.newSession();
			// 采用手动刷新
			kuduSession.setFlushMode(SessionConfiguration.FlushMode.MANUAL_FLUSH);
			// 用于批量写入的临时缓冲区要足够大
			kuduSession.setMutationBufferSpace(300000000);


			// 创建每条的Insert
			for(int  i = 0; i < consumerRecords.size() ; i++){
				Upsert insert = table.newUpsert();
				PartialRow row = insert.getRow();
				BDCheckLocationsKudu lo = consumerRecords.get(i);

				setRow(row, lo);
				// 先不提交kudu
				kuduSession.apply(insert);
//				OperationResponse resp = kuduSession.apply(insert);
//				if (resp.hasRowError()) {
//					log.error("终端{}写入kudu报错:{}", lo.getDeviceNo(), resp.getRowError().toString());
//				}

				if ((i+1) % batchSize == 0) {
					// 批量写入kudu
					kuduSession.flush();
				}
			}

			// 最后，补加一条批量写入
			kuduSession.flush();
			// 关闭和kudu的会话
			kuduSession.close();
			log.info("[topic:TerminalCheckResult]北斗识别检测结果消费成功，共{}条", consumerRecords.size());
			// 手动提交offset
			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("[topic:TerminalCheckResult]北斗识别检测结果消费失败 :" + e.getMessage(), e);
		}
	}


	private void setRow(PartialRow row, BDCheckLocationsKudu loc){
		DecimalFormat decimalFormat = new DecimalFormat("0.#########");
		decimalFormat.setMinimumFractionDigits(10);
		decimalFormat.setMaximumFractionDigits(10);
		if(loc.getDeviceNo() != null){
			row.addString("device_no", loc.getDeviceNo());
		}
		if(loc.getLongitude() != null){
			row.addDecimal("longitude", new BigDecimal(decimalFormat.format(loc.getLongitude())));
		}
		if(loc.getLatitude() != null){
			row.addDecimal("latitude", new BigDecimal(decimalFormat.format(loc.getLatitude())));
		}
		if(loc.getLocTime() != null){
			row.addLong("loc_time",loc.getLocTime());
		}

		if(loc.getDeviceId() != null){
			row.addLong("device_id", loc.getDeviceId());
		}

		if(loc.getDeviceType() != null){
			row.addLong("device_type", loc.getDeviceType());
		}

		if(loc.getDeviceModel() != null){
			row.addString("device_model", loc.getDeviceModel());
		}

		if(loc.getTargetId() != null){
			row.addLong("target_id", loc.getTargetId());
		}

		if(loc.getTargetType() != null){
			row.addLong("target_type", loc.getTargetType());
		}

		if(loc.getTargetName() != null){
			row.addString("target_name", loc.getTargetName());
		}

		if(loc.getDeviceNum() != null){
			row.addString("device_num", loc.getDeviceNum());
		}

		if(loc.getAltitude() != null){
			row.addLong("altitude", loc.getAltitude());
		}
		if(loc.getSpeed() != null){
			row.addDouble("speed", loc.getSpeed());
		}
		if(loc.getBearing() != null){
			row.addLong("bearing", loc.getBearing());
		}
		if(loc.getAlarmFlag() != null){
			row.addLong("alarm_flag", loc.getAlarmFlag());
		}
		if(loc.getStateFlag() != null){
			row.addLong("state_flag", loc.getStateFlag());
		}
		if(loc.getRecvTime() != null){
			row.addLong("recv_time", loc.getRecvTime());
		}
		if(loc.getValid() != null){
			row.addInt("valid", loc.getValid());
		}
		if(loc.getMileage() != null){
			row.addDouble("mileage", loc.getMileage());
		}
		if(loc.getGnssNum() != null){
			row.addInt("gnss_num", loc.getGnssNum());
		}
		if(loc.getWireless() != null){
			row.addInt("wireless", loc.getWireless());
		}
		if(loc.getRealSpeed() != null){
			row.addDouble("real_speed", loc.getRealSpeed());
		}
		if(loc.getExpandSignal() != null){
			row.addLong("expand_signal", loc.getExpandSignal());
		}
		if(loc.getIoStatus() != null){
			row.addLong("io_status", loc.getIoStatus());
		}
		if(loc.getTemperature() != null){
			row.addString("temperature", loc.getTemperature());
		}
		if(loc.getBatch() != null){
			row.addLong("batch", loc.getBatch());
		}
		if(loc.getAuxStr() != null){
			row.addString("aux_str", loc.getAuxStr());
		}
		if(loc.getAuxsNormal() != null){
			row.addString("auxs_normal", loc.getAuxsNormal());
		}
		if(loc.getDsmUniqueId() != null){
			row.addString("dsm_unique_id", loc.getDsmUniqueId());
		}
		if(loc.getTeState() != null){
			row.addLong("te_state", loc.getTeState());
		}
		if(loc.getLocTimeF() != null){
			row.addString("loc_time_f", loc.getLocTimeF());
		}
		if(loc.getRecvTimeF() != null){
			row.addString("recv_time_f", loc.getRecvTimeF());
		}
		if(loc.getOfflineTime() != null){
			row.addString("off_line_time", loc.getOfflineTime());
		}
		if(loc.getDateStr() != null){
			row.addString("date_str", loc.getDateStr());
		}
		if(loc.getCheckRes() != null){
			row.addString("check_res", loc.getCheckRes());
		}
		if(loc.getCheckResMessage() != null){
			row.addString("check_res_message", loc.getCheckResMessage());
		}
		if(loc.getSatelliteData() != null){
			row.addString("satellite_data", loc.getSatelliteData());
		}

		if(loc.getCreateTime() != null){
			row.addLong("create_time", loc.getCreateTime());
		}else{
			row.addLong("create_time", new Date().getTime());
		}

	}

}
