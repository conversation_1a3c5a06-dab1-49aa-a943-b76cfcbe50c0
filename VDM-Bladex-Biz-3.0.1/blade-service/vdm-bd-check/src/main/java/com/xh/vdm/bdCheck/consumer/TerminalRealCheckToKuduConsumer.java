package com.xh.vdm.bdCheck.consumer;

import com.alibaba.druid.util.StringUtils;
import com.xh.vdm.bdCheck.entity.BDCheckRealResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.kudu.client.*;
import org.springblade.common.utils.DateUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @Description: 从北斗实时检测结果topic读取数据，写入到kudu中
 * topic：TerminalLocCheckResult  kudu表：pos_gn.bd_check_real_result
 * 从kafka读取数据入Kudu，使用 Kudu 原生 API
 * @Author: zhouxw
 * @Date: 2023/5/18 11:24
 */
@Component
@EnableKafka
@Slf4j
public class TerminalRealCheckToKuduConsumer {


	private ExecutorService threadPool = Executors.newFixedThreadPool(10);

	@Resource
	private KuduClient client;

	@Value("${kudu.table_name}")
	private String tableName;

	/**
	 * @description: 读取kafka入kudu
	 * @author: zhouxw
	 * @date: 2023-05-138 11:30:27
	 * @param: [record]
	 * @return: void
	 **/
	@KafkaListener(containerFactory = "bdCheckRealTerminalListenerContainerFactory", topics = {"TerminalLocCheckResult"}, batch = "true")
	public void receiveToKudu(List<BDCheckRealResult> consumerRecords, Acknowledgment acknowledgment){
		//入数据到kudu中
		try{
			if (CollectionUtils.isEmpty(consumerRecords)) {
				return;
			}
			KuduTable table = client.openTable(tableName);
			KuduSession kuduSession = client.newSession();
			// 采用手动刷新
			kuduSession.setFlushMode(SessionConfiguration.FlushMode.MANUAL_FLUSH);
			// 用于批量写入的临时缓冲区要足够大
			kuduSession.setMutationBufferSpace(300000000);


			// 创建每条的Insert
			for(int  i = 0 ; i < consumerRecords.size() ; i++){
				Upsert insert = table.newUpsert();
				PartialRow row = insert.getRow();
				BDCheckRealResult lo = consumerRecords.get(i);
				setRow(row, lo);
				// 先不提交kudu
				kuduSession.apply(insert);

				if (i % 1000 == 0){
					// 批量写入kudu
					kuduSession.flush();
				}
			}

			// 最后，补加一条批量写入
			kuduSession.flush();
			// 关闭和kudu的会话
			kuduSession.close();
			log.info("[topic:position_data]消息消费成功，共{}条", consumerRecords.size());
			// 手动提交offset
			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("[topic:position_data]消息消费失败 :" + e.getMessage(), e);
		}
	}


	private void setRow(PartialRow row, BDCheckRealResult loc){
		DecimalFormat decimalFormat = new DecimalFormat("0.#########");
		decimalFormat.setMinimumFractionDigits(10);
		decimalFormat.setMaximumFractionDigits(10);
		if(loc.getDeviceNum() != null){
			row.addString("device_num", loc.getDeviceNum());
		}
		if(loc.getLongitude() != null){
			row.addDecimal("longitude", new BigDecimal(decimalFormat.format(loc.getLongitude())));
		}
		if(loc.getLatitude() != null){
			row.addDecimal("latitude", new BigDecimal(decimalFormat.format(loc.getLatitude())));
		}
		if(loc.getLocTime() != null){
			row.addLong("loc_time",loc.getLocTime());
		}

		if(loc.getTerminalType() != null){
			row.addString("terminal_type", loc.getTerminalType());
		}

		if(loc.getTerminalModel() != null){
			row.addString("terminal_model", loc.getTerminalModel());
		}

		if(loc.getCheckRes() != null){
			row.addString("check_res", loc.getCheckRes());
		}

		if(loc.getCheckResMessage() != null){
			row.addString("check_res_message", loc.getCheckResMessage());
		}

		if(loc.getDeptName() != null){
			row.addString("dept_name", loc.getDeptName());
		}

		if(loc.getSatelliteData() != null){
			row.addString("satellite_data", loc.getSatelliteData());
		}

		if(loc.getCreateTime() != null){
			row.addLong("create_time", loc.getCreateTime());
		}else{
			row.addLong("create_time", new Date().getTime());
		}

		if(loc.getUpdateTime() != null){
			row.addLong("update_time", loc.getUpdateTime());
		}else{
			row.addLong("update_time", new Date().getTime());
		}

		if(!StringUtils.isEmpty(loc.getDateStr())){
			row.addString("date_str", loc.getDateStr());
		}else{
			row.addString("date_str", DateUtil.getDateString());
		}
	}

}
