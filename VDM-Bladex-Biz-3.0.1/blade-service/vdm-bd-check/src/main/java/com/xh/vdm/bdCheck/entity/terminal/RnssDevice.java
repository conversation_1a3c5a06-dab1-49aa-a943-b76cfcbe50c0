/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.bdCheck.entity.terminal;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2024-05-23
 */
@Data
@TableName("bdm_rnss_device")
@ApiModel(value = "RnssDevice对象", description = "RnssDevice对象")
public class RnssDevice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，唯一标识码
     */
    @ApiModelProperty(value = "主键，唯一标识码")
    private Long id;
    /**
     * 设备id，也叫终端号
     */
    @ApiModelProperty(value = "设备id，也叫终端号")
    private String uniqueId;
    /**
     * 国际移动设备识别码
     */
    @ApiModelProperty(value = "国际移动设备识别码")
    private String imei;
    /**
     * 终端型号
     */
    @ApiModelProperty(value = "终端型号")
    private String model;
    /**
     * 生产厂商名称
     */
    @ApiModelProperty(value = "生产厂商名称")
    private String vendor;
    /**
     * 北斗芯片序列号
     */
    @ApiModelProperty(value = "北斗芯片序列号")
    private String bdChipSn;
    /**
     * 终端类别，1-北斗定位终端，2-北斗穿戴式终端，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端
     */
    @ApiModelProperty(value = "终端类别，1-北斗定位终端，2-北斗穿戴式终端，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端")
    private Integer deviceType;
    /**
     * 特殊性，1-旧设备，2-新设备，3-特殊设备
     */
    @ApiModelProperty(value = "特殊性，1-旧设备，2-新设备，3-特殊设备")
    private Integer specificity;
    /**
     * 所属部门id
     */
    @ApiModelProperty(value = "所属部门id")
    private Long deptId;
    /**
     * 监控对象id
     */
    @ApiModelProperty(value = "监控对象id")
    private Long targetId;
    /**
     * 监控对象类型，1-车辆，2-人员，3-基础设施、4-集装箱
     */
    @ApiModelProperty(value = "监控对象类型，1-车辆，2-人员，3-基础设施、4-集装箱")
    private Integer targetType;
    /**
     * 监控对象名称，监控对象名称改变时，同步修改改字段
     */
    @ApiModelProperty(value = "监控对象名称，监控对象名称改变时，同步修改改字段")
    private String targetName;
    /**
     * 激活状态，0-未激活，1-已激活
     */
    @ApiModelProperty(value = "激活状态，0-未激活，1-已激活")
    private Integer activated;
    /**
     * 终端种类/功能类型，1-定位终端，2-视频终端，3-智能终端，4-工卡，5-集装箱追踪器，...
     */
    @ApiModelProperty(value = "终端种类/功能类型，1-定位终端，2-视频终端，3-智能终端，4-工卡，5-集装箱追踪器，...")
    private Integer category;
    /**
     * 赋码值，16位字符串
     */
    @ApiModelProperty(value = "赋码值，16位字符串")
    private String deviceNum;
    /**
     * 视频通道个数，视频终端和智能终端特有
     */
    @ApiModelProperty(value = "视频通道个数，视频终端和智能终端特有")
    private Integer channelNum;
    /**
     * 安装日期
     */
    @ApiModelProperty(value = "安装日期")
    private Date installdate;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date updateTime;
    /**
     * 0-未删除，1-已删除
     */
    @ApiModelProperty(value = "0-未删除，1-已删除")
    private Integer deleted;
    /**
     * 应用场景，详见 blade_dict_biz
表中相关字典定义
     */
    @ApiModelProperty(value = "应用场景，详见 blade_dict_biz 表中相关字典定义")
    private Integer scenario;
    /**
     * 应用方向/领域，详见
blade_dict_biz 表中相关字典定
义
     */
    @ApiModelProperty(value = "应用方向/领域，详见 blade_dict_biz 表中相关字典定义")
    private Integer domain;
    /**
     * 定位模式，详见blade_dict_biz	表中相关字典定义
     */
    @ApiModelProperty(value = "定位模式，详见blade_dict_biz表中相关字典定义")
    private Integer gnssMode;


}
