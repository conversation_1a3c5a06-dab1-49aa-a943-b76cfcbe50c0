package com.xh.vdm.bdCheck.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bdCheck.entity.BdmTerminal;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bdCheck.vo.BDCheckRealRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-20
 */
public interface BdmTerminalMapper extends BaseMapper<BdmTerminal> {

	/**
	 * 根据赋码值查询设备归属单位
	 * @param deviceNum
	 * @return
	 */
	String getDeptNameByDeviceNum(@Param("deviceNum") String deviceNum);

	/**
	 * 查询终端信息
	 * @param request
	 * @return
	 */
	List<BdmTerminal> getTerminalInfo(@Param("request") BDCheckRealRequest request);

	/**
	 * 分页查询终端信息
	 * @param request
	 * @return
	 */
	IPage<BdmTerminal> getTerminalInfoPage(@Param("request") BDCheckRealRequest request, IPage<BdmTerminal> page);

	/**
	 * 根据指定的厂商列表查询绑定这些厂商的北斗终端的数量（北斗资源管理）
	 * @param manufacturerIdsArray
	 * @return
	 */
	long getBDTerminalCountByManufacturerId(@Param("array") String manufacturerIdsArray);


}
