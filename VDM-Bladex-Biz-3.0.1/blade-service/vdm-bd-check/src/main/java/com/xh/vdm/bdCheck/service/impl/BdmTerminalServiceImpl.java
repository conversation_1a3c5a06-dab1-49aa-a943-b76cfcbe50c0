package com.xh.vdm.bdCheck.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bdCheck.entity.BdmTerminal;
import com.xh.vdm.bdCheck.mapper.BdmTerminalMapper;
import com.xh.vdm.bdCheck.service.IBdmTerminalService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bdCheck.vo.BDCheckRealRequest;
import org.springblade.common.utils.StringUtils;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.utils.StringUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-20
 */
@Service
public class BdmTerminalServiceImpl extends ServiceImpl<BdmTerminalMapper, BdmTerminal> implements IBdmTerminalService {

	@Override
	public String findDeptNameByDeviceNum(String deviceNum) throws Exception {
		return baseMapper.getDeptNameByDeviceNum(deviceNum);
	}

	@Override
	public boolean isExistDeviceNum(String deviceNum) throws Exception {
		return StringUtil.isNotBlank(baseMapper.getDeptNameByDeviceNum(deviceNum));
	}

	@Override
	public List<BdmTerminal> findTerminal(BDCheckRealRequest request) throws Exception {
		return baseMapper.getTerminalInfo(request);
	}

	@Override
	public IPage<BdmTerminal> findTerminalPage(BDCheckRealRequest request, Query query) throws Exception {
		IPage<BdmTerminal> page = new Page<>(query.getCurrent(), query.getSize());
		//赋码编号和终端型号添加模糊查询
		if(StringUtils.isNotEmpty(request.getDeviceNum())){
			request.setDeviceNum("%" + request.getDeviceNum() + "%");
		}
		if(StringUtils.isNotEmpty(request.getTerminalModel())){
			request.setTerminalModel("%" + request.getTerminalModel() + "%");
		}
		return baseMapper.getTerminalInfoPage(request, page);
	}
}
