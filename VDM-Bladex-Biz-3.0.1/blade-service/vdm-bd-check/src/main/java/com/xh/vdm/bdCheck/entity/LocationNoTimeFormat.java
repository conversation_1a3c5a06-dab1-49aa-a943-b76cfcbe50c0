package com.xh.vdm.bdCheck.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

// 位置
// 不包含格式化字段，防止反序列化失败
@Data
public class LocationNoTimeFormat implements Serializable {

	// 记录ID
	@JsonProperty("id")
	@JSONField(name = "id")
	private String id;

	// 目标类型（类型值与名称的映射，详见blade_dict_biz表code=target_type的记录）
	@JsonProperty("target_type")
	@JSONField(name = "target_type")
	private Byte targetType;

	// 目标ID（如车辆、人员、设施等）
	@JsonProperty("target_id")
	@JSONField(name = "target_id")
	private Long targetId;

	// 设备类别（类型值与名称的映射，详见blade_dict_biz表code=device_type_new的记录）
	@JsonProperty("device_type")
	@JSONField(name = "device_type")
	private Byte deviceType;

	// 设备ID
	@JsonProperty("device_id")
	@JSONField(name = "device_id")
	private Long deviceId;

	// 设备编号
	@JsonProperty("device_num")
	@JSONField(name = "device_num")
	private String deviceNum;

	//设备编号
	//终端入网之前进行测试时，使用该字段进行表示
	@JsonProperty("device_no")
	private String deviceNo;

	//定位时间
	@JsonProperty("loc_time")
	@JSONField(name = "loc_time")
	private Long locTime;

	// 经度
	@JsonProperty("longitude")
	@JSONField(name = "longitude")
	private Double longitude;

	// 纬度
	@JsonProperty("latitude")
	@JSONField(name = "latitude")
	private Double latitude;

	// 海拔（单位：米）
	@JsonProperty("altitude")
	@JSONField(name = "altitude")
	private Double altitude;

	// 速度（单位：km/h）
	@JsonProperty("speed")
	@JSONField(name = "speed")
	private Double speed;

	// 方向（0~359°）
	@JsonProperty("bearing")
	@JSONField(name = "bearing")
	private Short bearing;

	// 里程（单位：km）
	@JsonProperty("mileage")
	@JSONField(name = "mileage")
	private Double mileage;

	// 定位时间（秒数）
	@JsonProperty("loc_time")
	@JSONField(name = "loc_time")
	private Long time;

	// 接收时间（秒数）
	@JsonProperty("recv_time")
	@JSONField(name = "recv_time")
	private Long recvTime;

	// 定位有效性（0：无效，1：有效）
	@JsonProperty("valid")
	@JSONField(name = "valid")
	private Byte valid;

	// 是否纠正（0：未纠正，1：已纠正）
	@JsonProperty("correction")
	@JSONField(name = "correction")
	private Byte correction;

	// 上传方式（0：正常上传，1：批量上传，2：补传）
	@JsonProperty("batch")
	@JSONField(name = "batch")
	private Byte batch;

	// 定位系统（1：卫星，2：基站，3：WIFI，4：蓝牙，5：惯导）
	@JsonProperty("pos_system")
	@JSONField(name = "pos_system")
	private Byte posSystem;

	// 终端状态（位图）
	@JsonProperty("state_flag")
	@JSONField(name = "state_flag")
	private Integer status;

	// 终端告警（位图）
	@JsonProperty("alarm_flag")
	@JSONField(name = "alarm_flag")
	private Integer alarm;

	// 定位附加信息
	@JsonProperty("aux_str")
	@JSONField(name = "aux_str")
	private String auxiliary;
}
