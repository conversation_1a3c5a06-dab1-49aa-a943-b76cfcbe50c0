package com.xh.vdm.bdCheck.service.bdCheck;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bdCheck.entity.bdCheck.BDCheckLocations;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;

import java.util.List;

/**
 * 北斗识别检测
 * 查询impala
 * <AUTHOR>
 * @since 2024-04-30
 */
public interface IBDCheckLocationsService extends IService<BDCheckLocations> {

	/**
	 * 根据终端编号查询检测日期
	 * @param terminalNo
	 * @return
	 * @throws Exception
	 */
	List<String> findCheckDateList(String terminalNo) throws Exception;
}
