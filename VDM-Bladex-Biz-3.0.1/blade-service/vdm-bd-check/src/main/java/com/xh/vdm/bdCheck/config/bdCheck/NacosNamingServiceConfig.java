package com.xh.vdm.bdCheck.config.bdCheck;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.client.naming.NacosNamingService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@AllArgsConstructor
public class NacosNamingServiceConfig {

	private final NacosDiscoveryProperties nacosDiscoveryProperties;

	@Bean
	public NacosNamingService nacosNamingService () {
		try {
			return new NacosNamingService(this.nacosDiscoveryProperties.getNacosProperties());
		} catch (NacosException e) {
			log.error("fail run nacos naming service config: {}", e.getMessage(), e);
			return null;
		}
	}
}
