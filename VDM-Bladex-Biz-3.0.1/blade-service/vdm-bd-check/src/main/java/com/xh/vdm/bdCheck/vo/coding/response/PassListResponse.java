package com.xh.vdm.bdCheck.vo.coding.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "返回体：可赋码设备列表")
@Data
public class PassListResponse {

	@JsonProperty("id")
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	@JsonProperty("device_type")
	@ApiModelProperty(name = "device_type", value = "设备类型", example = "基础设备", required = true)
	private String deviceType;

	@JsonProperty("device_no")
	@ApiModelProperty(name = "device_no", value = "设备编号", example = "75020205673", required = true)
	private String deviceNo;

	@JsonProperty("device_seq")
	@ApiModelProperty(name = "device_seq", value = "设备序列号", example = "aaaa000011112222", required = true)
	private String deviceSeq;

	@JsonProperty("chip_seq")
	@ApiModelProperty(name = "chip_seq", value = "北斗芯片序列号", example = "bbbb555566667777", required = true)
	private String chipSeq;

	@JsonProperty("imei")
	@ApiModelProperty(name = "imei", value = "imei号", example = "860503076670787", required = true)
	private String imei;

	@JsonProperty("sim")
	@ApiModelProperty(name = "sim", value = "sim号", example = "13600000000", required = true)
	private String sim;

	@JsonProperty("device_num")
	@ApiModelProperty(name = "device_num", value = "设备赋码值", example = "TM53502405210005", required = true)
	private String deviceNum;

	@JsonProperty("device_num_sign")
	@ApiModelProperty(name = "device_num_sign", value = "赋码签名", example = "0445c2f453171d742ed5b74bd56bebe04ccec353b18f3850636ef692fd847b732f88d7224edd17de4910e98bb6acd90f45e84f4ea9a772df89e550b88a75d1053a04e6e771878f9a903ee4108c66edb98df4951e445eedec6dc1c244a544d81170319b8bffe9d3f80cb4a7b084a480c08bd159d37de1d6cba5d6cebd241673f9646586f78fc7e73bc0c4c3a7f5f0c1c32ddeae7bccbc83914a2526c5ba89178d8d0c308902c04c97441d4de44490a82cf556a77cdcf5ab865d", required = true)
	private String deviceNumSign;

	@JsonProperty("device_model")
	@ApiModelProperty(name = "device_model", value = "设备型号", example = "abcd12345678", required = true)
	private String deviceModel;

	@JsonProperty("manufacturer")
	@ApiModelProperty(name = "manufacturer", value = "设备厂商", example = "9999", required = true)
	private String manufacturer;

	@JsonProperty("protocol")
	@ApiModelProperty(name = "protocol", value = "通信协议", example = "808", required = true)
	private String protocol;

	@JsonProperty("batch_no")
	@ApiModelProperty(name = "batch_no", value = "检测批次", example = "20240627", required = true)
	private String batchNo;

	@JsonProperty("company_name")
	@ApiModelProperty(name = "company_name", value = "送检企业", example = "星航", required = true)
	private String companyName;

	@JsonProperty("send_date")
	@ApiModelProperty(name = "send_date", value = "送检时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-06-27 08:00:00", required = true)
	private String sendDate;

	@JsonProperty("code_time")
	@ApiModelProperty(name = "code_time", value = "赋码时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-06-27 08:00:00", required = true)
	private String codeTime;

	@JsonProperty("formal")
	@ApiModelProperty(name = "formal", value = "入网状态（0：未正式入网，1：已正式入网）", example = "0", required = true)
	private Byte formal;

	@JsonProperty("formal_str")
	@ApiModelProperty(name = "formal_str", value = "入网状态名称", example = "未正式入网", required = true)
	private String formalStr;

	@JsonProperty("code_state")
	@ApiModelProperty(name = "code_state", value = "赋码结果名称", example = "合格", required = true)
	private String codeResult;

	@JsonProperty("code_res_message")
	@ApiModelProperty(name = "code_res_message", value = "赋码结果描述", example = "设备未上线。", required = true)
	private String codeResMessage;

	@JsonProperty("code_machine")
	@ApiModelProperty(name = "code_machine", value = "赋码所使用的赋码机", example = "CE01", required = true)
	private String codeMachine;

	@JsonProperty("create_time")
	@ApiModelProperty(name = "create_time", value = "创建时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-06-27 08:00:00", required = true)
	private String createTime;

	@JsonProperty("update_time")
	@ApiModelProperty(name = "update_time", value = "更新时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-06-27 08:00:00", required = true)
	private String updateTime;
}
