package com.xh.vdm.bdCheck.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.bdCheck.entity.BDCheckRealResult;
import com.xh.vdm.bdCheck.service.IBDCheckRealResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kudu.Common;
import org.springblade.common.constant.CommonConstant;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.management.monitor.CounterMonitor;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class BDCheckTask {

	//用于存储每个终端在系统中的日期数据 key: device_num   value：dateStr list
	public static ConcurrentHashMap<String, List<String>> BD_CHECK_DATE_NEAR = new ConcurrentHashMap<>();

	//用于标识终端日期统计是否执行完毕：true 正在执行统计  false 执行完毕
	public static boolean statRunningFlag = false;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private IBDCheckRealResultService realResService;

	/**
	 * 北斗实时检测--统计每个终端在系统中存储的哪几天的数据
	 * 用于查询最近检测结果数据
	 */
	@Scheduled(cron="0 0 3 * * ?")
	public void statBDCheckDataNear(){
		try{
			//为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
			Object executingFlag = stringRedisTemplate.opsForValue().get(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.STAT_BD_CHECK_REAL_BEFORE);
			if(executingFlag != null && CommonConstant.COMMON_TRUE.equals(executingFlag.toString())){
				//如果已经有跑批程序在执行，则不再执行
				log.info("[车辆行驶时长统计]跑批任务已经在执行，请等当前执行程序完成后再试");
				return ;
			}

			//获取执行权限之后，添加执行标记（有效期为1个小时）
			synchronized (this){
				stringRedisTemplate.opsForValue().set(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.STAT_BD_CHECK_REAL_BEFORE , CommonConstant.COMMON_TRUE , 1 , TimeUnit.HOURS);
			}

			statRunningFlag = true;
			//1.查询所有赋码号
			List<String> deviceNums = realResService.findRealCheckAllDeviceNum();
			//2.查询每个终端的最近上线时间
			for(String deviceNum : deviceNums){
				List<String> dateList = realResService.findRealCheckDateByDeviceNum(deviceNum);
				BD_CHECK_DATE_NEAR.put(deviceNum, dateList);
			}
		}catch (Exception e){
			log.error("数据统计失败",e);
		}finally {
			statRunningFlag = false;
			//执行完毕后，解除执行锁定
			synchronized (this){
				stringRedisTemplate.delete(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.STAT_BD_CHECK_REAL_BEFORE );
			}
		}
	}

	/**
	 * 删除最后3天之前的检测为北斗的实时检测结果数据
	 * 凌晨1点执行
	 */
	//todo 后续添加到xxl-job上
	//@XxlJob("updateRedisDict")
	@Scheduled(cron="0 0 1 * * ?")
	public void handleBDCheckDateNear(){

		try{
			//为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
			Object executingFlag = stringRedisTemplate.opsForValue().get(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.CLEAR_BD_CHECK_REAL_BEFORE);
			if(executingFlag != null && CommonConstant.COMMON_TRUE.equals(executingFlag.toString())){
				//如果已经有跑批程序在执行，则不再执行
				log.info("[车辆行驶时长统计]跑批任务已经在执行，请等当前执行程序完成后再试");
				return ;
			}

			//获取执行权限之后，添加执行标记（有效期为1个小时）
			synchronized (this){
				stringRedisTemplate.opsForValue().set(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.CLEAR_BD_CHECK_REAL_BEFORE , CommonConstant.COMMON_TRUE , 1 , TimeUnit.HOURS);
			}


			//1.获取每个终端在kudu中的日期数据
			statBDCheckDataNear();

			//2.删除3天之前的，识别为北斗的识别结果数据
			BD_CHECK_DATE_NEAR.forEach((deviceNum, dateList) -> {
				String dateStr = "";
				if(dateList != null && dateList.size() > 3){
					dateStr = dateList.get(3);
					realResService.remove(Wrappers.lambdaQuery(BDCheckRealResult.class)
						.eq(BDCheckRealResult::getCheckRes,"1")
						.le(BDCheckRealResult::getDateStr, dateStr));
				}
			});
			log.info("定时任务【删除最后3天之前的检测为北斗的实时检测结果数据】处理成功");
		}catch (Exception e){
			log.error("定时任务【删除最后3天之前的检测为北斗的实时检测结果数据】处理失败，",e);
		}finally {
			//执行完毕后，解除执行锁定
			synchronized (this){
				stringRedisTemplate.delete(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.CLEAR_BD_CHECK_REAL_BEFORE );
			}
		}

	}

}
