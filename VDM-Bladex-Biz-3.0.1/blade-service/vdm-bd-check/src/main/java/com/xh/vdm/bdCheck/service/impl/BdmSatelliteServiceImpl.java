package com.xh.vdm.bdCheck.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.bdCheck.entity.BdmSatellite;
import com.xh.vdm.bdCheck.mapper.BdmSatelliteMapper;
import com.xh.vdm.bdCheck.service.IBdmSatelliteService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 星历数据，年表，从中国卫星导航系统管理办公室测试评估研究中心下载的广播星历数据 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Service
@Slf4j
public class BdmSatelliteServiceImpl extends ServiceImpl<BdmSatelliteMapper, BdmSatellite> implements IBdmSatelliteService {


	@Autowired
	protected ApplicationContext applicationContext;

	@Override
	public void saveOrUpdateSatelliteBatch(List<BdmSatellite> list) {
		//todo mybatis plus 的批量操作实际上还是单条操作，太慢，后续改成jdbc 批量操作
		saveOrUpdateBatch(list);
		/*DataSource dataSource = applicationContext.getBean(DataSource.class);
		Connection conn = null;
		try {
			conn = dataSource.getConnection();
			conn.setAutoCommit(false);


			//PreparedStatement pstmt = conn.prepareStatement("update content set introtext=? where id=?");


			for(BdmSatellite satellite : list){
				baseMapper.saveOrUpdateSatellite(satellite);
			}
			conn.commit();
		} catch (Exception e) {
			log.error("批量入库星历数据失败", e);
		}finally {
			if(conn != null){
				try {
					conn.close();
				} catch (Exception e) {
				}
			}
		}*/
	}

	@Override
	public List<BdmSatellite> findSatelliteByDay(String dateStr) {
		String year = dateStr.substring(0,4);
		String month = dateStr.substring(5,7);
		String day = dateStr.substring(8,10);
		List<BdmSatellite> list = list(Wrappers.lambdaQuery(BdmSatellite.class).eq(BdmSatellite::getYear, year).eq(BdmSatellite::getMon, month).eq(BdmSatellite::getDay, day));
		return list;
	}

	@Override
	public List<BdmSatellite> findAllSatelliteInDateList(String num, List<String> dateList) {
		return baseMapper.getAllSatelliteInDateList(num, dateList);
	}

	@Override
	public List<BdmSatellite> findAllSatelliteInDateList(List<String> dateList) {
		return baseMapper.getAllSatelliteInDateListNoNum(dateList);
	}

	@Override
	public List<BdmSatellite> dataInfo() {
		return baseMapper.selectSateDataList();
	}

	@Override
	public List<BdmSatellite> findNewestSatellite(int beforeDays) throws Exception {
		Date dayBefore = DateUtil.getDateBeforeDay(new Date(),beforeDays);
		String dayBeforeStr = DateUtil.getDateString(dayBefore.getTime()/1000);
		return baseMapper.selectNewestSatellite(dayBeforeStr);
	}
}
