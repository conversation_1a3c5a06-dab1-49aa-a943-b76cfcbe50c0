package com.xh.vdm.bdCheck.entity.bdCheck;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springblade.common.utils.compare.Compare;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 检测记录信息
 * <AUTHOR>
 * @since 2024-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(schema = "bd_check")
public class BdcCheckReport implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 批次号
     */
	@Compare("批次号")
    private String batchNo;

    /**
     * 委托单位id
     */
	@JsonSerialize(using = ToStringSerializer.class)
	@Compare("委托企业id")
    private Long companyId;

    /**
     * 检测开始时间
     */
	@Compare("检测开始时间")
    private Date checkStartTime;

    /**
     * 检测结束日期
     */
	@Compare("检测结束时间")
    private Date checkEndTime;

    /**
     * 生成报告日期
     */
    private Date reportTime;

    /**
     * 检测方式：1 抽检   2 其他
     */
	@Compare("抽检方式")
    private String checkType;

    /**
     * 检测地点
     */
	@Compare("检测地点")
    private String checkAddress;

    /**
     * 检测依据
     */
	@Compare("检测依据")
    private String checkBase;

    /**
     * 检测方法
     */
	@Compare("检测方法")
    private String checkMethod;

    /**
     * 检测结果标识
     */
    private Integer checkResult;

    /**
     * 检测结果描述
     */
    private String checkResMessage;

	//接口检测结果
	private Integer testResult;
	//接口检测时间
	private Date testTime;

	/**
	 * 检测进度
	 * 0 检测未开始   1 在检    2 检测完成
	 */
	private String checkProcess;

    /**
     * 编制人员
     */
    private String editPerson;

    /**
     * 审核人员
     */
    private String auditPerson;

    /**
     * 批准人员
     */
    private String approvePerson;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 状态：U 生效   E 失效
     */
    private String state;

	//终端类型：（B：基础设备，N：导航定位设备，C：短报文通信设备，T：授时设备，D：探测监测设备，M：定位模组）
	@Compare("终端类型")
	private String terminalType;

	//终端型号
	@Compare("终端型号")
	private String terminalModel;

	//终端类别：（M：模组，T：终端）
	@Compare("终端类别")
	private String terminalCate;

	//北斗识别报告文件路径
	private String reportFile;

	//报告编号
	@Compare("报告编号")
	private String reportNo;

	//送检时间
	@Compare("送检时间")
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date sendDate;
}
