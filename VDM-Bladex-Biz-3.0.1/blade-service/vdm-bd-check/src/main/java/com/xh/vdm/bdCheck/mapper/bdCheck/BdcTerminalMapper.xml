<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bdCheck.mapper.bdCheck.BdcTerminalMapper">

    <select id="getCompanyNameByTerminalNo" resultType="string">
        select b.company_name
        from bdc_terminal a, bdc_company b
        where a.company_id = b.id
        and a.device_no = #{terminalNo} and a.is_del = 0
    </select>

    <select id="getReportByTerminalNo" resultType="com.xh.vdm.bdCheck.entity.bdCheck.BdcCheckReport">
        select *
        from bdc_terminal a, bdc_check_report b
        where a.report_id = b.id
        and a.device_no = #{terminalNo} and a.is_del = 0
    </select>

    <select id="getPassPage" resultType="com.xh.vdm.bdCheck.vo.coding.response.PassListResponse">
        select a.*, b.batch_no, b.send_date, c.company_name from bdc_terminal as a
        left join bdc_check_report as b on a.report_id = b.id
        left join bdc_company as c on b.company_id = c.id
        where a.is_del = 0
            <!-- code_machine = 'CE01' 表示是平台赋码 -->
            and (
                a.code_machine != 'CE01' or
                (a.code_machine = 'CE01' and a.test_result = 1 and b.check_process in ('2','3'))
        )
        <if test="request.deviceType != null and request.deviceType.length() gt 0">
            and a.device_type = #{request.deviceType}
        </if>
        <if test="request.deviceNo != null and request.deviceNo.length() gt 0">
            and a.device_no like concat('%', #{request.deviceNo}, '%')
        </if>
        <if test="request.deviceNum != null and request.deviceNum.length() gt 0">
            and a.device_num like concat('%', #{request.deviceNum}, '%')
        </if>
        <if test="request.batchNo != null and request.batchNo.length() gt 0">
            and b.batch_no = #{request.batchNo}
        </if>
        <if test="request.companyId != null">
            and b.company_id = #{request.companyId}
        </if>
        <if test="request.formal != null">
            and a.formal = #{request.formal}
        </if>
        <if test="request.codeResult != null">
            and a.code_result = #{request.codeResult}
        </if>
        <if test="request.codeMachine != null and request.codeMachine.length() gt 0">
            and a.code_machine like concat('%', #{request.codeMachine}, '%')
        </if>
        <if test="request.manufacturer != null and request.manufacturer.length() gt 0">
            and a.manufacturer = #{request.manufacturer}
        </if>
        <if test="request.deviceModel != null and request.deviceModel.length() gt 0">
            and a.device_model = #{request.deviceModel}
        </if>

        <if test="request.codeTime != null and request.codeTime.length() gt 0">
            and a.code_time = CAST(#{request.codeTime} AS timestamp with time zone)
        </if>

        order by a.create_time desc nulls last
    </select>

    <select id="getPassListByDeviceNo" resultType="com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal">
        select a.* from bdc_terminal as a inner join bdc_check_report as b on a.report_id = b.id
        where (a.is_del is null or a.is_del = 0) and a.code_machine = 'CE01' and a.test_result = 1 and b.check_process = '3'
        <if test="deviceNoList != null and deviceNoList.size() gt 0">
            and a.device_no in
            <foreach collection="deviceNoList" item="deviceNo" separator="," open="(" close=")">
                #{deviceNo}
            </foreach>
        </if>
    </select>

    <select id="getCountByDeviceSeq" resultType="int">
        select count(1) from vdm.bdm_device_code where serial = #{deviceSeq}
    </select>

    <select id="getCountByDeviceNum" resultType="int">
        select count(1) from vdm.bdm_device_code where device_num = #{deviceNum}
    </select>

    <insert id="addDeviceNum">
        insert into vdm.bdm_device_code values (
            #{testDevice.deviceNum}, #{testDevice.deviceNumSign}, #{testDevice.deviceType}, #{testDevice.manufacturer},
            #{testDevice.deviceModel}, #{testDevice.deviceSeq}, #{testDevice.imei}, #{testDevice.chipSeq},
            current_timestamp, 0, current_timestamp
        )
    </insert>

    <update id="editByDeviceSeq">
        update vdm.bdm_device_code set
            device_num = #{testDevice.deviceNum},
            signature = #{testDevice.deviceNumSign},
            kind = #{testDevice.deviceType},
            vendor = #{testDevice.manufacturer},
            model = #{testDevice.deviceModel},
            imei = #{testDevice.imei},
            bd_chip_serial = #{testDevice.chipSeq},
            update_time = current_timestamp
        where serial = #{testDevice.deviceSeq}
    </update>

    <update id="editByDeviceNum">
        update vdm.bdm_device_code set
            signature = #{testDevice.deviceNumSign},
            kind = #{testDevice.deviceType},
            vendor = #{testDevice.manufacturer},
            model = #{testDevice.deviceModel},
            serial = #{testDevice.deviceSeq},
            imei = #{testDevice.imei},
            bd_chip_serial = #{testDevice.chipSeq},
            update_time = current_timestamp
        where device_num = #{testDevice.deviceNum}
    </update>



    <update id="resetTerminalByReportId" parameterType="long">
        update bdc_terminal
        set
        test_result = 0,
        code_result = 0,
        check_result = '0',
        check_res_message = null,
        check_total_data_count = null,
        check_bd_data_count = null,
        check_non_bd_count = null,
        check_start_date = null,
        check_end_date = null,
        test_time = null,
        code_time = null,
        test_res_message = null
        where report_id = #{reportId} and is_del = 0
    </update>

    <select id="getExistDeviceNo" resultType="string">
        select distinct device_no from bdc_terminal where device_no = any (${deviceNoList}) and is_del = 0;
    </select>

    <select id="getTerminalListByManufacturerIds" resultType="com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal">
        select bt.*
        from bdc_terminal bt, bdc_manufactor bm
        where bt.manufacturer  = bm.code and bt.is_del = 0
          and bm.id in
            <foreach collection="manufacturerIds" open="(" close=")" separator="," item="id">
                #{id}
            </foreach>
    </select>

    <update id="updateByDeviceSeq">
        UPDATE bdc_terminal
        <set>
            <!--<if test="null != deviceCate and '' != deviceCate">device_cate = #{deviceCate},</if>-->
            <if test="null != deviceType and '' != deviceType">device_type = #{deviceType},</if>
            <if test="null != deviceNo and '' != deviceNo">device_no = #{deviceNo},</if>
            <if test="null != deviceSeq and '' != deviceSeq">device_seq = #{deviceSeq},</if>
            <if test="null != imei and '' != imei">imei = #{imei},</if>
            <if test="null != chipSeq and '' != chipSeq">chip_seq = #{chipSeq},</if>
            <if test="null != sim and '' != sim">sim = #{sim},</if>
            <if test="null != deviceNum and '' != deviceNum">device_num = #{deviceNum},</if>
            <if test="null != deviceNumSign and '' != deviceNumSign">device_num_sign = #{deviceNumSign},</if>
            <if test="null != deviceModel and '' != deviceModel">device_model = #{deviceModel},</if>
            <if test="null != manufacturer and '' != manufacturer">manufacturer = #{manufacturer},</if>
            <if test="null != protocol and '' != protocol">protocol = #{protocol},</if>
            <if test="null != testResult">test_result = #{testResult},</if>
            <if test="null != codeResult">code_result = #{codeResult},</if>
            <if test="null != companyId">company_id = #{companyId},</if>
            <if test="null != reportId">report_id = #{reportId},</if>
            <if test="null != checkResult">check_result = #{checkResult},</if>
            <if test="null != checkResMessage and '' != checkResMessage">check_res_message = #{checkResMessage},</if>
            <if test="null != checkTotalDataCount">check_total_data_count = #{checkTotalDataCount},</if>
            <if test="null != checkBDDataCount">check_bd_data_count = #{checkBDDataCount},</if>
            <if test="null != checkNonBDCount">check_non_bd_count = #{checkNonBDCount},</if>
            <if test="null != checkStartDate">check_start_date = #{checkStartDate},</if>
            <if test="null != checkEndDate">check_end_date = #{checkEndDate},</if>
            <if test="null != testTime">test_time = #{testTime},</if>
            <if test="null != codeTime">code_time = #{codeTime},</if>
            <if test="null != isDel">is_del = #{isDel},</if>
            <if test="null != createTime">create_time = #{createTime},</if>
            <if test="null != updateTime">update_time = #{updateTime},</if>
            <if test="null != isInCheck">is_in_check = #{isInCheck},</if>
            <if test="null != testResMessage and '' != testResMessage">test_res_message = #{testResMessage},</if>
            <if test="null != codeResMessage and '' != codeResMessage">code_res_message = #{codeResMessage},</if>
            <if test="null != codeMachine and '' != codeMachine">code_machine = #{codeMachine},</if>
            <if test="null != formal">formal = #{formal}</if>
        </set>
        where device_seq = #{deviceSeq} and is_del = 1
    </update>

    <select id="getUniqueIdByDeviceNum" parameterType="string" resultType="string">
        select unique_id from bdm_rnss_device where device_num = #{deviceNum,jdbcType=VARCHAR}
        union
        select unique_id from bdm_rdss_device where device_num = #{deviceNum,jdbcType=VARCHAR}
        union
        select unique_id from bdm_wearable_device where device_num = #{deviceNum,jdbcType=VARCHAR}
        union
        select unique_id from bdm_monit_device where device_num = #{deviceNum,jdbcType=VARCHAR}
        union
        select unique_id from bdm_pnt_device where device_num = #{deviceNum,jdbcType=VARCHAR}
    </select>

    <select id="getDeviceIdByDeviceNum" parameterType="string" resultType="long">
        select id from bdm_abstract_device bad where device_num = #{deviceNum,jdbcType=VARCHAR};
    </select>

</mapper>
