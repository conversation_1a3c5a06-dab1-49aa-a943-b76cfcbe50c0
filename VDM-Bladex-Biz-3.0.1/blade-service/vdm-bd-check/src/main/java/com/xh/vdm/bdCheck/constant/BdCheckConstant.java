package com.xh.vdm.bdCheck.constant;

public interface BdCheckConstant {

	// 赋码机是否禁用：否
	byte CODING_MACHINE_DISABLED_NO = 0;

	// 赋码机是否禁用：是
	byte CODING_MACHINE_DISABLED_YES = 1;

	// 赋码机启用状态：未启用
	byte CODING_MACHINE_ENABLE_NO = 0;

	// 赋码机启用状态：已启用
	byte CODING_MACHINE_ENABLE_YES = 1;

	// 赋码状态：成功
	byte CODE_STATE_DONE = 1;

	// 赋码状态：失败
	byte CODE_STATE_FAIL = 2;

	// 协议：808
	String DEVICE_PROTOCOL_808 = "JT/T-808";

	// 协议：MQTT
	String DEVICE_PROTOCOL_MQTT = "MQTT";



	//用于入网检测的终端在更新数据后，向kafka发送消息，入网检测服务（go）用于更新本地缓存
	String TOPIC_TERMINAL_UPDATE = "device_target_change_check_topic";
}
