package com.xh.vdm.bdCheck.vo.coding.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.bdCheck.vo.coding.request.group.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;

@ApiModel(value = "请求体：赋码流程")
@Data
public class ProcessRequest {

	@JsonProperty("device_num")
	@ApiModelProperty(name = "device_num", value = "赋码号", example = "TNCE012404090001", required = true)
	@NotEmpty(message = "赋码号为空。", groups = {DeliverDeviceNumGroup.class, SyncFromMachineGroup.class})
	@Pattern(regexp = "\\w{16}", message = "赋码号不正确。", groups = {DeliverDeviceNumGroup.class, SyncFromMachineGroup.class})
	private String deviceNum;

	@JsonProperty("device_num_sign")
	@ApiModelProperty(name = "device_num_sign", value = "赋码签名", example = "04fb20adb05b57b123318654910fc76fbdd0fe1d51f682c3a2b68e37366ad95bebb5245ee0a3277bce2678a6bebc50424a020eadc7bfd4bfedce38b7514b8035cba8f2eb924eb28f29d22b1c2aeb2922d6587c2528a3407742db2ea1570e7aa0b06ca28f57684c6f784b4ccb24b8fa575da4e9afa029a916543e116b39d0e32542b5a09731b0f7f2ad3b28af89e072191775a9f617266632185c1e78", required = true)
	@NotEmpty(message = "赋码签名为空。", groups = {DeliverDeviceNumGroup.class, SyncFromMachineGroup.class})
	@Pattern(regexp = "[a-f\\d]+", message = "赋码签名不正确。", groups = {DeliverDeviceNumGroup.class, SyncFromMachineGroup.class})
	private String deviceNumSign;

	@JsonProperty("device_type")
	@ApiModelProperty(name = "device_type", value = "设备类型（类型值与名称的映射，详见blade_dict_biz表中code=test_device_type的记录）", example = "B", required = true)
	@NotEmpty(message = "设备类型为空。", groups = {CheckParamGroup.class, SyncFromMachineGroup.class})
	private String deviceType;

	@JsonProperty("device_no")
	@ApiModelProperty(name = "device_no", value = "设备编号", example = "75020205673", required = true)
	@NotEmpty(message = "设备编号为空。", groups = {CheckParamGroup.class, FormDeviceNumGroup.class, DeliverDeviceNumGroup.class})
	private String deviceNo;

	@JsonProperty("device_seq")
	@ApiModelProperty(name = "device_seq", value = "设备序列号", example = "aaaa000011112222", required = true)
	@NotEmpty(message = "设备序列号为空。", groups = {CheckParamGroup.class, SyncFromMachineGroup.class})
	@Pattern(regexp = "\\w{1,16}", message = "设备序列号不正确。", groups = {SyncFromMachineGroup.class})
	private String deviceSeq;

	@JsonProperty("chip_seq")
	@ApiModelProperty(name = "chip_seq", value = "北斗芯片序列号", example = "aaaa000011112222", required = true)
	@NotEmpty(message = "北斗芯片序列号为空。", groups = {CheckParamGroup.class, SyncFromMachineGroup.class})
	private String chipSeq;

	@JsonProperty("imei")
	@ApiModelProperty(name = "imei", value = "imei号", example = "860503076670787", required = true)
	@NotEmpty(message = "imei号为空。", groups = {CheckParamGroup.class, SyncFromMachineGroup.class})
	private String imei;

	@JsonProperty("manufacturer")
	@ApiModelProperty(name = "manufacturer", value = "设备厂商", example = "0000", required = true)
	@NotEmpty(message = "设备厂商为空。", groups = {CheckParamGroup.class, SyncFromMachineGroup.class})
	@Pattern(regexp = "\\d{4}", message = "设备厂商不正确。", groups = {SyncFromMachineGroup.class})
	private String manufacturer;

	@JsonProperty("device_model")
	@ApiModelProperty(name = "device_model", value = "设备型号", example = "abcd12345678", required = true)
	@NotEmpty(message = "设备型号为空。", groups = {CheckParamGroup.class, SyncFromMachineGroup.class})
	private String deviceModel;

	@JsonProperty("batch_no")
	@ApiModelProperty(name = "batch_no", value = "批次号", example = "20240627", required = true)
	private String batchNo;

	@JsonProperty("company_id")
	@ApiModelProperty(name = "company_id", value = "送检企业ID", example = "1", required = true)
	private Long companyId;

	@JsonProperty("formal")
	@ApiModelProperty(name = "formal", value = "入网状态（0：未正式入网，1：已正式入网）", example = "0", required = true)
	private Byte formal;

	@JsonProperty("code_result")
	@ApiModelProperty(name = "code_result", value = "赋码结果（结果值与名称的映射，详见blade_dict_biz表code=code_result的记录）", example = "0", required = true)
	private Byte codeResult;

	@JsonProperty("code_time")
	@ApiModelProperty(name = "code_time", value = "赋码时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-06-27 08:00:00", required = true)
	@NotEmpty(message = "赋码时间为空。", groups = {SyncFromMachineGroup.class})
	@Pattern(regexp = "\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}", message = "赋码时间不正确。", groups = {SyncFromMachineGroup.class})
	private String codeTime;

	@JsonProperty("code_machine")
	@ApiModelProperty(name = "code_machine", value = "赋码机编号", example = "CE01", required = true)
	@NotEmpty(message = "赋码机编号为空。", groups = {SyncFromMachineGroup.class})
	@Pattern(regexp = "[A-Z]{2}\\d{2}", message = "赋码机编号不正确。", groups = {SyncFromMachineGroup.class})
	private String codeMachine;

	@JsonProperty("device_no_list")
	@ApiModelProperty(name = "device_no_list", value = "设备编号列表", example = "['75020205673', ...]", required = true)
	@NotEmpty(message = "设备编号列表为空。", groups = {BatchCompleteProcessGroup.class})
	private List<String> deviceNoList;
}
