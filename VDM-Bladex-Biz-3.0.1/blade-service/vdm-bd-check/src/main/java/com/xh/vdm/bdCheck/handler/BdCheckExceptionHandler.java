package com.xh.vdm.bdCheck.handler;

import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

//@ControllerAdvice
//@ResponseBody
public class BdCheckExceptionHandler {

	@ExceptionHandler(value = MethodArgumentNotValidException.class)
	public R<String> handleReqParamErr (MethodArgumentNotValidException e) {
		BindingResult br = e.getBindingResult();
		if (!br.hasErrors()) {
			return R.fail(ResultCode.FAILURE, "请求参数校验结果异常。");
		}

		List<ObjectError> errList = br.getAllErrors();
		if (CollectionUtils.isEmpty(errList)) {
			return R.fail(ResultCode.FAILURE, "请求参数校验结果为空。");
		}

		StringBuffer sb = new StringBuffer();
		errList.forEach(err -> {
			FieldError fe = (FieldError) err;
			sb.append(fe.getField()).append("：").append(fe.getDefaultMessage()).append("\n");
		});

		return R.fail(ResultCode.FAILURE, sb.toString());
	}
}
