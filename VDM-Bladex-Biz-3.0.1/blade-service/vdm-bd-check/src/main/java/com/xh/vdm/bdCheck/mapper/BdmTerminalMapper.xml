<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.bdCheck.mapper.BdmTerminalMapper">

    <select id="getDeptNameByDeviceNum" resultType="string">
        select dept_name
        from blade_dept
        where id in
              (
                  select dept_id from bdm_rnss_device where device_num = #{deviceNum,jdbcType=VARCHAR}
                  union
                  select dept_id from bdm_rdss_device where device_num = #{deviceNum,jdbcType=VARCHAR}
                  union
                  select dept_id from bdm_wearable_device where device_num = #{deviceNum,jdbcType=VARCHAR}
                  union
                  select dept_id from bdm_monit_device where device_num = #{deviceNum,jdbcType=VARCHAR}
                  union
                  select dept_id from bdm_pnt_device where device_num = #{deviceNum,jdbcType=VARCHAR}
              )
    </select>

    <select id="getTerminalInfo" resultType="com.xh.vdm.bdCheck.entity.BdmTerminal">
        select * from bdm_terminal
        where 1 = 1
        <if test="device_num != null and device_num != ''">
            and device_num = #{request.deviceNum,jdbcType=VARCHAR}
        </if>
        <if test="terminal_type != null and terminal_type != ''">
            and terminal_type = #{request.terminalType,jdbcType=VARCHAR}
        </if>
        <if test="terminal_model != null and terminal_model != ''">
            and terminal_model = #{request.terminalModel,jdbcType=VARCHAR}
        </if>
        <if test="deptIdList != null and deptIdList.size > 0">
            and dept_id in
            <foreach collection="deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
    </select>

    <select id="getTerminalInfoPage" resultType="com.xh.vdm.bdCheck.entity.BdmTerminal">
        select device_num,unique_id, device_type terminalType, model terminalModel, dept_id from bdm_abstract_device
        where 1 = 1
        <if test="request.deviceNum != null and request.deviceNum != ''">
            and device_num like #{request.deviceNum,jdbcType=VARCHAR}
        </if>
        <if test="request.terminalType != null and request.terminalType != ''">
            and terminalType::text = #{request.terminalType,jdbcType=VARCHAR}
        </if>
        <if test="request.terminalModel != null and request.terminalModel != ''">
            and terminalModel like #{request.terminalModel,jdbcType=VARCHAR}
        </if>
        <if test="request.deptIdList != null and request.deptIdList.size > 0">
            and dept_id in
            <foreach collection="request.deptIdList" item="deptId" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        order by unique_id
    </select>

    <select id="getBDTerminalCountByManufacturerId" resultType="long">
        select (select count(*) cnt from bdm_rnss_device where vendor = any(${array}) and deleted = 0)
                   + (select count(*) cnt from bdm_pnt_device where vendor = any(${array})  and deleted = 0)
                   + (select count(*) cnt from bdm_rdss_device where vendor = any(${array})  and deleted = 0)
                   + (select count(*) cnt from bdm_monit_device where vendor = any(${array})  and deleted = 0)
                   + (select count(*) cnt from bdm_wearable_device where vendor = any(${array})  and deleted = 0);
    </select>

</mapper>
