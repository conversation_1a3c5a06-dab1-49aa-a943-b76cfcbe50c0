package com.xh.vdm.bdCheck.vo.coding.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "返回体：赋码机列表")
@Data
public class MachineListResponse {

	@JsonProperty("code_machine_num")
	@ApiModelProperty(name = "code_machine_num", value = "赋码机编号", example = "CE01", required = true)
	private String codeMachineNum;

	@JsonProperty("manufacturer")
	@ApiModelProperty(name = "manufacturer", value = "使用赋码机的设备所属厂商编号", example = "9999", required = true)
	private String manufacturer;

	@JsonProperty("create_time")
	@ApiModelProperty(name = "create_time", value = "创建时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2024-06-27 08:00:00", required = true)
	private String createTime;

	@JsonProperty("enable")
	@ApiModelProperty(name = "enable", value = "启用状态（0：未启用，1：已启用）", example = "0", required = true)
	private Byte enable;
}
