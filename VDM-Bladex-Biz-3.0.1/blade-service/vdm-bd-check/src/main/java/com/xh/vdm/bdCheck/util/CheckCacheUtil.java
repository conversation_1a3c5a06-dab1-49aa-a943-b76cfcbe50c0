package com.xh.vdm.bdCheck.util;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.bdCheck.entity.bdCheck.BladeDictBiz;
import com.xh.vdm.bdCheck.service.bdCheck.IBladeDictBizService;
import org.springblade.common.constant.CommonConstant;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 缓存工具类
 * @Author: zhouxw
 * @Date: 2022/9/14 5:43 PM
 */
@Component
public class CheckCacheUtil {

    @Resource
    private IBladeDictBizService dictService;

    @Resource
    private RedisTemplate redisTemplate;


    /**
     * @description: 终端类型映射map
     * @author: zhouxw
     * @date: 2023-03-81 14:53:43
     * @param: []
     * @return: java.util.Map<java.lang.String,java.lang.Integer>
     **/
    public Map<String , String> getTerminalTypeMap(){
        Map<String, String> map = redisTemplate.opsForHash().entries(CommonConstant.PREFIX_DICTIONARY_HASH_KEY + CommonConstant.CACHE_TERMINAL_TYPE);
        if(map == null){
            map = new HashMap<>();
        }
        if(map.size() < 1){
            //如果缓存没有加载，则重新加载缓存
            //1.查询终端类型
            List<BladeDictBiz> terminalTypeList = dictService.list(Wrappers.lambdaQuery(BladeDictBiz.class).eq(BladeDictBiz::getIsDeleted, 0).eq(BladeDictBiz::getCode, CommonConstant.CACHE_TERMINAL_TYPE).ne(BladeDictBiz::getParentId,0));
            Map<String, String> finalMap = map;
            terminalTypeList.forEach(item -> {
                finalMap.put(item.getDictKey() , item.getDictValue());

                //2.加载到缓存
                redisTemplate.opsForHash().put(CommonConstant.PREFIX_DICTIONARY_HASH_KEY + CommonConstant.CACHE_TERMINAL_TYPE , item.getDictKey(), item.getDictValue());
				//有效期设置为1天
				redisTemplate.expire(CommonConstant.PREFIX_DICTIONARY_HASH_KEY + CommonConstant.CACHE_TERMINAL_TYPE,1, TimeUnit.DAYS);
            });
        }
        return map;
    }

	/**
	 * @description: 终端检测类型映射map
	 * @author: zhouxw
	 * @date: 2023-03-81 14:53:43
	 * @param: []
	 * @return: java.util.Map<java.lang.String,java.lang.Integer>
	 **/
	public Map<String , String> getCheckTerminalTypeMap(){
		Map<String, String> map = redisTemplate.opsForHash().entries(CommonConstant.PREFIX_DICTIONARY_HASH_KEY + CommonConstant.CACHE_CHECK_TERMINAL_TYPE);
		if(map == null){
			map = new HashMap<>();
		}
		if(map.size() < 1){
			//如果缓存没有加载，则重新加载缓存
			//1.查询终端类型
			List<BladeDictBiz> terminalTypeList = dictService.list(Wrappers.lambdaQuery(BladeDictBiz.class).eq(BladeDictBiz::getIsDeleted, 0).eq(BladeDictBiz::getCode, CommonConstant.CACHE_CHECK_TERMINAL_TYPE).ne(BladeDictBiz::getParentId,0));
			Map<String, String> finalMap = map;
			terminalTypeList.forEach(item -> {
				finalMap.put(item.getDictKey() , item.getDictValue());

				//2.加载到缓存
				redisTemplate.opsForHash().put(CommonConstant.PREFIX_DICTIONARY_HASH_KEY + CommonConstant.CACHE_CHECK_TERMINAL_TYPE , item.getDictKey(), item.getDictValue());
				//有效期设置为1天
				redisTemplate.expire(CommonConstant.PREFIX_DICTIONARY_HASH_KEY + CommonConstant.CACHE_CHECK_TERMINAL_TYPE,1, TimeUnit.DAYS);
			});
		}
		return map;
	}

}
