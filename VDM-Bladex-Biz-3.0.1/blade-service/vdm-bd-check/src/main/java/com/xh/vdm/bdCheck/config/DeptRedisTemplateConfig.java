package com.xh.vdm.bdCheck.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springblade.system.entity.DeptNode;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.SerializationException;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Configuration
public class DeptRedisTemplateConfig {
	@Bean("deptNodeRedisTemplate")
	public RedisTemplate<String, DeptNode> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
		RedisTemplate<String, DeptNode> template = new RedisTemplate<>();
		template.setConnectionFactory(redisConnectionFactory);

		// 使用StringRedisSerializer来序列化键
		template.setKeySerializer(new StringRedisSerializer());
		template.setHashKeySerializer(new Jackson2JsonRedisSerializer<>(Long.class));
		// 使用自定义序列化器来序列化值
		template.setValueSerializer(new DeptNodeRedisSerializer<>(DeptNode.class));
		template.setHashValueSerializer(new DeptNodeRedisSerializer<>(DeptNode.class));

		return template;
	}

	public static class DeptNodeRedisSerializer<T> implements RedisSerializer<T> {
		private final ObjectMapper objectMapper = new ObjectMapper();
		private final Class<T> type;

		public DeptNodeRedisSerializer(Class<T> type) {
			this.type = type;
		}

		@Override
		public byte[] serialize(T t) throws SerializationException {
			if (t == null) {
				return new byte[0];
			}
			try {
				return objectMapper.writeValueAsBytes(t);
			} catch (JsonProcessingException e) {
				throw new SerializationException("Failed to serialize object", e);
			}
		}

		@Override
		public T deserialize(byte[] bytes) throws SerializationException {
			if (bytes == null || bytes.length == 0) {
				return null;
			}
			try {
				return objectMapper.readValue(bytes, type);
			} catch (Exception e) {
				throw new SerializationException("Failed to deserialize object", e);
			}
		}
	}
}
