package com.xh.vdm.bdCheck.service.bdCheck.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bdCheck.entity.bdCheck.BladeDictBiz;
import com.xh.vdm.bdCheck.mapper.bdCheck.BladeDictBizMapper;
import com.xh.vdm.bdCheck.service.bdCheck.IBladeDictBizService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 业务字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-09
 */
@Service
public class BladeDictBizServiceImpl extends ServiceImpl<BladeDictBizMapper, BladeDictBiz> implements IBladeDictBizService {

	@Override
	public List<String> findProfessionList(Long professionId) {
		return baseMapper.getProfessionList(professionId);
	}
}
