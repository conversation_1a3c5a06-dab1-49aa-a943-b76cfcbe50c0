package com.xh.vdm.bdCheck.controller.coding;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bdCheck.constant.BdCheckConstant;
import com.xh.vdm.bdCheck.entity.coding.Machine;
import com.xh.vdm.bdCheck.service.coding.IMachineService;
import com.xh.vdm.bdCheck.vo.coding.request.MachineRequest;
import com.xh.vdm.bdCheck.vo.coding.request.group.AddMachineGroup;
import com.xh.vdm.bdCheck.vo.coding.request.group.EditMachineGroup;
import com.xh.vdm.bdCheck.vo.coding.request.group.LoginMachineGroup;
import com.xh.vdm.bdCheck.vo.coding.request.group.MachineListGroup;
import com.xh.vdm.bdCheck.vo.coding.response.MachineListResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.annotation.Log;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.beans.BeanUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Api(tags = "赋码机", protocols = "http", produces = "application/json", consumes = "application/json")
@RestController
@RequestMapping("/coding/machine")
public class MachineController {

	@Resource(name = "MachineService")
	private IMachineService machineService;

	@ApiOperation(value = "分页列表", httpMethod = "POST")
	@PostMapping("/page")
	public R<IPage<MachineListResponse>> getCodingMachinePage (@Validated(MachineListGroup.class) @RequestBody MachineRequest request, Query query) {
		try {
			IPage<Machine> tmp = this.machineService.getCodingMachinePage(request, query);
			IPage<MachineListResponse> res = new Page<>();
			BeanUtils.copyProperties(tmp, res);
			if (res.getTotal() <= 0) {
				return R.data(res);
			}

			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			List<MachineListResponse> responseList = new ArrayList<>();
			for (Machine machine : tmp.getRecords()) {
				MachineListResponse response = new MachineListResponse();
				response.setCodeMachineNum(machine.getNumber());
				response.setManufacturer(machine.getVendor());
				response.setCreateTime(format.format(machine.getCreateTime()));
				if ((machine.getDisabled() != null) && machine.getDisabled().equals(BdCheckConstant.CODING_MACHINE_DISABLED_NO)) {
					response.setEnable(BdCheckConstant.CODING_MACHINE_ENABLE_YES);
				} else {
					response.setEnable(BdCheckConstant.CODING_MACHINE_ENABLE_NO);
				}

				responseList.add(response);
			}

			res.setRecords(responseList);
			return R.data(res);
		} catch (Exception e) {
			log.error("fail get coding machine page: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "赋码机分页列表获取异常：" + e.getMessage());
		}
	}

	@Log(menu = "赋码机管理", operation = Operation.INSERT, objectType = ObjectType.MACHINE)
	@ApiOperation(value = "添加", httpMethod = "POST")
	@PostMapping("/add")
	public R<String> addCodingMachine (@Validated(AddMachineGroup.class) @RequestBody MachineRequest request, BladeUser user) {
		try {
			request.setCodeMachineNum(request.getCodeMachineNum().toUpperCase());
			return this.machineService.addCodingMachine(request);
		} catch (Exception e) {
			log.error("fail add coding machine: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "赋码机添加异常：" + e.getMessage());
		}
	}

	@Log(menu = "赋码机管理", operation = Operation.UPDATE, objectType = ObjectType.MACHINE)
	@ApiOperation(value = "更新", httpMethod = "POST")
	@PostMapping("/edit")
	public R<String> editCodingMachine (@Validated(EditMachineGroup.class) @RequestBody MachineRequest request, BladeUser user) {
		try {
			return this.machineService.editCodingMachine(request);
		} catch (Exception e) {
			log.error("fail edit coding machine: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "赋码机更新异常：" + e.getMessage());
		}
	}

	@ApiOperation(value = "登录", httpMethod = "POST")
	@PostMapping("/login")
	public R<String> loginCodingMachine (@Validated(LoginMachineGroup.class) @RequestBody MachineRequest request) {
		try {
			return R.data(this.machineService.loginCodingMachine(request));
		} catch (Exception e) {
			log.error("fail login coding machine: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "赋码机登录异常：" + e.getMessage());
		}
	}
}
