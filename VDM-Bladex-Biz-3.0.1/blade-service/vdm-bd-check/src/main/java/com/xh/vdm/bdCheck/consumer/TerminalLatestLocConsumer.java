package com.xh.vdm.bdCheck.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.bdCheck.entity.Location;
import com.xh.vdm.bdCheck.entity.LocationNoTimeFormat;
import com.xh.vdm.bdCheck.entity.TmpBdmLatestLoc;
import com.xh.vdm.bdCheck.service.ITmpBdmLatestLocService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 最终位置点
 * 临时使用，非正式方案
 * 从kafka读取数据，入PG，记录最终位置点
 */
@Component
@EnableKafka
@Slf4j
public class TerminalLatestLocConsumer {

	@Value("${spring.tmp-latest-loc.latest-loc.enable}")
	private boolean enable;

	@Resource
	private ITmpBdmLatestLocService locService;

	@KafkaListener(containerFactory = "tmpLatestLocListenerContainerFactory", batch = "true", topics = {"ce.comms.fct.location.0"})
	public void push(List<Object> consumerRecords, Acknowledgment acknowledgment){

		try{
			if(enable){
				log.debug("[TMP_LATEST_LOC]反序列化的数据为：{}", consumerRecords);
				if (CollectionUtils.isEmpty(consumerRecords)) {
					return;
				}
				log.debug("[TMP_LATEST_LOC]开始消费数据");
				log.debug("接收到的数据为："+ JSON.toJSONString(consumerRecords));
				for(int  i = 0 ; i < consumerRecords.size() ; i++){
					//1.将数据转换为最终位置点数据
					//兼容go和java写入的数据
					Object lnObj = consumerRecords.get(i);
					LocationNoTimeFormat ln = null;
					if(lnObj instanceof String){
						//如果是go
						try {
							ln = JSON.parseObject(lnObj.toString(), LocationNoTimeFormat.class);
						}catch (Exception e){
							log.error("反序列化失败，原始数据为：", lnObj.toString());
						}
					}else{
						ln = (LocationNoTimeFormat) lnObj;
					}

					log.debug("[TMP_LATEST_LOC]处理数据，数据为"+JSON.toJSONString(ln));
					if(ln == null){
						continue;
					}

					//只处理有效数据
					if(ln.getValid() != 1){
						continue;
					}

					TmpBdmLatestLoc loc = new TmpBdmLatestLoc();
					loc.setDeviceId(ln.getDeviceId());
					loc.setUniqueId(ln.getDeviceNo());
					loc.setLatitude(ln.getLatitude());
					loc.setLongitude(ln.getLongitude());
					Date date = new Date();
					if(ln != null && ln.getLocTime()!=null){
						date.setTime(ln.getLocTime() * 1000);
						loc.setTime(date);
					}
					log.debug("[TMP_LATEST_LOC]将要保存数据");
					locService.saveOrUpdate(loc, Wrappers.lambdaQuery(TmpBdmLatestLoc.class)
						.eq(TmpBdmLatestLoc::getDeviceId,ln.getDeviceId()));
					log.debug("[TMP_LATEST_LOC]保存数据成功");
				}
			}
			// 手动提交offset
			acknowledgment.acknowledge();
		} catch (Exception e) {
			log.error("[TMP_LATEST_LOC]消息消费失败 :" + e.getMessage(), e);
		}
	}
}
