package com.xh.vdm.bdCheck.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.kafka.KafkaProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.config.KafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;

/**
 * @Description: 终端北斗检测消费者
 * @Author: zhouxw
 * @Date: 2023/7/10 20:10
 */
@Configuration
public class TmpLatestLocConfig {


    @ConfigurationProperties(prefix = "spring.tmp-latest-loc")
    @Bean("tmpLatestLocProperties")
    public KafkaProperties locationKafkaProperties() {
        return new KafkaProperties();
    }

    //此处最好手动指定数据源factory的bean名称，在消费者端会用到
    @Bean("tmpLatestLocListenerContainerFactory")
    public KafkaListenerContainerFactory<ConcurrentMessageListenerContainer<Integer, String>>
    firstKafkaListenerContainerFactory(@Autowired @Qualifier("tmpLatestLocProperties") KafkaProperties firstKafkaProperties) {
        ConcurrentKafkaListenerContainerFactory<Integer, String> factory =
                new ConcurrentKafkaListenerContainerFactory<>();
        factory.setConsumerFactory(firstConsumerFactory(firstKafkaProperties));
        factory.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL_IMMEDIATE);
        factory.getContainerProperties().setMissingTopicsFatal(false);
		factory.getContainerProperties().setGroupId(firstKafkaProperties.getConsumer().getGroupId());
        return factory;
    }

    private ConsumerFactory<? super Integer, ? super String> firstConsumerFactory(KafkaProperties firstKafkaProperties) {
        return new DefaultKafkaConsumerFactory<>(firstKafkaProperties.buildConsumerProperties());
    }




}
