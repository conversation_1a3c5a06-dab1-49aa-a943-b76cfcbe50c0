package com.xh.vdm.bdCheck.entity.bdCheck;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 北斗识别检测：定位点检测数据，点位点+检测数据
 */
@Data
@TableName("bd_check_locations")
public class BDCheckLocations {

	@JsonProperty("device_no")
	private String deviceNo;

	@JsonProperty("longitude")
	private BigDecimal longitude;

	@JsonProperty("latitude")
	private BigDecimal latitude;

	@JsonProperty("loc_time")
	private Long locTime;

	@JsonProperty("device_id")
	private Long deviceId;

	@JsonProperty("device_type")
	private Long deviceType;

	@JsonProperty("device_model")
	private String deviceModel;

	@JsonProperty("target_id")
	private Long targetId;

	@JsonProperty("target_type")
	private Long targetType;

	@JsonProperty("target_name")
	private String targetName;

	@JsonProperty("device_num")
	private String deviceNum;

	@JsonProperty("altitude")
	private Long altitude;

	@JsonProperty("speed")
	private Double speed;

	@JsonProperty("bearing")
	private Long bearing;

	@JsonProperty("alarm_flag")
	private Long alarmFlag;

	@JsonProperty("state_flag")
	private Long stateFlag;

	@JsonProperty("recv_time")
	private Long recvTime;

	@JsonProperty("valid")
	private Integer valid;

	@JsonProperty("mileage")
	private Double mileage;

	@JsonProperty("gnss_num")
	private Integer gnssNum;

	@JsonProperty("wireless")
	private Integer wireless;

	@JsonProperty("real_speed")
	private Double realSpeed;

	@JsonProperty("expand_signal")
	private Long expandSignal;

	@JsonProperty("io_status")
	private Long  ioStatus;

	@JsonProperty("temperature")
	private String temperature;

	@JsonProperty("batch")
	private Long  batch;

	@JsonProperty("aux_str")
	private String auxStr;

	@JsonProperty("auxs_normal")
	private Map<Integer,Object> auxsNormal;

	@JsonProperty("dsm_unique_id")
	private String dsmUniqueId;

	@JsonProperty("te_state")
	private Long teState;

	@JsonProperty("loc_time_f")
	private String locTimeF;

	@JsonProperty("recv_time_f")
	private String recvTimeF;

	@JsonProperty("off_line_time")
	@TableField("off_line_time")
	private String offlineTime;

	@JsonProperty("date_str")
	private String dateStr;

	@JsonProperty("check_res")
	private String checkRes;

	@JsonProperty("check_res_message")
	private String checkResMessage;

	@JsonProperty("satellite_data")
	private String satelliteData;

	@JsonProperty("create_time")
	private Long createTime;
}
