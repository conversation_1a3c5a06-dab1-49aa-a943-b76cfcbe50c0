package com.xh.vdm.bdCheck.mapper.impala;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bdCheck.entity.bdCheck.BDCheckLocations;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;

import java.util.List;

/**
 * 终端识别检测 定位+检测结果数据
 * impala
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@DS("impala")
public interface BDCheckLocationsMapper extends BaseMapper<BDCheckLocations> {

	/**
	 * 根据终端编号查询终端的检测日期
	 * @param terminalNo
	 * @return
	 */
	List<String> getCheckDateList(String terminalNo);
}
