package com.xh.vdm.bdCheck.service.bdCheck.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bdCheck.constant.BdCheckConstant;
import com.xh.vdm.bdCheck.constant.ModeTypeEnum;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCheckReport;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;
import com.xh.vdm.bdCheck.entity.coding.Machine;
import com.xh.vdm.bdCheck.mapper.BdmTerminalMapper;
import com.xh.vdm.bdCheck.mapper.bdCheck.BdcTerminalMapper;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bdCheck.service.coding.IMachineService;
import com.xh.vdm.bdCheck.util.SM2Util;
import com.xh.vdm.bdCheck.vo.coding.request.ProcessRequest;
import com.xh.vdm.bdCheck.vo.coding.request.SyncFromMachineRequest;
import com.xh.vdm.bdCheck.vo.coding.response.PassListResponse;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.InvalidCipherTextException;
import org.springblade.common.constant.DictCodeConstant;
import org.springblade.common.constant.DictKeyConstant;
import org.springblade.common.constant.RedisConstant;
import org.springblade.common.utils.DateUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-30
 */
@Slf4j
@Service
public class BdcTerminalServiceImpl extends ServiceImpl<BdcTerminalMapper, BdcTerminal> implements IBdcTerminalService {

	@Value("${go-api.http.service.808}")
	private String goApiHttpService808;

	@Value("${go-api.http.service.mqtt}")
	private String goApiHttpServiceMqtt;

	@Value("${go-api.http.url.check-code-param.808}")
	private String goApiHttpUrlCheckCodeParam808;

	@Value("${go-api.http.url.check-code-param.mqtt}")
	private String goApiHttpUrlCheckCodeParamMqtt;

	@Value("${go-api.http.url.deliver-device-num.808}")
	private String goApiHttpUrlDeliverDeviceNum808;

	@Value("${go-api.http.url.deliver-device-num.mqtt}")
	private String goApiHttpUrlDeliverDeviceNumMqtt;

	@Value("${coding.machine.seq.web}")
	private String codingMachineSeqWeb;

	@Resource
	private ApplicationContext applicationContext;

	@Resource
	private NacosServiceManager nacosServiceManager;

	@Resource
	private NacosDiscoveryProperties nacosDiscoveryProperties;

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	@Resource
	private IDictBizClient dictBizClient;

	@Resource
	private IMachineService machineService;

	@Resource
	private BdmTerminalMapper terminalMapper;

	@Override
	public String findCompanyNameByTerminalNo(String terminalNo) {
		return baseMapper.getCompanyNameByTerminalNo(terminalNo);
	}

	@Override
	public BdcCheckReport findReportByTerminalNo(String terminalNo) {
		return baseMapper.getReportByTerminalNo(terminalNo);
	}

	@Override
	public IPage<PassListResponse> getPassPage (ProcessRequest request, Query query) {
		IPage<PassListResponse> page = Condition.getPage(query);
		page.setRecords(this.baseMapper.getPassPage(request, page));
		return page;
	}

	@Override
	public Map<String, Map<String, Object>> checkCodeParam (ProcessRequest request) {
		List<String> deviceNoList = new ArrayList<>();
		deviceNoList.add(request.getDeviceNo());
		List<BdcTerminal> testDeviceList = this.baseMapper.getPassListByDeviceNo(deviceNoList);
		Map<String, BdcTerminal> testDeviceMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(testDeviceList)) {
			testDeviceMap = testDeviceList.parallelStream().collect(Collectors.toMap(BdcTerminal::getDeviceNo, testDevice -> testDevice));
		}
		return this.checkCodeParamForSingleDevice(
			request.getDeviceType(),
			request.getDeviceNo(),
			request.getDeviceSeq(),
			request.getChipSeq(),
			request.getImei(),
			request.getManufacturer(),
			request.getDeviceModel(),
			testDeviceMap
		);
	}

	@Override
	public R<Map<String, String>> formDeviceNum (ProcessRequest request) {
		List<String> deviceNoList = new ArrayList<>();
		deviceNoList.add(request.getDeviceNo());
		List<BdcTerminal> testDeviceList = this.baseMapper.getPassListByDeviceNo(deviceNoList);
		if (CollectionUtils.isEmpty(testDeviceList)) {
			return R.fail(ResultCode.FAILURE, "设备不能赋码，请核实：1、设备是否存在；2、设备是否由平台来赋码；3、赋码是否已确定。");
		}

		return this.formDeviceNumForSingleDevice(testDeviceList.get(0));
	}

	@Override
	public R<String> deliverDeviceNum (ProcessRequest request) throws SQLException, NacosException {
		List<String> deviceNoList = new ArrayList<>();
		deviceNoList.add(request.getDeviceNo());
		List<BdcTerminal> testDeviceList = this.baseMapper.getPassListByDeviceNo(deviceNoList);
		if (CollectionUtils.isEmpty(testDeviceList)) {
			return R.fail(ResultCode.FAILURE, "设备不能赋码，请核实：1、设备是否存在；2、设备是否通过入网检测；3、设备是否由平台来赋码；4、赋码是否已确定。");
		}

		testDeviceList = testDeviceList.subList(0, 1);
		BdcTerminal testDevice = testDeviceList.get(0);
		R<String> r = this.deliverDeviceNumForSingleDevice(
			testDevice.getProtocol(),
			request.getDeviceNo(),
			request.getDeviceNum(),
			request.getDeviceNumSign()
		);
		if (!r.isSuccess()) {
			return r;
		}

		testDevice.setDeviceNum(request.getDeviceNum());
		testDevice.setDeviceNumSign(request.getDeviceNumSign());
		this.storeDeviceNum(testDeviceList);
		return R.success("");
	}

	@Override
	public void syncFromCodingMachine (SyncFromMachineRequest request) throws SQLException, ParseException {
		List<ProcessRequest> reqList = request.getList();
		List<BdcTerminal> deviceList = this.list(
			Wrappers.lambdaQuery(BdcTerminal.class).in(
				BdcTerminal::getDeviceSeq,
				reqList.parallelStream().map(ProcessRequest::getDeviceSeq).collect(Collectors.toList())
			).eq(BdcTerminal::getIsDel, 0)
		);

		Map<String, BdcTerminal> testDeviceMap = new HashMap<>();
		if (CollectionUtils.isNotEmpty(deviceList)) {
			testDeviceMap = deviceList.parallelStream().collect(Collectors.toMap(BdcTerminal::getDeviceSeq, testDevice -> testDevice));
		}

		List<BdcTerminal> testDeviceList = new ArrayList<>();
		List<BdcTerminal> updateList = new ArrayList<>();
		List<BdcTerminal> insertList = new ArrayList<>();
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		BdcTerminal testDevice;
		String deviceSeq;
		Date date;
		for (ProcessRequest req : reqList) {
			deviceSeq = req.getDeviceSeq();
			date = format.parse(req.getCodeTime());
			testDevice = new BdcTerminal();
			BeanUtils.copyProperties(req, testDevice);
			testDevice.setDeviceNo(deviceSeq);
			testDevice.setCodeResult((int) BdCheckConstant.CODE_STATE_DONE);
			testDevice.setCodeTime(date);
			testDevice.setUpdateTime(date);
			testDeviceList.add(testDevice);
			if (testDeviceMap.containsKey(deviceSeq)) {
				updateList.add(testDevice);
			} else {
				testDevice.setIsDel(0);
				testDevice.setCreateTime(new Date());
				insertList.add(testDevice);
			}
		}

		JSONObject jo;
		if (CollectionUtils.isNotEmpty(insertList)) {
			this.saveBatch(insertList);
		}
		for (BdcTerminal insertData : insertList) {
			jo = new JSONObject();
			jo.put("device_seq", insertData.getDeviceSeq());
			jo.put("chip_seq", insertData.getChipSeq());
			jo.put("imei", insertData.getImei());
			jo.put("manufacturer", insertData.getManufacturer());
			jo.put("device_model", insertData.getDeviceModel());
			this.redisTemplate.opsForHash().put(RedisConstant.HASH_DEVICE_NUM_CHECK, insertData.getDeviceNum(), jo);
		}
		for (BdcTerminal updateData : updateList) {
			this.update(updateData, Wrappers.lambdaQuery(BdcTerminal.class).eq(BdcTerminal::getDeviceSeq, updateData.getDeviceSeq()));
			jo = new JSONObject();
			jo.put("device_seq", updateData.getDeviceSeq());
			jo.put("chip_seq", updateData.getChipSeq());
			jo.put("imei", updateData.getImei());
			jo.put("manufacturer", updateData.getManufacturer());
			jo.put("device_model", updateData.getDeviceModel());
			this.redisTemplate.opsForHash().put(RedisConstant.HASH_DEVICE_NUM_CHECK, updateData.getDeviceNum(), jo);
		}

		this.addCodedDevice(testDeviceList);
	}

	@Override
	public R<String> batchCompleteCoding (ProcessRequest request) throws SQLException, NacosException {
		List<String> deviceNoList = request.getDeviceNoList();
		List<BdcTerminal> testDeviceList = this.baseMapper.getPassListByDeviceNo(deviceNoList);
		if (CollectionUtils.isEmpty(testDeviceList)) {
			return R.fail(ResultCode.FAILURE, "设备不能赋码，请核实：1、设备是否存在；2、设备是否通过入网检测；3、设备是否由平台来赋码；4、赋码是否已确定。");
		}

		Map<String, BdcTerminal> testDeviceMap =
			testDeviceList.parallelStream().collect(Collectors.toMap(BdcTerminal::getDeviceNo, testDevice -> testDevice));
		String msg = "已对所选设备完成一键批量赋码，若含有赋码失败的情况，会在对应设备的备注字段展示原因。";
		BdcTerminal updateData = new BdcTerminal();
		Map<String, JSONObject> deviceParamMap = new HashMap<>();
		R<JSONObject> rdp;
		for (String deviceNo : testDeviceMap.keySet()) {
			if ((testDeviceMap.get(deviceNo) != null) && StringUtils.isNotBlank(testDeviceMap.get(deviceNo).getProtocol())) {
				rdp = this.getDeviceParam(testDeviceMap.get(deviceNo).getProtocol(), deviceNo);
				if (!rdp.isSuccess()) {
					updateData.setCodeResult((int) BdCheckConstant.CODE_STATE_FAIL);
					updateData.setCodeResMessage(rdp.getMsg());
					this.update(updateData, Wrappers.lambdaQuery(BdcTerminal.class).eq(BdcTerminal::getDeviceNo, deviceNo));
					continue;
				}

				deviceParamMap.put(deviceNo, rdp.getData());
			}
		}
		if (deviceParamMap.isEmpty()) {
			return R.success(msg + "成功：0，失败：" + deviceNoList.size());
		}

		List<String> doneList1 = deviceParamMap.keySet().parallelStream().collect(Collectors.toList());
		List<String> doneList2 = new ArrayList<>();
		Map<String, Map<String, Object>> checkRes;
		StringBuffer codeResMessage;
		for (String deviceNo : doneList1) {
			checkRes = this.checkCodeParamForSingleDevice(
				deviceParamMap.containsKey(deviceNo) ? deviceParamMap.get(deviceNo).getOrDefault("device_type", "").toString() : "",
				deviceNo,
				deviceParamMap.containsKey(deviceNo) ? deviceParamMap.get(deviceNo).getOrDefault("device_seq", "").toString() : "",
				deviceParamMap.containsKey(deviceNo) ? deviceParamMap.get(deviceNo).getOrDefault("chip_seq", "").toString() : "",
				deviceParamMap.containsKey(deviceNo) ? deviceParamMap.get(deviceNo).getOrDefault("imei", "").toString() : "",
				deviceParamMap.containsKey(deviceNo) ? deviceParamMap.get(deviceNo).getOrDefault("manufacturer", "").toString() : "",
				deviceParamMap.containsKey(deviceNo) ? deviceParamMap.get(deviceNo).getOrDefault("device_model", "").toString() : "",
				testDeviceMap
			);

			codeResMessage = new StringBuffer();
			for (Map<String, Object> tmp : checkRes.values()) {
				if (Integer.parseInt(tmp.get("code").toString()) == 0) {
					codeResMessage.append(tmp.get("msg").toString()).append("|");
				}
			}
			if (codeResMessage.length() > 0) {
				updateData.setCodeResult((int) BdCheckConstant.CODE_STATE_FAIL);
				updateData.setCodeResMessage(codeResMessage.substring(0, codeResMessage.length() - 1));
				this.update(updateData, Wrappers.lambdaQuery(BdcTerminal.class).eq(BdcTerminal::getDeviceNo, deviceNo));
				continue;
			}

			doneList2.add(deviceNo);
		}
		if (CollectionUtils.isEmpty(doneList2)) {
			return R.success(msg + "成功：0，失败：" + deviceNoList.size());
		}

		Map<String, Map<String, String>> deviceNumMap = new HashMap<>();
		R<Map<String, String>> rdn;
		for (String deviceNo : doneList2) {
			if (testDeviceMap.containsKey(deviceNo)) {
				rdn = this.formDeviceNumForSingleDevice(testDeviceMap.get(deviceNo));
				if (!rdn.isSuccess()) {
					updateData.setCodeResult((int) BdCheckConstant.CODE_STATE_FAIL);
					updateData.setCodeResMessage(rdn.getMsg());
					this.update(updateData, Wrappers.lambdaQuery(BdcTerminal.class).eq(BdcTerminal::getDeviceNo, deviceNo));
					continue;
				}

				deviceNumMap.put(deviceNo, rdn.getData());
			}
		}
		if (deviceNumMap.isEmpty()) {
			return R.success(msg + "成功：0，失败：" + deviceNoList.size());
		}

		List<String> doneList3 = deviceNumMap.keySet().parallelStream().collect(Collectors.toList());
		List<BdcTerminal> doneList4 = new ArrayList<>();
		BdcTerminal testDevice;
		Map<String, String> tmp;
		String deviceNum;
		String deviceNumSign;
		R<String> rs;
		for (String deviceNo : doneList3) {
			if (testDeviceMap.containsKey(deviceNo) && deviceNumMap.containsKey(deviceNo)) {
				testDevice = testDeviceMap.get(deviceNo);
				tmp = deviceNumMap.get(deviceNo);
				deviceNum = tmp.get("device_num");
				deviceNumSign = tmp.get("device_num_sign");
				if (StringUtils.isNotBlank(deviceNum) && StringUtils.isNotBlank(deviceNumSign)) {
					rs = this.deliverDeviceNumForSingleDevice(testDevice.getProtocol(), deviceNo, deviceNum, deviceNumSign);
					if (!rs.isSuccess()) {
						updateData.setCodeResult((int) BdCheckConstant.CODE_STATE_FAIL);
						updateData.setCodeResMessage(rs.getMsg());
						this.update(updateData, Wrappers.lambdaQuery(BdcTerminal.class).eq(BdcTerminal::getDeviceNo, deviceNo));
						continue;
					}

					testDevice.setDeviceNum(deviceNum);
					testDevice.setDeviceNumSign(deviceNumSign);
					doneList4.add(testDevice);
				}
			}
		}
		if (CollectionUtils.isEmpty(doneList4)) {
			return R.success(msg + "成功：0，失败：" + deviceNoList.size());
		}

		this.storeDeviceNum(doneList4);
		return R.success(msg + "成功：" + doneList4.size() + "，失败：" + (deviceNoList.size() - doneList4.size()));
	}

	// 设备参数
	private R<JSONObject> getDeviceParam (String protocol, String deviceNo) throws NacosException {
		switch (protocol) {
			case BdCheckConstant.DEVICE_PROTOCOL_808:
				return this.getDeviceParamFor808(deviceNo);
			case BdCheckConstant.DEVICE_PROTOCOL_MQTT:
				return this.getDeviceParamForMqtt(deviceNo);
			default:
				return R.fail(ResultCode.FAILURE, "当前暂不支持对该协议（" + protocol + "）的设备进行参数获取。");
		}
	}

	// 设备参数：808协议
	private R<JSONObject> getDeviceParamFor808 (String deviceNo) throws NacosException {
		String host = this.getHostForServiceFromNacos(this.goApiHttpService808);
		if (StringUtils.isBlank(host)) {
			return R.fail(ResultCode.FAILURE, "无法识别获取设备（808协议）参数的接口。");
		}

		String url = host + this.goApiHttpUrlCheckCodeParam808;
		JSONObject requestBody = new JSONObject();
		requestBody.put("device_no", deviceNo);
		requestBody.put("param_ids", new int[] {65530, 65531, 65532, 65533, 65534, 65535});
		String reqBodyStr = JSON.toJSONString(requestBody);
		log.info("start request for get device param for 808, url: {}, request body: {}", url, reqBodyStr);
		String respBodyStr = HttpRequest.post(url).body(reqBodyStr).execute().body();
		log.info("request done for get device param for 808, url: {}, request body: {}, response body: {}", url, reqBodyStr, respBodyStr);
		JSONObject responseBody = JSON.parseObject(respBodyStr);
		if (
			(responseBody == null) ||
			(responseBody.get("code") == null) ||
			(responseBody.get("msg") == null) ||
			(responseBody.get("data") == null)
		) {
			return R.fail(ResultCode.FAILURE, "获取设备（808协议）参数失败。");
		}
		if (Integer.parseInt(responseBody.get("code").toString()) != 0) {
			return R.fail(ResultCode.FAILURE, responseBody.get("msg").toString());
		}

		JSONObject tmp = JSON.parseObject(JSON.toJSONString(responseBody.get("data")));
		if ((tmp == null) || (tmp.get("Params") == null)) {
			return R.fail(ResultCode.FAILURE, "获取设备（808协议）参数，返回结果异常。");
		}

		JSONObject jo = JSON.parseObject(JSON.toJSONString(tmp.get("Params")));
		JSONObject deviceParam = new JSONObject();
		deviceParam.put("device_type", jo.getOrDefault("device_type", ""));
		deviceParam.put("device_seq", jo.getOrDefault("device_serial", ""));
		deviceParam.put("chip_seq", jo.getOrDefault("device_chip_id", ""));
		deviceParam.put("imei", jo.getOrDefault("device_imei", ""));
		deviceParam.put("manufacturer", jo.getOrDefault("device_producer", ""));
		deviceParam.put("device_model", jo.getOrDefault("device_model", ""));
		return R.data(deviceParam);
	}

	// 设备参数：MQTT协议
	private R<JSONObject> getDeviceParamForMqtt (String deviceNo) throws NacosException {
		String host = this.getHostForServiceFromNacos(this.goApiHttpServiceMqtt);
		if (StringUtils.isBlank(host)) {
			return R.fail(ResultCode.FAILURE, "无法识别获取设备（MQTT协议）参数的接口。");
		}

		String url = host + this.goApiHttpUrlCheckCodeParamMqtt;
		JSONObject requestBody = new JSONObject();
		requestBody.put("device_no", deviceNo);
		requestBody.put("cmd_flag", "xhysl");
		String reqBodyStr = JSON.toJSONString(requestBody);
		log.info("start request for get device param for MQTT, url: {}, request body: {}", url, reqBodyStr);
		String respBodyStr = HttpRequest.post(url).body(reqBodyStr).execute().body();
		log.info("request done for get device param for MQTT, url: {}, request body: {}, response body: {}", url, reqBodyStr, respBodyStr);
		JSONObject responseBody = JSON.parseObject(respBodyStr);
		if (
			(responseBody == null) ||
			(responseBody.get("code") == null) ||
			(responseBody.get("msg") == null) ||
			(responseBody.get("res") == null)
		) {
			return R.fail(ResultCode.FAILURE, "获取设备（MQTT协议）参数失败。");
		}
		if (Integer.parseInt(responseBody.get("code").toString()) != ResultCode.SUCCESS.getCode()) {
			return R.fail(ResultCode.FAILURE, responseBody.get("msg").toString());
		}

		JSONObject jo = JSON.parseObject(JSON.toJSONString(responseBody.get("res")));
		if (jo == null) {
			return R.fail(ResultCode.FAILURE, "获取设备（MQTT协议）参数，返回结果异常。");
		}

		JSONObject deviceParam = new JSONObject();
		deviceParam.put("device_type", jo.getOrDefault("device_category", ""));
		deviceParam.put("device_seq", jo.getOrDefault("product_serial_number", ""));
		deviceParam.put("chip_seq", jo.getOrDefault("bd_chip_serial_number", ""));
		deviceParam.put("imei", jo.getOrDefault("imei", ""));
		deviceParam.put("manufacturer", jo.getOrDefault("manufacturer_id", ""));
		deviceParam.put("device_model", jo.getOrDefault("product_model", ""));
		return R.data(deviceParam);
	}

	// 根据服务名，从Nacos获取对应host。
	private String getHostForServiceFromNacos (String serviceName) throws NacosException {
		NamingService namingService = this.nacosServiceManager.getNamingService(this.nacosDiscoveryProperties.getNacosProperties());
		Instance instance = namingService.selectOneHealthyInstance(serviceName, this.nacosDiscoveryProperties.getGroup());
		return (instance == null) ? "" : (instance.getIp() + ":" + instance.getPort());
	}

	// 单台设备的参数检查
	private Map<String, Map<String, Object>> checkCodeParamForSingleDevice (
		String deviceType,
		String deviceNo,
		String deviceSeq,
		String chipSeq,
		String imei,
		String manufacturer,
		String deviceModel,
		Map<String, BdcTerminal> testDeviceMap
	) {
		Map<String, Boolean> flagMap = new HashMap<>();
		Map<String, Map<String, Object>> res = new HashMap<>();
		Map<String, Object> map;
		flagMap.put("device_type", false);
		flagMap.put("device_seq", false);
		flagMap.put("chip_seq", false);
		flagMap.put("imei", false);
		flagMap.put("manufacturer", false);
		flagMap.put("device_model", false);
		map = new HashMap<>();
		map.put("code", 1);
		map.put("msg", "");
		res.put("device_type", map);
		map = new HashMap<>();
		map.put("code", 1);
		map.put("msg", "");
		res.put("device_seq", map);
		map = new HashMap<>();
		map.put("code", 1);
		map.put("msg", "");
		res.put("chip_seq", map);
		map = new HashMap<>();
		map.put("code", 1);
		map.put("msg", "");
		res.put("imei", map);
		map = new HashMap<>();
		map.put("code", 1);
		map.put("msg", "");
		res.put("manufacturer", map);
		map = new HashMap<>();
		map.put("code", 1);
		map.put("msg", "");
		res.put("device_model", map);

		Map<String, String> deviceTypeMap = new HashMap<>();
		R<Map<String, String>> rdt = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(
			DictCodeConstant.TEST_DEVICE_TYPE,
			DictKeyConstant.DEFAULT_ROOT_KEY
		);
		if (rdt.isSuccess() && (rdt.getData() != null) && (!rdt.getData().isEmpty())) {
			deviceTypeMap = rdt.getData();
		}
		if (!deviceTypeMap.containsKey(deviceType)) {
			flagMap.put("device_type", true);
			res.get("device_type").put("code", 0);
			res.get("device_type").put("msg", "设备类型不正确。");
		}
		if (!deviceSeq.matches("\\w{1,16}")) {
			flagMap.put("device_seq", true);
			res.get("device_seq").put("code", 0);
			res.get("device_seq").put("msg", "设备序列号不正确。");
		}
		if (chipSeq.length() > 16) {
			flagMap.put("chip_seq", true);
			res.get("chip_seq").put("code", 0);
			res.get("chip_seq").put("msg", "北斗芯片序列号不正确。");
		}
		if (imei.length() > 15) {
			flagMap.put("imei", true);
			res.get("imei").put("code", 0);
			res.get("imei").put("msg", "imei号不正确。");
		}
		if (!manufacturer.matches("\\d{4}")) {
			flagMap.put("manufacturer", true);
			res.get("manufacturer").put("code", 0);
			res.get("manufacturer").put("msg", "设备厂商不正确。");
		}
		if (deviceModel.length() > 12) {
			flagMap.put("device_model", true);
			res.get("device_model").put("code", 0);
			res.get("device_model").put("msg", "设备型号不正确。");
		}

		if ((!testDeviceMap.containsKey(deviceNo)) || (testDeviceMap.get(deviceNo) == null)) {
			if (!flagMap.get("device_type")) {
				flagMap.put("device_type", true);
				res.get("device_type").put("code", 0);
				res.get("device_type").put("msg", "设备不能赋码，请核实：1、设备是否存在；2、设备是否通过入网检测；3、设备是否由平台来赋码。");
			}
			if (!flagMap.get("device_seq")) {
				flagMap.put("device_seq", true);
				res.get("device_seq").put("code", 0);
				res.get("device_seq").put("msg", "设备不能赋码，请核实：1、设备是否存在；2、设备是否通过入网检测；3、设备是否由平台来赋码。");
			}
			if (!flagMap.get("chip_seq")) {
				flagMap.put("chip_seq", true);
				res.get("chip_seq").put("code", 0);
				res.get("chip_seq").put("msg", "设备不能赋码，请核实：1、设备是否存在；2、设备是否通过入网检测；3、设备是否由平台来赋码。");
			}
			if (!flagMap.get("imei")) {
				flagMap.put("imei", true);
				res.get("imei").put("code", 0);
				res.get("imei").put("msg", "设备不能赋码，请核实：1、设备是否存在；2、设备是否通过入网检测；3、设备是否由平台来赋码。");
			}
			if (!flagMap.get("manufacturer")) {
				flagMap.put("manufacturer", true);
				res.get("manufacturer").put("code", 0);
				res.get("manufacturer").put("msg", "设备不能赋码，请核实：1、设备是否存在；2、设备是否通过入网检测；3、设备是否由平台来赋码。");
			}
			if (!flagMap.get("device_model")) {
				flagMap.put("device_model", true);
				res.get("device_model").put("code", 0);
				res.get("device_model").put("msg", "设备不能赋码，请核实：1、设备是否存在；2、设备是否通过入网检测；3、设备是否由平台来赋码。");
			}
		} else {
			BdcTerminal testDevice = testDeviceMap.get(deviceNo);
			if ((!flagMap.get("device_type")) && (!testDevice.getDeviceType().equals(deviceType))) {
				flagMap.put("device_type", true);
				res.get("device_type").put("code", 0);
				res.get("device_type").put("msg", "设备类型与已录入信息不一致。");
			}
			if ((!flagMap.get("device_seq")) && (!testDevice.getDeviceSeq().equals(deviceSeq))) {
				flagMap.put("device_seq", true);
				res.get("device_seq").put("code", 0);
				res.get("device_seq").put("msg", "设备序列号与已录入信息不一致。");
			}
			if ((!flagMap.get("chip_seq")) && (!testDevice.getChipSeq().equals(chipSeq))) {
				flagMap.put("chip_seq", true);
				res.get("chip_seq").put("code", 0);
				res.get("chip_seq").put("msg", "北斗芯片序列号与已录入信息不一致。");
			}
			if ((!flagMap.get("imei")) && (!testDevice.getImei().equals(imei))) {
				flagMap.put("imei", true);
				res.get("imei").put("code", 0);
				res.get("imei").put("msg", "imei号与已录入信息不一致。");
			}
			if ((!flagMap.get("manufacturer")) && (!testDevice.getManufacturer().equals(manufacturer))) {
				flagMap.put("manufacturer", true);
				res.get("manufacturer").put("code", 0);
				res.get("manufacturer").put("msg", "设备厂商与已录入信息不一致。");
			}
			if ((!flagMap.get("device_model")) && (!testDevice.getDeviceModel().equals(deviceModel))) {
				flagMap.put("device_model", true);
				res.get("device_model").put("code", 0);
				res.get("device_model").put("msg", "设备型号与已录入信息不一致。");
			}
		}

		return res;
	}

	// 赋码序号
	private int getDeviceNumSeq () throws ParseException {
		if (Boolean.FALSE.equals(this.redisTemplate.hasKey(RedisConstant.STRING_DEVICE_NUM_SEQ))) {
			this.redisTemplate.opsForValue().set(RedisConstant.STRING_DEVICE_NUM_SEQ, 0);
			this.redisTemplate.expireAt(
				RedisConstant.STRING_DEVICE_NUM_SEQ,
				new Date(DateUtil.getDayLastSecondTimestamp(new Date().getTime() / 1000) * 1000 + 999)
			);
		} else {
			this.redisTemplate.opsForValue().increment(RedisConstant.STRING_DEVICE_NUM_SEQ);
		}

		Object o = this.redisTemplate.opsForValue().get(RedisConstant.STRING_DEVICE_NUM_SEQ);
		return (o == null) ? 0 : Integer.parseInt(o.toString());
	}

	// 单个设备的生成
	private R<Map<String, String>> formDeviceNumForSingleDevice (BdcTerminal testDevice) {
		try {
			Machine machine = this.machineService.getOne(
				Wrappers.lambdaQuery(Machine.class)
					.eq(Machine::getNumber, this.codingMachineSeqWeb)
					.eq(Machine::getDisabled, BdCheckConstant.CODING_MACHINE_DISABLED_NO)
			);
			if (machine == null) {
				return R.fail(ResultCode.FAILURE, "未能获取赋码机。");
			}

			StringBuffer sb = new StringBuffer();
			sb.append(testDevice.getDeviceType())
				.append(this.codingMachineSeqWeb)
				.append(new SimpleDateFormat("yyyyMMdd").format(new Date()).substring(2))
				.append(String.format("%05d", this.getDeviceNumSeq()));
			String deviceNum = sb.toString();

			sb = new StringBuffer();
			sb.append(testDevice.getDeviceType()).append(":")
				.append(testDevice.getManufacturer()).append(":")
				.append(testDevice.getDeviceModel()).append(":")
				.append(testDevice.getDeviceSeq()).append(":")
				.append(testDevice.getImei()).append(":")
				.append(testDevice.getChipSeq()).append(":")
				.append(deviceNum);
			String deviceNumSign = SM2Util.encrypt(sb.toString(), "04" + machine.getPublicKey(), ModeTypeEnum.BASE_MODE);
			// 密钥对示例
			// 公钥：04ce7496ae0894eac41c084803dde2ebf35824eb43a7f3a14cc65e2a2b0f4003337f215f08abbd5fc5ea75e2b2581d407e409ee68bff07f15b27a8265dd8d92c2c
			// 私钥：008624891957a8688735a489f8c7b84ca42d438b5236bc22df503b6fe43a23b664

			Map<String, String> res = new HashMap<>();
			res.put("device_num", deviceNum);
			res.put("device_num_sign", deviceNumSign);
			return R.data(res);
		} catch (ParseException | InvalidCipherTextException e) {
			log.error("fail form device num for single device: {}", e.getMessage(), e);
			return R.fail(ResultCode.FAILURE, "赋码生成异常：" + e.getMessage());
		}
	}

	// 单个设备的下发
	private R<String> deliverDeviceNumForSingleDevice (String protocol, String deviceNo, String deviceNum, String deviceNumSign) throws NacosException {
		String host;
		String url;
		JSONObject requestBody = new JSONObject();
		switch (protocol) {
			case BdCheckConstant.DEVICE_PROTOCOL_808:
				host = this.getHostForServiceFromNacos(this.goApiHttpService808);
				if (StringUtils.isBlank(host)) {
					return R.fail(ResultCode.FAILURE, "无法识别赋码下发设备（808协议）的接口。");
				}

				url = host + this.goApiHttpUrlDeliverDeviceNum808;
				requestBody.put("device_no", deviceNo);
				requestBody.put("device_num", deviceNum);
				requestBody.put("sign", deviceNumSign);
				break;
			case BdCheckConstant.DEVICE_PROTOCOL_MQTT:
				host = this.getHostForServiceFromNacos(this.goApiHttpServiceMqtt);
				if (StringUtils.isBlank(host)) {
					return R.fail(ResultCode.FAILURE, "无法识别赋码下发设备（MQTT协议）的接口。");
				}

				url = host + this.goApiHttpUrlDeliverDeviceNumMqtt;
				requestBody.put("device_no", deviceNo);
				requestBody.put("device_num", deviceNum);
				requestBody.put("device_sign", deviceNumSign);
				break;
			default:
				return R.fail(ResultCode.FAILURE, "当前暂不支持对该协议（" + protocol + "）的设备进行赋码下发。");
		}

		String reqBodyStr = JSON.toJSONString(requestBody);
		log.info("start request for deliver device num, url: {}, request body: {}", url, reqBodyStr);
		String respBodyStr = HttpRequest.post(url).body(reqBodyStr).execute().body();
		log.info("request done for deliver device num, url: {}, request body: {}, response body: {}", url, reqBodyStr, respBodyStr);
		JSONObject responseBody = JSON.parseObject(respBodyStr);
		if ((responseBody == null) || (responseBody.get("code") == null) || (responseBody.get("msg") == null)) {
			return R.fail(ResultCode.FAILURE, "下发赋码失败。");
		}

		int code = Integer.parseInt(responseBody.get("code").toString());
		if (protocol.equals(BdCheckConstant.DEVICE_PROTOCOL_MQTT)) {
			if (code != ResultCode.SUCCESS.getCode()) {
				return R.fail(ResultCode.FAILURE, responseBody.get("msg").toString());
			}
		} else {
			if (code != 0) {
				return R.fail(ResultCode.FAILURE, responseBody.get("msg").toString());
			}
		}

		return R.success("");
	}

	// 结果入库
	private void storeDeviceNum (List<BdcTerminal> testDeviceList) throws SQLException {
		if (CollectionUtils.isEmpty(testDeviceList)) {
			return;
		}

		Date date = new Date();
		BdcTerminal tmp;
		JSONObject jo;
		for (BdcTerminal testDevice : testDeviceList) {
			tmp = new BdcTerminal();
			tmp.setDeviceNum(testDevice.getDeviceNum());
			tmp.setDeviceNumSign(testDevice.getDeviceNumSign());
			tmp.setCodeResult((int) BdCheckConstant.CODE_STATE_DONE);
			tmp.setCodeResMessage("");
			tmp.setCodeTime(date);
			tmp.setUpdateTime(date);
			this.update(tmp, Wrappers.lambdaQuery(BdcTerminal.class).eq(BdcTerminal::getDeviceNo, testDevice.getDeviceNo()));

			jo = new JSONObject();
			jo.put("device_seq", testDevice.getDeviceSeq());
			jo.put("chip_seq", testDevice.getChipSeq());
			jo.put("imei", testDevice.getImei());
			jo.put("manufacturer", testDevice.getManufacturer());
			jo.put("device_model", testDevice.getDeviceModel());
			jo.put("formal", testDevice.getFormal());
			this.redisTemplate.opsForHash().put(RedisConstant.HASH_DEVICE_NUM_CHECK, testDevice.getDeviceNum(), jo);
		}

		this.addCodedDevice(testDeviceList);
	}

	// 添加已赋码设备
	// 该过程中，会往vdm.bdm_device_code添加记录，该表的主键是device_num，添加的记录中，可能含有已存在的device_num，造成主键冲突，因此要用insertOrUpdate。
	// postgresql实现insertOrUpdate的语法是：insert into {table} values ({value}, ...), ... on conflict ({column}, ...) do update set {column} = excluded.{column}, ...
	// 直接执行是没问题的，但写到mybatis中的mapper中，就出问题了，压根解析不了语句。因此，直接使用以下方式算了。
	private void addCodedDevice (List<BdcTerminal> testDeviceList) throws SQLException {
		for (BdcTerminal testDevice : testDeviceList) {
			if (StringUtils.isBlank(testDevice.getDeviceSeq()) && StringUtils.isBlank(testDevice.getDeviceNum())) {
				throw new SQLException("设备序列号与赋码同时为空。设备：" + testDevice);
			}

			int num1 = this.baseMapper.getCountByDeviceSeq(testDevice.getDeviceSeq());
			int num2 = this.baseMapper.getCountByDeviceNum(testDevice.getDeviceNum());
			if (num1 > 0) {
				this.baseMapper.editByDeviceSeq(testDevice);
			}
			if (num2 > 0) {
				this.baseMapper.editByDeviceNum(testDevice);
			}
			if ((num1 <= 0) && (num2 <= 0)) {
				this.baseMapper.addDeviceNum(testDevice);
			}
		}

//		DataSource dataSource = this.applicationContext.getBean(DataSource.class);
//		Connection conn = dataSource.getConnection();
//		conn.setAutoCommit(false);
//		PreparedStatement statement = conn.prepareStatement(
//			"insert into vdm.bdm_device_code " +
//			"values (?, ?, ?, ?, ?, ?, ?, ?, current_timestamp, 0, current_timestamp) " +
//			"on conflict (device_num) do update " +
//			"set signature = excluded.signature, " +
//			"kind = excluded.kind, " +
//			"vendor = excluded.vendor, " +
//			"model = excluded.model, " +
//			"serial = excluded.serial, " +
//			"imei = excluded.imei, " +
//			"bd_chip_serial = excluded.bd_chip_serial, " +
//			"create_time = excluded.create_time, " +
//			"activated = excluded.activated, " +
//			"update_time = excluded.update_time"
//		);
//		for (BdcTerminal testDevice : testDeviceList) {
//			statement.setString(1, testDevice.getDeviceNum());
//			statement.setString(2, testDevice.getDeviceNumSign());
//			statement.setString(3, testDevice.getDeviceType());
//			statement.setString(4, testDevice.getManufacturer());
//			statement.setString(5, testDevice.getDeviceModel());
//			statement.setString(6, testDevice.getDeviceSeq());
//			statement.setString(7, testDevice.getImei());
//			statement.setString(8, testDevice.getChipSeq());
//			statement.addBatch();
//		}
//
//		statement.executeBatch();
//		statement.close();
//		conn.commit();
//		conn.setAutoCommit(true);
//		conn.close();
	}



	@Override
	public List<String> findExistDeviceNo(String deviceNoList) {
		return baseMapper.getExistDeviceNo("'"+deviceNoList+"'");
	}

	@Override
	public List<BdcTerminal> findTerminalListByManufacturerIds(List<Long> manufacturerIds) {
		return baseMapper.getTerminalListByManufacturerIds(manufacturerIds);
	}

	@Override
	public long findBDTerminalCountByVendor(List<String> venders) {
		String manufacturerIdsArray = "";
		StringBuffer sb = new StringBuffer();
		sb.append("'{");
		for(String vendor : venders){
			sb.append(vendor+"").append(",");
		}
		if(sb.length() > 0){
			sb = new StringBuffer(sb.substring(0,sb.length()-1));
		}
		sb.append("}'");
		return terminalMapper.getBDTerminalCountByManufacturerId(sb.toString());
	}

	@Override
	public void updateByDeviceSeq(BdcTerminal bdcTerminal) {
		System.out.println("bdcTerminal    " + bdcTerminal);
		baseMapper.updateByDeviceSeq(bdcTerminal);
	}

	@Override
	public String findUniqueIdByDeviceNum(String deviceNum) {
		return baseMapper.getUniqueIdByDeviceNum(deviceNum);
	}

	@Override
	public Long findDeviceIdByDeviceNum(String deviceNum) {
		return baseMapper.getDeviceIdByDeviceNum(deviceNum);
	}
}
