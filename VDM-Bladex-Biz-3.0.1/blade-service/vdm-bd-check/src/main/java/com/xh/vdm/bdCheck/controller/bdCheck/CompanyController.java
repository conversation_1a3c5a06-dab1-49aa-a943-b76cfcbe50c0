package com.xh.vdm.bdCheck.controller.bdCheck;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bdCheck.entity.BladeDept;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCheckReport;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCompany;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;
import com.xh.vdm.bdCheck.service.IBladeDeptService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcCheckReportService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcCompanyService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bdCheck.util.AuthUtils;
import com.xh.vdm.bdCheck.vo.bdCheck.BDCCompanyNameResponse;
import com.xh.vdm.bdCheck.vo.bdCheck.BdcCompanyRequest;
import com.xh.vdm.bdCheck.vo.bdCheck.BdcCompanyResponse;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springblade.common.annotation.Log;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.BeanUtil;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;

/**
 * 北斗识别检测-企业信息
 */
@RestController
@RequestMapping("/company")
@Slf4j
public class CompanyController {

	@Resource
	private IBdcCompanyService bdcCompanyService;

	@Resource
	private IBdcCompanyService companyService;

	@Resource
	private IBladeDeptService deptService;

	@Resource
	private IBdcTerminalService terminalService;

	@Resource
	private IBdcCheckReportService reportService;

	/**
	 * 查询企业名称信息-全部
	 *
	 * @return
	 */
	@GetMapping("/allCompanyName")
	public R<List<BDCCompanyNameResponse>> company(BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		// 超级管理员账户，查看所有部门
		R<List<Long>> r1 = AuthUtils.getDeptList(user);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}
		List<Long> deptIdList =  r1.getData();

		//		List<BdcCompany> list = companyService.list(Wrappers.lambdaQuery(BdcCompany.class).eq(BdcCompany::getState, CommonConstant.STATE_U).orderByDesc(BdcCompany::getCreateTime));
		List<BdcCompany> list = new ArrayList<>();
		List<BDCCompanyNameResponse> resList = new ArrayList<>();

		if (AuthUtil.isAdministrator()) {
			list = companyService.list(Wrappers.lambdaQuery(BdcCompany.class)
				.eq(BdcCompany::getState, CommonConstant.STATE_U)
				.orderByDesc(BdcCompany::getCreateTime)
			);
		}

		if (deptIdList != null && !deptIdList.isEmpty()) {
			// 分批处理：每批处理的数据量
			int batchSize = 1000;
			int totalSize = deptIdList.size();

			for (int i = 0; i < totalSize; i += batchSize) {
				int end = Math.min(i + batchSize, totalSize);
				List<Long> batchDeptIdList = deptIdList.subList(i, end);
				List<BdcCompany> batchList = new ArrayList<>();

				batchList = companyService.list(Wrappers.lambdaQuery(BdcCompany.class)
					.eq(BdcCompany::getState, CommonConstant.STATE_U)
					.in(BdcCompany::getDeptId, batchDeptIdList)
					.orderByDesc(BdcCompany::getCreateTime)
				);
				list.addAll(batchList);
			}
		}
		for (BdcCompany company : list) {
			BDCCompanyNameResponse cn = new BDCCompanyNameResponse();
			cn.setCompanyName(company.getCompanyName());
			cn.setId(company.getId());
			resList.add(cn);
		}
		return R.data(resList);
	}

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<BdcCompany> detail(BdcCompany bdcCompany) {
		BdcCompany detail = bdcCompanyService.getOne(Condition.getQueryWrapper(bdcCompany));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	public R<IPage<BdcCompanyResponse>> list(BdcCompanyRequest request, Query query, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		// 超级管理员账户，查看所有部门
		R<List<Long>> r1 = AuthUtils.getDeptList(user);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}
		List<Long> deptIdList =  r1.getData();
		BdcCompany company = new BdcCompany();
		BeanUtil.copy(request, company);
		company.setState(CommonConstant.STATE_U);
		String deptIds = "";
		if (deptIdList != null && !deptIdList.isEmpty()) {
			//deptId转为any表达式，解决 in 只能处理 1000 个参数的问题
			StringBuffer sb = new StringBuffer();
			sb.append(" '{");
			deptIdList.add(1L);
			deptIdList.add(2L);
			for(Long deptId : deptIdList){
				sb.append(deptId).append(",");
			}
			String s = sb.substring(0, sb.length()-1);
			s = s +"}'";
			deptIds = s;
		}
		IPage<BdcCompany> pages = companyService.findCompanyPage(Condition.getPage(query), company, deptIds);
		IPage<BdcCompanyResponse> pageList = new Page<>();
		List<BdcCompanyResponse> resList = new ArrayList<>();
		pageList.setCurrent(pages.getCurrent());
		pageList.setSize(pages.getSize());
		pageList.setPages(pages.getPages());
		pageList.setTotal(pages.getTotal());
		Map<String, String> deptMap = new HashMap<>();
		if (pages != null && pages.getRecords() != null && pages.getRecords().size() > 0) {
			pages.getRecords().forEach(item -> {
				BdcCompanyResponse response = new BdcCompanyResponse();
				BeanUtils.copyProperties(item, response);
				//设置deptName
				Long deptId = item.getDeptId();
				String deptName = deptMap.get(deptId + "");
				if (StringUtils.isEmpty(deptName)) {
					//查询dept信息
					BladeDept dept = deptService.getById(deptId);
					deptName = dept == null ? "" : dept.getDeptName();
					deptMap.put(deptId + "", deptName);
				}
				response.setDeptName(deptName);
				resList.add(response);
			});
		}
		pageList.setRecords(resList);
		return R.data(pageList);
	}

	/**
	 * 新增
	 */
	@Log(menu = "委托企业管理", operation = Operation.INSERT, objectType = ObjectType.COMPANY)
	@PostMapping("/save")
	public R save(@Valid @RequestBody BdcCompanyRequest request, BladeUser user) {
		String companyName = request.getCompanyName();
		if (StringUtils.isEmpty(companyName)) {
			return R.fail("企业名称不能为空");
		}
		//判断企业名称是否存在，如果已经存在，则不允许新增
		long count = companyService.count(Wrappers.lambdaQuery(BdcCompany.class).eq(BdcCompany::getCompanyName, companyName).eq(BdcCompany::getState, CommonConstant.STATE_U));
		if (count > 0) {
			return R.fail("该企业名称已经存在");
		}

		BdcCompany company = new BdcCompany();
		BeanUtil.copyProperties(request, company);
		company.setState(CommonConstant.STATE_U);
		company.setCreateTime(new Date());
		boolean result = bdcCompanyService.save(company);
		if (result) {
			return R.data(ResultCode.SUCCESS.getCode(), company.getId().toString(),"新增成功");
		}
		return R.status(result);
	}

	/**
	 * 修改
	 */
	@Log(menu = "委托企业管理", operation = Operation.UPDATE, objectType = ObjectType.COMPANY)
	@PostMapping("/update")
	public R update(@Valid @RequestBody BdcCompanyRequest request, BladeUser user) {
		try {
			String companyName = request.getCompanyName();
			if (StringUtils.isEmpty(companyName)) {
				return R.fail("企业名称不能为空");
			}
			//判断是否修改企业名称
			BdcCompany companyDB = companyService.getById(request.getId());
			if (!companyDB.getCompanyName().equals(companyName)) {
				//如果参数中的企业名称与数据库中不一致，表示要修改企业名称，那么就要判断将要修改的企业名称是否已经存在
				//判断企业名称是否存在，如果已经存在，则不允许新增
				long count = companyService.count(Wrappers.lambdaQuery(BdcCompany.class).eq(BdcCompany::getCompanyName, companyName).eq(BdcCompany::getState, CommonConstant.STATE_U));
				if (count > 0) {
					return R.fail("该企业名称已经存在");
				}
			}


			BdcCompany company = new BdcCompany();
			BeanUtil.copy(request, company);
			company.setUpdateTime(new Date());
			boolean flag = bdcCompanyService.updateById(company);
			log.info("执行结果为：" + flag);
			if (flag) {
				String result = new CompareUtils<BdcCompany>().compare(companyDB, company);
				return R.data(ResultCode.SUCCESS.getCode(), result,"编辑成功");
			}
			return R.status(flag);
		} catch (Exception e) {
			log.error("操作失败", e);
			return R.fail("操作失败");
		}
	}


	/**
	 * 删除
	 */
	@Log(menu = "委托企业管理", operation = Operation.DELETE, objectType = ObjectType.COMPANY)
	@GetMapping("/remove")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids, BladeUser user) {
		List<Long> list = Func.toLongList(ids);
		//1.校验是否包含已经绑定了其他数据的委托企业

		//1.1 检测入网检测管理
		long countReport = reportService.count(Wrappers.lambdaQuery(BdcCheckReport.class)
			.in(BdcCheckReport::getCompanyId, list).eq(BdcCheckReport::getState, CommonConstant.STATE_U));
		if (countReport > 0) {
			return R.fail("已有检测信息关联要删除的企业，请先删除相应入网检测信息");
		}

		//1.2 检测入网终端管理
		long count = terminalService.count(Wrappers.lambdaQuery(BdcTerminal.class).in(BdcTerminal::getCompanyId, list).eq(BdcTerminal::getIsDel, 0));
		if (count > 0) {
			return R.fail("已有终端关联要删除的企业，请先删除相应终端后再试");
		}

		//逻辑删除
		List<BdcCompany> comList = new ArrayList<>();
		for (Long id : list) {
			BdcCompany com = new BdcCompany();
			com.setId(id);
			com.setUpdateTime(new Date());
			com.setState(CommonConstant.STATE_E);
			comList.add(com);
		}
		return R.status(bdcCompanyService.updateBatchById(comList));
	}
}
