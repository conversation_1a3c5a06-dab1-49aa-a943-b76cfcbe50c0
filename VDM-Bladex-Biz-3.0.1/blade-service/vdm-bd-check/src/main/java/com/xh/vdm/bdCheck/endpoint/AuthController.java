package com.xh.vdm.bdCheck.endpoint;

import com.alibaba.excel.util.StringUtils;
import com.xh.vdm.bdCheck.event.AllTerminalWSEventListener;
import com.xh.vdm.bdCheck.event.SingleTerminalWSEventListener;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/7/1 14:12
 */
@RestController
@RequestMapping("/bdCheck/authWS")
@Slf4j
public class AuthController {

	//单个终端推送
	@Resource
	private SingleTerminalWSEventListener stl;

	//全部终端推送
	@Resource
	private AllTerminalWSEventListener atl;

	@GetMapping("/authCode")
	public R<String> authCode(){

		String userCode = UUID.randomUUID().toString();
		userCode = userCode.replace("-","");
		log.info("请求 userCode , 生成的userId为 {}", userCode);



		//***************单终端北斗检测websocket接口***********************
		stl.putUserCode(userCode);
		atl.putUserCode(userCode);

		return R.data(userCode);
	}


}
