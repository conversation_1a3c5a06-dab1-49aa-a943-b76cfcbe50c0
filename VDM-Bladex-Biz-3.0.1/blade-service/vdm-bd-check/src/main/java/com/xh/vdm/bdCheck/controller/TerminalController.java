package com.xh.vdm.bdCheck.controller;

import com.alibaba.fastjson.JSONObject;
import com.xh.vdm.bdCheck.service.IBdmTerminalService;
import com.xh.vdm.bdCheck.service.terminal.IRnssDeviceService;
import com.xh.vdm.bdCheck.vo.TerminalCountResponse;
import com.xh.vdm.bdCheck.vo.TerminalInfo;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.system.feign.ISysClient;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 终端服务
 */
@RestController
@RequestMapping("/bdCheck/terminal")
@Slf4j
public class TerminalController {


	@Resource
	private IBdmTerminalService terminalService;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private ISysClient sysClient;

	@Resource
	private IRnssDeviceService rnssDeviceService;

	/**
	 * 统计终端情况：终端总数、在线终端总数
	 *
	 * @return
	 */
	@GetMapping("/terminalCount")
	public R<TerminalCountResponse> statTerminalCount(BladeUser user) {

		List<Long> deptId = new ArrayList<>();
		// 超级管理员查所有
		if (!AuthUtil.isAdministrator()) {
			try {
				R<List<Long>> res = sysClient.getRegulatesDeptIds(user.getUserId());
				if (res == null || !res.isSuccess()) {
					log.error("查询用户监管的企业信息失败：调用feign接口失败");
				}
				deptId = res.getData();
			} catch (Exception e) {
				log.error("查询用户监管的企业信息失败", e);
			}
		}
		TerminalCountResponse res = new TerminalCountResponse();
		//1.统计终端总数
		long totalCount = rnssDeviceService.countTerminal(user.getUserId());
		//2.统计在线终端总数
		Set<String> keys = stringRedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
			Set<String> result = new HashSet<>();
			Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(CommonConstant.PREFIX_REDIS_ONLINESTATE + "*").count(1000).build());
			while (cursor.hasNext()) {
				result.add(new String(cursor.next()));
			}
			return result;
		});

		String keyToExcludePrefix = CommonConstant.PREFIX_REDIS_ONLINESTATE + "Delay";
		// 使用stream过滤掉所有以keyToExcludePrefix为前缀的键
		keys = keys.stream()
			.filter(key -> !key.startsWith(keyToExcludePrefix))
			.collect(Collectors.toSet());

		Long count = 0L;
		//计算是否在监管部门中
		List<String> list = stringRedisTemplate.opsForValue().multiGet(keys);
		if(AuthUtil.isAdministrator()){
			count = keys.stream().count();
		}else {
			for (String value : list) {
				TerminalInfo terminalInfo = JSONObject.parseObject(value, TerminalInfo.class);
				if(deptId.contains(terminalInfo.getDeptId())){
					count ++;
				}
			}
		}

		res.setTerminalTotalCount(totalCount);
		res.setTerminalOnlineCount(count);
		return R.data(res);
	}


}
