package com.xh.vdm.bdCheck.mapper.bdCheck;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCheckReport;
import com.xh.vdm.bdCheck.vo.bdCheck.BDCReportRequest;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 检测报告表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@DS("bdCheck")
public interface BdcCheckReportMapper extends BaseMapper<BdcCheckReport> {

	IPage<BdcCheckReport> getCheckReportPage(@Param("request") BDCReportRequest request, IPage<BdcCheckReport> page);

	/**
	 * 重置检测信息状态
	 * @param reportId
	 */
	void resetReportCheckState(Long reportId);
}
