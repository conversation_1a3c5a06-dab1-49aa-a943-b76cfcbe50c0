package com.xh.vdm.bdCheck.vo.bdCheck;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCheckReport;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 检测记录信息
 * <AUTHOR>
 * @since 2024-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName(schema = "bd_check")
public class BdcCheckReportRequest extends BdcCheckReport implements Serializable {

	//检测记录中关联的终端列表
	@JsonProperty("terminalCheckData")
    private List<BdcTerminal> terminalCheckList;

	@JsonProperty("deviceType")
	private String terminalType;

	@JsonProperty("deviceModel")
	private String terminalModel;

	@JsonProperty("deviceCate")
	private String terminalCate;

	private String manufacturer;

	private String protocol;
	//送检日期
	private Date sendDate;

}
