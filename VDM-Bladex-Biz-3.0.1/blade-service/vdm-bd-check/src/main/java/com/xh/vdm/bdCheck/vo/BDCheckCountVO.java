package com.xh.vdm.bdCheck.vo;

import lombok.Data;

/**
 * 北斗检测数量统计VO
 * 用于封装总检测数量和非北斗检测数量
 * 
 * <AUTHOR>
 * @since 2024-04-20
 */
@Data
public class BDCheckCountVO {

    /**
     * 总检测数量
     */
    private Long totalCount;

    /**
     * 非北斗检测数量
     */
    private Long nonBdCount;

    public BDCheckCountVO() {
    }

    public BDCheckCountVO(Long totalCount, Long nonBdCount) {
        this.totalCount = totalCount;
        this.nonBdCount = nonBdCount;
    }
}
