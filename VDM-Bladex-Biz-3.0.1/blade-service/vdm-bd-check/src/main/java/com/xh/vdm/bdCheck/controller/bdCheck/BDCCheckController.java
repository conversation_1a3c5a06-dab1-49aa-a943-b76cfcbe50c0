package com.xh.vdm.bdCheck.controller.bdCheck;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.xh.vdm.bdCheck.config.bdCheck.TemplateTableRenderPolicy;
import com.xh.vdm.bdCheck.constant.BdCheckConstant;
import com.xh.vdm.bdCheck.dto.bdCheck.BdcTerminalUpdateDTO;
import com.xh.vdm.bdCheck.entity.BladeParam;
import com.xh.vdm.bdCheck.entity.bdCheck.BDCheckLocations;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCheckReport;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCompany;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcTerminal;
import com.xh.vdm.bdCheck.service.IBladeParamService;
import com.xh.vdm.bdCheck.service.bdCheck.IBDCheckLocationsService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcCheckReportService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcCompanyService;
import com.xh.vdm.bdCheck.service.bdCheck.IBdcTerminalService;
import com.xh.vdm.bdCheck.util.CheckCacheUtil;
import com.xh.vdm.bdCheck.vo.bdCheck.*;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.PutObjectArgs;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.apache.kudu.Common;
import org.springblade.common.annotation.Log;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.enums.ObjectType;
import org.springblade.common.enums.Operation;
import org.springblade.common.minio.MinioService;
import org.springblade.common.utils.DateUtil;
import org.springblade.common.utils.compare.CompareUtils;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.oss.model.BladeFile;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.core.tool.utils.Func;
import org.springblade.resource.feign.IOssClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.PrintWriter;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;


/**
 * 北斗识别检测
 */
@RestController
@RequestMapping("/check")
@Slf4j
public class BDCCheckController {

	@Resource
	private IBdcCheckReportService reportService;

	@Resource
	private CheckCacheUtil cacheUtil;

	@Resource
	private IOssClient ossClient;

	@Resource
	private MinioService minioService;

	@Resource
	private IBdcTerminalService terminalService;

	@Resource
	private IBDCheckLocationsService locationsService;

	@Resource
	private IBdcCompanyService companyService;

	@Resource
	private IBladeParamService paramService;


	@Value("${bd-check.template-path}")
	private String reportTemplatePath;

	@Value("${bd-check.file-path}")
	private String reportFilePath;

	@Value("${bd-check.proxy-path}")
	private String reportProxyPath;

	@Resource
	private KafkaTemplate kafkaTemplate;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	private static Map<String, String> checkResMap = new HashMap<>();

	static {
		checkResMap.put("1", "通过");
		checkResMap.put("0", "不通过");
	}

	private static Map<String, String> checkTypeMap = new HashMap<>();

	static {
		checkTypeMap.put("1", "抽检");
		checkTypeMap.put("2", "其他");
	}

	/**
	 * 查看报告数据
	 *
	 * @param request
	 * @param query
	 * @return
	 */
	@PostMapping("/checkReport")
	public R<IPage<BDCReportResponse>> checkReport(@RequestBody BDCReportRequest request, Query query) {

		try {
			//1.根据条件分页查询报告数据
			IPage<BdcCheckReport> reportPage = reportService.findCheckReportPage(request, query);
			if (reportPage == null || reportPage.getRecords() == null || reportPage.getRecords().size() < 1) {
				return R.data(null);
			}
			List<BDCReportResponse> resList = new ArrayList<>();
			//存储企业信息
			Map<Long, String> map = new HashMap<>();

			//2.查询每个报告对应的终端检测信息
			for (BdcCheckReport report : reportPage.getRecords()) {
				BDCReportResponse res = new BDCReportResponse();
				BeanUtils.copyProperties(report, res);
				long reportId = report.getId();
				//2.1 查询报告关联的终端
				List<BdcTerminal> termianlList = terminalService.list(Wrappers.lambdaQuery(BdcTerminal.class)
					.eq(BdcTerminal::getReportId, reportId).eq(BdcTerminal::getIsDel, 0));
				//2.2 查询终端的检测数据
				//如果终端已经检测完成，则数据量查询终端表中的；否则，统计kudu表中的数据
				List<BDCTerminalCheckData> tList = new ArrayList<>();
				for (BdcTerminal terminal : termianlList) {
					if (terminal.getIsInCheck() != 1) {
						continue;
					}
					//查询校验数据总数
					long totalCount = 0;
					long bdCount = 0;
					long nonBDCount = 0;
					//如果还没有开始检测，则设置检测数据为0
					if (terminal.getCheckResult() != null) {
						//如果已经开始了检测了或者检测完成，则查询impala
						if(CommonConstant.CHECK_PROCESS_BD_CHECKING.equals(report.getCheckProcess()) || CommonConstant.CHECK_PROCESS_FINISH.equals(report.getCheckProcess())){
							//只查询在检测时段内的数据量
							LambdaQueryWrapper<BDCheckLocations> wrapperTotal = Wrappers.lambdaQuery(BDCheckLocations.class)
								.eq(BDCheckLocations::getDeviceNo, terminal.getDeviceNo())
								.eq(BDCheckLocations::getValid, 1);
							if (terminal.getCheckStartDate() != null) {
								wrapperTotal.ge(BDCheckLocations::getLocTime, terminal.getCheckStartDate().getTime() / 1000);
							}
							if (terminal.getCheckEndDate() != null) {
								wrapperTotal.lt(BDCheckLocations::getLocTime, terminal.getCheckEndDate().getTime() / 1000);
							}
							totalCount = locationsService.count(wrapperTotal);
							LambdaQueryWrapper<BDCheckLocations> wrapperBD = Wrappers.lambdaQuery(BDCheckLocations.class)
								.eq(BDCheckLocations::getDeviceNo, terminal.getDeviceNo())
								.eq(BDCheckLocations::getValid, 1)
								.eq(BDCheckLocations::getCheckRes, '1');
							if (terminal.getCheckStartDate() != null) {
								wrapperBD.ge(BDCheckLocations::getLocTime, terminal.getCheckStartDate().getTime() / 1000);
							}
							if (terminal.getCheckEndDate() != null) {
								wrapperBD.lt(BDCheckLocations::getLocTime, terminal.getCheckEndDate().getTime() / 1000);
							}
							bdCount = locationsService.count(wrapperBD);
							nonBDCount = totalCount - bdCount;
						}

					}
					terminal.setCheckTotalDataCount(totalCount);
					terminal.setCheckBDDataCount(bdCount);
					terminal.setCheckNonBDCount(nonBDCount);

					BDCTerminalCheckData t = new BDCTerminalCheckData();
					if (StringUtils.isEmpty(terminal.getCheckResult())) {
						t.setCheckResult(0);
					} else {
						t.setCheckResult(Integer.parseInt(terminal.getCheckResult()));
					}

					t.setCheckDataCount(terminal.getCheckTotalDataCount());
					t.setCheckBDDataCount(terminal.getCheckBDDataCount());
					t.setCheckNonBDDataCount(terminal.getCheckNonBDCount());
					t.setTerminalNo(terminal.getDeviceNo());
					t.setTerminalModel(terminal.getDeviceModel());
					t.setTerminalType(terminal.getDeviceType());
					t.setTestResult(terminal.getTestResult());
					t.setTestResMessage(terminal.getTestResMessage());
					t.setCheckResMessage(terminal.getCheckResMessage());
					t.setSim(terminal.getSim());
					t.setIsInCheck(terminal.getIsInCheck());
					t.setProtocol(terminal.getProtocol());

					//设置检测天数
					/*String checkDayCount = "";
					if(terminal.getCheckResult() != null){
						//如果检测完成
						if(terminal.getCheckEndDate() == null){
							//如果检测结束时间为空
							checkDayCount = "ERROR";
						}else{
							int count = DateUtil.workDayCount(terminal.getCheckStartDate(), terminal.getCheckEndDate());
							checkDayCount = "" + count;
						}
					}else{
						//如果检测未完成
						if(terminal.getCheckStartDate() == null){
							if(report.getCheckStartTime() == null){
								checkDayCount = "";
							}else {
								//如果终端的开始检测时间为空，则查询对应的检测报告的开始检测日期
								terminal.setCheckStartDate(report.getCheckStartTime());
								int count = DateUtil.workDayCount(terminal.getCheckStartDate(), new Date());
								checkDayCount = "" + count;
							}
						}
					}
					t.setCheckDayCount(checkDayCount);*/
					tList.add(t);
				}
				res.setTerminalCheckData(tList);
				resList.add(res);

				//2.3 查询企业名称
				Long companyId = res.getCompanyId();
				String companyName = map.get(companyId);
				if (StringUtils.isEmpty(companyName)) {
					BdcCompany company = companyService.getOne(Wrappers.lambdaQuery(BdcCompany.class).eq(BdcCompany::getId, companyId));
					res.setCompanyName(company == null ? "" : company.getCompanyName());
				}

			}

			//3.组合数据
			IPage<BDCReportResponse> resPage = new Page<>();
			BeanUtils.copyProperties(reportPage, resPage);
			resPage.setRecords(resList);
			return R.data(resPage);
		} catch (Exception e) {
			log.error("[北斗识别检测]查询检测数据失败", e);
			return R.fail("查询失败");
		}
	}


	/**
	 * 北斗识别检测 终端检测详情
	 * @param request
	 * @param query
	 * @return
	 */
	@PostMapping("/checkDetail")
	public R<IPage<BDCCheckDetailResponse>> checkDetail(@RequestBody BDCCheckDetailRequest request, Query query){
		if(StringUtils.isEmpty(request.getTerminalNo())){
			return R.fail("终端编号不能为空");
		}
		try {
			BdcCheckReport report = terminalService.findReportByTerminalNo(request.getTerminalNo());
			if(report == null){
				return R.fail("所属检测信息不存在");
			}
			if(!(CommonConstant.CHECK_PROCESS_BD_CHECKING.equals(report.getCheckProcess()) || CommonConstant.CHECK_PROCESS_FINISH.equals(report.getCheckProcess()))){
				//如果还没有开始检测，则不展示检测详情
				IPage<BDCCheckDetailResponse> resPage = new Page<>();
				return R.data(resPage);
			}else{
				LambdaQueryWrapper<BDCheckLocations> wrapper = Wrappers.lambdaQuery(BDCheckLocations.class);
				wrapper.eq(BDCheckLocations::getDeviceNo, request.getTerminalNo()).eq(BDCheckLocations::getValid, 1);

				if(!StringUtils.isEmpty(request.getCheckRes())){
					wrapper.eq(BDCheckLocations::getCheckRes, request.getCheckRes());
				}

				if (request.getStartTime() != null) {
					if(request.getStartTime() * 1000 < report.getCheckStartTime().getTime()){
						wrapper.ge(BDCheckLocations::getLocTime, report.getCheckStartTime().getTime() / 1000);
					}else{
						wrapper.ge(BDCheckLocations::getLocTime, request.getStartTime());
					}
				} else {
					wrapper.ge(BDCheckLocations::getLocTime, report.getCheckStartTime().getTime() / 1000);
				}
				if (request.getEndTime() != null) {
					wrapper.lt(BDCheckLocations::getLocTime, request.getEndTime());
				} else if (report.getCheckEndTime() != null) {
					wrapper.lt(BDCheckLocations::getLocTime, report.getCheckEndTime().getTime() / 1000);
				}

				wrapper.orderByDesc(BDCheckLocations::getLocTime);
				IPage<BDCheckLocations> page = Condition.getPage(query);
				page = locationsService.page(page, wrapper);
				IPage<BDCCheckDetailResponse> resPage = new Page<>();
				resPage.setTotal(page.getTotal());
				resPage.setPages(page.getPages());
				resPage.setSize(page.getSize());
				resPage.setCurrent(page.getCurrent());
				List<BDCCheckDetailResponse> resList = new ArrayList<>();
				if(page.getRecords() != null && !page.getRecords().isEmpty()){
					page.getRecords().forEach(t -> {
						BDCCheckDetailResponse res = new BDCCheckDetailResponse();
						res.setCheckResMessage(t.getCheckResMessage());
						res.setCheckRes(t.getCheckRes());
						res.setLocTime(t.getLocTime());
						res.setTerminalNo(t.getDeviceNo());
//						res.setTerminalModel(t.getDeviceModel());
						res.setTerminalModel(report.getTerminalModel());
//						res.setTerminalType(t.getDeviceType()+"");
						res.setTerminalType(report.getTerminalType());
						res.setLocTimeStr(DateUtil.getDateTimeString(t.getLocTime()));
						resList.add(res);
					});
					resPage.setRecords(resList);
					return R.data(resPage);
				}else{
					resPage = new Page<>();
					return R.data(resPage);
				}
			}
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}

	/**
	 * 开始北斗检测
	 *
	 * @param ids
	 * @return
	 */
	@GetMapping("/startBDCheck")
	public R<String> startBDCheck(String ids){
		if(ids == null || ids.length() < 1){
			return R.fail("操作失败，未选择报告");
		}
		List<Long> idList = Func.toLongList(ids);
		StringBuffer sb = new StringBuffer();
		int successCount = 0;
		for(Long id : idList){
			try {
				startBDCheckSingle(id);
				successCount ++;
			}catch (Exception e){
				sb.append(id + " :" + e.getMessage()).append(",");
				log.error("开始检测失败："+e);
			}
		}
		if(successCount == idList.size()){
			return R.success("操作成功");
		}
		if(successCount <= 0){
			return R.fail("操作失败: "+ sb.toString());
		}
		if(sb.length() > 0){
			return R.success("部分成功，检测信息["+sb.substring(0, sb.length()-1)+"]开始检测失败，请确认");
		}
		return R.success("操作成功");
	}


	/**
	 * 开始检测-单个报告
	 * @param reportId
	 */
	private void startBDCheckSingle(Long reportId) throws Exception{
		//0.检测报告信息是否存在
		BdcCheckReport report = reportService.getById(reportId);
		if(report == null){
			throw new Exception("报告信息["+reportId+"]不存在");
		}
		//还未通过接口检测的记录不能检测
		if(report.getTestResult() != 1){
			throw new Exception("接口检测未通过");
		}
		//已经检测过的检测记录不能再次检测
		if(CommonConstant.CHECK_PROCESS_FINISH.equals(report.getCheckProcess())){
			throw new Exception("检测信息["+reportId+"]已经检测完成，不能再次检测");
		}
		//正在北斗检测的检测记录不能检测
		if(CommonConstant.CHECK_PROCESS_BD_CHECKING.equals(report.getCheckProcess())){
			throw new Exception("检测信息["+reportId+"]已经开始检测，无需再次检测");
		}


		//1.更新报告检测时间和检测进度
		//更新检测时间
		Date startDate = new Date();
		report.setCheckStartTime(startDate);
		//更新检测进度
		report.setCheckProcess(CommonConstant.CHECK_PROCESS_BD_CHECKING);
		reportService.updateById(report);

		//2.更新终端开始检测时间
		List<BdcTerminal> list = terminalService.list(Wrappers.lambdaQuery(BdcTerminal.class)
			.eq(BdcTerminal::getReportId, reportId)
			.eq(BdcTerminal::getIsDel, 0)
			.eq(BdcTerminal::getIsInCheck, 1));
		for(BdcTerminal t : list){
			t.setCheckStartDate(startDate);
		}
		terminalService.updateBatchById(list);
	}



	/**
	 * 结束检测
	 * @param ids 终端id列表
	 * @return
	 */
	@GetMapping("/finishCheck")
	public R<String> finishCheck(String ids){

		if(ids == null || ids.length() < 1){
			return R.fail("操作失败，未选择报告");
		}
		List<Long> idList = Func.toLongList(ids);
		StringBuffer sb = new StringBuffer();
		int successCount = 0;
		StringBuffer errMessage = new StringBuffer();
		for(Long id : idList){
			try {
				finishCheckSingle(id);
				successCount ++;
			}catch (Exception e){
				sb.append(id).append(",");
				log.error("生成报告失败："+e);
				errMessage.append(e.getMessage()+"；");
			}
		}
		if(successCount == idList.size()){
			return R.success("操作成功");
		}
		if(successCount <= 0){
			return R.fail("操作失败：" + errMessage.toString());
		}
		if(sb.length() > 0){
			return R.success("部分成功，报告["+sb.substring(0, sb.length()-1)+"]生成失败，请确认");
		}
		return R.success("操作成功");
	}

	/**
	 * 结束检测单个报告数据
	 * @param reportId
	 * @throws Exception
	 */
	private void finishCheckSingle(Long reportId) throws Exception {
		//0.检测报告信息是否存在
		BdcCheckReport report = reportService.getById(reportId);
		if (report == null) {
			throw new Exception("报告信息[" + reportId + "]不存在");
		}

		//1.校验检测结果（如果全部超过阈值，则认为该批次终端为单北斗终端）
		List<BdcTerminal> list = terminalService.list(Wrappers.lambdaQuery(BdcTerminal.class)
			.eq(BdcTerminal::getReportId, reportId)
			.eq(BdcTerminal::getIsDel, 0)
			.eq(BdcTerminal::getIsInCheck, 1));
		//查看是否有终端检测结果未超过阈值
		//目前是设置的 100%，也就是说，终端的定位点中，如果存在一个定位点不是北斗定位，则认为终端为疑似非北斗终端
		BladeParam param = paramService.getOne(Wrappers.lambdaQuery(BladeParam.class).eq(BladeParam::getParamKey, CommonConstant.PARAM_BD_CHECK_THRESHOLD));
		Double threshold = Double.parseDouble(param.getParamValue());
		StringBuffer failTerminalList = new StringBuffer();
		Date endTime = new Date();
		for (BdcTerminal t : list) {

			//校验开始检测时间
			if(t.getCheckStartDate() == null){
				throw new Exception("终端["+t.getDeviceSeq()+"]未查询到开始检测时间，不能结束检测，请检查");
			}
			//2.更新检测数据量
			//2.1 查询总检测数据量，筛选在检测时间段内的数据量
			long totalCount = locationsService.count(Wrappers.lambdaQuery(BDCheckLocations.class)
				.eq(BDCheckLocations::getDeviceNo, t.getDeviceSeq())
				.eq(BDCheckLocations::getValid, 1)
				.ge(BDCheckLocations::getLocTime, t.getCheckStartDate().getTime() / 1000)
				.lt(BDCheckLocations::getLocTime, endTime.getTime() / 1000));
			//2.2 查询非单北斗数据量
			long nonBDCount = locationsService.count(Wrappers.lambdaQuery(BDCheckLocations.class)
				.eq(BDCheckLocations::getDeviceNo, t.getDeviceNo())
				.eq(BDCheckLocations::getValid, 1)
				.eq(BDCheckLocations::getCheckRes, CommonConstant.LOCATION_CHECK_RES_NO_BD)
				.ge(BDCheckLocations::getLocTime, t.getCheckStartDate().getTime() / 1000)
				.lt(BDCheckLocations::getLocTime, endTime.getTime() / 1000));
			long bdCount = totalCount - nonBDCount;
			t.setCheckTotalDataCount(totalCount);
			t.setCheckBDDataCount(bdCount);
			t.setCheckNonBDCount(nonBDCount);

			//检测结果
			double rate = 0;
			if (t.getCheckTotalDataCount() > 0) {
				rate = (double) t.getCheckBDDataCount() / (double) t.getCheckTotalDataCount();
			}
			String checkResult = "";
			if (rate < threshold) {
				failTerminalList.append(t.getDeviceNo()).append(",");
				//更新检测结果(不通过)
				t.setCheckResult(CommonConstant.TERMINAL_CHECK_RES_NO_BD + "");
				t.setCheckResMessage("该终端定位数据检测通过率不合格，疑似为非单北斗终端");
			} else {
				t.setCheckResult(CommonConstant.TERMINAL_CHECK_RES_BD + "");
				t.setCheckResMessage("该终端通过北斗识别检测，合格");
			}

			//3.更新检测结束时间
			t.setCheckEndDate(endTime);

			terminalService.updateById(t);
		}

		//4.更改检测报告信息
		//获取终端类型字典值
		Map<String, String> terminalTypeMap = cacheUtil.getCheckTerminalTypeMap();
		report.setCheckEndTime(endTime);
		if (failTerminalList.length() > 0) {
			//检测不通过
			failTerminalList.substring(0, failTerminalList.length() - 1);
			log.info("终端[" + failTerminalList + "]检测不合格");
			report.setCheckResult(CommonConstant.REPORT_CHECK_RES_NO_BD);
			report.setCheckResMessage("本公司对" + report.getTerminalModel() + "型" + terminalTypeMap.get(report.getTerminalType()) + "终端进行北斗检测测试，所检终端不合格。");
			//检测不通过时，将该检测信息下的所有终端设置为检测不通过
			List<BdcTerminal> tList = terminalService.list(Wrappers.lambdaQuery(BdcTerminal.class)
				.eq(BdcTerminal::getReportId, reportId)
				.eq(BdcTerminal::getIsDel, 0));
			tList.forEach(item -> {
				item.setCheckResult(CommonConstant.TERMINAL_CHECK_RES_NO_BD + "");
				//item.setTestResult(1);
				item.setUpdateTime(new Date());
			});
			terminalService.updateBatchById(tList);
		} else {
			//检测通过
			report.setCheckResult(CommonConstant.REPORT_CHECK_RES_BD);
			report.setCheckResMessage("本公司对" + report.getTerminalModel() + "型" + terminalTypeMap.get(report.getTerminalType()) + "终端进行北斗检测测试，所检终端合格。");
			//检测通过后，将所有的终端状态设置为检测通过
			List<BdcTerminal> tList = terminalService.list(Wrappers.lambdaQuery(BdcTerminal.class)
				.eq(BdcTerminal::getReportId, reportId)
				.eq(BdcTerminal::getIsDel, 0));
			tList.forEach(item -> {
				item.setCheckResult(CommonConstant.TERMINAL_CHECK_RES_BD + "");
				item.setTestResult(1);
				item.setUpdateTime(new Date());
			});
			terminalService.updateBatchById(tList);
		}
		report.setCheckProcess(CommonConstant.CHECK_PROCESS_FINISH);
		reportService.updateById(report);
	}

	/**
	 * 生成检测报告
	 *
	 * @return
	 */
	@GetMapping("/createCheckReport")
	public R<List<ReportPathResponse>> createCheckReport(String ids) {
		if (StringUtils.isEmpty(ids)) {
			return R.fail("操作失败，未选择报告");
		}
		List<Long> idList = Func.toLongList(ids);

		List<ReportPathResponse> resList = new ArrayList<>();
		for (Long id : idList) {
			ReportPathResponse rr = new ReportPathResponse();
			String filePath = "";
			try {
				filePath = createCheckReportSingle(id);
				rr.setSuccess(true);
			} catch (Exception e) {
				log.error("生成报告[" + id + "]失败", e);
				rr.setSuccess(false);
				rr.setMessage(e.getMessage());
			}
			rr.setId(id);
			rr.setFilePath(filePath);
			resList.add(rr);
		}
		return R.data(resList);
	}

	/**
	 * 创建北斗识别检测报告--单个报告
	 *
	 * @param id
	 * @return
	 * @throws Exception
	 */
	private String createCheckReportSingle(Long id) throws Exception {

		BDCheckReportExportVO resVO = new BDCheckReportExportVO();

		//1.查询报告信息
		BdcCheckReport report = reportService.getById(id);

		//2.查询单位信息
		BdcCompany company = companyService.getById(report.getCompanyId());

		//3.识别明细
		//3.1 终端信息
		//查询参与检测的终端
		List<BdcTerminal> terminalList = terminalService.list(Wrappers.lambdaQuery(BdcTerminal.class)
			.eq(BdcTerminal::getReportId, id)
			.eq(BdcTerminal::getIsDel, 0)
			.eq(BdcTerminal::getIsInCheck, 1));
		if (terminalList == null || terminalList.size() < 1) {
			log.error("报告[" + id + "]生成失败：未查询到对应的终端信息");
			throw new Exception("报告[" + id + "]生成失败：未查询到对应的终端信息");
		}
		List<BDCheckTerminalExportVO> terminalEList = new ArrayList<>();
		for (BdcTerminal terminal : terminalList) {
			//3.2 统计该终端的检测数据信息

			//统计检测的日期
			List<String> dateList = locationsService.findCheckDateList(terminal.getDeviceNo());
			if (dateList != null && dateList.size() > 0) {
				for (String date : dateList) {
					// 非法数据，丢弃
					if (StringUtils.isEmpty(date)) {
						continue;
					}

					//校验检测时间
					if (terminal.getCheckStartDate() == null || terminal.getCheckEndDate() == null) {
						throw new Exception("数据异常：检测开始时间和检测结束时间不能为空");
					}

					// 非检测时段内时间
					LocalDate day = LocalDate.parse(date);
					LocalDate startDate = terminal.getCheckStartDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
					LocalDate endDate = terminal.getCheckEndDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
					if (day.isBefore(startDate) || day.isAfter(endDate)) {
						continue;
					}

					//统计每日检测的数据量
					long totalCount = locationsService.count(Wrappers.lambdaQuery(BDCheckLocations.class)
						.eq(BDCheckLocations::getDeviceNo, terminal.getDeviceNo())
						.eq(BDCheckLocations::getValid, 1)
						.eq(BDCheckLocations::getDateStr, date)
						.ge(BDCheckLocations::getLocTime, terminal.getCheckStartDate().getTime() / 1000)
						.lt(BDCheckLocations::getLocTime, terminal.getCheckEndDate().getTime() / 1000));

					long bdCount = locationsService.count(Wrappers.lambdaQuery(BDCheckLocations.class)
						.eq(BDCheckLocations::getDeviceNo, terminal.getDeviceNo())
						.eq(BDCheckLocations::getValid, 1)
						.eq(BDCheckLocations::getCheckRes, '1')
						.eq(BDCheckLocations::getDateStr, date)
						.ge(BDCheckLocations::getLocTime, terminal.getCheckStartDate().getTime() / 1000)
						.lt(BDCheckLocations::getLocTime, terminal.getCheckEndDate().getTime() / 1000));
					long nonBDCount = totalCount - bdCount;

					//3.2 终端中的定位数据检测信息
					BDCheckTerminalExportVO vo = new BDCheckTerminalExportVO();
					vo.setTerminalNo(terminal.getDeviceNo());
					vo.setCheckDate(date);
					vo.setCheckBDDataCount(bdCount + "");
					vo.setCheckTotalDataCount(totalCount + "");
					vo.setCheckNonBDCount(nonBDCount + "");
					//查询轨迹点：如果该终端检测通过，则查询该天开始的10个定位点；如果该终端定位不通过，则展示今日该终端的10个检测不通过的定位点
					IPage<BDCheckLocations> page = new Page<>(1, 10);
					if (CommonConstant.TERMINAL_CHECK_RES_NO_BD.equals(Integer.valueOf(terminal.getCheckResult()))) {
						//如果检测不通过
						IPage<BDCheckLocations> pageRes = locationsService.page(page, Wrappers.lambdaQuery(BDCheckLocations.class)
							.eq(BDCheckLocations::getCheckRes, CommonConstant.LOCATION_CHECK_RES_NO_BD)
							.eq(BDCheckLocations::getDeviceNo, terminal.getDeviceNo())
							.eq(BDCheckLocations::getDateStr, date)
							.orderByAsc(BDCheckLocations::getLocTime));
						if (pageRes == null || pageRes.getRecords() == null || pageRes.getRecords().size() < 1) {
						} else {
							for (BDCheckLocations loc : pageRes.getRecords()) {
								vo.setLocCheckRes(checkResMap.get(loc.getCheckRes()));
								vo.setLocTime(DateUtil.getDateString(loc.getLocTime()));
								//vo.setLocCheckResMessage(loc.getCheckResMessage());
								terminalEList.add(vo);
							}
						}
					} else {
						//如果检测通过
						IPage<BDCheckLocations> pageRes = locationsService.page(page, Wrappers.lambdaQuery(BDCheckLocations.class)
							.eq(BDCheckLocations::getCheckRes, CommonConstant.LOCATION_CHECK_RES_BD)
							.eq(BDCheckLocations::getDeviceNo, terminal.getDeviceNo())
							.eq(BDCheckLocations::getDateStr, date)
							.orderByAsc(BDCheckLocations::getLocTime));
						List<BDCheckLocExportVO> eVOList = new ArrayList<>();
						if (pageRes == null || pageRes.getRecords() == null || pageRes.getRecords().size() < 1) {
						} else {
							for (BDCheckLocations loc : pageRes.getRecords()) {
								vo.setLocCheckRes(checkResMap.get(loc.getCheckRes()));
								vo.setLocTime(DateUtil.getDateString(loc.getLocTime()));
								//vo.setLocCheckResMessage(loc.getCheckResMessage());
								terminalEList.add(vo);
							}
						}
					}
				}
			}

		}


		//4.导出数据
		resVO.setCheckBase(report.getCheckBase());
		resVO.setCheckMethod(report.getCheckMethod());
		resVO.setCheckStartTime(DateUtil.sdfHolderShort.get().format(report.getCheckStartTime()));
		resVO.setCheckEndTime(DateUtil.sdfHolderShort.get().format(report.getCheckEndTime()));
		resVO.setAddress(company.getAddress());
		resVO.setCheckType(checkTypeMap.get(report.getCheckType()));
		resVO.setReportNo(report.getReportNo());
		resVO.setTerminalModel(report.getTerminalModel());
		resVO.setBatchNo(report.getBatchNo());
		resVO.setCheckAddress(report.getCheckAddress());
		//resVO.setTerminalSize(report.getTerminalList()==null?"0":report.getTerminalList().split(",").length+"");
		resVO.setCompanyName(company.getCompanyName());
		resVO.setCheckResMessage(report.getCheckResMessage());
		if (terminalEList == null || terminalEList.size() == 0) {
			//如果没有终端定位数据，则给定默认值，防止导出文件中出现错误
			BDCheckTerminalExportVO vo = new BDCheckTerminalExportVO();
			//vo.setLocCheckResMessage("");
			vo.setLocCheckRes("");
			vo.setCheckDate("");
			vo.setCheckBDDataCount("");
			vo.setCheckNonBDCount("");
			vo.setCheckTotalDataCount("");
			vo.setTerminalNo("");
			vo.setLocTime("");
			terminalEList.add(vo);
		}
		resVO.setTerminalList(terminalEList);

		//设置全部终端数量
		long terminalSize = terminalService.count(Wrappers.lambdaQuery(BdcTerminal.class)
			.eq(BdcTerminal::getReportId, id)
			.eq(BdcTerminal::getIsDel, 0));
		resVO.setTerminalSize(terminalSize + "");

		//终端类型转义
		Map<String, String> terminalTypeMap = cacheUtil.getTerminalTypeMap();
		resVO.setTerminalType(terminalTypeMap.get(report.getTerminalType()));
		String proxyPath = exportReport(resVO);

		//5.更新报告数据
		report.setReportFile(proxyPath);
		report.setReportTime(new Date());
		reportService.updateById(report);
		return proxyPath;
	}

	/**
	 * 导出日报数据到文件，docx文件
	 *
	 * @return 报表文件的代理地址
	 */
	private String exportReport(BDCheckReportExportVO vo) throws Exception {

		//获取url前缀（ip、port）
		BladeParam param = paramService.getOne(Wrappers.lambdaQuery(BladeParam.class).eq(BladeParam::getParamKey, CommonConstant.PARAM_CHECK_REPORT_PREFIX));
		String urlPrefix = param.getParamValue();

		//文件名称，防止冲突
		String fileName = vo.getReportNo() + ".docx";
		String filePath = reportFilePath + fileName;
		//代理地址，nginx上使用的代理地址
		String resFileUrl = urlPrefix + reportProxyPath + fileName;
		String templatePath = reportTemplatePath;

		XWPFTemplate template = null;
		FileOutputStream fos = null;
		try {
			//2.编译模板，渲染数据
			//LoopRowTableRenderPolicy hackLoopTableRenderPolicy = new LoopRowTableRenderPolicy();
			TemplateTableRenderPolicy policy = new TemplateTableRenderPolicy();
			Configure config =
				Configure.builder().bind("terminalList", policy)
					//.bind("locList", hackLoopTableRenderPolicy)
					.build();
			template = XWPFTemplate.compile(templatePath, config).render(vo);

			//3.写入到指定目录位置
			fos = new FileOutputStream(filePath);
			template.write(fos);
			return resFileUrl;
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception(e);
		} finally {
			if (fos != null) {
				try {
					fos.flush();
					fos.close();
				} catch (Exception e) {
				}
			}
			if (template != null) {
				try {
					template.close();
				} catch (Exception e) {
				}
			}
		}
	}

	/**
	 * 新增检测信息
	 * 包含检测信息 和 终端信息
	 * 终端入网管理--入网终端管理
	 *
	 * @param request
	 * @return
	 */
	@PostMapping("/addCheckReport")
	@Log(menu = "入网终端管理", operation = Operation.INSERT, objectType = ObjectType.TERMINALCHECK)
	public R<String> addCheckReport(@RequestBody BdcCheckReportRequest request, BladeUser user) {

		try {
			List<BdcTerminal> terminalList = request.getTerminalCheckList();
			//2.1 判断终端序列号是否已经存在，如果存在则生成文件
			String listStr = "";
			StringBuffer sb = new StringBuffer();
			Set<String> set = new HashSet<>();
			terminalList.forEach(item -> {
				sb.append(item.getDeviceSeq()).append(",");
				set.add(item.getDeviceSeq());
				item.setDeviceNo(item.getDeviceSeq());
			});
			//判断本批次中是否存在重复数据
			if(terminalList != null && set != null && terminalList.size() > set.size()){
				return R.fail("本批次中存在重复的终端序列号");
			}
			if (sb.length() > 0) {
				listStr = sb.substring(0, sb.lastIndexOf(","));
			}
			File fileOri = null;
			String deviceListStr = "{" + listStr + "}";
			List<String> existDevice = terminalService.findExistDeviceNo(deviceListStr);
			long reportId = 0;
			if (existDevice != null && existDevice.size() > 0) {
				//如果要添加的终端已经存在，则不允许添加，并将已经存在的终端序列号写入到文件中,最后上传到minio中
				//生成文件到临时目录
				PrintWriter writer = null;
				listStr = String.join(",",existDevice);
				try {
					String filePath = reportFilePath + File.separator + "exist_device_no_" + System.currentTimeMillis() + ".txt";
					fileOri = new File(filePath);
					writer = new PrintWriter(fileOri);
					listStr = listStr.replace(",", "\r\n");
					listStr = "发生重复的终端序列号为：\r\n" + listStr;
					writer.println(listStr);
				} catch (Exception e) {
					log.error("生成检测重复文件失败", e);
					return R.fail("录入的终端数据重复，请检查后再次录入");
				} finally {
					if (writer != null) {
						writer.flush();
						writer.close();
						if (fileOri != null) {
							//指定文件名称
							String fileName = request.getTerminalModel() + "~" + request.getBatchNo() + "~" + DateUtil.getDateStringWhole() + ".txt";
							FileInputStream stream = new FileInputStream(fileOri);
							String fileUrl = minioService.uploadFileWithFileName(fileName, stream, fileOri.length());

							if (!StringUtils.isEmpty(fileUrl)) {
								return R.data(ResultCode.FAILURE.getCode(), fileUrl, "终端序列号重复，具体信息已自动生成文件并下载，请查看");
							} else {
								//如果上传minio失败
								log.error("上传minio失败");
								return R.fail("录入的终端数据重复，请检查后再次录入");
							}
						}
					}
				}
			} else {
				//1.保存检测信息
				BdcCheckReport report = new BdcCheckReport();
				BeanUtils.copyProperties(request, report);
				report.setState(CommonConstant.STATE_U);
				report.setCreateTime(new Date());
				report.setReportNo(CommonConstant.REPORT_NO_PREFIX + System.currentTimeMillis());
				//保存检测进度
				report.setCheckProcess(CommonConstant.CHECK_PROCESS_BEFORE_CHECK);
				reportService.save(report);
				reportId = report.getId();
				// 构建查询条件
				String[] split = listStr.split(",");
				if (split.length > 0) {
					List<String> deviceSeqList = terminalService.getBaseMapper()
						.selectList(new QueryWrapper<BdcTerminal>().in("device_seq", split)).stream().map(BdcTerminal::getDeviceSeq).collect(Collectors.toList());

					if (deviceSeqList.size() > 0) {
						List<BdcTerminal> list = new ArrayList<>();
						for (String deviceSeq : split) {
							if (!deviceSeqList.contains(deviceSeq)) {
								//2.保存终端信息
								//2.2 保存终端信息
								BdcTerminal t = terminalList.stream()
									.filter(terminal -> terminal.getDeviceSeq().equals(deviceSeq))
									.findFirst()
									.orElse(null);
								t.setDeviceNo(t.getDeviceSeq());
								t.setCompanyId(request.getCompanyId());
								t.setReportId(report.getId());
								t.setDeviceType(request.getTerminalType());
								t.setDeviceModel(request.getTerminalModel());
								t.setProtocol(request.getProtocol());
								t.setManufacturer(request.getManufacturer());
								t.setCreateTime(new Date());
								t.setIsDel(0);
								list.add(t);
							} else {
								//2.保存终端信息
								//2.2 保存终端信息
								BdcTerminal t = terminalList.stream()
									.filter(terminal -> terminal.getDeviceSeq().equals(deviceSeq))
									.findFirst()
									.orElse(null);
								t.setDeviceNo(t.getDeviceSeq());
								t.setCompanyId(request.getCompanyId());
								t.setReportId(report.getId());
								t.setDeviceType(request.getTerminalType());
								t.setDeviceModel(request.getTerminalModel());
								t.setProtocol(request.getProtocol());
								t.setManufacturer(request.getManufacturer());
								t.setCreateTime(new Date());
								t.setIsDel(0);
								terminalService.updateByDeviceSeq(t);
							}
						}
						terminalService.saveBatch(list);
					} else {
						if (terminalList != null && terminalList.size() > 0) {
							for (BdcTerminal t : terminalList) {
								t.setDeviceNo(t.getDeviceSeq());
								t.setCompanyId(request.getCompanyId());
								t.setReportId(report.getId());
								t.setDeviceType(request.getTerminalType());
								t.setDeviceModel(request.getTerminalModel());
								t.setProtocol(request.getProtocol());
								t.setManufacturer(request.getManufacturer());
								t.setCreateTime(new Date());
								t.setIsDel(0);
							}
							terminalService.saveBatch(terminalList);
						}
					}
				}
			}
			return R.data(ResultCode.SUCCESS.getCode(), reportId + "","新增成功");
		} catch (Exception e) {
			log.error("操作失败", e);
			return R.fail("操作失败");
		}
	}


	/**
	 * 更新检测信息
	 * 包含检测信息 和 终端信息
	 * 数据更新时，不对终端的 is_in_check 进行更新
	 * 终端入网管理--入网终端管理
	 *
	 * @param request
	 * @return
	 */
	@PostMapping("/updateCheckReport")
	@Log(menu = "入网终端管理", operation = Operation.UPDATE, objectType = ObjectType.TERMINALCHECK)
	public R<String> updateCheckReport(@RequestBody BdcCheckReportRequest request, BladeUser user) {


		List<BdcTerminal> terminalListO = request.getTerminalCheckList();
		//2.1 判断终端序列号是否已经存在，如果存在则生成文件
		StringBuffer sb = new StringBuffer();
		Set<String> set = new HashSet<>();
		terminalListO.forEach(item -> {
			sb.append(item.getDeviceSeq()).append(",");
			set.add(item.getDeviceSeq());
			item.setDeviceNo(item.getDeviceSeq());
		});
		//判断本批次中是否存在重复数据
		if(terminalListO != null && set != null && terminalListO.size() > set.size()){
			return R.fail("本批次中存在重复的终端序列号");
		}

		try {
			//0.校验检测信息
			if (request.getId() == null) {
				log.error("检测信息中未包含id");
				return R.fail("未选择检测信息");
			}
			BdcCheckReport reportInDB = reportService.getById(request.getId());
			if (reportInDB == null || CommonConstant.STATE_E.equals(reportInDB.getState())) {
				//如果检测信息不存在，或者检测信息无效，则不允许修改
				return R.fail("检测信息不存在");
			}


			//1.保存检测信息
			//记录修改之前的数据
			BdcCheckReport ori = reportService.getById(request.getId());
			BdcCheckReport report = new BdcCheckReport();
			BeanUtils.copyProperties(request, report);
			reportService.updateById(report);
			//查询修改之后的数据
			BdcCheckReport now = reportService.getById(request.getId());
			String result = new CompareUtils<BdcCheckReport>().compare(ori, now);
			String terminalResult = "";

			//2.保存终端信息
			//包含id的，认为是修改；不包含id的，认为是新增；原来已经保存的，但是这次没有传过来的，认为是删除
			//2.1 查询检测信息包含的终端信息
			List<BdcTerminal> terminalInDBList = terminalService.list(Wrappers.lambdaQuery(BdcTerminal.class)
				.eq(BdcTerminal::getReportId, report.getId())
				.eq(BdcTerminal::getIsDel, 0));
			List<Long> terminalIdInDBList = new ArrayList<>();
			terminalInDBList.forEach(item -> {
				terminalIdInDBList.add(item.getId());
			});
			//2.2 处理前端传过来的终端数据
			List<BdcTerminal> terminalList = request.getTerminalCheckList();
			List<BdcTerminal> newList = new ArrayList<>();
			List<BdcTerminal> updateList = new ArrayList<>();
			//查询用于检测的终端，这些终端不允许删除
			List<BdcTerminal> isInCheckList = terminalService.list(Wrappers.lambdaQuery(BdcTerminal.class)
				.eq(BdcTerminal::getReportId, request.getId())
				.eq(BdcTerminal::getIsInCheck, 1)
				.eq(BdcTerminal::getIsDel, 0));
			List<Long> isInCheckIdList = new ArrayList<>();
			isInCheckList.forEach(item -> {
				isInCheckIdList.add(item.getId());
			});
			List<Long> deleteIdList = new ArrayList<>();
			List<Long> idList = new ArrayList<>();
			List<BdcTerminal> delTerminalList = new ArrayList<>();
			if (terminalList != null && terminalList.size() > 0) {
				for (BdcTerminal t : terminalList) {
					t.setDeviceType(request.getTerminalType());
					t.setDeviceModel(request.getTerminalModel());
					t.setProtocol(request.getProtocol());
					t.setManufacturer(request.getManufacturer());
					if (t.getId() == null) {
						//如果是新增
						t.setCreateTime(new Date());
						t.setReportId(report.getId());
						t.setIsDel(0);
						newList.add(t);
					} else {
						//如果是修改
						t.setUpdateTime(new Date());
						updateList.add(t);
						idList.add(t.getId());
					}
				}
				//整理删除的
				terminalIdInDBList.removeAll(idList);
				deleteIdList = terminalIdInDBList;
				terminalService.saveBatch(newList);
				terminalService.updateBatchById(updateList);
				terminalResult += "；新增的终端为："+JSON.toJSONString(newList)+"；修改的终端为："+JSON.toJSONString(updateList);
				List<BdcTerminal> deleteList = new ArrayList<>();
				deleteIdList.forEach(item -> {
					BdcTerminal terminal = new BdcTerminal();
					terminal.setId(item);
					terminal.setIsDel(1);
					terminal.setUpdateTime(new Date());
					//此处不再判断是否用于检测，改为前端控制
					//if (!isInCheckIdList.contains(terminal.getId())) {
						deleteList.add(terminal);
					//} else {
						//isInCheckWantDelete.set(true);
					//}
				});
				terminalService.updateBatchById(deleteList);
			} else {
				//如果没有给定终端，则删除原来的所有终端
				terminalInDBList.forEach(item -> {
					item.setIsDel(1);
					item.setUpdateTime(new Date());
					//此处不再判断是否用于检测，改为前端控制
					//if (!isInCheckIdList.contains(item.getId())) {
						delTerminalList.add(item);
					//} else {
					//	isInCheckWantDelete.set(true);
					//}
				});
				terminalResult += "；删除了所有的检测终端";
				terminalService.updateBatchById(delTerminalList);
			}

			reportService.updateById(report);
			//因为要同时控制是否用于检测与删除状态，这里转为前端控制
			/*if (isInCheckWantDelete.get()) {
				return R.fail("用于检测的终端不能删除，已忽略");
			}*/

			//向kafka中写入终端更新信息
			updateList.forEach(item -> {
				kafkaTemplate.send(BdCheckConstant.TOPIC_TERMINAL_UPDATE, item);
			});
			//向kafka中写入终端新增信息
			newList.forEach(item -> {
				kafkaTemplate.send(BdCheckConstant.TOPIC_TERMINAL_UPDATE, item);
			});
			//向kafka中写入终端删除信息
			delTerminalList.forEach(item -> {
				kafkaTemplate.send(BdCheckConstant.TOPIC_TERMINAL_UPDATE, item);
			});
			String resDesc = "";
			terminalResult = terminalResult.replace("[","").replace("]","");
			if(StringUtils.isEmpty(result)){
				resDesc = "[" + terminalResult + "]";
			}else{
				resDesc = "[" + result + terminalResult +"]";
			}
			return R.data(ResultCode.SUCCESS.getCode(), resDesc,"编辑成功");
		} catch (Exception e) {
			log.error("操作失败", e);
			return R.fail("操作失败");
		}
	}

	/**
	 * 删除检测信息和终端信息
	 * 终端入网管理--入网终端管理
	 *
	 * @param ids
	 * @return
	 */
	@GetMapping("/deleteCheckReport")
	@Log(menu = "入网终端管理", operation = Operation.DELETE, objectType = ObjectType.TERMINALCHECK)
	public R<String> deleteCheckReport(String ids, BladeUser user) {

		try {
			if (StringUtils.isEmpty(ids)) {
				return R.fail("未输入参数");
			}
			List<Long> idList = Func.toLongList(ids);
			List<BdcCheckReport> reportList = new ArrayList<>();
			List<BdcTerminal> terminalList = new ArrayList<>();
			for (Long id : idList) {
				//1.检测信息
				BdcCheckReport report = reportService.getById(id);

				//查看检测批次状态，检测中、检测通过不能删除
				Integer testResult = report.getTestResult();
				if(testResult != null && testResult == 1){
					//查看检测批次状态，检测中、检测通过不能删除
					Integer checkResult = report.getCheckResult();
					if(checkResult != null && (checkResult == 0 || checkResult == 1)){
						return R.fail("选择的检测批次中包含检测中或者检测通过的信息，不能删除，请确认");
					}
				}


				//设置无效
				report.setState(CommonConstant.STATE_E);
				reportList.add(report);


				//2.终端信息
				List<BdcTerminal> terminals = terminalService.list(Wrappers.lambdaQuery(BdcTerminal.class)
					.eq(BdcTerminal::getReportId, id).eq(BdcTerminal::getIsDel, 0));
				terminals.forEach(item -> {
					item.setIsDel(1);
				});
				terminalList.addAll(terminals);
			}
			//3.执行删除操作
			reportService.updateBatchById(reportList);
			terminalService.updateBatchById(terminalList);
		} catch (Exception e) {
			log.error("操作出错", e);
			return R.fail("操作失败");
		}
		return R.success("操作成功");
	}


	/**
	 * 重置检测状态
	 *
	 * @param reportId
	 * @return
	 */
	@GetMapping("/resetCheckStatus")
	public R<String> resetCheckStatus(Long reportId) {
		try {
			reportService.resetCheckState(reportId);
		} catch (Exception e) {
			log.error("操作失败", e);
			return R.fail("重置检测状态失败: " + e.getMessage());
		}
		return R.data("重置检测状态成功");
	}

	/**
	 * 判断终端是否在线
	 * @param deviceNum
	 * @return
	 */
	@GetMapping("/checkDeviceOnlineState")
	public R<DeviceOnlineVO> checkDeviceOnlineState(String deviceNum){
		if(StringUtils.isEmpty(deviceNum)){
			return R.fail("请输入赋码号");
		}
		//根据赋码号查询uniqueId
		Long deviceId = terminalService.findDeviceIdByDeviceNum(deviceNum);
		if(deviceId == null){
			log.error("根据赋码号["+deviceNum+"]未查询到终端deviceId");
			return R.fail("根据赋码号未查询到相应终端信息");
		}
		//从redis中查找终端是否在线
		Object rnssO = stringRedisTemplate.opsForHash().get(CommonConstant.REDIS_KEY_ONLINE_STATE, deviceId);
		DeviceOnlineVO vo = new DeviceOnlineVO();
		vo.setDeviceNum(deviceNum);
		if(rnssO != null ){
			//如果终端在线
			vo.setOnlineState("1");
		}else{
			//如果终端不在线
			vo.setOnlineState("0");
		}
		return R.data(vo);
	}


}
