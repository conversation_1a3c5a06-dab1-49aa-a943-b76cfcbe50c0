package com.xh.vdm.bdCheck.vo.bdCheck;

import com.xh.vdm.bdCheck.entity.TerminalSatelliteData;
import lombok.Data;

import java.util.List;

/**
 * 北斗识别结果
 */
@Data
public class BDCheckResultResponse {

	//终端编号
	private String deviceNo;
	//终端类型
	private String terminalType;
	//所属企业
	private String companyName;
	//北斗识别结果：1:北斗定位   0:疑似非北斗定位
	private String checkRes;
	//经度
	private Double longitude;
	//纬度
	private Double latitude;
	//识别结果描述
	private String checkResMessage;

	//北斗星历数据
	private List<TerminalSatelliteData> satellites;

	//定位时间
	private String locTime;
}
