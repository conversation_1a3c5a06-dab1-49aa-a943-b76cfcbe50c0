package com.xh.vdm.bdCheck.constant;

public enum FormalEnum {

	FORMAL_NO((byte) 0, "未正式入网"),
	FORMAL_YES((byte) 1, "已正式入网");

	private byte key;
	private String value;

	FormalEnum (byte key, String value) {
		this.setKey(key);
		this.setValue(value);
	}

	public void setKey (byte key) {
		this.key = key;
	}

	public void setValue (String value) {
		this.value = value;
	}

	public byte getKey () {
		return this.key;
	}

	public String getValue () {
		return this.value;
	}

	public static String getValueByKey (byte key) {
		FormalEnum[] formalList = FormalEnum.values();
		for (FormalEnum formal : formalList) {
			if (formal.getKey() == key) {
				return formal.getValue();
			}
		}

		return "";
	}
}
