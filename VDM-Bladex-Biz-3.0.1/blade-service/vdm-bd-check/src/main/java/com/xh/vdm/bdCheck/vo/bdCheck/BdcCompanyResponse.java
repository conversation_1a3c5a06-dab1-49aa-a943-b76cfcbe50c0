package com.xh.vdm.bdCheck.vo.bdCheck;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdcCompanyResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
	@JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 对应组织机构中的机构id
     */
	@JsonSerialize(using = ToStringSerializer.class)
    private Long deptId;

	private String deptName;

    /**
     * 企业地址
     */
    private String address;

	//联系人
	private String contactPerson;

	//联系电话
	private String contactPhone;

}
