package com.xh.vdm.bdCheck.config.bdCheck;

import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import com.deepoove.poi.policy.TableRenderPolicy;
import com.deepoove.poi.util.TableTools;
import com.xh.vdm.bdCheck.vo.bdCheck.BDCheckTerminalExportVO;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;

import java.util.List;

@NoArgsConstructor
public class TemplateTableRenderPolicy extends DynamicTableRenderPolicy {

	private XWPFTable xwpfTable;

	@Override
	public void render(XWPFTable xwpfTable, Object data) throws Exception {
		if (null == data) {
			return;
		}
		this.xwpfTable = xwpfTable;

		TemplateRowRenderData templateRowRenderData = new TemplateRowRenderData((List<BDCheckTerminalExportVO>)data);

		List<RowRenderData> typeRowRenderDataList = templateRowRenderData.getTypeRowRenderDataList();

		if (CollectionUtils.isNotEmpty(typeRowRenderDataList)) {
			// 表头下面那一行
			int typeMemberRow = 1;
			// 移除空白的表头下面那一行
			xwpfTable.removeRow(typeMemberRow);
			// 得到表头那一行的数据
			XWPFTableRow xwpfTableRow = xwpfTable.getRow(0);
			for (int i = typeRowRenderDataList.size() - 1; i > -1; i--) {
				// 重新插入表格
				XWPFTableRow insertNewTableRow = xwpfTable.insertNewTableRow(typeMemberRow);
				// 统一高度
				insertNewTableRow.setHeight(xwpfTableRow.getHeight());

				for (int j = 0; j < 7; j++) {
					insertNewTableRow.createCell();
				}
				// 渲染数据
				TableRenderPolicy.Helper.renderRow(xwpfTable.getRow(typeMemberRow), typeRowRenderDataList.get(i));
			}
			// 合并行 下标为1的行开始合并（去除表头）必须一个一个catch
			catchMergeRow(typeRowRenderDataList,0);
			catchMergeRow(typeRowRenderDataList,1);
			catchMergeRow(typeRowRenderDataList,2);
			catchMergeRow(typeRowRenderDataList,3);
			catchMergeRow(typeRowRenderDataList,4);
			catchMergeRow(typeRowRenderDataList,5);
			catchMergeRow(typeRowRenderDataList,6);
			//catchMergeRow(typeRowRenderDataList,7);
		}
	}

	private void catchMergeRow(List<RowRenderData> typeRowRenderDataList, int cell){
		try {
			mergeRow(typeRowRenderDataList,cell,1,1,false);
		}catch (RuntimeException ignore){

		}
	}

	/**
	 * 首尾指针递归判断列表下一个值是否和自己相同
	 *
	 * @param typeRowRenderDataList
	 * @param cell
	 * @param from
	 * @param to
	 * @param hasDef
	 */
	private void mergeRow(List<RowRenderData> typeRowRenderDataList, int cell, int from, int to, boolean hasDef) {
		if(from == typeRowRenderDataList.size()){
			throw new RuntimeException("跳出递归");
		}else{
			for (int i = from - 1 ; i < typeRowRenderDataList.size() - 1; i++) {
				String content = typeRowRenderDataList.get(i).getCells().get(cell).getParagraphs().get(0).getContents().toString();
				String nextContent = typeRowRenderDataList.get(i + 1).getCells().get(cell).getParagraphs().get(0).getContents().toString();
				if(nextContent.equals(content)){
					to = to + 1;
				}else{
					if(from > to){
						return;
					}
					if(from == to){
						// 整体下移一个单位
						from += 1;
						to += 1;
					}else{
						// 合并行
						TableTools.mergeCellsVertically(xwpfTable, cell, from, to);
						// 合并完成 首指针指向尾端
						from = to;
					}
					// 递归调用
					mergeRow(typeRowRenderDataList,cell,from,to,true);
				}
			}
		}
		// 如果这一列没有不同的值 全给他合并了
		if(!hasDef){
			if(from >= to){
				return;
			}
			TableTools.mergeCellsVertically(xwpfTable, cell, from, to);
		}
	}
}
