package com.xh.vdm.bdCheck.event;

import com.xh.vdm.bdCheck.service.IBdmTerminalService;
import groovy.util.logging.Slf4j;
import org.springblade.core.tool.utils.SpringUtil;
import javax.websocket.HandshakeResponse;
import javax.websocket.server.HandshakeRequest;
import javax.websocket.server.ServerEndpointConfig;

/**
 * 单终端websocket连接请求拦截配置
 */
@Slf4j
public class SingleTerminalWSConfigurator extends ServerEndpointConfig.Configurator {

	@Override
	public void modifyHandshake(ServerEndpointConfig sec, HandshakeRequest request, HandshakeResponse response) {
		// 这里可以获取请求中的HTTP头部信息，决定是否要修改握手响应
		// 例如，根据cookie或者其他HTTP头部信息来决定是否允许连接
		String deviceNum = request.getParameterMap().get("device_num").get(0);
		// 确认终端赋码号是否存在
		try {
			if (!SpringUtil.getBean(IBdmTerminalService.class).isExistDeviceNum(deviceNum)) {
				throw new RuntimeException("终端赋码号不存在！");
			}
		} catch (Exception e) {
			throw new RuntimeException(e);
		}

		System.out.printf("终端赋码号%s有效", deviceNum);
	}
}
