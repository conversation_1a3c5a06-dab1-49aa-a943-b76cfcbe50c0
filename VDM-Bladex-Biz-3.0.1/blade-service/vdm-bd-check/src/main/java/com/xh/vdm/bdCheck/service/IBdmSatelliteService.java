package com.xh.vdm.bdCheck.service;

import com.xh.vdm.bdCheck.entity.BdmSatellite;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 星历数据，年表，从中国卫星导航系统管理办公室测试评估研究中心下载的广播星历数据 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
public interface IBdmSatelliteService extends IService<BdmSatellite> {

	/**
	 * 存在即更新星历数据
	 * @return
	 */
	void saveOrUpdateSatelliteBatch(List<BdmSatellite> list);

	/**
	 * 根据日期查询星历数据
	 * @param dateStr yyyy-MM-dd
	 * @return
	 */
	List<BdmSatellite> findSatelliteByDay(String dateStr);

	/**
	 * 查询指定时间列表内的星历数据
	 * @param dateList 日期列表 [yyyyMMdd, yyyyMMdd, ...]
	 * @param num 星号
	 * @return
	 */
	List<BdmSatellite> findAllSatelliteInDateList(String num, List<String> dateList);

	/**
	 * 查询指定时间列表内的星历数据
	 * @param dateList 日期列表 [yyyyMMdd, yyyyMMdd, ...]
	 * @param num 星号
	 * @return
	 */
	List<BdmSatellite> findAllSatelliteInDateList( List<String> dateList);

	List<BdmSatellite> dataInfo();

	/**
	 * 查询指定天数内最新的星历数据
	 * @param beforeDays
	 * @return
	 */
	List<BdmSatellite> findNewestSatellite(int beforeDays) throws Exception;


}
