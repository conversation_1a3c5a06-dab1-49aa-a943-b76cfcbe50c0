package com.xh.vdm.bdCheck.mapper.bdCheck;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bdCheck.entity.bdCheck.BdcCompany;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-29
 */
@DS("bdCheck")
public interface BdcCompanyMapper extends BaseMapper<BdcCompany> {

	/**
	 * 分页查询委托企业
	 * @param page
	 * @param company
	 * @return
	 */
	IPage<BdcCompany> getCompanyPage(IPage<BdcCompany> page, @Param("company") BdcCompany company, @Param("deptIds") String deptIds);
}
