
package com.xh.vdm.interManager.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.interManager.constant.CommonConstant;
import com.xh.vdm.interManager.entity.SmInterfaceManage;
import com.xh.vdm.interManager.service.ISmInterfaceManageService;
import com.xh.vdm.interManager.utils.CommonUtil;
import com.xh.vdm.interManager.utils.R;
import com.xh.vdm.interManager.vo.SmInterfaceManageVO;
import lombok.AllArgsConstructor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

/**
 * 接口管理表 控制器
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@RestController
@AllArgsConstructor
@RequestMapping("/interManager/sminterfacemanage")
public class SmInterfaceManageController {

	private ISmInterfaceManageService smInterfaceManageService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<SmInterfaceManage> detail(SmInterfaceManage smInterfaceManage) {
		SmInterfaceManage detail = smInterfaceManageService.getOne(Wrappers.lambdaQuery(smInterfaceManage));
		return R.data(detail);
	}

	/**
	 * 分页 接口管理表
	 */
	@GetMapping("/list")
	public R<IPage<SmInterfaceManage>> list(SmInterfaceManage smInterfaceManage, Page<SmInterfaceManage> page) {
		IPage<SmInterfaceManage> pages = smInterfaceManageService.page(page, Wrappers.lambdaQuery(smInterfaceManage));
		return R.data(pages);
	}

	/**
	 * 自定义分页 接口管理表
	 */
	@GetMapping("/page")
	public R<IPage<SmInterfaceManageVO>> page(SmInterfaceManageVO smInterfaceManage, Page<SmInterfaceManageVO> page) {
		IPage<SmInterfaceManageVO> pages = smInterfaceManageService.selectSmInterfaceManagePage(page, smInterfaceManage);
		return R.data(pages);
	}

	/**
	 * 新增 接口管理表
	 */
	@PostMapping("/save")
	public R save( @RequestBody SmInterfaceManage smInterfaceManage) {
		return R.status(smInterfaceManageService.save(smInterfaceManage));
	}

	/**
	 * 修改 接口管理表
	 */
	@PostMapping("/update")
	public R update( @RequestBody SmInterfaceManage smInterfaceManage) {
		return R.status(smInterfaceManageService.updateById(smInterfaceManage));
	}

	/**
	 * 新增或修改 接口管理表
	 */
	@PostMapping("/submit")
	public R submit(@RequestBody SmInterfaceManage smInterfaceManage) {
		if(StringUtils.isEmpty(smInterfaceManage.getId())){
			//如果是新增
			smInterfaceManage.setState(CommonConstant.STATE_U);
			smInterfaceManage.setCreateDate(new Date());
		}else{
			smInterfaceManage.setUpdateDate(new Date());
		}
		return R.status(smInterfaceManageService.saveOrUpdate(smInterfaceManage));
	}


	/**
	 * 删除 接口管理表
	 */
	@PostMapping("/remove")
	public R remove(@RequestParam String ids) {
		return R.status(smInterfaceManageService.removeByIds(CommonUtil.toLongList(ids)));
	}


}
