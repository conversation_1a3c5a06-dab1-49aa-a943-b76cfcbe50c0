<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.InPosServiceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inPosServiceResultMap" type="com.xh.vdm.interManager.entity.InPosService">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="desc" property="desc"/>
        <result column="is_del" property="isDel"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <select id="selectInPosServicePage" resultMap="inPosServiceResultMap">
        select * from in_pos_service where is_deleted = 0
    </select>

</mapper>
