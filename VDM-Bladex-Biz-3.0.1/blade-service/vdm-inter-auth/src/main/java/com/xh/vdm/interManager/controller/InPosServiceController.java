/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.controller;

import cn.hutool.db.DaoTemplate;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.entity.InPosService;
import com.xh.vdm.interManager.vo.InPosServiceVO;
import com.xh.vdm.interManager.service.IInPosServiceService;

import java.util.Date;
import java.util.List;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("vdm-inter-manager/service")
@Api(value = "", tags = "接口")
public class InPosServiceController {

	private IInPosServiceService inPosServiceService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入inPosService")
	public R<InPosService> detail(InPosService inPosService) {
		InPosService detail = inPosServiceService.getOne(Condition.getQueryWrapper(inPosService));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入inPosService")
	public R<IPage<InPosService>> list(InPosService inPosService, Query query) {
		inPosService.setIsDel(0);
		IPage<InPosService> pages = inPosServiceService.page(Condition.getPage(query), Condition.getQueryWrapper(inPosService));
		return R.data(pages);
	}


	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入inPosService")
	public R save(@Valid @RequestBody InPosService inPosService) {
		inPosService.setIsDel(0);
		inPosService.setCreateTime(new Date());
		inPosService.setCreateUser(AuthUtil.getUserId());
		return R.status(inPosServiceService.save(inPosService));
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入inPosService")
	public R update(@Valid @RequestBody InPosService inPosService) {
		inPosService.setUpdateTime(new Date());
		inPosService.setUpdateUser(AuthUtil.getUserId());
		return R.status(inPosServiceService.updateById(inPosService));
	}


	/**
	 * 删除
	 * 逻辑删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		//逻辑删除
		List<Long> idList = Func.toLongList(ids);
		List<InPosService> list = inPosServiceService.listByIds(idList);
		list.forEach(item -> {
			item.setIsDel(1);
			item.setUpdateUser(AuthUtil.getUserId());
			item.setUpdateTime(new Date());
		});
		return R.status(inPosServiceService.updateBatchById(list));
	}


	/**
	 * 获取所有服务信息
	 * @return
	 */
	@GetMapping("/getAllService")
	public R getAllService(){
		try {
			List<InPosService> list = inPosServiceService.list(Wrappers.lambdaQuery(InPosService.class).eq(InPosService::getIsDel, 0));
			return R.data(list);
		}catch (Exception e){
			return R.fail("查询服务列表失败");
		}
	}


}
