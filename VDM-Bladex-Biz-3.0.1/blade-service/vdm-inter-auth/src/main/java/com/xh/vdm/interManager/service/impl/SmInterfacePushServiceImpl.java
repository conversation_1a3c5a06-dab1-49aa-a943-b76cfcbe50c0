package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.SmInterfacePush;
import com.xh.vdm.interManager.mapper.SmInterfacePushMapper;
import com.xh.vdm.interManager.service.ISmInterfacePushService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Service
public class SmInterfacePushServiceImpl extends ServiceImpl<SmInterfacePushMapper, SmInterfacePush> implements ISmInterfacePushService {

}
