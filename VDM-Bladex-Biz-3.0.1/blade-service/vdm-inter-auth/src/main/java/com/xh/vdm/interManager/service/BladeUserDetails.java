/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service;

import lombok.Getter;
import org.springblade.core.tool.support.Kv;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import java.util.Collection;

/**
 * 用户信息拓展
 *
 * <AUTHOR>
 */
@Getter
public class BladeUserDetails extends User {

	/**
	 * 用户id
	 */
	private final Long userId;
	/**
	 * 账号
	 */
	private final String account;
	/**
	 * 昵称
	 */
	private final String contactName;
	/**
	 * 真名
	 */
	private final String contactEmail;

	/**
	 * 部门id
	 */
	private final String contactPhone;
	/**
	 * 岗位id
	 */
	private final String alarmRuleId;
	/**
	 * 角色id
	 */
	private final Long systemId;
	/**
	 * 角色名
	 */
	private final String desc;

	public BladeUserDetails(Long userId, String contactName, String contactEmail, String contactPhone, String alarmRuleId, Long systemId, String desc, String username, String password, boolean enabled, boolean accountNonExpired, boolean credentialsNonExpired, boolean accountNonLocked, Collection<? extends GrantedAuthority> authorities) {
		super(username, password, enabled, accountNonExpired, credentialsNonExpired, accountNonLocked, authorities);
		this.userId = userId;
		this.account = username;
		this.contactName = contactName;
		this.contactEmail = contactEmail;
		this.contactPhone = contactPhone;
		this.alarmRuleId = alarmRuleId;
		this.systemId = systemId;
		this.desc = desc;
	}

}
