<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.SmInterfaceSystemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="smInterfaceSystemResultMap" type="com.xh.vdm.interManager.vo.SmInterfaceSystemVO">
        <id column="ID" property="id"/>
        <result column="SYSTEM_NAME" property="systemName"/>
        <result column="RES_DATA_KEY" property="resDataKey"/>
        <result column="RES_CODE_KEY" property="resCodeKey"/>
        <result column="SUCCESS_CODE_VALUE" property="successCodeValue"/>
        <result column="RES_MESSAGE_KEY" property="resMessageKey"/>
        <result column="CREATE_DATE" property="createDate"/>
        <result column="UPDATE_DATE" property="updateDate"/>
        <result column="STATE" property="state"/>
        <result column="NOTE" property="note"/>
        <result column="WEBSERVICE_AUTH_MODEL" property="webserviceAuthModel"/>
    </resultMap>


    <select id="selectSmInterfaceSystemPage" resultMap="smInterfaceSystemResultMap">
        select * from sm_interface_system where is_deleted = 0
    </select>

</mapper>
