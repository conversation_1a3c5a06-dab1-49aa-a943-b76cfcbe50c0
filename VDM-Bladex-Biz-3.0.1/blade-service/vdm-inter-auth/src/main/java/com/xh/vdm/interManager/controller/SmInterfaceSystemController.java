package com.xh.vdm.interManager.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.interManager.constant.CommonConstant;
import com.xh.vdm.interManager.entity.SmInterfaceSystem;
import com.xh.vdm.interManager.service.ISmInterfaceSystemService;
import com.xh.vdm.interManager.utils.CommonUtil;
import com.xh.vdm.interManager.utils.R;
import com.xh.vdm.interManager.vo.SmInterfaceSystemVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 接口管理系统表 控制器
 *
 * <AUTHOR>
 * @since 2021-09-10
 */
@RestController
@AllArgsConstructor
@RequestMapping("/interManager/sminterfacesystem")
@Slf4j
public class SmInterfaceSystemController {

	private ISmInterfaceSystemService smInterfaceSystemService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<SmInterfaceSystem> detail(SmInterfaceSystem smInterfaceSystem) {
		SmInterfaceSystem detail = smInterfaceSystemService.getOne(Wrappers.query(smInterfaceSystem));
		return R.data(detail);
	}

	/**
	 * 分页 接口管理系统表
	 */
	@GetMapping("/list")
	public R<IPage<SmInterfaceSystem>> list(SmInterfaceSystem smInterfaceSystem, Page<SmInterfaceSystem> page) {
		IPage<SmInterfaceSystem> pages = smInterfaceSystemService.page(page, Wrappers.lambdaQuery(smInterfaceSystem).eq(SmInterfaceSystem::getState, CommonConstant.STATE_U));
		return R.data(pages);
	}



	/**
	 * 查询系统列表
	 */
	@GetMapping("/list-system")
	public R<List<SmInterfaceSystem>> list() {
		List<SmInterfaceSystem> list = smInterfaceSystemService.list(Wrappers.<SmInterfaceSystem>query().lambda().eq(SmInterfaceSystem::getState , CommonConstant.STATE_U));
		return R.data(list);
	}


	/**
	 * 自定义分页 接口管理系统表
	 */
	@GetMapping("/page")
	public R<IPage<SmInterfaceSystemVO>> page(SmInterfaceSystemVO smInterfaceSystem, Page<SmInterfaceSystemVO> page) {
		IPage<SmInterfaceSystemVO> pages = smInterfaceSystemService.selectSmInterfaceSystemPage(page, smInterfaceSystem);
		return R.data(pages);
	}

	/**
	 * 新增 接口管理系统表
	 */
	@PostMapping("/save")
	public R save(@RequestBody SmInterfaceSystem smInterfaceSystem) {
		return R.status(smInterfaceSystemService.save(smInterfaceSystem));
	}

	/**
	 * 修改 接口管理系统表
	 */
	@PostMapping("/update")
	public R update( @RequestBody SmInterfaceSystem smInterfaceSystem) {
		return R.status(smInterfaceSystemService.updateById(smInterfaceSystem));
	}

	/**
	 * 新增或修改 接口管理系统表
	 */
	@PostMapping("/submit")
	public R submit( @RequestBody SmInterfaceSystem smInterfaceSystem) {
		return R.status(smInterfaceSystemService.saveOrUpdate(smInterfaceSystem));
	}


	/**
	 * 删除 接口管理系统表
	 */
	@PostMapping("/remove")
	public R remove( @RequestParam String ids) {
		//逻辑删除
		try {
			smInterfaceSystemService.update(Wrappers.lambdaUpdate(SmInterfaceSystem.class).in(SmInterfaceSystem::getId, CommonUtil.toLongList(ids)).set(SmInterfaceSystem::getState, CommonConstant.STATE_E));
		}catch (Exception e){
			log.error("删除系统数据出错",e);
			return R.fail("删除系统数据出错");
		}
		return R.success("操作成功");
		//return R.status(smInterfaceSystemService.removeByIds(CommonUtil.toLongList(ids)));
	}


}
