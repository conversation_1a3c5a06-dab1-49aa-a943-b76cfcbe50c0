<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.SmInterfaceParamMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="smInterfaceParamResultMap" type="com.xh.vdm.interManager.entity.SmInterfaceParam">
        <id column="id" property="id"/>
        <result column="interface_manage_id" property="interfaceManageId"/>
        <result column="param_type" property="paramType"/>
        <result column="param_key" property="paramKey"/>
        <result column="param_value" property="paramValue"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="state" property="state"/>
        <result column="note" property="note"/>
    </resultMap>


    <select id="selectSmInterfaceParamPage" resultMap="smInterfaceParamResultMap">
        select * from sm_interface_param where is_deleted = 0
    </select>

    <select id="getNewId" resultType="long" flushCache="true">
        select nextval('SEQ_sm_interface_param_ID')
    </select>

</mapper>
