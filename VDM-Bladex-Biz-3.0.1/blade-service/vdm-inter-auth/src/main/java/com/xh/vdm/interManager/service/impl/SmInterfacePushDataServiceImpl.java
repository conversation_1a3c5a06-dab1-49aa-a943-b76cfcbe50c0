package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.SmInterfacePushData;
import com.xh.vdm.interManager.mapper.SmInterfacePushDataMapper;
import com.xh.vdm.interManager.service.ISmInterfacePushDataService;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Service
public class SmInterfacePushDataServiceImpl extends ServiceImpl<SmInterfacePushDataMapper, SmInterfacePushData> implements ISmInterfacePushDataService {

}
