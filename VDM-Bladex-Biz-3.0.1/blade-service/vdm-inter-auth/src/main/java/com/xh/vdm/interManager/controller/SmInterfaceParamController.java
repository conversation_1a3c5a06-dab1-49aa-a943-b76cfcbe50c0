
package com.xh.vdm.interManager.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.interManager.constant.CommonConstant;
import com.xh.vdm.interManager.entity.SmInterfaceParam;
import com.xh.vdm.interManager.service.ISmInterfaceParamService;
import com.xh.vdm.interManager.utils.CommonUtil;
import com.xh.vdm.interManager.utils.R;
import com.xh.vdm.interManager.vo.*;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 接口参数表 控制器
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@RestController
@AllArgsConstructor
@RequestMapping("/interManager/sminterfaceparam")
public class SmInterfaceParamController {

	private ISmInterfaceParamService smInterfaceParamService;


	/**
	 * @Description 校验接口是否已经创建参数
	 * <AUTHOR>
	 * @Date 2020/1/19 17:27
	 * @Company CTTIC
	 **/
	@GetMapping("checkHasParam")
	public boolean checkHasParam(@RequestParam Long interfaceManageId){
		SmInterfaceParam param = new SmInterfaceParam();
		param.setInterfaceManageId(interfaceManageId);
		long count = smInterfaceParamService.count(Wrappers.lambdaQuery(param));
		if(count > 0){
			return true;
		}
		return false;
	}


	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<SmInterfaceParam> detail(SmInterfaceParam smInterfaceParam) {
		SmInterfaceParam detail = smInterfaceParamService.getOne(Wrappers.query(smInterfaceParam));
		return R.data(detail);
	}

	/**
	 * 分页 接口参数表
	 */
	@GetMapping("/list")
	public R<IPage<SmInterfaceParam>> list(SmInterfaceParam smInterfaceParam, Page<SmInterfaceParam> page) {
		smInterfaceParam.setState(CommonConstant.STATE_U);
		IPage<SmInterfaceParam> pages = smInterfaceParamService.page(page, Wrappers.query(smInterfaceParam));
		return R.data(pages);
	}

	/**
	 * 自定义分页 接口参数表
	 */
	@GetMapping("/page")
	public R<IPage<SmInterfaceParamVO>> page(SmInterfaceParamVO smInterfaceParam, Page<SmInterfaceParamVO> page) {
		IPage<SmInterfaceParamVO> pages = smInterfaceParamService.selectSmInterfaceParamPage(page, smInterfaceParam);
		return R.data(pages);
	}

	/**
	 * 新增 接口参数表
	 */
	@PostMapping("/save")
	public R save(@RequestBody SmInterfaceParam smInterfaceParam) {
		return R.status(smInterfaceParamService.save(smInterfaceParam));
	}

	/**
	 * 修改 接口参数表
	 */
	@PostMapping("/update")
	public R update(@RequestBody SmInterfaceParam smInterfaceParam) {
		return R.status(smInterfaceParamService.updateById(smInterfaceParam));
	}

	/**
	 * 新增或修改 接口参数表
	 */
	@PostMapping("/submit")
	public R submit(@RequestBody SmInterfaceParam smInterfaceParam) {
		return R.status(smInterfaceParamService.saveOrUpdate(smInterfaceParam));
	}


	/**
	 * 删除 接口参数表
	 */
	@PostMapping("/remove")
	public R remove(@RequestParam String ids) {
		return R.status(smInterfaceParamService.removeByIds(CommonUtil.toLongList(ids)));
	}


	@PostMapping("/submitForm")
	@Transactional
	public R submit(@RequestBody InterfaceParamWithModel model) {

		List<SmInterfaceParam> list = new ArrayList<>();

		String type = model.getType();
		switch (type){
			case "1":
				type = CommonConstant.INTER_TYPE_HTTP_URL;  //HTTP 业务接口
				break;
			case "2":
				type = CommonConstant.INTER_TYPE_WEBSERVICE_URL;  //WEBSERVICE 接口
				break;
			case "4":
				type = CommonConstant.INTER_FUNCTION_TYPE_TOKEN;  //HTTP token认证接口
				break;
		}

		if(CommonConstant.INTER_TYPE_HTTP_URL.equals(type)){
			//1.如果是 HTTP 转发接口
			//1.1 判断是否带有token
			String hasToken = model.getHasToken();
			if("Y".equals(hasToken)){
				//如果带有token
				//插入一条 token 的 key 以及位置 param
				SmInterfaceParam tokenParam = new SmInterfaceParam();
				tokenParam.setParamType(CommonConstant.PARAM_TYPE_USE_TOKEN_KEY);
				//tokenParam.setId(smInterfaceParamService.getNewId()+"");
				tokenParam.setInterfaceManageId(model.getInterfaceManageId());
				tokenParam.setParamKey(model.getTokenKey());
				tokenParam.setParamValue(model.getTokenStation().toUpperCase());
				tokenParam.setState(CommonConstant.STATE_U);
				tokenParam.setNote(CommonConstant.NOTE_INTERFACE_PARAM_HTTP_TOKEN);
				list.add(tokenParam);

				//插入一条 token 的 值 的前缀
				SmInterfaceParam tokenPrefixParam = new SmInterfaceParam();
				tokenPrefixParam.setParamType(CommonConstant.PARAM_TYPE_USE_TOKEN_PREFIX);
				//tokenPrefixParam.setId(smInterfaceParamService.getNewId()+"");
				tokenPrefixParam.setInterfaceManageId(model.getInterfaceManageId());
				tokenPrefixParam.setParamKey(model.getTokenPrefix());
				tokenPrefixParam.setParamValue(null);
				tokenPrefixParam.setState(CommonConstant.STATE_U);
				tokenPrefixParam.setNote(CommonConstant.NOTE_INTERFACE_PARAM_HTTP_TOKEN);
				list.add(tokenPrefixParam);

			}
			//1.2 判断header中是否带有参数
			String hasHeaderParam = model.getHasHeaderParam();
			if("Y".equals(hasHeaderParam)){
				//如果有header中的参数，则每个参数都要插入一条记录
				List<HeaderParamModel> headerList = model.getParamsInHeader();
				for(HeaderParamModel m : headerList){
					SmInterfaceParam param = new SmInterfaceParam();
					//param.setId(smInterfaceParamService.getNewId()+"");
					param.setInterfaceManageId(model.getInterfaceManageId());
					param.setParamType(CommonConstant.PARAM_TYEP_HEADER);
					param.setParamKey(m.getHeaderKey());
					param.setParamValue(m.getHeaderValue());
					param.setState(CommonConstant.STATE_U);
					param.setNote(CommonConstant.NOTE_INTERFACE_PARAM_HEADER);
					list.add(param);
				}
			}
			//1.3 判断body中是否带有参数
			String hasBodyParam = model.getHasBodyParam();
			if("Y".equals(hasBodyParam)){
				//如果有body中的参数，则每个参数都要插入一条记录
				List<BodyParamModel> bodyList = model.getParamsInBody();
				for(BodyParamModel m : bodyList){
					SmInterfaceParam param = new SmInterfaceParam();
					//param.setId(smInterfaceParamService.getNewId()+"");
					param.setInterfaceManageId(model.getInterfaceManageId());
					param.setParamType(CommonConstant.PARAM_TYPE_BODY);
					param.setParamKey(m.getBodyKey());
					param.setParamValue(m.getBodyValue());
					param.setState(CommonConstant.STATE_U);
					param.setNote(CommonConstant.NOTE_INTERFACE_PARAM_BODY);
					list.add(param);
				}
			}

			//1.4 判断url中是否带有参数
			String hasUrlParam = model.getHasUrlParam();
			if("Y".equals(hasUrlParam)){
				//如果有body中的参数，则每个参数都要插入一条记录
				List<UrlParamModel> paramList = model.getParamsInUrl();
				for(UrlParamModel m : paramList){
					SmInterfaceParam param = new SmInterfaceParam();
					//param.setId(smInterfaceParamService.getNewId()+"");
					param.setInterfaceManageId(model.getInterfaceManageId());
					param.setParamType(CommonConstant.PARAM_TYPE_URL);
					param.setParamKey(m.getUrlKey());
					param.setParamValue(m.getUrlValue());
					param.setState(CommonConstant.STATE_U);
					param.setNote(CommonConstant.NOTE_INTERFACE_PARAM_URL);
					list.add(param);
				}
			}
		}else if(CommonConstant.INTER_TYPE_WEBSERVICE_URL.equals(type)){
			//2.如果是 WEBSERVICE 转发接口
			//2.1 判断是否带有认证
			if("Y".equals(model.getHasAuth())){
				//如果带有认证，则需要添加用户名和密码设置，用户名和密码分别为一条参数配置
				//用户名参数配置
				SmInterfaceParam param = new SmInterfaceParam();
				//param.setId(smInterfaceParamService.getNewId()+"");
				param.setInterfaceManageId(model.getInterfaceManageId());
				param.setParamType(CommonConstant.PARAM_TYPE_WEBSERVICE_USERNAME);
				param.setParamKey("username");
				param.setParamValue(model.getUsername());
				param.setState(CommonConstant.STATE_U);
				param.setNote(CommonConstant.NOTE_INTERFACE_PARAM_WEBSERVICE_FORWARD_USERNAME);
				list.add(param);
				//密码参数配置
				SmInterfaceParam paramP = new SmInterfaceParam();
				//paramP.setId(smInterfaceParamService.getNewId()+"");
				paramP.setInterfaceManageId(model.getInterfaceManageId());
				paramP.setParamType(CommonConstant.PARAM_TYPE_WEBSERVICE_PASSWORD);
				paramP.setParamKey("password");
				paramP.setParamValue(model.getPassword());
				paramP.setState(CommonConstant.STATE_U);
				paramP.setNote(CommonConstant.NOTE_INTERFACE_PARAM_WEBSERVICE_FORWARD_PASSWORD);
				list.add(paramP);
			}
		}else if(CommonConstant.INTER_FUNCTION_TYPE_TOKEN.equals(type)){
			//4.如果是请求token接口
			//4.1 判断header中是否带有参数
			String hasHeaderParam = model.getHasHeaderParam();
			if("Y".equals(hasHeaderParam)){
				//如果有header中的参数，则每个参数都要插入一条记录
				List<HeaderParamModel> headerList = model.getParamsInHeader();
				for(HeaderParamModel m : headerList){
					SmInterfaceParam param = new SmInterfaceParam();
					//param.setId(smInterfaceParamService.getNewId()+"");
					param.setInterfaceManageId(model.getInterfaceManageId());
					param.setParamType(CommonConstant.PARAM_TYEP_HEADER);
					param.setParamKey(m.getHeaderKey());
					param.setParamValue(m.getHeaderValue());
					param.setState(CommonConstant.STATE_U);
					param.setNote(CommonConstant.NOTE_INTERFACE_PARAM_REQUEST_TOKEN_HEADER);
					list.add(param);
				}
			}
			//4.2 判断body中是否带有参数
			String hasBodyParam = model.getHasBodyParam();
			if("Y".equals(hasBodyParam)){
				//如果有body中的参数，则每个参数都要插入一条记录
				List<BodyParamModel> bodyList = model.getParamsInBody();
				for(BodyParamModel m : bodyList){
					SmInterfaceParam param = new SmInterfaceParam();
					//param.setId(smInterfaceParamService.getNewId()+"");
					param.setInterfaceManageId(model.getInterfaceManageId());
					param.setParamType(CommonConstant.PARAM_TYPE_BODY);
					param.setParamKey(m.getBodyKey());
					param.setParamValue(m.getBodyValue());
					param.setState(CommonConstant.STATE_U);
					param.setNote(CommonConstant.NOTE_INTERFACE_PARAM_REQUEST_TOKEN_BODY);
					list.add(param);
				}
			}

			//4.3 判断url中是否带有参数
			String hasUrlParam = model.getHasUrlParam();
			if("Y".equals(hasUrlParam)){
				//如果有body中的参数，则每个参数都要插入一条记录
				List<UrlParamModel> paramList = model.getParamsInUrl();
				for(UrlParamModel m : paramList){
					SmInterfaceParam param = new SmInterfaceParam();
					//param.setId(smInterfaceParamService.getNewId()+"");
					param.setInterfaceManageId(model.getInterfaceManageId());
					param.setParamType(CommonConstant.PARAM_TYPE_URL);
					param.setParamKey(m.getUrlKey());
					param.setParamValue(m.getUrlValue());
					param.setState(CommonConstant.STATE_U);
					param.setNote(CommonConstant.NOTE_INTERFACE_PARAM_URL);
					list.add(param);
				}
			}
			//4.4 配置返回的access_token的key
			SmInterfaceParam param = new SmInterfaceParam();
			//param.setId(smInterfaceParamService.getNewId()+"");
			param.setInterfaceManageId(model.getInterfaceManageId());
			param.setParamType(CommonConstant.PARAM_TYPE_TOKEN_KEY);
			param.setParamKey(model.getAccessTokenKey());
			param.setParamValue(model.getAccessTokenDuration());
			param.setState(CommonConstant.STATE_U);
			param.setNote(CommonConstant.NOTE_INTERFACE_PARAM_REQUEST_TOKEN_ACCESS_TOKEN_KEY);
			list.add(param);

			long a = smInterfaceParamService.getNewId();
			long b = smInterfaceParamService.getNewId();
			System.out.println("a = "+a+",b = "+b);
		}

		//按照模板创建请求参数，要将原来的参数置为无效
		SmInterfaceParam param = new SmInterfaceParam();
		param.setInterfaceManageId(model.getInterfaceManageId());
		param.setState(CommonConstant.STATE_U);
		List<SmInterfaceParam> listOri = smInterfaceParamService.list(Wrappers.query(param));
		for(SmInterfaceParam p : listOri){
			p.setState(CommonConstant.STATE_E);
		}
		smInterfaceParamService.saveOrUpdateBatch(listOri);


		//保存新的参数信息
		try {
			smInterfaceParamService.saveBatch(list);
		}catch (Exception e){
			e.printStackTrace();
			return R.fail("提交失败");
		}
		return R.success("提交成功");
	}



}
