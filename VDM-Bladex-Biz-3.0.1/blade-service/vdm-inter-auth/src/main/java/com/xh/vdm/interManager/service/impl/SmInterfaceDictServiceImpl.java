/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.SmInterfaceDict;
import com.xh.vdm.interManager.mapper.SmInterfaceDictMapper;
import com.xh.vdm.interManager.service.ISmInterfaceDictService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 业务字典表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-05-22
 */
@Service
public class SmInterfaceDictServiceImpl extends ServiceImpl<SmInterfaceDictMapper, SmInterfaceDict> implements ISmInterfaceDictService {

    @Override
    public List<SmInterfaceDict> getList(String code) {
        return baseMapper.getList(code);
    }
}
