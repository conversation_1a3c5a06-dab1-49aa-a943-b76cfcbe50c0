/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.interManager.entity.SmInterfaceParam;
import com.xh.vdm.interManager.mapper.SmInterfaceParamMapper;
import com.xh.vdm.interManager.service.ISmInterfaceParamService;
import com.xh.vdm.interManager.vo.SmInterfaceParamVO;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Random;

/**
 * 接口参数表 服务实现类
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
@Service
public class SmInterfaceParamServiceImpl extends ServiceImpl<SmInterfaceParamMapper, SmInterfaceParam> implements ISmInterfaceParamService {

	@Override
	public IPage<SmInterfaceParamVO> selectSmInterfaceParamPage(IPage<SmInterfaceParamVO> page, SmInterfaceParamVO smInterfaceParam) {
		return page.setRecords(baseMapper.selectSmInterfaceParamPage(page, smInterfaceParam));
	}

	@Override
	public long getNewId() {
		//不依赖数据库，使用当前系统时间加上随机数的形式生成id
		//long id = baseMapper.getNewId();
		long id = new Date().getTime() + (new Random()).nextInt(1000);
		return id;
	}

}
