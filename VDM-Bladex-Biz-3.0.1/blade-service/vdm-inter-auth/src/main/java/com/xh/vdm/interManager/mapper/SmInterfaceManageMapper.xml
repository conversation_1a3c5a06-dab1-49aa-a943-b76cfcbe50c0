<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.SmInterfaceManageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="smInterfaceManageResultMap" type="com.xh.vdm.interManager.entity.SmInterfaceManage">
        <id column="id" property="id"/>
        <result column="inter_code" property="interCode"/>
        <result column="inter_type" property="interType"/>
        <result column="request_type" property="requestType"/>
        <result column="inter_note" property="interNote"/>
        <result column="protocol" property="protocol"/>
        <result column="ip" property="ip"/>
        <result column="port" property="port"/>
        <result column="url" property="url"/>
        <result column="create_date" property="createDate"/>
        <result column="update_date" property="updateDate"/>
        <result column="token_inter_code" property="tokenInterCode"/>
        <result column="state" property="state"/>
        <result column="system_type" property="systemType"/>
        <result column="function_type" property="functionType"/>
        <result column="is_need_token" property="isNeedToken"/>
    </resultMap>


    <select id="selectSmInterfaceManagePage" resultMap="smInterfaceManageResultMap">
        select * from sm_interface_manage where is_deleted = 0
    </select>

    <select id="getNewId" resultType="long" flushCache="true">
        select nextval('SEQ_sm_interface_manage_ID')
    </select>

    <select id="getPublicKeyByAccount" parameterType="string" resultType="string">
        select iis.public_key from in_pos_user ipu, in_interface_system iis
        where ipu.system_id  = iis.id
          and ipu.account = #{account}
    </select>
</mapper>
