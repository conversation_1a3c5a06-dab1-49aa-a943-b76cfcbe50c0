<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.InPosInterfaceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inPosInterfaceResultMap" type="com.xh.vdm.interManager.entity.InPosInterface">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="desc" property="desc"/>
        <result column="url" property="url"/>
        <result column="service_id" property="serviceId"/>
        <result column="protocol_type" property="protocolType"/>
        <result column="request_type" property="requestType"/>
        <result column="is_del" property="isDel"/>
        <result column="state" property="state"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <select id="selectInPosInterfacePage" resultMap="inPosInterfaceResultMap">
        select * from in_pos_interface where is_deleted = 0
    </select>


    <select id="getInterfaceByUserId" resultType="com.xh.vdm.interManager.entity.InPosInterface">
        select b.*
        from in_pos_user_interface a, in_pos_interface b
        where a.interface_id = b.id and b.is_del = 0 and b.state = 'U'
          and a.user_id = #{userId}
    </select>
</mapper>
