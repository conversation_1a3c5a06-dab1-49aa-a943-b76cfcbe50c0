/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.interManager.cache.CacheNames;
import com.xh.vdm.interManager.constant.AuthConstant;
import com.xh.vdm.interManager.entity.InPosUser;
import com.xh.vdm.interManager.utils.TokenUtil;
import io.jsonwebtoken.Claims;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.jwt.JwtUtil;
import org.springblade.core.jwt.props.JwtProperties;
import org.springblade.core.redis.cache.BladeRedis;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.*;
import org.springblade.system.cache.ParamCache;
import org.springblade.system.entity.Tenant;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.user.entity.User;
import org.springblade.system.user.entity.UserInfo;
import org.springblade.system.user.enums.UserEnum;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class BladeUserDetailsServiceImpl implements UserDetailsService {

	public static final Integer FAIL_COUNT = 5;
	public static final String FAIL_COUNT_VALUE = "account.failCount";

	private final IUserClient userClient;
	private final ISysClient sysClient;

	private final BladeRedis bladeRedis;
	private final JwtProperties jwtProperties;

	private final IInPosUserService userService;

	@Override
	@SneakyThrows
	public BladeUserDetails loadUserByUsername(String username) {
		HttpServletRequest request = WebUtil.getRequest();
		String password = request.getParameter(TokenUtil.PASSWORD_KEY);
		String grantType = request.getParameter(TokenUtil.GRANT_TYPE_KEY);
		//查询用户信息
		InPosUser user = userService.getOne(Wrappers.lambdaQuery(InPosUser.class).eq(InPosUser::getAccount, username));

		//验证用户信息
		if(user == null){
			//如果用户不存在，则提示用户名密码错误
			throw new UsernameNotFoundException(TokenUtil.USER_NOT_FOUND);
		}
		if(grantType.equals(TokenUtil.PASSWORD_KEY) && !user.getPassword().equals(DigestUtil.hex(password))){
			//如果用户密码错误，则提示用户名密码错误
			throw new UsernameNotFoundException(TokenUtil.USER_NOT_FOUND);
		}
		if(StringUtils.isEmpty(user.getDeptId())){
			//如果没有指定部门，则提示未设置权限
			throw new RuntimeException(TokenUtil.DEPT_NOT_FOUND);
		}
		return new BladeUserDetails(user.getId(),
			user.getContactName(), user.getContactEmail(), user.getContactPhone(), user.getAlarmRuleId(), user.getSystemId(), user.getUserDesc(),
			username, AuthConstant.ENCRYPT + user.getPassword(), true, true, true, true,
			new ArrayList<>());

	}


	/**
	 * 校验refreshToken合法性
	 *
	 * @param grantType 认证类型
	 * @param request   请求
	 */
	private boolean judgeRefreshToken(String grantType, HttpServletRequest request) {
		if (jwtProperties.getState() && jwtProperties.getSingle() && StringUtil.equals(grantType, TokenUtil.REFRESH_TOKEN_KEY)) {
			String refreshToken = request.getParameter(TokenUtil.REFRESH_TOKEN_KEY);
			Claims claims = JwtUtil.parseJWT(refreshToken);
			String tenantId = String.valueOf(claims.get("tenant_id"));
			String userId = String.valueOf(claims.get("user_id"));
			String token = JwtUtil.getRefreshToken(tenantId, userId, refreshToken);
			return StringUtil.equalsIgnoreCase(token, refreshToken);
		}
		return true;
	}


}
