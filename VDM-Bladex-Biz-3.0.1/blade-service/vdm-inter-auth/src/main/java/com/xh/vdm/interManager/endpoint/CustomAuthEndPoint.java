package com.xh.vdm.interManager.endpoint;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.SM2;
import cn.hutool.crypto.symmetric.SM4;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.cloud.nacos.NacosServiceManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
import com.alibaba.nacos.client.naming.NacosNamingService;
import com.xh.vdm.interManager.entity.SmInterfaceSystem;
import com.xh.vdm.interManager.service.ISmInterfaceManageService;
import com.xh.vdm.interManager.service.ISmInterfaceSystemService;
import com.xh.vdm.interManager.vo.GovVehVO;
import com.xh.vdm.interManager.vo.RefreshTokenRequest;
import com.xh.vdm.interManager.vo.TokenRequest;
import com.xh.vdm.interManager.vo.PageTokenRequest;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Description: TODO
 * @Auther: zxing
 * @Date: 2021/9/18 19:43
 * @company：CTTIC
 */
@RestController
@RequestMapping("")
@Slf4j
public class CustomAuthEndPoint {

	@Value("${token.custom.protocol:http}")
	private String protocol;
	@Value("${token.custom.url:/blade-auth/oauth/token}")
	private String url;

	@Value("${token.custom.token-service_name:blade-auth}")
	private String tokenServiceName;

	@Value("${token.custom.grant_type:password}")
	private String grantType ;
	@Value("${token.custom.scope:all}")
	private String scope ;
	@Value("${token.custom.tenant_id:000000}")
	private String tenantId ;
	@Value("${token.custom.header_auth_type:Authorization}")
	private String headerAuthType;
	@Value("${token.custom.header_auth_value:Basic c3dvcmQ6c3dvcmRfc2VjcmV0}")
	private String headerAuthValue;
	@Value("${token.encrypt-ttl}")
	private Long encryptTTL;

	@Resource
	private NacosNamingService nacosNamingService;

	private static Instance instance;


	@Autowired
	private Environment env;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private ISmInterfaceSystemService systemService;

	@Resource
	private ISmInterfaceManageService manageService;


	@Autowired
	private NacosServiceManager nacosServiceManager;

	@Autowired
	private NacosDiscoveryProperties nacosDiscoveryProperties;

    /**
       * @Description 自定义认证接口
    	* <AUTHOR>
    	* @Date 2024/5/24 16:45
    	* @Company xh
    	* @Param []
    	* @return org.springblade.core.tool.api.R
    	**/
    @PostMapping("/celoc/gettoken")
    public Map<String,String> getToken(@RequestBody TokenRequest request){
		log.info("port is " + env.getProperty("server.port"));
		String port = env.getProperty("server.port");
    	String urlFinal = protocol+"://127.0.0.1"+":"+port+url;
		String username = request.getUsername();
		String password = request.getPassword();

		HttpResponse response = HttpRequest.post(urlFinal)
				.header(headerAuthType , headerAuthValue)
			.header("Tenant-Id",tenantId)
				.form("username" , username)
				.form("password" , password)
				.form("grant_type" , grantType)
				.form("scope" , scope)
				.form("tenantId" , tenantId)
				.execute();
		String res = response.body();
		JSONObject jsonO = JSONObject.parseObject(res);
		String accessToken = jsonO.getString("access_token");
		String refreshToken = jsonO.getString("refresh_token");
		//String tokenType = jsonO.getString("token_type");
		String expiresIn = jsonO.getString("expires_in");
		Map<String,String> map = new HashMap<>();
		if(!StringUtils.isEmpty(accessToken)){
			//如果请求token成功
			map.put("access_token",accessToken);
			map.put("refresh_token",refreshToken);
			//map.put("jsessionid","");
			//map.put("extendinfo","");
			map.put("expires_in",expiresIn);
			map.put("code" , "200");
		}else{
			map.put("code","400");
			map.put("text","用户名或密码错误");
		}
		return map;

    }


	@PostMapping("/celoc/refreshtoken")
	public Map<String,String> refreshToken(@RequestBody RefreshTokenRequest request){
		String port = env.getProperty("server.port");
		String urlFinal = protocol+"://127.0.0.1"+":"+port+url;
		HttpResponse response = HttpRequest.post(urlFinal)
			.header(headerAuthType , headerAuthValue)
			.header("Tenant-Id",tenantId)
			.form("grant_type" , "refresh_token")
			.form("scope" , scope)
			.form("tenantId" , tenantId)
			.form("refresh_token", request.getRefreshToken())
			.execute();
		String res = response.body();
		JSONObject jsonO = JSONObject.parseObject(res);
		String accessToken = jsonO.getString("access_token");
		String refreshToken = jsonO.getString("refresh_token");
		//String tokenType = jsonO.getString("token_type");
		String expiresIn = jsonO.getString("expires_in");
		Map<String,String> map = new HashMap<>();
		if(!StringUtils.isEmpty(accessToken)){
			//如果请求token成功
			map.put("access_token",accessToken);
			map.put("refresh_token",refreshToken);
			map.put("expires_in",expiresIn);
			map.put("code" , "200");
		}else{
			map.put("code","400");
			map.put("text","刷新token失败");
		}
		return map;

	}

	/**
	 * 页面对接，获取token
	 * 采用平台协议中，业务报文加密的流程，对用户的 md5(密码) + 时间戳进行了加密。加密的结果中，包含签名sign，加密了的SM4密钥code，以及加密的报文content
	 * 要先进行解密，然后再调用token接口
	 * @param request
	 * @return
	 */
	@PostMapping("/celoc/getPageToken")
	public Map<String,Object> getPageToken(@RequestBody PageTokenRequest request){
		Map<String,Object> map = new HashMap<>();
		try{
			//1.根据用户名查询公钥
			String thirdPublicKey = manageService.findPublicKeyByAccount(request.getUsername());

			//1.校验在有效期内该加密串是否已经使用过
			//如果已经使用过，则不允许再使用
			//加密的数据的SM3值，因为加密数据可能比较长，这里进行压缩
			String encData = request.getContent();
			String dataKey = SmUtil.sm3(encData);
			String encDataStr = stringRedisTemplate.opsForValue().get(CommonConstant.REDIS_KEY_PREFIX_ENCRYPT_KEY+dataKey);
			if(encDataStr != null){
				//如果加密串还未过期，并且已经使用过，则不允许再次使用
				map.put("code","400");
				map.put("text","加密信息失效");
				return map;
			}

			//4.如果加密串有效，则写入到redis中。在ttl的时效内，不允许再次使用
			stringRedisTemplate.opsForValue().set(CommonConstant.REDIS_KEY_PREFIX_ENCRYPT_KEY+dataKey,encData);
			stringRedisTemplate.expire(CommonConstant.REDIS_KEY_PREFIX_ENCRYPT_KEY+dataKey, encryptTTL, TimeUnit.SECONDS);



			String encryptedBody = request.getContent();
			//1.接收方收到报文后，对报文体中的数据信息密文计算信息摘要（SM3算法），用
			//发送方的公钥验证发送方数字签名(SM2算法)
			//1.3 验证签名
			String sign = request.getSign();
			byte[] crcNow = SmUtil.sm3().digest(encryptedBody);
			log.info("here crc is "+ Base64.encode(crcNow));
			log.info("【公务车】请求页面token，公钥为：" + thirdPublicKey);
			SM2 thirdSm2 = SmUtil.sm2(null, thirdPublicKey);
			boolean signResult = thirdSm2.verify(crcNow, Base64.decode(sign));
			if(!signResult){
				log.error("验签不通过，Message={}",JSON.toJSONString(request));
				map.put("code","400");
				map.put("text","验签不通过");
				return map;
			}
			//2.接收方用自己的私钥，解密报文头中的加密密钥串(SM2算法)，得到本次通信的SM4密钥，然后用SM4密钥对数据信息密文进行解密（SM4算法），得出数据信息明文
			//2.1 获取私钥
			String privateKey = "";
			Object sm2PrivateKeyO = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_KEY_PRIVATE);
			SmInterfaceSystem systemO = null;
			if(sm2PrivateKeyO == null || com.alibaba.druid.util.StringUtils.isEmpty(sm2PrivateKeyO.toString())){
				//如果从缓存中没有获取私钥，则查询数据库
				systemO = systemService.getById(CommonConstant.DEFAULT_SYSTEM_ID);
				if(com.alibaba.druid.util.StringUtils.isEmpty(systemO.getPrivateKey())){
					//如果未配置私钥
					log.info("位置平台未配置私钥信息");
					map.put("code","400");
					map.put("text","位置平台未配置私钥信息");
					return map;
				}
				privateKey = systemO.getPrivateKey();
				//添加到缓存中
				stringRedisTemplate.opsForValue().set(CommonConstant.CACHE_KEY_PRIVATE, privateKey);
				stringRedisTemplate.expire(CommonConstant.CACHE_KEY_PRIVATE, CommonConstant.CACHE_KEY_TTL, TimeUnit.SECONDS);
			}else{
				privateKey = new String(sm2PrivateKeyO.toString().getBytes());
			}

			//2.2 解密sm4密钥
			String encryptedCode = request.getCode();
			SM2 sm2 = SmUtil.sm2(privateKey, null);
			String sm4Key = new String(sm2.decrypt(Base64.decode(encryptedCode)));

			//2.3 解密报文
			SM4 sm4 = SmUtil.sm4(Base64.decode(sm4Key));
			byte[] bytes = sm4.decrypt(Base64.decode(request.getContent()));
			//类型转换
			String json = new String(bytes);
			GovVehVO vo = JSON.parseObject(json, GovVehVO.class);

			//3.判断数据是否过期：查看时间戳距离现在的时间差，如果大于 ttl ，则认为过期
			if(vo.getTimestamp() + encryptTTL * 1000 < new Date().getTime()){
				//如果已经过期
				throw new Exception("加密串失效");
			}

			//获取blade-auth服务的地址，因为是访问页面，所以应该使用blade的页面token
			log.info("port is " + env.getProperty("server.port"));
			String urlFinal = protocol+"://"+getServiceInstance()+url;
			String username = request.getUsername();
			String password = vo.getPassword();
			//3.请求token
			HttpResponse response = HttpRequest.post(urlFinal)
				.header(headerAuthType , headerAuthValue)
				.header("Tenant-Id",tenantId)
				.form("username" , username)
				.form("password" , password)
				.form("grant_type" , grantType)
				.form("scope" , scope)
				.form("tenantId" , tenantId)
				.execute();
			String res = response.body();
			log.info("请求本地token结果为：" + res);
			JSONObject jsonO = JSONObject.parseObject(res);
			String accessToken = jsonO.getString("access_token");
			if(!StringUtils.isEmpty(accessToken)){
				//如果请求token成功
				map = BeanUtil.beanToMap(jsonO);
			}else{
				map.put("code","400");
				map.put("text","用户名或密码错误");
			}
		}catch (Exception e){
			log.error("页面对接，请求token失败",e);
			map.put("code","400");
			map.put("text","加密信息无效");
		}
		return map;
	}


	/**
	 * 通过加密串解密密码
	 * @param encData
	 * @return
	 * @throws Exception
	 */
	private GovVehVO decryptPassword(String encData) throws Exception{
		//1.获取密钥
		String privateKey = "";
		Object sm2PrivateKeyO = stringRedisTemplate.opsForValue().get(CommonConstant.CACHE_KEY_PRIVATE);
		SmInterfaceSystem systemO = null;
		if(sm2PrivateKeyO == null || com.alibaba.druid.util.StringUtils.isEmpty(sm2PrivateKeyO.toString())){
			//如果从缓存中没有获取私钥，则查询数据库
			systemO = systemService.getById(CommonConstant.DEFAULT_SYSTEM_ID);
			if(com.alibaba.druid.util.StringUtils.isEmpty(systemO.getPrivateKey())){
				//如果未配置私钥
				log.info("位置平台未配置私钥信息");
				throw new Exception("位置平台未配置私钥信息");
			}
			privateKey = systemO.getPrivateKey();
			//添加到缓存中
			stringRedisTemplate.expire(CommonConstant.CACHE_KEY_PRIVATE, CommonConstant.CACHE_KEY_TTL, TimeUnit.SECONDS);
		}else{
			privateKey = new String(sm2PrivateKeyO.toString().getBytes());
		}
		//2.解密数据
		SM2 sm2 = SmUtil.sm2(privateKey, null);
		String jsonStr = new String(sm2.decrypt(Base64.decode(encData)));
		GovVehVO vo = JSON.parseObject(jsonStr, GovVehVO.class);
		//3.判断数据是否过期：查看时间戳距离现在的时间差，如果大于 ttl ，则认为过期
		if(vo.getTimestamp() + encryptTTL * 1000 < new Date().getTime()){
			//如果已经过期
			throw new Exception("加密串失效");
		}
		//4.如果加密串有效，则写入到redis中。在ttl的时效内，不允许再次使用
		stringRedisTemplate.opsForValue().set(CommonConstant.REDIS_KEY_PREFIX_ENCRYPT_KEY+encData,jsonStr);
		stringRedisTemplate.expire(CommonConstant.REDIS_KEY_PREFIX_ENCRYPT_KEY+encData, encryptTTL, TimeUnit.SECONDS);
		return vo;
	}

	/**
	 * 从nacos上获取服务实例ip和端口
	 * @return
	 */
	private String getServiceInstance(){

		//NamingService namingService = nacosServiceManager.getNamingService(nacosDiscoveryProperties.getNacosProperties());
		//Instance instance = null;
		if(instance == null){
			try {
				instance = nacosNamingService.selectOneHealthyInstance(tokenServiceName, nacosDiscoveryProperties.getGroup());
			} catch (NacosException e) {
				e.printStackTrace();
			}
		}
		return instance.getIp() + ":" + instance.getPort();
	}



}
