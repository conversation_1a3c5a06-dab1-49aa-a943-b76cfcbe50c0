/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service;

import com.xh.vdm.interManager.entity.InPosUserInterface;
import com.xh.vdm.interManager.vo.InPosUserInterfaceVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
public interface IInPosUserInterfaceService extends IService<InPosUserInterface> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inPosUserInterface
	 * @return
	 */
	IPage<InPosUserInterfaceVO> selectInPosUserInterfacePage(IPage<InPosUserInterfaceVO> page, InPosUserInterfaceVO inPosUserInterface);

}
