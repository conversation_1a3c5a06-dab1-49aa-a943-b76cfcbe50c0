/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.config;

import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.oauth2.config.annotation.web.configuration.ResourceServerConfigurerAdapter;

/**
 * 自定义资源放行
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@AllArgsConstructor
public class BladeResourceServerConfiguration extends ResourceServerConfigurerAdapter {

	@Override
	@SneakyThrows
	public void configure(HttpSecurity http) {
		http.authorizeRequests()
			.antMatchers(
				"/actuator/**",
				//"/oauth/captcha",
				"/auth/getToken/**",
				//"/oauth/logout",
				//"/oauth/clear-cache",
				//"/oauth/render/**",
				//"/oauth/callback/**",
				//"/oauth/revoke/**",
				//"/oauth/refresh/**",
				//"/oauth/login",
				//"/oauth/form",
				//"/token/**",
				//"/mobile/**",
				//"/static/**",
				"/gnToken",
				"/v2/api-docs").permitAll()
			.anyRequest().authenticated().and()
			.csrf().disable();
	}

}
