/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.interManager.entity.InPosUserInterface;
import com.xh.vdm.interManager.service.IInPosUserInterfaceService;
import com.xh.vdm.interManager.vo.InPosUserInterfaceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.DateUtil;
import org.springblade.core.tool.utils.DigestUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.user.entity.User;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.entity.InPosUser;
import com.xh.vdm.interManager.vo.InPosUserVO;
import com.xh.vdm.interManager.service.IInPosUserService;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 账号管理
 * 位置平台给外部平台开通账号
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("/user")
@Slf4j
public class InPosUserController {

	private IInPosUserService inPosUserService;

	private IInPosUserInterfaceService userInterfaceService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入inPosUser")
	public R<InPosUser> detail(InPosUser inPosUser) {
		InPosUser detail = inPosUserService.getOne(Condition.getQueryWrapper(inPosUser));
		detail.setIsDel(0);
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入inPosUser")
	public R<IPage<InPosUser>> list(InPosUser inPosUser, Query query) {
		inPosUser.setIsDel(0);
		IPage<InPosUser> pages = inPosUserService.page(Condition.getPage(query), Condition.getQueryWrapper(inPosUser));
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入inPosUser")
	public R save(@Valid @RequestBody InPosUser inPosUser) {

		//判断数据库中是否已经存在该account
		List<InPosUser> users = inPosUserService.list(Wrappers.lambdaQuery(InPosUser.class)
			.eq(InPosUser::getAccount,inPosUser.getAccount())
			.eq(InPosUser::getIsDel, 0));
		if(users.size() > 0){
			//如果系统中已经存在该account
			log.error("账户[{}]已经存在",inPosUser.getAccount());
			return R.fail("保存账户信息失败，账户"+inPosUser.getAccount()+"已经存在");
		}

		inPosUser.setCreateUser(AuthUtil.getUserId());
		inPosUser.setCreateTime(new Date());
		inPosUser.setIsDel(0);
		inPosUser.setStatus(CommonConstant.STATE_U);
		//设置密码
		String password = inPosUser.getPassword();
		if(StringUtils.isEmpty(password)){
			return R.fail("密码不能为空");
		}

		inPosUser.setPassword(DigestUtil.hex(password));
		try {
			boolean flag = inPosUserService.save(inPosUser);
			if(flag){
				return R.success("操作成功");
			}
			return R.fail("保存账户信息失败");
		}catch (Exception e){
			log.error("保存账户信息失败",e);
			return R.fail("保存账户信息失败，"+e.getMessage());
		}
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入inPosUser")
	public R update(@Valid @RequestBody InPosUser inPosUser) {
		inPosUser.setUpdateUser(AuthUtil.getUserId());
		inPosUser.setUpdateTime(new Date());
		return R.status(inPosUserService.updateById(inPosUser));
	}


	/**
	 * 删除
	 * 逻辑删除
	 */
	@GetMapping("/remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<InPosUser> list = inPosUserService.listByIds(Func.toLongList(ids));
		list.forEach(item -> {
			item.setUpdateTime(new Date());
			item.setUpdateUser(AuthUtil.getUserId());
			item.setIsDel(1);
		});
		return R.status(inPosUserService.updateBatchById(list));
	}


	/**
	 * 重置密码
	 * @param userIds
	 * @return
	 */
	@GetMapping("/resetPassword")
	public R resetPassword(String userIds){
		InPosUser user = new InPosUser();
		user.setPassword(DigestUtil.encrypt(CommonConstant.DEFAULT_PASSWORD));
		user.setUpdateTime(DateUtil.now());
		user.setUpdateUser(AuthUtil.getUserId());
		return R.status(inPosUserService.update(user, Wrappers.<InPosUser>update().lambda().in(InPosUser::getId, Func.toLongList(userIds))));
	}

	/**
	 * 冻结账号
	 * @param userIds
	 * @return
	 */
	@GetMapping("/freeze")
	public R freeze(String userIds){
		InPosUser user = new InPosUser();
		user.setStatus(CommonConstant.STATE_E);
		user.setUpdateTime(DateUtil.now());
		user.setUpdateUser(AuthUtil.getUserId());
		return R.status(inPosUserService.update(user, Wrappers.<InPosUser>update().lambda().in(InPosUser::getId, Func.toLongList(userIds))));
	}


	/**
	 * 解冻账号
	 * @param userIds
	 * @return
	 */
	@GetMapping("/unfreeze")
	public R unfreeze(String userIds){
		InPosUser user = new InPosUser();
		user.setStatus(CommonConstant.STATE_U);
		user.setUpdateTime(DateUtil.now());
		user.setUpdateUser(AuthUtil.getUserId());
		return R.status(inPosUserService.update(user, Wrappers.<InPosUser>update().lambda().in(InPosUser::getId, Func.toLongList(userIds))));
	}

	/**
	 * 接口授权
	 * @param interfaces
	 * @param userIds
	 * @return
	 */
	@GetMapping("/interAuth")
	public R interAuth(String interfaces, String userIds){
		try{
			//1.删除之前的关联关系
			boolean removeSuccess = userInterfaceService.remove(Wrappers.lambdaQuery(InPosUserInterface.class).in(InPosUserInterface::getUserId, Func.toLongList(userIds)));
			if(!removeSuccess){
				log.error("删除接口权限数据失败");
				return R.fail("授权失败");
			}
			//2.创建新的关联关系
			List<Long> interList = Func.toLongList(interfaces);
			List<Long> userList = Func.toLongList(userIds);
			if(interList == null || userList == null || interList.size() < 1 || userList.size() < 1){
				return R.fail("未包含用户数据或接口数据");
			}
			List<InPosUserInterface> list = new ArrayList<>();
			Date createTime = new Date();
			for(Long userId : userList){
				for(Long interId : interList){
					InPosUserInterface pi = new InPosUserInterfaceVO();
					pi.setUserId(userId);
					pi.setInterfaceId(interId);
					pi.setCreateTime(createTime);
					pi.setCreateUser(AuthUtil.getUserId());
					list.add(pi);
				}
			}
			userInterfaceService.saveBatch(list);
			return R.success("操作成功");
		}catch (Exception e){
			log.error("授权失败",e);
			return R.fail("授权失败, "+e.getMessage());
		}
	}

	/**
	 * 绑定部门
	 * 用于设置数据权限
	 * @param userIds
	 * @param deptIds
	 * @return
	 */
	@GetMapping("/bindDept")
	public R bindDept(String userIds, String deptIds){
		//直接更新用户表中的 deptId 字段
		InPosUser user = new InPosUser();
		user.setDeptId(deptIds);
		try {
			return R.status(inPosUserService.update(user, Wrappers.lambdaUpdate(InPosUser.class).in(InPosUser::getId, Func.toLongList(userIds))));
		}catch (Exception e){
			log.error("绑定部门失败", e);
			return R.fail("绑定失败, "+e.getMessage());
		}
	}


}
