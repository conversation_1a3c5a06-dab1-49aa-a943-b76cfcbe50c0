package com.xh.vdm.interManager.constant;

import org.springblade.core.launch.constant.AppConstant;

/**
 * 通用常量
 *
 * <AUTHOR>
 */
public interface CommonConstant {

	/**
	 * app name
	 */
	String APPLICATION_NAME = AppConstant.APPLICATION_NAME_PREFIX + "api";

	/**
	 * sword 系统名
	 */
	String SWORD_NAME = "sword";

	/**
	 * saber 系统名
	 */
	String SABER_NAME = "saber";

	/**
	 * 顶级父节点id
	 */
	Long TOP_PARENT_ID = 0L;

	/**
	 * 顶级父节点名称
	 */
	String TOP_PARENT_NAME = "顶级";

	/**
	 * 未封存状态值
	 */
	Integer NOT_SEALED_ID = 0;

	/**
	 * 默认密码
	 */
	String DEFAULT_PASSWORD = "123456";

	/**
	 * 默认密码参数值
	 */
	String DEFAULT_PARAM_PASSWORD = "account.initPassword";

	/**
	 * 默认排序字段
	 */
	String SORT_FIELD = "sort";

	/**
	 * 数据权限类型
	 */
	Integer DATA_SCOPE_CATEGORY = 1;

	/**
	 * 接口权限类型
	 */
	Integer API_SCOPE_CATEGORY = 2;



	//**************************接口管理中的接口类型******************************
	//REST接口
	String INTER_TYPE_HTTP_URL = "HTTP_URL";
	//webservice接口
	String INTER_TYPE_WEBSERVICE_URL = "WEBSERVICE_URL";


	//***************************接口功能***************************************
	String INTER_FUNCTION_TYPE_TOKEN = "INTER_TOKEN";  //token认证
	String INTER_FUNCTION_TYPE_BUSI = "INTER_BUSI"; //业务接口


	//************************接口管理中的请求参数类型**********************************
	String PARAM_TYEP_HEADER = "1"; //请求头 header
	String PARAM_TYPE_BODY = "2";  //请求体 body

	String PARAM_TYPE_URL = "30";  //请求url
	String PARAM_TYPE_TOKEN_KEY = "3";  //发送请求token接口时，返回报文中access_token的key名称
	String PARAM_TYPE_WEBSERVICE_USERNAME = "11";
	String PARAM_TYPE_WEBSERVICE_PASSWORD = "12";
	String PARAM_TYPE_WEBSERVICE_METHOD_NAME = "14";
	String PARAM_TYPE_WEBSERVICE_MESSAGE_CLASS = "15"; //webservice服务端的报文对象类型(服务端接口命名空间+.+Message)
	String PARAM_TYPE_USE_TOKEN_KEY = "21"; //在调用接口时使用token，用于设置token的key
	String PARAM_TYPE_USE_TOKEN_PREFIX = "22"; //在调用接口时使用token，用于设置token值的前缀，如bladex中会带有前缀 bearer xxx

	//************************webservice接口 header 中的认证模板********************************************************
	String WEBSERVICE_AUTH_MODEL_DEFAULT = "<SecurityHeader><username>#username#</username><password>#password#</password></SecurityHeader>";

	//*************************接口管理中的备注***************************************
	String NOTE_INTERFACE_PARAM_HTTP_TOKEN = "HTTP接口--token配置";
	String NOTE_INTERFACE_PARAM_HEADER = "HTTP接口--header中的参数配置";
	String NOTE_INTERFACE_PARAM_BODY = "HTTP接口--body中的参数配置";

	String NOTE_INTERFACE_PARAM_URL = "HTTP接口--url中的参数配置";
	String NOTE_INTERFACE_PARAM_WEBSERVICE_FORWARD_USERNAME = "WEBSERVICE接口--认证--用户名配置";
	String NOTE_INTERFACE_PARAM_WEBSERVICE_FORWARD_PASSWORD = "WEBSERVICE接口--认证--密码配置";
	String NOTE_INTERFACE_PARAM_REQUEST_TOKEN_HEADER = "请求token--header中的参数配置";
	String NOTE_INTERFACE_PARAM_REQUEST_TOKEN_BODY = "请求token--body中的参数配置";
	String NOTE_INTERFACE_PARAM_REQUEST_TOKEN_ACCESS_TOKEN_KEY = "请求token--返回报文中access_token key的参数配置";

	String STATE_U = "U";  //生效状态
	String STATE_E = "E"; //删除状态
	String STATE_EXP = "EXP"; //过期失效状态

	//*****************************请求类型*********************************************
	String REQUEST_TYPE_GET = "GET";
	String REQUEST_TYPE_POST = "POST";



	//*******************************是否需要token******************************
	String NEED_TOKEN = "Y";
	String NOT_NEED_TOKEN = "N";


	//*******************************调用接口状态*****************************
	String SUCCESS = "SUCCESS";
	String FAIL = "FAIL";


	//*******************************token前缀***********************************
	String TOKEN_PRIFIX = "token::";

	//*******************************inter_collect重试标志前缀*******************
	String REPEAT = "repeat::";

	//******************************接口管理常用提示语*********************
	String INTER_MESSAGE_ERROR = "调用 接口 失败";


	//******************************业务******************
	String TARGET_MODEL_FORMULA = "1";  //指标模式：公式
	String TARGET_MODEL_HAVE = "2";  //指标模式：有无

	//*****************************字典********************************
	//三能中观指标code
	String DICT_BIZ_CODE_TARGET_EFFECT_MIDDLE = "target_effect";




	//****************项目流程节点**************
	String PROCESS_START = "0";  				//流程开启
	String PROCESS_COMPANY = "1";				//企业填报
	String PROCESS_PROFESSOR = "2";				//专家评审
	String PROCESS_ASSESS = "3";				//项目评估
	String PROCESS_AUDIT_YES = "4";				//审核通过
	String PROCESS_AUDIT_NO = "5";				//审核不通过
	String PROCESS_COMPLETE = "9";				//归档


}
