package com.xh.vdm.interManager.feign;

import com.xh.vdm.interManager.entity.InPosInterface;
import com.xh.vdm.interManager.entity.InPosUserInterface;
import com.xh.vdm.interManager.service.IInPosInterfaceService;
import com.xh.vdm.interManager.util.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
public class UserClient implements IUserClient{

	@Resource
	private IInPosInterfaceService interfaceService;

	@Override
	public R<List<InPosInterface>> getUserInterfaceAuth(Long userId) {
		try{
			List<InPosInterface> list = interfaceService.findInterfaceListByUserId(userId);
			return R.data(list);
		}catch (Exception e){
			log.error("[feign]根据userId查询接口列表失败", e);
			return R.fail("查询接口权限失败");
		}
	}
}
