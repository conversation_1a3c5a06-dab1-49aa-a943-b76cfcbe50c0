/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.service;

import com.xh.vdm.interManager.entity.InPosInterface;
import com.xh.vdm.interManager.vo.InPosInterfaceVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
public interface IInPosInterfaceService extends IService<InPosInterface> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inPosInterface
	 * @return
	 */
	IPage<InPosInterfaceVO> selectInPosInterfacePage(IPage<InPosInterfaceVO> page, InPosInterfaceVO inPosInterface);

	/**
	 * 根据用户id查询用户接口权限
 	 * @param userId
	 * @return
	 * @throws Exception
	 */
	List<InPosInterface> findInterfaceListByUserId(Long userId) throws Exception;

}
