/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.entity.SmInterfaceManage;
import com.xh.vdm.interManager.vo.SmInterfaceManageVO;

import java.util.List;

/**
 * 接口管理表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2020-06-05
 */
public interface SmInterfaceManageMapper extends BaseMapper<SmInterfaceManage> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param smInterfaceManage
	 * @return
	 */
	List<SmInterfaceManageVO> selectSmInterfaceManagePage(IPage page, SmInterfaceManageVO smInterfaceManage);

	long getNewId();


	/**
	 * 根据账户查询公钥信息
	 * @param account
	 * @return
	 */
	String getPublicKeyByAccount(String account);
}
