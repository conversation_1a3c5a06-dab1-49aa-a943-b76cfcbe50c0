package com.xh.vdm.interManager.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.SM2;
import com.xh.vdm.interManager.service.ISmInterfaceManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class TestController {

    private ISmInterfaceManageService smInterfaceManageService;

	/*public static void main(String[] args) {
		//String encryptedCode = "BBOOM/v8xNIQWCIe0SpSYMM6/kOK2PgjWIsZF9HKWre7pJFMrDy5QopWjRozaVrFlaFhwGHP/e93AKYcHm8rZSrBckF/VTsusrBeZGSi9wLRDaXCwmFsE9wIhNfZeJeNcX7g3i+wqbslxDjMFw5tYZb3zdc6lgPiyg==";
		SM2 sm2 = SmUtil.sm2("MIGTAgEAMBMGByqGSM49AgEGCCqBHM9VAYItBHkwdwIBAQQg6sOlY0YMVWo5nlnDAyKdeeljBdeOP/UNcNToVSWRnbOgCgYIKoEcz1UBgi2hRANCAASyz5trx/Q528r1We0XP25dKqTDGdZWt9JSgvSEe0NrThnGFN/M1yj4X98XiIg9BVK0HTLtaXhLIBdaXttU3VbC", "MFkwEwYHKoZIzj0CAQYIKoEcz1UBgi0DQgAEss+ba8f0OdvK9VntFz9uXSqkwxnWVrfSUoL0hHtDa04ZxhTfzNco+F/fF4iIPQVStB0y7Wl4SyAXWl7bVN1Wwg==");
		//String sm4Key = new String(sm2.decrypt(Base64.decode(encryptedCode)));
		//log.info("sm4Key is " + sm4Key);

		String a = "123";
		byte[] encrypted = sm2.encrypt(a.getBytes());
		String aStr = new String(sm2.decrypt(encrypted));
		log.info("aStr is " + aStr);
	}*/
}
