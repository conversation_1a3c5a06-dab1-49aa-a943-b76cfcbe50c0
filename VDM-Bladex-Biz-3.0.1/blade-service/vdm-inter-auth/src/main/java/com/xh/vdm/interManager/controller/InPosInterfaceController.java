/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.interManager.entity.InPosInterface;
import com.xh.vdm.interManager.vo.InPosInterfaceVO;
import com.xh.vdm.interManager.service.IInPosInterfaceService;

import java.util.Date;
import java.util.List;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2024-05-26
 */
@RestController
@AllArgsConstructor
@RequestMapping("vdm-inter-manager/interface")
@Api(value = "", tags = "接口")
public class InPosInterfaceController {

	private IInPosInterfaceService inPosInterfaceService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入inPosInterface")
	public R<InPosInterface> detail(InPosInterface inPosInterface) {
		InPosInterface detail = inPosInterfaceService.getOne(Condition.getQueryWrapper(inPosInterface));
		return R.data(detail);
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入inPosInterface")
	public R<IPage<InPosInterface>> list(InPosInterface inPosInterface, Query query) {
		inPosInterface.setIsDel(0);
		IPage<InPosInterface> pages = inPosInterfaceService.page(Condition.getPage(query), Condition.getQueryWrapper(inPosInterface));
		return R.data(pages);
	}


	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入inPosInterface")
	public R save(@Valid @RequestBody InPosInterface inPosInterface) {
		inPosInterface.setCreateTime(new Date());
		inPosInterface.setCreateUser(AuthUtil.getUserId());
		inPosInterface.setIsDel(0);
		return R.status(inPosInterfaceService.save(inPosInterface));
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入inPosInterface")
	public R update(@Valid @RequestBody InPosInterface inPosInterface) {
		inPosInterface.setUpdateTime(new Date());
		inPosInterface.setUpdateUser(AuthUtil.getUserId());
		return R.status(inPosInterfaceService.updateById(inPosInterface));
	}


	/**
	 * 删除
	 * 逻辑删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		List<InPosInterface> list = inPosInterfaceService.listByIds(Func.toLongList(ids));
		list.forEach(item -> {
			item.setUpdateUser(AuthUtil.getUserId());
			item.setUpdateTime(new Date());
			item.setIsDel(1);
		});
		return R.status(inPosInterfaceService.updateBatchById(list));
	}


}
