<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.SmInterfaceDictMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="smInterfaceDictResultMap" type="com.xh.vdm.interManager.entity.SmInterfaceDict">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="code" property="code"/>
        <result column="dict_key" property="dictKey"/>
        <result column="dict_value" property="dictValue"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="is_sealed" property="isSealed"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <select id="getList" resultMap="smInterfaceDictResultMap">
        select code, dict_key, dict_value, sort, remark from sm_interface_dict where code = #{param1} and is_sealed = 0 and is_deleted = 0
    </select>
</mapper>
