<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.interManager.mapper.InPosUserInterfaceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="inPosUserInterfaceResultMap" type="com.xh.vdm.interManager.entity.InPosUserInterface">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="interface_id" property="interfaceId"/>
        <result column="is_del" property="isDel"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
    </resultMap>


    <select id="selectInPosUserInterfacePage" resultMap="inPosUserInterfaceResultMap">
        select * from in_pos_user_interface where is_deleted = 0
    </select>

</mapper>
