/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.xh.vdm.interManager;


import org.mybatis.spring.annotation.MapperScan;
import org.springblade.common.constant.ApplicationConstant;
import org.springblade.core.cloud.client.BladeCloudApplication;
import org.springblade.core.launch.BladeApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 用户认证服务器
 *
 * <AUTHOR>
 */
@BladeCloudApplication
@EnableFeignClients({"org.springblade","com.xh.vdm"})
@MapperScan("com.xh.vdm.interManager.mapper")
@EnableScheduling
public class InterAuthApplication {

	public static void main(String[] args) {
		String suffix = "";
		if(args != null && args.length > 0){
			for(String s : args){
				if(s.contains("suffix")){
					String param = s.split("=")[1].trim();
					suffix = "-" + param;
				}
			}
		}
		BladeApplication.run(ApplicationConstant.APPLICATION_INTER_AUTH + suffix, InterAuthApplication.class, args);
	}
}

