package com.xh.vdm.interManager.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.interManager.entity.SmInterfacePushData;
import com.xh.vdm.interManager.service.ISmInterfacePushDataService;
import com.xh.vdm.interManager.utils.CommonUtil;
import com.xh.vdm.interManager.utils.R;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@RestController
@RequestMapping("/interManager/sminterfacepushdata")
public class SmInterfacePushDataController {

	@Resource
	private ISmInterfacePushDataService SmInterfacePushDataDataService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	public R<SmInterfacePushData> detail(SmInterfacePushData SmInterfacePushData) {
		SmInterfacePushData detail = SmInterfacePushDataDataService.getOne(Wrappers.lambdaQuery(SmInterfacePushData));
		return R.data(detail);
	}

	/**
	 * 分页 接口管理表
	 */
	@GetMapping("/list")
	public R<IPage<SmInterfacePushData>> list(SmInterfacePushData SmInterfacePushData, Page<SmInterfacePushData> page) {
		IPage<SmInterfacePushData> pages = SmInterfacePushDataDataService.page(page, Wrappers.lambdaQuery(SmInterfacePushData));
		return R.data(pages);
	}


	/**
	 * 新增 接口管理表
	 */
	@PostMapping("/save")
	public R save( @RequestBody SmInterfacePushData SmInterfacePushData) {
		return R.status(SmInterfacePushDataDataService.save(SmInterfacePushData));
	}

	/**
	 * 修改 接口管理表
	 */
	@PostMapping("/update")
	public R update( @RequestBody SmInterfacePushData SmInterfacePushData) {
		return R.status(SmInterfacePushDataDataService.updateById(SmInterfacePushData));
	}

	/**
	 * 新增或修改 接口管理表
	 */
	@PostMapping("/submit")
	public R submit(@RequestBody SmInterfacePushData SmInterfacePushData) {
		SmInterfacePushData.setCreateTime(new Date());
		return R.status(SmInterfacePushDataDataService.saveOrUpdate(SmInterfacePushData));
	}


	/**
	 * 删除 接口管理表
	 */
	@PostMapping("/remove")
	public R remove(@RequestParam String ids) {
		return R.status(SmInterfacePushDataDataService.removeByIds(CommonUtil.toLongList(ids)));
	}

}
