package com.xh.vdm.interManager.core;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.interManager.constant.CommonConstant;
import com.xh.vdm.interManager.entity.SmInterfaceManage;
import com.xh.vdm.interManager.entity.SmInterfaceParam;
import com.xh.vdm.interManager.entity.SmInterfaceSystem;
import com.xh.vdm.interManager.interceptor.LoginInterceptor;
import com.xh.vdm.interManager.service.ISmInterfaceManageService;
import com.xh.vdm.interManager.service.ISmInterfaceParamService;
import com.xh.vdm.interManager.service.ISmInterfaceSystemService;
import com.xh.vdm.interManager.utils.CommonUtil;
import com.xh.vdm.interManager.utils.OKHttpClientUtil;
import com.xh.vdm.interManager.utils.R;
import com.xh.vdm.interManager.utils.ResultCode;
import com.xh.vdm.interManager.vo.InterfaceDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.endpoint.Endpoint;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.apache.cxf.service.model.BindingInfo;
import org.apache.cxf.service.model.BindingOperationInfo;
import org.apache.cxf.transport.http.HTTPConduit;
import org.apache.cxf.transports.http.configuration.HTTPClientPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.xml.namespace.QName;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: TODO
 * @Auther: zhouxw
 * @Date: 2019/10/13 21:09
 * @company：CTTIC
 */
@Slf4j
@Component
public class InterManager {

	@Resource
	private ISmInterfaceManageService service;
	@Resource
	private ISmInterfaceParamService paramService;
	@Resource
	private ISmInterfaceSystemService systemService;
	@Autowired
	private StringRedisTemplate redisUtil;

	public static final long COMMON_TOKEN_EXPIRE = 3600;
	public static final String TOKEN_POSITION_HEADER = "HEADER";
	public static final String TOKEN_POSITION_BODY = "BODY";
	public static final String TOKEN_POSITION_URL = "URL";
	public static final String DATA_POSITION_HEADER = "HEADER";
	public static final String DATA_POSITION_BODY = "BODY";

	private ThreadLocal<Boolean> repeatFlag = ThreadLocal.withInitial(() -> false);

	/**
	   * @Description 执行接口调用
		* <AUTHOR>
		* @Date 2021/9/10 14:35
		* @Company CTTIC
		* @Param [code, param]
		* @return org.springblade.core.tool.api.R<java.lang.Object>
		**/
	public R<Object> call(String code , Map<String, Object> param ){
		//1.根据系统id查询报文配置信息
		//1.1 查询接口所属系统id
		SmInterfaceManage manage = service.getOne(Wrappers.<SmInterfaceManage>query().lambda().eq(SmInterfaceManage::getInterCode, code).eq(SmInterfaceManage::getState , CommonConstant.STATE_U));
		String systemId = manage.getSystemType();
		if(StringUtils.isEmpty(systemId)){
			log.error("未配置系统类型");
			return R.fail("未配置系统类型");
		}
		//1.2 获取系统报文配置
		SmInterfaceSystem system = systemService.getOne(Wrappers.<SmInterfaceSystem>query().lambda().eq(SmInterfaceSystem::getId , systemId).eq(SmInterfaceSystem::getState,CommonConstant.STATE_U));
		if(system == null){
			log.error("配置的系统类型[systemId]有误");
			return R.fail("配置的系统类型有误");
		}
		String resDataKey = system.getResDataKey();
		String resCodeKey = system.getResCodeKey();
		String resSuccessValue = "200";
		resSuccessValue = StringUtils.isEmpty(system.getSuccessCodeValue())?"200":system.getSuccessCodeValue();
		resSuccessValue = resSuccessValue.lastIndexOf(",") == resSuccessValue.length() -1 ? resSuccessValue.substring(0 , resSuccessValue.length() -1) : resSuccessValue;
		String[] resSuccessValues = resSuccessValue.split(",");
		String resMessageKey = system.getResMessageKey();
		//webservice header 认证模板
		String webserviceAuthModel = system.getWebserviceAuthModel();

		//2 调用接口
		return call(code , param , resDataKey , resCodeKey , resSuccessValues , resMessageKey , webserviceAuthModel);
	}

	/*public static void main(String[] args) {
		String resSuccessValue = "";
		resSuccessValue = StringUtil.isEmpty(resSuccessValue)?"200":resSuccessValue;
		resSuccessValue = resSuccessValue.lastIndexOf(",") == resSuccessValue.length() -1 ? resSuccessValue.substring(0 , resSuccessValue.length() -1):resSuccessValue;
		System.out.println(resSuccessValue);
	}*/


	/**
	 * @Description 接口综合调用，用于自动包装返回参数
	 * <AUTHOR>
	 * @Date 2020/7/11 16:02
	 * @Company CTTIC
	 * @Param [code：接口 code , param：调用参数, resDataKey：返回参数中 data对应的key名称(如下面的例子中，resDataKey 为 data)]
	 * @return org.springblade.core.tool.api.R<java.lang.Object>
	 **/
	/**
	 * 样例：
	 * {
	 *     "code": 200,
	 *     "success": true,
	 *     "data": {
	 *
	 *     },
	 *     "msg": "操作成功"
	 * }
	 **/
	private R<Object> call(String code, Map<String, Object> param, String resDataKey, String resCodeKey ,String[] resSuccessValues ,String resMessageKey , String webserviceAuthModel) {
		R res = R.fail(CommonConstant.INTER_MESSAGE_ERROR);

		log.info("请求参数为"+param);

		try{
			InterfaceDTO dto = new InterfaceDTO();
			//如果map中的key为null，则value直接为查询参数
			try{
				if(param.get(null) != null){
					dto.setParam(param.get(null));
				}else{
					dto.setParam(param);
				}
			}catch (Exception e){
				dto.setParam(param);
			}
			dto.setInterCode(code);
			dto.setResCodeKey(resCodeKey);
			dto.setSuccessCodeValue(resSuccessValues);
			dto.setResMessageKey(resMessageKey);
			dto.setWebserviceAuthModel(webserviceAuthModel);
			dto.setResDataKey(resDataKey);
			res = getInterData(dto);
			//调用接口接口：仅表示功能侧通没通，不表示业务侧是否正确
			if(res.isSuccess()){
				return res;
				//if(res.getData() != null){
					//如果调用接口成功，并且返回数据不是空
					//解析 res，获取data；res一般为标准接口返回格式{code:xxx,message:'',data:'',status:xxx}
					//JSONObject jsonO = JSONObject.parseObject(res.getData()==null?"":res.getData().toString());
					//获取调用接口的结果，以状态码为准，200表示成功，其他表示异常
					//if(resCodeKey != null && jsonO.get(resCodeKey) == null){
					/*if(resCodeKey != null && CommonUtil.getValueFromJsonHier(jsonO , resCodeKey) == null){
						return R.fail("调用接口失败：底层接口返回数据中不包含状态码key="+resCodeKey);
					}else{
						if(resCodeKey == null){
							//如果没有指定 resCodeKey ，则表示调用成功（200）
							if (StringUtils.isEmpty(resDataKey)) {
								//如果 resDataKey 为空，表示返回内容即为业务报文，则返回整个报文
								return R.data(jsonO);
							} else {
								//如果 resDataKey 不为空，则只返回业务数据部分
								//return R.data(jsonO.get(resDataKey));
								return R.data(CommonUtil.getValueFromJsonHier(jsonO , resDataKey));
							}
						}else {
							//String interResCode = jsonO.get(resCodeKey).toString();
							String interResCode = CommonUtil.getValueFromJsonHier(jsonO,resCodeKey).toString();
							if (CommonUtil.containItem(resSuccessValues , interResCode)) {
								//如果调用接口成功
								if (StringUtils.isEmpty(resDataKey)) {
									//如果 resDataKey 为空，表示返回内容即为业务报文，则返回整个报文
									return R.data(jsonO);
								} else {
									//如果 resDataKey 不为空，则只返回业务数据部分
									//return R.data(jsonO.get(resDataKey));
									return R.data(CommonUtil.getValueFromJsonHier(jsonO , resDataKey));
								}
							} else {
								//如果调用接口异常
								//获取异常描述
								String interResMsg = jsonO.get(resMessageKey) == null ? "调用服务失败" : jsonO.get(resMessageKey).toString();
								return R.fail(interResMsg);
							}
						}
					}*/
				/*}else{
					return R.fail("调用接口失败：未接收到返回数据");
				}*/
			}else{
				//调用接口失败
				log.error("调用接口失败[code="+code+"]失败："+res.getMsg());
				return R.fail(res.getMsg());
			}
		}catch (Exception e){
			log.error("调用调用接口失败[code="+code+"]失败："+e.getMessage());
			e.printStackTrace();
			return R.fail(res.getMsg());
		}
	}


	/**
	 * @return org.springblade.core.tool.api.R<java.lang.String>
	 * @Description 执行报文转发
	 *
	 * 说明：因为webservice进行数据交互的时候，是通过java对象RPC的方式来进行的，那么转发时调用别人接口的时候，也要传递给别人java对象。因为队列中现在存放的时json报文，可以直接转换成java对象，所以，无需拼接或者转换成xml报文的形式
	 *
	 * <AUTHOR>
	 * @Date 2019/10/13 21:10
	 * @Company CTTIC
	 * @Param [username, data]
	 **/
	private R getInterData(InterfaceDTO interfaceDTO) {

		log.info("-->>进入到getInterData方法");

		//获取当前线程id
		long currentThreadId = Thread.currentThread().getId();
		if (interfaceDTO == null) {
			return R.fail("["+currentThreadId+"]执行转发失败：获取到的转发对象为null");
		}

		String interCode = interfaceDTO.getInterCode();
		if(interCode != null){
			interCode = interCode.toUpperCase();
		}
		Object params = null;
		params = interfaceDTO.getParam(); //此处可以进行强制类型转换

		//****************1.判断转发的类型，是restful接口，还是webservice接口********************************************************************
		log.info("-->>["+currentThreadId+"]查询是否配置webservice接口");
		SmInterfaceManage smInterfaceManageWebservice = new SmInterfaceManage();
		smInterfaceManageWebservice.setInterCode(interCode);
		smInterfaceManageWebservice.setState(CommonConstant.STATE_U);
		smInterfaceManageWebservice.setInterType(CommonConstant.INTER_TYPE_WEBSERVICE_URL);
		List<SmInterfaceManage> detailWebserviceList = null;
		try {
			detailWebserviceList = service.list(Wrappers.query(smInterfaceManageWebservice));
		} catch (Exception e) {
			e.printStackTrace();
			log.error("-->>["+currentThreadId+"]InterfaceClient--getData:[从sm_interface_manage表中查询webservice接口信息失败]，" + e.getStackTrace());
		}
		if (detailWebserviceList != null && detailWebserviceList.size() > 0 ) {
			log.info("-->>["+currentThreadId+"]已配置webservice接口，将按照webservice方式请求接口");

			SmInterfaceManage detailWebservice = detailWebserviceList.get(0);
		//****************2.如果是webservice接口，执行xml报文的转发*********************************************************
			return doWebservice(interfaceDTO,detailWebservice);
		}


		//****************3.如果是restful接口，采用 HTTP 方式调用*********************************************************************
		/*
		 * 说明：
		 * 1.在 interType = CommonConstant.INTER_TYPE_FORWARD_URL 的时候，对于param，如果paramType = CommonConstant.PARAM_TYPE_TOKEN_KEY
		 * 则 paramKey 表示的是发送转发请求时token值对应的key，paramValue 表示的是token要设置的位置 tokenPosition，在 header 还是 在body中。
		 * 2.如果没有设置 tokenPosition ，即 paramValue 为空，那么默认会添加到 header中。
		 **/
		log.info("-->>["+currentThreadId+"]没有配置webservice接口，将按照http restful 方式进行请求");
		log.info("["+currentThreadId+"]请求参数为：" + params);
		//data = JSON.parseObject(data).toJSONString();
		//1.根据用户名获取接口信息
		SmInterfaceManage smInterfaceManage = new SmInterfaceManage();
		smInterfaceManage.setInterCode(interCode);
		smInterfaceManage.setState(CommonConstant.STATE_U);
		smInterfaceManage.setInterType(CommonConstant.INTER_TYPE_HTTP_URL);
		SmInterfaceManage detail = null;
		try {
			List<SmInterfaceManage> list = service.list(Wrappers.query(smInterfaceManage));
			detail = service.getOne(Wrappers.query(smInterfaceManage));
		} catch (Exception e) {
			e.printStackTrace();
			log.error("-->>["+currentThreadId+"]InterfaceClient--getData:[从sm_interface_manage表中查询接口信息失败]，" + e.getStackTrace());
		}
		if (detail == null) {
			log.error("["+currentThreadId+"]调用接口["+interCode+"]失败，没有配置该接口的HTTP请求配置信息");
			repeatFlag.set(false);
			return R.fail("调用["+interCode+"]接口失败，没有配置该接口的HTTP请求配置信息");
		}
		log.info("-->>查询到接口配置");
		String protocol = detail.getProtocol();
		String ip = detail.getIp();
		String port = detail.getPort();
		String url = detail.getUrl();
		String isNeedToken = detail.getIsNeedToken();
		String tokenStr = null;
		String requestType = detail.getRequestType();
		String tokenInterCode = detail.getTokenInterCode();
		if("Y".equals(isNeedToken) && StringUtils.isEmpty(tokenInterCode)){
			log.error("调用接口["+interCode+"]需要token，但是未配置token请求接口");
			repeatFlag.set(false);
			return R.fail("接口需要token，但是未配置token请求接口");
		}

		//2.判断是否需要token认证
		if (CommonConstant.NEED_TOKEN.equals(isNeedToken)) {

			log.info("将要获取token");
			//如果需要token验证
			Object tokenO = redisUtil.opsForValue().get(CommonConstant.TOKEN_PRIFIX + tokenInterCode);
			String token = tokenO==null?"":tokenO.toString();
			if (token == null || StringUtils.isEmpty(token)) {
				//如果未查询到token，则查询数据库
				String resT = getToken(tokenInterCode);
				tokenStr = resT;
			}else{
				tokenStr = tokenO==null?"did not get token":tokenO.toString().trim();
			}
		} else {
			log.info("请求不需要token");
			tokenStr = "";
		}

		log.info("-->>将要获取请求参数");

		//3.获取请求参数
		//3.1 获取参数
		Long interfaceId = detail.getId();
		SmInterfaceParam interfaceParam = new SmInterfaceParam();
		interfaceParam.setInterfaceManageId(interfaceId);
		interfaceParam.setState(CommonConstant.STATE_U);
		List<SmInterfaceParam> paramList = paramService.list(Wrappers.query(interfaceParam));


		//3.2 拼接参数
		//token的参数key和token的位置
		String tokenKey = "";
		String tokenPosition = "";
		Map<String, String> header = new HashMap<String, String>();
		Map<String, Object> body = new HashMap<String, Object>();
		Map<String,String> urlParamMap = new HashMap<>();
		String tokenPrefix = "";
		log.info("将要拼接请求参数");
		for (SmInterfaceParam param : paramList) {
			//判断是否是使用token，设置token的key 和位置
			if (CommonConstant.NEED_TOKEN.equals(isNeedToken) && CommonConstant.PARAM_TYPE_USE_TOKEN_KEY.equals(param.getParamType())) {
				//tokenKey表示在使用token时的key，tokenPosition表示token的位置(如果为空，则表示在header中)
				tokenKey = param.getParamKey();
				tokenPosition = param.getParamValue();
			}else if(CommonConstant.NEED_TOKEN.equals(isNeedToken) && CommonConstant.PARAM_TYPE_USE_TOKEN_PREFIX.equals(param.getParamType())){
				//设置token的前缀
				tokenPrefix = param.getParamKey();
			} else {
				//如果不是token_key，则进行header和body的参数组装(预置参数)
				if (CommonConstant.PARAM_TYEP_HEADER.equals(param.getParamType())) {
					//如果是header参数
					header.put(param.getParamKey(), param.getParamValue());
				} else if (CommonConstant.PARAM_TYPE_BODY.equals(param.getParamType())) {
					//如果是body参数
					body.put(param.getParamKey(), param.getParamValue());
				}else if (CommonConstant.PARAM_TYPE_URL.equals(param.getParamType())){
					//如果是url参数
					urlParamMap.put(param.getParamKey(), param.getParamValue());
				}

			}
		}
		if(urlParamMap.size() > 0){
			//如果有url参数，则拼接参数
			if(!url.contains("?")){
				//如果url中还不包含参数
				url = url + "?";
			}
			for(String key : urlParamMap.keySet()){
				url = url + "&" + key + "=" + urlParamMap.get(key);
			}
		}
		//3.3 如果需要token，则拼接token到header或者body中
		if (CommonConstant.NEED_TOKEN.equals(isNeedToken)) {
			log.info("将要拼接token");
			if (!StringUtils.isEmpty(tokenPosition) && TOKEN_POSITION_BODY.equals(tokenPosition)) {
				//如果token需要添加到 body中
				body.put(tokenKey, tokenPrefix.trim() + " " + tokenStr);
			} else if(!StringUtils.isEmpty(tokenPosition) && TOKEN_POSITION_URL.equals(tokenPosition)){
				//如果token需要添加到 url 中
				//注意：如果token添加到url中，那么tokenKey应该包含分隔符，如 http://192.168.11.61:8881/detecting-api/restapi/detecting/get_data;token=0EJ3jxMvCaD9XPewu5ao85kcsDDsA0mY
				if(url.contains("?")){
					//如果url中已经有参数了
					url = url + "&" + tokenKey + "=" + tokenStr;
				}else{
					url = url + "?" + tokenKey + "=" + tokenStr;
				}
			}else {
				//如果token需要添加到header中或者没有配置position
				header.put(tokenKey, tokenPrefix.trim() + " " + tokenStr);
			}
		}

		//4.通过okHttp进行接口调用
		//4.1 拼接请求路径
		String finalUrl = protocol + "://" + ip + ":" + port + url;
		log.info("请求路径为："+finalUrl);
		//4.2 执行发送
		String res = "";
		try {

			log.info("将要发送请求");
			if("GET".equals(requestType)){
				log.info("将要发送get请求");
				//如果是GET请求
				res = OKHttpClientUtil.get(finalUrl , header , (Map<String,Object>)params);
				log.info("get请求完成");
			}else{
				//如果是POST请求
				log.info("将要发送post请求");
				String s = null;
				if(params instanceof Map){
					body.putAll((Map<String,Object>)params);
					s = JSONObject.toJSONString(body,SerializerFeature.WriteMapNullValue,	SerializerFeature.WriteNullListAsEmpty , SerializerFeature.WriteNullStringAsEmpty);
				}else{
					if(params instanceof List){
						s = JSONArray.toJSONString(params , SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullListAsEmpty ,SerializerFeature.WriteNullStringAsEmpty);
					}else{
						s = JSONObject.toJSONString(params,SerializerFeature.WriteMapNullValue,	SerializerFeature.WriteNullListAsEmpty,SerializerFeature.WriteNullStringAsEmpty);
					}
				}

				res = OKHttpClientUtil.postJson(finalUrl, header, s);
				log.info("post请求完成");
			}

			log.info("将要验证返回结果");
			log.info("返回 res 是" + res);
			//判断返回数据中是否包含 401，如果包含，则为请求其他系统服务失败
			JSONObject jsonO = JSONObject.parseObject(res);
			//String resCode = jsonO.get(interfaceDTO.getResCodeKey())+"";
			String resCode = CommonUtil.getValueFromJsonHier(jsonO , interfaceDTO.getResCodeKey())==null?"":CommonUtil.getValueFromJsonHier(jsonO , interfaceDTO.getResCodeKey()).toString();
			log.info("请求返回码是："+resCode);
			boolean isMatch = false;
			if("401".equals(resCode) || "403".equals(resCode)){
				isMatch = true;
			}
			//String pattern = ".*"+interfaceDTO.getResCodeKey()+"\\s*:\\s*401\\W+.*";
			//boolean isMatch = Pattern.matches(pattern, res);

			log.info("is 401 ? "+isMatch);
			if(StringUtils.isEmpty(res) || isMatch){
				log.info("请求返回401 或者 返回数据为空，将要重新获取token，然后重新发送一次请求");
				//如果是token过期，应当删除对应的缓存,并且重新发送请求
				redisUtil.delete(CommonConstant.TOKEN_PRIFIX + tokenInterCode);
				try{
					//如果获取token失败，则重新获取一次
					log.info("repeatFlag is "+repeatFlag);
					if(!repeatFlag.get()){
						repeatFlag.set(true);
						R rr =  getInterData(interfaceDTO);
						repeatFlag.set(false);
						return rr;
					}
				}catch (Exception e){
					log.error("因401重新请求底层接口失败");
					e.printStackTrace();
				}
				repeatFlag.set(false);
				return R.fail(401,"调用内部接口失败：未连接或未授权，请重试");
			}


			/*redisUtil.del(CommonConstant.REPEAT+tokenInterCode);*/
			//此处不进行数据的解析，只作为底层服务，进行数据传输
			repeatFlag.set(false);

			//如果执行成功：返回码在成功码列表中
			if(CommonUtil.containItem(interfaceDTO.getSuccessCodeValue() , resCode)){
				return R.data(res);
			}else{
				return R.data(ResultCode.FAILURE.getCode() , res , CommonUtil.getValueFromJsonHier(JSON.parseObject(res) , interfaceDTO.getResMessageKey())==null?"":CommonUtil.getValueFromJsonHier(JSON.parseObject(res) , interfaceDTO.getResMessageKey()).toString());
			}


		} catch (Exception e) {
			e.printStackTrace();
			repeatFlag.set(false);
			return R.fail("调用接口失败");
		}
	}


	/**
	 * @return org.springblade.core.tool.api.R<java.lang.String>
	 * @Description 获取token
	 * <AUTHOR>
	 * @Date 2019/10/15 15:07
	 * @Company CTTIC
	 * @Param [username, password]
	 **/
	private String getToken(String interCode) {

		long currentThreadId = Thread.currentThread().getId();

		/*
		 * 说明：
		 * 1.在 interType = CommonConstant.INTER_TYPE_GET_TOKEN_URL 的时候，如果配置 paramType = CommonConstant.PARAM_TYPE_TOKEN_KEY ，则其中paramKey表示返回的json串中access_key对应的key，
		 * paramValue表示token的有效期
		 * 2.如果没有配置paramType = CommonConstant.PARAM_TYPE_TOKEN_KEY 的参数，则表示返回的串就是token字符串
		 **/


		//1.查询获取token需要的参数，包含header中的和body中的
		SmInterfaceManage smInterfaceManage = new SmInterfaceManage();
		smInterfaceManage.setInterCode(interCode);
		smInterfaceManage.setState(CommonConstant.STATE_U);
		smInterfaceManage.setInterType(CommonConstant.INTER_TYPE_HTTP_URL);
		smInterfaceManage.setFunctionType(CommonConstant.INTER_FUNCTION_TYPE_TOKEN);
		SmInterfaceManage detail = null;
		try {
			detail = service.getOne(Wrappers.query(smInterfaceManage));
		} catch (Exception e) {
			e.printStackTrace();
			log.error("-->>["+currentThreadId+"]InterfaceClient--getToken:[从sm_interface_manage表获取请求token基础配置失败]" + e.getStackTrace());
			return null;
		}
		Long interfaceId = detail.getId();
		String url = detail.getUrl();
		SmInterfaceParam interfaceParam = new SmInterfaceParam();
		interfaceParam.setInterfaceManageId(interfaceId);
		interfaceParam.setState(CommonConstant.STATE_U);
		List<SmInterfaceParam> paramList = paramService.list(Wrappers.query(interfaceParam));
		//2.拼接请求参数
		Map<String, String> header = new HashMap<String, String>();
		Map<String, Object> body = new HashMap<String, Object>();
		String tokenKey = "";
		String tokenExpire = COMMON_TOKEN_EXPIRE + "";
		for (SmInterfaceParam param : paramList) {
			if (CommonConstant.PARAM_TYEP_HEADER.equals(param.getParamType())) {
				//拼接header
				header.put(param.getParamKey(), param.getParamValue());
			} else if (CommonConstant.PARAM_TYPE_BODY.equals(param.getParamType())) {
				//拼接body
				String paramValue = StringUtils.isEmpty(param.getParamValue())?"":param.getParamValue().replaceAll("&lt;","<")
					.replaceAll("&gt;",">").replaceAll("&amp;","&")
					.replaceAll("&nbsp;"," ").replaceAll("&quot;","\"");
				body.put(param.getParamKey(), paramValue);
			}else if(CommonConstant.PARAM_TYPE_URL.equals(param.getParamType())){
				//拼接url中的参数
				if(!url.contains("?")){
					//如果url中还没有拼接参数
					url = url + "?";
				}
				url = url + "&" + param.getParamKey() + "=" + param.getParamValue();
			}
			else if (CommonConstant.PARAM_TYPE_TOKEN_KEY.equals(param.getParamType())) {
				//token返回的json字符串中，表示access_token的key，如果没有设置该值，或者该值为空，则表示返回的串直接就是token串
				//tokenKey的格式，支持json多层级中取值，data:token表示从data节点中取token属性的值，如果有多层，中间使用:进行分隔即可
				tokenKey = param.getParamKey();
				//获取token的有效期
				tokenExpire = param.getParamValue();
			}
		}


		//3.获取基本请求参数，并请求接口
		String protocol = detail.getProtocol();
		String ip = detail.getIp();
		String port = detail.getPort();
		String finalUrl = protocol + "://" + ip + ":" + port  + url;

		String res = "";
        try {
            res = OKHttpClientUtil.postJson(finalUrl,header, JSONObject.toJSONString(body  , SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullListAsEmpty , SerializerFeature.WriteNullStringAsEmpty));
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用接口失败",e);
        }
        log.info("请求token结果："+res);
		//4.从请求参数中获取请求token
		if (tokenKey == null || StringUtils.isEmpty(tokenKey)) {
			//将获取到的token写入到redis中，并设置有效期
			long expire = COMMON_TOKEN_EXPIRE;
			try {
				expire = Long.parseLong(tokenExpire);
			} catch (Exception e) {
				e.printStackTrace();
				log.error("-->>ForwardManageClient--getToken:sm_interface_param中设置的token有效期不正确");
			}
			redisUtil.opsForValue().set(CommonConstant.TOKEN_PRIFIX + interCode, res.trim(), expire);
			//如果是直接返回token串
			return res;
		}
		//如果返回的是json字符串，获取token，并返回
		JSONObject jobj = JSONObject.parseObject(res);
		//支持获取多个层级下的token值，tokenKey设置为 data:token，表示获取data下的token的属性值。
		/*
			{
				"status": true,
				"code": 200,
				"message": "登陆成功",
				"data": {
					"token": "wRXMV0nK+HVBCddAAzH3Y1SCV4l3r9+l7yR6zFRQs+kIJ0jlw4djL/vvu3JX84xzsHfCJzLNgqHMHtGRa0h4+oNUeNcWuo/O+HhdLNQ9BYQiOpPNl+U6LVJn4LsSndaCWFcMjLMKTf53fWCr8Z8Ea0SD7i1fDgqDV9u5pypo9zthpSy0yK/VL7p4LNQ20KXwA6Nu1pfuXuZ9QGGhuCiuKlirC+OKYIMTGoMYCa2R+huei8Yp9/LSAx1pZawRmm1tJdywc3uSmgVFbALXVJkaEFbVRrL30ogKH7iZj6AXOFX+Ed7JszjBtATIPHuVUmQEWgax0eKGp9b2fwWw7i1DyhKyeewZmqO24F5eKWXBLjsmUJcpzuQqaib03yXDnIsDTgiZXE3h9GI="
				}
			}
		*/
		String token = CommonUtil.getValueFromJsonHier(jobj,tokenKey)==null?null: CommonUtil.getValueFromJsonHier(jobj,tokenKey).toString();

		//将获取到的token写入到redis中，并设置有效期
		long expire = COMMON_TOKEN_EXPIRE;
		try {
			expire = Long.parseLong(tokenExpire);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("-->>ForwardManageClient--getToken:sm_interface_param中设置的token有效期不正确");
		}
		if(StringUtils.isEmpty(token)){
			log.error("未获取到token，请确认请求token接口后返回的access_token key 配置");
			return null;
		}
		redisUtil.opsForValue().set(CommonConstant.TOKEN_PRIFIX + interCode, token.trim(), expire);
		return token;
	}


	/**
	   * @Description 转发webservice接口
	   * <AUTHOR>
	   * @Date 2019/11/1 16:40
	   * @Company CTTIC
	   * @Param []
	   * @return java.lang.String
	   **/
	private R<Object> doWebservice(InterfaceDTO dto, SmInterfaceManage detailWebservice){
		long currentThreadId = Thread.currentThread().getId();
		//1.获取到json报文，将json转换为 java bean
		//Map<String,Object> json = (Map<String,Object>)dto.getParam(); //此处可以进行强制类型转换
		Map<String,Object>json = CommonUtil.ObjectToMap(dto.getParam());
		if(json == null || json.entrySet().size() < 1){
			return R.fail("["+currentThreadId+"]报文不能为空");
		}

		//2.获取接口基础信息
		String protocol = detailWebservice.getProtocol();
		String ip = detailWebservice.getIp();
		String port = detailWebservice.getPort();
		String url = detailWebservice.getUrl();
		String finalUrl = protocol+"://"+ip+":"+port+url;

		//3.获取接口参数
		Long interfaceId = detailWebservice.getId();
		SmInterfaceParam interfaceParam = new SmInterfaceParam();
		interfaceParam.setInterfaceManageId(interfaceId);
		interfaceParam.setState(CommonConstant.STATE_U);
		List<SmInterfaceParam> paramList = paramService.list(Wrappers.query(interfaceParam));
		String username = "";
		String password = "";
		String methodName = "";
		String messageClass = "";
		String authModel = "";
		for(SmInterfaceParam param : paramList){
			if(CommonConstant.PARAM_TYPE_WEBSERVICE_USERNAME.equals(param.getParamType())){
				//如果是用户名
				username = param.getParamValue();
			}else if(CommonConstant.PARAM_TYPE_WEBSERVICE_PASSWORD.equals(param.getParamType())){
				//如果是密码
				password = param.getParamValue();
			}else if(CommonConstant.PARAM_TYPE_WEBSERVICE_METHOD_NAME.equals(param.getParamType())){
				//如果是接口方法名称
				methodName = param.getParamValue();
			}else if(CommonConstant.PARAM_TYPE_WEBSERVICE_MESSAGE_CLASS.equals(param.getParamType())){
				//如果是接口类名称：该类为对端接口的类全称，不是本系统的
				messageClass = param.getParamValue();
			}
		}
		//认证模板
		authModel = dto.getWebserviceAuthModel();
		if(StringUtils.isEmpty(authModel)){
			authModel = CommonConstant.WEBSERVICE_AUTH_MODEL_DEFAULT;
		}
		if(StringUtils.isEmpty(methodName)){
			log.error("-->>sm_interface_param中没有配置方法名称，不进行转发操作");
			return null;
		}

		//将传入的参数映射为配置的类型
		//Object message = JSONObject.parseObject(jsonParam, messageClass.getClass());

		//4.执行发送(动态调用方式)
		//4.1 根据接口地址获取调用客户端
		JaxWsDynamicClientFactory dcf = JaxWsDynamicClientFactory.newInstance();
		Client client = dcf.createClient(finalUrl);
		//设置调用规则
		HTTPConduit conduit = (HTTPConduit)client.getConduit();
		HTTPClientPolicy policy = new HTTPClientPolicy();
		policy.setConnectionTimeout(10000);
		policy.setAllowChunking(false);
		policy.setReceiveTimeout(10000);
		conduit.setClient(policy);

		//4.2 添加认证信息：只有用户名和密码都不为空，才会设置认证信息；否则，认为没有认证机制
		if(!StringUtils.isEmpty(username) && !StringUtils.isEmpty(password)){
			client.getOutInterceptors().add(new LoginInterceptor(username,password,authModel));
		}
		Object[] objects = new Object[0];
		try {
			//4.3 获取服务端上的相关实体对象：根据服务端上的entity路径来获取，如果不设置的话，invoke会报转换失败的异常
			//注意：此处的路径，是服务端上的对象的路径(即接口的命名空间+"."+实体类名称)，有一种简单的获取的方式，就是随便写一个，报错 cast 失败之后，拿到正确的，再写到这里
			Object messageO = Thread.currentThread().getContextClassLoader().loadClass(messageClass).newInstance();

			//4.4 进行对象属性数据的复制(因为两个对象是不同类型的对象，所以不能使用BeanUtils进行属性值的复制)
			String jsonStr = JSONObject.toJSONString(dto.getParam() , SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullListAsEmpty , SerializerFeature.WriteNullStringAsEmpty);

			//将本地的对象值复制给目标对象
			messageO = JSONObject.parseObject(jsonStr,messageO.getClass());

			//获取操作对应的Qname
			QName operateQName = getOperateQName(client,methodName);

			//4.5 进行远程方法调用
			objects = client.invoke(operateQName, messageO);
			if(objects == null || objects.length < 1){
				throw new Exception("转发失败：未接收到返回信息");
			}
			System.out.println("返回数据:" + objects[0]);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("转发失败："+e.getStackTrace());
			return R.fail("转发失败："+e.getMessage());
		}

		//解析执行结果
		String resStr = JSONObject.toJSONString(objects[0]  , SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullListAsEmpty ,SerializerFeature.WriteNullStringAsEmpty);
		String resCodeKey = dto.getResCodeKey();
		String resMessageKey = dto.getResMessageKey();
		String[] successCodeValues = dto.getSuccessCodeValue();
		String resDataKey = dto.getResDataKey();
		JSONObject jsonObject = JSONObject.parseObject(resStr);
		Object resCodeObj = CommonUtil.getValueFromJsonHier(jsonObject , resCodeKey);
		String resCodeValue = resCodeObj==null?"":resCodeObj.toString();
		Object messageObj = CommonUtil.getValueFromJsonHier(jsonObject ,resMessageKey );
		Object resData = CommonUtil.getValueFromJsonHier(jsonObject , resDataKey);
		//如果成功码列表中包含返回码，表示成功
		if(CommonUtil.containItem(successCodeValues , resCodeValue)){
			//return R.success(resStr);
			return R.data(ResultCode.SUCCESS.getCode() , resData==null?"":resData , messageObj==null?"成功":messageObj.toString()  );
		}else{
			//如果失败
			return R.fail(messageObj==null?"":messageObj.toString());
		}
	}




	/**
	   * @Description 解决接口和实现类不在同一目录并且 namespace 不同的问题
	 * 当 webservice 服务端 接口 和 实现类 不在同一个目录下，并且 namespace 不同时，普通的客户端调用方式 会报 No operation was found with the name
		* <AUTHOR>
		* @Date 2021/9/8 19:51
		* @Company CTTIC
		* @Param [client, operation]
		* @return javax.xml.namespace.QName
		**/
	private static QName getOperateQName(Client client, String operation){
		Endpoint endpoint = client.getEndpoint();
		QName opName = new QName(endpoint.getService().getName().getNamespaceURI(), operation);
		BindingInfo bindingInfo = endpoint.getEndpointInfo().getBinding();
		if (bindingInfo.getOperation(opName) == null) {
			for (BindingOperationInfo operationInfo : bindingInfo.getOperations()) {
				if (operation.equals(operationInfo.getName().getLocalPart())) {
					opName = operationInfo.getName();
					break;
				}
			}
		}
		log.info("Operation:"+operation+",namespaceURI:" + opName.getNamespaceURI());
		return opName;
	}

}
