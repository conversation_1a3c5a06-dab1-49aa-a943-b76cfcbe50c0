package com.xh.vdm.mqttOfficial.util;

import org.springframework.stereotype.Component;

import java.lang.management.ManagementFactory;
import java.net.InetAddress;
import java.net.NetworkInterface;

@Component
public class SnowflakeIdWorker {

	private final static long twepoch = 1717171200L;

	private final static long DEVICE_TYPE_BITS = 5L;
	private final static long DATACENTER_ID_BITS = 5L;
	private final static long MACHINE_ID_BITS = 5L;
	private final static long SEQUENCE_BITS = 16L;

	private final static long MAX_DEVICE_TYPE = -1L ^ (-1L << DEVICE_TYPE_BITS);
	private final static long MAX_DATACENTER_ID = -1L ^ (-1L << DATACENTER_ID_BITS);
	private final static long MAX_MACHINE_ID = -1L ^ (-1L << MACHINE_ID_BITS);
	private final static long SEQUENCE_MASK = -1L ^ (-1L << SEQUENCE_BITS);

	//业务编码
	private final static long MACHINE_ID_SHIFT = SEQUENCE_BITS;
	private final static long DATACENTER_ID_SHIFT = MACHINE_ID_SHIFT + MACHINE_ID_BITS;
	private final static long DEVICE_TYPE_SHIFT = DATACENTER_ID_SHIFT + DATACENTER_ID_BITS;
	private final static long TIME_SHIFT = DEVICE_TYPE_SHIFT + DEVICE_TYPE_BITS;

	private static long lastTimestamp = -1L;
	private long sequence = 0L;
	private final long deviceType;
	private final long datacenterId;
	private final long machineId;

	public SnowflakeIdWorker() {
		this.deviceType = 1L; // 默认设备类型为1
		this.datacenterId = getDatacenterId(MAX_DATACENTER_ID);
		this.machineId = getMaxMachineId(datacenterId, MAX_MACHINE_ID);
	}

	public SnowflakeIdWorker(long deviceType, long datacenterId, long machineId) {
		if (deviceType < 0 || deviceType > MAX_DEVICE_TYPE) {
			throw new IllegalArgumentException("无效的设备类型");
		}
		if (datacenterId < 0 || datacenterId > MAX_DATACENTER_ID) {
			throw new IllegalArgumentException(String.format("数据中心标识位数必须在0和%d之间", MAX_DATACENTER_ID));
		}
		if (machineId < 0 || machineId > MAX_MACHINE_ID) {
			throw new IllegalArgumentException(String.format("机器ID必须在0和%d之间", MAX_MACHINE_ID));
		}
		this.deviceType = deviceType;
		this.datacenterId = datacenterId;
		this.machineId = machineId;
	}

	public synchronized long nextId() {
		long timestamp = timeGen();

		if (timestamp < lastTimestamp) {
			throw new RuntimeException(String.format("时钟向后移动，拒绝生成%d毫秒内的ID", lastTimestamp - timestamp));
		}

		if (lastTimestamp == timestamp) {
			sequence = (sequence + 1) & SEQUENCE_MASK;
			if (sequence == 0) {
				timestamp = tilNextMillis(lastTimestamp);
			}
		} else {
			sequence = 0L;
		}

		lastTimestamp = timestamp;

		long nextId = ((timestamp - twepoch) << TIME_SHIFT)
			| (deviceType << DEVICE_TYPE_SHIFT)
			| (datacenterId << DATACENTER_ID_SHIFT)
			| (machineId << MACHINE_ID_SHIFT)
			| sequence + 1;

		nextId = nextId & Long.MAX_VALUE;

		return nextId;
	}

	private long tilNextMillis(final long lastTimestamp) {
		long timestamp = timeGen();
		while (timestamp <= lastTimestamp) {
			timestamp = timeGen();
		}
		return timestamp;
	}

	private long timeGen() {
		return System.currentTimeMillis() /1000;
	}

	protected static long getMaxMachineId(long datacenterId, long maxMachineId) {
		StringBuilder mpid = new StringBuilder();
		mpid.append(datacenterId);
		String name = ManagementFactory.getRuntimeMXBean().getName();
		if (!name.isEmpty()) {
			mpid.append(name.split("@")[0]);
		}
		return (mpid.toString().hashCode() & 0xffff) % (maxMachineId + 1);
	}

	protected static long getDatacenterId(long maxDatacenterId) {
		long id = 0L;
		try {
			InetAddress ip = InetAddress.getLocalHost();
			NetworkInterface network = NetworkInterface.getByInetAddress(ip);
			if (network == null) {
				id = 1L;
			} else {
				byte[] mac = network.getHardwareAddress();
				id = ((0x000000FF & (long) mac[mac.length - 1])
					| (0x0000FF00 & (((long) mac[mac.length - 2]) << 8))) >> 6;
				id = id % (maxDatacenterId + 1);
			}
		} catch (Exception e) {
			System.out.println("获取数据中心标识位异常：" + e.getMessage());
		}
		return id;
	}
}
