package com.xh.vdm.mqttOfficial.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *终端鉴权
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceAuthUpload {
	/**
	 * 赋码值
	 */
	@JsonProperty("device_num")
	@JSONField(name = "device_num")
	String deviceNum;
	/**
	 * 赋码签名
	 */
	@JsonProperty("device_sign")
	@JSONField(name = "device_sign")
	String deviceSign;

	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
	private Date dateTime;
}
