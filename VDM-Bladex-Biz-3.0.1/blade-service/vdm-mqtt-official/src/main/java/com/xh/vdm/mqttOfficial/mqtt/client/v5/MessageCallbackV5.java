package com.xh.vdm.mqttOfficial.mqtt.client.v5;

import com.xh.vdm.mqttOfficial.mqtt.handler.IMqttMessageHandler;
import com.xh.vdm.mqttOfficial.mqtt.handler.IMqttMessageHandlerV5;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.mqttv5.client.IMqttToken;
import org.eclipse.paho.mqttv5.client.MqttCallback;
import org.eclipse.paho.mqttv5.client.MqttDisconnectResponse;
import org.eclipse.paho.mqttv5.common.MqttException;
import org.eclipse.paho.mqttv5.common.MqttMessage;
import org.eclipse.paho.mqttv5.common.packet.MqttProperties;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * v5 消息回调
 * <AUTHOR>
 * @date 2024/4/12
 */
@Slf4j
public class MessageCallbackV5 implements MqttCallback {

    private final Map<Class<? extends IMqttMessageHandler>, IMqttMessageHandler> messageHandlers = new HashMap<>();

    private final MqttClientV5 mqttClient;


    public MessageCallbackV5(MqttClientV5 mqttClient) {
        this.mqttClient = mqttClient;
    }

    @Override
    public void disconnected(MqttDisconnectResponse disconnectResponse) {
        log.error("disconnected -> {}", disconnectResponse);
    }

    @Override
    public void mqttErrorOccurred(MqttException exception) {
        log.error("mqttErrorOccurred error", exception);
    }

    @Override
    public void messageArrived(String topic, MqttMessage message) {
        try {
            String msg = new String(message.getPayload(), StandardCharsets.UTF_8);
            if (message.isRetained()) {
                log.info("received reserved message -> [{}] topic -> [{}]", msg, topic);
                return;
            }

            if (log.isDebugEnabled()) {
                log.debug("messageArrived topic = {} message = {}", topic, msg);
            }
            // 匹配对应的消息处理器
            for (IMqttMessageHandler messageHandler : messageHandlers.values()) {
                if (messageHandler.match(topic)) {
                    messageHandler.process(topic, msg);
                    return;
                }
            }
            log.warn("unknown topic: topic -> [{}],message -> [{}]", topic, msg);
        } catch (Exception e) {
            log.error("messageArrived error", e);
        }
    }

    /**
     * 消息发送成功后调用
     * 不关注消息发送的结果 这里留空
     */
    @Override
    public void deliveryComplete(IMqttToken token) {

    }

    /**
     * 连接成功调用
     *
     * @param reconnect 自动重新连接
     * @param serverURI 服务器url
     */
    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        log.info("connectComplete serverURI->[{}] reconnect->[{}]", serverURI, reconnect);
        // 自动重连订阅丢失 重新订阅
        if (reconnect) {
            try {
                mqttClient.subscribe();
            } catch (MqttException e) {
                log.error("failed to reconnect to the subscription topic ");
            }
        }
    }

    @Override
    public void authPacketArrived(int reasonCode, MqttProperties properties) {

    }

    /**
     * 添加消息处理器
     */
    public void addMessageHandler(IMqttMessageHandler messageHandler) {
        if (messageHandler == null) {
            return;
        }
        if (messageHandler instanceof IMqttMessageHandlerV5) {
            messageHandlers.put(messageHandler.getClass(), messageHandler);
        }
    }
}
