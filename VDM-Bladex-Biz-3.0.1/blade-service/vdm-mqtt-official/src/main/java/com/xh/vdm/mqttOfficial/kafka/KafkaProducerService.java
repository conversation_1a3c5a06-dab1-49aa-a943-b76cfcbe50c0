package com.xh.vdm.mqttOfficial.kafka;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class KafkaProducerService {
    private static final String TOPIC = "my_topic";
    //正式入网之前的定位点topic
    private static final String LOC_CHECK_TOPIC = "TerminalCheckTopic";
    //正式入网之后的定位点topic
    private static final String LOC_TOPIC = "ce.comms.fct.location.0";
	//上下线
	private static final String OnOffLine_TOPIC="terminalstatus";
	//向大数据发送发送日志
	private static final String RawLogTopic="ce.comms.fct.log.0";

	@Resource
    private KafkaTemplate<String, String> kafkaTemplate;

    public void sendMessage(String message) {
        kafkaTemplate.send(TOPIC, message);
    }

	public void sendOnOffLineMessage(String message,String key) {
		log.info("sendOnOffLineMessage:{}",message);
		kafkaTemplate.send(OnOffLine_TOPIC,key, message);
	}

    public void sendPositionReportMessage(String message,String key){
        log.info("sendPositionReportMessage:{}",message);
        kafkaTemplate.send(LOC_TOPIC,key, message);
    }

    public void sendBatchPositionReportMessage(String message,String key){
        log.info("sendBatchPositionReportMessage:{}",message);
        kafkaTemplate.send(LOC_TOPIC,key, message);
    }

	public void sendRawLogMessage(String message,String key) {
		log.info("sendRawLogMessage:{}",message);
		kafkaTemplate.send(RawLogTopic,key, message);
	}

}
