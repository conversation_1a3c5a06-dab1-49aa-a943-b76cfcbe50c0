package com.xh.vdm.mqttOfficial.mqtt.handler.watch;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xh.vdm.mqttOfficial.config.MqttConfig;
import com.xh.vdm.mqttOfficial.config.WearableWorkerCache;
import com.xh.vdm.mqttOfficial.constant.CommonConstant;
import com.xh.vdm.mqttOfficial.constant.ModeTypeEnum;
import com.xh.vdm.mqttOfficial.dto.*;
import com.xh.vdm.mqttOfficial.entity.*;
import com.xh.vdm.mqttOfficial.kafka.KafkaProducerService;
import com.xh.vdm.mqttOfficial.mapper.BdmDeviceLinkMapper;
import com.xh.vdm.mqttOfficial.mapper.BdmDeviceOnlineMapper;
import com.xh.vdm.mqttOfficial.mapper.BdmDeviceStatusMapper;
import com.xh.vdm.mqttOfficial.mapper.BdmRawLogMapper;
import com.xh.vdm.mqttOfficial.mqtt.client.IMqttClient;
import com.xh.vdm.mqttOfficial.mqtt.client.v3.MqttClientV3;
import com.xh.vdm.mqttOfficial.mqtt.client.v5.MqttClientV5;
import com.xh.vdm.mqttOfficial.mqtt.handler.IMqttMessageHandlerV5;
import com.xh.vdm.mqttOfficial.mqtt.handler.MessageHandlerAbst;
import com.xh.vdm.mqttOfficial.service.BdcCodingMachineService;
import com.xh.vdm.mqttOfficial.service.DeviceCodeService;
import com.xh.vdm.mqttOfficial.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.xh.vdm.mqttOfficial.config.WearableWorkerCache.WearableWorkerMap;

@Slf4j
@Component
public class DeviceAuthMsgHandler extends MessageHandlerAbst implements IMqttMessageHandlerV5{
    /**
     * 设备号位置
     */
    private final int  deviceIndex;
    private String deviceId;
	private int result;
    @Resource
    MqttConfig mqttConfig;
    @Resource
	MqttClientV3 mqttClientV3;
	@Resource
	MqttClientV5 mqttClientV5;
    @Autowired
    BdcCodingMachineService bdcCodingMachineService;
	@Resource
	DeviceCodeService deviceCodeService;
    @Autowired
    StringRedisTemplate redisTemplate;
    @Autowired
    WearableWorkerCache wearableWorkerCache;
	@Resource
	PostGre postGre;
	@Resource
	Geo geo;
	@Autowired
	KafkaProducerService kafkaProducerService;
	@Resource
	private BdmDeviceLinkMapper bdmDeviceLinkMapper;

	@Resource
	private BdmDeviceOnlineMapper bdmDeviceOnlineMapper;

	@Resource
	private BdmDeviceStatusMapper bdmDeviceStatusMapper;

    public DeviceAuthMsgHandler(List<IMqttClient> messageClientList, MqttConfig mqttConfig) {
        super();
        this.setSubTopic(mqttConfig.getTopic().getSubscription().getDeviceAuth());
        this.deviceIndex = Arrays.asList(subTopic.split(CommonConstant.MqttConstant.SLASH)).indexOf(CommonConstant.MqttConstant.PLUS_SIGN);
        messageClientList.forEach(client -> {
            addClient(client.version(), client);
            client.addMessageHandler(this);
        });
    }

    @Override
    public void process(String topic, String msg) {
        deviceId = topic.split(CommonConstant.MqttConstant.SLASH)[deviceIndex];
		log.info("DeviceAuthMsgHandler process received deviceId：{}, msg: {}", deviceId, msg);
		postGre.saveLog(deviceId,(byte)2,(byte)0,topic.concat(":").concat(msg));
        List<DeviceAuthUpload> dtoList = analysisMessageToList(msg);
    }

    private List<DeviceAuthUpload> analysisMessageToList(String msg) {
		String authTopic =mqttConfig.getTopic().getPublish().getDeviceAuthResult().replace(CommonConstant.MqttConstant.PLACEHOLDER,deviceId);
        List<DeviceAuthUpload> list = new ArrayList<>();
        String[] params = msg.split(",");
        if (params.length<2){
			postGre.saveLog(deviceId,(byte)4,(byte)1,authTopic.concat(":").concat(String.valueOf(12)));
			mqttClientV3.publishMessage(authTopic,String.valueOf(12).getBytes(StandardCharsets.UTF_8),0,false);
            return list;
        }
		WearableWorker wearableWorker=WearableWorkerMap.get(deviceId);
		if (wearableWorker==null){
			postGre.saveLog(deviceId,(byte)4,(byte)1,authTopic.concat(":").concat(String.valueOf(13)));
			mqttClientV3.publishMessage(authTopic,String.valueOf(13).getBytes(StandardCharsets.UTF_8),0,false);
			return list;
		}
        DeviceAuthUpload dto=new DeviceAuthUpload();
        dto.setDeviceNum(params[0]);
        dto.setDeviceSign(params[1]);
		if (wearableWorker.getSpecificity()>=2){
			//新設備
			result = checkAuth(dto,wearableWorker);
		}
		postGre.saveLog(deviceId,(byte)4,(byte)1,authTopic.concat(":").concat(String.valueOf(result)));
		mqttClientV3.publishMessage(authTopic,String.valueOf(result).getBytes(StandardCharsets.UTF_8),0,false);
		if (result==0){
			String targetName=String.format("%s(%s)",wearableWorker.getTargetPersonName(),wearableWorker.getTargetName());
			String addr="";
			if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(CommonConstant.LocConstant.RealDevSte,wearableWorker.getDeviceId()+""))) {
				Object devObj =redisTemplate.opsForHash().get(CommonConstant.LocConstant.RealDevSte,wearableWorker.getDeviceId()+"");
				if (devObj!=null){
					BdmDevSte devSteHas=JSONObject.parseObject(devObj.toString(), BdmDevSte.class);
					devSteHas.setUpdateTime(new Date());
					redisTemplate.opsForHash().put(CommonConstant.LocConstant.RealDevSte,String.valueOf(wearableWorker.getDeviceId()),JSON.toJSONString(devSteHas));
				}
				return list;
			}
			TerminalStatus terminalStatus=new TerminalStatus();
			BdmDeviceLink bdmDeviceLink=new BdmDeviceLink();
			Object obj =redisTemplate.opsForHash().get(CommonConstant.LocConstant.RealLoc,wearableWorker.getDeviceId()+"");
			if (obj!=null){
				Track track = JSONObject.parseObject(obj.toString(), Track.class);
				terminalStatus.setLongitude(track.getLongitude());
				terminalStatus.setLatitude(track.getLatitude());
				bdmDeviceLink.setLongitude(track.getLongitude());
				bdmDeviceLink.setLatitude(track.getLatitude());
				addr=geo.selectManufacturer(track.getLongitude(),track.getLatitude());
			}
			terminalStatus.setDeviceId(wearableWorker.getDeviceId());
			terminalStatus.setPhone(wearableWorker.getDeviceUniqueId());
			terminalStatus.setDeviceType(wearableWorker.getDeviceType());
			terminalStatus.setTime(new Date());
			terminalStatus.setTargetId(wearableWorker.getTargetId());
			terminalStatus.setTargetType(wearableWorker.getTargetType());
			terminalStatus.setTargetName(targetName);
			terminalStatus.setUniqueId(wearableWorker.getDeviceUniqueId());
			terminalStatus.setDeviceNum(wearableWorker.getDeviceNum());
			terminalStatus.setOnOffLine(0);
			kafkaProducerService.sendOnOffLineMessage(JSON.toJSONString(terminalStatus),terminalStatus.getUniqueId());

			BdmDeviceOnline bdmDeviceOnline=new BdmDeviceOnline();
			SnowflakeIdWorker idWorker = new SnowflakeIdWorker(6, 1, 2);
			bdmDeviceOnline.setId(idWorker.nextId());
			bdmDeviceOnline.setDeviceId(wearableWorker.getDeviceId());
			bdmDeviceOnline.setDeviceType(wearableWorker.getDeviceType());
			bdmDeviceOnline.setDeviceNum(wearableWorker.getDeviceNum());
			bdmDeviceOnline.setTargetId(wearableWorker.getTargetId());
			bdmDeviceOnline.setTargetType(wearableWorker.getTargetType());
			bdmDeviceOnline.setTargetName(targetName);
			bdmDeviceOnline.setUniqueId(wearableWorker.getDeviceUniqueId());
			bdmDeviceOnline.setDeptId(wearableWorker.getDeptId());
			bdmDeviceOnline.setStartTime(new Date());
			bdmDeviceOnlineMapper.insert(bdmDeviceOnline);

			BdmDevSte devSte=new BdmDevSte();
			devSte.setId(bdmDeviceOnline.getId());
			devSte.setDeviceId(bdmDeviceOnline.getDeviceId());
			devSte.setDeviceType(bdmDeviceOnline.getDeviceType());
			devSte.setDeviceNum(bdmDeviceOnline.getDeviceNum());
			devSte.setDeviceCategory(wearableWorker.getDeviceCategory());
			devSte.setActionTime(new Date());
			devSte.setUniqueId(bdmDeviceOnline.getUniqueId());
			devSte.setTargetId(bdmDeviceOnline.getTargetId());
			devSte.setTargetName(targetName);
			devSte.setTargetType(bdmDeviceOnline.getTargetType());
			devSte.setDeptId(bdmDeviceOnline.getDeptId());
			devSte.setUpdateTime(new Date());
			if (obj!=null){
				Track track = JSONObject.parseObject(obj.toString(), Track.class);
				devSte.setLongitude(track.getLongitude());
				devSte.setLatitude(track.getLatitude());
			}
			redisTemplate.opsForHash().put(CommonConstant.LocConstant.RealDevSte,String.valueOf(wearableWorker.getDeviceId()),JSON.toJSONString(devSte));

			// 获取当前时间
			LocalDateTime now = LocalDateTime.now();
			// 获取明日零点的时间
			LocalDateTime tomorrowMidnight = now.plusDays(1).truncatedTo(ChronoUnit.DAYS);
			// 计算当前时间到明日零点的时间差
			Duration duration = Duration.between(now, tomorrowMidnight);
			redisTemplate.opsForValue().set(CommonConstant.LocConstant.TodayOnline+wearableWorker.getDeviceId(),wearableWorker.getDeviceModel(),duration);

			bdmDeviceLink.setDeviceType(wearableWorker.getDeviceType());
			bdmDeviceLink.setDeviceId(wearableWorker.getDeviceId());
			bdmDeviceLink.setDeviceNum(wearableWorker.getDeviceNum());
			bdmDeviceLink.setUniqueId(wearableWorker.getDeviceUniqueId());
			bdmDeviceLink.setAction((short)0);
			bdmDeviceLink.setTime(new Date());
			bdmDeviceLink.setDeptId(wearableWorker.getDeptId());
			bdmDeviceLink.setTargetId(wearableWorker.getTargetId());
			bdmDeviceLink.setTargetName(targetName);
			bdmDeviceLink.setTargetType(wearableWorker.getTargetType());
			bdmDeviceLink.setAddress(addr);
			bdmDeviceLinkMapper.insert(bdmDeviceLink);

			BdmDeviceStatus bdmDeviceStatus=new BdmDeviceStatus();
			bdmDeviceStatus.setDeviceId(wearableWorker.getDeviceId());
			bdmDeviceStatus.setDeviceType((byte)wearableWorker.getDeviceType());
			bdmDeviceStatus.setDeviceNum(wearableWorker.getDeviceNum());
			bdmDeviceStatus.setUniqueId(wearableWorker.getDeviceUniqueId());
			bdmDeviceStatus.setTargetName(targetName);
			bdmDeviceStatus.setTargetType(wearableWorker.getTargetType());
			bdmDeviceStatus.setTargetId(wearableWorker.getTargetId());
			bdmDeviceStatus.setAction((byte)0);
			bdmDeviceStatus.setActionTime(new Date());
			bdmDeviceStatus.setDeptId(wearableWorker.getDeptId());
//			String alarmKey="AlarmFaultNum:"+wearableWorker.getDeviceNum();
//			redisTemplate.opsForValue().set(alarmKey,"0");
			Integer statusResult=bdmDeviceStatusMapper.checkExist(wearableWorker.getDeviceUniqueId());
			int rows;
			if (statusResult == 0) {
				rows=bdmDeviceStatusMapper.insert(bdmDeviceStatus);
				log.info("bdmDeviceStatusMapper 插入成功:{}",rows);
			}else {
				UpdateWrapper<BdmDeviceStatus> updateWrapper = new UpdateWrapper<>();
				BdmDeviceStatus updateDeviceStatus=new BdmDeviceStatus();
				updateWrapper.eq("unique_id",wearableWorker.getDeviceUniqueId());
				updateDeviceStatus.setAction((byte)0);
				updateDeviceStatus.setActionTime(new Date());
				updateDeviceStatus.setDeptId(wearableWorker.getDeptId());
				updateDeviceStatus.setFaultCount((short)0);
				rows=bdmDeviceStatusMapper.update(updateDeviceStatus,updateWrapper);
				log.info("bdmDeviceStatusMapper 更新成功:{}",rows);
			}
		}
        list.add(dto);
        return list;
    }

    public int checkAuth(DeviceAuthUpload deviceAuthUpload,WearableWorker wearableWorker) {
		DeviceCode deviceCode=deviceCodeService.getDeviceCodeByDeviceNum(deviceAuthUpload.getDeviceNum());
		if (deviceCode==null){
			return 9;
		}
        String machineNumber=deviceAuthUpload.getDeviceNum().substring(1, 5);
        BdmCodingMachine bdmCodingMachine=bdcCodingMachineService.queryByNumber(machineNumber);
        if (bdmCodingMachine==null){
            return 10;
        }
        try{
            String  deviceNumSign = SM2Util.decrypt(deviceAuthUpload.getDeviceSign(), "00" + bdmCodingMachine.getPrivateKey(), ModeTypeEnum.BASE_MODE);
            log.info("赋码签名解密后:{}",deviceNumSign);
            String[] deviceNumSignArr=deviceNumSign.split(":");
            if (deviceNumSignArr.length!=7){
                return 8;
            }
            if (!deviceNumSignArr[0].equals(deviceCode.getKind())){
                return 1;
            }
            if (!deviceNumSignArr[1].equals(deviceCode.getVendor())){
                return 2;
            }
            if (!deviceNumSignArr[2].equals(deviceCode.getModel())){
                return 3;
            }
            if (!deviceNumSignArr[3].equals(deviceCode.getSerial())){
                return 4;
            }
            if (!deviceNumSignArr[4].equals(deviceCode.getImei())){
                return 5;
            }
            if (!deviceNumSignArr[5].equals(deviceCode.getBdChipSerial())){
                return 6;
            }
            if (!deviceNumSignArr[6].equals(deviceCode.getDeviceNum())){
                return 7;
            }
			if (!deviceNumSignArr[6].equals(wearableWorker.getDeviceNum())){
				return 11;
			}
        }catch (Exception e){
            e.printStackTrace();
			return 14;
        }
        log.info("checkAuth 鉴权成功:{},{}",deviceCode.getDeviceNum(),deviceCode.getSerial());
        return 0;
    }
    @Override
    public boolean match(String topic) {
        return TopicUtils.math(subTopic, topic, deviceIndex);
    }
}
