package com.xh.vdm.mqttOfficial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
@Data
@TableName("bdm_device_online")
public class BdmDeviceOnline implements Serializable {
	private static final long serialVersionUID = 1L;
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	@TableField(value = "device_id")
	private Long deviceId;
	@TableField(value = "device_type")
	private Integer deviceType;
	@TableField(value = "device_num")
	private String deviceNum;
	@TableField(value = "unique_id")
	private String uniqueId;
	@TableField(value = "target_id")
	private Long targetId;
	@TableField(value = "target_type")
	private Integer targetType;
	@TableField(value = "target_name")
	private String targetName;
	@TableField(value = "start_time")
	private Date startTime; // Online start time
	@TableField(value = "end_time")
	private Date endTime;   // Offline end time
	@TableField(value = "dept_id")
	private Long deptId;
}
