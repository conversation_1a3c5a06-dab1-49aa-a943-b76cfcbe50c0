package com.xh.vdm.mqttOfficial.mqtt.client;

import com.xh.vdm.mqttOfficial.mqtt.handler.IMqttMessageHandler;

public interface IMqttClient {

    String version();

    boolean isConnected();

    boolean reconnect();

    /**
     * @param topic    发送主题
     * @param payload  消息体
     * @param qos      qos
     * @param retained 是否保留消息
     */
    boolean publishMessage(String topic, byte[] payload, int qos, boolean retained) ;

    /**
     * 添加消息处理器
     * @param IMqttMessageHandler 消息处理器
     */
    void addMessageHandler(IMqttMessageHandler IMqttMessageHandler);
}
