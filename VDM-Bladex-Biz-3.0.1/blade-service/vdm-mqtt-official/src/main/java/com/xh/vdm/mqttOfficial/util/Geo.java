package com.xh.vdm.mqttOfficial.util;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
public class Geo {
	@Value("${custom.uid}")
	String uid;
	@Value("${custom.manufacturer}")
	String manufacturer;
	String url;
	private String GetSiWeiAddr(double longitude,double latitude){
		JSONObject requestBody = new JSONObject();
		url=String.format("http://c.qqearth.com:81/SE_RGC2?st=Rgc2&uid=%s&cd=wgs84&output=json&point=%f,%f",uid,longitude,latitude);
		log.info("GetSiWeiAddr url:{}",url);
		String respBodyStr = HttpRequest.get(url).execute().body();
		log.info("GetSiWeiAddr respBodyStr:{}",respBodyStr);
		JSONObject responseBody = JSON.parseObject(respBodyStr);
		String addr = "无法获取地址";
		if (Objects.equals(responseBody.get("status").toString(), "error")){
			log.info("######参数错误#######");
		}else {
			String districtText =responseBody.getJSONObject("result").get("district_text").toString().replaceAll(">","");
			String road =responseBody.getJSONObject("result").getJSONObject("road").get("road_level").toString();
			addr=districtText+responseBody.getJSONObject("result").get("address").toString();
			if (Objects.equals(responseBody.getJSONObject("result").get("address"),null)){
				addr=districtText+responseBody.getJSONObject("result").get("address").toString()+road;
			}
		}
		return addr;
	}
	private String GetGNAddr(double longitude,double latitude){
		JSONObject requestBody = new JSONObject();
		url=String.format("http://*************/api/geocoder?postStr={'lon':%f,'lat':%f,'ver':1}&type=geocode",longitude,latitude);
		log.info("GetGNAddr url:{}",url);
		String respBodyStr = HttpRequest.get(url).execute().body();
		log.info("GetGNAddr respBodyStr:{}",respBodyStr);
		JSONObject responseBody = JSON.parseObject(respBodyStr);
		String addr = "无法获取地址";
		if (!Objects.equals(responseBody.get("status").toString(), "0")){
			log.info("######参数错误#######");
		}else {
			addr=responseBody.getJSONObject("result").getString("formatted_address");
		}
		return addr;
	}

	public String selectManufacturer(double longitude,double latitude){
		String addr="";
		switch (manufacturer){
			case "sw":
				addr=GetSiWeiAddr(longitude,latitude);
				break;
			case "gn":
				addr=GetGNAddr(longitude,latitude);
				break;
		}
		return addr;
	}

}
