package com.xh.vdm.mqttOfficial.mqtt.handler.watch;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttOfficial.config.MqttConfig;
import com.xh.vdm.mqttOfficial.constant.CommEnum;
import com.xh.vdm.mqttOfficial.constant.CommonConstant;
import com.xh.vdm.mqttOfficial.dto.DeviceNumReport;
import com.xh.vdm.mqttOfficial.dto.NetCheck;
import com.xh.vdm.mqttOfficial.mqtt.client.IMqttClient;
import com.xh.vdm.mqttOfficial.mqtt.handler.IMqttMessageHandlerV5;
import com.xh.vdm.mqttOfficial.mqtt.handler.MessageHandlerAbst;
import com.xh.vdm.mqttOfficial.util.PostGre;
import com.xh.vdm.mqttOfficial.util.TopicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class DeviceNumReportMsgHandler extends MessageHandlerAbst implements IMqttMessageHandlerV5 {
    /**
     * 设备号位置
     */
    private final int deviceIndex;
    private String deviceId;
    @Autowired
    StringRedisTemplate redisTemplate;
    @Resource
    MqttConfig mqttConfig;
	@Resource
	PostGre postGre;
    public DeviceNumReportMsgHandler(List<IMqttClient> messageClientList, MqttConfig mqttConfig) {
        super();
        this.setSubTopic(mqttConfig.getTopic().getSubscription().getDeviceNumReport());
        this.deviceIndex = Arrays.asList(subTopic.split(CommonConstant.MqttConstant.SLASH)).indexOf(CommonConstant.MqttConstant.PLUS_SIGN);
        messageClientList.forEach(client -> {
            addClient(client.version(), client);
            client.addMessageHandler(this);
        });
    }

    @Override
    public void process(String topic, String msg) {
        deviceId = topic.split(CommonConstant.MqttConstant.SLASH)[deviceIndex];
		//postGre.saveLog(deviceId,(byte)2,(byte)0,topic.concat(":").concat(msg));
        List<DeviceNumReport> dtoList = analysisMessageToList(msg);
        log.info("DeviceNumReportMsgHandler process received deviceId：{}, msg: {}", deviceId, JSON.toJSONString(dtoList));
    }

    private List<DeviceNumReport> analysisMessageToList(String msg) {
        List<DeviceNumReport> list = new ArrayList<>();
        String[] params = msg.split(",");
        DeviceNumReport dto=new DeviceNumReport();
        dto.setDeviceCategory(params[0]);
        dto.setManufacturerId(params[1]);
        dto.setProductModel(params[2]);
        dto.setProductSerialNumber(params[3]);
        dto.setImei(params[4]);
        dto.setBdChipSerialNumber(params[5]);
        list.add(dto);
        return list;
    }

    @Override
    public boolean match(String topic) {
        return TopicUtils.math(subTopic, topic, deviceIndex);
    }
}
