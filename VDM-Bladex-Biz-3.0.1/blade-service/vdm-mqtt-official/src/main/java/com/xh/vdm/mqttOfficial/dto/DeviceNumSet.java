package com.xh.vdm.mqttOfficial.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceNumSet {
	/**
	 * 赋码值
	 * 长度16位，参照TNCE012404090001
	 */
	@JSONField(name = "device_num")
	@JsonProperty("device_num")
	private String deviceNum;
	/**
	 * 赋码签名
	 */
	@JSONField(name = "device_sign")
	@JsonProperty("device_sign")
	private String deviceSign;
	/**
	 * 终端唯一编号
	 */
	@JSONField(name = "device_no")
	@JsonProperty("device_no")
	private String deviceNo;

	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
	private Date dateTime;
}
