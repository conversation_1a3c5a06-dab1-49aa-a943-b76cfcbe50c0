package com.xh.vdm.mqttOfficial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
@TableName("bdm_device_status")
public class BdmDeviceStatus implements Serializable {
	private static final long serialVersionUID = 1L;
	@TableId(value = "id", type = IdType.AUTO)
	private Long id; // 主键，唯一标识码
	@TableField(value = "device_id")
	private Long deviceId; // 终端设备id
	@TableField(value = "device_type")
	private Byte deviceType; // 终端设备类型
	@TableField(value = "unique_id")
	private String uniqueId; // 终端编号
	@TableField(value = "device_num")
	private String deviceNum; // 终端设备赋码值
	@TableField(value = "target_id")
	private Long targetId ; // 监控对象id
	@TableField(value = "target_type")
	private Integer targetType ; // 监控对象类别
	@TableField(value = "target_name")
	private String targetName; // 监控对象名称
	@TableField(value = "action")
	private Byte action ; // 0-上线，1-下线
	@TableField(value = "action_time")
	private Date actionTime; // 上下线时刻
	@TableField(value = "fault_count")
	private Short faultCount ; // 设备故障数
	@TableField(value = "dept_id")
	private Long deptId; // 部门ID
}
