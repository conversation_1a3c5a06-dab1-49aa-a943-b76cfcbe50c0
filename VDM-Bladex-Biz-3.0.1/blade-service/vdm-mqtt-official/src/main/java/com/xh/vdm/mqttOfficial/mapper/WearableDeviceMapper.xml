<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.mqttOfficial.mapper.WearableDeviceMapper">

    <select id="queryWearableWorkerList" resultType="com.xh.vdm.mqttOfficial.entity.WearableWorker">
        SELECT
            d.id AS device_id,
            d.device_type,
            d.device_num,
            d.model AS device_model,
            d.unique_id AS device_unique_id,
            d.imei AS device_imei,
            d.category AS device_category,
            d.specificity,
            d.target_id AS target_id,
            w.target_type,
            w.wkno AS target_name,
            w.name AS target_person_name,
            w.post AS target_category,
            d.dept_id
        FROM
            vdm.bdm_wearable_device AS d
                JOIN vdm.bdm_worker AS w ON d.target_id = w.id
                AND d.target_type = w.target_type
        WHERE
            d.device_type = 2
          AND d.deleted = 0
          AND w.deleted =0
          AND d.iot_protocol=2
    </select>

    <select id="queryWearableVisitorList" resultType="com.xh.vdm.mqttOfficial.entity.WearableWorker">
        SELECT
            d.id AS device_id,
            d.device_type,
            d.device_num,
            d.model AS device_model,
            d.unique_id AS device_unique_id,
            d.imei AS device_imei,
            d.category AS device_category,
            d.specificity,
            d.target_id AS target_id,
            w.target_type,
            w.id_number AS target_name,
            w.name AS target_person_name,
            w.post AS target_category,
            d.dept_id
        FROM
            vdm.bdm_wearable_device AS d
                JOIN vdm.bdm_visitor AS w ON d.target_id = w.id
                AND d.target_type = w.target_type
        WHERE
            d.device_type = 2
          AND d.deleted = 0
          AND w.deleted =0
          AND d.iot_protocol=2
    </select>

    <select id="queryWearableTemporaryList" resultType="com.xh.vdm.mqttOfficial.entity.WearableWorker">
        SELECT
            d.id AS device_id,
            d.device_type,
            d.device_num,
            d.model AS device_model,
            d.unique_id AS device_unique_id,
            d.imei AS device_imei,
            d.category AS device_category,
            d.specificity,
            d.target_id AS target_id,
            w.target_type,
            w.wkno AS target_name,
            w.name AS target_person_name,
            w.post AS target_category,
            d.dept_id
        FROM
            vdm.bdm_wearable_device AS d
                JOIN vdm.bdm_temporary AS w ON d.target_id = w.id
                AND d.target_type = w.target_type
        WHERE
            d.device_type = 2
          AND d.deleted = 0
          AND w.deleted =0
          AND d.iot_protocol=2
    </select>

    <select id="queryWearableDeviceList" resultType="com.xh.vdm.mqttOfficial.entity.WearableDevice">
        SELECT
               id,
               unique_id,
               imei,
               device_type,
               specificity,
               dept_id,
               category,
               device_num,
               target_id,
               target_type
        FROM
            bdm_wearable_device
        WHERE
            deleted = 0
          AND iot_protocol = 2
    </select>
</mapper>
