package com.xh.vdm.mqttOfficial.entity;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("bdm_raw_log")
public class BdmRawLog implements Serializable {
	private static final long serialVersionUID = 1L;

	@JSONField(name = "id")
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	@JSONField(name = "device_id")
	@TableField(value = "device_id")
	private long deviceId;

	@JSONField(name = "device_type")
	@TableField(value = "device_type")
	private int deviceType;

	@JSONField(name = "device_num")
	@TableField(value = "device_num")
	private String deviceNum;

	@JSONField(name = "direction")
	@TableField(value = "direction")
	private byte direction;

	@JSONField(name = "event")
	@TableField(value = "event")
	private byte event;

	@JsonIgnore
	@TableField(value = "event_time")
	private Date eventTime;


	@JSONField(name = "event_stamp")
	@TableField(exist = false)
	private Long eventStamp;

	@JSONField(name = "content")
	@TableField(value = "content")
	private String content;

	@JSONField(name = "err_code")
	@TableField(value = "err_code")
	private byte errCode;

	@JSONField(name = "reason")
	@TableField(value = "reason")
	private String reason;

	@JSONField(name = "unique_id")
	@TableField(value = "unique_id")
	private String uniqueId;

	@JSONField(name = "dept_id")
	@TableField(value = "dept_id")
	private long deptId;

	@JSONField(name = "iot_protocol")
	@TableField(value = "iot_protocol")
	private byte iotProtocol; //1-jtt808,2-mqtt，3-北斗短报文终端，4-北斗监测终端，5-北斗授时终端
}
