package com.xh.vdm.mqttOfficial.constant;

import org.bouncycastle.crypto.engines.SM2Engine;

public enum ModeTypeEnum {

	BASE_MODE("base", SM2Engine.Mode.C1C3C2),
	BC_MODE("bc", SM2Engine.Mode.C1C2C3);

	private String type;
	private SM2Engine.Mode mode;

	ModeTypeEnum (String type, SM2Engine.Mode mode) {
		this.setType(type);
		this.setMode(mode);
	}

	public String getType () {
		return this.type;
	}

	public SM2Engine.Mode getMode () {
		return this.mode;
	}

	public void setType (String type) {
		this.type = type;
	}

	public void setMode (SM2Engine.Mode mode) {
		this.mode = mode;
	}
}
