package com.xh.vdm.mqttOfficial.config;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttOfficial.entity.WearableDevice;
import com.xh.vdm.mqttOfficial.entity.WearableWorker;
import com.xh.vdm.mqttOfficial.mapper.WearableDeviceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class WearableWorkerCache {
    public static ConcurrentHashMap<String, WearableWorker> WearableWorkerMap = new ConcurrentHashMap<>();
	public static ConcurrentHashMap<String, WearableDevice> WearableDeviceMap = new ConcurrentHashMap<>();
    @Resource
    WearableDeviceMapper workerMapper;
    @PostConstruct
    public void initWearableWorkerCacheConstruct(){
        initWearableWorkerCache();
    }
    public void initWearableWorkerCache(){
		long start = System.currentTimeMillis();
        List<WearableWorker> list = workerMapper.queryWearableWorkerList();
		List<WearableWorker> listTemporary = workerMapper.queryWearableTemporaryList();
		List<WearableWorker> listVisitor = workerMapper.queryWearableVisitorList();
		list.addAll(listTemporary);
		list.addAll(listVisitor);
        ConcurrentHashMap<String, WearableWorker> map = new ConcurrentHashMap<>();
        for(WearableWorker worker:list){
            map.put(worker.getDeviceUniqueId(),worker);
        }
		WearableWorkerMap = map;
		List<WearableDevice> deviceList=workerMapper.queryWearableDeviceList();
		ConcurrentHashMap<String, WearableDevice> deviceMap = new ConcurrentHashMap<>();
		for(WearableDevice device:deviceList){
			deviceMap.put(device.getUniqueId(),device);
		}
		WearableDeviceMap=deviceMap;
		long executionTime = System.currentTimeMillis() - start;
        log.info("initWearableWorkerCache耗时:{},WearableWorkerMap长度:{},WearableDeviceMap长度:{}",executionTime,WearableWorkerMap.size(),WearableWorkerMap.size());
    }
}
