package com.xh.vdm.mqttOfficial.dto;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 实时数据推送（实时报警推送）
 * 实时数据从手表上报到Cat1手表后台
 * 示例1：仅上报定位。上报无加密经纬度，不上报海拔、速度、方向，上报电池电量99
 * 1680106907,116.242132,23.260342,,,,1,0,99,,,,,
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RealTimeDataReportMsg {
	/**
	 * 时间戳，秒精度
	 */
	private Long ct;
	/**
	 * 经度，单位度，至少精确到小数点后6位
	 * 不同坐标下，也可以为x轴坐标，单位m
	 * typ为4 蓝牙信标时，此项可为空
	 */
	private Double lng;

	/**
	 * 纬度，单位度，至少精确到小数点后6位
	 * 不同坐标下，也可以为y轴坐标，单位m
	 * typ为4 蓝牙信标时，此项可为空
	 */
	private Double lat;

	/**
	 * 海拔，单位m，保留1位小数
	 * 不同坐标下，也可以为z轴坐标，单位m
	 */
	private Double alt;

	/**
	 * 速度，单位km/h
	 */
	private Double spd;
	/**
	 * 方向
	 */
	private Double dir;
	/**
	 * 是否新定位：1是0否
	 */
	private Integer nloc;
	/**
	 * 定位类型
	 * 0 WGS84坐标（无偏，不加密）（默认值）
	 * 1 GCJ02坐标（偏移，加密）
	 * 2 LBS定位
	 * 3 惯导
	 * 4 蓝牙信标
	 * 5 wifi定位
	 */
	private Integer typ;
	/**
	 * 电池余量，100为满电
	 */
	private Integer bat;
	/**
	 * bit0:0-正常，1-SOS报警
	 * bit1:0-佩戴，1-未佩戴
	 * bit2:0-正常，1-心率异常报警
	 * bit3:0-正常，1-血氧异常提醒
	 * bit4:0-正常，1-区域报警
	 * bit5:0-正常，1-离开区域报警
	 * bit6:0-正常，1-进入蓝牙信标报警
	 * bit7:0-正常，1-离开蓝牙信标报警
	 * bit8:0-正常，1-跌落（撞击）报警
	 * bit9:0-正常，1-静默报警
	 * bit10:0-正常，1-近电报警
	 * bit11:0-正常，1-登高报警
	 * bit12:0-正常，1-温度报警
	 * bit13:0-正常，1-体温报警
	 * bit14:0-正常，1-脱帽报警
	 * bit[15:31]:保留
	 */
	private Integer alm;
	/**
	 * 心率数据heartRate
	 */
	private Integer hr;
	/**
	 * 脉搏血氧饱和度pulse oxygen saturation (SPO2)
	 */
	private Integer spo2;
	/**
	 * 蓝牙信标数据由6字节MAC地址+1字节信号强度+1字节电量组成，多组数据间用分号“;”分割
	 */
	private String bluetooth;
	/**
	 * 事件定义类型：
	 * 1 休眠
	 * 2 开机
	 * 3 手动关机
	 */
	private Integer event;
	//通信信号强度
	private Integer wireless;
	//可见卫星颗数
	private Integer gnssNum;
	/**
	 * 卫星信息
	 */
	@JSONField(name="sate_hex_des")
	private String sateHexDes;
	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
	private Date dateTime;
}
