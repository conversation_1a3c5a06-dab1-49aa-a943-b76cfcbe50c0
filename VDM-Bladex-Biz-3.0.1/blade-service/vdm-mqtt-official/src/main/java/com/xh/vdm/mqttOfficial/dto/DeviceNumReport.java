package com.xh.vdm.mqttOfficial.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 赋码参数上报
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceNumReport {
	/**
	 *设备类别标识
	 * M：模组，T：终端
	 */
	@JsonProperty("device_category")
	@JSONField(name = "device_category")
	private String deviceCategory;
	/**
	 *生产厂商编号
	 * 0000~9999
	 */
	@JsonProperty("manufacturer_id")
	@JSONField(name = "manufacturer_id")
	private String manufacturerId;
	/**
	 *产品型号
	 * 12为字符编码，由数字和字母组成
	 */
	@JsonProperty("product_model")
	@JSONField(name = "product_model")
	private String productModel;
	/**
	 *产品序列号
	 * SN:16位长度字符编码，由数字和字母组成，包括4位设备类型、6位生产日期、5位批次顺序号、1位校验位
	 */
	@JsonProperty("product_serial_number")
	@JSONField(name = "product_serial_number")
	private String productSerialNumber;
	/**
	 *IMEI号
	 *移动通信设备的唯一编码
	 */
	@JsonProperty("imei")
	@JSONField(name = "imei")
	private String imei;
	/**
	 *北斗芯片序列号
	 *芯片厂家出厂唯一序列号
	 */
	@JsonProperty("bd_chip_serial_number")
	@JSONField(name = "bd_chip_serial_number")
	private String bdChipSerialNumber;

	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
	private Date dateTime;
}
