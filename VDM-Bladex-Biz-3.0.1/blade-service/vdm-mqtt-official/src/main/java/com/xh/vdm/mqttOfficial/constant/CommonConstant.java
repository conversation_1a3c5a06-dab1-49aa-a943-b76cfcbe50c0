package com.xh.vdm.mqttOfficial.constant;

public interface CommonConstant {
    interface MqttConstant {
        int QOS_0 = 0;
        int QOS_1 = 1;
        int QOS_2 = 2;
        String FRAME_HEADER_EB90 ="EB90";
        String FRAME_HEADER_EB91 ="EB91";
        String FRAME_HEADER_EB92 ="EB92";
        String SEPARATOR = ",";
        String PLUS_SIGN = "+";
        String SLASH = "/";
        String COLON = ":";
        String EMPTY = "";
        String POINT = ".";
        String PLACEHOLDER = "%s";
        String MQTT_VERSION_3_1_1 = "3.1.1";
        String MQTT_VERSION_5 = "5.0";

		int UpgradePacketLen=12*1024;

		String MQTT_FREQUENCY="MQTT:FREQUENCY:";

		String MQTT_UPGRADE_VERSION="MQTT:UPGRADE:VERSION:";
    }
    interface RedisConstant {
        String MQTT_WATCH="MQTT:DEVICE_NUM:WATCH:";
    }
    interface LocConstant {
        String LOC_WATCH="Loc:Wearable:";
		//上线情况
		String OnLineState = "OnLineState:Wearable:";
		//今日在线情况
		String OnLineTodayWearable = "OnLineToday:Wearable:";
		//OnLineStateDelay 上线延迟定位信息
		String OnLineStateDelay = "OnLineState:Delay:";
		//哈希实时状态定位信息
		String RealLoc="Real:Loc";

		String RealDevSte="Real:DevSte";

		String TodayOnline="Today:Online:";
    }
}
