package com.xh.vdm.mqttOfficial.mqtt.handler.watch;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xh.vdm.mqttOfficial.config.MqttConfig;
import com.xh.vdm.mqttOfficial.constant.CommEnum;
import com.xh.vdm.mqttOfficial.constant.CommonConstant;
import com.xh.vdm.mqttOfficial.dto.*;
import com.xh.vdm.mqttOfficial.entity.WearableWorker;
import com.xh.vdm.mqttOfficial.kafka.KafkaProducerService;
import com.xh.vdm.mqttOfficial.mqtt.client.IMqttClient;
import com.xh.vdm.mqttOfficial.mqtt.handler.IMqttMessageHandlerV5;
import com.xh.vdm.mqttOfficial.mqtt.handler.MessageHandlerAbst;
import com.xh.vdm.mqttOfficial.util.PostGre;
import com.xh.vdm.mqttOfficial.util.StringUtil;
import com.xh.vdm.mqttOfficial.util.TopicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.xh.vdm.mqttOfficial.config.WearableWorkerCache.WearableWorkerMap;
import static com.xh.vdm.mqttOfficial.util.CoordinateTransform.wgs84ToGcj02;

@Slf4j
@Component
public class ReportDataMessageHandler extends MessageHandlerAbst implements IMqttMessageHandlerV5 {
    /**
     * 设备号位置
     */
    private final int  deviceIndex;
    private static String deviceId;
    @Autowired
    StringRedisTemplate redisTemplate;
    @Autowired
    KafkaProducerService kafkaProducerService;
	@Resource
	PostGre postGre;
    public ReportDataMessageHandler(List<IMqttClient> messageClientList, MqttConfig mqttConfig) {
        super();
        this.setSubTopic(mqttConfig.getTopic().getSubscription().getPositionReport());
        this.deviceIndex = Arrays.asList(subTopic.split(CommonConstant.MqttConstant.SLASH)).indexOf(CommonConstant.MqttConstant.PLUS_SIGN);
        messageClientList.forEach(client -> {
            addClient(client.version(), client);
            client.addMessageHandler(this);
        });
    }
    @Override
    public void process(String topic, String msg) {
        deviceId = topic.split(CommonConstant.MqttConstant.SLASH)[deviceIndex];
		postGre.saveLog(deviceId,(byte)6,(byte)0,topic.concat(":").concat(msg));
        List<RealTimeDataReportMsg> dtoList = analysisMessageToList(msg);
        log.info("ReportDataMessageHandler process received deviceId：{},protocol:{}, msg: {}", deviceId,msg, JSON.toJSONString(dtoList));
    }
    private List<RealTimeDataReportMsg> analysisMessageToList(String msg) {
        List<RealTimeDataReportMsg> list = new ArrayList<>();
        String[] params = msg.split(CommonConstant.MqttConstant.SEPARATOR);
        if (params.length<14){
            log.error("analysisMessageToList [ERROR] 位置推送消息参数小于14个:{}",params.length);
            return list;
        }
        RealTimeDataReportMsg dto=new RealTimeDataReportMsg();
        dto.setCt(params[0] == null? 0L: Long.parseLong(params[0]));
        dto.setLng(params[1] == null? 0F:Double.parseDouble(params[1]));
        dto.setLat(params[2] == null? 0F:Double.parseDouble(params[2]));
        dto.setAlt(params[3] == null? 0F:Double.parseDouble(params[3]));
        dto.setSpd(params[4] == null? 0F:Double.parseDouble(params[4]));
        dto.setDir(params[5] == null? 0F:Double.parseDouble(params[5]));
        dto.setNloc(params[6] == null? 0:(int)Long.parseLong(params[6]));
        dto.setTyp(params[7] == null? 0:(int)Long.parseLong(params[7]));
        dto.setBat(params[8] == null? 0:(int)Long.parseLong(params[8]));
        dto.setAlm(params[9] == null? 0:(int)Long.parseLong(params[9]));
        dto.setHr(params[10] == null? 0:(int)Long.parseLong(params[10]));
        dto.setSpo2(params[11] == null? 0:(int)Long.parseLong(params[11]));
        dto.setBluetooth(params[12] == null? "-":params[12]);
        dto.setEvent(params[13] == null? 0:(int)Long.parseLong(params[13]));
		dto.setWireless(params[14]==null?0:Integer.parseInt(params[14]));
		dto.setGnssNum(params[15]==null?0:Integer.parseInt(params[15]));
		List<Satellite> sateList=new ArrayList<>();
        if (params.length==17){
            dto.setSateHexDes(params[14] == null? "":params[14]);
			sateList= analysisSatesInfo(dto.getSateHexDes());
        }
		if (dto.getLat()<1||dto.getLng()<1){
			return list;
		}
		sendReportDataToKafka(dto,sateList);
        list.add(dto);
        return list;
    }
    public void sendReportDataToKafka(RealTimeDataReportMsg msg, List<Satellite> sateList){
        Track track=new Track();
        WearableWorker wearableWorker=WearableWorkerMap.get(deviceId);
        if (wearableWorker==null){
            log.error("WearableWorkerMap中没有获取到设备工人信息:{}",deviceId);
            return;
        }
        track.setDeviceNo(wearableWorker.getDeviceUniqueId());
        track.setDeviceId(wearableWorker.getDeviceId());
        track.setDeviceModel(wearableWorker.getDeviceModel());
        track.setDeviceType(wearableWorker.getDeviceType());
        track.setDeviceNum(wearableWorker.getDeviceNum());
        track.setDeviceUniqueId(wearableWorker.getDeviceUniqueId());
        track.setTargetId(wearableWorker.getTargetId());
        track.setTargetType(wearableWorker.getTargetType());
        track.setTargetName(wearableWorker.getTargetName());
        track.setDeptId(wearableWorker.getDeptId());
        track.setLocTime(msg.getCt());
        track.setRecvTime(System.currentTimeMillis()/1000);
        track.setLocTimeF(new Date(msg.getCt()));
        track.setRecvTimeF(new Date(System.currentTimeMillis()));
        track.setLongitude(msg.getLng());
        track.setLatitude(msg.getLat());
        track.setAltitude(msg.getAlt().intValue());
        track.setSpeed(msg.getSpd());
        track.setBearing(msg.getDir().intValue());
        track.setAlarmFlag(msg.getAlm());
        track.setValid(1);
		track.setPosSystem(msg.getTyp());
		track.setIotProtocol((short)2);
		track.setWireless(msg.getWireless());
		track.setGnssNum(msg.getGnssNum());
        TrackAux trackAux=new TrackAux();
        trackAux.setBluetooth(msg.getBluetooth());
        trackAux.setBloodOxygen(msg.getSpo2());
        trackAux.setBatteryLevel(msg.getBat());
        trackAux.setHeartRate(msg.getHr());
        trackAux.setLocationType(msg.getTyp());
        trackAux.setEventType(msg.getEvent());
		trackAux.setWireless(msg.getWireless());
		trackAux.setSateNum(msg.getGnssNum());
        track.setAuxStr(JSON.toJSONString(trackAux));
        HashMap<String, Object> auxMap=new HashMap<>();
        auxMap.put("255",sateList);
        track.setAuxsNormal(auxMap);
        //经纬坐标转换
//        if (msg.getTyp()==0){
//            double[] cors=wgs84ToGcj02(msg.getLat(),msg.getLng());
//            track.setLatitude(cors[0]);
//            track.setLongitude(cors[1]);
//        }
        kafkaProducerService.sendPositionReportMessage(JSON.toJSONString(track),track.getDeviceUniqueId());
		redisTemplate.opsForHash().put(CommonConstant.LocConstant.RealLoc,String.valueOf(track.getDeviceId()),JSON.toJSONString(track));

		Object devObj =redisTemplate.opsForHash().get(CommonConstant.LocConstant.RealDevSte,wearableWorker.getDeviceId()+"");
		if (devObj!=null){
			BdmDevSte devSteHas=JSONObject.parseObject(devObj.toString(), BdmDevSte.class);
			devSteHas.setUpdateTime(new Date());
			devSteHas.setLongitude(track.getLongitude());
			devSteHas.setLatitude(track.getLatitude());
			if (track.getSpeed()>0){
				devSteHas.setKineState(1);
			}
			redisTemplate.opsForHash().put(CommonConstant.LocConstant.RealDevSte,String.valueOf(wearableWorker.getDeviceId()),JSON.toJSONString(devSteHas));
		}
//		//redisTemplate.expire(CommonConstant.LocConstant.OnLineState+deviceId, 1000, TimeUnit.SECONDS);
//		// 获取当前时间
//		LocalDateTime now = LocalDateTime.now();
//		// 获取明日零点的时间
//		LocalDateTime tomorrowMidnight = now.plusDays(1).truncatedTo(ChronoUnit.DAYS);
//		// 计算当前时间到明日零点的时间差
//		Duration duration = Duration.between(now, tomorrowMidnight);
//		redisTemplate.opsForValue().set(CommonConstant.LocConstant.OnLineTodayWearable+deviceId,JSON.toJSONString(onLineState),duration);
    }
    public static List<Satellite> analysisSatesInfo(String satesStr){
        List<Satellite> list = new ArrayList<>();
        List<String> listSateStr=StringUtil.splitString(satesStr,14);
        for (String s : listSateStr) {
            list.add(analysisSateInfo(s));
        }
        return list;
    }
    public static Satellite analysisSateInfo(String sateStr){
        Satellite sate=new Satellite();
        if (sateStr.length()!=14){
            return sate;
        }
        sate.setType(Integer.parseInt(sateStr.substring(0,2),16));
        sate.setId(Integer.parseInt(sateStr.substring(2,4),16));
        sate.setElevation(Integer.parseInt(sateStr.substring(4,6),16));
        sate.setAzimuth(Integer.parseInt(sateStr.substring(6,10),16));
        sate.setRatio(Integer.parseInt(sateStr.substring(10,12),16));
        sate.setFlag(Integer.parseInt(sateStr.substring(12,14),16));
        return sate;
    }
    @Override
    public boolean match(String topic) {
        return TopicUtils.math(subTopic, topic, deviceIndex);
    }
}
