package com.xh.vdm.mqttOfficial.dto;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NoticeBroadcast {
    /**
     *
     */
    private String messageIdentifier;  // 报文标识
    /**
     *
     */
    private String userNumber;         // 用户工号
    /**
     *设备唯一编号
     */
    private String deviceNumber;       // 设备号
    /**
     *
     */
    private String longitude;          // 经度
    /**
     *
     */
    private String latitude;           // 纬度
    /**
     *
     */
    private String altitude;           // 海拔
    /**
     *
     */
    private int speed;                 // 速度
    /**
     *方向
     */
    private String direction;          // 方向
    /**
     *单位：秒
     */
    private String time;               // 时间
    /**
     *广播音频文件
     */
    private byte[] audioFile;          // 音频文件
    /**
     *
     */
    private int crcChecksum;           // CRC 校验
}
