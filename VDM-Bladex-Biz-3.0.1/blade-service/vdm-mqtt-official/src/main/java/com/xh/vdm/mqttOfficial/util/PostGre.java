package com.xh.vdm.mqttOfficial.util;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttOfficial.entity.BdmCmdRecordNew;
import com.xh.vdm.mqttOfficial.entity.BdmRawLog;
import com.xh.vdm.mqttOfficial.entity.WearableDevice;
import com.xh.vdm.mqttOfficial.entity.WearableWorker;

import static com.xh.vdm.mqttOfficial.config.WearableWorkerCache.WearableDeviceMap;
import static com.xh.vdm.mqttOfficial.config.WearableWorkerCache.WearableWorkerMap;

import com.xh.vdm.mqttOfficial.kafka.KafkaProducerService;
import com.xh.vdm.mqttOfficial.mapper.BdmCmdRecordNewMapper;
import com.xh.vdm.mqttOfficial.mapper.BdmRawLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Component
@Repository
public class PostGre {
	@Resource
	private BdmRawLogMapper bdmRawLogMapper;
	@Autowired
	KafkaProducerService kafkaProducerService;
	@Resource
	private BdmCmdRecordNewMapper bdmCmdRecordNewMapper;
	public int saveLog(String uniqueId,byte event,byte direction,String content){
		WearableDevice device=WearableDeviceMap.get(uniqueId);
		BdmRawLog rawLog=new BdmRawLog();
		if (device==null){
			log.error("saveLog WearableWorkerMap中没有获取到设备工人信息:{}",uniqueId);
		}else {
			rawLog.setDeviceId(device.getId());
			rawLog.setDeviceType(device.getDeviceType());
			rawLog.setDeptId(device.getDeptId());
			rawLog.setDeviceNum(device.getDeviceNum());
		}
		rawLog.setId((long)0);
		rawLog.setContent(content);
		rawLog.setDirection(direction);
		rawLog.setEvent(event);
		rawLog.setEventTime(new Date());
		rawLog.setEventStamp(System.currentTimeMillis()/1000);
		rawLog.setIotProtocol((byte)2);
		rawLog.setUniqueId(uniqueId);
		rawLog.setReason("");
		kafkaProducerService.sendRawLogMessage(JSON.toJSONString(rawLog),rawLog.getUniqueId());
		//return bdmRawLogMapper.insert(rawLog);
		return 0;
	}

	public int savePacketLog(String uniqueId,byte event,byte direction,String content){
		BdmRawLog rawLog=new BdmRawLog();
		rawLog.setId((long)0);
		rawLog.setContent(content);
		rawLog.setDirection(direction);
		rawLog.setEvent(event);
		rawLog.setEventTime(new Date());
		rawLog.setEventStamp(System.currentTimeMillis()/1000);
		rawLog.setIotProtocol((byte)2);
		rawLog.setUniqueId(uniqueId);
		rawLog.setReason("");
		kafkaProducerService.sendRawLogMessage(JSON.toJSONString(rawLog),rawLog.getUniqueId());
		//return bdmRawLogMapper.insert(rawLog);
		return 0;
	}

	public int saveCmdRecord(String uniqueId,String content,String type){
		WearableDevice device=WearableDeviceMap.get(uniqueId);
		if (device==null){
			log.error("saveCmdRecord WearableWorkerMap中没有获取到设备工人信息:{}",uniqueId);
			return 0;
		}
		BdmCmdRecordNew record=new BdmCmdRecordNew();
		record.setCreateTime(new Date());
		record.setParam(content);
		record.setType(type);
		record.setDeptId(device.getDeptId());
		record.setDeviceNum(device.getDeviceNum());
		record.setDeviceCategory("北斗智能手表/环");
		record.setDeviceUniqueId(uniqueId);
		WearableWorker target=WearableWorkerMap.get(uniqueId);
		record.setTargetName(target.getTargetName());
		return bdmCmdRecordNewMapper.insert(record);
	}
}
