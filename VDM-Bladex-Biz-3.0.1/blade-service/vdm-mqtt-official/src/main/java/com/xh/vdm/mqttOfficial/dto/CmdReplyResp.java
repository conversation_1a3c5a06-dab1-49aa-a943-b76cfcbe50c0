package com.xh.vdm.mqttOfficial.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmdReplyResp {
	@JsonProperty("reply")
	@JSONField(name = "reply")
	private String reply;
	/**
	 * 终端唯一编号
	 */
	@JsonProperty("unique_id")
	@JSONField(name = "unique_id")
	private String uniqueId;
}
