package com.xh.vdm.mqttOfficial.kafka;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttOfficial.config.WearableWorkerCache;
import com.xh.vdm.mqttOfficial.entity.WearableWorker;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class KafkaConsumerService {
    @Autowired
    WearableWorkerCache wearableWorkerCache;
    //@KafkaListener(topics = "device_target_change_topic", groupId = "mqtt_official_device_target_change_watch")
	@KafkaListener(topics = "device_target_change_topic",
		groupId = "#{'mqtt_official_device_target_change_watch_' + T(java.net.InetAddress).getLocalHost().getHostAddress().replace('.', '_')}")
    public void listenWearableWorker(String msg) {
        WearableWorker wearableWorker=JSON.parseObject(msg, WearableWorker.class);
        log.info("kafka listenWearableWorker listen receive:{},{}",wearableWorker.getDeviceUniqueId(),msg);
        wearableWorkerCache.initWearableWorkerCache();
    }
}

