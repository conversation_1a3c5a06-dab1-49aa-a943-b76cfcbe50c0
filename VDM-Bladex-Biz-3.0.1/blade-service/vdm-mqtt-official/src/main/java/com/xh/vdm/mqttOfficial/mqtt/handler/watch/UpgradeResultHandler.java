package com.xh.vdm.mqttOfficial.mqtt.handler.watch;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttOfficial.config.MqttConfig;
import com.xh.vdm.mqttOfficial.constant.CommonConstant;
import com.xh.vdm.mqttOfficial.dto.RealTimeDataReportMsg;
import com.xh.vdm.mqttOfficial.kafka.KafkaProducerService;
import com.xh.vdm.mqttOfficial.mqtt.client.IMqttClient;
import com.xh.vdm.mqttOfficial.mqtt.handler.IMqttMessageHandlerV5;
import com.xh.vdm.mqttOfficial.mqtt.handler.MessageHandlerAbst;
import com.xh.vdm.mqttOfficial.util.PostGre;
import com.xh.vdm.mqttOfficial.util.TopicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class UpgradeResultHandler extends MessageHandlerAbst implements IMqttMessageHandlerV5 {
	private final int  deviceIndex;
	private static String deviceId;
	@Autowired
	StringRedisTemplate redisTemplate;
	@Autowired
	KafkaProducerService kafkaProducerService;
	@Resource
	PostGre postGre;
	public UpgradeResultHandler(List<IMqttClient> messageClientList, MqttConfig mqttConfig) {
		super();
		this.setSubTopic(mqttConfig.getTopic().getSubscription().getOtaResult());
		this.deviceIndex = Arrays.asList(subTopic.split(CommonConstant.MqttConstant.SLASH)).indexOf(CommonConstant.MqttConstant.PLUS_SIGN);
		messageClientList.forEach(client -> {
			addClient(client.version(), client);
			client.addMessageHandler(this);
		});
	}
	@Override
	public void process(String topic, String msg) {
		deviceId = topic.split(CommonConstant.MqttConstant.SLASH)[deviceIndex];
		postGre.saveLog(deviceId,(byte)14,(byte)0,topic.concat(":").concat(msg));
		log.info("UpgradeResultHandler process received deviceId：{},protocol:{}", deviceId,msg);
	}

	@Override
	public boolean match(String topic) {
		return TopicUtils.math(subTopic, topic, deviceIndex);
	}
}
