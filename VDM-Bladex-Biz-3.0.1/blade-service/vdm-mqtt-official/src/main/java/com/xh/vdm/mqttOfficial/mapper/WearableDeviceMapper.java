package com.xh.vdm.mqttOfficial.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.mqttOfficial.entity.WearableDevice;
import com.xh.vdm.mqttOfficial.entity.WearableWorker;
import java.util.List;

public interface WearableDeviceMapper extends BaseMapper<WearableDevice> {
    List<WearableWorker> queryWearableWorkerList();
	List<WearableWorker> queryWearableVisitorList();
	List<WearableWorker> queryWearableTemporaryList();
	List<WearableDevice> queryWearableDeviceList();
}
