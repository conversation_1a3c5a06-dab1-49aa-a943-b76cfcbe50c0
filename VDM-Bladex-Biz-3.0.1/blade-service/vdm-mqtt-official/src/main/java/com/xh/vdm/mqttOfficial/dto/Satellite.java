package com.xh.vdm.mqttOfficial.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 卫星信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Satellite {
	/**
	 *星座类型
	 *0-BD.1-GPS.2-GL.3-GA
	 */
	@JSONField(name = "type")
	private int type;  // 星座类型
	/**
	 *卫星编号
	 *卫星编号1～200
	 */
	@JSONField(name = "id")
	private int id;    // 卫星编号
	/**
	 *仰角
	 *0°～90°
	 */
	@JSONField(name = "elevation")
	private int elevation;  // 仰角
	/**
	 *方位角
	 *0°～359°
	 */
	@JSONField(name = "azimuth")
	private int azimuth;  // 方位角
	/**
	 *载噪比
	 *0 dBHz～99 dBHz
	 */
	@JSONField(name = "ratio")
	private int ratio; // 载噪比
	/**
	 *定位标识
	 *0-未参与定位，1-参与定位
	 */
	@JSONField(name = "flag")
	private int flag;  // 定位标识
}
