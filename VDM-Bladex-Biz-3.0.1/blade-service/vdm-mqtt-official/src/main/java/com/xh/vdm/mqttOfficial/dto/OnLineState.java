package com.xh.vdm.mqttOfficial.dto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.Date;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OnLineState {
	@JSONField(name = "device_num")
	private String deviceNum;
	@JSONField(name = "device_id")
	private Long deviceId;
	@JSONField(name = "device_type")
	private Integer deviceType;
	@JSONField(name = "target_type")
	private Integer targetType;
	@JSONField(name = "target_id")
	private Long targetId;
	@J<PERSON><PERSON>ield(name = "target_name")
	private String targetName;
	@JSONField(name = "longitude")
	private Double longitude;
	@JSONField(name = "latitude")
	private Double latitude;
	@JSONField(name = "dept_id")
	private Long deptId;
	@JSONField(name = "on_line_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
	private Date onLineTime;
	@JSONField(name = "on_line_time_stamp")
	private Long onLineTimeStamp;
	@JSONField(name = "update_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
	private Date updateTime;
}
