package com.xh.vdm.mqttOfficial.constant;

public enum CommEnum {

	DeviceNumSet("DeviceNumSet","终端赋码"),
	DeviceAuth("DeviceAuth","设备鉴权"),
	DeviceAuthResult("DeviceAuthResult","设备鉴权结果"),
	PositionReport("PositionReport","实时位置推送"),
	PositionBatch("PositionBatch","补传定位数据"),
	WeatherSend("WeatherSend","天气下发"),
	DataSend("DataSend","数据下发"),
	FrequencyControl("FrequencyControl","模式频率控制"),
	RemoteParamSet("RemoteParamSet","远程参数配置"),
	DeviceNumQuery("DeviceNumQuery","赋码参数查询"),
	DeviceNumReport("DeviceNumReport","赋码参数上报"),
	StarFileUpdate("StarFileUpdate","星历文件更新");

	private String key;
	private String value;

	CommEnum(String key, String value) {
		this.setKey(key);
		this.setValue(value);
	}

	public String getKey () {
		return this.key;
	}

	public void setKey (String key) {
		this.key = key;
	}

	public String getValue () {
		return this.value;
	}

	public void setValue (String value) {
		this.value = value;
	}

	public static CommEnum getByValue (String value) {
		CommEnum[] deviceCateList = values();
		for (CommEnum deviceCate : deviceCateList) {
			if (deviceCate.getValue().equals(value)) {
				return deviceCate;
			}
		}
		return null;
	}

	public static CommEnum getByKey (String key) {
		CommEnum[] deviceCateList = values();
		for (CommEnum deviceCate : deviceCateList) {
			if (deviceCate.getKey().equals(key)) {
				return deviceCate;
			}
		}
		return null;
	}

	public static String getKeyByValue (String value) {
		CommEnum[] deviceCateList = values();
		for (CommEnum deviceCate : deviceCateList) {
			if (deviceCate.getValue().equals(value)) {
				return deviceCate.getKey();
			}
		}
		return "";
	}
}
