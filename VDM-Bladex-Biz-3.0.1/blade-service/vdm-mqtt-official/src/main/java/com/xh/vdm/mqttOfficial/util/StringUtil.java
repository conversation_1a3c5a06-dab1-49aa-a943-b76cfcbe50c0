package com.xh.vdm.mqttOfficial.util;

import com.xh.vdm.mqttOfficial.dto.Satellite;

import java.util.ArrayList;
import java.util.List;

public class StringUtil {
    public static List<String> splitString(String input, int length) {
        int index = 0;
        List<String> list= new ArrayList<>();
        while (index < input.length()) {
            list.add(input.substring(index, Math.min(index + length, input.length())));
            index += length;
        }
        return list;
    }

	public static String bytesToHex(byte[] bytes) {
		StringBuilder result = new StringBuilder();
		for (byte b : bytes) {
			result.append(String.format("%02x", b));
		}
		return result.toString();
	}

	public static byte[] hexStringToByteArray(String hex) {
		int len = hex.length();
		byte[] data = new byte[len / 2];
		for (int i = 0; i < len; i += 2) {
			data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
				+ Character.digit(hex.charAt(i + 1), 16));
		}
		return data;
	}
}
