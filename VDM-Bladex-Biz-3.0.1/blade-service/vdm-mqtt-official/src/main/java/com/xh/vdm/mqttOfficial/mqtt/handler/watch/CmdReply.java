package com.xh.vdm.mqttOfficial.mqtt.handler.watch;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttOfficial.config.MqttConfig;
import com.xh.vdm.mqttOfficial.constant.CommonConstant;
import com.xh.vdm.mqttOfficial.dto.CmdReplyResp;
import com.xh.vdm.mqttOfficial.kafka.KafkaProducerService;
import com.xh.vdm.mqttOfficial.mqtt.client.IMqttClient;
import com.xh.vdm.mqttOfficial.mqtt.handler.IMqttMessageHandlerV5;
import com.xh.vdm.mqttOfficial.mqtt.handler.MessageHandlerAbst;
import com.xh.vdm.mqttOfficial.util.PostGre;
import com.xh.vdm.mqttOfficial.util.TopicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
@Slf4j
@Component
public class CmdReply extends MessageHandlerAbst implements IMqttMessageHandlerV5 {
	private final int  deviceIndex;
	private static String deviceId;
	@Autowired
	StringRedisTemplate redisTemplate;
	@Autowired
	KafkaProducerService kafkaProducerService;
	@Resource
	PostGre postGre;
	public CmdReply(List<IMqttClient> messageClientList, MqttConfig mqttConfig) {
		super();
		this.setSubTopic(mqttConfig.getTopic().getSubscription().getCmdReply());
		this.deviceIndex = Arrays.asList(subTopic.split(CommonConstant.MqttConstant.SLASH)).indexOf(CommonConstant.MqttConstant.PLUS_SIGN);
		messageClientList.forEach(client -> {
			addClient(client.version(), client);
			client.addMessageHandler(this);
		});
	}
	@Override
	public void process(String topic, String msg) {
		deviceId = topic.split(CommonConstant.MqttConstant.SLASH)[deviceIndex];
		postGre.saveLog(deviceId,(byte)14,(byte)0,topic.concat(":").concat(msg));
		log.info("CmdReply process received deviceId：{},protocol:{}", deviceId,msg);
		CmdReplyResp cmdReplyResp=new CmdReplyResp();
		cmdReplyResp.setUniqueId(deviceId);
		cmdReplyResp.setReply(msg);
		redisTemplate.opsForValue().set(CommonConstant.MqttConstant.MQTT_FREQUENCY+deviceId, JSON.toJSONString(cmdReplyResp));
	}

	@Override
	public boolean match(String topic) {
		return TopicUtils.math(subTopic, topic, deviceIndex);
	}
}
