package com.xh.vdm.mqttOfficial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("bdm_wearable_device")
public class WearableDevice implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "unique_id")
    private String uniqueId;

    @TableField(value = "imei")
    private String imei;

    @TableField(value = "model")
    private String model;

    @TableField(value = "vendor")
    private String vendor;

    @TableField(value = "bd_chip_sn")
    private String bdChipSn;

    @TableField(value = "device_type")
    private Short deviceType;

    @TableField(value = "specificity")
    private Short specificity;

    @TableField(value = "dept_id")
    private Long deptId;

    @TableField(value = "activated")
    private Short activated;

    @TableField(value = "category")
    private Short category;

    @TableField(value = "device_num")
    private String deviceNum;

    @TableField(value = "installdate")
    private Date installDate;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

    @TableField(value = "deleted")
    private Short deleted;

    @TableField(value = "scenario")
    private Short scenario;

    @TableField(value = "domain")
    private Short domain;

    @TableField(value = "gnss_mode")
    private Short gnssMode;

    @TableField(value="target_id")
    private Long targetId;

    @TableField(value="target_type")
    private Short targetType;
}
