package com.xh.vdm.mqttOfficial.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RedisService {
    @Autowired
    StringRedisTemplate redisTemplate;
    public void test(){
        try{
            String val = redisTemplate.opsForValue().get("Loc:TerminalRealLoc:18321237001");
            log.info("TEST RedisService:{}",val);
        }catch (Exception e){
            e.printStackTrace();
        }

    }
}

