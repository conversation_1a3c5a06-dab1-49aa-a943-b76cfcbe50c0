package com.xh.vdm.mqttOfficial.dto;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BdmDevSte {
	@JSONField(name = "id")
	private Long id; // 主键，唯一标识码

	@JSONField(name = "device_id")
	private Long deviceId; // 终端设备id

	@JSONField(name = "device_type")
	private Integer deviceType; // 终端设备类型

	@JSONField(name = "unique_id")
	private String uniqueId; // 终端编号

	@JSONField(name = "device_num")
	private String deviceNum; // 终端设备赋码值

	@JSONField(name = "target_id")
	private Long targetId; // 监控对象id

	@JSONField(name = "target_type")
	private Integer targetType; // 监控对象类别

	@JSONField(name = "target_name")
	private String targetName; // 监控对象名称

	@JSONField(name = "action")
	private Integer action; // 0-上线，1-下线

	@JSONField(name = "action_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
	private Date actionTime; // 上下线时刻

	@JSONField(name = "fault_count")
	private Short faultCount; // 设备故障数

	@JSONField(name = "dept_id")
	private Long deptId; // 所属单位

	@JSONField(name = "kinestate")
	private Integer kineState; // 运动状态，0-静止，1-运动
	@JSONField(name = "device_category")
	private Integer deviceCategory; // 设备类别

	@JSONField(name = "longitude")
	private Double longitude; // 经度

	@JSONField(name = "latitude")
	private Double latitude; // 纬度

	@JSONField(name = "update_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
	private Date updateTime; // 更新时间，用来检查离线
}
