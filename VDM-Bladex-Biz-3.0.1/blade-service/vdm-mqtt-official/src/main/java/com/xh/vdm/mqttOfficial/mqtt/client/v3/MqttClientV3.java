package com.xh.vdm.mqttOfficial.mqtt.client.v3;

import com.xh.vdm.mqttOfficial.config.MqttConfig;
import com.xh.vdm.mqttOfficial.constant.CommonConstant;
import com.xh.vdm.mqttOfficial.mqtt.client.IMqttClient;
import com.xh.vdm.mqttOfficial.mqtt.handler.IMqttMessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.persist.MqttDefaultFilePersistence;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class MqttClientV3 implements IMqttClient {

    private final MqttClient mqttClient;

    private final MessageCallbackV3 messageCallbackV3;

    public MqttClientV3(MqttConfig mqttConfig) throws MqttException {
        log.info("MqttClientV3 init");
        MqttConnectOptions mqttConnectOptions = new MqttConnectOptions();
        mqttConnectOptions.setMqttVersion(MqttConnectOptions.MQTT_VERSION_3_1_1);
        mqttConnectOptions.setAutomaticReconnect(true);
        mqttConnectOptions.setCleanSession(false);
        mqttConnectOptions.setUserName(mqttConfig.getUsername());
        mqttConnectOptions.setPassword(mqttConfig.getPassword().toCharArray());
        mqttConnectOptions.setKeepAliveInterval(60);
        // 默认地址为jar包所在目录
        MqttDefaultFilePersistence mqttDefaultFilePersistence = new MqttDefaultFilePersistence();
        MqttClient mqttClient = new MqttClient(mqttConfig.getBroker(),
                mqttConfig.getClientId() + CommonConstant.MqttConstant.COLON + version(),
                mqttDefaultFilePersistence
        );
        this.messageCallbackV3 = new com.xh.vdm.mqttOfficial.mqtt.client.v3.MessageCallbackV3(this);
        mqttClient.connectWithResult(mqttConnectOptions);
        mqttClient.setCallback(messageCallbackV3);
        this.mqttClient = mqttClient;
    }

    @Override
    public String version() {
        return CommonConstant.MqttConstant.MQTT_VERSION_3_1_1;
    }

    @Override
    public boolean isConnected() {
        return mqttClient.isConnected();
    }

    @Override
    public boolean reconnect() {
        try {
            if (!isConnected()) {
                mqttClient.reconnect();
            }
            return true;
        } catch (MqttException e) {
            log.error("reconnect error", e);
        }
        return false;
    }

    @Override
    public boolean publishMessage(String topic, byte[] payload, int qos, boolean retained) {
        if (topic == null || topic.trim().length() == 0) {
            throw new IllegalArgumentException("publishMessage topic can not be null or empty");
        }
        log.info("publishMessage {}:{}",topic,new String(payload, StandardCharsets.UTF_8));
        try {
            mqttClient.publish(topic, payload, qos, retained);
            return true;
        } catch (MqttException e) {
            log.error("publishMessage topic -> [{}] error", topic, e);
        }
        return false;
    }

    @Override
    public void addMessageHandler(IMqttMessageHandler messageHandler) {
        messageCallbackV3.addMessageHandler(messageHandler);
    }

    @PreDestroy
    private void destroy() {
        try {
            log.warn("MqttClientV3 destroy");
            mqttClient.disconnect();
            mqttClient.close();
        } catch (MqttException e) {
            log.error("MqttClientV3 destroy error ", e);
        }
    }
}
