package com.xh.vdm.mqttOfficial.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmdControl {
	/**
	 * 配置参数语句
	 */
	@JsonProperty("control")
	@JSONField(name = "control")
	private String control;
	/**
	 * 终端唯一编号
	 */
	@JsonProperty("device_id")
	@JSONField(name = "device_id")
	private Long deviceId;
}
