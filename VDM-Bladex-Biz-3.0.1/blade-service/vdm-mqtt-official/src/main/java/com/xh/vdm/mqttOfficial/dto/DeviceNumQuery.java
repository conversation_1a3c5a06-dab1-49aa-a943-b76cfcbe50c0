package com.xh.vdm.mqttOfficial.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 赋码参数查询
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeviceNumQuery {
	/**
	 * 指令标识
	 */
	@JsonProperty("cmd_flag")
	@JSONField(name = "cmd_flag")
	private String cmdFlag;
	/**
	 * 终端唯一编号
	 */
	@JsonProperty("device_no")
	@JSONField(name = "device_no")
	private String deviceNo;

	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
	private Date dateTime;
}
