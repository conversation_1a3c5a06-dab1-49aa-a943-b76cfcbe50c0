package com.xh.vdm.mqttOfficial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
@Data
@TableName("bd_device_auth")
public class BdDeviceAuth implements Serializable {
	private static final long serialVersionUID = 1L;
	@TableId(value = "id", type = IdType.AUTO)
	private Integer id;
	@TableField(value = "token")
	private String token;
	@TableField(value = "imei")
	private String imei; // Terminal IMEI
	@TableField(value = "device_num")
	private String deviceNum; // 16-byte code value
	@TableField(value = "sign")
	private String sign; // Code signature
	@TableField(value = "local_token")
	private String localToken; // Locally stored token
	@TableField(value = "device_id")
	private Long deviceId;
	@TableField(value = "device_type")
	private Integer deviceType;
	@TableField(value = "device_no")
	private String deviceNo; // Size 30
	@TableField(value = "device_model")
	private String deviceModel;
	@TableField(value = "device_unique_id")
	private String deviceUniqueId;
	@TableField(value = "target_id")
	private Long targetId;
	@TableField(value = "target_type")
	private Integer targetType;
	@TableField(value = "target_name")
	private String targetName; // Size 30
	@TableField(value = "device_num_user")
	private String deviceNumUser; // Size 50
	@TableField(value = "create_time")
	private Date createTime;
}
