package com.xh.vdm.mqttOfficial.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.mqttOfficial.entity.DeviceCode;
import com.xh.vdm.mqttOfficial.mapper.DeviceCodeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DeviceCodeService extends ServiceImpl<DeviceCodeMapper, DeviceCode> {
	public DeviceCode getDeviceCodeByDeviceNum(String deviceNum){
		return this.query().eq("device_num",deviceNum).one();
	}
}
