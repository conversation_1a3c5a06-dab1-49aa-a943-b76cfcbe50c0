package com.xh.vdm.mqttOfficial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;
@Data
@TableName("bdm_device_link")
public class BdmDeviceLink implements Serializable {
	private static final long serialVersionUID = 1L;
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	@TableField(value = "device_id")
	private Long deviceId;
	@TableField(value = "device_type")
	private Integer deviceType ;
	@TableField(value = "unique_id")
	private String uniqueId;
	@TableField(value = "device_num")
	private String deviceNum;
	@TableField(value = "target_id")
	private Long targetId;
	@TableField(value = "target_type")
	private Integer targetType;
	@TableField(value = "target_name")
	private String targetName ;
	@TableField(value = "longitude")
	private Double longitude ;
	@TableField(value = "latitude")
	private Double latitude ;
	@TableField(value = "address")
	private String address ;
	@TableField(value = "action")
	private Short action ; // 0 - Online, 1 - Offline
	@TableField(value = "time")
	private Date time;
	@TableField(value = "remark")
	private String remark ;
	@TableField(value = "dept_id")
	private Long deptId;
}
