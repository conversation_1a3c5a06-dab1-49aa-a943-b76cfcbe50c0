package com.xh.vdm.mqttOfficial.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OtaVersion {
	// 帧类型名
	private String fr;
	// 版本号
	private String version;
	// ufw文件分包总数，默认为0
	private String totalUfw;
	// bin文件分包总数，默认为0
	private String totalBin;
	// 版本信息，默认为0
	private String msg;
	// 时间（精确到毫秒）
	private String ct;
	// crc验证(4B十六进制)，对前6个字段验证
	private String crc;
	private String deviceId;
}
