package com.xh.vdm.mqttOfficial.mqtt.handler.watch;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xh.vdm.mqttOfficial.config.MqttConfig;
import com.xh.vdm.mqttOfficial.constant.CommEnum;
import com.xh.vdm.mqttOfficial.constant.CommonConstant;
import com.xh.vdm.mqttOfficial.dto.*;
import com.xh.vdm.mqttOfficial.entity.WearableWorker;
import com.xh.vdm.mqttOfficial.kafka.KafkaProducerService;
import com.xh.vdm.mqttOfficial.mqtt.client.IMqttClient;
import com.xh.vdm.mqttOfficial.mqtt.handler.IMqttMessageHandlerV5;
import com.xh.vdm.mqttOfficial.mqtt.handler.MessageHandlerAbst;
import com.xh.vdm.mqttOfficial.util.PostGre;
import com.xh.vdm.mqttOfficial.util.StringUtil;
import com.xh.vdm.mqttOfficial.util.TopicUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveRedisOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.xh.vdm.mqttOfficial.config.WearableWorkerCache.WearableWorkerMap;
import static com.xh.vdm.mqttOfficial.util.CoordinateTransform.wgs84ToGcj02;

@Slf4j
@Component
public class BatchPointsMsgHandler extends MessageHandlerAbst implements IMqttMessageHandlerV5 {
    /**
     * 设备号位置
     */
    private final int deviceIndex;
    private static String deviceId;
    @Autowired
    KafkaProducerService kafkaProducerService;
    @Autowired
    StringRedisTemplate redisTemplate;
	@Resource
	PostGre postGre;
    public BatchPointsMsgHandler(List<IMqttClient> messageClientList, MqttConfig mqttConfig) {
        super();
        super.setSubTopic(mqttConfig.getTopic().getSubscription().getPositionBatch());
        this.deviceIndex = Arrays.asList(subTopic.split(CommonConstant.MqttConstant.SLASH)).indexOf(CommonConstant.MqttConstant.PLUS_SIGN);
        messageClientList.forEach(client -> {
            addClient(client.version(), client);
            client.addMessageHandler(this);
        });
    }
    @Override
    public void process(String topic, String msg) {
		log.info("BatchPointsMsgHandler process msg:{},{}",topic,msg);
        deviceId = topic.split(CommonConstant.MqttConstant.SLASH)[deviceIndex];
		postGre.saveLog(deviceId,(byte)7,(byte)0,topic.concat(":").concat(msg));
        if (!msg.startsWith(CommonConstant.MqttConstant.FRAME_HEADER_EB90)){
            log.error("BatchPointsMsgHandler not startsWith EB90:{}",msg);
            return;
        }
        String[] posArr=msg.split(CommonConstant.MqttConstant.FRAME_HEADER_EB90);
        for (String pos:posArr) {
            String posWithFrame=CommonConstant.MqttConstant.FRAME_HEADER_EB90+pos;
			if (posWithFrame.length()>=36){
				//analysisSplitMessageToPos(posWithFrame);
				analysisSplitMessageToPosNew(posWithFrame);
			}else if (pos.length()>0) {
				log.info("分割EB90消息长度小于36:{}",posWithFrame);
			}
            //BatchPointsReportMsg posMsg=analysisSplitMessageToPos(posWithFrame);
            //dtoList.add(posMsg);
        }
    }
    private BatchPointsReportMsg analysisSplitMessageToPos(String msg) {
		log.info("BatchPointsMsgHandler analysisSplitMessageToPos msg:{}",msg);
        BatchPointsReportMsg dto=new BatchPointsReportMsg();
        dto.setFrameHead(Integer.parseInt(msg.substring(0,4),16));
        dto.setValidBit(Byte.parseByte(msg.substring(4,6),16));
        dto.setCt(Integer.parseInt(msg.substring(6,14),16));
        dto.setLng(Integer.parseInt(msg.substring(14,22),16));
        dto.setLat(Integer.parseInt(msg.substring(22,30),16));
        dto.setAlt(Integer.parseInt(msg.substring(30,34),16));
        dto.setLocType(Byte.parseByte(msg.substring(34,36),16));
        switch (dto.getValidBit()){
            case 0x7f:
                //全部数据都要
                if (msg.length()<68){
                    return dto;
                }
                dto.setSpeed(Byte.parseByte(msg.substring(36,38),16));
                dto.setBearing(Integer.parseInt(msg.substring(38,42),16));
                dto.setAlarm(Integer.parseInt(msg.substring(42,50),16));
                dto.setHr(Byte.parseByte(msg.substring(50,52),16));
                dto.setSpo2(Byte.parseByte(msg.substring(52,54),16));
                dto.setBluetooth(msg.substring(54,66));
                dto.setEvent(Byte.parseByte(msg.substring(66,68),16));
                //dto.setObligate(Integer.parseInt(msg.substring(68,70),16));
                break;
            case 0x00:
                //仅时间戳+经纬高
                break;
            case 0x01:
                //时间戳+经纬高+速度（第1个spd位为1）
                dto.setSpeed(Byte.parseByte(msg.substring(36,38),16));
                break;
            case 0x04:
                //时间戳+经纬高+报警（第3个alm报警位为1）
                dto.setAlarm(Integer.parseInt(msg.substring(36,44),16));
                break;
            case 0x24:
                //时间戳+经纬高+蓝牙信标（第6个bluetooth蓝牙信标编号为1）+报警（第3个alm报警位为1）
                dto.setBluetooth(msg.substring(36,48));
                dto.setAlarm(Integer.parseInt(msg.substring(48,56),16));
                break;
			case 0x08:
				dto.setHr(Byte.parseByte(msg.substring(36,38),16));
				break;
            default:
				log.info("有效数据位位数不对未解析:{},{}",deviceId,dto.getValidBit());
        }
		WearableWorker wearableWorker=WearableWorkerMap.get(deviceId);
		if (wearableWorker==null){
			log.error("BatchPointsReportMsg wearableWorker get null:{}",deviceId);
			return dto;
		}
        sendBatchPointsToKafka(dto,wearableWorker);

		Object devObj =redisTemplate.opsForHash().get(CommonConstant.LocConstant.RealDevSte,wearableWorker.getDeviceId()+"");
		if (devObj!=null){
			BdmDevSte devSteHas= JSONObject.parseObject(devObj.toString(), BdmDevSte.class);
			devSteHas.setUpdateTime(new Date());
			redisTemplate.opsForHash().put(CommonConstant.LocConstant.RealDevSte,String.valueOf(wearableWorker.getDeviceId()),JSON.toJSONString(devSteHas));
		}
        return dto;
    }

	private BatchPointsReportMsg analysisSplitMessageToPosNew(String msg) {
		log.info("BatchPointsMsgHandler analysisSplitMessageToPos msg:{}",msg);
		BatchPointsReportMsg dto=new BatchPointsReportMsg();
		byte[] data = StringUtil.hexStringToByteArray(msg);
		ByteBuffer buffer = ByteBuffer.wrap(data).order(ByteOrder.BIG_ENDIAN);
		// 解析帧头
		dto.setFrameHead(buffer.getShort()&0xffff);
		// 解析有效数据位
		dto.setValidBit(buffer.get());
		// 解析时间戳
		dto.setCt(buffer.getInt());
		// 解析经度
		dto.setLng(buffer.getInt());
		// 解析纬度
		dto.setLat(buffer.getInt());
		// 解析海拔
		dto.setAlt(buffer.getShort()&0xffff);
		// 解析定位类型
		dto.setLocType(buffer.get());
		Byte validBits=dto.getValidBit();
		if ((validBits&1)==1){
			dto.setSpeed(buffer.get());
		}
		if ((validBits&2)==2){
			dto.setBearing(buffer.getShort()&0xffff);
		}
		if ((validBits&4)==4){
			dto.setAlarm(buffer.getInt());
		}
		if ((validBits&8)==8){
			dto.setHr(buffer.get());
		}
		if ((validBits&16)==16){
			dto.setSpo2(buffer.get());
		}
		if ((validBits&32)==32&& buffer.hasRemaining()){
			byte[]blueT=new byte[6];
			buffer.get(blueT);
			dto.setBluetooth(StringUtil.bytesToHex(blueT));
		}
		if ((validBits&64)==64){
			dto.setEvent(buffer.get());
		}
		log.info("BatchPointsMsgHandler analysisSplitMessageToPos dto:{}",dto);
		WearableWorker wearableWorker=WearableWorkerMap.get(deviceId);
		if (wearableWorker==null){
			log.error("BatchPointsReportMsg wearableWorker get null:{}",deviceId);
			return dto;
		}
		sendBatchPointsToKafka(dto,wearableWorker);

		Object devObj =redisTemplate.opsForHash().get(CommonConstant.LocConstant.RealDevSte,wearableWorker.getDeviceId()+"");
		if (devObj!=null){
			BdmDevSte devSteHas= JSONObject.parseObject(devObj.toString(), BdmDevSte.class);
			devSteHas.setUpdateTime(new Date());
			redisTemplate.opsForHash().put(CommonConstant.LocConstant.RealDevSte,String.valueOf(wearableWorker.getDeviceId()),JSON.toJSONString(devSteHas));
		}
		return dto;
	}

    public void sendBatchPointsToKafka(BatchPointsReportMsg msg,WearableWorker wearableWorker){
        Track track=new Track();
        track.setDeviceId(wearableWorker.getDeviceId());
        track.setDeviceModel(wearableWorker.getDeviceModel());
        track.setDeviceType(wearableWorker.getDeviceType());
        track.setDeviceNum(wearableWorker.getDeviceNum());
        track.setDeviceUniqueId(wearableWorker.getDeviceUniqueId());
        track.setTargetId(wearableWorker.getTargetId());
        track.setTargetType(wearableWorker.getTargetType());
        track.setTargetName(wearableWorker.getTargetName());
        track.setDeptId(wearableWorker.getDeptId());
        track.setLocTime(msg.getCt());
        track.setRecvTime(System.currentTimeMillis()/1000);
		track.setValid(1);
        track.setLocTimeF(new Date(msg.getCt()*1000));
        track.setRecvTimeF(new Date(System.currentTimeMillis()));
        track.setLongitude((double)msg.getLng()/1000000);
        track.setLatitude((double)msg.getLat()/1000000);
        track.setAltitude(msg.getAlt()/10);
        track.setSpeed(msg.getSpeed()==null?0F:msg.getSpeed());
        track.setBearing(msg.getBearing()==null?0:msg.getBearing());
        track.setAlarmFlag(msg.getAlarm()==null?0:msg.getAlarm());
		track.setBatch(1);
		track.setIotProtocol((short)2);
		TrackAux trackAux=new TrackAux();
		if(msg.getSpo2()!=null){
			trackAux.setBloodOxygen((int)msg.getSpo2());
		}
		if (msg.getHr()!=null){
			trackAux.setHeartRate((int)msg.getHr());
		}
		if (msg.getEvent()!=null){
			trackAux.setEventType((int)msg.getEvent());
		}
		trackAux.setLocationType(msg.getLocType());
		track.setAuxStr(JSON.toJSONString(trackAux));
        //经纬坐标转换
//        if (msg.getLocType()==0){
//            double[] cors=wgs84ToGcj02(track.getLatitude(),track.getLongitude());
//            track.setLatitude(cors[0]);
//            track.setLongitude(cors[1]);
//        }
		if (track.getLatitude()<1||track.getLongitude()<1){
			return;
		}
        kafkaProducerService.sendBatchPositionReportMessage(JSON.toJSONString(track),track.getDeviceUniqueId());
    }
    @Override
    public boolean match(String topic) {
        return TopicUtils.math(subTopic, topic, deviceIndex);
    }
}
