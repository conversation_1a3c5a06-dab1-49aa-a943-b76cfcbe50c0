package com.xh.vdm.mqttOfficial.dto;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StarCalendarUpdate {
	/**
	 *星历文件
	 */
	@JsonProperty("ephemeris_file")
	@JSONField(name = "ephemeris_file")
	private byte[] ephemerisFile;
	/**
	 *crc校验
	 */
	@JsonProperty("crc_checksum")
	@JSONField(name = "crc_checksum")
	private int crcChecksum;

	@JSONField(name = "date_time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
	private Date dateTime;
}
