package com.xh.vdm.mqttOfficial.mqtt.handler;

import com.xh.vdm.mqttOfficial.constant.CommonConstant;
import com.xh.vdm.mqttOfficial.mqtt.client.IMqttClient;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
public abstract class MessageHandlerAbst {

    protected final Map<String, IMqttClient> clientMap = new ConcurrentHashMap<>();

    protected String subTopic;

    protected String pubTopic;

    protected void addClient(String version, IMqttClient client) {
        if (version != null && client != null) {
            clientMap.put(version, client);
        }
    }


    protected void setSubTopic(String subTopic) {
        checkSubTopic(subTopic);
        this.subTopic = subTopic;
    }

    protected void setPubTopic(String pubTopic) {
        checkPubTopic(pubTopic);
        this.pubTopic = pubTopic;
    }

    protected void checkPubTopic(String topic) {
        if (topic == null || topic.trim().length() == 0) {
            throw new IllegalArgumentException("checkPubTopic topic can not be null or empty");
        }
        if (Boolean.FALSE.equals(topic.contains(CommonConstant.MqttConstant.PLACEHOLDER))) {
            throw new IllegalArgumentException("checkPubTopic wrong subject topic");
        }
    }

    protected void checkSubTopic(String topic) {
        if (topic == null || topic.trim().length() == 0) {
            throw new IllegalArgumentException("checkSubTopic topic can not be null or empty");
        }
    }

}
