package com.xh.vdm.mqttOfficial.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("bdm_cmd_record_new")
public class BdmCmdRecordNew implements Serializable {
	private static final long serialVersionUID = 1L;
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;
	@TableField(value = "type")
	private String type;
	@TableField(value = "param")
	private String param;
	@TableField(value = "res_param")
	private int resParam;
	@TableField(value = "alarm_id")
	private String alarmId;
	@TableField(value = "device_category")
	private String deviceCategory;
	@TableField(value = "device_unique_id")
	private String deviceUniqueId;
	@TableField(value = "device_num")
	private String deviceNum;
	@TableField(value = "target_name")
	private String targetName;
	@TableField(value = "dept_id")
	private long deptId;
	@TableField(value = "create_user_time")
	private String createUserName;
	@TableField(value = "update_time")
	private Date updateTime;
	@TableField(value = "create_time")
	private Date createTime;
	@TableField(value = "is_del")
	private byte isDel;
	@TableField(value = "create_user_id")
	private long createUserId;
}
