package com.xh.vdm.mqttOfficial.controller;

import com.alibaba.fastjson.JSON;
import com.xh.vdm.mqttOfficial.config.MqttConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/mqtt")
public class DemoController {

    @Resource
    MqttConfig mqttConfig;

    @PostMapping("/hello")
    public String hello(@RequestParam(value = "name", defaultValue = "World") String name) {
        log.info("name is {}", name);
        return String.format("Hello %s!", name);
    }

    @GetMapping("/mqttConfig")
    public Object mqttConfig() {
        return JSON.toJSONString(mqttConfig);
    }
}
