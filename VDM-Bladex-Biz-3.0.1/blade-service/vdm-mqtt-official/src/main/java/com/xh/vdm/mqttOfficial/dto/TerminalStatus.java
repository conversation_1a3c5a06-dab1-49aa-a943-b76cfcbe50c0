package com.xh.vdm.mqttOfficial.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TerminalStatus {
	@JSO<PERSON>ield(name = "phone")
	private String phone; // Terminal phone number
	@JSONField(name = "device_id")
	private Long deviceId;
	@JSONField(name = "device_type")
	private Integer deviceType;
	@JSONField(name = "device_num")
	private String deviceNum;
	@JSONField(name = "unique_id")
	private String uniqueId;
	@JSO<PERSON>ield(name = "target_id")
	private Long targetId;
	@JSONField(name = "target_type")
	private Integer targetType;
	@JSONField(name = "target_name")
	private String targetName;
	@JSONField(name = "time",format = "yyyy-MM-dd'T'HH:mm:ssXXX")
	private Date time;
	@JSONField(name = "on_off_line")
	private Integer onOffLine; // 0 - Online, 1 - Offline
	@JSONField(name = "longitude")
	private Double longitude;
	@JSONField(name = "latitude")
	private Double latitude;
	@JSONField(name = "address")
	private String address;
}
