package com.xh.vdm.mqttOfficial.controller;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xh.vdm.mqttOfficial.config.MqttConfig;
import com.xh.vdm.mqttOfficial.config.WearableWorkerCache;
import com.xh.vdm.mqttOfficial.constant.CommEnum;
import com.xh.vdm.mqttOfficial.constant.CommonConstant;
import com.xh.vdm.mqttOfficial.dto.*;
import com.xh.vdm.mqttOfficial.entity.WearableDevice;
import com.xh.vdm.mqttOfficial.mqtt.client.v3.MqttClientV3;
import com.xh.vdm.mqttOfficial.service.WearableDeviceService;
import com.xh.vdm.mqttOfficial.util.CRC32Calculator;
import com.xh.vdm.mqttOfficial.util.PostGre;
import com.xh.vdm.mqttOfficial.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

@Slf4j
@RestController
@RequestMapping("/mqtt")
public class SendController {
    String payloadStr;
    @Resource
    MqttConfig mqttConfig;
    @Resource
    MqttClientV3 mqttClientV3;
    @Autowired
    StringRedisTemplate redisTemplate;
    @Autowired
    WearableWorkerCache wearableWorkerCache;
	@Resource
	PostGre postGre;
	@Resource
	CRC32Calculator crc32Calculator;
	@Resource
	WearableDeviceService wearableDeviceService;

    @PostMapping("/send/weather")
    public HttpParamResponse WeatherSend(@RequestBody WeatherNotifyMsg param) {
		WearableDevice wearableDevice= wearableDeviceService.getById(param.getDeviceId());
        log.info("WeatherNotifyMsg input param is {}", param);
        String paramStr=String.format("%s,%d,%d,%s,%s,%s",param.getFr(),param.getWxt(),param.getCt(),param.getLvl(),param.getDir(),param.getPred());
        String setTopic =mqttConfig.getTopic().getPublish().getWeatherSend().replace(CommonConstant.MqttConstant.PLACEHOLDER,wearableDevice.getUniqueId());
		postGre.saveCmdRecord(wearableDevice.getUniqueId(),paramStr,"天气下发");
		postGre.saveLog(wearableDevice.getUniqueId(),(byte)14,(byte)1,setTopic.concat(":").concat(paramStr));
        return DeviceNumController.getHttpParamResponse(paramStr, setTopic, mqttClientV3);
    }

    @PostMapping("/send/data")
    public HttpParamResponse DataSend(@RequestBody DataNotify param) {
		WearableDevice wearableDevice= wearableDeviceService.getById(param.getDeviceId());
		if (null == wearableDevice) {
			return new HttpParamResponse(400, "该终端不支持该操作，请确认该终端是否支持MQTT协议", null);
		}

        log.info("WeatherNotifyMsg input param is {}", param);
        String paramStr=String.format("%s,%d,%d,%s,%s,%s",param.getFr(),param.getPri(),param.getCt(),param.getMsg(),param.getTitle(),param.getSender());
        String setTopic =mqttConfig.getTopic().getPublish().getDataSend().replace(CommonConstant.MqttConstant.PLACEHOLDER,wearableDevice.getUniqueId());
		postGre.saveCmdRecord(wearableDevice.getUniqueId(),paramStr,"数据下发");
		postGre.saveLog(wearableDevice.getUniqueId(),(byte)14,(byte)1,setTopic.concat(":").concat(paramStr));
        return DeviceNumController.getHttpParamResponse(paramStr, setTopic, mqttClientV3);
    }

    @PostMapping("/send/frequency")
    public HttpParamResponse FrequencyControl(@RequestBody ModeFrequencyControl param) {
		WearableDevice wearableDevice= wearableDeviceService.getById(param.getDeviceId());
		if (null == wearableDevice) {
			return new HttpParamResponse(400, "该终端不支持该操作，请确认该终端是否支持MQTT协议", null);
		}

        log.info("FrequencyControl input param is {}", param);
        String setTopic =mqttConfig.getTopic().getPublish().getFrequencyControl().replace(CommonConstant.MqttConstant.PLACEHOLDER,wearableDevice.getUniqueId());
		if (param.getTyp().equals("20")){
			String paramStr=String.format("%s,%s,0,0,0",param.getFr(),param.getTyp());
			postGre.saveCmdRecord(wearableDevice.getUniqueId(),paramStr,"模式频率控制");
			postGre.saveLog(wearableDevice.getUniqueId(),(byte)14,(byte)1,setTopic.concat(":").concat(paramStr));
			boolean res=mqttClientV3.publishMessage(setTopic,paramStr.getBytes(StandardCharsets.UTF_8),2,false);
			if (Boolean.FALSE.equals(redisTemplate.hasKey(CommonConstant.MqttConstant.MQTT_FREQUENCY+wearableDevice.getUniqueId()))) {
				try {
					// 暂停当前线程执行5秒 (5000毫秒)
					Thread.sleep(5000);
				} catch (InterruptedException e) {
					// 处理被打断的异常
					System.out.println("Thread was interrupted");
				}
			}
			String frequencyRes=redisTemplate.opsForValue().get(CommonConstant.MqttConstant.MQTT_FREQUENCY+wearableDevice.getUniqueId());
			CmdReplyResp cmdReplyResp = JSONObject.parseObject(frequencyRes, CmdReplyResp.class);
			HttpParamResponse resp=new HttpParamResponse(200,"ok",cmdReplyResp);
			if (!res){
				resp.setCode(1);
				resp.setMsg("fail");
			}
			return resp;
		}else {
			String paramStr=String.format("%s,%s,%d,%d,%s",param.getFr(),param.getTyp(),param.getMo(),param.getSts(),param.getFrq());
			postGre.saveCmdRecord(wearableDevice.getUniqueId(),paramStr,"模式频率控制");
			postGre.saveLog(wearableDevice.getUniqueId(),(byte)14,(byte)1,setTopic.concat(":").concat(paramStr));
			return DeviceNumController.getHttpParamResponse(paramStr, setTopic, mqttClientV3);
		}
    }

    @PostMapping("/send/remoteparamset")
    public HttpParamResponse RemoteParamSet(@RequestBody RemoteParamSet param) {
		WearableDevice wearableDevice= wearableDeviceService.getById(param.getDeviceId());
		if (null == wearableDevice) {
			return new HttpParamResponse(400, "该终端不支持该操作，请确认该终端是否支持MQTT协议", null);
		}
        log.info("RemoteParamSet input param is {}", param);
        String paramStr=String.format("%s,%d,%d,%d",param.getFrameType(),param.getCategory(),param.getSwitchStatus(),param.getTimestamp());
        String setTopic =mqttConfig.getTopic().getPublish().getRemoteParamSet().replace(CommonConstant.MqttConstant.PLACEHOLDER,wearableDevice.getUniqueId());
		postGre.saveCmdRecord(wearableDevice.getUniqueId(),paramStr,"远程参数配置");
		postGre.saveLog(wearableDevice.getUniqueId(),(byte)14,(byte)1,setTopic.concat(":").concat(paramStr));
        return DeviceNumController.getHttpParamResponse(paramStr, setTopic, mqttClientV3);
    }

	@PostMapping("/send/cmdcontrol")
	public HttpParamResponse CmdControl(@RequestBody CmdControl param) {
		WearableDevice wearableDevice= wearableDeviceService.getById(param.getDeviceId());
		if (null == wearableDevice) {
			return new HttpParamResponse(400, "该终端不支持该操作，请确认该终端是否支持MQTT协议", null);
		}
		log.info("CmdControl input param is {}", param);
		String paramStr=String.format("%s",param.getControl());
		String setTopic =mqttConfig.getTopic().getPublish().getCmdControl().replace(CommonConstant.MqttConstant.PLACEHOLDER,wearableDevice.getUniqueId());
		postGre.saveCmdRecord(wearableDevice.getUniqueId(),paramStr,"更改IP");
		postGre.saveLog(wearableDevice.getUniqueId(),(byte)14,(byte)1,setTopic.concat(":").concat(paramStr));
		return DeviceNumController.getHttpParamResponse(paramStr, setTopic, mqttClientV3);
	}

    @PostMapping("/send/updatestar")
    public HttpParamResponse UpdateStarCalendar(@RequestBody StarCalendarUpdate param) {
        log.info("UpdateStarCalendar input param is {}", param);
        String paramStr=String.format("%s%s", Hex.encodeHexString(param.getEphemerisFile()),Integer.toHexString(param.getCrcChecksum()));
        String setTopic =mqttConfig.getTopic().getPublish().getStarUpdate();
        return DeviceNumController.getHttpParamResponse(paramStr, setTopic, mqttClientV3);
    }

	@PostMapping("/send/upgradeversion")
	public HttpParamResponse UpgradeVersion(@RequestBody OtaVersion param) {
		param.setFr("ota");
		if (param.getTotalUfw()==null){
			param.setTotalUfw("0");
		}
		if (param.getTotalBin()==null){
			param.setTotalBin("0");
		}
		if (param.getMsg()==null){
			param.setMsg("0");
		}
		// 调用 System.currentTimeMillis() 方法获取当前时间戳（毫秒）
		long currentTimestamp = System.currentTimeMillis();
		param.setCt(""+currentTimestamp);
		String[] versionSplit=param.getVersion().split("_");
		String version=versionSplit[0];
		String fileSuffix=versionSplit[1];
		String versionPacketNum=redisTemplate.opsForValue().get(CommonConstant.MqttConstant.MQTT_UPGRADE_VERSION+param.getVersion());
		if (fileSuffix.equals("ufw")){
			param.setTotalUfw(versionPacketNum);
		}else if (fileSuffix.equals("bin")){
			param.setTotalBin(versionPacketNum);
		}
		String preCode=String.format("%s,%s,%s,%s,%s,%s", param.getFr(),version,param.getTotalUfw(),param.getTotalBin(),param.getMsg(),param.getCt());
		byte[] codeData=preCode.getBytes();
		int crcResult = crc32Calculator.amBootloaderFastCrc32(0, codeData, codeData.length);
		param.setCrc(String.format("%08x", crcResult));
		log.info("Upgrade input param is {}", param);
		String paramStr=String.format("%s,%s,%s,%s,%s,%s,%s", param.getFr(),version,param.getTotalUfw(),param.getTotalBin(),param.getMsg(),param.getCt(),param.getCrc());
		String setTopic =mqttConfig.getTopic().getPublish().getOtaUpgrade().replace(CommonConstant.MqttConstant.PLACEHOLDER,param.getDeviceId());
		postGre.saveCmdRecord(param.getDeviceId(),paramStr,"下发OTA升级版本信息");
		postGre.saveLog(param.getDeviceId(),(byte)14,(byte)1,setTopic.concat(":").concat(paramStr));
		return DeviceNumController.getHttpParamResponseRetain(paramStr, setTopic, mqttClientV3);
	}

	@PostMapping("/send/uploadfile")
	public HttpParamResponse UploadFile(@RequestParam("file") MultipartFile file, @RequestParam("version") String version) throws IOException {
		if (file.isEmpty()) {
			new HttpParamResponse(400,"failed",null);
		}
		// 获取上传文件的输入流
		InputStream inputStream = file.getInputStream();
		// 可以在此处对字节流进行处理，例如读取字节流中的数据
		byte[] buffer = new byte[CommonConstant.MqttConstant.UpgradePacketLen];
		int bytesRead;
		int index=0;
		String indexStr="";
		boolean res=true;
		while ((bytesRead = inputStream.read(buffer)) != -1) {
			index++;
			indexStr=""+index;
			// 处理读取到的字节数据，这里简单打印读取的字节数
			log.info("读取数据大小:{}字节",bytesRead);
			// 截取实际读取的字节数据
			byte[] actualData = new byte[bytesRead];
			System.arraycopy(buffer, 0, actualData, 0, bytesRead);

			// 二进制数据包，2B长度+数据12k+4B CRC
			String packetLengthStr = String.format("%04x", actualData.length + 4);
			String packetStr = StringUtil.bytesToHex(actualData);

			int crcResult = crc32Calculator.amBootloaderFastCrc32(0, actualData, actualData.length);
			String crcResultStr = String.format("%08x", crcResult);

			log.info("发送数据：{}，{}，{}", actualData.length + 4, packetStr, crcResultStr);
			String sendData = packetLengthStr + packetStr + crcResultStr;

			String setTopic = String.format(mqttConfig.getTopic().getPublish().getOtaDownload(), version, indexStr);
			postGre.savePacketLog(version, (byte) 14, (byte) 1, setTopic.concat(":").concat("数据长度:" + packetLengthStr).concat("校验码:" + crcResultStr));

			boolean indexRes = mqttClientV3.publishMessage(setTopic, StringUtil.hexStringToByteArray(sendData), 2, true);
			if (!indexRes) {
				res = false;
			}
		}
		// 关闭输入流
		inputStream.close();
		redisTemplate.opsForValue().set(CommonConstant.MqttConstant.MQTT_UPGRADE_VERSION+version,index+"");
		postGre.saveCmdRecord("",version+":"+indexStr,"下发OTA二进制升级包");
		String subTotalData=String.format("%s,%d",version,index);
		String subTotalTopic =String.format(mqttConfig.getTopic().getPublish().getOtaTotal(), version);
		mqttClientV3.publishMessage(subTotalTopic,subTotalData.getBytes(StandardCharsets.UTF_8),2,false);
		postGre.saveLog(version,(byte)14,(byte)1,subTotalTopic.concat(":").concat(subTotalData));
		postGre.saveCmdRecord("",subTotalData,"下发OTA升级分包总数");
		HttpParamResponse resp=new HttpParamResponse(200,"ok",res);
		if (!res){
			resp.setCode(1);
			resp.setMsg("fail");
		}
		return resp;
	}
}
