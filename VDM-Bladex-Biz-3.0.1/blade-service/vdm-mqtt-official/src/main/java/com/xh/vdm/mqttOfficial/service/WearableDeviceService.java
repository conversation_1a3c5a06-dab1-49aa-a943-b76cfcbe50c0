package com.xh.vdm.mqttOfficial.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.mqttOfficial.entity.WearableDevice;
import com.xh.vdm.mqttOfficial.mapper.WearableDeviceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class WearableDeviceService extends ServiceImpl<WearableDeviceMapper,WearableDevice> {
	public WearableDevice getWearableDeviceByDeviceNum(String deviceNum){
		return this.query().eq("device_num",deviceNum).eq("is_del",0).one();
	}
}
