package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 漂移表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatDrift implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车牌号
     */
    private String licencePlate;

    /**
     * 漂移次数#总点数
     */
    private String d01;

    private String d02;

    private String d03;

    private String d04;

    private String d05;

    private String d06;

    private String d07;

    private String d08;

    private String d09;

    private String d10;

    private String d11;

    private String d12;

    private String d13;

    private String d14;

    private String d15;

    private String d16;

    private String d17;

    private String d18;

    private String d19;

    private String d20;

    private String d21;

    private String d22;

    private String d23;

    private String d24;

    private String d25;

    private String d26;

    private String d27;

    private String d28;

    private String d29;

    private String d30;

    private String d31;

    private Date updateTime;


}
