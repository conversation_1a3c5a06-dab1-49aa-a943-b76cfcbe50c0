package com.xh.vdm.statistic.task;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.constant.RedisConstants;
import com.xh.vdm.statistic.controller.StatisticsController;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.*;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.*;
import com.xh.vdm.statistic.vo.ObjectRestResponse;
import com.xh.vdm.statistic.vo.request.VehicleOnlineRequest;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/11/12 10:19
 */
@Component
@EnableAsync
@Slf4j
public class StatisticsTask {

    private final int binarySize = 6144; // 6 * 1024
    private final int imgSize = 307200; // 300 * 1024
    private final int videoSize = 6291456; // 6 * 1024 *1024
    private final int bytePerSec = 38400; // 300 / 8 * 1024

    @Autowired
    private StatisticsMapper statisticsMapper;

	@Resource
	private IBamThirdPartyPlatformService thirdPartyPlatformService;

	@Resource
	private IBladeDeptService bladeDeptService;

	@Resource
	private IBdmVehicleService vehicleService;

    @Resource
    private IStatEmailService emailService;

    @Resource
    private MailUtil mailUtil;

    @Autowired
    protected RedisTemplate<String, String> redisTemplate;

    @Autowired
    protected MapDistanceUtil mapDistanceUtil;

    @Resource
    private CacheUtil cacheUtil;

    @Autowired
    protected VehicleUtil vehicleUtil;

    @Autowired
    private BdmDsmDataMapper bdmDsmDataMapper;

    @Autowired
    private BdmVideoPlayMapper bdmVideoPlayMapper;

    @Autowired
    private DailyCarMegaByteMapper dailyCarMegaByteMapper;

    @Autowired
    BdmRouteMapper bdmRouteMapper;

    @Autowired
	IBdmRouteService bdmRouteService;

    @Autowired
    StatisticsService statisticsService;

    @Resource
    IBladeDeptService deptService;

	@Resource
	private ILastOnlineService lastOnlineService;

	@Resource
	private IAlarmService alarmService;

	@Resource
	private IOfflineMoveService offlineMoveService;


    @Autowired
    private StatisticsController statController;

    @Value("${email.file.tmp_path}")
    private String emailTmpFilePath;

    @Value("${email.debug}")
    private boolean emailDebug;

    @Value("${email.max_count}")
    private int emailDebugMaxCount;

    @Resource
    private LogUtil logUtil;

	@Resource
	private ILocationService locationService;

	private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    private static final ThreadLocal<SimpleDateFormat> sdfYMD = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd"));
    private static final ThreadLocal<SimpleDateFormat> sdfYM = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMM"));

    /**
     * 补充定位条数
	 *
     */
    @Async
    //@Scheduled(fixedDelay = 30000)
	@XxlJob("updatePointNum")
    public void updatePointNum(){

		XxlJobHelper.log("将要开始执行定时任务：locationDriftStat");

        List<OnlineOfflineRecord> list = statisticsMapper.queryNeedUpdatePointNum();
        if(list!=null&&!list.isEmpty()){
            list.forEach(record ->{
                //补充定位条数
                String licencePlate = record.getLicencePlate();
				Integer licenceColor = record.getLicenceColor();
                Date startDate = record.getOnLineTime();
                Long startTime = record.getOnLineTime().getTime()/1000;
                Long endTime = record.getOffLineTime().getTime()/1000;
                Long totalCount = 0L;
                SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
                String table = format.format(record.getOnLineTime());
                try{
                    HashMap map  = statisticsMapper.queryLocationCount(table, licencePlate,licenceColor,startTime, endTime);
                    if(map!=null&&map.get("location_count")!=null){
                        Long locationCnt = (Long)map.get("location_count");
                        totalCount += locationCnt;
                    }
                }catch (Exception e){
                    e.printStackTrace();
                }

                String endTable = format.format(record.getOffLineTime());
                while(!table.equals(endTable)){
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(startDate);
                    calendar.add(Calendar.DAY_OF_MONTH, 1);
                    table = format.format(calendar.getTime());
                    startDate = calendar.getTime();
                    try{
                        HashMap locationMap  = statisticsMapper.queryLocationCount(table, licencePlate,licenceColor,startTime, endTime);
                        if(locationMap!=null&&locationMap.get("location_count")!=null){
                            Long locationCnt = (Long)locationMap.get("location_count");
                            totalCount += locationCnt;
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
                statisticsMapper.updatePointNum(totalCount,record.getId());
            });
        }
		XxlJobHelper.log("执行定时任务结束：updatePointNum");
    }

    /**
     * 更新Redis中车辆信息
     */
    @Async
    //@Scheduled(cron="0 0 0/1 * * ?")
	@XxlJob("updateRedisVehicle")
    public void updateRedisVehicle(){

		XxlJobHelper.log("将要开始执行定时任务：locationDriftStat");

        List<Vehicle> list = statisticsMapper.getVehicleList();
        //更新Address字段
        Long vehicleKeySize = redisTemplate.opsForHash().size(RedisConstants.HashKey.VEHICLE_INFO_HASH_KEY);
        List<Integer> indList = new ArrayList<>();
        List<Point> pointList = new ArrayList<>();
        if(vehicleKeySize>0){
            //如果之前有数据，判断哪些变化了的，对变化了的进行更新
            for (int i = 0; i < list.size(); i++) {
                Vehicle vehicle = list.get(i);
                Vehicle vehicle1 = vehicleUtil.acquireVehicleByLicencePlate(vehicle.getLicencePlate(), vehicle.getLicenceColor()+"");
                if(vehicle1!=null){
                    if(vehicle.getLongitude()==null||vehicle.getLatitude()==null||vehicle.getLongitude()==0||vehicle.getLatitude()==0){

                    }else if(vehicle1.getAddress()!=null&&!"".equals(vehicle1.getAddress())&&vehicle.getLongitude().equals(vehicle1.getLongitude())&&vehicle.getLatitude().equals(vehicle1.getLatitude())){
                        vehicle.setAddress(vehicle1.getAddress());
                    }else{
                        indList.add(i);
                        pointList.add(new Point(vehicle.getLongitude(), vehicle.getLatitude()));
                    }
                }else{
                    if(vehicle.getLongitude()==null||vehicle.getLatitude()==null||vehicle.getLongitude()==0||vehicle.getLatitude()==0){

                    }else{
                        indList.add(i);
                        pointList.add(new Point(vehicle.getLongitude(), vehicle.getLatitude()));
                    }
                }
            }

            List<String> addressList = mapDistanceUtil.getAddress(pointList);
            for(int i=0;i<indList.size();i++){
                list.get(indList.get(i)).setAddress(addressList.get(i));
            }
        }else{
            //如果redis里面之前没有数据，全部查询之后放入Redis
            for(int i=0;i<list.size();i++){
                Double longitude = list.get(i).getLongitude();
                Double latitude = list.get(i).getLatitude();
                if(longitude!=null&&latitude!=null&&longitude!=0&&latitude!=0){
                    indList.add(i);
                    pointList.add(new Point(longitude, latitude));
                }else {
                    list.get(i).setAddress("");
                }
            }
            List<String> addressList = mapDistanceUtil.getAddress(pointList);
            for(int i=0;i<indList.size();i++){
                list.get(indList.get(i)).setAddress(addressList.get(i));
            }
        }

        //用于写入缓存的map，key为组织id-字典类型，value为（key为字典值，value为字典名称的map）
        Map<String, Vehicle> vehicleMap = new HashMap<>();
        //填写用于缓存的map
        list.forEach(vehicle ->{
            String key = vehicle.getLicencePlate()+"~"+vehicle.getLicenceColor() ;
            vehicleMap.put(key,vehicle);
        });
        Map<String,String> jsonMap = new HashMap<>();
        vehicleMap.forEach((key,value) ->{
            jsonMap.put(key,JSON.toJSONString(value));
        });
        //一次请求处理删除及添加
        SessionCallback<Boolean> sessionCallback = new SessionCallback<Boolean>() {
            @Override
            public <K, V> Boolean execute(RedisOperations<K, V> redisOperations) throws DataAccessException {
                redisOperations.multi();
                redisTemplate.delete(RedisConstants.HashKey.VEHICLE_INFO_HASH_KEY);
                redisTemplate.opsForHash().putAll(RedisConstants.HashKey.VEHICLE_INFO_HASH_KEY, jsonMap);
                List result = redisOperations.exec();
                if(result.size() > 0) {
                    return true;
                }
                return false;
            }
        };
        redisTemplate.execute(sessionCallback);
		XxlJobHelper.log("执行定时任务结束：updateRedisVehicle");
    }

    /**
     * 更新Redis中字典信息
     */
    @Async
    //@Scheduled(cron="0 0 1 * * ?")
	@XxlJob("updateRedisDict")
    public void updateRedisDict(){

		XxlJobHelper.log("将要开始执行定时任务：locationDriftStat");

        List<Dict> dictList = statisticsMapper.getDictionaryList();
        //记录旧的key，用于删除
        Set<String> keyList = redisTemplate.keys(RedisConstants.HashKey.DICTIONARY_HASH_KEY + "*");
        //一次请求处理删除及添加
        SessionCallback<Boolean> sessionCallback = new SessionCallback<Boolean>() {
            @Override
            public <K, V> Boolean execute(RedisOperations<K, V> redisOperations) throws DataAccessException {
                redisOperations.multi();
                redisTemplate.delete(keyList);
                //用于写入缓存的map，key为组织id-字典类型，value为（key为字典值，value为字典名称的map）
                Map<String, LinkedHashMap<String, String>> organizationIdAndTypeMapValueMapName = new HashMap<>();
                //填写用于缓存的map
                dictList.forEach(sysDictionary ->{
                    String key = sysDictionary.getDictType().toString();
                    LinkedHashMap<String, String> valueMapName = organizationIdAndTypeMapValueMapName.get(key);
                    if(null == valueMapName){
                        valueMapName = new LinkedHashMap<>();
                        organizationIdAndTypeMapValueMapName.put(key, valueMapName);
                    }
                    valueMapName.put(sysDictionary.getDictCode().toString(), sysDictionary.getDictName());
                });
                organizationIdAndTypeMapValueMapName.forEach((organizationIdAndType, valeuMapName)->{
                    redisTemplate.opsForHash().putAll(RedisConstants.HashKey.DICTIONARY_HASH_KEY + organizationIdAndType, valeuMapName);
                });
                List result = redisOperations.exec();
                if(result.size() > 0) {
                    return true;
                }
                return false;
            }
        };
        redisTemplate.execute(sessionCallback);
		XxlJobHelper.log("执行定时任务结束：updateRedisDict");
    }

    /**
     * 更新Redis中车辆信息
     */
    @Async
    //@Scheduled(fixedDelay = 1800000)
	@XxlJob("updateRedisSubDepts")
    public void updateRedisSubDepts(){


		XxlJobHelper.log("将要开始执行定时任务：locationDriftStat");

        VehicleOnlineRequest vehicleOnlineRequest = new VehicleOnlineRequest();
        //查询所有部门
        List<Dept> deptList = statisticsMapper.queryDepts();

        HashMap<Long,List<Long>> deptMap = new HashMap<>();
        for(Dept dept : deptList){
            Long deptId = dept.getId();
            List<Long> subList = getSubList(deptId,deptList);
            subList.add(deptId);
            try {
                deptMap.put(deptId,subList);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        //用于写入缓存的map，key为组织id-字典类型，value为（key为字典值，value为字典名称的map）
        Map<String,String> jsonMap = new HashMap<>();
        deptMap.forEach((key,value) ->{
            jsonMap.put(key.toString(),JSON.toJSONString(value));
        });
        //一次请求处理删除及添加
        SessionCallback<Boolean> sessionCallback = new SessionCallback<Boolean>() {
            @Override
            public <K, V> Boolean execute(RedisOperations<K, V> redisOperations) throws DataAccessException {
                redisOperations.multi();
                redisTemplate.delete(RedisConstants.HashKey.DEPT_INFO_HASH_KEY);
                redisTemplate.opsForHash().putAll(RedisConstants.HashKey.DEPT_INFO_HASH_KEY, jsonMap);
                List result = redisOperations.exec();
                if(result.size() > 0) {
                    return true;
                }
                return false;
            }
        };
        redisTemplate.execute(sessionCallback);
		XxlJobHelper.log("执行定时任务结束：updateRedisSubDepts");
    }

    public List<Long> getSubList(Long dept,List<Dept> deptList) {
        List<Long> subList = new ArrayList<>();
        for(Dept map : deptList){
            Long pid = map.getPid();
            Long id = map.getId();
            if(pid.longValue() == dept.longValue()){
                subList.add(id);
                subList.addAll(getSubList(id,deptList));
            }
        }
        return subList;
    }

    public List<Long> getDeptList(Long deptId) throws Exception {
        if(deptId==null){
            throw new Exception("企业编号为空");
        }
        List<HashMap> deptList = statisticsMapper.getDeptList(deptId);
        List<Long> idList = new ArrayList<>();
        deptList.forEach(map ->{
            idList.add((Long)map.get("id"));
        });
        idList.add(deptId);
        return idList;
    }

    /**
     * 生成日里程数据
     */
    //为了减小数据补录造成的影响，改为 每天早上 8:10 执行
    @Async
    //@Scheduled(cron="0 10 8 * * ?")
	@XxlJob("generateBdmRoute")
    //@Scheduled(fixedDelay = 1520000)
    public void generateBdmRoute(){


		XxlJobHelper.log("将要开始执行定时任务：generateBdmRoute");
		try{
			Calendar calendar = Calendar.getInstance();
			//得到前一天
			calendar.add(Calendar.DATE, -1);
			Date date = calendar.getTime();
			DateFormat df = new SimpleDateFormat("yyyyMMdd");
			String yesterday = df.format(date);

			bdmRouteService.generateBdmRoute(yesterday);


		}catch (Exception e){
			log.error("生成日里程数据报错",e);
			XxlJobHelper.handleFail("生成日里程数据报错 "+e.getMessage());
			return ;
		}
		XxlJobHelper.log("执行定时任务结束：generateBdmRoute");
    }


    /**
     * 统计报表统计前一天的数据并发送到客户邮箱
     * 每天凌晨 8:25 执行
     */
    @Async
    //@Scheduled(cron="0 25 08 * * ?")
	@XxlJob("statDayAndSendMail")
    public void statDayAndSendMail (){


		XxlJobHelper.log("将要开始执行定时任务：locationDriftStat");

        long start = System.currentTimeMillis();
        //获取前一天的日期
        Calendar cal = Calendar.getInstance();
        Date date = new Date();
        cal.setTime(date);
        cal.add(Calendar.DATE, -1);
        Date datePre = cal.getTime();
        try {
            statDayAndSendMailForOneDay(datePre, StatisticConstants.EMAIL_SEND_TYPE_SCHEDULE);
            log.info("前一天数据报表统计完成");
            long end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_DAY,"统计日报并发送邮箱：定时执行跑批任务",sdfYMD.get().format(datePre) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
        }catch (Exception e){
            log.error("统计前一天数据报表失败",e);
            long end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_DAY,"统计日报并发送邮箱：定时执行跑批任务",sdfYMD.get().format(datePre) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败"+e.getMessage());
        }

		XxlJobHelper.log("执行定时任务结束：statDayAndSendMail");
    }

    /**
     * 统计指定日期的数据，并发送到客户邮箱
     * @param day
     */
    public void statDayAndSendMailForOneDay(Date day, int sendType) throws Exception {
        //分企业进行处理
        //查询企业
        List<DeptAndEmail> emailList = deptService.findDeptAndEmail();

        int index = 0;
        int successSize = 0;
        Exception exception = null;
        //存放附件
        List<String> files = new ArrayList<>();
        for (DeptAndEmail de : emailList) {

            try {
                //如果开启debug模式，就最多只发送设置数量的邮件，防止发送邮件过多，邮箱被封
                if (emailDebug) {
                    index++;
                    if (index > emailDebugMaxCount) {
                        break;
                    }
                }

                //执行数据统计和日报发送
                files = statAndSendForOneDay(de, day, sendType);

                successSize++;

            }catch (Exception e){
                exception = e;
                log.error("统计日报并发送邮件失败",e);
            }
        }
        if(successSize < 1){
            throw new Exception("发送邮件失败, "+exception==null?"":exception.getMessage());
        }
    }

    /**
     * @description: 执行日报数据统计和邮件发送动作
     * @author: zhouxw
     * @date: 2023-03-62 11:15:52
     * @param: [de, day]
     * @return: java.util.List<java.lang.String>
     **/
    public List<String> statAndSendForOneDay(DeptAndEmail de, Date day, int sendType) throws Exception{

        //存放附件
        List<String> files = new ArrayList<>();
        String date = "";
        try{

            date = sdfYMD.get().format(day);

            long startSecondTimestamp = DateUtil.getDayFirstSecondTimestamp(day.getTime() / 1000);
            long endSecondTimestamp = DateUtil.getDayLastSecondTimestamp(day.getTime() / 1000);

            //1.统计全部报表指定日期的数据，并导出到文件中
            long deptId = de.getDeptId();
            String deptName = de.getDeptName();
            String email = de.getEmail();

            //1.1 疲劳驾驶
            //测试通过
            ObjectRestResponse<String> res1 = statController.exportFatigueDriving(null, deptId + "", startSecondTimestamp, endSecondTimestamp, null, null, null, null, null, emailTmpFilePath, deptName + "_" + date);
            if (validFilePath(res1)) {
                String filePath1 = emailTmpFilePath + res1.getData().substring(res1.getData().lastIndexOf("/") + 1);
                files.add(filePath1);
            }

            //1.2 车辆地图超速
            //测试通过
            ObjectRestResponse<String> res2 = statController.exportSegLimitSpeedMap(null, deptId + "", startSecondTimestamp, endSecondTimestamp, null, null, null, null, null, null, null, emailTmpFilePath, deptName + "_" + date);
            if (validFilePath(res2)) {
                String filePath2 = emailTmpFilePath + res2.getData().substring(res2.getData().lastIndexOf("/") + 1);
                files.add(filePath2);
            }

            //1.3 终端限速
            //测试通过
            ObjectRestResponse<String> res3 = statController.exportSegLimitSpeedTerminal(null, deptId + "", startSecondTimestamp, endSecondTimestamp, null, null, null, null, null, null, null, emailTmpFilePath, deptName + "_" + date);
            if (validFilePath(res3)) {
                String filePath3 = emailTmpFilePath + res3.getData().substring(res3.getData().lastIndexOf("/") + 1);
                files.add(filePath3);
            }

            //1.4 车辆在线情况抽查
            //测试通过
            ObjectRestResponse<String> res4 = statController.exportVehicleOnline(null, null, deptId + "", null, emailTmpFilePath, deptName + "_" + date);
            if (validFilePath(res4)) {
                String filePath4 = emailTmpFilePath + res4.getData().substring(res4.getData().lastIndexOf("/") + 1);
                files.add(filePath4);
            }

            //1.5 车辆运行情况巡检
            //测试通过
            ObjectRestResponse<String> res5 = statController.exportVehicleOperation(null, deptId + "", null, null, null, null, null, emailTmpFilePath, deptName + "_" + date);
            if (validFilePath(res5)) {
                String filePath5 = emailTmpFilePath + res5.getData().substring(res5.getData().lastIndexOf("/") + 1);
                files.add(filePath5);
            }


            //1.6 车辆日里程统计
            //测试通过
            ObjectRestResponse<String> res6 = statController.exportDayMileage(null, deptId + "", startSecondTimestamp, endSecondTimestamp, null, null, null, null, null, emailTmpFilePath, deptName + "_" + date);
            if (validFilePath(res6)) {
                String filePath6 = emailTmpFilePath + res6.getData().substring(res6.getData().lastIndexOf("/") + 1);
                files.add(filePath6);
            }

            //1.7 车辆在线率统计
            //测试通过
            /*ObjectRestResponse<String> res7 = statController.exportRateStatistics(null, null, null , emailTmpFilePath, deptName + "_" + date);
            if (validFilePath(res7)) {
                String filePath7 = emailTmpFilePath + res7.getData().substring(res7.getData().lastIndexOf("/") + 1);
                files.add(filePath7);
            }*/

            //1.8 车辆离线统计
            //测试通过
			ObjectRestResponse<String> res8 = null;
            /*ObjectRestResponse<String> res8 = statController.exportVehicleOffline(null, deptId + "", endSecondTimestamp, startSecondTimestamp, null, null, null, null, null, emailTmpFilePath, deptName + "_" + date);
            if (validFilePath(res8)) {
                String filePath8 = emailTmpFilePath + res8.getData().substring(res8.getData().lastIndexOf("/") + 1);
                files.add(filePath8);
            }*/

            //1.9 车辆夜间违规行车
            //测试通过
            ObjectRestResponse<String> res9 = statController.exportNightDriving(null, deptId + "", startSecondTimestamp, endSecondTimestamp, null, null, null, null, null, emailTmpFilePath, deptName + "_" + date);
            if (validFilePath(res9)) {
                String filePath9 = emailTmpFilePath + res9.getData().substring(res9.getData().lastIndexOf("/") + 1);
                files.add(filePath9);
            }

            //1.10 车辆上下线查询
            //测试通过
            ObjectRestResponse<String> res10 = statController.exportVehicleOnlineOrOffline(null, deptId + "", startSecondTimestamp, endSecondTimestamp, null, null, null, null, null, emailTmpFilePath, deptName + "_" + date);
            if (validFilePath(res10)) {
                String filePath10 = emailTmpFilePath + res10.getData().substring(res10.getData().lastIndexOf("/") + 1);
                files.add(filePath10);
            }

            //1.11 车辆安全信息记录
            //测试通过
            ObjectRestResponse<String> res11 = statController.exportSecurityInfo(null, deptId + "", startSecondTimestamp, endSecondTimestamp, null, null, null, null, null, emailTmpFilePath, deptName + "_" + date);
            if (validFilePath(res11)) {
                String filePath11 = emailTmpFilePath + res11.getData().substring(res11.getData().lastIndexOf("/") + 1);
                files.add(filePath11);
            }


            //2.发送到用户邮箱
            MailRequest mr = new MailRequest();
            mr.setSendTo(email);
            mr.setText(deptName + "，您好：<br /> &nbsp;&nbsp;&nbsp;&nbsp;附件为您的" + date + "数据日报表，请查收。");
            mr.setFilePath(files);
            mr.setSubject(date + "数据日报表");
            mailUtil.sendHtmlMail(mr);

            //记录邮件发送记录
            saveEmailHistory(de, date, StatisticConstants.EMAIL_SEND_SUCCESS, sendType, StatisticConstants.EMAIL_TYPE_DAY,null);

            //3.邮件发送完成之后，删除报表文件
            for (String fPath : files) {
                File file = new File(fPath);
                if (file.exists()) {
                    file.delete();
                }
            }
        }catch (Exception e){
            //记录邮件发送记录
            String note = e.getMessage();
            saveEmailHistory(de, date, StatisticConstants.EMAIL_SEND_FAIL, sendType, StatisticConstants.EMAIL_TYPE_DAY, note);
            log.error("执行日报统计和邮件发送报错",e);
            throw new Exception(e);
        }
        return files;
    }

    /**
     * @description: 记录邮件发送记录
     * @author: zhouxw
     * @date: 2023-03-62 17:11:17
     * @param: [de, date, sendResult, sendType, type]
     * @return: boolean
     **/
    private boolean saveEmailHistory(DeptAndEmail de, String date, int sendResult, int sendType, int type , String note){
        try {
            StatEmailHistory eh = new StatEmailHistory();
            eh.setDeptId(de.getDeptId());
            eh.setDeptName(de.getDeptName());
            eh.setDataDate(date);
            eh.setSendResult(sendResult);
            if(StatisticConstants.EMAIL_SEND_SUCCESS == sendResult){
                //如果发送成功
                eh.setSendResultDesc(StatisticConstants.SUCCESS_DESC);
            }else{
                eh.setSendResultDesc(StatisticConstants.FAIL_DESC);
            }
            eh.setSendType(sendType);
            eh.setType(type);
            eh.setUsername(de.getUsername());
            eh.setUserId(de.getUserId());
            eh.setReceiveMailAddress(de.getEmail());
            eh.setSendTime(new Date());
            if(!StringUtils.isBlank(note)){
                eh.setNote(note.length()>=1998?note.substring(0,1998):note);
            }
            emailService.save(eh);
        }catch (Exception e){
            log.error("保存邮件发送历史失败",e);
            return false;
        }
        return true;
    }


    //校验文件导出接口返回参数中的文件路径
    private boolean validFilePath(ObjectRestResponse<String> res){
        if(res != null && res.getData() != null){
            return true;
        }
        return false;
    }

    /**
     * 车辆在线率统计、车辆日里程统计每月1号，统计上个月数据，并发送到客户邮箱
     * 每月1号 8:45 执行
     */
    @Async
    //@Scheduled(cron="0 45 8 1 * ?")
	@XxlJob("statMonthAndSendMail")
    public void statMonthAndSendMail(){


		XxlJobHelper.log("将要开始执行定时任务：locationDriftStat");

        long start = System.currentTimeMillis();
        //获取上个月的日期
        Calendar cal = Calendar.getInstance();
        Date date = new Date();
        cal.setTime(date);
        cal.add(Calendar.MONTH, -1);
        Date datePre = cal.getTime();
        try {
            statDayAndSendMailForOneMonth(datePre, StatisticConstants.EMAIL_SEND_TYPE_SCHEDULE);
            log.info("上月数据报表统计完成");
            long end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_MONTH,"统计月报并发送邮箱：定时执行跑批任务",sdfYMD.get().format(datePre) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
        }catch (Exception e){
            log.error("统计上月数据报表失败",e);
            long end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_MONTH,"统计月报并发送邮箱：定时执行跑批任务",sdfYMD.get().format(datePre) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行失败"+e.getMessage());
        }

		XxlJobHelper.log("执行定时任务结束：statMonthAndSendMail");
    }

    /**
     * 获取指定日期的统计报表数据，并通过邮件发送到客户邮箱
     * @param dayInMonth
     * @throws Exception
     */
    public void statDayAndSendMailForOneMonth(Date dayInMonth,int sendType) throws Exception{
        log.info("开始统计月报");
        //分企业进行处理
        //查询企业
        List<DeptAndEmail> emailList = deptService.findDeptAndEmail();

        int index = 0;
        for(DeptAndEmail de : emailList){

            //如果开启debug模式，就最多只发送设置数量的邮件，防止发送邮件过多，邮箱被封
            if(emailDebug){
                index ++;
                if(index > emailDebugMaxCount){
                    break;
                }
            }

            //执行数据统计和发送邮件
            statAndSendMailForMonth(de, dayInMonth,sendType);

        }
        log.info("月报统计、发送邮件处理完成");
    }

    public void statAndSendMailForMonth(DeptAndEmail de, Date dayInMonth, int sendType) throws Exception{

        String month = sdfYM.get().format(dayInMonth);

        long startSecondTimestamp = DateUtil.getMonthFirstSecondTimestamp(dayInMonth.getTime()/1000);
        long endSecondTimestamp = DateUtil.getMonthLastSecondTimestamp(dayInMonth.getTime()/1000);

        //存放附件
        List<String> files = new ArrayList<>();

        //1.统计全部报表指定日期的数据，并导出到文件中
        long deptId = de.getDeptId();
        String deptName = de.getDeptName();
        String email = de.getEmail();



        //1.1 车辆日里程统计
        ObjectRestResponse<String> res6 = statController.exportDayMileage(null,deptId+"",startSecondTimestamp,endSecondTimestamp,null,null,null,null,null,emailTmpFilePath,deptName+"_"+month);
        String filePath6 = emailTmpFilePath + res6.getData().substring(res6.getData().lastIndexOf("/")+1);
        files.add(filePath6);

        //1.2 车辆在线率统计
        /*ObjectRestResponse<String> res7 = statController.exportRateStatistics(null,deptId+"",startSecondTimestamp,endSecondTimestamp,null,null,null,null,null,emailTmpFilePath,deptName+"_"+month);
        String filePath7 = emailTmpFilePath + res7.getData().substring(res7.getData().lastIndexOf("/")+1);
        files.add(filePath7);*/


        //2.发送到用户邮箱
        MailRequest mr = new MailRequest();
        mr.setSendTo(email);
        mr.setText(deptName+"，您好：<br /> &nbsp;&nbsp;&nbsp;&nbsp;附件为您的"+month+"数据月报表，请查收。");
        mr.setFilePath(files);
        mr.setSubject(month+"数据月报表");
        mailUtil.sendHtmlMail(mr);

        //记录邮件发送记录
        saveEmailHistory(de, month, StatisticConstants.EMAIL_SEND_SUCCESS, sendType, StatisticConstants.EMAIL_TYPE_MONTH,null);

        //3.邮件发送完成之后，删除报表文件
        for(String fPath : files){
            File file = new File(fPath);
            if(file.exists()){
                file.delete();
            }
        }
    }


    /**
     * 按天统计车辆数据大小。
     */
    //@Scheduled(cron = "0 30 1 * * ?")
	@XxlJob("dailyCarMegaByte")
    public void dailyCarMegaByte () {


		XxlJobHelper.log("将要开始执行定时任务：dailyCarMegaByte");

        Map<String, Long> carByte = new HashMap<>();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        String today = new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime());
        String todayStart = today + " 00:00:00";
        String todayEnd = today + " 23:59:59";
        List<BdmDsmData> attachList = this.bdmDsmDataMapper.getDailyCarAttach(todayStart, todayEnd);
        if ((attachList != null) && (!attachList.isEmpty())) {
            for (BdmDsmData attach : attachList) {
                String carKey = attach.getLicencePlate() + "-" + attach.getLicenceColor();
                if (!carByte.containsKey(carKey)) {
                    carByte.put(carKey, 0L);
                }

                long numByte = carByte.get(carKey);
                if ((attach.getBin() != null) && StringUtils.isNotBlank(attach.getBin())) {
                    numByte += this.binarySize;
                }
                if ((attach.getMp4() != null) && StringUtils.isNotBlank(attach.getMp4())) {
                    numByte += this.videoSize;
                }
                if ((attach.getJpg1() != null) && StringUtils.isNotBlank(attach.getJpg1())) {
                    numByte += this.imgSize;
                }
                if ((attach.getJpg2() != null) && StringUtils.isNotBlank(attach.getJpg2())) {
                    numByte += this.imgSize;
                }
                if ((attach.getJpg3() != null) && StringUtils.isNotBlank(attach.getJpg3())) {
                    numByte += this.imgSize;
                }

                carByte.put(carKey, numByte);
            }
        }

        List<BdmVideoPlay> videoPlayList = new ArrayList<>();
        try {
            int startTime = (int) (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(todayStart).getTime() / 1000);
            int endTime = (int) (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(todayEnd).getTime() / 1000);
            videoPlayList = this.bdmVideoPlayMapper.getDailyVideoPlay(startTime, endTime);
        } catch (ParseException e) {
            log.error("fail pass timestamp when daily car mega byte", e);
        }

        if ((videoPlayList != null) && (!videoPlayList.isEmpty())) {
            for (BdmVideoPlay videoPlay : videoPlayList) {
                Integer playStartTime = videoPlay.getPlayStartTime();
                Integer playEndTime = videoPlay.getPlayEndTime();
                if ((playEndTime == null) || (playStartTime >= playEndTime)) {
                    playEndTime = playStartTime + 300;
                }

                String carKey = videoPlay.getLicencePlate() + "-" + videoPlay.getLicenceColor();
                if (!carByte.containsKey(carKey)) {
                    carByte.put(carKey, 0L);
                }

                long numByte = carByte.get(carKey);
                numByte += ((long) this.bytePerSec * (playEndTime - playStartTime));
                carByte.put(carKey, numByte);
            }
        }

        List<DailyCarMegaByte> list = new ArrayList<>();
        for (Map.Entry<String, Long> entry : carByte.entrySet()) {
            String tmp = entry.getKey();
            String[] tmpList = tmp.split("-");
            double megaByte = ((double) entry.getValue()) / (1024 * 1024);
            DailyCarMegaByte obj = new DailyCarMegaByte();
            obj.setLicencePlate(tmpList[0]);
            obj.setLicenceColor(Integer.parseInt(tmpList[1]));
            obj.setYmd(today);
            obj.setMegaByte(megaByte);

			//查询车辆基本信息
			List<BdmVehicle> vList = vehicleService.list(Wrappers.lambdaQuery(BdmVehicle.class).eq(BdmVehicle::getLicencePlate,obj.getLicencePlate()).eq(BdmVehicle::getLicenceColor,obj.getLicenceColor()).eq(BdmVehicle::getIsDel,0));
			if(vList != null && vList.size() > 0){
				BdmVehicle bv = vList.get(0);
				obj.setVehicleId(bv.getId());
				obj.setDeptId(bv.getDeptId());
				obj.setVehicleUseType(Integer.parseInt(bv.getVehicleUseType()));
				obj.setVehicleOwnerId(bv.getVehicleOwnerId());
				obj.setAccessMode(bv.getAccessMode());
			}

            list.add(obj);
        }
        if (!list.isEmpty()) {
            this.dailyCarMegaByteMapper.setDaily(list);
        }

		XxlJobHelper.log("执行定时任务结束：dailyCarMegaByte");
    }

	/**
	 * 车辆离线位移记录生成
	 */
	@XxlJob("dailyOffDistance")
	public void dailyOffDistance () {
		String yesterday = this.dateFormat.format(new Date(System.currentTimeMillis() - (24 * 60 * 60 * 1000)));
		String startTimeStr = yesterday + " 00:00:00";
		String endTimeStr = yesterday + " 23:59:59";
		this.lastOnlineService.insertFromAll(startTimeStr, endTimeStr);
		List<LastOnline> onOffList = this.lastOnlineService.list(new QueryWrapper<LastOnline>().orderByAsc("start_time"));
		if ((onOffList == null) || onOffList.isEmpty()) {
			return;
		}

		Map<Integer, List<LastOnline>> carOnOffMap = new HashMap<>();
		for (LastOnline onOff : onOffList) {
			Date startDate= onOff.getStartTime();
			Date endDate = onOff.getEndTime();
			if ((startDate == null) || (endDate == null)) {
				continue;
			}

			long startTime = startDate.getTime();
			long endTime = endDate.getTime();
			if (startTime > endTime) {
				continue;
			}

			Integer vehicleId = onOff.getVehicleId();
			if (!carOnOffMap.containsKey(vehicleId)) {
				carOnOffMap.put(vehicleId, new ArrayList<>());
			}

			List<LastOnline> carOnOffList = carOnOffMap.get(vehicleId);
			carOnOffList.add(onOff);
		}

		this.lastOnlineService.truncate();
		List<LastOnline> lastOnlineList = new ArrayList<>();
		List<OfflineMove> offlineMoveList = new ArrayList<>();
		for (int vehicleId : carOnOffMap.keySet()) {
			List<LastOnline> carOnOffList = carOnOffMap.get(vehicleId);
			int numCarOnOff = carOnOffList.size();
			if (numCarOnOff == 1) {
				carOnOffList.get(0).setId(null);
				lastOnlineList.add(carOnOffList.get(0));
				continue;
			}
			for (int i = 0; i < numCarOnOff - 1; ++i) {
				String licenceColor = carOnOffList.get(i).getLicenceColor();
				String licencePlate = carOnOffList.get(i).getLicencePlate();
				long previousOffTime = carOnOffList.get(i).getEndTime().getTime();
				long nextOnTime = carOnOffList.get(i + 1).getStartTime().getTime();
				long nextOffTime = carOnOffList.get(i + 1).getEndTime().getTime();
				if (previousOffTime >= nextOnTime) {
					carOnOffList.get(i).setEndTime(new Date(Math.max(previousOffTime, nextOffTime)));
					carOnOffList.remove(i + 1);
					--numCarOnOff;
					if (i == (numCarOnOff - 1)) {
						carOnOffList.get(i).setId(null);
						lastOnlineList.add(carOnOffList.get(i));
					} else {
						--i;
					}

					continue;
				}
				if (i == (numCarOnOff - 2)) {
					carOnOffList.get(i + 1).setId(null);
					lastOnlineList.add(carOnOffList.get(i + 1));
				}

				List<Location> locationList = this.alarmService.getLocationFromTimeSeg(Integer.parseInt(licenceColor), licencePlate, previousOffTime / 1000, nextOnTime / 1000);
				if ((locationList == null) || locationList.isEmpty()) {
					continue;
				}

				int numLocation = locationList.size();
				double distance = 0;
				for (int j = 0; j < numLocation - 1; ++j) {
					double startLon = locationList.get(j).getLongitude();
					double startLat = locationList.get(j).getLatitude();
					double endLon = locationList.get(j + 1).getLongitude();
					double endLat = locationList.get(j + 1).getLatitude();
					distance += DistanceUtils.wgs84Distance(startLon, startLat, endLon, endLat);
				}
				if (distance > 0) {
					OfflineMove offlineMove = new OfflineMove();
					offlineMove.setVehicleId(vehicleId);
					offlineMove.setLicenceColor(licenceColor);
					offlineMove.setLicencePlate(licencePlate);
					offlineMove.setStartTime(new Date(previousOffTime));
					offlineMove.setEndTime(new Date(nextOnTime));
					offlineMove.setDistance(distance);
					offlineMoveList.add(offlineMove);
				}
			}
		}

		this.lastOnlineService.saveBatch(lastOnlineList);
		this.offlineMoveService.saveBatch(offlineMoveList);
	}
}
