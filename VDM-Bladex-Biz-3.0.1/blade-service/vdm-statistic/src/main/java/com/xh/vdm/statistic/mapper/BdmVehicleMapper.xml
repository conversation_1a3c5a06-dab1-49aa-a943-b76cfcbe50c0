<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmVehicleMapper">


    <select id="getAllVehicleCount" resultType="long">
        select count(*) from bdm_vehicle bv
        where is_del = 0
        and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>

    <select id="getAllVehicleCountByDeptId" resultType="long">
        select count(*) from bdm_vehicle bv
        where is_del = 0 and bv.operating_status = '1' and  bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        )
    </select>

    <select id="getCertExpiredCount" resultType="long">
        select count(*) from bdm_vehicle bv
        where 1 = 1
        and is_del = 0
        and operating_status = '1'
        and bv.certificate_expire_date is not null
        and bv.certificate_expire_date &lt;= #{expireDate}
        and bv.certificate_expire_date > now()
        and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>

    <select id="getCertHasExpiredCount" resultType="long">
        select count(*) from bdm_vehicle bv
        where 1 = 1
        and is_del = 0
        and operating_status = '1'
        and bv.certificate_expire_date is not null
        and bv.certificate_expire_date &lt;= #{expireDate}
        and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>

    <select id="getRunningCount" resultType="long">
        select count(*) from bdm_vehicle bv
        where bv.operating_status = '1'
        and bv.is_del = 0
        <!--and (bv.certificate_expire_date IS NULL or bv.certificate_expire_date > now() )-->
        and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>

    <select id="getRunningCountByDeptId" resultType="long">
        select count(*) from bdm_vehicle bv
        where bv.is_del = 0 and bv.operating_status = '1' and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        )  )
    </select>


    <select id="getOfflineCountBeforeDate" resultType="long">
        select count(*) from
        (
            select distinct licence_plate, licence_color from
            (
                select btr.licence_plate, btr.licence_color, max(btr.beat_time) date
                from bdm_vehicle_state btr, bdm_vehicle bv
                where btr.licence_plate = bv.licence_plate and btr.licence_color = bv.licence_color and bv.operating_status = '1'  and ( bv.dept_id in (
                <foreach collection="deptIds" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
                group by btr.licence_plate, btr.licence_color
            ) a
            where a.date &lt; #{date,jdbcType=VARCHAR}::date
        ) b
    </select>

    <select id="getOfflineListBeforeDate" resultType="com.xh.vdm.statistic.entity.VehicleAndCount">
        select * from (
            select id vehicle_id, licence_plate, licence_color,date_part('day',to_date(#{date},'yyyy-MM-dd'))-date_part('day', date) count from
            (
            select bv.id, bvs.licence_plate, bvs.licence_color, max(bvs.beat_time) date
            from bdm_vehicle_state bvs, bdm_vehicle bv
            where 1 = 1
            and bvs.te_state = 0
            and bvs.licence_plate = bv.licence_plate and bvs.licence_color = bv.licence_color
            and bv.operating_status = '1'
            and ( bv.dept_id in (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
            <if test="userId != null and userId != ''">
                or bv.id = any
                (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT})
            </if>
            )
            group by bv.id, bvs.licence_plate, bvs.licence_color
            ) a
            where a.date &lt; to_date(#{date,jdbcType=VARCHAR},'yyyy-MM-dd')
        ) c
        order by count desc
        limit #{limit,jdbcType=INTEGER}
    </select>


    <select id="getOfflineBeforeDatePage" resultType="com.xh.vdm.statistic.entity.VehicleAndCount">
        select * from (
        select id vehicle_id, licence_plate, licence_color,date_part('day',#{date}) - date_part('day', date) count from
        (
        select bv.id, bvs.licence_plate, bvs.licence_color, max(bvs.beat_time) date
        from bdm_vehicle_state bvs, bdm_vehicle bv
        where 1 = 1
        and bvs.te_state = 0
        and bvs.licence_plate = bv.licence_plate and bvs.licence_color = bv.licence_color
        and bv.operating_status = '1'
        <if test="licencePlate != null and licencePlate != ''">
            and bvs.licence_plate like concat('%', #{licencePlate}, '%')
        </if>
        <if test="licenceColor != null">
            and bvs.licence_color = #{licenceColor}
        </if>
        <if test="deptIds != null and deptIds.size() gt 0">
            and bv.dept_id in
            <foreach collection="deptIds" item="deptId" index="i" separator="," open="(" close=")">
                #{deptId, jdbcType=BIGINT}
            </foreach>
        </if>
        group by bv.id, bvs.licence_plate, bvs.licence_color
        ) a
        where a.date &lt; to_date(#{date,jdbcType=VARCHAR},'yyyy-MM-dd')
        ) c
        order by count desc
    </select>

    <select id="getRunningVehicle" resultType="com.xh.vdm.statistic.entity.VehicleBase">
        select licence_plate licencePlate, licence_color plateColor from bdm_vehicle bv
        where bv.operating_status = '1' and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>

    <select id="getUnKnowExpireCount" resultType="long">
        select count(*) from bdm_vehicle bv
        where operating_status = '1'
        and bv.certificate_expire_date is null and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>

    <select id="getStopRunningCount" resultType="long">
        select count(*) from bdm_vehicle bv
        where bv.operating_status = '2' and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>

    <select id="getInNetCount" resultType="long">
        select count(*) from bdm_vehicle bv
        where bv.net_sign_time &lt;= now()  and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>

    <select id="getGoOnlineCount" resultType="long">
        select count(*) from (
            select distinct(bv.id) from bdm_vehicle bv, bdm_terminalonlinerecord btr
            where 1 = 1
            and bv.id = btr.vehicle_id
            and (btr.on_line_time >= (select substring(now(),1,10)) or (btr.on_line_time &lt; (select substring(now(),1,10)) and btr.off_line_time >= (select substring(now(),1,10))))
            and ( bv.dept_id in (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
        ) a
    </select>

    <select id="getGoOnlineCountAllToday" resultType="long">
        select count(*) from stat_veh_online_day svod
        left join bdm_vehicle bv on svod.licence_plate = bv.licence_plate and svod.licence_color = bv.licence_color
        where svod.total_duration >  0
        AND bv.operating_status = '1'
        and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>

    <select id="getGoOnlineCountAllMonth" resultType="long">
        select count(*) from
        (
        select distinct bv.id
        from bdm_terminalonlinerecord_${month} bt
        left join bdm_vehicle bv on bt.vehicle_id = bv.id
        where (((bt.off_line_time &lt;= to_timestamp(concat(#{lastDate},' 23:59:59'),'yyyy-mm-dd hh24:mi:ss') or bt.off_line_time is null)
        and bt.off_line_time >= to_timestamp(#{monthDate},'yyyy-mm-dd hh24:mi:ss'))
        or (bt.on_line_time >= to_timestamp(#{monthDate},'yyyy-mm-dd hh24:mi:ss') and bt.on_line_time &lt;= to_timestamp(concat(#{lastDate}, ' 23:59:59'),'yyyy-mm-dd hh24:mi:ss') ))
        AND bv.operating_status = '1'
        <if test="deptIds != null and deptIds.size() > 0 or userId != null">
            and (
            <if test="deptIds != null and deptIds.size() > 0">
                bv.dept_id in (
                <foreach collection="deptIds" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT})
            </if>
            )
        </if>
        ) a
    </select>


    <select id="getGoOnlineDurationListAllMonthByVehicleId" resultType="com.xh.vdm.statistic.entity.VehicleAndCount">
        select licence_plate, licence_color, vehicleId, sum(duration) count
        from (
            select bv.licence_plate, bv.licence_color, bv.id vehicleId, (extract(epoch from coalesce(bt.off_line_time,now())) - extract(epoch from bt.on_line_time)) duration
            from bdm_terminalonlinerecord_${month} bt
            left join bdm_vehicle bv on bt.vehicle_id = bv.id
            where (((bt.off_line_time &lt;= to_timestamp(concat(#{lastDate},' 23:59:59'),'yyyy-mm-dd hh24:mi:ss') or bt.off_line_time is null) and bt.off_line_time >= to_timestamp(#{monthDate},'yyyy-mm-dd hh24:mi:ss'))
            or (bt.on_line_time >= to_timestamp(#{monthDate},'yyyy-mm-dd hh24:mi:ss') and bt.on_line_time &lt;= to_timestamp(concat(#{lastDate}, ' 23:59:59'),'yyyy-mm-dd hh24:mi:ss')))
            AND bv.operating_status = '1'
            <if test="vehicleIds != null and vehicleIds.size() > 0">
                and bv.id in (
                <foreach collection="vehicleIds" item="vehicleId" separator=",">
                    #{vehicleId}
                </foreach>
                )
            </if>
        ) a
        group by licence_plate, licence_color, vehicleId
    </select>


    <select id="getVehicleOnOfflineRecord" resultType="com.xh.vdm.statistic.entity.VehicleAndOnOfflineRecord">
        select distinct bv.licence_plate, bv.licence_color, bv.id vehicleId, substring(bt.off_line_time,1,10) offlineTime,substring(bt.on_line_time,1,10) onlineTime
        from bdm_terminalonlinerecord_${month} bt
        left join bdm_vehicle bv on bt.vehicle_id = bv.id
        where 1 = 1
        and bt.on_line_time like concat(#{statDate},'%')
        AND bv.operating_status = '1'
        and bv.id in (
        <foreach collection="vehicleIds" item="vehicleId" separator=",">
            #{vehicleId}
        </foreach>
        )
    </select>


    <select id="getVehicleOnOfflineRecordMonth" resultType="com.xh.vdm.statistic.entity.VehicleAndOnOfflineRecord">
        select distinct bv.licence_plate, bv.licence_color, bv.id vehicleId, coalesce(substring(to_char(bt.off_line_time,'yyyyy-mm-dd hh24:mi:ss'),1,10),to_char(now(),'yyyy-mm-dd hh24:mi:ss')) offlineTime,substring(to_char(bt.on_line_time,'yyyy-mm-dd hh24:mi:ss'),1,10) onlineTime
        from bdm_terminalonlinerecord_${month} bt
        left join bdm_vehicle bv on bt.vehicle_id = bv.id
        where 1 = 1
        AND bv.operating_status = '1'
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            and bv.id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
    </select>


    <select id="getGoOnlineCountByDate" resultType="long">
        select count(*) from (
        select distinct(bv.id) from bdm_vehicle bv, bdm_terminalonlinerecord_${month} btr
        where 1 = 1
        and bv.id = btr.vehicle_id and bv.operating_status = '1'
        and ((btr.on_line_time >= #{date} and btr.on_line_time &lt;= concat(#{date}, ' 23:59:59')) or ((btr.off_line_time &lt;= concat(#{date},' 23:59:59') or btr.off_line_time is null) and btr.off_line_time >= #{date}))
        <if test="(deptIds != null and deptIds.size() > 0) or (vehicleIds != null and vehicleIds.size() > 0)">
            and (
            <if test="deptIds != null and deptIds.size() > 0">
                bv.dept_id in (
                <foreach collection="deptIds" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="vehicleIds != null and vehicleIds.size() > 0">
                or (btr.vehicle_id in (
                <foreach collection="vehicleIds" separator="," item="vehicleId">
                    #{vehicleId}
                </foreach>
                ))
            </if>
            )
        </if>
        ) a
    </select>

    <select id="getGoOnlineDurationListByDateAndVehicleId" resultType="com.xh.vdm.statistic.entity.VehicleAndCount">
        select licence_plate, licence_color, vehicleId, sum(duration) count
        from(
            select bv.licence_plate, bv.licence_color, bv.id vehicleId,(extract(epoch from coalesce( btr.off_line_time, if(now() &lt; concat(#{date},' 23:59:59'), now(), concat(#{date},' 23:59:59')) ) ) - extract(epoch from if(btr.on_line_time&lt;#{date},#{date}, btr.on_line_time) )) duration
            from bdm_vehicle bv, bdm_terminalonlinerecord_${month} btr
            where 1 = 1
            and bv.id = btr.vehicle_id
            and ((btr.on_line_time >= #{date} and btr.on_line_time &lt;= concat(#{date}, ' 23:59:59')) or ((btr.off_line_time &lt;= concat(#{date},' 23:59:59') or btr.off_line_time is null) and btr.off_line_time >= #{date}))
            and btr.vehicle_id in (
            <foreach collection="vehicleIds" separator="," item="vehicleId">
                #{vehicleId}
            </foreach>
            )
        ) a
        group by licence_plate, licence_color, vehicleId
    </select>

    <select id="getOnlineCount" resultType="long">
        select count(*) from bdm_vehicle bv, bdm_vehicle_state bvs
        where bv.licence_plate = bvs.licence_plate and bv.licence_color = bvs.licence_color and bvs.te_state = 1  and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>



    <select id="getUserVehicleList" resultType="com.xh.vdm.statistic.entity.BdmVehicle">
        select id, dept_id, licence_color, licence_plate, vehicle_use_type, vehicle_owner_id, access_mode from bdm_vehicle where 1 = 1
        <if test="(deptList != null and deptList.size() gt 0) and (vehicleList == null or vehicleList.size() lte 0)">
            and dept_id in
            <foreach collection="deptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="(deptList == null or deptList.size() lte 0) and (vehicleList != null and vehicleList.size() gt 0)">
            and id in
            <foreach collection="vehicleList" item="vehicleId" index="i" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="deptList != null and deptList.size() gt 0 and vehicleList != null and vehicleList.size() gt 0">
            and (
            dept_id in
            <foreach collection="deptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
            or id in
            <foreach collection="vehicleList" item="vehicleId" index="i" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
    </select>


    <select id="getRunningVehicleCount" resultType="long">
        select count(*) from bdm_vehicle bv, bdm_vehicle_state bvs
        where bv.licence_plate = bvs.licence_plate and bv.licence_color = bvs.licence_color and bvs.te_state = 1 and bvs.speed > 0  and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>


    <select id="getOnlineDaysCount" resultType="com.xh.vdm.statistic.vo.response.OnlineDaysCountResponse">
        ${sql}
    </select>

    <select id="getTotalMileage" resultType="com.xh.vdm.statistic.entity.VehicleAndMileage">
        select bv.licence_plate licencePlate, bv.licence_color licenceColor, sum(total_mileage) mileage from stat_veh_running_state_day_${month} svrs, bdm_vehicle bv
        where svrs.licence_plate = bv.licence_plate and svrs.licence_color = bv.licence_color
        and svrs.date in (
            <foreach collection="dateList" item="date" open="" close="" separator=",">
                #{date}
            </foreach>
        ) and bv.id in (
            <foreach collection="vehicleIdList" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
        )
        group by bv.licence_plate, bv.licence_color
    </select>


    <select id="getTotalMileagePage" resultType="com.xh.vdm.statistic.entity.VehicleAndMileage">
        select licence_plate licencePlate, licence_color licenceColor, vehicle_id, total_mileage mileage
        from stat_veh_running_state_day_${month} svrs
        where 1 = 1
        and date = #{statDate}
        and svrs.dept_id in (
        <foreach collection="deptIds" item="deptId" open="" close="" separator=",">
            #{deptId}
        </foreach>
        )
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        order by total_mileage desc
    </select>

    <select id="getTotalMileageMonthPage" resultType="com.xh.vdm.statistic.entity.VehicleAndMileage">
        select licence_plate licencePlate, licence_color licenceColor, total_mileage mileage, vehicle_id vehicleId
        from stat_veh_running_state_month svrs
        where 1 = 1
        and month = #{month}
        and svrs.dept_id in (
        <foreach collection="deptIds" item="deptId" open="" close="" separator=",">
            #{deptId}
        </foreach>
        )
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        order by total_mileage desc
    </select>

    <select id="getVehicleByLicencePlateAndColor" resultType="com.xh.vdm.statistic.entity.BdmVehicle">
        select * from bdm_vehicle where concat(licence_plate, '~', licence_color) in
        <if test="keyList != null and keyList.size() > 0">
            <foreach collection="keyList" item="key" separator="," open="(" close=")">
                #{key}
            </foreach>
        </if>
    </select>

    <select id="getVehicleOnlineCount" resultType="com.xh.vdm.statistic.entity.VehicleAndDateAndCount">
        <foreach collection="dateList" item="date" separator=" union ">
            select licence_plate, licence_color_code licenceColor,month date, (
                <foreach collection="date.dateList" item="d" separator="+">
                    if(${d}>0,1,0)
                </foreach>
            ) count
            from cache_vehicle_online_rate
            where month = #{date.month} and concat(licence_plate, '~', licence_color_code) in (
                <foreach collection="vehicleList" item="v" separator=",">
                    #{v}
                </foreach>
            )
        </foreach>
    </select>

    <select id="getTotalMileageByDeptIds" resultType="double">
        select coalesce(sum(split_part(split_part(d${date},'#',-2),'#',1)),0)
        from stat_complete_${month} sc
        left join bdm_vehicle bv on sc.licence_plate = bv.licence_plate and sc.licence_color::text = bv.licence_color
        where 1 = 1
        <if test="deptList != null">
            and bv.dept_id in (
            <foreach collection="deptList" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
        </if>
    </select>

    <select id="getVehiclesByCondition" resultType="com.xh.vdm.statistic.entity.BdmVehicle">
        select * from bdm_vehicle bv
        where 1 = 1
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and bv.id in
            <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
            and (
            <if test="request.deptList != null and request.deptList.size() > 0">
                bv.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="request.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="request.vehicleUseType != null ">
            and bv.vehicle_use_type in
            <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.vehicleOwnerId != null">
            and bv.vehicle_owner_id = #{request.vehicleOwnerId}
        </if>
        <if test="request.accessMode != null">
            and bv.access_mode = #{request.accessMode}
        </if>
    </select>


    <select id="getVehiclesByConditionCount" resultType="long">
        select count(*) from bdm_vehicle bv
        where 1 = 1
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and bv.id in
            <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
            and (
            <if test="request.deptList != null">
                bv.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="request.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="request.vehicleUseType != null ">
            and bv.vehicle_use_type in
            <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.vehicleOwnerId != null">
            and bv.vehicle_owner_id = #{request.vehicleOwnerId}
        </if>
        <if test="request.accessMode != null">
            and bv.access_mode = #{request.accessMode}
        </if>
    </select>

    <select id="getVehicleCommonInfo" resultType="com.xh.vdm.statistic.entity.VehicleCommonInfo">
        select bv.id vehicleId, bv.licence_plate, bv.licence_color, bv.access_mode,  bd.dept_name, btlp.name platform_name, bv.vehicle_use_type, coalesce(btpp.name,'非营运车辆') vehicle_owner
        from bdm_vehicle bv
                 left join blade_dept bd on bv.dept_id = bd.id
                 left join bam_third_low_platform btlp on btlp.operator_no = bv.center_id::text
                 left join bam_third_party_platform btpp on btpp.id = bv.vehicle_owner_id
        where bv.id in (
            <foreach collection="vehicleIds" item="id" separator=",">
                #{id}
            </foreach>
        )
    </select>

    <select id="getVehicleMegaByteDaily" resultType="com.xh.vdm.statistic.vo.response.CarMegaByteResponse">
        select dcm.dept_id, dcm.licence_plate, dcm.licence_color, dcm.vehicle_owner_id, dcm.vehicle_use_type, dcm.ymd stat_date, dcm.mega_byte, dcm.access_mode
        from daily_car_mega_byte dcm
        where ymd in (
            <foreach collection="dateList" item="date" separator=",">
                #{date}
            </foreach>
        )
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and dcm.vehicle_id in
            <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
            and (
            <if test="request.deptList != null">
                dcm.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="request.userId != null">
                or dcm.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="request.vehicleUseType != null ">
            and dcm.vehicle_use_type in
            <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.vehicleOwnerId != null">
            and dcm.vehicle_owner_id = #{request.vehicleOwnerId}
        </if>
        <if test="request.accessMode != null">
            and dcm.access_mode = #{request.accessMode}
        </if>
        order by stat_date desc
    </select>

    <select id="getVehicleBaseListAll" resultType="com.xh.vdm.statistic.entity.VehicleBaseWithId">
        select id, licence_plate, licence_color from bdm_vehicle
        where 1 = 1
          and is_del = 0
          <if test="licencePlate != null and licencePlate != ''">
              and licence_plate like concat('%',#{licencePlate},'%')
          </if>
          and (
            <if test="deptIds != null and deptIds.size() > 0">
                dept_id in (
                <foreach collection="deptIds" separator="," item="deptId">
                    #{deptId}
                </foreach>
                )
            </if>
            <if test="vehicleIds != null and vehicleIds.size() > 0">
                or id in (
                <foreach collection="vehicleIds" separator="," item="vehicleId">
                    #{vehicleId}
                </foreach>
                )
            </if>
        )
    </select>

    <select id="getNumVehicleGroupByDept" resultType="java.util.Map">
        select dept_id, count(1) as num from bdm_vehicle where 1 = 1
        <if test="request.serviceRole == 1">
            and service_state != '2'
        </if>
        <if test="request.serviceRole == 2">
            and service_state != '1'
        </if>
        <if test="request.deptList != null and request.deptList.size() gt 0">
            and dept_id in
            <foreach collection="request.deptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        group by dept_id
    </select>


    <select id="getVehicleListByPage" resultType="com.xh.vdm.statistic.entity.BdmVehicle">
        select * from bdm_vehicle
        where is_del = 0
        and (dept_id in
            <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">#{deptId}
            </foreach>
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or id in
            <foreach collection="vehicleIds" item="vehicleId" separator="," open="(" close=")">
                #{vehicleId}
            </foreach>
        </if>
        )
    </select>
</mapper>
