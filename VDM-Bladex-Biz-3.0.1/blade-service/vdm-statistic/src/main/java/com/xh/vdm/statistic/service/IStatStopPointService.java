package com.xh.vdm.statistic.service;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.DateListAndMonth;
import com.xh.vdm.statistic.entity.StatStopPoint;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.response.StopPointDetailResponse;
import com.xh.vdm.statistic.vo.response.StopPointStatResponse;

import java.util.List;

/**
 * <p>
 * 车辆停止点表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-22
 */
public interface IStatStopPointService extends IService<StatStopPoint> {

    /**
     * @description: 统计指定日期的停止点
     *
     * @author: zhouxw
     * @date: 2022/12/22 10:39 AM
     * @param: [day：yyyyMMdd]
     * @return: void
     **/
    void statStopPoint(String day) throws Exception;

    /**
     * @description: 新增或更新停止点信息
     * @author: zhouxw
     * @date: 2022/12/23 9:50 AM
     * @param: [list , month: yyyyMM]
     * @return: void
     **/
    void saveOrUpdate(List<StatStopPoint> list, String month) throws Exception;

    /**
     * @description: 删除数据
     * @author: zhouxw
     * @date: 2022/12/23 10:14 AM
     * @param: [statDate: yyyy-MM-dd]
     * @return: void
     **/
    void delete(String statDate);

    /**
     * @description: 根据条件查询停止点
     * 按照停止开始时间正序排序
     * @author: zhouxw
     * @date: 2023-02-53 17:29:59
     * @param: [licenceCode, date, startSecondTimestamp, endSecondTimestamp]
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatStopPoint>
     **/
    List<StatStopPoint> findStopPointByCondition(String licenceCode, String date, long startSecondTimestamp, long endSecondTimestamp) throws Exception;

	/**
	 * 查询最近的停靠点
	 * @param licencePlate
	 * @param licenceColor
	 * @param statDate
	 * @return
	 * @throws Exception
	 */
	StatStopPoint findLatestStopPoint(String licencePlate, String licenceColor, String statDate) throws Exception;

	/**
	 * 统计停靠点数量和累计停靠时间
	 * @param request
	 * @return
	 * @throws Exception
	 */
	IPage<StopPointStatResponse> statStopInfo(CommonBaseRequest request, List<DateListAndMonth> dm, IPage<StopPointStatResponse> page) throws Exception;

	/**
	 * 车辆停车明细
	 * @param request
	 * @param dm
	 * @param page
	 * @return
	 * @throws Exception
	 */
	IPage<StopPointDetailResponse> findStopPointDetailInfo(CommonBaseRequest request, List<DateListAndMonth> dm, IPage<StopPointDetailResponse> page) throws Exception;

}
