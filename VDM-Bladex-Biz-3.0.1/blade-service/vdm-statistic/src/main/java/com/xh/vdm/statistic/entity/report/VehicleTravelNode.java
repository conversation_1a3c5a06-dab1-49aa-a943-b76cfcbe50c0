package com.xh.vdm.statistic.entity.report;

import lombok.Data;

/**
 * 车辆运行节点
 */
@Data
public class VehicleTravelNode {
	//车牌颜色描述
	private String licenceColorDesc;
	//车牌号
	private String licencePlate;
	//车牌颜色
	private String licenceColor;
	//总里程（km）
	private Double totalMileage;
	//总时长
	private Double totalDuration;
	//总时长描述
	private String totalDurationDesc;
	//平均里程 总里程/在线天数（km）
	private Double averageMileage = 0D;
	//平均时长 在线时长/在线天数
	private Double averageDuration = 0D;
	//最大速度
	private Double maxSpeed;
	//平均速度
	private Double averageSpeed = 0D;
	//上线天数
	private Long goOnlineDaysCount;
}
