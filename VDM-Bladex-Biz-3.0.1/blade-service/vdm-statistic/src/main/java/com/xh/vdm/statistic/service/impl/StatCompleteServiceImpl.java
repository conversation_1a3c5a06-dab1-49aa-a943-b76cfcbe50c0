package com.xh.vdm.statistic.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.StatCompleteMapper;
import com.xh.vdm.statistic.mapper.VehicleMapper;
import com.xh.vdm.statistic.service.IBdmVehicleService;
import com.xh.vdm.statistic.service.ILocationService;
import com.xh.vdm.statistic.service.IStatCompleteService;
import com.xh.vdm.statistic.service.IStatTaskLogService;
import com.xh.vdm.statistic.utils.DataUtils;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.DistanceUtils;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.response.LocationCompleteDetailResponse;
import com.xh.vdm.statistic.vo.response.LocationCompleteRateResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <p>
 * 轨迹完整率表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Service
@Slf4j
public class StatCompleteServiceImpl extends ServiceImpl<StatCompleteMapper, StatComplete> implements IStatCompleteService {

    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

    @Resource
    private ILocationService locationService;

    private ExecutorService threadPool = Executors.newFixedThreadPool(10);

    @Resource
    private DataUtils dataUtils;

    @Resource
    private IStatTaskLogService taskLogService;

	@Resource
	private VehicleMapper vehicleMapper;

	@Resource
	private IBdmVehicleService vehicleService;



    @Override
    public boolean locationCompleteStat(String day) throws Exception {
        long startTotal = System.currentTimeMillis();
        try{
            //1.校验给定的日期格式
            Date statDate = null;
            try {
                statDate = sdf.parse(day);
            } catch (ParseException e) {
                log.error("[轨迹完整率计算]执行出现异常，日期 " + day + " 格式错误 "  , e);
                throw new Exception("日期格式错误：" + day);
            }

            log.info("[轨迹完整率计算] 日期格式校验完成：日期格式正确");
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_COMPLETE , "[轨迹完整率]校验日期格式完成");


            //判断临时表是否存在，不存在，则根据模板表新建；存在，则清空
            dataUtils.checkTableExistAndCreate(StatisticConstants.LOCATION_COMPLETE_DATA_TEMP_TABLE, StatisticConstants.LOCATION_COMPLETE_DATA_TEMPLATE_TABLE);
            dataUtils.truncateTable(StatisticConstants.LOCATION_COMPLETE_DATA_TEMP_TABLE);
            //创建索引
            dataUtils.createIndex(StatisticConstants.LOCATION_COMPLETE_DATA_TEMP_TABLE, StatisticConstants.LICENCE_PLATE);


            //2.判断月表是否存在，如果不存在则创建
            checkTableExistAndCreate(day.substring(0 , 6));
            log.info("[轨迹完整率计算] 月表判断完成");
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_COMPLETE , "[轨迹完整率]判断月表是否存在完成");

            //3.根据指定的日期获取车辆列表
            long start1 = System.currentTimeMillis();
            List<VehicleBase> licencePlatesList = locationService.findUploadLicencePlatesByDay(day);
            log.info("[轨迹完整率计算] 车辆列表获取完成，共有 {} 辆车待处理");
            long end1 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_COMPLETE , "[轨迹完整率]获取车辆列表完成，共 "+(licencePlatesList==null?"0":licencePlatesList.size()) +" 辆车共耗时：" +(end1 - start1)+" ms");

			//查询车辆基本信息
			List<String> vcList = new ArrayList<>();
			licencePlatesList.forEach(item -> {
				vcList.add(item.getLicencePlate()+"~"+item.getLicenceColor());
			});
			List<BdmVehicle> vList = vehicleService.findVehicleByLicencePlateAndColor(vcList);
			Map<String,BdmVehicle> vehicleMap = new HashMap<>();
			if(vList != null){
				vList.forEach(item -> {
					String key = item.getLicencePlate()+"~"+item.getLicenceColor();
					vehicleMap.put(key, item);
				});
			}

            //4.对每辆车的轨迹完整率进行计算
            long start2 = System.currentTimeMillis();
            List<CompleteDataNode> completeDataNodes = new ArrayList<>();
            List<CompleteDataNode> completeDataNodesList = Collections.synchronizedList(completeDataNodes);
            CountDownLatch countDownLatch = new CountDownLatch(licencePlatesList.size());
            for(VehicleBase vehicle : licencePlatesList){
                String licencePlate = vehicle.getLicencePlate();
                long plateColor = vehicle.getLicenceColor();
                threadPool.submit(() -> {
                    try{
                        //4.1 查询车辆轨迹点列表
                        List<LocationKudu> list = locationService.findUploadLocationPointListByDay(day , licencePlate, (int)plateColor);
                        //4.2 统计车辆轨迹完整率信息
                        CompleteDataNode dataNode = locationCompleteStatByPositionList(list);
						String key = dataNode.getLicencePlate()+"~"+dataNode.getPlateColor();
						BdmVehicle ve = vehicleMap.get(key);
						if(ve != null){
							dataNode.setVehicleId(ve.getId());
							dataNode.setDeptId(ve.getDeptId());
						}
                        completeDataNodesList.add(dataNode);
                    }catch (Exception e){
                        e.printStackTrace();
                        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_COMPLETE , "[轨迹完整率]计算轨迹完整率出现异常："+e.getMessage());
                    }finally {
                        countDownLatch.countDown();
                    }
                });
            }
            countDownLatch.await();
            log.info("[轨迹完整率计算] 车辆轨迹完整率统计完成[数据待入库]");
            long end2 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_COMPLETE , "[轨迹完整率]计算轨迹完整率完成，共耗时：" +(end2 - start2)+" ms");

            //5.执行轨迹完整率数据入库（通过临时表的方式对列进行更新，提高效率）
            long start3 = System.currentTimeMillis();

            //5.1 数据入临时表
			if(completeDataNodesList != null && completeDataNodesList.size() > 0){
				baseMapper.insertTempBatch(completeDataNodesList);
			}

            long end3 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_COMPLETE , "[轨迹完整率]数据写入临时表完成，共耗时：" +(end3 - start3)+" ms");

            long start4 = System.currentTimeMillis();
            //5.2 对正式表中的数据进行插入和更新操作
            //5.2.1 对已经存在的数据执行更新操作
            //执行数据的更新
            baseMapper.updateExistData(day.substring(0,6) , day.substring(6,8));
            long end4 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_COMPLETE , "[轨迹完整率]对正式表进行更新操作成功，共耗时：" +(end4 - start4)+ "ms");

            long start5 = System.currentTimeMillis();
            //5.2.2 对新增的数据执行插入操作
            baseMapper.insertNewData(day.substring(0,6) , day.substring(6,8));
            log.info("[轨迹完整率计算] 执行数据的新增 和 更新 操作完成");
            long end5 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_COMPLETE , "[轨迹完整率]对新增的数据执行插入操作成功，共耗时：" +(end5 - start5)+ "ms");

            long start6 = System.currentTimeMillis();
            //5.3 清空临时表
            dataUtils.truncateTable(StatisticConstants.LOCATION_COMPLETE_DATA_TEMP_TABLE);
            log.info("[轨迹完整率计算] 临时表处理完成");
            long end6 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_COMPLETE , "[轨迹完整率]执行临时表清空操作成功，共耗时"+(end6 - start6)+"ms");

        }catch (Exception e){
            e.printStackTrace();
            log.error("[轨迹完整率]执行数据的录入失败");
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_COMPLETE , "[轨迹完整率]执行数据的录入失败："+e.getMessage());
            throw e;
        }
        log.info("[轨迹完整率计算] 轨迹完整率统计整体完成");
        long endTotal = System.currentTimeMillis();
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_COMPLETE , "[轨迹完整率]轨迹完整率统计完成，共耗时"+(endTotal - startTotal)+"ms");
        return true;
    }





    @Override
    public CompleteDataNode locationCompleteStatByPositionList(List<LocationKudu> list) {
        //1.对轨迹序列按照定位时间进行排序
        Collections.sort(list , (o1 , o2) -> {
            return (int) (o1.getTime() - o2.getTime());
        });

        //2.循环轨迹点，计算轨迹完整率
        //当前定位点
        LocationKudu locationNow = null;
        //上一个定位点
        LocationKudu locationOld = null;
        //总点数
        int totalPointCount = 0 ;
        //完整点数
        int completePointCount = 0;
        //大于2公里，小于10公里的路段次数
        int middleDistanceCount = 0;

        //全部里程
        double allMiles = 0;
        //完整里程
        double completeMiles = 0;

        //两个定位点之间的距离
        double perDistance = 0;

        //车牌号
        String licencePlate = "";
        //车牌颜色
        long plateColor = 0;

        for(int i = 0 ; i < list.size() ; i++){
            locationNow = list.get(i);
            if (i == 0) {
                //如果是第一个定位点
                completePointCount++;
//                licencePlate = list.get(i).getLicencePlate();
//                plateColor = list.get(i).getLicenceColor();
            } else {
                //计算两个定位点之间的距离
                perDistance = DistanceUtils.wgs84Distance(locationNow.getLongitude(),locationNow.getLatitude(),locationOld.getLongitude() , locationOld
                        .getLatitude());
                //轨迹连续计算方法
                if (perDistance <= 2000) {// 轨迹连续
                    completePointCount++;
                    completeMiles = completeMiles + perDistance;
                    allMiles = allMiles + perDistance;
                    middleDistanceCount = 0;
                } else if (perDistance > 2000 && perDistance <= 10000) {
                    middleDistanceCount++;
                    if (middleDistanceCount > 5) {// 轨迹不连续
                        allMiles = allMiles + perDistance;
                    } else {// 轨迹连续
                        completePointCount++;
                        completeMiles = completeMiles + perDistance;
                        allMiles = allMiles + perDistance;
                    }
                } else if (perDistance > 10000) {// 轨迹不连续
                    middleDistanceCount = 0;
                    allMiles = allMiles + perDistance;
                }
            }
            locationOld = locationNow;
            totalPointCount++;
        }

        CompleteDataNode sc = new CompleteDataNode();
        sc.setLicencePlate(licencePlate);
        sc.setPlateColor((int)plateColor);
        sc.setAllPointCount(totalPointCount);
        sc.setCompletePointCount(completePointCount);
        sc.setAllMiles(allMiles);
        sc.setCompleteMiles(completeMiles);
        if(allMiles <=0 || completeMiles <=0){
            //如果总里程或者完整里程为0，则轨迹完整率为0
            sc.setCompleteRate(0);
        }else{
            sc.setCompleteRate(completeMiles / allMiles);
        }
        return sc;
    }

    @Override
    public void checkTableExistAndCreate(String month) {
        try{
            baseMapper.checkExist(month);
        }catch (Exception e){
            log.info("月表[stat_complete_{}]不存在，将新创建该月表", month);
            baseMapper.createTable(month);
            //创建索引
            dataUtils.createIndex(StatisticConstants.LOCATION_COMPLETE_TABLE+"_"+month , StatisticConstants.LICENCE_PLATE);
        }
    }

    @Override
    public LocationCompleteRateResponse getLocationCompleteRate(RateParam param) {
        return baseMapper.getLocationCompleteRate(param);
    }

    @Override
    public IPage<LocationCompleteDetailResponse> findUnCompleteList(@Param("param") DetailParam param) {
        Page page = new Page();
        page.setCurrent(param.getCurrent());
        page.setSize(param.getSize());
        return baseMapper.getUnCompleteDetailList(page , param);
    }

    @Override
    public List<VehicleRateWithDept> findCompleteByDeptId(String month, List<Long> deptIds, Long ownerId) {
        month = month.replace("-" , "");
        return baseMapper.getCompleteRateByDeptId(month , deptIds , ownerId);
    }

	@Override
	public VehicleRateWithDept findCompleteByDeptIdDeptOrArea(String month, Long deptId, Long ownerId) {
		month = month.replace("-" , "");
		return baseMapper.getCompleteRateByDeptIdDeptOrArea(month , deptId , ownerId);
	}

	@Override
	public List<DateAndComplete> findCompleteInfoByDate(List<String> dateList, List<Long> deptIds, Long userId) throws Exception{

		//1.拼装部门id
		StringBuffer deptIdsStr = new StringBuffer();
		String deptIdsList = "";
		for(Long id : deptIds){
			deptIdsStr.append(id).append(",");
		}
		if(deptIdsStr.length() > 0){
			deptIdsList = deptIdsStr.substring(0,deptIdsStr.length() - 1);
		}

		//2.根据天数拼装sql
		StringBuffer sqls = new StringBuffer();
		int index = 0;
		for(String date : dateList){
			if(index != 0){
				sqls.append(" union ");
			}
			String dateStr = date.substring(6,8);
			String monthStr = date.substring(0,6);
			String template = "select coalesce(sum(rate.complete_"+dateStr+"),0) completeMile, coalesce(sum(rate.all_m_"+dateStr+"),0) allMile, " + date +" date "+
				"from (" +
				"select coalesce(split_part(d"+dateStr+" , '#' , -1),0) complete_"+dateStr+", coalesce( split_part(split_part(d"+dateStr+" , '#' , -2), '#' , 1),0) all_m_"+dateStr+", sc.dept_id from stat_complete_"+monthStr+" sc " +
				"where (sc.dept_id in ("+deptIdsList+") or sc.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = "+userId+")) " +
				") rate ";
			sqls.append(template);
			index ++;
		}

		//3.执行sql
		if(!StringUtils.isEmpty(sqls)){
			//List<Map<String, Object>> list = SqlRunner.db().selectList(sqls.toString());
			List<Map<String, Object>> list = vehicleMapper.dynamicQuery(sqls.toString());
			if(list == null || list.size() < 1){
				return null;
			}
			List<DateAndComplete> resList = new ArrayList<>();
			for(Map<String,Object> m : list){
				DateAndComplete dc = new DateAndComplete();
				dc.setCompleteMile(Double.parseDouble(m.get("completeMile").toString()));
				dc.setAllMile(Double.parseDouble(m.get("allMile").toString()));
				dc.setDate(m.get("date").toString());
				if(dc.getAllMile() == 0){
					dc.setCompleteRateStr("100%");
					dc.setCompleteRate(1D);
				}else{
					dc.setCompleteRate(MathUtil.divideRoundDouble(dc.getCompleteMile(), dc.getAllMile(), 4));
					dc.setCompleteRateStr(MathUtil.formatToPercent(dc.getCompleteRate(), 2));
				}
				resList.add(dc);
			}
			return resList;
		}else{
			return null;
		}


	}


	@Override
	public List<CompleteMileageNode> findCompleteMileage(CommonBaseCrossMonthRequest request) throws Exception {
		//1.获取查询月份和日期列表
		List<DateListAndMonth> dmList = DateUtil.getDateListAndMonth(request.getStartTime(), request.getEndTime());
		//2.按照月份查询（因为车辆可能在上个月上线，在下个月不上线，所以不能通过每个月份联查的sql来查询）
		List<String> dateList = new ArrayList<>();
		List<CompleteMileageNode> allList = new ArrayList<>();
		dmList.forEach(item -> {
			String month = item.getMonth().replace("-","");
			List<String> dateListTmp = item.getDateList();
			for (String s : dateListTmp) {
				dateList.add(s.substring(8,10));
			}
			List<CompleteMileageNode> list = baseMapper.getCompleteMileageMonth(request,month,dateList);
			allList.addAll(list);
		});
		//按照车牌号、车牌颜色分组
		Map<String,List<CompleteMileageNode>> map = new HashMap<>();
		allList.forEach(item -> {
			String licencePlate = item.getLicencePlate();
			String licenceColor = item.getLicenceColor();
			String key = licencePlate + "~" + licenceColor;
			List<CompleteMileageNode> tmpList = map.get(key);
			if(tmpList == null){
				tmpList = new ArrayList<>();
			}
			tmpList.add(item);
			map.put(key, tmpList);
		});
		//对每辆车的里程和轨迹完整率进行计算
		List<CompleteMileageNode> resList = new ArrayList<>();
		map.forEach((k,v) -> {
			String licencePlate = k.split("~")[0];
			String licenceColor = k.split("~")[1];
			//计算里程和轨迹完整率
			double completeMileage = 0;
			double totalMileage = 0;
			for(CompleteMileageNode m : v){
				completeMileage += m.getCompleteMileage();
				totalMileage += m.getTotalMileage();
			};
			double completeRate = totalMileage==0?0:(completeMileage / totalMileage);
			CompleteMileageNode cmn = new CompleteMileageNode();
			cmn.setCompleteMileage(completeMileage);
			cmn.setTotalMileage(totalMileage);
			cmn.setCompleteRate(completeRate);
			cmn.setLicencePlate(licencePlate);
			cmn.setLicenceColor(licenceColor);
			resList.add(cmn);
		});
		return resList;
	}
}
