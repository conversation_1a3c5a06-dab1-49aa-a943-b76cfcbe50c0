package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.alarm.entity.AlarmInfo;
import com.xh.vdm.alarm.entity.AlarmInfoToday;
import com.xh.vdm.alarm.feign.IAlarmClient;
import com.xh.vdm.base.feign.IBaseInfoClient;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.*;
import com.xh.vdm.statistic.vo.request.VehRunningStateDayRequest;
import com.xh.vdm.statistic.vo.response.*;
import com.xh.vdm.statistic.vo.response.alarm.AlarmSortResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.AlarmConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @Description: 监控一览接口
 * @Author: zhouxw
 * @Date: 2023/6/13 19:14
 */
@RestController
@RequestMapping("/monitor")
@Slf4j
public class MonitorController {


	@Resource
	private ISysClient sysClient;

	@Resource
	private IDictBizClient dictClient;

	@Resource
	private IBaseInfoClient baseInfoClient;

	@Resource
	private IStatVehOnlineDayService onlineDayService;

	@Resource
	private IAlarmClient alarmClient;

	private int useCount = 1000;

	public static final int MAX_USE_COUNT = 1000;

	@Resource
	private IVehicleStatService vehicleStatService;

	@Resource
	private IDictBizClient dictBizClient;

	private ConcurrentHashMap<String, List<String>> excludeAlarmTypeMap = new ConcurrentHashMap<>();


	@Resource
	private IUserClient userClient;


	@Autowired
	private VdmUserInfoUtil userInfoUtil;


	@Resource
	private IAlarmService alarmService;

	@Resource
	private IImpalaAlarmService impalaAlarmService;

	@Resource
	private IStatCompleteService statCompleteService;

	@Resource
	private ILocationQualityService locationQualityService;

	@Resource
	private IStatDriftService driftService;

	@Resource
	private IStatVehRunningStateService vehRunningStateService;

	@Resource
	private IBdmVehicleService vehicleService;

	@Resource
	private ILocationService locationService;

	@Resource
	private RedisTemplate<String,Object> redisTemplate;

	private ExecutorService threadPool = Executors.newFixedThreadPool(10);


	//要推送的报警类型 key - value
	public static final Map<String,String> alarmTypeMap = new HashMap<>();


	/**
	 * @description: 车辆基本信息
	 * 数据包含本部门、子部门、本账号关联车辆的信息
	 * @author: zhouxw
	 * @date: 2023-06-166 21:01:50
	 * @param: [user]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.statistic.vo.response.IndexBaseInfoResponse>
	 **/
	@GetMapping("/vehicleInfo")
	public R<MonitorVehicleResponse> vehicleInfo(BladeUser user){

		try{
			//1 获取登录的信息
			//查询本部门、子部门
			if(user == null){
				log.info("获取用户信息为空");
				return R.fail("用户登录失败或未授权");
			}
			List<Long> deptIds = null;
			try {
				deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);
			}catch (Exception e){
				log.error("查询部门信息失败",e);
				return R.fail("用户登录失败或未授权");
			}

			//2.查询车辆总数
			long vehicleCount = 0;
			try{
				vehicleCount = vehicleService.findAllVehicleCount(deptIds, user.getUserId());
			}catch (Exception e){
				log.error("[主页--基础信息]查询车辆总数失败");
			}
			//3.营运车辆数
			long runningCount = 0;
			try{
				runningCount = vehicleService.findRunningCount(deptIds, user.getUserId());
			}catch (Exception e){
				log.error("查询营运车辆数出错",e);
			}
			long start4 = System.currentTimeMillis();
			//4.上线车辆数(当日到现在发过定位点的车辆数)
			long goOnlineCount = 0;
			try{
				goOnlineCount = vehicleService.findGoOnlineCountAllToday(deptIds, user.getUserId());
			}catch (Exception e){
				log.error("查询上线车辆数出错",e);
			}
			long end4 = System.currentTimeMillis();
			log.info("-->time4: "+ (end4 - start4));
			long start5 = System.currentTimeMillis();


			//5.在线车辆数
			long onlineCount = 0;
			try{
				onlineCount = vehicleService.findOnlineCount(deptIds, user.getUserId());
			}catch (Exception e){
				log.error("查询在线车辆数出错",e);
			}
			long end5 = System.currentTimeMillis();
			log.info("-->time5 : "+(end5 - start5));
			long start6 = System.currentTimeMillis();
			//6.离线车辆数（营运车辆数-在线车辆数）
			long offlineCount = 0;
			try{
				offlineCount = runningCount - onlineCount;
			}catch (Exception e){
				log.error("查询离线车辆数出错",e);
			}
			long end6 = System.currentTimeMillis();
			log.info("-->time6: "+ (end6 - start6));

			long start7 = System.currentTimeMillis();
			//7.停运车辆数
			long stopRunningCount = 0;
			try{
				stopRunningCount = vehicleService.findStopRunningCount(deptIds, user.getUserId());
			}catch (Exception e){
				log.error("查询停运车辆数出错",e);
			}
			long end7 = System.currentTimeMillis();
			log.info("-->time7 : "+(end7 - start7));

			//8.上线率（上线数/营运车辆数）
			double goOnlineRate = 0D;
			if(runningCount != 0){
				goOnlineRate = MathUtil.divideRoundDouble(goOnlineCount, runningCount, 4);
			}
			String goOnlineRateStr = MathUtil.formatToPercent(goOnlineRate, 2);

			//9.在线率（在线车辆数/营运车辆数）
			double onlineRate = 0D;
			if(runningCount != 0){
				onlineRate = MathUtil.divideRoundDouble(onlineCount, runningCount, 4);
			}
			String onlineRateStr = MathUtil.formatToPercent(onlineRate, 2);

			MonitorVehicleResponse response = new MonitorVehicleResponse();
			response.setVehicleCount(vehicleCount);
			response.setRunningVehicleCount(runningCount);
			response.setGoOnlineVehicleCount(goOnlineCount);
			response.setOnlineVehicleCount(onlineCount);
			response.setOfflineVehicleCount(offlineCount);
			response.setStopRunningVehicleCount(stopRunningCount);
			response.setGoOnlineRate(goOnlineRate);
			response.setGoOnlineRateStr(goOnlineRateStr);
			response.setOnlineRate(onlineRate);
			response.setOnlineRateStr(onlineRateStr);
			return R.data(response);
		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询出错");
		}
	}


	/**
	 * @description: 统计报警情况（暂时未使用）
	 * @author: zhouxw
	 * @date: 2023-06-166 21:25:44
	 * @param: [user]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.alarm.entity.AlarmInfo>
	 **/
	@GetMapping("/alarmInfo")
	//todo 添加接口权限
	public R<AlarmInfo> alarmInfo(BladeUser user){

		if(user == null){
			return R.fail("用户未登录或未授权");
		}
		String deptId = user.getDeptId();
		Long userId = user.getUserId();
		String roleType = user.getRoleName();

		String userType = "";
		if(StatisticConstants.ROLE_THIRD.equals(roleType)){
			//如果包含第三方，则选定第三方
			userType = "2";
		}else if(StatisticConstants.ROLE_SERVER.equals(roleType)){
			//如果不包含第三方，包含服务商，则选定服务商
			userType = "1";
		}else if(StatisticConstants.ROLE_COMPANY.equals(roleType)){
			//如果是企业
			userType = "3";
		}
		//查询报警情况
		AlarmInfo info = null;
		long start = System.currentTimeMillis();
		try {
			R<AlarmInfo> res = alarmClient.getTodayAlarmInfo(Long.parseLong(deptId), userId);
			if(res == null || !res.isSuccess() || res.getData() == null){
				log.error("调用feign接口出错:{}",res.getMsg());
				return R.fail("查询出错");
			}
			info = res.getData();
		}catch (Exception e){
			log.error("[主页--报警信息]查询报警情况接口报错",e);
			return R.fail("查询出错");
		}
		long end = System.currentTimeMillis();
		log.info("-=-=time1: "+(end - start));

		//查询今日报警处理数量
		long start2 = System.currentTimeMillis();
		long handleCount = 0;
		try {
			R<Long> res = alarmClient.getTodayAlarmHandleCount(Long.parseLong(deptId), userId, userType);
			if(res == null || res.getData() == null){
				log.error("[主页--报警信息]查询报警处理数据报错或未查询到数据");
			}
			handleCount = res.getData();
		}catch (Exception e){
			log.error("[主页--报警信息]查询报警处理数量报错",e);
			return R.fail("查询出错");
		}
		long end2 = System.currentTimeMillis();
		log.info("-=-=time2: "+(end2 - start2));

		//计算其他报警数、其他报警处理数
		long realTimeOtherAlarmCount = info.getRealTimeAlarmTotalCount() - info.getRealTimeAcitveCount() - info.getRealTimeNightCount() - info.getRealTimeTiredCount() - info.getRealTimeNightCount();
		long realTimeOtherHandleCount = info.getRealTimeAlarmHandleTotalCount() - info.getRealTimeAcitveHandleCount() - info.getRealTimeNightHandleCount() - info.getRealTimeTiredHandleCount() - info.getRealTimeNightHandleCount();
		info.setRealTimeOtherCount(realTimeOtherAlarmCount < 0?0:realTimeOtherAlarmCount);
		info.setRealTimeOtherHandleCount(realTimeOtherHandleCount < 0?0:realTimeOtherHandleCount);


		//报警处理率
		double handleRate = 0;
		if(info.getTodayTotalAlarmCount() > 0){
			if(handleCount > info.getTodayTotalAlarmCount()){
				handleCount = info.getTodayTotalAlarmCount();
			}
			handleRate = MathUtil.roundDouble((double)handleCount / (double)info.getTodayTotalAlarmCount(), 2);
		}
		info.setHandleRate(MathUtil.formatToPercent(handleRate,0));

		if(info.getRealTimeOtherCount() < 0){
			info.setRealTimeOtherCount(0L);
		}
		return R.data(info);
	}


	/**
	 * 今日报警情况（在用）
	 * @param user
	 * @return
	 */
	@GetMapping("/alarmInfoToday")
	public R<AlarmInfoToday> alarmInfoToday (BladeUser user){
		if(user == null){
			return R.fail("用户未登录或未授权");
		}
		CountDownLatch countDownLatch = new CountDownLatch(7);

		try{
			List<Long> deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);
			List<Integer> vehicleIds = userInfoUtil.getVehicleIdList(user.getUserId());
			long startTime = DateUtil.getDayFirstSecondTimestamp();
			long endTime = DateUtil.getDayLastSecondTimestamp(startTime);

			//查询报警情况
			//1.报警总数
			AtomicLong totalCount = new AtomicLong();
			threadPool.submit(() -> {
				try{
					totalCount.set(impalaAlarmService.findAlarmCountByCondition(deptIds, vehicleIds, startTime, endTime));
				}catch (Exception e){
					log.error("查询报警总数失败",e);
				}finally {
					countDownLatch.countDown();
				}
			});

			//2.超速报警
			AtomicLong overSpeedCount = new AtomicLong();
			threadPool.submit(() -> {
				try{
					overSpeedCount.set(impalaAlarmService.findAlarmCountByAlarmTypes(deptIds, vehicleIds, VdmUserInfoUtil.getOverSpeedAlarmType(), startTime, endTime));
				}catch (Exception e){
					log.error("查询超速报警失败",e);
				}finally {
					countDownLatch.countDown();
				}
			});

			//3.疲劳报警
			AtomicLong fatigueCount = new AtomicLong();
			threadPool.submit(() -> {
				try{
					fatigueCount.set(impalaAlarmService.findAlarmCountByAlarmTypes(deptIds, vehicleIds, VdmUserInfoUtil.getFatigueAlarmType(), startTime, endTime));
				}catch (Exception e){
					log.error("查询疲劳报警失败",e);
				}finally {
					countDownLatch.countDown();
				}
			});


			//4.夜间异动报警
			AtomicLong nightLimitCount = new AtomicLong();
			threadPool.submit(() -> {
				try{
					nightLimitCount.set(impalaAlarmService.findAlarmCountByAlarmTypes(deptIds, vehicleIds, VdmUserInfoUtil.getNightLimitAlarmType(), startTime, endTime));
				}catch (Exception e){
					log.error("查询夜间异动报警失败",e);
				}finally {
					countDownLatch.countDown();
				}
			});

			//5.adas报警
			AtomicLong adasCount = new AtomicLong();
			threadPool.submit(() -> {
				try{
					adasCount.set(impalaAlarmService.findAlarmCountByAlarmTypes(deptIds, vehicleIds, VdmUserInfoUtil.getAdasAlarmType(), startTime, endTime));
				}catch (Exception e){
					log.error("查询主动安全报警失败",e);
				}finally {
					countDownLatch.countDown();
				}
			});

			//5.dsm报警
			AtomicLong dsmCount = new AtomicLong();
			threadPool.submit(() -> {
				try{
					dsmCount.set(impalaAlarmService.findAlarmCountByAlarmTypes(deptIds, vehicleIds, VdmUserInfoUtil.getDsmAlarmType(), startTime, endTime));
				}catch (Exception e){
					log.error("查询主动安全报警失败",e);
				}finally {
					countDownLatch.countDown();
				}
			});

			//6.紧急报警
			AtomicLong emergencyCount = new AtomicLong();
			threadPool.submit(() -> {
				try{
					emergencyCount.set(impalaAlarmService.findAlarmCountByAlarmTypes(deptIds, vehicleIds, VdmUserInfoUtil.getEmergencyAlarmType(), startTime, endTime));
				}catch (Exception e){
					log.error("查询紧急报警失败",e);
				}finally {
					countDownLatch.countDown();
				}
			});
			countDownLatch.await();
			Long activeCount = adasCount.get() + dsmCount.get();
			//7.其他报警
			long otherCount = totalCount.get() - overSpeedCount.get() - fatigueCount.get() - nightLimitCount.get() - activeCount - emergencyCount.get();

			AlarmInfoToday alarmInfo = new AlarmInfoToday();
			alarmInfo.setTodayTotalAlarmCount(totalCount.get());
			alarmInfo.setAdasCount(adasCount.get());
			alarmInfo.setDsmCount(dsmCount.get());
			alarmInfo.setActiveCount(activeCount);
			alarmInfo.setEmergencyCount(emergencyCount.get());
			alarmInfo.setNightCount(nightLimitCount.get());
			alarmInfo.setFatigueCount(fatigueCount.get());
			alarmInfo.setOverSpeedCount(overSpeedCount.get());
			alarmInfo.setOtherCount(otherCount);

			return R.data(alarmInfo);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}


	/**
	 * 查询运输证到期车辆数量
	 * @param user
	 * @return
	 */
	@GetMapping("/certExpiredCount")
	public R<ExpireCountResponse> certExpiredCount (BladeUser user){
		//1.查询子部门、本部门
		if(user == null){
			log.info("获取用户信息为空");
			return R.fail("用" +
				"户登录失败或未授权");
		}

		List<Long> list = null;
		try {
			list = userInfoUtil.getChildrenAndSelfDeptId(user);
		}catch (Exception e){
			log.error("查询部门信息失败",e);
			return R.fail("用户登录失败或未授权");
		}

		ExpireCountResponse resp = new ExpireCountResponse();
		try {

			//6个月内到期
			Date sixMonthDate = DateUtil.getDateAfterMonth(null, 6);
			long sixMonthExpireCount = vehicleService.findExpiredCount(sixMonthDate, list, user.getUserId());

			//3个月内到期
			Date threeMonthDate = DateUtil.getDateAfterMonth(null, 3);
			long threeMonthExpireCount = vehicleService.findExpiredCount(threeMonthDate, list, user.getUserId());

			//2个月内到期
			Date twoMonthDate = DateUtil.getDateAfterMonth(null, 2);
			long twoMonthExpireCount = vehicleService.findExpiredCount(twoMonthDate, list, user.getUserId());

			//1个月内到期
			Date oneMonthDate = DateUtil.getDateAfterMonth(null, 1);
			long oneMonthExpireCount = vehicleService.findExpiredCount(oneMonthDate, list, user.getUserId());

			//15天内到期
			Date day15Date = DateUtil.getDateAfterDay(null, 15);
			long day15DateExpireCount = vehicleService.findExpiredCount(day15Date, list, user.getUserId());

			//已过期
			long expiredCount = vehicleService.findHasExpiredCount(new Date(), list, user.getUserId());

			//未知时间段
			long unKnowExpireCount = vehicleService.findUnKnownExpireCount(list, user.getUserId());

			resp.setExpiredCount(expiredCount);
			resp.setOneMonthExpireCount(oneMonthExpireCount);
			resp.setDay15DateExpireCount(day15DateExpireCount);
			resp.setUnKnowExpireCount(unKnowExpireCount);
			resp.setThreeMonthExpireCount(threeMonthExpireCount);
			resp.setSixMonthExpireCount(sixMonthExpireCount);
			resp.setTwoMonthExpireCount(twoMonthExpireCount);

			return R.data(resp);
		}catch (Exception e){
			log.error("查询证件过期车辆数量报错",e);
			return R.fail("查询出错");
		}
	}




	/**
	 * 动态监控：按小时统计当天的报警数和处理数、处理率
	 * @param user
	 * @return
	 */
	@GetMapping("/alarmAndHandleCountHour")
	public R<List<AlarmAndHandleCountHourResponse>> alarmAndHandleCountHour(BladeUser user){

		try {
			//1.查询部门信息
			List<Long> deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);
			//2.查询用户关联的车辆
			List<Integer> vehicleIds = userInfoUtil.getVehicleIdList(user.getUserId());

			//3.执行查询
			long dateStart = DateUtil.getDayFirstSecondTimestamp(new Date().getTime() / 1000);
			long dateEnd = DateUtil.getDayLastSecondTimestamp(new Date().getTime() / 1000);

			//1.统计报警数量
			//查询mysql
			//List<DateAndCount> alarmList = alarmService.findAlarmCountHour(dateStart, dateEnd, list, user.getUserId());
			//查询impala
			List<DateAndCount> alarmList = impalaAlarmService.findAlarmCountHour(dateStart, dateEnd, deptIds, vehicleIds);
			Map<String, DateAndCount> alarmMap = new HashMap<>();
			for (DateAndCount dc : alarmList) {
				alarmMap.put(dc.getStatDate(), dc);
			}

			//2.统计报警处理数量（只统计服务商）
			//用户类型
			String userType = "";
			userType = StatisticConstants.USER_TYPE_SERVER;
			//查询mysql
			//List<DateAndCount> handleList = alarmService.findAlarmHandleCountHour(dateStart, dateEnd, list, user.getUserId(), userType);
			//查询impala
			List<DateAndCount> handleList = impalaAlarmService.findAlarmHandleCountHour(dateStart, dateEnd, deptIds, vehicleIds, userType);

			Map<String, DateAndCount> handleMap = new HashMap<>();
			for (DateAndCount dc : handleList) {
				handleMap.put(dc.getStatDate(), dc);
			}

			//3.整理数据
			List<String> dateList = new ArrayList<>();
			int time = DateUtil.getHour(new Date().getTime()/1000) - 1;
			for(int i = 0; i < 24; i++){
				if(i > time){
					break;
				}
				String timeStr = i + "";
				if(i < 10){
					timeStr = "0" + timeStr;
				}else{
					timeStr = timeStr + "";
				}
				dateList.add(timeStr);
			}

			List<AlarmAndHandleCountHourResponse> resList = new ArrayList<>();
			for (String date : dateList) {
				AlarmAndHandleCountHourResponse ac = new AlarmAndHandleCountHourResponse();
				long alarmCount = 0;
				if(alarmMap.get(date) != null){
					alarmCount = alarmMap.get(date).getCount();
				}
				DateAndCount handle = handleMap.get(date);
				long handleCount = handle == null ? 0 : handle.getCount();
				ac.setAlarmCount(alarmCount);
				ac.setHandleCount(handleCount);
				ac.setHour(date);
				if(alarmCount <= 0){
					ac.setHandleRate(0D);
				}else{
					ac.setHandleRate(MathUtil.divideRoundDouble(handleCount, alarmCount, 4));
				}

				ac.setHandleRateStr(MathUtil.formatToPercent(ac.getHandleRate(), 2));
				resList.add(ac);
			}

			return R.data(resList);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}


	/**
	 * 安全监控：统计近7天的报警数和处理数、处理率
	 * 统计的是第三方的处理
	 * @param user
	 * @return
	 */
	@GetMapping("/alarmAndHandleCount")
	public R<List<AlarmAndHandleCountDayResponse>> alarmAndHandleCount(BladeUser user){
		if(user == null){
			return R.fail("用户未授权或未登录");
		}
		try {
			//1.查询部门信息
			List<Long> deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);
			//2.查询用户关联的车辆
			List<Integer> vehicleIds = userInfoUtil.getVehicleIdList(user.getUserId());

			//3.执行查询
			long startTime = DateUtil.getDayFirstSecondTimestamp(DateUtil.getSecondTimeStampBeforeDay(new Date(), 7));
			long endTime = DateUtil.getDayFirstSecondTimestamp(DateUtil.getSecondTimeStampBeforeDay(new Date(), 0));

			CountDownLatch countDownLatch = new CountDownLatch(2);
			String userType = StatisticConstants.USER_TYPE_THIRD;
			//1.统计报警数量（只统计第三方）
			Map<String, DateAndCount> alarmMap = new HashMap<>();
			threadPool.submit(() -> {
				try{
					//查询mysql
					//List<DateAndCount> alarmList = alarmService.findAlarmCountHour(dateStart, dateEnd, list, user.getUserId());
					//查询impala
					List<DateAndCount> alarmList = impalaAlarmService.findAlarmAndHandleCountPerDate(startTime, endTime, deptIds, vehicleIds,null);
					for (DateAndCount dc : alarmList) {
						alarmMap.put(dc.getStatDate(), dc);
					}
				}catch (Exception e){
					log.error("查询统计报警数量报错",e);
				}finally {
					countDownLatch.countDown();
				}
			});

			//2.统计报警处理数量（只统计第三方）
			Map<String, DateAndCount> handleMap = new HashMap<>();
			threadPool.submit(() -> {
				try{
					//查询mysql
					//List<DateAndCount> handleList = alarmService.findAlarmHandleCountHour(dateStart, dateEnd, list, user.getUserId(), userType);
					//查询impala
					List<DateAndCount> handleList = impalaAlarmService.findAlarmAndHandleCountPerDate(startTime, endTime, deptIds, vehicleIds,userType);
					for (DateAndCount dc : handleList) {
						handleMap.put(dc.getStatDate(), dc);
					}
				}catch (Exception e){
					log.error("统计报警处理数量出错",e);
				}finally {
					countDownLatch.countDown();
				}
			});

			countDownLatch.await();

			//3.整理数据
			List<String> dateList = DateUtil.getDateList(startTime, endTime);
			//不包含当前天
			dateList.remove(dateList.size()-1);
			List<AlarmAndHandleCountDayResponse> resList = new ArrayList<>();
			for (String date : dateList) {
				AlarmAndHandleCountDayResponse ac = new AlarmAndHandleCountDayResponse();
				long alarmCount = 0;
				if(alarmMap.get(date) != null){
					alarmCount = alarmMap.get(date).getCount();
				}
				DateAndCount handle = handleMap.get(date);
				long handleCount = handle == null ? 0 : handle.getCount();
				ac.setAlarmCount(alarmCount);
				ac.setHandleCount(handleCount);
				ac.setDate(date);
				if(alarmCount <= 0){
					ac.setHandleRate(0D);
				}else{
					ac.setHandleRate(MathUtil.divideRoundDouble(handleCount, alarmCount, 4));
				}

				ac.setHandleRateStr(MathUtil.formatToPercent(ac.getHandleRate(), 2));
				resList.add(ac);
			}

			return R.data(resList);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}

	/**
	 * 统计每个小时的在线车辆数量
	 * @return
	 */
	@GetMapping("/onlineHourCount")
	public R<List<OnlineHourCountResponse>> onlineHourCount(BladeUser user){

		//1.获取部门id、子部门id
		if(user == null){
			log.info("获取用户信息为空");
			return R.fail("用户登录失败或未授权");
		}

		List<Long> deptIds = null;
		try {
			deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);
		}catch (Exception e){
			log.error("查询部门信息失败",e);
			return R.fail("用户登录失败或未授权");
		}

		try {
			//获取查询时段
			List<String> hourList = DateUtil.getHourList(DateUtil.getDayFirstSecondTimestamp(),DateUtil.getSecondTimestamp());
			if(hourList == null || hourList.size() < 1){
				return R.fail("查询失败");
			}
			hourList = hourList.subList(0, hourList.size() - 1);
			List<OnlineHourCountResponse> list = onlineDayService.findOnlineHourCount(hourList, deptIds, user.getUserId());
			return R.data(list);
		}catch (Exception e){
			log.error("查询失败",e);
			return R.fail("查询失败");
		}
	}


	/**
	 * 当日车辆报警排行（可指定排序顺序）
	 * @return
	 */
	@PostMapping("/statVehRunningDayState")
	R<IPage<StatVehRunningStateDay>> statVehRunningDayState(/*@RequestBody VehRunningStateDayRequest request,*/ Query query, BladeUser user){

		//默认按照报警数量的倒序排序
		VehRunningStateDayRequest request = new VehRunningStateDayRequest();
		request.setOrderField("totalAlarmCount");
		request.setOrderType("0");
		request.setDate(DateUtil.sdfHolderShort.get().format(new Date()).replace("-",""));
		try{

			List<Long> list = null;
			try {
				list = userInfoUtil.getChildrenAndSelfDeptId(user);
			}catch (Exception e){
				log.error("查询部门信息失败",e);
				return R.fail("用户登录失败或未授权");
			}
			//设置排序字段
			String orderField = request.getOrderField();
			if(!StringUtils.isEmpty(orderField)){
				orderField = LineAndHumpUtil.humpToLine(orderField);
			}
			request.setOrderField(orderField);
			//设置排序方式
			String oType = "desc";
			if("1".equals(request.getOrderType())){
				//如果是升序
				oType = "asc";
			}
			request.setOrderType(oType);
			IPage<StatVehRunningStateDay> page = vehRunningStateService.findRunningStateDayByPage(request, query, list, user.getUserId());
			//格式化数据
			page.getRecords().forEach(item -> {
				item.setTotalMileage(MathUtil.roundDouble(item.getTotalMileage()/1000,2));
			});
			return R.data(page);
		}catch (Exception e){
			log.error("查询数据报错",e);
			return R.fail("查询数据报错");
		}
	}


	/**
	 * 当日车辆报警排行
	 * @return
	 */
	@GetMapping("/statAlarmSortToday")
	R<List<AlarmSortResponse>> statAlarmSortToday(Query query, BladeUser user){

		if(user == null){
			return R.fail("用户未登录或未授权");
		}
		try{
			List<Long> deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);
			List<Integer> vehicleIds = userInfoUtil.getVehicleIdList(user.getUserId());
			List<AlarmSortResponse> page = vehRunningStateService.findAlarmSortInfoToday(query, deptIds, vehicleIds,user);
			return R.data(page);
		}catch (Exception e){
			log.error("查询数据报错",e);
			return R.fail("查询数据报错");
		}
	}

	/**
	 * @description: 重点报警
	 * @author: zhouxw
	 * @date: 2023-07-206 14:32:41
	 * @param: [user]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.statistic.vo.response.ImportAlarmResponse>
	 **/
	@GetMapping("/importAlarm")
	public R<ImportAlarmResponse> importAlarm(BladeUser user){

		if(user == null){
			return R.fail("用户未登录或没有权限");
		}


		List<Integer> vehicleIds = null;
		List<Long> deptIds = null;
		try{

			//查询账号关联的车辆
			vehicleIds = userInfoUtil.getVehicleIdList(user.getUserId());

			//查询部门
			deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);

			long startTime = DateUtil.getDayFirstSecondTimestamp();
			long endTime = DateUtil.getDayLastSecondTimestamp(startTime);

			//按照角色进行统计
			//a.如果是服务商 + 第三方，则按照第三方处理来算；
			//b.如果是服务商，按照服务商来算；
			//c.如果是第三方，按照第三方来算；
			//d.如果是企业，就查找企业处理的
			List<String> userTypes = userInfoUtil.getUserType(user.getRoleName());
			String userType = StatisticConstants.USER_TYPE_SERVER;
			if(userTypes.contains(StatisticConstants.USER_TYPE_SERVER)){
				//如果只包含服务商，按照服务商统计
				userType = StatisticConstants.USER_TYPE_SERVER;
			}else if(userTypes.contains(StatisticConstants.USER_TYPE_THIRD)){
				//如果包含第三方，按照第三方统计
				userType = StatisticConstants.USER_TYPE_THIRD;
			}else{
				//如果包含企业，则按照企业进行统计
				userType = StatisticConstants.USER_TYPE_COMPANY;
			}

			CountDownLatch countDownLatch = new CountDownLatch(2);

			//1.获取报警类型
			List<Integer> alarmTypes = new ArrayList<>();
			//超速
			alarmTypes.addAll(VdmUserInfoUtil.getOverSpeedAlarmType());
			//疲劳
			alarmTypes.addAll(VdmUserInfoUtil.getFatigueAlarmType());
			//夜间禁行
			alarmTypes.addAll(VdmUserInfoUtil.getNightLimitAlarmType());
			//前向碰撞
			alarmTypes.add((int)AlarmConstant.DICT_ALARM_TYPE_CAR_COLLISION);
			//车道偏离
			alarmTypes.add((int)AlarmConstant.DICT_ALARM_TYPE_LANE_DEPARTURE);
			//车距过近
			alarmTypes.add((int)AlarmConstant.DICT_ALARM_TYPE_CLOSE_DISTANCE);
			//生理疲劳
			alarmTypes.add((int)AlarmConstant.DICT_ALARM_TYPE_FATIGUED);
			//接打电话
			alarmTypes.add((int)AlarmConstant.DICT_ALARM_TYPE_PHONE);
			//抽烟
			alarmTypes.add((int)AlarmConstant.DICT_ALARM_TYPE_SMOKE);
			//长时间不目视前方
			alarmTypes.add((int)AlarmConstant.DICT_ALARM_TYPE_DISTRACTION);

			//2.查询报警数量
			List<Long> finalDeptIds = deptIds;
			List<Integer> finalVehicleIds = vehicleIds;
			AtomicReference<List<AlarmTypeAndDateAndCount>> alarmList = new AtomicReference<>(new ArrayList<>());
			threadPool.submit(() -> {
				try{
					alarmList.set(impalaAlarmService.findAlarmTypeAndDateAndCountOrHandle(alarmTypes, finalDeptIds, finalVehicleIds, startTime, endTime, null));
				}catch (Exception e){
					log.error("查询报警数量失败",e);
				}finally{
					countDownLatch.countDown();
				}
			});

			//3.查询报警处理数量
			AtomicReference<List<AlarmTypeAndDateAndCount>> handleList = new AtomicReference<>(new ArrayList<>());
			List<Long> finalDeptIds1 = deptIds;
			List<Integer> finalVehicleIds1 = vehicleIds;
			String finalUserType = userType;
			threadPool.submit(() -> {
				try{
					handleList.set(impalaAlarmService.findAlarmTypeAndDateAndCountOrHandle(alarmTypes, finalDeptIds1, finalVehicleIds1, startTime, endTime, finalUserType));
				}catch (Exception e){
					log.error("查询报警数量失败",e);
				}finally{
					countDownLatch.countDown();
				}
			});
			countDownLatch.await();

			//4.整理数据
			List<String> alarmTypeDescList = new ArrayList<>();
			//报警数据
			Map<String,Long> alarmMap = new HashMap<>();
			alarmList.get().forEach(item -> {
				if(VdmUserInfoUtil.getOverSpeedAlarmType().contains(Integer.parseInt(item.getAlarmType()))){
					//超速
					Long alarm1 = alarmMap.get("超速");
					if(alarm1 == null){
						alarmMap.put("超速",0L);
					}
					alarmMap.put("超速",alarmMap.get("超速")+item.getCount());
				}else if(VdmUserInfoUtil.getFatigueAlarmType().contains(Integer.parseInt(item.getAlarmType()))){
					//疲劳
					Long alarm1 = alarmMap.get("疲劳");
					if(alarm1 == null){
						alarmMap.put("疲劳",0L);
					}
					alarmMap.put("疲劳",alarmMap.get("疲劳")+item.getCount());
				}else if(VdmUserInfoUtil.getNightLimitAlarmType().contains(Integer.parseInt(item.getAlarmType()))){
					//夜间禁行
					Long alarm1 = alarmMap.get("2-5点违规");
					if(alarm1 == null){
						alarmMap.put("2-5点违规",0L);
					}
					alarmMap.put("2-5点违规",alarmMap.get("2-5点违规")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_CAR_COLLISION+"").equals(item.getAlarmType())){
					//前向碰撞
					Long alarm1 = alarmMap.get("前向碰撞");
					if(alarm1 == null){
						alarmMap.put("前向碰撞",0L);
					}
					alarmMap.put("前向碰撞",alarmMap.get("前向碰撞")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_LANE_DEPARTURE+"").equals(item.getAlarmType())){
					//车道偏离
					Long alarm1 = alarmMap.get("车道偏离");
					if(alarm1 == null){
						alarmMap.put("车道偏离",0L);
					}
					alarmMap.put("车道偏离",alarmMap.get("车道偏离")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_CLOSE_DISTANCE+"").equals(item.getAlarmType())){
					//车距过近
					Long alarm1 = alarmMap.get("车距过近");
					if(alarm1 == null){
						alarmMap.put("车距过近",0L);
					}
					alarmMap.put("车距过近",alarmMap.get("车距过近")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_FATIGUED+"").equals(item.getAlarmType())){
					//生理疲劳
					Long alarm1 = alarmMap.get("生理疲劳");
					if(alarm1 == null){
						alarmMap.put("生理疲劳",0L);
					}
					alarmMap.put("生理疲劳",alarmMap.get("生理疲劳")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_PHONE+"").equals(item.getAlarmType())){
					//接打电话
					Long alarm1 = alarmMap.get("接打电话");
					if(alarm1 == null){
						alarmMap.put("接打电话",0L);
					}
					alarmMap.put("接打电话",alarmMap.get("接打电话")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_SMOKE+"").equals(item.getAlarmType())){
					//抽烟
					Long alarm1 = alarmMap.get("抽烟");
					if(alarm1 == null){
						alarmMap.put("抽烟",0L);
					}
					alarmMap.put("抽烟",alarmMap.get("抽烟")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_DISTRACTION+"").equals(item.getAlarmType())){
					//长时间不目视前方
					Long alarm1 = alarmMap.get("长时间不目视前方");
					if(alarm1 == null){
						alarmMap.put("长时间不目视前方",0L);
					}
					alarmMap.put("长时间不目视前方",alarmMap.get("长时间不目视前方")+item.getCount());
				}
			});
			alarmTypeDescList.add("超速");
			alarmTypeDescList.add("疲劳");
			alarmTypeDescList.add("2-5点违规");
			alarmTypeDescList.add("前向碰撞");
			alarmTypeDescList.add("车道偏离");
			alarmTypeDescList.add("车距过近");
			alarmTypeDescList.add("生理疲劳");
			alarmTypeDescList.add("接打电话");
			alarmTypeDescList.add("抽烟");
			alarmTypeDescList.add("长时间不目视前方");

			//处理数据
			Map<String,Long> handleMap = new HashMap<>();
			handleList.get().forEach(item -> {
				if(VdmUserInfoUtil.getOverSpeedAlarmType().contains(Integer.parseInt(item.getAlarmType()))){
					//超速
					Long alarm1 = handleMap.get("超速");
					if(alarm1 == null){
						handleMap.put("超速",0L);
					}
					handleMap.put("超速",handleMap.get("超速")+item.getCount());
				}else if(VdmUserInfoUtil.getFatigueAlarmType().contains(Integer.parseInt(item.getAlarmType()))){
					//疲劳
					Long alarm1 = handleMap.get("疲劳");
					if(alarm1 == null){
						handleMap.put("疲劳",0L);
					}
					handleMap.put("疲劳",handleMap.get("疲劳")+item.getCount());
				}else if(VdmUserInfoUtil.getNightLimitAlarmType().contains(Integer.parseInt(item.getAlarmType()))){
					//夜间禁行
					Long alarm1 = handleMap.get("2-5点违规");
					if(alarm1 == null){
						handleMap.put("2-5点违规",0L);
					}
					handleMap.put("2-5点违规",handleMap.get("2-5点违规")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_CAR_COLLISION+"").equals(item.getAlarmType())){
					//前向碰撞
					Long alarm1 = handleMap.get("前向碰撞");
					if(alarm1 == null){
						handleMap.put("前向碰撞",0L);
					}
					handleMap.put("前向碰撞",handleMap.get("前向碰撞")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_LANE_DEPARTURE+"").equals(item.getAlarmType())){
					//车道偏离
					Long alarm1 = handleMap.get("车道偏离");
					if(alarm1 == null){
						handleMap.put("车道偏离",0L);
					}
					handleMap.put("车道偏离",handleMap.get("车道偏离")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_CLOSE_DISTANCE+"").equals(item.getAlarmType())){
					//车距过近
					Long alarm1 = handleMap.get("车距过近");
					if(alarm1 == null){
						handleMap.put("车距过近",0L);
					}
					handleMap.put("车距过近",handleMap.get("车距过近")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_FATIGUED+"").equals(item.getAlarmType())){
					//生理疲劳
					Long alarm1 = handleMap.get("生理疲劳");
					if(alarm1 == null){
						handleMap.put("生理疲劳",0L);
					}
					handleMap.put("生理疲劳",handleMap.get("生理疲劳")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_PHONE+"").equals(item.getAlarmType())){
					//接打电话
					Long alarm1 = handleMap.get("接打电话");
					if(alarm1 == null){
						handleMap.put("接打电话",0L);
					}
					handleMap.put("接打电话",handleMap.get("接打电话")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_SMOKE+"").equals(item.getAlarmType())){
					//抽烟
					Long alarm1 = handleMap.get("抽烟");
					if(alarm1 == null){
						handleMap.put("抽烟",0L);
					}
					handleMap.put("抽烟",handleMap.get("抽烟")+item.getCount());
				}else if((AlarmConstant.DICT_ALARM_TYPE_DISTRACTION+"").equals(item.getAlarmType())){
					//长时间不目视前方
					Long alarm1 = handleMap.get("长时间不目视前方");
					if(alarm1 == null){
						handleMap.put("长时间不目视前方",0L);
					}
					handleMap.put("长时间不目视前方",handleMap.get("长时间不目视前方")+item.getCount());
				}
			});

			//组装数据
			List<ImportAlarmNode> nodes = new ArrayList<>();
			for(String alarmName : alarmTypeDescList){
				ImportAlarmNode node = new ImportAlarmNode();
				node.setAlarmName(alarmName);
				node.setAlarmCount(alarmMap.get(alarmName)==null?0:alarmMap.get(alarmName));
				node.setHandleCount(handleMap.get(alarmName)==null?0:handleMap.get(alarmName));
				double rate = 0;
				if(node.getAlarmCount() > 0){
					rate = MathUtil.divideRoundDouble(node.getHandleCount(), node.getAlarmCount(), 4);
				}
				node.setHandleRate(rate > 1?1:rate);
				node.setHandleRateStr(MathUtil.formatToPercent(node.getHandleRate(),2));
				nodes.add(node);
			}
			ImportAlarmResponse ia = new ImportAlarmResponse();
			ia.setAlarmList(nodes);
			return R.data(ia);
		}catch (Exception e){
			log.error("查询数据报错",e);
			return R.fail("查询数据报错");
		}
	}

	/**
	 * @description: 判断车辆是否移动
	 * @author: zhouxw
	 * @date: 2023-07-206 14:04:48
	 * @param: [licencePlate, licenceColor, alarmTime]
	 * @return: boolean
	 **/
	private boolean checkIsMoveForSingleVehicle(String licencePlate, String licenceColor, Date alarmTime){
		//1.查询车辆3分钟之内的轨迹
		long startTime = alarmTime.getTime() / 1000;
		long endTime = startTime + 60 * 3;
		List<LocationKudu> list = locationService.findLocationByCondition(licencePlate, Integer.parseInt(licenceColor), startTime, endTime);

		//2.判断是否有移动距离
		double mileage = 0;
		for(int i = 0 ; i < list.size() - 1; i++){
			LocationKudu start = list.get(i);
			LocationKudu end = list.get(i + 1);
			mileage += DistanceUtils.wgs84Distance(start.getLongitude(), start.getLatitude(), end.getLongitude(), end.getLatitude());
		}
		if(mileage < 1){
			return true;
		}else{
			return false;
		}
	}

	/**
	 * 获取发生过位置移动的报警点id列表
	 * @param alarmList
	 * @return
	 */
	private List<Long> getMovedAlarmIdList(List<AlarmBase> alarmList, Long startTime, Long endTime){
		long a1 = System.currentTimeMillis();
		List<Long> alarmIdList = new ArrayList<>();
		alarmList.forEach(item -> {
			alarmIdList.add(item.getAlarmId());
		});
		List<AlarmLocation> list = locationService.findLocationByAlarmIdListInThreeMinutes(alarmIdList, startTime, endTime);
		long a2 = System.currentTimeMillis();
		log.info("-=-=a1: "+(a2 - a1));

		//2.判断是否有移动距离
		long b1 = System.currentTimeMillis();
		List<Long> movedAlarmIdList = new ArrayList<>();
		//2.1 对轨迹进行分组
		Map<String,List<LocationKudu>> map = new HashMap<>();
		list.forEach(item -> {
			Long alarmId = item.getAlarmId();
			String key = item.getTargetId() + "~" + item.getTargetType() + "~" + alarmId;
			List<LocationKudu> listTmp = map.get(key);
			if(listTmp == null){
				listTmp = new ArrayList<>();
				map.put(key, listTmp);
			}
			listTmp.add(item);
		});
		long b2 = System.currentTimeMillis();
		log.info("-=-=a2: "+(b2 - b1));

		//2.2 对每辆车进行计算
		long c1 = System.currentTimeMillis();
		map.forEach((k,v) -> {
			//对轨迹点进行排序
			Collections.sort(v, (s1, s2) -> s1.getTime().compareTo(s2.getTime()));
			//计算里程
			double mileage = 0;
			for(int i = 0 ; i < v.size() - 1; i++){
				LocationKudu start = v.get(i);
				LocationKudu end = v.get(i + 1);
				mileage += DistanceUtils.wgs84Distance(start.getLongitude(), start.getLatitude(), end.getLongitude(), end.getLatitude());
			}
			//移动距离小于1米，表示报警真实
			if(mileage < 1 && k.split("~").length == 3 && StringUtils.hasText(k.split("~")[2])){
				Long alarmId = Long.parseLong(k.split("~")[2]);
				movedAlarmIdList.add(alarmId);
			}
		});
		long c2 = System.currentTimeMillis();
		log.info("-=-=a3: "+(c2 - c1));
		return movedAlarmIdList;
	}



	/**
	 * @description: 车辆运行状态
	 * @author: zhouxw
	 * @date: 2023-07-207 13:29:33
	 * @param: [user]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.statistic.vo.response.VehicleRunningStateResponse>
	 **/
	@GetMapping("/vehicleRunningState")
	public R<VehicleRunningStateResponse> vehicleRunningState(BladeUser user){

		if(user == null){
			return R.fail("用户未登录或无权限");
		}

		List<Long> deptIds = null;
		try {
			deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);
		}catch (Exception e){
			log.error("查询部门信息失败",e);
			return R.fail("用户登录失败或未授权");
		}

		VehicleRunningStateResponse resp = null;
		try{
			resp = vehicleService.statVehicleRunningState(user);
			return R.data(resp);
		}catch (Exception e){
			log.error("查询营运车辆数出错",e);
			return R.fail("查询出错");
		}
	}


	/**
	 * 查询当前实时未处理的报警
	 * @param user
	 * @param query
	 * @return
	 */
	@GetMapping("/realTimeUnHandleAlarm")
	public R<IPage<AlarmPushResponse>> realTimeUnHandleAlarm(BladeUser user, Query query){
		if(user == null){
			return R.fail("用户未登录或无权限");
		}
		//查询部门
		List<Long> deptIds = null;
		try {
			deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);
		}catch (Exception e){
			log.error("查询部门信息失败",e);
			return R.fail("用户登录失败或未授权");
		}
		//查询账号关联的车辆
		List<Integer> vehicleIds = null;

		List<String> userTypes = userInfoUtil.getUserType(user.getRoleName());
		String userType = StatisticConstants.USER_TYPE_SERVER;
		if(userTypes.contains(StatisticConstants.USER_TYPE_COMPANY)){
			//如果包含企业，则按照企业进行统计
			userType = StatisticConstants.USER_TYPE_COMPANY;
		}else if(userTypes.contains(StatisticConstants.USER_TYPE_THIRD)){
			//如果包含第三方，按照第三方统计
			userType = StatisticConstants.USER_TYPE_THIRD;
		}else{
			//如果只包含服务商，按照服务商统计
			userType = StatisticConstants.USER_TYPE_SERVER;
		}

		IPage<BdmSecurity> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		try {
			vehicleIds = userInfoUtil.getVehicleIdList(user.getUserId());

			//查询需要统计的报警
			//根据userId获取要排除的报警类型
			String tenantId = user.getTenantId();
			List<String> pushAlarmTypeList = getPushAlarmTypeList(tenantId);

			//查询报警信息
			//查询mysql
			//IPage<BdmSecurity> listPage = alarmService.findUnHandleRealTimeAlarmByPage(pushAlarmTypeList, deptIds, user.getUserId(), userType, page);
			//查询impala
			List<Integer> alarmTypeList = new ArrayList<>();
			pushAlarmTypeList.forEach(item -> {
				alarmTypeList.add(Integer.parseInt(item));
			});
			IPage<UnHandleRealTimeAlarm> listPage = impalaAlarmService.findUnHandleRealTimeAlarmByPage(alarmTypeList, deptIds, vehicleIds, userType, page);
			//转换数据
			if(listPage == null || listPage.getTotal() < 1){
				return R.data(null);
			}
			List<AlarmPushResponse> list = new ArrayList<>();
			listPage.getRecords().forEach(item ->{
				AlarmPushResponse apr = new AlarmPushResponse();
				apr.setAlarmTime(DateUtil.sdfHolder.get().format(item.getAlarmTime()*1000));
				apr.setAlarmLevel(item.getAlarmLevel()+"");
				apr.setAlarmId(item.getId()+"");
				apr.setAlarmType(item.getAlarmType()+"");
				apr.setAlarmTypeDesc(alarmTypeMap.get(item.getAlarmType()+""));
				apr.setLicencePlate(item.getLicencePlate());
				apr.setLicenceColor(item.getLicenceColor());
				apr.setLatitude(item.getLatitude());
				apr.setLongitude(item.getLongitude());
				apr.setSpeed(item.getSpeed());
				if(item.getAlarmComplete() != null && item.getAlarmComplete() == 1){
					apr.setHandleStateDesc("违规行为已纠正");
					apr.setHandleStateCode("2");
				}else if(item.getAlarmComplete() != null && item.getAlarmComplete() == 0){
					apr.setHandleStateDesc("平台已下发提示");
					apr.setHandleStateCode("1");
				}
				list.add(apr);
			});
			IPage<AlarmPushResponse> resPage = new Page<>();
			resPage.setSize(listPage.getSize());
			resPage.setCurrent(listPage.getCurrent());
			resPage.setTotal(listPage.getTotal());
			resPage.setRecords(list);

			return R.data(resPage);
		}catch (Exception e){
			log.error("查询实时未处理报警报错",e);
			return R.fail("查询出错");
		}

	}

	private List<String> getPushAlarmTypeList(String tenantId){
		if(tenantId == null){
			return null;
		}
		List<String> pushAlarmTypeList = new ArrayList<>();
		//需要推送的报警
		List<DictBiz> resList = new ArrayList<>();
		R<List<DictBiz>> alarmTypeRes = dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfTenant(CommonConstant.DICT_ALARM_PUSH_MONITOR,CommonConstant.DICT_ALARM_PUSH_MONITOR_DICT_KEY, tenantId);
		if(alarmTypeRes.isSuccess() && alarmTypeRes.getData().size() > 0){
			resList.addAll(alarmTypeRes.getData());
		}
		for (DictBiz d : resList) {
			alarmTypeMap.put(d.getDictKey(), d.getDictValue());
			pushAlarmTypeList.add(d.getDictKey());
		}

		return pushAlarmTypeList;
	}

}
