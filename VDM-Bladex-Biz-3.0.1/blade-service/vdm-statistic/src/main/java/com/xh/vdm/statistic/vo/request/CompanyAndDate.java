package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 企业和日期
 */
@ApiModel(value = "请求体：企业报告列表")
@Data
public class CompanyAndDate {

	@JsonProperty("report_type")
	@ApiModelProperty(name = "report_type", value = "报告类型（day：日报，month：月报）", example = "day", required = true)
	@NotEmpty(message = "报告类型为空。")
	private String reportType;

	@JsonProperty("dept_id_list")
	@ApiModelProperty(name = "dept_id_list", value = "单位ID列表", example = "[1, ...]", required = false)
	private List<Long> deptIds;

	@JsonProperty("date")
	@ApiModelProperty(name = "date", value = "日期（支持日月，日格式：yyyy-MM，月格式yyyy-MM）", example = "2024-06-27或2024-06", required = false)
	private String date;
}
