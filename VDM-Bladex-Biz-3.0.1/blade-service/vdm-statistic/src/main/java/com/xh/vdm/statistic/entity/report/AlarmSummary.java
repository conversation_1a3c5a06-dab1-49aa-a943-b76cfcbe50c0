package com.xh.vdm.statistic.entity.report;

import com.xh.vdm.statistic.entity.AlarmBase;
import com.xh.vdm.statistic.entity.AlarmTypeAndList;
import lombok.Data;

import java.util.List;

/**
 * 日报/月报 报警情况
 */
@Data
public class AlarmSummary {


	//企业名称
	private String deptName;
	//统计日期
	private String statDate;

	//车辆总数
	private Long vehicleTotalCount;
	//上线车辆总数
	private Long vehicleGoOnlineCount;
	//离线车辆数
	private Long vehicleOfflineCount;
	//上线率
	private String goOnlineRateStr;
	//长期离线数量
	private Long vehicleLongOfflineCount;
	//报警总数
	private Long alarmTotalCount;
	//车均报警数
	private Double alarmPerVehicleCount;
	//报警概况
	private List<AlarmNode> alarmList;
	//报警详情
	private List<AlarmTypeAndList> alarmDetailList;

}
