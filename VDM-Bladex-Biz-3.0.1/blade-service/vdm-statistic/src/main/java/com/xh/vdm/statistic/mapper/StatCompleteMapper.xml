<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatCompleteMapper">

    <select id="checkExist" parameterType="string" resultType="com.xh.vdm.statistic.entity.StatComplete">
        select * from stat_complete_${_parameter} limit 1
    </select>

    <select id="createTable" parameterType="string">
        create table stat_complete_${_parameter} (like stat_complete INCLUDING INDEXES INCLUDING DEFAULTS)
    </select>

    <insert id="insertTempBatch">
        insert into tmp_stat_complete_data (licence_plate, licence_color,vehicle_id, dept_id, complete_data) values
        <foreach collection="list" item="item" open="" close="" separator=",">
            (#{item.licencePlate} , #{item.plateColor}, #{item.vehicleId}, #{item.deptId}, concat(#{item.completeRate},'#',#{item.allPointCount},'#',#{item.completePointCount},'#',#{item.allMiles},'#',#{item.completeMiles}))
        </foreach>
    </insert>

    <insert id="insertNewData">
        insert into stat_complete_${month} (licence_plate, licence_color,vehicle_id, dept_id, d${day}, update_time)
        select licence_plate ,licence_color,vehicle_id, dept_id, complete_data , now() from tmp_stat_complete_data tmp where not exists (
                select * from stat_complete_${month} stat where tmp.licence_plate = stat.licence_plate and tmp.licence_color = stat.licence_color
            );
    </insert>

    <update id="updateExistData">
        update stat_complete_${month}
        set d${day} = b.complete_data, update_time = now()
        from stat_complete_${month} a , tmp_stat_complete_data b
        where exists (select licence_plate from tmp_stat_complete_data c where a.licence_plate = c.licence_plate and a.licence_color = c.licence_color)
          and a.licence_plate = b.licence_plate and a.licence_color = b.licence_color
    </update>

    <select id="createLicencePlateTemp">
        create table if not exists tmp_complete_exist_licence_plate
        select licence_plate from tmp_stat_complete_data tmp
        where exists (
            select * from stat_complete_${month} stat where tmp.licence_plate = stat.licence_plate and tmp.licence_color = stat.licence_color
        );
    </select>

    <insert id="insertLicencePlateTemp">
        insert into tmp_complete_exist_licence_plate
        select licence_plate from tmp_stat_complete_data tmp
        where exists (
                      select * from stat_complete_${month} stat where tmp.licence_plate = stat.licence_plate and tmp.licence_color = stat.licence_color
                  );
    </insert>


    <select id="getLocationCompleteRate" parameterType="com.xh.vdm.statistic.entity.RateParam" resultType="com.xh.vdm.statistic.vo.response.LocationCompleteRateResponse">
        select round(sum(rate.total_continous_mileage),2) total_continous_mileage , round(sum(rate.total_mileage),2) total_mileage  ,  round((sum(rate.total_continous_mileage)/sum(rate.total_mileage)),4)  complete_rate  from
            (
                select
                    (coalesce(split_part(d01 , '#' , -1),0)+coalesce(split_part(d02 , '#' , -1),0)+coalesce(split_part(d03 , '#' , -1),0)+coalesce(split_part(d04 , '#' , -1),0)
                        +coalesce(split_part(d05 , '#' , -1),0)+coalesce(split_part(d06 , '#' , -1),0)+coalesce(split_part(d07 , '#' , -1),0)+coalesce(split_part(d08 , '#' , -1),0)
                        +coalesce(split_part(d09 , '#' , -1),0)+coalesce(split_part(d10 , '#' , -1),0)+coalesce(split_part(d11 , '#' , -1),0)+coalesce(split_part(d12 , '#' , -1),0)
                        +coalesce(split_part(d13 , '#' , -1),0)+coalesce(split_part(d14 , '#' , -1),0)+coalesce(split_part(d15 , '#' , -1),0)+coalesce(split_part(d16 , '#' , -1),0)
                        +coalesce(split_part(d17 , '#' , -1),0)+coalesce(split_part(d18 , '#' , -1),0)+coalesce(split_part(d19 , '#' , -1),0)+coalesce(split_part(d20 , '#' , -1),0)
                        +coalesce(split_part(d21 , '#' , -1),0)+coalesce(split_part(d22 , '#' , -1),0)+coalesce(split_part(d23 , '#' , -1),0)+coalesce(split_part(d24 , '#' , -1),0)
                        +coalesce(split_part(d25 , '#' , -1),0)+coalesce(split_part(d26 , '#' , -1),0)+coalesce(split_part(d27 , '#' , -1),0)+coalesce(split_part(d28 , '#' , -1),0)
                        +coalesce(split_part(d29 , '#' , -1),0)+coalesce(split_part(d30 , '#' , -1),0)+coalesce(split_part(d31 , '#' , -1),0)) total_continous_mileage,

                    (coalesce(split_part(split_part(d01 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d02 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d03 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d04 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d05 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d06 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d07 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d08 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d09 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d10 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d11 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d12 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d13 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d14 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d15 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d16 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d17 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d18 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d19 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d20 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d21 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d22 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d23 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d24 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d25 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d26 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d27 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d28 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d29 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d30 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d31, '#' , -2), '#' , 1),0)) total_mileage

                from stat_complete_${month} sc
                left join bdm_vehicle bv on sc.licence_plate = bv.licence_plate and sc.licence_color::text = bv.licence_color
                where 1 = 1
                    <if test="deptId != null and deptId != ''">
                        and bv.dept_id = #{deptId}
                    </if>
                  and bv.vehicle_owner_id = #{ownerId,jdbcType=BIGINT}
                and bv.is_check = 1
                  and bv.is_del = 0
            ) rate
    </select>


    <select id="getUnCompleteDetailList" parameterType="com.xh.vdm.statistic.entity.DetailParam" resultType="com.xh.vdm.statistic.vo.response.LocationCompleteDetailResponse">
        select sd.name dept_name , sc.licence_plate , bv.licence_color licence_color , bt.sim_id, bt.terminal_id , round(sc.total_continous_mileage,2) continous_mileage ,round(sc.total_mileage,2) total_mileage , concat(round(sc.complete_rate * 100,2), '%') complete_rate , sc.date , bt.terminal_model
        from
        (
        select concat(#{param.month}, '-01') date , licence_plate , licence_color , round(split_part(d01 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d01 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d01 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d01 , '#' , 1) &lt; 1 and d01 is not null
        union
        select concat(#{param.month}, '-02') date,licence_plate ,  licence_color , round(split_part(d02 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d02 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d02 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d02 , '#' , 1) &lt; 1 and d02 is not null
        union
        select concat(#{param.month}, '-03') date,licence_plate ,  licence_color ,round(split_part(d03 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d03 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d03 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d03 , '#' , 1) &lt; 1 and d03 is not null
        union
        select concat(#{param.month}, '-04') date,licence_plate ,  licence_color ,round(split_part(d04 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d04 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d04 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d04 , '#' , 1) &lt; 1 and d04 is not null
        union
        select concat(#{param.month}, '-05') date,licence_plate , licence_color , round(split_part(d05 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d05 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d05 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d05 , '#' , 1) &lt; 1 and d05 is not null
        union
        select concat(#{param.month}, '-06') date,licence_plate ,  licence_color ,round(split_part(d06 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d06 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d06 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d06 , '#' , 1) &lt; 1 and d06 is not null
        union
        select concat(#{param.month}, '-07') date,licence_plate ,  licence_color ,round(split_part(d07 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d07 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d07 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d07 , '#' , 1) &lt; 1 and d07 is not null
        union
        select concat(#{param.month}, '-08') date,licence_plate ,  licence_color ,round(split_part(d08 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d08 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d08 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d08 , '#' , 1) &lt; 1 and d08 is not null
        union
        select concat(#{param.month}, '-09') date,licence_plate , licence_color , round(split_part(d09 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d09 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d09 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d09 , '#' , 1) &lt; 1 and d09 is not null
        union
        select concat(#{param.month}, '-10') date,licence_plate ,  licence_color ,round(split_part(d10 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d10 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d10 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d10 , '#' , 1) &lt; 1 and d10 is not null
        union
        select concat(#{param.month}, '-11') date,licence_plate ,  licence_color ,round(split_part(d11 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d11 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d11 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d11 , '#' , 1) &lt; 1 and d11 is not null
        union
        select concat(#{param.month}, '-12') date,licence_plate ,  licence_color ,round(split_part(d12 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d12 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d12 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d12 , '#' , 1) &lt; 1 and d12 is not null
        union
        select concat(#{param.month}, '-13') date,licence_plate ,  licence_color ,round(split_part(d13 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d13 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d13 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d13 , '#' , 1) &lt; 1 and d13 is not null
        union
        select concat(#{param.month}, '-14') date,licence_plate ,  licence_color ,round(split_part(d14 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d14 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d14 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d14 , '#' , 1) &lt; 1 and d14 is not null
        union
        select concat(#{param.month}, '-15') date,licence_plate , licence_color , round(split_part(d15 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d15 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d15 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d15 , '#' , 1) &lt; 1 and d15 is not null
        union
        select concat(#{param.month}, '-16') date,licence_plate ,  licence_color ,round(split_part(d16 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d16 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d16 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d16 , '#' , 1) &lt; 1 and d16 is not null
        union
        select concat(#{param.month}, '-17') date,licence_plate ,  licence_color ,round(split_part(d17 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d17 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d17 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d17 , '#' , 1) &lt; 1 and d17 is not null
        union
        select concat(#{param.month}, '-18') date,licence_plate ,  licence_color ,round(split_part(d18 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d18 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d18 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d18 , '#' , 1) &lt; 1 and d18 is not null
        union
        select concat(#{param.month}, '-19') date,licence_plate ,  licence_color ,round(split_part(d19 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d19 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d19 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d19 , '#' , 1) &lt; 1 and d19 is not null
        union
        select concat(#{param.month}, '-20') date,licence_plate , licence_color , round(split_part(d20 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d20 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d20 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d20 , '#' , 1) &lt; 1 and d20 is not null
        union
        select concat(#{param.month}, '-21') date,licence_plate ,  licence_color ,round(split_part(d21 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d21 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d21 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d21 , '#' , 1) &lt; 1 and d21 is not null
        union
        select concat(#{param.month}, '-22') date,licence_plate ,  licence_color ,round(split_part(d22 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d22 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d22 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d22 , '#' , 1) &lt; 1 and d22 is not null
        union
        select concat(#{param.month}, '-23') date,licence_plate ,  licence_color ,round(split_part(d23 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d23 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d23 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d23 , '#' , 1) &lt; 1 and d23 is not null
        union
        select concat(#{param.month}, '-24') date,licence_plate , licence_color , round(split_part(d24 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d24 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d24 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d24 , '#' , 1) &lt; 1 and d24 is not null
        union
        select concat(#{param.month}, '-25') date,licence_plate ,  licence_color ,round(split_part(d25 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d25 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d25 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d25 , '#' , 1) &lt; 1 and d25 is not null
        union
        select concat(#{param.month}, '-26') date,licence_plate , licence_color , round(split_part(d26 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d26 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d26 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d26 , '#' , 1) &lt; 1 and d26 is not null
        union
        select concat(#{param.month}, '-27') date,licence_plate ,  licence_color ,round(split_part(d27 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d27 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d27 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d27 , '#' , 1) &lt; 1 and d27 is not null
        union
        select concat(#{param.month}, '-28') date,licence_plate , licence_color , round(split_part(d28 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d28 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d28 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d28 , '#' , 1) &lt; 1 and d28 is not null
        union
        select concat(#{param.month}, '-29') date,licence_plate ,  licence_color ,round(split_part(d29 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d29 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d29 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d29 , '#' , 1) &lt; 1 and d29 is not null
        union
        select concat(#{param.month}, '-30') date,licence_plate ,  licence_color ,round(split_part(d30 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d30 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d30 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d30 , '#' , 1) &lt; 1 and d30 is not null
        union
        select concat(#{param.month}, '-31' )  date,licence_plate ,  licence_color ,round(split_part(d31 , '#' , -1),4) total_continous_mileage,  round(split_part(split_part(d31 , '#' , -2), '#' , 1),4) total_mileage , round(split_part(d31 , '#' , 1),4) complete_rate
        from stat_complete_${param.monthStrict}
        where split_part(d31 , '#' , 1) &lt; 1 and d31 is not null
        ) sc
        left join bdm_vehicle bv on sc.licence_plate = bv.licence_plate and sc.licence_color::text = bv.licence_color
        left join sys_dept sd on bv.dept_id = sd.id
        left join bdm_terminal bt on bt.id = bv.terminal_id
        where 1 = 1
        <if test="param.deptId != null and param.deptId != ''">
            and bv.dept_id = #{param.deptId,jdbcType=BIGINT}
        </if>
        and bv.vehicle_owner_id = #{param.ownerId,jdbcType=BIGINT}
        <if test="param.licencePlate != null and param.licencePlate != ''">
            and bv.licence_plate = #{param.licencePlate,jdbcType=VARCHAR}
        </if>
        <if test="param.plateColor != null and param.plateColor != ''">
            and bv.licence_color = #{param.plateColor,jdbcType=INTEGER}
        </if>
        and bv.is_check = 1
        and bv.is_del = 0
        and sd.is_del = 0
        order by sd.id , sc.licence_plate , sc.licence_color, sc.date desc
    </select>

    <select id="getCompleteRateByDeptId" resultType="com.xh.vdm.statistic.entity.VehicleRateWithDept">
        select dept_id  ,  round((sum(rate.total_continous_mileage)/sum(rate.total_mileage)),4)  rate from
            (
                select
                    (coalesce(split_part(d01 , '#' , -1),0)+coalesce(split_part(d02 , '#' , -1),0)+coalesce(split_part(d03 , '#' , -1),0)+coalesce(split_part(d04 , '#' , -1),0)
                        +coalesce(split_part(d05 , '#' , -1),0)+coalesce(split_part(d06 , '#' , -1),0)+coalesce(split_part(d07 , '#' , -1),0)+coalesce(split_part(d08 , '#' , -1),0)
                        +coalesce(split_part(d09 , '#' , -1),0)+coalesce(split_part(d10 , '#' , -1),0)+coalesce(split_part(d11 , '#' , -1),0)+coalesce(split_part(d12 , '#' , -1),0)
                        +coalesce(split_part(d13 , '#' , -1),0)+coalesce(split_part(d14 , '#' , -1),0)+coalesce(split_part(d15 , '#' , -1),0)+coalesce(split_part(d16 , '#' , -1),0)
                        +coalesce(split_part(d17 , '#' , -1),0)+coalesce(split_part(d18 , '#' , -1),0)+coalesce(split_part(d19 , '#' , -1),0)+coalesce(split_part(d20 , '#' , -1),0)
                        +coalesce(split_part(d21 , '#' , -1),0)+coalesce(split_part(d22 , '#' , -1),0)+coalesce(split_part(d23 , '#' , -1),0)+coalesce(split_part(d24 , '#' , -1),0)
                        +coalesce(split_part(d25 , '#' , -1),0)+coalesce(split_part(d26 , '#' , -1),0)+coalesce(split_part(d27 , '#' , -1),0)+coalesce(split_part(d28 , '#' , -1),0)
                        +coalesce(split_part(d29 , '#' , -1),0)+coalesce(split_part(d30 , '#' , -1),0)+coalesce(split_part(d31 , '#' , -1),0)) total_continous_mileage,

                    (coalesce(split_part(split_part(d01 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d02 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d03 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d04 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d05 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d06 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d07 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d08 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d09 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d10 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d11 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d12 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d13 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d14 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d15 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d16 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d17 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d18 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d19 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d20 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d21 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d22 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d23 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d24 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d25 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d26 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d27 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d28 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d29 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d30 , '#' , -2), '#' , 1),0)
                        + coalesce( split_part(split_part(d31, '#' , -2), '#' , 1),0)) total_mileage,
                    bv.dept_id
                from stat_complete_${month} sc
                left join bdm_vehicle bv on sc.licence_plate = bv.licence_plate
                where 1 = 1
                and bv.is_check = 1
                <if test="deptIds != null and deptIds != ''">
                  and bv.dept_id in (
                    <foreach collection="deptIds" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                    )
                </if>
        <if test="ownerId != null and ownerId != ''">
            and bv.vehicle_owner_id = #{ownerId}
        </if>
                  and bv.is_del = 0
            ) rate
        group by dept_id
    </select>

    <select id="getCompleteRateByDeptIdDeptOrArea" resultType="com.xh.vdm.statistic.entity.VehicleRateWithDept">
        select
        <if test="deptId != null and deptId != ''">
            dept_id  ,
        </if>
        round((sum(rate.total_continous_mileage)/sum(rate.total_mileage)),4)  rate from
        (
        select
        (coalesce(split_part(d01 , '#' , -1),0)+coalesce(split_part(d02 , '#' , -1),0)+coalesce(split_part(d03 , '#' , -1),0)+coalesce(split_part(d04 , '#' , -1),0)
        +coalesce(split_part(d05 , '#' , -1),0)+coalesce(split_part(d06 , '#' , -1),0)+coalesce(split_part(d07 , '#' , -1),0)+coalesce(split_part(d08 , '#' , -1),0)
        +coalesce(split_part(d09 , '#' , -1),0)+coalesce(split_part(d10 , '#' , -1),0)+coalesce(split_part(d11 , '#' , -1),0)+coalesce(split_part(d12 , '#' , -1),0)
        +coalesce(split_part(d13 , '#' , -1),0)+coalesce(split_part(d14 , '#' , -1),0)+coalesce(split_part(d15 , '#' , -1),0)+coalesce(split_part(d16 , '#' , -1),0)
        +coalesce(split_part(d17 , '#' , -1),0)+coalesce(split_part(d18 , '#' , -1),0)+coalesce(split_part(d19 , '#' , -1),0)+coalesce(split_part(d20 , '#' , -1),0)
        +coalesce(split_part(d21 , '#' , -1),0)+coalesce(split_part(d22 , '#' , -1),0)+coalesce(split_part(d23 , '#' , -1),0)+coalesce(split_part(d24 , '#' , -1),0)
        +coalesce(split_part(d25 , '#' , -1),0)+coalesce(split_part(d26 , '#' , -1),0)+coalesce(split_part(d27 , '#' , -1),0)+coalesce(split_part(d28 , '#' , -1),0)
        +coalesce(split_part(d29 , '#' , -1),0)+coalesce(split_part(d30 , '#' , -1),0)+coalesce(split_part(d31 , '#' , -1),0)) total_continous_mileage,

        (coalesce(split_part(split_part(d01 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d02 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d03 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d04 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d05 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d06 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d07 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d08 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d09 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d10 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d11 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d12 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d13 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d14 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d15 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d16 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d17 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d18 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d19 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d20 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d21 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d22 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d23 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d24 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d25 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d26 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d27 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d28 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d29 , '#' , -2), '#' , 1),0)+ coalesce( split_part(split_part(d30 , '#' , -2), '#' , 1),0)
        + coalesce( split_part(split_part(d31, '#' , -2), '#' , 1),0)) total_mileage,
        bv.dept_id
        from stat_complete_${month} sc
        left join bdm_vehicle bv on sc.licence_plate = bv.licence_plate
        where 1 = 1
        and bv.vehicle_use_type in (10,11,12,30,31,32)
        <if test="deptId != null and deptId != ''">
            and bv.dept_id  = #{deptId}
        </if>
        <if test="ownerId != null and ownerId != ''">
            and bv.vehicle_owner_id = #{ownerId}
        </if>
        and bv.is_del = 0
        ) rate
        <if test="deptId != null and deptId != ''">
            group by dept_id
        </if>
    </select>

    <select id="getCompleteMileageMonth" resultType="com.xh.vdm.statistic.entity.CompleteMileageNode">
        select sc.licence_plate, sc.licence_color,
        coalesce((
        <foreach collection="dateList" item="date" separator="+" index="idx">
            coalesce(split_part(d${date}, '#', -1)::bigint, 0)
        </foreach>
        )::bigint,0)complete_mileage,
        coalesce((
        <foreach collection="dateList" item="date" separator="+" index="idx">
            coalesce(split_part(split_part(d${date}, '#', -2), '#', 1)::bigint, 0)
        </foreach>
        ),0) total_mileage
        from stat_complete_${month} sc
        left join bdm_vehicle bv on sc.licence_plate = bv.licence_plate and sc.licence_color::text = bv.licence_color
        where 1 = 1
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and bv.id in
            <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
            and (
            <if test="request.deptList != null">
                bv.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="request.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="request.vehicleUseType != null ">
            and bv.vehicle_use_type in
            <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.vehicleOwnerId != null">
            and bv.vehicle_owner_id = #{request.vehicleOwnerId}
        </if>
        <if test="request.accessMode != null">
            and bv.access_mode = #{request.accessMode}
        </if>
    </select>

</mapper>
