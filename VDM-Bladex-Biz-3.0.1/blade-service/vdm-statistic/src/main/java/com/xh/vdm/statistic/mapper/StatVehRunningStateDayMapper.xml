<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatVehRunningStateDayMapper">

    <select id="getVehRunningStateDayByPage" resultType="com.xh.vdm.statistic.entity.StatVehRunningStateDay">
        select * from stat_veh_running_state_day_${month}
        where date = #{request.date,jdbcType=VARCHAR}
        <if test="request.licencePlate != null and request.licencePlate != ''">
            and licence_plate = #{request.licencePlate,jdbcType=VARCHAR}
        </if>
        and ( dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
        order by ${request.orderField} ${request.orderType}
    </select>

    <select id="getAlarmSortInfoToday" resultType="com.xh.vdm.statistic.vo.response.alarm.AlarmSortResponse">
        select licence_plate, licence_color, date, over_speed_count, tired_count fatigue_count, night_driving_count night_count,adas_count, dsm_count, (over_speed_count+tired_count+night_driving_count+adas_count+dsm_count) total_alarm_count
        from stat_veh_running_state_day_${month}
        where 1 = 1
        and date = #{date}
        and (dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">#{deptId}
        </foreach>
        )
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach
                collection="vehicleIds" item="vehicleId" separator=",">#{vehicleId}
            </foreach>
            )
        </if>
        )
        order by (over_speed_count+tired_count+night_driving_count+adas_count+dsm_count) desc
    </select>
</mapper>
