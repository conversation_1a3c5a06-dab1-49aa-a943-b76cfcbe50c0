package com.xh.vdm.statistic.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xh.vdm.statistic.utils.DistanceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springblade.common.constant.DictKeyConstant;
import org.springblade.common.constant.RedisConstant;
import org.springblade.entity.Location;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@EnableKafka
public class TargetOdometerConsumer {

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	@KafkaListener(
		containerFactory = "targetOdometerKafkaListenerContainerFactory",
		topics = {"ce.comms.fct.location.0"},
		batch = "true"
	)
	public void addTargetOdometer (List<ConsumerRecord<String, String>> consumerRecords, Acknowledgment acknowledgment) {
		if (CollectionUtils.isEmpty(consumerRecords)) {
			return;
		}

		Map<Object, Object> targetMap =
			Boolean.TRUE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_BASE_INFO_TARGET)) ?
			this.redisTemplate.opsForHash().entries(RedisConstant.HASH_BASE_INFO_TARGET) :
			new HashMap<>();

		String msg;
		Location location;
		String targetKey;
		Object targetO;
		JSONObject targetJ;
		long time;
		double longitude;
		double latitude;
		double altitude;
		Object targetOdometerO;
		JSONObject targetOdometerJ;
		double lastLon;
		double lastLat;
		double lastAlt;
		double odometer;
		double distance;
		for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
			msg = consumerRecord.value();
//			log.info("receive msg when add target odometer: {}", msg);
			location = JSON.parseObject(msg, Location.class);
			if (location == null) {
				log.error("fail parse location when add target odometer: {}", msg);
				continue;
			}
			if (!location.getTargetType().toString().equals(DictKeyConstant.TARGET_TYPE_VEHICLE)) {
				continue;
			}

			targetKey = location.getTargetType() + "-" + location.getTargetId();
			if ((!targetMap.containsKey(targetKey)) || ((targetO = targetMap.get(targetKey)) == null)) {
				log.error("fail get target info when add target odometer: {}", targetKey);
				continue;
			}

			targetJ = JSONObject.parseObject(targetO.toString());
			if ((targetJ == null) || (!targetJ.containsKey("deptId"))) {
				log.error("fail get dept info when add target odometer: {}", targetO);
				continue;
			}

			targetKey += ("-" + targetJ.get("deptId"));
			time = location.getTime();
			longitude = location.getLongitude();
			latitude = location.getLatitude();
			altitude = location.getAltitude();
			if (
				Boolean.FALSE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_TARGET_ODOMETER)) ||
				(!this.redisTemplate.opsForHash().hasKey(RedisConstant.HASH_TARGET_ODOMETER, targetKey)) ||
				((targetOdometerO = this.redisTemplate.opsForHash().get(RedisConstant.HASH_TARGET_ODOMETER, targetKey)) == null)
			) {
				targetOdometerJ = new JSONObject();
				odometer = 0;
				distance = 0;
			} else {
				targetOdometerJ = JSON.parseObject(JSON.toJSONString(targetOdometerO));
				lastLon = targetOdometerJ.getDouble("last_lon");
				lastLat = targetOdometerJ.getDouble("last_lat");
				lastAlt = targetOdometerJ.getDouble("last_alt");
				odometer = targetOdometerJ.getDouble("odometer");
				distance = DistanceUtils.distanceOfHeight(longitude, latitude, altitude, lastLon, lastLat, lastAlt);
			}

			targetOdometerJ.put("last_time", time);
			targetOdometerJ.put("last_lon", longitude);
			targetOdometerJ.put("last_lat", latitude);
			targetOdometerJ.put("last_alt", altitude);
			targetOdometerJ.put("odometer", odometer + (distance / 1000));
			this.redisTemplate.opsForHash().put(RedisConstant.HASH_TARGET_ODOMETER, targetKey, targetOdometerJ);
		}

		// 有点类似InnoDB事务的提交，如果消费了消息，但消息处理过程出现异常而退出，则消息可被回滚到列队中。只有执行到这步，相当于提交，消息队列才正式认为该消息已被消费。
		acknowledgment.acknowledge();
	}
}
