package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.*;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-14
 */
public interface IBdmSecurityService extends IService<BdmSecurity> {

    /**
     * @description: 根据指定时间戳（精确到秒）查询当天预警次数
     * @author: zhouxw
     * @date: 2022/11/14 9:06 PM
     * @param: [day]
     * @return: int
     **/
    long findAlarmCountByDay(Long deptId , long secondTimestampInTheDay) throws Exception;

    /**
     * @description: 根据指定月份查询月度平均每天预警次数
     * 平均预警次数 = 当月总预警次数 / 当月天数
     * @author: zhouxw
     * @date: 2022/11/14 9:09 PM
     * @param: [month]
     * @return: int
     **/
    double findAverageAlarmCountByMonth(Long deptId , long secondTimestampInTheMonth) throws Exception;

    /**
     * @description: 根据指定月份查询月度平均预警趋势
     * 从指定月份开始（包含当前月份），向前查询，共12个月
     * @author: zhouxw
     * @date: 2022/11/14 9:21 PM
     * @param: [month]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
     **/
    List<DateAndData> findMonthTrend(Long deptId , String month) throws Exception;

    /**
     * @description: 查询指定月份内每天的预警次数
     * @author: zhouxw
     * @date: 2022/11/14 9:54 PM
     * @param: [month]
     * @return: java.util.List<com.xh.vdm.statistic.entity.AlarmTypeAndCount>
     **/
    List<AlarmTypeAndCount> findAlarmTypeAndCountEveryDayInMonth(Long deptId , String month) throws Exception;


    /**
     * @description: 查询指定时间段内指定驾驶员的报警信息
     * @author: zhouxw
     * @date: 2022/11/19 3:37 PM
     * @param: [idCard, startSecondTimestamp, endSecondTimestamp]
     * @return: java.util.List<com.xh.vdm.statistic.entity.BdmSecurity>
     **/
    List<BdmSecurity> findSecurityByIdCardAndDate (String idCard , long startSecondTimestamp , long endSecondTimestamp) throws Exception;

    /**
     * @description: 查询指定时间段内某驾驶员每天的报警类型
     * 获取每种报警类型发生在哪几天  date: yyyy-MM-dd
     * @author: zhouxw
     * @date: 2022/11/19 4:40 PM
     * @param: [idCard, startSecondTimestamp, endSecondTimestamp]
     * @return: java.util.List<com.xh.vdm.statistic.entity.AlarmTypeAndDateAndIdCard>
     **/
    List<AlarmTypeAndDateAndIdCard> findEveryAlarmTypeByIdCard(String idCard , long startSecondTimestamp , long endSecondTimestamp) throws Exception;

    /**
     * @description: 查询某驾驶员在指定时间段内每天的报警数
     * @author: zhouxw
     * @date: 2022/11/19 10:04 PM
     * @param: [idCard, startSecondTimestamp, endSecondTimestamp]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
     **/
    List<DateAndCount> findAlarmCountEveryDay(String idCard , long startSecondTimestamp , long endSecondTimestamp) throws Exception;

    /**
     * @description: 查询指定企业在某个时间段内的不规范行为次数总数
     * 如果不指定deptid ，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/21 11:19 AM
     * @param: [deptid, startSecondTimestamp, endSecondTimestamp]
     * @return: long
     **/
    long findAlarmCountForDept(Long deptid , long startSecondTimestamp , long endSecondTimestamp) throws Exception;
}
