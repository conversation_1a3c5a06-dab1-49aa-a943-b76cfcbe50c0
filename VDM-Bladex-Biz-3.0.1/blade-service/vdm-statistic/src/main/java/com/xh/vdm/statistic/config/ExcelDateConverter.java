package com.xh.vdm.statistic.config;


import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.xh.vdm.statistic.utils.DateUtil;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class ExcelDateConverter implements Converter<Date> {

	@Override
	public Class<?> supportJavaTypeKey() {
		return Converter.super.supportJavaTypeKey();
	}

	@Override
	public CellDataTypeEnum supportExcelTypeKey() {
		return Converter.super.supportExcelTypeKey();
	}

	@Override
	public WriteCellData<?> convertToExcelData(Date value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
		String dateValue = DateUtil.sdfHolder.get().format(value);
		return new WriteCellData<>(dateValue);
	}
}
