package com.xh.vdm.statistic.service.impl;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.StatVehRunningStateDayMapper;
import com.xh.vdm.statistic.mapper.StatVehRunningStateMonthMapper;
import com.xh.vdm.statistic.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.utils.*;
import com.xh.vdm.statistic.vo.request.*;
import com.xh.vdm.statistic.vo.response.*;
import com.xh.vdm.statistic.vo.response.alarm.AlarmSortResponse;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.AlarmConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.vo.DictTreeNodeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;


/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
@Service
@Slf4j
public class StatVehRunningStateServiceImpl extends ServiceImpl<StatVehRunningStateMonthMapper, StatVehRunningStateMonth> implements IStatVehRunningStateService {


	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private IBladeDeptService bladeDeptService;

	@Resource
	private CommonBusiUtil commonBusiUtil;

	@Resource
	private IBamThirdPartyPlatformService thirdPartyPlatformService;

	@Resource
	private LogUtil logUtil;

	private ExecutorService threadPool = new ThreadPoolExecutor(10,20,100, TimeUnit.SECONDS,new LinkedBlockingQueue<Runnable>());

	@Resource
	private IAlarmService alarmService;

	@Resource
	private IStatDriftService driftService;

	@Resource
	private ILocationQualityService locationQualityService;

	@Resource
	private IStatCompleteService completeService;
	@Resource
	private CommonBusiUtil busiUtil;

	@Resource
	private ISysClient sysClient;

	@Resource
	private IStatNotLocationService notLocationService;

	@Resource
	private IBladeDeptService deptService;

	@Resource
	private IBdmVehicleService vehicleService;

	private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

	@Resource
	private IStatTaskLogService taskLogService;

	@Resource
	private IImpalaAlarmService impalaAlarmService;

	@Resource
	private VdmUserInfoUtil vdmUserInfoUtil;

	ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM"));
	ThreadLocal<SimpleDateFormat> sdfHolderDate = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

	@Resource
	private StatVehRunningStateDayMapper dayMapper;

	@Override
	public IPage<StatVehRunningStateMonth> findRunningStateByPage(VehRunningStateRequest request, Query query, List<Long> deptIds , Long userId) throws Exception {
		Page<StatVehRunningStateMonth> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		String month = request.getMonth();
		if(StringUtils.isEmpty(month)){
			//如果没有指定month，则默认查询当前月份
			month = sdfHolder.get().format(new Date()).replace("-","");
		}
		request.setMonth(month+"%");
		if(StringUtil.isEmpty(request.getOrderField())){
			request.setOrderField("total_alarm_count");
		}
		if(StringUtil.isEmpty(request.getOrderType())){
			request.setOrderType("asc");
		}
		return baseMapper.getVehRunningStateByPage(request, page,deptIds, userId);
	}

	@Override
	public IPage<StatVehRunningStateDay> findRunningStateDayByPage(VehRunningStateDayRequest request, Query query, List<Long> deptIds , Long userId) throws Exception {
		Page<StatVehRunningStateMonth> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		String date = request.getDate();
		String month = date.substring(0,6).replace("-","");
		if(StringUtils.isEmpty(date)){
			//如果没有指定日期，则默认查询当天
			date = sdfHolderDate.get().format(new Date());
		}
		request.setDate(date);
		if(StringUtil.isEmpty(request.getOrderField())){
			request.setOrderField("licence_plate");
		}
		if(StringUtil.isEmpty(request.getOrderType())){
			request.setOrderType("asc");
		}
		return dayMapper.getVehRunningStateDayByPage(month,request, page,deptIds, userId);
	}

	@Override
	public List<AlarmSortResponse> findAlarmSortInfoToday(Query query, List<Long> deptIds, List<Integer> vehicleIds, BladeUser user) throws Exception {
		if(user == null){
			throw new Exception("用户未登录或未授权");
		}
		String date = DateUtil.getDateString().replace("-","");
		String month = date.substring(0,6);
		IPage<AlarmSortResponse> page = new Page<>();
		page.setSize(query.getSize());
		page.setCurrent(query.getCurrent());

		long startTime = DateUtil.getDayFirstSecondTimestamp();
		long endTime = DateUtil.getDayLastSecondTimestamp(startTime);

		//0.整理报警类型
		//超速
		List<Integer> overSpeedAlarmType = VdmUserInfoUtil.getOverSpeedAlarmType();
		//疲劳
		List<Integer> fatigueAlarmType = VdmUserInfoUtil.getFatigueAlarmType();
		//夜间禁行
		List<Integer> nightAlarmType = VdmUserInfoUtil.getNightLimitAlarmType();
		//ADAS
		List<Integer> adasAlarmType = VdmUserInfoUtil.getAdasAlarmType();
		//DSM
		List<Integer> dsmAlarmType = VdmUserInfoUtil.getDsmAlarmType();

		//报警集合
		List<Integer> alarmList = new ArrayList<>();
		alarmList.addAll(overSpeedAlarmType);
		alarmList.addAll(fatigueAlarmType);
		alarmList.addAll(nightAlarmType);
		alarmList.addAll(adasAlarmType);
		alarmList.addAll(dsmAlarmType);

		//1.查询报警数前n的车辆
		List<VehicleAndCount> vehTop = impalaAlarmService.findVehicleAlarmCountTop(alarmList,deptIds,vehicleIds,startTime, endTime,query.getSize());
		if(vehTop == null || vehTop.size() < 1){
			return null;
		}

		//2.查询前n车辆的报警情况
		Map<String,Long> countMap = new HashMap<>();
		List<Integer> vehicles = new ArrayList<>();
		vehTop.forEach(item -> {
			vehicles.add(item.getVehicleId());
			countMap.put(item.getVehicleId()+"", item.getCount());
		});
		List<VehicleAndAlarmTypeAndCount> alarmCountInfo = impalaAlarmService.findVehicleAlarmCountInfo(alarmList, vehicles, startTime, endTime);

		//3.整理数据
		//整理报警情况
		Map<String,AlarmSortResponse> alarmInfoMap = new HashMap<>();
		alarmCountInfo.forEach(item -> {
			String vehicleId = item.getVehicleId()+"";
			AlarmSortResponse res = alarmInfoMap.get(vehicleId);
			if(res == null){
				AlarmSortResponse response = new AlarmSortResponse();
				response.setLicencePlate(item.getLicencePlate());
				response.setLicenceColor(Integer.parseInt(item.getLicenceColor()));
				response.setTotalAlarmCount(countMap.get(vehicleId));
				response.setOverSpeedCount(0L);
				response.setFatigueCount(0L);
				response.setNightCount(0L);
				response.setAdasCount(0L);
				response.setDsmCount(0L);
				response.setDate(DateUtil.getDateString());
				alarmInfoMap.put(vehicleId,response);
				res = response;
			}
			Integer alarmType = item.getAlarmType();
			if(overSpeedAlarmType.contains(alarmType)){
				//超速
				res.setOverSpeedCount(res.getOverSpeedCount()+item.getCount());
			}else if(fatigueAlarmType.contains(alarmType)){
				//疲劳
				res.setFatigueCount(res.getFatigueCount()+item.getCount());
			}else if(nightAlarmType.contains(alarmType)){
				//夜间禁行
				res.setNightCount(res.getNightCount()+item.getCount());
			}else if(adasAlarmType.contains(alarmType)){
				//adas
				res.setAdasCount(res.getAdasCount()+item.getCount());
			}else if(dsmAlarmType.contains(alarmType)){
				//dsm
				res.setDsmCount(res.getDsmCount()+item.getCount());
			}
		});

		//数据排序
		List<AlarmSortResponse> list = new ArrayList<>();
		alarmInfoMap.forEach((k,v) -> {
			list.add(v);
		});
		Collections.sort(list, (o1, o2) -> (int)(o2.getTotalAlarmCount() - o1.getTotalAlarmCount()));

		return list;
	}

	@Override
	public IPage<VehicleLastPositionResponse> findLastPositionInfo(CommonBaseRequest request, Query query) throws Exception {
		IPage<VehicleLastPositionResponse> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		IPage<VehicleLastPositionResponse> pageRes = baseMapper.getLastPositionInfo(request, page);
		//格式化数据
		if(pageRes != null && pageRes.getRecords() != null && pageRes.getRecords().size() > 0){
			pageRes.getRecords().forEach(item -> {
				item.setSpeed(MathUtil.roundDouble(item.getSpeed(),2));
			});
		}
		return pageRes;
	}

	@Override
	public IPage<NotLocationResponse> findNotLocationInfo(NotLocationRequest request, Query query) throws Exception {

		CountDownLatch countDownLatch = new CountDownLatch(1);

		//查询部门信息、上级平台信息，用于后续手动增加字段
		Map<String,BladeDept> deptMap = new HashMap<>();
		Map<String,BamThirdPartyPlatform> platMap = new HashMap<>();
		threadPool.submit(() -> {
			try{
				List<BladeDept> depts = null;
				List<BamThirdPartyPlatform> platList = null;
				platList = thirdPartyPlatformService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled, 0));
				depts = bladeDeptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
				platList.forEach(item -> {
					platMap.put(item.getId()+"", item);
				});
				depts.forEach(item -> {
					deptMap.put(item.getId()+"", item);
				});
			}catch (Exception e){
				log.error("异步处理失败",e);
			}finally {
				countDownLatch.countDown();
			}
		});

		//1.获取查询月份和查询时段
		long startTime = request.getStartTime();
		long endTime = request.getEndTime();

		//2.查询月份和统计日期
		List<DateListAndMonth> dmList = new ArrayList<>();
		List<String> monthList = DateUtil.getMonthList(startTime, endTime);
		if(monthList == null || monthList.size() < 1){
			return null;
		}
		for(int i = 0 ; i < monthList.size(); i++){
			long startTimeTmp = 0;
			long endTimeTmp = 0;
			if(i == 0){
				//如果是首个月份
				//从开始时间到月末
				startTimeTmp = startTime;
				endTimeTmp = DateUtil.getMonthLastSecondTimestamp(startTimeTmp);
			}else if(i == monthList.size() - 1 && i > 0){
				//如果是最后一个月份
				//从月初到最后时间
				startTimeTmp = DateUtil.getMonthFirstSecondTimestamp(endTime);
				endTimeTmp = endTime;
			}else{
				//如果是中间月份
				//从月初到月末
				startTimeTmp = DateUtil.getMonthFirstSecondTimestamp(monthList.get(i));
				endTimeTmp = DateUtil.getMonthLastSecondTimestamp(monthList.get(i));
			}
			List<String> dateListTmp = DateUtil.getDateList(startTimeTmp, endTimeTmp);
			DateListAndMonth dm = new DateListAndMonth();
			dm.setDateList(dateListTmp);
			dm.setMonth(monthList.get(i).replace("-",""));
			dmList.add(dm);
		}
		request.setDmList(dmList);

		//3.统计数据
		IPage<NotLocationResponse> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		IPage<NotLocationResponse> pageRes = baseMapper.getNotLocationInfo(request, page);

		//补充字段
		countDownLatch.await();
		//添加字段信息
		pageRes.getRecords().forEach(item -> {
			Long deptId = item.getDeptId();
			Long vehicleOwnerId = item.getVehicleOwnerId();
			BladeDept dept = deptMap.get(deptId+"");
			BamThirdPartyPlatform plat = platMap.get(vehicleOwnerId+"");
			if(dept != null){
				item.setDeptName(dept.getDeptName());
			}
			if(plat != null){
				item.setVehicleOwner(StringUtils.isEmpty(plat.getName())?"非营运车辆":plat.getName());
			}else{
				item.setVehicleOwner("非营运车辆");
			}
		});

		return pageRes;
	}


	@Override
	public IPage<ErrLocationMoveResponse> findErrLocationMoveInfo(CommonBaseCrossMonthRequest request, Query query) throws Exception {
		//1.获取查询月份和查询时段
		long startTime = request.getStartTime();
		long endTime = request.getEndTime();

		//2.查询月份和统计日期
		List<DateListAndMonth> dmList = new ArrayList<>();
		List<String> monthList = DateUtil.getMonthList(startTime, endTime);
		if(monthList == null || monthList.size() < 1){
			return null;
		}
		if(monthList.size() == 1){
			long startTimeTmp = 0;
			long endTimeTmp = 0;
			//如果是单月查询
			startTimeTmp = startTime;
			endTimeTmp = endTime;
			List<String> dateListTmp = DateUtil.getDateList(startTimeTmp, endTimeTmp);
			DateListAndMonth dm = new DateListAndMonth();
			dm.setDateList(dateListTmp);
			dm.setMonth(monthList.get(0).replace("-",""));
			dmList.add(dm);
		}else{
			for(int i = 0 ; i < monthList.size(); i++){
				long startTimeTmp = 0;
				long endTimeTmp = 0;
				if(i == 0){
					//如果是首个月份
					//从开始时间到月末
					startTimeTmp = startTime;
					endTimeTmp = DateUtil.getMonthLastSecondTimestamp(startTimeTmp);
				}else if(i == monthList.size() - 1 && i > 0){
					//如果是最后一个月份
					//从月初到最后时间
					startTimeTmp = DateUtil.getMonthFirstSecondTimestamp(endTime);
					endTimeTmp = endTime;
				}else{
					//如果是中间月份
					//从月初到月末
					startTimeTmp = DateUtil.getMonthFirstSecondTimestamp(monthList.get(i));
					endTimeTmp = DateUtil.getMonthLastSecondTimestamp(monthList.get(i));
				}
				List<String> dateListTmp = DateUtil.getDateList(startTimeTmp, endTimeTmp);
				DateListAndMonth dm = new DateListAndMonth();
				dm.setDateList(dateListTmp);
				dm.setMonth(monthList.get(i).replace("-",""));
				dmList.add(dm);
			}
		}
		request.setDmList(dmList);

		//3.统计数据
		IPage<ErrLocationMoveResponse> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());

		//补充字段
		CountDownLatch countDownLatch = new CountDownLatch(1);

		//查询部门信息、上级平台信息，用于后续手动增加字段
		Map<String,BladeDept> deptMap = new HashMap<>();
		Map<String,BamThirdPartyPlatform> platMap = new HashMap<>();
		threadPool.submit(() -> {
			long start1 = System.currentTimeMillis();
			try{
				List<BladeDept> depts = null;
				List<BamThirdPartyPlatform> platList = null;
				platList = thirdPartyPlatformService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled, 0));
				depts = bladeDeptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
				platList.forEach(item -> {
					platMap.put(item.getId()+"", item);
				});
				depts.forEach(item -> {
					deptMap.put(item.getId()+"", item);
				});
			}catch (Exception e){
				log.error("异步处理失败",e);
			}finally {
				countDownLatch.countDown();
			}
			long end1 = System.currentTimeMillis();
			log.info("-=-=-=time1 is "+ (end1 - start1));
		});


		//查询数据
		long start2 = System.currentTimeMillis();
		IPage<ErrLocationMoveResponse> pageRes = baseMapper.getErrLocationMoveInfo(request, page);
		long end2 = System.currentTimeMillis();
		log.info("-=-=-=time2 is "+(end2 - start2));

		countDownLatch.await();
		long start3 = System.currentTimeMillis();
		//添加字段信息
		pageRes.getRecords().forEach(item -> {
			Long deptId = item.getDeptId();
			Long vehicleOwnerId = item.getVehicleOwnerId();
			BladeDept dept = deptMap.get(deptId+"");
			BamThirdPartyPlatform plat = platMap.get(vehicleOwnerId+"");
			if(dept != null){
				item.setDeptName(dept.getDeptName());
			}
			if(plat != null){
				item.setVehicleOwner(StringUtils.isEmpty(plat.getName())?"非营运车辆":plat.getName());
			}else{
				item.setVehicleOwner("非营运车辆");
			}
		});

		if(pageRes != null && pageRes.getRecords() != null && pageRes.getRecords().size() > 1){
			pageRes.getRecords().forEach(item -> {
				item.setDistance(MathUtil.roundDouble(item.getDistance(),2));
			});
		}
		long end3 = System.currentTimeMillis();
		log.info("-=-=-=time3 is "+(end3 - start3));
		return pageRes;
	}

	@Override
	public List<Map<String, Object>> findVehicleMileageDay(CommonBaseCrossMonthWithLineRequest request, Query query) throws Exception {
		//1.查询日里程数据
		/*IPage<Map<String,Object>> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());*/

		//对每个月的数据进行计算
		List<Map<String,Object>> totalList = new ArrayList<>();
		List<DateListAndMonthWithLine> dmList = request.getDmList();
		//记录要查询的所有日期
		List<String> dateListAll = new ArrayList<>();
		dmList.forEach(item -> {
			String month = item.getMonth();
			String monthLine = item.getMonthLine();
			List<String> dateList = item.getDateList();
			dateList.forEach(date -> {
				dateListAll.add(monthLine+"-"+date);
			});
			List<LinkedHashMap<String,Object>> list = baseMapper.getMileageDay(request, month, monthLine,dateList);
			totalList.addAll(list);
		});

		//2.按照车辆整理数据
		List<Map<String,Object>> resList = new ArrayList<>();
		Map<String,List<Map<String,Object>>> resMap = new HashMap<>();
		totalList.forEach(item -> {
			String licencePlate = item.get("licencePlate").toString();
			String licenceColor = item.get("licenceColor").toString();
			String key = licencePlate + "~" + licenceColor;
			List<Map<String,Object>> listTmp = resMap.get(key);
			if(listTmp == null){
				listTmp = new ArrayList<>();
			}

			//如果有些日期没有数据，则填充为0
//			dateListAll.forEach(date -> {
//				if(!item.containsKey(date)){
//					item.put(date,0);
//				}
//			});
			listTmp.add(item);
			resMap.put(key, listTmp);
		});
		resMap.forEach((k,v) -> {
			Map<String,Object> mapTmp = new LinkedHashMap<>();
			v.forEach(item ->{
				mapTmp.putAll(item);
			});
			resList.add(mapTmp);
		});

		List<Map<String,Object>> mapListTmp = resList;
		//3.对于未查询到数据的车辆，补充数据
		List<Long> vIdList = request.getVehicleIdList();
		List<BdmVehicle> vList = vehicleService.listByIds(vIdList);
		Map<String,BdmVehicle> vMap = new HashMap<>();
		vList.forEach(item -> {
			String licencePlate = item.getLicencePlate();
			String licenceColor = item.getLicenceColor();
			String key = licencePlate + "~" + licenceColor;
			vMap.put(key,item);
		});
		//查询车辆基本信息
		List<VehicleCommonInfo> commonList = vehicleService.findVehicleCommonInfo(vIdList);
		Map<String,VehicleCommonInfo> commonMap = new HashMap<>();
		commonList.forEach(item -> {
			String licencePlate = item.getLicencePlate();
			String licenceColor = item.getLicenceColor();
			String key = licencePlate + "~" + licenceColor;
			commonMap.put(key, item);
		});
		vMap.keySet().removeAll(resMap.keySet());
		vMap.forEach((k,v) -> {
			Map<String,Object> tmpMap = new HashMap<>();
			String licencePlate = k.split("~")[0];
			String licenceColor = k.split("~")[1];
			//设置基础数据
			tmpMap.put("licencePlate", licencePlate);
			tmpMap.put("licenceColor", licenceColor);
			tmpMap.put("deptName", commonMap.get(k).getDeptName());
			tmpMap.put("vehicleOwner",commonMap.get(k).getVehicleOwner());
			tmpMap.put("accessMode", commonMap.get(k).getAccessMode());
			tmpMap.put("vehicleUseType", commonMap.get(k).getVehicleUseType());
			//设置里程数据
			dateListAll.forEach(date -> {
				tmpMap.put(date, 0);
			});
			tmpMap.put("onlineDaysCount",0);
			mapListTmp.add(tmpMap);
		});


		//4.对字段进行处理

		if(mapListTmp == null || mapListTmp.size() < 1){
			return null;
		}
		for(Map<String,Object> map : mapListTmp){
			map.forEach((k,v) -> {
				//格式化数据
				if(k.indexOf("-") > 0){
					//如果是里程字段(km)
					map.put(k, MathUtil.roundDouble(Double.parseDouble(v==null?"0":v.toString())/1000,2));
				}
			});
		}
		return resList;
	}

	@Override
	public IPage<CompanyAllStatInfoResponse> findCompanyAllInfo(CompanyAndDate cd, Query query, BladeUser user) throws Exception {
		//1.查询企业
		List<Long> deptList = null;
		long deptCount = 0;
		if(cd.getDeptIds() != null && cd.getDeptIds().size() > 0){
			//如果指定企业
			deptList = cd.getDeptIds();
			deptCount = deptList==null?0:deptList.size();
		}else{
			//部门id
			List<Long> deptAllList = busiUtil.getDeptList(user);
			deptCount = deptAllList==null?0:deptAllList.size();
			int start = (query.getCurrent()-1) * query.getSize();
			int end = start + query.getSize();
			if(end > deptAllList.size()){
				end = deptAllList.size();
			}
			deptList = deptAllList.subList(start, end);
		}

		//默认使用前一天的日期
		//格式： yyyy-MM-dd
		String date = DateUtil.getDateBeforeDayStr(new Date(), -1);
		if(!StringUtils.isEmpty(cd.getDate())){
			date = cd.getDate();
		}

		//设置统计日期
		/*List<String> dateList = new ArrayList<>();
		if(cd.getStartTime() == null && cd.getEndTime() != null){
			//如果开始时间为空，结束时间不为空，则设置为结束时间的日期
			String date = DateUtil.getDateString(cd.getEndTime());
			dateList.add(date);
		}else if(cd.getStartTime() != null && cd.getEndTime() == null){
			//如果开始时间不为空，结束时间为空，则设置为开始时间的日期
			String date = DateUtil.getDateString(cd.getStartTime());
			dateList.add(date);
		}else if(cd.getStartTime() == null && cd.getEndTime() == null){
			//如果开始时间和结束时间都是空，则设置为前一天的日期
			String date = DateUtil.getDateBeforeDayStr(new Date(), 1);
			dateList.add(date);
		}else{
			//如果开始时间和结束时间都不为空
			List<String> dates = DateUtil.getDateList(cd.getStartTime(), cd.getEndTime());
			dateList = dates;
		}*/


		//对每个企业执行操作
		List<CompanyAllStatInfoResponse> resList = new ArrayList<>();
		CountDownLatch countDownLatch = new CountDownLatch(deptList.size());
		for(Long deptId : deptList){
			String finalDate = date;
			threadPool.submit(() -> {
				try{
					//查询deptName
					BladeDept bd = deptService.getById(deptId);
					if(bd == null){
						//如果企业不存在，则不执行后续操作
						return;
					}
					CompanyAllStatInfoResponse ca = new CompanyAllStatInfoResponse();
					ca.setDeptName(bd.getDeptName());
					List<Long> deptIds = new ArrayList<>();
					deptIds.add(deptId);
					//2.查询企业车辆总数
					long vehicleCount = 0;
					vehicleCount = vehicleService.findAllVehicleCountByDeptId(deptIds);
					ca.setVehicleCount(vehicleCount);

					//3.查询企业上线车辆数
					long goOnlineCount = 0;
					goOnlineCount = vehicleService.findGoOnlineCountByDate(deptIds,null, finalDate);
					ca.setVehicleGoOnlineCount(goOnlineCount);

					//4.查询企业离线车辆数
					//营运车辆数
					long runningCount = 0;
					runningCount = vehicleService.findRunningCountByDeptId(deptIds);
					//离线车辆数
					long offlineCount = runningCount - goOnlineCount;
					ca.setVehicleOfflineCount(offlineCount);

					//5.上线率
					double goOnlineRate = runningCount==0?0:MathUtil.divideRoundDouble(goOnlineCount, runningCount,4);
					String goOnlineRateStr = MathUtil.formatToPercent(goOnlineRate, 2);
					ca.setGoOnlineRate(goOnlineRate);
					ca.setGoOnlineRateStr(goOnlineRateStr);

					//6.不定位车辆数
					long notLocationCount = notLocationService.findNotLocationCount(deptIds, finalDate);
					ca.setNotLocationCount(notLocationCount);

					//7.定位率
					double locationRate = goOnlineRate == 0?0:MathUtil.divideRoundDouble((goOnlineCount - notLocationCount), goOnlineCount, 4);
					String locationRateStr = MathUtil.formatToPercent(locationRate, 2);
					ca.setLocationRate(locationRate);
					ca.setLocationRateStr(locationRateStr);

					//8.里程（总里程）
					String mDate = finalDate.substring(8,10);
					String mMonth = finalDate.substring(0,7).replace("-","");
					//单位：米
					double totalMileage = vehicleService.findTotalMileageByDeptIds(deptIds, mDate, mMonth);
					ca.setTotalMileage(MathUtil.roundDouble(totalMileage/1000, 2));

					//9.定位里程
					//查询不定位里程
					double notLocationMileage = notLocationService.findNotLocationMileage(deptIds, finalDate);
					//定位里程：总里程 - 不定位里程
					//单位：米
					double locationMileage = totalMileage - notLocationMileage;
					ca.setLocationMileage(MathUtil.roundDouble(locationMileage/1000, 2));

					//10.总报警次数
					long alarmTotalCount = alarmService.findAlarmCountByDeptIds(deptIds, finalDate);
					ca.setTotalAlarmCount(alarmTotalCount);

					//11.超速报警次数
					//获取超速报警类型
					Set<DictTreeNodeVO> set = alarmService.getChildrenFlatAlarmTypes(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED+"");
					List<Integer> osList = new ArrayList<>();
					for(DictTreeNodeVO vo : set){
						if(vo != null && vo.getDictKey() != null){
							osList.add(Integer.parseInt(vo.getDictKey()));
						}
					}
					//添加超速父类型
					osList.add((int) AlarmConstant.DICT_ALARM_TYPE_OVER_SPEED);
					//超速报警次数
					long overSpeedAlarmCount = alarmService.findAlarmCountByDeptIdsAndAlarmType(deptIds, osList, finalDate);
					ca.setOverSpeedAlarmCount(overSpeedAlarmCount);

					//12.疲劳驾驶报警次数
					//获取疲劳报警类型
					List<Integer> tiredlist = new ArrayList<>();
					//疲劳驾驶报警（终端）
					tiredlist.add((int)AlarmConstant.DICT_ALARM_TYPE_TIRED_TERMINAL);
					//疲劳驾驶报警（平台）
					tiredlist.add((int)AlarmConstant.DICT_ALARM_TYPE_TIRED_PLATFORM);
					long tiredAlarmCount = alarmService.findAlarmCountByDeptIdsAndAlarmType(deptIds, tiredlist, finalDate);
					ca.setTiredAlarmCount(tiredAlarmCount);

					//13.夜间禁行报警次数
					//获取夜间行驶报警类型
					List<Integer> nightList = new ArrayList<>();
					//夜间行驶报警
					nightList.add((int)AlarmConstant.DICT_ALARM_TYPE_NIGHT);
					long nightAlarmCount = alarmService.findAlarmCountByDeptIdsAndAlarmType(deptIds, nightList, finalDate);
					ca.setNightRunningAlarmCount(nightAlarmCount);

					//14.ADAS报警次数
					List<Integer> listADAS = new ArrayList<>();
					Set<DictTreeNodeVO> setADAS = alarmService.getChildrenFlatAlarmTypesByTenantId(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_ADAS+"", user.getTenantId());
					for(DictTreeNodeVO vo : setADAS){
						if(vo != null && vo.getDictKey() != null){
							listADAS.add(Integer.parseInt(vo.getDictKey()));
						}
					}
					listADAS.add((int)AlarmConstant.DICT_ALARM_TYPE_ADAS);
					long adasAlarmCount = alarmService.findAlarmCountByDeptIdsAndAlarmType(deptIds, listADAS, finalDate);
					ca.setAdasAlarmCount(adasAlarmCount);

					//15.DSM报警次数
					List<Integer> listDSM = new ArrayList<>();
					Set<DictTreeNodeVO> setDSM = alarmService.getChildrenFlatAlarmTypesByTenantId(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_DSM+"", user.getTenantId());
					for(DictTreeNodeVO vo : setDSM){
						if(vo != null && vo.getDictKey() != null){
							listDSM.add(Integer.parseInt(vo.getDictKey()));
						}
					}
					listDSM.add((int)AlarmConstant.DICT_ALARM_TYPE_DSM);
					long dsmAlarmCount = alarmService.findAlarmCountByDeptIdsAndAlarmType(deptIds, listDSM, finalDate);
					ca.setDsmAlarmCount(dsmAlarmCount);
					resList.add(ca);
				}catch (Exception e){
					log.error("查询报错",e);
				}finally {
					countDownLatch.countDown();
				}
			});
		}

		countDownLatch.await();
		//拼装结果
		IPage<CompanyAllStatInfoResponse> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		page.setRecords(resList);
		page.setTotal(deptCount);

		return page;
	}

	@Override
	public IPage<CompanyAllRateResponse> findCompanyAllRateInfo(CommonBaseCrossMonthRequest request, Query query, BladeUser user) throws Exception {

		//1.初始化请求参数
		//部门id
		List<Long> deptList = busiUtil.getDeptList(user);
		request.setDeptList(deptList);
		//行业类型
		List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
		if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
			vehicleUseTypes.add(request.getVehicleUseType());
		}
		request.setProfessionList(vehicleUseTypes);
		request.setUserId(user.getUserId());
		if(request.getStartTime() == null){
			long startTimeTmp = DateUtil.getSecondTimeStampBeforeDay(new Date(), 7);
			request.setStartTime(startTimeTmp);
		}
		if(request.getEndTime() == null){
			long endTimeTmp = DateUtil.getSecondTimeStampBeforeDay(new Date(),1);
			request.setEndTime(endTimeTmp);
		}

		//2.查看是否指定了车辆
		List<BdmVehicle> vehicleList = new ArrayList<>();
		//根据条件查询车辆
		IPage<BdmVehicle> vList = vehicleService.findVehiclesByCondition(request, query);
		if(vList == null || vList.getRecords() == null || vList.getRecords().size() < 1){
			//如果没有查询到车辆，则不在进行后边的逻辑
			IPage<CompanyAllRateResponse> p = new Page<>();
			p.setRecords(null);
			p.setTotal(0);
			p.setCurrent(query.getCurrent());
			p.setSize(query.getSize());
			return p;
		}
		vehicleList = vList.getRecords();
		List<Long> vehicleIds = new ArrayList<>();
		vehicleList.forEach(item -> {
			vehicleIds.add(item.getId().longValue());
		});

		List<Long> originVehicleList = request.getVehicleIdList();
		request.setVehicleIdList(vehicleIds);

		//3.统计指标
		//轨迹完整率相关：连续里程、总里程、轨迹完整率
		CommonBaseCrossMonthRequest requestComplete = new CommonBaseCrossMonthRequest();
		BeanUtils.copyProperties(request, requestComplete);
		List<DateListAndMonth> dmListComplete = DateUtil.getDateListAndMonth(request.getStartTime(), request.getEndTime());
		for(DateListAndMonth dm : dmListComplete){
			List<String> dateListTmp = dm.getDateList();
			List<String> dateList = new ArrayList<>();
			dateListTmp.forEach(date -> {
				dateList.add(date.substring(8,10));
			});
			dm.setDateList(dateList);
			dm.setMonth(dm.getMonth().replace("-",""));
		}
		requestComplete.setDmList(dmListComplete);
		List<CompleteMileageNode> completeList = completeService.findCompleteMileage(requestComplete);
		Map<String,CompleteMileageNode> completeMap = new HashMap<>();
		completeList.forEach(item -> {
			completeMap.put(item.getLicencePlate() + "~" + item.getLicenceColor(), item);
		});

		//数据质量相关：错误定位信息数、定位信息总数、错误定位信息数占比、数据合格率
		CommonBaseCrossMonthRequest requestQuality = new CommonBaseCrossMonthRequest();
		BeanUtils.copyProperties(request, requestQuality);
		List<DateListAndMonth> dmListQuality = DateUtil.getDateListAndMonth(request.getStartTime(), request.getEndTime());
		for(DateListAndMonth dm : dmListQuality){
			dm.setMonth(dm.getMonth().replace("-",""));
		}
		requestQuality.setDmList(dmListQuality);
		List<VehicleAndQuality> qualityList = locationQualityService.findQualityByCondition(requestQuality);
		Map<String, VehicleAndQuality> qualityMap = new HashMap<>();
		qualityList.forEach(item -> {
			qualityMap.put(item.getLicencePlate()+"~"+item.getLicenceColor(),item);
		});

		//定位漂移次数
		CommonBaseCrossMonthRequest requestDrift = new CommonBaseCrossMonthRequest();
		BeanUtils.copyProperties(request, requestDrift);
		requestDrift.setDmList(dmListComplete);
		List<VehicleAndCount> driftList = driftService.findDriftCount(requestDrift);
		Map<String,VehicleAndCount> driftMap = new HashMap<>();
		driftList.forEach(item -> {
			driftMap.put(item.getLicencePlate()+"~"+item.getLicenceColor(),item);
		});

		//4.查询车辆基本信息
		List<VehicleCommonInfo> vehicleCommonList = vehicleService.findVehicleCommonInfo(request.getVehicleIdList());
		Map<String,VehicleCommonInfo> vehicleMap = new HashMap<>();
		vehicleCommonList.forEach(item -> {
			vehicleMap.put(item.getLicencePlate()+"~"+item.getLicenceColor(), item);
		});

		//5.整理数据
		List<CompanyAllRateResponse> list = new ArrayList<>();
		for(BdmVehicle v : vehicleList){
			CompanyAllRateResponse car = new CompanyAllRateResponse();
			String key = v.getLicencePlate()+"~"+v.getLicenceColor();
			//基本信息
			VehicleCommonInfo vc = vehicleMap.get(key);
			car.setAccessMode(vc.getAccessMode());
			car.setDeptName(vc.getDeptName());
			car.setLicencePlate(vc.getLicencePlate());
			car.setLicenceColor(vc.getLicenceColor());
			car.setPlatformName(vc.getPlateformName());
			car.setVehicleOwner(vc.getVehicleOwner());
			car.setVehicleUseType(vc.getVehicleUseType());
			car.setStartDate(DateUtil.getDateString(request.getStartTime()));
			car.setEndDate(DateUtil.getDateString(request.getEndTime()));

			//轨迹完整率
			CompleteMileageNode cm = completeMap.get(key);
			if(cm != null){
				car.setCompleteMileage(MathUtil.roundDouble(cm.getCompleteMileage()/1000,2));
				car.setTotalMileage(MathUtil.roundDouble(cm.getTotalMileage()/1000,2));
				car.setCompleteRate(cm.getCompleteRate());
				car.setCompleteRateStr(MathUtil.formatToPercent(car.getCompleteRate(),2));
			}else{
				car.setCompleteMileage(0);
				car.setTotalMileage(0);
				car.setCompleteRate(1);
				car.setCompleteRateStr(MathUtil.formatToPercent(1,2));
			}


			//数据质量
			VehicleAndQuality vq = qualityMap.get(key);
			if(vq != null){
				car.setLocationErrCount(vq.getTotalErrorCount());
				car.setLocationTotalCount(vq.getTotalCount());
				car.setQualityRate((vq.getTotalCount() <= 0) ? 0 : (vq.getTotalCount() - vq.getTotalErrorCount()) / (vq.getTotalCount() * 1.0));
				car.setQualityRateStr(MathUtil.formatToPercent(car.getQualityRate(),2));
				car.setErrLocationRate(1 - car.getQualityRate());
				car.setErrLocationRateStr(MathUtil.formatToPercent(car.getErrLocationRate(),2));
			}else{
				car.setLocationErrCount(0);
				car.setLocationTotalCount(0);
				car.setQualityRate(1);
				car.setQualityRateStr(MathUtil.formatToPercent(1,2));
				car.setErrLocationRate(0);
				car.setErrLocationRateStr(MathUtil.formatToPercent(0,2));
			}

			//漂移
			VehicleAndCount va = driftMap.get(key);
			if(va == null){
				car.setDriftCount(0);
			}else{
				car.setDriftCount(va.getCount());
			}


			list.add(car);
		}
		//查询车辆总数
		CommonBaseCrossMonthRequest req = new CommonBaseCrossMonthRequest();
		BeanUtils.copyProperties(request, req);
		req.setVehicleIdList(originVehicleList);
		long count = vehicleService.findVehiclesByConditionCount(req);
		IPage<CompanyAllRateResponse> page = new Page<>();
		page.setSize(query.getSize());
		page.setCurrent(query.getCurrent());
		page.setTotal(count);
		page.setRecords(list);
		return page;
	}

	@Override
	public List<StatVehRunningStateDay> findStateInfoByDeptWithAlarm(String month, String date, List<Long> deptIds, List<Integer> vehicleIds) throws Exception {
		return baseMapper.getStateInfoByDeptWithAlarm(month, date, deptIds, vehicleIds);
	}

	@Override
	public List<StatVehRunningStateDay> findStateInfoByDeptWithAlarmMonth(String month, List<Long> deptIds, List<Integer> vehicleIds) throws Exception {
		return baseMapper.getStateInfoByDeptWithAlarmMonth(month, deptIds,vehicleIds);
	}
}
