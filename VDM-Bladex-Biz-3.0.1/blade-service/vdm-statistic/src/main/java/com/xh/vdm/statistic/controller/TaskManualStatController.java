package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.BdmRoute;
import com.xh.vdm.statistic.entity.Point;
import com.xh.vdm.statistic.mapper.BdmRouteMapper;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.service.impl.BdmRouteServiceImpl;
import com.xh.vdm.statistic.task.StatisticsTask;
import com.xh.vdm.statistic.utils.*;
import com.xh.vdm.statistic.vo.ObjectRestResponse;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 跑批任务手动执行接口
 * @Author: zhouxw
 * @Date: 2022/8/30 2:44 PM
 */
@RestController
@Slf4j
@RequestMapping("/bt/statistics/task")
public class TaskManualStatController {

    @Resource
    private ILocationQualityService locationQualityService;

    @Resource
    private IStatCompleteService statCompleteService;

    @Resource
    private IStatDriftService statDriftService;

    @Autowired
	IBdmRouteService bdmRouteService;

    @Autowired
    BdmRouteMapper bdmRouteMapper;

	@Resource
	ILocationService locationService;

    @Autowired
    MapDistanceUtil mapDistanceUtil;

    @Resource
    private LogUtil logUtil;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private StatisticsTask statisticsTask;

    @Resource
    private ICacheSegLimitSpeedMapService cacheSegLimitSpeedMapService;

    @Resource
    private ICacheSegLimitSpeedTerminalService cacheSegLimitSpeedTerminalService;

    @Resource
    private ICacheVehicleOnlineOrOfflineService cacheVehicleOnlineOrOfflineService;

    @Resource
    private ICacheSecurityInfoService cacheSecurityInfoService;

    @Resource
    private ICacheVehicleOnlineRateService vehicleOnlineRateService;

    @Resource
    private ICacheStatDriftRateService cacheStatDriftRateService;

    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    private static final SimpleDateFormat sdfMonth = new SimpleDateFormat("yyyyMM");


    /**
     * @description: 数据质量统计接口
     * 用于生成数据质量数据
     * 支持多次重复执行
     * 支持单天执行和时间段执行，单天格式：yyyyMMdd ； 时间段格式：yyyyMMdd~yyyyMMdd，包含首尾
     * @author: zhouxw
     * @date: 2022/9/5 1:59 PM
     * @param: [day]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @GetMapping("/statLocationQuality")
    public R statLocationQuality(String dayDuration){
        //日期格式校验
        if(StringUtils.isBlank(dayDuration) || !(dayDuration.length() == 8 || dayDuration.length() == 17) ||(dayDuration.length() == 17 && dayDuration.indexOf("~") < 0) ){
            return R.fail("日期格式不正确，请改正后再试");
        }
        R res = new R();
        //执行数据统计
        if(dayDuration.indexOf("~") > 0 ){
            //如果是时间段统计
            //依次执行每天的统计
            String startDay = dayDuration.split("~")[0];
            String endDay = dayDuration.split("~")[1];
            Calendar cal = Calendar.getInstance();
            try {
                cal.setTime(sdf.parse(startDay));
            } catch (ParseException e) {
                return R.fail("日期格式错误：" + dayDuration);
            }
            //存放执行失败的日期
            StringBuffer sb = new StringBuffer();
            while(endDay.compareTo(sdf.format(cal.getTime())) >= 0){
                res = statLocationQualityForOneDay(sdf.format(cal.getTime()));
                if(res.getCode() != 0){
                    sb.append(sdf.format(cal.getTime())).append(",");
                }
                cal.add(Calendar.DAY_OF_YEAR , 1);
            }
            if(sb.length() < 1){
                //如果全部执行成功
                return R.success("手动执行[数据质量统计]成功："+dayDuration);
            }else{
                //如果部分失败
                return R.success("手动执行[数据质量统计]部分失败：[执行周期"+dayDuration+"]，失败日期为 "+sb.toString());
            }

        }else{
            //如果是单天统计
            res = statLocationQualityForOneDay(dayDuration);
            return res;
        }

    }




    /**
     * @description: 执行一天的数据质量统计
     * @author: zhouxw
     * @date: 2022/9/21 9:52 AM
     * @param: [day]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    private R statLocationQualityForOneDay(String day){
        try{
            //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
            Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_QUALITY);
            if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
                //如果已经有跑批程序在执行，则不再执行
                log.info("[数据质量统计]跑批程序正在执行，本次暂不执行，请等待当前程序执行完毕后再试！");
                return R.fail("[数据质量统计]跑批程序正在执行，本次暂不执行，请等待当前程序执行完毕后再试！");
            }

            //获取执行权限之后，添加执行标记（有效期为1个小时）
            synchronized (this){
                stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_QUALITY , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
            }

            long start = System.currentTimeMillis();
            long end = 0 ;
            try {
                locationQualityService.locationQualityStat(day);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("[数据质量统计]执行数据质量统计失败，出现异常",e);
                logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_QUALITY,"数据质量统计：手动执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行出现异常："+e.getMessage());
                return R.fail("[数据质量统计]执行数据质量统计失败：" + e.getMessage());
            }finally {
                //执行完毕后，解除执行锁定
                synchronized (this){
                    stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_QUALITY );
                }
            }
            end = System.currentTimeMillis();
            log.info("[数据质量统计]执行数据质量统计成功，共耗时{}毫秒", end - start);
            logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_QUALITY,"数据质量统计：手动执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

            return R.success("[数据质量统计]执行数据质量统计成功");
        }catch (Exception e){
            e.printStackTrace();
            log.error("[数据质量统计]执行数据质量统计出现异常：",e);
            return R.fail("[数据质量统计]执行数据质量统计出现异常：" + e.getMessage());
        }
    }


    /**
     * @description: 轨迹完整率数据统计接口
     * 用于手动调用，执行轨迹完整率统计操作
     * 支持多次重复执行
     * @author: zhouxw
     * @date: 2022/9/5 1:59 PM
     * @param: [day]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @GetMapping("/statLocationComplete")
    public R statLocationComplete(String dayDuration){

        //日期格式校验
        if(StringUtils.isBlank(dayDuration) || !(dayDuration.length() == 8 || dayDuration.length() == 17) ||(dayDuration.length() == 17 && dayDuration.indexOf("~") < 0) ){
            return R.fail("日期格式不正确，请改正后再试");
        }
        R res = new R();
        //执行数据统计
        if(dayDuration.indexOf("~") > 0 ){
            //如果是时间段统计
            //依次执行每天的统计
            String startDay = dayDuration.split("~")[0];
            String endDay = dayDuration.split("~")[1];
            Calendar cal = Calendar.getInstance();
            try {
                cal.setTime(sdf.parse(startDay));
            } catch (ParseException e) {
                return R.fail("日期格式错误：" + dayDuration);
            }
            //存放执行失败的日期
            StringBuffer sb = new StringBuffer();
            while(endDay.compareTo(sdf.format(cal.getTime())) >= 0){
                res = statLocationCompleteForOneDay(sdf.format(cal.getTime()));
                if(res.getCode() != 0){
                    sb.append(sdf.format(cal.getTime())).append(",");
                }
                cal.add(Calendar.DAY_OF_YEAR , 1);
            }
            if(sb.length() < 1){
                //如果全部执行成功
                return R.success("手动执行[轨迹完整率统计]成功："+dayDuration);
            }else{
                //如果部分失败
                return R.success("手动执行[轨迹完整率统计]部分失败：[执行周期"+dayDuration+"]，失败日期为 "+sb.toString());
            }

        }else{
            //如果是单天统计
            res = statLocationCompleteForOneDay(dayDuration);
            return res;
        }
    }

    /**
     * @description: 执行一天的轨迹完整率统计
     * @author: zhouxw
     * @date: 2022/9/21 10:30 AM
     * @param: [day]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    private R statLocationCompleteForOneDay(String day){
        try {
            //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
            Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX + StatisticConstants.TASK_LOCATION_COMPLETE);
            if (executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())) {
                //如果已经有跑批程序在执行，则不再执行
                log.info("[轨迹完整率统计]跑批程序正在执行，本次暂不执行，请等待当前程序执行完毕后再试！");
                return R.fail("[轨迹完整率统计]跑批程序正在执行，本次暂不执行，请等待当前程序执行完毕后再试！");
            }

            //获取执行权限之后，添加执行标记（有效期为1个小时）
            synchronized (this) {
                stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX + StatisticConstants.TASK_LOCATION_COMPLETE, StatisticConstants.COMMON_TRUE, 1, TimeUnit.HOURS);
            }

            long start = System.currentTimeMillis();
            long end = 0;
            try {
                statCompleteService.locationCompleteStat(day);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("[轨迹完整率统计]执行轨迹完整率统计失败，出现异常", e);
                logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_COMPLETE, "轨迹完整率统计：手动执行跑批任务", day, new Date(start), new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL, "执行出现异常：" + e.getMessage());
                return R.fail("[轨迹完整率统计]执行轨迹完整率统计失败：" + e.getMessage());
            }finally {
                //执行完毕后，解除执行锁定
                synchronized (this) {
                    stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX + StatisticConstants.TASK_LOCATION_COMPLETE);
                }
            }
            end = System.currentTimeMillis();
            log.info("[轨迹完整率统计]执行轨迹完整率统计成功，共耗时{}毫秒", end - start);
            logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_COMPLETE, "轨迹完整率统计：手动执行跑批任务", day, new Date(start), new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS, "执行成功");


            return R.data("[轨迹完整率统计]执行轨迹完整率统计成功");
        }catch (Exception e){
            e.printStackTrace();
            log.error("[轨迹完整率统计]执行轨迹完整率统计失败：",e);
            return R.fail("[轨迹完整率统计]执行轨迹完整率统计失败："+ e.getMessage());
        }
    }




    /**
     * @description: 漂移率数据统计接口
     * 用于手动调用统计漂移率
     * 支持多次重复执行
     * @author: zhouxw
     * @date: 2022/9/5 1:59 PM
     * @param: [day]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @GetMapping("/statLocationDrift")
    public R statLocationDrift(String dayDuration){

        //日期格式校验
        if(StringUtils.isBlank(dayDuration) || !(dayDuration.length() == 8 || dayDuration.length() == 17) ||(dayDuration.length() == 17 && dayDuration.indexOf("~") < 0) ){
            return R.fail("日期格式不正确，请改正后再试");
        }
        R res = new R();
        //执行数据统计
        if(dayDuration.indexOf("~") > 0 ){
            //如果是时间段统计
            //依次执行每天的统计
            String startDay = dayDuration.split("~")[0];
            String endDay = dayDuration.split("~")[1];
            Calendar cal = Calendar.getInstance();
            try {
                cal.setTime(sdf.parse(startDay));
            } catch (ParseException e) {
                return R.fail("日期格式错误：" + dayDuration);
            }
            //存放执行失败的日期
            StringBuffer sb = new StringBuffer();
            while(endDay.compareTo(sdf.format(cal.getTime())) >= 0){
                res = statLocationDriftForOneDay(sdf.format(cal.getTime()));
                if(res.getCode() != 0){
                    sb.append(sdf.format(cal.getTime())).append(",");
                }
                cal.add(Calendar.DAY_OF_YEAR , 1);
            }
            if(sb.length() < 1){
                //如果全部执行成功
                return R.success("手动执行[漂移率统计]成功："+dayDuration);
            }else{
                //如果部分失败
                return R.success("手动执行[漂移率统计]部分失败：[执行周期"+dayDuration+"]，失败日期为 "+sb.toString());
            }

        }else{
            //如果是单天统计
            res = statLocationDriftForOneDay(dayDuration);
            return res;
        }
    }

    /**
     * @description: 单天统计漂移数据
     * @author: zhouxw
     * @date: 2022/9/21 10:47 AM
     * @param: [day]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    private R statLocationDriftForOneDay(String day){
        try{
            //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
            Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_DRIFT);
            if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
                //如果已经有跑批程序在执行，则不再执行
                log.info("[漂移率统计]跑批程序正在执行，本次暂不执行，请等待当前程序执行完毕后再试！");
                return R.fail("[漂移率统计]跑批程序正在执行，本次暂不执行，请等待当前程序执行完毕后再试！");
            }

            //获取执行权限之后，添加执行标记（有效期为1个小时）
            synchronized (this){
                stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_DRIFT , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
            }

            long start = System.currentTimeMillis();
            long end = 0;
            try {
                statDriftService.locationDriftStat(day);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("[漂移率统计]执行漂移率统计失败，出现异常",e);
                logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_DRIFT,"漂移统计：手动执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行出现异常："+e.getMessage());
                return R.fail("[漂移率统计]执行漂移率统计失败：" + e.getMessage());
            }finally{
                //执行完毕后，解除执行锁定
                synchronized (this){
                    stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_DRIFT );
                }
            }
            end = System.currentTimeMillis();
            log.info("[漂移率统计]执行漂移率统计成功，共耗时{}毫秒", end - start);
            logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_DRIFT,"漂移统计：手动执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

            return R.data("[漂移率统计]执行漂移率统计成功");
        }catch (Exception e){
            e.printStackTrace();
            log.error("[漂移率统计]执行漂移率统计出现异常：",e);
            return R.fail("[漂移率统计]执行漂移率统计出现异常："+e.getMessage());
        }
    }



    /**
     *查询车辆在线情况抽查
     * 查询车辆在线情况抽查,查询在指定时间内，车辆的上线情况，车辆在这段之间内上线过就算
     * @param request
     * @return
     */
    @GetMapping("/generateBdmRoute")
    @ApiOperation(value = "生成里程",notes = "生成里程")
    public ObjectRestResponse<String> generateBdmRoute(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "date",required = false) String date) throws Exception {
        try{

            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

            if(date.contains("~")){
                //如果是时间段
                String start = date.split("~")[0];
                String end = date.split("~")[1];
                long time = sdf.parse(start).getTime();
                while(time < sdf.parse(end).getTime()+1){
                    Date d = new Date();
                    d.setTime(time);
                    String dS = sdf.format(d);
                    log.info("将要执行测试跑批 bdm_route 日期为：" + dS);
					bdmRouteService.generateBdmRoute(dS);
                    time += 24*3600*1000;
                }
            }else{
                //如果不是时间段
                //date: yyyyMMdd
                log.info("将要执行测试跑批 bdm_route 日期为：" + date);
				bdmRouteService.generateBdmRoute(date);
            }

            return new ObjectRestResponse<>().data(null);
        }catch (Exception e){
            log.error("测试生成里程报错",e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }







    /**
     * 统计指定日期/日期段的日报，然后通过邮件的形式发送到客户邮箱
     * 注意：执行指定日期的数据的统计，而不是指定日期前一天的
     * @param dayDuration yyyyMMdd / yyyyMMdd~yyyyMMdd
     * @return
     */
    @GetMapping("/statAndSendMailForDay")
    public R statAndSendMailForDay(String dayDuration){

        long start = System.currentTimeMillis();
        //日期格式校验
        if(StringUtils.isBlank(dayDuration) || !(dayDuration.length() == 8 || dayDuration.length() == 17) ||(dayDuration.length() == 17 && dayDuration.indexOf("~") < 0) ){
            long end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_DAY,"统计日报并发送邮箱：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"日期格式不正确");
            return R.fail("日期格式不正确，请改正后再试");
        }
        //执行数据统计
        if(dayDuration.indexOf("~") > 0 ){
            //如果是时间段统计
            //依次执行每天的统计
            String startDay = dayDuration.split("~")[0];
            String endDay = dayDuration.split("~")[1];
            Calendar cal = Calendar.getInstance();
            try {
                cal.setTime(sdf.parse(startDay));
            } catch (ParseException e) {
                long end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_DAY,"统计日报并发送邮箱：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"日期格式不正确");
                return R.fail("日期格式错误：" + dayDuration);
            }
            //存放执行失败的日期
            StringBuffer sb = new StringBuffer();
            while(endDay.compareTo(sdf.format(cal.getTime())) >= 0){
                try {
                    statisticsTask.statDayAndSendMailForOneDay(cal.getTime(), StatisticConstants.EMAIL_SEND_TYPE_INTERFACE);
                }catch (Exception e){
                    log.error("统计日报并发送邮件[{}] 失败",e, sdf.format(cal.getTime()));
                    sb.append(sdf.format(cal.getTime())).append(",");
                }
                cal.add(Calendar.DAY_OF_YEAR , 1);
            }
            if(sb.length() < 1){
                long end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_DAY,"统计日报并发送邮箱：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
                //如果全部执行成功
                return R.success("手动执行[统计日报并邮件发送]成功："+dayDuration);
            }else{
                long end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_DAY,"统计日报并发送邮箱：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"部分失败：[执行周期"+dayDuration+"]，失败日期为 "+sb);
                //如果部分失败
                return R.success("手动执行[统计日报并邮件发送]部分失败：[执行周期"+dayDuration+"]，失败日期为 "+sb);
            }
        }else{
            //如果是单天统计
            try {
                statisticsTask.statDayAndSendMailForOneDay(sdf.parse(dayDuration), StatisticConstants.EMAIL_SEND_TYPE_INTERFACE);
            }catch (Exception e){
                log.error("统计日报并发送邮件失败",e);
                long end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_DAY,"统计日报并发送邮箱：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败");
                return R.fail("手动执行[统计日报并邮件发送]失败：" + e.getMessage());
            }
            long end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_DAY,"统计日报并发送邮箱：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
            return R.success("手动执行[统计日报并邮件发送]成功" + dayDuration);
        }
    }



    /**
     * 统计指定月份的月报，然后通过邮件的形式发送到客户邮箱
     * 注意：执行指定月份的数据的统计，而不是指定月份的前一个月
     * @param month yyyyMM
     * @return
     */
    @GetMapping("/statAndSendMailForMonth")
    public R statAndSendMailForMonth(String month){
        long start = System.currentTimeMillis();
        //日期格式校验
        if(StringUtils.isBlank(month) || !(month.length() == 6 )  ){
            long end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_DAY,"统计月报并发送邮箱：手动执行跑批任务",month ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败：日期格式不正确");
            return R.fail("日期格式不正确，请改正后再试");
        }
        //执行数据统计
        try {
            statisticsTask.statDayAndSendMailForOneMonth(sdfMonth.parse(month), StatisticConstants.EMAIL_SEND_TYPE_INTERFACE);
        }catch (Exception e){
            log.error("统计月报并发送邮件[{}] 失败",month,e);
            long end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_DAY,"统计月报并发送邮箱：手动执行跑批任务",month ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,e.getMessage());
            return R.success("手动执行[统计月报并邮件发送]失败：" + e.getMessage());
        }
        long end = System.currentTimeMillis();
        logUtil.insertStatLog(StatisticConstants.TASK_STAT_SEND_EMAIL_DAY,"统计月报并发送邮箱：手动执行跑批任务",month ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行成功");
        return R.success("手动执行[统计月报并邮件发送]成功" + month);
    }

    /**
     * @description: 手动执行 车辆地图超速DB缓存 统计
     * @author: zhouxw
     * @date: 2023-02-41 10:44:40
     * @param: [dayDuration yyyyMMdd / yyyyMMdd~yyyyMMdd]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @GetMapping("/statisticSegLimitSpeedMapCache")
    public R statisticSegLimitSpeedMapCache(String dayDuration){
        long start = System.currentTimeMillis();
        long end = 0;
        //日期格式校验
        if(StringUtils.isBlank(dayDuration) || !(dayDuration.length() == 8 || dayDuration.length() == 17) ||(dayDuration.length() == 17 && dayDuration.indexOf("~") < 0) ){
            return R.fail("日期格式不正确，请改正后再试");
        }
        //执行数据统计
        if(dayDuration.indexOf("~") > 0 ){
            //如果是时间段统计
            //依次执行每天的统计
            String startDay = dayDuration.split("~")[0];
            String endDay = dayDuration.split("~")[1];
            Calendar cal = Calendar.getInstance();
            try {
                cal.setTime(sdf.parse(startDay));
            } catch (ParseException e) {
                return R.fail("日期格式错误：" + dayDuration);
            }
            //存放执行失败的日期
            StringBuffer sb = new StringBuffer();
            while(endDay.compareTo(sdf.format(cal.getTime())) >= 0){
                try{
                    //获取开始时间和结束时间
                    long tmpTimestamp = cal.getTimeInMillis()/1000;
                    long startSecondTimeStamp = DateUtil.getDayFirstSecondTimestamp(tmpTimestamp);
                    long endSecondTimestamp = DateUtil.getDayLastSecondTimestamp(tmpTimestamp);
                    cacheSegLimitSpeedMapService.statisticsSegLimitSpeedMap(startSecondTimeStamp * 1000, endSecondTimestamp * 1000);
                }catch (Exception e){
                    sb.append(sdf.format(cal.getTime())).append(",");
                }
                cal.add(Calendar.DAY_OF_YEAR , 1);
            }
            if(sb.length() < 1){
                //如果全部执行成功
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_MAP,"统计车辆地图超速DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
                return R.success("手动执行[车辆地图超速]成功："+dayDuration);
            }else{
                //如果部分失败
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_MAP,"统计车辆地图超速DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"部分失败，失败日期为："+sb.toString());
                return R.fail("手动执行[车辆地图超速]部分失败：[执行周期"+dayDuration+"]，失败日期为 "+sb.toString());
            }
        }else{
            //如果是单天统计
            try{
                long tmpTimestamp = sdf.parse(dayDuration).getTime()/1000;
                long startSecondTimeStamp = DateUtil.getDayFirstSecondTimestamp(tmpTimestamp);
                long endSecondTimestamp = DateUtil.getDayLastSecondTimestamp(tmpTimestamp);
                cacheSegLimitSpeedMapService.statisticsSegLimitSpeedMap(startSecondTimeStamp * 1000, endSecondTimestamp * 1000);
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_MAP,"统计车辆地图超速DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
            }catch (Exception e){
                log.error("手动执行[车辆地图超速]失败：[执行周期"+dayDuration+"] ", e);
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_MAP,"统计车辆地图超速DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,e.getMessage());
                return R.fail("手动执行[车辆地图超速]失败：[执行周期"+dayDuration+"] ");
            }
            return R.success("手动执行[车辆地图超速]成功："+dayDuration);
        }
    }




    /**
     * @description: 手动执行 终端限速 DB缓存 统计
     * @author: zhouxw
     * @date: 2023-02-41 10:44:40
     * @param: [dayDuration yyyyMMdd / yyyyMMdd~yyyyMMdd]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @GetMapping("/statisticSegLimitSpeedTerminalCache")
    public R statisticSegLimitSpeedTerminalCache(String dayDuration){
        long start = System.currentTimeMillis();
        long end = 0;
        //日期格式校验
        if(StringUtils.isBlank(dayDuration) || !(dayDuration.length() == 8 || dayDuration.length() == 17) ||(dayDuration.length() == 17 && dayDuration.indexOf("~") < 0) ){
            return R.fail("日期格式不正确，请改正后再试");
        }
        //执行数据统计
        if(dayDuration.indexOf("~") > 0 ){
            //如果是时间段统计
            //依次执行每天的统计
            String startDay = dayDuration.split("~")[0];
            String endDay = dayDuration.split("~")[1];
            Calendar cal = Calendar.getInstance();
            try {
                cal.setTime(sdf.parse(startDay));
            } catch (ParseException e) {
                return R.fail("日期格式错误：" + dayDuration);
            }
            //存放执行失败的日期
            StringBuffer sb = new StringBuffer();
            while(endDay.compareTo(sdf.format(cal.getTime())) >= 0){
                try{
                    //获取开始时间和结束时间
                    long tmpTimestamp = cal.getTimeInMillis()/1000;
                    long startSecondTimeStamp = DateUtil.getDayFirstSecondTimestamp(tmpTimestamp);
                    long endSecondTimestamp = DateUtil.getDayLastSecondTimestamp(tmpTimestamp);
                    cacheSegLimitSpeedTerminalService.statisticsSegLimitSpeedTerminal(startSecondTimeStamp * 1000, endSecondTimestamp * 1000);
                }catch (Exception e){
                    sb.append(sdf.format(cal.getTime())).append(",");
                }
                cal.add(Calendar.DAY_OF_YEAR , 1);
            }
            if(sb.length() < 1){
                //如果全部执行成功
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_TERMINAL,"统计终端限速DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
                return R.success("手动执行[终端限速]成功："+dayDuration);
            }else{
                //如果部分失败
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_TERMINAL,"统计终端限速DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"部分失败，失败日期为："+sb.toString());
                return R.fail("手动执行[终端限速]部分失败：[执行周期"+dayDuration+"]，失败日期为 "+sb.toString());
            }
        }else{
            //如果是单天统计
            try{
                long tmpTimestamp = sdf.parse(dayDuration).getTime()/1000;
                long startSecondTimeStamp = DateUtil.getDayFirstSecondTimestamp(tmpTimestamp);
                long endSecondTimestamp = DateUtil.getDayLastSecondTimestamp(tmpTimestamp);
                cacheSegLimitSpeedTerminalService.statisticsSegLimitSpeedTerminal(startSecondTimeStamp * 1000, endSecondTimestamp * 1000);
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_TERMINAL,"统计终端限速DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
            }catch (Exception e){
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_TERMINAL,"统计终端限速DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,e.getMessage());
                log.error("手动执行[终端限速]失败：[执行周期"+dayDuration+"] ", e);
                return R.fail("手动执行[终端限速]失败：[执行周期"+dayDuration+"] ");
            }
            return R.success("手动执行[终端限速]成功："+dayDuration);
        }
    }


    /**
     * @description: 手动执行 车辆上下线查询 DB缓存 统计
     * @author: zhouxw
     * @date: 2023-02-41 10:44:40
     * @param: [dayDuration yyyyMMdd / yyyyMMdd~yyyyMMdd]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @GetMapping("/statisticVehicleOnlineOrOfflineCache")
    public R statisticVehicleOnlineOrOfflineCache(String dayDuration){

        long start = System.currentTimeMillis();
        long end = 0;
        //日期格式校验
        if(StringUtils.isBlank(dayDuration) || !(dayDuration.length() == 8 || dayDuration.length() == 17) ||(dayDuration.length() == 17 && dayDuration.indexOf("~") < 0) ){
            return R.fail("日期格式不正确，请改正后再试");
        }
        //执行数据统计
        if(dayDuration.indexOf("~") > 0 ){
            //如果是时间段统计
            //依次执行每天的统计
            String startDay = dayDuration.split("~")[0];
            String endDay = dayDuration.split("~")[1];
            Calendar cal = Calendar.getInstance();
            try {
                cal.setTime(sdf.parse(startDay));
            } catch (ParseException e) {
                return R.fail("日期格式错误：" + dayDuration);
            }
            //存放执行失败的日期
            StringBuffer sb = new StringBuffer();
            while(endDay.compareTo(sdf.format(cal.getTime())) >= 0){
                try{
                    //获取开始时间和结束时间
                    long tmpTimestamp = cal.getTimeInMillis()/1000;
                    long startSecondTimeStamp = DateUtil.getDayFirstSecondTimestamp(tmpTimestamp);
                    long endSecondTimestamp = DateUtil.getDayLastSecondTimestamp(tmpTimestamp);
                    cacheVehicleOnlineOrOfflineService.statisticsVehicleOnlineOrOffline(startSecondTimeStamp * 1000, endSecondTimestamp * 1000, CommonConstant.DEFAULT_TENANT_ID);
                }catch (Exception e){
					log.error("跑批失败",e);
                    sb.append(sdf.format(cal.getTime())).append(",");
                }
                cal.add(Calendar.DAY_OF_YEAR , 1);
            }
            if(sb.length() < 1){
                //如果全部执行成功
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_OFFLINE,"统计车辆上下线查询DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
                return R.success("手动执行[车辆上下线查询]成功："+dayDuration);
            }else{
                //如果部分失败
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_OFFLINE,"统计车辆上下线查询DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"部分失败，失败日期为："+sb.toString());
                return R.fail("手动执行[车辆上下线查询]部分失败：[执行周期"+dayDuration+"]，失败日期为 "+sb.toString());
            }
        }else{
            //如果是单天统计
            try{
                long tmpTimestamp = sdf.parse(dayDuration).getTime()/1000;
                long startSecondTimeStamp = DateUtil.getDayFirstSecondTimestamp(tmpTimestamp);
                long endSecondTimestamp = DateUtil.getDayLastSecondTimestamp(tmpTimestamp);
                cacheVehicleOnlineOrOfflineService.statisticsVehicleOnlineOrOffline(startSecondTimeStamp * 1000, endSecondTimestamp * 1000, CommonConstant.DEFAULT_TENANT_ID);
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_OFFLINE,"统计车辆上下线查询DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
            }catch (Exception e){
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_OFFLINE,"统计车辆上下线查询DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,e.getMessage());
                log.error("手动执行[车辆上下线查询]失败：[执行周期"+dayDuration+"] ", e);
                return R.fail("手动执行[车辆上下线查询]失败：[执行周期"+dayDuration+"] ");
            }
            return R.success("手动执行[车辆上下线查询]成功："+dayDuration);
        }
    }



    /**
     * @description: 手动执行 车辆安全信息记录 DB缓存 统计
     * @author: zhouxw
     * @date: 2023-02-41 10:44:40
     * @param: [dayDuration yyyyMMdd / yyyyMMdd~yyyyMMdd]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @GetMapping("/statisticSecurityInfoCache")
    public R statisticSecurityInfoCache(String dayDuration){
        long start = System.currentTimeMillis();
        long end = 0;
        //日期格式校验
        if(StringUtils.isBlank(dayDuration) || !(dayDuration.length() == 8 || dayDuration.length() == 17) ||(dayDuration.length() == 17 && dayDuration.indexOf("~") < 0) ){
            return R.fail("日期格式不正确，请改正后再试");
        }
        //执行数据统计
        if(dayDuration.indexOf("~") > 0 ){
            //如果是时间段统计
            //依次执行每天的统计
            String startDay = dayDuration.split("~")[0];
            String endDay = dayDuration.split("~")[1];
            Calendar cal = Calendar.getInstance();
            try {
                cal.setTime(sdf.parse(startDay));
            } catch (ParseException e) {
                return R.fail("日期格式错误：" + dayDuration);
            }
            //存放执行失败的日期
            StringBuffer sb = new StringBuffer();
            while(endDay.compareTo(sdf.format(cal.getTime())) >= 0){
                try{
                    //获取开始时间和结束时间
                    long tmpTimestamp = cal.getTimeInMillis()/1000;
                    long startSecondTimeStamp = DateUtil.getDayFirstSecondTimestamp(tmpTimestamp);
                    long endSecondTimestamp = DateUtil.getDayLastSecondTimestamp(tmpTimestamp);
                    cacheSecurityInfoService.statisticsSecurityInfo(startSecondTimeStamp * 1000, endSecondTimestamp * 1000);
                }catch (Exception e){
                    sb.append(sdf.format(cal.getTime())).append(",");
                }
                cal.add(Calendar.DAY_OF_YEAR , 1);
            }
            if(sb.length() < 1){
                //如果全部执行成功
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SECURITY_INFO,"统计车辆安全信息记录DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
                return R.success("手动执行[车辆安全信息记录]成功："+dayDuration);
            }else{
                //如果部分失败
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SECURITY_INFO,"统计车辆安全信息记录DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"部分失败，失败日期为："+sb.toString());
                return R.fail("手动执行[车辆安全信息记录]部分失败：[执行周期"+dayDuration+"]，失败日期为 "+sb.toString());
            }
        }else{
            //如果是单天统计
            try{
                long tmpTimestamp = sdf.parse(dayDuration).getTime()/1000;
                long startSecondTimeStamp = DateUtil.getDayFirstSecondTimestamp(tmpTimestamp);
                long endSecondTimestamp = DateUtil.getDayLastSecondTimestamp(tmpTimestamp);
                cacheSecurityInfoService.statisticsSecurityInfo(startSecondTimeStamp * 1000, endSecondTimestamp * 1000);
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SECURITY_INFO,"统计车辆安全信息记录DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
            }catch (Exception e){
                end = System.currentTimeMillis();
                logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SECURITY_INFO,"统计车辆安全信息记录DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,e.getMessage());
                log.error("手动执行[车辆安全信息记录]失败：[执行周期"+dayDuration+"] ", e);
                return R.fail("手动执行[车辆安全信息记录]失败：[执行周期"+dayDuration+"] ");
            }
            return R.success("手动执行[车辆安全信息记录]成功："+dayDuration);
        }
    }


    /**
     * @description: 车辆在线率DB缓存统计接口
     * @author: zhouxw
     * @date: 2023-03-68 15:30:32
     * @param: [dayDuration yyyyMMdd / yyyyMMdd~yyyyMMdd]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @GetMapping("/statVehicleOnlineRateCache")
    public R statVehicleOnlineRate(String dayDuration){

        //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
        Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE);
        if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
            //如果已经有跑批程序在执行，则不再执行
            log.info("[车辆在线率DB缓存]跑批程序正在执行，请等待当前程序执行完毕后再试！");
            return R.fail("[车辆在线率DB缓存]跑批程序正在执行，请等待当前程序执行完毕后再试！");
        }

        //获取执行权限之后，添加执行标记（有效期为1个小时）
        synchronized (this){
            stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
        }


        long start = System.currentTimeMillis();
        long end = System.currentTimeMillis();
        //日期格式校验
        if(StringUtils.isBlank(dayDuration) || !(dayDuration.length() == 8 || dayDuration.length() == 17) ||(dayDuration.length() == 17 && dayDuration.indexOf("~") < 0) ){
            return R.fail("日期格式不正确，请改正后再试");
        }
        List<String> dateList = new ArrayList<>();
		Long startTime = 0L;
		Long endTime = 0L;
        try{
            //执行数据统计
            if(dayDuration.indexOf("~") > 0 ){
                //如果是时间段统计
                //计算dateList
                String dateStart = dayDuration.split("~")[0].substring(0,4)+"-"+dayDuration.split("~")[0].substring(4,6)+"-"+dayDuration.split("~")[0].substring(6,8);
                String dateEnd = dayDuration.split("~")[1].substring(0,4)+"-"+dayDuration.split("~")[1].substring(4,6)+"-"+dayDuration.split("~")[1].substring(6,8);
                dateList = DateUtil.getDateList(dateStart, dateEnd);
				startTime = DateUtil.getDayFirstSecondTimestamp(dateStart);
				long startTimeTmp = DateUtil.getDayFirstSecondTimestamp(dateEnd);
				endTime = DateUtil.getDayLastSecondTimestamp(startTimeTmp);
            }else{
                //如果是单天统计
                String date = dayDuration.substring(0,4)+"-"+dayDuration.substring(4,6)+"-"+dayDuration.substring(6,8);
                //dateList.add(date);
				startTime = DateUtil.getDayFirstSecondTimestamp(date);
				endTime = DateUtil.getDayLastSecondTimestamp(startTime);
            }
            //由于数据量比较大，一次执行完对数据库和内存压力比较大，所以，如果时间超过3天，则每3天的数据执行一次
            /*if(dateList.size() <=3){
                vehicleOnlineRateService.statisticsVehicleOnlineRate(dateList);
            }else{
                List<String> tmpList = new ArrayList<>();
                for(int i = 0 ; i < dateList.size(); i++){
                    tmpList.add(dateList.get(i));
                    if(i > 0 && i % 3 ==0){
                        vehicleOnlineRateService.statisticsVehicleOnlineRate(tmpList);
                        tmpList.clear();
                    }
                }
                if(tmpList.size() > 0){
                    vehicleOnlineRateService.statisticsVehicleOnlineRate(tmpList);
                }
            }*/

			vehicleOnlineRateService.statisticsVehicleOnlineRate(startTime, endTime);

            end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE,"统计车辆在线率DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
            return R.success("统计车辆在线率DB缓存成功");
        }catch (Exception e){
            log.error("统计车辆在线率失败",e);
            end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE,"统计车辆在线率DB缓存：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败");
            return R.fail("统计车辆在线率DB缓存失败");
        }finally {
            //执行完毕后，解除执行锁定
            synchronized (this){
                stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE );
            }
        }
    }



    //*******************************************考核管理DB缓存相关*******************************************************
    /**
     * @description: 考核管理--车辆漂移率DB缓存统计接口
     * @author: zhouxw
     * @date: 2023-03-68 15:30:32
     * @param: [dayDuration yyyyMM / yyyyMM~yyyyMM]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @GetMapping("/statDBCacheDriftRate")
    public R statDBCacheDriftRate(String dayDuration){

        //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
        Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_STAT_DRIFT_RATE);
        if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
            //如果已经有跑批程序在执行，则不再执行
            log.info("[考核--车辆漂移率DB缓存]跑批程序正在执行，请等待当前程序执行完毕后再试！");
            return R.fail("[考核--车辆漂移率DB缓存]跑批程序正在执行，请等待当前程序执行完毕后再试！");
        }

        //获取执行权限之后，添加执行标记（有效期为1个小时）
        synchronized (this){
            stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_STAT_DRIFT_RATE , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
        }


        long start = System.currentTimeMillis();
        long end = System.currentTimeMillis();
        //日期格式校验
        if(StringUtils.isBlank(dayDuration) || !(dayDuration.length() == 6 || dayDuration.length() == 13) ||(dayDuration.length() == 13 && dayDuration.indexOf("~") < 0) ){
            return R.fail("日期格式不正确，请改正后再试");
        }
        try{
            //执行数据统计
            if(dayDuration.indexOf("~") > 0 ){
                //如果是时间段统计
                String[] array = dayDuration.split("~");
                List<String> monthList = DateUtil.getMonthList(array[0]+"-01",array[1]+"-01");
                for(String month : monthList){
                    String monthStr = month.substring(0,4)+"-"+month.substring(4,6);
                    cacheStatDriftRateService.statAndSaveDriftRateCache(monthStr);
                }
            }else{
                //如果是单月统计
                String monthStr = dayDuration.substring(0,4)+"-"+dayDuration.substring(4,6);
                cacheStatDriftRateService.statAndSaveDriftRateCache(monthStr);
            }

            end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_STAT_DRIFT_RATE,"[考核--车辆漂移率DB缓存]：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
            return R.success("统计车辆漂移率DB缓存成功");
        }catch (Exception e){
            log.error("统计[考核--车辆漂移率DB缓存]失败",e);
            end = System.currentTimeMillis();
            logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_STAT_DRIFT_RATE,"[考核--车辆漂移率DB缓存]：手动执行跑批任务",dayDuration ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败");
            return R.fail("统计[考核--车辆漂移率DB缓存]失败");
        }finally {
            //执行完毕后，解除执行锁定
            synchronized (this){
                stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_STAT_DRIFT_RATE );
            }
        }
    }

}
