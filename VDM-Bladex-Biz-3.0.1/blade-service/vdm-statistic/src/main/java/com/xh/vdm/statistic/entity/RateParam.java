package com.xh.vdm.statistic.entity;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Description: 指标查询条件
 * @Author: zhouxw
 * @Date: 2022/9/8 1:58 PM
 */
@Data
public class RateParam {

    //统计月份：格式 yyyyMM
    @NotEmpty(message = "统计月份不能为空")
    private String month;

    //上级平台
    @NotNull(message = "上级平台不能为空")
    private Long ownerId;

    //企业名称
    @NotNull(message = "企业名称不能为空")
    private Long deptId;
}
