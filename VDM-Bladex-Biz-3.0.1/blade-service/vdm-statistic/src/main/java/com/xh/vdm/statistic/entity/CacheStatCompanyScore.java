package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 企业评分明细DB缓存表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CacheStatCompanyScore implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 月份
     */
    private String month;

    /**
     * 上级平台
     */
    private Long vehicleOwnerId;

    /**
     * 车组id
     */
    private Long deptId;

    /**
     * 企业名称
     */
    private String deptName;

    /**
     * 入网车辆数
     */
    private Long innetCount;

    /**
     * 车辆上线率
     */
    private Double goonlineRate;

    /**
     * 轨迹完整率
     */
    private Double completeRate;

    /**
     * 数据合格率
     */
    private Double qualityRate;

    /**
     * 定位漂移率
     */
    private Double driftRate;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 备注
     */
    private String note;


}
