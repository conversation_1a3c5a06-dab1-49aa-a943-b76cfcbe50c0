package com.xh.vdm.statistic.service;

import com.xh.vdm.statistic.entity.StatVehOnlineDay;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.vo.response.OnlineHourCountResponse;

import java.util.List;

/**
 * <p>
 * 车辆每个小时在线时长统计 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
public interface IStatVehOnlineDayService extends IService<StatVehOnlineDay> {

	/**
	 * 统计车辆在线情况
	 */
	void statVehOnlineDay() throws Exception ;

	/**
	 * 查询当日每个小时在线车辆数
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	List<OnlineHourCountResponse> findOnlineHourCount(List<String> hourList, List<Long> deptIds, Long userId);
}
