<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatDriveStatusMapper">

<!--    <select id="getDriverFaceResult" resultType="com.xh.vdm.statistic.entity.DriverFaceResultNode">-->
<!--        select licence_plate , driver_name , bdfr.face_id,  bvd.idcard , result , extract(epoch from time) time-->
<!--        from bdm_driver_face_result bdfr-->
<!--        left join bdm_vehicle_driver bvd on bdfr.face_id = bvd.face_id-->
<!--        where bvd.idcard is not null-->
<!--          and time like #{date,jdbcType=VARCHAR}-->
<!--        order by licence_plate , bvd.idcard , time-->
<!--    </select>-->
    <select id="getDriverFaceResult" resultType="com.xh.vdm.statistic.entity.DriverFaceResultNode">
        select licence_plate , driver_name , bdfr.face_id,  bvd.idcard , result , extract(epoch from bdfr.time) time
        from bdm_driver_face_result bdfr
            left join bdm_vehicle_driver bvd on bdfr.face_id = bvd.face_id
        where bvd.idcard is not null
          and date_trunc('day', time::date) = #{date}
        order by licence_plate , bvd.idcard , bdfr.time
    </select>


    <select id="getDriverFaceResultByDateAndIdCard" resultType="com.xh.vdm.statistic.entity.DriverFaceResultNode">
        select licence_plate , driver_name , bvd.idcard , result , extract(epoch from time) time
        from bdm_driver_face_result bdfr
                 left join bdm_vehicle_driver bvd on bdfr.driver_name = bvd.name
        where bvd.idcard is not null
            and bvd.idcard = #{idCard,jdbcType=VARCHAR}
          and time like #{date,jdbcType=VARCHAR}
        order by licence_plate , bvd.idcard , time
    </select>


    <select id="getNewestDriverWithFaceByLicencePlate" resultType="com.xh.vdm.statistic.entity.DriverFaceResultNode">
        select licence_plate , driver_name , bvd.idcard , result , extract(epoch from time) time
        from bdm_driver_face_result bdfr
            left join bdm_vehicle_driver bvd on bdfr.driver_name = bvd.name
        where bvd.idcard is not null
          and bdfr.licence_plate = #{licencePlate,jdbcType=VARCHAR} and bdfr.result = 0
        order by time desc limit 1
    </select>


    <select id="getDriverICResult" resultType="com.xh.vdm.statistic.entity.DriverICResultNode">
        select licence_plate , licence_color,  bdi.id_card id_card ,bvd.name driverName, operation , result , extract(epoch from op_time) time
        from bdm_driver_ic bdi
        left join bdm_person bvd on bdi.id_card = bvd.id_card
        where 1 = 1
          and date_trunc('day', op_time::date) = #{date}
          and length(licence_plate) > 0
        order by licence_plate  , time
    </select>

    <select id="getDriverICResultByDateAndIdCard" resultType="com.xh.vdm.statistic.entity.DriverICResultNode">
        select licence_plate , bdi.id_card id_card ,bvd.name, operation , result , extract(epoch from time) time
        from bdm_driver_ic bdi
                 left join bdm_person bvd on bdi.id_card = bvd.id_card
        where 1 = 1
          and bdi.op_time like #{date,jdbcType=VARCHAR}
            and bdi.id_card = #{idCard,jdbcType=VARCHAR}
          and length(licence_plate) > 0
        order by licence_plate  , bdi.op_time
    </select>

    <select id="getLastICResultByDate" resultType="com.xh.vdm.statistic.entity.DriverICResultNode">
        select bdi.licence_plate , bdi.licence_color, bdi.id_card idCard ,bvd.name driverName, operation , result , extract(epoch from bdi.op_time) time
        from bdm_driver_ic bdi
            left join bdm_person bvd on bdi.id_card = bvd.id_card
        where 1 = 1
          and bdi.op_time like #{date,jdbcType=VARCHAR}
          and length(licence_plate) > 0
            and bdi.licence_plate = #{licencePlate}
            and bdi.licence_color = #{licenceColor}
        order by bdi.op_time desc limit 1
    </select>

    <insert id="saveBatch" parameterType="com.xh.vdm.statistic.entity.StatDriveStatus" >

        insert into stat_drive_status_${month}(stat_date , driver_name , id_card , drive_duration ,drive_mileage, create_date , duration_list, state)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.statDate},#{item.driverName} , #{item.idCard} , #{item.driveDuration} , #{item.driveMileage} , #{item.createDate} , #{item.durationList}, #{item.state})
        </foreach>

    </insert>

    <delete id="deleteDataByDate" >
        delete from stat_drive_status_${month} where stat_date = #{date}
    </delete>

    <delete id="deleteAppendData">
        delete from stat_drive_status_${month}
        where state = 'a' and stat_date in (
            <foreach collection="dateList" item="date" separator=",">
                #{date}
            </foreach>
        )
    </delete>


    <select id="getDriveDaysByMonthAndIdCard" resultType="int">
        select count(*) from stat_drive_status_${month}
        where id_card = #{idCard,jdbcType=VARCHAR}
          and stat_date like #{monthLike,jdbcType=VARCHAR}
          and drive_duration > 0
    </select>

    <select id="getTotalDriveDurationByMonthAndIdCard" resultType="long">
        select coalesce(sum(drive_duration),0) from stat_drive_status_${month}
        where id_card = #{idCard,jdbcType=VARCHAR}
          and stat_date like #{monthLike,jdbcType=VARCHAR}
    </select>

    <select id="getTotalDriveMileageByMonthAndIdCard" resultType="long">
        select coalesce(sum(drive_mileage),0) from stat_drive_status_${month}
        where id_card = #{idCard,jdbcType=VARCHAR}
          and stat_date like #{monthLike,jdbcType=VARCHAR}
    </select>

    <select id="getDriveStatusList" resultType="com.xh.vdm.statistic.entity.DriveDurationBaseInfo">
        select stat_date date , id_card , drive_duration , drive_mileage from stat_drive_status_${month}
        where 1 = 1
          and stat_date like #{monthLike,jdbcType=VARCHAR}
          and id_card = #{idCard,jdbcType=VARCHAR}
          and drive_duration > 0
        order by stat_date
    </select>

    <select id="getDriverDriveDaysInMonth" resultType="com.xh.vdm.statistic.entity.DeptAndIdCardAndCount">

        select sd.id dept_id , sd.name dept_name,  sds.id_card , count(*) count
        from stat_drive_status_${month} sds
        left join bdm_vehicle_driver bvd on sds.id_card = bvd.idcard
        left join sys_dept sd on sd.id = bvd.dept_id
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=VARCHAR}
        </if>
        and drive_duration > 0
        group by sd.id , sds.id_card
    </select>

    <select id="getTotalDriveDaysInMonth" resultType="int">
        select count(*) from stat_drive_status_${month} sds
        left join bdm_vehicle_driver bvd on sds.id_card = bvd.idcard
        where 1 = 1
        <if test="deptId != null and deptId != ''">
          and bvd.dept_id = #{deptId,jdbcType=BIGINT}
        </if>
          and drive_duration > 0
    </select>


    <select id="getDriverDriveDaysInMonthDuration" resultType="com.xh.vdm.statistic.entity.DeptAndIdCardAndCount">

        select sd.id dept_id , sd.name dept_name,  sds.id_card , count(*) count
        from stat_drive_status_${month} sds
        left join bdm_vehicle_driver bvd on sds.id_card = bvd.idcard
        left join sys_dept sd on sd.id = bvd.dept_id
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=VARCHAR}
        </if>
        and stat_date &lt;= #{endDate}
        and stat_date >= #{startDate}
        and drive_duration > 0
        group by sd.id , sds.id_card
    </select>

    <select id="getDriveDaysCountInMonthByIdCard" resultType="int">
        select count(*) from
        (
            select distinct stat_date
            from stat_drive_status_${month}
            where id_card = #{idCard}
        )a
    </select>

    <select id="getDeptDriveMileageCount" resultType="com.xh.vdm.statistic.entity.DeptAndDriveStatus">
        select sd.name dept_name , sd.id dept_id ,  sds.driver_name ,  sds.stat_date , sds.drive_mileage
        from stat_drive_status_${month} sds
        left join bdm_vehicle_driver bvd on sds.id_card = bvd.idcard
        left join sys_dept sd on sd.id = bvd.dept_id
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=VARCHAR}
        </if>
        and drive_mileage > 0
        and stat_date >= #{startDate}
        and stat_date &lt;= #{endDate}
    </select>

    <select id="getTotalDurationForDeptEveryDay" resultType="com.xh.vdm.statistic.entity.DeptAndDateAndData">
        select sd.name dept_name ,  sds.stat_date , sum(sds.drive_duration) data
        from stat_drive_status_${month} sds
        left join bdm_vehicle_driver bvd on sds.id_card = bvd.idcard
        left join sys_dept sd on sd.id = bvd.dept_id
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=VARCHAR}
        </if>
        and drive_duration > 0
        and stat_date >= #{startDate,jdbcType=VARCHAR}
        and stat_date &lt;= #{endDate,jdbcType=VARCHAR}
        group by sd.name  , sds.stat_date
    </select>

    <select id="getTotalMileageInMonth" resultType="com.xh.vdm.statistic.entity.DateAndData">
        select stat_date , sum(drive_mileage) data
        from stat_drive_status_${month} sds
         left join bdm_vehicle_driver bvd on sds.id_card = bvd.idcard
         left join sys_dept sd on sd.id = bvd.dept_id
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=VARCHAR}
        </if>
        GROUP BY stat_date
    </select>

    <select id="getTotalMileageInMonthAll" resultType="double">
        select stat_date , sum(drive_mileage) data
        from stat_drive_status_${month} sds
        left join bdm_vehicle_driver bvd on sds.id_card = bvd.idcard
        left join sys_dept sd on sd.id = bvd.dept_id
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getDriverCountEveryDay" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select  sds.stat_date , count(*)
        from stat_drive_status_${month} sds
        left join bdm_vehicle_driver bvd on sds.id_card = bvd.idcard
        left join sys_dept sd on sd.id = bvd.dept_id
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=VARCHAR}
        </if>
        and drive_duration > 0
        and stat_date >= #{startDate,jdbcType=VARCHAR}
        and stat_date &lt;= #{endDate,jdbcType=VARCHAR}
        group by
        <if test="deptId != null and deptId != ''">
            sd.name  ,
        </if>
        sds.stat_date
    </select>


    <select id="getDriverCountByDate" resultType="int">
        select count(*) from stat_drive_status_${month} sds
        left join bdm_vehicle_driver bvd on sds.id_card = bvd.idcard
        where 1 = 1
        <if test="deptId != null and deptId != ''">
          and bvd.dept_id = #{deptId,jdbcType=BIGINT}
        </if>
        <if test="date != null and date != ''">
            and stat_date = #{date,jdbcType=VARCHAR}
        </if>
          and drive_duration > 0
    </select>

    <select id="getTotalDriveDurationByDate" resultType="double">
        select coalesce(sum(sds.drive_duration),0) from stat_drive_status_${month} sds
        left join bdm_vehicle_driver bvd on sds.id_card = bvd.idcard
        where 1 = 1
        <if test="deptId != null and deptId != ''">
          and bvd.dept_id = #{deptId,jdbcType=BIGINT}
        </if>
        <if test="date != null and date != ''">
            and stat_date = #{date}
        </if>
          and drive_duration > 0
    </select>

    <select id="getTotalDriveMileageByDate" resultType="double">
        select coalesce(sum(sds.drive_mileage),0) from stat_drive_status_${month} sds
        left join bdm_vehicle_driver bvd on sds.id_card = bvd.idcard
        where 1 = 1
          and bvd.dept_id = #{deptId,jdbcType=BIGINT}
          and stat_date = #{date,jdbcType=VARCHAR}
          and drive_mileage > 0
    </select>

    <select id="getDriveStatusByIdCard" resultType="com.xh.vdm.statistic.entity.StatDriveStatus">
        select * from stat_drive_status_${month}
        where 1 = 1
        and id_card = #{idCard}
        and stat_date = #{statDate}
    </select>

    <select id="getDriveStatusByIdCardMonth" resultType="com.xh.vdm.statistic.entity.wx.TotalMileageAndDurationInMonth">
        select #{month}, sum(drive_duration) total_duration, sum(drive_mileage) total_mileage
        from stat_drive_status_${month}
        where id_card = #{idCard}
    </select>

    <select id="getMileageAndDurationEveryDayInMonth" resultType="com.xh.vdm.statistic.entity.DateAndMileageAndDuration">
        select stat_date, sum(drive_duration) duration, sum(drive_mileage) mileage
        from stat_drive_status_${month}
        where id_card = #{idCard}
        group by stat_date
        order by stat_date
    </select>

</mapper>
