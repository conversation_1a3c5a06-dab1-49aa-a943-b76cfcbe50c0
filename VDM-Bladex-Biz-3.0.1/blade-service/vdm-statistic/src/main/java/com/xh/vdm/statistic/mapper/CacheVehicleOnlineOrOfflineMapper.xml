<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.CacheVehicleOnlineOrOfflineMapper">


    <select id="getList" resultType="com.xh.vdm.statistic.vo.response.VehicleOnlineOrOfflineResponse">
        select cvo.dept_name, cvo.licence_plate, cvo.licence_color, cvo.vehicle_use_type, cvo.vehicle_owner, cvo.te_state, cvo.on_line_time, cvo.off_line_time, cvo.position_count, cvo.driver, cvo.access_mode
        from cache_vehicle_online_offline cvo
        where 1 = 1
        <if test= 'request.startTime != null and request.startTime !="" '>
            AND on_line_time &gt;= to_timestamp(#{request.startTime})
        </if>
        <if test= 'request.endTime != null and request.endTime != "" '>
            AND on_line_time &lt;= to_timestamp(#{request.endTime})
        </if>
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and cvo.vehicle_id in
            <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
            and (
            <if test="request.deptList != null">
                cvo.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="request.userId != null">
                or cvo.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="request.vehicleUseType != null ">
            and cvo.vehicle_use_type_code in
            <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.vehicleOwnerId != null">
            and cvo.vehicle_owner_id = #{request.vehicleOwnerId}
        </if>
        <if test="request.accessMode != null">
            and cvo.access_mode_code = #{request.accessMode}
        </if>
        order by on_line_time desc
    </select>

    <select id="getVeState" resultType="com.xh.vdm.statistic.entity.TeState" parameterType="string">
        select bv.licence_plate, bv.licence_color, bvs.te_state
        from bdm_vehicle_state bvs
                 left join bdm_terminal bt on bvs.phone = bt.phone
                 left join bdm_vehicle bv on bv.terminal_id = bt.id
        where concat(bv.licence_plate, '~', bv.licence_color) in
            <foreach collection="params" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
    </select>

</mapper>
