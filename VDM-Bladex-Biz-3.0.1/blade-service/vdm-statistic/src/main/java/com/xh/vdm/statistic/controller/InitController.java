package com.xh.vdm.statistic.controller;

import com.alibaba.druid.util.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.statistic.entity.BladeDept;
import com.xh.vdm.statistic.entity.BladeParam;
import com.xh.vdm.statistic.entity.DeptTree;
import com.xh.vdm.statistic.entity.UserDept;
import com.xh.vdm.statistic.service.IBladeDeptService;
import com.xh.vdm.statistic.service.IBladeParamService;
import com.xh.vdm.statistic.service.IBladeUserService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.node.ForestNodeMerger;
import org.springblade.system.vo.DictBizVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.print.attribute.HashAttributeSet;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平台新租户初始化控制器
 */
@RestController
@Slf4j
@RequestMapping("/init")
public class InitController {


	@Resource
	private IBladeDeptService deptService;

	@Resource
	private IBladeUserService userService;

	@Resource
	private IBladeParamService paramService;


	/**
	 * 查询系统参数
	 * @param paramName
	 * @return
	 */
	@GetMapping("/getSystemParam")
	public R<String> getSystemParam(String paramName){
		if(StringUtils.isEmpty(paramName)){
			return R.fail("参数名称不能为空");
		}
		try{
			BladeParam param = paramService.getOne(Wrappers.lambdaQuery(BladeParam.class).eq(BladeParam::getParamKey, paramName));
			return R.data(param.getParamValue());
		}catch (Exception e){
			log.error("查询系统参数出错",e);
		}
		return R.fail("查询系统参数出错");
	}

	/**
	 * 查询系统版本
	 * @return
	 */
	@GetMapping("/getSystemVersion")
	public R<String> queryVersion(){
		try{
			BladeParam param = paramService.getOne(Wrappers.lambdaQuery(BladeParam.class).eq(BladeParam::getParamKey, CommonConstant.PARAM_SYSTEM_VERSION));
			return R.data(param.getParamValue());
		}catch (Exception e){
			log.error("查询系统版本出错",e);
		}
		return R.fail("查询系统版本出错");
	}

	/**
	 * 获取websocket地址
	 * @return
	 */
	@GetMapping("/getWebsocketParam")
	public R<String> getWebsocketParam(){
		try{
			BladeParam param = paramService.getOne(Wrappers.lambdaQuery(BladeParam.class).eq(BladeParam::getParamKey, CommonConstant.PARAM_WEBSOCKET));
			return R.data(param.getParamValue());
		}catch (Exception e){
			log.error("查询websocket地址出错",e);
		}
		return R.fail("查询websocket地址出错");
	}

	/**
	 * 初始化部门层级数据
	 * 执行逻辑：根据parent_id建立树形结构，获取层级数据，更新到数据库
	 * 使用时机：部门数据已经成功导入 blade_dept 之后，并且parent_id是准确的
	 * @param tenantId
	 * @return
	 */
	@GetMapping("/initDeptAncestos")
	public R<String> initDeptAncestors(String tenantId){
		try{
			//1.查找租户下所有部门信息
			List<DeptTree> list = deptService.findDeptTree(tenantId);
			log.info("共查询到部门信息"+(list==null?0:list.size())+"个");
			//2.构建树
			List<DeptTree> tree = ForestNodeMerger.merge(list);
			//3.遍历树，并且对每个节点计算 层级信息
			if(tree == null || tree.size() == 0){
				return R.fail("未查询到部门数据");
			}
			List<DeptTree> nodeList = new ArrayList<>();
			for(DeptTree t : tree){
				combineAncestors(t, t.getAncestors()==null?"0":t.getAncestors(),nodeList);
			}
			Map<String,DeptTree> map = new HashMap<>();
			nodeList.forEach(item -> {
				map.put(item.getId()+"",item);
			});
			//4.更新到数据库
			List<BladeDept> deptList = new ArrayList<>();
			List<Long> deptIds = new ArrayList<>();
			for (DeptTree d : nodeList) {
				deptIds.add(d.getId());
			}
			List<BladeDept> depts = deptService.listByIds(deptIds);
			depts.forEach(item -> {
				DeptTree node = map.get(item.getId()+"");
				if(node != null){
					item.setAncestors(node.getAncestors());
				}
			});
			deptService.updateBatchById(depts);
			return R.success("初始化部门层级成功");
		}catch (Exception e){
			log.error("初始化部门层级数据失败",e);
			return R.fail("初始化部门层级数据失败"+e.getMessage());
		}
	}

	/**
	 * 根据 blade_user_dept 中的数据初始化 blade_user 表中的deptId
	 * 多个部门id之间，使用 , 分隔
	 * @return
	 */
	@GetMapping("/initDeptIdInBladeUser")
	public R<String> initDeptIdInBladeUser(){
		try{
			//1.查询用户对应部门数量大于1的数据
			List<Long> userIds = userService.findUserIdsWithMultiDeptCount();
			//2.对每一组数据执行deptId的拼接
			List<UserDept> list = new ArrayList<>();
			for(Long userId : userIds){
				List<Long> deptIds = userService.findDeptIdsByUserId(userId);
				//拼接部门
				StringBuffer sb = new StringBuffer();
				for(int i = 0; i < deptIds.size(); i++){
					if(i < deptIds.size()-1){
						sb.append(deptIds.get(i)).append(",");
					}else{
						sb.append(deptIds.get(i));
					}
				}
				UserDept ud = new UserDept();
				ud.setUserId(userId);
				ud.setDeptId(sb.toString());
				list.add(ud);
			}
			//3.保存到数据库
			list.forEach(item -> {
				userService.updateDeptId(item);
			});
		}catch (Exception e){
			log.error("初始化blade_user的deptId字段失败",e);
			return R.fail("操作失败");
		}
		return R.success("操作成功");

	}

	/**
	 * 递归拼接部门层级
	 * @param tree
	 * @param parentAncestor
	 * @param nodeList
	 */
	private void combineAncestors(DeptTree tree, String parentAncestor, List<DeptTree> nodeList){
		Long parentId = tree.getParentId();
		if(parentId == 0 && StringUtils.isEmpty(tree.getAncestors())){
			tree.setAncestors("0");
			nodeList.add(tree);
			combineAncestors(tree,"0", nodeList);
		}else{
			List<DeptTree> list = tree.getChildren();
			for(DeptTree n : list){
				String ancestor = parentAncestor+","+n.getParentId();
				n.setAncestors(ancestor);
				nodeList.add(n);
				if(n.getChildren() != null && n.getChildren().size() > 0){
					combineAncestors(n,parentAncestor+","+n.getParentId(),nodeList);
				}
			}
		}
	}
}
