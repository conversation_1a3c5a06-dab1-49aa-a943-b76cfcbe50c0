package com.xh.vdm.statistic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.BdmVehicle;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.request.CommonStatRequest;
import com.xh.vdm.statistic.vo.response.CarMegaByteResponse;
import com.xh.vdm.statistic.vo.response.OnlineDaysCountResponse;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-25
 */
public interface BdmVehicleMapper extends BaseMapper<BdmVehicle> {


	long getAllVehicleCount(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);

	/**
	 * 根据deptId查询全部车辆数
	 *
	 * @param deptIds
	 * @return
	 */
	long getAllVehicleCountByDeptId(@Param("deptIds") List<Long> deptIds);

	/**
	 * 统计证件到期车辆数
	 *
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	long getCertExpiredCount(@Param("expireDate") Date expreDate, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	/**
	 * 查询已经过期的道路运输证的数量
	 *
	 * @param expreDate
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	long getCertHasExpiredCount(@Param("expireDate") Date expreDate, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	/**
	 * @description: 获取营运车辆数
	 * @author: zhouxw
	 * @date: 2023-07-194 09:54:07
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long getRunningCount(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);

	/**
	 * @description: 获取营运车辆数
	 * @author: zhouxw
	 * @date: 2023-07-194 09:54:07
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long getRunningCountByDeptId(@Param("deptIds") List<Long> deptIds);

	/**
	 * @description: 查询在指定日期之前未上过线的车辆数
	 * @author: zhouxw
	 * @date: 2023-07-207 14:34:43
	 * @param: [deptIds, userId, date：未上过线的日期 yyyy-MM-dd]
	 * @return: long
	 **/

	long getOfflineCountBeforeDate(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId, @Param("date") String date);


	/**
	 * @description: 查询在指定日期之前未上过线的车辆明细
	 * @author: zhouxw
	 * @date: 2023-07-207 14:34:43
	 * @param: [deptIds, userId, date：未上过线的日期 yyyy-MM-dd]
	 * @return: long
	 **/
	List<VehicleAndCount> getOfflineListBeforeDate(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId, @Param("date") String date, @Param("limit") Integer limit);

	/**
	 * 分页查询指定日期之前未上线的车辆
	 *
	 * @param deptIds
	 * @param userId
	 * @param date
	 * @return
	 */
	IPage<VehicleAndCount> getOfflineBeforeDatePage(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId, @Param("date") String date, @Param("licencePlate") String licencePlate, @Param("licenceColor") Long licenceColor, IPage<VehicleAndCount> page);

	/**
	 * @description: 获取营运车辆
	 * @author: zhouxw
	 * @date: 2023-07-194 09:54:07
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	List<VehicleBase> getRunningVehicle(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);

	/**
	 * @description: 获取未知过期时间的车辆数
	 * @author: zhouxw
	 * @date: 2023-07-205 21:54:17
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long getUnKnowExpireCount(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	/**
	 * @description: 获取停运车辆数
	 * @author: zhouxw
	 * @date: 2023-07-194 09:54:07
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long getStopRunningCount(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	/**
	 * @description: 查询入网车辆数
	 * @author: zhouxw
	 * @date: 2023-07-194 10:38:30
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long getInNetCount(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);

	/**
	 * @description: 查询上线车辆数
	 * @author: zhouxw
	 * @date: 2023-07-205 20:29:36
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long getGoOnlineCount(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);

	/**
	 * 统计今日上过线的车辆数
	 * 今日上传过定位点
	 *
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	long getGoOnlineCountAllToday(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	/**
	 * 统计指定月份上过线的车辆数
	 * 不包含上线超过一个月的情况
	 *
	 * @param deptIds
	 * @param userId
	 * @param month   yyyyMM
	 * @return
	 */
	long getGoOnlineCountAllMonth(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId, @Param("month") String month, @Param("lastDate") String lastDateInMonth, @Param("monthDate") String monthDate);

	/**
	 * 统计指定月份上过线的车辆数
	 * 不包含上线超过一个月的情况
	 *
	 * @param month           yyyyMM
	 * @param lastDateInMonth yyyy-MM-dd
	 * @return
	 */
	List<VehicleAndCount> getGoOnlineDurationListAllMonthByVehicleId(@Param("vehicleIds") List<Integer> vehicleIds, @Param("month") String month, @Param("lastDate") String lastDateInMonth, @Param("monthDate") String monthDate);

	/**
	 * 查询车辆上线天数，根据车辆上下线记录查询
	 * 每条上下线记录会返回一条，所以车牌号会重复
	 *
	 * @param vehicleIds
	 * @param month
	 * @param statDate
	 * @return
	 */
	List<VehicleAndOnOfflineRecord> getVehicleOnOfflineRecord(@Param("vehicleIds") List<Integer> vehicleIds, @Param("month") String month, @Param("statDate") String statDate);

	/**
	 * 查询车辆上线天数，根据车辆上下线记录查询，按照月份查询
	 * 每条上下线记录会返回一条，所以车牌号会重复
	 *
	 * @param vehicleIds
	 * @param month
	 * @return
	 */
	List<VehicleAndOnOfflineRecord> getVehicleOnOfflineRecordMonth(@Param("vehicleIds") List<Integer> vehicleIds, @Param("month") String month);


	/**
	 * 查询指定天的上线车辆数
	 * 暂不考虑连续在线超过一个月的情况！！
	 *
	 * @param deptIds
	 * @param date    yyyy-MM-dd
	 * @return
	 */
	long getGoOnlineCountByDate(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("date") String date, @Param("month") String month);


	/**
	 * 查询指定天的上线车辆数
	 * 暂不考虑连续在线超过一个月的情况！！
	 *
	 * @param vehicleIds
	 * @param date       yyyy-MM-dd
	 * @return
	 */
	List<VehicleAndCount> getGoOnlineDurationListByDateAndVehicleId(@Param("vehicleIds") List<Integer> vehicleIds, @Param("date") String date, @Param("month") String month);


	/**
	 * @description: 查询在线车辆数
	 * @author: zhouxw
	 * @date: 2023-07-194 14:41:42
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long getOnlineCount(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	List<BdmVehicle> getUserVehicleList(@Param("deptList") List<Long> deptList, @Param("vehicleList") List<Integer> vehicleList);


	/**
	 * 统计行驶车辆数
	 *
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	long getRunningVehicleCount(@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	/**
	 * 根据动态sql查询数量
	 *
	 * @param sql
	 * @return
	 */
	List<OnlineDaysCountResponse> getOnlineDaysCount(String sql);

	/**
	 * 统计总里程
	 *
	 * @param dateList
	 * @param vehicleIdList
	 * @param month
	 * @return
	 */
	List<VehicleAndMileage> getTotalMileage(@Param("dateList") List<String> dateList, @Param("vehicleIdList") List<Long> vehicleIdList, @Param("month") String month);


	/**
	 * 统计车辆总里程
	 *
	 * @param deptIds
	 * @param vehicleIds
	 * @param month      yyyyMM
	 * @param statDate   yyyyMMdd
	 * @return
	 */
	IPage<VehicleAndMileage> getTotalMileagePage(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("statDate") String statDate, @Param("month") String month, IPage<VehicleAndMileage> page);


	/**
	 * 统计车辆总里程
	 *
	 * @param deptIds
	 * @param vehicleIds
	 * @param month      yyyyMM
	 * @return
	 */
	IPage<VehicleAndMileage> getTotalMileageMonthPage(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("month") String month, IPage<VehicleAndMileage> page);


	/**
	 * 根据车牌号、车牌颜色列表查询车辆信息
	 *
	 * @param plateAndColorList 车牌号+"~"+车牌颜色
	 * @return
	 */
	@DS("master")
	List<BdmVehicle> getVehicleByLicencePlateAndColor(@Param("keyList") List<String> plateAndColorList);

	/**
	 * 获取车辆每月在线天数
	 *
	 * @param vehicleList
	 * @param dateList
	 * @return
	 */
	List<VehicleAndDateAndCount> getVehicleOnlineCount(@Param("vehicleList") List<String> vehicleList, @Param("dateList") List<DateListAndMonth> dateList);

	/**
	 * 根据deptId查询总里程
	 *
	 * @param deptIds
	 * @param date    dd，如 02
	 * @param month   yyyyMM
	 * @return
	 */
	double getTotalMileageByDeptIds(@Param("deptList") List<Long> deptIds, @Param("date") String date, @Param("month") String month);

	/**
	 * 根据条件分页查询车辆
	 *
	 * @param request
	 * @param page
	 * @return
	 */
	IPage<BdmVehicle> getVehiclesByCondition(@Param("request") CommonBaseCrossMonthRequest request, IPage<BdmVehicle> page);


	/**
	 * 根据条件查询车辆总数
	 *
	 * @param request
	 * @return
	 */
	long getVehiclesByConditionCount(@Param("request") CommonBaseCrossMonthRequest request);

	/**
	 * 查询车辆基本信息
	 *
	 * @param vehicleIds
	 * @return
	 */
	List<VehicleCommonInfo> getVehicleCommonInfo(List<Long> vehicleIds);

	/**
	 * 车辆日流量统计
	 *
	 * @param request
	 * @return
	 */
	IPage<CarMegaByteResponse> getVehicleMegaByteDaily(@Param("request") CommonBaseRequest request, @Param("dateList") List<String> dateList, IPage<CarMegaByteResponse> page);

	/**
	 * 查询车辆基本信息
	 * @param deptIds
	 * @param vehicleIds
	 * @param licencePlate
	 * @return
	 */
	List<VehicleBaseWithId> getVehicleBaseListAll(List<Long> deptIds, List<Integer> vehicleIds, String licencePlate);

	/**
	 * 按单位分组的车辆数
	 * @param request
	 * @return
	 */
	List<Map<String, Long>> getNumVehicleGroupByDept(@Param("request") CommonStatRequest request);

	IPage<BdmVehicle> getVehicleListByPage(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, IPage<Vehicle> page);

}
