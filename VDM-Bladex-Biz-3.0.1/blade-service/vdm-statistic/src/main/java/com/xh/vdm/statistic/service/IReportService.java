package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.VehicleAndCount;
import com.xh.vdm.statistic.entity.report.AlarmSummary;
import com.xh.vdm.statistic.entity.report.ReportSummary;
import com.xh.vdm.statistic.entity.report.VehicleTravelMessage;
import com.xh.vdm.statistic.entity.wx.AlarmInfoWX;
import com.xh.vdm.statistic.entity.wx.VehicleBaseInfoWX;
import com.xh.vdm.statistic.vo.response.OnlineHourCountResponse;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;

import java.util.List;

/**
 * 报告服务
 */
public interface IReportService {

	/**
	 * 统计企业概况
	 * 包含基本运行情况、报警相关信息、企业车辆排行
	 * @param deptId 部门id
	 * @param statDate 统计日期，可以是天，也可以是月 yyyy-MM-dd / yyyy-MM
	 * @param startSecondTime 开始时间戳（精确到秒）
	 * @param endSecondTime 结束时间戳（精确到秒）
	 * @return
	 * @throws Exception
	 */
	ReportSummary reportSummary(Long deptId, String statDate, Long startSecondTime, Long endSecondTime) throws Exception;


	/**
	 * 统计企业基本信息
	 * @param deptId
	 * @param statDate yyyy-MM-dd / yyyy-MM
	 * @return
	 * @throws Exception
	 */
	VehicleBaseInfoWX vehicleBaseInfo(Long deptId, String statDate) throws Exception;

	/**
	 * 统计每小时在线车辆数
	 * @param deptId
	 * @param date yyyy-MM-dd
	 * @return
	 * @throws Exception
	 */
	List<OnlineHourCountResponse> onlineHourCount(Long deptId, String date) throws Exception;






	/**
	 * 统计报警数据
	 * @param deptId
	 * @param statDate
	 * @return
	 * @throws Exception
	 */
	AlarmInfoWX findAlarmInfo(Long deptId, String statDate) throws Exception;


	/**
	 * 统计企业概况--微信小程序
	 * 包含基本运行情况、报警相关信息、企业车辆排行
	 * @param deptId 部门id
	 * @param statDate 统计日期，可以是天，也可以是月 yyyy-MM-dd / yyyy-MM
	 * @param startSecondTime 开始时间戳（精确到秒）
	 * @param endSecondTime 结束时间戳（精确到秒）
	 * @return
	 * @throws Exception
	 */
	ReportSummary reportSummaryWX(Long deptId, String statDate, Long startSecondTime, Long endSecondTime) throws Exception;

	/**
	 * 车辆运行情况
	 * @param deptId
	 * @param statDate yyyy-MM / yyyy-MM-dd
	 * @return
	 * @throws Exception
	 */
	VehicleTravelMessage vehicleTravel(Long deptId, String statDate, Query query) throws Exception;



	/**
	 * 统计报警概况
	 * @param deptId
	 * @param statDate 统计日期，可以是天，也可以是月 yyyy-MM-dd / yyyy-MM
	 * @param startSecondTime
	 * @param endSecondTime
	 * @return
	 * @throws Exception
	 */
	AlarmSummary alarmSummary(Long deptId, String statDate,Long startSecondTime, Long endSecondTime) throws Exception;


	/**
	 * 分页查询长期不在线的车辆
	 * @param query
	 * @param user
	 * @return
	 * @throws Exception
	 */
	IPage<VehicleAndCount> longOfflineVehiclePage(Query query, long deptId, String licencePlate, Long licenceColor, BladeUser user) throws Exception;


	/**
	 * 分页查询超速报警排行
	 * @param query
	 * @param user
	 * @return
	 * @throws Exception
	 */
	IPage<VehicleAndCount> overSpeedPage(Query query,Long startTime, Long endTime, String licencePlate, Long licenceColor, BladeUser user) throws Exception;
}
