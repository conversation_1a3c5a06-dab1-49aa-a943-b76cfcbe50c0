<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmAbstractDeviceMapper">

    <select id="getAccessDeviceList" resultType="com.xh.vdm.statistic.entity.BdmAbstractDevice">
        select *
        from bdm_abstract_device
        where 1 = 1
        <if test="account != null and account != ''">
            and create_account = #{account}
        </if>

    </select>

</mapper>
