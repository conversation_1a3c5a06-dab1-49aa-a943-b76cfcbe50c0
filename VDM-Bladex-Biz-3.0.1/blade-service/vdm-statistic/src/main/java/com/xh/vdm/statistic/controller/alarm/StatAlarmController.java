package com.xh.vdm.statistic.controller.alarm;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.dto.alarm.AlarmCount;
import com.xh.vdm.statistic.entity.BamThirdPartyPlatform;
import com.xh.vdm.statistic.entity.BladeDept;
import com.xh.vdm.statistic.entity.impala.ImpalaAlarm;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.vo.request.CommonStatRequest;
import com.xh.vdm.statistic.vo.request.alarm.*;
import com.xh.vdm.statistic.vo.response.alarm.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springblade.common.config.ExcelExportFontAndStyleConfig;
import org.springblade.common.config.ExcelFillCellMergePrevCol;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.EasyExcelUtils;
import org.springblade.common.utils.ReportVO;
import org.springblade.common.utils.UserUtil;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.system.cache.SysCache;
import org.springblade.system.entity.Dept;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.user.entity.BdmVehicle;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "报警类统计报表", protocols = "http", produces = "application/json", consumes = "application/json")
@Slf4j
@RestController
@RequestMapping("stat/alarm")
public class StatAlarmController {

	private final SimpleDateFormat timeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	@Value("${static.file.path}")
	private String staticFilePath;

	@Value("${proxy.file.path}")
	private String proxyFilePath;

	@Resource
	private ISysClient sysClient;

	@Autowired
	private IUserClient userClient;

	@Resource
	private IDictBizClient dictBizClient;

	@Autowired
	private IBladeDeptService deptService;

	@Autowired
	private IBdmVehicleService bdmVehicleService;

	@Autowired
	private IBamThirdPartyPlatformService bamThirdPartyPlatformService;

	@Autowired
	private ITerminalService terminalService;

	@Autowired
	private IAlarmService alarmService;

	private R<List<String>> getVehicleUseTypeList (String vehicleUseType) {
		List<String> vehicleUseTypeList = new ArrayList<>();
		if (StringUtils.isBlank(vehicleUseType)) {
			return R.data(ResultCode.SUCCESS.getCode(), vehicleUseTypeList, "");
		}

		R<List<DictBiz>> r = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelf(CommonConstant.DICT_VEHICLE_USE_TYPE, vehicleUseType);
		if (!r.isSuccess()) {
			return R.fail(r.getCode(), r.getMsg());
		}

		List<DictBiz> dictList = r.getData();
		if ((dictList == null) || dictList.isEmpty()) {
			return R.fail(ResultCode.FAILURE.getCode(), "行业类型不存在。");
		}

		vehicleUseTypeList = dictList.parallelStream().map(DictBiz::getDictKey).collect(Collectors.toList());
		return R.data(ResultCode.SUCCESS.getCode(), vehicleUseTypeList, "");
	}

	private R<String> isValidServiceRole (String serviceRole, BladeUser user) {
		switch (serviceRole) {
			case "1":
				if (!UserUtil.isServiceRoleServer(user)) {
					return R.fail(ResultCode.FAILURE.getCode(), "当前登录用户没有对应的服务角色，请联系管理员添加。");
				}

				break;
			case "2":
				if (!UserUtil.isServiceRoleThird(user)) {
					return R.fail(ResultCode.FAILURE.getCode(), "当前登录用户没有对应的服务角色，请联系管理员添加。");
				}

				break;
			case "3":
				if (!UserUtil.isServiceRoleCompany(user)) {
					return R.fail(ResultCode.FAILURE.getCode(), "当前登录用户没有对应的服务角色，请联系管理员添加。");
				}

				break;
			default:
				return R.fail(ResultCode.FAILURE.getCode(), "服务角色不存在。");
		}

		return R.success(ResultCode.SUCCESS);
	}

	private R<Object> addDataRangeForRequest (CommonStatRequest request, BladeUser user) {
		if ((user == null) || (user.getUserId() == null) || StringUtils.isBlank(user.getRoleName()) || StringUtils.isBlank(user.getDeptId())) {
			return R.fail(ResultCode.FAILURE.getCode(), "用户登录信息异常。");
		}

		R<String> r0 = this.isValidServiceRole(request.getServiceRole(), user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}

		String[] deptListStr = user.getDeptId().split(",");
		List<Long> deptList = new ArrayList<>();
		List<Long> tmp;
		for (String deptIdStr : deptListStr) {
			tmp = SysCache.getDeptChildIds(Long.parseLong(deptIdStr));
			if (CollectionUtils.isNotEmpty(tmp)) {
				deptList.addAll(tmp);
			}
		}
		if (CollectionUtils.isEmpty(deptList)) {
			return R.fail(ResultCode.FAILURE.getCode(), "组织架构信息异常。");
		}
		if (CollectionUtils.isNotEmpty(request.getDeptIdList())) {
			Collection<Long> tmpList = CollectionUtils.intersection(deptList, request.getDeptIdList());
			if (CollectionUtils.isEmpty(tmpList)) {
				return R.fail(ResultCode.FAILURE.getCode(), "没有指定组织架构的访问权限。");
			}

			deptList = tmpList.parallelStream().collect(Collectors.toList());
		}

		R<List<BdmVehicle>> r1 = this.userClient.findVehiclesByUserId(user.getUserId());
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		R<List<String>> r2 = this.getVehicleUseTypeList(request.getVehicleUseType());
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		request.setDeptList(deptList);
		request.setVehicleList(((r1.getData() == null) || r1.getData().isEmpty()) ? new ArrayList<>() : r1.getData().parallelStream().map(BdmVehicle::getId).collect(Collectors.toList()));
		request.setVehicleUseTypeList(r2.getData());
		List<String> vehicleUseTypeList = request.getVehicleUseTypeList();
		Long vehicleOwnerId = request.getVehicleOwnerId();
		String accessMode = request.getAccessMode();
		if (((vehicleUseTypeList == null) || vehicleUseTypeList.isEmpty()) && (vehicleOwnerId == null) && StringUtils.isBlank(accessMode)) {
			return R.success(ResultCode.SUCCESS, "");
		}

		List<Integer> vehicleIdList = this.bdmVehicleService.getVehicleIdList(vehicleUseTypeList, vehicleOwnerId, accessMode);
		if ((vehicleIdList == null) || vehicleIdList.isEmpty()) {
			vehicleIdList = new ArrayList<>();
			vehicleIdList.add(-1); // 为了区分车辆列表为空、车辆列表不存在两种情况。前者表示：查询条件不需要过滤车辆列表；后者表示：查询不到包含对应车辆列表的数据。
		} else {
			if ((request.getVehicleIdList() != null) && (!request.getVehicleIdList().isEmpty())) {
				Collection<Integer> tmpList = CollectionUtils.intersection(request.getVehicleIdList(), vehicleIdList);
				if ((tmpList == null) || tmpList.isEmpty()) {
					vehicleIdList = new ArrayList<>();
					vehicleIdList.add(-1);
				} else {
					vehicleIdList = tmpList.parallelStream().collect(Collectors.toList());
				}
			}
		}

		request.setVehicleIdList(vehicleIdList);
		return R.success(ResultCode.SUCCESS, "");
	}

	private R<Object> addDataRangeForMail (CommonStatRequest request) {
		List<Long> deptList = SysCache.getDeptChildIds(request.getDeptId());
		if ((deptList == null) || deptList.isEmpty()) {
			return R.fail(ResultCode.FAILURE.getCode(), "组织架构信息异常。");
		}

		request.setDeptList(deptList);
		return R.success(ResultCode.SUCCESS, "");
	}

	private R<Object> formCommonAlarmStatPageResponse (List<ImpalaAlarm> alarmList, List<CommonAlarmStatResponse> responseList, CommonStatRequest request, BladeUser user) {
		R<Map<Long, Dept>> r = this.sysClient.getDeptChildWithOuterMapWithSelf(user.getUserId());
		if (!r.isSuccess()) {
			return R.fail(r.getCode(), r.getMsg());
		}

		Map<Long, Dept> deptMap = ((r.getData() == null) || r.getData().isEmpty()) ? new HashMap<>() : r.getData();
		Map<Integer, com.xh.vdm.statistic.entity.BdmVehicle> vehicleMap = this.bdmVehicleService.getVehicleMap(request);
		Map<String, String> terminalMap = this.terminalService.getTerminalMap();
		Map<Long, BamThirdPartyPlatform> ownerMap = new HashMap<>();
		List<BamThirdPartyPlatform> ownerList = this.bamThirdPartyPlatformService.list();
		if ((ownerList != null) && (!ownerList.isEmpty())) {
			ownerMap = ownerList.parallelStream().collect(Collectors.toMap(BamThirdPartyPlatform::getId, owner -> owner));
		}
		for (ImpalaAlarm alarm : alarmList) {
			CommonAlarmStatResponse response = new CommonAlarmStatResponse();
			BeanUtils.copyProperties(alarm, response);
			response.setStartAddress(alarm.getAlarmAddress());
			response.setEndAddress(alarm.getAlarmEndAddress());
			response.setAlarmType(alarm.getAlarmType().toString());
			response.setAlarmLevel(alarm.getAlarmLevel().toString());
			response.setDurationTime(alarm.getAlarmEndTime() - alarm.getAlarmTime());
			response.setOverSpeedPercent(((alarm.getMaxSpeed() == null) || (alarm.getLimitSpeed() == null)) ? "0" : this.getRate(alarm.getMaxSpeed() - alarm.getLimitSpeed(), alarm.getLimitSpeed()));
			response.setStartTime(this.timeFormat.format(new Date(alarm.getAlarmTime() * 1000)));
			response.setEndTime(this.timeFormat.format(new Date(alarm.getAlarmEndTime() * 1000)));
			response.setDrivingTime((alarm.getDrivingTime() == null) ? "" : this.timeFormat.format(new Date(alarm.getDrivingTime() * 1000)));
			response.setTerminalType(terminalMap.getOrDefault(alarm.getPhone(), ""));
			response.setDeptName("");
			response.setVehicleUseType("");
			response.setVehicleOwnerName("非营运车辆");
			response.setAccessMode("");
			response.setDealMeasures("");
			response.setDealContent("");
			response.setDealTime("");
			String serviceRole = request.getServiceRole();
			int vehicleId = alarm.getVehicleId();
			switch (serviceRole) {
				case "1":
					response.setDealMeasures(alarm.getServerMeasures());
					response.setDealContent(alarm.getServerContent());
					response.setDealTime((alarm.getServerTime() == null) ? "" : this.timeFormat.format(new Date(alarm.getServerTime() * 1000)));
					break;
				case "2":
					response.setDealMeasures(alarm.getThirdMeasures());
					response.setDealContent(alarm.getThirdContent());
					response.setDealTime((alarm.getThirdTime() == null) ? "" : this.timeFormat.format(new Date(alarm.getThirdTime() * 1000)));
					break;
				case "3":
					response.setDealMeasures(alarm.getCompanyMeasures());
					response.setDealContent(alarm.getCompanyContent());
					response.setDealTime((alarm.getCompanyTime() == null) ? "" : this.timeFormat.format(new Date(alarm.getCompanyTime() * 1000)));
					break;
				default:
					break;
			}
			if (vehicleMap.containsKey(vehicleId)) {
				com.xh.vdm.statistic.entity.BdmVehicle vehicle = vehicleMap.get(vehicleId);
				if ((vehicle != null) && (vehicle.getDeptId() != null)) {
					Dept dept = deptMap.get(vehicle.getDeptId());
					if ((dept != null) && StringUtils.isNotBlank(dept.getDeptName())) {
						response.setDeptName(dept.getDeptName());
					}
				}
				if ((vehicle != null) && StringUtils.isNotBlank(vehicle.getVehicleUseType())) {
					response.setVehicleUseType(vehicle.getVehicleUseType());
				}
				if ((vehicle != null) && StringUtils.isNotBlank(vehicle.getAccessMode())) {
					response.setAccessMode(vehicle.getAccessMode());
				}
				if ((vehicle != null) && (vehicle.getVehicleOwnerId() != null)) {
					Long ownerId = vehicle.getVehicleOwnerId();
					if (ownerMap.containsKey(ownerId)) {
						BamThirdPartyPlatform owner = ownerMap.get(ownerId);
						if ((owner != null) && StringUtils.isNotBlank(owner.getName())) {
							response.setVehicleOwnerName(owner.getName());
						}
					}
				}
			}

			responseList.add(response);
		}

		return R.success(ResultCode.SUCCESS);
	}

	private R<Object> formCommonAlarmStatExportResponse (List<ImpalaAlarm> alarmList, List<CommonAlarmStatResponse> responseList, CommonStatRequest request, BladeUser user) {
		R<Map<Long, Dept>> r = this.sysClient.getDeptChildWithOuterMapWithSelf(user.getUserId());
		if (!r.isSuccess()) {
			return R.fail(r.getCode(), r.getMsg());
		}

		Map<Long, Dept> deptMap = ((r.getData() == null) || r.getData().isEmpty()) ? new HashMap<>() : r.getData();
		Map<Integer, com.xh.vdm.statistic.entity.BdmVehicle> vehicleMap = this.bdmVehicleService.getVehicleMap(request);
		Map<String, String> terminalMap = this.terminalService.getTerminalMap();
		Map<Long, BamThirdPartyPlatform> ownerMap = new HashMap<>();
		List<BamThirdPartyPlatform> ownerList = this.bamThirdPartyPlatformService.list();
		if ((ownerList != null) && (!ownerList.isEmpty())) {
			ownerMap = ownerList.parallelStream().collect(Collectors.toMap(BamThirdPartyPlatform::getId, owner -> owner));
		}

		R<Map<String, String>> RLicenceColor = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_LICENCE_COLOR, "-1");
		if (!RLicenceColor.isSuccess()) {
			return R.fail(RLicenceColor.getCode(), RLicenceColor.getMsg());
		}

		R<Map<String, String>> RAlarmType = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_ALARM_TYPE, "16");
		if (!RAlarmType.isSuccess()) {
			return R.fail(RAlarmType.getCode(), RAlarmType.getMsg());
		}

		R<Map<String, String>> RAlarmLevel = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_ALARM_LEVEL, "-1");
		if (!RAlarmLevel.isSuccess()) {
			return R.fail(RAlarmLevel.getCode(), RAlarmLevel.getMsg());
		}

		R<Map<String, String>> RVehicleUseType = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_VEHICLE_USE_TYPE, "-1");
		if (!RVehicleUseType.isSuccess()) {
			return R.fail(RVehicleUseType.getCode(), RVehicleUseType.getMsg());
		}

		R<Map<String, String>> RAccessMode = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_ACCESS_MODE, "-1");
		if (!RAccessMode.isSuccess()) {
			return R.fail(RAccessMode.getCode(), RAccessMode.getMsg());
		}

		R<Map<String, String>> RRoadType = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_ROAD_TYPE, "-1");
		if (!RRoadType.isSuccess()) {
			return R.fail(RRoadType.getCode(), RRoadType.getMsg());
		}

		R<Map<String, String>> RTerminalType = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_TERMINAL_TYPE, "-1");
		if (!RTerminalType.isSuccess()) {
			return R.fail(RTerminalType.getCode(), RTerminalType.getMsg());
		}

		Map<String, String> licenceColorMap = ((RLicenceColor.getData() == null) || RLicenceColor.getData().isEmpty()) ? new HashMap<>() : RLicenceColor.getData();
		Map<String, String> alarmTypeMap = ((RAlarmType.getData() == null) || RAlarmType.getData().isEmpty()) ? new HashMap<>() : RAlarmType.getData();
		Map<String, String> alarmLevelMap = ((RAlarmLevel.getData() == null) || RAlarmLevel.getData().isEmpty()) ? new HashMap<>() : RAlarmLevel.getData();
		Map<String, String> vehicleUseTypeMap = ((RVehicleUseType.getData() == null) || RVehicleUseType.getData().isEmpty()) ? new HashMap<>() : RVehicleUseType.getData();
		Map<String, String> accessModeMap = ((RAccessMode.getData() == null) || RAccessMode.getData().isEmpty()) ? new HashMap<>() : RAccessMode.getData();
		Map<String, String> roadTypeMap = ((RRoadType.getData() == null) || RRoadType.getData().isEmpty()) ? new HashMap<>() : RRoadType.getData();
		Map<String, String> terminalTypeMap = ((RTerminalType.getData() == null) || RTerminalType.getData().isEmpty()) ? new HashMap<>() : RTerminalType.getData();
		for (ImpalaAlarm alarm : alarmList) {
			CommonAlarmStatResponse response = new CommonAlarmStatResponse();
			BeanUtils.copyProperties(alarm, response);
			response.setStartAddress(alarm.getAlarmAddress());
			response.setEndAddress(alarm.getAlarmEndAddress());
			response.setLicenceColor(licenceColorMap.getOrDefault(alarm.getLicenceColor(), ""));
			response.setAlarmType(alarmTypeMap.getOrDefault(alarm.getAlarmType().toString(), ""));
			response.setAlarmLevel(alarmLevelMap.getOrDefault(alarm.getAlarmLevel().toString(), ""));
			response.setRoadType(roadTypeMap.getOrDefault(alarm.getRoadType(), ""));
			response.setDurationTimeStr(this.getChineseTimeStr(alarm.getAlarmEndTime() - alarm.getAlarmTime()));
			response.setOverSpeedPercent(((alarm.getMaxSpeed() == null) || (alarm.getLimitSpeed() == null)) ? "0" : this.getRate(alarm.getMaxSpeed() - alarm.getLimitSpeed(), alarm.getLimitSpeed()));
			response.setStartTime(this.timeFormat.format(new Date(alarm.getAlarmTime() * 1000)));
			response.setEndTime(this.timeFormat.format(new Date(alarm.getAlarmEndTime() * 1000)));
			response.setDrivingTime((alarm.getDrivingTime() == null) ? "" : this.timeFormat.format(new Date(alarm.getDrivingTime() * 1000)));
			response.setDeptName("");
			response.setVehicleUseType("");
			response.setVehicleOwnerName("非营运车辆");
			response.setAccessMode("");
			response.setTerminalType("");
			response.setDealMeasures("");
			response.setDealContent("");
			response.setDealTime("");
			String serviceRole = request.getServiceRole();
			int vehicleId = alarm.getVehicleId();
			String phone = alarm.getPhone();
			switch (serviceRole) {
				case "1":
					response.setDealMeasures(alarm.getServerMeasures());
					response.setDealContent(alarm.getServerContent());
					response.setDealTime((alarm.getServerTime() == null) ? "" : this.timeFormat.format(new Date(alarm.getServerTime() * 1000)));
					break;
				case "2":
					response.setDealMeasures(alarm.getThirdMeasures());
					response.setDealContent(alarm.getThirdContent());
					response.setDealTime((alarm.getThirdTime() == null) ? "" : this.timeFormat.format(new Date(alarm.getThirdTime() * 1000)));
					break;
				case "3":
					response.setDealMeasures(alarm.getCompanyMeasures());
					response.setDealContent(alarm.getCompanyContent());
					response.setDealTime((alarm.getCompanyTime() == null) ? "" : this.timeFormat.format(new Date(alarm.getCompanyTime() * 1000)));
					break;
				default:
					break;
			}
			if (vehicleMap.containsKey(vehicleId)) {
				com.xh.vdm.statistic.entity.BdmVehicle vehicle = vehicleMap.get(vehicleId);
				if ((vehicle != null) && (vehicle.getDeptId() != null)) {
					Dept dept = deptMap.get(vehicle.getDeptId());
					if ((dept != null) && StringUtils.isNotBlank(dept.getDeptName())) {
						response.setDeptName(dept.getDeptName());
					}
				}
				if ((vehicle != null) && StringUtils.isNotBlank(vehicle.getVehicleUseType())) {
					response.setVehicleUseType(vehicleUseTypeMap.getOrDefault(vehicle.getVehicleUseType(), ""));
				}
				if ((vehicle != null) && StringUtils.isNotBlank(vehicle.getAccessMode())) {
					response.setAccessMode(accessModeMap.getOrDefault(vehicle.getAccessMode(), ""));
				}
				if ((vehicle != null) && (vehicle.getVehicleOwnerId() != null)) {
					Long ownerId = vehicle.getVehicleOwnerId();
					if (ownerMap.containsKey(ownerId)) {
						BamThirdPartyPlatform owner = ownerMap.get(ownerId);
						if ((owner != null) && StringUtils.isNotBlank(owner.getName())) {
							response.setVehicleOwnerName(owner.getName());
						}
					}
				}
			}
			if (terminalMap.containsKey(phone)) {
				response.setTerminalType(terminalTypeMap.getOrDefault(terminalMap.get(phone), ""));
			}

			responseList.add(response);
		}

		return R.success(ResultCode.SUCCESS);
	}

	private String getRate (int numerator, int denominator) {
		if (denominator == 0) {
			return "--";
		} else {
			return String.format("%.2f", numerator * 100.00 / denominator) + "%";
		}
	}

	private String getRate (double numerator, double denominator) {
		if (denominator == 0) {
			return "0.00%";
		} else {
			return String.format("%.2f", numerator * 100 / denominator) + "%";
		}
	}

	private String getChineseTimeStr (long duration) {
		long durationDay = duration / 86400;
		long durationHour = (duration - (durationDay * 86400)) / 3600;
		long durationMinute = (duration - ((durationDay * 86400) + (durationHour * 3600))) / 60;
		long durationSecond = duration - ((durationDay * 86400) + (durationHour * 3600) + (durationMinute * 60));
		StringBuffer sb = new StringBuffer();
		if (durationDay > 0) {
			sb.append(durationDay).append("天");
		}
		if (durationHour > 0) {
			sb.append(durationHour).append("小时");
		}
		if (durationMinute > 0) {
			sb.append(durationMinute).append("分");
		}
		if (durationSecond > 0) {
			sb.append(durationSecond).append("秒");
		}

		return (sb.length() <= 0) ? "0秒" : sb.toString();
	}

	@ApiOperation(value = "超速分页", httpMethod = "POST")
	@PostMapping("over-speed/page")
	public R<IPage<CommonAlarmStatResponse>> overSpeedPage (@Validated @RequestBody CommonAlarmStatRequest request, Query query, BladeUser user) {
		R<Object> r1 = this.addDataRangeForRequest(request, user);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		IPage<ImpalaAlarm> page = this.alarmService.overSpeedPage(request, query);
		IPage<CommonAlarmStatResponse> res = new Page<>();
		BeanUtils.copyProperties(page, res);
		if (page.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return R.data(ResultCode.SUCCESS.getCode(), res, "");
		}

		List<CommonAlarmStatResponse> responseList = new ArrayList<>();
		R<Object> r2 = this.formCommonAlarmStatPageResponse(page.getRecords(), responseList, request, user);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		res.setRecords(responseList);
		return R.data(ResultCode.SUCCESS.getCode(), res, "");
	}

	@ApiOperation(value = "超速导出", httpMethod = "POST")
	@PostMapping("over-speed/export")
	public R<String> exportOverSpeed (@Validated @RequestBody CommonAlarmStatRequest request, BladeUser user) {
		R<Object> r1;
		if (request.getDeptId() != null) {
			r1 = this.addDataRangeForMail(request);
		} else {
			r1 = this.addDataRangeForRequest(request, user);
		}
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<ImpalaAlarm> alarmList = this.alarmService.overSpeedList(request);
		List responseList = new ArrayList<>();
		R<Object> r2 = this.formCommonAlarmStatExportResponse(alarmList, responseList, request, user);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		try {
			return R.data(
				ResultCode.SUCCESS.getCode(),
				EasyExcelUtils.exportExcelDynamicsHead(this.staticFilePath, this.proxyFilePath, "超速", responseList , request.getHeadNameList(), request.getColumnNameList(), CommonAlarmStatResponse.class, false),
				""
			);
		} catch (FileNotFoundException e) {
			log.error(e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}
	}

	@ApiOperation(value = "疲劳驾驶分页", httpMethod = "POST")
	@PostMapping("fatigue-drive/page")
	public R<IPage<CommonAlarmStatResponse>> fatigueDrivePage (@Validated @RequestBody CommonStatRequest request, Query query, BladeUser user) {
		R<Object> r1 = this.addDataRangeForRequest(request, user);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		IPage<ImpalaAlarm> page = this.alarmService.fatigueDrivePage(request, query);
		IPage<CommonAlarmStatResponse> res = new Page<>();
		BeanUtils.copyProperties(page, res);
		if (page.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return R.data(ResultCode.SUCCESS.getCode(), res, "");
		}

		List<CommonAlarmStatResponse> responseList = new ArrayList<>();
		R<Object> r2 = this.formCommonAlarmStatPageResponse(page.getRecords(), responseList, request, user);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		res.setRecords(responseList);
		return R.data(ResultCode.SUCCESS.getCode(), res, "");
	}

	@ApiOperation(value = "疲劳驾驶导出", httpMethod = "POST")
	@PostMapping("fatigue-drive/export")
	public R<String> exportFatigueDrive (@Validated @RequestBody CommonStatRequest request, BladeUser user) {
		R<Object> r1;
		if (request.getDeptId() != null) {
			r1 = this.addDataRangeForMail(request);
		} else {
			r1 = this.addDataRangeForRequest(request, user);
		}
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<ImpalaAlarm> alarmList = this.alarmService.fatigueDriveList(request);
		List responseList = new ArrayList<>();
		R<Object> r2 = this.formCommonAlarmStatExportResponse(alarmList, responseList, request, user);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		try {
			return R.data(
				ResultCode.SUCCESS.getCode(),
				EasyExcelUtils.exportExcelDynamicsHead(this.staticFilePath, this.proxyFilePath, "疲劳驾驶", responseList , request.getHeadNameList(), request.getColumnNameList(), CommonAlarmStatResponse.class, false),
				""
			);
		} catch (FileNotFoundException e) {
			log.error(e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}
	}

	@ApiOperation(value = "夜间异动分页", httpMethod = "POST")
	@PostMapping("night-drive/page")
	public R<IPage<CommonAlarmStatResponse>> NightDrivePage (@Validated @RequestBody CommonStatRequest request, Query query, BladeUser user) {

		try{
			R<Object> r1 = this.addDataRangeForRequest(request, user);
			if (!r1.isSuccess()) {
				return R.fail(r1.getCode(), r1.getMsg());
			}

			IPage<ImpalaAlarm> page = this.alarmService.nightDrivePage(request, query);
			IPage<CommonAlarmStatResponse> res = new Page<>();
			BeanUtils.copyProperties(page, res);
			if (page.getTotal() <= 0) {
				res.setRecords(new ArrayList<>());
				return R.data(ResultCode.SUCCESS.getCode(), res, "");
			}

			List<CommonAlarmStatResponse> responseList = new ArrayList<>();
			R<Object> r2 = this.formCommonAlarmStatPageResponse(page.getRecords(), responseList, request, user);
			if (!r2.isSuccess()) {
				return R.fail(r2.getCode(), r2.getMsg());
			}

			res.setRecords(responseList);
			return R.data(ResultCode.SUCCESS.getCode(), res, "");
		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询报错");
		}
	}

	@ApiOperation(value = "夜间异动导出", httpMethod = "POST")
	@PostMapping("night-drive/export")
	public R<String> exportNightDrive (@Validated @RequestBody CommonStatRequest request, BladeUser user) {
		R<Object> r1;
		if (request.getDeptId() != null) {
			r1 = this.addDataRangeForMail(request);
		} else {
			r1 = this.addDataRangeForRequest(request, user);
		}
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<ImpalaAlarm> alarmList = this.alarmService.nightDriveList(request);
		List responseList = new ArrayList<>();
		R<Object> r2 = this.formCommonAlarmStatExportResponse(alarmList, responseList, request, user);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		try {
			return R.data(
				ResultCode.SUCCESS.getCode(),
				EasyExcelUtils.exportExcelDynamicsHead(this.staticFilePath, this.proxyFilePath, "夜间异动", responseList , request.getHeadNameList(), request.getColumnNameList(), CommonAlarmStatResponse.class, false),
				""
			);
		} catch (FileNotFoundException e) {
			log.error(e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}
	}

	@ApiOperation(value = "误报报警分页", httpMethod = "POST")
	@PostMapping("wrong/page")
	public R<IPage<CommonAlarmStatResponse>> wrongPage (@Validated @RequestBody CommonAlarmStatRequest request, Query query, BladeUser user) {
		R<Object> r1 = this.addDataRangeForRequest(request, user);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		IPage<ImpalaAlarm> page = this.alarmService.wrongPage(request, query);
		IPage<CommonAlarmStatResponse> res = new Page<>();
		BeanUtils.copyProperties(page, res);
		if (page.getTotal() <= 0) {
			res.setRecords(new ArrayList<>());
			return R.data(ResultCode.SUCCESS.getCode(), res, "");
		}

		List<CommonAlarmStatResponse> responseList = new ArrayList<>();
		R<Object> r2 = this.formCommonAlarmStatPageResponse(page.getRecords(), responseList, request, user);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		res.setRecords(responseList);
		return R.data(ResultCode.SUCCESS.getCode(), res, "");
	}

	@ApiOperation(value = "误报报警导出", httpMethod = "POST")
	@PostMapping("wrong/export")
	public R<String> exportWrong (@Validated @RequestBody CommonAlarmStatRequest request, BladeUser user) {
		R<Object> r1;
		if (request.getDeptId() != null) {
			r1 = this.addDataRangeForMail(request);
		} else {
			r1 = this.addDataRangeForRequest(request, user);
		}
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<ImpalaAlarm> alarmList = this.alarmService.wrongList(request);
		List responseList = new ArrayList<>();
		R<Object> r2 = this.formCommonAlarmStatExportResponse(alarmList, responseList, request, user);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		try {
			return R.data(
				ResultCode.SUCCESS.getCode(),
				EasyExcelUtils.exportExcelDynamicsHead(this.staticFilePath, this.proxyFilePath, "误报报警", responseList , request.getHeadNameList(), request.getColumnNameList(), CommonAlarmStatResponse.class, false),
				""
			);
		} catch (FileNotFoundException e) {
			log.error(e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}
	}

	@ApiOperation(value = "车辆报警统计分页列表", httpMethod = "POST")
	@PostMapping("car-alarm-stat/page")
	public R<IPage<Map<String, String>>> carAlarmStatPage (@Validated @RequestBody CarAlarmRequest request, Query query, BladeUser user) {
		R<Object> r1 = this.addDataRangeForRequest(request, user);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<AlarmCount> carAlarmStatList = this.alarmService.getCarAlarmStatList(request);
		IPage<Map<String, String>> page = new Page<>();
		page.setTotal(0);
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		if (CollectionUtils.isEmpty(carAlarmStatList)) {
			return R.data(ResultCode.SUCCESS.getCode(), page, "");
		}

		R<Map<Long, Dept>> r2 = this.sysClient.getDeptChildWithOuterMapWithSelf(user.getUserId());
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		Map<Long, Dept> deptMap = ((r2.getData() == null) || r2.getData().isEmpty()) ? new HashMap<>() : r2.getData();
		Map<Integer, com.xh.vdm.statistic.entity.BdmVehicle> vehicleMap = this.bdmVehicleService.getVehicleMap(request);
		Map<Long, BamThirdPartyPlatform> ownerMap = new HashMap<>();
		List<BamThirdPartyPlatform> ownerList = this.bamThirdPartyPlatformService.list();
		if (CollectionUtils.isNotEmpty(ownerList)) {
			ownerMap = ownerList.parallelStream().collect(Collectors.toMap(BamThirdPartyPlatform::getId, owner -> owner));
		}

		Map<Integer, Map<String, String>> responseMap = new HashMap<>();
		for (AlarmCount carAlarmStat : carAlarmStatList) {
			int vehicleId = carAlarmStat.getVehicleId();
			if (!responseMap.containsKey(vehicleId)) {
				Map<String, String> response = new HashMap<>();
				response.put("dept_name", "");
				response.put("vehicle_id", String.valueOf(vehicleId));
				response.put("licence_color", "");
				response.put("licence_plate", "");
				response.put("vehicle_use_type", "");
				response.put("vehicle_owner_name", "非营运车辆");
				response.put("access_mode", "");
				response.put("num_alarm", "0");
				response.put("num_over_speed", "0");
				response.put("num_fatigue_drive", "0");
				response.put("num_night_drive", "0");
				if (vehicleMap.containsKey(vehicleId)) {
					com.xh.vdm.statistic.entity.BdmVehicle vehicle = vehicleMap.get(vehicleId);
					response.put("licence_color", vehicle.getLicenceColor());
					response.put("licence_plate", vehicle.getLicencePlate());
					response.put("vehicle_use_type", vehicle.getVehicleUseType());
					response.put("access_mode", vehicle.getAccessMode());
					Long deptId = vehicle.getDeptId();
					Long ownerId = vehicle.getVehicleOwnerId();
					if ((deptId != null) && deptMap.containsKey(deptId)) {
						Dept dept = deptMap.get(deptId);
						if ((dept != null) && StringUtils.isNotBlank(dept.getDeptName())) {
							response.put("dept_name", dept.getDeptName());
						}
					}
					if ((ownerId != null) && ownerMap.containsKey(ownerId)) {
						BamThirdPartyPlatform owner = ownerMap.get(ownerId);
						if ((owner != null) && StringUtils.isNotBlank(owner.getName())) {
							response.put("vehicle_owner_name", owner.getName());
						}
					}
				}

				responseMap.put(vehicleId, response);
			}

			Map<String, String> response = responseMap.get(vehicleId);
			switch (carAlarmStat.getAlarmType()) {
				case 1:
				case 100:
				case 101:
				case 105:
					response.put("num_over_speed", String.valueOf(Integer.parseInt(response.get("num_over_speed")) + carAlarmStat.getNumAlarm()));
					response.put("num_alarm", String.valueOf(Integer.parseInt(response.get("num_alarm")) + carAlarmStat.getNumAlarm()));
					break;
				case 2:
//				case 102:
//				case 161:
					response.put("num_fatigue_drive", String.valueOf(Integer.parseInt(response.get("num_fatigue_drive")) + carAlarmStat.getNumAlarm()));
					response.put("num_alarm", String.valueOf(Integer.parseInt(response.get("num_alarm")) + carAlarmStat.getNumAlarm()));
					break;
				case 103:
					response.put("num_night_drive", String.valueOf(Integer.parseInt(response.get("num_night_drive")) + carAlarmStat.getNumAlarm()));
					response.put("num_alarm", String.valueOf(Integer.parseInt(response.get("num_alarm")) + carAlarmStat.getNumAlarm()));
					break;
				default:
					break;
			}
		}

		List<Map<String, String>> responseList = responseMap.values().parallelStream().collect(Collectors.toList());
		page.setTotal(responseList.size());
		int fromIdx = query.getSize() * (query.getCurrent() - 1);
		if (fromIdx >= responseList.size()) {
			return R.data(ResultCode.SUCCESS.getCode(), page, "");
		}

		int endIdx = fromIdx + query.getSize();
		endIdx = Math.min(endIdx, responseList.size());
		page.setRecords(responseList.subList(fromIdx, endIdx));
		return R.data(ResultCode.SUCCESS.getCode(), page, "");
	}

	@ApiOperation(value = "车辆报警统计列表", httpMethod = "POST")
	@PostMapping("car-alarm-stat/list")
	public R<List<Map<String, String>>> carAlarmStatList (@Validated @RequestBody CarAlarmRequest request, BladeUser user) {
		R<Object> r1 = this.addDataRangeForRequest(request, user);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<AlarmCount> carAlarmStatList = this.alarmService.getCarAlarmStatList(request);
		if ((carAlarmStatList == null) || carAlarmStatList.isEmpty()) {
			return R.data(ResultCode.SUCCESS.getCode(), new ArrayList<>(), "");
		}

		R<Map<Long, Dept>> r2 = this.sysClient.getDeptChildWithOuterMapWithSelf(user.getUserId());
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		Map<Long, Dept> deptMap = ((r2.getData() == null) || r2.getData().isEmpty()) ? new HashMap<>() : r2.getData();
		Map<Integer, com.xh.vdm.statistic.entity.BdmVehicle> vehicleMap = this.bdmVehicleService.getVehicleMap(request);
		Map<Long, BamThirdPartyPlatform> ownerMap = new HashMap<>();
		List<BamThirdPartyPlatform> ownerList = this.bamThirdPartyPlatformService.list();
		if ((ownerList != null) && (!ownerList.isEmpty())) {
			ownerMap = ownerList.parallelStream().collect(Collectors.toMap(BamThirdPartyPlatform::getId, owner -> owner));
		}

		Map<Integer, Map<String, String>> responseMap = new HashMap<>();
		for (AlarmCount carAlarmStat : carAlarmStatList) {
			int vehicleId = carAlarmStat.getVehicleId();
			if (!responseMap.containsKey(vehicleId)) {
				Map<String, String> response = new HashMap<>();
				response.put("dept_name", "");
				response.put("vehicle_id", String.valueOf(vehicleId));
				response.put("licence_color", "");
				response.put("licence_plate", "");
				response.put("vehicle_use_type", "");
				response.put("vehicle_owner_name", "非营运车辆");
				response.put("access_mode", "");
				response.put("num_alarm", "0");
				if (vehicleMap.containsKey(vehicleId)) {
					com.xh.vdm.statistic.entity.BdmVehicle vehicle = vehicleMap.get(vehicleId);
					response.put("licence_color", vehicle.getLicenceColor());
					response.put("licence_plate", vehicle.getLicencePlate());
					response.put("vehicle_use_type", vehicle.getVehicleUseType());
					response.put("access_mode", vehicle.getAccessMode());
					Long deptId = vehicle.getDeptId();
					Long ownerId = vehicle.getVehicleOwnerId();
					if ((deptId != null) && deptMap.containsKey(deptId)) {
						Dept dept = deptMap.get(deptId);
						if ((dept != null) && StringUtils.isNotBlank(dept.getDeptName())) {
							response.put("dept_name", dept.getDeptName());
						}
					}
					if ((ownerId != null) && ownerMap.containsKey(ownerId)) {
						BamThirdPartyPlatform owner = ownerMap.get(ownerId);
						if ((owner != null) && StringUtils.isNotBlank(owner.getName())) {
							response.put("vehicle_owner_name", owner.getName());
						}
					}
				}

				responseMap.put(vehicleId, response);
			}

			Map<String, String> response = responseMap.get(vehicleId);
			response.put(carAlarmStat.getAlarmType().toString(), carAlarmStat.getNumAlarm().toString());
			response.put("num_alarm", String.valueOf(Integer.parseInt(response.get("num_alarm")) + carAlarmStat.getNumAlarm()));
		}

		return R.data(ResultCode.SUCCESS.getCode(), responseMap.values().parallelStream().collect(Collectors.toList()), "");
	}

	@ApiOperation(value = "车辆报警分析列表", httpMethod = "POST")
	@PostMapping("car-alarm-analysis/list")
	public R<List<CommonAlarmStatResponse>> carAlarmAnalysisList (@Validated @RequestBody CarAlarmRequest request, BladeUser user) {
		R<Object> r1 = this.addDataRangeForRequest(request, user);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<ImpalaAlarm> carAlarmAnalysisList = this.alarmService.getCarAlarmAnalysisList(request);
		if ((carAlarmAnalysisList == null) || carAlarmAnalysisList.isEmpty()) {
			return R.data(ResultCode.SUCCESS.getCode(), new ArrayList<>(), "");
		}

		List<CommonAlarmStatResponse> responseList = new ArrayList<>();
		R<Object> r2 = this.formCommonAlarmStatPageResponse(carAlarmAnalysisList, responseList, request, user);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		return R.data(ResultCode.SUCCESS.getCode(), responseList, "");
	}

	@ApiOperation(value = "车辆报警统计导出", httpMethod = "POST")
	@PostMapping("car-alarm-stat/export")
	public R<String> exportCarAlarmStat (@Validated @RequestBody CarAlarmRequest request, BladeUser user) {
		R<Object> r1;
		if (request.getDeptId() != null) {
			r1 = this.addDataRangeForMail(request);
		} else {
			r1 = this.addDataRangeForRequest(request, user);
		}
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<AlarmCount> carAlarmStatList = this.alarmService.getCarAlarmStatList(request);
		if ((carAlarmStatList == null) || carAlarmStatList.isEmpty()) {
			return R.fail(ResultCode.FAILURE.getCode(), "没有符合条件的数据。");
		}

		R<Map<String, String>> RLicenceColor = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_LICENCE_COLOR, "-1");
		if (!RLicenceColor.isSuccess()) {
			return R.fail(RLicenceColor.getCode(), RLicenceColor.getMsg());
		}

		R<Map<String, String>> RVehicleUseType = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_VEHICLE_USE_TYPE, "-1");
		if (!RVehicleUseType.isSuccess()) {
			return R.fail(RVehicleUseType.getCode(), RVehicleUseType.getMsg());
		}

		R<Map<String, String>> RAccessMode = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_ACCESS_MODE, "-1");
		if (!RAccessMode.isSuccess()) {
			return R.fail(RAccessMode.getCode(), RAccessMode.getMsg());
		}

		R<Map<Long, Dept>> r2 = this.sysClient.getDeptChildWithOuterMapWithSelf(user.getUserId());
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		Map<String, String> licenceColorMap = ((RLicenceColor.getData() == null) || RLicenceColor.getData().isEmpty()) ? new HashMap<>() : RLicenceColor.getData();
		Map<String, String> vehicleUseTypeMap = ((RVehicleUseType.getData() == null) || RVehicleUseType.getData().isEmpty()) ? new HashMap<>() : RVehicleUseType.getData();
		Map<String, String> accessModeMap = ((RAccessMode.getData() == null) || RAccessMode.getData().isEmpty()) ? new HashMap<>() : RAccessMode.getData();
		Map<Long, Dept> deptMap = ((r2.getData() == null) || r2.getData().isEmpty()) ? new HashMap<>() : r2.getData();
		Map<Integer, com.xh.vdm.statistic.entity.BdmVehicle> vehicleMap = this.bdmVehicleService.getVehicleMap(request);
		Map<Long, BamThirdPartyPlatform> ownerMap = new HashMap<>();
		List<BamThirdPartyPlatform> ownerList = this.bamThirdPartyPlatformService.list();
		if ((ownerList != null) && (!ownerList.isEmpty())) {
			ownerMap = ownerList.parallelStream().collect(Collectors.toMap(BamThirdPartyPlatform::getId, owner -> owner));
		}

		Map<Integer, ExportCarAlarmStatResponse> responseMap = new HashMap<>();
		for (AlarmCount carAlarmStat : carAlarmStatList) {
			int vehicleId = carAlarmStat.getVehicleId();
			if (!responseMap.containsKey(vehicleId)) {
				ExportCarAlarmStatResponse response = new ExportCarAlarmStatResponse();
				response.setDeptName("");
				response.setLicenceColor("");
				response.setLicencePlate("");
				response.setVehicleUseType("");
				response.setVehicleOwnerName("非营运车辆");
				response.setAccessMode("");
				response.setNumAlarm(0);
				response.setNumEmergency(0);
				response.setNumOverSpeedTerminal(0);
				response.setNumOverSpeedWarn(0);
				response.setNumOverSpeedSegment(0);
				response.setNumOverSpeedNight(0);
				response.setNumOverSpeedRoad(0);
				response.setNumFatigueAccumulate(0);
				response.setNumViolate(0);
				response.setNumForbidDrive(0);
				response.setNumFatigueTerminal(0);
				response.setNumFatigueWarn(0);
				response.setNumOverTimeDrive(0);
				response.setNumOverTimePark(0);
				response.setNumFatiguePlatform(0);
				response.setNumNightDrive(0);
				response.setNumAreaInOut(0);
				response.setNumRouteInOut(0);
				response.setNumRouteDriveTimeBug(0);
				response.setNumRouteDeviate(0);
				response.setNumOverSpeedTerminalAddition(0);
				response.setNumAreaOrRouteInOut(0);
				response.setNumAdditionRouteDriveTimeBug(0);
				response.setNumForbidIn(0);
				response.setNumForbidOut(0);
				response.setNumStepIn(0);
				response.setNumStepOut(0);
				response.setNumStepInOut(0);
				response.setNumAdasBug(0);
				response.setNumCarCollision(0);
				response.setNumLaneDeviate(0);
				response.setNumCloseDistance(0);
				response.setNumPedestrianCollision(0);
				response.setNumFrequentChangeLane(0);
				response.setNumOverSizeOrWeight(0);
				response.setNumObstacle(0);
				response.setNumSignRecognition(0);
				response.setNumActiveCapture(0);
				response.setNumOvertakeChangeLane(0);
				response.setNumDangerousDrive(0);
				response.setNumBodyFatigue(0);
				response.setNumPhone(0);
				response.setNumSmoke(0);
				response.setNumDistraction(0);
				response.setNumNoDriver(0);
				response.setNumChangeDriver(0);
				response.setNumSteeringWheelWithoutControl(0);
				response.setNumDsmBug(0);
				response.setNumAutoCapture(0);
				response.setNumUrgentAccelerate(0);
				response.setNumUrgentModerate(0);
				response.setNumUrgentSwerve(0);
				response.setNumIdle(0);
				response.setNumGnssBug(0);
				response.setNumAntennaOff(0);
				response.setNumAntennaShortCircuit(0);
				response.setNumPowerUnderVoltage(0);
				response.setNumPowerOff(0);
				response.setNumScreenBug(0);
				response.setNumTtsBug(0);
				response.setNumCameraBug(0);
				response.setNumCardBug(0);
				response.setNumRightBlindAreaBug(0);
				response.setNumVss(0);
				response.setNumGasBug(0);
				response.setNumStolen(0);
				response.setNumInvalidOn(0);
				response.setNumInvalidMove(0);
				response.setNumCollisionRollover(0);
				response.setNumRollover(0);
				response.setNumStoreBug(0);
				response.setNumVideoLost(0);
				response.setNumVideoCover(0);
				response.setNumDeviceVideoLost(0);
				response.setNumDeviceVideoCover(0);
				response.setNumStoreUnitBug(0);
				response.setNumVideoDeviceBug(0);
				response.setNumOverload(0);
				response.setNumAbnormalDrive(0);
				response.setNumOverStore(0);
				response.setNumMoveWhileStop(0);
				if (vehicleMap.containsKey(vehicleId)) {
					com.xh.vdm.statistic.entity.BdmVehicle vehicle = vehicleMap.get(vehicleId);
					response.setLicencePlate(vehicle.getLicencePlate());
					response.setLicenceColor(licenceColorMap.getOrDefault(vehicle.getLicenceColor(), ""));
					response.setVehicleUseType(vehicleUseTypeMap.getOrDefault(vehicle.getVehicleUseType(), ""));
					response.setAccessMode(accessModeMap.getOrDefault(vehicle.getAccessMode(), ""));
					Long deptId = vehicle.getDeptId();
					Long ownerId = vehicle.getVehicleOwnerId();
					if ((deptId != null) && deptMap.containsKey(deptId)) {
						Dept dept = deptMap.get(deptId);
						if ((dept != null) && StringUtils.isNotBlank(dept.getDeptName())) {
							response.setDeptName(dept.getDeptName());
						}
					}
					if ((ownerId != null) && ownerMap.containsKey(ownerId)) {
						BamThirdPartyPlatform owner = ownerMap.get(ownerId);
						if ((owner != null) && StringUtils.isNotBlank(owner.getName())) {
							response.setVehicleOwnerName(owner.getName());
						}
					}
				}

				responseMap.put(vehicleId, response);
			}

			ExportCarAlarmStatResponse response = responseMap.get(vehicleId);
			int num = carAlarmStat.getNumAlarm().intValue();
			response.setNumAlarm(response.getNumAlarm() + num);
			switch (carAlarmStat.getAlarmType()) {
				case 0:
					response.setNumEmergency(num);
					break;
				case 1:
					response.setNumOverSpeedTerminal(num);
					break;
				case 13:
					response.setNumOverSpeedWarn(num);
					break;
				case 100:
					response.setNumOverSpeedSegment(num);
					break;
				case 101:
					response.setNumOverSpeedNight(num);
					break;
				case 105:
					response.setNumOverSpeedRoad(num);
					break;
				case 111:
					response.setNumFatigueAccumulate(num);
					break;
				case 15:
					response.setNumViolate(num);
					break;
				case 104:
					response.setNumForbidDrive(num);
					break;
				case 2:
					response.setNumFatigueTerminal(num);
					break;
				case 14:
					response.setNumFatigueWarn(num);
					break;
				case 18:
					response.setNumOverTimeDrive(num);
					break;
				case 19:
					response.setNumOverTimePark(num);
					break;
				case 102:
					response.setNumFatiguePlatform(num);
					break;
				case 103:
					response.setNumNightDrive(num);
					break;
				case 20:
					response.setNumAreaInOut(num);
					break;
				case 21:
					response.setNumRouteInOut(num);
					break;
				case 22:
					response.setNumRouteDriveTimeBug(num);
					break;
				case 23:
					response.setNumRouteDeviate(num);
					break;
				case 63:
					response.setNumOverSpeedTerminalAddition(num);
					break;
				case 64:
					response.setNumAreaOrRouteInOut(num);
					break;
				case 65:
					response.setNumAdditionRouteDriveTimeBug(num);
					break;
				case 106:
					response.setNumForbidIn(num);
					break;
				case 107:
					response.setNumForbidOut(num);
					break;
				case 108:
					response.setNumStepIn(num);
					break;
				case 109:
					response.setNumStepOut(num);
					break;
				case 110:
					response.setNumStepInOut(num);
					break;
				case 274:
					response.setNumAdasBug(num);
					break;
				case 211:
					response.setNumCarCollision(num);
					break;
				case 212:
					response.setNumLaneDeviate(num);
					break;
				case 213:
					response.setNumCloseDistance(num);
					break;
				case 214:
					response.setNumPedestrianCollision(num);
					break;
				case 215:
					response.setNumFrequentChangeLane(num);
					break;
				case 216:
					response.setNumOverSizeOrWeight(num);
					break;
				case 217:
					response.setNumObstacle(num);
					break;
				case 218:
					response.setNumSignRecognition(num);
					break;
				case 219:
					response.setNumActiveCapture(num);
					break;
				case 181:
					response.setNumOvertakeChangeLane(num);
					break;
				case 3:
					response.setNumDangerousDrive(num);
					break;
				case 161:
					response.setNumBodyFatigue(num);
					break;
				case 162:
					response.setNumPhone(num);
					break;
				case 163:
					response.setNumSmoke(num);
					break;
				case 164:
					response.setNumDistraction(num);
					break;
				case 165:
					response.setNumNoDriver(num);
					break;
				case 166:
					response.setNumChangeDriver(num);
					break;
				case 167:
					response.setNumSteeringWheelWithoutControl(num);
					break;
				case 168:
					response.setNumDsmBug(num);
					break;
				case 220:
					response.setNumAutoCapture(num);
					break;
				case 171:
					response.setNumUrgentAccelerate(num);
					break;
				case 172:
					response.setNumUrgentModerate(num);
					break;
				case 173:
					response.setNumUrgentSwerve(num);
					break;
				case 174:
					response.setNumIdle(num);
					break;
				case 4:
					response.setNumGnssBug(num);
					break;
				case 5:
					response.setNumAntennaOff(num);
					break;
				case 6:
					response.setNumAntennaShortCircuit(num);
					break;
				case 7:
					response.setNumPowerUnderVoltage(num);
					break;
				case 8:
					response.setNumPowerOff(num);
					break;
				case 9:
					response.setNumScreenBug(num);
					break;
				case 10:
					response.setNumTtsBug(num);
					break;
				case 11:
					response.setNumCameraBug(num);
					break;
				case 12:
					response.setNumCardBug(num);
					break;
				case 17:
					response.setNumRightBlindAreaBug(num);
					break;
				case 24:
					response.setNumVss(num);
					break;
				case 25:
					response.setNumGasBug(num);
					break;
				case 26:
					response.setNumStolen(num);
					break;
				case 27:
					response.setNumInvalidOn(num);
					break;
				case 28:
					response.setNumInvalidMove(num);
					break;
				case 29:
					response.setNumCollisionRollover(num);
					break;
				case 30:
					response.setNumRollover(num);
					break;
				case 223:
					response.setNumStoreBug(num);
					break;
				case 239:
					response.setNumVideoLost(num);
					break;
				case 243:
					response.setNumVideoCover(num);
					break;
				case 247:
					response.setNumDeviceVideoLost(num);
					break;
				case 248:
					response.setNumDeviceVideoCover(num);
					break;
				case 249:
					response.setNumStoreUnitBug(num);
					break;
				case 250:
					response.setNumVideoDeviceBug(num);
					break;
				case 251:
					response.setNumOverload(num);
					break;
				case 252:
					response.setNumAbnormalDrive(num);
					break;
				case 253:
					response.setNumOverStore(num);
					break;
				case 112:
					response.setNumMoveWhileStop(num);
					break;
				default:
					break;
			}
		}

		List exportList = responseMap.values().parallelStream().collect(Collectors.toList());
		Map<String, String> columnNameMap = new HashMap<>();
		columnNameMap.put("0", "numEmergency");
		columnNameMap.put("1", "numOverSpeedTerminal");
		columnNameMap.put("13", "numOverSpeedWarn");
		columnNameMap.put("100", "numOverSpeedSegment");
		columnNameMap.put("101", "numOverSpeedNight");
		columnNameMap.put("105", "numOverSpeedRoad");
		columnNameMap.put("111", "numFatigueAccumulate");
		columnNameMap.put("15", "numViolate");
		columnNameMap.put("104", "numForbidDrive");
		columnNameMap.put("2", "numFatigueTerminal");
		columnNameMap.put("14", "numFatigueWarn");
		columnNameMap.put("18", "numOverTimeDrive");
		columnNameMap.put("19", "numOverTimePark");
		columnNameMap.put("102", "numFatiguePlatform");
		columnNameMap.put("103", "numNightDrive");
		columnNameMap.put("20", "numAreaInOut");
		columnNameMap.put("21", "numRouteInOut");
		columnNameMap.put("22", "numRouteDriveTimeBug");
		columnNameMap.put("23", "numRouteDeviate");
		columnNameMap.put("63", "numOverSpeedTerminalAddition");
		columnNameMap.put("64", "numAreaOrRouteInOut");
		columnNameMap.put("65", "numAdditionRouteDriveTimeBug");
		columnNameMap.put("106", "numForbidIn");
		columnNameMap.put("107", "numForbidOut");
		columnNameMap.put("108", "numStepIn");
		columnNameMap.put("109", "numStepOut");
		columnNameMap.put("110", "numStepInOut");
		columnNameMap.put("274", "numAdasBug");
		columnNameMap.put("211", "numCarCollision");
		columnNameMap.put("212", "numLaneDeviate");
		columnNameMap.put("213", "numCloseDistance");
		columnNameMap.put("214", "numPedestrianCollision");
		columnNameMap.put("215", "numFrequentChangeLane");
		columnNameMap.put("216", "numOverSizeOrWeight");
		columnNameMap.put("217", "numObstacle");
		columnNameMap.put("218", "numSignRecognition");
		columnNameMap.put("219", "numActiveCapture");
		columnNameMap.put("181", "numOvertakeChangeLane");
		columnNameMap.put("3", "numDangerousDrive");
		columnNameMap.put("161", "numBodyFatigue");
		columnNameMap.put("162", "numPhone");
		columnNameMap.put("163", "numSmoke");
		columnNameMap.put("164", "numDistraction");
		columnNameMap.put("165", "numNoDriver");
		columnNameMap.put("166", "numChangeDriver");
		columnNameMap.put("167", "numSteeringWheelWithoutControl");
		columnNameMap.put("168", "numDsmBug");
		columnNameMap.put("220", "numAutoCapture");
		columnNameMap.put("171", "numUrgentAccelerate");
		columnNameMap.put("172", "numUrgentModerate");
		columnNameMap.put("173", "numUrgentSwerve");
		columnNameMap.put("174", "numIdle");
		columnNameMap.put("4", "numGnssBug");
		columnNameMap.put("5", "numAntennaOff");
		columnNameMap.put("6", "numAntennaShortCircuit");
		columnNameMap.put("7", "numPowerUnderVoltage");
		columnNameMap.put("8", "numPowerOff");
		columnNameMap.put("9", "numScreenBug");
		columnNameMap.put("10", "numTtsBug");
		columnNameMap.put("11", "numCameraBug");
		columnNameMap.put("12", "numCardBug");
		columnNameMap.put("17", "numRightBlindAreaBug");
		columnNameMap.put("24", "numVss");
		columnNameMap.put("25", "numGasBug");
		columnNameMap.put("26", "numStolen");
		columnNameMap.put("27", "numInvalidOn");
		columnNameMap.put("28", "numInvalidMove");
		columnNameMap.put("29", "numCollisionRollover");
		columnNameMap.put("30", "numRollover");
		columnNameMap.put("223", "numStoreBug");
		columnNameMap.put("239", "numVideoLost");
		columnNameMap.put("243", "numVideoCover");
		columnNameMap.put("247", "numDeviceVideoLost");
		columnNameMap.put("248", "numDeviceVideoCover");
		columnNameMap.put("249", "numStoreUnitBug");
		columnNameMap.put("250", "numVideoDeviceBug");
		columnNameMap.put("251", "numOverload");
		columnNameMap.put("252", "numAbnormalDrive");
		columnNameMap.put("253", "numOverStore");
		columnNameMap.put("112", "numMoveWhileStop");

		List<String> columnList = new ArrayList<>();
		for (String column : request.getColumnNameList()) {
			columnList.add(columnNameMap.getOrDefault(column, column));
		}
		try {
			return R.data(
				ResultCode.SUCCESS.getCode(),
				EasyExcelUtils.exportExcelDynamicsHead(this.staticFilePath, this.proxyFilePath, "车辆报警统计", exportList , request.getHeadNameList(), columnList, ExportCarAlarmStatResponse.class, false),
				""
			);
		} catch (FileNotFoundException e) {
			log.error(e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}
	}

	@ApiOperation(value = "车辆报警分析导出", httpMethod = "POST")
	@PostMapping("car-alarm-analysis/export")
	public R<String> exportCarAlarmAnalysis (@Validated @RequestBody CarAlarmRequest request, BladeUser user) {
		R<Object> r1;
		if (request.getDeptId() != null) {
			r1 = this.addDataRangeForMail(request);
		} else {
			r1 = this.addDataRangeForRequest(request, user);
		}
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<ImpalaAlarm> carAlarmAnalysisList = this.alarmService.getCarAlarmAnalysisList(request);
		if ((carAlarmAnalysisList == null) || carAlarmAnalysisList.isEmpty()) {
			return R.fail(ResultCode.FAILURE.getCode(), "没有符合条件的数据。");
		}

		List responseList = new ArrayList<>();
		R<Object> r2 = this.formCommonAlarmStatExportResponse(carAlarmAnalysisList, responseList, request, user);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}
		try {
			return R.data(
				ResultCode.SUCCESS.getCode(),
				EasyExcelUtils.exportExcelDynamicsHead(this.staticFilePath, this.proxyFilePath, "车辆报警分析", responseList , request.getHeadNameList(), request.getColumnNameList(), CommonAlarmStatResponse.class, false),
				""
			);
		} catch (FileNotFoundException e) {
			log.error(e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}
	}

	@ApiOperation(value = "驾驶员报警统计列表", httpMethod = "POST")
	@PostMapping("driver-alarm-stat/list")
	public R<List<Map<String, String>>> driverAlarmStatList (@Validated @RequestBody DriverAlarmRequest request, BladeUser user) {
		R<Object> r1 = this.addDataRangeForRequest(request, user);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<AlarmCount> driverAlarmStatList = this.alarmService.getDriverAlarmStatList(request);
		if ((driverAlarmStatList == null) || driverAlarmStatList.isEmpty()) {
			return R.data(ResultCode.SUCCESS.getCode(), new ArrayList<>(), "");
		}

		R<Map<Long, Dept>> r2 = this.sysClient.getDeptChildWithOuterMapWithSelf(user.getUserId());
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		Map<Long, Dept> deptMap = ((r2.getData() == null) || r2.getData().isEmpty()) ? new HashMap<>() : r2.getData();
		Map<Integer, com.xh.vdm.statistic.entity.BdmVehicle> vehicleMap = this.bdmVehicleService.getVehicleMap(request);
		List<Map<String, String>> responseList = new ArrayList<>();
		for (AlarmCount driverAlarmStat : driverAlarmStatList) {
			long deptId = driverAlarmStat.getDeptId();
			int vehicleId = driverAlarmStat.getVehicleId();
			Map<String, String> response = new HashMap<>();
			response.put("dept_name", "");
			response.put("vehicle_id", driverAlarmStat.getVehicleId().toString());
			response.put("licence_color", "");
			response.put("licence_plate", "");
			response.put("id_card", driverAlarmStat.getIdCard());
			response.put("driver_name", driverAlarmStat.getDriverName());
			response.put("num_alarm", driverAlarmStat.getNumAlarm().toString());
			if (deptMap.containsKey(deptId)) {
				Dept dept = deptMap.get(deptId);
				if ((dept != null) && StringUtils.isNotBlank(dept.getDeptName())) {
					response.put("dept_name", dept.getDeptName());
				}
			}
			if (vehicleMap.containsKey(vehicleId)) {
				com.xh.vdm.statistic.entity.BdmVehicle vehicle = vehicleMap.get(vehicleId);
				response.put("licence_color", vehicle.getLicenceColor());
				response.put("licence_plate", vehicle.getLicencePlate());
			}

			responseList.add(response);
		}

		return R.data(ResultCode.SUCCESS.getCode(), responseList, "");
	}

	@ApiOperation(value = "驾驶员报警分析列表", httpMethod = "POST")
	@PostMapping("driver-alarm-analysis/list")
	public R<List<CommonAlarmStatResponse>> driverAlarmAnalysisList (@Validated @RequestBody DriverAlarmRequest request, BladeUser user) {
		R<Object> r1 = this.addDataRangeForRequest(request, user);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<ImpalaAlarm> driverAlarmAnalysisList = this.alarmService.getDriverAlarmAnalysisList(request);
		if ((driverAlarmAnalysisList == null) || driverAlarmAnalysisList.isEmpty()) {
			return R.data(ResultCode.SUCCESS.getCode(), new ArrayList<>(), "");
		}

		List<CommonAlarmStatResponse> responseList = new ArrayList<>();
		R<Object> r2 = this.formCommonAlarmStatPageResponse(driverAlarmAnalysisList, responseList, request, user);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		return R.data(ResultCode.SUCCESS.getCode(), responseList, "");
	}

	@ApiOperation(value = "驾驶员报警统计导出", httpMethod = "POST")
	@PostMapping("driver-alarm-stat/export")
	public R<String> exportDriverAlarmStat (@Validated @RequestBody DriverAlarmRequest request, BladeUser user) {
		R<Object> r1;
		if (request.getDeptId() != null) {
			r1 = this.addDataRangeForMail(request);
		} else {
			r1 = this.addDataRangeForRequest(request, user);
		}
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<AlarmCount> driverAlarmStatList = this.alarmService.getDriverAlarmStatList(request);
		if ((driverAlarmStatList == null) || driverAlarmStatList.isEmpty()) {
			return R.fail(ResultCode.FAILURE.getCode(), "没有符合条件的数据。");
		}

		R<Map<String, String>> RLicenceColor = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_LICENCE_COLOR, "-1");
		if (!RLicenceColor.isSuccess()) {
			return R.fail(RLicenceColor.getCode(), RLicenceColor.getMsg());
		}

		R<Map<Long, Dept>> r2 = this.sysClient.getDeptChildWithOuterMapWithSelf(user.getUserId());
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		Map<String, String> licenceColorMap = ((RLicenceColor.getData() == null) || RLicenceColor.getData().isEmpty()) ? new HashMap<>() : RLicenceColor.getData();
		Map<Long, Dept> deptMap = ((r2.getData() == null) || r2.getData().isEmpty()) ? new HashMap<>() : r2.getData();
		Map<Integer, com.xh.vdm.statistic.entity.BdmVehicle> vehicleMap = this.bdmVehicleService.getVehicleMap(request);
		List responseList = new ArrayList<>();
		for (AlarmCount driverAlarmStat : driverAlarmStatList) {
			long deptId = driverAlarmStat.getDeptId();
			int vehicleId = driverAlarmStat.getVehicleId();
			ExportDriverAlarmStatResponse response = new ExportDriverAlarmStatResponse();
			response.setIdCard(driverAlarmStat.getIdCard());
			response.setDriverName(driverAlarmStat.getDriverName());
			response.setNumAlarm(driverAlarmStat.getNumAlarm().intValue());
			response.setDeptName("");
			response.setLicenceColor("");
			response.setLicencePlate("");
			if (deptMap.containsKey(deptId)) {
				Dept dept = deptMap.get(deptId);
				if ((dept != null) && StringUtils.isNotBlank(dept.getDeptName())) {
					response.setDeptName(dept.getDeptName());
				}
			}
			if (vehicleMap.containsKey(vehicleId)) {
				com.xh.vdm.statistic.entity.BdmVehicle vehicle = vehicleMap.get(vehicleId);
				response.setLicencePlate(vehicle.getLicencePlate());
				response.setLicenceColor(licenceColorMap.getOrDefault(vehicle.getLicenceColor(), ""));
			}

			responseList.add(response);
		}

		try {
			return R.data(
				ResultCode.SUCCESS.getCode(),
				EasyExcelUtils.exportExcelDynamicsHead(this.staticFilePath, this.proxyFilePath, "驾驶员报警统计", responseList , request.getHeadNameList(), request.getColumnNameList(), ExportDriverAlarmStatResponse.class, false),
				""
			);
		} catch (FileNotFoundException e) {
			log.error(e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}
	}

	@ApiOperation(value = "驾驶员报警分析导出", httpMethod = "POST")
	@PostMapping("driver-alarm-analysis/export")
	public R<String> exportDriverAlarmAnalysis (@Validated @RequestBody DriverAlarmRequest request, BladeUser user) {
		R<Object> r1;
		if (request.getDeptId() != null) {
			r1 = this.addDataRangeForMail(request);
		} else {
			r1 = this.addDataRangeForRequest(request, user);
		}
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<ImpalaAlarm> driverAlarmAnalysisList = this.alarmService.getDriverAlarmAnalysisList(request);
		if ((driverAlarmAnalysisList == null) || driverAlarmAnalysisList.isEmpty()) {
			return R.fail(ResultCode.FAILURE.getCode(), "没有符合条件的数据。");
		}

		List responseList = new ArrayList<>();
		R<Object> r2 = this.formCommonAlarmStatExportResponse(driverAlarmAnalysisList, responseList, request, user);
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}
		try {
			return R.data(
				ResultCode.SUCCESS.getCode(),
				EasyExcelUtils.exportExcelDynamicsHead(this.staticFilePath, this.proxyFilePath, "驾驶员报警分析", responseList , request.getHeadNameList(), request.getColumnNameList(), CommonAlarmStatResponse.class, false),
				""
			);
		} catch (FileNotFoundException e) {
			log.error(e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}
	}


	@ApiOperation(value = "报警处理率分页列表", httpMethod = "POST")
	@PostMapping("deal-rate/page")
	public R<IPage<Map<String, String>>> getDealRate (@Validated @RequestBody CommonStatRequest request, Query query, BladeUser user) {
		R<Object> r1 = this.addDataRangeForRequest(request, user);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		IPage<Map<String, Long>> tmpPage = this.alarmService.getDealRatePage(request, query);
		IPage<Map<String, String>> responsePage = new Page<>();
		BeanUtils.copyProperties(tmpPage, responsePage);
		if (tmpPage.getTotal() <= 0) {
			return R.data(ResultCode.SUCCESS.getCode(), responsePage, "");
		}

		R<Map<Long, Dept>> r2 = this.sysClient.getDeptChildWithOuterMapWithSelf(user.getUserId());
		if (!r2.isSuccess()) {
			return R.fail(r2.getCode(), r2.getMsg());
		}

		Map<Long, Dept> deptMap = ((r2.getData() == null) || r2.getData().isEmpty()) ? new HashMap<>() : r2.getData();
		List<Map<String, String>> responseList = new ArrayList<>();
		for (Map<String, Long> tmp : tmpPage.getRecords()) {
			long deptId = tmp.get("dept_id");
			Map<String, String> response = new HashMap<>();
			response.put("dept_name", "");
			response.put("num_vehicle", tmp.getOrDefault("num_vehicle", 0L).toString());
			response.put("num_alarm", tmp.getOrDefault("num_alarm", 0L).toString());
			response.put("num_deal", tmp.getOrDefault("num_deal", 0L).toString());
			response.put("deal_rate", this.getRate(Integer.parseInt(response.get("num_deal")), Integer.parseInt(response.get("num_alarm"))));
			response.put("num_alarm_over_speed", tmp.getOrDefault("num_alarm_over_speed", 0L).toString());
			response.put("num_deal_over_speed", tmp.getOrDefault("num_deal_over_speed", 0L).toString());
			response.put("deal_rate_over_speed", this.getRate(Integer.parseInt(response.get("num_deal_over_speed")), Integer.parseInt(response.get("num_alarm_over_speed"))));
			response.put("num_alarm_fatigue", tmp.getOrDefault("num_alarm_fatigue", 0L).toString());
			response.put("num_deal_fatigue", tmp.getOrDefault("num_deal_fatigue", 0L).toString());
			response.put("deal_rate_fatigue", this.getRate(Integer.parseInt(response.get("num_deal_fatigue")), Integer.parseInt(response.get("num_alarm_fatigue"))));
			response.put("num_alarm_night_drive", tmp.getOrDefault("num_alarm_night_drive", 0L).toString());
			response.put("num_deal_night_drive", tmp.getOrDefault("num_deal_night_drive", 0L).toString());
			response.put("deal_rate_night_drive", this.getRate(Integer.parseInt(response.get("num_deal_night_drive")), Integer.parseInt(response.get("num_alarm_night_drive"))));
			response.put("num_alarm_adas", tmp.getOrDefault("num_alarm_adas", 0L).toString());
			response.put("num_deal_adas", tmp.getOrDefault("num_deal_adas", 0L).toString());
			response.put("deal_rate_adas", this.getRate(Integer.parseInt(response.get("num_deal_adas")), Integer.parseInt(response.get("num_alarm_adas"))));
			response.put("num_alarm_dsm", tmp.getOrDefault("num_alarm_dsm", 0L).toString());
			response.put("num_deal_dsm", tmp.getOrDefault("num_deal_dsm", 0L).toString());
			response.put("deal_rate_dsm", this.getRate(Integer.parseInt(response.get("num_deal_dsm")), Integer.parseInt(response.get("num_alarm_dsm"))));
			if (deptMap.containsKey(deptId)) {
				Dept dept = deptMap.get(deptId);
				if ((dept != null) && StringUtils.isNotBlank(dept.getDeptName())) {
					response.put("dept_name", dept.getDeptName());
				}
			}

			responseList.add(response);
		}

		responsePage.setRecords(responseList);
		return R.data(ResultCode.SUCCESS.getCode(), responsePage, "");
	}

	@ApiOperation(value = "报警处理率导出", httpMethod = "POST")
	@PostMapping("deal-rate/export")
	public R<String> exportDealRate (@Validated @RequestBody CommonStatRequest request, BladeUser user) {
		R<Object> r1;
		if (request.getDeptId() != null) {
			r1 = this.addDataRangeForMail(request);
		} else {
			r1 = this.addDataRangeForRequest(request, user);
		}
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		List<Map<String, Long>> tmpList = this.alarmService.getDealRateList(request);
		List exportList = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(tmpList)) {
			R<Map<Long, Dept>> r2 = this.sysClient.getDeptChildWithOuterMapWithSelf(user.getUserId());
			if (!r2.isSuccess()) {
				return R.fail(r2.getCode(), r2.getMsg());
			}

			Map<Long, Dept> deptMap = ((r2.getData() == null) || r2.getData().isEmpty()) ? new HashMap<>() : r2.getData();
			for (Map<String, Long> tmp : tmpList) {
				long deptId = tmp.get("dept_id");
				ExportDealRateResponse export = new ExportDealRateResponse();
				export.setDeptName("");
				export.setNumVehicle(tmp.getOrDefault("num_vehicle", 0L).intValue());
				export.setNumDealAndAlarm(tmp.getOrDefault("num_deal", 0L).intValue() + "/" + tmp.getOrDefault("num_alarm", 0L).intValue());
				export.setDealRate(this.getRate(tmp.getOrDefault("num_deal", 0L).intValue(), tmp.getOrDefault("num_alarm", 0L).intValue()));
				export.setNumDealAndAlarmOverSpeed(tmp.getOrDefault("num_deal_over_speed", 0L).intValue() + "/" + tmp.getOrDefault("num_alarm_over_speed", 0L).intValue());
				export.setDealRateOverSpeed(this.getRate(tmp.getOrDefault("num_deal_over_speed", 0L).intValue(), tmp.getOrDefault("num_alarm_over_speed", 0L).intValue()));
				export.setNumDealAndAlarmFatigue(tmp.getOrDefault("num_deal_fatigue", 0L).intValue() + "/" + tmp.getOrDefault("num_alarm_fatigue", 0L).intValue());
				export.setDealRateFatigue(this.getRate(tmp.getOrDefault("num_deal_fatigue", 0L).intValue(), tmp.getOrDefault("num_alarm_fatigue", 0L).intValue()));
				export.setNumDealAndAlarmNightDrive(tmp.getOrDefault("num_deal_night_drive", 0L).intValue() + "/" + tmp.getOrDefault("num_alarm_night_drive", 0L).intValue());
				export.setDealRateNightDrive(this.getRate(tmp.getOrDefault("num_deal_night_drive", 0L).intValue(), tmp.getOrDefault("num_alarm_night_drive", 0L).intValue()));
				export.setNumDealAndAlarmAdas(tmp.getOrDefault("num_deal_adas", 0L).intValue() + "/" + tmp.getOrDefault("num_alarm_adas", 0L).intValue());
				export.setDealRateAdas(this.getRate(tmp.getOrDefault("num_deal_adas", 0L).intValue(), tmp.getOrDefault("num_alarm_adas", 0L).intValue()));
				export.setNumDealAndAlarmDsm(tmp.getOrDefault("num_deal_dsm", 0L).intValue() + "/" + tmp.getOrDefault("num_alarm_dsm", 0L).intValue());
				export.setDealRateDsm(this.getRate(tmp.getOrDefault("num_deal_dsm", 0L).intValue(), tmp.getOrDefault("num_alarm_dsm", 0L).intValue()));
				if (deptMap.containsKey(deptId)) {
					Dept dept = deptMap.get(deptId);
					if ((dept != null) && StringUtils.isNotBlank(dept.getDeptName())) {
						export.setDeptName(dept.getDeptName());
					}
				}

				exportList.add(export);
			}
		}

		try {
			return R.data(
				ResultCode.SUCCESS.getCode(),
				EasyExcelUtils.exportExcelDynamicsHead(this.staticFilePath, this.proxyFilePath, "报警处理率", exportList , request.getHeadNameList(), request.getColumnNameList(), ExportDealRateResponse.class, false),
				""
			);
		} catch (FileNotFoundException e) {
			log.error("err occur when download from exportDealRate: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}
	}

	@ApiOperation(value = "车辆驾驶行为排行", httpMethod = "POST")
	@PostMapping("car-rank/export")
	public R<String> exportCarRank (@Validated @RequestBody CommonStatRequest request) {
		R<Object> r1 = this.addDataRangeForMail(request);
		if (!r1.isSuccess()) {
			return R.fail(r1.getCode(), r1.getMsg());
		}

		/* 车辆驾驶行为排行汇总行 */
		ReportVO sumRow = new ReportVO();
		sumRow.setColumn1(String.valueOf(this.alarmService.getNumCarForDept(request)));
		sumRow.setColumn2(String.valueOf(this.alarmService.getNumAlarmForDept(request)));
		sumRow.setColumn3(String.valueOf(this.alarmService.getNumOverSpeed(request)));
		sumRow.setColumn4(String.valueOf(this.alarmService.getNumFatigueDrive(request)));
		sumRow.setColumn5(String.valueOf(this.alarmService.getNumNightDrive(request)));
		sumRow.setColumn6(String.valueOf(this.alarmService.getNumNoPosition(request)));
		sumRow.setColumn7(String.valueOf(this.alarmService.getNumOfflineMove(request)));
		List<ReportVO> sumRowList = new ArrayList<>();
		sumRowList.add(sumRow);
		List<List<ReportVO>> exportList = new ArrayList<>();
		exportList.add(sumRowList);
		/**/

		/* 各驾驶行为车辆排行 */
		R<Map<String, String>> RCompanyMeasures = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_COMPANY_MEASURES, "-1");
		if (!RCompanyMeasures.isSuccess()) {
			return R.fail(RCompanyMeasures.getCode(), RCompanyMeasures.getMsg());
		}

		Map<String, String> companyMeasuresMap = ((RCompanyMeasures.getData() == null) || RCompanyMeasures.getData().isEmpty()) ? new HashMap<>() : RCompanyMeasures.getData();
		CommonAlarmStatRequest req = new CommonAlarmStatRequest();
		BeanUtils.copyProperties(request, req);
		List<Short> alarmTypeList = new ArrayList<>();
		alarmTypeList.add((short) 1); // 超速（终端）
		alarmTypeList.add((short) 100); // 分段限速（平台）
		alarmTypeList.add((short) 101); // 夜间限速（平台）
		alarmTypeList.add((short) 105); // 道路限速（平台）
		req.setAlarmTypeList(alarmTypeList);
		List<AlarmCount> rankList = this.alarmService.getCarRankOfAlarmType(req);
		List<AlarmCount> dealList = this.alarmService.getNumEachCarDeal(req);
		Map<String, Map<String, Long>> dealMap = new HashMap<>();
		for (AlarmCount deal : dealList) {
			String licencePlate = deal.getLicencePlate();
			if (!dealMap.containsKey(licencePlate)) {
				dealMap.put(licencePlate, new HashMap<>());
			}

			Map<String, Long> numMap = dealMap.get(licencePlate);
			String companyMeasuresStr = StringUtils.isBlank(deal.getCompanyMeasures()) ? "未处理" : companyMeasuresMap.getOrDefault(deal.getCompanyMeasures(), "其它");
			if (!numMap.containsKey(companyMeasuresStr)) {
				numMap.put(companyMeasuresStr, 0L);
			}

			numMap.put(companyMeasuresStr, numMap.get(companyMeasuresStr) + deal.getNumAlarm());
		}

		List<ReportVO> carRankOverSpeed = new ArrayList<>();
		for (int i = 0; i < rankList.size(); ++i) {
			ReportVO carNumOverSpeed = new ReportVO();
			carNumOverSpeed.setColumn1(String.valueOf(i + 1));
			carNumOverSpeed.setColumn2(rankList.get(i).getLicencePlate());
			carNumOverSpeed.setColumn3(String.valueOf(rankList.get(i).getNumAlarm()));
			Map<String, Long> numMap = dealMap.get(rankList.get(i).getLicencePlate());
			StringBuffer sb = new StringBuffer();
			for (String k : numMap.keySet()) {
				sb.append(k).append("：").append(numMap.get(k)).append("；");
			}

			carNumOverSpeed.setColumn4(sb.toString());
			carRankOverSpeed.add(carNumOverSpeed);
		}

		alarmTypeList = new ArrayList<>();
		alarmTypeList.add((short) 2); // 疲劳驾驶（终端）
		alarmTypeList.add((short) 102); // 疲劳驾驶（平台）
		alarmTypeList.add((short) 161); // 生理疲劳
		req.setAlarmTypeList(alarmTypeList);
		rankList = this.alarmService.getCarRankOfAlarmType(req);
		dealList = this.alarmService.getNumEachCarDeal(req);
		dealMap = new HashMap<>();
		for (AlarmCount deal : dealList) {
			String licencePlate = deal.getLicencePlate();
			if (!dealMap.containsKey(licencePlate)) {
				dealMap.put(licencePlate, new HashMap<>());
			}

			Map<String, Long> numMap = dealMap.get(licencePlate);
			String companyMeasuresStr = StringUtils.isBlank(deal.getCompanyMeasures()) ? "未处理" : companyMeasuresMap.getOrDefault(deal.getCompanyMeasures(), "其它");
			if (!numMap.containsKey(companyMeasuresStr)) {
				numMap.put(companyMeasuresStr, 0L);
			}

			numMap.put(companyMeasuresStr, numMap.get(companyMeasuresStr) + deal.getNumAlarm());
		}

		List<ReportVO> carRankFatigueDrive = new ArrayList<>();
		for (int i = 0; i < rankList.size(); ++i) {
			ReportVO carNumFatigueDrive = new ReportVO();
			carNumFatigueDrive.setColumn1(String.valueOf(i + 1));
			carNumFatigueDrive.setColumn2(rankList.get(i).getLicencePlate());
			carNumFatigueDrive.setColumn3(String.valueOf(rankList.get(i).getNumAlarm()));
			Map<String, Long> numMap = dealMap.get(rankList.get(i).getLicencePlate());
			StringBuffer sb = new StringBuffer();
			for (String k : numMap.keySet()) {
				sb.append(k).append("：").append(numMap.get(k)).append("；");
			}

			carNumFatigueDrive.setColumn4(sb.toString());
			carRankFatigueDrive.add(carNumFatigueDrive);
		}

		alarmTypeList = new ArrayList<>();
		alarmTypeList.add((short) 103); // 夜间异动（平台）
		req.setAlarmTypeList(alarmTypeList);
		rankList = this.alarmService.getCarRankOfAlarmType(req);
		dealList = this.alarmService.getNumEachCarDeal(req);
		dealMap = new HashMap<>();
		for (AlarmCount deal : dealList) {
			String licencePlate = deal.getLicencePlate();
			if (!dealMap.containsKey(licencePlate)) {
				dealMap.put(licencePlate, new HashMap<>());
			}

			Map<String, Long> numMap = dealMap.get(licencePlate);
			String companyMeasuresStr = StringUtils.isBlank(deal.getCompanyMeasures()) ? "未处理" : companyMeasuresMap.getOrDefault(deal.getCompanyMeasures(), "其它");
			if (!numMap.containsKey(companyMeasuresStr)) {
				numMap.put(companyMeasuresStr, 0L);
			}

			numMap.put(companyMeasuresStr, numMap.get(companyMeasuresStr) + deal.getNumAlarm());
		}

		List<ReportVO> carRankNightDrive = new ArrayList<>();
		for (int i = 0; i < rankList.size(); ++i) {
			ReportVO carNumNightDrive = new ReportVO();
			carNumNightDrive.setColumn1(String.valueOf(i + 1));
			carNumNightDrive.setColumn2(rankList.get(i).getLicencePlate());
			carNumNightDrive.setColumn3(String.valueOf(rankList.get(i).getNumAlarm()));
			Map<String, Long> numMap = dealMap.get(rankList.get(i).getLicencePlate());
			StringBuffer sb = new StringBuffer();
			for (String k : numMap.keySet()) {
				sb.append(k).append("：").append(numMap.get(k)).append("；");
			}

			carNumNightDrive.setColumn4(sb.toString());
			carRankNightDrive.add(carNumNightDrive);
		}

		rankList = this.alarmService.getCarRankOfNoPosition(request);
		List<ReportVO> carRankNoPosition = new ArrayList<>();
		for (int i = 0; i < rankList.size(); ++i) {
			ReportVO carNumNoPosition = new ReportVO();
			carNumNoPosition.setColumn1(String.valueOf(i + 1));
			carNumNoPosition.setColumn2(rankList.get(i).getLicencePlate());
			carNumNoPosition.setColumn3(String.valueOf(rankList.get(i).getNumAlarm()));
			carNumNoPosition.setColumn4("已生成企业日报表通知企业。");
			carRankNoPosition.add(carNumNoPosition);
		}

		rankList = this.alarmService.getCarRankOfOfflineMove(request);
		List<ReportVO> carRankOfflineMove = new ArrayList<>();
		for (int i = 0; i < rankList.size(); ++i) {
			ReportVO carNumOfflineMove = new ReportVO();
			carNumOfflineMove.setColumn1(String.valueOf(i + 1));
			carNumOfflineMove.setColumn2(rankList.get(i).getLicencePlate());
			carNumOfflineMove.setColumn3(String.valueOf(rankList.get(i).getNumAlarm()));
			carNumOfflineMove.setColumn4("已生成企业日报表通知企业。");
			carRankOfflineMove.add(carNumOfflineMove);
		}

		exportList.add(carRankOverSpeed);
		exportList.add(carRankFatigueDrive);
		exportList.add(carRankNightDrive);
		exportList.add(carRankNoPosition);
		exportList.add(carRankOfflineMove);
		/**/

		/* 各驾驶行为标题 */
		List<List<List<String>>> titleList = new ArrayList<>();
		List<List<String>> sumTitle = new ArrayList<>();
		List<List<String>> overSpeedTitle = new ArrayList<>();
		List<List<String>> fatigueDriveTitle = new ArrayList<>();
		List<List<String>> nightDriveTitle = new ArrayList<>();
		List<List<String>> noPositionTitle = new ArrayList<>();
		List<List<String>> offlineMoveTitle = new ArrayList<>();
		for (int i = 0; i < 7; ++i) {
			sumTitle.add(Collections.singletonList("报警情况汇总一览表"));
			overSpeedTitle.add(Collections.singletonList("（一）超速行驶车辆报警统计"));
			fatigueDriveTitle.add(Collections.singletonList("（二）疲劳驾驶车辆报警统计"));
			nightDriveTitle.add(Collections.singletonList("（三）1-4点禁行车辆报警统计"));
			noPositionTitle.add(Collections.singletonList("（四）在线车辆未定位报警统计"));
			offlineMoveTitle.add(Collections.singletonList("（五）离线位移车辆报警统计"));
		}

		titleList.add(sumTitle);
		titleList.add(overSpeedTitle);
		titleList.add(fatigueDriveTitle);
		titleList.add(nightDriveTitle);
		titleList.add(noPositionTitle);
		titleList.add(offlineMoveTitle);
		/**/

		/* 各驾驶行为字段 */
		List<List<List<String>>> fieldList = new ArrayList<>();
		List<List<String>> sumFieldList = new ArrayList<>();
		sumFieldList.add(Collections.singletonList("公司车辆总数"));
		sumFieldList.add(Collections.singletonList("报警累计"));
		sumFieldList.add(Collections.singletonList("超速行驶"));
		sumFieldList.add(Collections.singletonList("疲劳驾驶"));
		sumFieldList.add(Collections.singletonList("1-4点禁行"));
		sumFieldList.add(Collections.singletonList("在线未定位"));
		sumFieldList.add(Collections.singletonList("离线位移"));
		fieldList.add(sumFieldList);

		List<List<String>> overSpeedFieldList = new ArrayList<>();
		overSpeedFieldList.add(Collections.singletonList("排名"));
		overSpeedFieldList.add(Collections.singletonList("车牌号"));
		overSpeedFieldList.add(Collections.singletonList("超速行驶次数"));
		overSpeedFieldList.add(Collections.singletonList("处理情况"));
		overSpeedFieldList.add(Collections.singletonList("处理情况"));
		overSpeedFieldList.add(Collections.singletonList("处理情况"));
		overSpeedFieldList.add(Collections.singletonList("处理情况"));
		fieldList.add(overSpeedFieldList);

		List<List<String>> fatigueDriveFieldList = new ArrayList<>();
		fatigueDriveFieldList.add(Collections.singletonList("排名"));
		fatigueDriveFieldList.add(Collections.singletonList("车牌号"));
		fatigueDriveFieldList.add(Collections.singletonList("疲劳驾驶次数"));
		fatigueDriveFieldList.add(Collections.singletonList("处理情况"));
		fatigueDriveFieldList.add(Collections.singletonList("处理情况"));
		fatigueDriveFieldList.add(Collections.singletonList("处理情况"));
		fatigueDriveFieldList.add(Collections.singletonList("处理情况"));
		fieldList.add(fatigueDriveFieldList);

		List<List<String>> nightDriveFieldList = new ArrayList<>();
		nightDriveFieldList.add(Collections.singletonList("排名"));
		nightDriveFieldList.add(Collections.singletonList("车牌号"));
		nightDriveFieldList.add(Collections.singletonList("1-4点禁行次数"));
		nightDriveFieldList.add(Collections.singletonList("处理情况"));
		nightDriveFieldList.add(Collections.singletonList("处理情况"));
		nightDriveFieldList.add(Collections.singletonList("处理情况"));
		nightDriveFieldList.add(Collections.singletonList("处理情况"));
		fieldList.add(nightDriveFieldList);

		List<List<String>> noPositionFieldList = new ArrayList<>();
		noPositionFieldList.add(Collections.singletonList("排名"));
		noPositionFieldList.add(Collections.singletonList("车牌号"));
		noPositionFieldList.add(Collections.singletonList("在线未定位次数"));
		noPositionFieldList.add(Collections.singletonList("处理情况"));
		noPositionFieldList.add(Collections.singletonList("处理情况"));
		noPositionFieldList.add(Collections.singletonList("处理情况"));
		noPositionFieldList.add(Collections.singletonList("处理情况"));
		fieldList.add(noPositionFieldList);

		List<List<String>> offlineMoveFieldList = new ArrayList<>();
		offlineMoveFieldList.add(Collections.singletonList("排名"));
		offlineMoveFieldList.add(Collections.singletonList("车牌号"));
		offlineMoveFieldList.add(Collections.singletonList("离线位移次数"));
		offlineMoveFieldList.add(Collections.singletonList("处理情况"));
		offlineMoveFieldList.add(Collections.singletonList("处理情况"));
		offlineMoveFieldList.add(Collections.singletonList("处理情况"));
		offlineMoveFieldList.add(Collections.singletonList("处理情况"));
		fieldList.add(offlineMoveFieldList);
		/**/

		// 设置需要合并的单元格
		ExcelFillCellMergePrevCol excelFillCellMergePrevCol = new ExcelFillCellMergePrevCol();
		int i = 0;
		for (List<ReportVO> export : exportList) {
			// 最开头为车辆驾驶行为排行汇总，其上无分隔行，其中也无合并列，因此不需要任何操作，直接跳过。由于汇总内容固定占3行，因此直接i+3。
			// 每个驾驶行为的车辆排行，正式数据之前共有4行：空行、条目号行、标题行、表头行，因此，i+4。
			if (i > 0) {
				for (int j = 0; j < export.size(); ++j) {
					excelFillCellMergePrevCol.add(i + 4, 3, 3); // 列索引由0开始。
					++i;
				}
			}

			i += 3; // 空行、标题、表头各占1行，共3行。
		}

		BladeDept dept = this.deptService.getById(request.getDeptId());
		String deptName = ((dept == null) || StringUtils.isBlank(dept.getDeptName())) ? "" : dept.getDeptName();
		String fileName = deptName + "车辆驾驶行为排行" + System.currentTimeMillis() + ".xlsx";
		try {
			FileOutputStream os = new FileOutputStream(this.staticFilePath + fileName);
			ExcelWriter excelWriter = EasyExcel.write(os, ReportVO.class).registerWriteHandler(
				new ExcelExportFontAndStyleConfig(Arrays.asList(0, 1), new WriteCellStyle(), new WriteCellStyle())
			).registerWriteHandler(excelFillCellMergePrevCol).build();

			List<List<String>> title = new ArrayList<>();
			String titleStr = deptName + Integer.parseInt(new SimpleDateFormat("MM").format(new Date(request.getStartTime() * 1000))) + "月份车辆驾驶行为排名表";
			for (int k = 0; k < 7; ++k) {
				title.add(Collections.singletonList(titleStr));
			}

			// 创建sheet并设置其名称。
			WriteSheet writeSheet = EasyExcel.writerSheet(titleStr).needHead(Boolean.TRUE).head(title).build();

			// 正式按顺序遍历titleList、fieldList，生成表格。
			int num = 0;
			int sortCode = 0;
			for (List<List<String>> moduleTitle: titleList) {
				excelWriter.write((Collection<?>) null, writeSheet, EasyExcel.writerTable(num).needHead(Boolean.TRUE).head(moduleTitle).build());
				WriteTable writeTable = EasyExcel.writerTable(num + 1).needHead(Boolean.TRUE).head(fieldList.get(sortCode)).build();
				excelWriter.write(exportList.get(sortCode), writeSheet, writeTable);
				excelWriter.write(Collections.singletonList(""), writeSheet, writeTable);
				++sortCode;
				num += 2;

				// 写入尾行
				if (sortCode >= titleList.size()) {
					List<List<String>> tailList = new ArrayList<>();
					tailList.add(Collections.singletonList("注：企业月报 是指每月初至月末企业的报警总数及具体车辆报警次数统计。"));
					excelWriter.write(tailList, writeSheet, writeTable);
				}
			}

			excelWriter.finish();
			return R.data(ResultCode.SUCCESS.getCode(), this.proxyFilePath + fileName, "");
		} catch (java.io.FileNotFoundException e) {
			log.error(e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), e.getMessage());
		}
	}
}
