package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.BladeDept;
import com.xh.vdm.statistic.entity.OverSpeedWithDeptAndDate;
import com.xh.vdm.statistic.mapper.StatOverSpeedMapper;
import com.xh.vdm.statistic.service.IBladeDeptService;
import com.xh.vdm.statistic.service.IStatOverSpeedService;
import com.xh.vdm.statistic.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: 超速相关
 * @Author: zhouxw
 * @Date: 2022/11/15 11:34 PM
 */
@Service
@Slf4j
public class StatOverSpeedServiceImpl implements IStatOverSpeedService {

    @Resource
    private StatOverSpeedMapper mapper;

    @Resource
    private IBladeDeptService deptService;

    private static ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM"));

    @Override
    public List<OverSpeedWithDeptAndDate> statOverSpeedAverageCount(Long deptId, String month , int count , int start) throws Exception{
        //1. 如果 deptId 为空，则分页查询所有企业的超速信息
        List<Long> deptIds = new ArrayList<>();
        Map<Long,String> deptNamesMap = new HashMap<>();

		//租户id
		BladeUser user = AuthUtil.getUser();
		if(user == null){
			log.info("用户未登录或授权失败");
			throw new Exception("用户未登录或授权失败");
		}
		String tenantId = user.getTenantId();

        if(deptId != null){
            //如果指定了企业
            deptIds.add(deptId);
            BladeDept dept = deptService.getById(deptId);
            deptNamesMap.put(deptId,dept.getDeptName());
        }else{

            //如果没有指定企业
            //分页查询企业信息
            Page page = new Page();
            page.setSize(count);
            page.setCurrent(start / count + (start % count>0?1:0));

            IPage<BladeDept> depts = deptService.page(page , Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId , tenantId).eq(BladeDept::getIsDeleted ,0));
            for(BladeDept dept : depts.getRecords()){
                deptIds.add(dept.getId());
                deptNamesMap.put(dept.getId() , dept.getDeptName());
            }
        }

        //2.统计超速
        Date mon = sdfHolder.get().parse(month);
        long startSecondTimestamp = DateUtil.getMonthFirstSecondTimestamp(mon.getTime() / 1000);
        long endSecondTimestamp = DateUtil.getMonthLastSecondTimestamp(mon.getTime() / 1000);

        List<OverSpeedWithDeptAndDate> list = mapper.statOverSpeedByDeptAndDuration(deptIds , startSecondTimestamp , endSecondTimestamp);

        //3.补充 deptName
        list.stream().forEach(item -> {
            Long dId = item.getDeptId();
            String deptName = deptNamesMap.get(dId);
            item.setDeptName(deptName);
        });

        return list;
    }

    @Override
    public OverSpeedWithDeptAndDate findOverSpeedAverageCount(Long deptId, Long startSecondTimestamp, Long endSecondTimestamp) throws Exception {
        OverSpeedWithDeptAndDate overSpeed = mapper.getOverSpeedByDeptAndDuration(deptId , startSecondTimestamp , endSecondTimestamp);
        return overSpeed;
    }
}
