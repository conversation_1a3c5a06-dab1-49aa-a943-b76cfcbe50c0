package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.BdmTrackCalendar;
import com.xh.vdm.statistic.mapper.BdmTrackCalendarMapper;
import com.xh.vdm.statistic.service.IBdmTrackCalendarService;
import lombok.extern.slf4j.Slf4j;
import org.springblade.entity.Location;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Service("BdmTrackCalendarService")
public class BdmTrackCalendarServiceImpl extends ServiceImpl<BdmTrackCalendarMapper, BdmTrackCalendar> implements IBdmTrackCalendarService {

	@Override
	public List<BdmTrackCalendar> getLocCalendarByLocation (Location location) {
		if (location == null) {
			return new ArrayList<>();
		}

		return this.baseMapper.getLocCalendarByObjMonth(
			location.getTargetType(),
			location.getTargetId(),
			location.getDeviceType(),
			location.getDeviceId(),
			new SimpleDateFormat("yyyy-MM").format(new Date(location.getTime() * 1000))
		);
	}

	@Override
	public Integer list(Long deviceId, Integer deviceType,Long targetId, Integer targetType, Date month) {
		return this.baseMapper.list(deviceId,deviceType,targetId, targetType,month);
	}
}
