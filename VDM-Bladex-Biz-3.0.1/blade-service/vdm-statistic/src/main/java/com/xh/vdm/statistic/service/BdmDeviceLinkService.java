package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.BdmDeviceLink;
import com.xh.vdm.statistic.vo.request.BdmDeviceLinkRequest;
import com.xh.vdm.statistic.vo.response.terminal.LinkCountResponse;
import org.springblade.core.mp.support.Query;
import org.springblade.entity.DataAuthCE;

import java.util.List;

/**
 * (BdmDeviceLink)表服务接口
 */
public interface BdmDeviceLinkService  extends IService<BdmDeviceLink> {


	/**
	 * 分页查询
	 *
	 * @param dataAuthCE 权限
	 * @param bdmDeviceLinkRequest 筛选条件
	 * @param query                分页对象
	 * @param userId
	 * @return 查询结果
	 */
	List<BdmDeviceLink> queryByPage(DataAuthCE dataAuthCE, BdmDeviceLinkRequest bdmDeviceLinkRequest, Query query, Long userId);

	long countLink(DataAuthCE dataAuthCE, BdmDeviceLinkRequest bdmDeviceLinkRequest, Long userId);

	/**
	 * 查询长期离线终端数量
	 * @param deptList
	 * @param secondTimestamp
	 * @return
	 */
	long findLongOfflineCount(List<Long> deptList, Long secondTimestamp);

	List<LinkCountResponse> onlineTrendered(Long userId);
}
