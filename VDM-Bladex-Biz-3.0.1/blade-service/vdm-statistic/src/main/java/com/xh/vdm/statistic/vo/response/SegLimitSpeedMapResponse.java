package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆超速报表（地图限速）返回类
 * <AUTHOR>
 * @date 2021/11/1 10:19
 */
@ApiModel(value = "车辆超速报表（地图限速）返回类")
public class SegLimitSpeedMapResponse {

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("enterprise")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "企业名称"})
    @ColumnWidth(40)
    private String enterprise;

    @ApiModelProperty(value = "车队名称")
    @JsonProperty("dept_name")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "车队名称"})
    @ColumnWidth(40)
    private String deptName;

    @ApiModelProperty(value = "车牌号码")
    @JsonProperty("licence_plate")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "车牌号码"})
    @ColumnWidth(15)
    private String licencePlate;

    @ApiModelProperty(value = "车牌颜色")
    @JsonProperty("licence_color")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "车牌颜色"})
    private String licenceColor;

    @ApiModelProperty(value = "行业类型")
    @JsonProperty("vehicle_model")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "行业类型"})
    @ColumnWidth(20)
    private String vehicleModel;

    @ApiModelProperty(value = "车辆归属")
    @JsonProperty("vehicle_owner")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "车辆归属"})
    @ColumnWidth(20)
    private String vehicleOwner;

    @ApiModelProperty(value = "车辆接入方式")
    @JsonProperty("access_mode")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "车辆接入方式"})
    @ColumnWidth(15)
    private String accessMode;

    @ApiModelProperty(value = "地图限速报警时间")
    @JsonProperty("start_alarm_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "地图限速报警时间"})
    @ColumnWidth(22)
    private Date startAlarmTime;

    @ApiModelProperty(value = "持续时间(秒)")
    @JsonProperty("duration_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "持续时间(秒)"})
    @ColumnWidth(20)
    private String durationTime;

    @ApiModelProperty(value = "最大速度(km/h)")
    @JsonProperty("max_speed")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "最大速度(km/h)"})
    @ColumnWidth(15)
    private Double maxSpeed;

    @ApiModelProperty(value = "地图限速(km/h)")
    @JsonProperty("speed_limit")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "地图限速(km/h)"})
    @ColumnWidth(15)
    private Double speedLimit;

    @ApiModelProperty(value = "经度")
    @JsonProperty("longitude")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "经度"})
    @ColumnWidth(20)
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    @JsonProperty("latitude")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "纬度"})
    @ColumnWidth(20)
    private Double latitude;

    @ApiModelProperty(value = "报警位置")
    @JsonProperty("start_alarm_address")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆超速报表（地图限速）", "报警位置"})
    @ColumnWidth(90)
    private String startAlarmAddress;




    public String getAccessMode() {
        return accessMode;
    }

    public void setAccessMode(String accessMode) {
        this.accessMode = accessMode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getEnterprise() {
        return enterprise;
    }

    public void setEnterprise(String enterprise) {
        this.enterprise = enterprise;
    }

    public String getLicencePlate() {
        return licencePlate;
    }

    public void setLicencePlate(String licencePlate) {
        this.licencePlate = licencePlate;
    }

    public String getLicenceColor() {
        return licenceColor;
    }

    public void setLicenceColor(String licenceColor) {
        this.licenceColor = licenceColor;
    }

    public String getVehicleModel() {
        return vehicleModel;
    }

    public void setVehicleModel(String vehicleModel) {
        this.vehicleModel = vehicleModel;
    }

    public String getVehicleOwner() {
        return vehicleOwner;
    }

    public void setVehicleOwner(String vehicleOwner) {
        this.vehicleOwner = vehicleOwner;
    }

    public Date getStartAlarmTime() {
        return startAlarmTime;
    }

    public void setStartAlarmTime(Date startAlarmTime) {
        this.startAlarmTime = startAlarmTime;
    }

    public String getDurationTime() {
        return durationTime;
    }

    public void setDurationTime(String durationTime) {
        this.durationTime = durationTime;
    }

    public Double getMaxSpeed() {
//        if(maxSpeed==null||maxSpeed==0){
//            return 0D;
//        }else{
////            return new BigDecimal((double)maxSpeed/10).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
//            return maxSpeed;
//        }

        if(maxSpeed==null||maxSpeed==0){
            maxSpeed = 0D;
        }
        if(( maxSpeed % 1) == 0){
            return new BigDecimal(maxSpeed).doubleValue();
        }else{
            return new BigDecimal(maxSpeed).setScale(1,BigDecimal.ROUND_HALF_UP).doubleValue();
        }
    }

    public void setMaxSpeed(Double maxSpeed) {
        this.maxSpeed = maxSpeed;
    }

    public Double getSpeedLimit() {
//        return new BigDecimal((double)speedLimit/10).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
//        return speedLimit;
        if(speedLimit==null||speedLimit==0){
            speedLimit = 0D;
        }
        if(( speedLimit % 1) == 0){
            return new BigDecimal(speedLimit).doubleValue();
        }else{
            return new BigDecimal(speedLimit).setScale(1,BigDecimal.ROUND_HALF_UP).doubleValue();
        }
    }

    public void setSpeedLimit(Double speedLimit) {
        this.speedLimit = speedLimit;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getStartAlarmAddress() {
        return startAlarmAddress;
    }

    public void setStartAlarmAddress(String startAlarmAddress) {
        this.startAlarmAddress = startAlarmAddress;
    }
}
