package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.statistic.service.IStatReportDayService;
import com.xh.vdm.statistic.service.IStatReportMonthService;
import com.xh.vdm.statistic.task.ReportTask;
import com.xh.vdm.statistic.vo.request.CompanyAndDate;
import com.xh.vdm.statistic.vo.response.ReportInfoResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 日报、月报
 */
@Slf4j
@RestController
@RequestMapping("/report")
@Api(tags = "企业报告", protocols = "http", produces = "application/json", consumes = "application/json")
public class ReportController {

	@Resource
	private ReportTask task;

	@Resource
	private IStatReportDayService reportDayService;

	@Resource
	private IStatReportMonthService reportMonthService;

	@ApiOperation(value = "报告分页列表", httpMethod = "POST")
	@PostMapping("/page")
	public R<IPage<ReportInfoResponse>> sheet (@Validated @RequestBody CompanyAndDate request, Query query) {
		try {
			if (request.getReportType().equals("day")) {
				return R.data(ResultCode.SUCCESS.getCode(), this.reportDayService.findReportDayByPage(request, query), "");
			} else if (request.getReportType().equals("month")) {
				return R.data(ResultCode.SUCCESS.getCode(), this.reportMonthService.getMonthReportPage(request, query), "");
			} else {
				return R.fail(ResultCode.FAILURE.getCode(), "报告类型不正确。");
			}
		} catch (Exception e) {
			log.error("fail page company report: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), "报告分页列表获取异常：" + e.getMessage());
		}
	}

	@ApiOperation(value = "重跑报告", httpMethod = "POST")
	@PostMapping("/refresh")
	public R<String> refresh (@Validated @RequestBody CompanyAndDate request) {
		List<Long> deptList = request.getDeptIds();
		if (CollectionUtils.isEmpty(deptList)) {
			return R.fail(ResultCode.FAILURE.getCode(), "需重跑的单位为空。");
		}

		String date = request.getDate();
		if (StringUtils.isBlank(date)) {
			return R.fail(ResultCode.FAILURE.getCode(), "需要重跑的日期为空。");
		}

		StringBuffer sb = new StringBuffer();
		for (Long deptId : deptList) {
			sb.append(deptId).append(",");
		}

		String deptIds = sb.substring(0, sb.length() - 1);
		try {
			if (request.getReportType().equals("day") && date.matches("\\d{4}-\\d{2}-\\d{2}")) {
				this.task.reportSummaryDay(deptIds, date);
				this.task.reportAlarmDay(deptIds, date);
				this.task.reportRunningDay(deptIds, date);
			} else if (request.getReportType().equals("month") && date.matches("\\d{4}-\\d{2}")) {
				this.task.reportSummaryMonth(deptIds, date);
				this.task.reportAlarmMonth(deptIds, date);
				this.task.reportRunningMonth(deptIds, date);
			} else {
				return R.fail(ResultCode.FAILURE.getCode(), "报告类型或日期不正确。");
			}
		} catch (Exception e) {
			log.error("fail refresh company report: " + e.getMessage(), e);
			return R.fail(ResultCode.FAILURE.getCode(), "报告重跑异常：" + e.getMessage());
		}

		return R.success(ResultCode.SUCCESS, "报告重跑成功。");
	}
}
