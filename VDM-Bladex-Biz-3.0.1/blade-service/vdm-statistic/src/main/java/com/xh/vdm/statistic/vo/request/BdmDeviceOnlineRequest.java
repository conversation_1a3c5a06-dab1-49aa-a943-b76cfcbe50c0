package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 在线记录入参
 */
@Data
public class BdmDeviceOnlineRequest implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long deviceId;

	/**
	 * 终端设备类型
	 */
	private Integer deviceType;
	/**
	 * 终端号
	 */
	private String uniqueId;
	/**
	 * 终端赋码号
	 */
	private String deviceNum;
	/**
	 * 时间段开始时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startTime;
	/**
	 * 时间段结束时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endTime;

	private String targetName;

}

