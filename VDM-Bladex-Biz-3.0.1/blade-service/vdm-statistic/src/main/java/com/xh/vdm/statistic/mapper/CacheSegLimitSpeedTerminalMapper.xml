<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.CacheSegLimitSpeedTerminalMapper">


    <select id="getList" resultType="com.xh.vdm.statistic.vo.response.SegLimitSpeedTerminalResponse">
        select enterprise, dept_name, licence_plate, licence_color, vehicle_model, vehicle_owner, start_alarm_time, end_alarm_time,
        duration_time, min_speed,  max_speed, average_speed, speed_limit, start_longitude, start_latitude, end_longitude, end_latitude, start_alarm_address,
        end_alarm_address, driver_info, access_mode
        from cache_seg_limit_speed_terminal
        where 1 = 1
        <if test= "param.licencePlate != null and param.licencePlate != ''">
            AND licence_plate = #{param.licencePlate}
        </if>
        <if test= "param.licenceColor != null and param.licenceColor != ''">
            AND licence_color_code = #{param.licenceColor}
        </if>
        <if test= 'param.startTime != null and param.startTime != "" '>
            AND start_alarm_time &gt;= to_timestamp(#{param.startTime})
        </if>
        <if test= 'param.endTime != null and param.endTime != "" '>
            AND start_alarm_time &lt;= to_timestamp(#{param.endTime})
        </if>
        <if test= 'param.limitSpeed != null and param.limitSpeed != "" '>
            AND speed_limit &gt;=#{param.limitSpeed}
        </if>
        <if test= 'param.durationTime != null and param.durationTime != "" '>
            AND duration_time &gt;=#{param.durationTime}
        </if>
        <if test= "param.vehicleOwnerId != null ">
            AND vehicle_owner_id = #{param.vehicleOwnerId}
        </if>
        <if test= "param.accessMode != null and param.accessMode != ''">
            AND access_mode_code = #{param.accessMode}
        </if>
        <if test= "param.deptList != null and param.deptList.size() != 0">
            AND dept_id IN
            <foreach collection="param.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="param.professionList != null and param.professionList.size() > 0">
            AND vehicle_model_code IN
            <foreach collection="param.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        order by start_alarm_time desc
    </select>


    <select id="deleteByDateDuration" resultType="Long">
        DELETE FROM cache_seg_limit_speed_terminal     WHERE start_alarm_time &lt;= #{endDate} AND start_alarm_time >= #{startDate}
    </select>

</mapper>
