package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆漂移率DB缓存表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CacheStatDriftRate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 月份
     */
    private String month;

    /**
     * 上级平台
     */
    private Long vehicleOwnerId;

    /**
     * 车组id
     */
    private Long deptId;

    /**
     * 班线上线车辆数
     */
    private Integer busGoonlineCount;

    /**
     * 班线漂移车辆数
     */
    private Integer busDriftCount;

    /**
     * 包车上线车辆数
     */
    private Integer charterGoonlineCount;

    /**
     * 包车漂移车辆数
     */
    private Integer charterDriftCount;

    /**
     * 危险品车辆上线数
     */
    private Integer dangerGoonlineCount;

    /**
     * 危险品车辆漂移数
     */
    private Integer dangerDriftCount;

    /**
     * 总上线车辆数
     */
    private Integer totalGoonlineCount;

    /**
     * 总漂移车辆数
     */
    private Integer totalDriftCount;

    /**
     * 漂移率
     */
    private Double driftRate;

    /**
     * 考核得分
     */
    private Double driftScore;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 备注
     */
    private String note;


}
