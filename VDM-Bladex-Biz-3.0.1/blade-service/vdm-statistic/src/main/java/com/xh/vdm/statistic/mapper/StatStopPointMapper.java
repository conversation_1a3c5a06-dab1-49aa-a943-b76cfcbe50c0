package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.DateListAndMonth;
import com.xh.vdm.statistic.entity.StatStopPoint;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.response.StopPointDetailResponse;
import com.xh.vdm.statistic.vo.response.StopPointStatResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 车辆停止点表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-22
 */
public interface StatStopPointMapper extends BaseMapper<StatStopPoint> {

    /**
     * @description: 新增或更新停止点信息
     * @author: zhouxw
     * @date: 2022/12/23 9:49 AM
     * @param: [list, month：yyyyMM]
     * @return: void
     **/
    void saveOrUpdate(@Param("list") List<StatStopPoint> list , @Param("month") String month);

    /**
     * @description: 删除数据
     * @author: zhouxw
     * @date: 2022/12/23 10:17 AM
     * @param: [statDate, month]
     * @return: void
     **/
    void delete(@Param("statDate") String statDate , @Param("month") String month);

	/**
	 * 查询最新的停靠点
	 * @param licencePlate 车牌号
	 * @param licenceColor 车牌颜色
	 * @param month 月份 yyyyMM
	 * @param statDate 统计日期 yyyy-MM-dd
	 * @return
	 */
	StatStopPoint getLatestStopPoint(@Param("licencePlate") String licencePlate, @Param("licenceColor") String licenceColor, @Param("month") String month, @Param("statDate") String statDate);

	/**
	 * 根据条件查询停靠点列表
	 * @param licenceCode
	 * @param date
	 * @param month
	 * @param startSecondTimestamp
	 * @param endSecondTimestamp
	 * @return
	 */
	List<StatStopPoint> getStopPointListByCondition(@Param("licencePlate") String licenceCode, @Param("date") String date, @Param("month") String month, @Param("startSecondTimestamp") long startSecondTimestamp, @Param("endSecondTimestamp") long endSecondTimestamp);

	/**
	 * 获取停靠点统计信息
	 * @param request
	 * @param dm
	 * @return
	 */
	List<StopPointStatResponse> getStopPointInfo(@Param("request") CommonBaseRequest request, @Param("dm") List<DateListAndMonth> dm);

	/**
	 * 分页查询停靠点统计信息
	 * @param request
	 * @param dm
	 * @param page
	 * @return
	 */
	IPage<StopPointStatResponse> getStopPointInfoPage(@Param("request") CommonBaseRequest request, @Param("dm") List<DateListAndMonth> dm, IPage<StopPointStatResponse> page);

	/**
	 * 分页查询停靠点明细
	 * @param request
	 * @param dm
	 * @param page
	 * @return
	 */
	IPage<StopPointDetailResponse> getStopPointDetailPage(@Param("request") CommonBaseRequest request, @Param("dm") List<DateListAndMonth> dm, IPage<StopPointDetailResponse> page);

}
