package com.xh.vdm.statistic.task;

import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.service.IAlarmService;
import com.xh.vdm.statistic.service.IStatCountRateUserService;
import com.xh.vdm.statistic.service.IStatTaskLogService;
import com.xh.vdm.statistic.utils.DataUtils;
import com.xh.vdm.statistic.utils.LogUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 首页统计任务
 */
@Component
@Slf4j
public class IndexTask {

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private LogUtil logUtil;

	private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

	@Resource
	private IStatTaskLogService taskLogService;

	@Resource
	private IAlarmService alarmService;

	@Resource
	private IStatCountRateUserService statCountRateUserService;

	@Resource
	private DataUtils dataUtils;

	public static final String COUNT_RATE_TABLE = "stat_count_rate_user";


	/**
	 * @description: 近30天账号相关数据 定时任务
	 * 统计前一天的定位数据，将每辆车的统计指标记录到表中
	 * 定位数据允许有8个小时的补传，所以，数据的处理应当在当天的8点之后
	 * 每天 8点30 执行
	 * 如果给定日期，则按照给定的日期进行统计，如果没有给定日期，则统计前一天的数据
	 * @author: zhouxw
	 * @date: 2022/9/1 9:36 AM
	 * @param: [date yyyyMMdd]
	 * @return: void
	 **/
	//@Scheduled(cron="0 30 8 * * ?")
	//@XxlJob("userDataStat")
	public void userDataStat(String date){
		XxlJobHelper.log("将要开始执行定时任务：alarmAndHandleCountStat");

		//为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
		Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_INDEX_ALARM_HANDLE_COUNT_30_DAY);
		if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
			//如果已经有跑批程序在执行，则不再执行
			log.info("[主页-30天内报警数和处理数统计]跑批任务已经在执行，请等当前执行程序完成后再试");
			return ;
		}

		//获取执行权限之后，添加执行标记（有效期为1个小时）
		synchronized (this){
			stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_INDEX_ALARM_HANDLE_COUNT_30_DAY , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
		}

		//删除原有相关数据
		dataUtils.truncateTable(COUNT_RATE_TABLE);

		//统计报警数和报警率（如果没有指定日期，则执行前一天的统计；如果给定日期，则执行给定日期的统计）
		long start = System.currentTimeMillis();
		long end = 0;
		try {
			alarmService.statCountAndRateIn30Day(date);
			end = System.currentTimeMillis();
		} catch (Exception e) {
			end = System.currentTimeMillis();
			log.error("[首页-近30天报警数报警率统计-{}]定时任务 执行失败 ，发生异常" , date , e);
			e.printStackTrace();
			logUtil.insertStatLog(StatisticConstants.TASK_INDEX_VEH_RUNNING_STATE,"首页-近30天报警数报警率统计",date ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行失败，发生异常："+e.getMessage());
		}

		//执行完毕后，解除执行锁定
		synchronized (this){
			stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_INDEX_ALARM_HANDLE_COUNT_30_DAY );
		}

		XxlJobHelper.log("定时任务执行完毕：alarmAndHandleCountStat");

	}


}
