package com.xh.vdm.statistic.utils;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/3/21 11:21
 */
public class StrUtils {

    /**
     * @description: 判断字符是否是中文
     * @author: zhouxw
     * @date: 2023-03-80 11:21:47
     * @param: [c]
     * @return: boolean
     **/
    public static boolean isChinese(char c) {
        return c >= 0x4E00 && c <= 0x9FA5;// 根据字节码判断
    }

    /**
     * @description: 判断字符串中是否包含中文
     * @author: zhouxw
     * @date: 2023-03-80 11:22:01
     * @param: [str]
     * @return: boolean
     **/
    public static boolean isChinese(String str) {
        if (str == null) return false;
        for (char c : str.toCharArray()) {
            if (isChinese(c)) return true;// 有一个中文字符就返回
        }
        return false;
    }

    /**
     * @description: 判断字符串首字符是否是中文
     * @author: zhouxw
     * @date: 2023-03-80 11:24:42
     * @param: [str]
     * @return: boolean
     **/
    public static boolean firstIsChinese(String str){
        if(StringUtils.isBlank(str)){
            return false;
        }
        char[] array = str.toCharArray();
        return isChinese(array[0]);
    }
}
