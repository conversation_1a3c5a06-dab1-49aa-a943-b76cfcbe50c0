package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.VehicleMapper;
import com.xh.vdm.statistic.service.IBladeDeptService;
import com.xh.vdm.statistic.service.IVehicleStatService;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.GoOnlineRateBaseResponse;
import com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse;
import com.xh.vdm.statistic.vo.response.VehicleRateDetailResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description: 车辆统计相关
 * @Author: zhouxw
 * @Date: 2022/9/6 9:29 AM
 */
@Service
@Slf4j
public class VehicleStatServiceImpl extends ServiceImpl<VehicleMapper, UnGoOnlineVehicleResponse> implements IVehicleStatService {

    @Resource
    private VehicleMapper mapper;

	@Resource
	private IBladeDeptService deptService;

    @Override
    public List<VehicleCountAndType> findGoOnlineVehicleCountByType(RateRequest request) {
        return mapper.getGoOnlineVehicleCountAndType(request);
    }

	@Override
	public List<DateAndCount> findGoOnlineCountByDay(String startDate, String endDate, List<Long> deptIds, Long userId) throws Exception {
		List<DateAndCount> list = mapper.getGoOnlineCountByDay(startDate.replace("-", "").substring(0, 6), startDate, endDate, deptIds, userId);
		List<String> dateList = DateUtil.getDateList(startDate, endDate);
		Map<String, DateAndCount> map = new HashMap<>();
		list.forEach(item -> {
			String date = item.getStatDate();
			map.put(date, item);
		});
		//如果某个日期没有上线，则设置为0
		for(String date : dateList){
			if(!map.containsKey(date)){
				DateAndCount dc = new DateAndCount();
				dc.setStatDate(date);
				dc.setCount(0L);
				list.add(dc);
			}
		}
		Collections.sort(list, (o1, o2) -> {
			int d1 = Integer.parseInt(o1.getStatDate().replace("-",""));
			int d2 = Integer.parseInt(o2.getStatDate().replace("-",""));
			return d2 - d1;
		});
		return list;
	}

	@Override
	public List<DateAndCount> findGoOnlineCountByHour(Date startDate, Date endDate, List<Long> deptIds, Long userId) {
		return mapper.getGoOnlineCountByHour(startDate, endDate, deptIds, userId);
	}

	@Override
    public List<VehicleCountAndType> findInNetVehicleCountByType(RateRequest request) {
        return mapper.getInNetVehicleCountAndType(request);
    }

    @Override
    public IPage<UnGoOnlineVehicleResponse> findUnGoOnlineVehicle(UnGoOnlineQuery query) {
        Page page = new Page();
        page.setCurrent(query.getCurrent());
        page.setSize(query.getSize());
        return mapper.getUnGoOnlineVehicle(page , query);
    }

    @Override
    public List<UnGoOnlineVehicleResponse> findUnGoOnlineVehicleAll(UnGoOnlineQuery query) {
        return mapper.getUnGoOnlineVehicleAll( query);
    }

    @Override
    public int findInNetCountByOwnerId(String nextMonthFirstDay, long ownerId) {
        return baseMapper.getInNetCountByOwnerId(nextMonthFirstDay , ownerId);
    }

    @Override
    public int findGoOnlineCountByOwnerId(String month, long ownerId) {
        month = month + "%";
        return baseMapper.getGoOnlineCountByOwnerId(month , ownerId);
    }

	@Override
	public List<VehicleCountWithDept> findInNetCountByDeptId(String nextMonthFirstDay, List<Long> deptIds, Long ownerId) {
		return baseMapper.getInNetCountByDeptId(nextMonthFirstDay , deptIds , ownerId);
	}

	@Override
	public long findInNetCountByDate(Date date, List<Long> deptIds, Long userId) {
		return baseMapper.getInNetCountByDate(date, deptIds, userId);
	}

	@Override
    public List<VehicleCountWithDept> findGoOnlineCountByDeptId(String month, List<Long> deptIds , Long ownerId) {
        month = month + "%";
        return baseMapper.getGoOnlineCountByDeptId(month , deptIds , ownerId);
    }

    @Override
    public IPage<VehicleRateDetailResponse> findVehicleRateDetail(DetailParam param) {
        Page page = new Page();
        page.setCurrent(param.getCurrent());
        page.setSize(param.getSize());
        return baseMapper.getVehicleRateDetail(page , param);
    }

    @Override
    public IPage<DeptAndCount> findDeptAndInNetCountByPage(IPage page , String nextMonthFirstDay, Long ownerId) {
        return baseMapper.getDeptAndInNetCountByPage(page , nextMonthFirstDay , ownerId);
    }

	@Override
	public List<DateAndDeptAndCount> findDeptVehicleCountByDay(Long deptId, Long startTime, Long endTime, String vehicleUseType) throws Exception {
		//因为前台传入的是按天为单位的时间戳，所以这里：startTime当前天的开始时间戳，endTime为当前天的结束时间戳
		startTime = DateUtil.getDayFirstSecondTimestamp(startTime);
		endTime = DateUtil.getDayLastSecondTimestamp(endTime);
		return baseMapper.getDeptVehCountByDay(deptId , startTime , vehicleUseType , endTime);
	}

	@Override
	public List<DateAndDeptAndCount> findPassengerVehicleCountByDay(Long deptId, long startTime, long endTime) throws Exception {
		//因为前台传入的是按天为单位的时间戳，所以这里：startTime当前天的开始时间戳，endTime为当前天的结束时间戳
		startTime = DateUtil.getDayFirstSecondTimestamp(startTime);
		endTime = DateUtil.getDayLastSecondTimestamp(endTime);
		return baseMapper.getDeptPassengerVehCountByDay(deptId , startTime ,endTime);
	}

	@Override
	public VehicleCountWithDept findGoOnlineCountByDeptIdDeptOrArea(String month, Long deptId , Long ownerId) {
		month = month + "%";
		return baseMapper.getGoOnlineCountByDeptIdDeptOrArea(month , deptId , ownerId);
	}

	@Override
	public List<VehicleCountWithDept> statVehicleCountByDeptId(Long deptId, long ownerId ,int count , int start) throws Exception {
		//1. 如果 deptId 为空，则分页查询所有企业的超速信息
		List<Long> deptIds = new ArrayList<>();
		Map<Long,String> deptNamesMap = new HashMap<>();
		if(deptId != null){
			//如果指定了企业
			deptIds.add(deptId);
			BladeDept dept = deptService.getById(deptId);
			deptNamesMap.put(deptId,dept.getDeptName());
		}else{
			//如果没有指定企业
			//分页查询企业信息

			//租户id
			BladeUser user = AuthUtil.getUser();
			if(user == null){
				log.info("用户未登录或授权失败");
				throw new Exception("用户未登录或授权失败");
			}
			String tenantId = user.getTenantId();
			Page page = new Page();
			page.setSize(count);
			page.setCurrent(start / count + (start % count>0?1:0));

			IPage<BladeDept> depts = deptService.page(page , Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId , tenantId).eq(BladeDept::getIsDeleted ,0));
			for(BladeDept dept : depts.getRecords()){
				deptIds.add(dept.getId());
				deptNamesMap.put(dept.getId() , dept.getDeptName());
			}
		}

		List<VehicleCountWithDept> list = baseMapper.getTotalCountByDeptId(deptIds,ownerId);
		return list;
	}

	@Override
	public VehicleCountWithDept findVehicleCountByDeptIdDeptOrArea(Long deptId, long ownerId) {
		return baseMapper.getTotalCountByDeptIdDeptOrArea(deptId , ownerId);
	}

	@Override
	public VehicleCountWithDept findInNetCountByDeptIdDeptOrArea(String nextMonthFirstDay, Long deptId , long ownerId) {
		return baseMapper.getInNetCountByDeptIdDeptOrArea(nextMonthFirstDay , deptId , ownerId);
	}

	@Override
	public List<DateAndCountAndRate> statGoOnlineCountAndRateIn30Days(String startDate, String endDate, List<Long> deptIds, Long userId) throws Exception {

		int inNetCount = 0;

		//查询入网车辆数
		List<VehicleCountWithDept> deptVehicleList = findInNetCountByDeptId(startDate, deptIds, null);
		for(VehicleCountWithDept vd : deptVehicleList){
			inNetCount += vd.getCount();
		}

		//查询上线数
		List<DateAndCount> goOnlineCountList = findGoOnlineCountByDay(startDate, endDate, deptIds, userId);

		//计算上线率
		List<DateAndCountAndRate> resList = new ArrayList<>();
		for (DateAndCount dc : goOnlineCountList) {
			DateAndCountAndRate response = new DateAndCountAndRate();
			response.setDate(dc.getStatDate());
			response.setCount(dc.getCount());
			if(inNetCount <= 0){
				response.setRate(0);
			}else {
				response.setRate(MathUtil.divideRoundDouble(dc.getCount(), inNetCount, 4));
			}
			response.setRateStr(MathUtil.formatToPercent(response.getRate(), 2) );
			resList.add(response);
		}

		return resList;
	}


	@Override
	public double statDeptMileage(String statDate, List<Long> deptIds, Long userId) throws Exception {
		String month = statDate.substring(0,7).replace("-","");
		statDate = statDate.replace("-","");
		return baseMapper.getDeptMileage(statDate, month, deptIds, userId);
	}

	@Override
	public double statDeptMileageForApp(String statDate, List<Long> deptIds, Long userId) throws Exception {
		String month = statDate.substring(0,6).replace("-","");
		statDate = statDate.replace("-","");
		return baseMapper.getDeptMileage(statDate, month, deptIds, userId);
	}

	@Override
	public List<VehicleAndData> findMaxSpeedListByVehicleId(String statDate, List<Integer> vehicleIds) throws Exception {
		String month = statDate.substring(0,7).replace("-","");
		String date = statDate.replace("-","");
		return baseMapper.getMaxSpeedListByVehicleId(date, month, vehicleIds);
	}

	@Override
	public List<VehicleAndData> findMaxSpeedListMonthByVehicleId(String month, List<Integer> vehicleIds) throws Exception {
		month = month.replace("-","");
		return baseMapper.getMaxSpeedMonthByVehicleId(month, vehicleIds);
	}

	@Override
	public double statDeptMileageMonth(String month, List<Long> deptIds, Long userId) throws Exception {
		month = month.replace("-","");
		return baseMapper.getDeptMileageMonth(month,deptIds,userId);
	}

	@Override
	public long statDeptDuration(String month, String statDate, List<Long> deptIds, List<Integer> vehicleIds) throws Exception {
		month = month.replace("-","");
		return baseMapper.getDeptDuration(statDate,month, deptIds, vehicleIds);
	}

	@Override
	public long statDeptDurationMonth(String month, String lastDateInMonth, List<Long> deptIds, List<Integer> vehicleIds) {
		return this.baseMapper.getDeptDurationMonth(month.replace("-",""), month.concat("-01"), lastDateInMonth, deptIds, vehicleIds);
	}
}
