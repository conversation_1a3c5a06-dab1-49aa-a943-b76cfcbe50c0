package com.xh.vdm.statistic.entity;

import lombok.Data;

/**
 * @Description: 轨迹完整率数据节点
 * @Author: zhouxw
 * @Date: 2022/9/5 9:36 AM
 */
@Data
public class CompleteDataNode {

    //车牌号
    private String licencePlate;

    //车牌颜色
    private int plateColor;

    //轨迹完整率
    private double completeRate;

    //全部点数
    private int allPointCount;

    //完整点数
    private int completePointCount;

    //全部里程
    private double allMiles;

    //完整里程
    private double completeMiles;

	//车辆id
	private Integer vehicleId;
	//部门id
	private Long deptId;
}
