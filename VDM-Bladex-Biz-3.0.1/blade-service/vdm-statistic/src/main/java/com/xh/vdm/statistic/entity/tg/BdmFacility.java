package com.xh.vdm.statistic.entity.tg;

import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 基础设施管理实体
 */
@Data
public class BdmFacility implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	@Compare("设施名称")
	private String name;
	@Compare("设施类型")
	private Integer category;

	private String address;

	private Integer targetType;
	@Compare("所属机构")
	private Long deptId;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;
	@Compare("点串坐标，描述基础设施范围")
	private String geometry;

	private String code;
}

