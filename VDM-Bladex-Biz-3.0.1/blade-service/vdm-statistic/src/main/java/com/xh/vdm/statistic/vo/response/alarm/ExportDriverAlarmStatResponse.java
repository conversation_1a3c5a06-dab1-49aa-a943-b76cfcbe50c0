package com.xh.vdm.statistic.vo.response.alarm;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel(value = "返回体：驾驶员报警统计导出")
@Data
public class ExportDriverAlarmStatResponse {

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"驾驶员报警统计", "驾驶员身份证"})
	private String idCard;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"驾驶员报警统计", "驾驶员姓名"})
	private String driverName;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"驾驶员报警统计", "单位名称"})
	private String deptName;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"驾驶员报警统计", "车牌颜色"})
	private String licenceColor;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"驾驶员报警统计", "车牌号"})
	private String licencePlate;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"驾驶员报警统计", "报警数"})
	private Integer numAlarm;
}
