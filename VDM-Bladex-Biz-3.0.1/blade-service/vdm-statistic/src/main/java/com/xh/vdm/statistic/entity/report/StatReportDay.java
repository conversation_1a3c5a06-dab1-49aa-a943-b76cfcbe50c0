package com.xh.vdm.statistic.entity.report;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 企业日报表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatReportDay implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 部门id
     */
    private Long deptId;

	//报告类型
	private String type;

    /**
     * 统计日期
     */
    private String date;

    /**
     * 报表文件路径
     */
    private String proxyFileUrl;

	//文件保存路径
	private String filePath;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 更新日期
     */
    private Date updateTime;


}
