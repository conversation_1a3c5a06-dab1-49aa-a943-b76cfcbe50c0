package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.OnlineOfflineRecord;
import com.xh.vdm.statistic.entity.VehicleOnlineBase;
import com.xh.vdm.statistic.mapper.OnlineOfflineRecordMapper;
import com.xh.vdm.statistic.service.IOnlineOfflineRecordService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/12 12:25
 */
@Service
public class OnlineOfflineRecordServiceImpl extends ServiceImpl<OnlineOfflineRecordMapper, OnlineOfflineRecord> implements IOnlineOfflineRecordService {

	@Override
	public List<VehicleOnlineBase> findLastOnlineDate(List<Integer> vehicleIds) {
		return baseMapper.getLastOnlineDate(vehicleIds);
	}
}
