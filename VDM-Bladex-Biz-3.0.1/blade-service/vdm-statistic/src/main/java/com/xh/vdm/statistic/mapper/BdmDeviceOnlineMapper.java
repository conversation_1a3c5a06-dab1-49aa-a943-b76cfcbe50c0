package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.BdmDeviceOnline;
import com.xh.vdm.statistic.vo.request.BdmDeviceOnlineRequest;
import com.xh.vdm.statistic.vo.response.DeviceOnlineResponse;
import com.xh.vdm.statistic.vo.response.terminal.OnlineCountResponse;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.mp.support.Query;
import java.util.List;

/**
 * (BdmDeviceOnline)表数据库访问层
 */
public interface BdmDeviceOnlineMapper extends BaseMapper<BdmDeviceOnline> {

    List<DeviceOnlineResponse> queryByPage(@Param("bdo") BdmDeviceOnlineRequest bdo, @Param("query") Query query, @Param("userId") Long userId, @Param("deptIds") String deptIds, @Param("account") String account);

	long countOnline(@Param("bdo") BdmDeviceOnlineRequest bdo, @Param("userId") Long userId, @Param("deptIds") String deptIds, @Param("account") String account);

	List<OnlineCountResponse> getOnlineTrendered(@Param("userId") Long userId);
}

