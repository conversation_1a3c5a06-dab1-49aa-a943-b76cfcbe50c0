package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatNotLocation implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车牌号
     */
    private String licencePlate;

    /**
     * 车牌颜色
     */
    private String licenceColor;

    /**
     * 统计日期 yyyy-MM-dd
     */
    private String statDate;

    /**
     * 不定位开始时间
     */
    private Date startTime;

    /**
     * 不定位结束时间
     */
    private Date endTime;

    /**
     * 不定位开始位置
     */
    private String startAddr;

    /**
     * 不定位开始经度
     */
    private Double startLongitude;

    /**
     * 不定位开始纬度
     */
    private Double startLatitude;

    /**
     * 不定位结束位置
     */
    private String endAddr;

    /**
     * 不定位结束经度
     */
    private Double endLongitude;

    /**
     * 不定位结束纬度
     */
    private Double endLatitude;

    /**
     * 不定位持续时间
     */
    private Long duration;

    /**
     * 数据创建时间
     */
    private Date createTime;


}
