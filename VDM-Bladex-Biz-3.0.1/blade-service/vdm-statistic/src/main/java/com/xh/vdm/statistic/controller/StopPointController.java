package com.xh.vdm.statistic.controller;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.BamThirdPartyPlatform;
import com.xh.vdm.statistic.entity.BladeDept;
import com.xh.vdm.statistic.entity.DateListAndMonth;
import com.xh.vdm.statistic.service.IBamThirdPartyPlatformService;
import com.xh.vdm.statistic.service.IBladeDeptService;
import com.xh.vdm.statistic.service.IStatStopPointService;
import com.xh.vdm.statistic.utils.*;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.response.StopPointDetailResponse;
import com.xh.vdm.statistic.vo.response.StopPointStatResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

@RestController
@RequestMapping("/stat/stopPoint")
@Slf4j
public class StopPointController {


	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(20, 50, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

	@Resource
	private IStatStopPointService statStopPointService;

	@Autowired
	UserInfoUtil userInfoUtil;

	@Resource
	private IBladeDeptService bladeDeptService;

	@Resource
	private IBamThirdPartyPlatformService thirdPartyPlatformService;

	@Value("${static.file.path:/statistic/files/}")
	String staticFilePath;

	//前端代理的文件路径，用于在前端访问或者下载使用，可以使用nginx等工具代理
	@Value("${proxy.file.path:/bt/statistics/files/}")
	private String proxyFilePath ;

	@Resource
	private CommonBusiUtil commonBusiUtil;

	@Resource
	private CommonBusiUtil busiUtil;

	/**
	 * 车辆停车统计
	 * @param request
	 * @param user
	 * @return
	 */
	@PostMapping("/stopPointStatInfo")
	public R<IPage<StopPointStatResponse>> stopPointStatInfo(@RequestBody CommonBaseRequest request, Query query, BladeUser user){
		//1.计算统计时段
		try{
			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			request.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
			request.setProfessionList(vehicleUseTypes);
			request.setUserId(user.getUserId());

			CountDownLatch countDownLatch = new CountDownLatch(1);

			//查询部门信息、上级平台信息，用于后续手动增加字段
			Map<String,BladeDept> deptMap = new HashMap<>();
			Map<String,BamThirdPartyPlatform> platMap = new HashMap<>();
			threadPool.submit(() -> {
				try{
					List<BladeDept> depts = null;
					List<BamThirdPartyPlatform> platList = null;
					platList = thirdPartyPlatformService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled, 0));
					depts = bladeDeptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
					platList.forEach(item -> {
						platMap.put(item.getId()+"", item);
					});
					depts.forEach(item -> {
						deptMap.put(item.getId()+"", item);
					});
				}catch (Exception e){
					log.error("异步处理失败",e);
				}finally {
					countDownLatch.countDown();
				}
			});

			if(request.getStartTime() == null){
				//如果没有指定开始时间，则开始时间定到7天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 7);
				request.setStartTime(date.getTime()/1000);
			}
			if(request.getEndTime() == null){
				//如果没有指定结束时间，则结束时间定到前一天
				Date date = DateUtil.getDateBeforeDay(new Date(), 1);
				request.setEndTime(date.getTime() / 1000);
			}
			long startTime = request.getStartTime();
			long endTime = request.getEndTime();
			List<String> dateList = new ArrayList<>();
			List<DateListAndMonth> dmList = new ArrayList<>();
			List<String> monthList = DateUtil.getMonthList(startTime, endTime);
			if(monthList.size() == 1){
				//如果只有一个月份
				List<String> dateListTmp = DateUtil.getDateList(startTime, endTime);
				dateListTmp.forEach(item -> {
					String date = item.replace("-","");
					dateList.add(date);
				});
				DateListAndMonth dm = new DateListAndMonth();
				dm.setMonth(monthList.get(0).replace("-",""));
				dm.setDateList(dateList);
				dmList.add(dm);
			}else{
				for(int i = 0 ; i < monthList.size(); i++){
					DateListAndMonth dm = new DateListAndMonth();
					String month = monthList.get(i);
					long startTimeTmp = 0;
					long endTimeTmp = 0;
					//如果是第一个月
					if(i == 0){
						startTimeTmp = startTime;
						endTimeTmp = DateUtil.getMonthLastSecondTimestamp(startTimeTmp);
					}else if(i == monthList.size()-1){
						//如果是最后一个月
						endTimeTmp = endTime;
						startTimeTmp = DateUtil.getMonthFirstSecondTimestamp(endTime);
					}else {
						//如果是中间月
						endTimeTmp = DateUtil.getMonthLastSecondTimestamp(month);
						startTimeTmp = DateUtil.getMonthFirstSecondTimestamp(month);
					}
					//获取dateList
					List<String> dateListTmp = DateUtil.getDateList(startTimeTmp, endTimeTmp);
					dateListTmp.forEach(item -> {
						String date = item.replace("-","");
						dateList.add(date);
					});
					dm.setMonth(month.replace("-",""));
					dm.setDateList(dateList);
					dmList.add(dm);
				}
			}
			IPage<StopPointStatResponse> page = new Page<>();
			page.setCurrent(query.getCurrent());
			page.setSize(query.getSize());
			long start = System.currentTimeMillis();
			IPage<StopPointStatResponse> pageRes = statStopPointService.statStopInfo(request,dmList, page);
			if(pageRes == null || pageRes.getTotal() < 1){
				return R.data(page);
			}

			countDownLatch.await();
			//添加字段信息
			pageRes.getRecords().forEach(item -> {
				Long deptId = item.getDeptId();
				Long vehicleOwnerId = item.getVehicleOwnerId();
				BladeDept dept = deptMap.get(deptId+"");
				BamThirdPartyPlatform plat = platMap.get(vehicleOwnerId+"");
				if(dept != null){
					item.setDeptName(dept.getDeptName());
				}
				if(plat != null){
					item.setVehicleOwner(StringUtils.isEmpty(plat.getName())?"非营运车辆":plat.getName());
				}else{
					item.setVehicleOwner("非营运车辆");
				}
			});

			long end = System.currentTimeMillis();
			log.info("查询数据库耗时：" + (end - start));
			return R.data(pageRes);
		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询出错");
		}
	}


	/**
	 * 车辆停车统计 导出
	 * @param request
	 * @param user
	 * @return
	 */
	@PostMapping("/stopPointStatInfo/export")
	public R<String> stopPointStatInfoExport(@RequestBody CommonBaseRequest request, Query query, BladeUser user, String exportPath, String deptName){
		try{

			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<StopPointStatResponse>> pageRes = stopPointStatInfo(request, query, user);
			List<StopPointStatResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//补充vehicleUseType中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
				list.forEach(item -> {
					item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
					item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
					item.setAccessModeDesc(accessModeMap.get(item.getAccessMode()));
					//设置持续时间格式
					item.setStopDurationFormatStr(DateUtil.getFormatDateString(item.getStopDuration().longValue()));
				});
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "车辆停车统计";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , StopPointStatResponse.class,withDate);
			return R.data(fileName);
		}catch (Exception e){
			log.error("导出报错",e);
			return R.fail("导出出错");
		}
	}



	/**
	 * 车辆停车明细
	 * @param request
	 * @param user
	 * @return
	 */
	@PostMapping("/stopPointDetail")
	public R<IPage<StopPointDetailResponse>> stopPointDetail(@RequestBody CommonBaseRequest request, Query query, BladeUser user){
		//1.计算统计时段
		try{
			long currentTime = System.currentTimeMillis() / 1000;
			IPage<StopPointDetailResponse> page = new Page<>(query.getCurrent(), query.getSize());
			if (request.getStartTime() > currentTime) {
				return R.data(ResultCode.SUCCESS.getCode(), page, "");
			}

			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			request.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
			if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
				vehicleUseTypes.add(request.getVehicleUseType());
			}
			request.setProfessionList(vehicleUseTypes);
			request.setUserId(user.getUserId());

			CountDownLatch countDownLatch = new CountDownLatch(1);

			//查询部门信息、上级平台信息，用于后续手动增加字段
			Map<String,BladeDept> deptMap = new HashMap<>();
			Map<String,BamThirdPartyPlatform> platMap = new HashMap<>();
			threadPool.submit(() -> {
				try{
					List<BladeDept> depts = null;
					List<BamThirdPartyPlatform> platList = null;
					platList = thirdPartyPlatformService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled, 0));
					depts = bladeDeptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
					platList.forEach(item -> {
						platMap.put(item.getId()+"", item);
					});
					depts.forEach(item -> {
						deptMap.put(item.getId()+"", item);
					});
				}catch (Exception e){
					log.error("异步处理失败",e);
				}finally {
					countDownLatch.countDown();
				}
			});



			if(request.getStartTime() == null){
				//如果没有指定开始时间，则开始时间定到7天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 7);
				request.setStartTime(date.getTime()/1000);
			}
			if(request.getEndTime() == null){
				//如果没有指定结束时间，则结束时间定到前一天
				Date date = DateUtil.getDateBeforeDay(new Date(), 1);
				request.setEndTime(date.getTime() / 1000);
			}
			long startTime = request.getStartTime();
			long endTime = (request.getEndTime() > currentTime) ? currentTime : request.getEndTime();
			List<String> monthList = DateUtil.getMonthList(startTime, endTime);
			List<DateListAndMonth> dmList = new ArrayList<>();
			for(int i = 0 ; i < monthList.size(); i++){
				DateListAndMonth dm = new DateListAndMonth();
				String month = monthList.get(i);
				long startTimeTmp = 0;
				long endTimeTmp = 0;
				//如果是第一个月
				if (monthList.size() == 1) {
					startTimeTmp = startTime;
					endTimeTmp = endTime;
				} else if(i == 0){
					startTimeTmp = startTime;
					endTimeTmp = DateUtil.getMonthLastSecondTimestamp(startTimeTmp);
				}else if(i == monthList.size()-1){
					//如果是最后一个月
					endTimeTmp = endTime;
					startTimeTmp = DateUtil.getMonthFirstSecondTimestamp(endTime);
				}else {
					//如果是中间月
					endTimeTmp = DateUtil.getMonthLastSecondTimestamp(month);
					startTimeTmp = DateUtil.getMonthFirstSecondTimestamp(month);
				}
				//获取dateList
				List<String> dateListTmp = DateUtil.getDateList(startTimeTmp, endTimeTmp);
				List<String> dateList = new ArrayList<>();
				dateListTmp.forEach(item -> {
					String date = item.replace("-","");
					dateList.add(date);
				});
				dm.setMonth(month.replace("-",""));
				dm.setDateList(dateList);
				dmList.add(dm);
			}

			IPage<StopPointDetailResponse> pageRes = statStopPointService.findStopPointDetailInfo(request,dmList, page);


			countDownLatch.await();
			//添加字段信息
			pageRes.getRecords().forEach(item -> {
				Long deptId = item.getDeptId();
				Long vehicleOwnerId = item.getVehicleOwnerId();
				BladeDept dept = deptMap.get(deptId+"");
				BamThirdPartyPlatform plat = platMap.get(vehicleOwnerId+"");
				if(dept != null){
					item.setDeptName(dept.getDeptName());
				}
				if(plat != null){
					item.setVehicleOwner(StringUtils.isEmpty(plat.getName())?"非营运车辆":plat.getName());
				}else{
					item.setVehicleOwner("非营运车辆");
				}
			});


			if(pageRes.getRecords() != null && pageRes.getRecords().size() > 0){
				for(StopPointDetailResponse res : pageRes.getRecords()){
					res.setStopStartTimeStr(DateUtil.getDateTimeString(res.getStopStartTime()));
					res.setStopEndTimeStr(DateUtil.getDateTimeString(res.getStopEndTime()));
				}
			}

			return R.data(pageRes);
		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询出错");
		}
	}

	/**
	 * 车辆停车明细 导出
	 * @param request
	 * @param user
	 * @return
	 */
	@PostMapping("/stopPointDetail/export")
	public R<String> stopPointDetailExport(@RequestBody CommonBaseRequest request, Query query, BladeUser user, String exportPath, String deptName){
		try{

			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);


			//1.查询数据
			R<IPage<StopPointDetailResponse>> pageRes = stopPointDetail(request, query, user);
			List<StopPointDetailResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//补充vehicleUseType中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
				list.forEach(item -> {
					item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
					item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
					item.setAccessModeDesc(accessModeMap.get(item.getAccessMode()));
					//设置停车时长
					item.setStopDurationFormatStr(DateUtil.getFormatDateString(item.getStopDuration().longValue()));
				});
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "车辆停车明细报表";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , StopPointDetailResponse.class,withDate);
			return R.data(fileName);
		}catch (Exception e){
			log.error("导出报错",e);
			return R.fail("导出出错");
		}
	}
}
