<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xh.vdm.statistic.mapper.alarm.AlarmImpalaMapper">

    <select id="alarmStatics" resultType="com.xh.vdm.statistic.vo.response.terminal.AlarmCountResponse">
        SELECT
            CONCAT(
                SUBSTR(CAST(from_unixtime(a.start_time, 'yyyy-MM-dd') AS STRING), 6, 2),
                '/',
                SUBSTR(CAST(from_unixtime(a.start_time, 'yyyy-MM-dd') AS STRING), 9, 2)
                ) AS monthDay,
            COUNT(*) AS alarmCount
        FROM
            alarm a
        WHERE
            a.start_time >= (unix_timestamp() - 6 * 86400)
            <if test="deptList != null and deptList.size() gt 0 ">
                and a.dept_id in
                <foreach collection="deptList" item="deptId" index="index" open="(" close=")" separator=",">
                    <if test="index != 0 and index % 999 == 0">
                        #{deptId}) or a.dept_id in (
                    </if>
                    #{deptId}
                </foreach>
            </if>
        GROUP BY
            SUBSTR(CAST(from_unixtime(a.start_time, 'yyyy-MM-dd') AS STRING), 6, 2),
            SUBSTR(CAST(from_unixtime(a.start_time, 'yyyy-MM-dd') AS STRING), 9, 2)
        ORDER BY
            MIN(a.start_time) DESC;
    </select>

    <select id="count" resultType="java.lang.Long">
        select count(*) from alarm
    </select>
</mapper>
