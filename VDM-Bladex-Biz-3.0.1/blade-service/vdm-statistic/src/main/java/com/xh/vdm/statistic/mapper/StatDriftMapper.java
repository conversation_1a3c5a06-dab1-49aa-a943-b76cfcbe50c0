package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.response.DriftDetailResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 漂移表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
public interface StatDriftMapper extends BaseMapper<StatDrift> {


    /**
     * 漂移率数据入临时表
     * @param licencePlate
     * @param data
     */
    void insertTempBatch(@Param("list") List<DriftDataNode> list);

    /**
     * @description: 向漂移率表中插入新增的数据
     * @author: zhouxw
     * @date: 2022/9/5 11:48 AM
     * @param: [month：月份，day：第几天]
     * @return: void
     **/
    void insertNewData(@Param("month") String month , @Param("day") String day);

    /**
     * @description: 更新漂移率表中已经存在的数据
     * @author: zhouxw
     * @date: 2022/9/5 11:49 AM
     * @param: [month：月份，day：第几天]
     * @return: void
     **/
    void updateExistData(@Param("month") String month , @Param("day") String day);

    /**
     * @description: 创建 车辆已经存在 的车牌号临时表
     * @author: zhouxw
     * @date: 2022/9/5 3:28 PM
     * @param: [month]
     * @return: void
     **/
    void createLicencePlateTemp(@Param("month") String month);

    /**
     * @description: 插入数据到车牌号临时表
     * @author: zhouxw
     * @date: 2022/9/5 7:16 PM
     * @param: [month]
     * @return: void
     **/
    void insertLicencePlateTemp(@Param("month") String month);

    /**
     * @description: 根据行业类型查询漂移车辆数
     * @author: zhouxw
     * @date: 2022/9/9 8:48 AM
     * @param: [month]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DriftVehicleCount>
     **/
    List<DriftVehicleCount> getDriftVehicleCountByType(@Param("param") RateParam param);

    /**
     * @description: 查询车辆漂移明细
     * @author: zhouxw
     * @date: 2022/9/9 11:27 AM
     * @param: [param]
     * @return: com.baomidou.mybatisplus.core.metadata.IPage<java.util.List<com.xh.vdm.statistic.vo.response.DriftDetailResponse>>
     **/
    IPage<DriftDetailResponse> getDriftDetailList(IPage page, @Param("param") DetailParam param);

    /**
     * @description: 根据部门id获取漂移车辆数
     * @author: zhouxw
     * @date: 2022/9/13 6:09 PM
     * @param: [month, deptIds]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
     **/
    List<VehicleCountWithDept> getDriffCountByDeptId(@Param("month") String month , @Param("deptIds") List<Long> deptIds , @Param("ownerId") Long ownerId);

    /**
     * @description: 查询上线车辆数，按照 车组、上级平台、行业类型分组
     * @author: zhouxw
     * @date: 2023-03-88 14:01:54
     * @param: [month]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleOwnerAndDeptAndTypeAndCount>
     **/
    List<VehicleOwnerAndDeptAndTypeAndCount> getGoOnlineCountByGroup(String month);

    /**
     * @description: 查询漂移车辆数，按照 车组、上级平台、行业类型分组
     * @author: zhouxw
     * @date: 2023-03-88 14:07:19
     * @param: [month]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleOwnerAndDeptAndTypeAndCount>
     **/
    List<VehicleOwnerAndDeptAndTypeAndCount> getDriftCountByGroup(String month);

	/**
	 * @description: 根据部门id获取漂移车辆数
	 * 如果不指定企业id，就查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 6:09 PM
	 * @param: [month, deptIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	VehicleCountWithDept getDriffCountByDeptIdDeptOrArea(@Param("month") String month , @Param("deptId") Long deptId , @Param("ownerId") Long ownerId);

	/**
	 * 查询车辆指定月份的漂移次数
	 * @param request
	 * @param month yyyyMM
	 * @param dateList MM，如 01
	 * @return
	 */
	List<VehicleAndCount> getDriftCountMonth(@Param("request")CommonBaseCrossMonthRequest request, @Param("month") String month, @Param("dateList") List<String> dateList);
}
