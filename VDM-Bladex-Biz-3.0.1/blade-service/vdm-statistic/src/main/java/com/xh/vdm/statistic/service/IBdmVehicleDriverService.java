package com.xh.vdm.statistic.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.BdmVehicleDriver;
import com.xh.vdm.statistic.entity.DescAndCount;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public interface IBdmVehicleDriverService extends IService<BdmVehicleDriver> {

    /**
     * @description: 根据身份证号或者手机号查询驾驶员基本信息
     * @author: zhouxw
     * @date: 2022/11/18 4:12 PM
     * @param: [idCard]
     * @return: com.xh.vdm.statistic.entity.BdmVehicleDriver
     **/
    BdmVehicleDriver findDriverBaseInfo(String idCardOrPhone) throws Exception;

    /**
     * @description: 查询驾驶员总数
     * 如果不指定 deptId ，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 6:17 PM
     * @param: [deptId]
     * @return: int
     **/
    int findDriverCount(Long deptId) throws Exception;

    /**
     * @description: 查询从业资格证过期数量
     * 如果不指定 deptId ，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 6:25 PM
     * @param: [deptId]
     * @return: int
     **/
    int findCertExpirationCount(Long deptId) throws Exception;


    /**
     * @description: 查询从业资格证未过期数量
     * 如果不指定 deptId ，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 6:25 PM
     * @param: [deptId]
     * @return: int
     **/
    int findCertIntimeCount(Long deptId) throws Exception;

    /**
     * @description: 查询驾驶员年龄分布
     * 如果不指定 deptId ，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 6:25 PM
     * @param: [deptId]
     * @return: int
     **/
    List<DescAndCount> findDriverAgeGroup(Long deptId)throws Exception;


    /**
     * @description: 查询驾驶员驾龄分布
     * 如果不指定 deptId ，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 6:25 PM
     * @param: [deptId]
     * @return: int
     **/
    List<DescAndCount> findDriverDriveAgeGroup(Long deptId)throws Exception;

}
