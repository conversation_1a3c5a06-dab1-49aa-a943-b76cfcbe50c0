package com.xh.vdm.statistic.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudera.impala.hivecommon.api.IHiveClient;
import com.xh.vdm.statistic.config.ExcelExportFontAndStyleConfig;
import com.xh.vdm.statistic.config.ExcelExportRowHeightConfig;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.EasyExcelUtils;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.utils.OKHttpClientUtil;
import com.xh.vdm.statistic.vo.request.*;
import com.xh.vdm.statistic.vo.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.Dept;
import org.springblade.system.feign.ISysClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: 企业基本情况及经营情况统计分析
 * @Author: zhouxw
 * @Date: 2022/11/15 2:23 PM
 */
@RestController
@RequestMapping("/bt/statistics/company")
@Slf4j
public class CompanyStatController {

    private static ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM"));

    private static ThreadLocal<SimpleDateFormat> sdfHolderYMD = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    private static ThreadLocal<SimpleDateFormat> sdfHolderAll = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    //平台连通率接口ip
    @Value("${connection-rate.ip}")
    private String connectionIp;

    //平台连通率接口port
    @Value("${connection-rate.port}")
    private String connectionPort;

    //平台连通率接口url
    @Value("${connection-rate.url}")
    private String connectionUrl;

    @Value("${static.file.path:/statistic/files/}")
    private String staticFilePath;


    @Resource
    private IStatOverSpeedService overSpeedService;

    @Resource
    private IStatFatigueService fatigueService;

    @Resource
    private IBamThirdPartyPlatformService platformService;

    @Resource
    private IVehicleStatService vehicleStatService;

    @Resource
    private ScoreController scoreController;

    @Resource
    private IBdmVehicleDriverService driverService;

    @Resource
    private IStatVehTravelDurationService vehTravelDurationService;

    @Resource
    private IStatDriveStatusService driveStatusService;

    @Resource
    private ITerminalService terminalService;

    @Resource
    private IBladeDeptService deptService;

    @Resource
    private IBdmSecurityService securityService;

    @Value("${default.owner_id:1}")
    private Long OWNER_ID ;


    /**
     * @description: 企业运营评估--同比环比分析
     * 同比：今年2月与去年2月相比
     * 环比：今年2月与今年1月相比
     * @author: zhouxw
     * @date: 2022/11/28 12:26 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.xh.vdm.statistic.vo.response.SameAndChainCompareResponse>
     **/
    @PostMapping("/statSameAndChainCompare")
    public R<SameAndChainCompareResponse> statSameAndChainCompare(@Validated @RequestBody DeptAndDurationNoPageRequest request){

        SameAndChainCompareResponse res = new SameAndChainCompareResponse();
        try{
            //1。如果不输入企业，则查询全平台
            //2。如果不输入月份，则默认统计上个月份
            if(StringUtils.isBlank(request.getMonth())){
                request.setMonth(DateUtil.getLastMonthString());
            }

            //3。统计车辆数（上线）
            //查询月份车辆数
            int vehicleCount = 0;
            try {
                VehicleCountWithDept vehicleCountDept = vehicleStatService.findGoOnlineCountByDeptIdDeptOrArea(request.getMonth(), request.getDeptId(), OWNER_ID);
                vehicleCount = vehicleCountDept == null ? 0 : vehicleCountDept.getCount();
            }catch (Exception e){
                log.error("查询指定月份车辆数失败",e);
            }
            //查询月份 上个月 车辆数
            int vehicleCountPreMonth = 0;
            try {
                VehicleCountWithDept vehicleCountDeptPreMonth = vehicleStatService.findGoOnlineCountByDeptIdDeptOrArea(DateUtil.getLastMonthString(request.getMonth()), request.getDeptId(), OWNER_ID);
                vehicleCountPreMonth = vehicleCountDeptPreMonth == null ? 0 : vehicleCountDeptPreMonth.getCount();
            }catch (Exception e){
                log.error("查询上个月车辆数失败",e);
            }
            //查询月份 前一年相同月份 车辆数
            int vehicleCountPreYear = 0;
            try {
                VehicleCountWithDept vehicleCountDeptPreYear = vehicleStatService.findGoOnlineCountByDeptIdDeptOrArea(DateUtil.getLastYearMonthString(request.getMonth()), request.getDeptId(), OWNER_ID);
                vehicleCountPreYear = vehicleCountDeptPreYear == null ? 0 : vehicleCountDeptPreYear.getCount();
            }catch (Exception e){
                log.error("查询前一年相同月份车辆数失败",e);
            }

            //环比
            double vehicleCountChain = vehicleCountPreMonth==0?0:MathUtil.roundDouble((vehicleCount - vehicleCountPreMonth) / vehicleCountPreMonth , 4);
            String vehicleCountChainStr = vehicleCountPreMonth==0?"100%":MathUtil.formatToPercent( vehicleCountChain, 1);
            //同比
            double vehicleCountSame = vehicleCountPreYear==0?0:MathUtil.roundDouble((vehicleCount - vehicleCountPreYear) / vehicleCountPreYear , 4);
            String vehicleCountSameStr = vehicleCountPreYear==0?"100%":MathUtil.formatToPercent(vehicleCountSame , 1);

            res.setVehicleCountChain(vehicleCountChain);
            res.setVehicleCountChainStr(vehicleCountChainStr);
            res.setVehicleCountSame(vehicleCountSame);
            res.setVehicleCountSameStr(vehicleCountSameStr);

            //4。统计驾驶员数（出勤驾驶员数量）
            //查询月份驾驶员数
            int driverCount = 0;
            try {
                driverCount = driveStatusService.findDriverCountByDate(request.getMonth().replace("-", ""), null, request.getDeptId());
            }catch (Exception e){
                log.error("查询指定月份驾驶员数失败",e);
            }
            //查询月份 上个月 驾驶员数
            int driverCountPreMonth = 0;
            try {
                driverCountPreMonth = driveStatusService.findDriverCountByDate(DateUtil.getLastMonthString(request.getMonth()).replace("-", ""), null, request.getDeptId());
            }catch (Exception e){
                log.error("查询上个月驾驶员数失败",e);
            }
            //查询月份 前一年相同月份 驾驶员数
            int driverCountPreYear = 0;
            try{
                driverCountPreYear = driveStatusService.findDriverCountByDate(DateUtil.getLastYearMonthString(request.getMonth()).replace("-",""), null ,request.getDeptId());
            }catch (Exception e){
                log.error("查询前一年相同月份驾驶员数失败",e);
            }


            //环比
            double driverCountChain = driverCountPreMonth==0?0:MathUtil.roundDouble((driverCount - driverCountPreMonth) / driverCountPreMonth , 4);
            String driverCountChainStr = driverCountPreMonth==0?"100%":MathUtil.formatToPercent( vehicleCountChain, 1);
            //同比
            double driverCountSame = driverCountPreYear==0?0:MathUtil.roundDouble((driverCount - driverCountPreYear) / driverCountPreYear , 4);
            String driverCountSameStr = driverCountPreYear==0?"100%":MathUtil.formatToPercent(vehicleCountSame , 1);

            res.setDriverCountChain(driverCountChain);
            res.setDriverCountChainStr(driverCountChainStr);
            res.setDriverCountSame(driverCountSame);
            res.setDriverCountSameStr(driverCountSameStr);


            //5。统计平均驾驶时长（日平均驾驶时长：月总时长/天数）
            //查询月份平均驾驶时长
            double totalDuration = 0;
            try {
                totalDuration = driveStatusService.findTotalDriveDurationByDate(request.getMonth().replace("-", ""), null, request.getDeptId());
            }catch (Exception e){
                log.error("查询指定月份平均驾驶时长失败",e);
            }
            double days = DateUtil.getDaysCountInMonth(request.getMonth());
            double averageDuration = days==0?0:totalDuration / days;

            //查询月份 上个月 平均驾驶时长
            double totalDurationPreMonth = 0;
            try {
                totalDurationPreMonth = driveStatusService.findTotalDriveDurationByDate(DateUtil.getLastMonthString(request.getMonth()).replace("-", ""), null, request.getDeptId());
            }catch (Exception e){
                log.error("查询上个月平均驾驶时长失败",e);
            }
            double daysPreMonth = DateUtil.getDaysCountInMonth(DateUtil.getLastMonthString(request.getMonth()) );
            double averageDurationPreMonth = daysPreMonth==0?0:totalDurationPreMonth / daysPreMonth;

            //查询月份 前一年相同月份 平均驾驶时长
            double totalDurationPreYear = 0;
            try {
                totalDurationPreYear = driveStatusService.findTotalDriveDurationByDate(DateUtil.getLastYearMonthString(request.getMonth()).replace("-", ""), null, request.getDeptId());
            }catch (Exception e){
                log.error("查询前一年相同月份的平均驾驶时长失败",e);
            }
            double daysPreYear = DateUtil.getDaysCountInMonth(DateUtil.getLastYearMonthString(request.getMonth()));
            double averageDurationPreYear = daysPreYear==0?0:totalDurationPreYear / daysPreYear;

            //环比
            double averageDurationChain = averageDurationPreMonth==0?0:MathUtil.roundDouble((averageDuration - averageDurationPreMonth) / averageDurationPreMonth , 4);
            String averageDurationChainStr = averageDurationPreMonth==0?"100%":MathUtil.formatToPercent( averageDurationChain, 1);
            //同比
            double averageDurationChainSame = averageDurationPreYear==0?0:MathUtil.roundDouble((averageDuration - averageDurationPreYear) / averageDurationPreYear , 4);
            String averageDurationChainSameStr = averageDurationPreYear==0?"100%":MathUtil.formatToPercent(averageDurationChainSame , 1);

            res.setAverageDriveDurationChain(averageDurationChain);
            res.setAverageDriveDurationChainStr(averageDurationChainStr);
            res.setAverageDriveDurationSame(averageDurationChainSame);
            res.setAverageDriveDurationSameStr(averageDurationChainSameStr);

            //6。统计平均驾驶天数(总天数/驾驶员数，因为驾驶员数一定，所以这里只用计算总天数即可)
            //查询月份 平均驾驶天数
            int driveDays = 0;
            try {
                driveDays = driveStatusService.findTotalDriveDaysInMonth(request.getMonth().replace("-", ""), request.getDeptId());
            }catch (Exception e){
                log.error("查询指定月份平均驾驶天数失败",e);
            }
            //查询月份 上个月 平均驾驶时长
            int driveDaysPreMonth = 0;
            try {
                driveDaysPreMonth = driveStatusService.findTotalDriveDaysInMonth(DateUtil.getLastMonthString(request.getMonth()).replace("-", ""), request.getDeptId());
            }catch (Exception e){
                log.error("查询上个月平均驾驶时长失败",e);
            }
            //查询月份 前一年相同月份 平均驾驶天数
            int driveDaysPreYear = 0;
            try {
                driveDaysPreYear = driveStatusService.findTotalDriveDaysInMonth(DateUtil.getLastYearMonthString(request.getMonth()).replace("-", ""), request.getDeptId());
            }catch (Exception e){
                log.error("查询前一年相同月份平均驾驶天数失败",e);
            }

            //环比
            double averageDaysChain = driveDaysPreMonth==0?0:MathUtil.roundDouble((driveDays - driveDaysPreMonth) / driveDaysPreMonth , 4);
            String averageDaysChainStr = driveDaysPreMonth==0?"100%":MathUtil.formatToPercent( averageDaysChain, 1);
            //同比
            double averageDaysChainSame = driveDaysPreYear==0?0:MathUtil.roundDouble((driveDays - driveDaysPreYear) / driveDaysPreYear , 4);
            String averageDaysChainSameStr = driveDaysPreYear==0?"100%":MathUtil.formatToPercent(averageDaysChainSame , 1);

            res.setAverageDriveDaysChain(averageDaysChain);
            res.setAverageDriveDaysChainStr(averageDaysChainStr);
            res.setAverageDriveDaysSame(averageDaysChainSame);
            res.setAverageDriveDaysSameStr(averageDaysChainSameStr);

            //7。统计平均驾驶里程（总里程/上线车辆数）
            //查询月份 平均驾驶里程
            double totalMileage = 0;
            try{
                totalMileage = driveStatusService.findTotalMileageInMonth(request.getDeptId() , request.getMonth().replace("-",""));
            }catch (Exception e){
                log.error("查询指定月份平均驾驶里程失败",e);
            }
            double averageMileage = vehicleCount==0?0:MathUtil.roundDouble((totalMileage / vehicleCount) , 4);

            //查询月份 上个月 平均驾驶里程
            double totalMileagePreMonth = 0;
            try {
                totalMileagePreMonth = driveStatusService.findTotalMileageInMonth(request.getDeptId(), DateUtil.getLastMonthString(request.getMonth()).replace("-",""));
            }catch (Exception e){
                log.error("查询上个月平均驾驶里程失败",e);
            }
            double averageMileagePreMonth = vehicleCountPreMonth==0?0:MathUtil.roundDouble((totalMileagePreMonth / vehicleCountPreMonth) , 4);

            //查询月份 前一年相同月份 平均驾驶里程
            double totalMileagePreYear = 0;
            try {
                totalMileagePreYear = driveStatusService.findTotalMileageInMonth(request.getDeptId(), DateUtil.getLastYearMonthString(request.getMonth()).replace("-",""));
            }catch (Exception e){
                log.error("查询前一年相同月份平均驾驶里程失败",e);
            }
            double averageMileagePreYear = vehicleCountPreYear==0?0:MathUtil.roundDouble((totalMileagePreYear / vehicleCountPreYear) , 4);

            //环比
            double averageMileageChain = averageMileagePreMonth==0?0:MathUtil.roundDouble((averageMileage - averageMileagePreMonth) / averageMileagePreMonth , 4);
            String averageMileageChainStr = averageMileagePreMonth==0?"100%":MathUtil.formatToPercent( averageMileageChain, 1);
            //同比
            double averageMileageChainSame = averageMileagePreYear==0?0:MathUtil.roundDouble((averageMileage - averageMileagePreYear) / averageMileagePreYear , 4);
            String averageMileageChainSameStr = averageMileagePreYear==0?"100%":MathUtil.formatToPercent(averageMileageChainSame , 1);

            res.setAverageDriveMileageChain(averageMileageChain);
            res.setAverageDriveMileageChainStr(averageMileageChainStr);
            res.setAverageDriveMileageSame(averageMileageChainSame);
            res.setAverageDriveMileageSameStr(averageMileageChainSameStr);

            return R.data(res);

        }catch (Exception e){
            log.error("同比环比分析失败",e);
            return R.fail("同比环比分析失败");
        }
    }


    /**
     * @description: 企业运营评估 -- 考核分析
     * 如果指定企业，则查询企业的考核结果；如果不指定企业，则分页查询所有企业每个企业的考核结果
     * @author: zhouxw
     * @date: 2022/11/16 8:49 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.core.metadata.PageList<com.xh.vdm.statistic.vo.response.CompanyScoreResponse>>
     **/
    @PostMapping("/statCompanyScore")
    public R<IPage<CompanyScoreResponse>> statCompanyScore (@RequestBody @Validated CompanyScoreRequest request){
        try{

            if(request.getCount() == null || request.getCount() < 1){
                //如果不传递页面size，那么就默认设置为10
                request.setCount(10);
            }
            if(request.getStart() == null || request.getStart() < 1){
                request.setStart(1);
            }
            if(StringUtils.isBlank(request.getMonth())){
                String monthStr = sdfHolder.get().format(new Date());
                request.setMonth(monthStr);
            }

            List<CompanyScoreResponse> list = findCompanyScoreData(request);

            IPage pageF = new Page();
            pageF.setSize(request.getCount());
            pageF.setCurrent(request.getStart()/request.getCount()+(request.getStart() % request.getCount()>0?1:0));
            //查询企业总数
			//租户id
			BladeUser user = AuthUtil.getUser();
			if(user == null){
				log.info("用户未登录或授权失败");
				throw new Exception("用户未登录或授权失败");
			}
			String tenantId = user.getTenantId();
			long totalCount = deptService.count( Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId , tenantId).eq(BladeDept::getIsDeleted ,0));
            pageF.setTotal(totalCount);
            pageF.setRecords(list);

            return R.data(pageF);
        }catch (Exception e){
            log.error("统计考核分析失败",e);
            return R.fail("统计考核分析失败");
        }
    }

    /**
     * @description: 导出企业分析结果
     * @author: zhouxw
     * @date: 2022/12/1 8:52 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<java.lang.String>
     **/
    @PostMapping("/exportCompanyScore")
    public R<String> exportCompanyScore (@RequestBody @Validated CompanyScoreRequest request){
        try{

            request.setStart(0);
            request.setCount(Integer.MAX_VALUE);

            List<CompanyScoreResponse> list = findCompanyScoreData(request);

            //导出数据
            String title = "企业考核分析";
            Date date = new Date();
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
            String dateStr = format.format(date);
            String fileName = title + "_" + dateStr + ".xlsx";
            FileOutputStream file = new FileOutputStream(staticFilePath + fileName);
            fileName =  "/bt/statistics/files/"+fileName;

            //主标题和副标题在excel中分别是是第0和第1行
            List<Integer> columnIndexes = Arrays.asList(0,1);
            //自定义标题和内容策略(具体定义在下文)
            ExcelExportFontAndStyleConfig cellStyleStrategy =
                    new ExcelExportFontAndStyleConfig(columnIndexes,new WriteCellStyle(), new WriteCellStyle());

            EasyExcel.write(file, CompanyScoreResponse.class).registerWriteHandler(cellStyleStrategy).registerWriteHandler(new ExcelExportRowHeightConfig()).sheet(title).doWrite(list);
            return R.data(fileName);
        }catch (Exception e){
            log.error("导出企业考核信息失败",e);
            return R.fail("导出企业考核信息失败");
        }
    }

    /**
     * @description: 查询企业考核信息
     * @author: zhouxw
     * @date: 2022/12/1 8:44 AM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.CompanyScoreResponse>
     **/
    private List<CompanyScoreResponse> findCompanyScoreData (CompanyScoreRequest request) throws Exception{
        if(request.getCount() < 1){
            //如果不传递页面size，那么就默认设置为10
            request.setCount(10);
        }
        if(request.getStart() < 1){
            request.setStart(1);
        }

		//租户id
		BladeUser user = AuthUtil.getUser();
		if(user == null){
			log.info("用户未登录或授权失败");
			throw new Exception("用户未登录或授权失败");
		}
		String tenantId = user.getTenantId();




		//日期校验
        String month = request.getMonth();

        //如果没有指定统计月份，那么就采用上个月的月份
        if(StringUtils.isBlank(month)){
            month = DateUtil.getLastMonthString();
            request.setMonth(month);
        }

        //如果输入的日期格式错误，则采用上个月的月份
        try{
            sdfHolder.get().parse(month);
        }catch (Exception e){
            log.info("统计月份[{}]格式错误，采用默认月份(上个月)",month);
            month = DateUtil.getLastMonthString();
            request.setMonth(month);
        }

        //本月的天数
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdfHolder.get().parse(request.getMonth()));
        int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

        //1.复用考核模块指标统计：入网率、数据合格率、轨迹完整率、漂移率
        CompanyRateRequest scoreRequest = new CompanyRateRequest();
        scoreRequest.setOwnerId(OWNER_ID);
        scoreRequest.setDeptId(request.getDeptId());
        scoreRequest.setMonth(request.getMonth());
        scoreRequest.setStart(request.getStart());
        scoreRequest.setCount(request.getCount());
        List<CompanyRateResponse> scoreList = scoreController.getCompanyRateList(scoreRequest , false);

        //统计平台连通率
        ConnectionNode connectionNode = getConnectionRateAndScore(request.getMonth());
        String connectionRate = connectionNode.getConnectivity()+"%";
        double connectionScore = connectionNode.getFraction();

        //存入map
        Map<String, CompanyRateResponse> scoreMap = new HashMap<>();
        scoreList.stream().forEach(item -> {
            scoreMap.put(item.getDeptName() , item);
        });

        //2.统计超速指标
        List<OverSpeedWithDeptAndDate> overSpeedList = overSpeedService.statOverSpeedAverageCount(request.getDeptId() , month , request.getCount() , request.getStart());

        //存入map
        Map<String,OverSpeedWithDeptAndDate> overSpeedMap = new HashMap<>();
        overSpeedList.stream().forEach(item -> {
            overSpeedMap.put(item.getDeptName() , item);
        });

        //查询区域内总上线车辆数
        int totalGoOnlineCount = vehicleStatService.findGoOnlineCountByOwnerId(month , OWNER_ID);

        //查询区域平均超速次数（兵团）
        Date mon = sdfHolder.get().parse(month);
        long startSecondTimestamp = DateUtil.getMonthFirstSecondTimestamp(mon.getTime() / 1000);
        long endSecondTimestamp = DateUtil.getMonthLastSecondTimestamp(mon.getTime() / 1000);
        OverSpeedWithDeptAndDate areaOverSpeed = overSpeedService.findOverSpeedAverageCount(null , startSecondTimestamp , endSecondTimestamp);
        int totalAreaOverSpeedCount = areaOverSpeed.getCount();
        double areaOverSpeedAverage = totalAreaOverSpeedCount / totalGoOnlineCount / daysInMonth;


        //3.统计疲劳指标
        List<FatigueWithDeptAndDate> fatigueList = fatigueService.statFatigueAverageSecond(request.getDeptId() , month , request.getCount() , request.getStart());

        //存入map
        Map<String,FatigueWithDeptAndDate> fatigueMap = new HashMap<>();
        fatigueList.stream().forEach(item -> {
            fatigueMap.put(item.getDeptName() , item);
        });

        //查询区域平均疲劳时长（兵团）
        FatigueWithDeptAndDate fatigue = fatigueService.findFatigueAverageSecond(null , startSecondTimestamp , endSecondTimestamp);
        long totalFatigueDuration = fatigue.getFatigueSecond();
        double areaFatigueAverage = totalFatigueDuration / totalGoOnlineCount / daysInMonth;


        //4.统计入网率
        //获取车辆总数
        List<VehicleCountWithDept> vehicleCountList = vehicleStatService.statVehicleCountByDeptId(request.getDeptId() , OWNER_ID , request.getCount() , request.getStart());

        //存入map
        Map<String,VehicleCountWithDept> vehicleCountMap = new HashMap<>();
        vehicleCountList.stream().forEach(item -> {
            vehicleCountMap.put(item.getDeptName() , item);
        });



        //6.数据拼装
        Long deptId = request.getDeptId();
        List<String> deptNames = new ArrayList<>();
        if(deptId != null){
            //如果指定了企业
            BladeDept dept = deptService.getById(deptId);
            deptNames.add(dept.getDeptName());
        }else{
            //如果没有指定企业
            //分页查询企业信息
            Page page = new Page();
            page.setSize(request.getCount());
            page.setCurrent(request.getStart() / request.getCount() + (request.getStart() % request.getCount()>0?1:0));

            IPage<BladeDept> depts = deptService.page(page , Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId , tenantId).eq(BladeDept::getIsDeleted ,0));
            for(BladeDept dept : depts.getRecords()){
                deptNames.add(dept.getDeptName());
            }
        }
        List<CompanyScoreResponse> responses = new ArrayList<>();
        for(int i = 0 ; i < deptNames.size() ; i++){
            CompanyScoreResponse csr = new CompanyScoreResponse();
            String deptName = deptNames.get(i);
            csr.setDeptName(deptName);

            //入网率、数据合格率、轨迹完整率、漂移率
            CompanyRateResponse crr = scoreMap.get(deptName);
            csr.setMonth(crr.getStatDate());
            //入网数
            int inNetCount = crr.getIntNetCount();

            //上线数
            int goOnlineCount = crr.getGoOnlineCount();

            //车辆总数
            int totalCount = vehicleCountMap==null||vehicleCountMap.get(deptName)==null?0:vehicleCountMap.get(deptName).getCount();
            crr.setDeptName(deptName);


            //*入网率
            double inNetRate = totalCount==0?0:MathUtil.divideRoundDouble(inNetCount , totalCount , 4);
            String inNetRateStr = MathUtil.formatToPercent(inNetRate , 2);
            double inNetRateScore = 0;
            //入网率低于 90% 不得分
            if(inNetRate >= 0.9){
                inNetRateScore = inNetRate * 5;
            }
            csr.setInNetRate(inNetRate);
            csr.setInNetRateStr(inNetRateStr);
            csr.setInNetScore(inNetRateScore);

            //*上线率
            String goOnlineRateStr = crr.getGoOnlineRate();
            double goOnlineRateD = crr.getGoOnlineRateD();
            double goOnlineRateScore = goOnlineRateD * 5;
            csr.setGoOnlineRate(goOnlineRateD);
            csr.setGoOnlineRateStr(MathUtil.formatToPercent(goOnlineRateD,2));
            csr.setGoOnlineRateScore(goOnlineRateScore);

            //*轨迹完整率
            String completeRateStr = crr.getCompleteRate();
            double completeRateD = crr.getCompleteRateD();
            double completeRateScore = 0;
            //轨迹完整率低于70%不得分
            if(completeRateD >= 0.7){
                completeRateScore = completeRateD * 15;
            }
            csr.setCompleteRate(completeRateD);
            csr.setCompleteRateStr(MathUtil.formatToPercent(completeRateD ,2));
            csr.setCompleteScore(completeRateScore);

            //*数据合格率
            String qualityRateStr = crr.getQualifiedRate();
            double qualityRateD = crr.getQualifiedRateD();
            double qualityRateScore = 0;
            if(qualityRateD >= 0.8){
                qualityRateScore = qualityRateD * 15;
            }
            csr.setQualityRate(qualityRateD);
            csr.setQualityRateStr(MathUtil.formatToPercent(qualityRateD ,2));
            csr.setQualityScore(qualityRateScore);

            //*漂移率
            String driftRateStr = crr.getDriftRate();
            double driftRateD = crr.getDriftRateD();
            double driftScore = 0;
            if(driftRateD <= 0.05){
                driftScore = 10 - 10* driftRateD;
            }
            csr.setDriftRate(driftRateD);
            csr.setDriftRateStr(MathUtil.formatToPercent(driftRateD ,2));
            csr.setDriftScore(driftScore);

            //*平均车辆超速次数(总次数/上线车辆数/天数)
            OverSpeedWithDeptAndDate overSpeed = overSpeedMap.get(deptName);
            int overSpeedCount = overSpeed==null?0:overSpeed.getCount();
            double average = 0;
            try {
                average = goOnlineCount==0||daysInMonth==0?0:overSpeedCount / goOnlineCount / daysInMonth;
            }catch (Exception e){
                log.info("计算平均车辆超速次数失败",e);
            }
            double overSpeedAverageCount = MathUtil.roundDouble(average , 2);
            double overSpeedAverageScore = 0;
            if(overSpeedAverageCount <= areaOverSpeedAverage){
                //平均车辆超速次数 小于等于 区域平均超速次数
                overSpeedAverageScore = 10 + (areaOverSpeedAverage==0?0:(areaOverSpeedAverage - overSpeedAverageCount) / areaOverSpeedAverage) * 10;
            }else if (overSpeedAverageCount > areaOverSpeedAverage && overSpeedAverageCount < 2 * areaOverSpeedAverage){
                //平均车辆超速次数 大于 区域平均超速次数 ，并且 车辆超速次数小于 区域平均超速次数的2倍
                overSpeedAverageScore = areaOverSpeedAverage==0?0:((2 * areaOverSpeedAverage - overSpeedAverageCount ) / areaOverSpeedAverage) * 10;
            }else if(overSpeedAverageCount >= 2 * areaOverSpeedAverage){
                //平均车辆超速次数 大于 区域平均超速次数的2倍，不得分
                overSpeedAverageScore = 0;
            }
            csr.setOverSpeedCount(overSpeedAverageCount);
            csr.setOverSpeedScore(overSpeedAverageScore);

            //*平均疲劳驾驶时长(总时长/上线车辆数/天数)
            FatigueWithDeptAndDate fatigueD = fatigueMap.get(deptName);
            long fatigueSecond = fatigueD==null?0:fatigueD.getFatigueSecond();
            double averageFatigue = 0;
            try {
                averageFatigue = goOnlineCount==0||daysInMonth==0?0:fatigueSecond / goOnlineCount / daysInMonth;
            }catch (Exception e){
                log.info("计算平均疲劳驾驶时长失败",e);
            }
            double averageFatigueSecond = MathUtil.roundDouble(averageFatigue , 2);
            double averageFatigueScore = 0;
            if(averageFatigueScore <= areaFatigueAverage){
                //平均车辆超速次数 小于等于 区域平均超速次数
                averageFatigueScore = 10 + ((areaFatigueAverage - averageFatigueScore) / areaFatigueAverage) * 10;
            }else if (averageFatigueScore > areaFatigueAverage && averageFatigueScore < 2 * areaFatigueAverage){
                //平均车辆超速次数 大于 区域平均超速次数 ，并且 车辆超速次数小于 区域平均超速次数的2倍
                averageFatigueScore = ((2 * areaFatigueAverage - averageFatigueScore ) / areaFatigueAverage) * 10;
            }else if(averageFatigueScore >= 2 * areaFatigueAverage){
                //平均车辆超速次数 大于 区域平均超速次数的2倍，不得分
                averageFatigueScore = 0;
            }
            csr.setFatigueDuration(averageFatigueSecond);
            csr.setFatigueScore(averageFatigueScore);

            //平台查岗率，给满分 10
            csr.setCheckResponseRate("100%");
            csr.setCheckResponseScore(10);

            //平台连通率
            csr.setConnectionRate(MathUtil.divideRoundDouble(connectionNode.getConnectivity() , 100 , 4));
            csr.setConnectionRateStr(connectionRate);
            csr.setConnectionRateScore(connectionScore);

            responses.add(csr);
        }
        return responses;
    }

    /**
     * @description: 查询平台连通率
     * 查询企业的平台连通率，实际上是查询企业的上级平台的连通率，但是因为三师项目中只有一个上级平台，兵团，所以这里实际上是查询兵团的平台连通率
     * @author: zhouxw
     * @date: 2022/12/6 11:17 AM
     * @param: [deptId, month, count, start]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DeptAndConnectionRateAndScore>
     **/
    private ConnectionNode getConnectionRateAndScore(String month) throws Exception{

        //1。查询上级平台(三师项目中，上级平台只有一个，就是兵团，连通率不变)
        BamThirdPartyPlatform platform = platformService.getById(OWNER_ID);
        if(platform == null || StringUtils.isBlank(platform.getName())){
            return null;
        }
        String ownerName = platform.getName();

        //2。执行查询
        String url = "http://" + connectionIp + ":" + connectionPort + connectionUrl;
        Map<String,String> param = new HashMap<>();
        param.put("month" , month);
        param.put("upper_platform",ownerName);
        Map<String,String> header = new HashMap<>();
        header.put("Content-Type","application/json");
        String json = OKHttpClientUtil.doPost(url , new HashMap<>() , JSONObject.toJSONString(param));
        JSONObject jsonO = JSONObject.parseObject(json);
        ConnectionNode connection = null;
        if(jsonO != null){
            JSONArray jsonA = JSONArray.parseArray(JSONObject.toJSONString(jsonO.get("data")));
            if(jsonA != null && jsonA.size() > 0){
                connection = JSONObject.parseObject(JSONObject.toJSONString(jsonA.get(0)) , ConnectionNode.class);
            }
        }

        return connection;
    }


    /*public static void main(String[] args) throws Exception {
        String url = "http://**************:20403/regulatorycenter/assessment/connectivity";
        Map<String,String> param = new HashMap<>();
        param.put("month" , "2022-02");
        param.put("upper_platform","兵团内部监管平台");
        Map<String,String> header = new HashMap<>();
        header.put("Content-Type","application/json");
        String json = OKHttpClientUtil.doPost(url , new HashMap<>() , JSONObject.toJSONString(param));
        System.out.println(json);
        //sss
    }*/


    /**
     * @description: 考核得分趋势
     * @author: zhouxw
     * @date: 2022/11/16 8:40 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<java.util.List<com.xh.vdm.statistic.vo.response.ScoreTrendResponse>>
     **/
    @PostMapping("/statScoreTrend")
    public R<List<ScoreTrendResponse>> statScoreTrend (@RequestBody @Validated CompanyScoreRequest request){
        try{

            //日期校验
            String month = request.getMonth();

            //如果没有指定统计月份，那么就采用上个月的月份
            if(StringUtils.isBlank(month)){
                month = DateUtil.getLastMonthString();
                request.setMonth(month);
            }

            //如果输入的日期格式错误，则采用上个月的月份
            try{
                sdfHolder.get().parse(month);
            }catch (Exception e){
                log.info("统计月份[{}]格式错误，采用默认月份(上个月)",month);
                month = DateUtil.getLastMonthString();
                request.setMonth(month);
            }

            Calendar cal = Calendar.getInstance();
            cal.setTime(sdfHolder.get().parse(month));
            List<ScoreTrendResponse> list = new ArrayList<>();
            for(int i = 0 ; i < 12 ; i++){
                int monthIndex = cal.get(Calendar.MONTH)+1;
                String monthIndexStr = "";
                if(monthIndex < 10){
                    monthIndexStr = "0"+monthIndex;
                }else{
                    monthIndexStr = monthIndex+"";
                }
                String monStr = cal.get(Calendar.YEAR)+"-"+monthIndexStr;
                ScoreTrendResponse req = getScoreTrendNode(OWNER_ID ,request.getDeptId(),monStr);
                list.add(req);
                cal.add(Calendar.MONTH , -1);
            }

            //调整 list 顺序
            Collections.sort(list , Comparator.comparingInt(o -> Integer.parseInt(o.getMonth().replace("-", ""))));

            return R.data(list);
        }catch (Exception e){
            log.error("统计考核分数趋势失败",e);
            return R.fail("统计考核分数趋势失败");
        }
    }

    /**
     * @description: 统计指定月的考核得分趋势
     * @author: zhouxw
     * @date: 2022/11/16 8:49 PM
     * @param: [month]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.ScoreTrendResponse>
     **/
    private ScoreTrendResponse getScoreTrendNode( Long ownerId , Long deptId ,String month) throws Exception {
        ScoreTrendResponse response = new ScoreTrendResponse();
        response.setMonth(month);
        Date mon = sdfHolder.get().parse(month);

        //本月开始、结束时间戳
        long startSecondTimestamp = DateUtil.getMonthFirstSecondTimestamp(mon.getTime() / 1000);
        long endSecondTimestamp = DateUtil.getMonthLastSecondTimestamp(mon.getTime() / 1000);

        //本月的天数
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(sdfHolder.get().parse(month));
        int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);

        //1.复用考核模块指标统计：入网率、数据合格率、轨迹完整率、漂移率
        CompanyRateResponse scoreDept = scoreController.getCompanyRateListDeptOrArea(ownerId , deptId , month);

        //2.统计超速指标
        OverSpeedWithDeptAndDate overSpeedDept = overSpeedService.findOverSpeedAverageCount(deptId , startSecondTimestamp , endSecondTimestamp );

        //查询区域内总上线车辆数
        int totalGoOnlineCount = vehicleStatService.findGoOnlineCountByOwnerId(month , OWNER_ID);

        //查询区域平均超速次数（兵团）
        OverSpeedWithDeptAndDate areaOverSpeed = overSpeedService.findOverSpeedAverageCount(null , startSecondTimestamp , endSecondTimestamp);
        int totalAreaOverSpeedCount = areaOverSpeed==null?0:areaOverSpeed.getCount();
        double areaOverSpeedAverage = 0;
        try {
            areaOverSpeedAverage =totalGoOnlineCount==0||daysInMonth==0?0:totalAreaOverSpeedCount / totalGoOnlineCount / daysInMonth;
        }catch (Exception e){
            log.info("计算区域平均超速次数失败",e);
        }

        //3.统计疲劳指标
        FatigueWithDeptAndDate fatigueDept = fatigueService.findFatigueAverageSecond(deptId , startSecondTimestamp , endSecondTimestamp);

        //查询区域平均疲劳时长（兵团）
        FatigueWithDeptAndDate fatigue = fatigueService.findFatigueAverageSecond(null , startSecondTimestamp , endSecondTimestamp);
        long totalFatigueDuration = fatigue==null?0:fatigue.getFatigueSecond();
        double areaFatigueAverage = 0;
        try{
            areaFatigueAverage = totalGoOnlineCount==0||daysInMonth==0?0:totalFatigueDuration / totalGoOnlineCount / daysInMonth;
        }catch (Exception e){
            log.info("计算平均疲劳时长失败",e);
        }


        //4.统计入网率
        //获取车辆总数
        VehicleCountWithDept vehicleCountDept = vehicleStatService.findVehicleCountByDeptIdDeptOrArea(deptId, OWNER_ID );


        //6.数据拼装

        //入网率、数据合格率、轨迹完整率、漂移率
        CompanyRateResponse crr = scoreDept;
        //入网数
        int inNetCount = crr.getIntNetCount();

        //上线数
        int goOnlineCount = crr.getGoOnlineCount();

        //车辆总数
        int totalCount = vehicleCountDept.getCount();

        //*入网率
        double inNetRate = MathUtil.divideRoundDouble(inNetCount , totalCount , 4);
        double inNetRateScore = 0;
        //入网率低于 90% 不得分
        if(inNetRate >= 0.9){
            inNetRateScore = inNetRate * 5;
        }
        response.setInNetRateScore(inNetRateScore);

        //*上线率
        double goOnlineRateD = crr.getGoOnlineRateD();
        double goOnlineRateScore = goOnlineRateD * 5;
        response.setGoOnlineRateScore(goOnlineRateScore);

        //*轨迹完整率
        double completeRateD = crr.getCompleteRateD();
        double completeRateScore = 0;
        //轨迹完整率低于70%不得分
        if(completeRateD >= 0.7){
            completeRateScore = completeRateD * 15;
        }
        response.setCompleteRateScore(completeRateScore);

        //*数据合格率
        double qualityRateD = crr.getQualifiedRateD();
        double qualityRateScore = 0;
        if(qualityRateD >= 0.8){
            qualityRateScore = qualityRateD * 15;
        }
        response.setQualityRateScore(qualityRateScore);

        //*漂移率
        double driftRateD = crr.getDriftRateD();
        double driftScore = 0;
        if(driftRateD <= 0.05){
            driftScore = 10 - 10* driftRateD;
        }
        response.setDriftRateScore(driftScore);

        //*平均车辆超速次数(总次数/上线车辆数/天数)
        OverSpeedWithDeptAndDate overSpeed = overSpeedDept;
        int overSpeedCount = overSpeed==null?0:overSpeed.getCount();
        double average = 0;
        try {
            average = goOnlineCount==0||daysInMonth==0?0:overSpeedCount / goOnlineCount / daysInMonth;
        }catch (Exception e){
            log.info("查询平均车辆超速次数失败",e);
        }
        double overSpeedAverageCount = MathUtil.roundDouble(average , 2);
        double overSpeedAverageScore = 0;
        if(overSpeedAverageCount <= areaOverSpeedAverage){
            //平均车辆超速次数 小于等于 区域平均超速次数
            overSpeedAverageScore = 10 + (areaOverSpeedAverage==0?0:((areaOverSpeedAverage - overSpeedAverageCount) / areaOverSpeedAverage)) * 10;
        }else if (overSpeedAverageCount > areaOverSpeedAverage && overSpeedAverageCount < 2 * areaOverSpeedAverage){
            //平均车辆超速次数 大于 区域平均超速次数 ，并且 车辆超速次数小于 区域平均超速次数的2倍
            overSpeedAverageScore = (areaOverSpeedAverage==0?0:((2 * areaOverSpeedAverage - overSpeedAverageCount ) / areaOverSpeedAverage)) * 10;
        }else if(overSpeedAverageCount >= 2 * areaOverSpeedAverage){
            //平均车辆超速次数 大于 区域平均超速次数的2倍，不得分
            overSpeedAverageScore = 0;
        }
        response.setOverSpeedAverageScore(overSpeedAverageScore);

        //*平均疲劳驾驶时长(总时长/上线车辆数/天数)
        FatigueWithDeptAndDate fatigueD = fatigueDept;
        long fatigueSecond = fatigueD==null?0:fatigueD.getFatigueSecond();
        double averageFatigue = 0;
        try {
            averageFatigue = goOnlineCount==0||daysInMonth==0?0:fatigueSecond / goOnlineCount / daysInMonth;
        }catch (Exception e){
            log.info("c查询平均疲劳驾驶时长失败",e);
        }
        double averageFatigueSecond = MathUtil.roundDouble(averageFatigue , 2);
        double averageFatigueScore = 0;
        if(averageFatigueScore <= areaFatigueAverage){
            //平均车辆超速次数 小于等于 区域平均超速次数
            averageFatigueScore = 10 + (areaFatigueAverage==0?0:((areaFatigueAverage - averageFatigueScore) / areaFatigueAverage)) * 10;
        }else if (averageFatigueScore > areaFatigueAverage && averageFatigueScore < 2 * areaFatigueAverage){
            //平均车辆超速次数 大于 区域平均超速次数 ，并且 车辆超速次数小于 区域平均超速次数的2倍
            averageFatigueScore = (areaFatigueAverage==0?0:((2 * areaFatigueAverage - averageFatigueScore ) / areaFatigueAverage)) * 10;
        }else if(averageFatigueScore >= 2 * areaFatigueAverage){
            //平均车辆超速次数 大于 区域平均超速次数的2倍，不得分
            averageFatigueScore = 0;
        }
        response.setFatigueAverageRateScore(averageFatigueScore);

        //平台查岗率，给满分 10
        response.setCheckResponseRateScore(10);
        return response;
    }

    /**
     * @description: 企业运营情况分析--企业基本信息
     * @author: zhouxw
     * @date: 2022/11/21 8:27 AM
     * @param: [deptId]
     * @return: com.xh.vdm.statistic.utils.R<com.xh.vdm.statistic.vo.response.CompanyBaseInfoResponse>
     **/
    @GetMapping("/companyBaseInfo")
    public R<CompanyBaseInfoResponse> companyBaseInfo(Long deptId){
        if(deptId == null){
            return R.fail("企业不能为空");
        }
        try{
            //1。企业基本信息
            BladeDept dept = deptService.getById(deptId);

            //2。车辆总数
            VehicleCountWithDept vcd = vehicleStatService.findVehicleCountByDeptIdDeptOrArea(deptId , OWNER_ID);

            //3。驾驶员总数
            int driverCount = driverService.findDriverCount(deptId);

            //4。入网车辆数
            Date date = new Date();
            long secondTimestamp = date.getTime() / 1000;
            String nextMonthFirstDay = DateUtil.getNextMonthFirstDay(secondTimestamp);
            VehicleCountWithDept inNetCountDept = vehicleStatService.findInNetCountByDeptIdDeptOrArea(nextMonthFirstDay , deptId , OWNER_ID);

            //5。车载终端型号和数量
            List<TerminalModelAndCount> modelList = terminalService.findTerminalModelAndCount(deptId);
            /*List<TerminalModelAndCount> modelFList = new ArrayList<>();
            if(modelList != null && modelList.size() > 0){
                modelList.forEach(item -> {
                    if(item.getTerminalModel().equals("DX5") || item.getTerminalModel().equals("Z/HT-9C") || item.getTerminalModel().equals("Z/HT-7")){
                        modelFList.add(item);
                    }
                });
            }*/

            //6。终端类型（视频终端、定位终端）和数量
            List<TerminalTypeAndCount> typeList = terminalService.findTerminalTypeAndCount(deptId);
            List<TerminalTypeAndCount> typeFList = new ArrayList<>();
            //只取视频终端和定位终端
            if(typeList != null && typeList.size() > 0){
                typeList.forEach(item -> {
                    if(item.getTerminalType().equals("1") || item.getTerminalType().equals("2")){
                        typeFList.add(item);
                    }
                });
            }

            //7。组装数据
            CompanyBaseInfoResponse res = new CompanyBaseInfoResponse();
            res.setDeptName(dept.getDeptName());
            res.setVehicleCount(vcd.getCount());
            res.setDriverCount(driverCount);
            res.setInNetVehicleCount(inNetCountDept.getCount());
            res.setTerminalTypeAndCount(typeFList);
            res.setTerminalModelAndCount(modelList);
            res.setCertificateCode(dept.getBusiCertificate());
            res.setCertExpirationDate(dept.getEndDate()==null?"未知":sdfHolderAll.get().format(dept.getEndDate()));

            return R.data(res);
        }catch (Exception e){
            log.error("查询企业基本信息失败", e);
            return R.fail("查询企业基本信息失败");
        }
    }

    /**
     * @description: 统计平均数据指标
     * 暂时指定企业不能为空，如果企业为空，则默认为企业基本信息接口中传入的企业
     * @author: zhouxw
     * @date: 2022/11/21 9:38 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.xh.vdm.statistic.vo.response.AverageDataResponse>
     **/
    @PostMapping("/averageData")
    public R<AverageDataResponse> averageData(@RequestBody @Validated VehicleTravelDurationRequest request){

        //暂时先添加该验证，后续跟前端说明情况后，去掉该校验
        if(request.getDeptId() == null){
            return R.fail("企业不能为空");
        }

        try{
            //1.根据日期判断是否跨月，如果跨月，需要查询多次
            request = validRequest(request);

            long finalStartTime = request.getStartTime();
            long finalEndTime = request.getEndTime();

            //2.判断开始时间的月份和结束时间的月份是否跨月，并执行数据统计
            //车辆行驶里程
            List<TotalMileageNodeForDept> listAll = new ArrayList<>();
            //驾驶员出勤天数
            List<DeptAndIdCardAndCount> listDriverDaysListAll = new ArrayList<>();
            //驾驶员驾驶里程
            List<DeptAndDriveStatus> listDriveMileageListAll = new ArrayList<>();
            String monthStart = sdfHolder.get().format(request.getStartTime() * 1000).substring(0 , 7);
            String monthEnd = sdfHolder.get().format(request.getEndTime() * 1000).substring(0 , 7);
            if(!monthStart.equals(monthEnd)){
                //如果跨月了，就需要查询多次
                int monthCount = DateUtil.monthCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime());
                for(int i = 0 ; i < monthCount ; i++){
                    List<TotalMileageNodeForDept> list = new ArrayList<>();
                    List<DeptAndIdCardAndCount> listDriverDaysList = new ArrayList<>();
                    List<DeptAndDriveStatus> listDriveMileageList = new ArrayList<>();
                    request = handleRequestForMultiMonth(i , monthCount , monthStart , monthEnd , finalStartTime , finalEndTime , request);
                    //车辆行驶里程
                    list = vehTravelDurationService.findTotalMileageByDayForDept(request);
                    if(list != null && list.size() > 0){
                        listAll.addAll(list);
                    }

                    String startDate = sdfHolderYMD.get().format(request.getStartTime() * 1000);
                    String endDate = sdfHolderYMD.get().format(request.getEndTime() * 1000);

                    //驾驶员驾驶天数
                    listDriverDaysList = driveStatusService.findDriverDriveDaysInMonthDuration(request.getMonth() , request.getDeptId() , startDate , endDate);
                    listDriverDaysListAll.addAll(listDriverDaysList);

                    //驾驶员驾驶里程
                    listDriveMileageList =  driveStatusService.findDriverDriveMileageEveryDay(request.getDeptId(), request.getMonth() ,startDate , endDate);
                    listDriveMileageListAll.addAll(listDriveMileageList);


                }

            }else{
                //如果没有跨月，只需要查询一次
                request.setMonth(monthStart.replace("-" , ""));

                List<TotalMileageNodeForDept> list = new ArrayList<>();
                List<DeptAndIdCardAndCount> listDriverDaysList = new ArrayList<>();
                List<DeptAndDriveStatus> listDriveMileageList = new ArrayList<>();
                //车辆行驶里程
                list = vehTravelDurationService.findTotalMileageByDayForDept(request);
                if(list != null && list.size() > 0) {
                    listAll.addAll(list);
                }

                String startDate = sdfHolderYMD.get().format(request.getStartTime() * 1000);
                String endDate = sdfHolderYMD.get().format(request.getEndTime() * 1000);

                //驾驶员驾驶天数
                listDriverDaysList = driveStatusService.findDriverDriveDaysInMonthDuration(request.getMonth() , request.getDeptId() , startDate , endDate);
                if(listDriverDaysList != null && listDriverDaysList.size() > 0) {
                    listDriverDaysListAll.addAll(listDriverDaysList);
                }

                //驾驶员驾驶里程
                listDriveMileageList =  driveStatusService.findDriverDriveMileageEveryDay(request.getDeptId(), request.getMonth() ,startDate , endDate);
                if(listDriveMileageList != null && listDriveMileageList.size() > 0) {
                    listDriveMileageListAll.addAll(listDriveMileageList);
                }
            }

            //3.统计平均行驶里程
            //统计总里程
            double totalMileage = 0;
            for(TotalMileageNodeForDept t : listAll){
                totalMileage = totalMileage + t.getTotalMileage();
            }
            //车辆上线数
            String month  = request.getMonth().substring(0,4)+"-"+request.getMonth().substring(4,6);
            VehicleCountWithDept goOnlineCountDept = vehicleStatService.findGoOnlineCountByDeptIdDeptOrArea(month , request.getDeptId() , OWNER_ID);
            int goOnlineCount = goOnlineCountDept==null?0:goOnlineCountDept.getCount();
            //平均行驶里程
            double averageMileage = goOnlineCount==0?0:MathUtil.divideRoundDouble(totalMileage / 1000 , goOnlineCount , 0);

            //4.驾驶员平均驾驶天数
            //总驾驶天数
            int totalDays = 0;
            for(DeptAndIdCardAndCount dc: listDriverDaysListAll){
                totalDays = totalDays + dc.getCount();
            }
            //总驾驶员数量
            int driverCount = driverService.findDriverCount(request.getDeptId());
            //平均驾驶天数
            double averageDriveDays = MathUtil.divideRoundDouble(totalDays , driverCount , 0);

            //5.驾驶员平均驾驶里程
            //总驾驶里程
            double totalMileageD = 0;
            for(DeptAndDriveStatus dds : listDriveMileageListAll){
                totalMileageD = totalMileageD + dds.getDriveMileage();
            }
            //平均驾驶里程
            double averageMileageD = MathUtil.divideRoundDouble(totalMileageD , driverCount , 0);

            //6.不规范行为次数
            long totalAlarmCount = securityService.findAlarmCountForDept(request.getDeptId() , request.getStartTime() ,request.getEndTime());
            //平均次数
            double averageAlarmCount = MathUtil.divideRoundDouble(totalAlarmCount , driverCount , 0);


            AverageDataResponse res = new AverageDataResponse();
            res.setAverageMileage(averageMileage);
            res.setAverageAlarmCount(averageAlarmCount);
            res.setAverageDays(averageDriveDays);
            res.setDriveAverageMileage(averageMileageD);

            return R.data(res);
        }catch (Exception e){
            log.error("统计平均数据指标失败",e);
            return R.fail("统计平均数据指标失败");
        }
    }



    /**
     * @description: 企业经营情况统计--分页列表
     * 企业不能为空，如果没有指定企业，就默认使用企业基本信息中传入的企业（需要告知前端）
     * @author: zhouxw
     * @date: 2022/11/25 7:03 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<java.util.List<com.xh.vdm.statistic.vo.response.CompanyAllInfoResponse>>
     **/
    @PostMapping("/allInfo")
    public R<IPage<List<CompanyAllInfoResponse>>> allInfo(@RequestBody @Validated DeptAndDurationRequest request){

        try{
            //企业不能为空，如果用户不选择企业，那么应当从前端传入企业基本信息中的企业
            if(request.getDeptId() == null){
                return R.fail("企业不能为空");
            }

            //如果开始时间为空，则查询当月开始
            if(request.getStartSecondTimestamp() == null || request.getStartSecondTimestamp() <= 0){
                String monthStr = sdfHolderAll.get().format(new Date());
                monthStr = monthStr.substring(0,7)+"-01 00:00:00";
                long secondTimestamp = (sdfHolderAll.get().parse(monthStr).getTime()) / 1000;
                request.setStartSecondTimestamp(secondTimestamp);
            }

            //如果结束时间为空，则查询截止到前一天
            if(request.getEndSecondTimestamp() == null || request.getEndSecondTimestamp() <= 0){
                long timestamp = new Date().getTime() - 24*3600*1000;
                Date date = new Date();
                date.setTime(timestamp);
                String dayStr = sdfHolderAll.get().format(date);
                dayStr = dayStr.substring(0,10)+" 23:59:59";
                long secondTimestamp = sdfHolderAll.get().parse(dayStr).getTime() / 1000;
                request.setEndSecondTimestamp(secondTimestamp);
            }

            request = validRequest(request);

            List<CompanyAllInfoResponse> resList = findCompanyAllInfo(request);

            //根据开始时间和结束时间获取数据总条数
            int totalSize = DateUtil.getDayCountBetweenSecondTimestamp(request.getStartSecondTimestamp() , request.getEndSecondTimestamp());

            Page page = new Page();
            page.setSize(request.getCount());
            page.setCurrent(request.getStart() / request.getCount() + (request.getStart() % request.getCount()>0?1:0));
            page.setTotal(totalSize);
            page.setRecords(resList);
            return R.data(page);

        }catch (Exception e){
            log.error("查询企业经营情况统计列表失败",e);
            return R.fail("查询企业经营情况统计列表失败");
        }

    }

    /**
     * @description: 导出企业经营情况
     * @author: zhouxw
     * @date: 2022/12/1 9:18 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.core.metadata.IPage<java.util.List<com.xh.vdm.statistic.vo.response.CompanyAllInfoResponse>>>
     **/
    @PostMapping("/exportAllInfo")
    public R<String> exportAllInfo(@RequestBody @Validated DeptAndDurationRequest request){

        try{
            //企业不能为空，如果用户不选择企业，那么应当从前端传入企业基本信息中的企业
            if(request.getDeptId() == null){
                return R.fail("企业不能为空");
            }

            request = validRequest(request);

            request.setStart(0);
            request.setCount(DateUtil.getDayCountBetweenSecondTimestamp(request.getStartSecondTimestamp() , request.getEndSecondTimestamp()));
            List<CompanyAllInfoResponse> resList = findCompanyAllInfo(request);

            //导出数据
            String title = "企业经营情况";
            String filePath = EasyExcelUtils.export(staticFilePath ,EasyExcelUtils.DEFAULT_PROXY_PATH , title ,resList , CompanyAllInfoResponse.class, false);

            return R.data(filePath);

        }catch (Exception e){
            log.error("导出企业经营情况失败",e);
            return R.fail("导出企业经营情况失败");
        }

    }


    /**
     * @description: 查询企业运营信息
     * @author: zhouxw
     * @date: 2022/12/1 9:17 AM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.CompanyAllInfoResponse>
     **/
    private List<CompanyAllInfoResponse> findCompanyAllInfo(DeptAndDurationRequest request) throws Exception{
        request.setStart(request.getStart());

        //对于指定的企业，执行统计操作
        //因为日期不定，统计日期不定，这里采用指定日期，针对每个日期分别查询的方式
        //1。查询指定时间段
        //获取要查询的时间段，中间有多少天
        long pageStartSecondTime = 0;
        int dayCount = DateUtil.getDayCountBetweenSecondTimestamp(request.getStartSecondTimestamp() , request.getEndSecondTimestamp());
        if(dayCount < request.getCount()){
            //如果要查询的数据小于1页，那么就全部展示
            pageStartSecondTime = DateUtil.getDayFirstSecondTimestamp(request.getStartSecondTimestamp());
            //request.setCount(dayCount);
        }else{
            //如果要查询的数据多于1页，则分页展示，根据日期分页
            //查询本页的开始时间
            pageStartSecondTime = DateUtil.getDayFirstSecondTimestamp(request.getStartSecondTimestamp() + request.getStart() * 24*3600);
        }

        List<CompanyAllInfoResponse> resList = new ArrayList<>();

        //2。执行数据查询
        for(int i = 0 ; i < dayCount ; i++){
            long startSecondTimestamp = pageStartSecondTime + i * 24 * 3600;

            //3。行驶车辆数
            String date = DateUtil.getDateString(startSecondTimestamp);
            String month = date.substring(0,4)+date.substring(5,7);
            int vehicleCount = 0;
            try{
                vehicleCount = vehTravelDurationService.findTravelVehicleCountByDate(month , date , request.getDeptId(), OWNER_ID);
            }catch (Exception e){
                log.info("查询行驶车辆数报错",e);
            }

            //4。出勤驾驶员数
            int driverCount = 0;
            try{
                driverCount = driveStatusService.findDriverCountByDate(month , date , request.getDeptId());
            }catch (Exception e){
                log.info("查询出勤驾驶员数失败",e);
            }

            //5。车辆日平均行驶里程
            //总行驶里程
            double totalMileage = 0;
            try{
                totalMileage = vehTravelDurationService.findCompanyTotalMileageByDate(month ,date.substring(8,10), request.getDeptId());
            }catch (Exception e){
                log.info("查询车辆总行驶里程失败",e);
            }
            //企业总上线车辆数
            VehicleCountWithDept goOnlineCountWithDept = vehicleStatService.findGoOnlineCountByDeptIdDeptOrArea(month , request.getDeptId() , OWNER_ID);
            int goOnlineCount = goOnlineCountWithDept==null?0:goOnlineCountWithDept.getCount();
            //平均行驶里程
            double averageTravelMileage = goOnlineCount==0?0:MathUtil.divideRoundDouble(totalMileage , goOnlineCount , 2);

            //6。驾驶员日均驾驶时长
            //总驾驶时长
            double totalDriveDuraion = 0;
            try{
                totalDriveDuraion = driveStatusService.findTotalDriveDurationByDate(month , date , request.getDeptId());
            }catch (Exception e){
                log.info("查询总驾驶时长失败",e);
            }
            //驾驶员总数
            int totalDriverCount = 0;
            try{
                totalDriverCount = driverService.findDriverCount(request.getDeptId());
            }catch (Exception e){
                log.info("查询驾驶员总是失败",e);
            }
            //日均驾驶时长
            double averageDriveDuration = totalDriverCount==0?0:MathUtil.divideRoundDouble(totalDriveDuraion , totalDriverCount , 2);

            //7。驾驶员日均驾驶里程
            //总驾驶里程
            double totalDriveMileage = 0;
            try{
                totalDriveMileage = driveStatusService.findTotalDriveMileageByDate(month , date ,request.getDeptId());
            }catch (Exception e){
                log.info("查询总驾驶里程失败",e);
            }
            //平均驾驶里程
            double averageDriveMileage = totalDriverCount==0?0:MathUtil.divideRoundDouble(totalDriveMileage , totalDriverCount , 2);

            //8。驾驶员日均不规范行为次数
            //总预警次数
            long totalAlarmount = 0;
            try{
                totalAlarmount = securityService.findAlarmCountByDay(request.getDeptId() , startSecondTimestamp);
            }catch (Exception e){
                log.info("查询总预警次数失败",e);
            }
            //日平均驾驶员预警次数
            double averageAlarmCount = totalDriverCount==0?0:MathUtil.divideRoundDouble(totalAlarmount , totalDriverCount , 2);

            CompanyAllInfoResponse res = new CompanyAllInfoResponse();
            res.setStatDate(date);
            res.setDeptId(request.getDeptId());
            res.setVehicleCount(vehicleCount);
            //查询deptName
            BladeDept dept = deptService.getById(request.getDeptId());
            if(dept == null){
                res.setDeptName("未知");
            }else{
                res.setDeptName(dept.getDeptName());
            }
            res.setDriverCount(driverCount);
            res.setAverageAlarmCount(averageAlarmCount);
            res.setAverageDriveDuration(averageDriveDuration);
            res.setAverageDriveMileage(averageDriveMileage);
            res.setAverageTravelMileage(averageTravelMileage);
            resList.add(res);
        }
        return resList;
    }

    /**
     * @description: 校验请求参数
     * @author: zhouxw
     * @date: 2022/12/1 10:39 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.vo.request.DeptAndDurationRequest
     **/
    private DeptAndDurationRequest validRequest(DeptAndDurationRequest request) throws Exception{
        //如果开始时间为空，则查询当月开始
        if(request.getStartSecondTimestamp() == null || request.getStartSecondTimestamp() <= 0){
            String monthStr = sdfHolder.get().format(new Date());
            monthStr = monthStr.substring(0,7)+"-01 00:00:00";
            long secondTimestamp = (sdfHolder.get().parse(monthStr).getTime()) / 1000;
            request.setStartSecondTimestamp(secondTimestamp);
        }

        //如果结束时间为空，则查询截止到前一天
        if(request.getEndSecondTimestamp() == null || request.getEndSecondTimestamp() <= 0){
            long timestamp = new Date().getTime() - 24*3600*1000;
            Date date = new Date();
            date.setTime(timestamp);
            String dayStr = sdfHolderYMD.get().format(date);
            dayStr = dayStr.substring(0,10)+" 23:59:59";
            long secondTimestamp = sdfHolder.get().parse(dayStr).getTime() / 1000;
            request.setEndSecondTimestamp(secondTimestamp);
        }
        return request;
    }

    /**
     * @description: 企业经营情况统计--车辆在线分析
     * @author: zhouxw
     * @date: 2022/11/27 11:27 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<java.util.List<com.xh.vdm.statistic.entity.DateAndCount>>
     **/
    @PostMapping("/statVehicleOnlineCount")
    public R<List<DateAndCount>> statVehicleOnlineCount(@RequestBody @Validated DeptAndDurationNoPageRequest request){
        try{
            //企业不能为空，如果用户不选择企业，那么应当从前端传入企业基本信息中的企业
            if(request.getDeptId() == null){
                return R.fail("企业不能为空");
            }


            //如果开始时间为空，则查询当月开始
            if(request.getStartSecondTimestamp() == null || request.getStartSecondTimestamp() <= 0){
                String monthStr = sdfHolder.get().format(new Date());
                monthStr = monthStr.substring(0,7)+"-01 00:00:00";
                long secondTimestamp = (sdfHolder.get().parse(monthStr).getTime()) / 1000;
                request.setStartSecondTimestamp(secondTimestamp);
            }

            //如果结束时间为空，则查询截止到前一天
            if(request.getEndSecondTimestamp() == null || request.getEndSecondTimestamp() <= 0){
                long timestamp = new Date().getTime() - 24*3600*1000;
                Date date = new Date();
                date.setTime(timestamp);
                String dayStr = sdfHolderYMD.get().format(date);
                dayStr = dayStr.substring(0,10)+" 23:59:59";
                long secondTimestamp = sdfHolderYMD.get().parse(dayStr).getTime() / 1000;
                request.setEndSecondTimestamp(secondTimestamp);
            }

            //查询车辆在线情况
            List<DateAndCount> list = vehTravelDurationService.findVehicleOnlineCount(request);
            return R.data(list);

        }catch (Exception e){
            log.error("车辆在线情况分析失败",e);
            return R.fail("车辆在线情况分析失败");
        }
    }






    /**
     * @description: 校验请求参数
     * @author: zhouxw
     * @date: 2022/11/10 1:55 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest
     **/
    private VehicleTravelDurationRequest validRequest(VehicleTravelDurationRequest request) throws Exception{
        //如果开始时间为空，则查询当月开始
        if(request.getStartTime() == null || request.getStartTime() <= 0){
            String monthStr = sdfHolder.get().format(new Date());
            monthStr = monthStr.substring(0,7)+"-01 00:00:00";
            long secondTimestamp = (sdfHolder.get().parse(monthStr).getTime()) / 1000;
            request.setStartTime(secondTimestamp);
        }

        //如果结束时间为空，则查询截止到前一天
        if(request.getEndTime() == null || request.getEndTime() <= 0){
            long timestamp = new Date().getTime() - 24*3600*1000;
            Date date = new Date();
            date.setTime(timestamp);
            String dayStr = sdfHolderYMD.get().format(date);
            dayStr = dayStr.substring(0,10)+" 23:59:59";
            long secondTimestamp = sdfHolderAll.get().parse(dayStr).getTime() / 1000;
            request.setEndTime(secondTimestamp);
        }
        return request;
    }


    private VehicleTravelDurationRequest handleRequestForMultiMonth(int monthOffsetIndex , int monthCount , String monthStart , String monthEnd , long finalStartTime , long finalEndTime ,VehicleTravelDurationRequest request){
        int i = monthOffsetIndex;
        if(i == 0){
            //如果是首月
            //查询从开始时间到当月月底
            long endSecondTimestamp = DateUtil.getMonthLastSecondTimestamp(finalStartTime);
            request.setMonth(monthStart.replace("-",""));
            request.setStartTime(finalStartTime);
            request.setEndTime(endSecondTimestamp);
        }else if(i == monthCount - 1){
            //如果是尾月
            //查询从当月月初到结束时间
            request.setMonth(monthEnd.replace("-",""));
            request.setStartTime(DateUtil.getMonthFirstSecondTimestamp(finalEndTime));
            request.setEndTime(finalEndTime);
        }else{
            //如果是其他月份
            //查询从当月月初到月尾
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.YEAR , Integer.parseInt(monthStart.split("-")[0]));
            cal.set(Calendar.MONTH, Integer.parseInt(monthStart.split("-")[1])-1);
            cal.add(Calendar.MONTH , i);
            int month = cal.get(Calendar.MONTH) + 1;
            String monthStr = month + "";
            if(month < 10){
                monthStr = "0" + month;
            }
            request.setMonth(cal.get(Calendar.YEAR) + "" + monthStr);
            request.setStartTime(DateUtil.getMonthFirstSecondTimestamp(cal.getTimeInMillis() / 1000));
            request.setEndTime(DateUtil.getMonthLastSecondTimestamp(cal.getTimeInMillis() / 1000));
        }
        return request;
    }

}
