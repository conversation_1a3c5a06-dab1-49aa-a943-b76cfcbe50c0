<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.terminal.RnssDeviceMapper">

    <select id="getRnssCountByGnssMode"
            resultType="com.xh.vdm.statistic.vo.response.terminal.RnssGnssModeCountResponse">
        SELECT gnss_mode, COUNT(*) AS count
        FROM bdm_rnss_device rd
        WHERE deleted = 0
        <if test="userId != null">
            AND EXISTS (
            SELECT 1
            FROM bdm_user_dept_regulates usr
            WHERE usr.dept_id =rd.dept_id
            AND usr.user_id = #{userId}
            )
        </if>
        GROUP BY gnss_mode
        ORDER BY gnss_mode;
    </select>

</mapper>
