package com.xh.vdm.statistic.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.config.ExcelExportFontAndStyleConfig;
import com.xh.vdm.statistic.config.ExcelExportRowHeightConfig;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.service.IBladeDeptService;
import com.xh.vdm.statistic.service.IStatVehTravelDurationService;
import com.xh.vdm.statistic.service.IVehicleStatService;
import com.xh.vdm.statistic.utils.BeanUtil;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.vo.request.VehicleTravelAverageDurationResponse;
import com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest;
import com.xh.vdm.statistic.vo.response.PassengerVehicleInNightCountResponse;
import com.xh.vdm.statistic.vo.response.VehicleTravelDurationCountResponse;
import com.xh.vdm.statistic.vo.response.VehicleTravelMileageResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.beans.IntrospectionException;
import java.io.FileOutputStream;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: 车辆行驶相关接口
 * @Author: zhouxw
 * @Date: 2022/11/9 2:55 PM
 */
@RestController
@Slf4j
@RequestMapping("/bt/statistics/vehicleTravel")
public class VehicleTravelDurationController {

    @Resource
    private IStatVehTravelDurationService service;

    @Resource
    private IVehicleStatService vehicleStatService;

    @Resource
    private IBladeDeptService deptService;


    //企业名称
    private static final String DEPT_NAME = "deptName";
    //统计时间
    private static final String STAT_DATE = "statDate";

    @Value("${static.file.path:/statistic/files/}")
    private String staticFilePath;


    private static ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    private static ThreadLocal<SimpleDateFormat> sdfHolderShort = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));


    /**
     * @description: 查询企业各个小时行驶车辆数
     * 综合企业和日期分页
     * @author: zhouxw
     * @date: 2022/11/10 9:01 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @PostMapping("/findVehicleCountPerHour")
    public R<IPage> findVehicleCountByTravelDuration(@Validated @RequestBody VehicleTravelDurationRequest request, BladeUser bladeUser){

        try{

            if(request.getStart() == null || request.getStart() < 1){
                request.setStart(1);
            }
            if(request.getCount() == null){
                request.setCount(10);
            }

            //1.根据日期判断是否跨月，如果跨月，需要查询多次
            //如果开始时间为空，则查询当月开始
            if(request.getStartTime() == null || request.getStartTime() <= 0){
                String monthStr = sdfHolder.get().format(new Date());
                monthStr = monthStr.substring(0,7)+"-01 00:00:00";
                long secondTimestamp = (sdfHolder.get().parse(monthStr).getTime()) / 1000;
                request.setStartTime(secondTimestamp);
            }

            //如果结束时间为空，则查询截止到前一天
            if(request.getEndTime() == null || request.getEndTime() <= 0){
                long timestamp = new Date().getTime() - 24*3600*1000;
                Date date = new Date();
                date.setTime(timestamp);
                String dayStr = sdfHolder.get().format(date);
                dayStr = dayStr.substring(0,10)+" 23:59:59";
                long secondTimestamp = sdfHolder.get().parse(dayStr).getTime() / 1000;
                request.setEndTime(secondTimestamp);
            }

			//租户id
			if(bladeUser == null){
				log.info("用户未登录或者鉴权失败");
				return R.fail("操作失败：用户未登录或者鉴权失败");
			}
			String tenantId = bladeUser.getTenantId();

            IPage pageF = new Page();
            //查询要查询的时间段天数
            int days = DateUtil.getDayCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime()) + 1;
            //查询企业总数
            LambdaQueryWrapper<BladeDept> wrapper = Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted ,0).eq(BladeDept::getTenantId,tenantId);
            if(request.getDeptId() != null){
                wrapper.eq(BladeDept::getId , request.getDeptId());
            }
            wrapper.orderByAsc(BladeDept::getId);
            long deptCount = deptService.count(wrapper);
            List<BladeDept> deptList = deptService.list(wrapper);
            long totalCount = days * deptCount;
            pageF.setTotal(request.getDeptId()==null?totalCount:days);

            List<VehicleTravelDurationCountResponse> listAll = new ArrayList<>();
            //说明：分页以企业为第一维度，日期为第二维度。每个企业对应的日期时间段相同，也就是说每个企业对应的天数相同；以企业分组，每组里边有多个天。

            //根据页码获取要查询的范围
            int start = request.getStart();
            int end = request.getStart() + request.getCount();
            if(end > pageF.getTotal()){
                end = (int)pageF.getTotal();
            }

            int deptListStartIndex = (start-1) / days;
            int daysStartOffset = start % days;
            int deptListEndIndex = (end-1) / days;
            int daysEndOffset = (end-1) % days;

            if(deptListStartIndex == deptListEndIndex){
                //如果开始点和结束点是相同的企业
                long startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartTime()) + daysStartOffset * 24 * 3600;
                long endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getStartTime()) + daysEndOffset * 24 * 3600;
                request.setStartTime(startTimestamp);
                request.setEndTime(endTimestamp);
                request.setDeptId(deptList.get(deptListStartIndex).getId());
                List<VehicleTravelDurationCountResponse> onceList = getVehicleCountPerHourForOnce(request , DateUtil.getDayFirstSecondTimestamp(request.getStartTime()) ,DateUtil.getDayLastSecondTimestamp(request.getEndTime()) );
                listAll.addAll(onceList);
            }else{
                //如果开始点和结束点是不同的企业
                int diff = deptListEndIndex - deptListStartIndex;
                long startTimestamp = 0;
                long endTimestamp = 0;
                long deptId = 0;
                for(int i = 0 ; i <= diff ; i++){
                    if(i == 0){
                        //如果是第一个企业
                        startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartTime()) + daysStartOffset * 24 * 3600;
                        endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getEndTime());
                        deptId = deptList.get(deptListStartIndex).getId();
                    }else if (i == diff){
                        //如果是最后一个企业
                        startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartTime());
                        endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getStartTime()) + daysEndOffset * 24 * 3600;
                        deptId = deptList.get(deptListEndIndex).getId();
                    }else{
                        //如果是中间企业
                        startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartTime());
                        endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getEndTime());
                        deptId = deptList.get(deptListStartIndex + i).getId();
                    }
                    VehicleTravelDurationRequest req = new VehicleTravelDurationRequest();
                    req.setStart(request.getStart());
                    req.setCount(request.getCount());
                    req.setDeptId(deptId);
                    req.setStartTime(startTimestamp);
                    req.setEndTime(endTimestamp);
                    List<VehicleTravelDurationCountResponse> onceList = getVehicleCountPerHourForOnce(req , startTimestamp ,endTimestamp );
                    listAll.addAll(onceList);
                }
            }

            //对结果进行排序
            Collections.sort(listAll,(o1 , o2) -> {
                if(o1.getDeptId().longValue() != o2.getDeptId().longValue()){
                    return Math.toIntExact(o1.getDeptId() - o2.getDeptId());
                }else{
                    int diff = 0;
                    try {
                        diff = (int) (sdfHolderShort.get().parse(o1.getStatDate()).getTime() - sdfHolderShort.get().parse(o2.getStatDate()).getTime());
                    }catch (Exception e){
                        log.info("类型转换报错",e);
                    }
                    return diff;
                }
            });


            pageF.setRecords(listAll);
            return R.data(pageF);
        }catch (Exception e){
            log.error("统计企业各小时行驶车辆数失败",e);
            return R.fail("统计企业各小时行驶车辆数失败");
        }

    }

    /**
     * @description: 执行一次查询
     * 企业已经给定
     * 判断是否跨月
     * @author: zhouxw
     * @date: 2022/11/20 11:41 PM
     * @param:
     * @return:
     **/
    private List<VehicleTravelDurationCountResponse> getVehicleCountPerHourForOnce(VehicleTravelDurationRequest request,long finalStartTime ,long finalEndTime) throws Exception{
        List<VehicleTravelDurationCountResponse> listF = new ArrayList<>();
        //判断开始时间的月份和结束时间的月份是否跨月，并执行数据统计
        String monthStart = sdfHolder.get().format(request.getStartTime() * 1000).substring(0 , 7);
        String monthEnd = sdfHolder.get().format(request.getStartTime() * 1000).substring(0 , 7);
        if(!monthStart.equals(monthEnd)){
            //如果跨月了，就需要查询多次
            int monthCount = DateUtil.monthCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime());
            for(int i = 0 ; i < monthCount ; i++){
                List<VehicleTravelDurationNode> list = new ArrayList<>();
                String month = "";
                long startTime = 0;
                long endTime = 0;
                if(i == 0){
                    //如果是首月
                    //查询从开始时间到当月月底
                    long startSecondTimestamp = finalStartTime;
                    long endSecondTimestamp = DateUtil.getMonthLastSecondTimestamp(startSecondTimestamp);
                    month = monthStart.replace("-" , "");
                    startTime = startSecondTimestamp;
                    endTime = endSecondTimestamp;
                }else if(i == monthCount - 1){
                    //如果是尾月
                    //查询从当月月初到结束时间
                    month = monthEnd.replace("-" , "");
                    startTime = DateUtil.getMonthFirstSecondTimestamp(finalEndTime);
                    endTime = finalEndTime;
                }else{
                    //如果是其他月份
                    //查询从当月月初到月尾
                    Calendar cal = Calendar.getInstance();
                    cal.set(Calendar.YEAR , Integer.parseInt(monthStart.split("-")[0]));
                    cal.set(Calendar.MONTH, Integer.parseInt(monthStart.split("-")[1])-1);
                    cal.add(Calendar.MONTH , i);
                    int monthIndex = cal.get(Calendar.MONTH)+1 ;
                    month = cal.get(Calendar.YEAR) + "" + (monthIndex < 10 ? "0"+month : month);
                    startTime = DateUtil.getMonthFirstSecondTimestamp(cal.getTimeInMillis() / 1000);
                    endTime = DateUtil.getMonthLastSecondTimestamp(cal.getTimeInMillis() / 1000);
                }
                VehicleTravelDurationRequest req = new VehicleTravelDurationRequest();
                req.setDeptId(request.getDeptId());
                req.setMonth(month);
                req.setStartTime(startTime);
                req.setEndTime(endTime);
                List<VehicleTravelDurationCountResponse> listO = findVehicleCountForDept(req);
                listF.addAll(listO);
            }

        }else{
            //如果没有跨月，只需要查询一次
            String month = monthStart.replace("-" , "");
            VehicleTravelDurationRequest req = new VehicleTravelDurationRequest();
            req.setDeptId(request.getDeptId());
            req.setMonth(month);
            req.setStartTime(finalStartTime);
            req.setEndTime(finalEndTime);
            List<VehicleTravelDurationCountResponse> listO = findVehicleCountForDept(req);
            listF.addAll(listO);
        }

        return listF;
    }


    /**
     * @description: 指定企业和日期，分页查询每小时车辆数据
     * 给定企业
     * 给定一个月内的某个时间段
     * @author: zhouxw
     * @date: 2022/11/29 10:25 AM
     * @param: [request, isExport]
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     **/
    private List<VehicleTravelDurationCountResponse> findVehicleCountForDept(VehicleTravelDurationRequest request) throws Exception{

        List<VehicleTravelDurationNode> listAll = new ArrayList<>();
        //1.根据日期判断是否跨月，如果跨月，需要查询多次
        //如果开始时间为空，则查询当月开始
        if(request.getStartTime() == null || request.getStartTime() <= 0){
            String monthStr = sdfHolder.get().format(new Date());
            monthStr = monthStr.substring(0,7)+"-01 00:00:00";
            long secondTimestamp = (sdfHolder.get().parse(monthStr).getTime()) / 1000;
            request.setStartTime(secondTimestamp);
        }

        //如果结束时间为空，则查询截止到前一天
        if(request.getEndTime() == null || request.getEndTime() <= 0){
            long timestamp = new Date().getTime() - 24*3600*1000;
            Date date = new Date();
            date.setTime(timestamp);
            String dayStr = sdfHolder.get().format(date);
            dayStr = dayStr.substring(0,10)+" 23:59:59";
            long secondTimestamp = sdfHolder.get().parse(dayStr).getTime() / 1000;
            request.setEndTime(secondTimestamp);
        }

        //2.判断开始时间的月份和结束时间的月份是否跨月，并执行数据统计

            DeptVehicleTravelCount countBean = getDaysCount(request);
            List<DeptAndDateAndCount> countList = countBean.getCountList();

            //执行日期的合并（因为筛选时，时间段是连续的，虽然结果不连续）
            //对月份进行分组，并记录日期的开始时间和结束时间
            Map<String,List<String>> map = new HashMap<>();
            for(int i = 0 ; i < countList.size() ; i++){
                String statDate = countList.get(i).getStatDate();
                String month = statDate.substring(0,7);
                if(map.get(month) == null){
                    List<String> list = new ArrayList<>();
                    list.add(statDate);
                    map.put(month , list);
                }else{
                    map.get(month).add(statDate);
                }
            }
            //对 map 中的list进行排序
            for(String key : map.keySet()){
                List<String> list = map.get(key);
                Collections.sort(list , (o1 , o2) -> {
                    try {
                        return (int) (sdfHolderShort.get().parse(o1).getTime() - sdfHolderShort.get().parse(o2).getTime());
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            //对每个月份分组进行查询
            VehicleTravelDurationRequest req = new VehicleTravelDurationRequest();
            BeanUtils.copyProperties(request , req);
            for(String month : map.keySet()){
                List<String> list = map.get(month);
                if(list == null || list.size() < 1){
                    continue;
                }else if(list.size() == 1 ){
                    //如果只有一天
                    Date date = sdfHolderShort.get().parse(list.get(0));
                    req.setStartTime(DateUtil.getDayFirstSecondTimestamp(date.getTime() / 1000));
                    req.setEndTime(DateUtil.getDayLastSecondTimestamp(date.getTime() / 1000));
                    req.setMonth(list.get(0).substring(0 ,7).replace("-",""));
                }else{
                    //如果有多天
                    Date startDate = sdfHolderShort.get().parse(list.get(0));
                    Date endDate = sdfHolderShort.get().parse(list.get(list.size()-1));
                    req.setMonth(list.get(0).substring(0 ,7).replace("-",""));
                    req.setStartTime(DateUtil.getDayFirstSecondTimestamp(startDate.getTime() / 1000));
                    req.setEndTime(DateUtil.getDayLastSecondTimestamp(endDate.getTime() / 1000));
                }
                req.setVehicleUseType(request.getVehicleUseType());
                List<VehicleTravelDurationNode> nodeList = service.findVehicleTravelDurationCountByCondition(req);
                listAll.addAll(nodeList);
            }



        //3.拼装对象
        Map<String, Map<String,Object>> durationMap = new HashMap<>();
        listAll.stream().forEach(item -> {
            String deptName = item.getDeptName();
            String statDate = item.getStatDate();
            Map<String,Object> duration = durationMap.get(deptName+":"+statDate);
            if(duration == null){
                durationMap.put(deptName+":"+statDate , duration = initDuration(deptName , statDate));
            }
            duration.put(item.getHour(),item.getCount());
        });
        List<Map<String , Object>> resList = new ArrayList<>();
        durationMap.forEach((k ,v) -> {
            resList.add(v);
        });
        //对结果进行正序排序
        Collections.sort(resList , Comparator.comparingInt(o -> Integer.parseInt(o.get(STAT_DATE).toString().replace("-",""))));
        //返回类型转换
        List<VehicleTravelDurationCountResponse> finalList = new ArrayList<>();
        resList.forEach(item -> {
            try {
                VehicleTravelDurationCountResponse vc = (VehicleTravelDurationCountResponse) BeanUtil.mapToBean(item, VehicleTravelDurationCountResponse.class);
                vc.setDeptId(request.getDeptId());
                finalList.add(vc);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        //补充数据：对于没有驾驶员出勤的天，初始化处理
        Set<String> daySet = new HashSet<>();
        finalList.forEach(item -> {
            daySet.add(item.getStatDate());
        });

        int days = DateUtil.getDayCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime());
        long timestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartTime());
        String deptName = "";
        if((finalList == null || finalList.size() < 1) && StringUtils.isBlank(deptName)){
            BladeDept dept = deptService.getById(request.getDeptId());
            if(dept != null){
                deptName = dept.getDeptName();
            }
        }else{
            deptName = finalList.get(0).getDeptName();
        }
        for(int i = 0 ; i <= days; i++){
            String date = DateUtil.getDateString(timestamp);
            if(!daySet.contains(date)){
                VehicleTravelDurationCountResponse dsr = new VehicleTravelDurationCountResponse();
                dsr.setDeptId(request.getDeptId());
                dsr.setDeptName(deptName);
                dsr.setStatDate(date);
                dsr.setH01(0);
                dsr.setH02(0);
                dsr.setH03(0);
                dsr.setH04(0);
                dsr.setH05(0);
                dsr.setH06(0);
                dsr.setH07(0);
                dsr.setH08(0);
                dsr.setH09(0);
                dsr.setH10(0);
                dsr.setH11(0);
                dsr.setH12(0);
                dsr.setH13(0);
                dsr.setH14(0);
                dsr.setH15(0);
                dsr.setH16(0);
                dsr.setH17(0);
                dsr.setH18(0);
                dsr.setH19(0);
                dsr.setH20(0);
                dsr.setH21(0);
                dsr.setH22(0);
                dsr.setH23(0);
                dsr.setH24(0);
                finalList.add(dsr);
                daySet.add(date);
            }
            timestamp += 24*3600;
        }
        return finalList;
    }






    /**
     * @description: 导出企业各个小时行驶车辆数
     * @author: zhouxw
     * @date: 2022/11/30 8:38 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<java.lang.String>
     **/
    @PostMapping("/exportVehicleCountPerHour")
    public R<String> exportVehicleCountByTravelDuration(@Validated @RequestBody VehicleTravelDurationRequest request){
        try{
            List<VehicleTravelDurationCountResponse> list = findVehicleCountForExport(request, true);
            //导出数据
            String title = "企业车辆分时数量分析";
            Date date = new Date();
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
            String dateStr = format.format(date);
            String fileName = title + "_" + dateStr + ".xlsx";
            FileOutputStream file = new FileOutputStream(staticFilePath + fileName);
            fileName =  "/bt/statistics/files/"+fileName;

            //主标题和副标题在excel中分别是是第0和第1行
            List<Integer> columnIndexes = Arrays.asList(0,1);
            //自定义标题和内容策略(具体定义在下文)
            ExcelExportFontAndStyleConfig cellStyleStrategy =
                    new ExcelExportFontAndStyleConfig(columnIndexes,new WriteCellStyle(), new WriteCellStyle());

            EasyExcel.write(file, VehicleTravelDurationCountResponse.class).registerWriteHandler(cellStyleStrategy).registerWriteHandler(new ExcelExportRowHeightConfig()).sheet(title).doWrite(list);
            return R.data(fileName);

        }catch (Exception e){
            log.error("查询各小时行驶车辆数据失败",e);
            return R.fail("数据查询失败");
        }
    }

    /**
     * @description: 分页查询或者导出每小时车辆数据
     * @author: zhouxw
     * @date: 2022/11/29 10:25 AM
     * @param: [request, isExport]
     * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
     **/
    private List<VehicleTravelDurationCountResponse> findVehicleCountForExport(VehicleTravelDurationRequest request, boolean isExport) throws Exception{
        List<VehicleTravelDurationNode> listAll = new ArrayList<>();
        //1.根据日期判断是否跨月，如果跨月，需要查询多次
        //如果开始时间为空，则查询当月开始
        if(request.getStartTime() == null || request.getStartTime() <= 0){
            String monthStr = sdfHolder.get().format(new Date());
            monthStr = monthStr.substring(0,7)+"-01 00:00:00";
            long secondTimestamp = (sdfHolder.get().parse(monthStr).getTime()) / 1000;
            request.setStartTime(secondTimestamp);
        }

        //如果结束时间为空，则查询截止到前一天
        if(request.getEndTime() == null || request.getEndTime() <= 0){
            long timestamp = new Date().getTime() - 24*3600*1000;
            Date date = new Date();
            date.setTime(timestamp);
            String dayStr = sdfHolder.get().format(date);
            dayStr = dayStr.substring(0,10)+" 23:59:59";
            long secondTimestamp = sdfHolder.get().parse(dayStr).getTime() / 1000;
            request.setEndTime(secondTimestamp);
        }

        long finalStartTime = request.getStartTime();
        long finalEndTime = request.getEndTime();

        //2.判断开始时间的月份和结束时间的月份是否跨月，并执行数据统计
        //记录累计数据条数
        String monthStart = sdfHolder.get().format(request.getStartTime() * 1000).substring(0 , 7);
        String monthEnd = sdfHolder.get().format(request.getEndTime() * 1000).substring(0 , 7);

        if(!isExport){
            //如果是分页查询
            //前台传过来的 start 从0开始
            //本页的开始下标（在全部记录中的下标）
            int startInThisPage = request.getStart() ;
            //本页结束的下标（在全部记录中的下标）
            int endInThisPage = request.getStart() + request.getCount() ;
            //查询数据总数（总天数）
            DeptVehicleTravelCount countBean = getDaysCount(request);
            int totalCount = countBean.getTotalCount();
            List<DeptAndDateAndCount> countList = countBean.getCountList();

            //根据条件查询当前页应该限定的时间范围
            //查询指定时间段内，企业日期列表
            List<DeptAndDateAndCount> durationCountList = null;
            if(countList.size() < request.getCount()){
                durationCountList = countList;
            }else {
                durationCountList = countList.subList(startInThisPage, endInThisPage);
            }
            //执行日期的合并（因为筛选时，时间段是连续的，虽然结果不连续）
            //对月份进行分组，并记录日期的开始时间和结束时间
            Map<String,List<String>> map = new HashMap<>();
            for(int i = 0 ; i < durationCountList.size() ; i++){
                String statDate = durationCountList.get(i).getStatDate();
                String month = statDate.substring(0,4);
                if(map.get(month) == null){
                    List<String> list = new ArrayList<>();
                    list.add(statDate);
                    map.put(month , list);
                }else{
                    map.get(month).add(statDate);
                }
            }
            //对 map 中的list进行排序
            for(String key : map.keySet()){
                List<String> list = map.get(key);
                Collections.sort(list , (o1 , o2) -> {
                    try {
                        return (int) (sdfHolderShort.get().parse(o1).getTime() - sdfHolderShort.get().parse(o2).getTime());
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            //对每个月份分组进行查询
            VehicleTravelDurationRequest req = new VehicleTravelDurationRequest();
            BeanUtils.copyProperties(request , req);
            for(String month : map.keySet()){
                List<String> list = map.get(month);
                if(list == null || list.size() < 1){
                    continue;
                }else if(list.size() == 1 ){
                    //如果只有一天
                    Date date = sdfHolderShort.get().parse(list.get(0));
                    req.setStartTime(DateUtil.getDayFirstSecondTimestamp(date.getTime() / 1000));
                    req.setEndTime(DateUtil.getDayLastSecondTimestamp(date.getTime() / 1000));
                    req.setMonth(list.get(0).substring(0 ,7).replace("-",""));
                }else{
                    //如果有多天
                    Date startDate = sdfHolderShort.get().parse(list.get(0));
                    Date endDate = sdfHolderShort.get().parse(list.get(list.size()-1));
                    req.setMonth(list.get(0).substring(0 ,7).replace("-",""));
                    req.setStartTime(DateUtil.getDayFirstSecondTimestamp(startDate.getTime() / 1000));
                    req.setEndTime(DateUtil.getDayLastSecondTimestamp(endDate.getTime() / 1000));
                }
                List<VehicleTravelDurationNode> nodeList = service.findVehicleTravelDurationCountByCondition(request);
                listAll.addAll(nodeList);
            }

        }else{
            //如果是导出
            if(!monthStart.equals(monthEnd)){
                //如果跨月了，就需要查询多次
                int monthCount = DateUtil.monthCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime());
                for(int i = 0 ; i < monthCount ; i++){
                    List<VehicleTravelDurationNode> list = new ArrayList<>();
                    if(i == 0){
                        //如果是首月
                        //查询从开始时间到当月月底
                        long startSecondTimestamp = finalStartTime;
                        long endSecondTimestamp = DateUtil.getMonthLastSecondTimestamp(startSecondTimestamp);
                        request.setMonth(monthStart.replace("-" , ""));
                        request.setStartTime(startSecondTimestamp);
                        request.setEndTime(endSecondTimestamp);
                    }else if(i == monthCount - 1){
                        //如果是尾月
                        //查询从当月月初到结束时间
                        request.setMonth(monthEnd.replace("-" , ""));
                        request.setStartTime(DateUtil.getMonthFirstSecondTimestamp(finalEndTime));
                        request.setEndTime(finalEndTime);
                    }else{
                        //如果是其他月份
                        //查询从当月月初到月尾
                        Calendar cal = Calendar.getInstance();
                        cal.set(Calendar.YEAR , Integer.parseInt(monthStart.split("-")[0]));
                        cal.set(Calendar.MONTH, Integer.parseInt(monthStart.split("-")[1])-1);
                        cal.add(Calendar.MONTH , i);
                        int month = cal.get(Calendar.MONTH)+1 ;
                        request.setMonth(cal.get(Calendar.YEAR) + "" + (month < 10 ? "0"+month : month));
                        request.setStartTime(DateUtil.getMonthFirstSecondTimestamp(cal.getTimeInMillis() / 1000));
                        request.setEndTime(DateUtil.getMonthLastSecondTimestamp(cal.getTimeInMillis() / 1000));
                    }
                    try {
                        list = service.findVehicleTravelDurationCountByCondition(request);
                    }catch (Exception e){
                        //本次查询失败，不能影响后续的查询
                        log.error("本次数据查询失败[{}]，继续执行后面的查询" , request.getMonth() , e);
                    }

                    listAll.addAll(list);
                }

            }else{
                //如果没有跨月，只需要查询一次
                request.setMonth(monthStart.replace("-" , ""));
                List<VehicleTravelDurationNode> list = service.findVehicleTravelDurationCountByCondition(request);
                listAll.addAll(list);
            }
        }

        //3.拼装对象
        Map<String, Map<String,Object>> durationMap = new HashMap<>();
        listAll.stream().forEach(item -> {
            String deptName = item.getDeptName();
            String statDate = item.getStatDate();
            Map<String,Object> duration = durationMap.get(deptName+":"+statDate);
            if(duration == null){
                durationMap.put(deptName+":"+statDate , duration = initDuration(deptName , statDate));
            }
            duration.put(item.getHour(),item.getCount());
        });
        List<Map<String , Object>> resList = new ArrayList<>();
        durationMap.forEach((k ,v) -> {
            resList.add(v);
        });
        //对结果进行正序排序
        Collections.sort(resList , Comparator.comparingInt(o -> Integer.parseInt(o.get(STAT_DATE).toString().replace("-",""))));
        //返回类型转换
        List<VehicleTravelDurationCountResponse> finalList = new ArrayList<>();
        resList.forEach(item -> {
            try {
                VehicleTravelDurationCountResponse vc = (VehicleTravelDurationCountResponse)BeanUtil.mapToBean(item, VehicleTravelDurationCountResponse.class);
                finalList.add(vc);
            } catch (IntrospectionException e) {
                throw new RuntimeException(e);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException(e);
            } catch (InvocationTargetException e) {
                throw new RuntimeException(e);
            } catch (InstantiationException e) {
                throw new RuntimeException(e);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        });
        return finalList;
    }

    /**
     * @description: 企业在指定时间段内行驶的总天数（有车辆行驶的天数）
     * @author: zhouxw
     * @date: 2022/11/29 4:16 PM
     * @param: [request]
     * @return: int
     **/
    private DeptVehicleTravelCount getDaysCount(VehicleTravelDurationRequest req){
        VehicleTravelDurationRequest request = new VehicleTravelDurationRequest();
        BeanUtils.copyProperties(req , request);
        String monthStart = sdfHolder.get().format(request.getStartTime() * 1000).substring(0 , 7);
        String monthEnd = sdfHolder.get().format(request.getEndTime() * 1000).substring(0 , 7);

        long finalStartTime = request.getStartTime();
        long finalEndTime = request.getEndTime();

        List<DeptAndDateAndCount> countList = new ArrayList<>();

        int totalCount = 0;
        if(!monthStart.equals(monthEnd)){
            //如果跨月了，就需要查询多次
            int monthCount = DateUtil.monthCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime());
            for(int i = 0 ; i < monthCount ; i++){
                long count = 0;
                if(i == 0){
                    //如果是首月
                    //查询从开始时间到当月月底
                    long startSecondTimestamp = finalStartTime;
                    long endSecondTimestamp = DateUtil.getMonthLastSecondTimestamp(startSecondTimestamp);
                    request.setMonth(monthStart.replace("-" , ""));
                    request.setStartTime(startSecondTimestamp);
                    request.setEndTime(endSecondTimestamp);
                }else if(i == monthCount - 1){
                    //如果是尾月
                    //查询从当月月初到结束时间
                    request.setMonth(monthEnd.replace("-" , ""));
                    request.setStartTime(DateUtil.getMonthFirstSecondTimestamp(finalEndTime));
                    request.setEndTime(finalEndTime);
                }else{
                    //如果是其他月份
                    //查询从当月月初到月尾
                    Calendar cal = Calendar.getInstance();
                    cal.set(Calendar.YEAR , Integer.parseInt(monthStart.split("-")[0]));
                    cal.set(Calendar.MONTH, Integer.parseInt(monthStart.split("-")[1])-1);
                    cal.add(Calendar.MONTH , i);
                    int month = cal.get(Calendar.MONTH)+1 ;
                    request.setMonth(cal.get(Calendar.YEAR) + "" + (month < 10 ? "0"+month : month));
                    request.setStartTime(DateUtil.getMonthFirstSecondTimestamp(cal.getTimeInMillis() / 1000));
                    request.setEndTime(DateUtil.getMonthLastSecondTimestamp(cal.getTimeInMillis() / 1000));
                }
                List<DeptAndDateAndCount> list = new ArrayList<>();
                try {
                    //查询指定月总数
                    count = service.findVehicleTravelCount(request);
                    //查询指定月车辆行驶数量列表
                    list = service.findVehicleTravelCountForDept(request);
                }catch (Exception e){
                    //本次查询失败，不能影响后续的查询
                    log.info("本次数据查询失败[{}]，继续执行后面的查询" , request.getMonth() , e);
                }
                totalCount += count;
                countList.addAll(list);
            }

        }else{
            //如果没有跨月，只需要查询一次
            request.setMonth(monthStart.replace("-" , ""));
            int count = 0 ;
            List<DeptAndDateAndCount> list = new ArrayList<>();
            try {
                count = service.findVehicleTravelCount(request);
                //查询指定月车辆行驶数量列表
                list = service.findVehicleTravelCountForDept(request);
            }catch (Exception e){
                log.error("查询车辆行驶记录条数失败",e);
            }
            totalCount = count;
            countList.addAll(list);
        }

        DeptVehicleTravelCount countBean = new DeptVehicleTravelCount();
        countBean.setTotalCount(totalCount);
        countBean.setCountList(countList);

        return countBean;
    }



    /**
     * @description: 初始化各小时行驶车辆数量map
     * @author: zhouxw
     * @date: 2022/11/10 9:01 AM
     * @param: [deptName, statDate]
     * @return: java.util.Map<java.lang.String,java.lang.Object>
     **/
    private Map<String,Object> initDuration(String deptName , String statDate){
        Map<String,Object> duration = new HashMap<>();
        duration.put(DEPT_NAME , deptName);
        duration.put(STAT_DATE , statDate);
        for(int i = 1 ; i < 25 ; i++){
            if(i < 10){
                duration.put("h0"+i , 0);
            }else{
                duration.put("h"+i , 0);
            }
        }
        return duration;
    }

    /**
     * @description: 校验请求参数
     * @author: zhouxw
     * @date: 2022/11/10 1:55 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest
     **/
    private VehicleTravelDurationRequest validRequest(VehicleTravelDurationRequest request) throws Exception{
        //如果开始时间为空，则查询当月开始
        if(request.getStartTime() == null || request.getStartTime() <= 0){
            String monthStr = sdfHolder.get().format(new Date());
            monthStr = monthStr.substring(0,7)+"-01 00:00:00";
            long secondTimestamp = (sdfHolder.get().parse(monthStr).getTime()) / 1000;
            request.setStartTime(secondTimestamp);
        }

        //如果结束时间为空，则查询截止到前一天
        if(request.getEndTime() == null || request.getEndTime() <= 0){
            long timestamp = new Date().getTime() - 24*3600*1000;
            Date date = new Date();
            date.setTime(timestamp);
            String dayStr = sdfHolder.get().format(date);
            dayStr = dayStr.substring(0,10)+" 23:59:59";
            long secondTimestamp = sdfHolder.get().parse(dayStr).getTime() / 1000;
            request.setEndTime(secondTimestamp);
        }
        return request;
    }

    /**
     * @description: 处理跨月份请求时的请求对象
     * @author: zhouxw
     * @date: 2022/11/10 2:04 PM
     * @param: [monthOffsetIndex, monthCount, monthStart, monthEnd, request]
     * @return: com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest
     **/
    private VehicleTravelDurationRequest handleRequestForMultiMonth(int monthOffsetIndex , int monthCount , String monthStart , String monthEnd , long finalStartTime , long finalEndTime ,VehicleTravelDurationRequest request){
        int i = monthOffsetIndex;
        if(i == 0){
            //如果是首月
            //查询从开始时间到当月月底
            long endSecondTimestamp = DateUtil.getMonthLastSecondTimestamp(finalStartTime);
            request.setMonth(monthStart.replace("-",""));
            request.setStartTime(finalStartTime);
            request.setEndTime(endSecondTimestamp);
        }else if(i == monthCount - 1){
            //如果是尾月
            //查询从当月月初到结束时间
            request.setMonth(monthEnd.replace("-",""));
            request.setStartTime(DateUtil.getMonthFirstSecondTimestamp(finalEndTime));
            request.setEndTime(finalEndTime);
        }else{
            //如果是其他月份
            //查询从当月月初到月尾
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.YEAR , Integer.parseInt(monthStart.split("-")[0]));
            cal.set(Calendar.MONTH, Integer.parseInt(monthStart.split("-")[1])-1);
            cal.add(Calendar.MONTH , i);
            int month = cal.get(Calendar.MONTH) + 1;
            String monthStr = month + "";
            if(month < 10){
                monthStr = "0" + month;
            }
            request.setMonth(cal.get(Calendar.YEAR) + "" + monthStr);
            request.setStartTime(DateUtil.getMonthFirstSecondTimestamp(cal.getTimeInMillis() / 1000));
            request.setEndTime(DateUtil.getMonthLastSecondTimestamp(cal.getTimeInMillis() / 1000));
        }
        return request;
    }

    /**
     * @description: 统计客运车辆夜间行驶的情况
     * @author: zhouxw
     * @date: 2022/11/10 1:50 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @PostMapping("/statPassengerVehicleInNight")
    public R statPassengerVehicleInNight(@Validated @RequestBody VehicleTravelDurationRequest request){

        try{

            //1.根据日期判断是否跨月，如果跨月，需要查询多次
            request = validRequest(request);

            long finalStartTime = request.getStartTime();
            long finalEndTime = request.getEndTime();

            //2.判断开始时间的月份和结束时间的月份是否跨月，并执行数据统计
            List<DateAndCount> listAll = new ArrayList<>();
            String monthStart = sdfHolder.get().format(request.getStartTime() * 1000).substring(0 , 7);
            String monthEnd = sdfHolder.get().format(request.getStartTime() * 1000).substring(0 , 7);
            if(!monthStart.equals(monthEnd)){
                //如果跨月了，就需要查询多次
                int monthCount = DateUtil.monthCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime());
                for(int i = 0 ; i < monthCount ; i++){
                    List<DateAndCount> list = new ArrayList<>();
                    request = handleRequestForMultiMonth(i , monthCount , monthStart , monthEnd , finalStartTime , finalEndTime , request);
                    list = service.findPassengerVehCountInNight(request);
                    listAll.addAll(list);
                }
            }else{
                //如果没有跨月，只需要查询一次
                request.setMonth(monthStart.replace("-" , ""));
                List<DateAndCount> list = service.findPassengerVehCountInNight(request);
                listAll.addAll(list);
            }
            //转换为map，方便后续查找使用
            Map<String ,Integer> countInNightMap = new HashMap<>();
            listAll.stream().forEach(item -> {
                countInNightMap.put(item.getStatDate() , item.getCount() == null ? 0 : item.getCount().intValue());
            });

            //3.查询车辆上线情况
            List<DateAndDeptAndCount> goOnlineList =  vehicleStatService.findPassengerVehicleCountByDay(request.getDeptId() , finalStartTime , finalEndTime);
            //转换为map，方便后续查找使用
            Map<String , Integer> goOnlineMap = new HashMap<>();
            goOnlineList.stream().forEach(item -> {
                goOnlineMap.put(item.getStatDate() , item.getCount());
            });

            //4.组装返回数据
            //将所有天都返回，即使没有数据
            Date startDate = new Date();
            startDate.setTime(request.getStartTime() * 1000);
            Calendar cal = Calendar.getInstance();
            cal.setTime(startDate);
            List<PassengerVehicleInNightCountResponse> list = new ArrayList<>();
            while(cal.getTimeInMillis() < request.getEndTime() * 1000){
                PassengerVehicleInNightCountResponse req = new PassengerVehicleInNightCountResponse();
                String statDate = sdfHolder.get().format(cal.getTime()).substring(0,10);
                int inNightCount = countInNightMap.get(statDate)==null?0:countInNightMap.get(statDate);
                int goOnlineCount = goOnlineMap.get(statDate)==null?0:goOnlineMap.get(statDate);
                double rate = 0;
                if(inNightCount != 0 && goOnlineCount != 0){
                    rate = MathUtil.divideRoundDouble(inNightCount, goOnlineCount, 4);
                }
                String ratePercentStr = MathUtil.formatToPercent(rate , 2);
                req.setStatDate(statDate);
                req.setPassengerVehInNightCount(inNightCount);
                req.setPassengerVehGoOnlineCount(goOnlineCount);
                req.setPassengerVehInNightRate(rate);
                req.setPassengerVehInNightRatePercentStr(ratePercentStr);
                list.add(req);
                cal.add(Calendar.DAY_OF_MONTH , 1);
            }
            return R.data(list);
        }catch (Exception e){
            log.error("统计客车夜间行驶情况失败",e);
            return R.fail("统计客车夜间行驶情况失败");
        }
    }


    /**
     * @description: 车辆行驶时长分析（单位：小时）
     * 统计车辆日平均行驶时长，日平均行驶时长=总行驶时长/上线车辆数
     * @author: zhouxw
     * @date: 2022/11/10 3:14 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @PostMapping("/statvehicleTravelDuration")
    public R statvehicleTravelDuration(@Validated @RequestBody VehicleTravelDurationRequest request){
        try{

            //1.根据日期判断是否跨月，如果跨月，需要查询多次
            request = validRequest(request);

            long finalStartTime = request.getStartTime();
            long finalEndTime = request.getEndTime();

            //2.判断开始时间的月份和结束时间的月份是否跨月，并执行数据统计
            List<DateAndData> listAll = new ArrayList<>();
            String monthStart = sdfHolder.get().format(request.getStartTime() * 1000).substring(0 , 7);
            String monthEnd = sdfHolder.get().format(request.getEndTime() * 1000).substring(0 , 7);
            if(!monthStart.equals(monthEnd)){
                //如果跨月了，就需要查询多次
                int monthCount = DateUtil.monthCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime());
                for(int i = 0 ; i < monthCount ; i++){
                    List<DateAndData> list = new ArrayList<>();
                    request = handleRequestForMultiMonth(i , monthCount , monthStart , monthEnd , finalStartTime , finalEndTime , request);
                    try {
                        list = service.findAverageTravelDurationByDay(request);
                    }catch (Exception e){
                        //查询失败时，继续下面的查询
                        log.info("执行数据查询失败[{}]，继续执行下面的查询" , request.getMonth() , e);
                    }
                    listAll.addAll(list);
                }

            }else{
                //如果没有跨月，只需要查询一次
                request.setMonth(monthStart.replace("-" , ""));
                List<DateAndData> list = service.findAverageTravelDurationByDay(request);
                listAll.addAll(list);
            }
            //转换为map，方便后续查找使用
            Map<String ,Double> totalDurationMap = new HashMap<>();
            listAll.stream().forEach(item -> {
                totalDurationMap.put(item.getStatDate() , item.getData());
            });

            //3.查询车辆上线情况
            List<DateAndDeptAndCount> goOnlineList =  vehicleStatService.findPassengerVehicleCountByDay(request.getDeptId() , finalStartTime , finalEndTime);
            //转换为map，方便后续查找使用
            Map<String , Integer> goOnlineMap = new HashMap<>();
            goOnlineList.stream().forEach(item -> {
                goOnlineMap.put(item.getStatDate() , item.getCount());
            });

            //4.组装返回数据
            //将所有天都返回，即使没有数据
            Date startDate = new Date();
            startDate.setTime(finalStartTime * 1000);
            Calendar cal = Calendar.getInstance();
            cal.setTime(startDate);
            List<VehicleTravelAverageDurationResponse> list = new ArrayList<>();
            while(cal.getTimeInMillis() < request.getEndTime() * 1000){
                VehicleTravelAverageDurationResponse req = new VehicleTravelAverageDurationResponse();
                String statDate = sdfHolder.get().format(cal.getTime()).substring(0,10);
                double totalDurationSecond = totalDurationMap.get(statDate) == null?0:totalDurationMap.get(statDate);

                double totalDuration = totalDurationSecond / 3600;
                int goOnlineCount = goOnlineMap.get(statDate) == null?0:goOnlineMap.get(statDate);

                double averageDuration = 0;
                if(totalDuration != 0 && goOnlineCount != 0){
                    averageDuration = MathUtil.divideRoundDouble(totalDuration , goOnlineCount , 2);
                }

                req.setStatDate(statDate);
                req.setAverageDuration(averageDuration);
                list.add(req);
                cal.add(Calendar.DAY_OF_MONTH , 1);
            }


            return R.data(list);
        }catch (Exception e){
            log.error("车辆行驶时长统计失败",e);
            return R.fail("车辆行驶时长统计失败");
        }
    }

    /**
     * @description: 车辆行驶里程分析
     * 综合企业和日期进行分页
     * 统计每天不同里程段的车辆数，按照完整率表中的总里程进行统计
     * @author: zhouxw
     * @date: 2022/11/10 4:10 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @PostMapping("/statVehicleCountByTravelDistance")
    public R<IPage<List<VehicleTravelMileageResponse>>> statVehicleCountByTravelDistance(@Validated @RequestBody VehicleTravelDurationRequest request){

        try{

            request = validRequest(request);

            if(request.getCount() == null || request.getCount() < 1){
                //如果不传递页面size，那么就默认设置为10
                request.setCount(10);
            }
            if(request.getStart() == null ){
                request.setStart(0);
            }

            IPage pageF = new Page();
            //查询企业总数
			//查询 tenantId
			BladeUser user = AuthUtil.getUser();
			if(user == null){
				log.error("用户未登录或鉴权失败");
				return R.fail("操作失败：用户未登录或鉴权失败");
			}
			String tenantId = user.getTenantId();
            LambdaQueryWrapper<BladeDept> wrapper = Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId , tenantId).eq(BladeDept::getIsDeleted ,0);
            if(request.getDeptId() != null){
                wrapper.eq(BladeDept::getId , request.getDeptId());
            }
            wrapper.orderByAsc(BladeDept::getId);
            //查询要查询的时间段天数
            int days = DateUtil.getDayCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime()) + 1;
            long deptCount = deptService.count(wrapper);
            long totalCount = days * deptCount;

            List<VehicleTravelMileageResponse> listAll = VehicleCountByTravelDistanceForSearchOrExport(request );
            pageF.setTotal(totalCount);
            pageF.setSize(request.getCount());
            pageF.setCurrent(request.getStart()/request.getCount()+(request.getStart() % request.getCount()>0?1:0));
            pageF.setRecords(listAll);
            return R.data(pageF);
        }catch (Exception e){
            log.error("统计驾驶员驾驶里程分析失败",e);
            return R.fail("统计驾驶员驾驶里程分析失败");
        }
    }



    /**
     * @description: 车辆行驶里程分析导出
     * @author: zhouxw
     * @date: 2022/11/30 3:28 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R
     **/
    @PostMapping("exportVehicleCountByTravelDistance")
    public R<String> exportVehicleCountByTravelDistance(@Validated @RequestBody VehicleTravelDurationRequest request){
        try{

            //1.根据日期判断是否跨月，如果跨月，需要查询多次
            request = validRequest(request);

            List<VehicleTravelMileageResponse> list =  VehicleCountByTravelDistanceForSearchOrExport(request);

            //导出数据
            String title = "企业车辆行驶里程分析";
            Date date = new Date();
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
            String dateStr = format.format(date);
            String fileName = title + "_" + dateStr + ".xlsx";
            FileOutputStream file = new FileOutputStream(staticFilePath + fileName);
            fileName =  "/bt/statistics/files/"+fileName;

            //主标题和副标题在excel中分别是是第0和第1行
            List<Integer> columnIndexes = Arrays.asList(0,1);
            //自定义标题和内容策略(具体定义在下文)
            ExcelExportFontAndStyleConfig cellStyleStrategy =
                    new ExcelExportFontAndStyleConfig(columnIndexes,new WriteCellStyle(), new WriteCellStyle());

            EasyExcel.write(file, VehicleTravelMileageResponse.class).registerWriteHandler(cellStyleStrategy).registerWriteHandler(new ExcelExportRowHeightConfig()).sheet(title).doWrite(list);
            return R.data(fileName);
        }catch (Exception e){
            log.error("企业车辆行驶里程分析导出失败",e);
            return R.fail("企业车辆行驶里程分析导出失败");
        }
    }



    private List<VehicleTravelMileageResponse> VehicleCountByTravelDistanceForSearchOrExport(VehicleTravelDurationRequest request) throws Exception{
		//查询要查询的时间段天数
		int days = DateUtil.getDayCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime()) + 1;
        //查询企业总数
		//查询tenantId
		BladeUser user = AuthUtil.getUser();
		if(user == null){
			throw new Exception("用户未登录或鉴权失败");
		}
		String tenantId = user.getTenantId();
        LambdaQueryWrapper<BladeDept> wrapper = Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId , tenantId).eq(BladeDept::getIsDeleted ,0);
        if(request.getDeptId() != null){
            wrapper.eq(BladeDept::getId , request.getDeptId());
        }
        wrapper.orderByAsc(BladeDept::getId);
        List<BladeDept> deptList = deptService.list(wrapper);
        if(deptList == null){
            deptList = new ArrayList<>();
        }
        int totalCount = days * deptList.size();
        totalCount = request.getDeptId()==null?totalCount:days;
            List<VehicleTravelMileageResponse> listAll = new ArrayList<>();
            //说明：分页以企业为第一维度，日期为第二维度。每个企业对应的日期时间段相同，也就是说每个企业对应的天数相同；以企业分组，每组里边有多个天。

            //根据页码获取要查询的范围
            int start = request.getStart()==null?0:request.getStart();
            int end = start + (request.getCount()==null?Integer.MAX_VALUE:request.getCount());
            if(end > totalCount){
                end = totalCount;
            }

            int deptListStartIndex = (start-1) / days;
            int daysStartOffset = start % days;
            int deptListEndIndex = (end-1) / days;
            int daysEndOffset = (end-1) % days;

            if(deptListStartIndex == deptListEndIndex){
                //如果开始点和结束点是相同的企业
                long startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartTime()) + daysStartOffset * 24 * 3600;
                long endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getStartTime()) + daysEndOffset * 24 * 3600;
                VehicleTravelDurationRequest req = new VehicleTravelDurationRequest();
                req.setStart(request.getStart());
                req.setCount(request.getCount());
                req.setDeptId(request.getDeptId());
                req.setStartTime(startTimestamp);
                req.setEndTime(endTimestamp);
                req.setVehicleUseType(request.getVehicleUseType());
                //List<VehicleTravelDurationCountResponse> onceList = getVehicleCountPerHourForOnce(request , DateUtil.getDayFirstSecondTimestamp(request.getStartTime()) ,DateUtil.getDayLastSecondTimestamp(request.getEndTime()) );
                List<VehicleTravelMileageResponse> list = statVehicleCountForOnce(req);
                listAll.addAll(list);
            }else{
                //如果开始点和结束点是不同的企业
                int diff = deptListEndIndex - deptListStartIndex;
                long startTimestamp = 0;
                long endTimestamp = 0;
                long deptId = 0;
                for(int i = 0 ; i <= diff ; i++){
                    if(i == 0){
                        //如果是第一个企业
                        startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartTime()) + daysStartOffset * 24 * 3600;
                        endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getEndTime());
                        deptId = deptList.get(deptListStartIndex).getId();
                    }else if (i == diff){
                        //如果是最后一个企业
                        startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartTime());
                        endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getStartTime()) + daysEndOffset * 24 * 3600;
                        deptId = deptList.get(deptListEndIndex).getId();
                    }else{
                        //如果是中间企业
                        startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartTime());
                        endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getEndTime());
                        deptId = deptList.get(deptListStartIndex + i).getId();
                    }
                    VehicleTravelDurationRequest req = new VehicleTravelDurationRequest();
                    req.setStart(request.getStart());
                    req.setCount(request.getCount());
                    req.setDeptId(deptId);
                    req.setStartTime(startTimestamp);
                    req.setEndTime(endTimestamp);
                    req.setVehicleUseType(request.getVehicleUseType());
                    List<VehicleTravelMileageResponse> list = statVehicleCountForOnce(req);
                    listAll.addAll(list);
                }
            }

            //对结果进行排序
            Collections.sort(listAll,(o1 , o2) -> {
                if(o1.getDeptId().longValue() != o2.getDeptId().longValue()){
                    return Math.toIntExact(o1.getDeptId() - o2.getDeptId());
                }else{
                    int diff = 0;
                    try {
                        diff = (int) (sdfHolderShort.get().parse(o1.getStatDate()).getTime() - sdfHolderShort.get().parse(o2.getStatDate()).getTime());
                    }catch (Exception e){
                        log.info("类型转换报错",e);
                    }
                    return diff;
                }
            });

            return listAll;
    }


    /**
     * 指定企业
     * @param request
     * @return
     * @throws Exception
     */
    private List<VehicleTravelMileageResponse> statVehicleCountForOnce(VehicleTravelDurationRequest request ) throws Exception{

        long finalStartTime = request.getStartTime();
        long finalEndTime = request.getEndTime();

        List<TotalMileageNode> listAll = new ArrayList<>();
        String monthStart = sdfHolder.get().format(request.getStartTime() * 1000).substring(0 , 7);
        String monthEnd = sdfHolder.get().format(request.getEndTime() * 1000).substring(0 , 7);
        if(!monthStart.equals(monthEnd)){
            //如果跨月了，就需要查询多次
            int monthCount = DateUtil.monthCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime());
            for(int i = 0 ; i < monthCount ; i++){
                List<TotalMileageNode> list = new ArrayList<>();
                VehicleTravelDurationRequest req = new VehicleTravelDurationRequest();
                BeanUtils.copyProperties(request , req);
                req = handleRequestForMultiMonth(i , monthCount , monthStart , monthEnd , finalStartTime , finalEndTime , req);
                list = service.findTotalMileageByDay(req);
                listAll.addAll(list);
            }

        }else{
            //如果没有跨月，只需要查询一次
            request.setMonth(monthStart.replace("-" , ""));
            List<TotalMileageNode> list = service.findTotalMileageByDay(request);
            listAll.addAll(list);
        }

        //统计各个里程段的车辆数量，以及总里程（按照 deptId 和 statDate 分组）
        Map<String, VehicleTravelMileageResponse> mileageMap = new HashMap<>();
        listAll.stream().forEach(item -> {
            String statDate = item.getStatDate();
            long deptId = item.getDeptId();
            VehicleTravelMileageResponse req = mileageMap.get(deptId+"~"+statDate);
            if(req == null){
                req = new VehicleTravelMileageResponse();
            }
            //判断里程
            double mileage = item.getTotalMileage();
            if(mileage / 1000 > 0 && mileage / 1000 <= 100 ){
                req.setLevel1Count(req.getLevel1Count() + 1);
            }else if(mileage / 1000 > 100 && mileage / 1000 <= 500){
                req.setLevel2Count(req.getLevel2Count() + 1);
            }else if(mileage / 1000 > 500 && mileage / 1000 <= 1000){
                req.setLevel3Count(req.getLevel3Count() + 1);
            }else{
                req.setLevel4Count(req.getLevel4Count() + 1);
            }
            req.setDeptId(item.getDeptId());
            req.setDeptName(item.getDeptName());
            req.setStatDate(item.getStatDate());
            req.setTotalMileage(req.getTotalMileage() + item.getTotalMileage() / 1000); //千米
            mileageMap.put(deptId+"~"+statDate , req);
        });


        //3.查询车辆上线情况
        List<DateAndDeptAndCount> goOnlineDeptList = vehicleStatService.findDeptVehicleCountByDay(request.getDeptId() , finalStartTime , finalEndTime , request.getVehicleUseType());
        //转换为map，方便后续查找使用
        Map<String , Integer> goOnlineMap = new HashMap<>();
        goOnlineDeptList.stream().forEach(item -> {
            long deptId = item.getDeptId();
            String statDate = item.getStatDate();
            goOnlineMap.put(deptId+"~"+statDate.replace("-","") , item.getCount());
        });

        //4.组装返回数据
        List<VehicleTravelMileageResponse> resList = new ArrayList<>();
        mileageMap.forEach((k , v) ->{
            //总里程
            double totalMileage = v.getTotalMileage();
            //总车辆数
            int totalCount = goOnlineMap.get(k)==null?0:goOnlineMap.get(k);
            //平均里程
            double average = 0;
            if(totalMileage != 0 && totalCount != 0){
                average = MathUtil.roundDouble(totalMileage / totalCount , 2);
            }
            v.setTotalMileage(MathUtil.roundDouble(v.getTotalMileage() , 2));
            v.setTotalVehicleCount(totalCount);
            v.setAverageMileage(MathUtil.roundDouble(average , 2));
            v.setStatDate(v.getStatDate().substring(0,4)+"-"+v.getStatDate().substring(4,6)+"-"+v.getStatDate().substring(6,8));
            resList.add(v);
        });

        Collections.sort(resList , (o1 , o2) -> {
            if(o1.getDeptId() != o2.getDeptId()){
                return (int)o1.getDeptId().longValue() - (int)o2.getDeptId().longValue();
            }else{
                return Integer.parseInt(o1.getStatDate()) - Integer.parseInt(o2.getStatDate());
            }
        });

        //补充数据
        int days = DateUtil.getDayCountBetweenSecondTimestamp(request.getStartTime() , request.getEndTime());
        long timestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartTime());
        String deptName = "";
        if((resList == null || resList.size() < 1) && StringUtils.isBlank(deptName)){
            BladeDept dept = deptService.getById(request.getDeptId());
            if(dept != null){
                deptName = dept.getDeptName();
            }
        }else{
            deptName = resList.get(0).getDeptName();
        }
        Set<String> daySet = new HashSet<>();
        resList.forEach(item -> {
            daySet.add(item.getStatDate());
        });
        for(int i = 0 ; i <= days; i++){
            String statDate = DateUtil.getDateString(timestamp);
            //总车辆数
            String k = request.getDeptId()+"~"+statDate;
            int totalCount = goOnlineMap.get(k)==null?0:goOnlineMap.get(k);
            if(!daySet.contains(statDate)){
                VehicleTravelMileageResponse node = new VehicleTravelMileageResponse();
                node.setDeptId(request.getDeptId());
                node.setDeptName(deptName);
                node.setStatDate(statDate);
                node.setTotalMileage(0D);
                node.setTotalVehicleCount(totalCount);
                node.setAverageMileage(0D);
                node.setLevel1Count(0);
                node.setLevel2Count(0);
                node.setLevel3Count(0);
                node.setLevel4Count(0);
                resList.add(node);
            }
            timestamp += 24*3600;
        }

        return resList;
    }

}
