package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 按用户统计数据（用于主页数据展示）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatCountRateUser implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 报警数量
     */
    private Long alarmCount = 0L;

    /**
     * 处理数
     */
    private Long handleCount = 0L;

    /**
     * 处理率
     */
    private Double handleRate = 0D;

	//上线率
	private Long goOnlineCount = 0L;

	//上线率
	private Double goOnlineRate = 0D;

	//数据合格率
	private Double qualityRate = 0D;

	//轨迹完整率
	private Double completeRate = 0D;

	//定位漂移率
	private Double driftRate = 0D;

    /**
     * 统计日期（该日期之前的30天参与数据统计）
     */
    private String date;

	//数据创建日期
	private Date createTime;


}
