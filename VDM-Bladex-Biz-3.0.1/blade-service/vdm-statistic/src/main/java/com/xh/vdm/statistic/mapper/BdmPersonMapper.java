package com.xh.vdm.statistic.mapper;

import com.xh.vdm.statistic.entity.BdmPerson;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 人员管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface BdmPersonMapper extends BaseMapper<BdmPerson> {

	/**
	 * 根据身份证号/手机号、岗位类型获取人员信息
	 * @param idCard
	 * @param phone
	 * @param jobType
	 * @return
	 */
	List<BdmPerson> getPerson(@Param("idCard") String idCard, @Param("phone") String phone, @Param("jobType") String jobType);


	/**
	 * 获取人员信息
	 * @param idCard
	 * @return
	 */
	List<BdmPerson> getPersonByIdCard(@Param("idCard") String idCard);
}
