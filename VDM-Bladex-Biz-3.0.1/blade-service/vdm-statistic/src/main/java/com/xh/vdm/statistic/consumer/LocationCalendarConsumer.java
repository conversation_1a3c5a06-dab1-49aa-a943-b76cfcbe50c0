package com.xh.vdm.statistic.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xh.vdm.statistic.entity.BdmTrackCalendar;
import com.xh.vdm.statistic.service.IBdmTrackCalendarService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springblade.entity.Location;
import org.springframework.beans.BeanUtils;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

@Slf4j
@Component
@EnableKafka
public class LocationCalendarConsumer {

	@Resource(name = "BdmTrackCalendarService")
	private IBdmTrackCalendarService bdmTrackCalendarService;

	@KafkaListener(
		containerFactory = "locationCalendarKafkaListenerContainerFactory",
		topics = {"ce.comms.fct.location.0"},
		batch = "true"
	)
	public void recordLocationCalendar (List<ConsumerRecord<String, String>> consumerRecords, Acknowledgment acknowledgment) {
		if (CollectionUtils.isEmpty(consumerRecords)) {
			return;
		}

		String locStr;
		Location location;
		List<BdmTrackCalendar> locationCalendarList;
		BdmTrackCalendar locationCalendar;
		Date date;
		SimpleDateFormat dayFormat = new SimpleDateFormat("dd");
		int day;
		for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
			locStr = consumerRecord.value();
//			log.info("receive msg when record location calendar: {}", locStr);
			location = JSON.parseObject(locStr, Location.class);
			if (location == null) {
				log.error("fail parse location when record location calendar: {}", locStr);
				continue;
			}

			date = new Date(location.getTime() * 1000);
			day = Integer.parseInt(dayFormat.format(date));
			locationCalendarList = this.bdmTrackCalendarService.getLocCalendarByLocation(location);
			if (CollectionUtils.isEmpty(locationCalendarList)) {
				locationCalendar = new BdmTrackCalendar();
				BeanUtils.copyProperties(location, locationCalendar);
				locationCalendar.setMonth(date);
				locationCalendar.setMark(1 << day);
				this.bdmTrackCalendarService.save(locationCalendar);
			} else {
				locationCalendar = locationCalendarList.get(0);
				locationCalendar.setMark(locationCalendar.getMark() | (1 << day));
				this.bdmTrackCalendarService.updateById(locationCalendar);
			}
		}

		// 有点类似InnoDB事务的提交，如果消费了消息，但消息处理过程出现异常而退出，则消息可被回滚到列队中。只有执行到这步，相当于提交，消息队列才正式认为该消息已被消费。
		acknowledgment.acknowledge();
	}
}
