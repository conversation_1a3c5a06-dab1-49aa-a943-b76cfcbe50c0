package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.StatEmailHistory;
import com.xh.vdm.statistic.mapper.StatEmailMapper;
import com.xh.vdm.statistic.service.IStatEmailService;
import com.xh.vdm.statistic.vo.request.EmailRequest;
import com.xh.vdm.statistic.vo.response.EmailHistoryResponse;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Service
public class StatEmailServiceImpl extends ServiceImpl<StatEmailMapper, StatEmailHistory> implements IStatEmailService {

    @Override
    public IPage<EmailHistoryResponse> findEmailHistoryList(IPage<EmailHistoryResponse> page, EmailRequest request) throws Exception {
        return baseMapper.getEmailHistoryList(page, request);
    }
}
