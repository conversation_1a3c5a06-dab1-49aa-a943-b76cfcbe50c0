package com.xh.vdm.statistic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.BdmVideoPlay;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 映射：视频播放表
 */
public interface BdmVideoPlayMapper extends BaseMapper<BdmVideoPlay> {

    /**
     * 根据时间段获取视频播放
     */
    @DS("master")
    List<BdmVideoPlay> getDailyVideoPlay (@Param("startTime") Integer startTime, @Param("endTime") Integer endTime);
}
