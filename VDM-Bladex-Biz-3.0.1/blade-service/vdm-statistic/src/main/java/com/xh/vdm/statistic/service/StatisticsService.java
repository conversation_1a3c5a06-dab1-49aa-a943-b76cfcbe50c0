package com.xh.vdm.statistic.service;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.CacheVehicleOnlineOrOfflineMapper;
import com.xh.vdm.statistic.mapper.StatisticsMapper;
import com.xh.vdm.statistic.utils.CacheUtil;
import com.xh.vdm.statistic.utils.CommonBusiUtil;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.vo.request.*;
import com.xh.vdm.statistic.vo.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

/**
 * <AUTHOR>
 * @date 2021/10/26 14:39
 */
@Service
@Slf4j
public class StatisticsService {

    @Autowired
    StatisticsMapper statisticsMapper;

	@Resource
	private IBladeDeptService bladeDeptService;

	@Resource
	private CacheVehicleOnlineOrOfflineMapper mapper;

	@Resource
	private CommonBusiUtil commonBusiUtil;

	@Resource
	private CacheUtil cacheUtil;

	@Resource
	private IOnlineOfflineRecordService onlineService;

	@Resource
	private IBamThirdPartyPlatformService thirdPartyPlatformService;

	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(20, 50, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

	/**
	 * 查询部门
	 * @param deptIds
	 * @param deptId
	 * @param query
	 * @return
	 */
    public IPage<BladeDept> findDept(List<Long> deptIds, String deptId, Query query){
        IPage<BladeDept> page = new Page<>();
		page.setSize(query.getSize());
		page.setCurrent(query.getCurrent());
        return statisticsMapper.getDept(deptIds,deptId, page);
    }

	/**
	 * 查找子部门
	 * @param deptId
	 * @return
	 */
	public List<BladeDept> findChildrenDept(Long deptId){
		return statisticsMapper.getChildrenDept(deptId);
	}

    public Page<VehicleOfflineResponse> vehicleOffline(VehicleOfflineRequest req){
        Page<VehicleOfflineResponse> page = new Page<>(req.getCurrent(), req.getSize());
        return statisticsMapper.getVehicleOfflinePage(page, req);
    }


    public IPage<VehicleOperationResponse> vehicleOperation(CommonBaseRequest req, String tenantId, Query query) throws Exception{
		IPage<VehicleOperationResponse> page = new Page<>();
		page.setSize(query.getSize());
		page.setCurrent(query.getCurrent());
		//查询部门信息、上级平台信息，用于后续手动增加字段
		Map<String,BladeDept> deptMap = new HashMap<>();
		Map<String, BamThirdPartyPlatform> platMap = new HashMap<>();
		Map<String, VehicleOnlineBase> lastOnlineMap = new HashMap<>();
		CountDownLatch countDownLatch = new CountDownLatch(1);
		threadPool.submit(() -> {
			try{
				List<BladeDept> depts = null;
				List<BamThirdPartyPlatform> platList = null;
				List<VehicleOnlineBase> onlineList = null;
				platList = thirdPartyPlatformService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled, 0));
				depts = bladeDeptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
				platList.forEach(item -> {
					platMap.put(item.getId()+"", item);
				});
				depts.forEach(item -> {
					deptMap.put(item.getId()+"", item);
				});
			}catch (Exception e){
				log.error("异步处理失败",e);
			}finally {
				countDownLatch.countDown();
			}
		});
		IPage<VehicleOperationResponse> pageList = statisticsMapper.vehicleOperation(page, req, tenantId);

		if(pageList == null || pageList.getTotal() < 1){
			return pageList;
		}
		//查询车辆最后上线时间
		List<Integer> vehicleIds = new ArrayList<>();
		pageList.getRecords().forEach(item -> {
			vehicleIds.add(item.getVehicleId());
		});
		List<VehicleOnlineBase> onlineList = onlineService.findLastOnlineDate(vehicleIds);
		onlineList.forEach(item -> {
			lastOnlineMap.put(item.getVehicleId()+"", item);
		});
		countDownLatch.await();
		//补充字段
		pageList.getRecords().forEach(item -> {
			Long deptId = item.getDeptId();
			Long vehicleOwnerId = item.getVehicleOwnerId();
			BladeDept dept = deptMap.get(deptId+"");
			BamThirdPartyPlatform plat = platMap.get(vehicleOwnerId+"");
			VehicleOnlineBase vehicleOnlineBase = lastOnlineMap.get(item.getVehicleId()+"");
			if(dept != null){
				item.setDeptName(dept.getDeptName());
			}
			if(plat != null){
				item.setVehicleOwner(StringUtils.isEmpty(plat.getName())?"非营运车辆":plat.getName());
			}else{
				item.setVehicleOwner("非营运车辆");
			}
			if(vehicleOnlineBase != null){
				item.setLastOnlineTime(vehicleOnlineBase.getOnlineDate());
			}

			//从字典中获取数据
			Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
			Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
			Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
			item.setVehicleUseType(vehicleUseTypeMap.get(item.getVehicleUseTypeCode()));
			item.setLicenceColor(licenceColorMap.get(item.getLicenceColorCode()));
			item.setAccessMode(accessModeMap.get(item.getAccessModeCode()));
		});

		return pageList;
    }

    /**
     * @description: 车辆运行情况巡检，不包含最后上线时间
     * @author: zhouxw
     * @date: 2023-02-46 18:26:21
     * @param: [req]
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.VehicleOperationResponse>
     **/
    public Page<VehicleOperationResponse> vehicleOperationWithoutLastOnlineTime(VehicleOperationRequest req){
        Page page = new Page(req.getCurrent(), req.getSize());
        return statisticsMapper.vehicleOperationWithoutLastOnlineTime(page, req);
    }

    /**
     * @description: 根据车牌
     * @author: zhouxw
     * @date: 2023-02-46 18:10:18
     * @param: [licencePlate, licenceColor]
     * @return: java.util.Date
     **/
    public List<LastOnlineTimeEntity> findLastOnlineTime(List<String> licencePlateList){
        return statisticsMapper.getLastOnlineTime(licencePlateList);
    }


    public Page<Map> rateStatistics(OnlineRateRequest req){
		List<String> dateList = req.getDateList();
		List<String> yearMonthList = new ArrayList<>();
		for (String date : dateList) {
			yearMonthList.add(date.replace("-", "").substring(0, 6));
		}

		req.setYearMonthList(yearMonthList);
        Page page = new Page(req.getCurrent(), req.getSize());
        return statisticsMapper.rateStatistics(page, req);
    }

//    public Page<VehicleOnlineOrOfflineResponse> vehicleOnlineOrOffline(VehicleOnlineOrOfflineRequest req){
//        Page page = new Page(req.getCurrent(), req.getSize());
//        return statisticsMapper.vehicleOnlineOrOffline(page, req);
//    }

//    public Page<NightDrivingResponse> statisticsNightDriving(NightDrivingRequest req){
//        Page page = new Page(req.getCurrent(), req.getSize());
//        return statisticsMapper.getNightDriving(page, req);
//    }

//    public Page<FatigueDrivingResponse> statisticsFatigueDriving(FatigueDrivingRequest req){
//        Page page = new Page(req.getCurrent(), req.getSize());
//        return statisticsMapper.getFatigueDriving(page, req);
//    }

    public Page<HashMap> queryDayMileage(DayMileageRequest req){
        Page page = new Page(/*req.getCurrent(), req.getSize()*/);
        return statisticsMapper.queryDayMileage(page, req);
    }

    public Page<SecurityInfoResponse> securityInfo(SecurityInfoRequest req){
        Page page = new Page(req.getCurrent(), req.getSize());
        return statisticsMapper.securityInfo(page, req);
    }

    /**
     * @description: 根据时间段查询 车辆安全信息记录
     * @author: zhouxw
     * @date: 2023-02-45 17:03:44
     * @param: [startTime, endTime]
     * @return: java.util.List<com.xh.vdm.statistic.entity.CacheSecurityInfoResponse>
     **/
    public List<CacheSecurityInfoResponse> securityInfoWithTime(Date startTime, Date endTime){
        return statisticsMapper.securityInfoWithTime(startTime, endTime);
    }


	/**
	 * 查询跨月上下线查询记录
	 * @param request
	 * @return
	 */
	public IPage<VehicleOnlineOrOfflineResponse> findVehicleOnlineOrOffline(CommonBaseCrossMonthDurationRequest request, IPage<VehicleOnlineOrOfflineResponse> page){
		IPage<VehicleOnlineOrOfflineResponse> pageList = statisticsMapper.getVehicleOnlineOrOffline(request, page);
		if (pageList.getTotal() <= 0) {
			return pageList;
		}

		List<String> params = new ArrayList<>();

		pageList.getRecords().forEach(item -> {
			params.add(item.getLicencePlate()+"~"+item.getLicenceColor());
		});
		//查询车辆实时在线状态
		List<TeState> TeStates = mapper.getVeState(params);

		Map<String,String> stateMap = cacheUtil.getTerminalStateMap();
		Map<String,String> stateResultMap = new HashMap<>();
		//更新实时在线状态
		for (TeState teState : TeStates) {
			stateResultMap.put(teState.getLicencePlate()+"~"+teState.getLicenceColor(),stateMap.get(teState.getTeState()+""));
		}

		pageList.getRecords().forEach(item -> {
			item.setTeState(stateResultMap.get(item.getLicencePlate()+"~"+item.getLicenceColor()));
		});

		return pageList;
	}















}
