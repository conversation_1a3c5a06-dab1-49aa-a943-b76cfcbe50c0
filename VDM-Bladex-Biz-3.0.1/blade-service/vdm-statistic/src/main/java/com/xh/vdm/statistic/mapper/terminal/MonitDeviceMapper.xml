<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.terminal.MonitDeviceMapper">

    <sql id="dept_list">
        <if test="userId != null">
            AND EXISTS (
            SELECT 1
            FROM bdm_user_dept_regulates usr
            WHERE usr.dept_id = ${tableAlias}.dept_id
            AND usr.user_id = #{userId}
            )
        </if>
    </sql>

    <select id="statisticsTerminalByYear" resultType="java.lang.Integer">
        SELECT count(*)
        FROM (SELECT create_time
              FROM bdm_rdss_device dd
              WHERE deleted = 0
              <if test="userId != null">
                  <include refid="dept_list">
                      <property name="tableAlias" value="dd"/>
                  </include>
              </if>

              UNION ALL

              SELECT create_time
              FROM bdm_rnss_device rd
              WHERE deleted = 0
            <if test="userId != null">
                <include refid="dept_list">
                    <property name="tableAlias" value="rd"/>
                </include>
            </if>

              UNION ALL

              SELECT create_time
              FROM bdm_monit_device md
              WHERE deleted = 0
            <if test="userId != null">
                <include refid="dept_list">
                    <property name="tableAlias" value="md"/>
                </include>
            </if>

              UNION ALL

              SELECT create_time
              FROM bdm_pnt_device pd
              WHERE deleted = 0
            <if test="userId != null">
                <include refid="dept_list">
                    <property name="tableAlias" value="pd"/>
                </include>
            </if>

              UNION ALL

              SELECT create_time
              FROM bdm_wearable_device wd
              WHERE deleted = 0
            <if test="userId != null">
                <include refid="dept_list">
                    <property name="tableAlias" value="wd"/>
                </include>
            </if>
              ) device
        WHERE EXTRACT(YEAR FROM create_time) = #{year}
    </select>

    <select id="getTerminalCountByDept"
            resultType="com.xh.vdm.statistic.vo.response.terminal.TerminalDeptCountResponse">
        SELECT device.dept_id, count(*), dt.dept_name
        FROM (SELECT dept_id
              FROM "bdm_rdss_device" dd
              WHERE deleted = 0
        <if test="userId != null">
            <include refid="dept_list">
                <property name="tableAlias" value="dd"/>
            </include>
        </if>

              UNION ALL

              SELECT dept_id
              FROM "bdm_rnss_device" rd
              WHERE deleted = 0
        <if test="userId != null">
            <include refid="dept_list">
                <property name="tableAlias" value="rd"/>
            </include>
        </if>

              UNION ALL

              SELECT dept_id
              FROM "bdm_monit_device" md
              WHERE deleted = 0
        <if test="userId != null">
            <include refid="dept_list">
                <property name="tableAlias" value="md"/>
            </include>
        </if>

              UNION ALL

              SELECT dept_id
              FROM "bdm_pnt_device" pd
              WHERE deleted = 0
        <if test="userId != null">
            <include refid="dept_list">
                <property name="tableAlias" value="pd"/>
            </include>
        </if>

              UNION ALL

              SELECT dept_id
              FROM "bdm_wearable_device" wd
              WHERE deleted = 0
        <if test="userId != null">
            <include refid="dept_list">
                <property name="tableAlias" value="wd"/>
            </include>
        </if>
              ) device
        left join blade_dept as dt on device.dept_id = dt.id
        WHERE device.dept_id != 0
        GROUP BY device.dept_id, dt.dept_name
        ORDER BY device.count desc
        limit 10
    </select>

</mapper>
