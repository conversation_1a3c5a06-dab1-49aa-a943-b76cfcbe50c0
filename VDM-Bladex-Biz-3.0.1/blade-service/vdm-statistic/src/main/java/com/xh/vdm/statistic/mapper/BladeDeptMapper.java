package com.xh.vdm.statistic.mapper;

import com.xh.vdm.statistic.entity.BladeDept;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.DeptAndEmail;
import com.xh.vdm.statistic.entity.DeptTree;

import java.util.List;

/**
 * <p>
 * 机构表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-10
 */
public interface BladeDeptMapper extends BaseMapper<BladeDept> {

	/**
	 * 查询企业联系人邮箱信息
	 * @return
	 */
	List<DeptAndEmail> getDeptAndEmail();

	/**
	 * 获取部门树数据，用于后续构建树
	 * @param tenantId
	 * @return
	 */
	List<DeptTree> getDeptTreeData(String tenantId);
}
