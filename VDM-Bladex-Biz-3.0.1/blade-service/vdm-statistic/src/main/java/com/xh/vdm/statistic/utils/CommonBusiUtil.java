package com.xh.vdm.statistic.utils;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 业务工具类
 */
@Component
@Slf4j
public class CommonBusiUtil {


	@Autowired
	private VdmUserInfoUtil userInfoUtil;

	@Resource
	private IDictBizClient dictBizClient;




	/**
	 * 获取本部门及子部门id
	 * @param user
	 * @return
	 * @throws Exception
	 */
	public List<Long> getDeptList(BladeUser user) throws Exception{
		//0.查询本部门、子部门
		if(user == null){
			log.info("获取用户信息为空");
			throw new Exception("用户登录失败或未授权");
		}
		List<Long> list = null;
		try {
			list = userInfoUtil.getChildrenAndSelfDeptId(user);
		}catch (Exception e){
			log.error("查询部门信息失败",e);
			throw new Exception("用户登录失败或未授权");
		}

		return list;
	}

	/**
	 * 查询行业类型
	 * @param vehicleUseType
	 * @return
	 */
	public List<String> getVehicleUseTypes(String vehicleUseType){
		List<String> vehicleUseTypes = new ArrayList<>();
		try{
			R<List<DictBiz>> res = dictBizClient.getDictTreeFlatByCodeAndKeyWithSelf(CommonConstant.DICT_VEHICLE_USE_TYPE, vehicleUseType);
			if(!res.isSuccess()){
				log.error("调用行业类型接口失败");
				return null;
			}

			List<DictBiz> list = res.getData();
			list.forEach(item -> {
				vehicleUseTypes.add(item.getDictKey());
			});
			return vehicleUseTypes;
		}catch (Exception e){
			log.error("查询行业类型失败",e);
			return null;
		}
	}

	/**
	 * 根据key获取字典map
	 * @param key
	 * @return
	 */
	public Map<String,String> getDictMap(String key){
		try{
			R<Map<String,String>> res = dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(key, "-1");
			if(!res.isSuccess()){
				log.error("调用字典接口失败");
				return null;
			}
			return res.getData();
		}catch (Exception e){
			log.error("查询字典接口失败",e);
			return null;
		}
	}

	/**
	 * 根据key和value获取字典map
	 * @param key
	 * @return
	 */
	public Map<String,String> getDictMap(String key,String value){
		try{
			R<Map<String,String>> res = dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(key, value);
			if(!res.isSuccess()){
				log.error("调用字典接口失败");
				return null;
			}
			return res.getData();
		}catch (Exception e){
			log.error("查询字典接口失败",e);
			return null;
		}
	}
}
