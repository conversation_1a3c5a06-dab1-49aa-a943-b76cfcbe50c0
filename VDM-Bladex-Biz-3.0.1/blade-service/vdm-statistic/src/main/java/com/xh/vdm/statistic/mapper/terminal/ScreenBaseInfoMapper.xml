<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.terminal.ScreenBaseInfoMapper">

    <sql id="dept_list">
        <if test="userId != null">
            AND EXISTS (
            SELECT 1
            FROM bdm_user_dept_regulates usr
            WHERE usr.dept_id = ${tableAlias}.dept_id
            AND usr.user_id = #{userId}
            )
        </if>
    </sql>

    <sql id="category_list">
        <if test="keyIntegers != null and keyIntegers.size() gt 0">
            AND ${tableAlias}.category in
                <foreach collection="keyIntegers" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </if>
    </sql>


    <select id="getHighPrecisionCount" resultType="java.lang.Integer">
        SELECT domain
        FROM bdm_rdss_device dd
        WHERE deleted = 0
        <if test="userId != null">
            <include refid="dept_list">
                <property name="tableAlias" value="dd"/>
            </include>
        </if>

        UNION ALL

        SELECT domain
        FROM bdm_rnss_device rd
        WHERE deleted = 0
        <if test="userId != null">
            <include refid="dept_list">
                <property name="tableAlias" value="rd"/>
            </include>
        </if>

        UNION ALL

        SELECT domain
        FROM bdm_monit_device md
        WHERE deleted = 0
        <if test="userId != null">
            <include refid="dept_list">
                <property name="tableAlias" value="md"/>
            </include>
        </if>

        UNION ALL

        SELECT domain
        FROM bdm_pnt_device pd
        WHERE deleted = 0
        <if test="userId != null">
            <include refid="dept_list">
                <property name="tableAlias" value="pd"/>
            </include>
        </if>

        UNION ALL

        SELECT domain
        FROM bdm_wearable_device wd
        WHERE deleted = 0
        <if test="userId != null">
            <include refid="dept_list">
                <property name="tableAlias" value="wd"/>
            </include>
        </if>
    </select>


</mapper>
