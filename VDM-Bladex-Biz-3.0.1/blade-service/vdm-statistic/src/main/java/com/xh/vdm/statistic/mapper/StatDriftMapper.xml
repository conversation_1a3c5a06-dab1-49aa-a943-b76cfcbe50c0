<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatDriftMapper">


    <insert id="insertTempBatch">
        insert into tmp_stat_drift_data (licence_plate, licence_color, drift_data) values
        <foreach collection="list" item="item" open="" close="" separator=",">
            (#{item.licencePlate} , #{item.plateColor}, concat(#{item.continuousDriftCount}, '#' , #{item.totalCount} , '#' , #{item.driftCount} ))
        </foreach>
    </insert>

    <insert id="insertNewData">
        insert into stat_drift_${month} (licence_plate , licence_color, d${day}, update_time)
        select licence_plate , licence_color, drift_data , now() from tmp_stat_drift_data tmp where not exists (
                select * from stat_drift_${month} stat where tmp.licence_plate = stat.licence_plate and tmp.licence_color = stat.licence_color
            );
    </insert>

    <update id="updateExistData">
        update stat_drift_${month} a , tmp_stat_drift_data b
        set a.d${day} = b.drift_data , update_time = now()
        where exists (select licence_plate from tmp_stat_drift_data c where a.licence_plate = c.licence_plate and a.licence_color = c.licence_color)
        and a.licence_plate = b.licence_plate and a.licence_color = b.licence_color

        update stat_drift_${month}
        set d${day} = b.drift_data , update_time = now()
        from stat_drift_${month} a , tmp_stat_drift_data b
        where exists (select licence_plate from tmp_stat_drift_data c where a.licence_plate = c.licence_plate and a.licence_color = c.licence_color)
          and a.licence_plate = b.licence_plate and a.licence_color = b.licence_color
    </update>

    <select id="createLicencePlateTemp">
        create table if not exists tmp_drift_exist_licence_plate
        select licence_plate from tmp_stat_drift_data tmp
        where exists (
                      select * from stat_drift_${month} stat where tmp.licence_plate = stat.licence_plate and tmp.licence_color = stat.licence_color
                  );
    </select>

    <insert id="insertLicencePlateTemp">
        insert into tmp_drift_exist_licence_plate
        select licence_plate from tmp_stat_drift_data tmp
        where exists (
                      select * from stat_drift_${month} stat where tmp.licence_plate = stat.licence_plate and tmp.licence_color = stat.licence_color
                  );
    </insert>

    <select id="getDriftVehicleCountByType" parameterType="com.xh.vdm.statistic.entity.RateParam" resultType="com.xh.vdm.statistic.entity.DriftVehicleCount">
        select bv.trans vehicle_use_type ,count(*) count from stat_drift_${param.month} sdf
            left join bdm_vehicle bv on sdf.licence_plate = bv.licence_plate and sdf.licence_color = bv.licence_color
        where 1 = 1
            <if test="param.deptId != null and param.deptId != ''">
                and bv.dept_id = #{param.deptId,jdbcType=BIGINT}
            </if>
        <if test="param.ownerId != null and param.ownerId != ''">
            and bv.vehicle_owner_id = #{param.ownerId,jdbcType=BIGINT}
        </if>
          and bv.is_check = 1
          and bv.is_del = 0
        group by bv.trans
    </select>

    <select id="getDriftDetailList" parameterType="com.xh.vdm.statistic.entity.DetailParam" resultType="com.xh.vdm.statistic.vo.response.DriftDetailResponse" >
        select sd.name dept_name , bv.licence_plate , bv.licence_color licence_color , bt.sim_id , bt.terminal_id , sdf.drift_count , sdf.date , bt.terminal_model from
            (
                select licence_plate , licence_color, split_part(d01 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-01') date from stat_drift_${param.monthStrict} where d01 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d02 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-02')  date  from stat_drift_${param.monthStrict} where d02 is not null
                UNION
                select  licence_plate , licence_color,split_part(d03 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-03')  date  from stat_drift_${param.monthStrict} where d03 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d04 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-04')  date  from stat_drift_${param.monthStrict} where d04 is not null
                UNION
                select  licence_plate , licence_color,split_part(d05 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-05')  date  from stat_drift_${param.monthStrict} where d05 is not null
                UNION
                select  licence_plate , licence_color,split_part(d06 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-06')  date  from stat_drift_${param.monthStrict} where d06 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d07 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-07')  date  from stat_drift_${param.monthStrict} where d07 is not null
                UNION
                select  licence_plate , licence_color,split_part(d08 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-08')  date  from stat_drift_${param.monthStrict} where d08 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d09 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-09')  date  from stat_drift_${param.monthStrict} where d09 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d10 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-10')  date  from stat_drift_${param.monthStrict} where d10 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d11 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-11')  date  from stat_drift_${param.monthStrict} where d11 is not null
                UNION
                select  licence_plate , licence_color,split_part(d12 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-12')  date  from stat_drift_${param.monthStrict} where d12 is not null
                UNION
                select  licence_plate , licence_color,split_part(d13 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-13')  date  from stat_drift_${param.monthStrict} where d13 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d14 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-14')  date  from stat_drift_${param.monthStrict} where d14 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d15 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-15')  date  from stat_drift_${param.monthStrict} where d15 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d16 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-16')  date  from stat_drift_${param.monthStrict} where d16 is not null
                UNION
                select  licence_plate , licence_color,split_part(d17 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-17')  date  from stat_drift_${param.monthStrict} where d17 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d18 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-18')  date  from stat_drift_${param.monthStrict} where d18 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d19 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-19')  date  from stat_drift_${param.monthStrict} where d19 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d20 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-20')  date  from stat_drift_${param.monthStrict} where d20 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d21 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-21')  date  from stat_drift_${param.monthStrict} where d21 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d22 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-22')  date  from stat_drift_${param.monthStrict} where d22 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d23 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-23')   date from stat_drift_${param.monthStrict} where d23 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d24 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-24')   date from stat_drift_${param.monthStrict} where d24 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d25 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-25')   date from stat_drift_${param.monthStrict} where d25 is not null
                UNION
                select licence_plate , licence_color,split_part(d26 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-26')   date from stat_drift_${param.monthStrict} where d26 is not null
                UNION
                select  licence_plate , licence_color,split_part(d27 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-27')   date from stat_drift_${param.monthStrict} where d27 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d28 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-28')   date from stat_drift_${param.monthStrict} where d28 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d29 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-29')   date from stat_drift_${param.monthStrict} where d29 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d30 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-30')   date from stat_drift_${param.monthStrict} where d30 is not null
                UNION
                select  licence_plate ,licence_color, split_part(d31 , '#' , 1)  drift_count , concat( #{param.month,jdbcType=VARCHAR}  , '-31')   date from stat_drift_${param.monthStrict} where d31 is not null
            ) sdf
                left join bdm_vehicle bv on sdf.licence_plate = bv.licence_plate and sdf.licence_color = bv.licence_color
                left join sys_dept sd on bv.dept_id = sd.id
                left join bdm_terminal bt on bt.id = bv.terminal_id
        where 1 = 1
            <if test="param.deptId != null and param.deptId != ''">
                and bv.dept_id = #{param.deptId,jdbcType=BIGINT}
            </if>
            <if test="param.ownerId != null and param.ownerId != ''">
                and bv.vehicle_owner_id = #{param.ownerId,jdbcType=BIGINT}
            </if>

            <if test="param.licencePlate != null and param.licencePlate != ''">
                and bv.licence_plate = #{param.licencePlate,jdbcType=VARCHAR}
            </if>
        <if test="param.plateColor != null and param.plateColor != ''">
            and bv.licence_color = #{param.plateColor,jdbcType=INTEGER}
        </if>
        and bv.is_check = 1
          and bv.is_del = 0
          and sd.is_del = 0
    </select>

    <select id="getDriffCountByDeptId" resultType="com.xh.vdm.statistic.entity.VehicleCountWithDept">
        select bv.dept_id ,count(*) count from stat_drift_${month} sdf
        left join bdm_vehicle bv on sdf.licence_plate = bv.licence_plate and sdf.licence_color = bv.licence_color
        where 1 = 1
        <if test="deptIds != null and deptIds != ''">
          and bv.dept_id in (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId}
            </foreach>
            )
        </if>
        <if test="ownerId != null and ownerId != ''">
            and bv.vehicle_owner_id = #{ownerId}
        </if>
          and bv.is_del = 0
        and bv.is_check = 1
        group by bv.dept_id
    </select>

    <select id="getGoOnlineCountByGroup" parameterType="string" resultType="com.xh.vdm.statistic.entity.VehicleOwnerAndDeptAndTypeAndCount">
        select bv.dept_id, bv.vehicle_owner_id, bv.trans vehicleUseType , count(distinct bt.vehicle_id) count
        from bdm_terminalonlinerecord bt
            left join bdm_vehicle bv on bt.vehicle_id = bv.id
        where 1 = 1
          and bt.on_line_time like #{month,jdbcType=VARCHAR}
          and bv.net_sign_time > 0
          and bv.is_check = 1
          and bt.is_del = 0 and bv.is_del = 0
        group by bv.dept_id, bv.vehicle_owner_id, bv.trans
    </select>

    <select id="getDriftCountByGroup" parameterType="string" resultType="com.xh.vdm.statistic.entity.VehicleOwnerAndDeptAndTypeAndCount">
        SELECT
            bv.dept_id,
            bv.vehicle_owner_id,
            bv.trans vehicle_use_type,
            count( * ) count
        FROM
            stat_drift_${month} sdf
            LEFT JOIN bdm_vehicle bv ON sdf.licence_plate = bv.licence_plate
            AND sdf.licence_color = bv.licence_color
            AND bv.is_check = 1
            AND bv.is_del = 0
        where bv.dept_id is not null
        GROUP BY
            bv.dept_id, bv.vehicle_owner_id,bv.trans
    </select>

    <select id="getDriffCountByDeptIdDeptOrArea" resultType="com.xh.vdm.statistic.entity.VehicleCountWithDept">
        select
        <if test="deptId != null and deptId != ''">
            bv.dept_id ,
        </if>
        count(*) count from stat_drift_${month} sdf
        left join bdm_vehicle bv on sdf.licence_plate = bv.licence_plate
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and bv.dept_id = #{deptId}
        </if>
        <if test="ownerId != null and ownerId != ''">
            and bv.vehicle_owner_id = #{ownerId}
        </if>
        and bv.is_del = 0
        and bv.vehicle_use_type in (10,11,12,30,31,32)
        <if test="deptId != null and deptId != ''">
            group by bv.dept_id
        </if>
    </select>

    <select id="getDriftCountMonth" resultType="com.xh.vdm.statistic.entity.VehicleAndCount">
        select sd.licence_plate, sd.licence_color,
        (
            <foreach collection="dateList" item="date" separator="+">
                coalesce(split_part(d${date},'#',1)::bigint,0)
            </foreach>
        )count
        from stat_drift_${month} sd
        left join bdm_vehicle bv on bv.licence_plate = sd.licence_plate and bv.licence_color = sd.licence_color::text
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and bv.id in
            <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
            and (
            <if test="request.deptList != null">
                bv.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="request.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="request.vehicleUseType != null ">
            and bv.vehicle_use_type in
            <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.vehicleOwnerId != null">
            and bv.vehicle_owner_id = #{request.vehicleOwnerId}
        </if>
        <if test="request.accessMode != null">
            and bv.access_mode = #{request.accessMode}
        </if>
    </select>

</mapper>
