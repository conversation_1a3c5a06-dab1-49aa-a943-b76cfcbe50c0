<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatNotLocationMapper">

    <select id="getNotLocationCount" resultType="long">
        select count(*)
        from (
                 select distinct snl.licence_plate, snl.licence_color
                 from stat_not_location_${month} snl
                 left join bdm_vehicle bv on bv.licence_plate = snl.licence_plate and bv.licence_color = snl.licence_color
                 where stat_date = #{date}
                <if test="deptList != null">
                    and ( bv.dept_id in (
                        <foreach collection="deptList" item="deptId" separator=",">
                            #{deptId,jdbcType=BIGINT}
                        </foreach>
                        )
                    )
                </if>
             )a
    </select>

    <select id="getNotLocationMileage" resultType="double">
        select sum(mileage)
        from stat_not_location_${month}
        where stat_date = #{date}
        <if test="deptList != null">
            and ( bv.dept_id in (
                <foreach collection="deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            )
        </if>
    </select>
</mapper>
