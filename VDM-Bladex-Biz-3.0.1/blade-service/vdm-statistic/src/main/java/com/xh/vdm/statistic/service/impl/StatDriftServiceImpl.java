package com.xh.vdm.statistic.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.StatDriftMapper;
import com.xh.vdm.statistic.mapper.VehicleMapper;
import com.xh.vdm.statistic.service.ILocationService;
import com.xh.vdm.statistic.service.IStatDriftService;
import com.xh.vdm.statistic.service.IStatTaskLogService;
import com.xh.vdm.statistic.service.IVehicleStatService;
import com.xh.vdm.statistic.utils.DataUtils;
import com.xh.vdm.statistic.utils.DistanceUtils;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.DriftDetailResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <p>
 * 漂移表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Service
@Slf4j
public class StatDriftServiceImpl extends ServiceImpl<StatDriftMapper, StatDrift> implements IStatDriftService {

    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

    @Resource
    private ILocationService locationService;

    @Resource
    private IVehicleStatService vehicleStatService;

    private ExecutorService threadPool = Executors.newFixedThreadPool(10);

    @Resource
    private DataUtils dataUtils;


    @Resource
    private IStatTaskLogService taskLogService;

	@Resource
	private VehicleMapper vehicleMapper;


    @Override
    public boolean locationDriftStat(String day) throws Exception {
        long startTotal = System.currentTimeMillis();
        try{
            //1.校验给定的日期格式
            Date statDate = null;
            try {
                statDate = sdf.parse(day);
            } catch (ParseException e) {
                log.error("[漂移率计算]执行出现异常，日期 " + day + " 格式错误 "  , e);
                throw new Exception("日期格式错误：" + day);
            }

            log.info("[漂移率计算] 日期格式校验完成：日期格式正确");
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[漂移率计算]校验日期格式完成");

            //判断临时表是否存在，不存在，则根据模板表新建；存在，则清空
            dataUtils.checkTableExistAndCreate(StatisticConstants.LOCATION_DRIFT_DATA_TEMP_TABLE, StatisticConstants.LOCATION_DRIFT_DATA_TEMPLATE_TABLE);
            dataUtils.truncateTable(StatisticConstants.LOCATION_DRIFT_DATA_TEMP_TABLE);
            //创建索引
            dataUtils.createIndex(StatisticConstants.LOCATION_DRIFT_DATA_TEMP_TABLE , StatisticConstants.LICENCE_PLATE);

            //2.判断月表是否存在，如果不存在则创建
            dataUtils.checkTableExistAndCreate(StatisticConstants.LOCATION_DRIFT_TABLE + "_"+day.substring(0 , 6) , StatisticConstants.LOCATION_DRIFT_TABLE);
            //创建索引
            dataUtils.createIndex(StatisticConstants.LOCATION_DRIFT_TABLE + "_"+day.substring(0 , 6) , StatisticConstants.LICENCE_PLATE);
            log.info("[漂移率计算] 月表判断完成");
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[漂移率计算]月表校验完成");

            long start1 = System.currentTimeMillis();
            //3.根据指定的日期获取车辆列表
            List<VehicleBase> licencePlatesList = locationService.findUploadLicencePlatesByDay(day);
            log.info("[漂移率计算] 车辆列表获取完成，共有 {} 辆车待处理");
            long end1 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[漂移率计算]获取车辆列表完成，共 "+(licencePlatesList==null?"0":licencePlatesList.size())+" 辆车共耗时："+(end1 - start1)+"ms");

            long start2 = System.currentTimeMillis();
            //4.对每辆车的漂移率进行计算
            List<DriftDataNode> driftDataNodes = new ArrayList<>();
            List<DriftDataNode> driftDataNodesList = Collections.synchronizedList(driftDataNodes);
            CountDownLatch countDownLatch = new CountDownLatch(licencePlatesList.size());
            for(VehicleBase vehicle : licencePlatesList){
                String licencePlate = vehicle.getLicencePlate();
                long plateColor = vehicle.getLicenceColor();
                threadPool.submit(() -> {
                    try{
                        //4.1 查询车辆轨迹点列表
                        List<LocationKudu> list = locationService.findUploadLocationPointListByDay(day , licencePlate, (int)plateColor);
                        //4.2 统计车辆漂移信息
                        DriftDataNode dataNode = locationDriftStatByPositionList(list);
                        if(dataNode != null){
                            driftDataNodesList.add(dataNode);
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                    }finally {
                        countDownLatch.countDown();
                    }
                });
            }
            countDownLatch.await();
            log.info("[漂移率计算] 漂移率统计完成[数据待入库]");
            long end2 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[漂移率计算]漂移率统计完成，共耗时："+(end2 - start2)+"ms");

            //如果没有漂移数据，则不再继续往下执行
            if(driftDataNodesList == null || driftDataNodesList.size() < 1){
                return true;
            }

            long start3 = System.currentTimeMillis();
            //5.执行漂移数据入库（通过临时表的方式对列进行更新，提高效率）

            //5.1 数据入临时表
            baseMapper.insertTempBatch(driftDataNodesList);
            long end3 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[漂移率计算]数据入临时表完成，共耗时："+(end3 - start3)+" ms");

            long start4 = System.currentTimeMillis();
            //5.2 对临时表中的数据进行插入和更新操作
            //5.2.1 对已经存在的数据执行更新操作
            //执行数据的更新
            baseMapper.updateExistData(day.substring(0,6) , day.substring(6,8));
            long end4 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[漂移率计算]主表数据更新完成，共耗时："+(end4 - start4)+"ms");

            long start5 = System.currentTimeMillis();
            //5.2.2 对新增的数据执行插入操作
            baseMapper.insertNewData(day.substring(0,6) , day.substring(6,8));
            log.info("[漂移率计算] 执行数据的新增 和 更新 操作完成");
            long end5 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[漂移率计算]向主表中插入数据完成，共耗时："+(end5 - start5)+"ms");

            long start6 = System.currentTimeMillis();
            //5.3 清空临时表
            dataUtils.truncateTable(StatisticConstants.LOCATION_DRIFT_DATA_TEMP_TABLE);
            log.info("[漂移率计算] 临时表处理完成");
            long end6 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[漂移率计算]清空临时表完成，共耗时："+(end6 - start6)+"ms");
        }catch (Exception e){
            e.printStackTrace();
            log.error("[漂移率计算]执行数据的录入失败");
            throw e;
        }
        log.info("[漂移率计算] 轨迹完整率统计整体完成");
        long endTotal = System.currentTimeMillis();
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[漂移率计算]漂移率统计完成，共耗时："+(endTotal - startTotal)+"ms");
        return true;
    }

    @Override
    public DriftDataNode locationDriftStatByPositionList(List<LocationKudu> list) {
        //1.对轨迹序列按照定位时间进行排序
        Collections.sort(list , (o1 , o2) -> {
            return (int) (o1.getTime() - o2.getTime());
        });

        //当前点与上个点之间的速度
        double speed = 0;
        //上上个点与上个点之间的速度
        double speedOld = 0;
        //当前定位点
        LocationKudu locationNow = null;
        //上个定位点
        LocationKudu locationOld = null;
        //漂移点数量
        int driftCount = 0;
        double distance = 0 ;
        double time = 0;
        boolean isDrift = false;
        int continuouseDrift = 0;
        for(int i = 0 ; i < list.size() ; i++){
            locationNow = list.get(i);
            if(i > 0){
                //从第二个点开始计算
                //两点间的距离，单位为米
                distance = DistanceUtils.wgs84Distance(locationNow.getLongitude() , locationNow.getLatitude() , locationOld.getLongitude() , locationOld.getLatitude());
                //两点间的时间差，单位为秒
                time = locationNow.getTime() - locationOld.getTime();
                //如果两个点的定位时间相同，就认为 速度为0 ，减少误判
                if (time == 0) {
                    speed = 0;
                } else{
                    speed = distance / time * 3.6;
                }
                //两点间的速度，单位为 km/h
                if(i >= 1){
                    if(speed > 160 && speedOld > 160){
                        driftCount++;
                        if(!isDrift){
                            isDrift = true;
                            continuouseDrift ++;
                        }
                    }else{
                        isDrift = false;
                    }
                }
            }
            locationOld = locationNow;
            speedOld = speed;
        }
        if(driftCount > 0){
            //只有当发生漂移了，才记录漂移
            DriftDataNode driftDataNode = new DriftDataNode();
            driftDataNode.setDriftCount(driftCount);
            driftDataNode.setTotalCount(list.size());
			// TODO 车牌号请通过target_id和target_type查询
//            driftDataNode.setLicencePlate(licencePlate);
//            driftDataNode.setPlateColor((int)plateColor);
            //记录连续漂移次数
            driftDataNode.setContinuousDriftCount(continuouseDrift);
            return driftDataNode;
        }
        return null;
    }

    @Override
    public List<DriftAndGoOnlineCountNode> findDriftAndOnlineCount(RateParam param) {
        //1.查询车辆漂移数量
        List<DriftVehicleCount> driftCountList = baseMapper.getDriftVehicleCountByType(param);
        //2.查询车辆上线数量
        RateRequest request = new RateRequest();
        BeanUtils.copyProperties(param , request);
        request.setMonth(param.getMonth().substring(0,4)+"-"+param.getMonth().substring(4,6)+"%");
        List<VehicleCountAndType> goOnlineCountList = vehicleStatService.findGoOnlineVehicleCountByType(request);
        //3.组装数据
        List<DriftAndGoOnlineCountNode> nodeList = new ArrayList<>();
        for(VehicleCountAndType type : goOnlineCountList){
            DriftAndGoOnlineCountNode node = new DriftAndGoOnlineCountNode();
            node.setGoOnlineCount(type.getCount());
            node.setVehicleUseType(type.getVehicleUseType());
            for(DriftVehicleCount count : driftCountList){
                if(count.getVehicleUseType() == type.getVehicleUseType()){
                    node.setDriftCount(count.getCount());
                }
            }
            nodeList.add(node);
        }
        return nodeList;
    }

    @Override
    public IPage<DriftDetailResponse> findDriftDetailList(DetailParam param) {
        Page page = new Page();
        page.setCurrent(param.getCurrent());
        page.setSize(param.getSize());
        return baseMapper.getDriftDetailList(page , param);
    }

    @Override
    public List<VehicleCountWithDept> findDriffCountByDeptId(String month, List<Long> deptIds , Long ownerId) {
        month = month.replace("-" , "");
        return baseMapper.getDriffCountByDeptId(month , deptIds , ownerId);
    }

	@Override
	public VehicleCountWithDept findDriftCountByDeptIdDeptOrArea(String month, Long deptId , Long ownerId) {
		month = month.replace("-" , "");
		return baseMapper.getDriffCountByDeptIdDeptOrArea(month , deptId , ownerId);
	}

	@Override
	public List<DateAndCount> findDriftCountByDate(List<String> dateList, List<Long> deptIds, Long userId) throws Exception {
		//1.拼装部门id
		StringBuffer deptIdsStr = new StringBuffer();
		String deptIdsList = "";
		for(Long id : deptIds){
			deptIdsStr.append(id).append(",");
		}
		if(deptIdsStr.length() > 0){
			deptIdsList = deptIdsStr.substring(0,deptIdsStr.length() - 1);
		}

		//2.根据天数拼装sql
		StringBuffer sqls = new StringBuffer();
		int index = 0;
		for(String date : dateList){
			if(index != 0){
				sqls.append(" union ");
			}
			String dateStr = date.substring(6,8);
			String monthStr = date.substring(0,6);
			String template = "select count(*) count ,  '"+date+"' date  " +
				"from stat_drift_"+monthStr+" sd, bdm_vehicle bv " +
				"where sd.licence_plate = bv.licence_plate and sd.licence_color = bv.licence_color " +
				"and (bv.dept_id in ("+deptIdsList+") or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = "+userId+")) and d"+dateStr+" is not null ";
			sqls.append(template);
			index ++;
		}

		//3.执行sql
		if(!StringUtils.isEmpty(sqls)){
			//List<Map<String, Object>> list = SqlRunner.db().selectList(sqls.toString());
			List<Map<String,Object>> list = vehicleMapper.dynamicQuery(sqls.toString());
			if(list == null || list.size() < 1){
				return null;
			}
			List<DateAndCount> resList = new ArrayList<>();
			for(Map<String,Object> m : list){
				//DateAndCount dc = (DateAndCount) BeanUtil.mapToBean(m, DateAndCount.class);
				DateAndCount dc = new DateAndCount();
				dc.setCount(Long.parseLong(m.get("count").toString()));
				dc.setStatDate(m.get("date").toString());
				resList.add(dc);
			}
			return resList;
		}else{
			return null;
		}
	}

	@Override
	public List<VehicleAndCount> findDriftCount(CommonBaseCrossMonthRequest request) {

		//1.每个月份都查询一次
		List<VehicleAndCount> listAll = new ArrayList<>();
		List<DateListAndMonth> dmList = request.getDmList();
		dmList.forEach(item -> {
			String month = item.getMonth();
			List<String> dateList = item.getDateList();
			List<VehicleAndCount> list = baseMapper.getDriftCountMonth(request, month ,dateList);
			listAll.addAll(list);
		});

		//2.整理数据
		Map<String,List<VehicleAndCount>> map = new HashMap<>();
		listAll.forEach(item -> {
			String licencePlate = item.getLicencePlate();
			String licenceColor = item.getLicenceColor();
			String key = licencePlate + "~" + licenceColor;
			List<VehicleAndCount> tmpList = map.get(key);
			if(tmpList == null){
				tmpList = new ArrayList<>();
			}
			tmpList.add(item);
			map.put(key, tmpList);
		});
		List<VehicleAndCount> resList = new ArrayList<>();
		map.forEach((k,v) -> {
			String licencePlate = k.split("~")[0];
			String licenceColor = k.split("~")[1];
			AtomicLong totalCount = new AtomicLong();
			v.forEach(item -> {
				totalCount.addAndGet(item.getCount());
			});
			VehicleAndCount vc = new VehicleAndCount();
			vc.setLicencePlate(licencePlate);
			vc.setLicenceColor(licenceColor);
			vc.setCount(totalCount.get());
			resList.add(vc);
		});

		return resList;
	}
}
