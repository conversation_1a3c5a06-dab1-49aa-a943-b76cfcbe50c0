package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.CacheVehicleOnlineRate;
import com.xh.vdm.statistic.vo.request.VehicleOnlineRateRequest;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 车辆在线率DB缓存表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
public interface ICacheVehicleOnlineRateService extends IService<CacheVehicleOnlineRate> {

    /**
     * @description: 统计车辆在线率，并入库
     * startTime、endTime：精确到秒的时间戳
     * @author: zhouxw
     * @date: 2023-02-40 15:27:09
     * @param: [request, req]
     * @return: com.xh.vdm.statistic.vo.ObjectRestResponse<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>>
     **/
    void statisticsVehicleOnlineRate(Long startTime, Long endTime) throws Exception;


    /**
     * @description: 查询车辆在线率
     * @author: zhouxw
     * @date: 2023-02-40 16:20:53
     * @param: [request, req]
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    IPage<Map> queryVehicleOnlineRateCache(IPage page, VehicleOnlineRateRequest req) throws Exception;
}
