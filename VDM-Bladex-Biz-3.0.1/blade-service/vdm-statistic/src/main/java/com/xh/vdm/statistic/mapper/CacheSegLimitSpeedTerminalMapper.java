package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.CacheSegLimitSpeedTerminalResponse;
import com.xh.vdm.statistic.vo.request.SegLimitSpeedTerminalRequest;
import com.xh.vdm.statistic.vo.response.SegLimitSpeedTerminalResponse;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 车辆地图超速
 *
 * <AUTHOR>
 * @since 2022-09-14
 */
public interface CacheSegLimitSpeedTerminalMapper extends BaseMapper<CacheSegLimitSpeedTerminalResponse> {

    /**
     * @description: 查询车辆地图超速信息
     * @author: zhouxw
     * @date: 2023-02-40 17:46:00
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    List<SegLimitSpeedTerminalResponse> getList(@Param("param") SegLimitSpeedTerminalRequest segLimitSpeedTerminalRequest);


    /**
     * @description: 分页查询车辆地图超速信息
     * @author: zhouxw
     * @date: 2023-02-40 17:46:00
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    IPage<SegLimitSpeedTerminalResponse> getList(IPage<SegLimitSpeedTerminalResponse> page, @Param("param") SegLimitSpeedTerminalRequest segLimitSpeedTerminalRequest);


    /**
     * @description: 删除指定时间段内的数据
     * @author: zhouxw
     * @date: 2023-02-44 16:10:25
     * @param: [startDate, endDate]
     * @return: long
     **/
    Long deleteByDateDuration(Date startDate, Date endDate);

}
