package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class CommonBaseRequest {

	//车辆id列表
	@JsonProperty("vehicle_id_list")
	private List<Long> vehicleIdList;

	//开始时间
	@JsonProperty("start_time")
	private Long startTime;

	//结束时间
	@JsonProperty("end_time")
	private Long endTime;

	//行业类型
	@JsonProperty("vehicle_use_type")
	private String vehicleUseType;

	//车辆归属
	@JsonProperty("vehicle_owner")
	private Long vehicleOwnerId;

	//车辆接入方式
	@JsonProperty("access_mode")
	private String accessMode;


	//租户id
	private String tenantId;
	//登录用户id
	private Long userId;

	//所有车组，包含子车组
	private List<Long> deptList;
	//行业类型
	private List<String> professionList;

}
