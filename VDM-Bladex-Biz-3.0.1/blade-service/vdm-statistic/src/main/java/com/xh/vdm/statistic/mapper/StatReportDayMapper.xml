<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatReportDayMapper">

    <select id="getReportDayPage" resultType="com.xh.vdm.statistic.vo.response.ReportInfoResponse">
        select a.dept_id, bd.dept_name, a.date date,
            a.proxy_file_url summary_url, b.proxy_file_url alarm_url, c.proxy_file_url running_url
        from stat_report_day as a
        inner join stat_report_day as b on a.dept_id = b.dept_id and a.date = b.date and a.type = 'report' and b.type = 'alarm'
        inner join stat_report_day as c on b.dept_id = c.dept_id and b.date = c.date and b.type = 'alarm' and c.type = 'running'
        inner join blade_dept as bd on a.dept_id = bd.id
        where 1 = 1
        <if test="cd.deptIds != null and cd.deptIds != ''">
            and a.dept_id in
            <foreach collection="cd.deptIds" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="cd.date != null and cd.date != ''">
            and a.date = #{cd.date}
        </if>
    </select>
</mapper>
