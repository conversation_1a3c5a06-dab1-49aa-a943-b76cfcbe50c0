package com.xh.vdm.statistic.service;

import com.xh.vdm.statistic.entity.BdmTerminalonlinerecord;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 车辆上下线记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 */
public interface IBdmTerminalonlinerecordService extends IService<BdmTerminalonlinerecord> {

	/**
	 * 查询在指定时间段内上过线的记录
	 * 在指定时间段内，下线时间为空，或者下线时间大于开始时间，而且上线时间小于等于今日最大时间
	 * 暂不支持跨月
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws Exception
	 */
	List<BdmTerminalonlinerecord> findOnlineRecord(long startTime, long endTime) throws Exception;
}
