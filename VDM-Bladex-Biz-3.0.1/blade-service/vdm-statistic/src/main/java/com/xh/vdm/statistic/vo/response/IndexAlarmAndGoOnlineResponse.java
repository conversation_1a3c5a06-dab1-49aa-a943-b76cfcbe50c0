package com.xh.vdm.statistic.vo.response;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.xh.vdm.statistic.entity.DateAndComplete;
import com.xh.vdm.statistic.entity.DateAndDrift;
import com.xh.vdm.statistic.entity.DateAndLocationQuality;
import com.xh.vdm.statistic.entity.DateAndRate;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/7/23 17:14
 */
@Data
public class IndexAlarmAndGoOnlineResponse {

	//上线情况
	List<GoOnlineRateBaseResponse> goOnlineList;

	//报警情况
	List<AlarmAndHandleCountResponse> alarmList;

	//完整率
	private List<DateAndRate> completeRate;
	//合格率
	private List<DateAndRate> qualifiedRate;
	//漂移率
	private List<DateAndRate> driftRate;

}
