package com.xh.vdm.statistic.service.terminal.impl;

import com.xh.vdm.statistic.mapper.terminal.ScreenDynamicMapper;
import com.xh.vdm.statistic.service.terminal.IScreenDynamicService;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description: 终端统计
 */
@Service
public class ScreenDynamicServiceImpl implements IScreenDynamicService {
	@Resource
	private ScreenDynamicMapper screenDynamicMapper;

	@Override
	public long count(Long userId) {
		if (AuthUtil.isAdministrator()) {
			userId = null;
		}
		return screenDynamicMapper.count(userId);
	}
}
