package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.vo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 车辆离线报表请求类
 * <AUTHOR>
 * @date 2021/10/26 13:37
 */
@ApiModel(value = "车辆离线报表请求类")
@Data
public class VehicleOfflineRequest extends PageParam {


    @ApiModelProperty(value = "查询开始时间")
    @JsonProperty("start_time")
    private Long startTime;

    @ApiModelProperty(value = "查询结束时间")
    @JsonProperty("end_time")
    private Long endTime;

    @ApiModelProperty(value = "车辆归属")
    @JsonProperty("vehicle_owner")
    private Long vehicleOwnerId;

    @ApiModelProperty(value = "行业类型")
    @JsonProperty("vehicle_use_type")
    private Long vehicleUseType;

    @ApiModelProperty(value = "车辆接入方式")
    @JsonProperty("access_mode")
    private Integer accessMode;

	@ApiModelProperty(value = "离线时长")
	@JsonProperty("offline_days")
	private Integer offlineDays;

	//车辆id列表
	@JsonProperty("vehicle_id_list")
	private List<Long> vehicleIdList;


	//租户id
	private String tenantId;

	//登录账户的userId
	private Long userId;

    //所有车组，包含子车组
    private List<Long> deptList;
	//行业类型
    private List<String> professionList;

}
