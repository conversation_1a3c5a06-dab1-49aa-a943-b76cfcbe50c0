<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatisticsMapper">

    <select id="getSegLimitSpeedTerminalCount" resultType="long" parameterType="com.xh.vdm.statistic.vo.request.SegLimitSpeedTerminalRequest">
        SELECT count(*) FROM
        bdm_security bsy
        left join bdm_vehicle bv on bsy.licence_plate = bv.licence_plate and bsy.licence_color = bv.licence_color
        where 1=1 and bsy.alarm_type in (1,13,63) and bsy.alarm_end_time > bsy.alarm_time
        <if test= "segLimitSpeedTerminalRequest.startTime != null">
            AND bsy.alarm_time &gt;= #{segLimitSpeedTerminalRequest.startTime}
        </if>
        <if test= "segLimitSpeedTerminalRequest.endTime != null">
            AND bsy.alarm_time &lt;= #{segLimitSpeedTerminalRequest.endTime}
        </if>
        <if test= "segLimitSpeedTerminalRequest.deptList != null and segLimitSpeedTerminalRequest.deptList.size() != 0">
         AND bsy.dept_id IN
         <foreach collection="segLimitSpeedTerminalRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="segLimitSpeedTerminalRequest.professionList != null and segLimitSpeedTerminalRequest.professionList.size() > 0">
            AND bv.vehicle_use_type IN
            <foreach collection="segLimitSpeedTerminalRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        <if test= "segLimitSpeedTerminalRequest.licencePlate != null">
        AND bsy.licence_plate LIKE CONCAT('%',#{segLimitSpeedTerminalRequest.licencePlate},'%')
        </if>
        <if test= "segLimitSpeedTerminalRequest.licenceColor != null">
        AND bsy.licence_color = #{segLimitSpeedTerminalRequest.licenceColor}
        </if>
        <if test= "segLimitSpeedTerminalRequest.limitSpeed != null">
        AND bsy.limit_speed &gt;=#{segLimitSpeedTerminalRequest.limitSpeed}
        </if>
        <if test= "segLimitSpeedTerminalRequest.durationTime != null">
        AND ( CASE WHEN bsy.alarm_end_time=to_timestamp(0) THEN 0 ELSE (coalesce(bsy.alarm_end_time,0) - coalesce(bsy.alarm_time,0)) END ) &gt;=#{segLimitSpeedTerminalRequest.durationTime}
        </if>
        <if test= "segLimitSpeedTerminalRequest.vehicleOwnerId != null">
        AND bv.vehicle_owner_id = #{segLimitSpeedTerminalRequest.vehicleOwnerId}
        </if>
        <if test= "segLimitSpeedTerminalRequest.accessMode != null">
        AND bv.access_mode = #{segLimitSpeedTerminalRequest.accessMode}
        </if>

    </select>

    <select id="getDept" resultType="com.xh.vdm.statistic.entity.BladeDept">
        select *
        from blade_dept
        where 1 = 1
        <if test="deptIds != null and deptIds.size() > 0">
            and id in (
                <foreach collection="deptIds" item="id" separator=",">
                    #{id}
                </foreach>
            )
        </if>
        <if test="deptIds == null or deptIds.size() == 0">
            and ancestors like concat('%',#{deptId},'%')
        </if>
    </select>


    <select id="getChildrenDept" resultType="com.xh.vdm.statistic.entity.BladeDept">
        select *
        from blade_dept
        where 1 = 1
        and ancestors like concat('%',#{deptId},'%')
        and is_deleted = 0
    </select>


    <select id="getSegLimitSpeedTerminal" resultType="com.xh.vdm.statistic.vo.response.SegLimitSpeedTerminalResponse">
        SELECT
        (select coalesce((select pd.name from sys_dept pd where pd.id=d.pid),d.name) from sys_dept d where d.id = bs.dept_id) as enterprise,
        (select d.name from sys_dept d where d.id = bs.dept_id) as dept_name,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), "非营运车辆" ) AS vehicle_owner,
        bs.licence_plate,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 3 AND bd.pid != 0
        AND bd.dict_code = bv.licence_color) AS licence_color,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 2 AND bd.pid != 0
        AND bd.dict_code = bv.vehicle_use_type) AS vehicle_model,
        to_timestamp(bs.alarm_time) as start_alarm_time,
        ( CASE WHEN bs.alarm_end_time=to_timestamp(0) THEN to_timestamp(bs.alarm_time) ELSE FROM_UNIXTIME(bs.alarm_end_time) END ) as end_alarm_time,
        ( CASE WHEN bs.alarm_end_time=to_timestamp(0) THEN 0 ELSE (coalesce(bs.alarm_end_time,0) - coalesce(bs.alarm_time,0)) END ) as duration_time,
        1 as over_speed_count,
        bs.speed,
        bs.limit_speed speed_limit,
        bs.longitude start_longitude,
        bs.latitude start_latitude,
        bs.alarm_address start_alarm_address,
        bs.big_speed max_speed,
        bs.low_speed min_speed,
        bs.avg_speed average_speed,
        bs.mileage,
        bs.longitude_end end_longitude,
        bs.latitude_end end_latitude,
        bs.alarm_end_address end_alarm_address,
        coalesce(bs.driver_name,"未知") as driver_info,
        bs.access_mode
        FROM
        <if test="current != null and size != null">
            (select bsy.*, bd.dict_name access_mode from bdm_security bsy
            left join bdm_vehicle bv
            on bsy.licence_plate = bv.licence_plate and bsy.licence_color = bv.licence_color
            left join bam_dict bd on bv.access_mode = bd.dict_code and bd.dict_type = 30 and bd.is_del = 0
            where 1=1 and bsy.alarm_type in (1,13,63) and bsy.alarm_end_time>bsy.alarm_time
            <if test= "segLimitSpeedTerminalRequest.vehicleOwnerId != null">
                AND bv.vehicle_owner_id = #{segLimitSpeedTerminalRequest.vehicleOwnerId}
            </if>
            <if test= "segLimitSpeedTerminalRequest.accessMode != null">
                AND bv.access_mode = #{segLimitSpeedTerminalRequest.accessMode}
            </if>
            <if test= "segLimitSpeedTerminalRequest.startTime != null">
                AND bsy.alarm_time &gt;= #{segLimitSpeedTerminalRequest.startTime}
            </if>
            <if test= "segLimitSpeedTerminalRequest.endTime != null">
                AND bsy.alarm_time &lt;= #{segLimitSpeedTerminalRequest.endTime}
            </if>
            <if test= "segLimitSpeedTerminalRequest.deptList != null and segLimitSpeedTerminalRequest.deptList.size() != 0">
                AND bsy.dept_id IN
                <foreach collection="segLimitSpeedTerminalRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="segLimitSpeedTerminalRequest.professionList != null and segLimitSpeedTerminalRequest.professionList.size() > 0">
                AND bv.vehicle_use_type IN
                <foreach collection="segLimitSpeedTerminalRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                    #{profession}
                </foreach>
            </if>
            <if test= "segLimitSpeedTerminalRequest.licencePlate != null">
                AND bsy.licence_plate LIKE CONCAT("%",#{segLimitSpeedTerminalRequest.licencePlate},"%")
            </if>
            <if test= "segLimitSpeedTerminalRequest.licenceColor != null">
                AND bsy.licence_color = #{segLimitSpeedTerminalRequest.licenceColor}
            </if>
            <if test= "segLimitSpeedTerminalRequest.limitSpeed != null">
                AND bsy.limit_speed &gt;=#{segLimitSpeedTerminalRequest.limitSpeed}
            </if>
            <if test= "segLimitSpeedTerminalRequest.durationTime != null">
                AND ( CASE WHEN bsy.alarm_end_time=to_timestamp(0) THEN 0 ELSE (coalesce(bsy.alarm_end_time,0) - coalesce(bsy.alarm_time,0)) END ) &gt;=#{segLimitSpeedTerminalRequest.durationTime}
            </if>
            order by bsy.alarm_time desc limit ${size} offset ${(current - 1) * size},
            ) bs left join
        </if>
        bdm_vehicle bv
        on bs.licence_plate = bv.licence_plate and bs.licence_color = bv.licence_color
        WHERE
        1=1
        order by bs.alarm_time desc
    </select>


    <select id="getSegLimitSpeedTerminalWithTime" resultType="com.xh.vdm.statistic.entity.CacheSegLimitSpeedTerminalResponse">
        SELECT
        (select coalesce((select pd.name from sys_dept pd where pd.id=d.pid),d.name) from sys_dept d where d.id = bs.dept_id) as enterprise,
        bs.dept_id,
        (select d.name from sys_dept d where d.id = bs.dept_id) as dept_name,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), "非营运车辆" ) AS vehicle_owner,
        bv.vehicle_owner_id,
        bs.licence_plate,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 3 AND bd.pid != 0
        AND bd.dict_code = bv.licence_color) AS licence_color,
        bv.licence_color licence_color_code,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 2 AND bd.pid != 0
        AND bd.dict_code = bv.vehicle_use_type) AS vehicle_model,
        bv.vehicle_use_type vehicle_model_code,
        to_timestamp(bs.alarm_time) as start_alarm_time,
        ( CASE WHEN bs.alarm_end_time=to_timestamp(0) THEN to_timestamp(bs.alarm_time) ELSE FROM_UNIXTIME(bs.alarm_end_time) END ) as end_alarm_time,
        ( CASE WHEN bs.alarm_end_time=to_timestamp(0) THEN 0 ELSE (coalesce(bs.alarm_end_time,0) - coalesce(bs.alarm_time,0)) END ) as duration_time,
        1 as over_speed_count,
        bs.speed,
        bs.limit_speed speed_limit,
        bs.longitude start_longitude,
        bs.latitude start_latitude,
        bs.alarm_address start_alarm_address,
        bs.mileage,
        bs.longitude_end end_longitude,
        bs.latitude_end end_latitude,
        bs.alarm_end_address end_alarm_address,
        coalesce(bs.driver_name,"未知") as driver_info,
        bs.access_mode,
        bs.access_mode_code
        FROM
        (select bsy.*, bd.dict_name access_mode, bv.access_mode access_mode_code from bdm_security bsy
        left join bdm_vehicle bv
        on bsy.licence_plate = bv.licence_plate and bsy.licence_color = bv.licence_color
        left join bam_dict bd on bv.access_mode = bd.dict_code and bd.dict_type = 30 and bd.is_del = 0
        where 1=1 and bsy.alarm_type in (1,13,63) and bsy.alarm_end_time>bsy.alarm_time
        <if test= "startTime != null">
            AND bsy.alarm_time &gt;= #{startTime}
        </if>
        <if test= "endTime != null">
            AND bsy.alarm_time &lt;= #{endTime}
        </if>
        ) bs left join
        bdm_vehicle bv
        on bs.licence_plate = bv.licence_plate and bs.licence_color = bv.licence_color
    </select>


    <resultMap id="getNightDrivingMap" type="com.xh.vdm.statistic.vo.response.NightDrivingResponse">
        <result property = "deptName" column = "dept_name"/>
        <result property = "enterprise"  column = "enterprise"/>
        <result property = "licencePlate"  column = "licence_plate"/>
        <result property = "licenceColor"  column = "licence_color"/>
        <result property = "vehicleModel" column = "vehicle_model"/>
        <result property = "vehicleOwner" column = "vehicle_owner"/>
        <result property = "startAlarmTime" column = "alarm_time"/>
        <result property = "endAlarmTime" column = "alarm_end_time"/>
        <result property = "durationTime" column = "duration_time"/>
        <result property = "address" column = "alarm_address"/>
        <result property = "accessMode" column = "access_mode"/>
    </resultMap>

    <select id="getNightDriving" resultMap="getNightDrivingMap">
        SELECT
        (select coalesce((select pd.name from sys_dept pd where pd.id=d.pid),d.name) from sys_dept d where d.id = bs.dept_id) as enterprise,
        (select d.name from sys_dept d where d.id = bs.dept_id) as dept_name,
        bs.licence_plate,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 3 AND bd.pid != 0
        AND bd.dict_code = bv.licence_color) AS licence_color,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 2 AND bd.pid != 0
        AND bd.dict_code = bv.vehicle_use_type) AS vehicle_model,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        to_timestamp(bs.alarm_time) as alarm_time,
        ( CASE WHEN bs.alarm_end_time=to_timestamp(0) THEN to_timestamp(bs.alarm_time) ELSE FROM_UNIXTIME(bs.alarm_end_time) END ) as alarm_end_time,
        ( CASE WHEN bs.alarm_end_time=to_timestamp(0) THEN 0 ELSE (coalesce(bs.alarm_end_time,0) - coalesce(bs.alarm_time,0)) END ) as duration_time,
        bs.speed,
        bs.limit_speed,
        bs.longitude,
        bs.latitude,
        bs.alarm_address,
        bd.dict_name access_mode
        FROM
        ( select bsy.* from bdm_security bsy
        left join bdm_vehicle bv
        on bsy.licence_plate = bv.licence_plate and bsy.licence_color = bv.licence_color
        where bsy.alarm_type = 103 and bsy.alarm_end_time>=bsy.alarm_time
        <if test= "nightDrivingRequest.vehicleOwnerId != null and nightDrivingRequest.vehicleOwnerId == 0">
            AND (bv.vehicle_owner_id = 0 or bv.vehicle_owner_id is null)
        </if>
        <if test= "nightDrivingRequest.vehicleOwnerId != null and nightDrivingRequest.vehicleOwnerId != 0">
            AND bv.vehicle_owner_id = #{nightDrivingRequest.vehicleOwnerId}
        </if>
        <if test= "nightDrivingRequest.accessMode != null">
            AND bv.access_mode = #{nightDrivingRequest.accessMode}
        </if>
        <if test= "nightDrivingRequest.startTime != null">
            AND bsy.alarm_time &gt;= #{nightDrivingRequest.startTime}
        </if>
        <if test= "nightDrivingRequest.endTime != null">
            AND bsy.alarm_time &lt;= #{nightDrivingRequest.endTime}
        </if>
        <if test= "nightDrivingRequest.deptList != null and nightDrivingRequest.deptList.size() != 0">
            and bsy.dept_id IN
            <foreach collection="nightDrivingRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="nightDrivingRequest.professionList != null and nightDrivingRequest.professionList.size() > 0">
            and bv.vehicle_use_type IN
            <foreach collection="nightDrivingRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        <if test= "nightDrivingRequest.licencePlate != null">
            AND bsy.licence_plate LIKE CONCAT('%',#{nightDrivingRequest.licencePlate},'%')
        </if>
        <if test= "nightDrivingRequest.licenceColor != null">
            AND bsy.licence_color = #{nightDrivingRequest.licenceColor}
        </if>
        order by bsy.alarm_time desc limit ${size} offset ${(current - 1) * size}
        ) bs left join
        bdm_vehicle bv
        on bs.licence_plate = bv.licence_plate and bs.licence_color = bv.licence_color
        left join bam_dict bd on bv.access_mode = bd.dict_code and bd.dict_type = 30 and bd.is_del = 0
        WHERE
        1=1  order by bs.alarm_time desc
    </select>

    <select id="getNightDrivingCount" parameterType="com.xh.vdm.statistic.vo.request.NightDrivingRequest" resultType="long">
        SELECT
        count(*)
        FROM
        ( select bsy.* from bdm_security bsy
        left join bdm_vehicle bv
        on bsy.licence_plate = bv.licence_plate and bsy.licence_color = bv.licence_color
        where bsy.alarm_type = 103 and bsy.alarm_end_time>=bsy.alarm_time
        <if test= "nightDrivingRequest.vehicleOwnerId != null and nightDrivingRequest.vehicleOwnerId == 0">
            AND (bv.vehicle_owner_id = 0 or bv.vehicle_owner_id is null)
        </if>
        <if test= "nightDrivingRequest.vehicleOwnerId != null and nightDrivingRequest.vehicleOwnerId != 0">
            AND bv.vehicle_owner_id = #{nightDrivingRequest.vehicleOwnerId}
        </if>
        <if test= "nightDrivingRequest.accessMode != null">
            AND bv.access_mode = #{nightDrivingRequest.accessMode}
        </if>
        <if test= "nightDrivingRequest.startTime != null">
            AND bsy.alarm_time &gt;= #{nightDrivingRequest.startTime}
        </if>
        <if test= "nightDrivingRequest.endTime != null">
            AND bsy.alarm_time &lt;= #{nightDrivingRequest.endTime}
        </if>
        <if test= "nightDrivingRequest.deptList != null and nightDrivingRequest.deptList.size() != 0">
            and bsy.dept_id IN
            <foreach collection="nightDrivingRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="nightDrivingRequest.professionList != null and nightDrivingRequest.professionList.size() > 0">
            and bsy.vehicle_use_type IN
            <foreach collection="nightDrivingRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        <if test= "nightDrivingRequest.licencePlate != null">
            AND bsy.licence_plate LIKE CONCAT('%',#{nightDrivingRequest.licencePlate},'%')
        </if>
        <if test= "nightDrivingRequest.licenceColor != null">
            AND bsy.licence_color = #{nightDrivingRequest.licenceColor}
        </if>
        ) bs

    </select>


    <select id="queryDayMileage" resultType="hashmap">
        SELECT
        (select coalesce((select pd.name from sys_dept pd where pd.id=d.pid),d.name) from sys_dept d where d.id = bvo.dept_id) as enterprise,
        (select d.name from sys_dept d where d.id = bvo.dept_id) as dept_name,
        (SELECT
        d.name
        FROM
        sys_dept d
        WHERE
        d.id = bvo.dept_id) AS enterprise,
        bvo.licence_plate,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 3 AND bd.pid != 0
        AND bd.dict_code = bvo.licence_color) AS licence_color,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 2 AND bd.pid != 0
        AND bd.dict_code = bvo.vehicle_use_type) AS vehicle_model,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bvo.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        bvt.statistic_date,
        bvt.mileage,
        coalesce(bvt.total_mileage,0) as total_mileage,
        bd.dict_name access_mode
        FROM
        bdm_vehicle bvo
        LEFT JOIN
        (SELECT
        bv.id,
        bv.licence_plate,
        bv.licence_color,
        GROUP_CONCAT(v.statistic_date) AS statistic_date,
        GROUP_CONCAT(v.mileage) AS mileage,
        SUM(v.mileage) AS total_mileage
        FROM
        bdm_vehicle bv, (select cast(`bdm_route`.`start_time` as date) AS `statistic_date`,`bdm_route`.`licence_plate` AS `licence_plate`,`bdm_route`.`licence_color`,sum(`bdm_route`.`mileage`) AS `mileage` from `bdm_route` where
        start_time&gt;=to_timestamp(#{dayMileageRequest.startTime}) and
        start_time&lt;=to_timestamp(#{dayMileageRequest.endTime})
        <if test= 'dayMileageRequest.deptList != null and dayMileageRequest.deptList.size() != 0'>
            and licence_plate = any (select bvd.licence_plate from bdm_vehicle bvd where bvd.dept_id IN
            <foreach collection="dayMileageRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
            )
        </if>
        <if test='dayMileageRequest.professionList != null and dayMileageRequest.professionList.size() > 0'>
            and licence_plate = any (select bvd.licence_plate from bdm_vehicle bvd where bvd.vehicle_use_type IN
            <foreach collection="dayMileageRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
            )
        </if>
        <if test= 'dayMileageRequest.licencePlate != null'>
            AND licence_plate LIKE CONCAT('%',#{dayMileageRequest.licencePlate},'%')
        </if>
        <if test= 'dayMileageRequest.licenceColor != null'>
            AND licence_color = #{dayMileageRequest.licenceColor}
        </if>
        group by `bdm_route`.`licence_plate`,`bdm_route`.`licence_color` , cast(`bdm_route`.`start_time` as date)) v
        WHERE
        bv.licence_plate = v.licence_plate and bv.licence_color = v.licence_color
        GROUP BY bv.id , bv.licence_plate, bv.licence_color) bvt ON bvo.id = bvt.id
        left join bam_dict bd on bvo.access_mode = bd.dict_code and bd.dict_type = 30 and bd.is_del = 0
        WHERE  1=1
        <if test= 'dayMileageRequest.deptList != null and dayMileageRequest.deptList.size() != 0'>
            and bvo.dept_id IN
            <foreach collection="dayMileageRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test='dayMileageRequest.professionList != null and dayMileageRequest.professionList.size() > 0'>
            and bvo.vehicle_use_type IN
            <foreach collection="dayMileageRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        <if test= 'dayMileageRequest.licencePlate != null'>
            AND bvo.licence_plate LIKE CONCAT('%',#{dayMileageRequest.licencePlate},'%')
        </if>
        <if test= 'dayMileageRequest.licenceColor != null'>
            AND bvo.licence_color = #{dayMileageRequest.licenceColor}
        </if>
        <if test= "dayMileageRequest.vehicleOwnerId != null">
            AND bvo.vehicle_owner_id = #{dayMileageRequest.vehicleOwnerId}
        </if>
        <if test= "dayMileageRequest.accessMode != null">
            AND bvo.access_mode = #{dayMileageRequest.accessMode}
        </if>
    </select>


    <select id="getFatigueDrivingCount" parameterType="com.xh.vdm.statistic.vo.request.FatigueDrivingRequest" resultType="long">
        SELECT count(1)
        FROM
        (select bsy.* from bdm_security bsy
        left join bdm_vehicle bv on bsy.licence_plate = bv.licence_plate and bsy.licence_color = bv.licence_color
        where 1=1 and bsy.alarm_type in (2,14,53,102) and bsy.alarm_end_time>bsy.alarm_time
        <if test= "fatigueDrivingRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{fatigueDrivingRequest.vehicleOwnerId}
        </if>
        <if test= "fatigueDrivingRequest.accessMode != null">
            AND bv.access_mode = #{fatigueDrivingRequest.accessMode}
        </if>
        <if test= "fatigueDrivingRequest.deptList != null and fatigueDrivingRequest.deptList.size() != 0">
            AND bv.dept_id IN
            <foreach collection="fatigueDrivingRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="fatigueDrivingRequest.professionList != null and fatigueDrivingRequest.professionList.size() > 0">
            AND bv.vehicle_use_type IN
            <foreach collection="fatigueDrivingRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        <if test= 'fatigueDrivingRequest.startTime != null'>
            AND bsy.alarm_time &gt;= #{fatigueDrivingRequest.startTime}
        </if>
        <if test= 'fatigueDrivingRequest.endTime != null'>
            AND bsy.alarm_time &lt;= #{fatigueDrivingRequest.endTime}
        </if>
        <if test= 'fatigueDrivingRequest.deptList !=null and fatigueDrivingRequest.deptList.size() != 0'>
            AND bsy.dept_id IN
            <foreach collection="fatigueDrivingRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test= 'fatigueDrivingRequest.licencePlate != null'>
            AND bsy.licence_plate LIKE CONCAT('%',#{fatigueDrivingRequest.licencePlate},'%')
        </if>
        <if test= 'fatigueDrivingRequest.licenceColor != null'>
            AND bsy.licence_color = #{fatigueDrivingRequest.licenceColor}
        </if>
        ) bs,
        bdm_vehicle bv
        WHERE
        bs.licence_plate = bv.licence_plate and bs.licence_color = bv.licence_color
        <if test= "fatigueDrivingRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{fatigueDrivingRequest.vehicleOwnerId}
        </if>
        <if test= "fatigueDrivingRequest.accessMode != null">
            AND bv.access_mode = #{fatigueDrivingRequest.accessMode}
        </if>
        <if test= "fatigueDrivingRequest.deptList != null and fatigueDrivingRequest.deptList.size() != 0">
            AND bv.dept_id IN
            <foreach collection="fatigueDrivingRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="fatigueDrivingRequest.professionList != null and fatigueDrivingRequest.professionList.size() > 0">
            AND bv.vehicle_use_type IN
            <foreach collection="fatigueDrivingRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
    </select>


    <resultMap id="getFatigueDrivingMap" type="com.xh.vdm.statistic.vo.response.FatigueDrivingResponse">
        <result property = "deptName" column = "dept_name"/>
        <result property = "enterprise"  column = "enterprise"/>
        <result property = "licencePlate"  column = "licence_plate"/>
        <result property = "licenceColor"  column = "licence_color"/>
        <result property = "vehicleModel" column = "vehicle_model"/>
        <result property = "vehicleOwner" column = "vehicle_owner"/>
        <result property = "startAlarmTime" column = "alarm_time"/>
        <result property = "endAlarmTime" column = "alarm_end_time"/>
        <result property = "durationTime" column = "duration_time"/>
        <result property = "alarmType" column = "alarm_type"/>
        <result property = "speed" column = "speed"/>
        <result property = "startAddress" column = "alarm_address"/>
        <result property = "endAddress" column = "alarm_end_address"/>
        <result property = "accessMode" column = "access_mode"/>
    </resultMap>

    <select id="getFatigueDriving" resultMap="getFatigueDrivingMap">
        SELECT
        (select coalesce((select pd.name from sys_dept pd where pd.id=d.pid),d.name) from sys_dept d where d.id = bs.dept_id) as enterprise,
        (select d.name from sys_dept d where d.id = bs.dept_id) as dept_name,
        bs.licence_plate,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 3 AND bd.pid != 0
        AND bd.dict_code = bv.licence_color) AS licence_color,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 2 AND bd.pid != 0
        AND bd.dict_code = bv.vehicle_use_type) AS vehicle_model,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        (select concat(ba.alarm_name,'(',ba.alarm_origin,')') from bdm_ararmtype ba where ba.alarm_id = bs.alarm_type) AS alarm_type,
        to_timestamp(bs.alarm_time) as alarm_time,
        ( CASE WHEN bs.alarm_end_time=to_timestamp(0) THEN to_timestamp(bs.alarm_time) ELSE FROM_UNIXTIME(bs.alarm_end_time) END ) as alarm_end_time,
        ( CASE WHEN bs.alarm_end_time=to_timestamp(0) THEN 0 ELSE (coalesce(bs.alarm_end_time,0) - coalesce(bs.alarm_time,0)) END ) as duration_time,
        bs.speed,
        bs.limit_speed,
        bs.longitude,
        bs.latitude,
        bs.alarm_end_address,
        bs.alarm_address,
        bs.access_mode
        FROM
        (select bsy.*, bd.dict_name access_mode from bdm_security bsy
        left join bdm_vehicle bv on bsy.licence_plate = bv.licence_plate and bsy.licence_color = bv.licence_color
        left join bam_dict bd on bv.access_mode = bd.dict_code and bd.dict_type = 30 and bd.is_del = 0
        where 1=1 and bsy.alarm_type in (2,14,53,102) and bsy.alarm_end_time>bsy.alarm_time
        <if test= "fatigueDrivingRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{fatigueDrivingRequest.vehicleOwnerId}
        </if>
        <if test= "fatigueDrivingRequest.accessMode != null">
            AND bv.access_mode = #{fatigueDrivingRequest.accessMode}
        </if>
        <if test= "fatigueDrivingRequest.deptList != null and fatigueDrivingRequest.deptList.size() != 0">
            AND bv.dept_id IN
            <foreach collection="fatigueDrivingRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="fatigueDrivingRequest.professionList != null and fatigueDrivingRequest.professionList.size() > 0">
            AND bv.vehicle_use_type IN
            <foreach collection="fatigueDrivingRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        <if test= 'fatigueDrivingRequest.startTime != null'>
            AND bsy.alarm_time &gt;= #{fatigueDrivingRequest.startTime}
        </if>
        <if test= 'fatigueDrivingRequest.endTime != null'>
            AND bsy.alarm_time &lt;= #{fatigueDrivingRequest.endTime}
        </if>
        <if test= 'fatigueDrivingRequest.deptList !=null and fatigueDrivingRequest.deptList.size() != 0'>
            AND bsy.dept_id IN
            <foreach collection="fatigueDrivingRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test= 'fatigueDrivingRequest.licencePlate != null'>
            AND bsy.licence_plate LIKE CONCAT('%',#{fatigueDrivingRequest.licencePlate},'%')
        </if>
        <if test= 'fatigueDrivingRequest.licenceColor != null'>
            AND bsy.licence_color = #{fatigueDrivingRequest.licenceColor}
        </if>
        order by bsy.alarm_time desc limit ${size} offset ${(current - 1) * size}
        ) bs,
        bdm_vehicle bv
        WHERE
        bs.licence_plate = bv.licence_plate and bs.licence_color = bv.licence_color
        <if test= "fatigueDrivingRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{fatigueDrivingRequest.vehicleOwnerId}
        </if>
        <if test= "fatigueDrivingRequest.accessMode != null">
            AND bv.access_mode = #{fatigueDrivingRequest.accessMode}
        </if>
        <if test= "fatigueDrivingRequest.deptList != null and fatigueDrivingRequest.deptList.size() != 0">
            AND bv.dept_id IN
            <foreach collection="fatigueDrivingRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="fatigueDrivingRequest.professionList != null and fatigueDrivingRequest.professionList.size() > 0">
            AND bv.vehicle_use_type IN
            <foreach collection="fatigueDrivingRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        order by bs.alarm_time desc
    </select>

    <resultMap id="getSegLimitSpeedMapMap" type="com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse">
        <result property = "deptName" column = "dept_name"/>
        <result property = "enterprise"  column = "enterprise"/>
        <result property = "licencePlate"  column = "licence_plate"/>
        <result property = "licenceColor"  column = "licence_color"/>
        <result property = "vehicleModel" column = "vehicle_model"/>
        <result property = "vehicleOwner" column = "vehicle_owner"/>
        <result property = "startAlarmTime" column = "alarm_time"/>
        <result property = "durationTime" column = "duration_time"/>
        <result property = "maxSpeed" column = "speed"/>
        <result property = "speedLimit" column = "limit_speed"/>
        <result property = "longitude" column = "longitude"/>
        <result property = "latitude" column = "latitude"/>
        <result property = "startAlarmAddress" column = "alarm_address"/>
        <result property = "accessMode" column = "access_mode"/>
    </resultMap>

    <select id="getSegLimitSpeedMap" resultMap="getSegLimitSpeedMapMap">
        SELECT
        (select coalesce((select pd.name from sys_dept pd where pd.id=d.pid),d.name) from sys_dept d where d.id = bs.dept_id) as enterprise,
        (select d.name from sys_dept d where d.id = bs.dept_id) as dept_name,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        bs.licence_plate,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 3 AND bd.pid != 0
        AND bd.dict_code = bv.licence_color) AS licence_color,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 2 AND bd.pid != 0
        AND bd.dict_code = bv.vehicle_use_type) AS vehicle_model,
        to_timestamp(bs.alarm_time) as alarm_time,
        ( CASE WHEN bs.alarm_end_time=to_timestamp(0) THEN 0 ELSE (coalesce(bs.alarm_end_time,0) - coalesce(bs.alarm_time,0)) END ) as duration_time,
        bs.speed,
        bs.limit_speed,
        bs.longitude,
        bs.latitude,
        bs.alarm_address,
        bs.access_mode
        FROM
        (select bsy.*,bd.dict_name access_mode from bdm_security bsy
        left join bdm_vehicle bv on bsy.licence_plate = bv.licence_plate and bsy.licence_color = bv.licence_color
        left join bam_dict bd on bv.access_mode = bd.dict_code and bd.dict_type = 30 and bd.is_del = 0
        where 1=1 and bsy.alarm_type = 100  and bsy.alarm_end_time>bsy.alarm_time and bsy.mileage >= 1
        <if test= "segLimitSpeedMapRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{segLimitSpeedMapRequest.vehicleOwnerId}
        </if>
        <if test= "segLimitSpeedMapRequest.accessMode != null">
            AND bv.access_mode = #{segLimitSpeedMapRequest.accessMode}
        </if>
        <if test= "segLimitSpeedMapRequest.deptList != null and segLimitSpeedMapRequest.deptList.size() != 0">
            AND bv.dept_id IN
            <foreach collection="segLimitSpeedMapRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="segLimitSpeedMapRequest.professionList != null and segLimitSpeedMapRequest.professionList.size() > 0">
            AND bv.vehicle_use_type IN
            <foreach collection="segLimitSpeedMapRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        <if test= 'segLimitSpeedMapRequest.startTime != null'>
            AND bsy.alarm_time &gt;= #{segLimitSpeedMapRequest.startTime}
        </if>
        <if test= 'segLimitSpeedMapRequest.endTime != null'>
            AND bsy.alarm_time &lt;= #{segLimitSpeedMapRequest.endTime}
        </if>
        <if test= 'segLimitSpeedMapRequest.deptList != null and segLimitSpeedMapRequest.deptList.size() != 0'>
            AND bsy.dept_id IN
            <foreach collection="segLimitSpeedMapRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test= 'segLimitSpeedMapRequest.licencePlate != null'>
            AND bsy.licence_plate LIKE CONCAT('%',#{segLimitSpeedMapRequest.licencePlate},'%')
        </if>
        <if test= 'segLimitSpeedMapRequest.licenceColor != null'>
            AND bsy.licence_color = #{segLimitSpeedMapRequest.licenceColor}
        </if>
        <if test= 'segLimitSpeedMapRequest.limitSpeed != null'>
            AND bsy.limit_speed &gt;=#{segLimitSpeedMapRequest.limitSpeed}
        </if>
        <if test= 'segLimitSpeedMapRequest.durationTime != null'>
            AND ( CASE WHEN bsy.alarm_end_time=to_timestamp(0) THEN 0 ELSE (coalesce(bsy.alarm_end_time,0) - coalesce(bsy.alarm_time,0)) END ) &gt;=#{segLimitSpeedMapRequest.durationTime}
        </if>
        order by bsy.alarm_time desc limit ${size} offset ${(current - 1) * size}
        ) bs left join
        bdm_vehicle bv
        on bs.licence_plate = bv.licence_plate and bs.licence_color = bv.licence_color
        WHERE
        1=1
        <if test= "segLimitSpeedMapRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{segLimitSpeedMapRequest.vehicleOwnerId}
        </if>
        <if test= "segLimitSpeedMapRequest.accessMode != null">
            AND bv.access_mode = #{segLimitSpeedMapRequest.accessMode}
        </if>
        <if test= "segLimitSpeedMapRequest.deptList != null and segLimitSpeedMapRequest.deptList.size() != 0">
            AND bv.dept_id IN
            <foreach collection="segLimitSpeedMapRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="segLimitSpeedMapRequest.professionList != null and segLimitSpeedMapRequest.professionList.size() > 0">
            AND bv.vehicle_use_type IN
            <foreach collection="segLimitSpeedMapRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        order by bs.alarm_time desc
    </select>



    <resultMap id="getSegLimitSpeedMapMapWithTime" type="com.xh.vdm.statistic.entity.CacheSegLimitSpeedMapResponse">
        <result property = "deptName" column = "dept_name"/>
        <result property = "enterprise"  column = "enterprise"/>
        <result property = "licencePlate"  column = "licence_plate"/>
        <result property = "licenceColor"  column = "licence_color"/>
        <result property = "vehicleModel" column = "vehicle_model"/>
        <result property = "vehicleOwner" column = "vehicle_owner"/>
        <result property = "startAlarmTime" column = "alarm_time"/>
        <result property = "durationTime" column = "duration_time"/>
        <result property = "maxSpeed" column = "speed"/>
        <result property = "speedLimit" column = "limit_speed"/>
        <result property = "longitude" column = "longitude"/>
        <result property = "latitude" column = "latitude"/>
        <result property = "startAlarmAddress" column = "alarm_address"/>
        <result property = "accessMode" column = "access_mode"/>
        <result property = "deptId" column = "dept_id"/>
        <result property = "licenceColorCode"  column = "licence_color_code"/>
        <result property = "vehicleModelCode"  column = "vehicle_model_code"/>
        <result property = "vehicleOwnerId"  column = "vehicle_owner_id"/>
        <result property = "accessModeCode"  column = "access_mode_code"/>
    </resultMap>
    <select id="getSegLimitSpeedMapWithTime" resultMap="getSegLimitSpeedMapMapWithTime">
        SELECT
        (select coalesce((select pd.name from sys_dept pd where pd.id=d.pid),d.name) from sys_dept d where d.id = bs.dept_id) as enterprise,
        (select d.name from sys_dept d where d.id = bs.dept_id) as dept_name,
        bs.dept_id,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        bv.vehicle_owner_id,
        bs.licence_plate,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 3 AND bd.pid != 0
        AND bd.dict_code = bv.licence_color) AS licence_color,
        bv.licence_color licence_color_code,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 2 AND bd.pid != 0
        AND bd.dict_code = bv.vehicle_use_type) AS vehicle_model,
        bv.vehicle_use_type vehicle_model_code,
        to_timestamp(bs.alarm_time) as alarm_time,
        ( CASE WHEN bs.alarm_end_time=to_timestamp(0) THEN 0 ELSE (coalesce(bs.alarm_end_time,0) - coalesce(bs.alarm_time,0)) END ) as duration_time,
        bs.speed,
        bs.limit_speed,
        bs.longitude,
        bs.latitude,
        bs.alarm_address,
        bs.access_mode,
        bs.access_mode_code
        FROM
        (select bsy.*,bd.dict_name access_mode, bv.access_mode access_mode_code from bdm_security bsy
        left join bdm_vehicle bv on bsy.licence_plate = bv.licence_plate and bsy.licence_color = bv.licence_color
        left join bam_dict bd on bv.access_mode = bd.dict_code and bd.dict_type = 30 and bd.is_del = 0
        where 1=1 and bsy.alarm_type = 100  and bsy.alarm_end_time>bsy.alarm_time and bsy.mileage >= 1
        <if test= 'startTime != null'>
            AND bsy.alarm_time &gt;= #{startTime}
        </if>
        <if test= 'endTime != null'>
            AND bsy.alarm_time &lt;= #{endTime}
        </if>
        ) bs left join
        bdm_vehicle bv
        on bs.licence_plate = bv.licence_plate and bs.licence_color = bv.licence_color
        WHERE
        1=1
    </select>



    <select id="getSegLimitSpeedMapCount" resultType="long">
        SELECT count(*)
        FROM
        (select bsy.* from bdm_security bsy
        left join bdm_vehicle bv on bsy.licence_plate = bv.licence_plate and bsy.licence_color = bv.licence_color
        where 1=1 and bsy.alarm_type = 100  and bsy.alarm_end_time>bsy.alarm_time and bsy.mileage >= 1
        <if test= "segLimitSpeedMapRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{segLimitSpeedMapRequest.vehicleOwnerId}
        </if>
        <if test= "segLimitSpeedMapRequest.accessMode != null">
            AND bv.access_mode = #{segLimitSpeedMapRequest.accessMode}
        </if>
        <if test= "segLimitSpeedMapRequest.deptList != null and segLimitSpeedMapRequest.deptList.size() != 0">
            AND bv.dept_id IN
            <foreach collection="segLimitSpeedMapRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="segLimitSpeedMapRequest.professionList != null and segLimitSpeedMapRequest.professionList.size() > 0">
            AND bv.vehicle_use_type IN
            <foreach collection="segLimitSpeedMapRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        <if test= 'segLimitSpeedMapRequest.startTime != null'>
            AND bsy.alarm_time &gt;= #{segLimitSpeedMapRequest.startTime}
        </if>
        <if test= 'segLimitSpeedMapRequest.endTime != null'>
            AND bsy.alarm_time &lt;= #{segLimitSpeedMapRequest.endTime}
        </if>
        <if test= 'segLimitSpeedMapRequest.deptList != null and segLimitSpeedMapRequest.deptList.size() != 0'>
            AND bsy.dept_id IN
            <foreach collection="segLimitSpeedMapRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test= 'segLimitSpeedMapRequest.licencePlate != null'>
            AND bsy.licence_plate LIKE CONCAT('%',#{segLimitSpeedMapRequest.licencePlate},'%')
        </if>
        <if test= 'segLimitSpeedMapRequest.licenceColor != null'>
            AND bsy.licence_color = #{segLimitSpeedMapRequest.licenceColor}
        </if>
        <if test= 'segLimitSpeedMapRequest.limitSpeed != null'>
            AND bsy.limit_speed &gt;=#{segLimitSpeedMapRequest.limitSpeed}
        </if>
        <if test= 'segLimitSpeedMapRequest.durationTime != null'>
            AND ( CASE WHEN bsy.alarm_end_time=to_timestamp(0) THEN 0 ELSE (coalesce(bsy.alarm_end_time,0) - coalesce(bsy.alarm_time,0)) END ) &gt;=#{segLimitSpeedMapRequest.durationTime}
        </if>
        ) bs left join
        bdm_vehicle bv
        on bs.licence_plate = bv.licence_plate and bs.licence_color = bv.licence_color
        WHERE
        1=1
        <if test= "segLimitSpeedMapRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{segLimitSpeedMapRequest.vehicleOwnerId}
        </if>
        <if test= "segLimitSpeedMapRequest.accessMode != null">
            AND bv.access_mode = #{segLimitSpeedMapRequest.accessMode}
        </if>
        <if test= "segLimitSpeedMapRequest.deptList != null and segLimitSpeedMapRequest.deptList.size() != 0">
            AND bv.dept_id IN
            <foreach collection="segLimitSpeedMapRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="segLimitSpeedMapRequest.professionList != null and segLimitSpeedMapRequest.professionList.size() > 0">
            AND bv.vehicle_use_type IN
            <foreach collection="segLimitSpeedMapRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
    </select>

    <resultMap id="vehicleOperationMap" type="com.xh.vdm.statistic.vo.response.VehicleOperationResponse">
        <result property = "vehicleId" column = "vehicle_id"/>
        <result property = "licencePlate" column = "licence_plate"/>
        <result property = "deptName"  column = "dept_name"/>
        <result property = "deptId" column = "dept_id"/>
        <result property = "licenceColor"  column = "licence_color"/>
        <result property = "licenceColorCode"  column = "licence_color_code"/>
        <result property = "vehicleUseType" column = "vehicle_use_type"/>
        <result property = "vehicleUseTypeCode" column = "vehicle_use_type_code"/>
        <result property = "vehicleOwner" column = "vehicle_owner"/>
        <result property = "vehicleOwnerId" column = "vehicle_owner_id"/>
        <result property = "onOff" column = "te_state"/>
        <result property = "track" column = "vehicle_track"/>
        <result property = "lastOnlineTime" column = "on_line_time"/>
        <result property = "speed" column = "speed"/>
        <result property = "longitude" column = "longitude"/>
        <result property = "latitude" column = "latitude"/>
        <result property = "locAddr" column = "loc_addr"/>
        <result property = "accessMode" column = "access_mode"/>
        <result property = "accessModeCode" column = "access_mode_code"/>
    </resultMap>

    <select id="vehicleOperation" resultMap="vehicleOperationMap">
        SELECT
        bv.id vehicle_id,
        bv.licence_plate,
        bv.dept_id,
        bv.licence_color licence_color_code,
        bv.vehicle_use_type vehicle_use_type_code,
        bv.access_mode access_mode_code,
        bv.vehicle_owner_id ,
        case when bvs.te_state=1 then '正常' else '未在线' end te_state,
        '正常' as vehicle_track,
        bvs.speed,
        bvs.longitude,
        bvs.latitude,
        bvs.loc_addr
        FROM
        bdm_vehicle bv
        LEFT JOIN bdm_terminal bt ON bv.terminal_id = bt.id
        AND bt.un_bind_flag = 0
        LEFT JOIN bdm_vehicle_state bvs ON bvs.phone = bt.phone  where 1=1
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and bv.id in
            <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
            and (
            <if test="request.deptList != null">
                bv.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="request.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="request.vehicleUseType != null ">
            and bv.vehicle_use_type in
            <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.vehicleOwnerId != null">
            and bv.vehicle_owner_id = #{request.vehicleOwnerId}
        </if>
        <if test="request.accessMode != null">
            and bv.access_mode = #{request.accessMode}
        </if>
        order by dept_id asc
    </select>



    <select id="vehicleOperationWithoutLastOnlineTime" resultMap="vehicleOperationMap">
        SELECT
        bv.licence_plate,
        (select coalesce((select pd.name from sys_dept pd where pd.id=d.pid),d.name) from sys_dept d where d.id = bv.dept_id) as enterprise,
        (select d.name from sys_dept d where d.id = bv.dept_id) as dept_name,
        (select dict_name from bam_dict bd where bd.dict_type=3 and bd.pid !=0 and bd.dict_code=bv.licence_color) as vehicle_color,
        (select dict_name from bam_dict bd where bd.dict_type=2 and bd.pid !=0 and bd.dict_code=bv.vehicle_use_type) as vehicle_model,
        (SELECT dict_name FROM bam_dict bd WHERE bd.dict_type = 30 AND bd.pid != 0 AND bd.dict_code = bv.access_mode ) AS access_mode,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        case when bvs.te_state=1 then '正常' else '未在线' end te_state,
        '正常' as vehicle_track,
        -- (select max(on_line_time) from bdm_terminalonlinerecord bt where bt.vehicle_id=bv.id) as on_line_time,
        bvs.speed,
        bvs.longitude,
        bvs.latitude,
        bvs.loc_addr
        FROM
        bdm_vehicle bv
        LEFT JOIN bdm_terminal bt ON bv.terminal_id = bt.id
        AND bt.un_bind_flag = 0
        LEFT JOIN bdm_vehicle_state bvs ON bvs.phone = bt.phone  where 1=1
        <if test= ' vehicleOperationRequest.deptList != null and vehicleOperationRequest.deptList.size() != 0'>
            and bv.dept_id IN
            <foreach collection="vehicleOperationRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test='vehicleOperationRequest.professionList != null and vehicleOperationRequest.professionList.size() > 0'>
            and bv.vehicle_use_type IN
            <foreach collection="vehicleOperationRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        <if test= 'vehicleOperationRequest.licencePlate != null'>
            AND bv.licence_plate LIKE CONCAT('%',#{vehicleOperationRequest.licencePlate},'%')
        </if>
        <if test= 'vehicleOperationRequest.licenceColor != null'>
            AND bv.licence_color = #{vehicleOperationRequest.licenceColor}
        </if>
        <if test= "vehicleOperationRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{vehicleOperationRequest.vehicleOwnerId}
        </if>
        <if test= "vehicleOperationRequest.accessMode != null">
            AND bv.access_mode = #{vehicleOperationRequest.accessMode}
        </if>
        order by dept_id asc
    </select>


    <select id="getLastOnlineTime" resultType="com.xh.vdm.statistic.entity.LastOnlineTimeEntity">
        SELECT bv.licence_plate, bv.licence_color, max( on_line_time ) lastOnlineTime FROM bdm_terminalonlinerecord bto
        right join bdm_vehicle bv on bto.vehicle_id = bv.id
        where bv.licence_plate in (
            <foreach collection="licencePlateList" item="licencePlate" separator=",">
                #{licencePlate}
            </foreach>
            )
        group by bv.licence_plate, bv.licence_color;

    </select>



    <select id="rateStatistics" resultType="hashmap">
        SELECT
        bd.dict_value access_mode,
        (select d.dept_name from blade_dept d where d.id = bv.dept_id) as dept_name,
        bv.licence_plate,
        (SELECT
        dict_value
        FROM
        blade_dict_biz bd
        WHERE
        bd.code = 'licence_color' AND bd.dict_key > 0 and bd.tenant_id = #{onlineRateRequest.tenantId}
        AND bd.dict_key = bv.licence_color) AS licence_color,
        (SELECT
        dict_value
        FROM
        blade_dict_biz bd
        WHERE
        bd.code = '2' AND bd.dict_key > 0  and bd.tenant_id = #{onlineRateRequest.tenantId}
        AND bd.dict_key = bv.vehicle_use_type) AS vehicle_model,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        <if test= 'onlineRateRequest.dateList != null'>
            <foreach collection="onlineRateRequest.dateList" item="id" index="index" open="" close="" separator=",">
                CASE WHEN (select count(1) from bdm_route br where br.licence_plate = bv.licence_plate and br.licence_color = bv.licence_color AND br.start_time &gt;= date_format( '${id}', '%Y-%m-%d' ) AND br.start_time &lt; DATE_ADD( date_format( '${id}', '%Y-%m-%d' ), INTERVAL 1 DAY ))&gt;=1
                THEN
                (SELECT
                SUM(extract(epoch from IF(coalesce(off_line_time, NOW()) > DATE_ADD(date_format('${id}','%Y-%m-%d'),INTERVAL 1 DAY),
                DATE_ADD(date_format('${id}','%Y-%m-%d'),INTERVAL 1 DAY),
                coalesce(off_line_time, NOW()))) - extract(epoch from IF(on_line_time &lt; date_format('${id}','%Y-%m-%d'),
                date_format('${id}','%Y-%m-%d'),
                on_line_time)))
                FROM
                bdm_terminalonlinerecord_${onlineRateRequest.yearMonthList.get(index)} bt
                WHERE
                bt.vehicle_id = bv.id
                and (bt.on_line_time>current_date or bt.off_line_time is not null)
                AND ((off_line_time &gt;= date_format('${id}','%Y-%m-%d')
                AND off_line_time &lt; DATE_ADD(date_format('${id}','%Y-%m-%d'),INTERVAL 1 DAY))
                OR (on_line_time &gt;= date_format('${id}','%Y-%m-%d')
                AND on_line_time &lt; DATE_ADD(date_format('${id}','%Y-%m-%d'),INTERVAL 1 DAY))
                OR (on_line_time &lt; date_format('${id}','%Y-%m-%d')
                AND (off_line_time &gt; DATE_ADD(date_format('${id}','%Y-%m-%d'),INTERVAL 1 DAY)
                OR off_line_time IS NULL))) group by bt.vehicle_id )
                ELSE 0 END
                as `${id}`
            </foreach>
        </if>
        FROM
        bdm_vehicle bv
        left join blade_dict_biz bd on bv.access_mode = bd.dict_key and bd.code = 'access_mode'  and bd.tenant_id = #{onlineRateRequest.tenantId}
        where 1=1


        <if test="onlineRateRequest.vehicleIdList == null">
            and ( bv.dept_id in (
            <foreach collection="onlineRateRequest.deptList" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
            <if test="onlineRateRequest.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{onlineRateRequest.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="onlineRateRequest.vehicleIdList != null">
            and bv.id in
            <foreach collection="onlineRateRequest.vehicleIdList" separator="," item="vehicleId" open="(" close=")">
                #{vehicleId}
            </foreach>
        </if>


        <if test='onlineRateRequest.professionList != null and onlineRateRequest.professionList.size() > 0'>
            and bv.vehicle_use_type IN
            <foreach collection="onlineRateRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>

        <if test= "onlineRateRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{onlineRateRequest.vehicleOwnerId}
        </if>
        <if test= "onlineRateRequest.accessMode != null">
            AND bv.access_mode = #{onlineRateRequest.accessMode}
        </if>
    </select>


    <select id="rateStatisticsWithTime" resultType="hashmap">
        SELECT
        (select d.id from blade_dept d where d.id = bv.dept_id) as deptId,
        (select d.dept_name from blade_dept d where d.id = bv.dept_id) as deptName,
        bv.licence_plate licencePlate,
        bv.licence_color licenceColorCode,
        (SELECT
        dict_value
        FROM
        blade_dict_biz bd
        WHERE
        bd.code = 'licence_color' AND bd.dict_key > 0 and bd.tenant_id = (select tenant_id from blade_dept d where d.id = bv.dept_id )
        AND bd.dict_key = bv.licence_color) AS licenceColor,
        (SELECT
        dict_key
        FROM
        blade_dict_biz bd
        WHERE
        bd.code = 2 AND bd.dict_key > 0 and bd.tenant_id = (select tenant_id from blade_dept d where d.id = bv.dept_id )
        AND bd.dict_key = bv.vehicle_use_type) AS vehicleModelCode,
        (SELECT
        dict_value
        FROM
        blade_dict_biz bd
        WHERE
        bd.code = 2 AND bd.dict_key > 0 and bd.tenant_id = (select tenant_id from blade_dept d where d.id = bv.dept_id )
        AND bd.dict_key = bv.vehicle_use_type) AS vehicleModel,
        bv.vehicle_owner_id vehicleOwnerId,
        bv.id vehicleId,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicleOwner,
        bv.access_mode accessModeCode,
        bd.dict_value accessMode,

        <if test= 'dateList != null'>
            <foreach collection="dateList" item="id" index="index" open="" close="" separator=",">
                CASE WHEN (select count(1) from bdm_route br where br.licence_plate = bv.licence_plate and br.licence_color = bv.licence_color AND br.start_time &gt;= date_format( '${id}', '%Y-%m-%d' ) AND br.start_time &lt; DATE_ADD( date_format( '${id}', '%Y-%m-%d' ), INTERVAL 1 DAY ))&gt;=1
                THEN
                (SELECT
                SUM(extract(epoch from IF(coalesce(off_line_time, NOW()) > DATE_ADD(date_format('${id}','%Y-%m-%d'),INTERVAL 1 DAY),
                DATE_ADD(date_format('${id}','%Y-%m-%d'),INTERVAL 1 DAY),
                coalesce(off_line_time, NOW()))) - extract(epoch from IF(on_line_time &lt; date_format('${id}','%Y-%m-%d'),
                date_format('${id}','%Y-%m-%d'),
                on_line_time)))
                FROM
                bdm_terminalonlinerecord_${yearMonthList.get(index)} bt
                WHERE
                bt.vehicle_id = bv.id
                and (bt.on_line_time>current_date or bt.off_line_time is not null)
                AND ((off_line_time &gt;= date_format('${id}','%Y-%m-%d')
                AND off_line_time &lt; DATE_ADD(date_format('${id}','%Y-%m-%d'),INTERVAL 1 DAY))
                OR (on_line_time &gt;= date_format('${id}','%Y-%m-%d')
                AND on_line_time &lt; DATE_ADD(date_format('${id}','%Y-%m-%d'),INTERVAL 1 DAY))
                OR (on_line_time &lt; date_format('${id}','%Y-%m-%d')
                AND (off_line_time &gt; DATE_ADD(date_format('${id}','%Y-%m-%d'),INTERVAL 1 DAY)
                OR off_line_time IS NULL))) group by bt.vehicle_id )
                ELSE 0 END
                as `${id}`
            </foreach>
        </if>
        FROM
        bdm_vehicle bv
        left join blade_dict_biz bd on bv.access_mode = bd.dict_key and bd.code = 'access_mode' and bd.tenant_id = (select tenant_id from blade_dept d where d.id = bv.dept_id )
        where 1=1
    </select>

    <select id="getVehicleOnlineDataByMonth" resultType="com.xh.vdm.statistic.entity.BdmTerminalonlinerecord">
        select licence_plate, licence_color, on_line_time , off_line_time
        from bdm_terminalonlinerecord_${month}
        where on_line_time &lt;= to_timestamp(#{endTime}) and (off_line_time >= FROM_UNIXTIME(#{startTime}) or off_line_time is null)
        order by licence_plate, licence_color, on_line_time
    </select>



    <resultMap id="getVehicleOfflinePageMap" type="com.xh.vdm.statistic.vo.response.VehicleOfflineResponse">
        <result property = "deptName" column = "dept_name"/>
       <!-- <result property = "enterprise"  column = "enterprise"/>-->
        <result property = "vehicleLicence"  column = "licence_plate"/>
        <result property = "licenceColor"  column = "vehicle_color"/>
        <result property = "vehicleModel" column = "vehicle_model"/>
        <result property = "vehicleOwner" column = "vehicle_owner"/>
        <result property = "offLineTime" column = "loc_time"/>
        <result property = "locAddr" column = "loc_addr"/>
        <result property = "accessMode" column = "access_mode"/>
        <result property="offlineDays" column="offline_days"></result>
    </resultMap>

    <select id="getVehicleOfflinePage" resultMap="getVehicleOfflinePageMap">
        SELECT
        bv.licence_plate,
        <!--(select coalesce((select pd.dept_name from blade_dept pd where pd.id=d.pid),d.name) from blade_dept d where d.id = bv.dept_id) as enterprise,-->
        (select d.dept_name from blade_dept d where d.id = bv.dept_id) as dept_name,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        <!--(select dict_value from blade_dict_biz bd where bd.code='licence_color' and bd.dict_key > 0 and bd.dict_key=bv.licence_color and bd.tenant_id=#{vehicleOfflineRequest.tenantId,jdbcType=VARCHAR}) as vehicle_color,-->
        bv.licence_color vehicle_color,
        <!--(select dict_value from blade_dict_biz bd where bd.code=2 and bd.dict_key > 0 and bd.dict_key=bv.vehicle_use_type and bd.tenant_id=#{vehicleOfflineRequest.tenantId,jdbcType=VARCHAR}) as vehicle_model,-->
        bv.vehicle_use_type vehicle_model,
        bvs.loc_time as loc_time,
        floor((extract(epoch from now()) - extract(epoch from loc_time))/86400) offline_days,
        (case when bvs.loc_time&lt;(current_date - INTERVAL '1 DAY') then bvs.loc_addr else concat(bvs.longitude,',',latitude) end) as loc_addr,
        <!--,bd.dict_value access_mode-->
        bv.access_mode
        FROM
        bdm_vehicle bv
        left join
        bdm_terminal bt
        on bv.terminal_id =bt.id and bt.un_bind_flag = 0
        left join
        bdm_vehicle_state bvs on bvs.phone=bt.phone
        <!--left join blade_dict_biz bd on bv.access_mode = bd.dict_key and bd.code = 'access_mode' and bd.tenant_id=#{vehicleOfflineRequest.tenantId,jdbcType=VARCHAR}-->
        WHERE
        1=1
        and bvs.te_state=0

        <if test="vehicleOfflineRequest.vehicleIdList == null">
            and ( bv.dept_id in (
                <foreach collection="vehicleOfflineRequest.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
                <if test="vehicleOfflineRequest.userId != null">
                    or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{vehicleOfflineRequest.userId,jdbcType=BIGINT})
                </if>
            )
        </if>

        <if test="vehicleOfflineRequest.vehicleIdList != null">
            and bv.id in
            <foreach collection="vehicleOfflineRequest.vehicleIdList" separator="," item="vehicleId" open="(" close=")">
                #{vehicleId}
            </foreach>
        </if>

        <if test="vehicleOfflineRequest.offlineDays != null">
            and floor((extract(epoch from now()) - extract(epoch from loc_time))/86400) >= #{vehicleOfflineRequest.offlineDays,jdbcType=INTEGER}
        </if>

        <if test='vehicleOfflineRequest.professionList != null and vehicleOfflineRequest.professionList.size() > 0'>
            AND bv.vehicle_use_type IN
            <foreach collection="vehicleOfflineRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>

        <if test= 'vehicleOfflineRequest.startTime != null'>
            and extract(epoch from bvs.loc_time) &gt;= #{vehicleOfflineRequest.startTime}
        </if>
        <if test= 'vehicleOfflineRequest.endTime != null'>
            and extract(epoch from bvs.loc_time) &lt;= #{vehicleOfflineRequest.endTime}
        </if>
        <if test= "vehicleOfflineRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{vehicleOfflineRequest.vehicleOwnerId}
        </if>
        <if test= "vehicleOfflineRequest.accessMode != null">
            AND bv.access_mode = #{vehicleOfflineRequest.accessMode}
        </if>
        order by bv.dept_id asc,bv.licence_plate asc, bv.licence_color
    </select>


    <resultMap id="vehicleOnlineOrOfflineMap" type="com.xh.vdm.statistic.vo.response.VehicleOnlineOrOfflineResponse">
        <result property = "licencePlate"  column = "licence_plate"/>
        <result property = "deptName" column = "dept_name"/>
        <result property = "licenceColor"  column = "vehicle_color"/>
        <result property = "vehicleUseType" column = "vehicle_use_type"/>
        <result property = "vehicleOwner" column = "vehicle_owner"/>
        <result property = "teState" column = "te_state"/>
        <result property = "positionCount" column = "position_count"/>
        <result property = "onLineTime" column = "on_line_time"/>
        <result property = "offLineTime" column = "off_line_time"/>
        <result property = "driver" column = "driver_name"/>
        <result property = "accessMode" column = "access_mode"/>
    </resultMap>

    <select id="vehicleOnlineOrOffline" resultMap="vehicleOnlineOrOfflineMap">
        SELECT
        bv.licence_plate,
        bd.dict_value access_mode,
        bt.on_line_time,
        bt.off_line_time,
        (select case when vs.te_state=0 then '离线' else '在线' end from bdm_vehicle_state vs where vs.id=bv.vehicle_state_id) as te_state,
        coalesce(bt.point_num,0) as position_count,
        (select d.dept_name from blade_dept d where d.id=bv.dept_id) as dept_name,
        (select dict_value from blade_dict_biz bd where bd.code='licence_color' and bd.parent_id !=0 and bd.dict_key=bv.licence_color and bd.tenant_id=#{tenantId}) as vehicle_color,
        (select dict_value from blade_dict_biz bd where bd.code=2 and bd.parent_id !=0 and bd.dict_key=bv.vehicle_use_type and bd.tenant_id=#{tenantId}) as vehicle_use_type,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        coalesce(bt.driver_name,'未知') as driver_name
        FROM
        ( select bty.* from bdm_terminalonlinerecord bty
        left join bdm_vehicle bv on bty.vehicle_id = bv.id
        where 1=1
        <if test= 'request.startTime != null'>
            AND bty.on_line_time &gt;= to_timestamp(#{request.startTime})
        </if>
        <if test= 'request.endTime != null'>
            AND bty.on_line_time &lt;= to_timestamp(#{request.endTime})
        </if>
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and bv.id in
            <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
            and (
            <if test="request.deptList != null">
                bv.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="request.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="request.vehicleUseType != null ">
            and bv.vehicle_use_type in
            <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.vehicleOwnerId != null">
            and bv.vehicle_owner_id = #{request.vehicleOwnerId}
        </if>
        <if test="request.accessMode != null">
            and bv.access_mode = #{request.accessMode}
        </if>
        order by bty.on_line_time desc limit ${size} offset ${(current - 1) * size}
        ) bt left join
        bdm_vehicle bv
        on bt.vehicle_id = bv.id
        left join blade_dict_biz bd on bv.access_mode = bd.dict_key and bd.code = 'access_mode' and bd.tenant_id = #{tenantId}
        WHERE
        1=1 and bv.id is not null

    </select>



    <resultMap id="vehicleOnlineOrOfflineMapCache" type="com.xh.vdm.statistic.entity.CacheVehicleOnlineOrOfflineResponse">
        <result property = "licencePlate"  column = "licence_plate"/>
        <result property = "deptName" column = "dept_name"/>
        <result property = "licenceColor"  column = "vehicle_color"/>
        <result property = "vehicleUseType" column = "vehicle_use_type"/>
        <result property = "vehicleUseTypeCode" column = "vehicle_use_type_code"/>
        <result property = "vehicleOwner" column = "vehicle_owner"/>
        <result property = "teState" column = "te_state"/>
        <result property = "positionCount" column = "position_count"/>
        <result property = "onLineTime" column = "on_line_time"/>
        <result property = "offLineTime" column = "off_line_time"/>
        <result property = "driver" column = "driver_name"/>
        <result property = "accessMode" column = "access_mode"/>
    </resultMap>

    <select id="vehicleOnlineOrOfflineWithTime" resultMap="vehicleOnlineOrOfflineMapCache">
        SELECT
        bv.licence_plate,
        bd.dict_value access_mode,
        bd.dict_key access_mode_code,
        bt.on_line_time,
        bt.off_line_time,
        (select case when vs.te_state=0 then '离线' else '在线' end from bdm_vehicle_state vs where vs.id=bv.vehicle_state_id) as te_state,
        coalesce(bt.point_num,0) as position_count,
        (select d.dept_name from blade_dept d where d.id=bv.dept_id) as dept_name,
        bv.dept_id,
        (select dict_value from blade_dict_biz bd where bd.code='licence_color' and bd.parent_id !=0 and bd.dict_key=bv.licence_color and tenant_id=#{tenantId}) as vehicle_color,
        bv.licence_color licence_color_code,
        (select dict_value from blade_dict_biz bd where bd.code='2' and bd.parent_id !=0 and bd.dict_key=bv.vehicle_use_type and tenant_id=#{tenantId}) as vehicle_use_type,
        bv.vehicle_use_type vehicle_use_type_code,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        bv.vehicle_owner_id,
        coalesce(bt.driver_name,'未知') as driver_name
        FROM
        ( select bty.* from bdm_terminalonlinerecord bty
        left join bdm_vehicle bv on bty.vehicle_id = bv.id
        where 1=1
        <if test= 'startTime != null'>
            AND bty.on_line_time &gt;= #{startTime}
        </if>
        <if test= 'endTime != null'>
            AND bty.on_line_time &lt;= #{endTime}
        </if>
        ) bt left join
        bdm_vehicle bv
        on bt.vehicle_id = bv.id
        left join blade_dict_biz bd on bv.access_mode = bd.dict_key and bd.code = 'access_mode' and tenant_id = #{tenantId}
        WHERE
        1=1 and bv.id is not null

    </select>



    <select id="vehicleOnlineOrOfflineWithTimeMonth" resultMap="vehicleOnlineOrOfflineMapCache">

        <foreach collection="request.dmList" separator=" union " item="dm">
            select bv.licence_plate,
            bv.id vehicleId,
            bv.licence_color licence_color_code,
            bt.on_line_time,
            bt.off_line_time,
            bv.dept_id,
            <!--coalesce(bt.point_num,0) as position_count,-->
            bv.vehicle_use_type vehicle_use_type_code,
            bv.vehicle_owner_id,
            bv.access_mode access_mode_code,
            bvs.te_state,
            coalesce(bt.driver_name,'未知') as driver_name
            from bdm_terminalonlinerecord_${dm.month} bt
            left join bdm_vehicle bv on bt.vehicle_id = bv.id
            left join bdm_vehicle_state bvs on bvs.licence_plate = bv.licence_plate and bvs.licence_color = bv.licence_color
            where 1 = 1
            <if test= 'dm.durationStartTime != null'>
                AND bt.on_line_time &gt;= #{dm.durationStartTime}
            </if>
            <if test= 'dm.durationEndTime != null'>
                AND bt.on_line_time &lt;= #{dm.durationEndTime}
            </if>
        </foreach>

    </select>



    <select id="vehicleOnlineOrOfflineCount" resultType="long">
        SELECT count(1)
        FROM
        ( select bty.* from bdm_terminalonlinerecord bty
        left join bdm_vehicle bv on bty.vehicle_id = bv.id
        where 1=1
        <if test= 'request.startTime != null'>
            AND bty.on_line_time &gt;= to_timestamp(#{request.startTime})
        </if>
        <if test= 'request.endTime != null'>
            AND bty.on_line_time &lt;= to_timestamp(#{request.endTime})
        </if>
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and bv.id in
            <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
            and (
            <if test="request.deptList != null">
                bv.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="request.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="request.vehicleUseType != null ">
            and bv.vehicle_use_type in
            <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.vehicleOwnerId != null">
            and bv.vehicle_owner_id = #{request.vehicleOwnerId}
        </if>
        <if test="request.accessMode != null">
            and bv.access_mode = #{request.accessMode}
        </if>
        ) bt left join
        bdm_vehicle bv
        on bt.vehicle_id = bv.id
        WHERE
        1=1 and bv.id is not null
    </select>


    <resultMap id="securityInfoMap" type="com.xh.vdm.statistic.vo.response.SecurityInfoResponse">
        <result property = "deptName" column = "dept_name"/>
        <result property = "licencePlate"  column = "licence_plate"/>
        <result property = "licenceColor"  column = "licence_color"/>
        <result property = "vehicleModel" column = "vehicle_model"/>
        <result property = "vehicleOwner" column = "vehicle_owner"/>
        <result property = "sendTime" column = "update_time"/>
        <result property = "sendMessage" column = "param"/>
        <result property = "sendState" column = "res_param"/>
        <result property = "accessMode" column = "access_mode"/>
    </resultMap>

    <select id="securityInfo" resultMap="securityInfoMap">
        SELECT
        bd.dict_name access_mode,
        (select coalesce((select pd.name from sys_dept pd where pd.id=d.pid),d.name) from sys_dept d where d.id = bv.dept_id) as enterprise,
        (select d.name from sys_dept d where d.id = bv.dept_id) as dept_name,
        bv.licence_plate,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 3 AND bd.pid != 0
        AND bd.dict_code = bv.licence_color) AS licence_color,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 2 AND bd.pid != 0
        AND bd.dict_code = bv.vehicle_use_type) AS vehicle_model,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        bcr.update_time,
        bcr.param,
        case when bcr.res_param=0 then '成功' else '失败' end as  res_param
        FROM
        bdm_cmd_record bcr,
        bdm_vehicle bv
        left join bam_dict bd on bv.access_mode = bd.dict_code and bd.dict_type = 30 and bd.is_del = 0
        WHERE
        bcr.licence_plate = bv.licence_plate and bcr.licence_color = bv.licence_color
        <if test= 'securityInfoRequest.startTime != null'>
            AND bcr.update_time &gt;= to_timestamp(#{securityInfoRequest.startTime})
        </if>
        <if test= 'securityInfoRequest.endTime != null'>
            AND bcr.update_time &lt;= to_timestamp(#{securityInfoRequest.endTime})
        </if>
        <if test= 'securityInfoRequest.deptList != null and securityInfoRequest.deptList.size() != 0'>
            and bv.dept_id IN
            <foreach collection="securityInfoRequest.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test='securityInfoRequest.professionList != null and securityInfoRequest.professionList.size() > 0'>
            and bv.vehicle_use_type IN
            <foreach collection="securityInfoRequest.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        <if test= 'securityInfoRequest.licencePlate != null'>
            AND bcr.licence_plate LIKE CONCAT('%',#{securityInfoRequest.licencePlate},'%')
        </if>
        <if test= 'securityInfoRequest.licenceColor != null'>
            AND bcr.licence_color = #{securityInfoRequest.licenceColor}
        </if>
        <if test= "securityInfoRequest.vehicleOwnerId != null">
            AND bv.vehicle_owner_id = #{securityInfoRequest.vehicleOwnerId}
        </if>
        <if test= "securityInfoRequest.accessMode != null">
            AND bv.access_mode = #{securityInfoRequest.accessMode}
        </if>
        order by bcr.update_time desc
    </select>


    <select id="securityInfoWithTime" resultType="com.xh.vdm.statistic.entity.CacheSecurityInfoResponse">
        SELECT
        bd.dict_name access_mode,
        bv.access_mode access_mode_code,
        (select coalesce((select pd.name from sys_dept pd where pd.id=d.pid),d.name) from sys_dept d where d.id = bv.dept_id) as enterprise,
        bv.dept_id,
        (select d.name from sys_dept d where d.id = bv.dept_id) as dept_name,
        bv.licence_plate,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 3 AND bd.pid != 0
        AND bd.dict_code = bv.licence_color) AS licence_color,
        bv.licence_color licence_color_code,
        (SELECT
        dict_name
        FROM
        bam_dict bd
        WHERE
        bd.dict_type = 2 AND bd.pid != 0
        AND bd.dict_code = bv.vehicle_use_type) AS vehicle_model,
        bv.vehicle_use_type vehicle_model_code,
        coalesce( (SELECT btp.NAME FROM bam_third_party_platform btp WHERE btp.id = bv.vehicle_owner_id ), '非营运车辆' ) AS vehicle_owner,
        bv.vehicle_owner_id,
        bcr.update_time send_time,
        bcr.param send_message,
        case when bcr.res_param=0 then '成功' else '失败' end as  send_state
        FROM
        bdm_cmd_record bcr,
        bdm_vehicle bv
        left join bam_dict bd on bv.access_mode = bd.dict_code and bd.dict_type = 30 and bd.is_del = 0
        WHERE
        bcr.licence_plate = bv.licence_plate and bcr.licence_color = bv.licence_color
        <if test= 'startTime != null'>
            AND bcr.update_time &gt;= #{startTime}
        </if>
        <if test= 'endTime != null'>
            AND bcr.update_time &lt;= #{endTime}
        </if>
    </select>


    <select id="getVehicleOnlineOrOffline" resultType="com.xh.vdm.statistic.vo.response.VehicleOnlineOrOfflineResponse">
        select a.licence_plate, a.licence_color, a.access_mode, a.on_line_time, a.off_line_time, a.point_num, a.driver_name, a.dept_id, a.vehicle_use_type,a.vehicle_owner_id, a.checkTime
        from (
        <foreach collection="request.dmList" item="item" separator=" union ">
            select bv.dept_id, bv.licence_plate, bv.access_mode, bv.licence_color, bv.vehicle_use_type, bv.vehicle_owner_id,  btr.on_line_time, btr.off_line_time, btr.point_num, btr.driver_name, now() checkTime
            from bdm_terminalonlinerecord_${item.month} btr, bdm_vehicle bv
            where btr.vehicle_id = bv.id
            and on_line_time >= #{item.durationStartTime} and on_line_time &lt;= #{item.durationEndTime}
            <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
                and btr.vehicle_id in
                <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                    #{vehicleId}
                </foreach>
            </if>

            <if test="request.vehicleUseType != null ">
                and bv.vehicle_use_type in
                <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.vehicleOwnerId != null">
                and bv.vehicle_owner_id = #{request.vehicleOwnerId}
            </if>
            <if test="request.accessMode != null">
                and bv.access_mode = #{request.accessMode}
            </if>
        </foreach>
        ) a
    </select>




    <select id="getUnexpectedReport" resultType="com.xh.vdm.statistic.vo.response.UnexpectedReportResponse">
        select
        (select d.dept_name from blade_dept d where d.id = bs.dept_id) as dept_name,
        bs.licence_plate,
        (select dict_value from blade_dict_biz bd where bd.code = 'licence_color' and bd.parent_id != 0 and bd.dict_key = bs.licence_color and bd.tenant_id=#{tenantId}) as licence_color,
        (select dict_value from blade_dict_biz bd where bd.code = 2 and bd.parent_id != 0 and bd.dict_key = bv.vehicle_use_type and bd.tenant_id=#{tenantId}) as vehicle_use_type,
        (select dict_value from blade_dict_biz bd where bd.code = 'access_mode' and bd.parent_id != 0 and bd.dict_key = bv.access_mode and bd.tenant_id=#{tenantId}) as access_mode,
        coalesce((select btp.name from bam_third_party_platform btp where btp.id = bv.vehicle_owner_id), '非营运车辆') as vehicle_owner,
        (select dict_value from blade_dict_biz bd where bd.code='alarm_type' and bd.parent_id != 0 and  bd.dict_key = bs.alarm_type and bd.tenant_id=#{tenantId}) as alarm_type,
        -- from_unixtime(bv.stop_report_start_time) as start_time,
        -- from_unixtime(bv.stop_report_end_time) as end_time,
        (select concat(start_date,' ',start_time) from bdm_call_stop_change_rule bc where bc.id = bs.rule_id) as start_time,
        (select concat(end_date,' ',end_time) from bdm_call_stop_change_rule bc where bc.id = bs.rule_id) as end_time,
        to_timestamp(bs.alarm_time) as alarm_time,
        bs.alarm_address
        from (
        select bsy.* from bdm_security bsy
        left join bdm_vehicle bv on bv.id = bsy.vehicle_id
        where alarm_type=260
        <if test='request.startTime != null'>
        and bsy.alarm_time &gt;= to_timestamp(#{request.startTime})
        </if>
        <if test='request.endTime != null'>
        and bsy.alarm_time &lt;= to_timestamp(#{request.endTime})
        </if>
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and bv.id in
            <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
            and (
            <if test="request.deptList != null">
                bv.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="request.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
            </if>
            )
        </if>
        ) bs, bdm_vehicle bv
        where bs.licence_plate = bv.licence_plate and bs.licence_color = bv.licence_color
        <if test="request.vehicleUseType != null ">
            and bv.vehicle_use_type in
            <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.vehicleOwnerId != null">
            and bv.vehicle_owner_id = #{request.vehicleOwnerId}
        </if>
        <if test="request.accessMode != null">
            and bv.access_mode = #{request.accessMode}
        </if>
         order by bs.alarm_time desc
    </select>

    <select id="getVehicleOnline" resultType="com.xh.vdm.statistic.vo.response.VehicleOnlineResponse">
        SELECT d.dept_name,
        (
        SELECT coalesce(SUM(num),0)
        FROM VIEW_VEHICLE_STATE v
        WHERE dept_id IN
        <foreach collection="deptIdList" item="id" index="index" open="(" close=")" separator=",">#{id}
        </foreach>
        AND te_state = 0
        ) AS
        vehicle_offline,
        (
        SELECT coalesce(SUM(num),0)
        FROM
        VIEW_VEHICLE_STATE v
        WHERE dept_id IN
        <foreach collection="deptIdList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                 </foreach>
                 AND te_state = 1
            ) AS vehicle_online,
            (
                SELECT coalesce(COUNT(1),0)
                FROM bdm_vehicle
                WHERE dept_id IN
                <foreach collection="deptIdList" item="id" index="index" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            ) AS vehicle_total
           FROM
               blade_dept d
           WHERE 1=1 and d.is_deleted=0
           <if test= 'deptIdList != null and deptIdList.size() != 0'>
             AND d.id = #{deptId}
            </if>
    </select>

</mapper>
