package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@TableName("bdm_last_online_record")
@Data
public class LastOnline implements Serializable {

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	@TableField(value = "vehicle_id")
	private Integer vehicleId;

	@TableField(value = "licence_color")
	private String licenceColor;

	@TableField(value = "licence_plate")
	private String licencePlate;

	@TableField(value = "start_time")
	private Date startTime;

	@TableField(value = "end_time")
	private Date endTime;
}
