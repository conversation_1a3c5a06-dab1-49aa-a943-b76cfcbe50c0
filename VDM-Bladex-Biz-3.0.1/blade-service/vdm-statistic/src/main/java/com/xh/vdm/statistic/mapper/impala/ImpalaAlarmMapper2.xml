<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.impala.ImpalaAlarmMapper2">
    <resultMap id="targetAlarmDTO" type="com.xh.vdm.statistic.dto.TargetAlarmDTO">
        <result property="deviceType" column="device_type"/>
        <result property="deviceNum" column="device_num"/>
        <result property="targetId" column="target_id"/>
        <result property="type" column="type"/>
        <result property="level" column="level"/>
        <result property="startTime" column="start_time"/>
    </resultMap>

    <select id="getAlarmListWithDev" resultMap="targetAlarmDTO">
        select device_type,device_num,target_id,dept_id,type,level,start_time,device_id from alarm where 1 = 1
        <if test="startTime != null and startTime gt 0">
            and start_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime gt 0">
            and start_time &lt;= #{endTime}
        </if>
        <if test="deviceIdList != null and deviceIdList.size() gt 0 ">
            and device_id in
            <foreach collection="deviceIdList" item="deviceId" index="index" open="(" close=")" separator=",">
                <if test="index != 0 and index % 999 == 0">
                    #{deviceId}) or device_id in (
                </if>
                #{deviceId}
            </foreach>
        </if>
        order by start_time desc
        limit ${size} offset ${(current - 1) * size}
    </select>

    <select id="getAlarmListWithDept" resultMap="targetAlarmDTO">
        select device_type,device_num,target_id,dept_id,type,level,start_time,device_id from alarm where 1 = 1
        <if test="startTime != null and startTime gt 0">
            and start_time &gt;= #{startTime}
        </if>
        <if test="endTime != null and endTime gt 0">
            and start_time &lt;= #{endTime}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and dept_id = any(${deptIds})
        </if>
        order by start_time desc
        limit ${size} offset ${(current - 1) * size}
    </select>

</mapper>
