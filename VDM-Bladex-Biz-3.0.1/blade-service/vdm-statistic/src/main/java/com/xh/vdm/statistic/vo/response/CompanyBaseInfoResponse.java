package com.xh.vdm.statistic.vo.response;

import com.xh.vdm.statistic.entity.TerminalModelAndCount;
import com.xh.vdm.statistic.entity.TerminalTypeAndCount;
import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2022/11/21 8:08 AM
 */
@Data
public class CompanyBaseInfoResponse {

    //企业名称
    private String deptName;
    //企业经营许可证号
    private String certificateCode;
    //企业经营许可证号过期时间
    private String certExpirationDate;
    //车辆数量
    private Integer vehicleCount;
    //驾驶员数量
    private Integer driverCount;
    //入网车辆数量
    private Integer inNetVehicleCount;
    //终端分类及数量
    private List<TerminalTypeAndCount> terminalTypeAndCount;
    //视频终端和定位终端数量
    private List<TerminalModelAndCount> terminalModelAndCount;
}
