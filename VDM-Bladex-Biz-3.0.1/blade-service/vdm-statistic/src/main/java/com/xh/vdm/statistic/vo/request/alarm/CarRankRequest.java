package com.xh.vdm.statistic.vo.request.alarm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.vo.request.CommonStatRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

@ApiModel(value = "请求体：车辆驾驶行为排行")
@Data
@EqualsAndHashCode(callSuper = true)
public class CarRankRequest extends CommonStatRequest {

	@JsonProperty("dept_id")
	@ApiModelProperty(name = "dept_id", value = "单位ID", example = "1", required = true)
	@NotNull(message = "单位ID为空。")
	@DecimalMin(value = "1", message = "单位ID不正确。")
	private Long deptId;

	@JsonProperty("year_month")
	@ApiModelProperty(name = "year_month", value = "年月", example = "202306", required = true)
	@NotEmpty(message = "年月为空。")
	@Pattern(regexp = "\\d{6}", message = "年月不正确。")
	private String yearMonth;
}
