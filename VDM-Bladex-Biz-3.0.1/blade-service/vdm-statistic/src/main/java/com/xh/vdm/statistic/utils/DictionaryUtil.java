package com.xh.vdm.statistic.utils;

import com.xh.vdm.statistic.constant.RedisConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据字典工具类
 * <AUTHOR>
 * @date Created in 11:39 2021/4/29
 * @description
 */
@Slf4j
@Component
public class DictionaryUtil {

    @Autowired
    protected RedisTemplate<String, String> redisTemplate;

    /**
     * 根据类别码获取数据字典
     * <AUTHOR>
     * @date 2021-04-29 11:43:24
     * @param typeCode
     * @return
     **/
    public Map<Integer, String> acquireDictionaryByTypeCode(String typeCode){
        Map<Object, Object> value = redisTemplate.opsForHash().entries(RedisConstants.HashKey.DICTIONARY_HASH_KEY + "-" + typeCode);
        try{
            if(null != value){
                Map<Integer, String> result = new HashMap<>();
                value.forEach((k, v)->{
                    result.put(Integer.valueOf((String)k), (String)v);
                });
            }
        }catch (ClassCastException e){
            log.error("{} error, get DictionaryByTypeCode error, typeCode:{}",CodeInfoUtils._FILE_LINE_FUNC_(), typeCode);
        }
        return null;
    }

    /**
     * 根据类别码和字典value获取数据字典名称
     * <AUTHOR>
     * @date 2021-04-29 15:16:55
     * @param typeCode
     * @param value
     * @return
     **/
    public String acquireDictionaryNameByTypeCodeAndValue(String typeCode, Integer value){
        try{
            String result = (String) redisTemplate.opsForHash().get(RedisConstants.HashKey.DICTIONARY_HASH_KEY + "-" + typeCode, value.toString());
            return result;
        }catch (ClassCastException e){
            log.error("{} error, get DictionaryNameByTypeCodeAndValue error, typeCode:{}, value:{}", CodeInfoUtils._FILE_LINE_FUNC_(), typeCode, value);
        }
        return null;
    }

}
