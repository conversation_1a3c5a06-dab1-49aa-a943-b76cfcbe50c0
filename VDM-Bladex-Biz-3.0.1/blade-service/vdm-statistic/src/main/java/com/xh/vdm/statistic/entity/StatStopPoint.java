package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆停止点表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-22
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatStopPoint implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 统计日期
     */
    private Date statDate;

    /**
     * 车牌号
     */
    private String licencePlate;

	/**
	 * 车牌颜色
	 */
	private String licenceColor;

    /**
     * 停止开始时定位点id
     */
    private String stopStartId;

    /**
     * 停止结束时定位点id
     */
    private String stopEndId;

    /**
     * 停止开始时间
     */
    private Long stopStartTime;

    /**
     * 停止结束时间
     */
    private Long stopEndTime;

    /**
     * 停止点经度
     */
    private Double stopLongitude;

    /**
     * 停止点纬度
     */
    private Double stopLatitude;

    /**
     * 停止时长
     */
    private Long stopDuration;

	//停止位置
	private String stopAddress;

    //停止后打点数
    private Integer stopLocationCount;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;


	//部门id
	private Long deptId;

	//车辆id
	private Integer vehicleId;

	//行业类型
	private Integer vehicleUseType;

	//车辆归属
	private Long vehicleOwnerId;

	//接入类型
	private String accessMode;

}
