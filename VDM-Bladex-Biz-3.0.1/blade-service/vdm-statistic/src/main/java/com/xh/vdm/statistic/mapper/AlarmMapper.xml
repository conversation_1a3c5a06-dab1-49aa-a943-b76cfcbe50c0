<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.AlarmMapper">
    <select id="getAlarmCount" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select rate.date statDate , count(*) count
        from (
        select substring(alarm_time,1,10) date from bdm_security bs where bs.alarm_level != 0 and alarm_time &lt;= to_timestamp(#{endTime,jdbcType=BIGINT}) and alarm_time >= FROM_UNIXTIME(#{startTime,jdbcType=BIGINT}) and (is_wrong != 1 or is_wrong is null) and ( (bs.dept_id in (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId}
            </foreach>
            )) or bs.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}))
        ) rate group by date
    </select>


    <select id="getAlarmCountHour" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select rate.date statDate , count(*) count
        from (
        select substring(alarm_time,12,2) date from bdm_security bs where bs.alarm_level != 0 and alarm_time &lt;= to_timestamp(#{endTime,jdbcType=BIGINT}) and alarm_time >= FROM_UNIXTIME(#{startTime,jdbcType=BIGINT}) and (is_wrong != 1 or is_wrong is null) and ( (bs.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )) or bs.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}))
        ) rate group by date
    </select>

    <select id="getHandleCount" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select rate.date statDate , count(*) count
        from (
        select substring(alarm_time,1,10) date from bdm_security bs where bs.alarm_level != 0 and alarm_time &lt;= to_timestamp(#{endTime,jdbcType=BIGINT}) and alarm_time >= FROM_UNIXTIME(#{startTime,jdbcType=BIGINT}) and (is_wrong != 1 or is_wrong is null) and ( (bs.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )) or bs.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}))
            <!-- 服务商 -->
            <if test="userType == 1">
                and server_state = 1
            </if>
            <!-- 第三方服务商 -->
            <if test="userType == 2">
                and third_state = 1
            </if>
            <!-- 企业 -->
            <if test="userType == 3">
                and company_state = 1
            </if>
        ) rate group by date
    </select>

    <select id="getHandleCountTotal" resultType="long">
        select count(*) count
        from (
        select substring(alarm_time,1,10) date from bdm_security bs where bs.alarm_level != 0 and alarm_time &lt;= to_timestamp(#{endTime,jdbcType=BIGINT}) and alarm_time >= FROM_UNIXTIME(#{startTime,jdbcType=BIGINT}) and (is_wrong != 1 or is_wrong is null) and ( (bs.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        ))
        <if test="userId != null and userId != ''">
            or bs.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT})
        </if>
        )
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方服务商 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        ) rate
    </select>

    <select id="getUnHandleRealTimeAlarmByPage" resultType="com.xh.vdm.statistic.entity.BdmSecurity">
        select * from bdm_security bs
        where bs.alarm_level != 0
        and alarm_time &lt;= to_timestamp(#{endTime,jdbcType=BIGINT})
        and alarm_time >= to_timestamp(#{startTime,jdbcType=BIGINT})
        and (is_wrong != 1 or is_wrong is null)
        and ( (bs.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )) or bs.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}))
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 0
        </if>
        <!-- 第三方服务商 -->
        <if test="userType == 2">
            and third_state = 0
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 0
        </if>
        <if test="alarmTypeList != null">
            and alarm_type in
            <foreach collection="alarmTypeList" item="alarmType" open="(" close=")" separator=",">
                #{alarmType}
            </foreach>
        </if>
        and alarm_complete = 0
    </select>


    <select id="getHandleCountHour" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select rate.date statDate , count(*) count
        from (
        select substring(alarm_time,12,2) date from bdm_security bs where alarm_time &lt;= to_timestamp(#{endTime,jdbcType=BIGINT}) and alarm_time >= FROM_UNIXTIME(#{startTime,jdbcType=BIGINT}) and (is_wrong != 1 or is_wrong is null) and ( (bs.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )) or bs.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}))
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方服务商 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        ) rate group by date
    </select>

    <select id="getFatigueCountByDateAndDuration" resultType="long">
        select count(*) from
        (
            select licence_plate , licence_color, alarm_type, alarm_time, alarm_end_time, (extract(epoch from alarm_end_time) - extract(epoch from alarm_time)) duration
            from bdm_security bs where alarm_type in (102,2) and alarm_time >= #{startDate,jdbcType=VARCHAR} and alarm_time &lt; #{endDate,jdbcType=VARCHAR}
            and ( (bs.dept_id in (
                <foreach collection="deptIds" item="deptId" separator=",">
                    #{deptId}
                </foreach>
            )) or bs.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}))
        ) a
        where a.duration > #{duration}
    </select>

    <select id="getFatigueHandleCountByDateAndDuration" resultType="long">
        select count(*) from
        (
        select licence_plate , licence_color, alarm_type, alarm_time, alarm_end_time, (extract(epoch from alarm_end_time) - extract(epoch from alarm_time)) duration
        from bdm_security bs where alarm_type in (102,2) and alarm_time >= #{startDate,jdbcType=VARCHAR} and alarm_time &lt; #{endDate,jdbcType=VARCHAR}
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        and ( (bs.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )) or bs.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}))
        ) a
        where a.duration > #{duration}
    </select>



    <select id="getAlarmCountByDate" resultType="long">
        select count(*) from
        (
        select licence_plate , licence_color, alarm_type, alarm_time, alarm_end_time
        from bdm_security bs where alarm_type in
        <foreach collection="alarmTypes" item="type" separator="," open="(" close=")">
            #{type}
        </foreach>
        and alarm_time >= #{startDate,jdbcType=VARCHAR} and alarm_time &lt; #{endDate,jdbcType=VARCHAR}
        and ( (bs.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )) or bs.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}))
        ) a
    </select>


    <select id="getAlarmHandleCountByDate" resultType="long">
        select count(*) from
        (
        select licence_plate , licence_color, alarm_type, alarm_time, alarm_end_time
        from bdm_security bs where alarm_type in
        <foreach collection="alarmTypes" item="type" separator="," open="(" close=")">
            #{type}
        </foreach>
        and alarm_time >= #{startDate,jdbcType=VARCHAR} and alarm_time &lt; #{endDate,jdbcType=VARCHAR}
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        and ((bs.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )) or bs.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}))
        ) a
    </select>

    <select id="getAlarmBaseByDate" resultType="com.xh.vdm.statistic.entity.AlarmBase">
        select id alarmId, licence_plate licencePlate , licence_color licenceColor, alarm_type alarmType, alarm_time alarmTime, alarm_end_time alarmEndTime
        from bdm_security bs where alarm_type in
        <foreach collection="alarmTypes" item="type" separator="," open="(" close=")">
            #{type}
        </foreach>
        and alarm_time >= #{startDate,jdbcType=VARCHAR} and alarm_time &lt; #{endDate,jdbcType=VARCHAR}
        and ( (bs.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )) or bs.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}))
    </select>

    <select id="getAlarmHandleCountByAlarmIds" resultType="long">
        select count(*) from
        (
        select licence_plate , licence_color, alarm_type, alarm_time, alarm_end_time
        from bdm_security bs where 1 = 1
        and id in
        <foreach collection="alarmIds" item="alarmId" open="(" close=")" separator=",">
            #{alarmId}
        </foreach>
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>

        ) a
    </select>

    <select id="getNumCarForDept" resultType="int">
        select count(1) as num_car from bdm_vehicle where 1 = 1
        <if test="request.deptList != null and request.deptList.size() gt 0">
            and dept_id in
            <foreach collection="request.deptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
    </select>

    <select id="getNumNoPosition" resultType="int">
        select count(1) as num from stat_not_location_${yearMonth} as snl
        inner join bdm_vehicle as bv on snl.licence_color = bv.licence_color and snl.licence_plate = bv.licence_plate
        where 1 = 1
        <if test="request.deptList != null and request.deptList.size() gt 0">
            and bv.dept_id in
            <foreach collection="request.deptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
    </select>

    <select id="getNumOfflineMove" resultType="int">
        select count(1) as num from bdm_offline_move as bom inner join bdm_vehicle as bv on bom.vehicle_id = bv.id
        where 1 = 1
        <if test="request.deptList != null and request.deptList.size() gt 0">
            and bv.dept_id in
            <foreach collection="request.deptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="request.startTime != null">
            and bom.end_time &gt;= to_timestamp(#{request.startTime})
        </if>
        <if test="request.endTime != null">
            and bom.start_time &lt;= to_timestamp(#{request.endTime})
        </if>
    </select>

    <select id="getCarRankOfNoPosition" resultType="com.xh.vdm.statistic.dto.alarm.AlarmCount">
        select snl.licence_plate, count(1) as num_alarm from stat_not_location_${yearMonth} as snl
        inner join bdm_vehicle as bv on snl.licence_color = bv.licence_color and snl.licence_plate = bv.licence_plate
        where 1 = 1
        <if test="request.deptList != null and request.deptList.size() gt 0">
            and bv.dept_id in
            <foreach collection="request.deptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        group by snl.licence_plate having count(1) > 0 order by num_alarm desc
    </select>

    <select id="getCarRankOfOfflineMove" resultType="com.xh.vdm.statistic.dto.alarm.AlarmCount">
        select bom.licence_plate, count(1) as num_alarm from bdm_offline_move as bom inner join bdm_vehicle as bv on bom.vehicle_id = bv.id
        where 1 = 1
        <if test="request.deptList != null and request.deptList.size() gt 0">
            and bv.dept_id in
            <foreach collection="request.deptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="request.startTime != null">
            and bom.end_time &gt;= to_timestamp(#{request.startTime})
        </if>
        <if test="request.endTime != null">
            and bom.start_time &lt;= to_timestamp(#{request.endTime})
        </if>
        group by bom.licence_plate having count(1) > 0 order by num_alarm desc
    </select>

</mapper>
