package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 疲劳驾驶统计表返回类
 * <AUTHOR>
 * @date 2021/11/1 10:19
 */
@ApiModel(value = "疲劳驾驶统计表返回类")
public class FatigueDrivingResponse {

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("enterprise")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "企业名称"})
    @ColumnWidth(40)
    private String enterprise;

    @ApiModelProperty(value = "车队名称")
    @JsonProperty("dept_name")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ColumnWidth(40)
    @ExcelProperty({"疲劳驾驶统计表", "车队名称"})
    private String deptName;

    @ApiModelProperty(value = "车牌号码")
    @JsonProperty("licence_plate")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "车牌号码"})
    @ColumnWidth(15)
    private String licencePlate;

    @ApiModelProperty(value = "车牌颜色")
    @JsonProperty("licence_color")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "车牌颜色"})
    private String licenceColor;

    @ApiModelProperty(value = "行业类型")
    @JsonProperty("vehicle_model")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "行业类型"})
    @ColumnWidth(20)
    private String vehicleModel;

    @ApiModelProperty(value = "车辆归属")
    @JsonProperty("vehicle_owner")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "车辆归属"})
    @ColumnWidth(20)
    private String vehicleOwner;


    @ApiModelProperty(value = "车辆接入方式")
    @JsonProperty("access_mode")
    @ExcelProperty({"疲劳驾驶统计表", "车辆接入方式"})
    @ColumnWidth(15)
    private String accessMode;

    @ApiModelProperty(value = "报警类型")
    @JsonProperty("alarm_type")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "报警类型"})
    @ColumnWidth(25)
    private String alarmType;

    @ApiModelProperty(value = "疲劳驾驶开始时间")
    @JsonProperty("start_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "疲劳驾驶开始时间"})
    @ColumnWidth(22)
    private Date startAlarmTime;

    @ApiModelProperty(value = "疲劳驾驶结束时间")
    @JsonProperty("end_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "疲劳驾驶结束时间"})
    @ColumnWidth(22)
    private Date endAlarmTime;

    @ApiModelProperty(value = "持续时间")
    @JsonProperty("duration_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "持续时间"})
    @ColumnWidth(20)
    private String durationTime;

    @ApiModelProperty(value = "速度")
    @JsonProperty("speed")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "速度(km/h)"})
    @ColumnWidth(12)
    private Double speed;

    @ApiModelProperty(value = "开始位置")
    @JsonProperty("start_address")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "开始位置"})
    @ColumnWidth(90)
    private String startAddress;

    @ApiModelProperty(value = "结束位置")
    @JsonProperty("end_address")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"疲劳驾驶统计表", "结束位置"})
    @ColumnWidth(90)
    private String endAddress;




    public String getAccessMode() {
        return accessMode;
    }

    public void setAccessMode(String accessMode) {
        this.accessMode = accessMode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getEnterprise() {
        return enterprise;
    }

    public void setEnterprise(String enterprise) {
        this.enterprise = enterprise;
    }

    public String getLicencePlate() {
        return licencePlate;
    }

    public void setLicencePlate(String licencePlate) {
        this.licencePlate = licencePlate;
    }

    public String getLicenceColor() {
        return licenceColor;
    }

    public void setLicenceColor(String licenceColor) {
        this.licenceColor = licenceColor;
    }

    public String getVehicleModel() {
        return vehicleModel;
    }

    public void setVehicleModel(String vehicleModel) {
        this.vehicleModel = vehicleModel;
    }

    public String getVehicleOwner() {
        return vehicleOwner;
    }

    public void setVehicleOwner(String vehicleOwner) {
        this.vehicleOwner = vehicleOwner;
    }

    public Date getStartAlarmTime() {
        return startAlarmTime;
    }

    public void setStartAlarmTime(Date startAlarmTime) {
        this.startAlarmTime = startAlarmTime;
    }

    public Date getEndAlarmTime() {
        return endAlarmTime;
    }

    public void setEndAlarmTime(Date endAlarmTime) {
        this.endAlarmTime = endAlarmTime;
    }

    public String getDurationTime() {
        return durationTime;
    }

    public void setDurationTime(String durationTime) {
        this.durationTime = durationTime;
    }

    public String getStartAddress() {
        return startAddress;
    }

    public void setStartAddress(String startAddress) {
        this.startAddress = startAddress;
    }

    public String getAlarmType() {
        return alarmType;
    }

    public void setAlarmType(String alarmType) {
        this.alarmType = alarmType;
    }

    public Double getSpeed() {
//        if(speed==null||speed==0){
//            return 0D;
//        }else{
//            return speed;
//        }
        if(speed==null||speed==0){
            speed = 0D;
        }
        if(( speed % 1) == 0){
            return new BigDecimal(speed).doubleValue();
        }else{
            return new BigDecimal(speed).setScale(1,BigDecimal.ROUND_HALF_UP).doubleValue();
        }
    }

    public void setSpeed(Double speed) {
        this.speed = speed;
    }

    public String getEndAddress() {
        return endAddress;
    }

    public void setEndAddress(String endAddress) {
        this.endAddress = endAddress;
    }
}
