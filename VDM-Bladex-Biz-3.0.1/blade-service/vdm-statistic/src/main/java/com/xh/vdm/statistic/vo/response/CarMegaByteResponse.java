package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "获取返回体：车辆数据大小")
@Data
public class CarMegaByteResponse {

    @JsonProperty("dept_name")
    @ApiModelProperty(name = "dept_name", value = "车队名称", example = "星航", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ColumnWidth(40)
    @ExcelProperty({"车辆每日流量报表", "车队名称"})
    private String deptName;

    @JsonProperty("licence_plate")
    @ApiModelProperty(name = "licence_plate", value = "车牌号", example = "粤A12345", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆每日流量报表", "车牌号码"})
    @ColumnWidth(15)
    private String licencePlate;

    @JsonProperty("licence_color")
    @ApiModelProperty(name = "licence_color", value = "车牌颜色", example = "黄色", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
    @ColumnWidth(15)
    private String licenceColor;

	@ApiModelProperty(name = "licence_color_desc", value = "车牌颜色", example = "黄色", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@JsonIgnore
	@ExcelProperty({"车辆每日流量报表", "车牌颜色"})
	@ColumnWidth(15)
	private String licenceColorDesc;

    @JsonProperty("vehicle_use_type")
    @ApiModelProperty(name = "vehicle_use_type", value = "车辆类型", example = "客运", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
    @ColumnWidth(20)
    private String vehicleUseType;


	@ApiModelProperty(name = "vehicle_use_type_desc", value = "车辆类型", example = "客运", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty({"车辆每日流量报表", "行业类型"})
	@JsonIgnore
	@ColumnWidth(20)
	private String vehicleUseTypeDesc;


    @JsonProperty("access_mode")
    @ApiModelProperty(name = "access_mode", value = "接入方式", example = "直营", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
    @ColumnWidth(15)
    private String accessMode;

	@ApiModelProperty(name = "access_mode", value = "接入方式", example = "直营", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty({"车辆每日流量报表", "接入方式"})
	@JsonIgnore
	@ColumnWidth(15)
	private String accessModeDesc;

    @JsonProperty("vehicle_owner")
    @ApiModelProperty(name = "vehicle_owner", value = "车辆归属", example = "兵通", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆每日流量报表", "车辆归属"})
    @ColumnWidth(20)
    private String vehicleOwner;

	@ExcelIgnore
	@JsonIgnore
	private Long vehicleOwnerId;

	@ExcelIgnore
	@JsonIgnore
	private Long deptId;


	@JsonProperty("stat_date")
	@ApiModelProperty(name = "stat_date", value = "统计日期", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty({"车辆每日流量报表", "统计日期"})
	@ColumnWidth(20)
	private String statDate;

    @JsonProperty("mega_byte")
    @ApiModelProperty(name = "mega_byte", value = "数据大小（单位：MB）", example = "12.34", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆每日流量报表", "数据大小（单位：MB）"})
    @ColumnWidth(20)
    private Double megaByte;
}
