package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.vo.page.PageParam;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 基础请求类
 * <AUTHOR>
 * @date 2021/11/4 18:52
 */
public class BaseRequest extends PageParam {

    /**
     * 企业编号列表（包含子级）
     */
    private List<Long> deptList;

    /**
     * 车辆列表
     */
    private List<Long> vehicleList;

    public List<Long> getDeptList() {
        return deptList;
    }

    public void setDeptList(List<Long> deptList) {
        this.deptList = deptList;
    }

    @ApiModelProperty(value = "企业编号")
    @JsonProperty("dept_id")
    private Long deptId;

    public Long getDeptId() {
        return deptId;
    }

    public void setDeptId(Long deptId) {
        this.deptId = deptId;
    }

    public List<Long> getVehicleList() {
        return vehicleList;
    }

    public void setVehicleList(List<Long> vehicleList) {
        this.vehicleList = vehicleList;
    }
}
