package com.xh.vdm.statistic.vo.response;

import com.xh.vdm.statistic.entity.DriftAndGoOnlineCountNode;
import lombok.Data;

import java.util.List;

/**
 * @Description: 漂移率返回对象
 * @Author: zhouxw
 * @Date: 2022/9/9 9:20 AM
 */
@Data
public class DriftRateResponse {

    //漂移和在线车辆
    private List<DriftAndGoOnlineCountNode> driftCountList;
    //总漂移车辆数
    int totalDrift;
    //总上线车辆数
    int totalGoOnline;
    //漂移率
    private double driftRate;
    //考核得分
    private double score;

}
