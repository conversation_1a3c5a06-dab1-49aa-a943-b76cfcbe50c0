package com.xh.vdm.statistic.vo.response.alarm;

import lombok.Data;

/**
 * 报警排行
 */
@Data
public class AlarmSortResponse {

	//车牌号
	private String licencePlate;
	//车牌颜色
	private Integer licenceColor;
	//统计日期
	private String date;
	//总报警数：超速+疲劳+夜间禁行+adas+dsm
	private Long totalAlarmCount;
	//超速报警数
	private Long overSpeedCount;
	//疲劳报警数
	private Long fatigueCount;
	//夜间禁行报警数
	private Long nightCount;
	//adas报警数
	private Long adasCount;
	//dsm报警数
	private Long dsmCount;
}
