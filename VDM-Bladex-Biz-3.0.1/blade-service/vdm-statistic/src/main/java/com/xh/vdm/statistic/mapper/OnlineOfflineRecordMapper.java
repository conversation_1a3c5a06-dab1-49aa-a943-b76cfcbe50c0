package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.OnlineOfflineRecord;
import com.xh.vdm.statistic.entity.VehicleOnlineBase;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/12 12:24
 */
public interface OnlineOfflineRecordMapper extends BaseMapper<OnlineOfflineRecord> {

	/**
	 * 查询车辆最后上线时间
	 * @param vehicleIds
	 * @return
	 */
	List<VehicleOnlineBase> getLastOnlineDate(@Param("vehicleIds") List<Integer> vehicleIds);
}
