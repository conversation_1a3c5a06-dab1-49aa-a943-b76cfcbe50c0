package com.xh.vdm.statistic.vo.response;

import lombok.Data;

/**
 * @Description: 驾驶员动态信息
 * @Author: zhouxw
 * @Date: 2022/11/18 4:56 PM
 */
@Data
public class DriverActiveInfo {

    //今日驾驶时长
    private String todayDriveDuration;
    //今日驾驶里程
    private Double todayDriveMileage;
	//今日超速报警次数
	private Integer todayOverSpeedAlarmCount;
	//今日疲劳报警次数
	private Integer todayFatigueAlarmCount;
	//今日ADAS报警次数
	private Integer todayAdasAlarmCount;
	//今日DSM报警次数
	private Integer todayDsmAlarmCount;

    //当月驾驶天数
    private Integer monthDriveDays;
	//当月驾驶时长
	private String monthDriveDuration;
	//当月驾驶里程
	private Double monthDriveMileage;
    //当月平均日驾驶时长
    private String monthAverageDriveDuration;
    //当月平均日驾驶里程
    private Integer monthAverageDriveMileage;
	//当月超速报警次数
	private Integer monthOverSpeedAlarmCount;
	//当月疲劳报警次数
	private Integer monthFatigueAlarmCount;
	//当月ADAS报警次数
	private Integer monthAdasAlarmCount;
	//当月DSM报警次数
	private Integer monthDSMAlarmCount;

}
