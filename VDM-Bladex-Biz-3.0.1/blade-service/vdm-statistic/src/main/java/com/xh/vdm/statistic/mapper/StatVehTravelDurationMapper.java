package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 车辆行驶时长表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface StatVehTravelDurationMapper extends BaseMapper<StatVehTravelDuration> {

    /**
     * @description: 按照统计日期删除统计数据
     * @author: zhouxw
     * @date: 2022/11/9 10:08 AM
     * @param: [month, day]
     * @return: void
     **/
    void deleteDataByDay(@Param("month") String month , @Param("day") String day);

    /**
     * @description: 按照统计日期批量保存数据
     * @author: zhouxw
     * @date: 2022/11/9 10:09 AM
     * @param: [day, list]
     * @return: void
     **/
    void saveBatchByDay(@Param("month") String month , @Param("list") List<StatVehTravelDuration> list);

    /**
     * @description: 根据条件查询车辆行驶的数量
     * @author: zhouxw
     * @date: 2022/11/9 3:38 PM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleTravelDurationNode>
     **/
    List<VehicleTravelDurationNode> getVehicleTranvelDurationCountByCondition(@Param("request") VehicleTravelDurationRequest request);

    /**
     * @description: 根据条件查询客运车辆夜间行驶情况
     * @author: zhouxw
     * @date: 2022/11/10 1:37 PM
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
     **/
    List<DateAndCount> getPassengerVehCountInNight(@Param("param")VehicleTravelDurationRequest request);

    /**
     * @description: 根据条件查询平均行驶时长
     * @author: zhouxw
     * @date: 2022/11/10 3:29 PM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndData>
     **/
    List<DateAndData> getAverageTravelDurationByDay(@Param("request")VehicleTravelDurationRequest request);

    /**
     * @description: 根据日期查询行驶车辆数
     * @author: zhouxw
     * @date: 2022/11/27 12:28 AM
     * @param: [date:yyyy-MM-dd , month:yyyyMM]
     * @return: int
     **/
    int getTravelVehicleCountByDate(@Param("month") String month , @Param("date") String date , @Param("deptId") Long deptId , @Param("vehicleOwnerId") Long vehicleOwnerId);

    /**
     * @description: 根据日期查询企业总行驶里程
     * @author: zhouxw
     * @date: 2022/11/27 12:55 AM
     * @param: [month, date]
     * @return: double
     **/
    double getCompanyTotalMileageByDate(@Param("month") String month , @Param("index") String dateIndex , @Param("deptId") Long deptId) ;

    /**
     * @description: 根据时间段查询每天车辆在线数
     * @author: zhouxw
     * @date: 2022/11/27 10:57 PM
     * @param: [month：yyyyMM, startDate: yyyy-MM-dd, endDate: yyyy-MM-dd]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
     **/
    List<DateAndCount> getVehicleOnlineCount(@Param("month") String month , @Param("startDate") String startDate , @Param("endDate") String endDate , @Param("deptId") Long deptId) ;

    /**
     * @description: 根据条件查询车辆行驶的总数
     * 共有多少条车辆上线运行记录（一辆车一天为一条记录）
     * 限定某个月内
     * @author: zhouxw
     * @date: 2022/11/29 4:05 PM
     * @param: [request]
     * @return: long
     **/
    int getVehicleTravelCountByDuration(@Param("request")VehicleTravelDurationRequest request);

    /**
     * @description: 查询在某个月内部门车辆行驶次数
     * @author: zhouxw
     * @date: 2022/11/29 4:45 PM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DeptAndDateAndData>
     **/
    List<DeptAndDateAndCount> getVehicleTravelCountForDept(@Param("request")VehicleTravelDurationRequest request);


}
