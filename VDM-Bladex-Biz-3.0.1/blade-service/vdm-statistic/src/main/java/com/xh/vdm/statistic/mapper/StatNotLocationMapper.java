package com.xh.vdm.statistic.mapper;

import com.xh.vdm.statistic.entity.StatNotLocation;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
public interface StatNotLocationMapper extends BaseMapper<StatNotLocation> {

	/**
	 * 查询不定位车辆数，指定日期
	 * @param deptList
	 * @param userId
	 * @param date yyyy-MM-dd
	 * @Param month yyyyMM
	 * @return
	 */
	long getNotLocationCount(@Param("deptList") List<Long> deptList, @Param("date") String date, @Param("month") String month);

	/**
	 * 查询不定位里程
	 * @param deptList
	 * @param date
	 * @param month
	 * @return
	 */
	double getNotLocationMileage (@Param("deptList") List<Long> deptList, @Param("date") String date, @Param("month") String month);
}
