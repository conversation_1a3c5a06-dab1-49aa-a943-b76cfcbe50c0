package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.CacheVehicleOnlineRate;
import com.xh.vdm.statistic.entity.VehicleBase;
import com.xh.vdm.statistic.entity.VehicleOnlineRateDateList;
import com.xh.vdm.statistic.vo.request.VehicleOnlineRateRequest;
import com.xh.vdm.statistic.vo.response.VehicleOnlineOrOfflineResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 车辆在线率DB缓存表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
public interface CacheVehicleOnlineRateMapper extends BaseMapper<CacheVehicleOnlineRate> {

    /**
     * @description: 根据条件查询车辆列表
     * 用于分页，只关注车牌号等基础信息
     * @author: zhouxw
     * @date: 2023-02-40 17:46:00
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    List<VehicleBase> getVehicleList(@Param("param") VehicleOnlineRateRequest param, String monthList);


    /**
     * @description: 根据条件分页查询车辆列表
     * @author: zhouxw
     * @date: 2023-02-40 17:46:00
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    IPage<VehicleBase> getVehicleList(IPage<VehicleOnlineOrOfflineResponse> page, @Param("param") VehicleOnlineRateRequest param, @Param("monthList") List<String> monthList);

	/**
	 * @description: 根据条件分页查询车辆列表 数据数量
	 * @author: zhouxw
	 * @date: 2023-02-40 17:46:00
	 * @param: []
	 * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
	 **/
	long getVehicleListCount(@Param("param") VehicleOnlineRateRequest param, @Param("monthList") List<String> monthList);


	/**
	 * @description: 根据条件分页查询车辆列表
	 * @author: zhouxw
	 * @date: 2023-02-40 17:46:00
	 * @param: []
	 * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
	 **/
	List<VehicleBase> getVehicles(@Param("param") VehicleOnlineRateRequest param, @Param("monthList") List<String> monthList);


	/**
     * @description: 根据条件查询车辆上线情况
     * @author: zhouxw
     * @date: 2023-03-67 16:33:42
     * @param: [vrd]
     * @return: java.util.List<com.xh.vdm.statistic.entity.CacheVehicleOnlineRate>
     **/
    List<CacheVehicleOnlineRate> getCacheVehicleOnlineRate(VehicleOnlineRateDateList vrd);


    /**
     * @description: 根据车牌号、车牌颜色、月份查询在线率缓存
     * @author: zhouxw
     * @date: 2023-03-69 11:09:40
     * @param: [list: 车牌号~车牌颜色~月份]
     * @return: java.util.List<com.xh.vdm.statistic.entity.CacheVehicleOnlineRate>
     **/
    List<CacheVehicleOnlineRate> getCacheVehicleOnlineRateWithCondition(Set<String> list);

}
