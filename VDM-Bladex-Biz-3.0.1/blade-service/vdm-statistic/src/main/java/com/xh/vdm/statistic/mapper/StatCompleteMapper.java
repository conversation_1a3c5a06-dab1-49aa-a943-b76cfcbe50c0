package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.DateAndComplete;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.response.LocationCompleteDetailResponse;
import com.xh.vdm.statistic.vo.response.LocationCompleteRateResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 轨迹完整率表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
public interface StatCompleteMapper extends BaseMapper<StatComplete> {

    /**
     * @description: 判断表是否存在
     * @author: zhouxw
     * @date: 2022/9/2 8:07 AM
     * @param: []
     * @return: void
     **/
    void checkExist(String month);

    /**
     * @description: 创建月表
     * @author: zhouxw
     * @date: 2022/9/2 8:11 AM
     * @param: [month]
     * @return: void
     **/
    void createTable(String month);

    /**
     * 完整率数据入临时表
     * @param licencePlate
     * @param data
     */
    void insertTempBatch(@Param("list") List<CompleteDataNode> list);

    /**
     * @description: 向轨迹完整率表中插入新增的数据
     * @author: zhouxw
     * @date: 2022/9/5 11:48 AM
     * @param: [month：月份，day：第几天]
     * @return: void
     **/
    void insertNewData(@Param("month") String month , @Param("day") String day);

    /**
     * @description: 更新轨迹完整率表中已经存在的数据
     * @author: zhouxw
     * @date: 2022/9/5 11:49 AM
     * @param: [month：月份，day：第几天]
     * @return: void
     **/
    void updateExistData(@Param("month") String month , @Param("day") String day);

    /**
     * @description: 创建 车辆已经存在 的车牌号临时表
     * @author: zhouxw
     * @date: 2022/9/5 3:28 PM
     * @param: [month]
     * @return: void
     **/
    void createLicencePlateTemp(@Param("month") String month);

    void insertLicencePlateTemp(@Param("month") String month);

    /**
     * @description: 查询轨迹完整率
     * @author: zhouxw
     * @date: 2022/9/8 1:59 PM
     * @param: [param]
     * @return: com.xh.vdm.statistic.vo.response.LocationCompleteRateResponse
     **/
    LocationCompleteRateResponse getLocationCompleteRate(RateParam param);

    /**
     * @description: 获取轨迹不完整的车辆完整率明细
     * @author: zhouxw
     * @date: 2022/9/8 4:30 PM
     * @param: [param]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.LocationCompleteDetailResponse>
     **/
    IPage<LocationCompleteDetailResponse> getUnCompleteDetailList(IPage page , @Param("param") DetailParam param);

    /**
     * @description: 根据部门id获取完整率
     * @author: zhouxw
     * @date: 2022/9/13 5:41 PM
     * @param: [month, deptIds]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleRateWithDept>
     **/
    List<VehicleRateWithDept> getCompleteRateByDeptId(@Param("month") String month , @Param("deptIds") List<Long> deptIds , @Param("ownerId") Long ownerId);


	/**
	 * @description: 根据部门id获取完整率
	 * 如果不指定企业ID，则查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 5:41 PM
	 * @param: [month, deptIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleRateWithDept>
	 **/
	VehicleRateWithDept getCompleteRateByDeptIdDeptOrArea(@Param("month") String month , @Param("deptId") Long deptId , @Param("ownerId") Long ownerId);


	/**
	 * 查询车辆轨迹完整率信息
	 * @param request
	 * @return
	 */
	List<CompleteMileageNode> getCompleteMileageMonth(@Param("request")CommonBaseCrossMonthRequest request, @Param("month") String month, @Param("dateList") List<String> dateList);

}
