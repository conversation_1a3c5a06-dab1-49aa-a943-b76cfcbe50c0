package com.xh.vdm.statistic.service.impl;

import com.xh.vdm.statistic.entity.TerminalModelAndCount;
import com.xh.vdm.statistic.entity.TerminalTypeAndCount;
import com.xh.vdm.statistic.mapper.TerminalMapper;
import com.xh.vdm.statistic.service.ITerminalService;
import com.xh.vdm.statistic.vo.TerminalBaseVO;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.StringUtils;
import org.springblade.entity.DataAuthCE;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2022/11/21 9:11 AM
 */
@Service
public class TerminalServiceImpl implements ITerminalService {

    @Resource
    private TerminalMapper mapper;

	@Resource
	private CETokenUtil ceTokenUtil;

    @Override
    public List<TerminalTypeAndCount> findTerminalTypeAndCount(Long deptId) {
        return mapper.getTerminalTypeAndCount(deptId);
    }

    @Override
    public List<TerminalModelAndCount> findTerminalModelAndCount(Long deptId) {
        return mapper.getTerminalModelAndCount(deptId);
    }

	@Override
	public Map<String, String> getTerminalMap () {
		List<Map<String, String>> terminalList = this.mapper.getTerminalList();
		Map<String, String> terminalMap = new HashMap<>();
		if ((terminalList == null) || terminalList.isEmpty()) {
			return terminalMap;
		}
		for (Map<String, String> terminal : terminalList) {
			terminalMap.put(terminal.get("phone"), terminal.get("terminal_type"));
		}

		return terminalMap;
	}

	@Override
	public List<TerminalBaseVO> findTerminalList(String deviceNum, Integer deviceType, String targetName, String deptIds, String account) {
		if(!StringUtils.isEmpty(deviceNum)){
			deviceNum = "%" + deviceNum + "%";
		}
		if(!StringUtils.isEmpty(targetName)){
			targetName = "%" + targetName + "%";
		}
		return mapper.getTerminals(deviceNum, deviceType, targetName, deptIds, account);
	}

	@Override
	public TerminalBaseVO findTerminalOne(String deviceNum) {
		DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
		if(!"1".equals(dataAuthCE.getGnDataAuthType())){
			//如果数据权限类型不是个人，则清空account数据
			dataAuthCE.setAccount(null);
		}
		return mapper.getTerminalOne(deviceNum, dataAuthCE.getOrgListStr(), dataAuthCE.getAccount());
	}

}
