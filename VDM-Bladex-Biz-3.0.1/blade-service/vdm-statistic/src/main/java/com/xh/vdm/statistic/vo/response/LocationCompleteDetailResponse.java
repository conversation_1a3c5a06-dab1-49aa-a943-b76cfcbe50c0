package com.xh.vdm.statistic.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 车辆轨迹明细
 * @Author: zhouxw
 * @Date: 2022/9/8 4:17 PM
 */
@Data
public class LocationCompleteDetailResponse {

    //企业名称
    private String deptName;
    //车牌号
    private String licencePlate;
    //车牌颜色
    @JsonProperty("licenceColor")
    private String plateColor;
    //SIM卡号
    private String simId;
    //终端ID
    private String terminalId;
    //终端型号
    private String terminalModel;
    //连续里程
    private double continousMileage;
    //总里程
    private double totalMileage;
    //完整率
    private String completeRate;
    //统计日期
    private String date;

}
