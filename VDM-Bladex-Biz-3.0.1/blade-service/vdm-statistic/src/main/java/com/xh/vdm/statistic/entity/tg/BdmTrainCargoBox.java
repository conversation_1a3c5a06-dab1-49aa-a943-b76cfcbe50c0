package com.xh.vdm.statistic.entity.tg;

import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;
import java.util.Date;

/**
 * (BdmTrainCargoBox)实体类
 */
@Data
public class BdmTrainCargoBox implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	@Compare("货车车厢编号")
	private String number;

	private Integer targetType;
	@Compare("箱型")
	private Integer model;
	@Compare("尺寸")
	private Integer size;
	@Compare("所属部门id")
	private Long deptId;
	@Compare("总重")
	private Float maxGross;
	@Compare("自重/皮重")
	private Float tare;
	@Compare("载重/净重")
	private Float net;
	@Compare("最大装货容积")
	private Float cuCap;
	@Compare("长度")
	private Integer length;
	@Compare("高度")
	private Integer height;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;
}

