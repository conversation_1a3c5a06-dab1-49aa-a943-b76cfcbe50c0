package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.LastOnline;
import com.xh.vdm.statistic.mapper.LastOnlineMapper;
import com.xh.vdm.statistic.service.ILastOnlineService;
import org.springframework.stereotype.Service;

@Service
public class LastOnlineServiceImpl extends ServiceImpl<LastOnlineMapper, LastOnline> implements ILastOnlineService {

	@Override
	public void insertFromAll (String startTime, String endTime) {
		this.baseMapper.insertFromAll(startTime, endTime);
	}

	@Override
	public void truncate () {
		this.baseMapper.truncate();
	}
}
