package com.xh.vdm.statistic.service;


import com.xh.vdm.statistic.entity.FatigueWithDeptAndDate;

import java.util.List;

/**
 * @Description: 疲劳驾驶统计
 * @Author: zhouxw
 * @Date: 2022/11/15 10:03 PM
 */
public interface IStatFatigueService {

    /**
     * @description: 根据 企业id 和 月份 统计当月的平均疲劳驾驶时长
     * 如果没有指定企业，那么就分页查询所有企业
     * @author: zhouxw
     * @date: 2022/11/15 11:32 PM
     * @param: [deptId, month, count 分页时每页的数量, start 分页开始的下标]
     * @return: java.util.List<com.xh.vdm.statistic.entity.OverSpeedWithDeptAndDate>
     **/
    List<FatigueWithDeptAndDate> statFatigueAverageSecond(Long deptId , String month, int count , int start) throws Exception;

    /**
     * @description: 根据 企业id 和 月份 查询当月的平均疲劳驾驶时长
     * //如果没有指定企业，就查询区域
     * @author: zhouxw
     * @date: 2022/11/15 11:32 PM
     * @param: [deptId, month, count 分页时每页的数量, start 分页开始的下标]
     * @return: java.util.List<com.xh.vdm.statistic.entity.OverSpeedWithDeptAndDate>
     **/
    FatigueWithDeptAndDate findFatigueAverageSecond(Long deptId , Long startSecondTimestamp , Long endSecondTimestamp) throws Exception;
}
