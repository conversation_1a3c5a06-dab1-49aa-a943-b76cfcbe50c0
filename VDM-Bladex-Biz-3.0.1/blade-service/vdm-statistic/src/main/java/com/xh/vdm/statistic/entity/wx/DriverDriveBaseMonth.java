package com.xh.vdm.statistic.entity.wx;

import lombok.Data;

/**
 * 当月驾驶情况
 */
@Data
public class DriverDriveBaseMonth {

	//驾驶时长
	private String driveDuration;
	//驾驶里程
	private Double driveMileage;
	//驾驶天数
	private Integer driveDaysCount;
	//日平均驾驶时长(分钟)
	private Double averageDuration;
	//日平均驾驶里程
	private Double averageMileage;
	//adas报警数量
	private Long adasAlarmCount;
	//dsm报警数量
	private Long dsmAlarmCount;
	//疲劳报警数量
	private Long fatigueAlarmCount;
	//超速报警数量
	private Long overSpeedAlarmCount;
}
