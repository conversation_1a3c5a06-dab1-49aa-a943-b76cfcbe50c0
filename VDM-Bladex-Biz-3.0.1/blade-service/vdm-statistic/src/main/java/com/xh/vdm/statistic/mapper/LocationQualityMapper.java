package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.LocationQualityResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 定位数据质量表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
public interface LocationQualityMapper extends BaseMapper<LocationQuality> {

    /**
     * @description: 将数据保存当相应的月表中
     * @author: zhouxw
     * @date: 2022/9/1 5:54 PM
     * @param: [day, list]
     * @return: void
     **/
    void saveBatchMonth(@Param("month") String month , @Param("list") List<LocationQuality> list);

    /**
     * @description: 判断表是否存在
     * @author: zhouxw
     * @date: 2022/9/2 8:07 AM
     * @param: []
     * @return: void
     **/
    void checkExist(String month);

    /**
     * @description: 创建月表
     * @author: zhouxw
     * @date: 2022/9/2 8:11 AM
     * @param: [month]
     * @return: void
     **/
    void createTable(String month);

    /**
     * @description: 删除指定日期的数据
     * @author: zhouxw
     * @date: 2022/9/2 10:09 AM
     * @param: []
     * @return: void
     **/
    void deleteDataByDay(@Param("month") String month , @Param("day") String day);


    /**
     * @description: 查询数据合格率
     * @author: zhouxw
     * @date: 2022/9/6 4:17 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.vo.response.LocationQualityResponse
     **/
    LocationQualityResponse getLocationQualityRate(RateRequest request);

    /**
     * @description: 根据条件获取轨迹异常总条数
     * 如果指定天，则查询当天的总异常条数；否则查询整月的
     * @author: zhouxw
     * @date: 2022/9/9 3:01 PM
     * @param: [param]
     * @return: int
     **/
    int getTotalErrorCount(@Param("param") DetailDayParam param);

    /**
     * @description: 根据条件查询轨迹异常车辆所在的日期和车牌号
     * @author: zhouxw
     * @date: 2022/9/9 3:34 PM
     * @param: [param]
     * @return: java.util.List<java.lang.String>
     **/
    List<DayAndLicencePlate> getStatDateWithLocationError(DetailParam param);

    /**
     * @description: 根据条件查询数据质量信息
     * @author: zhouxw
     * @date: 2022/9/9 4:52 PM
     * @param: [param]
     * @return: java.util.List<com.xh.vdm.statistic.entity.LocationQuality>
     **/
    List<LocationQuality> getLocationQualityByCondition(@Param("param") DetailParam param);

    /**
     * @description: 根据条件查询不合格数据质量信息
     * @author: zhouxw
     * @date: 2022/9/13 8:51 AM
     * @param: [param]
     * @return: java.util.List<com.xh.vdm.statistic.entity.LocationQuality>
     **/
    List<LocationQuality> getLocationQualityWithLocationError(@Param("param") DetailParam param);

    /**
     * @description: 根据企业id获取数据合格率
     * @author: zhouxw
     * @date: 2022/9/13 5:53 PM
     * @param: [month, deptIds]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleRateWithDept>
     **/
    List<VehicleRateWithDept> getQualityRateByDeptId(@Param("month") String month , @Param("deptIds") List<Long> deptIds , @Param("ownerId") Long ownerId);

	/**
	 * @description: 根据企业id获取数据合格率
	 * 如果不指定 企业Id，就查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 5:53 PM
	 * @param: [month, deptIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleRateWithDept>
	 **/
	VehicleRateWithDept getQualityRateByDeptIdDeptOrArea(@Param("month") String month , @Param("deptId") Long deptId , @Param("ownerId") Long ownerId);


	/**
	 * 根据条件查询数据质量信息
	 * @param request
	 * @return
	 */
	List<VehicleAndQuality> getQualityByCondition(@Param("request")CommonBaseCrossMonthRequest request);
}
