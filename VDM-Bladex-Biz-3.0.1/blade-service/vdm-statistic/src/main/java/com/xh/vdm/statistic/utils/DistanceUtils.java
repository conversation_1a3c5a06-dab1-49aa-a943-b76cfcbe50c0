package com.xh.vdm.statistic.utils;

/**
 * <AUTHOR>
 * @date 2019/10/27.
 * @description
 */
public class DistanceUtils {

    /**
     * 计算地球上任意两点(经纬度)距离
     *
     * @param long1 第一点经度
     * @param lat1  第一点纬度
     * @param long2 第二点经度
     * @param lat2  第二点纬度
     * @return 返回距离 单位：米
     */
    public static double wgs84Distance(double long1, double lat1, double long2,
                                       double lat2) {
        double a, b, r;
        // 地球半径
        r = 6378137;
        lat1 = lat1 * Math.PI / 180.0;
        lat2 = lat2 * Math.PI / 180.0;
        a = lat1 - lat2;
        b = (long1 - long2) * Math.PI / 180.0;
        double d;
        double sa2, sb2;
        sa2 = Math.sin(a / 2.0);
        sb2 = Math.sin(b / 2.0);
        d = 2
                * r
                * Math.asin(Math.sqrt(sa2 * sa2 + Math.cos(lat1)
                * Math.cos(lat2) * sb2 * sb2));
        return d;
    }

    /**
     * @param long1   第一点经度
     * @param lat1    第一点纬度
     * @param height1 第一点高程
     * @param long2   第二点经度
     * @param lat2    第二点纬度
     * @param height2 第二点高程
     * @return
     * <AUTHOR>
     * @date 2019/11/5
     * 根据经纬度和高程计算地球上任意两点的距离
     */
    public static double distanceOfHeight(double long1, double lat1, double height1, double long2,
                                          double lat2, double height2) {
        double d = wgs84Distance(long1, lat1, long2, lat2);
        double h = Math.abs(height1 - height2);
        double result = Math.sqrt(d * d + h * h);
        return result;
    }

    /*public static void main(String[] args) {
        System.out.println(wgs84Distance(
                108.953475, 34.2675,
                108.953475, 34.2676
        ));

        System.out.println(distanceOfHeight(
                108.953475, 34.2675, 22,
                108.953475, 34.2676, 9
        ));
    }*/
}
