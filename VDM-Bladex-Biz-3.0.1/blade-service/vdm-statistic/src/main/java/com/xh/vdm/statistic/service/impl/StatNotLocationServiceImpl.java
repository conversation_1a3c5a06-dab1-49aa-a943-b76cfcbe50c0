package com.xh.vdm.statistic.service.impl;

import com.xh.vdm.statistic.entity.StatNotLocation;
import com.xh.vdm.statistic.mapper.StatNotLocationMapper;
import com.xh.vdm.statistic.service.IStatNotLocationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
@Service
public class StatNotLocationServiceImpl extends ServiceImpl<StatNotLocationMapper, StatNotLocation> implements IStatNotLocationService {

	@Override
	public long findNotLocationCount(List<Long> deptIds,  String date) throws Exception {
		String month = date.substring(0,7).replace("-","");
		return baseMapper.getNotLocationCount(deptIds, date, month);
	}

	@Override
	public double findNotLocationMileage(List<Long> deptIds,  String date) throws Exception {
		String month = date.substring(0,7).replace("-","");
		return baseMapper.getNotLocationCount(deptIds, date, month);
	}
}
