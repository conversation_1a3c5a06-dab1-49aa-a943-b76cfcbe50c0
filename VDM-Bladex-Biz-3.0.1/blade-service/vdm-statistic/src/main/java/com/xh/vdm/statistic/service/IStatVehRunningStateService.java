package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.StatVehRunningStateDay;
import com.xh.vdm.statistic.entity.StatVehRunningStateMonth;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.vo.request.*;
import com.xh.vdm.statistic.vo.response.*;
import com.xh.vdm.statistic.vo.response.alarm.AlarmSortResponse;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
public interface IStatVehRunningStateService extends IService<StatVehRunningStateMonth> {

	/**
	 * 分页统计车辆运行情况--按月
	 * @param request
	 * @param query
	 * @return
	 */
	IPage<StatVehRunningStateMonth> findRunningStateByPage(VehRunningStateRequest request, Query query, List<Long> deptIds, Long userId) throws Exception;

	/**
	 * 分页统计车辆运行情况--按天
	 * @param request
	 * @param query
	 * @return
	 */
	IPage<StatVehRunningStateDay> findRunningStateDayByPage(VehRunningStateDayRequest request, Query query, List<Long> deptIds, Long userId) throws Exception;

	/**
	 * 报警排行，分页
	 * @param query
	 * @param deptIds
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	List<AlarmSortResponse> findAlarmSortInfoToday(Query query, List<Long> deptIds, List<Integer> vehicleIds, BladeUser user) throws Exception;


	/**
	 * 统计车辆运行综合情况
	 * 该跑批改为使用 spark 跑批
	 * @param day
	 * @return
	 */
	//boolean statVehRunningState(String day) throws Exception;


	/**
	 * 车辆最终定位点信息统计
	 * @param request
	 * @param query
	 * @return
	 * @throws Exception
	 */
	IPage<VehicleLastPositionResponse> findLastPositionInfo(CommonBaseRequest request, Query query) throws Exception;

	/**
	 * 查询在线不定位信息
	 * @param request
	 * @param query
	 * @return
	 * @throws Exception
	 */
	IPage<NotLocationResponse> findNotLocationInfo(NotLocationRequest request, Query query) throws Exception;


	/**
	 * 查询异常定位信息
	 * @param request
	 * @param query
	 * @return
	 * @throws Exception
	 */
	IPage<ErrLocationMoveResponse> findErrLocationMoveInfo(CommonBaseCrossMonthRequest request, Query query) throws Exception;


	/**
	 * 查询车辆日里程
	 * @param request
	 * @param query
	 * @return
	 * @throws Exception
	 */
	List<Map<String,Object>> findVehicleMileageDay(CommonBaseCrossMonthWithLineRequest request, Query query) throws Exception;

	/**
	 * 查询车辆日运行统计情况
	 * @return
	 * @throws Exception
	 */
	IPage<CompanyAllStatInfoResponse> findCompanyAllInfo(CompanyAndDate cd, Query query, BladeUser user) throws Exception;

	/**
	 * 轨迹完整率统计
	 * @return
	 * @throws Exception
	 */
	IPage<CompanyAllRateResponse> findCompanyAllRateInfo(CommonBaseCrossMonthRequest request, Query query, BladeUser user) throws Exception;

	/**
	 * 根据部门和关联车辆id查询车辆运行情况
	 * @param deptIds
	 * @param vehicleIds
	 * @param month yyyyMM
	 * @param date yyyyMMdd
	 * @return
	 * @throws Exception
	 */
	List<StatVehRunningStateDay> findStateInfoByDeptWithAlarm(String month, String date, List<Long> deptIds, List<Integer> vehicleIds) throws Exception;


	/**
	 * 按照月份、车组、关联车辆，统计报警总体情况
	 * @param month
	 * @param deptIds
	 * @param vehicleIds
	 * @return
	 * @throws Exception
	 */
	List<StatVehRunningStateDay> findStateInfoByDeptWithAlarmMonth(String month, List<Long> deptIds, List<Integer> vehicleIds) throws Exception;

}
