package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.CacheVehicleOnlineOrOfflineMapper;
import com.xh.vdm.statistic.mapper.StatisticsMapper;
import com.xh.vdm.statistic.service.IBamThirdPartyPlatformService;
import com.xh.vdm.statistic.service.IBladeDeptService;
import com.xh.vdm.statistic.service.ICacheVehicleOnlineOrOfflineService;
import com.xh.vdm.statistic.utils.CacheUtil;
import com.xh.vdm.statistic.utils.CommonBusiUtil;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthDurationRequest;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.response.VehicleOnlineOrOfflineResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.*;

/**
 * 车辆地图超速服务类
 *
 * <AUTHOR>
 * @since 2022-09-14
 */
@Service
@Slf4j
public class CacheVehicleOnlineOrOfflineServiceImpl extends ServiceImpl<CacheVehicleOnlineOrOfflineMapper, CacheVehicleOnlineOrOfflineResponse> implements ICacheVehicleOnlineOrOfflineService {

	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(20, 50, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

    @Resource
    private StatisticsMapper statisticsMapper;

    @Resource
    private CacheVehicleOnlineOrOfflineMapper mapper;

	@Resource
	private DataSource dataSource;

	@Resource
	private IBamThirdPartyPlatformService thirdPartyPlatformService;

	@Resource
	private CommonBusiUtil commonBusiUtil;

	@Resource
	private IBladeDeptService bladeDeptService;

    @Resource
    private CacheUtil cacheUtil;

    @Override
    //@Transactional 此处不能使用事务，否则不能获取jdbc连接
    public void statisticsVehicleOnlineOrOffline(Long startTime, Long endTime, String tenantId) throws Exception {

        Date startDate = new Date();
        startDate.setTime(startTime );
        Date endDate = new Date();
        endDate.setTime(endTime );

        //1.删除指定时间段的数据
        int count = mapper.delete(Wrappers.lambdaQuery(CacheVehicleOnlineOrOfflineResponse.class).le(CacheVehicleOnlineOrOfflineResponse::getOnLineTime, endDate).ge(CacheVehicleOnlineOrOfflineResponse::getOnLineTime, startDate));
        log.info("删除指定时间段的数据成功，共删除{}条数据",count);


		CountDownLatch countDownLatch = new CountDownLatch(1);

		//查询部门信息、上级平台信息，用于后续手动增加字段
		Map<String, BladeDept> deptMap = new HashMap<>();
		Map<String,BamThirdPartyPlatform> platMap = new HashMap<>();
		threadPool.submit(() -> {
			try{
				List<BladeDept> depts = null;
				List<BamThirdPartyPlatform> platList = null;
				platList = thirdPartyPlatformService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled, 0));
				depts = bladeDeptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
				platList.forEach(item -> {
					platMap.put(item.getId()+"", item);
				});
				depts.forEach(item -> {
					deptMap.put(item.getId()+"", item);
				});
			}catch (Exception e){
				log.error("异步处理失败",e);
			}finally {
				countDownLatch.countDown();
			}
		});




        //2.统计指定时间段的数据
		//获取查询时间段和日期
		List<DateDurationAndMonth> dmList = new ArrayList<>();
		List<DateListAndMonth> dateList = DateUtil.getDateListAndMonth(startTime/1000, endTime/1000);
		for(DateListAndMonth dm : dateList){
			DateDurationAndMonth ddm = new DateDurationAndMonth();
			ddm.setMonth(dm.getMonth().replace("-",""));
			ddm.setDurationStartTime(DateUtil.getDateByDateString(dm.getDateList().get(0)+" 00:00:00"));
			ddm.setDurationEndTime(DateUtil.getDateByDateString(dm.getDateList().get(dm.getDateList().size()-1)+" 23:59:59"));
			dmList.add(ddm);
		}
		CommonBaseCrossMonthDurationRequest req = new CommonBaseCrossMonthDurationRequest();
		req.setTenantId(tenantId);
		req.setDmList(dmList);

		//查询基础数据
        List<CacheVehicleOnlineOrOfflineResponse> list = statisticsMapper.vehicleOnlineOrOfflineWithTimeMonth(req);
        //添加创建时间
        for(CacheVehicleOnlineOrOfflineResponse cache : list){
            cache.setCreateTime(new Date());
        }

		Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
		Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
		Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
		Map<String, String> teStateMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ONLINE_STATE);

		countDownLatch.await();

		//添加字段
		//添加字段信息
		list.forEach(item -> {
			Long deptId = item.getDeptId();
			Long vehicleOwnerId = item.getVehicleOwnerId();
			BladeDept dept = deptMap.get(deptId+"");
			BamThirdPartyPlatform plat = platMap.get(vehicleOwnerId+"");
			if(dept != null){
				item.setDeptName(dept.getDeptName());
			}
			if(plat != null){
				item.setVehicleOwner(StringUtils.isEmpty(plat.getName())?"非营运车辆":plat.getName());
			}else{
				item.setVehicleOwner("非营运车辆");
			}

			//增加 licenceColorDesc 和 vehicleUseTypeDesc
			item.setAccessMode(accessModeMap.get(item.getAccessModeCode()));
			item.setVehicleUseType(vehicleUseTypeMap.get(item.getVehicleUseTypeCode()+""));
			item.setLicenceColor(licenceColorMap.get(item.getLicenceColorCode()));
			item.setTeState(teStateMap.get(item.getTeState()));

		});

        //boolean saveFlag = this.saveBatch(list);
		try {
			log.info("将要执行数据批量保存操作");
			long cnt = saveBatchWithJDBC(list);
			log.info("统计数据保存成功，共保存{}条数据",cnt);
		}catch (Exception e){
			log.error("统计数据保存失败",e);
		}
    }


	private long saveBatchWithJDBC(List<CacheVehicleOnlineOrOfflineResponse> list){
		String sql = "insert into cache_vehicle_online_offline (dept_id, licence_color_code, vehicle_use_type_code, vehicle_owner_id, access_mode_code , create_time, dept_name, licence_plate, licence_color, vehicle_use_type , vehicle_owner, te_state, on_line_time, off_line_time, driver , vehicle_id) " +
			"values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
		Connection connection = null;
		PreparedStatement stmt = null;
		try {
			connection = dataSource.getConnection();
			stmt = connection.prepareStatement(sql);
			// 关闭事务的自动提交
			connection.setAutoCommit(false);
			for(int i = 0; i < list.size(); i++){
				CacheVehicleOnlineOrOfflineResponse data = list.get(i);
				try {
					stmt.setLong(1, data.getDeptId());
				}catch (Exception e){}
				try {
					stmt.setString(2, data.getLicenceColorCode());
				}catch (Exception e){}
				try{
					stmt.setLong(3, data.getVehicleUseTypeCode());
				}catch (Exception e){}
				try{
					stmt.setLong(4, data.getVehicleOwnerId());
				}catch (Exception e){}
				try{
					stmt.setString(5, data.getAccessModeCode());
				}catch (Exception e){}
				try{
					stmt.setDate(6, new java.sql.Date(data.getCreateTime().getTime()));
				}catch (Exception e){}
				try{
					stmt.setString(7, data.getDeptName());
				}catch (Exception e){}
				try{
					stmt.setString(8, data.getLicencePlate());
				}catch (Exception e){}
				try{
					stmt.setString(9, data.getLicenceColor());
				}catch (Exception e){}
				try{
					stmt.setString(10, data.getVehicleUseType());
				}catch (Exception e){}
				try{
					stmt.setString(11, data.getVehicleOwner());
				}catch (Exception e){}
				try{
					stmt.setString(12, data.getTeState());
				}catch (Exception e){}
				try{
					stmt.setTimestamp(13, (data.getOnLineTime() == null) ? null : new java.sql.Timestamp(data.getOnLineTime().getTime()));
				}catch (Exception e){}
				try{
					stmt.setTimestamp(14, (data.getOffLineTime() == null) ? null : new java.sql.Timestamp(data.getOffLineTime().getTime()));
				}catch (Exception e){}
				try {
					stmt.setString(15, data.getDriver());
				}catch (Exception e){}
				try {
					stmt.setLong(16, data.getVehicleId());
				}catch (Exception e){}
				stmt.addBatch();
				if(i > 0 && i % 1000==0){
					stmt.executeBatch();
					connection.commit();
				}
			}

			// 执行批处理
			stmt.executeBatch();
			// 事务提交
			connection.commit();
		} catch (Exception e) {
			e.printStackTrace();
			return -1;
		}finally {
			if(stmt != null){
				try {
					stmt.close();
				} catch (SQLException e) {
					log.info("自定义批量提交，关闭stmt失败",e);
				}
			}
			if(connection != null){
				try {
					connection.setAutoCommit(true);
					connection.close();
				} catch (SQLException e) {
					log.info("自定义批量提交，关闭连接失败",e);
				}

			}
		}
		return list.size();
	}


    @Override
    public IPage<VehicleOnlineOrOfflineResponse> queryVehicleOnlineOrOfflineCache(CommonBaseRequest request, Query query) throws Exception {

        //1.查询车辆上下线查询信息
		IPage<VehicleOnlineOrOfflineResponse> page = new Page<>(query.getCurrent(), query.getSize());
        IPage<VehicleOnlineOrOfflineResponse> pageList = mapper.getList(request, page);
		if (pageList.getTotal() <= 0) {
			return pageList;
		}

        //2.更新车辆在线状态
        //注意：报表中的在线状态，指的是车辆当前时刻的在线情况，是实时的数据，所以要对查询出的车辆获取对应的在线状态(bdm_vehicle_state)
        //key:颜色名称  value：颜色值
        Map<String,String> colorValueMap = cacheUtil.getVehicleColorValueMap();
        Map<String,String> stateMap = cacheUtil.getTerminalStateMap();
        Map<String,List<VehicleOnlineOrOfflineResponse>> map = new HashMap<>();
        List<String> params = new ArrayList<>();
        pageList.getRecords().forEach(item -> {
            List<VehicleOnlineOrOfflineResponse> list = map.get(item.getLicencePlate()+"~"+colorValueMap.get(item.getLicenceColor()));
            if(list == null){
                list = new ArrayList<>();
            }
            list.add(item);
            map.put(item.getLicencePlate()+"~"+colorValueMap.get(item.getLicenceColor()), list);
            params.add(item.getLicencePlate()+"~"+colorValueMap.get(item.getLicenceColor()));
        });
        //查询车辆实时在线状态
        List<TeState> TeStates = mapper.getVeState(params);
        //更新实时在线状态
        for (TeState teState : TeStates) {
            List<VehicleOnlineOrOfflineResponse> list = map.get(teState.getLicencePlate()+"~" + teState.getLicenceColor());
            list.forEach(item -> {
                item.setTeState(stateMap.get(teState.getTeState()+""));
            });
        }
        return pageList;
    }

}
