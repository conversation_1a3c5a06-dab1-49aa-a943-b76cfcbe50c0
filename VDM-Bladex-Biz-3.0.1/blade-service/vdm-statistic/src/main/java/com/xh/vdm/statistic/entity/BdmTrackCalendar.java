package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "bdm_track_calendar", autoResultMap = true)
public class BdmTrackCalendar implements Serializable {

	@TableField(value = "id")
	private Long id;

	@TableField(value = "target_type")
	private Byte targetType;

	@TableField(value = "target_id")
	private Long targetId;

	@TableField(value = "device_type")
	private Byte deviceType;

	@TableField(value = "device_id")
	private Long deviceId;

	@TableField(value = "month")
	private Date month;

	@TableField(value = "mark")
	private Integer mark;
}
