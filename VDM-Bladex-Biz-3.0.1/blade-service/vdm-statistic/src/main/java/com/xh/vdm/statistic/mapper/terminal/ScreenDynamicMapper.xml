<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.terminal.ScreenDynamicMapper">

    <sql id="base_dept">
        <if test="userId != null">
            AND EXISTS (
            SELECT 1
            FROM bdm_user_dept_regulates usr
            WHERE usr.dept_id = ${tableAlias}.dept_id
            AND usr.user_id = #{userId}
            )
        </if>
    </sql>

    <select id="count" resultType="java.lang.Long">
        select
            (select count(*) from bdm_wearable_device wd where deleted = 0
        <if test="userId != null">
            <include refid="base_dept">
                <property name="tableAlias" value="wd"/>
            </include>
        </if>)
            +
            (select count(*) from bdm_rnss_device rd where deleted = 0
        <if test="userId != null">
            <include refid="base_dept">
                <property name="tableAlias" value="rd"/>
            </include>
        </if>)
            +
            (select count(*) from bdm_rdss_device dd where deleted = 0
        <if test="userId != null">
            <include refid="base_dept">
                <property name="tableAlias" value="dd"/>
            </include>
        </if>)
            +
            (select count(*) from bdm_pnt_device pd where deleted = 0
        <if test="userId != null">
            <include refid="base_dept">
                <property name="tableAlias" value="pd"/>
            </include>
        </if>)
            +
            (select count(*) from bdm_monit_device md where deleted = 0
        <if test="userId != null">
            <include refid="base_dept">
                <property name="tableAlias" value="md"/>
            </include>
        </if>) as total;
    </select>

</mapper>
