package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmCallStopChangeRule implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 报警规则分类id
     */
    private Integer ruleTypeId;

    /**
     * 是否报警（0：否 1：是）
     */
    private Integer isAlarm;

    /**
     * 位移距离（单位：米）
     */
    private Double displacementDistance;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    private String duration;

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    private String remark;

    private Integer isDel;

    private Date createTime;

    private Date updateTime;

    private String createDeptIdStr;

    private String createUserName;


}
