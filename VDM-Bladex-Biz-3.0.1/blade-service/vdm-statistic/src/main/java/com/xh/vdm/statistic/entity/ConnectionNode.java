package com.xh.vdm.statistic.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * 平台连通率
 */
@Data
public class ConnectionNode {

    //上级平台名称
    @JSONField(name = "upper_platform")
    private String upperPlateform;

    //总时长
    @JSONField(name = "total_time")
    private long totalTime;

    //上线时长
    @JSONField(name = "online_time")
    private long onlineTime;

    //断开时长
    @JSONField(name = "disconnection_time")
    private long disconnectionTime;


    //断开次数
    @JSONField(name = "disconnection_num")
    private long disconnectionNum;


    //连通率
    private double connectivity;

    //连通率分数
    private double fraction;

}
