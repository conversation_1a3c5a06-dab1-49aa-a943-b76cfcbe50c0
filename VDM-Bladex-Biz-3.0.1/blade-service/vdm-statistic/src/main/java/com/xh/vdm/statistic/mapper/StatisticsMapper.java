package com.xh.vdm.statistic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.vo.request.*;
import com.xh.vdm.statistic.vo.response.*;
import org.apache.ibatis.annotations.*;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/10/26 14:29
 */
public interface StatisticsMapper  {

    /**
     * 查询所有分段限速报警信息 地图平台
     *
     * <AUTHOR>
     * @param segLimitSpeedMapRequest 查询所有分段限速报警信息 地图平台
     * @return
     */
    List<SegLimitSpeedMapResponse> getSegLimitSpeedMap(@Param("current") Integer current, @Param("size") Integer size, @Param("segLimitSpeedMapRequest") SegLimitSpeedMapRequest segLimitSpeedMapRequest);

    /**
     * @description: 根据开始时间和结束时间统计车辆地图超速信息
     * @author: zhouxw
     * @date: 2023-02-40 16:29:15
     * @param: [startTime, endTime]
     * @return: java.util.List<com.xh.vdm.statistic.entity.CacheSegLimitSpeedMapResponse>
     **/
    List<CacheSegLimitSpeedMapResponse> getSegLimitSpeedMapWithTime(Long startTime, Long endTime);

    /**
     * 查询所有分段限速报警信息 地图平台
     *
     * <AUTHOR>
     * @param segLimitSpeedMapRequest 查询所有分段限速报警信息 地图平台
     * @return
     */
    long getSegLimitSpeedMapCount(@Param("segLimitSpeedMapRequest") SegLimitSpeedMapRequest segLimitSpeedMapRequest);

    /**
     * 查询所有分段限速报警信息 终端
     *
     * <AUTHOR>
     * @param segLimitSpeedTerminalRequest 交通事故请求对象
     * @return IPage<SysOrganizationInfo>
     */
    List<SegLimitSpeedTerminalResponse> getSegLimitSpeedTerminal(@Param("current") Integer current, @Param("size") Integer size, @Param("segLimitSpeedTerminalRequest") SegLimitSpeedTerminalRequest segLimitSpeedTerminalRequest);

    /**
     * @description: 按照开始时间和结束时间统计终端限速信息
     * @author: zhouxw
     * @date: 2023-02-44 15:45:52
     * @param: [startTime, endTime]
     * @return: java.util.List<com.xh.vdm.statistic.entity.CacheSegLimitSpeedTerminalResponse>
     **/
    List<CacheSegLimitSpeedTerminalResponse> getSegLimitSpeedTerminalWithTime(Long startTime, Long endTime);


    /**
     * 查询所有分段限速报警信息 终端
     *
     * <AUTHOR>
     * @param segLimitSpeedTerminalRequest 交通事故请求对象
     * @return IPage<SysOrganizationInfo>
     */
    long getSegLimitSpeedTerminalCount(@Param("segLimitSpeedTerminalRequest") SegLimitSpeedTerminalRequest segLimitSpeedTerminalRequest);


    /**
     * 查询组织机构
     *
     * <AUTHOR>
     * @param deptId           本级组织机构
     * @return IPage<SysOrganizationInfo>
     */
    IPage<BladeDept> getDept(@Param("deptIds") List<Long> deptIds, @Param("deptId") String deptId, IPage<BladeDept> page);

	/**
	 * 查找子部门
	 * @param deptId
	 * @return
	 */
	List<BladeDept> getChildrenDept(@Param("deptId") Long deptId);

    /**
     * 查询车辆在线情况抽查，单个企业
     *
     * <AUTHOR>
     * @return IPage<SysOrganizationInfo>
     */
    VehicleOnlineResponse getVehicleOnline(@Param("deptIdList") List<Long> deptIdList, @Param("deptId") Long deptId);

    /**
     * 离线报表
     *
     * <AUTHOR>
     * @param page           分页参数
     * @param vehicleOfflineRequest 交通事故请求对象
     * @return IPage<SysOrganizationInfo>
     */
    Page<VehicleOfflineResponse> getVehicleOfflinePage(Page page, @Param("vehicleOfflineRequest") VehicleOfflineRequest vehicleOfflineRequest);


    /**
     * 查询车辆运行情况巡检
     *
     * <AUTHOR>
     * @param page           分页参数
     * @param request 查询车辆运行情况巡检
     * @return IPage
     */
    IPage<VehicleOperationResponse> vehicleOperation(IPage<VehicleOperationResponse> page, @Param("request") CommonBaseRequest request, @Param("tenantId") String tenantId);

    /**
     * @description: 查询车辆运行情况巡检，不包含最后上线时间
     * @author: zhouxw
     * @date: 2023-02-46 18:27:51
     * @param: [page, vehicleOperationRequest]
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.VehicleOperationResponse>
     **/
    Page<VehicleOperationResponse> vehicleOperationWithoutLastOnlineTime(Page page, @Param("vehicleOperationRequest") VehicleOperationRequest vehicleOperationRequest);

    /**
     * @description: 根据给定的车辆列表查询车辆最后上线时间
     * @author: zhouxw
     * @date: 2023-02-46 18:14:31
     * @param: [licencePlateList]
     * @return: java.util.List<com.xh.vdm.statistic.entity.LastOnlineTimeEntity>
     **/
    List<LastOnlineTimeEntity> getLastOnlineTime(List<String> licencePlateList);

    /**
     * 查询车辆离线
     *
     * <AUTHOR>
     * @param vehicleOnlineOrOfflineRequest 查询车辆离线请求类
     * @return IPage<SysOrganizationInfo>
     */
    List<VehicleOnlineOrOfflineResponse> vehicleOnlineOrOffline(@Param("current") Integer current, @Param("size") Integer size, @Param("request") CommonBaseRequest request, @Param("tenantId") String tenantId);





    /**
     * 根据时间段查询车辆离线
     *
     * <AUTHOR>
     * @return IPage<SysOrganizationInfo>
     */
    List<CacheVehicleOnlineOrOfflineResponse> vehicleOnlineOrOfflineWithTime(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("tenantId") String tenantId);


	/**
	 * 根据时间段查询车辆离线
	 * 该方法只查询基础信息，字典信息、部门信息等，在外层的代码中添加
	 * <AUTHOR>
	 * @return IPage<SysOrganizationInfo>
	 */
	List<CacheVehicleOnlineOrOfflineResponse> vehicleOnlineOrOfflineWithTimeMonth(@Param("request") CommonBaseCrossMonthDurationRequest request);



    /**
     * 查询车辆离线
     *
     * <AUTHOR>
     * @return IPage<SysOrganizationInfo>
     */
    long vehicleOnlineOrOfflineCount(@Param("request") CommonBaseRequest request);



    /**
     * 车辆夜间违规行车报表
     *
     * <AUTHOR>
     * @param nightDrivingRequest 车辆夜间违规行车请求类
     * @return IPage
     */
    List<NightDrivingResponse> getNightDriving(@Param("current") Integer current, @Param("size") Integer size, @Param("nightDrivingRequest") NightDrivingRequest nightDrivingRequest);

    /**
     * 车辆夜间违规行车报表
     *
     * <AUTHOR>
     * @param nightDrivingRequest 车辆夜间违规行车请求类
     * @return IPage
     */
    long getNightDrivingCount(@Param("nightDrivingRequest") NightDrivingRequest nightDrivingRequest);

    /**
     * 疲劳驾驶统计表
     *
     * <AUTHOR>
     * @param fatigueDrivingRequest 疲劳驾驶统计请求类
     * @return IPage
     */
    List<FatigueDrivingResponse> getFatigueDriving(@Param("current") Integer current, @Param("size") Integer size, @Param("fatigueDrivingRequest") FatigueDrivingRequest fatigueDrivingRequest);

    /**
     * 疲劳驾驶统计表
     *
     * <AUTHOR>
     * @param fatigueDrivingRequest 疲劳驾驶统计请求类
     * @return IPage
     */
    long getFatigueDrivingCount(@Param("fatigueDrivingRequest") FatigueDrivingRequest fatigueDrivingRequest);

    /**
     * 报停异动列表
     */
    IPage<UnexpectedReportResponse> getUnexpectedReport (@Param("request") CommonBaseRequest request, @Param("tenantId") String tenantId, IPage<UnexpectedReportResponse> page);

    /**
     * 报停异动数目
     */
    @Select("<script>" +
        "select count(1) as count from bdm_security bs, bdm_vehicle bv " +
        "where bs.alarm_type = 260 and bs.licence_plate = bv.licence_plate and bs.plate_color = bv.licence_color " +
        "<if test='request.startTime != null'>" +
            "and bs.alarm_time &gt;= #{request.startTime} " +
        "</if>" +
        "<if test='request.endTime != null'>" +
            "and bs.alarm_time &lt;= #{request.endTime} " +
        "</if>" +
        "<if test='request.licencePlate != null'>" +
            "and bs.licence_plate like concat('%', #{request.licencePlate}, '%') " +
        "</if>" +
        "<if test='request.licenceColor != null'>" +
            "and bs.plate_color = #{request.licenceColor} " +
        "</if>" +
        "<if test='request.vehicleOwnerId != null'>" +
            "and bv.vehicle_owner_id = #{request.vehicleOwnerId} " +
        "</if>" +
        "<if test='request.accessMode != null'>" +
            "and bv.access_mode = #{request.accessMode} " +
        "</if>" +
        "<if test='request.deptList != null and request.deptList.size() > 0'>" +
            "and bs.dept_id in " +
            "<foreach collection='request.deptList' item='deptId' index='index' open='(' close=')' separator=','>" +
                "#{deptId}" +
            "</foreach>" +
        "</if>" +
        "<if test='request.professionList != null and request.professionList.size() > 0'>" +
            "and bv.vehicle_use_type in " +
            "<foreach collection='request.professionList' item='profession' index='index' open='(' close=')' separator=','>" +
                "#{profession}" +
            "</foreach>" +
        "</if>" +
    "</script>")
    List<HashMap> countUnexpectedReport (@Param("request") UnexpectedReportListRequest request);

    /**
     * 车辆每日数据大小
     */
    @Select("<script>" +
        "select " +
            "(select coalesce((select pd.name from sys_dept pd where pd.id = d.pid), d.name) from sys_dept d where d.id = bv.dept_id) as enterprise, " +
            "(select d.name from sys_dept d where d.id = bv.dept_id) as dept_name, " +
            "dcmb.licence_plate, " +
            "(select dict_name from bam_dict bd where bd.dict_type = 3 and bd.pid != 0 and bd.dict_code = dcmb.licence_color) as licence_color, " +
            "(select dict_name from bam_dict bd where bd.dict_type = 2 and bd.pid != 0 and bd.dict_code = bv.vehicle_use_type) as vehicle_model, " +
            "(select dict_name from bam_dict bd where bd.dict_type = 30 and bd.pid != 0 and bd.dict_code = bv.access_mode) as access_mode, " +
            "coalesce((select btp.name from bam_third_party_platform btp where btp.id = bv.vehicle_owner_id), '非营运车辆') as vehicle_owner, " +
            "dcmb.ymd, " +
            "dcmb.mega_byte " +
        "from (" +
            "select * from daily_car_mega_byte where 1=1 " +
            "<if test='request.startDate != null'>" +
                "and ymd &gt;= #{request.startDate} " +
            "</if>" +
            "<if test='request.endDate != null'>" +
                "and ymd &lt;= #{request.endDate} " +
            "</if>" +
            "<if test='request.licenceColor != null'>" +
                "and licence_color = #{request.licenceColor} " +
            "</if>" +
            "<if test='request.licencePlate != null'>" +
                "and licence_plate like concat('%', #{request.licencePlate}, '%') " +
            "</if>" +
        ") dcmb, bdm_vehicle bv " +
        "where dcmb.licence_color = bv.licence_color and dcmb.licence_plate = bv.licence_plate " +
        "<if test='request.vehicleOwnerId != null'>" +
            "and bv.vehicle_owner_id = #{request.vehicleOwnerId} " +
        "</if>" +
        "<if test='request.accessMode != null'>" +
            "and bv.access_mode = #{request.accessMode} " +
        "</if>" +
        "<if test='request.deptList != null and request.deptList.size() > 0'>" +
            "and bv.dept_id in " +
            "<foreach collection='request.deptList' item='deptId' index='index' open='(' close=')' separator=','>" +
                "#{deptId}" +
            "</foreach>" +
        "</if>" +
        "<if test='request.professionList != null and request.professionList.size() > 0'>" +
            " and bv.vehicle_use_type in " +
            "<foreach collection='request.professionList' item='profession' index='index' open='(' close=')' separator=','>" +
                "#{profession}" +
            "</foreach>" +
        "</if>" +
        " order by dcmb.ymd desc" +
    "</script>")
    @Results({
        @Result(property = "enterprise", column = "enterprise"),
        @Result(property = "deptName", column = "dept_name"),
        @Result(property = "licencePlate", column = "licence_plate"),
        @Result(property = "licenceColor", column = "licence_color"),
        @Result(property = "vehicleModel", column = "vehicle_model"),
        @Result(property = "accessMode", column = "access_mode"),
        @Result(property = "vehicleOwner", column = "vehicle_owner"),
        @Result(property = "ymd", column = "ymd"),
        @Result(property = "megaByte", column = "mega_byte")
    })
    List<DailyCarMegaByteResponse> getDailyCarMegaByte (@Param("request") DailyCarMegaByteListRequest request);

    /**
     * 车辆数据大小
     */
    @Select("<script>" +
        "select " +
            "(select coalesce((select pd.name from sys_dept pd where pd.id = d.pid), d.name) from sys_dept d where d.id = bv.dept_id) as enterprise, " +
            "(select d.name from sys_dept d where d.id = bv.dept_id) as dept_name, " +
            "dcmb.licence_plate, " +
            "(select dict_name from bam_dict bd where bd.dict_type = 3 and bd.pid != 0 and bd.dict_code = dcmb.licence_color) as licence_color, " +
            "(select dict_name from bam_dict bd where bd.dict_type = 2 and bd.pid != 0 and bd.dict_code = bv.vehicle_use_type) as vehicle_model, " +
            "(select dict_name from bam_dict bd where bd.dict_type = 30 and bd.pid != 0 and bd.dict_code = bv.access_mode) as access_mode, " +
            "coalesce((select btp.name from bam_third_party_platform btp where btp.id = bv.vehicle_owner_id), '非营运车辆') as vehicle_owner, " +
            "dcmb.mega_byte " +
        "from (" +
            "select licence_color, licence_plate, sum(mega_byte) as mega_byte from daily_car_mega_byte where 1=1 " +
            "<if test='request.startDate != null'>" +
                "and ymd &gt;= #{request.startDate} " +
            "</if>" +
            "<if test='request.endDate != null'>" +
                "and ymd &lt;= #{request.endDate} " +
            "</if>" +
            "<if test='request.licenceColor != null'>" +
                "and licence_color = #{request.licenceColor} " +
            "</if>" +
            "<if test='request.licencePlate != null'>" +
                "and licence_plate like concat('%', #{request.licencePlate}, '%') " +
            "</if>" +
            "group by licence_color, licence_plate" +
        ") dcmb, bdm_vehicle bv " +
        "where dcmb.licence_color = bv.licence_color and dcmb.licence_plate = bv.licence_plate " +
        "<if test='request.vehicleOwnerId != null'>" +
            "and bv.vehicle_owner_id = #{request.vehicleOwnerId} " +
        "</if>" +
        "<if test='request.accessMode != null'>" +
            "and bv.access_mode = #{request.accessMode} " +
        "</if>" +
        "<if test='request.deptList != null and request.deptList.size() > 0'>" +
            "and bv.dept_id in " +
            "<foreach collection='request.deptList' item='deptId' index='index' open='(' close=')' separator=','>" +
                "#{deptId}" +
            "</foreach>" +
        "</if>" +
        "<if test='request.professionList != null and request.professionList.size() > 0'>" +
            " and bv.vehicle_use_type in " +
            "<foreach collection='request.professionList' item='profession' index='index' open='(' close=')' separator=','>" +
                "#{profession}" +
            "</foreach>" +
        "</if>" +
        " limit ${request.size} offset ${(request.current - 1) * request.size}" +
    "</script>")
    @Results({
        @Result(property = "enterprise", column = "enterprise"),
        @Result(property = "deptName", column = "dept_name"),
        @Result(property = "licencePlate", column = "licence_plate"),
        @Result(property = "licenceColor", column = "licence_color"),
        @Result(property = "vehicleModel", column = "vehicle_model"),
        @Result(property = "accessMode", column = "access_mode"),
        @Result(property = "vehicleOwner", column = "vehicle_owner"),
        @Result(property = "megaByte", column = "mega_byte")
    })
    List<CarMegaByteResponse> getCarMegaByte (@Param("request") DailyCarMegaByteListRequest request);

    /**
     * 车辆数据大小数目
     */
    @Select("<script>" +
        "select count(1) as count from (" +
            "select dcmb.licence_color, dcmb.licence_plate, sum(dcmb.mega_byte) from daily_car_mega_byte dcmb, bdm_vehicle bv " +
            "where dcmb.licence_color = bv.licence_color and dcmb.licence_plate = bv.licence_plate " +
            "<if test='request.startDate != null'>" +
                "and dcmb.ymd &gt;= #{request.startDate} " +
            "</if>" +
            "<if test='request.endDate != null'>" +
                "and dcmb.ymd &lt;= #{request.endDate} " +
            "</if>" +
            "<if test='request.licencePlate != null'>" +
                "and dcmb.licence_plate like concat('%', #{request.licencePlate}, '%') " +
            "</if>" +
            "<if test='request.licenceColor != null'>" +
                "and dcmb.licence_color = #{request.licenceColor} " +
            "</if>" +
            "<if test='request.vehicleOwnerId != null'>" +
                "and bv.vehicle_owner_id = #{request.vehicleOwnerId} " +
            "</if>" +
            "<if test='request.accessMode != null'>" +
                "and bv.access_mode = #{request.accessMode} " +
            "</if>" +
            "<if test='request.deptList != null and request.deptList.size() > 0'>" +
                "and bv.dept_id in " +
                "<foreach collection='request.deptList' item='deptId' index='index' open='(' close=')' separator=','>" +
                    "#{deptId}" +
                "</foreach>" +
            "</if>" +
            "<if test='request.professionList != null and request.professionList.size() > 0'>" +
                " and bv.vehicle_use_type in " +
                "<foreach collection='request.professionList' item='profession' index='index' open='(' close=')' separator=','>" +
                    "#{profession}" +
                "</foreach>" +
            "</if>" +
            " group by licence_color, licence_plate" +
        ") tmp" +
    "</script>")
    List<HashMap> countCarMegaByte (@Param("request") DailyCarMegaByteListRequest request);

    /**
     * 车辆日里程统计查询
     *
     * <AUTHOR>
     * @param page           分页参数
     * @param dayMileageRequest 交通事故请求对象
     * @return IPage<SysOrganizationInfo>
     */
    Page<HashMap> queryDayMileage(Page page, @Param("request") DayMileageRequest dayMileageRequest);


    /**
     * 在线率统计查询
     *
     * <AUTHOR>
     * @param page           分页参数
     * @param onlineRateRequest 交通事故请求对象
     * @return IPage<SysOrganizationInfo>
     */
    Page<Map> rateStatistics(Page page, @Param("onlineRateRequest") OnlineRateRequest onlineRateRequest);


    /**
     * @description: 根据指定的日期统计车辆在线情况
     * @author: zhouxw
     * @date: 2023-03-67 14:02:22
     * @param: [dateList]
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<java.util.HashMap>
     **/
    List<HashMap<String, Object>> rateStatisticsWithTime(@Param("dateList") List<String> dateList, @Param("yearMonthList") List<String> yearMonthList);


	List<BdmTerminalonlinerecord> getVehicleOnlineDataByMonth(@Param("month") String month, @Param("startTime") long startTime, @Param("endTime") long endTime);


    @Select("<script>" +
            " select min(speed) as minSpeed,max(speed) as maxSpeed from DataMine.location_${tableDate} where licence_plate=#{licencePlate} and time&gt;= #{startTime} and time&lt;= #{endTime}" +
            "</script>")
    List<HashMap> queryLocation(@Param("tableDate")String tableDate,@Param("licencePlate")String licencePlate,@Param("startTime")Long startTime, @Param("endTime")Long endTime);

    /**
     * 车辆安全信息发送记录查询
     *
     * <AUTHOR>
     * @param page           分页参数
     * @param securityInfoRequest 车辆安全信息发送记录查询请求类
     * @return IPage
     */
    /*@Results({
            @Result(property = "deptName", column = "dept_name"),
            @Result(property = "licencePlate", column = "licence_plate"),
            @Result(property = "licenceColor", column = "licence_color"),
            @Result(property = "vehicleModel", column = "vehicle_model"),
            @Result(property = "vehicleOwner", column = "vehicle_owner"),
            @Result(property = "sendTime", column = "update_time"),
            @Result(property = "sendMessage", column = "param"),
            @Result(property = "sendState", column = "res_param")
    })*/
    Page<SecurityInfoResponse> securityInfo(Page page, @Param("securityInfoRequest") SecurityInfoRequest securityInfoRequest);


    /**
     * @description: 根据时间段查询 车辆安全信息记录
     * @author: zhouxw
     * @date: 2023-02-45 16:55:54
     * @param: [startTime, endTime]
     * @return: java.util.List<com.xh.vdm.statistic.entity.CacheSecurityInfoResponse>
     **/
    List<CacheSecurityInfoResponse> securityInfoWithTime(Date startTime, Date endTime);

	/**
	 * 根据条件查询车辆上下线记录
	 * @param request
	 * @return
	 */
	IPage<VehicleOnlineOrOfflineResponse> getVehicleOnlineOrOffline(@Param("request") CommonBaseCrossMonthDurationRequest request, IPage<VehicleOnlineOrOfflineResponse> page);

    /**
     * 获取企业下属企业编号
     *
     * <AUTHOR>
     * @param parentDeptId 父级部门编号
     * @return IPage
     */
    @Select("<script>" +
            " select id,pid from sys_dept where is_del=0 and find_in_set(id,getChild(#{parentDeptId}))  " +
            "</script>")
    List<HashMap> getDeptList (@Param("parentDeptId") Long parentDeptId);

    @Select("<script>" +
            " select count(1) as location_count from pos_gn.locations where licence_plate=#{licencePlate} " +
            " and licence_color=#{licenceColor} " +
            " and loc_time&gt;= #{startTime} and loc_time&lt;= #{endTime}" +
            "</script>")
	@DS("location")
    HashMap queryLocationCount(@Param("tableDate")String tableDate,@Param("licencePlate")String licencePlate, @Param("licenceColor") Integer licenceColor, @Param("startTime")Long startTime, @Param("endTime")Long endTime);

    /**
     * 判断是否管理员角色
     *
     * <AUTHOR>
     * @param userId 用户编号
     * @return IPage
     */
    @Select("<script>" +
            " select role_id from sys_users_roles where user_id=#{userId} and role_id=1 " +
            "</script>")
    List<HashMap> judgeAdminRole (@Param("userId") Long userId);

    /**
     * 查询用户部门
     * @param userId
     * @return
     */
    @Select("<script>" +
            " select dept_id from sys_users_depts where user_id=#{userId} " +
            "</script>")
    List<HashMap> queryDepartments(@Param("userId") Long userId);


    /**
     * 查询已下线未更新点数的数据
     * @return
     */
    @DS("master")
    @Select("<script>" +
            " select id, vehicle_id,(select bv.licence_plate from bdm_vehicle bv where bv.id=bt.vehicle_id) as licence_plate,(select bv.licence_color from bdm_vehicle bv where bv.id=bt.vehicle_id) as licence_color, on_line_time, off_line_time from bdm_terminalonlinerecord bt where bt.is_del=0 and bt.point_num is null and bt.off_line_time is not null limit 5000 " +
            "</script>")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "vehicleId", column = "vehicle_id"),
            @Result(property = "licencePlate", column = "licence_plate"),
            @Result(property = "onLineTime", column = "on_line_time"),
            @Result(property = "offLineTime", column = "off_line_time")
    })
    List<OnlineOfflineRecord> queryNeedUpdatePointNum();


    /**
     * 查询已下线未更新点数的数据
     * @return
     */
    @Update("<script>" +
            " update bdm_terminalonlinerecord set point_num=#{pointNum} where id=#{id} " +
            "</script>")
    void updatePointNum(@Param("pointNum") Long pointNum,@Param("id") Long id);

    /**
     * 查询用户分派的车辆
     * @param userId 用户编号
     * @return
     */
    @Select("<script>" +
            " select vehicle_id from sys_vehicles_users where user_id=#{userId} " +
            "</script>")
    List<HashMap> getVehicleIdListByUserId(@Param("userId") Long userId);

    /**
     * 查询车辆列表
     * @param
     * @return
     */
    @Select("<script>" +
            " select bv.id,bv.licence_plate,bv.vehicle_model,bv.vehicle_color,bv.licence_color,bv.dept_id,bvs.longitude,bvs.latitude from bdm_vehicle bv,bdm_vehicle_state bvs where bv.vehicle_state_id=bvs.id and bv.is_del=0 " +
            "</script>")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "licencePlate", column = "licence_plate"),
            @Result(property = "vehicleModel", column = "vehicle_model"),
            @Result(property = "vehicleColor", column = "vehicle_color"),
            @Result(property = "licenceColor", column = "licence_color"),
            @Result(property = "deptId", column = "dept_id"),
            @Result(property = "longitude", column = "longitude"),
            @Result(property = "latitude", column = "latitude")
    })
    List<Vehicle> getVehicleList();

    /**
     * 查询字典列表
     * @param
     * @return
     */
    @Select("<script>" +
            " select bd.id,bd.dict_code,bd.dict_name,bd.pid,bd.dict_type from bam_dict bd where bd.is_del=0 and bd.pid != 0 order by dict_type asc,dict_code asc " +
            "</script>")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "dictCode", column = "dict_code"),
            @Result(property = "dictName", column = "dict_name"),
            @Result(property = "pid", column = "pid"),
            @Result(property = "dictType", column = "dict_type")
    })
    List<Dict> getDictionaryList();


    /**
     * 查询所有部门
     * @return
     */
    @Select("<script>" +
            " select id,parent_id from blade_dept where is_deleted=0 " +
            "</script>")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "pid", column = "parent_id")
    })
    List<Dept> queryDepts();
}
