package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @Description: 车辆行驶里程返回对象
 * @Author: zhouxw
 * @Date: 2022/11/10 6:04 PM
 */
@Data
public class VehicleTravelMileageResponse {
    //企业id
    @JsonIgnore
    @ExcelIgnore
    private Long deptId;
    //企业名称
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆行驶里程分析", "企业名称"})
    private String deptName;
    //统计日期
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆行驶里程分析", "日期"})
    private String statDate;
    //0～100公里车辆数
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆行驶里程分析", "0～100公里"})
    private int level1Count;
    //100～500公里车辆数
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆行驶里程分析", "100～500公里"})
    private int level2Count;
    //500～1000公里车辆数
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆行驶里程分析", "500～1000公里"})
    private int level3Count;
    //1000公里以上车辆数
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆行驶里程分析", "1000公里以上"})
    private int level4Count;
    //总里程
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆行驶里程分析", "总里程（公里）"})
    private double totalMileage;
    //总上线车辆数
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆行驶里程分析", "总车辆数"})
    private int totalVehicleCount;
    //平均里程
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆行驶里程分析", "平均里程（公里）"})
    private double averageMileage;
}
