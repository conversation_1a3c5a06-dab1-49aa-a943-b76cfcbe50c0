package com.xh.vdm.statistic.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.entity.terminal.*;
import com.xh.vdm.statistic.mapper.LocationsMapper;
import com.xh.vdm.statistic.mapper.tg.*;
import com.xh.vdm.statistic.service.ITerminalService;
import com.xh.vdm.statistic.service.LocationsService;
import com.xh.vdm.statistic.service.terminal.*;

import com.xh.vdm.statistic.utils.PosAuxField;
import com.xh.vdm.statistic.vo.DeviceTypeAndDeviceIdVO;
import com.xh.vdm.statistic.vo.TerminalBaseVO;
import com.xh.vdm.statistic.vo.request.LocationsRequest;
import com.xh.vdm.statistic.vo.response.LocationKuduResponse;
import com.xh.vdm.statistic.vo.response.terminal.TargetInfo;
import com.xh.vdm.statistic.vo.response.tg.TargetResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.RedisConstant;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.common.utils.StringUtils;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.entity.DataAuthCE;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * @Description: 定位信息统计
 */
@RestController
@RequestMapping("/static/locations")
@Slf4j
public class LocationsController {
	/**
	 * 服务对象
	 */
	/**
	 * 服务对象
	 */
	@Resource
	private LocationsService locationsService;
	@Resource
	private LocationsMapper locationsMapper;
	@Resource
	private ITerminalService terminalService;
	@Resource
	private RedisTemplate<String, String> redisTemplate;

	@Resource
	private IRnssDeviceService rnssDeviceService;
	@Resource
	private IWearableDeviceService wearableDeviceService;
	@Resource
	private IRdssDeviceService rdssDeviceService;
	@Resource
	private IMonitDeviceService monitDeviceService;
	@Resource
	private IPntDeviceService pntDeviceService;

	@Resource
	private CETokenUtil ceTokenUtil;

	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(20, 50, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());


	/**
	 * 给定默认赋码号
	 * @return
	 */
	//pre_auth_test
	@GetMapping("/defaultDeviceNum")
	public R<String> defaultDeviceNum(BladeUser user){

		//2.查询终端信息
		TerminalBaseVO terminal = terminalService.findTerminalOne(null);
		if(terminal != null){
			return R.data(terminal.getDeviceNum());
		}
		return R.data("");
	}

	/**
	 * 给定默认赋码号
	 * @return
	 */
	//pre_auth_test
	@GetMapping("/defaultDeviceId")
	public R<DeviceTypeAndDeviceIdVO> defaultDeviceId(BladeUser user){

		//2.查询终端信息
		TerminalBaseVO terminal = terminalService.findTerminalOne(null);
		if(terminal != null){
			DeviceTypeAndDeviceIdVO vo = new DeviceTypeAndDeviceIdVO();
			vo.setDeviceId(terminal.getDeviceId());
			vo.setDeviceType(terminal.getDeviceType());
			vo.setDeptName(terminal.getDeptName());
			vo.setTargetName(terminal.getTargetName());
			return R.data(vo);
		}
		return R.data(null);
	}

	/**
	 * 查询定位信息
	 * 主查询逻辑支持同时查询多个终端，但是因为终端较多严重影响查询效率，所以入口参数要求只能查询一个终端
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 */
	//pre_auth_test
	@PostMapping("/list")
	public R<IPage<LocationKuduResponse>> page(@RequestBody LocationsRequest request, Query query, BladeUser user) {

		//监控对象和赋码号不能同时为空
		if(StringUtils.isEmpty(request.getDeviceNum()) && StringUtils.isEmpty(request.getTargetName())){
			return R.fail("赋码编号和监控对象不能同时为空");
		}

		DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
		//2.查询终端（根据赋码号、终端类别、监控对象、部门）
		long start3 = System.currentTimeMillis();
		List<TerminalBaseVO> terminalList = terminalService.findTerminalList(request.getDeviceNum(), request.getDeviceType(),request.getTargetName(), dataAuthCE.getOrgListStr(), dataAuthCE.getAccount());


		if (terminalList == null || terminalList.size() < 1) {
			return R.data(new Page<>(query.getCurrent(), query.getSize()));
		}
		Map<String,String> targetNameMap = new HashMap<>();
		Map<Long,Integer>iotProtocolMap=new HashMap<>();
		//获取当前部门管理的targetId，解决防止查出target更换部门之前的轨迹
		List<Long> targetIds = new ArrayList<>();
		for(TerminalBaseVO vo : terminalList){
			targetNameMap.put(vo.getTargetId()+"", vo.getTargetName());
			iotProtocolMap.put(vo.getDeviceId(),vo.getIotProtocol());
			targetIds.add(vo.getTargetId());
		}
		long end3 = System.currentTimeMillis();
		log.info("time3 is " + (end3 - start3));

		//3.查询轨迹
		long start4 = System.currentTimeMillis();
		List<Long> deviceIds = new ArrayList<>();
		for(TerminalBaseVO vo : terminalList){
			deviceIds.add(vo.getDeviceId());
		}

		//targetIdList分组，每组1000个
		List<List<Long>> targetIdList = new ArrayList<>();
		List<Long> tIds = new ArrayList<>();
		for(int i = 0 ; i < targetIds.size(); i++){
			tIds.add(targetIds.get(i));
			if(i != 0 && i % 1000 == 0){
				targetIdList.add(tIds);
				tIds = new ArrayList<>();
			}
		}
		//最后一组
		if(tIds.size() > 0){
			targetIdList.add(tIds);
		}

		//deviceIds 分组，每组1000个
		List<List<Long>> idList = new ArrayList<>();
		List<Long> ids = new ArrayList<>();
		for(int i = 0 ; i < deviceIds.size(); i++){
			ids.add(deviceIds.get(i));
			if(i != 0 && i % 1000 == 0){
				idList.add(ids);
				ids = new ArrayList<>();
			}
		}
		//最后一组
		if(ids.size() > 0){
			idList.add(ids);
		}
		long start41 = System.currentTimeMillis();

		//由于mybatis对impala的分页解析有问题，可以自定义分页插件，或者自己手动分页。这里采用手动分页的方式，结合多线程，可以提高效率
		final List<LocationKudu>[] resArray = new List[]{null};
		CountDownLatch countDownLatch = new CountDownLatch(2);
		threadPool.submit(new Runnable() {
			@Override
			public void run() {
				try {
					resArray[0] = locationsService.locationPage(idList, targetIdList, request.getBatch() == null ? 0 : (int) request.getBatch(), request.getStartTime(), request.getEndTime(), query);
				}catch (Exception e){
					log.error("分页查询定位信息失败",e);
				}finally{
					countDownLatch.countDown();
				}
			}
		});

		final long[] count = {0};
		threadPool.submit(new Runnable() {
			@Override
			public void run() {
				try {
					count[0] = locationsService.locationCount(idList, targetIdList, request.getBatch() == null ? 0 : (int) request.getBatch(), request.getStartTime(), request.getEndTime());
				}catch (Exception e){
					log.error("查询定位信息数量失败",e);
				}finally{
					countDownLatch.countDown();
				}
			}
		});
		try {
			countDownLatch.await();
		} catch (InterruptedException e) {
			log.error("多线程处理分页查询失败",e);
			return R.fail("查询出错");
		}

		//组装分页数据
		long end41 = System.currentTimeMillis();
		log.info("time41 is " + (end41 - start41));
		if (resArray == null || resArray[0] == null || resArray[0].size() < 1) {
			return R.data(new Page<>(query.getCurrent(), query.getSize()));
		}
		long end4 = System.currentTimeMillis();
		log.info("time4 is " + (end4 - start4));

		//4.填充字段
		List<LocationKuduResponse> resList = new ArrayList<>();
		for(LocationKudu loc : resArray[0]){
			LocationKuduResponse res = new LocationKuduResponse();
			BeanUtils.copyProperties(loc, res);
			try {
				Integer iotProtocol=iotProtocolMap.get(loc.getDeviceId());
				PosAuxField field = PosAuxField.getAuxField(res.getAuxiliary(),iotProtocol);
				res.setGnssNum(field.gnssNum);
				res.setWireless(field.wireless);
				res.setIoState(field.ioState);
				res.setCharge(field.charge);
			}catch(Exception e){
				log.error("getAuxField解析附加信息报错:{}",e.getMessage());
			}
			resList.add(res);
			res.setTargetName(targetNameMap.get(loc.getTargetId()+""));
		}

		IPage<LocationKuduResponse> res = new Page<>(query.getCurrent(), query.getSize());
		res.setTotal(count[0]);
		res.setRecords(resList);
		return R.data(res);
	}



	private void processLocations(List<LocationKudu> locations, List<LocationKuduResponse> list) {
		Map<Object, Object> targetMap = this.redisTemplate.opsForHash().entries(RedisConstant.HASH_BASE_INFO_TARGET);
		for (LocationKudu location : locations) {
			LocationKuduResponse response = new LocationKuduResponse();
			BeanUtils.copyProperties(location, response);

			AtomicBoolean foundMatch = new AtomicBoolean(false);
			targetMap.forEach((key, value) -> {
				String base = response.getTargetType() + "-" + response.getTargetId();
				if (base.equals(key) && !foundMatch.get()) {
					// 判断value的类型是否是JSON字符串
					if (value instanceof String) {
						String jsonValue = (String) value;
						TargetInfo info = JSONObject.parseObject(jsonValue, TargetInfo.class);
						response.setTargetName(info.getTargetName());
						foundMatch.set(true);
					}
				}
			});
			list.add(response);
		}
	}

	private void processEntity(TargetResponse entity, List<String> ids) {
		String concatenatedId = entity.getTargetType() + "_" + entity.getId();
		ids.add(concatenatedId);
	}

}

