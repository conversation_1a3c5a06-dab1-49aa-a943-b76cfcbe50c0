package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 上下线表
 * <AUTHOR>
 * @date 2021/11/12 11:48
 */

@ApiModel(value = "上下线表")
@TableName("bdm_terminalonlinerecord")
@Data
public class OnlineOfflineRecord {

    @TableId("id")
    @ApiModelProperty(value = "编号")
    private Long id;

    @TableField("vehicle_id")
    @ApiModelProperty(value = "车辆编号")
    private Integer vehicleId;

    @TableField("on_line_time")
    @ApiModelProperty(value = "上线时间")
    private Date onLineTime;

    @TableField("off_line_time")
    @ApiModelProperty(value = "下线时间")
    private Date offLineTime;

    @TableField("update_time")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    @TableField("create_time")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @TableField("is_del")
    @ApiModelProperty(value = "是否有效")
    private Integer isDel;

    @TableField("driver_name")
    @ApiModelProperty(value = "司机名称")
    private String driverName;

    @TableField("point_num")
    @ApiModelProperty(value = "定位条数")
    private Long pointNum;

    @TableField(exist = false)
    @ApiModelProperty(value = "车牌号码")
    private String licencePlate;

    //车牌颜色
    private Integer licenceColor;

    public static final String COL_IS_DEL = "is_del";
    public static final String COL_POINT_NUM = "point_num";
    public static final String COL_OFF_LINE_TIME = "off_line_time";
    public static final String COL_ON_LINE_TIME = "is_del";
}
