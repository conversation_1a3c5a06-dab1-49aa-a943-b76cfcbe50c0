package com.xh.vdm.statistic.utils;

import com.alibaba.fastjson.JSONArray;
import com.xh.vdm.statistic.constant.RedisConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 数据字典工具类
 * <AUTHOR>
 * @date Created in 11:39 2021/4/29
 * @description
 */
@Slf4j
@Component
public class DeptUtil {

    @Autowired
    protected RedisTemplate<String, String> redisTemplate;

    /**
     * 根据车牌号获取车辆信息
     * <AUTHOR>
     * @date 2021-04-29 11:43:24
     * @param deptId
     * @return
     **/
    public List<Long> acquireDepts(String deptId){
        try{
            Object object = redisTemplate.opsForHash().get(RedisConstants.HashKey.DEPT_INFO_HASH_KEY,deptId);
            if(null != object){
                String value =(String) object;
                List<Long> depts = JSONArray.parseArray(value,Long.class);
                return depts;
            }
        }catch (ClassCastException e){
            log.error("{} error, get Dept error, typeCode:{}",CodeInfoUtils._FILE_LINE_FUNC_(), deptId);
        }
        return null;
    }

}
