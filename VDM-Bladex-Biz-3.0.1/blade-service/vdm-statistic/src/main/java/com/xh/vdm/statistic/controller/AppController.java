package com.xh.vdm.statistic.controller;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.report.ReportSummary;
import com.xh.vdm.statistic.entity.report.VehicleTravelMessage;
import com.xh.vdm.statistic.entity.wx.AlarmInfoWX;
import com.xh.vdm.statistic.entity.wx.VehicleBaseInfoWX;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.VdmUserInfoUtil;
import com.xh.vdm.statistic.vo.request.DeptAndLicencePlateRequest;
import com.xh.vdm.statistic.vo.request.VehicleReportRequest;
import com.xh.vdm.statistic.vo.response.OnlineHourCountResponse;
import com.xh.vdm.statistic.vo.response.ReportSummaryResponse;
import com.xh.vdm.statistic.vo.response.WxStatResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 移动端应用
 */
@RestController
@RequestMapping("/statistics/app")
@Slf4j
public class AppController {

	@Resource
	private IAppService appService;


	@Resource
	private IBdmVehicleService vehicleService;

	public static final Integer LIMIT = 10;

	@Resource
	private IImpalaAlarmService impalaAlarmService;

	private final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(10,20,300, TimeUnit.SECONDS,new LinkedBlockingQueue<>());

	@Resource
	private IReportService reportService;

	@Resource
	private IAlarmService alarmService;

	@Resource
	private VdmUserInfoUtil vdmUserInfoUtil;

	@Resource
	private IBladeDeptService deptService;

	@Resource
	private IVehicleStatService vehicleStatService;

	/**
	 * 微信统计信息--小程序，首页
	 * @param user
	 * @return
	 */
	@GetMapping("/wxStat")
	public R<WxStatResponse> wxStatInfo(BladeUser user){
		try{
			WxStatResponse resp = appService.findWxStatInfo(user);
			return R.data(resp);
		}catch (Exception e){
			log.error("查询微信统计信息失败",e);
			return R.fail("统计信息失败");
		}
	}


	/**
	 * 查询子部门及子部门
	 * @param deptId
	 * @param user
	 * @return
	 */
	@GetMapping("/childrenDepts")
	public R<List<BladeDept>> childrenDepts(Long deptId, BladeUser user){
		try{
			List<Long> deptIds = null;
			if(deptId == null){
				//如果不指定企业，则查询当前账号能看到的
				deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
			}else{
				//如果指定企业
				deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
			}
			if(deptIds == null){
				return R.data(null);
			}
			List<BladeDept> depts = deptService.listByIds(deptIds);
			return R.data(depts);
		}catch (Exception e){
			log.error("查询子部门失败",e);
			return R.fail("查询失败");
		}
	}

	/**
	 * 查询部门下的车辆（包含账号关联的车辆）
	 * 支持车牌号模糊查询
	 * @param request
	 * @param user
	 * @return
	 */
	@PostMapping("/vehicleList")
	public R<List<VehicleBaseWithId>> vehicleInfoByDept(@RequestBody DeptAndLicencePlateRequest request, BladeUser user){
		try{
			List<VehicleBaseWithId> list = appService.findVehicleByDeptIdAndLicencePlate(request.getDeptId(),request.getLicencePlate(),user);
			return R.data(list);
		}catch(Exception e){
			log.error("查询出错",e);
			return R.fail("查询失败");
		}
	}

	/**
	 * 车辆报表
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 */
	@PostMapping("/vehicleReport")
	public R<Map<String,Object>> vehicleReport(@RequestBody VehicleReportRequest request, Query query, BladeUser user){
		try{
			if(user == null){
				return R.fail("用户未登录或未授权");
			}
			Map<String,Object> page = appService.findVehicleReportInfo(request, query, user);
			return R.data(page);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}


	/**
	 * 日报统计（暂时不用，后边做了接口拆解和逻辑修改）
	 * @param date 日期，yyyy-MM-dd
	 * @param user
	 * @return
	 */
	@GetMapping("/reportDay")
	R<List<ReportSummaryResponse>> reportDay(String date,Long deptId, BladeUser user){
		if(user == null){
			log.error("用户未授权或未登录");
			return R.fail("用户未授权或未登录");
		}
		try{
			long startTime = DateUtil.getDayFirstSecondTimestamp(date);
			long endTime = DateUtil.getDayLastSecondTimestamp(startTime);
			List<ReportSummaryResponse> resps = new ArrayList<>();
			if(deptId != null){
				//如果指定企业
				ReportSummary summary = reportService.reportSummary(deptId, date, startTime, endTime);
				String deptName = summary.getDeptName();
				ReportSummaryResponse resp = new ReportSummaryResponse();
				resp.setSummary(summary);
				resp.setDeptId(deptId);
				resp.setDeptName(deptName);
				resps.add(resp);
			}else{
				String[] deptIds = user.getDeptId().split(",");
				for(String deptIdStr : deptIds){
					Long dId = Long.parseLong(deptIdStr);
					ReportSummary summary = reportService.reportSummary(dId, date, startTime, endTime);
					String deptName = summary.getDeptName();
					ReportSummaryResponse resp = new ReportSummaryResponse();
					resp.setSummary(summary);
					resp.setDeptId(dId);
					resp.setDeptName(deptName);
					resps.add(resp);
				}
			}
			return R.data(resps);
		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询报错");
		}
	}

	/**
	 * 查询企业基本情况
	 * @param statDate yyyy-MM-dd / yyyy-MM
	 * @param deptId
	 * @param user
	 * @return
	 */
	@GetMapping("/vehicleBase")
	public R<VehicleBaseInfoWX> vehicleBaseInfo(String statDate, Long deptId, BladeUser user){
		if(user == null){
			return R.fail("用户未登录或未授权");
		}
		try{
			VehicleBaseInfoWX info = reportService.vehicleBaseInfo(deptId, statDate);
			return R.data(info);
		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询出错");
		}
	}

	/**
	 * 统计车辆上线趋势
	 * @param statDate
	 * @param deptId
	 * @param user
	 * @return
	 */
	@GetMapping("/goOnlineCount")
	public R<List<DateAndCount>> goOnlineCount(String statDate,Long deptId,  BladeUser user){
		try{
			if(StringUtils.isEmpty(statDate)){
				return R.fail("请输入查询条件");
			}
			List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
			List<DateAndCount> resList;
			if(statDate.length() == 7){
				//如果是月报
				String startDate = statDate + "-" + "01";
				String endDate = statDate + "-" +DateUtil.getDaysCountInMonth(statDate);
				List<DateAndCount> list = vehicleStatService.findGoOnlineCountByDay(startDate, endDate, deptIds, user.getUserId());
				resList = list;
			}else{
				resList = new ArrayList<>();
				//如果是日报
				List<OnlineHourCountResponse> list = reportService.onlineHourCount(deptId, statDate);
				list.forEach(item -> {
					DateAndCount dc = new DateAndCount();
					dc.setCount(item.getCount().longValue());
					dc.setStatDate(item.getHour());
					resList.add(dc);
				});
			}
			return R.data(resList);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}


	/**
	 * 报警信息
	 * @param deptId
	 * @param statDate
	 * @param user
	 * @return
	 */
	@GetMapping("/alarmInfo")
	public R<AlarmInfoWX> alarmInfo(Long deptId, String statDate,BladeUser user){
		if(user == null){
			log.info("用户未登录或未授权");
			return R.fail("用户未登录或未授权");
		}
		try{
			AlarmInfoWX alarmInfo = reportService.findAlarmInfo(deptId, statDate);
			return R.data(alarmInfo);
		}catch (Exception e){
			log.error("查询报警信息出错",e);
			return R.fail("查询出错");
		}
	}

	/**
	 * 长期未在线排行
	 * @return
	 */
	@GetMapping("/longOfflineSort")
	public R<List<VehicleAndCountAndDesc>> longOfflineList(Long deptId, BladeUser user){
		if(user == null){
			return R.fail("用户未登录或未授权");
		}
		try{
			List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
			List<VehicleAndCount> longOfflineList = vehicleService.findOfflineListByOfflineDayCount(deptIds, null, CommonConstant.LONG_OFFLINE_DAYS_COUNT,LIMIT);
			List<VehicleAndCountAndDesc> descList = new ArrayList<>();
			longOfflineList.forEach(item -> {
				VehicleAndCountAndDesc desc = new VehicleAndCountAndDesc();
				desc.setVehicleId(item.getVehicleId());
				desc.setLicencePlate(item.getLicencePlate());
				desc.setLicenceColor(item.getLicenceColor());
				desc.setCountDesc(DateUtil.getFormatDateString(item.getCount()));
				desc.setCount(item.getCount());
				descList.add(desc);
			});
			return R.data(descList);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}


	/**
	 * 超速排行
	 * @return
	 */
	@GetMapping("/overSpeedSortLimit")
	public R<List<VehicleAndCount>> overSpeedSortLimit(Long deptId, String statDate, BladeUser user){
		if(user == null){
			return R.fail("用户未登录或未授权");
		}
		if(StringUtils.isEmpty(statDate)){
			return R.fail("日期不能为空");
		}
		try{
			List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
			long startSecondTime = 0;
			long endSecondTime = 0;
			if(statDate.length() == 7){
				//如果是月报
				startSecondTime = DateUtil.getMonthFirstSecondTimestamp(statDate);
				endSecondTime = DateUtil.getMonthLastSecondTimestamp(statDate);
			}else{
				//如果是日报
				startSecondTime = DateUtil.getDayFirstSecondTimestamp(statDate);
				endSecondTime = DateUtil.getDayLastSecondTimestamp(startSecondTime);
			}
			List<Integer> overSpeedAlarmList = VdmUserInfoUtil.getOverSpeedAlarmType();
			List<VehicleAndCount> overSpeedList = alarmService.findAlarmListByDeptIdsAndAlarmType(deptIds,overSpeedAlarmList,startSecondTime, endSecondTime, LIMIT);
			return R.data(overSpeedList);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}



	/**
	 * 车辆运行分析
	 * @return
	 */
	@GetMapping("/vehicleTravel")
	public R<VehicleTravelMessage> vehicleTravel(Long deptId, String statDate, Query query, BladeUser user){
		if(user == null){
			return R.fail("用户未登录或未授权");
		}
		if(StringUtils.isEmpty(statDate)){
			return R.fail("日期不能为空");
		}
		try{
			VehicleTravelMessage vt = reportService.vehicleTravel(deptId, statDate,query);
			return R.data(vt);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}




	/**
	 * 分页查询长期未在线车辆排行
	 * @param query
	 * @param user
	 * @return
	 */
	@GetMapping("/longOfflineVehicleSort")
	R<IPage<VehicleAndCount>> longOfflineVehicle(Query query, Long deptId, String licencePlate, Long licenceColor, BladeUser user){
		if ((user == null) || (deptId == null)) {
			return R.fail(ResultCode.FAILURE.getCode(), "用户未授权或未登录。");
		}
		try{
			IPage<VehicleAndCount> page = reportService.longOfflineVehiclePage(query, deptId, licencePlate, licenceColor, user);
			return R.data(page);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}

	/**
	 * 分页查询超速车辆排行
	 * @param query
	 * @param user
	 * @return
	 */
	@GetMapping("/overSpeedSort")
	public R<IPage<VehicleAndCount>> overSpeed(Query query, String date,String licencePlate, Long licenceColor, BladeUser user){
		try{
			long startTime;
			long endTime;
			if (date.matches("\\d{4}-\\d{2}")) {
				startTime = DateUtil.getMonthFirstSecondTimestamp(date);
				endTime = DateUtil.getMonthLastSecondTimestamp(date);
			} else if (date.matches("\\d{4}-\\d{2}-\\d{2}")) {
				startTime = DateUtil.getDayFirstSecondTimestamp(date);
				endTime = DateUtil.getDayLastSecondTimestamp(startTime);
			} else {
				return R.fail(ResultCode.FAILURE.getCode(), "查询时间段格式不正确。");
			}

			IPage<VehicleAndCount> page = reportService.overSpeedPage(query,startTime,endTime,licencePlate, licenceColor, user);
			return R.data(page);
		}catch (Exception e){
			log.error("查询失败",e);
			return R.fail("查询失败");
		}
	}


	/**
	 * 驾驶员排行
	 * @param query
	 * @param date
	 * @param type
	 * @param user
	 * @return
	 */
	@GetMapping("/driverAndAlarmCountSort")
	public R<IPage<DriverNameAndCount>> driverAndAlarmCountSort(Query query, String date, String type,String driverName, BladeUser user){

		try{
			IPage<DriverNameAndCount> pageRes = null;
			List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
			long startTime = DateUtil.getDayFirstSecondTimestamp(date);
			long endTime = DateUtil.getDayLastSecondTimestamp(startTime);
			if("1".equals(type)){
				//超速
				IPage<DriverNameAndCount> overSpeedDriverList = alarmService.findDriverAlarmListByDeptIdsAndAlarmTypePage(deptIds,VdmUserInfoUtil.getOverSpeedAlarmType(),startTime, endTime,driverName, query);
				pageRes = overSpeedDriverList;
			}else if("2".equals(type)){
				//疲劳
				List<Integer> fatigueAlarmType = VdmUserInfoUtil.getFatigueAlarmType();
				IPage<DriverNameAndCount> fatigueDriverList = alarmService.findDriverAlarmListByDeptIdsAndAlarmTypePage(deptIds,fatigueAlarmType,startTime, endTime,driverName,query);
				pageRes = fatigueDriverList;
			}else if("3".equals(type)){
				//夜间禁行
				List<Integer> nightAlarmType = VdmUserInfoUtil.getNightLimitAlarmType();
				IPage<DriverNameAndCount> nightList = alarmService.findDriverAlarmListByDeptIdsAndAlarmTypePage(deptIds,nightAlarmType,startTime, endTime,driverName,query);
				pageRes = nightList;
			}
			return R.data(pageRes);
		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询报错");
		}

	}




	/**
	 * 月报统计
	 * @param month 日期，yyyy-MM
	 * @param user
	 * @return
	 */
	@GetMapping("/reportMonth")
	R<List<ReportSummaryResponse>> reportMonth(String month,Long deptId, BladeUser user){
		if(user == null){
			log.error("用户未授权或未登录");
			return R.fail("用户未授权或未登录");
		}
		try{
			long startTime = DateUtil.getMonthFirstSecondTimestamp(month);
			long endTime = DateUtil.getMonthLastSecondTimestamp(month);
			String[] deptIds = user.getDeptId().split(",");
			List<ReportSummaryResponse> resps = new ArrayList<>();
			if(deptId != null){
				ReportSummary summary = reportService.reportSummary(deptId, month, startTime, endTime);
				if(summary == null){
					return R.data(null, "不存在该企业");
				}
				String deptName = summary.getDeptName();
				ReportSummaryResponse resp = new ReportSummaryResponse();
				resp.setSummary(summary);
				resp.setDeptId(deptId);
				resp.setDeptName(deptName);
				resps.add(resp);
			}else{
				for(String deptIdStr : deptIds){
					Long dId = Long.parseLong(deptIdStr);
					ReportSummary summary = reportService.reportSummary(dId, month, startTime, endTime);
					if(summary == null){
						continue;
					}
					String deptName = summary.getDeptName();
					ReportSummaryResponse resp = new ReportSummaryResponse();
					resp.setSummary(summary);
					resp.setDeptId(dId);
					resp.setDeptName(deptName);
					resps.add(resp);
				}
			}
			return R.data(resps);
		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询报错");
		}
	}



}
