package com.xh.vdm.statistic.mapper.impala;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.dto.TargetAlarmDTO;
import org.apache.ibatis.annotations.Param;
import org.springblade.entity.Alarm;

import java.util.List;

@DS("impala")
public interface ImpalaAlarmMapper2 extends BaseMapper<Alarm> {
	/**
	 * 获取告警信息。
	 * @param startTime
	 * @param endTime
	 * @param deviceIdList
	 * @param current
	 * @param size
	 * @return
	 */
	List<TargetAlarmDTO> getAlarmListWithDev (
		@Param("startTime") Long startTime,
		@Param("endTime") Long endTime,
		@Param("deviceIdList") List<Long> deviceIdList,
		@Param("current") long current,
		@Param("size") long size
	);

	/**
	 * 获取告警信息。
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param current
	 * @param size
	 * @return
	 */
	List<TargetAlarmDTO> getAlarmListWithDept (
		@Param("startTime") Long startTime,
		@Param("endTime") Long endTime,
		@Param("deptIds") String deptIds,
		@Param("current") long current,
		@Param("size") long size
	);
}
