<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatVehOnlineDayMapper">

    <select id="saveOrUpdateVehDuration">
        replace into stat_veh_online_day(licence_plate, licence_color, stat_date, m00,m01,m02,m03,m04,m05,m06,
        m07,m08,m09,m10,m11,m12,m13,m14,m15,m16,m17,m18,m19,m20,m21,m22,m23,total_duration,
        create_time,update_time)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.licencePlate}, #{item.licenceColor}, #{item.statDate}, #{item.m00},#{item.m01},#{item.m02},#{item.m03},#{item.m04},#{item.m05},
            #{item.m06},#{item.m07},#{item.m08},#{item.m09},#{item.m10},#{item.m11},#{item.m12},#{item.m13},
            #{item.m14},#{item.m15},#{item.m16},#{item.m17},#{item.m18},#{item.m19},#{item.m20},#{item.m21},
            #{item.m22},#{item.m23},#{item.totalDuration},#{item.createTime},#{item.updateTime}
            )
        </foreach>
    </select>

    <select id="getOnlineHourCount" resultType="com.xh.vdm.statistic.vo.response.OnlineHourCountResponse">
        <foreach collection="hourList" item="hour" separator=" union ">
            select #{hour} "hour", count(*) count
            from stat_veh_online_day svod
            left join bdm_vehicle bv on svod.licence_plate = bv.licence_plate and svod.licence_color = bv.licence_color
            where m${hour} > 0
            and ( bv.dept_id in (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
            <if test="userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT})
            </if>
            )

        </foreach>
    </select>

</mapper>
