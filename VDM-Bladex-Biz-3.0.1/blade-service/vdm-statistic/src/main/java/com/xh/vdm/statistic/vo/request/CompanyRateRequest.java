package com.xh.vdm.statistic.vo.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Description: 企业评分请求参数
 * @Author: zhouxw
 * @Date: 2022/9/13 3:47 PM
 */
@Data
public class CompanyRateRequest {

    //统计月份
    @NotEmpty(message = "统计月份不能为空")
    private String month;

    //企业id
    private Long deptId;

    //上级平台
    @NotNull(message = "上级平台不能为空")
    private Long ownerId;

    //每页的数量
    private int count;

    //当前页的开始下标
    private int start;
}
