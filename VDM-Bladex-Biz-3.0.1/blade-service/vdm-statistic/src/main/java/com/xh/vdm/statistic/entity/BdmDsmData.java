package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 告警附件表
 */
@Data
@TableName(value = "taxicab.bdm_dsm_data_sh", autoResultMap = true)
public class BdmDsmData implements Serializable {

    /**
     * 车牌颜色
     */
    @TableField(value = "licence_color")
    private Integer licenceColor;

    /**
     * 车牌号
     */
    @TableField(value = "licence_plate")
    private String licencePlate;

    /**
     * 图1
     */
    @TableField(value = "jpg1")
    private String jpg1;

    /**
     * 图2
     */
    @TableField(value = "jpg2")
    private String jpg2;

    /**
     * 图3
     */
    @TableField(value = "jpg3")
    private String jpg3;

    /**
     * 视频
     */
    @TableField(value = "mp4")
    private String mp4;

    /**
     * 附件
     */
    @TableField(value = "bin")
    private String bin;
}
