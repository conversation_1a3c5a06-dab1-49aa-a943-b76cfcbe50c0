package com.xh.vdm.statistic.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.constant.RedisConstants;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.impala.ImpalaAlarm;
import com.xh.vdm.statistic.mapper.StatisticsMapper;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.*;
import com.xh.vdm.statistic.vo.ObjectRestResponse;
import com.xh.vdm.statistic.vo.page.DayPage;
import com.xh.vdm.statistic.vo.request.*;
import com.xh.vdm.statistic.vo.response.*;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/10/26 11:36
 */
@RestController
@RequestMapping("/bt/statistics")
@Slf4j
public class StatisticsController {

    @Autowired
    MapDistanceUtil mapDistanceUtil;

	@Resource
	private IBladeDeptService bladeDeptService;

    @Autowired
    StatisticsService statisticsService;

	@Resource
	private CommonBusiUtil commonBusiUtil;

    @Resource
    StatisticsMapper statisticsMapper;

	//报停异动报警描述
	public static final String CALL_STOP_ALARM_DESC = "报停异动报警";

	@Resource
	private IBamThirdPartyPlatformService thirdPartyPlatformService;

	@Resource
	private IBladeDeptService deptService;

    @Resource
    private CacheUtil cacheUtil;

	@Resource
	private IBladeDictBizService dictBizService;

	@Resource
	private ILocationService locationService;

	@Resource
	private IBdmCallStopChangeRuleService callStopChangeRuleService;

    @Autowired
    DeptUtil deptUtil;

    @Autowired
    protected RedisTemplate<String, String> redisTemplate;

	@Resource
	private IAlarmService alarmService;

    @Autowired
    UserInfoUtil userInfoUtil;

	@Resource
	private CommonBusiUtil busiUtil;

	@Resource
	private IStatVehRunningStateService vehRunningStateService;

    public static SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    @Value("${static.file.path:/statistic/files/}")
    String staticFilePath;

    //前端代理的文件路径，用于在前端访问或者下载使用，可以使用nginx等工具代理
    @Value("${proxy.file.path:/bt/statistics/files/}")
    private String proxyFilePath ;

    @Value("${db_cache.enable:true}")
    private boolean dbCacheEnable;

    @Value("${default.admin_name:admin}")
    private String defaultAdminName;

    @Autowired
    DictionaryUtil dictionaryUtil;

    @Autowired
    protected VehicleUtil vehicleUtil;

    @Resource
    private ICacheSegLimitSpeedMapService cacheSegLimitSpeedMapService;

    @Resource
    private ICacheSecurityInfoService cacheSecurityInfoService;

    @Resource
    private ICacheSegLimitSpeedTerminalService cacheSegLimitSpeedTerminalService;

    @Resource
    private ICacheVehicleOnlineOrOfflineService cacheVehicleOnlineOrOfflineService;

    @Resource
    private ICacheVehicleOnlineRateService cacheVehicleOnlineRateService;

	@Resource
	private IBdmVehicleService vehicleService;

    private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(10, 20, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(5));



    /**
     *车辆超速报表（地图限速）
     * 查车组在时间范围内没有上过线的车，并且输出他的最近的定位时间
     * @param request
     * @param req
     * @return
     */
	// todo: 已改造
    @PostMapping("/statisticsSegLimitSpeedMap")
    @ApiOperation(value = "车辆超速报表（地图限速）",notes = "查车组在时间范围内没有上过线的车，并且输出他的最近的定位时间")
    public ObjectRestResponse<Page<SegLimitSpeedMapResponse>> statisticsSegLimitSpeedMap(HttpServletRequest request, @RequestBody SegLimitSpeedMapRequest req) {
        try{
            req.setDeptList(this.getDeptListFinal(request, req.getDeptId()));
            req.setProfessionList(this.getProfessionList(req.getVehicleUseType()));

            //在执行查询前，首先查询DB缓存表
            if(dbCacheEnable){
                try{
                    IPage<SegLimitSpeedMapResponse> page = cacheSegLimitSpeedMapService.querySegLimitSpeedMapCache(req);
                    return new ObjectRestResponse<>().data(page);
                }catch (Exception e){
                    log.error("查询缓存表[cache_seg_limit_speed_map]失败，将要实时查询业务逻辑",e);
                }
            }

            Page<SegLimitSpeedMapResponse> page = new Page<>(req.getCurrent(),req.getSize());
            long count = statisticsMapper.getSegLimitSpeedMapCount(req);
            page.setTotal(count);
            List<SegLimitSpeedMapResponse> records = statisticsMapper.getSegLimitSpeedMap(req.getCurrent(),req.getSize(),req);
            page.setRecords(records);
            return new ObjectRestResponse<>().data(page);
        }catch (Exception e){
            log.error("查询车辆地图超速失败",e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, "查询车辆地图超速失败");
        }
    }

    /**
     *车辆超速报表（地图限速）
     * @param request
     * @return
     */
	// todo: 已改造
    @GetMapping("/exportSegLimitSpeedMap")
    @ApiOperation(value = "导出车辆超速报表（地图限速）",notes = "导出车辆超速报表（地图限速）")
    public ObjectRestResponse<String> exportSegLimitSpeedMap(HttpServletRequest request, @RequestParam(value = "dept_id",required = false) String deptId,@RequestParam(value = "start_time",required = false) Long startTime,@RequestParam(value = "end_time",required = false) Long endTime,@RequestParam(value = "licence_plate",required = false) String licencePlate,@RequestParam(value = "licence_color",required = false) String licenceColor,@RequestParam(value = "limit_speed",required = false) Integer limitSpeed,@RequestParam(value = "duration_time",required = false) Long durationTime, @RequestParam(value = "vehicle_owner",required = false) Long vehicleOwnerId, @RequestParam(value = "vehicle_use_type",required = false) Long vehicleUseType,@RequestParam(value = "access_mode",required = false) Integer accessMode, @RequestParam(required = false) String exportPath,@RequestParam(required = false) String deptName) throws Exception {
        try{
            //String userName = userInfoUtil.getUserName(request);
            SegLimitSpeedMapRequest req = new SegLimitSpeedMapRequest();
            req.setDeptId(deptId);
            req.setVehicleOwnerId(vehicleOwnerId);
            req.setVehicleUseType(vehicleUseType);
            req.setAccessMode(accessMode);
            req.setEndTime(endTime);
            req.setStartTime(startTime);
            req.setLicencePlate(licencePlate);
            req.setLicenceColor(licenceColor);
            req.setLimitSpeed(limitSpeed);
            req.setDurationTime(durationTime);
            req.setCount(1000000);
            req.setDeptList(this.getDeptListFinal(request, deptId));
            req.setProfessionList(this.getProfessionList(vehicleUseType));

            List<SegLimitSpeedMapResponse> list;
            //在执行查询前，首先查询DB缓存表
            if(dbCacheEnable){
                //如果开启了DB缓存，则先查询缓存
                try{
                    req.setStart(0);
                    req.setCount(Integer.MAX_VALUE);
                    IPage<SegLimitSpeedMapResponse> page = cacheSegLimitSpeedMapService.querySegLimitSpeedMapCache(req);
                    list = page.getRecords();
                }catch (Exception e){
                    log.error("查询缓存表[cache_seg_limit_speed_map]失败，将要实时查询业务逻辑",e);
                    //如果查询缓存失败，则查询实时数据
                    list = statisticsMapper.getSegLimitSpeedMap(1,req.getSize(),req);
                }
            }else{
                //如果没有开启缓存，则查询实时数据
                list = statisticsMapper.getSegLimitSpeedMap(1,req.getSize(),req);
            }






            list.forEach(record ->{
                if(record.getDurationTime()!=null){
                    Long duringTime = Long.valueOf(record.getDurationTime());
                    record.setDurationTime(timeToString(duringTime));
                }
            });
            String title = "车辆超速报表（地图限速）";
            //String[] arrs = {"企业名称","车队名称","车牌号码","车牌颜色","行业类型","车辆归属","车辆接入方式","地图限速报警时间","持续时间","最大速度(km/h)","地图限速(km/h)","经度","纬度","报警位置"};
            //调用Excel导出工具类
    //        ExcelExport.export(response,title,list,arrs);

            String fileName = "";
            /*if(StringUtils.isBlank(exportPath)){
                //如果没有指定导出路径，则按照默认配置
                fileName = ExcelExport.exportExcelFile(title,list,arrs,staticFilePath);
            }else{
                //如果指定了导出路径，则按照给定的配置导出
                fileName = ExcelExport.exportExcelFileWithoutDate(StringUtils.isBlank(deptName)?title:title+"_"+deptName,list,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            }*/


            //改用easyExcel导出
            boolean withDate = false;
            if(!StringUtils.isBlank(exportPath)){
                withDate = true;
            }
            fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , SegLimitSpeedMapResponse.class,withDate);


            return new ObjectRestResponse<>().data(fileName);
        }catch (Exception e){
            log.error("导出车辆超速报表（地图限速）",e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

    /**
     *超速分析报表
     * 统计分段限速情况 终端
     * 超速分析报表（终端限速）
     * @param request
     * @param req
     * @return
     */
	// todo: 已改造
    @PostMapping("/statisticsSegLimitSpeedTerminal")
    @ApiOperation(value = "超速分析报表",notes = "超速分析报表")
    public ObjectRestResponse<Page<SegLimitSpeedTerminalResponse>> statisticsSegLimitSpeedTerminal(HttpServletRequest request, @RequestBody SegLimitSpeedTerminalRequest req) {
        try{
            req.setDeptList(this.getDeptListFinal(request, req.getDeptId()));
            req.setProfessionList(this.getProfessionList(req.getVehicleUseType()));

            //首先查询DB缓存表，如果查询失败再查询实时业务数据
            if(dbCacheEnable){
                try{
                    IPage<SegLimitSpeedTerminalResponse> page = cacheSegLimitSpeedTerminalService.querySegLimitSpeedTerminalCache(req);
                    return new ObjectRestResponse<>().data(page);
                }catch (Exception e){
                    log.error("查询缓存表[cache_seg_limit_speed_terminal]失败，将要实时查询业务逻辑",e);
                }
            }

            Page<SegLimitSpeedTerminalResponse> page = new Page<>(req.getCurrent(),req.getSize());
            long count = statisticsMapper.getSegLimitSpeedTerminalCount(req);
            page.setTotal(count);
            List<SegLimitSpeedTerminalResponse> records = statisticsMapper.getSegLimitSpeedTerminal(req.getCurrent(),req.getSize(),req);
            page.setRecords(records);
            return new ObjectRestResponse<>().data(page);
        }catch (Exception e){
            e.printStackTrace();
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

    /**
     *导出超速分析报表（终端限速）
     * @param request
     * @return
     */
	// todo: 已改造
    @GetMapping("/exportSegLimitSpeedTerminal")
    @ApiOperation(value = "导出超速分析报表（终端限速）",notes = "导出超速分析报表（终端限速）")
    public ObjectRestResponse<String> exportSegLimitSpeedTerminal(HttpServletRequest request, @RequestParam(value = "dept_id",required = false) String deptId,@RequestParam(value = "start_time",required = false) Long startTime,@RequestParam(value = "end_time",required = false) Long endTime,@RequestParam(value = "licence_plate",required = false) String licencePlate ,@RequestParam(value = "licence_color",required = false) Integer licenceColor , @RequestParam(value = "duration_time" , required = false) Long durationTime,@RequestParam(value = "limit_speed" , required = false) Integer limitSpeed,@RequestParam(value = "vehicle_owner",required = false) Long vehicleOwnerId, @RequestParam(value = "vehicle_use_type",required = false) Long vehicleUseType,@RequestParam(value = "access_mode",required = false) Integer accessMode, @RequestParam(required = false) String exportPath,@RequestParam(required = false) String deptName) throws Exception {
        try{
            //String userName = userInfoUtil.getUserName(request);
            SegLimitSpeedTerminalRequest req = new SegLimitSpeedTerminalRequest();
            req.setDeptId(deptId);
            req.setEndTime(endTime);
            req.setStartTime(startTime);
            req.setLicencePlate(licencePlate);
            req.setLicenceColor(licenceColor);
            req.setCount(1000000);
            req.setDurationTime(durationTime);
            req.setLimitSpeed(limitSpeed);
            req.setDeptId(deptId);
            req.setVehicleOwnerId(vehicleOwnerId);
            req.setVehicleUseType(vehicleUseType);
            req.setAccessMode(accessMode);
            req.setDeptList(this.getDeptListFinal(request, deptId));
            req.setProfessionList(this.getProfessionList(vehicleUseType));

            List<SegLimitSpeedTerminalResponse> recordList;
            //首先查询DB缓存表，如果查询失败再查询实时业务数据
            if(dbCacheEnable){
                //如果开启DB缓存，则先查询缓存
                try{
                    req.setStart(0);
                    req.setCount(Integer.MAX_VALUE);
                    IPage<SegLimitSpeedTerminalResponse> page = cacheSegLimitSpeedTerminalService.querySegLimitSpeedTerminalCache(req);
                    recordList = page.getRecords();
                }catch (Exception e){
                    log.error("查询缓存表[cache_seg_limit_speed_terminal]失败，将要实时查询业务逻辑",e);
                    //如果查询缓存失败
                    recordList = statisticsMapper.getSegLimitSpeedTerminal(1,req.getSize(),req);
                }
            }else{
                //如果没有开启缓存，则查询实时数据
                recordList = statisticsMapper.getSegLimitSpeedTerminal(1,req.getSize(),req);
            }


            recordList.forEach(record ->{
                if(record.getDurationTime()!=null){
                    Long duringTime = Long.valueOf(record.getDurationTime());
                    record.setDurationTime(timeToString(duringTime));
                }
            });
            String title = "超速分析报表（终端限速）";
            //String[] arrs = {"车牌号码","企业名称","车队名称","车牌颜色","行业类型","车辆归属","车辆接入方式","开始时间","结束时间","持续时间(秒)","最小速度(km/h)","最大速度(km/h)","平均速度(km/h)","终端阈值(km/h)","起点经度","起点纬度","超速起点","终点经度","终点纬度","超速终点","驾驶员信息"};
            //调用Excel导出工具类
            //String fileName = ExcelExport.exportExcelFile(title,recordList,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            String fileName = "";
            /*if(StringUtils.isBlank(exportPath)){
                //如果没有指定导出路径，则按照默认配置
                fileName = ExcelExport.exportExcelFile(title,recordList,arrs,staticFilePath);
            }else{
                //如果指定了导出路径，则按照给定的配置导出
                fileName = ExcelExport.exportExcelFileWithoutDate(StringUtils.isBlank(deptName)?title:title+"_"+deptName,recordList,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            }*/


            //改用easyExcel导出
            boolean withDate = false;
            if(!StringUtils.isBlank(exportPath)){
                withDate = true;
            }
            fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , recordList , SegLimitSpeedTerminalResponse.class,withDate);


            return new ObjectRestResponse<>().data(fileName);
        }catch (Exception e){
            log.error("导出超速分析报表（终端限速）",e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

    /**
     *疲劳驾驶统计表
     * @param request
     * @param req
     * @return
     */
    // todo: 已改造
	@PostMapping("/statisticsFatigueDriving")
    @ApiOperation(value = "疲劳驾驶统计表",notes = "疲劳驾驶统计表")
    public ObjectRestResponse<Page<FatigueDrivingResponse>> statisticsFatigueDriving(HttpServletRequest request, @RequestBody FatigueDrivingRequest req) {
        try{
            req.setDeptList(this.getDeptListFinal(request, req.getDeptId()));
            req.setProfessionList(this.getProfessionList(req.getVehicleUseType()));
            Page<FatigueDrivingResponse> page = new Page<>(req.getCurrent(),req.getSize());
            long count = statisticsMapper.getFatigueDrivingCount(req);
            page.setTotal(count);
            List<FatigueDrivingResponse> records = statisticsMapper.getFatigueDriving(req.getCurrent(),req.getSize(),req);
            page.setRecords(records);
            return new ObjectRestResponse<>().data(page);
        }catch (Exception e){
            log.error("疲劳驾驶统计失败",e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

    /**
     *导出疲劳驾驶统计表
     * @param request
     * @return
     */
    // todo: 已改造
	@GetMapping("/exportFatigueDriving")
    @ApiOperation(value = "导出疲劳驾驶统计表",notes = "导出疲劳驾驶统计")
    public ObjectRestResponse<String> exportFatigueDriving(HttpServletRequest request, @RequestParam(value = "dept_id",required = false) String deptId,@RequestParam(value = "start_time",required = false) Long startTime,@RequestParam(value = "end_time",required = false) Long endTime,@RequestParam(value = "licence_plate",required = false) String licencePlate,@RequestParam(value = "licence_color",required = false) String licenceColor,@RequestParam(value = "vehicle_owner",required = false) Long vehicleOwnerId, @RequestParam(value = "vehicle_use_type",required = false) Long vehicleUseType,@RequestParam(value = "access_mode",required = false) Integer accessMode, @RequestParam(required = false) String exportPath,@RequestParam(required = false) String deptName) throws Exception {
        try{
//            String userName = userInfoUtil.getUserName(request);
            FatigueDrivingRequest req = new FatigueDrivingRequest();
            req.setDeptId(deptId);
            req.setEndTime(endTime);
            req.setStartTime(startTime);
            req.setLicencePlate(licencePlate);
            req.setLicenceColor(licenceColor);
            req.setCount(1000000);
            req.setDeptId(deptId);
            req.setVehicleOwnerId(vehicleOwnerId);
            req.setVehicleUseType(vehicleUseType);
            req.setAccessMode(accessMode);
            req.setDeptList(this.getDeptListFinal(request, deptId));
            req.setProfessionList(this.getProfessionList(vehicleUseType));
            List<FatigueDrivingResponse> list = statisticsMapper.getFatigueDriving(1,req.getSize(),req);
            list.forEach(record ->{
                if(record.getDurationTime()!=null){
                    Long duringTime = Long.valueOf(record.getDurationTime());
                    record.setDurationTime(timeToString(duringTime));
                }
            });
//            List list = page.getRecords();
            String title = "疲劳驾驶统计表";
            //String[] arrs = {"企业名称","车队名称","车牌号码","车牌颜色","行业类型","车辆归属","车辆接入方式","报警类型","疲劳驾驶开始时间","疲劳驾驶结束时间","持续时间","速度","开始位置","结束位置"};
            //调用Excel导出工具类
    //        ExcelExport.export(response,title,list,arrs);
            //String fileName = ExcelExport.exportExcelFile(title,list,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            String fileName = "";
            /*if(StringUtils.isBlank(exportPath)){
                //如果没有指定导出路径，则按照默认配置
                fileName = ExcelExport.exportExcelFile(title,list,arrs,staticFilePath);
            }else{
                //如果指定了导出路径，则按照给定的配置导出
                fileName = ExcelExport.exportExcelFileWithoutDate(StringUtils.isBlank(deptName)?title:title+"_"+deptName,list,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            }*/


            //改用easyExcel导出
            boolean withDate = false;
            if(!StringUtils.isBlank(exportPath)){
                withDate = true;
            }
            fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , FatigueDrivingResponse.class,withDate);

            return new ObjectRestResponse<>().data(fileName);
        }catch (Exception e){
            log.error("导出疲劳驾驶统计表",e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

    /**
     *查询车辆在线情况抽查
     * 查询车辆在线情况抽查,查询在指定时间内，车辆的上线情况，车辆在这段之间内上线过就算
     * @param request
     * @param request
     * @return
     */
    @PostMapping("/vehicleOnline")
    @ApiOperation(value = "查询车辆在线情况抽查",notes = "查询车辆在线情况抽查,查询在指定时间内，车辆的上线情况，车辆在这段之间内上线过就算")
    public R<IPage<VehicleOnlineResponse>> vehicleOnline(@RequestBody VehicleOnlineRequest request, Query query,  BladeUser user) {
        try{
			//1.查询企业
			String deptId = user.getDeptId();
            IPage<BladeDept> page = statisticsService.findDept(request.getDeptIdList(), deptId, query);
            List<VehicleOnlineResponse> list = new ArrayList<>();
			//2.对每个企业执行抽检
            page.getRecords().forEach(record ->{
                Long queryDeptId = record.getId();
				//查询所有子部门
				List<BladeDept> childrenDepts = statisticsService.findChildrenDept(queryDeptId);
				List<Long> deptIdList = new ArrayList<>();
				childrenDepts.forEach(item -> {
					deptIdList.add(item.getId());
				});
				//添加本部门
				deptIdList.add(queryDeptId);
                VehicleOnlineResponse vehicleOnlineResponse = statisticsMapper.getVehicleOnline(deptIdList,queryDeptId);
				if (vehicleOnlineResponse == null) {
					return;
				}

                vehicleOnlineResponse.setMonitor(user.getAccount());
                list.add(vehicleOnlineResponse);
            });

			IPage<VehicleOnlineResponse> resPage = new Page<>();
			resPage.setCurrent(query.getCurrent());
			resPage.setSize(query.getSize());
			resPage.setTotal(page.getTotal());
			resPage.setRecords(list);

            return R.data(resPage);
        }catch (Exception e){
			log.error("查询失败",e);
            return R.fail("查询失败");
        }
    }

	/**
	 *车辆在线情况抽查 导出
	 * 查询车辆在线情况抽查,查询在指定时间内，车辆的上线情况，车辆在这段之间内上线过就算
	 * @param request
	 * @param request
	 * @return
	 */
	@PostMapping("/vehicleOnline/export")
	@ApiOperation(value = "车辆在线情况抽查",notes = "查询车辆在线情况抽查,查询在指定时间内，车辆的上线情况，车辆在这段之间内上线过就算")
	public R<String> vehicleOnlineExport(@RequestBody VehicleOnlineRequest request, Query query,  BladeUser user, String exportPath, String deptName) {
		try{
			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<VehicleOnlineResponse>> pageRes = vehicleOnline(request, query, user);
			List<VehicleOnlineResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "车辆在线情况抽查报表";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , VehicleOnlineResponse.class,withDate);
			return R.data(fileName);
		}catch (Exception e){
			log.error("导出失败",e);
			return R.fail("导出失败");
		}
	}

    /**
     *查询车辆在线情况抽查
     * 查询车辆在线情况抽查,查询在指定时间内，车辆的上线情况，车辆在这段之间内上线过就算
     * @param request
     * @return
     */
    @GetMapping("/exportVehicleOnline")
    @ApiOperation(value = "导出车辆在线情况抽查",notes = "导出车辆在线情况抽查,查询在指定时间内，车辆的上线情况，车辆在这段之间内上线过就算")
    public ObjectRestResponse<String> exportVehicleOnline(HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "dept_id",required = false) String deptId,@RequestParam(value = "licence_plate",required = false) String licencePlate, @RequestParam(required = false) String exportPath,@RequestParam(required = false) String deptName) throws Exception {
       /* try{
            String userName = defaultAdminName;
            if(request != null){
                userName = userInfoUtil.getUserName(request);
            }
            if(StringUtils.isBlank(userName)){
                userName = defaultAdminName;
            }
            VehicleOnlineRequest req = new VehicleOnlineRequest();
            //req.setDeptId(deptId);
            req.setDeptList(this.getDeptListFinal(request, deptId));
//            req.setStartTime(startTime);
//            req.setLicencePlate(licencePlate);
            req.setCount(1000000);
//            Page<VehicleOnlineResponse> page = statisticsService.vehicleOnline(req);
//            page.getRecords().forEach(record ->{
//                record.setMonitor(userName);
//            });
//            List list = page.getRecords();
            //req.setDeptId(null);
            IPage<HashMap> page = statisticsService.findDept(null,null,null);
            List<VehicleOnlineResponse> list = new ArrayList<>();

            //使用多线程处理
            int totalCount = page.getRecords().size();
            if(totalCount > 100){
                //使用10个线程进行处理
                int batch = totalCount / 10;
                int nowCount = 0;
                int batchCount = totalCount % 10 == 0?10:11;
                CountDownLatch countDownLatch = new CountDownLatch(batchCount);
                //记录整体顺序：
                Map<Integer,List<VehicleOnlineResponse>> map = new HashMap<>();
                int batchIndex = 0;
                for(int i = 1 ; i * batch <= totalCount ; i++){
                    int finalI = i;
                    String finalUserName = userName;
                    threadPool.submit(() -> {
                        List<VehicleOnlineResponse> localList = new ArrayList<>();
                        page.getRecords().subList((finalI -1)*batch , finalI *batch).forEach(record ->{
                            Long queryDeptId = (Long)record.get("id");
                            List<Long> deptIdList = null;
                            try {
                                deptIdList = deptUtil.acquireDepts(queryDeptId.toString());
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            VehicleOnlineResponse vehicleOnlineResponse = statisticsMapper.getVehicleOnline( deptIdList);
                            vehicleOnlineResponse.setMonitor(finalUserName);
                            //list.add(vehicleOnlineResponse);
                            localList.add(vehicleOnlineResponse);
                        });
                        map.put(finalI , localList);
                        countDownLatch.countDown();
                    });
                    nowCount = i * batch;
                    batchIndex = i;
                }


                //执行剩余的数据导出
                int finalBatchIndex = batchIndex;
                int finalNowCount = nowCount;
                String finalUserName1 = userName;
                if(totalCount % 10 > 0){
                    threadPool.submit(()->{
                        List<VehicleOnlineResponse> localList = new ArrayList<>();
                        page.getRecords().subList(finalNowCount, totalCount).forEach(record ->{
                            Long queryDeptId = (Long)record.get("id");
                            List<Long> deptIdList = null;
                            try {
                                deptIdList = deptUtil.acquireDepts(queryDeptId.toString());
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            VehicleOnlineResponse vehicleOnlineResponse = statisticsMapper.getVehicleOnline(deptIdList);
                            vehicleOnlineResponse.setMonitor(finalUserName1);
                            //list.add(vehicleOnlineResponse);
                            localList.add(vehicleOnlineResponse);
                        });
                        map.put(finalBatchIndex +1 , localList);
                        countDownLatch.countDown();
                    });
                }
                countDownLatch.await();

                //全部执行完成之后，拼装最终的list
                for(int i = 1 ; i <= map.size() ; i++){
                    if(map.get(new Integer(i)) != null){
                        list.addAll(map.get(new Integer(i)));
                    }
                }


            }else{
                String finalUserName2 = userName;
                page.getRecords().forEach(record ->{
                    Long queryDeptId = (Long)record.get("id");
                    List<Long> deptIdList = null;
                    try {
                        deptIdList = deptUtil.acquireDepts(queryDeptId.toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    VehicleOnlineResponse vehicleOnlineResponse = statisticsMapper.getVehicleOnline( deptIdList);
                    vehicleOnlineResponse.setMonitor(finalUserName2);
                    list.add(vehicleOnlineResponse);
                });
            }


            String title = "车辆在线情况抽查统计表";
            //String[] arrs = {"企业名称","车队名称","抽查时间","车辆总数","在线车辆数","离线车辆数","上线率","监控人员"};
            //String fileName = ExcelExport.exportExcelFile(title,list,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            String fileName = "";
            *//*if(StringUtils.isBlank(exportPath)){
                //如果没有指定导出路径，则按照默认配置
                fileName = ExcelExport.exportExcelFile(title,list,arrs,staticFilePath);
            }else{
                //如果指定了导出路径，则按照给定的配置导出
                fileName = ExcelExport.exportExcelFileWithoutDate(StringUtils.isBlank(deptName)?title:title+"_"+deptName,list,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            }*//*


            //改用easyExcel导出
            boolean withDate = false;
            if(!StringUtils.isBlank(exportPath)){
                withDate = true;
            }
            fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , VehicleOnlineResponse.class,withDate);



            return new ObjectRestResponse<>().data(fileName);
        }catch (Exception e){
            log.error("导出车辆在线情况抽查 失败" , e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }*/
		return null;
    }

    /**
     *查询车辆运行情况巡检  车辆运行情况巡检抽查登记表
     * 查询车辆运行情况巡检，主要是查看当前时间段内的哪些车辆在线以及最后上线时间
     * @param request
     * @param request
     * @return
     */
    @PostMapping("/vehicleOperation")
    @ApiOperation(value = "查询车辆运行情况巡检",notes = "查询车辆运行情况巡检，主要是查看当前时间段内的哪些车辆在线以及最后上线时间")
    public R<IPage<VehicleOperationResponse>> vehicleOperation(@RequestBody CommonBaseRequest request, Query query, BladeUser user) {
        try{
            //1.整理查询参数
			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			request.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
			if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
				vehicleUseTypes.add(request.getVehicleUseType());
			}
			request.setProfessionList(vehicleUseTypes);
			request.setUserId(user.getUserId());

            IPage<VehicleOperationResponse> page = statisticsService.vehicleOperation(request, user.getTenantId(), query);
            List<VehicleOperationResponse> list = page.getRecords();
            List<Integer> indList = new ArrayList<>();
            List<Point> pointList = new ArrayList<>();
            for(int i=0;i<list.size();i++){
                list.get(i).setMonitor(user.getAccount());
                Double longitude = list.get(i).getLongitude();
                Double latitude = list.get(i).getLatitude();
                if(longitude!=null&&latitude!=null&&longitude!=0&&latitude!=0){
                    indList.add(i);
                    pointList.add(new Point(longitude, latitude));
                }
            }
			//todo 已经改成了访问国能地图逆地理编码，但是测试环境服务器访问不通逆地理编码生产环境地址，所以此处暂不处理物理地址
            /*List<String> addressList = mapDistanceUtil.getAddress(pointList);
            for(int i=0;i<indList.size();i++){
                list.get(indList.get(i)).setLocAddr(addressList.get(i));
            }*/
            page.setRecords(list);
            return R.data(page);
        }catch (Exception e){
            e.printStackTrace();
            return R.fail("查询失败");
        }
    }




	/**
	 *查询车辆运行情况巡检 导出
	 * 查询车辆运行情况巡检，主要是查看当前时间段内的哪些车辆在线以及最后上线时间
	 * @param request
	 * @param request
	 * @return
	 */
	@PostMapping("/vehicleOperation/export")
	@ApiOperation(value = "查询车辆运行情况巡检",notes = "查询车辆运行情况巡检，主要是查看当前时间段内的哪些车辆在线以及最后上线时间")
	public R<String> vehicleOperationExport(@RequestBody CommonBaseRequest request, Query query, BladeUser user, String exportPath, String deptName) {
		try{
			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<VehicleOperationResponse>> pageRes = vehicleOperation(request, query, user);
			List<VehicleOperationResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "车辆运行情况巡检报表";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , VehicleOperationResponse.class,withDate);
			return R.data(fileName);
		}catch (Exception e){
			log.error("导出报错",e);
			return R.fail("导出失败");
		}
	}

    /**
     *导出车辆运行情况巡检
     * @param request
     * @return
     */
    @GetMapping("/exportVehicleOperation")
    @ApiOperation(value = "导出车辆运行情况巡检",notes = "导出车辆运行情况巡检")
    public ObjectRestResponse<String> exportVehicleOperation(HttpServletRequest request, @RequestParam(value = "dept_id",required = false) String deptId,@RequestParam(value = "licence_plate",required = false) String licencePlate, @RequestParam(value = "licence_color",required = false) String licenceColor,@RequestParam(value = "vehicle_owner",required = false) Long vehicleOwnerId, @RequestParam(value = "vehicle_use_type",required = false) Long vehicleUseType,@RequestParam(value = "access_mode",required = false) Integer accessMode, @RequestParam(required = false) String exportPath,@RequestParam(required = false) String deptName) throws Exception {
        long start1 = System.currentTimeMillis();
        try{
            String userName = defaultAdminName;
            if(request != null){
                userName = userInfoUtil.getUserName(request);
            }
            if(StringUtils.isBlank(userName)){
                userName = defaultAdminName;
            }
            VehicleOperationRequest req = new VehicleOperationRequest();
            req.setDeptId(deptId);
            req.setVehicleOwnerId(vehicleOwnerId);
            req.setVehicleUseType(vehicleUseType);
            req.setAccessMode(accessMode);
            req.setLicencePlate(licencePlate);
            req.setLicenceColor(licenceColor);
            req.setDeptList(this.getDeptListFinal(request, deptId));
            req.setProfessionList(this.getProfessionList(vehicleUseType));
            req.setCount(1000000);
            //不包含最后上线时间，后续提供
            Page<VehicleOperationResponse> page = statisticsService.vehicleOperationWithoutLastOnlineTime(req);
            List<VehicleOperationResponse> list = page.getRecords();
            long end1 = System.currentTimeMillis();
            log.info("[车辆运行情况巡检]查询数据耗时："+((end1 - start1)/1000));

            //获取redis中的地址数据，存储在map中，然后比较，代替逐条查询redis
            Map<Object,Object> map = redisTemplate.opsForHash().entries(RedisConstants.HashKey.VEHICLE_INFO_HASH_KEY);

            //获取颜色缓存，存储在map中，防止频繁访问redis
            //key: 颜色中文名称  value：颜色值
            Map<String, String> colorMap = cacheUtil.getVehicleColorValueMap();

            long start2 = System.currentTimeMillis();
            List<Integer> indList = new ArrayList<>();
            List<Point> pointList = new ArrayList<>();
            List<String> licencePlateList = new ArrayList<>();
            //如果之前有数据，判断哪些变化了的，对变化了的进行更新
            for (int i = 0; i < list.size(); i++) {
                list.get(i).setMonitor(userName);
                VehicleOperationResponse vehicleOffline = list.get(i);
                Double longitude = list.get(i).getLongitude();
                Double latitude = list.get(i).getLatitude();
                licencePlateList.add(list.get(i).getLicencePlate());
                if(longitude!=null&&latitude!=null&&longitude!=0&&latitude!=0){
                    //判断是否与Redis位置数据相同
                    //Vehicle vehicle1 = vehicleUtil.acquireVehicleByLicencePlate(vehicleOffline.getLicencePlate(), cacheUtil.getVehicleColorCodeByName(vehicleOffline.getLicenceColor())+"");
                    Object obj = map.get(vehicleOffline.getLicencePlate()+"~"+colorMap.get(vehicleOffline.getLicenceColor()));
                    if(obj != null){
                        Vehicle vehicle1 = JSONObject.parseObject(obj.toString(),Vehicle.class);
                        if(vehicle1!=null){
                            if(vehicle1.getAddress()!=null&&!"".equals(vehicle1.getAddress())&&longitude.equals(vehicle1.getLongitude())&&latitude.equals(vehicle1.getLatitude())){
                                vehicleOffline.setLocAddr(vehicle1.getAddress());
                            }else{
                                indList.add(i);
                                pointList.add(new Point(longitude, latitude));
                            }
                        }else{
                            indList.add(i);
                            pointList.add(new Point(longitude, latitude));
                        }
                    }
                }else {
                    list.get(i).setLocAddr("");
                }
            }


            //批量查询车辆最后上线时间
            //列表中缺少数据，造成部分车辆没有最后上线时间；
            //现在查询太慢，可以考虑分批次执行（in超过一定数量，就不走索引了，分批次，一批1000即可）
            //构建map，方便查询
            Map<String,Date> lastOnlineTimeMap = new HashMap<>();
            List<LastOnlineTimeEntity> lastOnlineTimeList = new ArrayList<>();
            int i = 0;
            CountDownLatch countDownLatch = new CountDownLatch(licencePlateList.size() / 1000);
            for(i = 0; i < licencePlateList.size() / 1000; i++){
                int finalI = i;
                threadPool.submit(() -> {
                    List<String> tmpList = licencePlateList.subList(finalI * 1000, (finalI +1)*1000);
                    List<LastOnlineTimeEntity> onlineTimeTmpList = statisticsService.findLastOnlineTime(tmpList);
                    lastOnlineTimeList.addAll(onlineTimeTmpList);
                    countDownLatch.countDown();
                });
            }
            //执行最后一批的查询
            List<String> tmpList = licencePlateList.subList(i * 1000, licencePlateList.size());
            List<LastOnlineTimeEntity> onlineTimeTmpList = statisticsService.findLastOnlineTime(tmpList);
            lastOnlineTimeList.addAll(onlineTimeTmpList);
            countDownLatch.await();
            for(LastOnlineTimeEntity entity : lastOnlineTimeList){
                String color = null;
                if(StringUtils.isNotBlank(entity.getLicenceColor()) && colorMap.containsValue(Integer.parseInt(entity.getLicenceColor()))){
                    color = entity.getLicenceColor();
                }
                lastOnlineTimeMap.put(entity.getLicencePlate()+"~"+color,entity.getLastOnlineTime());
                if(entity.getLicencePlate().equals("新AA2859")){
                    System.out.println(lastOnlineTimeMap.get(entity.getLicencePlate()+"~"+color));
                }
            }
            //设置最后上线时间
            for(VehicleOperationResponse v : list){
                v.setLastOnlineTime(lastOnlineTimeMap.get(v.getLicencePlate()+"~"+colorMap.get(v.getLicenceColor())));
                if(v.getLicencePlate().equals("新AA2859")){
                    System.out.println(lastOnlineTimeMap.get(v.getLicencePlate()+"~"+colorMap.get(v.getLicenceColor())));
                }
            }

            page.setRecords(list);
            long end2 = System.currentTimeMillis();
            log.info("[车辆运行情况巡检]设置地址、设置最后上线时间耗时："+((end2 - start2)/1000));

            long start3 = System.currentTimeMillis();
            String title = "车辆运行情况巡检抽查登记表";
            //String[] arrs = {"车牌号码","企业名称","车队名称","车牌颜色","行业类型","车辆归属","车辆接入方式","抽查时间","车辆点名","行驶轨迹","最后上线时间","车速","经度","纬度","地理位置","监控人员"};
            String fileName = "";



            /*if(StringUtils.isBlank(exportPath)){
                //如果没有指定导出路径，则按照默认配置
                fileName = ExcelExport.exportExcelFile(title,list,arrs,staticFilePath);
            }else{
                //如果指定了导出路径，则按照给定的配置导出
                fileName = ExcelExport.exportExcelFileWithoutDate(StringUtils.isBlank(deptName)?title:title+"_"+deptName,list,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            }*/

            //改用easyExcel导出
            boolean withDate = false;
            if(!StringUtils.isBlank(exportPath)){
                withDate = true;
            }
            fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , VehicleOperationResponse.class,withDate);




            long end3 = System.currentTimeMillis();
            log.info("[车辆运行情况巡检]数据导出耗时：" + ((end3 - start3)/1000));
            long end = System.currentTimeMillis();
            log.info("[车辆运行情况巡检]数据导出总共耗时 "+ ((end - start1) / 1000));
            return new ObjectRestResponse<>().data(fileName);
        }catch (Exception e){
            log.error("导出车辆运行情况巡检失败",e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

    /**
     *车辆日里程统计查询
     * @param req
     * @return
     */
    @PostMapping("/queryDayMileage")
    @ApiOperation(value = "车辆日里程统计查询",notes = "车辆日里程统计查询")
    public R<IPage<Map<String,Object>>> queryDayMileage(@RequestBody CommonBaseCrossMonthWithLineRequest req, Query query, BladeUser user) {
        try{
			boolean containVehicleList = false;
			if(req.getStartTime()==null||req.getStartTime()==0){
				//如果开始时间为空，则设置为7天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 7);
				long secondTimestamp = DateUtil.getDayFirstSecondTimestamp(date.getTime() / 1000);
				req.setStartTime(secondTimestamp);
			}
			if(req.getEndTime()==null||req.getEndTime()==0){
				//如果结束时间为空，则设置为1天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 1);
				long secondTimestamp = DateUtil.getDayLastSecondTimestamp(date.getTime() / 1000);
				req.setEndTime(secondTimestamp);
			}

			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			req.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(req.getVehicleUseType()+"");
			if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
				vehicleUseTypes.add(req.getVehicleUseType());
			}
			req.setProfessionList(vehicleUseTypes);
			req.setUserId(user.getUserId());

			//2.查看是否指定了车辆
			List<BdmVehicle> vehicleList = new ArrayList<>();
			if(req.getVehicleIdList() == null || req.getVehicleIdList().size() < 1){
				//如果没有指定车辆，则根据条件查询车辆
				CommonBaseCrossMonthRequest request = new CommonBaseCrossMonthRequest();
				BeanUtils.copyProperties(req, request);
				IPage<BdmVehicle> list = vehicleService.findVehiclesByCondition(request, query);
				vehicleList = list.getRecords();
				List<Long> vehicleIds = new ArrayList<>();
				vehicleList.forEach(item -> {
					vehicleIds.add(item.getId().longValue());
				});
				req.setVehicleIdList(vehicleIds);
			}else{
				//如果指定了车辆，则查询车辆列表
				containVehicleList = true;
				vehicleList = vehicleService.listByIds(req.getVehicleIdList());
			}

			if(vehicleList.size() < 1 ){
				log.info("车辆列表为空，不执行后边的查询操作");
				return R.data(null);
			}


           	List<DateListAndMonth> dateList = DateUtil.getDateListAndMonth(req.getStartTime(), req.getEndTime());
			List<DateListAndMonthWithLine> dmList = new ArrayList<>();
			dateList.forEach(item -> {
				List<String> dList = item.getDateList();
				List<String> dListTmp = new ArrayList<>();
				dList.forEach(d -> {
					dListTmp.add(d.substring(8,10));
				});
				String monthLine = item.getMonth();
				String month = item.getMonth().replace("-","");
				DateListAndMonthWithLine dm = new DateListAndMonthWithLine();
				dm.setMonth(month);
				dm.setMonthLine(monthLine);
				dm.setDateList(dListTmp);
				dmList.add(dm);
			});
			req.setDmList(dmList);
			//里程单位为：km
			List<Map<String,Object>> resList = vehRunningStateService.findVehicleMileageDay(req, query);


			//添加在线天数
			List<String> vehicles = new ArrayList<>();
			resList.forEach(record ->{
				String licencePlate = (String)record.get("licencePlate");
				String licenceColor = record.get("licenceColor")+"";
				vehicles.add(licencePlate+"~"+licenceColor);
			});
			List<VehicleOnlineCount> vList = vehicleService.findVehicleOnlineCount(vehicles, req.getStartTime(), req.getEndTime());
			//转换为map
			Map<String,Integer> vMap = new HashMap<>();
			vList.forEach(item -> {
				vMap.put(item.getLicencePlate()+"~"+item.getLicenceColor(), item.getOnlineDaysCount());
			});

			resList.forEach(item -> {
				String licencePlate = (String)item.get("licencePlate");
				String licenceColor = item.get("licenceColor")+"";
				String key = licencePlate + "~" + licenceColor;
				item.put("onlineDaysCount", vMap.get(key));
			});

			//查询车辆总数
			CommonBaseCrossMonthRequest request = new CommonBaseCrossMonthRequest();
			BeanUtils.copyProperties(req, request);
			if(!containVehicleList){
				//如果没有指定车辆
				request.setVehicleIdList(null);
			}else{
				//如果指定了车辆
				List<Long> vIdList = new ArrayList<>();
				vehicleList.forEach(v -> {
					vIdList.add(v.getId().longValue());
				});
				request.setVehicleIdList(vIdList);
			}

			long count = vehicleService.findVehiclesByConditionCount(request);
			Page<Map<String,Object>> page = new Page<>();
			page.setSize(query.getSize());
			page.setCurrent(query.getCurrent());
			page.setRecords(resList);
			page.setTotal(count);

            return R.data(page);
        }catch (Exception e){
            e.printStackTrace();
            log.error("车辆日里程统计出错",e);
            return R.fail("查询出错");
        }
    }


	/**
	 *车辆日里程统计 导出
	 * @param req
	 * @return
	 */
	@PostMapping("/queryDayMileage/export")
	@ApiOperation(value = "车辆日里程统计导出",notes = "车辆日里程统计导出")
	public R<String> queryDayMileageExport(@RequestBody CommonBaseCrossMonthWithLineRequest req, Query query, BladeUser user, String exportPath, String deptName) {
		try{
			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			List<Map<String, Object>> list = new ArrayList<>();
			R<IPage<Map<String,Object>>> pageRes = queryDayMileage(req, query, user);
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			List<ExcelHead> headList = new ArrayList<>();
			headList.add(new ExcelHead("deptName","企业名称"));
			headList.add(new ExcelHead("licencePlate","车牌号"));
			headList.add(new ExcelHead("licenceColorDesc","车牌颜色"));
			headList.add(new ExcelHead("vehicleUseTypeDesc","行业类型"));
			headList.add(new ExcelHead("vehicleOwner","车辆归属"));
			headList.add(new ExcelHead("accessMode","接入方式"));
			//headList.add(new ExcelHead("onlineDaysCount","在线天数"));




			Map<String,Object> map = list.get(0);
			map.forEach((k,v) -> {
				if(k.indexOf("-")>0){
					headList.add(new ExcelHead(k,k));
				}
			});

			//补充中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
				List<VehicleDailyMileageExportResponse> veList = new ArrayList<>();
				list.forEach(item -> {
					//增加 licenceColorDesc 和 vehicleUseTypeDesc
					item.put("licenceColorDesc",licenceColorMap.get(item.get("licenceColor")+""));
					item.put("vehicleUseTypeDesc", vehicleUseTypeMap.get(item.get("vehicleUseType")+""));
					item.put("accessMode",accessModeMap.get(item.get("accessMode")));
					//删除 licenceColor 和 vehicleUseType
					item.remove("licenceColor");
					item.remove("vehicleUseType");
				});

			}


			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "车辆日里程统计报表";
			String fileName = "";

			//fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , Map.class,withDate);


			fileName = EasyExcelUtils.exportDynamicColumnWithType(StringUtils.isBlank(exportPath)?staticFilePath:exportPath, proxyFilePath, title ,headList, list, false);


			return R.data(fileName);
		}catch (Exception e){
			log.error("导出出错",e);
			return R.fail("导出出错");
		}
	}

    /**
     *导出车辆日里程统计
     * @return
     */
    @GetMapping("/exportDayMileage")
    @ApiOperation(value = "导出车辆日里程统计",notes = "导出车辆日里程统计")
    public ObjectRestResponse<String> exportDayMileage(HttpServletRequest request,@RequestParam(value = "dept_id",required = false) String deptId,@RequestParam(value = "start_time",required = false) Long startTime,@RequestParam(value = "end_time",required = false) Long endTime,@RequestParam(value = "licence_plate",required = false) String licencePlate,@RequestParam(value = "licence_color",required = false) String licenceColor, @RequestParam(value = "vehicle_owner",required = false) Long vehicleOwnerId, @RequestParam(value = "vehicle_use_type",required = false) Long vehicleUseType,@RequestParam(value = "access_mode",required = false) Integer accessMode, @RequestParam(required = false) String exportPath,@RequestParam(required = false) String deptName ) throws Exception {
            try{
                DayMileageRequest req = new DayMileageRequest();
                /*req.setStartTime(startTime);
                req.setEndTime(endTime);
                req.setDeptId(deptId);
                req.setCount(1000000);
                req.setLicencePlate(licencePlate);
                req.setLicenceColor(licenceColor);
                req.setVehicleOwnerId(vehicleOwnerId);
                req.setVehicleUseType(vehicleUseType);
                req.setAccessMode(accessMode);
                req.setDeptId(deptId);
                req.setDeptList(this.getDeptListFinal(request, deptId));
                req.setProfessionList(this.getProfessionList(vehicleUseType));*/
                if(req.getStartTime()==null||req.getStartTime()==0){
                    throw new Exception("开始时间不能为空");
                }
                if(req.getEndTime()==null||req.getEndTime()==0){
                    req.setEndTime((System.currentTimeMillis())/1000);
                }
                List<String> dateList = getDays(req.getStartTime(), req.getEndTime());
                req.setDateList(dateList);
//                req.setStartTime(dateFormat.parse(dateList.get(0)).getTime()/1000);
//                req.setEndTime(dateFormat.parse(dateList.get(dateList.size()-1)).getTime()/1000);
                Page<HashMap> page = statisticsService.queryDayMileage(req);
                List<List> resultList = new ArrayList<>();
                page.getRecords().forEach(record ->{
                    ArrayList result = new ArrayList();
                    result.add(record.get("enterprise"));//企业名称
                    result.add(record.get("dept_name"));//车队名称
                    result.add(record.get("licence_plate"));
                    result.add(record.get("licence_color"));
                    result.add(record.get("vehicle_model"));
                    result.add(record.get("vehicle_owner"));
                    result.add(record.get("access_mode"));
                    result.add(record.get("total_mileage"));

                    String statisticDate  = (String)record.get("statistic_date");
                    String mileage  = (String)record.get("mileage");
                    HashMap<String,Double> dateMileageMap = new HashMap<>();
                    if(mileage!=null){
                        String[] statisticDateList = statisticDate.split(",");
                        String[] mileageList = mileage.split(",");
                        for(int i=0;i<mileageList.length;i++){
                            dateMileageMap.put(statisticDateList[i],Double.valueOf(mileageList[i]));
                        }
                    }
                    List<Double> mileageList = new ArrayList<>();
                    for(int i=0;i<dateList.size();i++){
                        Double mileageData = dateMileageMap.get(dateList.get(i));
                        result.add(mileageData==null?0:mileageData);
                    }
                    resultList.add(result);
                });
                String title = "车辆日里程统计查询";
                String[] arrs = {"企业名称","车队名称","车牌号码","车牌颜色","行业类型","车辆归属","车辆接入方式","总计里程（千米）"};
                String[] dateStrArray = new String[dateList.size()];
                dateList.toArray(dateStrArray);
                String[] dd = new String[arrs.length + dateStrArray.length];
                System.arraycopy(arrs, 0, dd, 0, arrs.length);
                System.arraycopy(dateStrArray, 0, dd, arrs.length, dateStrArray.length);

//                log.info(JSON.toJSONString(resultList)+"   "+JSON.toJSONString(dd));
                //调用Excel导出工具类
    //            ExcelExport.exportList(response,title,resultList,dd);
            //String fileName = ExcelExport.exportExcelList(title,resultList,dd,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
                String fileName = "";
                if(StringUtils.isBlank(exportPath)){
                    //如果没有指定导出路径，则按照默认配置
                    fileName = ExcelExport.exportExcelListForFirstTwoWideColumn(title,resultList,dd,staticFilePath);
                }else{
                    //如果指定了导出路径，则按照给定的配置导出
                    fileName = ExcelExport.exportExcelListWithoutDateForFirstTwoWideColumn(StringUtils.isBlank(deptName)?title:title+"_"+deptName,resultList,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
                }
                return new ObjectRestResponse<>().data(fileName);
            }catch (Exception e){
                log.error("导出车辆日里程统计失败",e);
                return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
            }
    }

    /**
     *查询车辆在线率统计
     * 查询车辆在线率统计，并且显示车辆在某一时间段内每一天的的在线率
     * @param req
     * @return
     */
    @PostMapping("/rateStatistics")
    @ApiOperation(value = "查询车辆在线率统计",notes = "查询车辆在线率统计，并且显示车辆在某一时间段内每一天的的在线率")
    public R<IPage<VehicleOperationResponse>> rateStatistics(@RequestBody OnlineRateRequest req, Query query, BladeUser user) {
        try{
            if(req.getStartTime()==null||req.getStartTime()==0){
				//如果开始时间为空，则设置为7天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 7);
				long secondTimestamp = DateUtil.getDayFirstSecondTimestamp(date.getTime() / 1000);
				req.setStartTime(secondTimestamp);
			}
			if(req.getEndTime()==null||req.getEndTime()==0){
				//如果结束时间为空，则设置为1天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 1);
				long secondTimestamp = DateUtil.getDayLastSecondTimestamp(date.getTime() / 1000);
				req.setEndTime(secondTimestamp);
			}

			//总统计天数
//			int daysCount = DateUtil.getDayCountBetweenSecondTimestamp(req.getStartTime(), req.getEndTime());
			int daysCount = (int) Math.ceil((req.getEndTime() - req.getStartTime()) / (24.0 * 60 * 60));

			//查询车辆总行驶里程
			List<OnlineDaysCountResponse> totalMileageList = null;
			if(req.getVehicleIdList() != null && req.getVehicleIdList().size() > 0){
				//如果指定了车辆，则在此处查询，内部的方法不再查询；否则，后续去查询
				totalMileageList = getTotalMileage(req.getStartTime(), req.getEndTime(),req.getVehicleIdList());
			}

			//1.统计准备：deptList, userId
			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			req.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(req.getVehicleUseType()+"");
			req.setProfessionList(vehicleUseTypes);
			//tenantId
			req.setTenantId(user.getTenantId());
			req.setUserId(user.getUserId());
			req.setStart(query.getCurrent()-1);
			req.setCount(query.getSize());

            //查询缓存表
            Page<Map> page = new Page<>();
            //在执行查询前，首先查询DB缓存表
            if(dbCacheEnable){
                //如果开启了DB缓存，则先查询缓存
                try{
                    Page pageTmp = new Page(query.getCurrent(), query.getSize());
                    VehicleOnlineRateRequest request1 = new VehicleOnlineRateRequest();
                    BeanUtils.copyProperties(req, request1);
                    IPage<Map> pageList = cacheVehicleOnlineRateService.queryVehicleOnlineRateCache(pageTmp, request1);

                    List<String> dateList = getDays(req.getStartTime(), req.getEndTime());

					if(pageList.getTotal()>0){
						pageList.getRecords().forEach(record ->{
							int dayOnline = 0;
							List<String> onlineRateList = new ArrayList<>();
							Double totalOnlineTime = 0D;
							for(int i=0;i<dateList.size();i++){
								String date = dateList.get(i);

								Long dayOnlineTimeValue = null;
								if(record.get(date) instanceof Long){
									dayOnlineTimeValue = (Long) record.get(date);
								}else{
									if(record.get(date) == null){
										dayOnlineTimeValue = 0L;
									}else {
										dayOnlineTimeValue = ((BigDecimal) record.get(date)).longValue();
									}
								}


								if(dayOnlineTimeValue==null||dayOnlineTimeValue.intValue()==0){
									onlineRateList.add("0%");
								}else{
									String onlineRate = new BigDecimal((dayOnlineTimeValue>86400?86400:dayOnlineTimeValue)*10000/86400).divide(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP).toString()+"%";
									onlineRateList.add(onlineRate);
									totalOnlineTime += (dayOnlineTimeValue>86400?86400:dayOnlineTimeValue);
									if(dayOnlineTimeValue>=9){
										dayOnline++;
									}
								}
							}
							record.put("online_num",dayOnline);
							record.put("day_num",onlineRateList);
							record.put("total_online_rate",new BigDecimal(totalOnlineTime*10000/(86400*dateList.size())).divide(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP).toString()+"%");
						});
					}

                    page.setSize(pageList.getSize());
                    page.setCurrent(pageList.getCurrent());
                    page.setRecords(pageList.getRecords());
                    page.setTotal(pageList.getTotal());
                }catch (Exception e){
                    log.error("查询缓存表[cache_vehicle_online_rate]失败，将要实时查询业务逻辑",e);
                    //如果查询缓存失败，则查询实时数据
                    page = getVehicleRateDataInDB(req);
                }
            }else{
                //如果没有开启缓存，则查询实时数据
                page = getVehicleRateDataInDB(req);
            }


			Map<String,String> colorMap = cacheUtil.getVehicleColorValueMap();
			if(req.getVehicleIdList() == null || req.getVehicleIdList().size() < 1){

				//如果没有指定车辆，那么就查询上述结果中的车辆的总里程
				List<Map> list = page.getRecords();
				if(list != null && list.size() > 0){
					List<String> keyList = new ArrayList<>();
					for(Map m : list){
						String licencePlate = m.get("licence_plate")==null?"":m.get("licence_plate").toString();
						String licenceColorCode = m.get("licence_color_code")==null?"":m.get("licence_color_code").toString();
						//String licenceColorCode = colorMap.get(licenceColor);
						String key = licencePlate+"~"+licenceColorCode;
						keyList.add(key);
					}
					//根据车牌号、车牌颜色查询vehicleId
					List<BdmVehicle> vList = vehicleService.findVehicleByLicencePlateAndColor(keyList);
					List<Long> vIdList = new ArrayList<>();
					vList.forEach(item -> {
						vIdList.add((long)item.getId());
					});
					//查询车辆的总里程
					//如果指定了车辆，则在此处查询，内部的方法不再查询；否则，后续去查询
					totalMileageList = getTotalMileage(req.getStartTime(), req.getEndTime(),vIdList);
				}
			}

			Map<String,Double> mileageMap = new HashMap<>();
			if(totalMileageList != null && totalMileageList.size() > 0){

				//转换为map
				totalMileageList.forEach(item -> {
					String key = item.getLicencePlate()+"~"+item.getLicenceColorCode();
					mileageMap.put(key, item.getTotalMileage());
				});
			}


				//添加里程
				List<Map> list = page.getRecords();
				if(list != null && list.size() > 0){
					for(Map m : list){
						String licencePlate = m.get("licence_plate")==null?"":m.get("licence_plate").toString();
						String licenceColorCode = m.get("licence_color_code")==null?"":m.get("licence_color_code").toString();
						//String licenceColorCode = colorMap.get(licenceColor);
						String key = licencePlate+"~"+licenceColorCode;
						double mileage = mileageMap.get(key)==null?0D:mileageMap.get(key);
						m.put("total_mileage", mileage);
						//添加离线天数
						m.put("offline_num", daysCount - (m.get("online_num")==null?0:Integer.parseInt(m.get("online_num").toString())));
					}
				}
            List<String> dateList = getDays(req.getStartTime(), req.getEndTime());
			if(page != null && page.getRecords() != null && page.getRecords().size() > 0){
				//添加没有包含的日期数值为0
				page.getRecords().forEach(item -> {
					int i = 0;
					List<String> dayNumList = (ArrayList<String>) item.get("day_num");
					for (String date : dateList) {
						if (item.get(date) == null) {
							item.put(date, 0);
						} else {
							item.put(date, dayNumList.get(i));
						}

						++i;
					}
				});
			}
            DayPage dayPage = new DayPage();
            BeanUtils.copyProperties(page, dayPage);
            dayPage.setDataList(dateList);
            return R.data(dayPage);
        }catch (Exception e){
            log.error("统计车辆在线率失败",e);
            return R.fail("查询失败");
        }
    }

	private List<OnlineDaysCountResponse> getTotalMileage(long startTime, long endTime, List<Long> vehicleIds) throws Exception{
		//1.判断是否跨月
		List<String> monthList = DateUtil.getMonthList(startTime, endTime);

		//2.对每个月份进行查询
		List<VehicleAndMileage> list = new ArrayList<>();
		//判断是否跨月
		if(monthList.size() < 2){
			//如果没有跨月，则只查询当月
			String startDateStr = DateUtil.getDateString(startTime);
			String endDateStr = DateUtil.getDateString(endTime);
			String month = startDateStr.substring(0,7);
			List<String> dateList = DateUtil.getDateList(startDateStr, endDateStr);
			List<String> dateListTmp = new ArrayList<>();
			for(String date : dateList){
				dateListTmp.add(date.replace("-",""));
			}
			List<VehicleAndMileage> tmpList = vehicleService.findTotalMileage(dateListTmp, vehicleIds, month.replace("-",""));
			list.addAll(tmpList);
		}else{
			for(int i = 0 ; i < monthList.size(); i++){
				String sqlTmp = "";
				if(i == 0){
					//如果是第一个月，则为startDate 到月末
					String startDateStr = DateUtil.getDateString(startTime);
					String endDateStr = DateUtil.getDateString(DateUtil.getMonthLastSecondTimestamp(startTime));
					String month = startDateStr.substring(0,7);
					List<String> dateList = DateUtil.getDateList(startDateStr, endDateStr);
					dateList.stream().map(item -> item.replace("-","")).collect(Collectors.toList());
					List<VehicleAndMileage> tmpList = vehicleService.findTotalMileage(dateList, vehicleIds, month.replace("-",""));
					list.addAll(tmpList);
				}else if(i == monthList.size()-1){
					//如果是最后一个月，则为月初到endDate
					String startDateStr = DateUtil.getDateString(DateUtil.getMonthFirstSecondTimestamp(endTime));
					String endDateStr = DateUtil.getDateString(endTime);
					String month = startDateStr.substring(0,7);
					List<String> dateList = DateUtil.getDateList(startDateStr, endDateStr);
					dateList.stream().map(item -> item.replace("-","")).collect(Collectors.toList());
					List<VehicleAndMileage> tmpList = vehicleService.findTotalMileage(dateList, vehicleIds, month.replace("-",""));
					list.addAll(tmpList);
				}else{
					//中间月份，从月初到月末
					String month = monthList.get(i);
					String startDateStr = month +"-01";
					String endDateStr = DateUtil.getMonthLastDateStr(month);
					List<String> dateList = DateUtil.getDateList(startDateStr, endDateStr);
					dateList.stream().map(item -> item.replace("-","")).collect(Collectors.toList());
					List<VehicleAndMileage> tmpList = vehicleService.findTotalMileage(dateList, vehicleIds, month.replace("-",""));
					list.addAll(tmpList);
				}
			}
		}

		//计算车辆的总里程
		Map<String,List<VehicleAndMileage>> map = new HashMap<>();
		list.forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			if(map.get(key) == null){
				List<VehicleAndMileage> listT = new ArrayList<>();
				listT.add(item);
				map.put(key, listT);
			}else{
				map.get(key).add(item);
			}
		});

		List<OnlineDaysCountResponse> orList = new ArrayList<>();

		map.forEach((k,v)->{
			String licencePlate = k.split("~")[0];
			String licenceColor = k.split("~")[1];
			double mileage = 0;
			for(VehicleAndMileage vm : v){
				mileage += vm.getMileage();
			}
			OnlineDaysCountResponse or = new OnlineDaysCountResponse();
			or.setLicencePlate(licencePlate);
			or.setLicenceColorCode(licenceColor);
			or.setTotalMileage(MathUtil.roundDouble(mileage/1000, 2));
			orList.add(or);
		});

		return orList;
	}

    private  Page<Map>  getVehicleRateDataInDB(OnlineRateRequest req) throws Exception{
        Page<Map> page = new Page<>();
        List<String> dateList = getDays(req.getStartTime(), req.getEndTime());
        req.setDateList(dateList);
        req.setStartTime(dateFormat.parse(dateList.get(0)).getTime()/1000);
        req.setEndTime(dateFormat.parse(dateList.get(dateList.size()-1)).getTime()/1000);
        page = statisticsService.rateStatistics(req);

        long end1 = System.currentTimeMillis();
        long start2 = System.currentTimeMillis();
        page.getRecords().forEach(record ->{
            int dayOnline = 0;
            List<String> onlineRateList = new ArrayList<>();
            Double totalOnlineTime = 0D;
            for(int i=0;i<dateList.size();i++){
                String date = dateList.get(i);
                BigDecimal dayOnlineTime = (BigDecimal) record.get(date);
                if(dayOnlineTime==null||dayOnlineTime.intValue()==0){
                    onlineRateList.add("0%");
                }else{
                    String onlineRate = new BigDecimal((dayOnlineTime.longValue()>86400?86400:dayOnlineTime.longValue())*10000/86400).divide(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP).toString()+"%";
                    onlineRateList.add(onlineRate);
                    totalOnlineTime += (dayOnlineTime.doubleValue()>86400?86400:dayOnlineTime.doubleValue());
                    if(dayOnlineTime.longValue()>=9){
                        dayOnline++;
                    }
                }
            }
            record.put("online_num",dayOnline);
            record.put("day_num",onlineRateList);
            record.put("total_online_rate",new BigDecimal(totalOnlineTime*10000/(86400*dateList.size())).divide(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP).toString()+"%");
        });
        return page;
    }

    /**
     *导出车辆在线率统计
     * @return
     */
    @PostMapping("/rateStatistics/export")
    @ApiOperation(value = "导出车辆在线率统计",notes = "导出车辆在线率统计")
    public R<String> exportRateStatistics(@RequestBody OnlineRateRequest request, Query query, BladeUser user, String exportPath, String deptName) throws Exception {
        try{
			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<VehicleOperationResponse>> pageRes = rateStatistics(request, query, user);
			List<Map> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){

				list = JSON.parseArray(JSON.toJSONString(pageRes.getData().getRecords()), Map.class);
			}

			List<ExcelHead> headList = new ArrayList<>();
			headList.add(new ExcelHead("dept_name","企业名称"));
			headList.add(new ExcelHead("licence_plate","车牌号"));
			headList.add(new ExcelHead("licence_color","车牌颜色"));
			/*headList.add(new ExcelHead("vehicle_use_type","行业类型"));*/
			headList.add(new ExcelHead("vehicle_owner","车辆归属"));
			headList.add(new ExcelHead("vehicle_model","行业类型"));
			headList.add(new ExcelHead("access_mode","接入方式"));
			headList.add(new ExcelHead("online_num","在线天数"));
			headList.add(new ExcelHead("total_online_rate","平均在线率"));

			List<String> dateList = getDays(request.getStartTime(), request.getEndTime());
			dateList.forEach(date -> {
				headList.add(new ExcelHead(date,date));
			});


			//补充中文描述
			//从字典中获取数据
			/*if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				list.forEach(item -> {
					item.put(vehicleUseTypeMap.get(item.get()));
					item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));

				});
			}*/






			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "车辆在线率统计报表";
			String fileName = "";

			/*List<Map<String,Object>> mapList = new ArrayList<>();
			list.forEach(item -> {
				try {
					Map<String,Object> map = BeanUtil.beanToMap(item);
					mapList.add(map);
				} catch (Exception e) {
					throw new RuntimeException(e);
				}
			});*/

			fileName = EasyExcelUtils.exportDynamicColumn(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName ,headList, list , withDate);
			return R.data(fileName);
        }catch (Exception e){
            log.error("导出车辆在线率统计失败",e);
            return R.fail("导出报错");
        }
    }


    /**
     *离线车辆统计
     * 查车组在时间范围内没有上过线的车，并且输出他的最近的定位时间
     * @param query 分页参数
     * @param req
     * @return
     */
    @PostMapping("/vehicleOffline")
    @ApiOperation(value = "查询车辆离线",notes = "查车组在时间范围内没有上过线的车，并且输出他的最近的定位时间")
    public R<IPage<VehicleOfflineResponse>> vehicleOffline(@RequestBody VehicleOfflineRequest req, Query query, BladeUser user) {
        try{

			//开始时间和结束时间不能为空
			if(req.getStartTime() == null){
				return R.fail("开始时间不能为空");
			}
			if(req.getEndTime() == null){
				return R.fail("结束时间不能为空");
			}

			//1.统计准备：deptList, userId
			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			req.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(req.getVehicleUseType()+"");
			req.setProfessionList(vehicleUseTypes);

			req.setTenantId(user.getTenantId());
			req.setUserId(user.getUserId());
			req.setStart((query.getCurrent()-1)*query.getSize());
			req.setCount(query.getSize());


            Page<VehicleOfflineResponse> page = statisticsService.vehicleOffline(req);
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DATE,-1);
            String yesterday = new SimpleDateFormat( "yyyy-MM-dd").format(cal.getTime());
            List<VehicleOfflineResponse> list = page.getRecords();
            List<Integer> indList = new ArrayList<>();
            List<Point> pointList = new ArrayList<>();

			//补充字段
            for(int i=0;i<list.size();i++){
				//补充定位数据
                String locAddr = list.get(i).getLocAddr();
                if(list.get(i).getOffLineTime().after(new SimpleDateFormat( "yyyy-MM-dd").parse(yesterday))&&locAddr!=null&&!"".equals(locAddr)){
                    String[] pointStr = locAddr.split(",");
                    Double longitude = Double.valueOf(pointStr[0]);
                    Double latitude = Double.valueOf(pointStr[1]);
                    if(longitude!=null&&latitude!=null&&longitude!=0&&latitude!=0){
                        indList.add(i);
                        pointList.add(new Point(longitude, latitude));
                    }else {
                        list.get(i).setLocAddr("");
                    }
                }
            }
            List<String> addressList = mapDistanceUtil.getAddress(pointList);
            for(int i=0;i<indList.size();i++){
                list.get(indList.get(i)).setLocAddr(addressList.get(i));
            }
            page.setRecords(list);
			//补充字典数据
			Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
			Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
			Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
			page.getRecords().forEach(item -> {
				item.setVehicleModel(vehicleUseTypeMap.get(item.getVehicleModel()));
				item.setLicenceColor(licenceColorMap.get(item.getLicenceColor()));
				item.setAccessMode(accessModeMap.get(item.getAccessMode()));
			});
            return R.data(page);
        }catch (Exception e){
            e.printStackTrace();
            return R.fail("查询失败");
        }
    }

    /**
     *离线车辆统计 导出
     * @return
     */
    @PostMapping("/vehicleOffline/export")
    @ApiOperation(value = "导出车辆离线报表",notes = "导出车辆离线报表")
    public R<String> exportVehicleOffline(@RequestBody VehicleOfflineRequest req, Query query, BladeUser user, String exportPath, String deptName) throws Exception {
        try{

			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

            //1.执行查询
			//设置最大分页数据
			query.setSize(Integer.MAX_VALUE);
			R<IPage<VehicleOfflineResponse>> pageRes = vehicleOffline(req, query, user);
			List<VehicleOfflineResponse> list = null;
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}
            String title = "离线车辆统计报表";
            String fileName = "";

            //2.执行导出
            boolean withDate = false;
            if(!StringUtils.isBlank(exportPath)){
                withDate = true;
            }
            fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , VehicleOfflineResponse.class,withDate);
            return R.data(fileName);
        }catch (Exception e){
            log.error("导出车辆离线报表",e);
            return R.fail("导出失败");
        }
    }

    /**
     *车辆夜间违规行车报表
     * @param request
     * @param req
     * @return
     */
    @PostMapping("/statisticsNightDriving")
    @ApiOperation(value = "车辆夜间违规行车报表",notes = "车辆夜间违规行车报表")
    public ObjectRestResponse<Page<NightDrivingResponse>> statisticsNightDriving(HttpServletRequest request, @RequestBody NightDrivingRequest req) {
        try{
            req.setDeptList(this.getDeptListFinal(request, req.getDeptId()));
            req.setProfessionList(this.getProfessionList(req.getVehicleUseType()));
            Page<NightDrivingResponse> page = new Page<>(req.getCurrent(),req.getSize());
            long count = statisticsMapper.getNightDrivingCount(req);
            page.setTotal(count);
            List<NightDrivingResponse> records = statisticsMapper.getNightDriving(req.getCurrent(),req.getSize(),req);
            page.setRecords(records);
            return new ObjectRestResponse<>().data(page);
        }catch (Exception e){
            log.error("查询车辆夜间违规行车失败",e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

    /**
     *导出车辆夜间违规行车报表
     * @param request
     * @return
     */
    @GetMapping("/exportNightDriving")
    @ApiOperation(value = "导出车辆夜间违规行车报表",notes = "导出车辆夜间违规行车报表")
    public ObjectRestResponse<String> exportNightDriving(HttpServletRequest request, @RequestParam(value = "dept_id",required = false) String deptId,@RequestParam(value = "start_time",required = false) Long startTime,@RequestParam(value = "end_time",required = false) Long endTime,@RequestParam(value = "licence_plate",required = false) String licencePlate,@RequestParam(value = "licence_color",required = false) String licenceColor,@RequestParam(value = "vehicle_owner",required = false) Long vehicleOwnerId, @RequestParam(value = "vehicle_use_type",required = false) Long vehicleUseType,@RequestParam(value = "access_mode",required = false) Integer accessMode, @RequestParam(required = false) String exportPath,@RequestParam(required = false) String deptName) throws IOException {
        try{
            NightDrivingRequest req = new NightDrivingRequest();
            req.setEndTime(endTime);
            req.setDeptId(deptId);
            req.setStartTime(startTime);
            req.setLicencePlate(licencePlate);
            req.setLicenceColor(licenceColor);
            req.setCount(1000000);
            req.setDeptId(deptId);
            req.setVehicleOwnerId(vehicleOwnerId);
            req.setVehicleUseType(vehicleUseType);
            req.setAccessMode(accessMode);
            req.setDeptList(this.getDeptListFinal(request, deptId));
            req.setProfessionList(this.getProfessionList(vehicleUseType));
            List<NightDrivingResponse> list = statisticsMapper.getNightDriving(1,req.getSize(),req);
            list.stream().forEach(record ->{
                if(record.getDurationTime()!=null){
                    Long duringTime = Long.valueOf(record.getDurationTime());
                    record.setDurationTime(timeToString(duringTime));
                }
            });
            String title = "车辆夜间违规行车报表";
            //String[] arrs = {"企业名称","车队名称","车牌号码","车牌颜色","行业类型","车辆归属","车辆接入方式","夜间违规行车开始时间","夜间违规行车结束时间","持续时间","位置"};
            //调用Excel导出工具类
            //String fileName = ExcelExport.exportExcelFile(title,list,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            String fileName = "";

            /*if(StringUtils.isBlank(exportPath)){
                //如果没有指定导出路径，则按照默认配置
                fileName = ExcelExport.exportExcelFile(title,list,arrs,staticFilePath);
            }else{
                //如果指定了导出路径，则按照给定的配置导出
                fileName = ExcelExport.exportExcelFileWithoutDate(StringUtils.isBlank(deptName)?title:title+"_"+deptName,list,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            }*/

            //改用easyExcel导出
            boolean withDate = false;
            if(!StringUtils.isBlank(exportPath)){
                withDate = true;
            }
            fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , NightDrivingResponse.class,withDate);


            return new ObjectRestResponse<>().data(fileName);
        }catch (Exception e){
            log.error("导出车辆夜间违规行车报表失败", e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

    /**
     *车辆上下线查询
     * 车辆上下线查询,需要把在某一时间段内每一辆车的上下线记录查找出来
     * @param request
     * @return
     */
    @PostMapping("/vehicleOnlineOrOffline")
    @ApiOperation(value = "车辆上下线查询",notes = "车辆上下线查询,需要把在某一时间段内每一辆车的上下线记录查找出来")
    public R<IPage<VehicleOnlineOrOfflineResponse>> vehicleOnlineOrOffline(@RequestBody CommonBaseRequest request, Query query, BladeUser user) {
        try{

			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			request.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
			if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
				vehicleUseTypes.add(request.getVehicleUseType());
			}
			request.setProfessionList(vehicleUseTypes);
			request.setUserId(user.getUserId());

            //在执行查询前，首先查询DB缓存表
            if(dbCacheEnable){
                try{
                    IPage<VehicleOnlineOrOfflineResponse> pageList = cacheVehicleOnlineOrOfflineService.queryVehicleOnlineOrOfflineCache(request, query);
                    //添加查询时间
                    for(VehicleOnlineOrOfflineResponse res : pageList.getRecords()){
                        res.setCheckTime(new Date());
                    }
                    return R.data(pageList);
                }catch (Exception e){
                    log.error("查询缓存表[cache_vehicle_online_offline]失败，将要实时查询业务逻辑",e);
                }
            }

			//组装查询条件
			CommonBaseCrossMonthDurationRequest cmdr = new CommonBaseCrossMonthDurationRequest();
			BeanUtils.copyProperties(request, cmdr);
			List<DateDurationAndMonth> dmList = new ArrayList<>();
			cmdr.setDmList(dmList);
			List<DateListAndMonth> dateList = DateUtil.getDateListAndMonth(request.getStartTime(), request.getEndTime());
			for(DateListAndMonth dm : dateList){
				DateDurationAndMonth ddm = new DateDurationAndMonth();
				ddm.setMonth(dm.getMonth().replace("-",""));
				ddm.setDurationStartTime(DateUtil.getDateByDateString(dm.getDateList().get(0)+" 00:00:00"));
				ddm.setDurationEndTime(DateUtil.getDateByDateString(dm.getDateList().get(dm.getDateList().size()-1)+" 23:59:59"));
				cmdr.getDmList().add(ddm);
			}

			CountDownLatch countDownLatch = new CountDownLatch(1);

			//查询部门信息、上级平台信息，用于后续手动增加字段
			Map<String,BladeDept> deptMap = new HashMap<>();
			Map<String,BamThirdPartyPlatform> platMap = new HashMap<>();
			threadPool.submit(() -> {
				long start1 = System.currentTimeMillis();
				try{
					List<BladeDept> depts = null;
					List<BamThirdPartyPlatform> platList = null;
					platList = thirdPartyPlatformService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled, 0));
					depts = bladeDeptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
					platList.forEach(item -> {
						platMap.put(item.getId()+"", item);
					});
					depts.forEach(item -> {
						deptMap.put(item.getId()+"", item);
					});
				}catch (Exception e){
					log.error("异步处理失败",e);
				}finally {
					countDownLatch.countDown();
				}
				long end1 = System.currentTimeMillis();
				log.info("-=-=-=time1 is "+ (end1 - start1));
			});

            IPage<VehicleOnlineOrOfflineResponse> page = new Page<>(query.getCurrent(),query.getSize());
            page = statisticsService.findVehicleOnlineOrOffline(cmdr, page);
			countDownLatch.await();

			Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
			Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
			Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);

            page.getRecords().forEach(record ->{
                if(StringUtils.isEmpty(record.getDriver())){
                    record.setDriver("未知");
                }

				//车牌颜色
				Map<String, String> colorValueMap = cacheUtil.getVehicleColorValueMap();
				String vehicleColor = colorValueMap.get(record.getLicenceColor());

				//更新字典信息
				record.setVehicleUseType(vehicleUseTypeMap.get(record.getVehicleUseType()));
				record.setLicenceColor(licenceColorMap.get(record.getLicenceColor()));
				record.setAccessMode(accessModeMap.get(record.getAccessMode()));
				if(deptMap != null && deptMap.get(record.getDeptId()+"") != null){
					record.setDeptName(deptMap.get(record.getDeptId()+"").getDeptName());
				}
				String vehicleOwnerName = record.getVehicleOwnerId()==0?"非营运车辆":platMap.get(record.getVehicleOwnerId()+"").getName();
				record.setVehicleOwner(vehicleOwnerName==null?"非营运车辆":vehicleOwnerName);

				//设置查询时间
				record.setCheckTime(new Date());
                if(record.getOffLineTime()==null){
                    try{

                        String licencePlate = record.getLicencePlate();

                        Long startTime = record.getOnLineTime().getTime()/1000;
                        Long endTime = null;
                        if(record.getOffLineTime()==null){
                            endTime = System.currentTimeMillis()/1000;
                        }else{
                            endTime = record.getOffLineTime().getTime()/1000;
                        }

						//补充定位条数
						//long pointCount = locationService.findLocationCountByCondition(licencePlate, Integer.parseInt(vehicleColor), startTime, endTime );
						//record.setPositionCount(pointCount);
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            });
            return R.data(page);
        }catch (Exception e){
            log.error("车辆上下线查询出错",e);
            return R.fail("查询出错");
        }
    }


	/**
	 *车辆上下线查询 导出
	 * 车辆上下线查询,需要把在某一时间段内每一辆车的上下线记录查找出来
	 * @param request
	 * @return
	 */
	@PostMapping("/vehicleOnlineOrOffline/export")
	@ApiOperation(value = "车辆上下线查询",notes = "车辆上下线查询,需要把在某一时间段内每一辆车的上下线记录查找出来")
	public R<String> vehicleOnlineOrOfflineExport(@RequestBody CommonBaseRequest request, Query query, BladeUser user, String exportPath, String deptName) {
		try{

			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<VehicleOnlineOrOfflineResponse>> pageRes = vehicleOnlineOrOffline(request, query,user);
			List<VehicleOnlineOrOfflineResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "车辆上下线查询统计报表";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , VehicleOnlineOrOfflineResponse.class,withDate);
			return R.data(fileName);
		}catch (Exception e){
			log.error("车辆上下线查询出错",e);
			return R.fail("查询出错");
		}
	}

    /**
     *导出车辆上下线查询
     * @param request
     * @return
     */
    @GetMapping("/exportVehicleOnlineOrOffline")
    @ApiOperation(value = "导出车辆上下线查询",notes = "导出车辆上下线查询")
    public ObjectRestResponse<String> exportVehicleOnlineOrOffline(HttpServletRequest request, @RequestParam(value = "dept_id",required = false) String deptId,@RequestParam(value = "start_time",required = false) Long startTime,@RequestParam(value = "end_time",required = false) Long endTime,@RequestParam(value = "licence_plate",required = false) String licencePlate,@RequestParam(value = "licence_color",required = false) String licenceColor,@RequestParam(value = "vehicle_owner",required = false) Long vehicleOwnerId, @RequestParam(value = "vehicle_use_type",required = false) Long vehicleUseType,@RequestParam(value = "access_mode",required = false) Integer accessMode, @RequestParam(required = false) String exportPath,@RequestParam(required = false) String deptName) throws IOException {
        try{
            VehicleOnlineOrOfflineRequest req = new VehicleOnlineOrOfflineRequest();
            req.setEndTime(endTime);
            req.setDeptId(deptId);
            req.setVehicleOwnerId(vehicleOwnerId);
            req.setVehicleUseType(vehicleUseType);
            req.setAccessMode(accessMode);
            req.setStartTime(startTime);
            req.setLicencePlate(licencePlate);
            req.setLicenceColor(licenceColor);
            req.setCount(1000000);
            req.setDeptList(this.getDeptListFinal(request, deptId));
            req.setProfessionList(this.getProfessionList(vehicleUseType));

            List<VehicleOnlineOrOfflineResponse> list = new ArrayList<>();
            //在执行查询前，首先查询DB缓存表
            /*if(dbCacheEnable){
                try{
                    req.setCount(Integer.MAX_VALUE);
                    req.setStart(0);
                    IPage<VehicleOnlineOrOfflineResponse> pageList = cacheVehicleOnlineOrOfflineService.queryVehicleOnlineOrOfflineCache(req);
                    //添加查询时间
                    for(VehicleOnlineOrOfflineResponse res : pageList.getRecords()){
                        res.setCheckTime(new Date());
                    }
                    list = pageList.getRecords();
                }catch (Exception e){
                    log.error("查询缓存表[cache_vehicle_online_offline]失败，将要实时查询业务逻辑",e);
                    //如果查询缓存表失败，则查询实时数据
                    list = statisticsMapper.vehicleOnlineOrOffline(1,req.getSize(),req);
                }
            }else{
                list = statisticsMapper.vehicleOnlineOrOffline(1,req.getSize(),req);
            }*/


            list.forEach(record ->{
                if(record.getDriver()==null){
                    record.setDriver("未知");
                }
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date());
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                Date zero = calendar.getTime();
                if(record.getOffLineTime()==null && (record.getOnLineTime().getTime()>zero.getTime())){
                    //补充定位条数
                    try{
                        String licencePlateRecord = record.getLicencePlate();
                        Integer plateColor = null;
                        //获取车牌颜色
                        try {
							String plateColorStr = cacheUtil.getVehicleColorValueMap().get(record.getLicenceColor());
                            plateColor = Integer.parseInt(plateColorStr);
                        }catch (Exception e){
                            log.error("查询车盘颜色失败",e);
                        }

                        Long onlineTime = record.getOnLineTime().getTime()/1000;
//                    Long offlineTime = record.getOffLineTime().getTime()/1000;
                        Long offlineTime = null;
                        if(record.getOffLineTime()==null){
                            offlineTime = System.currentTimeMillis()/1000;
                        }else{
                            offlineTime = record.getOffLineTime().getTime()/1000;
                        }
						//long pointCount = locationService.findLocationCountByCondition(licencePlateRecord, plateColor, onlineTime, offlineTime );
						//record.setPositionCount(pointCount);
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            });
            String title = "车辆上下线查询";
            String fileName = "";

            //改用easyExcel导出
            boolean withDate = false;
            if(!StringUtils.isBlank(exportPath)){
                withDate = true;
            }
            fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , VehicleOnlineOrOfflineResponse.class,withDate);


            return new ObjectRestResponse<>().data(fileName);
        }catch (Exception e){
            log.error("导出车辆上下线查询",e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

    /**
     *车辆安全信息发送记录表
     * @param request
     * @param req
     * @return
     */
    @PostMapping("/securityInfo")
    @ApiOperation(value = "车辆安全信息发送记录表",notes = "车辆安全信息发送记录表")
    public ObjectRestResponse<Page<SecurityInfoResponse>> securityInfo(HttpServletRequest request, @RequestBody SecurityInfoRequest req) {
        try{
            JSONObject user = userInfoUtil.getUser(request);
            Long userId = user.getLong("id");
            if(userId==null){
                throw new Exception("用户编号为空");
            }

            req.setDeptList(this.getDeptListFinal(request, req.getDeptId()));
            req.setProfessionList(this.getProfessionList(req.getVehicleUseType()));

            //在执行查询前，首先查询DB缓存表
            if(dbCacheEnable){
                try{
                    IPage<SecurityInfoResponse> page = cacheSecurityInfoService.querySecurityInfoCache(req);
                    return new ObjectRestResponse<>().data(page);
                }catch (Exception e){
                    log.error("查询缓存表[cache_security_info]失败，将要实时查询业务逻辑",e);
                }
            }


            Page<SecurityInfoResponse> page = statisticsService.securityInfo(req);
            String userName = userInfoUtil.getUserName(request);
            page.getRecords().forEach(record ->{
                record.setMonitorPerson(userName);
            });
            return new ObjectRestResponse<>().data(page);
        }catch (Exception e){
            log.error("车辆安全信息记录查询失败",e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, "车辆安全信息记录查询失败");
        }
    }

    /**
     *导出车辆安全信息发送记录表
     * @param request
     * @return
     */
    @GetMapping("/exportSecurityInfo")
    @ApiOperation(value = "导出车辆安全信息发送记录表",notes = "导出车辆安全信息发送记录表")
    public ObjectRestResponse<String> exportSecurityInfo(HttpServletRequest request, @RequestParam(value = "dept_id",required = false) String deptId,@RequestParam(value = "start_time",required = false) Long startTime,@RequestParam(value = "end_time",required = false) Long endTime,@RequestParam(value = "licence_plate",required = false) String licencePlate,@RequestParam(value = "licence_color",required = false) Integer licenceColor,@RequestParam(value = "vehicle_owner",required = false) Long vehicleOwnerId, @RequestParam(value = "vehicle_use_type",required = false) Long vehicleUseType,@RequestParam(value = "access_mode",required = false) Integer accessMode, @RequestParam(required = false) String exportPath,@RequestParam(required = false) String deptName) throws Exception {
        SecurityInfoRequest req = new SecurityInfoRequest();
        req.setEndTime(endTime);
        req.setDeptId(deptId);
        req.setVehicleOwnerId(vehicleOwnerId);
        req.setVehicleUseType(vehicleUseType);
        req.setAccessMode(accessMode);
        req.setStartTime(startTime);
        req.setLicencePlate(licencePlate);
        req.setLicenceColor(licenceColor);
        req.setDeptList(this.getDeptListFinal(request, deptId));
        req.setProfessionList(this.getProfessionList(vehicleUseType));

        try{
            req.setCount(1000000);
            String userName;
            if(request != null){
                userName = userInfoUtil.getUserName(request);
            } else {
                userName = defaultAdminName;
            }

            IPage<SecurityInfoResponse> page = null;

            //在执行查询前，首先查询DB缓存表
            if(dbCacheEnable){
                //如果开启DB缓存，则查询缓存
                try{
                    page = cacheSecurityInfoService.querySecurityInfoCache(req);
                }catch (Exception e){
                    log.error("查询缓存表[cache_security_info]失败，将要实时查询业务逻辑",e);
                    //如果查询缓存失败，则查询实时数据
                    page = statisticsService.securityInfo(req);
                }
            }else{
                //如果没有开启DB缓存，则查询实时数据
                page = statisticsService.securityInfo(req);
            }



            page.getRecords().forEach(record ->{
                record.setMonitorPerson(userName);
            });
            List list = page.getRecords();
            String title = "车辆安全信息发送记录表";
            //String[] arrs = {"企业名称","车队名称","车牌号码","车牌颜色","行业类型","车辆归属","车辆接入方式","发送时间","信息内容","发送状态","监控人员"};
            //调用Excel导出工具类
            //String fileName = ExcelExport.exportExcelFile(title,list,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            String fileName = "";
            /*if(StringUtils.isBlank(exportPath)){
                //如果没有指定导出路径，则按照默认配置
                fileName = ExcelExport.exportExcelFile(title,list,arrs,staticFilePath);
            }else{
                //如果指定了导出路径，则按照给定的配置导出
                fileName = ExcelExport.exportExcelFileWithoutDate(StringUtils.isBlank(deptName)?title:title+"_"+deptName,list,arrs,StringUtils.isBlank(exportPath)?staticFilePath:exportPath);
            }*/

            //改用easyExcel导出
            boolean withDate = false;
            if(!StringUtils.isBlank(exportPath)){
                withDate = true;
            }
            fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , SecurityInfoResponse.class,withDate);




            return new ObjectRestResponse<>().data(fileName);
        }catch (Exception e){
            log.error("导出车辆安全信息发送记录表失败",e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

	/**
	 * 报停异动统计
	 * 报警信息查询 impala
	 * @param request
	 * @return
	 */
    @PostMapping("/unexpectedReport")
    @ApiOperation(value = "分页获取报停异动", httpMethod = "POST")
    public R<IPage<UnexpectedReportResponse>> unexpectedReport (@RequestBody CommonBaseRequest request, Query query, BladeUser user) {
        try {

			//1.组织查询数据
			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			request.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
			if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
				vehicleUseTypes.add(request.getVehicleUseType());
			}
			request.setProfessionList(vehicleUseTypes);
			request.setUserId(user.getUserId());

			if(request.getStartTime() == null){
				//如果没有指定开始时间，则开始时间定到7天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 7);
				request.setStartTime(date.getTime()/1000);
			}
			if(request.getEndTime() == null){
				//如果没有指定结束时间，则结束时间定到前一天
				Date date = DateUtil.getDateBeforeDay(new Date(), 1);
				request.setEndTime(date.getTime() / 1000);
			}

			//2.查询参与统计的车辆
			CommonBaseCrossMonthRequest cbcRequest = new CommonBaseCrossMonthRequest();
			BeanUtils.copyProperties(request, cbcRequest);
			Query vQuery = new Query();
			vQuery.setSize(Integer.MAX_VALUE);
			vQuery.setCurrent(1);
			IPage<BdmVehicle> bvPage = vehicleService.findVehiclesByCondition(cbcRequest, vQuery);
			List<Long> vehicleIdList = new ArrayList<>();
			Map<String,BdmVehicle> vehicleMap = new HashMap<>();
			List<Integer> ownerIds = new ArrayList<>();
			List<Long> deptIds = new ArrayList<>();

			if(bvPage != null && bvPage.getRecords().size() > 0){
				bvPage.getRecords().forEach(item -> {
					vehicleIdList.add(item.getId().longValue());
					vehicleMap.put(item.getId()+"", item);
					deptIds.add(item.getDeptId());
					if (item.getVehicleOwnerId() != null) {
						ownerIds.add(item.getVehicleOwnerId().intValue());
					}
				});
			}else{
				IPage<UnexpectedReportResponse> pageRes = new Page<>();
				pageRes.setSize(query.getSize());
				pageRes.setCurrent(query.getCurrent());
				pageRes.setTotal(0);
				pageRes.setRecords(null);
				return R.data(pageRes);
			}

			//查询 dept信息
			List<BladeDept> depts = deptService.listByIds(deptIds);
			Map<String,BladeDept> deptMap = new HashMap<>();
			depts.forEach(item -> {
				deptMap.put(item.getId()+"", item);
			});

			//查询 owner信息
			List<BamThirdPartyPlatform> owners = thirdPartyPlatformService.listByIds(ownerIds);
			Map<String, BamThirdPartyPlatform> ownerMap = new HashMap<>();
			owners.forEach(item -> {
				ownerMap.put(item.getId()+"", item);
			});

			//3.统计报警数据
			CommonBaseImpalaAlarmRequest req = new CommonBaseImpalaAlarmRequest();
			List<Long> alarmTypeList = new ArrayList<>();
			alarmTypeList.add(Long.parseLong(CommonConstant.DICT_ALARM_TYPE_STOP_ERR_MOVE));
			req.setAlarmTypeList(alarmTypeList);
			req.setVehicleIdList(vehicleIdList);
			req.setStartTime(request.getStartTime());
			req.setEndTime(request.getEndTime());
			IPage<ImpalaAlarm> page = alarmService.findAlarmByCondition(req, query);
			if(page == null || page.getRecords().size() < 1){
				IPage<UnexpectedReportResponse> pageRes = new Page<>();
				pageRes.setSize(query.getSize());
				pageRes.setCurrent(query.getCurrent());
				pageRes.setTotal(0);
				pageRes.setRecords(null);
				return R.data(pageRes);
			}

			//4.查询报停信息
			List<Integer> ruleIds = new ArrayList<>();
			page.getRecords().forEach(item -> {
				ruleIds.add(item.getRuleId());
			});
			List<BdmCallStopChangeRule> ruleList = callStopChangeRuleService.listByIds(ruleIds);
			Map<String, BdmCallStopChangeRule> ruleMap = new HashMap<>();
			ruleList.forEach(item -> {
				ruleMap.put(item.getId() + "", item);
			});

			//4.补充其他数据字段
			List<UnexpectedReportResponse> resList = new ArrayList<>();
			page.getRecords().forEach(item -> {
				UnexpectedReportResponse response = new UnexpectedReportResponse();
				//报警信息
				response.setAlarmType(item.getAlarmType()+"");
				response.setAlarmTime(DateUtil.getDateBySecondTimestamp(item.getAlarmTime()));
				response.setAlarmAddress(item.getAlarmAddress());
				//报停信息
				BdmCallStopChangeRule rule = ruleMap.get(item.getRuleId()+"");
				if(rule != null){
					try {
						response.setStartTime(DateUtil.getDateByDateString(rule.getStartDate()+" "+rule.getStartTime()));
						response.setEndTime(DateUtil.getDateByDateString(rule.getEndDate() + " " + rule.getEndTime()));
					} catch (Exception e) {
						log.error("日期格式转换失败",e);
					}
				}
				//车辆基本信息
				BdmVehicle bv = vehicleMap.get(item.getVehicleId()+"");
				if(bv != null){
					response.setLicencePlate(bv.getLicencePlate());
					response.setLicenceColor(bv.getLicenceColor());
					response.setAccessMode(bv.getAccessMode());
					response.setVehicleUseType(bv.getVehicleUseType());
					response.setDeptName(deptMap.get(bv.getDeptId()+"")==null?"":deptMap.get(bv.getDeptId()+"").getDeptName());
					response.setVehicleOwner(ownerMap.get(bv.getVehicleOwnerId()+"")==null?"非营运车辆":ownerMap.get(bv.getVehicleOwnerId()+"").getName());
					resList.add(response);
				}
			});

			IPage<UnexpectedReportResponse> resPage = new Page<>();
			resPage.setTotal(page.getTotal());
			resPage.setSize(page.getSize());
			resPage.setCurrent(page.getCurrent());
			resPage.setRecords(resList);

            return R.data(resPage);
        } catch (Exception e) {
            log.error("fail sheet unexpected report", e);
            return R.fail("查询失败");
        }
    }

	/**
	 * 报停异动统计 导出
	 * 报警信息查询 impala
	 * @param request
	 * @return
	 */
	@PostMapping("/unexpectedReport/export")
	@ApiOperation(value = "分页获取报停异动", httpMethod = "POST")
	public R<String> unexpectedReportExport (@RequestBody CommonBaseRequest request, Query query, BladeUser user, String exportPath, String deptName) {
		try {
			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<UnexpectedReportResponse>> pageRes = unexpectedReport(request, query, user);
			List<UnexpectedReportResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//补充中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
				list.forEach(item -> {
					item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
					item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
					item.setAccessModeDesc(accessModeMap.get(item.getAccessMode()));
					item.setAlarmTypeDesc(CALL_STOP_ALARM_DESC);
				});
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "报停异动统计报表";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , UnexpectedReportResponse.class,withDate);
			return R.data(fileName);

		} catch (Exception e) {
			log.error("fail sheet unexpected report", e);
			return R.fail("导出失败");
		}
	}

    @PostMapping("statistics/unexpected-report/export")
    @ApiOperation(value = "导出报停异动", httpMethod = "POST")
    public ObjectRestResponse<String> exportUnexpectedReport (HttpServletRequest hsr, @Validated @RequestBody UnexpectedReportListRequest request) {
        try {
            request.setStart(1);
            request.setCount(100000000);
            request.setDeptList(this.getDeptListFinal(hsr, request.getDeptId()));
            request.setProfessionList(this.getProfessionList(request.getVehicleUseType()));
            List<UnexpectedReportResponse> list = null;
            String title = "报停异动统计表";
            String[] fields = {"企业名称", "车队名称", "车牌号", "车牌颜色", "行业类型",  "车辆归属", "接入方式","报警类型", "报停开始时间", "报停结束时间", "报停告警时间", "报停告警位置"};
            String fileName = ExcelExport.exportExcelFile(title, list, fields, this.staticFilePath);

            //改用easyExcel导出
            fileName = EasyExcelUtils.export(staticFilePath , proxyFilePath , title , list , UnexpectedReportResponse.class,false);


            return new ObjectRestResponse<>().data(fileName);
        } catch (Exception e) {
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

    @ApiOperation(value = "分页获取车辆每日数据大小", httpMethod = "POST")
    @PostMapping("statistics/daily-car-mega-byte")
    public ObjectRestResponse<List<DailyCarMegaByteResponse>> getDailyCarMegaByte (HttpServletRequest hsr, @Validated @RequestBody DailyCarMegaByteListRequest request) {
        if ((request.getLicenceColor() == null) && StringUtils.isBlank(request.getPlateColor())) {
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, "车牌颜色为空。");
        }
        if (StringUtils.isBlank(request.getLicencePlate())) {
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, "车牌号为空。");
        }
        try {
            request.setDeptList(this.getDeptListFinal(hsr, request.getDeptId()));
            request.setProfessionList(this.getProfessionList(request.getVehicleUseType()));
            if (StringUtils.isNotBlank(request.getPlateColor())) {
                request.setLicenceColor(Integer.parseInt(this.cacheUtil.getVehicleColorCodeByName(request.getPlateColor())));
            }
            if (request.getStartTime() != null) {
                request.setStartDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date(request.getStartTime() * 1000)));
            }
            if (request.getEndTime() != null) {
                request.setEndDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date(request.getEndTime() * 1000)));
            }

            return new ObjectRestResponse<>().data(this.statisticsMapper.getDailyCarMegaByte(request));
        } catch (Exception e) {
            log.error("fail get daily car mega byte", e);
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

	/**
	 * 车辆每日流量
	 * @param request
	 * @return
	 */
    @ApiOperation(value = "分页获取车辆数据大小", httpMethod = "POST")
    @PostMapping("/vehicleMegaByte")
    public R<IPage<CarMegaByteResponse>> sheetCarMegaByte (@Validated @RequestBody CommonBaseRequest request, Query query, BladeUser user) {
        try {
			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			request.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
			if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
				vehicleUseTypes.add(request.getVehicleUseType());
			}
			request.setProfessionList(vehicleUseTypes);
			request.setUserId(user.getUserId());


			if(request.getStartTime() == null){
				//如果没有指定开始时间，则开始时间定到7天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 7);
				request.setStartTime(date.getTime()/1000);
			}
			if(request.getEndTime() == null){
				//如果没有指定结束时间，则结束时间定到前一天
				Date date = DateUtil.getDateBeforeDay(new Date(), 1);
				request.setEndTime(date.getTime() / 1000);
			}

			CountDownLatch countDownLatch = new CountDownLatch(1);

			//查询部门信息、上级平台信息，用于后续手动增加字段
			Map<String,BladeDept> deptMap = new HashMap<>();
			Map<String,BamThirdPartyPlatform> platMap = new HashMap<>();
			threadPool.submit(() -> {
				try{
					List<BladeDept> depts = null;
					List<BamThirdPartyPlatform> platList = null;
					platList = thirdPartyPlatformService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled, 0));
					depts = bladeDeptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
					platList.forEach(item -> {
						platMap.put(item.getId()+"", item);
					});
					depts.forEach(item -> {
						deptMap.put(item.getId()+"", item);
					});
				}catch (Exception e){
					log.error("异步处理失败",e);
				}finally {
					countDownLatch.countDown();
				}
			});

            IPage<CarMegaByteResponse> page = vehicleService.findVehicleMegaByteDaily(request, query);

			countDownLatch.await();
			//添加字段信息
			page.getRecords().forEach(item -> {
				Long deptId = item.getDeptId();
				Long vehicleOwnerId = item.getVehicleOwnerId();
				BladeDept dept = deptMap.get(deptId+"");
				BamThirdPartyPlatform plat = platMap.get(vehicleOwnerId+"");
				if(dept != null){
					item.setDeptName(dept.getDeptName());
				}
				if(plat != null){
					item.setVehicleOwner(StringUtils.isEmpty(plat.getName())?"非营运车辆":plat.getName());
				}else{
					item.setVehicleOwner("非营运车辆");
				}
			});

            return R.data(page);
        } catch (Exception e) {
            log.error("fail sheet car mega byte", e);
            return R.fail("查询报错");
        }
    }


	/**
	 * 车辆每日流量 导出
	 * @param request
	 * @return
	 */
	@ApiOperation(value = "分页获取车辆数据大小", httpMethod = "POST")
	@PostMapping("/vehicleMegaByte/export")
	public R<String> vehicleMegaByteExport (@Validated @RequestBody CommonBaseRequest request, Query query, BladeUser user, String exportPath, String deptName) {
		try {
			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<CarMegaByteResponse>> pageRes = sheetCarMegaByte(request, query, user);
			List<CarMegaByteResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//补充中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
				list.forEach(item -> {
					item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
					item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
					item.setAccessModeDesc(accessModeMap.get(item.getAccessMode()));
				});
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "车辆每日流量报表";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , CarMegaByteResponse.class,withDate);
			return R.data(fileName);
		} catch (Exception e) {
			log.error("fail sheet car mega byte", e);
			return R.fail("导出报错");
		}
	}

    @ApiOperation(value = "导出车辆数据大小", httpMethod = "POST")
    @PostMapping("statistics/car-mega-byte/export")
    public ObjectRestResponse<String> exportCarMegaByte (HttpServletRequest hsr, @Validated @RequestBody DailyCarMegaByteListRequest request) {
        try {
            request.setStart(1);
            request.setCount(100000000);
            request.setDeptList(this.getDeptListFinal(hsr, request.getDeptId()));
            request.setProfessionList(this.getProfessionList(request.getVehicleUseType()));
            if (request.getStartTime() != null) {
                request.setStartDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date(request.getStartTime() * 1000)));
            }
            if (request.getEndTime() != null) {
                request.setEndDate(new SimpleDateFormat("yyyy-MM-dd").format(new Date(request.getEndTime() * 1000)));
            }

            List<CarMegaByteResponse> list = this.statisticsMapper.getCarMegaByte(request);
            String title = "车辆每日数据大小";
            String[] fields = {"企业名称", "车队名称", "车牌号", "车牌颜色", "行业类型", "接入方式", "车辆归属", "数据大小（单位：MB）"};
            String fileName = "";
            //String fileName = ExcelExport.exportExcelFile(title, list, fields, this.staticFilePath);

            //改用easyExcel导出
            fileName = EasyExcelUtils.export(staticFilePath , proxyFilePath , title , list , CarMegaByteResponse.class,false);



            return new ObjectRestResponse<>().data(fileName);
        } catch (Exception e) {
            return new ObjectRestResponse<>(StatisticConstants.FAILED_CODE, e.getMessage());
        }
    }

    /**
     *下载文件
     * @param request
     * @return
     */
    @GetMapping("/files/{fileName}")
    @ApiOperation(value = "下载文件",notes = "下载文件")
    public void downloadFile(HttpServletRequest request,HttpServletResponse response, @PathVariable("fileName") String fileName) throws Exception {
        BufferedInputStream inputStream = null;
        OutputStream outputStream = null;
        try{
            File file = new File(staticFilePath+fileName);
            if (!file.exists()){
                throw new Exception("路径有误，文件不存在！");
            }

            /** 将文件名称进行编码 */
            response.setHeader("Content-Disposition", "attachment;fileName="+new String(fileName.getBytes("UTF-8"),"ISO-8859-1"));
            response.setContentType("content-type:octet-stream");
            inputStream = new BufferedInputStream(new FileInputStream(file));
            outputStream = response.getOutputStream();
            byte[] buffer = new byte[1024];
            int len;
            while ((len = inputStream.read(buffer)) != -1){
                outputStream.write(buffer ,0 , len);
            }
        }finally {
            if(inputStream!=null){
                inputStream.close();
            }
            if(outputStream!=null){
                outputStream.close();
            }
        }
    }

    public static List<String> getDays(Long beginDay , Long endDay) throws Exception {
        if(beginDay>endDay){
            throw new Exception("结束时间小于开始时间");
        }
        beginDay = beginDay*1000;
        endDay = endDay*1000;
        List<String> dateList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date(beginDay));
        calendar.set(Calendar.HOUR_OF_DAY,0);
        calendar.set(Calendar.MINUTE,0);
        calendar.set(Calendar.SECOND,0);
        while(calendar.getTimeInMillis()<=endDay){
            dateList.add(dateFormat.format(calendar.getTime()));
            calendar.setTime(new Date(calendar.getTime().getTime()+24*60*60*1000));
        }
        return dateList;
    }

    /*public static void main(String[] args) throws Exception {
        *//*Double d = 250D;

        BigDecimal b = new BigDecimal(d/10);

        d = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();

        System.out.println(d.toString());*//*
        Date start = dateFormat.parse("2023-01-01");
        Date end = dateFormat.parse("2023-01-31");
        int days = DateUtil.getDayCountBetweenSecondTimestamp(start.getTime()/1000, end.getTime()/1000);
        System.out.println("days is " + days);
    }*/

    public List<Long> getDeptList(Long deptId) throws Exception {
        if(deptId==null){
            throw new Exception("企业编号为空");
        }
//        List<HashMap> deptList = statisticsMapper.getDeptList(deptId);
//        List<Long> idList = new ArrayList<>();
//        deptList.forEach(map ->{
//            idList.add((Long)map.get("id"));
//        });
//        idList.add(deptId);
//        return idList;

        return deptUtil.acquireDepts(deptId.toString());
    }

    /**
     * 根据给定的车组查询所有车组，包含子车组
     * @param deptId , 如果是多个车组，中间使用英文逗号","分隔
     * @return
     * @throws Exception
     */
    public List<Long> getDeptListMulti(String deptId) throws Exception{
        String[] deptArray = deptId.split(",");
        List<Long> deptList = new ArrayList<>();
        //如果是多个部门
        for(String dept: deptArray){
            List<Long> list = getDeptList(Long.parseLong(dept));
            if(list != null && list.size() > 0){
                deptList.addAll(list);
            }else{
                deptList.add(Long.parseLong(dept));
            }
        }
        return deptList;
    }

    public static String timeToString(long allSeconds) {
        String DateTimes = null;

        long days = allSeconds / (60 * 60 * 24);
        long hours = (allSeconds % (60 * 60 * 24)) / (60 * 60);
        long minutes = (allSeconds % (60 * 60)) / 60;
        long seconds = allSeconds % 60;

        if (days > 0) {
            DateTimes = days + "天" + hours + "小时" + minutes + "分钟" + seconds + "秒";
        } else if (hours > 0) {
            DateTimes = hours + "小时" + minutes + "分钟" + seconds + "秒";
        } else if (minutes > 0) {
            DateTimes = minutes + "分钟" + seconds + "秒";
        } else {
            DateTimes = seconds + "秒";
        }

        return DateTimes;
    }

    public List<Long> getDeptListFinal (HttpServletRequest hsr, String deptIdListStr) throws Exception {
        if (StringUtils.isNotBlank(deptIdListStr)) {
            return this.getDeptListMulti(deptIdListStr);
        } else {
            JSONObject user = this.userInfoUtil.getUser(hsr);
            if ((user == null) || (user.getLong("id")) == null) {
                throw new Exception("获取用户信息异常。");
            }

            List<Long> deptList = new ArrayList<>();
            Long userId = user.getLong("id");
            List<HashMap> map = this.statisticsMapper.judgeAdminRole(userId);
            if ((map == null) || map.isEmpty()) {
                List<HashMap> deptIds = this.statisticsMapper.queryDepartments(userId);
                if ((deptIds != null) && (!deptIds.isEmpty())) {
                    for (HashMap deptMap : deptIds) {
                        Long deptId = (Long) ((deptMap.get("dept_id") == null) ? 0 : deptMap.get("dept_id"));
                        List<Long> depts = this.getDeptList(deptId);
                        if ((depts != null) && (!depts.isEmpty())) {
                            deptList.addAll(depts);
                        }
                    }
                }
            }

            return deptList;
        }
    }

    public List<Long> getProfessionList (Long professionId) {
        List<Long> professionList = new ArrayList<>();
        if (professionId == null) {
            return professionList;
        }

        professionList.add(professionId);
        List<String> tmpList = dictBizService.findProfessionList(professionId);
        if ((tmpList == null) || (tmpList.isEmpty())) {
            return professionList;
        }
        for (String tmp : tmpList) {
            professionList.add(Long.parseLong(tmp));
        }

        return professionList;
    }
}
