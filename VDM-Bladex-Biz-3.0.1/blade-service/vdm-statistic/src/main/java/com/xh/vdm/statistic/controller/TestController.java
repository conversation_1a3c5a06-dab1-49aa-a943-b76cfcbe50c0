package com.xh.vdm.statistic.controller;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.constant.RedisConstants;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.impala.ImpalaAlarm;
import com.xh.vdm.statistic.mapper.BdmRouteMapper;
import com.xh.vdm.statistic.mapper.impala.ImpalaAlarmMapper;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.service.impl.BdmRouteServiceImpl;
import com.xh.vdm.statistic.task.*;
import com.xh.vdm.statistic.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

import static com.xh.vdm.statistic.validator.MonthLessThisMonthValidator.sdf;

/**
 * <AUTHOR>
 * @date 2021/10/26 11:36
 */
@RestController
@RequestMapping("/test")
@Slf4j
public class TestController {

    @Autowired
    BdmRouteMapper bdmRouteMapper;

    @Autowired
    MapDistanceUtil mapDistanceUtil;

    @Autowired
	BdmRouteServiceImpl bdmRouteService;

	private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

	@Resource
	private IStatDriveStatusService driveStatusService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ILocationService locationService;

	@Resource
	private LocationTask locationTask;

	@Resource
	private StatisticsTask statisticsTask;

    @Autowired
    protected RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private IStatDriftService driftService;

    @Autowired
    private StatisticsTask task;

	@Resource
	private DBCacheTask cacheTask;

	@Resource
	private IndexTask indexTask;

	@Resource
	private FuseTask fuseTask;


	@Resource
	private IBladeUserService userService;


	@Resource
	private IAlarmService alarmService;

	@Resource
	private ImpalaAlarmMapper impalaAlarmMapper;

	@Resource
	private IBdmVehicleService vehicleService;

	@GetMapping("/testForDate")
	public R<String> testForDate(){
		List<Long> deptIds = new ArrayList<>();
		deptIds.add(1L);
		try {
			vehicleService.findOfflineCountByOfflineDayCount(deptIds, 1L, 10);
		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询报错");
		}
		return R.success("查询成功");
	}

	@GetMapping("/statDriveDurationAndMileage")
	public R<String> statDriveDurationAndMileage(String date){

		//统计当天的驾驶员驾驶时长
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		String day = date;
		long start = 0;
		long end = 0;
		try {
			start = System.currentTimeMillis();
			driveStatusService.statDriverStatus(day);
			end = System.currentTimeMillis();
			//如果定时任务执行成功
			log.info("[驾驶员驾驶时长统计-{}]定时任务 执行完成，共耗时{}毫秒" , day , end - start);
			return R.success("执行成功");
		} catch (Exception e) {
			log.error("[驾驶员驾驶时长统计-{}]定时任务 执行失败 ，发生异常" , day , e);
			e.printStackTrace();
			return R.fail("执行失败");
		}
	}

	@GetMapping("/simpleTest")
	public R<String> simpleTest(){
		return R.data("haha123456");
	}


    @GetMapping("/testParam")
    public R testParam(List<Long> list){
        for(int i = 0 ; i < list.size() ; i++){
            log.info(""+list.get(i));
        }
        return R.data(list);
    }

	@GetMapping("/testRedis")
	public R<String> testRedis(String key){
		Object obj = redisTemplate.opsForValue().get(key);
		String json = JSON.toJSONString(obj);
		return R.data(json);
	}

	@GetMapping("/testAlarmTypeInRedis")
	public R<String> testAlarmTypeInRedis(String key){
		Object obj = redisTemplate.opsForHash().get(CommonConstant.DICT_ALARM_TYPE,key);
		String json = JSON.toJSONString(obj);
		return R.data(json);
	}



    /**
     * @description: 测试多数据源
     * @author: zhouxw
     * @date: 2022/8/31 5:08 PM
     * @param: []
     * @return: java.util.List
     **/
    @GetMapping("/testDS")
    @DS("location")
    public List testDynamicDatasource(){
        return jdbcTemplate.queryForList("select * from location_20220830 limit 10");
    }


    /*@GetMapping("/testLocationList")
    public List testLocationList (){
        String day = "20220108";
        String licencePlate = "新CE6D56";
        //return locationService.findUploadLocationPointListByDay(day , licencePlate);
        return locationService.findUploadLicencePlatesByDay(day);
    }*/

    /**
     * @description: 测试轨迹完整率
     * @author: zhouxw
     * @date: 2022/9/23 2:10 PM
     * @param: []
     * @return: java.lang.String
     **/
    /*@GetMapping("/testComplete")
    public Map<String,String> testLocationComplete(String day , String licencePlate, Integer plateColor){
        //String day = "20220301";
        //String licencePlate = "新NT3109";
        day = "20220908";
        String[] licencePlates = new String[]{
            "新E07021"
                    ,"粤A30265"
                    ,"粤A41024"

        };
        Map<String ,String> map = new HashMap<>();

        for(String s : licencePlates){
            //查询轨迹
            List<LocationKudu> list = locationService.findUploadLocationPointListByDay(day , s, plateColor);
            //判断轨迹
            List<LocationKudu> locations = locationCompleteStatByPositionList(list);
            StringBuffer sb = new StringBuffer();
            for(LocationKudu loc : locations){
				//原来记录id，现在记录定位时间 locTime
                sb.append(loc.getLocTime()).append(",");
            }
            map.put(s , sb.toString());
        }

        return map;
    }*/

    /**
     * @description: 测试漂移
     * @author: zhouxw
     * @date: 2022/9/26 6:04 PM
     * @param: [date yyyyMMdd, licencePlate]
     * @return: java.util.Map<java.lang.String,java.lang.String>
     **/
    /*@GetMapping("/checkDrift")
    public List<String> checkDrift(String date , String licencePlate, Integer plateColor){
        List<LocationKudu> locations = locationService.findUploadLocationPointListByDay(date , licencePlate, plateColor);
        List<String> ids = locationDriftStatByPositionList(locations);
        return ids;
    }*/


    private List<String> locationDriftStatByPositionList(List<LocationKudu> list) {
        //1.对轨迹序列按照定位时间进行排序
        Collections.sort(list , (o1 , o2) -> {
            return (int) (o1.getTime() - o2.getTime());
        });

        //当前点与上个点之间的速度
        double speed = 0;
        //上上个点与上个点之间的速度
        double speedOld = 0;
        //当前定位点
        LocationKudu locationNow = null;
        //上个定位点
        LocationKudu locationOld = null;
        //漂移点数量
        int driftCount = 0;
        double distance = 0 ;
        double time = 0;
        List<String> ids = new ArrayList<>();
        //用于验证漂移段
        boolean isDrift = false;
        for(int i = 0 ; i < list.size() ; i++){
            locationNow = list.get(i);
            StringBuffer sb = new StringBuffer();
            if(i > 0){
                //从第二个点开始计算
                //两点间的距离，单位为米
                distance = DistanceUtils.wgs84Distance(locationNow.getLongitude() , locationNow.getLatitude() , locationOld.getLongitude() , locationOld.getLatitude());
                //两点间的时间差，单位为秒
                time = locationNow.getTime() - locationOld.getTime();
                //两点间的速度，单位为 km/h
                if (time == 0) {
                    speed = 0;
                } else{
                    speed = distance / time * 3.6;
                }
                if(i > 1){
                    if(speed > 160 && speedOld > 160){
                        driftCount++;
                        if(!isDrift) {
                            isDrift = true;
							//原来记录id，现在记录定位时间
                            sb.append(locationNow.getTime() + ": " + speed + ", " + speedOld);
                            ids.add(sb.toString());
                        }
                    }else{
                        isDrift = false;
                    }
                }
            }
            locationOld = locationNow;
            speedOld = speed;
        }
        return ids;
    }


    private List<LocationKudu> locationCompleteStatByPositionList(List<LocationKudu> list) {
        List<LocationKudu> unComplete = new ArrayList<>();
        //1.对轨迹序列按照定位时间进行排序
        Collections.sort(list , (o1 , o2) -> {
            return (int) (o1.getTime() - o2.getTime());
        });

        //2.循环轨迹点，计算轨迹完整率
        //当前定位点
        LocationKudu locationNow = null;
        //上一个定位点
        LocationKudu locationOld = null;
        //总点数
        int totalPointCount = 0 ;
        //完整点数
        int completePointCount = 0;
        //大于2公里，小于10公里的路段次数
        int middleDistanceCount = 0;

        //全部里程
        double allMiles = 0;
        //完整里程
        double completeMiles = 0;

        //两个定位点之间的距离
        double perDistance = 0;

        for(int i = 0 ; i < list.size() ; i++){
            locationNow = list.get(i);
            if (i == 0) {
                //如果是第一个定位点
                completePointCount++;
            } else {
                //计算两个定位点之间的距离
                perDistance = DistanceUtils.wgs84Distance(locationNow.getLongitude(),locationNow.getLatitude(),locationOld.getLongitude() , locationOld
                        .getLatitude());
                //轨迹连续计算方法
                if (perDistance <= 2000) {// 轨迹连续
                    completePointCount++;
                    completeMiles = completeMiles + perDistance;
                    allMiles = allMiles + perDistance;
                    middleDistanceCount = 0;
                } else if (perDistance > 2000 && perDistance <= 10000) {
                    middleDistanceCount++;
                    if (middleDistanceCount > 5) {// 轨迹不连续
                        allMiles = allMiles + perDistance;
                        //记录不连续的location
                        unComplete.add(list.get(i));
                    } else {// 轨迹连续
                        completePointCount++;
                        completeMiles = completeMiles + perDistance;
                        allMiles = allMiles + perDistance;
                    }
                } else if (perDistance > 10000) {// 轨迹不连续
                    middleDistanceCount = 0;
                    allMiles = allMiles + perDistance;
                    //记录不连续的location
                    unComplete.add(list.get(i));
                }
            }
            locationOld = locationNow;
            totalPointCount++;
        }

        return unComplete;
    }

    @GetMapping("/testEmail")
    public String testEmail(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String date = "2023-02-01";
        try {
            //按日统计数据并发送邮件测试
            //task.statDayAndSendMailForOneDay(sdf.parse(date));
            task.statDayAndSendMail();

            //按月统计数据并发送邮件测试
            //task.statDayAndSendMailForOneMonth(sdf.parse(date));
            //task.statMonthAndSendMail();

        }catch (Exception e){
            e.printStackTrace();
        }
        return "success";
    }

    /**
     * @description: 获取redis Hash全部内容测试
     * @author: zhouxw
     * @date: 2023-02-46 17:36:08
     * @param: []
     * @return: java.lang.String
     **/
    @GetMapping("/testCacheGetAllValue")
    public String testCacheGetAllValue(){
        //获取整个hash中的全部内容
        Map<Object,Object> map = redisTemplate.opsForHash().entries(RedisConstants.HashKey.VEHICLE_INFO_HASH_KEY);
        for(int i = 0 ; i < 10 ; i++){
            System.out.println(map);
        }
        return "";
    }


	/**
	 * @description: 首页报警信息统计
	 * @author: zhouxw
	 * @date: 2023-07-202 15:17:20
	 * @param: []
	 * @return: com.xh.vdm.statistic.utils.R<java.lang.String>
	 **/
	@GetMapping("/testIndexAlarm")
	public R<String> testIndexAlarm(){
		try {
			indexTask.userDataStat(null);
			return R.success("执行操作成功");
		}catch (Exception e){
			return R.fail("操作失败");
		}
	}


	@GetMapping("/testUpdateById")
	public R<String> testUpdateById(Integer type){
		if(type == null){
			type = 1;
		}
		if(type == 1){
			//新增
			BladeUser user = new BladeUser();
			user.setDeptId("123123");
			user.setAccount("test");
			user.setTenantId("123123");
			user.setCreateUser(123123L);
			userService.save(user);
			return R.success("新增成功");
		}else{
			//修改
			BladeUser user = userService.getOne(Wrappers.lambdaQuery(BladeUser.class).eq(BladeUser::getAccount, "test"));
			user.setCreateUser(null);
			//userService.updateById(user);
			userService.saveOrUpdate(user);
			return R.success("修改成功");
		}

	}

	@GetMapping("/testHandleCount")
	public R<String> testHandleCount(){
		String date = "********";
		try {
			alarmService.statCountAndRateIn30Day(date);
		} catch (Exception e) {
			log.error("执行失败",e);
			return R.fail("执行失败,"+e.getMessage());
		}
		return R.success("执行成功");
	}


	@GetMapping("/testBdmRoute")
	public R<String> testBdmRoute(){
		task.generateBdmRoute();
		return R.data("执行完成");
	}

	@GetMapping("/testOnlineRate")
	public R<String> testOnlineRate(String dateString){
		cacheTask.statisticVehicleOnlineRate(dateString);
		return R.data("执行完成");
	}

	@GetMapping("/testStopPoint")
	public R<String> testStopPoint(String startDate, String endDate){
		String startDateStr = "2023-09-10";
		String endDateStr = "2023-09-20";
		if(StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)){
			startDateStr = "2023-09-10";
			endDateStr = "2023-09-20";
		}else{
			startDateStr = startDate;
			endDateStr = endDate;
		}

		try{
			List<String> dateList = DateUtil.getDateList(startDateStr, endDateStr);
			dateList.forEach(item -> {
				item = item.replace("-","");
				fuseTask.statStopPoint(item);
			});
		}catch (Exception e){
			log.error("执行报错",e);
			return R.fail("执行报错"+e.getMessage());
		}

		return R.data("执行完成");
	}

	/**
	 * 统计每天每辆车每个小时在线时长
	 * @return
	 */
	@GetMapping("testVehOnlineDay")
	public R<String> testVehOnlineDay(){
		locationTask.vehOnlineDayStat();
		return R.success("执行完成");
	}

	/**
	 * 车辆上下线查询DB缓存跑批测试
	 * @return
	 */
	@GetMapping("/testOnOfflineDBCache")
	public R<String> testOnOfflineDBCache(String dayDuration){
		try {
			cacheTask.statisticVehicleOnlineOrOfflineCache(dayDuration);
		}catch (Exception e){
			return R.fail("执行失败");
		}
		return R.success("执行完成");
	}

	/**
	 * 补充定位条数测试
	 * @return
	 */
	@GetMapping("/testUpdatePointNum")
	public R<String> testUpdatePointNum(){
		try {
			statisticsTask.updatePointNum();
		}catch (Exception e){
			return R.fail("执行是吧");
		}
		return R.success("执行完成");
	}


	@GetMapping("/statNL")
	public R<String> statNotLocation(){
		try{
			List<LocationKudu> list = locationService.findAllUploadLocationPointListByDay("20230825","新Q22832D",1);
			List<Location> locList = new ArrayList<>();
			list.forEach(item -> {
				Location loc = new Location();
				BeanUtils.copyProperties(item, loc);
				locList.add(loc);
			});
			List<StatNotLocation> resList = statNotLocations(locList);
			return R.success("执行成功，数量为："+resList.size());
		}catch (Exception e){
			return R.fail("查询失败:"+e.getMessage());
		}
	}

	private static List<StatNotLocation> statNotLocations(List<Location> list){
		List<StatNotLocation> notLocList = new ArrayList<>();
		String licencePlate = list.get(0).getLicencePlate();
		Integer licenceColor = list.get(0).getLicenceColor();
		//连续未定位时间
		long duration = 0;
		Location startPos = null;
		Location endPos = null;
		for(int i = 1 ; i < list.size(); i++){
			Location locPre = list.get(i - 1);
			Location locNow = list.get(i);
			if((locNow.getLocTime() - locPre.getLocTime()) <= 300 && (locPre.getStateFlag()&2)==0 && (locNow.getStateFlag()&2)==0){
				//如果两个连续的点都是未定位，并且两个点的定位时间小于5分钟，表示是连续未定位点
				if(startPos == null){
					//记录开始点
					startPos = locPre;
				}
				//记录连续未定位时间
				duration = duration + (locNow.getLocTime() - locPre.getLocTime());
			}else{
				//如果两个点不是连续未定位点，则停止计算
				if(startPos != null){
					//记录结束点
					endPos = locPre;
					//if(endPos.getLocTime() - startPos.getLocTime() > 600){
					if(endPos.getLocTime() - startPos.getLocTime() > 0){
						//如果连续未定位时间大于10分钟
						//记录未定位段
						StatNotLocation snl = new StatNotLocation();
						snl.setDuration(duration);
						snl.setStatDate("test");
						snl.setCreateTime(new Date());
						snl.setStartTime(DateUtil.getDateBySecondTimestamp(startPos.getLocTime()));
						snl.setStartLongitude(startPos.getLongitude());
						snl.setStartLatitude(startPos.getLatitude());
						snl.setEndLongitude(endPos.getLongitude());
						snl.setEndLatitude(endPos.getLatitude());
						snl.setEndTime(DateUtil.getDateBySecondTimestamp(endPos.getLocTime()));
						snl.setLicencePlate(licencePlate);
						snl.setLicenceColor(licenceColor+"");
						notLocList.add(snl);
					}
					//清空信息
					duration = 0;
					startPos = null;
					endPos = null;
				}
			}
		}
		return notLocList;
	}

	@GetMapping("/testDailyCarMegaByte")
	public R<String> testDailyCarMegaByte(){

		try{
			statisticsTask.dailyCarMegaByte();
			return R.success("执行成功");
		}catch (Exception e){
			return R.fail("执行失败");
		}
	}

}
