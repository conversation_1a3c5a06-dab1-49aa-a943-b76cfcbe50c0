package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.StatTaskLog;

/**
 * <p>
 * 定时任务执行日志表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
public interface IStatTaskLogService extends IService<StatTaskLog> {

    /**
     * @description: 添加跑批任务过程信息
     * @author: zhouxw
     * @date: 2022/9/15 3:21 PM
     * @param: [taskType, content]
     * @return: void
     **/
    void addStatTaskProcessLog(String taskType , String content);
}
