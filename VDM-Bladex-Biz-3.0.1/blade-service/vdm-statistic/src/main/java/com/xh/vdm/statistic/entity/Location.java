package com.xh.vdm.statistic.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description: <PERSON>du中的定位数据 pos_gn.locations
 * @Author: zhouxw
 * @Date: 2023/5/23 19:12
 */
@Data
public class Location implements Serializable {

	//车牌号+车牌颜色，用于分组
	private String key;

	private String id;
	private String licencePlate;
	private Integer licenceColor ;
	private Long locTime;
	private Double longitude ;
	private Double latitude ;
	private String phone;
	private Integer altitude;
	private Double speed;
	private Long recvTime;
	private Integer bearing;
	private Double mileage;
	private Integer satelliteNum;
	private Long alarmFlag;
	private Long stateFlag;
	private Byte valid;
	private Integer expandSignal;
	private Long ioStatus;
	private String temperature;
	private Byte batch ;
	private Integer areacode;
	private Integer curareacode;
	private String auxStr ;
	private Byte protocolType;

}
