package com.xh.vdm.statistic.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.mapper.LocationsMapper;
import com.xh.vdm.statistic.service.LocationsService;
import com.xh.vdm.statistic.vo.request.LocationsRequest;
import com.xh.vdm.statistic.vo.response.LocationKuduResponse;
import com.xh.vdm.statistic.vo.response.tg.TargetResponse;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.mp.support.Query;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;

/**
 * 服务实现类
 */
@Service
@DS("impala")
public class LocationsServiceImpl extends ServiceImpl<LocationsMapper, LocationKudu> implements LocationsService {
	@Resource
	private LocationsMapper locationsMapper;


	/**
	 * 分页查询
	 *
	 * @param locationRequest 筛选条件
	 * @param query           分页对象
	 * @param ids
	 * @return 查询结果
	 */
	@Override
	public List<LocationKuduResponse> queryByPage(LocationsRequest locationRequest, Query query, List<String> ids) {
		return this.locationsMapper.queryByPage(locationRequest,query, ids);
	}

	@Override
	public long countLocations(LocationsRequest locationRequest, List<String> ids) {
		return this.locationsMapper.countLocations(locationRequest, ids);
	}


	@Override
	public List<LocationKudu> locationPage(List<List<Long>> deviceIds, List<List<Long>> targetIdList, Integer batch, Long startTime, Long endTime, Query query) {
		long limit = query.getSize();
		long offset = (query.getCurrent()-1) * query.getSize();
		return locationsMapper.locationPage(deviceIds, targetIdList, batch, startTime, endTime, limit, offset);
	}

	@Override
	public long locationCount(List<List<Long>> deviceIds, List<List<Long>> targetIdList, Integer batch, Long startTime, Long endTime) {
		return locationsMapper.locationCount(deviceIds, targetIdList, batch, startTime, endTime);
	}
}
