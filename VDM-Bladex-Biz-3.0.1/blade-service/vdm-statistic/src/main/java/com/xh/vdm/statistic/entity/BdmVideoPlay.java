package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 视频播放表
 */
@Data
@TableName(value = "taxicab.bdm_video_playback_record", autoResultMap = true)
public class BdmVideoPlay implements Serializable {

    /**
     * 车牌颜色
     */
    @TableField(value = "licence_color")
    private Integer licenceColor;

    /**
     * 车牌号
     */
    @TableField(value = "licence_plate")
    private String licencePlate;

    /**
     * 播放开始时间
     */
    @TableField(value = "play_start_time")
    private Integer playStartTime;

    /**
     * 播放结束时间
     */
    @TableField(value = "play_end_time")
    private Integer playEndTime;
}
