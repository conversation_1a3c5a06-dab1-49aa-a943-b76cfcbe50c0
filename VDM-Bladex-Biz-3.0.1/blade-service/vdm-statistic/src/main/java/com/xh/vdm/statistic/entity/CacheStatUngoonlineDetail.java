package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 未上线车辆明细DB缓存表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CacheStatUngoonlineDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 月份
     */
    private String month;

    /**
     * 上级平台
     */
    private Long vehicleOwnerId;

    /**
     * 车组id
     */
    private Long deptId;

    /**
     * 企业名称
     */
    private String deptName;

    /**
     * 车牌号码
     */
    private String licencePlate;

    /**
     * 车牌颜色
     */
    private String plateColor;

    /**
     * 车牌颜色编码
     */
    private String plateColorCode;

    /**
     * SIM卡号
     */
    @TableField("simId")
    private String simId;

    /**
     * 终端id
     */
    private Integer terminalId;

    /**
     * 终端型号
     */
    private String terminalModel;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 备注
     */
    private String note;


}
