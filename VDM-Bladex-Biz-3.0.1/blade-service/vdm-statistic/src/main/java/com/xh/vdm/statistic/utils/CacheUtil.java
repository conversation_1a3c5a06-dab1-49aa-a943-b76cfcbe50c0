package com.xh.vdm.statistic.utils;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.constant.RedisConstants;
import com.xh.vdm.statistic.entity.BladeDictBiz;
import com.xh.vdm.statistic.service.IBladeDictBizService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 缓存工具类
 * @Author: zhouxw
 * @Date: 2022/9/14 5:43 PM
 */
@Component
public class CacheUtil {

    @Resource
    private DictionaryUtil dictionaryUtil;

    @Resource
    private IBladeDictBizService dictService;

    @Resource
    private RedisTemplate redisTemplate;

    /**
     * @description: 获取车辆颜色 map
     * key: 颜色值， value: 颜色名
     * @author: zhouxw
     * @date: 2022/9/14 5:46 PM
     * @param: []
     * @return: java.util.Map<java.lang.Integer,java.lang.String>
     **/
    public Map<String , String> getVehicleColorMap(){
        Map<String, String> map = redisTemplate.opsForHash().entries(RedisConstants.HashKey.DICTIONARY_HASH_KEY + "-" + StatisticConstants.DICT_TYPE_COLOR);
        if(map == null){
            map = new HashMap<>();
        }
        if(map.size() < 1){
            //如果缓存没有加载，则重新加载缓存
            //1.查询车辆颜色
            List<BladeDictBiz> colorList = dictService.list(Wrappers.lambdaQuery(BladeDictBiz.class).eq(BladeDictBiz::getIsDeleted, StatisticConstants.IN_USE).eq(BladeDictBiz::getIsSealed, StatisticConstants.IN_USE).eq(BladeDictBiz::getCode, StatisticConstants.DICT_TYPE_COLOR));
            Map<String, String> finalMap = map;
            colorList.forEach(item -> {
                finalMap.put(item.getDictKey(), item.getDictValue());

                //2.加载到缓存
                redisTemplate.opsForHash().put(RedisConstants.HashKey.DICTIONARY_HASH_KEY + "-" + StatisticConstants.DICT_TYPE_COLOR , item.getDictKey() , item.getDictValue());
            });
        }
        return map;
    }



    /**
     * @description: 获取车辆颜色 map
     * key: 颜色名， value: 颜色值
     * @author: zhouxw
     * @date: 2022/9/14 5:46 PM
     * @param: []
     * @return: java.util.Map<java.lang.Integer,java.lang.String>
     **/
    public Map<String , String> getVehicleColorValueMap(){
        Map<String, String> map = redisTemplate.opsForHash().entries(RedisConstants.HashKey.DICTIONARY_HASH_KEY + "-" + StatisticConstants.VEHICLE_COLOR_VALUE);
        if(map == null){
            map = new HashMap<>();
        }
        if(map.size() < 1){
            //如果缓存没有加载，则重新加载缓存
            //1.查询车辆颜色
            List<BladeDictBiz> colorList = dictService.list(Wrappers.lambdaQuery(BladeDictBiz.class).eq(BladeDictBiz::getIsDeleted, StatisticConstants.IN_USE).eq(BladeDictBiz::getIsSealed, StatisticConstants.IN_USE).eq(BladeDictBiz::getCode, StatisticConstants.DICT_TYPE_COLOR).ne(BladeDictBiz::getParentId,0));
            Map<String, String> finalMap = map;
            colorList.forEach(item -> {
                finalMap.put(item.getDictValue(), item.getDictKey());

                //2.加载到缓存
                redisTemplate.opsForHash().put(RedisConstants.HashKey.DICTIONARY_HASH_KEY + "-" + StatisticConstants.VEHICLE_COLOR_VALUE, item.getDictValue(), item.getDictKey() );
            });
        }
        return map;
    }

    /**
     * @description: 根据车牌颜色名称获取车牌颜色值
     * @author: zhouxw
     * @date: 2023-02-46 11:19:52
     * @param: [colorName]
     * @return: java.lang.Integer
     **/
    public String getVehicleColorCodeByName(String colorName){
        if(StringUtils.isBlank(colorName)){
            return null;
        }
        Map<String, String> map = getVehicleColorValueMap();
        return map.get(colorName.trim());
    }


    /**
     * @description: 查询终端状态(车辆在线、离线状态)
     * @author: zhouxw
     * @date: 2023-03-81 14:53:43
     * @param: []
     * @return: java.util.Map<java.lang.String,java.lang.Integer>
     **/
    public Map<String , String> getTerminalStateMap(){
        Map<String, String> map = redisTemplate.opsForHash().entries(RedisConstants.HashKey.DICTIONARY_HASH_KEY + "-" + StatisticConstants.VEHICLE_ONLINE_OFFLINE_STATE);
        if(map == null){
            map = new HashMap<>();
        }
        if(map.size() < 1){
            //如果缓存没有加载，则重新加载缓存
            //1.查询终端状态
            List<BladeDictBiz> colorList = dictService.list(Wrappers.lambdaQuery(BladeDictBiz.class).eq(BladeDictBiz::getIsDeleted, StatisticConstants.IN_USE).eq(BladeDictBiz::getIsSealed, StatisticConstants.IN_USE).eq(BladeDictBiz::getCode, StatisticConstants.DICT_TYPE_ONLINE_OFFLINE).ne(BladeDictBiz::getParentId,0));
            Map<String, String> finalMap = map;
            colorList.forEach(item -> {
                finalMap.put(item.getDictKey() , item.getDictValue());

                //2.加载到缓存
                redisTemplate.opsForHash().put(RedisConstants.HashKey.DICTIONARY_HASH_KEY + "-" + StatisticConstants.VEHICLE_ONLINE_OFFLINE_STATE , item.getDictKey(), item.getDictValue());
            });
        }
        return map;
    }



}
