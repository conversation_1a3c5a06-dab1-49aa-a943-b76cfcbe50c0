package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 日里程表
 * <AUTHOR>
 * @date 2022/1/20 14:24
 */
@ApiModel(value = "日里程表")
@TableName("bdm_route")
@Data
public class BdmRoute {
    /**
     * 编号
     */
    @TableId(value = "id",type = IdType.AUTO)
    @ApiModelProperty(value = "编号")
    private Long id;

    /**
     * 车牌号码
     */
    @TableField("licence_plate")
    @ApiModelProperty(value = "车牌号码")
    private String licencePlate;

    /**
     * 车牌颜色
     */
    @TableField("licence_color")
    @ApiModelProperty(value = "车牌颜色")
    private Integer licenceColor;

    /**
     * 开始时间
     */
    @TableField("start_time")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     *
     */
    @TableField("form")
    @ApiModelProperty(value = "（已废弃）")
    private Long form;

    /**
     * 状态
     */
    @TableField("state")
    @ApiModelProperty(value = "状态（已废弃）")
    private Integer state;

    /**
     * 处理标志
     */
    @TableField("deal")
    @ApiModelProperty(value = "处理标志（已废弃）")
    private Integer deal;

    /**
     * 里程
     */
    @TableField("mileage")
    @ApiModelProperty(value = "里程")
    private Double mileage;

    /**
     * 开始地址
     */
    @TableField("depart")
    @ApiModelProperty(value = "开始地址")
    private String depart;

    /**
     * 目的地
     */
    @TableField("destination")
    @ApiModelProperty(value = "目的地")
    private String destination;

    /**
     * 敏感区域
     */
    @TableField("sensitive_area")
    @ApiModelProperty(value = "敏感区域（无）")
    private String sensitiveArea;

    /**
     * 目的地
     */
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 出发地经度
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "出发地经度")
    private Double departLongitude;

    /**
     * 出发地纬度
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "出发地纬度")
    private Double departLatitude;

    /**
     * 目的地经度
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "目的地经度")
    private Double destinationLongitude;

    /**
     * 目的地纬度
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "目的地纬度")
    private Double destinationLatitude;

}
