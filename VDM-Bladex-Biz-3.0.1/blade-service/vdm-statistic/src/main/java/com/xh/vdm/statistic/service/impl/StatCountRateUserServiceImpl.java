package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.StatCountRateUser;
import com.xh.vdm.statistic.mapper.StatCountRateUserMapper;
import com.xh.vdm.statistic.service.IStatCountRateUserService;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 按用户统计报警数和处理数、处理率 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Service
public class StatCountRateUserServiceImpl extends ServiceImpl<StatCountRateUserMapper, StatCountRateUser> implements IStatCountRateUserService {

}
