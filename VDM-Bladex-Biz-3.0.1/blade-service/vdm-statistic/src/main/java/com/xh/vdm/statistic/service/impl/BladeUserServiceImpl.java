package com.xh.vdm.statistic.service.impl;

import com.xh.vdm.statistic.entity.BladeUser;
import com.xh.vdm.statistic.entity.UserDept;
import com.xh.vdm.statistic.mapper.BladeUserMapper;
import com.xh.vdm.statistic.service.IBladeUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 用户表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@Service
public class BladeUserServiceImpl extends ServiceImpl<BladeUserMapper, BladeUser> implements IBladeUserService {


	@Override
	public List<String> findRoleStringByRoleIds(List<Long> roleIds) {
		return baseMapper.getRoleNamesByRoleIds(roleIds);
	}

	@Override
	public List<Long> findUserIdsWithMultiDeptCount() {
		return baseMapper.getUserIdWithMultiDept();
	}

	@Override
	public List<Long> findDeptIdsByUserId(Long userId) {
		return baseMapper.getDeptIdsByUserId(userId);
	}

	@Override
	public void updateDeptId(UserDept userDept) {
		baseMapper.updateDeptIdByUserId(userDept);
	}
}
