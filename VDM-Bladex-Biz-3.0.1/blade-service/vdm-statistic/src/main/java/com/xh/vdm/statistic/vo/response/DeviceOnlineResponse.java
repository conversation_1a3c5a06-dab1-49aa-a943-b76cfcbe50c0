package com.xh.vdm.statistic.vo.response;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (BdmDeviceOnline)实体类
 */
@Data
public class DeviceOnlineResponse implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private Long deviceId;

	private Integer deviceType;

	private String uniqueId;

	private String deviceNum;

	private Date startTime;

	private Date endTime;

	private Long targetId;

	private Integer targetType;

	private String targetName;

	private Double onlineTime;

	private Long deptId;
	private String deptName;

}

