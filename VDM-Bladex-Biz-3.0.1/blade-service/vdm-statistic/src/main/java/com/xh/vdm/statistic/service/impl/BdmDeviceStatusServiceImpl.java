package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.BdmDeviceOnline;
import com.xh.vdm.statistic.entity.BdmDeviceStatus;
import com.xh.vdm.statistic.mapper.BdmDeviceOnlineMapper;
import com.xh.vdm.statistic.mapper.BdmDeviceStatusMapper;
import com.xh.vdm.statistic.service.BdmDeviceStatusService;
import com.xh.vdm.statistic.vo.response.AlarmResponse;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.DataAuthCE;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * (BdmDeviceLink)表服务实现类
 */
@Service
public class BdmDeviceStatusServiceImpl extends ServiceImpl<BdmDeviceStatusMapper, BdmDeviceStatus> implements BdmDeviceStatusService {

	@Resource
	private BdmDeviceStatusMapper bdmDeviceStatusMapper;
	@Resource
	private BdmDeviceOnlineMapper bdmDeviceOnlineMapper;
	public static final List<Integer> TYPE_LIST = Arrays.asList(0, 4, 5, 6, 7, 8, 9, 10, 11, 12);

	@Resource
	private CETokenUtil ceTokenUtil;


	@Override
	public IPage<BdmDeviceStatus> page(String deviceNum, String targetName, Integer runningStatus, Long startTime, Long endTime, Query query, Long userId) {

		IPage<BdmDeviceStatus> iPage = new Page<>(query.getCurrent(),query.getSize());
		Date startT = null;
		if (null != startTime) {
			startT = new Date(startTime*1000);
		}
		Date endT = null;
		if (null != endTime) {
			endT = new Date(endTime*1000);
		}
		if (AuthUtil.isAdministrator()) {
			userId = null;
		}
		DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
		List<BdmDeviceStatus> list = this.bdmDeviceStatusMapper.queryAll(query,deviceNum,targetName,runningStatus,startT,endT,userId, dataAuthCE.getOrgListStr(), dataAuthCE.getAccount());
		iPage.setRecords(list);
		if (!list.isEmpty()) {
			long totalCount = bdmDeviceStatusMapper.count(deviceNum,targetName,runningStatus,startT,endT,userId, dataAuthCE.getOrgListStr(), dataAuthCE.getAccount());
			iPage.setTotal(totalCount);
			iPage.setPages(totalCount / query.getSize());
		}
		return iPage;
	}

	@Override
	public IPage<AlarmResponse> alarmPage(Integer action, Long time, Long deviceId, Integer deviceType, Query query) {
		IPage<AlarmResponse> iPage = new Page<>(query.getCurrent(),query.getSize());
		if (action == 0){
			List<AlarmResponse> list = this.bdmDeviceStatusMapper.getAlarmPage(query, time, deviceId, deviceType, TYPE_LIST);
			iPage.setRecords(list);
			if (!list.isEmpty()) {
				long totalCount = bdmDeviceStatusMapper.getAlarmCount(time, deviceId, deviceType, TYPE_LIST);
				iPage.setTotal(totalCount);
				iPage.setPages(totalCount / query.getSize());
			}
		}else {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

			BdmDeviceOnline deviceOnline = bdmDeviceOnlineMapper.selectOne(  new QueryWrapper<BdmDeviceOnline>()
				.apply("to_char(end_time, 'YYYY-MM-DD HH24:MI:SS') = '" + sdf.format(time * 1000) + "'")
				.eq("device_id", deviceId)
				.eq("device_type", deviceType)
			);

			if (deviceOnline != null) {
				long startTimeUnix = deviceOnline.getStartTime().getTime() / 1000;
				List<AlarmResponse> list = this.bdmDeviceStatusMapper.getAlarmPageByAction(query, time, deviceId, deviceType,startTimeUnix, TYPE_LIST);
				iPage.setRecords(list);
				if (!list.isEmpty()) {
					long totalCount = bdmDeviceStatusMapper.getAlarmPageByActionCount(time, deviceId, deviceType,startTimeUnix, TYPE_LIST);
					iPage.setTotal(totalCount);
					iPage.setPages(totalCount / query.getSize());
				}
			}

		}
		return iPage;
	}
}
