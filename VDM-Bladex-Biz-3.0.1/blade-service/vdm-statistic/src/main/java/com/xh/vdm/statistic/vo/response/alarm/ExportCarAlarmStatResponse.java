package com.xh.vdm.statistic.vo.response.alarm;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel(value = "返回体：车辆报警统计导出")
@Data
public class ExportCarAlarmStatResponse {

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "单位名称"})
	private String deptName;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "车牌颜色"})
	private String licenceColor;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "车牌号"})
	private String licencePlate;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "行业类型"})
	private String vehicleUseType;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "车辆归属"})
	private String vehicleOwnerName;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "接入方式"})
	private String accessMode;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "报警数"})
	private Integer numAlarm;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "紧急报警数"})
	private Integer numEmergency;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "终端超速报警数"})
	private Integer numOverSpeedTerminal;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "超速预警数"})
	private Integer numOverSpeedWarn;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "分段限速报警数"})
	private Integer numOverSpeedSegment;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "道路限速报警数"})
	private Integer numOverSpeedRoad;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "夜间限速报警数"})
	private Integer numOverSpeedNight;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "累计驾驶疲劳报警数"})
	private Integer numFatigueAccumulate;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "违规行驶报警数"})
	private Integer numViolate;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "禁行异动报警数"})
	private Integer numForbidDrive;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "终端疲劳驾驶报警数"})
	private Integer numFatigueTerminal;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "疲劳驾驶预警数"})
	private Integer numFatigueWarn;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "超时驾驶报警数"})
	private Integer numOverTimeDrive;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "超时停车报警数"})
	private Integer numOverTimePark;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "平台疲劳驾驶报警数"})
	private Integer numFatiguePlatform;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "夜间行驶报警数"})
	private Integer numNightDrive;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "进出区域报警数"})
	private Integer numAreaInOut;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "进出路线报警数"})
	private Integer numRouteInOut;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "路段行驶时间异常报警数"})
	private Integer numRouteDriveTimeBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "路线偏离报警数"})
	private Integer numRouteDeviate;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "终端附加超速报警数"})
	private Integer numOverSpeedTerminalAddition;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "进出区域/路线报警数"})
	private Integer numAreaOrRouteInOut;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "路段行驶时间附加异常报警数"})
	private Integer numAdditionRouteDriveTimeBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "禁入报警数"})
	private Integer numForbidIn;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "禁出报警数"})
	private Integer numForbidOut;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "跨入报警数"})
	private Integer numStepIn;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "跨出报警数"})
	private Integer numStepOut;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "跨入/跨出报警数"})
	private Integer numStepInOut;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "ADAS失效报警数"})
	private Integer numAdasBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "前车碰撞报警数"})
	private Integer numCarCollision;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "车道偏离报警数"})
	private Integer numLaneDeviate;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "车距过近报警数"})
	private Integer numCloseDistance;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "行人碰撞报警数"})
	private Integer numPedestrianCollision;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "频繁变道报警数"})
	private Integer numFrequentChangeLane;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "道路标识超限报警数"})
	private Integer numOverSizeOrWeight;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "障碍物报警数"})
	private Integer numObstacle;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "道路标志识别报警数"})
	private Integer numSignRecognition;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "主动抓拍报警数"})
	private Integer numActiveCapture;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "超车变道报警数"})
	private Integer numOvertakeChangeLane;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "危险驾驶行为报警数"})
	private Integer numDangerousDrive;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "生理疲劳报警数"})
	private Integer numBodyFatigue;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "接打电话报警数"})
	private Integer numPhone;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "抽烟报警数"})
	private Integer numSmoke;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "分神驾驶报警数"})
	private Integer numDistraction;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "未检测到驾驶员报警数"})
	private Integer numNoDriver;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "驾驶员变更报警数"})
	private Integer numChangeDriver;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "双手脱离方向盘报警数"})
	private Integer numSteeringWheelWithoutControl;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "DSM失效报警数"})
	private Integer numDsmBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "自动抓拍报警数"})
	private Integer numAutoCapture;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "急加速报警数"})
	private Integer numUrgentAccelerate;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "急减速报警数"})
	private Integer numUrgentModerate;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "急转弯报警数"})
	private Integer numUrgentSwerve;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "怠速报警数"})
	private Integer numIdle;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "GNSS模块故障报警数"})
	private Integer numGnssBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "GNSS天线未接入报警数"})
	private Integer numAntennaOff;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "GNSS天线短路报警数"})
	private Integer numAntennaShortCircuit;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "终端主电源欠压报警数"})
	private Integer numPowerUnderVoltage;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "终端主电源掉电报警数"})
	private Integer numPowerOff;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "终端LCD或显示器故障报警数"})
	private Integer numScreenBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "TTS模块故障报警数"})
	private Integer numTtsBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "摄像头故障报警数"})
	private Integer numCameraBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "IC卡模块故障报警数"})
	private Integer numCardBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "右转盲区异常报警数"})
	private Integer numRightBlindAreaBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "车辆VSS报警数"})
	private Integer numVss;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "车辆油量异常报警数"})
	private Integer numGasBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "车辆被盗报警数"})
	private Integer numStolen;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "车辆非法点火报警数"})
	private Integer numInvalidOn;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "车辆非法位移报警数"})
	private Integer numInvalidMove;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "碰撞侧翻报警数"})
	private Integer numCollisionRollover;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "侧翻报警数"})
	private Integer numRollover;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "存储器故障报警数"})
	private Integer numStoreBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "视频信号丢失报警数"})
	private Integer numVideoLost;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "视频信号遮挡报警数"})
	private Integer numVideoCover;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "视频信号丢失报警数"})
	private Integer numDeviceVideoLost;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "视频信号遮挡报警数"})
	private Integer numDeviceVideoCover;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "存储单元故障报警数"})
	private Integer numStoreUnitBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "其它视频设备故障报警数"})
	private Integer numVideoDeviceBug;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "客车超员报警数"})
	private Integer numOverload;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "异常驾驶行为报警数"})
	private Integer numAbnormalDrive;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "报警录像达到存储阀值	报警数"})
	private Integer numOverStore;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"车辆报警统计", "报停异动报警数"})
	private Integer numMoveWhileStop;
}
