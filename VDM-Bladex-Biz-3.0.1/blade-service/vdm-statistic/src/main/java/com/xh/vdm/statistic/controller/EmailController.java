package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.DeptAndEmail;
import com.xh.vdm.statistic.entity.StatEmailHistory;
import com.xh.vdm.statistic.service.IStatEmailService;
import com.xh.vdm.statistic.task.StatisticsTask;
import com.xh.vdm.statistic.utils.R;
import com.xh.vdm.statistic.vo.request.EmailRequest;
import com.xh.vdm.statistic.vo.response.EmailHistoryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Description: 邮件管理
 * @Author: zhouxw
 * @Date: 2023/3/2 9:31
 */
@RestController
@RequestMapping("/bt/statistics/email")
@Slf4j
public class EmailController {


    @Resource
    private IStatEmailService emailService;

    @Resource
    private StatisticsTask task;

    private static final ThreadLocal<SimpleDateFormat> sdfYMD = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd"));
    private static final ThreadLocal<SimpleDateFormat> sdfYM = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMM"));



    /**
     * @description: 查询邮件发送历史
     * @author: zhouxw
     * @date: 2023-03-61 10:58:07
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<java.util.List<com.xh.vdm.statistic.entity.StatEmail>>
     **/
    @PostMapping("/findSendHistory")
    public R<IPage<EmailHistoryResponse>> findSendHistory(@RequestBody EmailRequest request){

        try{
            //1.参数初始化
            if(request.getCurrent() == null){
                request.setCurrent(1);
            }
            if(request.getSize() == null){
                request.setSize(10);
            }

            //2.查询发送记录
            IPage<EmailHistoryResponse> page = new Page<>();
            page.setCurrent(request.getCurrent());
            page.setSize(request.getSize());
            IPage<EmailHistoryResponse> list = emailService.findEmailHistoryList(page, request);
            return R.data(list);
        }catch (Exception e){
            log.error("查询邮件发送历史失败",e);
            return R.fail("查询邮件发送历史失败");
        }
    }


    /**
     * @description: 重新发送邮件：日报/月报
     * @author: zhouxw
     * @date: 2023-03-62 10:48:29
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<java.lang.Boolean>
     **/
    @GetMapping("/reSend")
    public R<Boolean> reSendEmail(@RequestParam Long historyId){


        try{
            //1.根据historyId获取邮件信息
            StatEmailHistory history = emailService.getById(historyId);

            //2.判断报表类型
            DeptAndEmail de = new DeptAndEmail();
            de.setEmail(history.getReceiveMailAddress());
            de.setUsername(history.getUsername());
            de.setUserId(history.getUserId());
            de.setDeptId(history.getDeptId());
            de.setDeptName(history.getDeptName());

            Date dataDate = sdfYMD.get().parse(history.getDataDate());

            int type = history.getType();
            int sendType = StatisticConstants.EMAIL_SEND_TYPE_PAGE;
            if(StatisticConstants.EMAIL_TYPE_DAY == type){
                //如果是日报
                task.statAndSendForOneDay(de, dataDate, sendType);
            }else{
                //如果是月报
                task.statAndSendMailForMonth(de, sdfYM.get().parse(history.getDataDate()), sendType);
            }
            return R.success("重新发送邮件成功");
        }catch (Exception e){
            log.error("重新发送邮件出错",e);
            return R.fail("重新发送邮件出错");
        }


    }



}
