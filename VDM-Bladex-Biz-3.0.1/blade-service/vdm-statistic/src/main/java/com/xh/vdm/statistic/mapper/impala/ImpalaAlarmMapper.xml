<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xh.vdm.statistic.mapper.impala.ImpalaAlarmMapper">

    <sql id="dataRangeCondition">
        <if test="request.serviceRole == 1">
            and service_state != 2
        </if>
        <if test="request.serviceRole == 2">
            and service_state != 1
        </if>
        <if test="(request.deptList != null and request.deptList.size() gt 0) and (request.vehicleSliceList == null or request.vehicleSliceList.size() lte 0)">
            and dept_id in
            <foreach collection="request.deptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
        <if test="(request.deptList == null or request.deptList.size() lte 0) and (request.vehicleSliceList != null and request.vehicleSliceList.size() gt 0)">
            and (
                <foreach collection="request.vehicleSliceList" item="vehicleList" separator=" or ">
                    vehicle_id in
                    <foreach collection="vehicleList" item="vehicleId" open="(" close=")" separator=",">
                        #{vehicleId}
                    </foreach>
                </foreach>
            )
        </if>
        <if test="request.deptList != null and request.deptList.size() gt 0 and request.vehicleSliceList != null and request.vehicleSliceList.size() gt 0">
            and (
                dept_id in
                <foreach collection="request.deptList" item="deptId" index="i" open="(" close=")" separator=",">
                    #{deptId}
                </foreach>
                or (
                    <foreach collection="request.vehicleSliceList" item="vehicleList" separator=" or ">
                        vehicle_id in
                        <foreach collection="vehicleList" item="vehicleId" open="(" close=")" separator=",">
                            #{vehicleId}
                        </foreach>
                    </foreach>
                )
            )
        </if>
    </sql>

    <sql id="deptRangeCondition">
        <if test="request.serviceRole == 1">
            and service_state != 2
        </if>
        <if test="request.serviceRole == 2">
            and service_state != 1
        </if>
        <if test="request.deptList != null and request.deptList.size() gt 0">
            and dept_id in
            <foreach collection="request.deptList" item="deptId" index="i" open="(" close=")" separator=",">
                #{deptId}
            </foreach>
        </if>
    </sql>

    <sql id="basicBusinessCondition">
        and alarm_level != 0
        <if test="request.startTime != null">
            and alarm_end_time &gt;= #{request.startTime}
        </if>
        <if test="request.endTime != null">
            and alarm_time &lt;= #{request.endTime}
        </if>
        <if test="request.vehicleIdSliceList != null and request.vehicleIdSliceList.size() gt 0">
            and (
                <foreach collection="request.vehicleIdSliceList" item="vehicleIdList" separator=" or ">
                    vehicle_id in
                    <foreach collection="vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                        #{vehicleId}
                    </foreach>
                </foreach>
            )
        </if>
    </sql>

    <sql id="alarmTypeCondition">
        <if test="request.alarmTypeList != null and request.alarmTypeList.size() gt 0">
            and alarm_type in
            <foreach collection="request.alarmTypeList" item="alarmType" index="i" open="(" close=")" separator=",">
                #{alarmType}
            </foreach>
        </if>
    </sql>

    <sql id="dealCondition">
        <if test="request.serviceRole == 1">
            <if test="request.dealState == 0">
                and (server_state is null or server_state = 0)
            </if>
            <if test="request.dealState == 1">
                and server_state = 1
            </if>
        </if>
        <if test="request.serviceRole == 2">
            <if test="request.dealState == 0">
                and (third_state is null or third_state = 0)
            </if>
            <if test="request.dealState == 1">
                and third_state = 1
            </if>
        </if>
        <if test="request.serviceRole == 3">
            <if test="request.dealState == 0">
                and (company_state is null or company_state = 0)
            </if>
            <if test="request.dealState == 1">
                and company_state = 1
            </if>
        </if>
    </sql>

    <select id="getNumOverSpeed" resultType="int">
        select count(1) from alarms where alarm_type in (1, 100, 101, 105) and (is_wrong is null or is_wrong = 0)
        <if test="request.alarmType != null">
            and alarm_type = #{request.alarmType}
        </if>
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
    </select>

    <select id="getOverSpeedList" resultType="com.xh.vdm.statistic.entity.impala.ImpalaAlarm">
        select * from alarms where alarm_type in (1, 100, 101, 105) and (is_wrong is null or is_wrong = 0)
        <if test="request.alarmType != null">
            and alarm_type = #{request.alarmType}
        </if>
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
        order by alarm_time desc
        <if test="current gt 0 and size gt 0">
            limit ${size} offset ${(current - 1) * size}
        </if>
    </select>

    <select id="getNumFatigueDrive" resultType="int">
        select count(1) from alarms where alarm_type in (2, 102, 161) and (is_wrong is null or is_wrong = 0)
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
    </select>

    <select id="getFatigueDriveList" resultType="com.xh.vdm.statistic.entity.impala.ImpalaAlarm">
        select * from alarms where alarm_type in (2, 102, 161) and (is_wrong is null or is_wrong = 0)
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
        order by alarm_time desc
        <if test="current gt 0 and size gt 0">
            limit ${size} offset ${(current - 1) * size}
        </if>
    </select>

    <select id="getNumNightDrive" resultType="int">
        select count(1) from alarms where alarm_type = 103 and (is_wrong is null or is_wrong = 0)
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
    </select>

    <select id="getNightDriveList" resultType="com.xh.vdm.statistic.entity.impala.ImpalaAlarm">
        select * from alarms where alarm_type = 103 and (is_wrong is null or is_wrong = 0)
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
        order by alarm_time desc
        <if test="current gt 0 and size gt 0">
            limit ${size} offset ${(current - 1) * size}
        </if>
    </select>

    <select id="getNumWrong" resultType="int">
        select count(1) from alarms where is_wrong = 1
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
        <include refid="alarmTypeCondition" />
    </select>

    <select id="getWrongList" resultType="com.xh.vdm.statistic.entity.impala.ImpalaAlarm">
        select * from alarms where is_wrong = 1
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
        <include refid="alarmTypeCondition" />
        order by alarm_time desc
        <if test="current gt 0 and size gt 0">
            limit ${size} offset ${(current - 1) * size}
        </if>
    </select>

    <select id="getCarAlarmStatList" resultType="com.xh.vdm.statistic.dto.alarm.AlarmCount">
        select vehicle_id, alarm_type, count(1) as num_alarm from alarms where (is_wrong is null or is_wrong = 0)
        <if test="request.alarmLevel != null">
            and alarm_level = #{request.alarmLevel}
        </if>
        <if test="request.alarmOrigin != null and request.alarmOrigin.length() gt 0">
            and alarm_origin = #{request.alarmOrigin}
        </if>
        <if test="request.durationTime != null">
            and alarm_end_time - alarm_time &gt;= #{request.durationTime}
        </if>
        <if test="request.speed != null">
            and speed &gt;= #{request.speed}
        </if>
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
        <include refid="alarmTypeCondition" />
        <include refid="dealCondition" />
        group by vehicle_id, alarm_type
    </select>

    <select id="getCarAlarmAnalysisList" resultType="com.xh.vdm.statistic.entity.impala.ImpalaAlarm">
        select * from alarms where (is_wrong is null or is_wrong = 0)
        <if test="request.vehicleId != null">
            and vehicle_id = #{request.vehicleId}
        </if>
        <if test="request.alarmLevel != null">
            and alarm_level = #{request.alarmLevel}
        </if>
        <if test="request.alarmOrigin != null and request.alarmOrigin.length() gt 0">
            and alarm_origin = #{request.alarmOrigin}
        </if>
        <if test="request.durationTime != null">
            and alarm_end_time - alarm_time &gt;= #{request.durationTime}
        </if>
        <if test="request.speed != null">
            and speed &gt;= #{request.speed}
        </if>
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
        <include refid="alarmTypeCondition" />
        <include refid="dealCondition" />
        order by alarm_time desc
    </select>

    <select id="getDriverAlarmStatList" resultType="com.xh.vdm.statistic.dto.alarm.AlarmCount">
        select id_card, driver_name, dept_id, vehicle_id, count(1) as num_alarm from alarms where (is_wrong is null or is_wrong = 0) and id_card is not null and id_card != ''
        <if test="request.alarmLevel != null">
            and alarm_level = #{request.alarmLevel}
        </if>
        <if test="request.driverName != null and request.driverName.length() gt 0">
            and driver_name like concat('%', #{request.driverName}, '%')
        </if>
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
        <include refid="alarmTypeCondition" />
        group by id_card, driver_name, dept_id, vehicle_id
    </select>

    <select id="getDriverAlarmAnalysisList" resultType="com.xh.vdm.statistic.entity.impala.ImpalaAlarm">
        select * from alarms where (is_wrong is null or is_wrong = 0) and id_card is not null and id_card != ''
        <if test="request.alarmLevel != null">
            and alarm_level = #{request.alarmLevel}
        </if>
        <if test="request.driverName != null and request.driverName.length() gt 0">
            and driver_name like concat('%', #{request.driverName}, '%')
        </if>
        <if test="request.idCard != null and request.idCard.length() gt 0">
            and id_card = #{request.idCard}
        </if>
        <if test="request.vehicleId != null">
            and vehicle_id = #{request.vehicleId}
        </if>
        <include refid="dataRangeCondition" />
        <include refid="basicBusinessCondition" />
        <include refid="alarmTypeCondition" />
        order by alarm_time desc
    </select>

    <select id="getNumAlarmGroupByDeptType" resultType="java.util.Map">
        select dept_id, alarm_type, count(1) as num from alarms where (is_wrong is null or is_wrong = 0)
        <include refid="deptRangeCondition" />
        <include refid="basicBusinessCondition" />
        <include refid="alarmTypeCondition" />
        group by dept_id, alarm_type
    </select>

    <select id="getNumDealGroupByDeptType" resultType="java.util.Map">
        select dept_id, alarm_type, count(1) as num from alarms where (is_wrong is null or is_wrong = 0)
        <if test="request.serviceRole == 1">
            and server_state = 1
        </if>
        <if test="request.serviceRole == 2">
            and third_state = 1
        </if>
        <if test="request.serviceRole == 3">
            and company_state = 1
        </if>
        <include refid="deptRangeCondition" />
        <include refid="basicBusinessCondition" />
        <include refid="alarmTypeCondition" />
        group by dept_id, alarm_type
    </select>

    <select id="getLocationFromTimeSeg" resultType="com.xh.vdm.statistic.entity.Location">
        select longitude, latitude from locations
        where licence_color = #{licenceColor} and licence_plate = #{licencePlate} and loc_time &gt;= #{startTime} and loc_time &lt;= #{endTime}
        order by loc_time
    </select>

    <select id="getNumAlarmForDept" resultType="int">
        select count(1) as num_alarm from alarms where (is_wrong is null or is_wrong = 0)
        <include refid="deptRangeCondition" />
        <include refid="basicBusinessCondition" />
    </select>

    <select id="getCarRankOfAlarmType" resultType="com.xh.vdm.statistic.dto.alarm.AlarmCount">
        select licence_plate, count(1) as num_alarm from alarms where (is_wrong is null or is_wrong = 0)
        <include refid="deptRangeCondition" />
        <include refid="basicBusinessCondition" />
        <include refid="alarmTypeCondition" />
        group by licence_plate order by num_alarm desc
    </select>

    <select id="getNumEachCarDeal" resultType="com.xh.vdm.statistic.dto.alarm.AlarmCount">
        select licence_plate, company_measures, count(1) as num_alarm from alarms where (is_wrong is null or is_wrong = 0)
        <include refid="deptRangeCondition" />
        <include refid="basicBusinessCondition" />
        <include refid="alarmTypeCondition" />
        group by licence_plate, company_measures
    </select>

    <select id="getAlarmCountByDeptIds" resultType="long">
        select count(*) from alarms
        where (is_wrong is null or is_wrong = 0)
        <if test="deptList != null">
            and dept_id in (
            <foreach collection="deptList" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
        </if>
        <if test="startTime != null">
            and alarm_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and alarm_time &lt;= #{endTime}
        </if>
    </select>

    <select id="getAlarmCountByDeptIdsAndAlarmTypes" resultType="long">
        select count(*) from alarms
        where (is_wrong is null or is_wrong = 0)
        <if test="deptList != null">
            and dept_id in (
            <foreach collection="deptList" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
        </if>
        <if test="alarmTypes != null">
            and alarm_type in (
                <foreach collection="alarmTypes" item="alarmType" separator=",">
                    #{alarmType}
                </foreach>
            )
        </if>
        <if test="startTime != null">
            and alarm_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and alarm_time &lt;= #{endTime}
        </if>
    </select>


    <select id="getAlarmListByDeptIdsAndAlarmTypes" resultType="com.xh.vdm.statistic.entity.VehicleAndCount">
        select * from
        (
            select vehicle_id, licence_plate, licence_color ,count(*) count from alarms
            where (is_wrong is null or is_wrong = 0)
            <if test="deptList != null">
                and dept_id in (
                <foreach collection="deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="alarmTypes != null">
                and alarm_type in (
                <foreach collection="alarmTypes" item="alarmType" separator=",">
                    #{alarmType}
                </foreach>
                )
            </if>
            <if test="startTime != null">
                and alarm_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and alarm_time &lt;= #{endTime}
            </if>
            group by vehicle_id, licence_plate, licence_color
        ) a
        order by a.count desc
        <if test="limit != null">
            limit #{limit} offset 0
        </if>
    </select>

    <select id="getNumAlarmByDeptIdsAndAlarmTypes" resultType="int">
        select count(1) from
        (
        select vehicle_id, licence_plate, licence_color ,count(*) count from alarms
        where (is_wrong is null or is_wrong = 0)
        <if test="deptList != null">
            and dept_id in (
            <foreach collection="deptList" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
        </if>
        <if test="alarmTypes != null">
            and alarm_type in (
            <foreach collection="alarmTypes" item="alarmType" separator=",">
                #{alarmType}
            </foreach>
            )
        </if>
        <if test="startTime != null">
            and alarm_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and alarm_time &lt;= #{endTime}
        </if>
        <if test="licencePlate != null and licencePlate != ''">
            and licence_plate like concat('%', #{licencePlate}, '%')
        </if>
        <if test="licenceColor != null">
            and licence_color = #{licenceColor}
        </if>
        group by vehicle_id, licence_plate, licence_color
        ) a
    </select>

    <select id="getAlarmListByDeptIdsAndAlarmTypesPage" resultType="com.xh.vdm.statistic.entity.VehicleAndCount">
        select * from
        (
        select vehicle_id, licence_plate, licence_color ,count(*) count from alarms
        where (is_wrong is null or is_wrong = 0)
        <if test="deptList != null">
            and dept_id in (
            <foreach collection="deptList" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
        </if>
        <if test="alarmTypes != null">
            and alarm_type in (
            <foreach collection="alarmTypes" item="alarmType" separator=",">
                #{alarmType}
            </foreach>
            )
        </if>
        <if test="startTime != null">
            and alarm_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and alarm_time &lt;= #{endTime}
        </if>
        <if test="licencePlate != null and licencePlate != ''">
            and licence_plate like concat('%', #{licencePlate}, '%')
        </if>
        <if test="licenceColor != null">
            and licence_color = #{licenceColor}
        </if>
        group by vehicle_id, licence_plate, licence_color
        ) a
        order by a.count desc limit ${size} offset ${(current - 1) * size}
    </select>

    <select id="getVehicleCountByDeptIdsAndAlarmTypes" resultType="long">
        select count(*) from (
            select licence_plate, licence_color ,count(*) count from alarms
            where (is_wrong is null or is_wrong = 0)
            <if test="deptList != null and deptList.size() gt 0">
                and dept_id in (
                <foreach collection="deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
            </if>
            <if test="alarmTypes != null and alarmTypes.size() gt 0">
                and alarm_type in (
                <foreach collection="alarmTypes" item="alarmType" separator=",">
                    #{alarmType}
                </foreach>
                )
            </if>
            <if test="startTime != null">
                and alarm_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and alarm_time &lt;= #{endTime}
            </if>
            group by licence_plate, licence_color
        ) a
    </select>

    <select id="getDriverAlarmListByDeptIdsAndAlarmTypes" resultType="com.xh.vdm.statistic.entity.DriverNameAndCount">
        select * from
        (
        select driver_name, count(*) count from alarms
        where (is_wrong is null or is_wrong = 0)
        <if test="deptList != null">
            and dept_id in (
            <foreach collection="deptList" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
        </if>
        <if test="alarmTypes != null">
            and alarm_type in (
            <foreach collection="alarmTypes" item="alarmType" separator=",">
                #{alarmType}
            </foreach>
            )
        </if>
        <if test="startTime != null">
            and alarm_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and alarm_time &lt;= #{endTime}
        </if>
        group by driver_name
        ) a
        order by a.count desc
        <if test="limit != null">
            limit #{limit} offset 0
        </if>
    </select>

    <select id="getDriverAlarmListByDeptIdsAndAlarmTypesPage" resultType="com.xh.vdm.statistic.entity.DriverNameAndCount">
        select * from
        (
        select driver_name, count(*) count from alarms
        where (is_wrong is null or is_wrong = 0)
        <if test="deptList != null">
            and dept_id in (
            <foreach collection="deptList" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
        </if>
        <if test="alarmTypes != null">
            and alarm_type in (
            <foreach collection="alarmTypes" item="alarmType" separator=",">
                #{alarmType}
            </foreach>
            )
        </if>
        <if test="startTime != null">
            and alarm_time >= #{startTime}
        </if>
        <if test="endTime != null">
            and alarm_time &lt;= #{endTime}
        </if>
        <if test="driverName != null and driverName != ''">
            and driver_name = #{driverName}
        </if>
        group by driver_name
        ) a
        order by a.count desc
    </select>

    <select id="getAlarmsByConditionPage" resultType="com.xh.vdm.statistic.entity.impala.ImpalaAlarm">
        select *
        from alarms
        where 1 = 1
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and vehicle_id in (
                <foreach collection="request.vehicleIdList" item="id" separator=",">
                    #{id}
                </foreach>
            )
        </if>
        <if test="request.startTime != null">
            and alarm_time >= #{request.startTime}
        </if>
        <if test="request.endTime != null">
            and alarm_time &lt; #{request.endTime}
        </if>
        <if test="request.alarmTypeList != null and request.alarmTypeList.size() > 0">
            and alarm_type in (
                <foreach collection="request.alarmTypeList" item="alarmType" separator=",">
                    #{alarmType}
                </foreach>
            )
        </if>
    </select>



    <select id="getAlarmCountHour" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select rate.statDate 'statDate' , count(*) count
        from (
        select substring(from_unixtime(alarm_time),12,2) statDate from alarms
        where alarm_level != 0 and alarm_time &lt;= #{endTime,jdbcType=BIGINT}
        and alarm_time >= #{startTime,jdbcType=BIGINT}
        and (is_wrong != 1 or is_wrong is null)
        and ( dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        )
        ) rate group by statDate
    </select>


    <select id="getHandleCountHour" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select rate.statDate 'statDate' , count(*) count
        from (
        select substring(from_unixtime(alarm_time),12,2) statDate from alarms
        where alarm_level != 0
        and alarm_time &lt;= #{endTime,jdbcType=BIGINT} and alarm_time >= #{startTime,jdbcType=BIGINT}
        and (is_wrong != 1 or is_wrong is null)
        and ( dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        )
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方服务商 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        ) rate group by statDate
    </select>

    <select id="getFatigueCountByDateAndDuration" resultType="long">
        select count(*) from
        (
        select licence_plate , licence_color, alarm_type, alarm_time, alarm_end_time, (alarm_end_time - alarm_time) duration
        from alarms where alarm_type in (102,111)
        and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
        and ( (dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        ))
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        )
        ) a
        where a.duration > #{duration}
    </select>


    <select id="getFatigueHandleCountByDateAndDuration" resultType="long">
        select count(*) from
        (
        select licence_plate , licence_color, alarm_type, alarm_time, alarm_end_time, (alarm_end_time - alarm_time) duration
        from alarms where alarm_type in (102,2)
        and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        and ( (dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        ))
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        )
        ) a
        where a.duration > #{duration}
    </select>

    <select id="getAlarmCountByDate" resultType="long">
        select count(*) from
        (
        select licence_plate , licence_color, alarm_type, alarm_time, alarm_end_time
        from alarms bs where alarm_type in
        <foreach collection="alarmTypes" item="type" separator="," open="(" close=")">
            #{type}
        </foreach>
        and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
        and ( (bs.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        ))
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or bs.vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        )
        ) a
    </select>

    <select id="getAlarmHandleCountByDate" resultType="long">
        select count(*) from
        (
        select licence_plate , licence_color, alarm_type, alarm_time, alarm_end_time
        from alarms where alarm_level != 0 and alarm_type in
        <foreach collection="alarmTypes" item="type" separator="," open="(" close=")">
            #{type}
        </foreach>
        and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        and ((dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        ))
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        )
        ) a
    </select>

    <select id="getAlarmHandleCountPerDate" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select substring(from_unixtime(alarm_time),1,10) stat_date, count(*) count
        from alarms
        where alarm_level != 0
        and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        and ((dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        ))
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        )
        group by substring(from_unixtime(alarm_time),1,10)
    </select>


    <select id="getAlarmBaseByDate" resultType="com.xh.vdm.statistic.entity.AlarmBase">
        select dept_id, id alarmId, licence_plate licencePlate , licence_color licenceColor, alarm_type alarmType, from_unixtime(alarm_time) alarmTime, from_unixtime(alarm_end_time) alarmEndTime,
        vehicle_use_type, alarm_level, alarm_address, alarm_end_address,max_speed, speed, limit_speed, road_name
        from alarms where alarm_type in
        <foreach collection="alarmTypes" item="type" separator="," open="(" close=")">
            #{type}
        </foreach>
        and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
        and ( (dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        ))
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        )
    </select>

    <select id="getAlarmHandleCountByAlarmIds" resultType="long">
        select count(*) from
        (
        select licence_plate , licence_color, alarm_type, alarm_time, alarm_end_time
        from alarms where 1 = 1
        and id in
        <foreach collection="alarmIds" item="alarmId" open="(" close=")" separator=",">
            #{alarmId}
        </foreach>
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        ) a
    </select>


    <select id="getUnHandleRealTimeAlarmByPage" resultType="com.xh.vdm.statistic.entity.UnHandleRealTimeAlarm">
        select id, alarm_type, alarm_level, alarm_time, licence_plate, licence_color, longitude, latitude, speed, alarm_complete
        from alarms
        where alarm_level != 0
        and alarm_time &lt;= #{endTime,jdbcType=BIGINT}
        and alarm_time >= #{startTime,jdbcType=BIGINT}
        and (is_wrong != 1 or is_wrong is null)
        and ( dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        )
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 0
        </if>
        <!-- 第三方服务商 -->
        <if test="userType == 2">
            and third_state = 0
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 0
        </if>
        <if test="alarmTypeList != null">
            and alarm_type in
            <foreach collection="alarmTypeList" item="alarmType" open="(" close=")" separator=",">
                #{alarmType}
            </foreach>
        </if>
        and alarm_complete = 0
    </select>

    <select id="getAlarmCountByDeptIdAndDurationOrHandle" resultType="long">
        select (dc.num + bc.num) count from
        (select count(*) num from alarms
        where alarm_level != 0

        and dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        ) and (is_wrong != 1 or is_wrong is null) and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}) dc,
        (
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            select count(*) num from alarms
            where alarm_level != 0
            and vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
            and (is_wrong != 1 or is_wrong is null) and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
        </if>
        <if test="vehicleIds == null or vehicleIds.size() == 0">
            select 0 num
        </if>
        ) bc
    </select>

    <select id="getAlarmCountByDeptIdAndDurationAndType" resultType="long">
        select (dc.num + bc.num) count from
        (select count(*) num from alarms
        where alarm_level != 0
        and dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        ) and (is_wrong != 1 or is_wrong is null)
        and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
        and alarm_type in (
        <foreach collection="alarmTypes" item="alarmType" separator=",">
            #{alarmType}
        </foreach>
        )) dc,
        (
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            select count(*) num from alarms
            where alarm_level != 0
            and vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
            and (is_wrong != 1 or is_wrong is null)
            and alarm_time >= #{startTime}
            and alarm_time &lt; #{endTime}
            and alarm_type in (
            <foreach collection="alarmTypes" item="alarmType" separator=",">
                #{alarmType}
            </foreach>
            )
        </if>
        <if test="vehicleIds == null or vehicleIds.size() == 0">
            select 0 num
        </if>
        ) bc
    </select>

    <select id="getAlarmHandleCountByDeptIdAndDurationAndType" resultType="long">
        select (dc.num + bc.num) count from
        (select count(*) num from alarms
        where alarm_level != 0
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方服务商 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        and dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        ) and (is_wrong != 1 or is_wrong is null)
        and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}
        <if test="alarmTypes != null and alarmTypes.size() > 0">
            and alarm_type in (
            <foreach collection="alarmTypes" item="alarmType" separator=",">
                #{alarmType}
            </foreach>
            )
        </if>
        ) dc,
        (
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            select count(*) num from alarms
            where alarm_level != 0
            <!-- 服务商 -->
            <if test="userType == 1">
                and server_state = 1
            </if>
            <!-- 第三方服务商 -->
            <if test="userType == 2">
                and third_state = 1
            </if>
            <!-- 企业 -->
            <if test="userType == 3">
                and company_state = 1
            </if>
            and vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
            and (is_wrong != 1 or is_wrong is null)
            and alarm_time >= #{startTime}
            and alarm_time &lt; #{endTime}
            <if test="alarmTypes != null and alarmTypes.size() > 0">
                and alarm_type in (
                <foreach collection="alarmTypes" item="alarmType" separator=",">
                    #{alarmType}
                </foreach>
            </if>
            )
        </if>
        <if test="vehicleIds == null or vehicleIds.size() == 0">
            select 0 num
        </if>
        ) bc
    </select>

    <select id="getAlarmTypeAndDateAndCountOrHandle" resultType="com.xh.vdm.statistic.entity.AlarmTypeAndDateAndCount">
        select alarm_type, substring(from_unixtime(alarm_time),1,10) statDate, count(*) count
        from alarms
        where 1 = 1
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方服务商 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        and alarm_level != 0
        and(
            dept_id in (
                <foreach collection="deptIds" item="deptId" separator=",">
                    #{deptId}
                </foreach>
            )
            <if test="vehicleIds != null and vehicleIds.size() > 0">
                or vehicle_id in (
                    <foreach collection="vehicleIds" item="vehicleId" separator=",">
                        #{vehicleId}
                    </foreach>
                )
            </if>
        )
        and (is_wrong != 1 or is_wrong is null)
        and alarm_time >= #{startTime}
        and alarm_time &lt; #{endTime}
        and alarm_type in (
        <foreach collection="alarmTypes" item="alarmType" separator=",">
            #{alarmType}
        </foreach>
        )
        group by alarm_type, substring(from_unixtime(alarm_time),1,10)
        order by alarm_type
    </select>


    <select id="getAlarmTypeAndCountOrHandle" resultType="com.xh.vdm.statistic.entity.report.AlarmBaseTypeAndCount">
        select alarm_type,  count(*) count
        from alarms
        where 1 = 1
        <!-- 服务商 -->
        <if test="userType == 1">
            and server_state = 1
        </if>
        <!-- 第三方服务商 -->
        <if test="userType == 2">
            and third_state = 1
        </if>
        <!-- 企业 -->
        <if test="userType == 3">
            and company_state = 1
        </if>
        and alarm_level != 0
        and(
        dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        )
        and (is_wrong != 1 or is_wrong is null)
        and alarm_time >= #{startTime}
        and alarm_time &lt; #{endTime}
        <if test="alarmTypes != null and alarmTypes.size() > 0">
            and alarm_type in (
            <foreach collection="alarmTypes" item="alarmType" separator=",">
                #{alarmType}
            </foreach>
            )
        </if>
        group by alarm_type
        order by alarm_type
    </select>


    <select id="getVehicleAlarmCountTop" resultType="com.xh.vdm.statistic.entity.VehicleAndCount">
        select licence_plate, licence_color,vehicle_id, count(*) count
        from alarms
        where 1 = 1
        and alarm_level != 0
        and(
        dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
        )
        and (is_wrong != 1 or is_wrong is null)
        and alarm_time >= #{startTime}
        and alarm_time &lt; #{endTime}
        and alarm_type in (
        <foreach collection="alarmTypes" item="alarmType" separator=",">
            #{alarmType}
        </foreach>
        )
        group by licence_plate, licence_color, vehicle_id
        order by count(*) desc
        limit #{count}
    </select>

    <select id="getVehicleAlarmCountInfo" resultType="com.xh.vdm.statistic.entity.VehicleAndAlarmTypeAndCount">
        select licence_plate, licence_color,vehicle_id, alarm_type, count(*) count
        from alarms
        where 1 = 1
        and alarm_level != 0
        and vehicle_id in (
        <foreach collection="vehicleIds" item="vehicleId" separator=",">
            #{vehicleId}
        </foreach>
        )
        and (is_wrong != 1 or is_wrong is null)
        and alarm_time >= #{startTime}
        and alarm_time &lt; #{endTime}
        and alarm_type in (
        <foreach collection="alarmTypes" item="alarmType" separator=",">
            #{alarmType}
        </foreach>
        )
        group by licence_plate, licence_color, vehicle_id, alarm_type
    </select>

    <select id="getVehicleAlarmCountInfoByDriverDuration" resultType="com.xh.vdm.statistic.entity.AlarmTypeAndCount">
        select alarm_type, count(*) count
        from alarms
        where 1 = 1
        and alarm_level != 0
        and (is_wrong != 1 or is_wrong is null)
        <if test="list != null and list.size() gt 0">
            and (
            <foreach collection="list" item="item" separator=" or " >
                (
                    licence_plate = #{item.licencePlate} and licence_color = cast(#{item.licenceColor} as string)
                    and alarm_time >= #{item.durationStartTime} and alarm_time &lt;= #{item.durationEndTime}
                )
            </foreach>
            )
        </if>
        <if test="alarmTypes != null and alarmTypes.size() gt 0">
            and alarm_type in (
            <foreach collection="alarmTypes" item="alarmType" separator=",">
                #{alarmType}
            </foreach>
            )
        </if>
        group by alarm_type
    </select>

    <select id="test" resultType="com.xh.vdm.statistic.entity.impala.ImpalaAlarm">
        select * from alarms limit 1
    </select>

    <select id="getAlarmCountByDeptIdAndDuration" resultType="long">
        select dc.num  count from
        (select count(*) num from alarms
        where alarm_level != 0
        and (
            <if test="(deptIds != null and deptIds.size() gt 0) and (vehicleIds == null or vehicleIds.size() lte 0)">
                dept_id in
                <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                    #{deptId}
                </foreach>
            </if>
            <if test="(deptIds == null or deptIds.size() lte 0) and (vehicleIds != null and vehicleIds.size() gt 0)">
                vehicle_id in
                <foreach collection="vehicleIds" item="vehicleId" separator="," open="(" close=")">
                    #{vehicleId}
                </foreach>
            </if>
            <if test="(deptIds != null and deptIds.size() gt 0) and (vehicleIds != null and vehicleIds.size() gt 0)">
                dept_id in
                <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                    #{deptId}
                </foreach>
                or
                vehicle_id in
                <foreach collection="vehicleIds" item="vehicleId" separator="," open="(" close=")">
                    #{vehicleId}
                </foreach>
            </if>
        )
        and (is_wrong != 1 or is_wrong is null) and alarm_time >= #{startTime} and alarm_time &lt; #{endTime}) dc
    </select>
</mapper>
