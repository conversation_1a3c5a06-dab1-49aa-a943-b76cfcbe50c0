package com.xh.vdm.statistic.service;


import com.xh.vdm.statistic.entity.TerminalModelAndCount;
import com.xh.vdm.statistic.entity.TerminalTypeAndCount;
import com.xh.vdm.statistic.vo.TerminalBaseVO;

import java.util.List;
import java.util.Map;

/**
 * @Description: 定位终端相关
 * @Author: zhouxw
 * @Date: 2022/11/21 9:11 AM
 */
public interface ITerminalService {

    /**
     * @description: 查询定位终端类型和数量
     * 如果不指定deptId，就查询全区域
     * @author: zhouxw
     * @date: 2022/11/21 9:12 AM
     * @param: [deptId]
     * @return: java.util.List<com.xh.vdm.statistic.entity.TerminalTypeAndCount>
     **/
    List<TerminalTypeAndCount> findTerminalTypeAndCount(Long deptId);

    /**
     * @description: 查询定位终端型号和数量
     * 如果不指定deptId，就查询全区域
     * @author: zhouxw
     * @date: 2022/11/21 9:12 AM
     * @param: [deptId]
     * @return: java.util.List<com.xh.vdm.statistic.entity.TerminalTypeAndCount>
     **/
    List<TerminalModelAndCount> findTerminalModelAndCount(Long deptId);

	/**
	 * 查询终端映射表
	 */
	Map<String, String> getTerminalMap ();


	/**
	 * 查询终端基本信息列表
	 * @param deviceNum
	 * @param deviceType
	 * @param targetName
	 * @param deptIds
	 * @return
	 */
	List<TerminalBaseVO> findTerminalList(String deviceNum, Integer deviceType, String targetName, String deptIds, String account );

	/**
	 * 根据条件查询一条终端信息
	 * @param deviceNum
	 * @return
	 */
	TerminalBaseVO findTerminalOne(String deviceNum );

}
