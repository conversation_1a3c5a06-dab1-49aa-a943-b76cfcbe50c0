package com.xh.vdm.statistic.vo.response;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/7/26 16:26
 */
@Data
public class AlarmPushResponse {

	//报警id
	private String alarmId;
	//车牌号
	private String licencePlate;
	//车牌颜色
	private String licenceColor;
	//报警类型
	private String alarmType;
	//报警类型描述
	private String alarmTypeDesc;
	//报警等级
	private String alarmLevel;
	//报警时间
	private String alarmTime;
	//处理状态编码
	private String handleStateCode;
	//处理状态描述
	private String handleStateDesc;

	/**
	 * 速度
	 */
	private Double speed;
	/**
	 * 定位纬度
	 */
	private BigDecimal latitude;

	/**
	 * 定位经度
	 */
	private BigDecimal longitude;
}
