package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 定时任务执行日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatTaskLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 定时任务类型
     */
    private String taskType;

    /**
     * 定时任务描述
     */
    private String taskDesc;

    /**
     * 执行参数
     */
    private String taskParam;

    /**
     * 执行结果
     */
    private String result;

    /**
     * 执行结果描述
     */
    private String resultDesc;

    /**
     * 执行开始时间
     */
    private Date startTime;

    /**
     * 执行结束时间
     */
    private Date endTime;

    /**
     * 执行耗时（单位：毫秒）
     */
    private Long totalTime;

    /**
     * 数据创建时间
     */
    private Date createTime;


}
