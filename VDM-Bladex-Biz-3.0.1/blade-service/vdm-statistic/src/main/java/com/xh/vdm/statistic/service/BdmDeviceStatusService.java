package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.BdmDeviceStatus;
import com.xh.vdm.statistic.vo.response.AlarmResponse;
import org.springblade.core.mp.support.Query;

import java.util.List;

/**
 * (BdmDeviceLink)表服务接口
 */
public interface BdmDeviceStatusService extends IService<BdmDeviceStatus> {


	IPage<BdmDeviceStatus> page(String deviceNum, String targetName, Integer runningStatus, Long startTime, Long endTime, Query query, Long userId);

	IPage<AlarmResponse> alarmPage(Integer action, Long time, Long deviceId, Integer deviceType, Query query);
}
