package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.BdmTrackCalendar;
import org.springblade.entity.Location;

import java.util.Date;
import java.util.List;

public interface IBdmTrackCalendarService extends IService<BdmTrackCalendar> {

	List<BdmTrackCalendar> getLocCalendarByLocation (Location location);

	Integer list(Long deviceId, Integer deviceType,Long targetId, Integer targetType, Date month);
}
