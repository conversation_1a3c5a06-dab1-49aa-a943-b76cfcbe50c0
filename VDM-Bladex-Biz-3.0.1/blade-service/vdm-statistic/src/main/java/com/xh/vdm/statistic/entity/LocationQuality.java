package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 定位数据质量表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class LocationQuality implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车牌号
     */
    private String licencePlate;

    //车牌颜色
    private Integer plateColor;

	//车辆id
	private Integer vehicleId;
	//部门id
	private Long deptId;

    /**
     * 总上报数量
     */
    private Integer totalCount;

    /**
     * 总错误数量
     */
    private Integer totalErrorCount;

    /**
     * 定位时间错误数量
     */
    private Integer timeErrCount;

    /**
     * 接收时间错误数量
     */
    private Integer receiveTimeErrCount;

    /**
     * 经度错误数量
     */
    private Integer longitudeErrCount;

    /**
     * 纬度错误数量
     */
    private Integer latitudeErrCount;

    /**
     * 速度错误数量
     */
    private Integer speedErrCount;

    /**
     * 海拔错误数量
     */
    private Integer altitudeErrCount;

    /**
     * 统计周期
     */
    private Date statDate;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 删除标记：0-未删除，1-已删除
     */
    private Integer isDel;

    //拓展字段，用于记录异常的定位点
    private String errorLocationList;


}
