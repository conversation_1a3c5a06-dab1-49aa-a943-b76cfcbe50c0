package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2022/11/20 9:51 PM
 */
@Data
public class DriverMileageStatResponse {

    //企业Id
    @JsonIgnore
    @ExcelIgnore
    private Long deptId;
    //企业名称
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶里程分析", "企业名称"})
    private String deptName;
    //统计日期
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶里程分析", "日期"})
    private String statDate;
    //行驶0～100驾驶员数量
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶里程分析", "0～100公里"})
    private Integer level1Count;
    //行驶100～300驾驶员数量
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶里程分析", "101～300公里"})
    private Integer level2Count;
    //行驶300～500驾驶员数量
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶里程分析", "301～500公里"})
    private Integer level3Count;
    //行驶500～700驾驶员数量
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶里程分析", "500～700公里"})
    private Integer level4Count;
    //行驶700～1000驾驶员数量
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶里程分析", "700～1000公里"})
    private Integer level5Count;
    //行驶1000以上驾驶员数量
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶里程分析", "1000公里以上"})
    private Integer level6Count;
    //总里程
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶里程分析", "总里程（公里）"})
    private Double totalMileage;
    //总车辆数
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶里程分析", "总车辆数"})
    private Integer totalVehicles;
    //平均驾驶里程
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶里程分析", "平均里程（公里）"})
    private Double averageDriveMileage;
}

