package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 每日车辆数据大小
 */
@Data
@TableName(value = "taxicab.daily_car_mega_byte", autoResultMap = true)
public class DailyCarMegaByte implements Serializable {

    /**
     * 车牌颜色
     */
    @TableField(value = "licence_color")
    private Integer licenceColor;

    /**
     * 车牌号
     */
    @TableField(value = "licence_plate")
    private String licencePlate;

    /**
     * 日期
     */
    @TableField(value = "ymd")
    private String ymd;

    /**
     * 数据大小（单位：MB）
     */
    @TableField(value = "mega_byte")
    private double megaByte;

	//部门id
	private Long deptId;

	//车辆id
	private Integer vehicleId;

	//行业类型
	private Integer vehicleUseType;

	//车辆归属
	private Long vehicleOwnerId;

	//接入类型
	private String accessMode;
}
