package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.OnlineOfflineRecord;
import com.xh.vdm.statistic.entity.VehicleOnlineBase;

import java.util.List;

public interface IOnlineOfflineRecordService extends IService<OnlineOfflineRecord> {

	/**
	 * 查找车辆最后上线时间
	 * @param vehicleIds
	 * @return
	 */
	List<VehicleOnlineBase> findLastOnlineDate(List<Integer> vehicleIds) throws Exception;
}
