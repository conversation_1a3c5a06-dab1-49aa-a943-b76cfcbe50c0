package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.UnGoOnlineQuery;
import com.xh.vdm.statistic.entity.VehicleCountAndType;
import com.xh.vdm.statistic.service.IVehicleStatService;
import com.xh.vdm.statistic.utils.*;
import com.xh.vdm.statistic.vo.request.DetailRequest;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.GoOnline;
import com.xh.vdm.statistic.vo.response.GoOnlineRateResponse;
import com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Description: 车辆上线率相关接口
 * @Author: zhouxw
 * @Date: 2022/8/30 2:34 PM
 */
@RestController
@RequestMapping("/bt/statistics/vehicle")
@Slf4j
public class GoOnlineController {

    @Resource
    private IVehicleStatService vehicleStatService;


    @Value("${static.file.path:/statistic/files/}")
    String staticFilePath;


    @Resource
    private CacheUtil cacheUtil;


    /**
     * @description: 车辆上线率统计
     * @author: zhouxw
     * @date: 2022/9/6 8:37 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.vo.ObjectRestResponse<com.xh.vdm.statistic.vo.response.GoOnlineRateResponse>
     **/
    @PostMapping("/goOnlineRate")
    public R<GoOnlineRateResponse> goOnlineRate(@Validated @RequestBody RateRequest request){
        GoOnlineRateResponse response = new GoOnlineRateResponse();
        try{
            //1.二次校验
            if(StringUtils.isBlank(request.getMonth()) || request.getOwnerId() == null){
                return R.fail("请求参数不能为空");
            }
            //2.查询上线情况
            //对参数进行改造
            RateRequest rateRequest = new RateRequest();
            String month = request.getMonth();
            month = month + "%"; //上线时间通过 like 匹配
            rateRequest.setMonth(month);
            rateRequest.setOwnerId(request.getOwnerId());
            rateRequest.setDeptId(request.getDeptId());
            List<VehicleCountAndType> goOnlineListOri = vehicleStatService.findGoOnlineVehicleCountByType(rateRequest);
            if(goOnlineListOri == null || goOnlineListOri.size() < 1){
                //return R.fail("未查询到上线数据");
                log.info("[车辆上线率统计]未查询到上线数据");
            }
            List<VehicleCountAndType> goOnlineList = new ArrayList<>();
            //上线情况整合：对于危险品车辆，氛围三种 30、31、32，应当整合到一起，整合到30
            AtomicInteger dangerCount = new AtomicInteger(0);
            goOnlineListOri.forEach(item -> {
              if(item.getVehicleUseType() == 30 || item.getVehicleUseType() == 31 || item.getVehicleUseType() == 32){
                  dangerCount.addAndGet(item.getCount());
              }else{
                  VehicleCountAndType ct = new VehicleCountAndType();
                  ct.setVehicleUseType(item.getVehicleUseType());
                  ct.setCount(item.getCount());
                  goOnlineList.add(ct);
              }
            });
            VehicleCountAndType ct = new VehicleCountAndType();
            ct.setVehicleUseType(StatisticConstants.VEHICLE_USE_TYPE_DANGER);
            ct.setCount(dangerCount.intValue());
            goOnlineList.add(ct);

            //3.查询入网情况
            RateRequest rateRequestInNet = new RateRequest();
            rateRequestInNet.setOwnerId(rateRequest.getOwnerId());
            rateRequestInNet.setDeptId(request.getDeptId());
            String monthInNet = request.getMonth();
            String monthStr = monthInNet.substring(5,7);
            int mon = Integer.parseInt(monthStr);
            //查询入网，日期要限制到下个月1号
            mon = mon + 1;
            if(mon < 10){
                rateRequestInNet.setMonth(monthInNet.substring(0,5) + "0" + mon + "-" + "01");
            }else{
                rateRequestInNet.setMonth(monthInNet.substring(0,5) + mon + "-" + "01");
            }
            List<VehicleCountAndType> inNetListOri = vehicleStatService.findInNetVehicleCountByType(rateRequestInNet);
            if(inNetListOri == null || inNetListOri.size() < 1){
                //return R.fail("未查询到入网数据");
                log.info("[车辆上线率统计]未查询到入网数据");
            }
            List<VehicleCountAndType> inNetList = new ArrayList<>();
            //入网情况整合：对于危险品车辆，氛围三种 30、31、32，应当整合到一起
            AtomicInteger dangerInNetCount = new AtomicInteger(0);
            inNetListOri.forEach(item -> {
                if(item.getVehicleUseType() == StatisticConstants.VEHICLE_USE_TYPE_DANGER || item.getVehicleUseType() == StatisticConstants.VEHICLE_USE_TYPE_DANGER_SUB1 || item.getVehicleUseType() == StatisticConstants.VEHICLE_USE_TYPE_DANGER_SUB2){
                    dangerInNetCount.addAndGet(item.getCount());
                }else{
                    VehicleCountAndType cti = new VehicleCountAndType();
                    cti.setVehicleUseType(item.getVehicleUseType());
                    cti.setCount(item.getCount());
                    inNetList.add(cti);
                }
            });
            VehicleCountAndType cti = new VehicleCountAndType();
            cti.setVehicleUseType(StatisticConstants.VEHICLE_USE_TYPE_DANGER);
            cti.setCount(dangerInNetCount.intValue());
            inNetList.add(cti);




            List<GoOnline> goOnlines = new ArrayList<>();
            //总上线数
            int totalOnline = 0;
            //总入网数
            int totalInNet = 0;
            for(VehicleCountAndType online : goOnlineList){
                for(VehicleCountAndType inNet : inNetList){
                    if(online.getVehicleUseType() == inNet.getVehicleUseType()){
                        totalOnline += online.getCount();
                        totalInNet += inNet.getCount();
                        GoOnline go = new GoOnline();
                        go.setVehicleType(inNet.getVehicleUseType());
                        go.setGoOnlineCount(online.getCount());
                        go.setInNetCount(inNet.getCount());
                        double rate = 0;
                        if(inNet.getCount() != 0){
                            //四舍五入，保留四位小数
                            rate = (double) online.getCount() / inNet.getCount();
                        }
                        BigDecimal b = new BigDecimal(rate);
                        double onlineRate = b.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
                        go.setGoOnlineRate(onlineRate);
                        goOnlines.add(go);
                    }
                }
            }

            //组织返回数据
            if(goOnlines == null || goOnlines.size() < 1){
                //如果没有上线情况数据，则返回初始状态（包含三类数据11、12、30）
                goOnlines.add(initGoOnline(StatisticConstants.VEHICLE_USE_TYPE_REGULAR));
                goOnlines.add(initGoOnline(StatisticConstants.VEHICLE_USE_TYPE_CHARTERED));
                goOnlines.add(initGoOnline(StatisticConstants.VEHICLE_USE_TYPE_DANGER));
            }else{
                //如果包含上线数据，则补充不全的部分
                List<Integer> typeList = new ArrayList<>();
                for(GoOnline go : goOnlines){
                    typeList.add(go.getVehicleType());
                }
                if(!typeList.contains(StatisticConstants.VEHICLE_USE_TYPE_REGULAR)){
                    goOnlines.add(initGoOnline(StatisticConstants.VEHICLE_USE_TYPE_REGULAR));
                }
                if(!typeList.contains(StatisticConstants.VEHICLE_USE_TYPE_CHARTERED)){
                    goOnlines.add(initGoOnline(StatisticConstants.VEHICLE_USE_TYPE_CHARTERED));
                }
                if(!typeList.contains(StatisticConstants.VEHICLE_USE_TYPE_DANGER)){
                    goOnlines.add(initGoOnline(StatisticConstants.VEHICLE_USE_TYPE_DANGER));
                }
            }


            if(totalInNet < 1){
                //return R.fail("未查询到车辆入网信息");
                log.info("[车辆上线率统计]未查询东车辆入网信息");
            }
            if(totalOnline < 1){
                //return R.fail("未查询到车辆上线信息");
                log.info("[车辆上线率统计]未查询到车辆上线信息");
            }

            double onlineRate = 0;
            if(totalInNet != 0){
                onlineRate = MathUtil.divideRoundDouble(totalOnline , totalInNet , 4);
            }

            //按照服务商标准计算得分
            double score = onlineRate * 10;
            response.setOnline(goOnlines);
            response.setScore(MathUtil.roundDouble(score , 4));
        }catch (Exception e){
            e.printStackTrace();
            log.error("车辆上线率统计失败",e);
            return R.fail("车辆上线率统计失败" );
        }

        return R.data(response);
    }

    /**
     * @description: 初始化返回参数
     * @author: zhouxw
     * @date: 2022/10/14 9:34 AM
     * @param: []
     * @return: com.xh.vdm.statistic.vo.response.GoOnline
     **/
    private GoOnline initGoOnline(int type){
        GoOnline go = new GoOnline();
        go.setVehicleType(type);
        go.setGoOnlineRate(0);
        go.setGoOnlineCount(0);
        go.setInNetCount(0);
        return go;
    }


    /**
     * @description: 未上线车辆明细查询
     * @author: zhouxw
     * @date: 2022/9/6 11:27 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse>>
     **/
    @PostMapping("/unSendPositionPoint")
    public R<IPage<UnGoOnlineVehicleResponse>> unGoOnlineVehicle (@RequestBody @Validated DetailRequest request){

        //前端下标从 0 开始，此处要加1
        request.setStart(request.getStart()+1);

        IPage<UnGoOnlineVehicleResponse> pageList = null;
        try{
            //1.拼装参数
            UnGoOnlineQuery query = new UnGoOnlineQuery();
            if(request.getDeptId() != null && StringUtils.isNotBlank(request.getDeptId()+"")){
                query.setDeptId(request.getDeptId());
            }
            query.setMonthLike(request.getMonth()+"%");
            int mon = Integer.parseInt(request.getMonth().replace("-","").substring(4,6));
            //查询入网，日期要限制到下个月1号
            mon = mon + 1;
            if(mon < 10){
                query.setFirstDayNextMonth(request.getMonth().substring(0,5) + "0" + mon + "-" + "01");
            }else{
                query.setFirstDayNextMonth(request.getMonth().substring(0,5) + mon + "-" + "01");
            }
            query.setOwnerId(request.getOwnerId());
            query.setLicencePlate(request.getLicencePlate());
            query.setPlateColor(request.getPlateColor());
            query.setMonth(request.getMonth());
            if(request.getCount() < 1){
                //如果不传递页面size，那么就默认设置为10
                request.setCount(10);
            }
            if(request.getStart() < 1){
                request.setStart(1);
            }
            query.setCurrent(request.getStart()/request.getCount()+(request.getStart() % request.getCount()>0?1:0));
            query.setSize(request.getCount());

            //2.查询未上线车辆明细
            pageList = vehicleStatService.findUnGoOnlineVehicle(query);

            //3.颜色名值转换
            Map<String , String> colorMap = cacheUtil.getVehicleColorMap();
            pageList.getRecords().forEach(item ->{
                item.setPlateColor(colorMap.get(item.getPlateColor()));
            });

        }catch (Exception e){
            e.printStackTrace();
            log.error("查询未上线车辆明细失败",e);
            return R.fail("查询未上线车辆明细失败：" + e.getMessage());
        }
        return R.data(pageList);
    }


    /**
     * @description: 未上线车辆明细导出
     * @author: zhouxw
     * @date: 2022/9/6 11:27 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse>>
     **/
    @PostMapping("/unSendPositionPointExport")
    public R<String> unSendPositionPointExport (@RequestBody @Validated DetailRequest request){
        try{

            //1.拼装参数
            UnGoOnlineQuery query = new UnGoOnlineQuery();
            if(request.getDeptId() != null && StringUtils.isNotBlank(request.getDeptId()+"")) {
                query.setDeptId(request.getDeptId());
            }
            query.setMonthLike(request.getMonth()+"%");
            int mon = Integer.parseInt(request.getMonth().replace("-","").substring(4,6));
            //查询入网，日期要限制到下个月1号
            mon = mon + 1;
            if(mon < 10){
                query.setFirstDayNextMonth(request.getMonth().substring(0,5) + "0" + mon + "-" + "01");
            }else{
                query.setFirstDayNextMonth(request.getMonth().substring(0,5) + mon + "-" + "01");
            }
            query.setOwnerId(request.getOwnerId());
            query.setLicencePlate(request.getLicencePlate());
            query.setPlateColor(request.getPlateColor());
            query.setMonth(request.getMonth());

            //2.查询未上线车辆明细
            List<UnGoOnlineVehicleResponse> list = vehicleStatService.findUnGoOnlineVehicleAll(query);

            //3.颜色名值转换
            Map<String , String> colorMap = cacheUtil.getVehicleColorMap();
            list.forEach(item ->{
                item.setPlateColor(colorMap.get(item.getPlateColor()));
            });

            //4.执行数据导出
            String title = "未上线车辆明细";
            String[] arrs = {"企业名称","车牌号","车辆颜色","SIM卡号","终端ID","统计月份","终端型号"};
            String fileName = ExcelExport.exportExcelFile(title,list,arrs,staticFilePath);
            return R.data(fileName);
        }catch (Exception e){
            e.printStackTrace();
            log.error("导出未上线车辆明细失败",e);
            return R.fail("导出未上线车辆明细失败");
        }
    }




}
