package com.xh.vdm.statistic.task;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.service.IStatDriveStatusService;
import com.xh.vdm.statistic.service.IStatStopPointService;
import com.xh.vdm.statistic.service.IStatVehTravelDurationService;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.LogUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 融合分析相关统计任务
 * @Author: zhouxw
 * @Date: 2022/11/7 2:32 PM
 */
@Component
@Slf4j
public class FuseTask {


    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

    @Autowired
    private IStatVehTravelDurationService service;

    @Resource
    private IStatDriveStatusService driveStatusService;

    @Resource
    private IStatStopPointService stopPointService;

    @Resource
    private LogUtil logUtil;


    /**
     * @description: 车辆行驶时长 定时任务
     * 统计前一天的定位数据，将每辆车的行驶时长记录到表 stat_veh_travel_duration_#{month} 中
     * 定位数据允许有8个小时的补传，所以，数据的处理应当在当天的8点之后
     * 每天 10 点执行
     * @author: zhouxw
     * @date: 2022/9/1 9:36 AM
     * @param: []
     * @return: void
     **/
    //@Scheduled(cron="0 0 10 * * ?")
    public void vehicleTravelDurationStat(){

        //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
        Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION);
        if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
            //如果已经有跑批程序在执行，则不再执行
            log.info("[车辆行驶时长统计]跑批任务已经在执行，请等当前执行程序完成后再试");
            return ;
        }

        //获取执行权限之后，添加执行标记（有效期为1个小时）
        synchronized (this){
            stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
        }

        //1. 统计前一天的定位数据的车辆行驶时长
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_YEAR , -1);
        String day = sdf.format(cal.getTime());
        long start = 0;
        long end = 0;
        boolean success = false;
        try {
            start = System.currentTimeMillis();
            success = service.vehTravelDurationStat(day);
            end = System.currentTimeMillis();
            if(success){
                //如果定时任务执行成功
                log.info("[车辆行驶时长统计-{}]定时任务 执行完成，共耗时{}毫秒" , day , end - start);
                logUtil.insertStatLog(StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION,"车辆行驶时长统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
            }else{
                log.error("[车辆行驶时长统计-{}]定时任务 执行失败" , day );
                logUtil.insertStatLog(StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION,"车辆行驶时长统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败");
            }
        } catch (Exception e) {
            log.error("[车辆行驶时长统计-{}]定时任务 执行失败 ，发生异常" , day , e);
            e.printStackTrace();
            logUtil.insertStatLog(StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION,"车辆行驶时长统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行失败，发生异常："+e.getMessage());
        }

        //执行完毕后，解除执行锁定
        synchronized (this){
            stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION );
        }


    }

    /**
     * @description: 驾驶员驾驶时长和驾驶里程统计任务(包含驾驶时段统计)
     * 定位数据允许有8个小时的补传，所以，数据的处理应当在当天的8点之后
     * 每天 10:30 执行
     * @author: zhouxw
     * @date: 2022/11/17 12:08 PM
     * @param: []
     * @return: void
     **/
    //@Scheduled(cron="0 30 10 * * ?")
    public void driveDurationAndMileageStat(){

        //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
        Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DRIVER_DRIVE_DURATION);
        if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
            //如果已经有跑批程序在执行，则不再执行
            log.info("[驾驶员驾驶时长统计]跑批任务已经在执行，请等当前执行程序完成后再试");
            return ;
        }

        //获取执行权限之后，添加执行标记（有效期为1个小时）
        synchronized (this){
            stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DRIVER_DRIVE_DURATION , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
        }

        //统计前一天的驾驶员驾驶时长
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_YEAR , -1);
        String day = sdf.format(cal.getTime());
        long start = 0;
        long end = 0;
        try {
            start = System.currentTimeMillis();
            driveStatusService.statDriverStatus(day);
            end = System.currentTimeMillis();
            //如果定时任务执行成功
            log.info("[驾驶员驾驶时长统计-{}]定时任务 执行完成，共耗时{}毫秒" , day , end - start);
            logUtil.insertStatLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION,"驾驶员驾驶时长统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
        } catch (Exception e) {
            log.error("[驾驶员驾驶时长统计-{}]定时任务 执行失败 ，发生异常" , day , e);
            e.printStackTrace();
            logUtil.insertStatLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION,"驾驶员驾驶时长统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行失败，发生异常："+e.getMessage());
        }

        //执行完毕后，解除执行锁定
        synchronized (this){
            stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION );
        }
    }



    /**
     * @description: 驾驶员驾驶时长和驾驶里程统计任务(包含驾驶时段统计)
     * 统计当日数据，每个小时的 50 分执行一次
     * 每天 10:30 执行
     * @author: zhouxw
     * @date: 2022/11/17 12:08 PM
     * @param: []
     * @return: void
     **/
    //@Scheduled(cron="0 50 * * * ?")
    public void driveDurationAndMileageStatThisDay(){

        //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
        Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DRIVER_DRIVE_DURATION);
        if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
            //如果已经有跑批程序在执行，则不再执行
            log.info("[驾驶员驾驶时长统计]跑批任务已经在执行，请等当前执行程序完成后再试");
            return ;
        }

        //获取执行权限之后，添加执行标记（有效期为1个小时）
        synchronized (this){
            stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DRIVER_DRIVE_DURATION , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
        }

        //统计当天的驾驶员驾驶时长
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        String day = sdf.format(cal.getTime());
        long start = 0;
        long end = 0;
        try {
            start = System.currentTimeMillis();
            driveStatusService.statDriverStatus(day);
            end = System.currentTimeMillis();
            //如果定时任务执行成功
            log.info("[驾驶员驾驶时长统计-{}]定时任务 执行完成，共耗时{}毫秒" , day , end - start);
            logUtil.insertStatLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION,"驾驶员驾驶时长统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
        } catch (Exception e) {
            log.error("[驾驶员驾驶时长统计-{}]定时任务 执行失败 ，发生异常" , day , e);
            e.printStackTrace();
            logUtil.insertStatLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION,"驾驶员驾驶时长统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行失败，发生异常："+e.getMessage());
        }

        //执行完毕后，解除执行锁定
        synchronized (this){
            stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DRIVER_DRIVE_DURATION );
        }
    }


    /**
     * @description: 统计车辆停止点信息(暂不使用，已经迁移到了spark跑批)
     * 每次从上一个停止点开始计算，截止到当前时刻为止，查询轨迹表，如果存在停止点，则将停止点记录到停止点表中
     * 停止点：10分钟之内的点位经纬度没有变化
     * 实时执行，每10分钟执行一次
     * 用作辅助判断驾驶员极端情况下的驾驶时段
     * @author: zhouxw
     * @date: 2022/12/22 9:28 AM
     * @param: []
     * @return: void
     **/
    //@Scheduled(cron="0 0/10 * * * ?")
	//@XxlJob("statStopPoint")
    public void statStopPoint(String date){

		XxlJobHelper.log("将要开始执行定时任务：statStopPoint");

        //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
        Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_STOP_POINT);
        if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
            //如果已经有跑批程序在执行，则不再执行
            log.info("[车辆停止点统计]跑批任务已经在执行，请等当前执行程序完成后再试");
            return ;
        }

        //获取执行权限之后，添加执行标记（有效期为10分钟）
        synchronized (this){
            stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_STOP_POINT , StatisticConstants.COMMON_TRUE , 10 , TimeUnit.MINUTES);
        }

        //统计当前时间的停止点信息
        String day = sdf.format(new Date());
		if(!StringUtils.isEmpty(date)){
			day = date;
		}
        long start = 0;
        long end = 0;
        try {
            start = System.currentTimeMillis();
            stopPointService.statStopPoint(day);
            end = System.currentTimeMillis();
            //如果定时任务执行成功
            log.info("[车辆停止点统计-{}]定时任务 执行完成，共耗时{}毫秒" , day , end - start);
            logUtil.insertStatLog(StatisticConstants.TASK_STOP_POINT,"车辆停止点统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
        } catch (Exception e) {
            log.error("[车辆停止点统计-{}]定时任务 执行失败 ，发生异常" , day , e);
            e.printStackTrace();
            logUtil.insertStatLog(StatisticConstants.TASK_STOP_POINT,"车辆停止点统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行失败，发生异常："+e.getMessage()==null?e.getMessage().substring(0,2000):"");
        }

        //执行完毕后，解除执行锁定
        synchronized (this){
            stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_STOP_POINT );
        }

		XxlJobHelper.log("执行定时任务结束：statStopPoint");
    }

}
