package com.xh.vdm.statistic.entity.report;

import lombok.Data;

/**
 * 报警信息
 */
@Data
public class AlarmNode {

	//企业名称
	private String deptName;
	//车牌号
	private String licencePlate;
	//车牌颜色
	private String licenceColor;
	//车牌颜色名称
	private String licenceColorDesc;
	//超速报警次数
	private Long overSpeedAlarmCount;
	//疲劳报警次数
	private Long fatigueAlarmCount;
	//夜间禁行报警次数
	private Long nightAlarmCount;
	//其他报警次数
	private Long otherAlarmCount;
	//总报警次数
	private Long totalAlarmCount;
}
