package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatEmailHistory implements Serializable {

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonIgnore
    private Long id;

    /**
     * 发送类型：1 日报   2 周报
     */
    private int type;


    //邮件发送类型：1 定时自动发送  2 手动调用接口发送  3 手动页面发送
    private int sendType;

    /**
     * 用户id
     */
    @JsonIgnore
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    //部门id
    @JsonIgnore
    private Long deptId;

    //部门名称
    private String deptName;

    /**
     * 接收邮件的邮箱地址
     */
    private String receiveMailAddress;

    /**
     * 邮件中统计的数据的日期
     */
    private String dataDate;

    /**
     * 邮件发送结果： 1 成功  2 失败
     */
    private int sendResult;

    /**
     * 邮件发送结果描述
     */
    private String sendResultDesc;

    /**
     * 邮件发送日期
     */
    private Date sendTime;

    /**
     * 备注
     */
    private String note;


}
