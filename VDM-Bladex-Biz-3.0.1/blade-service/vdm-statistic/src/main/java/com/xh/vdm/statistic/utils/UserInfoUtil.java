package com.xh.vdm.statistic.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.xh.vdm.statistic.constant.RedisConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 获取用户基础信息
 * <AUTHOR>
 * @date Created in 13:37 2021/4/26
 * @description
 */
@Slf4j
@Component
public class UserInfoUtil {

    @Autowired
    protected RedisTemplate<String, String> redisTemplate;

    /**
     * 任意请求方法的uri的key值
     */
    public static final String ANY_METHOD_URI_LIST_KEY = "";

    /**
     * 根据token获取基础用户信息
     * <AUTHOR>
     * @date 2021-04-26 13:51:25
     * @param token
     * @return
     **/
    public JSONObject acquireUserBaseInfoByToken(String token){
        String key = RedisConstants.StringKey.LOGINED_USER_CACHE_PREFIX + token;
        String valueString = redisTemplate.opsForValue().get(key);
        if(null != valueString){
            JSONObject user = JSON.parseObject(valueString);
            return user;
        }
        return null;
    }

    /**
     * 获取请求中的token
     * 优先查找cookie，没有再查找header
     * <AUTHOR>
     * @date 2021-04-26 15:23:03
     * @param request
     * @return
     **/
    public String acquireToken(HttpServletRequest request) throws Exception {
        //先查cookie，没有再查header
//        String token = null;
//        Cookie[] cookies = request.getCookies();
//        if(null != cookies){
//            cookieFor: for(Cookie cookie : cookies){
//                if (CookieConstants.USER_TOKEN.equals(cookie.getName())){
//                    token = cookie.getValue();
//                    break cookieFor;
//                }
//            }
//        }
//        if(null == token){
//            token = request.getHeader(HeaderConstants.USER_TOKEN);
//        }
        String token = request.getHeader("Authorization");
        if(token==null||"".equals(token)){
            throw new Exception("令牌为空");
        }
        token = token.replace("Bearer ","");
        return token;
    }

    public JSONObject getUser(HttpServletRequest request) throws Exception {
        String token = acquireToken(request);
        if(token!=null){
            return acquireUserBaseInfoByToken(token);
        }else{
            return null;
        }
    }

    public String getUserName(HttpServletRequest request) throws Exception {
        String token = acquireToken(request);
//        String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJJZCI6MSwiQXZhdGFyIjoiaHR0cHM6Ly9nb2FwaS55aXhpYW5nLmNvL3N0YXRpYy91cGxvYWQvMS5qcGciLCJFbWFpbCI6InlzaG9wQHFxLmNvbSIsIlVzZXJuYW1lIjoiYWRtaW4iLCJQaG9uZSI6IjE4ODg4ODg4ODg4IiwiTmlja05hbWUiOiLnrqHnkIblkZgiLCJTZXgiOiLnlLciLCJEZXB0Ijoi56CU5Y-R6YOoIiwiSm9iIjoi5YWo5qCI5byA5Y-RMiIsImV4cCI6MTYzNzQ2MjAwOSwianRpIjoiMSIsImlzcyI6IllzaG9wR28ifQ.-upG9RJRebu_fLAi0qbrhJ8OEz_DlfKlXA581KtOUPk";
        if(token!=null){
            JSONObject user = acquireUserBaseInfoByToken(token);
            if(user!=null){
                String userName = user.getString("username");
                if(userName!=null){
                    return userName;
                }else{
                    return "";
                }
            }else{
                throw new Exception("用户信息不存在");
            }
        }else{
            throw new Exception("令牌为空");
        }
    }
}
