package com.xh.vdm.statistic.service;

import com.xh.vdm.statistic.entity.StatNotLocation;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-25
 */
public interface IStatNotLocationService extends IService<StatNotLocation> {

	/**
	 * 查询不定位车辆数，指定日期
	 * @param deptIds
	 * @param userId
	 * @param date yyyy-MM-dd
	 * @return
	 * @throws Exception
	 */
	long findNotLocationCount(List<Long> deptIds, String date) throws Exception;

	double findNotLocationMileage(List<Long> deptIds, String date) throws Exception;
}
