package com.xh.vdm.statistic.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.bd.entity.VehicleStopPointBase;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.DateListAndMonth;
import com.xh.vdm.statistic.entity.Point;
import com.xh.vdm.statistic.entity.StatStopPoint;
import com.xh.vdm.statistic.mapper.StatStopPointMapper;
import com.xh.vdm.statistic.service.ILocationService;
import com.xh.vdm.statistic.service.IStatStopPointService;
import com.xh.vdm.statistic.service.IStatTaskLogService;
import com.xh.vdm.statistic.utils.DataUtils;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.MapDistanceUtil;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.response.StopPointDetailResponse;
import com.xh.vdm.statistic.vo.response.StopPointStatResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 车辆停止点表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-22
 */
@Slf4j
@Service
public class StatStopPointServiceImpl extends ServiceImpl<StatStopPointMapper, StatStopPoint> implements IStatStopPointService {

	@Autowired
	MapDistanceUtil mapDistanceUtil;

    private ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> {return new SimpleDateFormat("yyyyMMdd");}) ;


	public final ThreadPoolExecutor thread_pool = new ThreadPoolExecutor(10,20,300, TimeUnit.SECONDS,new LinkedBlockingQueue<>());

    @Resource
    private IStatTaskLogService taskLogService;

    @Resource
    private DataUtils dataUtils;

    @Resource
    private ILocationService locationService;

    //车辆停止时间阈值（超过该值，表示车辆停止）
    public static final long DEFAULT_STOP_TIME = 10 * 60;

    /**
     * @description: 计算停止点（对于物理位置来讲，是停止点，停靠点，侧重经纬度，地点；对于时间来讲，是停靠时段。这里取名停止点）
     * 停止点的几种场景：
     * （1）某辆车第一个定位点之前的时间段（暂不考虑，没有添加到停止点列表中）
     * （2）某辆车最后一个定位点之后的时间段（暂不考虑，没有添加到停止点列表中）
     * （3）经纬度相同保持时间超过10分钟的时间段
     * （4）经纬度不同，但间隔时间超过10分钟的时间段
     * @author: zhouxw
     * @date: 2022/12/23 11:14 AM
     * @param: [day]
     * @return: void
     **/
    @Override
    public void statStopPoint(String day) throws Exception {
        long startTotal = System.currentTimeMillis();
        try{
            //1.校验给定的日期格式
            Date statDate = null;
            try {
                statDate = sdfHolder.get().parse(day);
            } catch (ParseException e) {
                log.error("[停止点计算]执行出现异常，日期 " + day + " 格式错误 "  , e);
                throw new Exception("日期格式错误：" + day);
            }

            log.info("[停止点计算] 日期格式校验完成：日期格式正确");
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[停止点计算]校验日期格式完成");

            //2.判断月表是否存在，如果不存在则创建
            dataUtils.checkTableExistAndCreate(StatisticConstants.STOP_POINT_TEMPLATE_TABLE + "_"+day.substring(0 , 6) , StatisticConstants.STOP_POINT_TEMPLATE_TABLE);
            //创建索引
            dataUtils.createIndex(StatisticConstants.STOP_POINT_TEMPLATE_TABLE + "_"+day.substring(0 , 6) , StatisticConstants.LICENCE_PLATE);
            log.info("[停止点计算] 月表判断完成");
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[停止点计算]月表校验完成");

            long start1 = System.currentTimeMillis();
            //3.根据指定的日期获取车辆列表
            List<VehicleStopPointBase> licencePlatesList = locationService.findStopPointVehicleListByDay(day);
            log.info("[停止点计算] 车辆列表获取完成，共有 {} 辆车待处理",licencePlatesList==null?0:licencePlatesList.size());
            long end1 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_DRIFT , "[停止点计算]获取车辆列表完成，共 "+(licencePlatesList==null?"0":licencePlatesList.size())+" 辆车共耗时："+(end1 - start1)+"ms");

            long start2 = System.currentTimeMillis();
            //4.对每辆车的停止点进行计算
            CountDownLatch countDownLatch = new CountDownLatch(licencePlatesList.size());
            String dateNow = sdfHolder.get().format(new Date());
            List<StatStopPoint> stopListAll = new ArrayList<>();
            stopListAll = Collections.synchronizedList(stopListAll);
            for(VehicleStopPointBase vehicle : licencePlatesList){
                Date finalStatDate = statDate;
                List<StatStopPoint> finalStopListAll = stopListAll;
                thread_pool.submit(() -> {
                    try{
						String licencePlate = vehicle.getLicencePlate();
						int licenceColor = vehicle.getLicenceColor().intValue();
                        //4.1 查询上一个停止点，获取停止点定位点id
						String dayQ = day.substring(0,4)+"-"+day.substring(4,6) + "-" + day.substring(6,8);
						StatStopPoint latestStopPoint = findLatestStopPoint(licencePlate, licenceColor+"", dayQ);
                        //4.2 获取计算停止点的定位点
                        //判断要计算的是否是当前天，如果不是当前天，则查询该停止点到最后的点位；如果是当前天，则查询该停止点到当前时刻所有的定位点
                        List<LocationKudu> list = null;
                        if(dateNow.equals(day)){
                            //如果要计算的是当前天
                            if(latestStopPoint == null ){
                                //如果还没有停止点，则查询所有定位点
                                list = locationService.findUploadLocationPointListByDay(day , licencePlate, licenceColor);
                            }else{
                                //如果存在停止点，则查询上一个停止定位点到现在的定位点列表
                                long stopEndTime = latestStopPoint.getStopEndTime();
								list = locationService.findLocationByCondition(licencePlate, licenceColor, stopEndTime , new Date().getTime() / 1000);
                            }
                        }else{
                            //如果要计算的不是当前天
                            if(latestStopPoint == null ){
                                //如果还没有停止点，则查询所有定位点
                                //list = locationService.findUploadLocationPointListByDay(day , licencePlate);
								list = locationService.findUploadLocationPointListByDay(day, licencePlate, licenceColor);
                            }else{
                                //如果存在停止点，则查询上一个停止定位点到最后的定位点列表
                                long stopEndTime = latestStopPoint.getStopEndTime();
                                long endTime = DateUtil.getDayLastSecondTimestamp(sdfHolder.get().parse(day).getTime()/1000);
                                //list = locationService.findUploadLocationPointListByDayAndDuration(day , licencePlate , stopEndTime , endTime);
								list = locationService.findLocationByCondition(licencePlate, licenceColor, stopEndTime, endTime);
                            }
                        }
                        if(list == null || list.size() < 1){
                            return ;
                        }
                        LocationKudu stopStart = null;
                        LocationKudu stopEnd = null;
                        //存放停止点
                        List<StatStopPoint> stopList = new ArrayList<>();
                        int index = 0;
                        //4.3 根据轨迹点计算停止点
                        for(int i = 1 ; i < list.size(); i++){
                            LocationKudu pre = list.get(i - 1);
                            LocationKudu now = list.get(i);
//							if(pre.getLicencePlate().equals("新QD9M20")){
//								System.out.println("this is ok");
//							}
                            //如果两个轨迹点经纬度相同，则开始记录停止点
                            if(pre.getLongitude().doubleValue() == now.getLongitude().doubleValue() && pre.getLatitude().doubleValue() == now.getLatitude().doubleValue()){
                                if(stopStart == null){
                                    //开始记录
                                    stopStart = pre;
                                    index = i;
                                }
                                //如果到最后一个定位点，则比较该点，是否能构成停止点
                                if(i == list.size() - 1){
                                    stopEnd = now;

                                    //计算停止时间
                                    long timestamp = stopEnd.getTime() - stopStart.getTime();
                                    if(timestamp > DEFAULT_STOP_TIME){
                                        //找到停止点
                                        StatStopPoint stopPoint = createNewStatStopPoint(stopStart , stopEnd, i - index + 1);
                                        stopList.add(stopPoint);
                                        stopStart = null;
                                        stopEnd = null;
                                    }else{
                                        stopStart = null;
                                    }
                                }
                            }else{

                                if(stopStart != null){
                                    //停车后，连续打点（连续相同经纬度的点）
                                    //如果两个轨迹点经纬度不相同，则停止记录
                                    stopEnd = now;

                                    //计算停止时间
                                    long timestamp = stopEnd.getTime() - stopStart.getTime();
                                    if(timestamp > DEFAULT_STOP_TIME){
                                        //找到停止点
                                        int locationCount = i - index + 1;
                                        StatStopPoint stopPoint = createNewStatStopPoint(stopStart , stopEnd, locationCount);
                                        stopList.add(stopPoint);

                                        stopStart = null;
                                        stopEnd = null;
                                    }else{
                                        stopStart = null;
                                    }
                                }else{
                                    //没有连续相同经纬度的点
                                    //判断两个点之间的时间间隔是否超过10分钟，如果超过，也表示停车了
                                    long timestamp = now.getTime() - pre.getTime();
                                    if(timestamp > DEFAULT_STOP_TIME){
                                        //找到停止点
                                        StatStopPoint stopPoint = createNewStatStopPoint(pre , now, 1);
                                        stopList.add(stopPoint);

                                        stopStart = null;
                                        stopEnd = null;
                                    }else{
                                        stopStart = null;
                                    }
                                }
                            }

                        }


                        //判断数据库中存储的最后一个停止点与当前批次的第一个停止点是否是同一个
                        //判断该停止点与上一个停止点是否经纬度相同，如果相同，表示是同一个停止段
                        if(latestStopPoint != null  && stopList != null && stopList.size() > 0){
                            StatStopPoint lastStopPoint = latestStopPoint;
                            if(lastStopPoint.getStopLongitude().doubleValue() == stopList.get(0).getStopLongitude().doubleValue()&& lastStopPoint.getStopLatitude().doubleValue() == stopList.get(0).getStopLatitude().doubleValue()){
                                //必须是连续的，经纬度相同的轨迹点才会进行断点判断（在同一个位置停车；有可能存在车去了别的地方再返回的情况，去别的地方的定位点少于10分钟或者在正常跑，不会计入到stopList中）
                                if(list.get(0).getLongitude().doubleValue() == lastStopPoint.getStopLongitude().doubleValue() && list.get(0).getLatitude().doubleValue() == lastStopPoint.getStopLatitude().doubleValue()){
                                    //如果是同一个停止点，更改停止点信息
                                    StatStopPoint sp = stopList.get(0);
                                    lastStopPoint.setStopEndId(sp.getStopEndId());
                                    lastStopPoint.setStopEndTime(sp.getStopEndTime());
                                    lastStopPoint.setStopDuration(lastStopPoint.getStopEndTime() - lastStopPoint.getStopStartTime());
                                    lastStopPoint.setUpdateTime(new Date());
                                    lastStopPoint.setStopLocationCount(sp.getStopLocationCount()+ lastStopPoint.getStopLocationCount());

                                    BeanUtils.copyProperties(lastStopPoint , sp);
                                }
                            }
                        }

						//向停止点中添加字段
						stopList.forEach(item -> {
							item.setVehicleId(vehicle.getVehicleId());
							item.setAccessMode(vehicle.getAccessMode());
							item.setDeptId(vehicle.getDeptId());
							item.setVehicleUseType(vehicle.getVehicleUseType());
							item.setVehicleOwnerId(vehicle.getVehicleOwnerId());
						});

                        finalStopListAll.addAll(stopList);

                    }catch (Exception e){
                        e.printStackTrace();
                    }finally {
                        countDownLatch.countDown();
                    }
                });
            }

            countDownLatch.await();
            log.info("[停止点计算] 停止点统计完成[数据待入库]");
            long end2 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_STOP_POINT , "[停止点计算]停止点统计完成，共耗时："+(end2 - start2)+"ms");

            if(stopListAll == null || stopListAll.size() < 1){
                return ;
            }

            long start6 = System.currentTimeMillis();

			//调用地图服务，查询停止点地址
			//记录调用了地图服务的数据的下标
			List<Integer> indexList = new ArrayList<>();
			List<Point> pointList = new ArrayList<>();
			for(int i = 0; i < stopListAll.size(); i++){
				StatStopPoint stopPoint = stopListAll.get(i);
				Double longitude = stopPoint.getStopLongitude();
				Double latitude = stopPoint.getStopLatitude();
				if(StringUtils.isEmpty(stopPoint.getStopAddress()) && longitude != null && longitude.doubleValue() != 0
					&& latitude != null && latitude.doubleValue() != 0){
					indexList.add(i);
					pointList.add(new Point(longitude, latitude));
				}
			}
			List<String> addressList = mapDistanceUtil.getAddressContainNull(pointList);
			for(int  i = 0 ; i < indexList.size(); i++){
				stopListAll.get(indexList.get(i)).setStopAddress(addressList.get(i));
			}

            //5.对计算的停止点执行新增/更新操作
            saveOrUpdate(stopListAll , day.substring(0,6));

            long end6 = System.currentTimeMillis();
            taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_STOP_POINT , "[停止点计算]停止点入库完成，共耗时："+(end6 - start6)+"ms");
        }catch (Exception e){
            e.printStackTrace();
            log.error("[停止点计算]执行数据的录入失败");
            throw e;
        }
        log.info("[停止点计算] 停止点统计整体完成");
        long endTotal = System.currentTimeMillis();
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_STOP_POINT , "[停止点计算]漂移率统计完成，共耗时："+(endTotal - startTotal)+"ms");
    }



    /**
     * @description: 创建新的停止点
     * @author: zhouxw
     * @date: 2022/12/22 4:13 PM
     * @param: [stopStart, stopEnd]
     * @return: com.xh.vdm.statistic.entity.StatStopPoint
     **/
    private StatStopPoint createNewStatStopPoint(LocationKudu stopStart , LocationKudu stopEnd, int locationCount) {
        StatStopPoint stopPoint = new StatStopPoint();
//        stopPoint.setLicencePlate(stopStart.getLicencePlate());
//		stopPoint.setLicenceColor(stopStart.getLicenceColor()+"");
        Date date = new Date();
        date.setTime(stopStart.getTime()*1000);
        stopPoint.setStatDate(date);
        stopPoint.setStopStartTime(stopStart.getTime());
        stopPoint.setStopEndTime(stopEnd.getTime());
        stopPoint.setStopDuration(stopEnd.getTime() - stopStart.getTime());
        stopPoint.setStopLocationCount(locationCount);
        stopPoint.setStopStartId(stopStart.getId());
        stopPoint.setStopEndId(stopEnd.getId());
        stopPoint.setStopLongitude(stopStart.getLongitude());
        stopPoint.setStopLatitude(stopStart.getLatitude());
        stopPoint.setCreateTime(new Date());
        return stopPoint;
    }


    @Override
    public void saveOrUpdate(List<StatStopPoint> list,String month) throws Exception {
        baseMapper.saveOrUpdate(list,month);
    }

    @Override
    public void delete(String statDate) {
        String month = statDate.substring(0,4)+statDate.substring(5,7);
        baseMapper.delete(statDate , month);
    }

    @Override
    public List<StatStopPoint> findStopPointByCondition(String licenceCode, String date, long startSecondTimestamp, long endSecondTimestamp) throws Exception {
		String month = date.substring(0,7).replace("-","");
		List<StatStopPoint> list = baseMapper.getStopPointListByCondition(licenceCode, date, month, startSecondTimestamp,endSecondTimestamp);
        return list;
    }

	@Override
	public StatStopPoint findLatestStopPoint(String licencePlate, String licenceColor, String statDate) throws Exception {
		String month = statDate.substring(0,4) + statDate.substring(5,7);
		return baseMapper.getLatestStopPoint(licencePlate, licenceColor, month, statDate);
	}

	@Override
	public IPage<StopPointStatResponse> statStopInfo(CommonBaseRequest request, List<DateListAndMonth> dm, IPage<StopPointStatResponse> page) throws Exception {
		return baseMapper.getStopPointInfoPage(request, dm, page);
	}

	@Override
	public IPage<StopPointDetailResponse> findStopPointDetailInfo(CommonBaseRequest request, List<DateListAndMonth> dm, IPage<StopPointDetailResponse> page) throws Exception {
		return baseMapper.getStopPointDetailPage(request, dm, page);
	}
}
