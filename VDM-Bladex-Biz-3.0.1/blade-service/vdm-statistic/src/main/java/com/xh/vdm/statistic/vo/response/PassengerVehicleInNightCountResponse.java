package com.xh.vdm.statistic.vo.response;

import lombok.Data;

/**
 * @Description: 客运车辆夜间行驶情况 对象
 * @Author: zhouxw
 * @Date: 2022/11/10 2:57 PM
 */
@Data
public class PassengerVehicleInNightCountResponse {
    //统计日期
    private String statDate;
    //客运车辆上线数
    private int passengerVehGoOnlineCount;
    //客运车辆夜间行驶数
    private int passengerVehInNightCount;
    //客运车辆夜间行驶率
    private double passengerVehInNightRate;
    //客运车辆夜间行驶率百分数表示
    private String passengerVehInNightRatePercentStr;
}
