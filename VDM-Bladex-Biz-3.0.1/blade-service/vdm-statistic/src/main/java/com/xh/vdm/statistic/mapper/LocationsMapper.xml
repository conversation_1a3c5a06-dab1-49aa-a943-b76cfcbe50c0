<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.LocationsMapper">

    <sql id="ids">
        <if test="ids != null  and ids.size() > 0">
            AND CONCAT(CAST(target_type AS STRING), '_', CAST(target_id AS STRING)) IN
            <foreach collection="ids" item="target" separator="," open="(" close=")">
                #{target}
            </foreach>
        </if>
    </sql>
    <sql id="allResponses">
        <if test="allResponses != null  and allResponses.size() > 0">
            AND (
            <foreach collection="allResponses" item="target" separator=" OR " open="(" close=")">
                target_type = #{target.targetType} AND target_id = #{target.id}
            </foreach>
            )
        </if>
    </sql>

    <select id="queryByPage" resultType="com.xh.vdm.statistic.vo.response.LocationKuduResponse">
        select * from locations
        <where>
            <if test="locations.targetId != null">
                and target_id = #{locations.targetId}
            </if>
            <if test="locations.targetType != null">
                and target_type = #{locations.targetType}
            </if>
            <if test="locations.deviceId != null">
                and device_id = #{locations.deviceId}
            </if>
            <if test="locations.deviceType != null">
                and device_type = #{locations.deviceType}
            </if>
            <if test="locations.deviceNum != null and locations.deviceNum != ''">
                and device_num like concat('%', #{locations.deviceNum}, '%')
            </if>
            <if test="locations.batch != null">
                and batch = #{locations.batch}
            </if>
            <if test="locations.startTime != null">
                and time &gt;= #{locations.startTime, jdbcType=BIGINT}
            </if>
            <if test="locations.endTime != null">
                and time &lt;= #{locations.endTime, jdbcType=BIGINT}
            </if>
            <include refid="ids" />
        </where>
        order by time desc
        <if test="query.current gt 0 and query.size gt 0">
            limit ${query.size} offset ${(query.current - 1) * query.size}
        </if>
    </select>

    <select id="countLocations" resultType="java.lang.Long">
        select count(*) from locations
        <where>
            <if test="locations.targetId != null">
                and target_id = #{locations.targetId}
            </if>
            <if test="locations.targetType != null">
                and target_type = #{locations.targetType}
            </if>
            <if test="locations.deviceId != null">
                and device_id = #{locations.deviceId}
            </if>
            <if test="locations.deviceType != null">
                and device_type = #{locations.deviceType}
            </if>
            <if test="locations.deviceNum != null and locations.deviceNum != ''">
                and device_num like concat('%', #{locations.deviceNum}, '%')
            </if>
            <if test="locations.batch != null">
                and batch = #{locations.batch}
            </if>
            <if test="locations.startTime != null">
                and time &gt;= #{locations.startTime, jdbcType=BIGINT}
            </if>
            <if test="locations.endTime != null">
                and time &lt;= #{locations.endTime, jdbcType=BIGINT}
            </if>
            <include refid="ids" />
        </where>
    </select>

    <select id="selectLocations" resultType="com.xh.vdm.bd.entity.LocationKudu">
        select * from locations
        <where>
            <if test="locations.targetId != null">
                and target_id = #{locations.targetId}
            </if>
            <if test="locations.targetType != null">
                and target_type = #{locations.targetType}
            </if>
            <if test="locations.deviceId != null">
                and device_id = #{locations.deviceId}
            </if>
            <if test="locations.deviceType != null">
                and device_type = #{locations.deviceType}
            </if>
            <if test="locations.deviceNum != null and locations.deviceNum != ''">
                and device_num like concat('%', #{locations.deviceNum}, '%')
            </if>
            <if test="locations.batch != null">
                and batch = #{locations.batch}
            </if>
            <if test="locations.startTime != null">
                and time &gt;= #{locations.startTime, jdbcType=BIGINT}
            </if>
            <if test="locations.endTime != null">
                and time &lt;= #{locations.endTime, jdbcType=BIGINT}
            </if>
            <include refid="ids" />
        </where>
        order by time desc
        <if test="query.current gt 0 and query.size gt 0">
            limit ${query.size} offset ${(query.current - 1) * query.size}
        </if>
    </select>

    <select id="locationPage" resultType="com.xh.vdm.bd.entity.LocationKudu">
        select device_id, device_num, device_type, target_id ,longitude , latitude, altitude, speed, bearing, time, recv_time , batch, correction, valid, auxiliary  from locations
        where 1 = 1
        <if test="idsList != null and idsList.size() > 0">
            and (
            <foreach collection="idsList" item="ids" separator=" or " open="(" close=")">
                device_id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </foreach>
            )
        </if>
        <if test="targetIdList != null and targetIdList.size() > 0">
            and (
            <foreach collection="targetIdList" item="ids" separator=" or " open="(" close=")">
                target_id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </foreach>
            )
        </if>
        <if test="batch != null">
            and batch = #{batch}
        </if>
        <if test="startTime != null">
            and time > #{startTime}
        </if>
        <if test="endTime != null">
            and time &lt;= #{endTime}
        </if>
        order by time desc
        limit #{limit} offset #{offset}
    </select>

    <select id="locationCount" resultType="long">
        select count(*)
        from locations
        where 1 = 1
        <if test="idsList != null and idsList.size() > 0">
            and (
            <foreach collection="idsList" item="ids" separator=" or " open="(" close=")">
                device_id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </foreach>
            )
        </if>
        <if test="targetIdList != null and targetIdList.size() > 0">
            and (
            <foreach collection="targetIdList" item="ids" separator=" or " open="(" close=")">
                target_id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            </foreach>
            )
        </if>
        <if test="batch != null">
            and batch = #{batch}
        </if>
        <if test="startTime != null">
            and time > #{startTime}
        </if>
        <if test="endTime != null">
            and time &lt;= #{endTime}
        </if>
    </select>
</mapper>

