package com.xh.vdm.statistic.mapper.impala;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.dto.alarm.AlarmCount;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.impala.ImpalaAlarm;
import com.xh.vdm.statistic.entity.report.AlarmBaseTypeAndCount;
import com.xh.vdm.statistic.vo.request.CommonBaseImpalaAlarmRequest;
import com.xh.vdm.statistic.vo.request.CommonStatRequest;
import com.xh.vdm.statistic.vo.request.alarm.CarAlarmRequest;
import com.xh.vdm.statistic.vo.request.alarm.CommonAlarmStatRequest;
import com.xh.vdm.statistic.vo.request.alarm.DriverAlarmRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@DS("impala")
public interface ImpalaAlarmMapper extends BaseMapper<BdmSecurity> {

	int getNumOverSpeed(@Param("request") CommonAlarmStatRequest request);

	List<ImpalaAlarm> getOverSpeedList(@Param("request") CommonAlarmStatRequest request, @Param("current") long current, @Param("size") long size);

	int getNumFatigueDrive(@Param("request") CommonStatRequest request);

	List<ImpalaAlarm> getFatigueDriveList(@Param("request") CommonStatRequest request, @Param("current") long current, @Param("size") long size);

	int getNumNightDrive(@Param("request") CommonStatRequest request);

	List<ImpalaAlarm> getNightDriveList(@Param("request") CommonStatRequest request, @Param("current") long current, @Param("size") long size);

	int getNumWrong(@Param("request") CommonAlarmStatRequest request);

	List<ImpalaAlarm> getWrongList(@Param("request") CommonAlarmStatRequest request, @Param("current") long current, @Param("size") long size);

	List<AlarmCount> getCarAlarmStatList(@Param("request") CarAlarmRequest request);

	List<ImpalaAlarm> getCarAlarmAnalysisList(@Param("request") CarAlarmRequest request);

	List<AlarmCount> getDriverAlarmStatList(@Param("request") DriverAlarmRequest request);

	List<ImpalaAlarm> getDriverAlarmAnalysisList(@Param("request") DriverAlarmRequest request);

	/**
	 * 按单位类型分组的报警数
	 */
	List<Map<String, Long>> getNumAlarmGroupByDeptType(@Param("request") CommonAlarmStatRequest request);

	/**
	 * 按单位类型分组的已处理报警数
	 */
	List<Map<String, Long>> getNumDealGroupByDeptType(@Param("request") CommonAlarmStatRequest request);

	/**
	 * 根据时间段获取某辆车的定位数据
	 */
	List<Location> getLocationFromTimeSeg(@Param("licenceColor") int licenceColor, @Param("licencePlate") String licencePlate, @Param("startTime") long startTime, @Param("endTime") long endTime);

	/**
	 * 获取单位的报警数
	 */
	int getNumAlarmForDept(@Param("request") CommonStatRequest request);

	/**
	 * 获取单位特定报警类型的车辆排行
	 */
	List<AlarmCount> getCarRankOfAlarmType(@Param("request") CommonAlarmStatRequest request);

	/**
	 * 获取车辆报警各处理措施统计
	 */
	List<AlarmCount> getNumEachCarDeal(@Param("request") CommonAlarmStatRequest request);

	/**
	 * 根据deptId查询报警数量
	 *
	 * @param deptIds
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	long getAlarmCountByDeptIds(@Param("deptList") List<Long> deptIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

	/**
	 * 根据deptId查询报警数量
	 *
	 * @param deptIds
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	long getAlarmCountByDeptIdsAndAlarmTypes(@Param("deptList") List<Long> deptIds, @Param("alarmTypes") List<Byte> alarmTypes, Long startTime, Long endTime);


	/**
	 * 根据deptId查询车辆报警列表
	 *
	 * @param deptIds
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	List<VehicleAndCount> getAlarmListByDeptIdsAndAlarmTypes(@Param("deptList") List<Long> deptIds, @Param("alarmTypes") List<Byte> alarmTypes, Long startTime, Long endTime, @Param("limit") Integer limit);

	int getNumAlarmByDeptIdsAndAlarmTypes (@Param("deptList") List<Long> deptIds, @Param("alarmTypes") List<Byte> alarmTypes, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("licencePlate") String licencePlate, @Param("licenceColor") String licenceColor);

	/**
	 * 根据deptId查询车辆报警列表，分页
	 *
	 * @param deptIds
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	List<VehicleAndCount> getAlarmListByDeptIdsAndAlarmTypesPage(@Param("deptList") List<Long> deptIds, @Param("alarmTypes") List<Byte> alarmTypes, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("licencePlate") String licencePlate, @Param("licenceColor") String licenceColor, @Param("current") long current, @Param("size") long size);


	/**
	 * 根据deptId和alarmType查询发生报警的车辆数
	 *
	 * @param deptIds
	 * @param alarmTypes
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	long getVehicleCountByDeptIdsAndAlarmTypes(@Param("deptList") List<Long> deptIds, @Param("alarmTypes") List<Byte> alarmTypes, Long startTime, Long endTime);

	/**
	 * 根据驾驶员驾驶时段查询报警数量
	 * @param list
	 * @param alarmTypes
	 * @return
	 */
	List<AlarmTypeAndCount> getVehicleAlarmCountInfoByDriverDuration(List<StatDriverDuration> list, List<Integer> alarmTypes);




	/**
	 * 根据deptId查询每个驾驶员的报警数量
	 *
	 * @param deptIds
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	List<DriverNameAndCount> getDriverAlarmListByDeptIdsAndAlarmTypes(@Param("deptList") List<Long> deptIds, @Param("alarmTypes") List<Byte> alarmTypes, Long startTime, Long endTime, @Param("limit") Integer limit);


	/**
	 * 根据deptId查询每个驾驶员的报警数量，分页查询
	 *
	 * @param deptIds
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	IPage<DriverNameAndCount> getDriverAlarmListByDeptIdsAndAlarmTypesPage(@Param("deptList") List<Long> deptIds, @Param("alarmTypes") List<Byte> alarmTypes, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("driverName") String driverName, IPage<DriverNameAndCount> page);


	/**
	 * 分页查询报警信息
	 *
	 * @param request
	 * @param page
	 * @return
	 */
	IPage<ImpalaAlarm> getAlarmsByConditionPage(@Param("request") CommonBaseImpalaAlarmRequest request, IPage<ImpalaAlarm> page);


	/**
	 * 统计 指定时间段内的 报警数
	 * 按小时进行统计
	 *
	 * @return
	 */
	List<DateAndCount> getAlarmCountHour(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds);

	/**
	 * 统计 指定时间段内的报警处理数
	 * 按小时统计
	 *
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param vehicleIds
	 * @param userType
	 * @return
	 */
	List<DateAndCount> getHandleCountHour(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("userType") String userType);


	/**
	 * @description: 查询指定时间段的超过指定时长的疲劳报警数
	 * @author: zhouxw
	 * @date: 2023-07-206 09:44:11
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd , duration: 超过的时长 秒]
	 * @return: long
	 **/
	long getFatigueCountByDateAndDuration(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("duration") long duration, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds);


	/**
	 * @description: 查询指定时间段的超过指定时长的疲劳报警处理数
	 * @author: zhouxw
	 * @date: 2023-07-206 09:44:11
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd , duration: 超过的时长 秒, userType: 用户类型]
	 * @return: long
	 **/
	long getFatigueHandleCountByDateAndDuration(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("duration") long duration, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("userType") String userType);


	/**
	 * @description: 查询指定时间段的报警数
	 * @author: zhouxw
	 * @date: 2023-07-206 09:44:11
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd , duration: 超过的时长 秒]
	 * @return: long
	 **/
	long getAlarmCountByDate(@Param("alarmTypes") List<Integer> alarmTypes, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds);

	/**
	 * @description: 查询指定时间段的报警处理数
	 * @author: zhouxw
	 * @date: 2023-07-206 09:44:11
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd , duration: 超过的时长 秒, userType: 用户类型]
	 * @return: long
	 **/
	long getAlarmHandleCountByDate(@Param("alarmTypes") List<Integer> alarmTypes, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("userType") String userType);


	/**
	 * @description: 查询指定时间段内每天的报警数量
	 * @author: zhouxw
	 * @date: 2023-07-206 09:44:11
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd , duration: 超过的时长 秒, userType: 用户类型]
	 * @return: long
	 **/
	List<DateAndCount> getAlarmHandleCountPerDate(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("userType") String userType);


	/**
	 * @description: 查询指定时间段的报警数
	 * @author: zhouxw
	 * @date: 2023-07-206 09:44:11
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd , duration: 超过的时长 秒]
	 * @return: long
	 **/
	List<AlarmBase> getAlarmBaseByDate(@Param("alarmTypes") List<Integer> alarmTypes, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds);

	/**
	 * @description: 根据报警id查询已经处理的报警的数量
	 * @author: zhouxw
	 * @date: 2023-07-206 14:13:07
	 * @param: [alarmIds, userType]
	 * @return: long
	 **/
	long getAlarmHandleCountByAlarmIds(@Param("alarmIds") List<Long> alarmIds, @Param("userType") String userType);

	/**
	 * 分页查询实时未处理的报警
	 *
	 * @param deptIds
	 * @param vehicleIds
	 * @param userType
	 * @return
	 */
	IPage<UnHandleRealTimeAlarm> getUnHandleRealTimeAlarmByPage(@Param("alarmTypeList") List<Integer> alarmTypeList, @Param("startTime") long startTime, @Param("endTime") long endTime, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("userType") String userType, IPage<BdmSecurity> page);


	/**
	 * @description: 根据部门id和时间段获取报警数量（本节点及子节点）
	 * 不包含误报
	 * @author: zhouxw
	 * @date: 2023-06-164 21:05:12
	 * @param: [deptIds 本节点及子节点的deptId]
	 * @return: long
	 **/
	long getAlarmCountByDeptIdAndDurationOrHandle(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime);


	/**
	 * @description: 根据部门id和时间段、报警类型获取报警数量（本节点及子节点）
	 * 不包含误报
	 * @author: zhouxw
	 * @date: 2023-06-164 21:05:12
	 * @param: [deptIds 本节点及子节点的deptId； alarmTypes 报警类型，多个类型中间使用英文逗号分隔；startTime 开始时间，精确到秒的时间戳，时间范围包含开始时间； endTime 结束时间，精确到秒的时间戳，时间范围不包含结束时间；]
	 * @return: long
	 **/
	long getAlarmCountByDeptIdAndDurationAndType(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("alarmTypes") List<Integer> alarmTypes);


	/**
	 * @description: 根据部门id和时间段、报警类型获取报警数量（本节点及子节点）
	 * 不包含误报
	 * @author: zhouxw
	 * @date: 2023-06-164 21:05:12
	 * @param: [deptIds 本节点及子节点的deptId； alarmTypes 报警类型，多个类型中间使用英文逗号分隔；startTime 开始时间，精确到秒的时间戳，时间范围包含开始时间； endTime 结束时间，精确到秒的时间戳，时间范围不包含结束时间；]
	 * @return: long
	 **/
	long getAlarmHandleCountByDeptIdAndDurationAndType(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("alarmTypes") List<Integer> alarmTypes, @Param("userType") String userType);


	/**
	 * 查询报警类型和数量
	 *
	 * @param deptIds
	 * @param vehicleIds
	 * @param startTime
	 * @param endTime
	 * @param alarmTypes
	 * @return
	 */
	List<AlarmTypeAndDateAndCount> getAlarmTypeAndDateAndCountOrHandle(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("alarmTypes") List<Integer> alarmTypes, @Param("userType") String userType);

	/**
	 * 查询报警类型和数量
	 *
	 * @param deptIds
	 * @param vehicleIds
	 * @param startTime
	 * @param endTime
	 * @param alarmTypes
	 * @return
	 */
	List<AlarmBaseTypeAndCount> getAlarmTypeAndCountOrHandle(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("alarmTypes") List<Integer> alarmTypes, @Param("userType") String userType);


	/**
	 * 查询车辆报警数量排行前n的车辆
	 *
	 * @param deptIds
	 * @param vehicleIds
	 * @param startTime
	 * @param endTime
	 * @param alarmTypes
	 * @return
	 */
	List<VehicleAndCount> getVehicleAlarmCountTop(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("alarmTypes") List<Integer> alarmTypes, @Param("count") Integer count);

	/**
	 * 查询指定的车辆报警情况
	 *
	 * @param vehicleIds
	 * @param startTime
	 * @param endTime
	 * @param alarmTypes
	 * @return
	 */
	List<VehicleAndAlarmTypeAndCount> getVehicleAlarmCountInfo(@Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("alarmTypes") List<Integer> alarmTypes);

	/**
	 * @description: 根据部门id和时间段获取报警数量（本节点及子节点）
	 * 不包含误报
	 * @author: zhouxw
	 * @date: 2023-06-164 21:05:12
	 * @param: [deptIds 本节点及子节点的deptId]
	 * @return: long
	 **/
	long getAlarmCountByDeptIdAndDuration(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime);
}
