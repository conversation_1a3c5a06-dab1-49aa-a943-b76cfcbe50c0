package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.BdmVehicleMapper;
import com.xh.vdm.statistic.mapper.StatisticsMapper;
import com.xh.vdm.statistic.service.IBdmVehicleService;
import com.xh.vdm.statistic.service.IBladeDeptService;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.VdmUserInfoUtil;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.request.CommonStatRequest;
import com.xh.vdm.statistic.vo.response.CarMegaByteResponse;
import com.xh.vdm.statistic.vo.response.OnlineDaysCountResponse;
import com.xh.vdm.statistic.vo.response.UnexpectedReportResponse;
import com.xh.vdm.statistic.vo.response.VehicleRunningStateResponse;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.system.cache.SysCache;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-25
 */
@Service
public class BdmVehicleServiceImpl extends ServiceImpl<BdmVehicleMapper, BdmVehicle> implements IBdmVehicleService {

	@Autowired
	private VdmUserInfoUtil vdmUserInfoUtil;


	@Resource
	private RedisTemplate<String,Object> redisTemplate;

	@Autowired
	private IBladeDeptService bladeDeptService;

	@Resource
	private StatisticsMapper statisticsMapper;

	@Override
	public long findAllVehicleCount(List<Long> deptIds, Long userId) {
		return baseMapper.getAllVehicleCount(deptIds, userId);
	}

	public long findAllVehicleCountByDeptId(List<Long> deptIds) {
		return baseMapper.getAllVehicleCountByDeptId(deptIds);
	}

	@Override
	public long findExpiredCount(Date date, List<Long> deptIds, Long userId) throws Exception {
		return baseMapper.getCertExpiredCount(date, deptIds, userId);
	}

	@Override
	public long findHasExpiredCount(Date expireDate, List<Long> deptIds, Long userId) throws Exception {
		return baseMapper.getCertHasExpiredCount(expireDate, deptIds, userId);
	}

	@Override
	public long findUnKnownExpireCount(List<Long> deptIds, Long userId) {
		return baseMapper.getUnKnowExpireCount(deptIds, userId);
	}

	@Override
	public long findRunningCount(List<Long> deptIds, Long userId) {
		return baseMapper.getRunningCount(deptIds, userId);
	}

	public long findRunningCountByDeptId(List<Long> deptIds) {
		return baseMapper.getRunningCountByDeptId(deptIds);
	}

	@Override
	public long findOfflineCountByOfflineDayCount(List<Long> deptIds, Long userId, int offlineDayCount) throws Exception {
		//查询要统计的日期
		String dateBefore = DateUtil.getDateBeforeDayStr(new Date(), offlineDayCount);
		long count = baseMapper.getOfflineCountBeforeDate(deptIds, userId, dateBefore);
		return count;
	}

	@Override
	public List<VehicleAndCount> findOfflineListByOfflineDayCount(List<Long> deptIds, Long userId, int offlineDayCount, int limit) throws Exception {
		//查询要统计的日期
		String dateBefore = DateUtil.getDateBeforeDayStr(new Date(), offlineDayCount);
		List<VehicleAndCount> vList = baseMapper.getOfflineListBeforeDate(deptIds, userId, dateBefore, limit);
		return vList;
	}

	@Override
	public IPage<VehicleAndCount> findOfflineListByOfflineDayCountPage(List<Long> deptIds, Long userId, Integer offlineDayCount,String licencePlate, Long licenceColor, Query query) throws Exception {
		IPage<VehicleAndCount> page = new Page<>(query.getCurrent(), query.getSize());
		//查询要统计的日期
		String dateBefore = DateUtil.getDateBeforeDayStr(new Date(), offlineDayCount);
		IPage<VehicleAndCount> pageRes = baseMapper.getOfflineBeforeDatePage(deptIds, userId, dateBefore, licencePlate, licenceColor, page);
		return pageRes;
	}

	@Override
	public List<VehicleBase> findRunningVehicle(List<Long> deptIds, Long userId) {
		return baseMapper.getRunningVehicle(deptIds, userId);
	}

	@Override
	public long findStopRunningCount(List<Long> deptIds, Long userId) {
		return baseMapper.getStopRunningCount(deptIds, userId);
	}

	@Override
	public long findInNetCount(List<Long> deptIds, Long userId) {
		return baseMapper.getInNetCount(deptIds, userId);
	}

	@Override
	public long findGoOnlineCount(List<Long> deptIds, Long userId) {
		return baseMapper.getGoOnlineCount(deptIds, userId);
	}

	@Override
	public long findGoOnlineCountAllToday(List<Long> deptIds, Long userId) {
		return baseMapper.getGoOnlineCountAllToday(deptIds, userId);
	}

	@Override
	public long findGoOnlineCountAllMonth(List<Long> deptIds, Long userId, String month) throws Exception {
		return baseMapper.getGoOnlineCountAllMonth(deptIds, userId,month.replace("-",""),DateUtil.getMonthLastDateStr(month), month);
	}


	@Override
	public List<VehicleAndCount> findGoOnlineDurationListAllMonthByVehicleId(List<Integer> vehicleIds, String month, String lastDateInMonth) throws Exception {
		return baseMapper.getGoOnlineDurationListAllMonthByVehicleId(vehicleIds, month.replace("-",""), lastDateInMonth,month);
	}

	@Override
	public long findGoOnlineCountByDate(List<Long> deptIds,List<Integer> vehicleIds, String date) {
		String month = date.substring(0,7).replace("-","");
		return baseMapper.getGoOnlineCountByDate(deptIds,vehicleIds, date, month);
	}

	@Override
	public List<VehicleAndCount> findGoOnlineDurationListByDateAndVehicleId(List<Integer> vehicleIds, String date) {
		String month = date.substring(0,7).replace("-","");
		return baseMapper.getGoOnlineDurationListByDateAndVehicleId(vehicleIds, date, month);
	}

	@Override
	public long findOnlineCount(List<Long> deptIds, Long userId) {
		return baseMapper.getOnlineCount(deptIds, userId);
	}

	@Override
	public List<Integer> getVehicleIdList (List<String> vehicleUseTypeList, Long vehicleOwnerId, String accessMode) {
		QueryWrapper<BdmVehicle> wrapper = new QueryWrapper<>();
		if ((vehicleUseTypeList != null) && (!vehicleUseTypeList.isEmpty())) {
			wrapper.in("vehicle_use_type", vehicleUseTypeList);
		}
		if (vehicleOwnerId != null) {
			wrapper.eq("vehicle_owner_id", vehicleOwnerId);
		}
		if (StringUtils.isNotBlank(accessMode)) {
			wrapper.eq("access_mode", accessMode);
		}

		List<BdmVehicle> vehicleList = this.list(wrapper);
		if ((vehicleList == null) || vehicleList.isEmpty()) {
			return new ArrayList<>();
		}

		return vehicleList.parallelStream().map(BdmVehicle::getId).collect(Collectors.toList());
	}

	@Override
	public IPage<BdmVehicle> getVehicleListByPage (List<String> vehicleUseTypeList, Long vehicleOwnerId, String accessMode, Query query) {
		QueryWrapper<BdmVehicle> wrapper = new QueryWrapper<>();
		if ((vehicleUseTypeList != null) && (!vehicleUseTypeList.isEmpty())) {
			wrapper.in("vehicle_use_type", vehicleUseTypeList);
		}
		if (vehicleOwnerId != null) {
			wrapper.eq("vehicle_owner_id", vehicleOwnerId);
		}
		if (StringUtils.isNotBlank(accessMode)) {
			wrapper.eq("access_mode", accessMode);
		}

		IPage<BdmVehicle> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		IPage<BdmVehicle> vehicleList = page(page, wrapper);
		return vehicleList;
	}

	@Override
	public Map<Integer, BdmVehicle> getVehicleMap (CommonStatRequest request) {
		List<BdmVehicle> vehicleList = this.baseMapper.getUserVehicleList(request.getDeptList(), request.getVehicleList());
		Map<Integer, BdmVehicle> vehicleMap = new HashMap<>();
		if ((vehicleList == null) || vehicleList.isEmpty()) {
			return vehicleMap;
		}

		return vehicleList.parallelStream().collect(Collectors.toMap(BdmVehicle::getId, vehicle -> vehicle));
	}


	@Override
	public VehicleRunningStateResponse statVehicleRunningState(BladeUser user) throws Exception{
		if(user == null){
			throw new Exception("用户未登录或无权限");
		}

		List<Long> deptIds = null;
		try {
			deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
		}catch (Exception e){
			log.error("查询部门信息失败",e);
			throw new Exception("用户登录失败或未授权");
		}


		long runningCount = 0;
		try{
			//0. 营运车辆数
			runningCount = findRunningCount(deptIds, user.getUserId());

			//1.统计在线车辆数量
			//1.1 查询当前在线总车辆
			long onlineCount = 0;
			//todo 后期改成读取redis
			//Set<String> onlineVehicle = redisTemplate.keys(CommonConstant.CACHE_VEHICLE_STATE);
			onlineCount = findOnlineCount(deptIds, user.getUserId());

			//1.2 查询当前用户能查询到的车辆（营运车辆）
			//List<VehicleBase> vehicleList = findRunningVehicle(deptIds, user.getUserId());

			//1.3 两者取交集，获取当前用户能查询到的在线车辆数
			/*Set<String> vehicleSet = new HashSet<>();
			vehicleList.forEach(item -> {
				String key = CommonConstant.CACHE_VEHICLE_STATE + item.getLicencePlate() + ":" + item.getPlateColor();
				vehicleSet.add(key);
			});*/
			//onlineVehicle.retainAll(vehicleSet);
			//onlineCount = onlineVehicle.size();

			//2.统计长期不在线的车辆数量（15天内没有上过线）
			long longOfflineCount = findOfflineCountByOfflineDayCount(deptIds, user.getUserId(), 15);

			//3.统计离线车辆数量（15天内上过线，但是目前不在线）
			//离线车辆数 = 营运车辆数 - 在线车辆数 - 长期不在线车辆数
			long offlineCount = runningCount - onlineCount - longOfflineCount;
			if(offlineCount < 1){
				offlineCount = 0;
			}
			VehicleRunningStateResponse resp = new VehicleRunningStateResponse();
			resp.setOfflineCount(offlineCount);
			resp.setOfflineLongTime(longOfflineCount);
			resp.setOnlineCount(onlineCount);
			return resp;
		}catch (Exception e){
			log.error("查询营运车辆数出错",e);
			throw new Exception("查询出错");
		}
	}

	@Override
	public long findRunningVehicleCount(List<Long> deptIds, Long userId) throws Exception {
		return baseMapper.getRunningVehicleCount(deptIds, userId);
	}

	@Override
	public List<OnlineDaysCountResponse> findCountByDynamicSql(String sql) throws Exception {
		return baseMapper.getOnlineDaysCount(sql);
	}

	@Override
	public List<VehicleAndMileage> findTotalMileage(List<String> dateList, List<Long> vehicleIdList, String month) {
		return baseMapper.getTotalMileage(dateList, vehicleIdList, month);
	}

	@Override
	public IPage<VehicleAndMileage> findTotalMileagePage(List<Long> deptIds, List<Integer> vehicleIds, String month, String statDate, Query query) throws Exception {
		IPage<VehicleAndMileage> page = new Page<>(query.getCurrent(), query.getSize());
		month = month.replace("-","");
		statDate = statDate.replace("-","");
		return baseMapper.getTotalMileagePage(deptIds, vehicleIds, statDate, month, page);
	}

	@Override
	public IPage<VehicleAndMileage> findTotalMileageMonthPage(List<Long> deptIds, List<Integer> vehicleIds, String month, Query query) throws Exception {
		IPage<VehicleAndMileage> page = new Page<>(query.getCurrent(), query.getSize());
		month = month.replace("-","");
		return baseMapper.getTotalMileageMonthPage(deptIds, vehicleIds, month, page);
	}

	@Override
	public List<BdmVehicle> findVehicleByLicencePlateAndColor(List<String> plateAndColorKey) {
		if(plateAndColorKey == null || plateAndColorKey.size() < 1){
			return null;
		}
		return baseMapper.getVehicleByLicencePlateAndColor(plateAndColorKey);
	}

	@Override
	public List<VehicleOnlineCount> findVehicleOnlineCount(List<String> vehicles, long startTime, long endTime) throws Exception{
		//1.获取查询月份和日期列表
		List<DateListAndMonth> dmListTmp = DateUtil.getDateListAndMonth(startTime, endTime);
		//2.改造日期列为字段名称
		List<DateListAndMonth> dmList = new ArrayList<>();
		dmListTmp.forEach(item -> {
			DateListAndMonth dm = new DateListAndMonth();
			dm.setMonth(item.getMonth());
			List<String> dateListTmp = item.getDateList();
			List<String> dateList = new ArrayList<>();
			for (String s : dateListTmp) {
				String dateColumn = "d" + s.substring(8,10);
				dateList.add(dateColumn);
			}
			dm.setDateList(dateList);
			dmList.add(dm);
		});
		//3.查询车辆每月在线天数
		List<VehicleAndDateAndCount> list = baseMapper.getVehicleOnlineCount(vehicles, dmList);
		//4.统计最终每辆车在线天数
		List<VehicleOnlineCount> resList = new ArrayList<>();
		Map<String, List<VehicleAndDateAndCount>> map = new HashMap<>();
		list.forEach(item -> {
			String key = item.getLicencePlate() + "~" +item.getLicenceColor();
			List<VehicleAndDateAndCount> tmpList = map.get(key);
			if(tmpList == null){
				tmpList = new ArrayList<>();
			}
			tmpList.add(item);
			map.put(key, tmpList);
		});
		map.forEach((k,v) -> {
			String licencePlate = k.split("~")[0];
			String licenceColor = k.split("~")[1];
			List<VehicleAndDateAndCount> tmpList = v;
			AtomicInteger count = new AtomicInteger();
			if(tmpList != null && tmpList.size() > 0){
				tmpList.forEach(item -> {
					int c = item.getCount();
					count.addAndGet(c);
				});
			}
			VehicleOnlineCount voc = new VehicleOnlineCount();
			voc.setLicencePlate(licencePlate);
			voc.setLicenceColor(licenceColor);
			voc.setOnlineDaysCount(count.get());
			resList.add(voc);
		});

		return resList;
	}

	@Override
	public double findTotalMileageByDeptIds(List<Long> deptIds, String date, String month) throws Exception {
		return baseMapper.getTotalMileageByDeptIds(deptIds, date, month);
	}

	@Override
	public IPage<BdmVehicle> findVehiclesByCondition(CommonBaseCrossMonthRequest request, Query query) {
		IPage<BdmVehicle> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		return baseMapper.getVehiclesByCondition(request, page);
	}

	@Override
	public long findVehiclesByConditionCount(CommonBaseCrossMonthRequest request) {
		return baseMapper.getVehiclesByConditionCount(request);
	}

	@Override
	public List<VehicleCommonInfo> findVehicleCommonInfo(List<Long> vehicleIds) {
		return baseMapper.getVehicleCommonInfo(vehicleIds);
	}

	@Override
	public IPage<CarMegaByteResponse> findVehicleMegaByteDaily(CommonBaseRequest request, Query query) throws Exception {
		//1.获取查询日期列表
		List<String> dateList = DateUtil.getDateList(request.getStartTime(), request.getEndTime());
		IPage<CarMegaByteResponse> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		return baseMapper.getVehicleMegaByteDaily(request, dateList, page);
	}

	@Override
	public IPage<UnexpectedReportResponse> findUnexpectedReport(CommonBaseRequest request, Query query, BladeUser user) throws Exception {
		IPage<UnexpectedReportResponse> page = new Page<>();
		page.setSize(query.getSize());
		page.setCurrent(query.getCurrent());
		return statisticsMapper.getUnexpectedReport(request, user.getTenantId(), page);
	}

	@Override
	public List<VehicleBaseWithId> findVehicleBaseListAll(List<Long> deptIds, List<Integer> vehicleIds, String licencePlate) throws Exception {
		return baseMapper.getVehicleBaseListAll(deptIds, vehicleIds, licencePlate);
	}

	@Override
	public Map<Long, Integer> getDeptMapNumVehicle (CommonStatRequest request) {
		Map<Long, Integer> deptMapNumVehicle = new HashMap<>();
		List<Map<String, Long>> numVehicleGroupByDept = this.baseMapper.getNumVehicleGroupByDept(request);
		if (CollectionUtils.isEmpty(numVehicleGroupByDept)) {
			return deptMapNumVehicle;
		}
		for (Map<String, Long> tmp : numVehicleGroupByDept) {
			deptMapNumVehicle.put(tmp.get("dept_id"), tmp.get("num").intValue());
		}

		return deptMapNumVehicle;
	}

	@Override
	public List<VehicleAndCount> findOnlineDaysCount(String statDate, List<Integer> vehicleIds) throws Exception {
		String month = statDate.substring(0,7).replace("-","");
		//1.获取上下线记录
		List<VehicleAndOnOfflineRecord> records = baseMapper.getVehicleOnOfflineRecord(vehicleIds,month, statDate);
		//2.根据上下线记录统计上线天数
		Map<String,Set> setMap = new HashMap<>();
		records.forEach(item -> {
			String onlineTime = item.getOnlineTime();
			String offlineTime = item.getOfflineTime()==null?DateUtil.getDateString():item.getOfflineTime();
			String key = item.getLicencePlate()+"~"+item.getLicenceColor()+"~"+item.getVehicleId();
			try {
				List<String> dateList = DateUtil.getDateList(onlineTime, offlineTime);
				Set<String> days = setMap.get(key);
				if(days == null){
					days = new HashSet<>();
					setMap.put(key, days);
				}
				days.addAll(dateList);
				setMap.put(key, days);
			}catch (Exception e){
				log.error("获取日期列表失败",e);
			}
		});
		List<VehicleAndCount> list = new ArrayList<>();
		setMap.forEach((k,v)->{
			VehicleAndCount vc = new VehicleAndCount();
			vc.setLicencePlate(k.split("~")[0]);
			vc.setLicenceColor(k.split("~")[1]);
			vc.setVehicleId(Integer.parseInt(k.split("~")[2]));
			vc.setCount((long)v.size());
			list.add(vc);
		});
		return list;
	}

	@Override
	public List<VehicleAndCount> findOnlineDaysCountMonth(String month, List<Integer> vehicleIds) throws Exception {
		month = month.replace("-","");
		//1.获取上下线记录
		List<VehicleAndOnOfflineRecord> records = baseMapper.getVehicleOnOfflineRecordMonth(vehicleIds,month);
		//2.根据上下线记录统计上线天数
		Map<String,Set> setMap = new HashMap<>();
		records.forEach(item -> {
			String onlineTime = item.getOnlineTime();
			String offlineTime = item.getOfflineTime();
			String key = item.getLicencePlate()+"~"+item.getLicenceColor()+"~"+item.getVehicleId();
			try {
				List<String> dateList = DateUtil.getDateList(onlineTime, offlineTime);
				Set<String> days = setMap.get(key);
				if(days == null){
					days = new HashSet<>();
					setMap.put(key, days);
				}
				days.addAll(dateList);
				setMap.put(key, days);
			}catch (Exception e){
				log.error("获取日期列表失败",e);
			}
		});
		List<VehicleAndCount> list = new ArrayList<>();
		setMap.forEach((k,v)->{
			VehicleAndCount vc = new VehicleAndCount();
			vc.setLicencePlate(k.split("~")[0]);
			vc.setLicenceColor(k.split("~")[1]);
			vc.setVehicleId(Integer.parseInt(k.split("~")[2]));
			vc.setCount((long)v.size());
			list.add(vc);
		});
		return list;
	}



	/**
	 * 查询当前用户的所有车辆，包含部门和账号关联的车辆
	 * @param user
	 * @return
	 * @throws Exception
	 */
	public List<Integer> getVehicleIdListAll(BladeUser user) throws Exception{
		//1.获取部门信息
		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
		//查询部门下的车辆
		List<com.xh.vdm.statistic.entity.BdmVehicle> vList = list(Wrappers.lambdaQuery(com.xh.vdm.statistic.entity.BdmVehicle.class).in(com.xh.vdm.statistic.entity.BdmVehicle::getDeptId,deptIds));
		List<Integer> vIds = new ArrayList<>();
		vList.forEach(item -> {
			vIds.add(item.getId());
		});
		//2.查询用户关联的车辆
		List<org.springblade.system.user.entity.BdmVehicle> vehicleList =  vdmUserInfoUtil.getVehicleListByUserId(user.getUserId());
		vehicleList.forEach(item -> {
			vIds.add(item.getId());
		});
		return vIds;
	}

	/**
	 * 查询当前用户的所有车辆，包含部门和账号关联的车辆
	 * @param user
	 * @return
	 * @throws Exception
	 */
	public List<com.xh.vdm.statistic.entity.BdmVehicle> getVehicleListAll(BladeUser user) throws Exception{
		List<com.xh.vdm.statistic.entity.BdmVehicle> vehicleList = new ArrayList<>();
		//1.获取部门信息
		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
		//查询部门下的车辆
		List<com.xh.vdm.statistic.entity.BdmVehicle> vList = list(Wrappers.lambdaQuery(com.xh.vdm.statistic.entity.BdmVehicle.class).in(com.xh.vdm.statistic.entity.BdmVehicle::getDeptId,deptIds));
		vehicleList.addAll(vList);
		//2.查询用户关联的车辆
		List<org.springblade.system.user.entity.BdmVehicle> vehicleUList =  vdmUserInfoUtil.getVehicleListByUserId(user.getUserId());
		List<com.xh.vdm.statistic.entity.BdmVehicle> vehicleBList = new ArrayList<>();
		vehicleUList.forEach(item -> {
			com.xh.vdm.statistic.entity.BdmVehicle b = new BdmVehicle();
			BeanUtils.copyProperties(item, b);
			vehicleList.add(b);
		});
		return vehicleList;
	}

	public IPage<BdmVehicle> getVehicleListAllByPage(BladeUser user, long deptId, Query query) throws Exception{
		//1.获取部门信息
		List<Long> deptIds = SysCache.getDeptChildIds(deptId);
//		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);

		//2.查询用户关联的车辆
		List<Integer> vehicleIds = new ArrayList<>();
		if(user != null){
			vehicleIds = vdmUserInfoUtil.getVehicleIdList(user.getUserId());
		}

		//3.分页查询车辆
		IPage<Vehicle> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		IPage pages = baseMapper.getVehicleListByPage(deptIds, vehicleIds, page);
		return pages;
	}

}
