package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.StatEmailHistory;
import com.xh.vdm.statistic.vo.request.EmailRequest;
import com.xh.vdm.statistic.vo.response.EmailHistoryResponse;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
public interface IStatEmailService extends IService<StatEmailHistory> {


    /**
     * @description: 查询邮件发送历史
     * @author: zhouxw
     * @date: 2023-03-61 11:12:39
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatEmail>
     **/
    IPage<EmailHistoryResponse> findEmailHistoryList(IPage<EmailHistoryResponse> page, EmailRequest request) throws Exception;

}
