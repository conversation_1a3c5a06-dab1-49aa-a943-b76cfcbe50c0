package com.xh.vdm.statistic.task;

import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @Description: xxl-job 定时任务执行测试
 * @Author: zhouxw
 * @Date: 2023/6/6 14:20
 */
//@Component
@Slf4j
public class XxlJobTest {


	/**
	 * 1、简单任务示例（Bean模式）
	 */
	//@XxlJob("printJobHandler")
	public void demoJobHandler(String p) throws Exception {

		//获取xxl-job传入的参数
		String param = XxlJobHelper.getJobParam();
		log.info("接收到 xxl-job 的参数(工具类获取)为：" + param);


		XxlJobHelper.log("XXL-JOB, Hello World.");
		log.info("此处执行了定时任务");

		for (int i = 0; i < 5; i++) {
			XxlJobHelper.log("beat at:" + i);
			TimeUnit.SECONDS.sleep(2);
		}
		// default success
	}



}
