<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatOverSpeedMapper">

    <select id="statOverSpeedByDeptAndDuration" resultType="com.xh.vdm.statistic.entity.OverSpeedWithDeptAndDate">
        select os.dept_id , os.date , sum(os.over_speed_second) over_speed_second , sum(ceiling(os.over_speed_second / 30) )over_speed_count  from
        (
        select dept_id , to_timestamp(alarm_time, '%Y-%m') date , (alarm_end_time - alarm_time) over_speed_second  from bdm_security
        where 1 = 1
        and dept_id in (
        <foreach collection="deptIds" item="item" separator=",">
            #{item}
        </foreach>
        )
        and alarm_type in (1)
        and alarm_time >= #{startSecondTimestamp,jdbcType=BIGINT}
        and alarm_type &lt;= #{endSecondTimestamp,jdbcType=BIGINT}
        ) os
        where os.over_speed_second > 30
        group by os.dept_id , os.date
    </select>

    <select id="getOverSpeedByDeptAndDuration" resultType="com.xh.vdm.statistic.entity.OverSpeedWithDeptAndDate">
        select
        <if test="deptId != null and deptId != ''">
            os.dept_id , os.date ,
        </if>
        sum(os.over_speed_second) over_speed_second , sum(ceiling(os.over_speed_second / 30) )over_speed_count  from
        (
        select dept_id , to_timestamp(alarm_time, '%Y-%m') date , (alarm_end_time - alarm_time) over_speed_second  from bdm_security
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=BIGINT}
        </if>
        and alarm_type in (1)
        and alarm_time >= #{startSecondTimestamp,jdbcType=BIGINT}
        and alarm_time &lt;= #{endSecondTimestamp,jdbcType=BIGINT}
        ) os
        where os.over_speed_second > 30
        <if test="deptId != null and deptId != ''">
            group by os.dept_id , os.date
        </if>
    </select>
</mapper>
