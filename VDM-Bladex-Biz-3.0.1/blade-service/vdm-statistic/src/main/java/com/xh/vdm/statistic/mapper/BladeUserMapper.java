package com.xh.vdm.statistic.mapper;

import com.xh.vdm.statistic.entity.BladeUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.UserDept;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
public interface BladeUserMapper extends BaseMapper<BladeUser> {

	List<String> getRoleNamesByRoleIds(@Param("roleIds") List<Long> roleIds);

	/**
	 * 查询关联多个部门的账户id
	 * @return
	 */
	List<Long> getUserIdWithMultiDept();

	/**
	 * 根据 userId 查询 部门id
	 * @return
	 */
	List<Long> getDeptIdsByUserId(@Param("userId") Long userId);

	/**
	 * 跟新deptId
	 * @param userDept
	 */
	void updateDeptIdByUserId(@Param("userDept") UserDept userDept);
}
