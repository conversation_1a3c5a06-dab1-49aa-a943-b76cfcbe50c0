package com.xh.vdm.statistic.vo.request.alarm;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.vo.request.CommonStatRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import java.util.List;

@ApiModel(value = "请求体：报警类统计报表通用请求")
@Data
@EqualsAndHashCode(callSuper = true)
public class CommonAlarmStatRequest extends CommonStatRequest {

	@JsonProperty("alarm_type")
	@ApiModelProperty(name = "alarm_type", value = "报警类型（类型值与名称的映射，详见blade_dict_biz表，code=alarm_type的记录）", example = "1", required = false)
	@DecimalMin(value = "0", message = "报警类型不正确。")
	private Short alarmType;

	@JsonProperty("alarm_type_list")
	@ApiModelProperty(name = "alarm_type_list", value = "报警类型列表（每个元素为类型值，其与名称的映射，详见blade_dict_biz表，code=alarm_type的记录）", example = "[1, ...]", required = false)
	private List<Short> alarmTypeList;
}
