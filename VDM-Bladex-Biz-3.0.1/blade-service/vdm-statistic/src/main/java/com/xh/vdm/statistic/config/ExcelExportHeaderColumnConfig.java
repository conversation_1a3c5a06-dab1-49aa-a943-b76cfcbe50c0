package com.xh.vdm.statistic.config;

import com.alibaba.excel.write.style.row.AbstractRowHeightStyleStrategy;
import org.apache.poi.ss.usermodel.Row;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2022/10/8 1:44 PM
 */
public class ExcelExportHeaderColumnConfig extends AbstractRowHeightStyleStrategy {

    @Override
    protected void setHeadColumnHeight(Row row, int relativeRowIndex) {
        //设置主标题行高
        if(relativeRowIndex == 0){
            //如果excel需要显示行高为15，那这里就要设置为15*20=300
            row.setHeight((short) (556));
        }
    }

    @Override
    protected void setContentColumnHeight(Row row, int relativeRowIndex) {
    }
}
