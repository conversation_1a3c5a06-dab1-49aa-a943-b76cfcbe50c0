package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.DetailParam;
import com.xh.vdm.statistic.entity.RateParam;
import com.xh.vdm.statistic.service.IStatCompleteService;
import com.xh.vdm.statistic.utils.*;
import com.xh.vdm.statistic.vo.request.DetailRequest;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.LocationCompleteDetailResponse;
import com.xh.vdm.statistic.vo.response.LocationCompleteRateResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description: 轨迹完整率相关接口
 * @Author: zhouxw
 * @Date: 2022/9/8 9:20 AM
 */
@RestController
@RequestMapping("/bt/statistics/complete")
@Slf4j
public class LocationCompleteController {

    @Resource
    private IStatCompleteService completeService;

    @Value("${static.file.path:/statistic/files/}")
    String staticFilePath;


    @Resource
    private DictionaryUtil dictionaryUtil;

    @Resource
    private CacheUtil cacheUtil;

    /**
     * @description: 查询轨迹完整率
     * @author: zhouxw
     * @date: 2022/9/8 2:06 PM
     * @param: []
     * @return: com.xh.vdm.statistic.vo.response.LocationCompleteRateResponse
     **/
    @PostMapping("/completeRate")
    public R<LocationCompleteRateResponse> getLocationCompleteRate(@RequestBody @Validated RateRequest request){
        LocationCompleteRateResponse response;
        try{
            RateParam param = new RateParam();
            BeanUtils.copyProperties(request , param);
            param.setMonth(request.getMonth().replace("-",""));
            response = completeService.getLocationCompleteRate(param);
            if(response == null){
                //return R.fail("未查询到轨迹完整率信息");
                //如果未查询到轨迹完整率信息，则进行返回参数的初始化
                return R.data(new LocationCompleteRateResponse() , "未查询到轨迹完整率信息");
            }
            //更改总里程和连续里程单位
            response.setTotalContinousMileage(MathUtil.roundDouble(response.getTotalContinousMileage()/1000 ,2));
            response.setTotalMileage(MathUtil.roundDouble(response.getTotalMileage()/1000 , 2));
            //计算得分
            double rate = response.getCompleteRate();
            if(rate >= 0.7){
                //按照服务商标准计算得分
                response.setScore(MathUtil.roundDouble(rate * 25 , 2));
            }else{
                response.setScore(0);
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("查询轨迹完整率失败",e);
            return R.fail("查询轨迹完整率失败");
        }
        return R.data(response);
    }


    /**
     * @description: 查询轨迹不完整车辆的轨迹明细
     * @author: zhouxw
     * @date: 2022/9/8 4:55 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.core.metadata.IPage<java.util.List<com.xh.vdm.statistic.vo.response.LocationCompleteDetailResponse>>>
     **/
    @PostMapping("/unCompleteDetail")
    public R<IPage<LocationCompleteDetailResponse>> unCompleteDetail (@RequestBody @Validated DetailRequest request){
        //前端下标从 0 开始，此处要加1
        request.setStart(request.getStart()+1);

        IPage<LocationCompleteDetailResponse> response;
        try{
            DetailParam param = new DetailParam();
            BeanUtils.copyProperties(request , param);
            param.setMonthStrict(request.getMonth().replace("-" , ""));
            param.setCurrent(request.getStart()/request.getCount()+(request.getStart() % request.getCount()>0?1:0));
            param.setSize(request.getCount());
            response = completeService.findUnCompleteList(param);

            //2.颜色名值转换
            Map<String , String> colorMap = cacheUtil.getVehicleColorMap();
            List<LocationCompleteDetailResponse> list = response.getRecords();
            list.forEach(item ->{
                item.setPlateColor(colorMap.get(item.getPlateColor()));

                //更改总里程和连续里程单位
                item.setTotalMileage(MathUtil.roundDouble(item.getTotalMileage()/1000 ,2));
                item.setContinousMileage(MathUtil.roundDouble(item.getContinousMileage()/1000 , 2));
            });

        }catch (Exception e){
            e.printStackTrace();
            log.error("查询轨迹不完整车辆的轨迹明细失败",e);
            return R.fail("查询轨迹不完整车辆的轨迹明细失败");
        }
        return R.data(response);
    }


    /**
     * @description: 导出轨迹不完整车辆的轨迹明细
     * @author: zhouxw
     * @date: 2022/9/8 4:55 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.core.metadata.IPage<java.util.List<com.xh.vdm.statistic.vo.response.LocationCompleteDetailResponse>>>
     **/
    @PostMapping("/unCompleteDetailExport")
    public R<String> unCompleteDetailExport (@RequestBody @Validated DetailRequest request){
        IPage<LocationCompleteDetailResponse> response;
        try{
            //1.查询所有数据
            DetailParam param = new DetailParam();
            BeanUtils.copyProperties(request , param);
            param.setMonthStrict(request.getMonth().replace("-" , ""));
            param.setCurrent(0);
            param.setSize(Integer.MAX_VALUE);
            response = completeService.findUnCompleteList(param);

            //2.颜色名值转换
            Map<String , String> colorMap = cacheUtil.getVehicleColorMap();
            List<LocationCompleteDetailResponse> list = response.getRecords();
            list.forEach(item ->{
                item.setPlateColor(colorMap.get(item.getPlateColor()));

                //更改总里程和连续里程单位
                item.setTotalMileage(MathUtil.roundDouble(item.getTotalMileage()/1000 ,2));
                item.setContinousMileage(MathUtil.roundDouble(item.getContinousMileage()/1000 , 2));
            });

            //3.执行数据导出
            String title = "轨迹不完整车辆明细";
            String[] arrs = {"企业名称","车牌号","车辆颜色","SIM卡号","终端ID","终端型号","完整里程（km）","总里程（km）","轨迹完整率","日期"};
            String fileName = ExcelExport.exportExcelFile(title,list,arrs,staticFilePath);
            return R.data(fileName);

        }catch (Exception e){
            e.printStackTrace();
            log.error("导出轨迹不完整车辆的轨迹明细失败",e);
            return R.fail("导出轨迹不完整车辆的轨迹明细失败");
        }
    }

}
