package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.response.DriftDetailResponse;

import java.util.List;

/**
 * <p>
 * 漂移表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
public interface IStatDriftService extends IService<StatDrift> {

    /**
     * @description: 统计指定日期的车辆漂移情况
     * day 格式为 yyyyMMdd
     * @author: zhouxw
     * @date: 2022/9/1 10:28 AM
     * @param: [day 指定的定位数据日期]
     * @return: boolean
     **/
    boolean locationDriftStat(String day) throws Exception;

    /**
     * @description: 根据给定的车辆定位数据列表计算漂移
     * @author: zhouxw
     * @date: 2022/9/5 8:32 AM
     * @param: []
     * @return: boolean
     **/
    DriftDataNode locationDriftStatByPositionList(List<LocationKudu> list);

    /**
     * @description: 查询车辆漂移数量和在线数量
     * @author: zhouxw
     * @date: 2022/9/9 8:57 AM
     * @param: [param]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DriftDataNode>
     **/
    List<DriftAndGoOnlineCountNode> findDriftAndOnlineCount(RateParam param);

    /**
     * @description: 查询漂移车辆明细
     * @author: zhouxw
     * @date: 2022/9/9 12:18 PM
     * @param: [param]
     * @return: com.baomidou.mybatisplus.core.metadata.IPage<java.util.List<com.xh.vdm.statistic.vo.response.DriftDetailResponse>>
     **/
    IPage<DriftDetailResponse> findDriftDetailList(DetailParam param);

    /**
     * @description: 根据企业id查询漂移车辆数
     * @author: zhouxw
     * @date: 2022/9/13 6:13 PM
     * @param: [month, deptIds]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
     **/
    List<VehicleCountWithDept> findDriffCountByDeptId(String month , List<Long> deptIds , Long ownerId);

	/**
	 * @description: 根据企业id查询漂移车辆数
	 * 如果不指定企业id，就查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 6:13 PM
	 * @param: [month, deptIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	VehicleCountWithDept findDriftCountByDeptIdDeptOrArea(String month , Long deptId , Long ownerId);

	/**
	 * 根据日期统计每天的漂移车辆数
	 * @param dateList
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	List<DateAndCount> findDriftCountByDate(List<String> dateList, List<Long> deptIds, Long userId) throws Exception;

	/**
	 * 查询车辆漂移次数
	 * @param request
	 * @return
	 */
	List<VehicleAndCount> findDriftCount(CommonBaseCrossMonthRequest request);

}
