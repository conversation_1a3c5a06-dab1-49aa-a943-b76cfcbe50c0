package com.xh.vdm.statistic.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TrackAux {
	/**
	 * 定位类型
	 * 0 WGS84坐标（无偏，不加密）
	 * 1 GCJ02坐标（偏移，加密）
	 * 2 LBS定位
	 * 3 惯导
	 * 4 蓝牙信标
	 * 5 WIFI定位
	 */
	@JSONField(name = "location_type")
	private int locationType;

	/**
	 * 电池余量，0到100，100为满电
	 */
	@JSONField(name = "battery_level")
	private byte batteryLevel;

	/**
	 * 心率数据
	 */
	@JSONField(name = "heart_rate")
	private Integer heartRate;

	/**
	 * 脉搏血氧饱和度
	 */
	@JSONField(name = "blood_oxygen")
	private Integer bloodOxygen;
	/**
	 * 蓝牙信标编号，蓝牙信标数据由6字节MAC地址+1字节信号强度+1字节电量组成，多组数据间用分号“;”分割
	 */
	@JSONField(name = "bluetooth")
	private String bluetooth;
	/**
	 * 事件定义类型
	 * 1 休眠
	 * 2 开机
	 * 3 手动关机
	 */
	@JSONField(name = "event_type")
	private Integer eventType;

	//通信信号强度
	@JSONField(name = "wireless")
	private byte wireless;
	//可见卫星颗数
	@JSONField(name = "sate_num")
	private byte sateNum;
}
