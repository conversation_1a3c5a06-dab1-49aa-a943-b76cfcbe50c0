package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 驾驶员驾驶情况表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-16
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatDriveStatus implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 统计日期
     */
    private String statDate;

    /**
     * 驾驶员姓名
     */
    private String driverName;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 驾驶时长(秒)
     */
    private Long driveDuration;

    /**
     * 驾驶里程(米)
     */
    private Double driveMileage;

    /**
     * 数据创建日期
     */
    private Date createDate;

    /**
     * 驾驶时段列表
     */
    private String durationList;

	//针对跨天数据，a 后期跑批追加的数据
	private String state;


}
