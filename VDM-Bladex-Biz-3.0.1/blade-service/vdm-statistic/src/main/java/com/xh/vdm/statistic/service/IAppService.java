package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.VehicleBaseWithId;
import com.xh.vdm.statistic.vo.request.VehicleReportRequest;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import com.xh.vdm.statistic.vo.response.WxStatResponse;

import java.util.List;
import java.util.Map;

/**
 * 移动端service
 */
public interface IAppService {

	/**
	 * 获取统计信息
	 * @param user
	 * @return
	 * @throws Exception
	 */
	WxStatResponse findWxStatInfo(BladeUser user) throws Exception;

	/**
	 * 根据部门、车牌号查询车辆列表，车牌号支持模糊查询
	 * @param deptId
	 * @param licencePlate
	 * @return
	 * @throws Exception
	 */
	List<VehicleBaseWithId> findVehicleByDeptIdAndLicencePlate(Long deptId, String licencePlate, BladeUser user) throws Exception;


	/**
	 * 查询车辆报表
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 * @throws Exception
	 */
	Map<String,Object> findVehicleReportInfo(VehicleReportRequest request, Query query, BladeUser user) throws Exception;

}
