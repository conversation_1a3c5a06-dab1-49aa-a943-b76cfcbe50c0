package com.xh.vdm.statistic;

import org.mybatis.spring.annotation.MapperScan;
import org.springblade.common.constant.ApplicationConstant;
import org.springblade.core.cloud.client.BladeCloudApplication;
import org.springblade.core.launch.BladeApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 兵通统计报表模块
 * <AUTHOR>
 */
@MapperScan("com.xh.vdm.statistic.mapper")
@EnableScheduling
@EnableTransactionManagement(proxyTargetClass = true)
@BladeCloudApplication
@EnableFeignClients({"org.springblade","com.xh.vdm"})
//@LoadBalancerClient(name = "nacos", configuration = NacosLoadBalancerConfiguration.class)
public class StatisticsApplication {

    public static void main(String[] args) {
		String suffix = "";
		if(args != null && args.length > 0){
			for(String s : args){
				if(s.contains("suffix")){
					String param = s.split("=")[1].trim();
					suffix = "-" + param;
				}
			}
		}
		String applicationName = ApplicationConstant.APPLICATION_STATISTIC + suffix;
		BladeApplication.run(applicationName, StatisticsApplication.class, args);
    }

}
