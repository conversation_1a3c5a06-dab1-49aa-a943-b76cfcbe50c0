package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.service.BdmDeviceOnlineService;
import com.xh.vdm.statistic.utils.AuthUtils;
import com.xh.vdm.statistic.vo.request.BdmDeviceOnlineRequest;
import com.xh.vdm.statistic.vo.response.DeviceOnlineResponse;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 终端在线记录表
 */
@RestController
@RequestMapping("/device/online")
public class BdmDeviceOnlineController {
	/**
	 * 服务对象
	 */
	@Resource
	private BdmDeviceOnlineService bdmDeviceOnlineService;

	/**
	 * 分页查询
	 *
	 * @param bdmDeviceOnlineRequest 筛选条件
	 * @param query                  分页对象
	 * @return 查询结果
	 */
	//pre_auth_test
	@PostMapping("/list")
	public R<IPage<DeviceOnlineResponse>> queryByPage(@RequestBody BdmDeviceOnlineRequest bdmDeviceOnlineRequest, Query query, BladeUser user) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}

		IPage<DeviceOnlineResponse> iPage = new Page<>(query.getCurrent(), query.getSize());
		List<DeviceOnlineResponse> list = this.bdmDeviceOnlineService.queryByPage(bdmDeviceOnlineRequest, query, user.getUserId());
		iPage.setRecords(list);

		long totalCount = bdmDeviceOnlineService.countOnline(bdmDeviceOnlineRequest, user.getUserId());
		iPage.setTotal(totalCount);
		iPage.setPages(totalCount / query.getSize() +1);
		return R.data(iPage);
	}

}

