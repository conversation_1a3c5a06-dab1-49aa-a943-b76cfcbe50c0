package com.xh.vdm.statistic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.BdmDeviceStatus;
import com.xh.vdm.statistic.vo.response.AlarmResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.mp.support.Query;

import java.util.Date;
import java.util.List;

/**
 * (BdmDeviceLink)表数据库访问层
 */
@Mapper
public interface BdmDeviceStatusMapper extends BaseMapper<BdmDeviceStatus> {

	List<BdmDeviceStatus> queryAll(@Param("query") Query query, @Param("deviceNum") String deviceNum, @Param("targetName") String targetName, @Param("runningStatus") Integer runningStatus, @Param("startTime") Date startT, @Param("endTime") Date endT, @Param("userId") Long userId, @Param("deptIds") String deptIds, @Param("account") String account);

	long count(@Param("deviceNum") String deviceNum, @Param("targetName") String targetName, @Param("runningStatus") Integer runningStatus, @Param("startTime") Date startT, @Param("endTime") Date endT, @Param("userId") Long userId, @Param("deptIds") String deptIds, @Param("account") String account);

	@DS("location")
	List<AlarmResponse> getAlarmPage(@Param("query") Query query, @Param("time") Long time, @Param("deviceId") Long deviceId, @Param("deviceType") Integer deviceType, @Param("typeList") List<Integer> typeList);

	@DS("location")
	long getAlarmCount(@Param("time") Long time, @Param("deviceId") Long deviceId, @Param("deviceType") Integer deviceType, @Param("typeList") List<Integer> typeList);

	@DS("location")
	List<AlarmResponse> getAlarmPageByAction(@Param("query") Query query, @Param("time") Long time, @Param("deviceId") Long deviceId, @Param("deviceType") Integer deviceType, @Param("startTime") long startTime, @Param("typeList") List<Integer> typeList);

	@DS("location")
	long getAlarmPageByActionCount(@Param("time") Long time, @Param("deviceId") Long deviceId, @Param("deviceType") Integer deviceType, @Param("startTime") long startTime, @Param("typeList") List<Integer> typeList);
}

