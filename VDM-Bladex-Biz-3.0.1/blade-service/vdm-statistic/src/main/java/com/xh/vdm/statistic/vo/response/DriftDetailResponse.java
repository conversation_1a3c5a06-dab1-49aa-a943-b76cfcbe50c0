package com.xh.vdm.statistic.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 漂移车辆明细返回对象
 * @Author: zhouxw
 * @Date: 2022/9/9 11:21 AM
 */
@Data
public class DriftDetailResponse {

    //企业名称
    private String deptName;
    //车牌号
    private String licencePlate;
    //车牌颜色
    @JsonProperty("licenceColor")
    private String plateColor;
    //SIM卡号
    private String simId;
    //终端ID
    private String terminalId;
    //终端型号
    private String terminalModel;
    //漂移次数
    private int driftCount;
    //统计日期
    private String date;

}
