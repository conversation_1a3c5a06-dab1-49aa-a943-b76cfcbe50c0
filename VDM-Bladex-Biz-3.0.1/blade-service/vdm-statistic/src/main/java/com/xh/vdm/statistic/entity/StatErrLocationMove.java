package com.xh.vdm.statistic.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 异常位移统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatErrLocationMove implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
	@ExcelIgnore
	@JsonIgnore
    private Long id;

    /**
     * 车牌号
     */
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"异常位移统计", "车牌号码"},order = 3)
	@ColumnWidth(22)
    private String licencePlate;

    /**
     * 车牌颜色
     */
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(22)
	@ExcelIgnore
    private String licenceColor;


	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"异常位移统计", "车牌颜色"}, order = 2)
	@ColumnWidth(22)
	@JsonIgnore
	private String licenceColorDesc;

	//企业名称
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"异常位移统计", "企业名称"},order = 1)
	@ColumnWidth(22)
	private String deptName;

	//车辆归属
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"异常位移统计", "车辆归属"},order = 5)
	@ColumnWidth(22)
	private String vehicleOwner;

	//行业类型
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
	@ColumnWidth(22)
	private String vehicleUseType;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"异常位移统计", "行业类型"},order = 4)
	@ColumnWidth(22)
	@JsonIgnore
	private String vehicleUseTypeDesc;

	//统计日期
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"异常位移统计", "统计日期"},order = 6)
	@ColumnWidth(22)
	private String statDate;

    /**
     * 开始经度
     */
//	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
//	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
//	@HeadFontStyle(fontHeightInPoints = 12)
//	@ContentFontStyle(fontHeightInPoints = 12)
//	@ExcelProperty(value = {"异常位移统计", "开始经度"},order = 7)
//	@ColumnWidth(22)
//    private Double startLongitude;

    /**
     * 开始纬度
     */
//	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
//	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
//	@HeadFontStyle(fontHeightInPoints = 12)
//	@ContentFontStyle(fontHeightInPoints = 12)
//	@ExcelProperty(value = {"异常位移统计", "开始纬度"},order = 8)
//	@ColumnWidth(22)
//    private Double startLatitude;

    /**
     * 开始位置
     */
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"异常位移统计", "异常位移开始位置"},order = 10)
	@ColumnWidth(30)
    private String startAddr;

	//开始时间
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
	@ColumnWidth(22)
	private Long startTime;

    /**
     * 结束经度
     */
//	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
//	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
//	@HeadFontStyle(fontHeightInPoints = 12)
//	@ContentFontStyle(fontHeightInPoints = 12)
//	@ExcelProperty(value = {"异常位移统计", "结束经度"},order = 11)
//	@ColumnWidth(22)
//    private Double endLongitude;

    /**
     * 结束纬度
     */
//	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
//	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
//	@HeadFontStyle(fontHeightInPoints = 12)
//	@ContentFontStyle(fontHeightInPoints = 12)
//	@ExcelProperty(value = {"异常位移统计", "结束纬度"},order = 12)
//	@ColumnWidth(22)
//    private Double endLatitude;

    /**
     * 结束位置
     */
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"异常位移统计", "异常位移结束位置"},order = 11)
	@ColumnWidth(30)
    private String endAddr;

	//结束时间
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
	@ColumnWidth(22)
	private Long endTime;

    /**
     * 距离
     */
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"异常位移统计", "异常位移距离（米）"},order = 13)
	@ColumnWidth(22)
    private Double distance;

    /**
     * 时长
     */
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
	@ColumnWidth(22)
    private Long duration;

	/**
	 * 时长
	 */
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"异常位移统计", "异常位移定位时长"},order = 9)
	@ColumnWidth(22)
	private String durationFormatStr;

    /**
     * 创建时间
     */
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
	@ColumnWidth(22)
	@JsonIgnore
    private Date createTime;

    /**
     * 类型（暂时不用）
     */
	@JsonIgnore
	@ExcelIgnore
    private String type;

    /**
     * 异常原因（暂时不用）
     */
	@JsonIgnore
	@ExcelIgnore
    private String reason;



	//部门id
	@JsonIgnore
	@ExcelIgnore
	private Long deptId;

	//车辆id
	@JsonIgnore
	@ExcelIgnore
	private Integer vehicleId;

	//车辆归属
	@JsonIgnore
	@ExcelIgnore
	private Long vehicleOwnerId;

}
