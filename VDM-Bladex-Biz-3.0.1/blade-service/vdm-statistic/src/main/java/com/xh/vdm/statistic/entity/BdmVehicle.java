package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-25
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmVehicle implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 车牌号码
     */
    private String licencePlate;

    /**
     * 车牌颜色枚举编码
     */
    private String licenceColor;

    /**
     * 车辆类型枚举编码
     */
    private String vehicleModel;

    /**
     * 车辆使用状态，0-正常 1-维修
     */
    private String vehicleUseType;

    /**
     * 年检时间
     */
    private Date yearCheckTime;

    /**
     * 入网时间
     */
    private Date netSignTime;

    private Date installTime;

    /**
     * 车籍地编码
     */
    private Integer vehicleDomicileCode;

    /**
     * 购车时间
     */
    private Date buyTime;

    /**
     * 发动机号
     */
    private String engineNumber;

    /**
     * 维修时间
     */
    private Date repairTime;

    /**
     * 车辆类型等级
     */
    private String vehicleModelLevel;

    /**
     * 厂牌型号
     */
    private String brandModel;

    /**
     * 燃料类型
     */
    private String fuelType;

    /**
     * 车辆外廓尺寸
     */
    private Integer vehicleCorridorSize;

    /**
     * 载人吨位
     */
    private Integer mannedWeight;

    /**
     * 发动机型号
     */
    private String engineModel;

    private String transportType;

    /**
     * 车架号
     */
    private String vin;

    private String vehicleUrl;

    /**
     * 接驳状态
     */
    private Boolean connectStatus;

    /**
     * 归属单位
     */
    private Long vehicleOwnerId;

    /**
     * 车辆接入方式:1: 直营 2.接入
     */
    private String accessMode;

    private Integer receiveSecurity;

    private Integer serviceState;

    private String operatingStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 隶属车组单位
     */
    private Long deptId;

    /**
     * 绑定的终端
     */
    private Integer terminalId;

    /**
     * 车辆状态
     */
    private Integer vehicleStateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 删除标记：0-未删除，1-已删除
     */
    private Integer isDel;

    /**
     * 创建用户Id
     */
    private Long createUserId;

    private String transCertificateWord;

    private String transCertificateCode;

    private String grantOrgan;

    private Date certificateBeginDate;

    private Date certificateExpireDate;

    private String businessScopeCode;

    private String businessScopeDesc;

    private String certificateType;


}
