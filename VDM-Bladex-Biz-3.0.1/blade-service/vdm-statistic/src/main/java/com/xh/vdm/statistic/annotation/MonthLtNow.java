package com.xh.vdm.statistic.annotation;

import com.xh.vdm.statistic.validator.MonthLessThisMonthValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @description: 给定月份小于当前月份 校验注解
 * @author: zhouxw
 * @date: 2022/9/20 5:11 PM
 * @param:
 * @return:
 **/
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = MonthLessThisMonthValidator.class)
public @interface MonthLtNow {
    String message() default "只能查询本月之前的数据";
    Class<?>[] groups() default {};
    Class<? extends Payload>[] payload() default {};
}
