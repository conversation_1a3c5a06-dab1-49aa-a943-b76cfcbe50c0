package com.xh.vdm.statistic.vo.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Description: 数据统计率请求对象
 * @Author: zhouxw
 * @Date: 2022/9/6 8:21 AM
 */
@Data
public class RateRequest {

    //统计月份：格式 yyyy-MM
    @NotEmpty(message = "统计月份不能为空")
    private String month;

    //上级平台
    @NotNull(message = "上级平台不能为空")
    private Long ownerId;

    //企业名称
    //@NotNull(message = "企业名称不能为空")
    private Long deptId;
}
