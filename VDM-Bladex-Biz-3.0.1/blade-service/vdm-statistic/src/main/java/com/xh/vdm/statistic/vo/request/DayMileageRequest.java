package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.vo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 日里程统计请求类
 * <AUTHOR>
 * @date 2021/11/1 10:19
 */
@ApiModel(value = "日里程统计请求类")
@Data
public class DayMileageRequest extends CommonBaseRequest {

	//统计日期列表
    private List<String> dateList;
}
