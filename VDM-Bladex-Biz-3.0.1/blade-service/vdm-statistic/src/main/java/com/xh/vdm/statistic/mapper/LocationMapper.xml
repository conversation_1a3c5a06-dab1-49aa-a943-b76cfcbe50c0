<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.LocationMapper">

    <select id="getUploadLicencePlateListByDay" resultType="com.xh.vdm.bd.entity.VehicleBase">
        select distinct licence_plate licencePlate, licence_color licenceColor from locations where valid in (1,2) and loc_time >= #{startTime} and loc_time &lt; #{endTime}
    </select>


    <select id="getUploadLocationListByDay" resultType="com.xh.vdm.bd.entity.LocationKudu">
        select * from locations
        where valid in (1,2) and licence_plate = #{licencePlate,jdbcType=VARCHAR} and licence_color = #{licenceColor}
        and loc_time >= #{startTime} and loc_time &lt; #{endTime} order by loc_time
    </select>


    <select id="getAllUploadLocationListByDay" resultType="com.xh.vdm.bd.entity.LocationKudu">
        select * from locations
        where 1 = 1 and licence_plate = #{licencePlate,jdbcType=VARCHAR} and licence_color = #{licenceColor}
          and loc_time >= #{startTime} and loc_time &lt; #{endTime} order by loc_time
    </select>

    <select id="getLocationByAlarmIdListInThreeMinutes" resultType="com.xh.vdm.statistic.entity.AlarmLocation">
        select alarm.alarm_id , loc.* from locations loc,
        (select id alarm_id, licence_plate, licence_color, alarm_time, (alarm_time + 180) limit_time from alarms where id in
        <foreach collection="alarmIdList" item="alarmId" separator="," open="(" close=")">
            #{alarmId}
        </foreach>
        ) alarm
        where loc.licence_plate = alarm.licence_plate and cast(loc.licence_color as string)  = alarm.licence_color and loc.loc_time &lt;= alarm.limit_time
        and loc.loc_time >= alarm.alarm_time
        and loc.loc_time >= #{startTime,jdbcType=BIGINT} and loc.loc_time &lt;= #{endTime,jdbcType=BIGINT}
    </select>

    <select id="getUploadLocationListById" resultType="com.xh.vdm.bd.entity.LocationKudu">
        select * from locations
        where valid in (1, 2 ) and id in (#{idList,jdbcType=VARCHAR}) order by loc_time
    </select>

    <select id="getLocationByVehicleAndId" resultType="com.xh.vdm.bd.entity.LocationKudu">
        select * from locations
        where valid in (1, 2 ) and id in (#{idList,jdbcType=VARCHAR}) and licence_plate = #{licencePlate,jdbcType=VARCHAR} and licence_color = #{licenceColor} order by loc_time
    </select>

    <select id="getLocationCountByCondition" resultType="long">
        select count(*) from locations
        where loc_time >= #{startTime} and loc_time &lt;#{endTime} and licence_plate = #{licencePlate,jdbcType=VARCHAR} and licence_color = #{licenceColor}
    </select>

    <select id="getStartLocationsByVehicleListAndDuration" resultType="com.xh.vdm.statistic.entity.VehicleAndDate">
        select licence_plate, licence_color, from_unixtime(min(loc_time)) dateStr from locations
        where valid in (1,2) and loc_time >= #{startTime,jdbcType=BIGINT} and loc_time &lt;= #{endTime,jdbcType=BIGINT}
        and concat(licence_plate,'~',cast(licence_color as string)) in (
            <foreach collection="vehicleList" item="vehicleKey" separator=",">
                #{vehicleKey}
            </foreach>
        )
        group by licence_plate, licence_color
    </select>

    <select id="getEndLocationsByVehicleListAndDuration" resultType="com.xh.vdm.statistic.entity.VehicleAndDate">
        select licence_plate, licence_color, from_unixtime(max(loc_time)) dateStr from locations
        where valid in (1,2) and loc_time >= #{startTime,jdbcType=BIGINT} and loc_time &lt;= #{endTime,jdbcType=BIGINT}
        and concat(licence_plate,'~',cast(licence_color as string)) in (
        <foreach collection="vehicleList" item="vehicleKey" separator=",">
            #{vehicleKey}
        </foreach>
        )
        group by licence_plate, licence_color
    </select>

    <select id="getDailyCarLocStart" resultType="java.util.Map">
        select licence_color, licence_plate, from_unixtime(loc_time, 'yyyy-MM-dd') as loc_date, min(loc_time) as loc_start from locations
        where valid in (1, 2) and loc_time &gt;= #{startTime} and loc_time &lt;= #{endTime}
        and concat(licence_plate, '~', cast(licence_color as string)) in
        <foreach collection="vehicleList" item="vehicleKey" separator="," open="(" close=")">
            #{vehicleKey}
        </foreach>
        group by licence_color, licence_plate, from_unixtime(loc_time, 'yyyy-MM-dd')
    </select>

    <select id="getDailyCarLocEnd" resultType="java.util.Map">
        select licence_color, licence_plate, from_unixtime(loc_time, 'yyyy-MM-dd') as loc_date, max(loc_time) as loc_end from locations
        where valid in (1, 2) and loc_time &gt;= #{startTime} and loc_time &lt;= #{endTime}
        and concat(licence_plate, '~', cast(licence_color as string)) in
        <foreach collection="vehicleList" item="vehicleKey" separator="," open="(" close=")">
            #{vehicleKey}
        </foreach>
        group by licence_color, licence_plate, from_unixtime(loc_time, 'yyyy-MM-dd')
    </select>
</mapper>
