<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatVehRunningStateMonthMapper">

    <select id="getVehRunningStateByPage" resultType="com.xh.vdm.statistic.entity.StatVehRunningStateMonth">
        select * from stat_veh_running_state_month
        where month like #{request.month,jdbcType=VARCHAR}
            <if test="request.licencePlate != null and request.licencePlate != ''">
                and licence_plate = #{request.licencePlate,jdbcType=VARCHAR}
            </if>
        and ( dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
        order by ${request.orderField} ${request.orderType}
    </select>

    <select id="getLastPositionInfo" resultType="com.xh.vdm.statistic.vo.response.VehicleLastPositionResponse">
        select bd.dept_name, bvs.licence_plate, bv.access_mode, bvs.licence_color, bv.vehicle_use_type,
        coalesce(btpp.name,'非营运车辆') vehicle_owner, bv.vehicle_model, bvs.speed, bvs.te_state, bvs.loc_addr,
        bvs.loc_time, btlp.name server_name
        from bdm_vehicle_state bvs
         left join bdm_vehicle bv on bvs.licence_plate = bv.licence_plate and bvs.licence_color = bv.licence_color
         left join blade_dept bd on bv.dept_id = bd.id
         left join bam_third_low_platform btlp on btlp.operator_no = bv.center_id::text
        left join bam_third_party_platform btpp on btpp.id = bv.vehicle_owner_id
        where 1 = 1
        <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
            and bv.id in
            <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                #{vehicleId}
            </foreach>
        </if>
        <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
            and ( bv.dept_id in (
            <foreach collection="request.deptList" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
            <if test="request.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="request.startTime != null">
            and bvs.loc_time >= to_timestamp(#{request.startTime})
        </if>
        <if test="request.startTime != null">
            and bvs.loc_time &lt;= to_timestamp(#{request.endTime})
        </if>

        <if test="request.vehicleUseType != null ">
            and bv.vehicle_use_type in
            <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="request.vehicleOwnerId != null">
            and bv.vehicle_owner_id = #{request.vehicleOwnerId}
        </if>
        <if test="request.accessMode != null">
            and bv.access_mode = #{request.accessMode}
        </if>
    </select>

    <select id="getNotLocationInfo" resultType="com.xh.vdm.statistic.vo.response.NotLocationResponse">
        select * from (
        <foreach collection="request.dmList" item="dm" separator=" union ">
            select snl.licence_plate, snl.licence_color, snl.access_mode, snl.vehicle_use_type, snl.stat_date date,
            snl.start_time, snl.end_time, snl.start_addr, snl.end_addr, snl.duration
            , dept_id, vehicle_owner_id,start_longitude,start_latitude,end_longitude,end_latitude,mileage
            from stat_not_location_${dm.month} snl
            where 1 = 1
            and stat_date in
            <foreach collection="dm.dateList" item="date" separator="," open="(" close=")">
                #{date}
            </foreach>
            <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
                and snl.vehicle_id in
                <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                    #{vehicleId}
                </foreach>
            </if>
            <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
                and ( snl.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
                <if test="request.userId != null">
                    or snl.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id =
                    #{request.userId,jdbcType=BIGINT})
                </if>
                )
            </if>
            <if test="request.startTime != null">
                and snl.start_time >= to_timestamp(#{request.startTime})
            </if>
            <if test="request.endTime != null">
                and snl.end_time &lt;= to_timestamp(#{request.endTime})
            </if>

            <if test="request.vehicleUseType != null ">
                and snl.vehicle_use_type in
                <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.vehicleOwnerId != null">
                and snl.vehicle_owner_id = #{request.vehicleOwnerId}
            </if>
            <if test="request.accessMode != null">
                and snl.access_mode = #{request.accessMode}
            </if>
        </foreach>
        ) t order by t.start_time desc
    </select>

    <select id="getErrLocationMoveInfo" resultType="com.xh.vdm.statistic.vo.response.ErrLocationMoveResponse">
        <foreach collection="request.dmList" item="dm" separator=" union " >
            select selm.*, to_timestamp(selm.start_time) start_time_str, to_timestamp(selm.end_time) end_time_str
            from stat_err_location_move_${dm.month} selm
            where 1 = 1
            and selm.stat_date in
            <foreach collection="dm.dateList" item="date" separator="," open="(" close=")">
                #{date}
            </foreach>
            <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
                and selm.vehicle_id in
                <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                    #{vehicleId}
                </foreach>
            </if>
            <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
                and ( selm.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
                <if test="request.userId != null">
                    or selm.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
                </if>
                )
            </if>

            <if test="request.startTime != null">
                and selm.start_time >= #{request.startTime}
            </if>
            <if test="request.endTime != null">
                and selm.end_time &lt;= #{request.endTime}
            </if>

            <if test="request.vehicleUseType != null ">
                and selm.vehicle_use_type in
                <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.vehicleOwnerId != null">
                and selm.vehicle_owner_id = #{request.vehicleOwnerId}
            </if>
            <if test="request.accessMode != null">
                and selm.access_mode = #{request.accessMode}
            </if>
        </foreach>
        order by start_time desc
    </select>

    <select id="getMileageDay" resultType="java.util.LinkedHashMap">
            select sc.licence_plate licencePlate, sc.licence_color licenceColor, bv.access_mode accessMode, bd.dept_name deptName, bv.vehicle_use_type vehicleUseType, coalesce(btpp.name,'非营运车辆') vehicleOwner,
            <foreach collection="dateList" item="date" separator=",">
                coalesce(split_part(split_part(d${date},'#',-2),'#',1),'0')::bigint "${monthLine}-${date}"
            </foreach>
            from stat_complete_${month} sc
            left join bdm_vehicle bv on bv.licence_plate = sc.licence_plate and bv.licence_color = sc.licence_color::text
            left join blade_dept bd on bv.dept_id = bd.id
            left join bam_third_party_platform btpp on bv.vehicle_owner_id = btpp.id
            where 1 = 1
            <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
                and bv.id in
                <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                    #{vehicleId}
                </foreach>
            </if>
            <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
                and ( bv.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
                <if test="request.userId != null">
                    or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
                </if>
                )
            </if>
            <if test="request.vehicleUseType != null ">
                and bv.vehicle_use_type in
                <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.vehicleOwnerId != null">
                and bv.vehicle_owner_id = #{request.vehicleOwnerId}
            </if>
            <if test="request.accessMode != null">
                and bv.access_mode = #{request.accessMode}
            </if>
    </select>

    <select id="getStateInfoByDeptWithAlarm" resultType="com.xh.vdm.statistic.entity.StatVehRunningStateDay">
        select * from stat_veh_running_state_day_${month}
        where 1 = 1
        and (dept_id in (
                <foreach collection="deptIds" item="deptId" separator=",">#{deptId}
                </foreach>
            )
            <if test="vehicleIds != null and vehicleIds.size() > 0">
                or vehicle_id in (
                    <foreach
                        collection="vehicleIds" item="vehicleId" separator=",">#{vehicleId}
                    </foreach>
                )
            </if>
        )
        and total_alarm_count > 0
        and date = #{date}
    </select>


    <select id="getStateInfoByDeptWithAlarmMonth" resultType="com.xh.vdm.statistic.entity.StatVehRunningStateDay">
        select licence_plate, licence_color, sum(total_alarm_count) total_alarm_count, sum(over_speed_count) over_speed_count, sum(tired_count) tired_count, sum(night_driving_count) night_driving_count, sum(active_alarm_count) active_alarm_count, sum(adas_count) adas_count, sum(dsm_count) dsm_count, sum(intense_count) intense_count
        from stat_veh_running_state_day_${month}
        where 1 = 1
        and (dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">#{deptId}
        </foreach>
        )
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or vehicle_id in (
            <foreach
                collection="vehicleIds" item="vehicleId" separator=",">#{vehicleId}
            </foreach>
            )
        </if>
        )
        and total_alarm_count > 0
        group by licence_plate, licence_color
    </select>
</mapper>
