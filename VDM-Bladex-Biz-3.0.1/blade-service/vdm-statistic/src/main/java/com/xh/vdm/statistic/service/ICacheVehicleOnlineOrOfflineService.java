package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.CacheVehicleOnlineOrOfflineResponse;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.request.VehicleOnlineOrOfflineRequest;
import com.xh.vdm.statistic.vo.response.VehicleOnlineOrOfflineResponse;
import org.springblade.core.mp.support.Query;

public interface ICacheVehicleOnlineOrOfflineService extends IService<CacheVehicleOnlineOrOfflineResponse> {

    /**
     * @description: 统计车辆上下线，并入库
     * @author: zhouxw
     * @date: 2023-02-40 15:27:09
     * @param: [request, req]
     * @return: com.xh.vdm.statistic.vo.ObjectRestResponse<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>>
     **/
    void statisticsVehicleOnlineOrOffline(Long startTime, Long endTime, String tenantId) throws Exception;


    /**
     * @description: 查询车辆上下线
     * @author: zhouxw
     * @date: 2023-02-40 16:20:53
     * @param: [request, req]
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    IPage<VehicleOnlineOrOfflineResponse> queryVehicleOnlineOrOfflineCache(CommonBaseRequest request, Query query) throws Exception;


}
