package com.xh.vdm.statistic.entity;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.jetbrains.annotations.NotNull;

/**
 * <p>
 * 车辆每个小时在线时长统计
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatVehOnlineDay implements Serializable, Comparable {

    private static final long serialVersionUID = 1L;

    /**
     * 车牌号
     */
    private String licencePlate;

    /**
     * 车牌颜色
     */
    private String licenceColor;

    /**
     * 统计日期
     */
    private String statDate;

    /**
     * 0时~1时在线时长
     */
	private Long m00;

    private Long m01;

    private Long m02;

    private Long m03;

    private Long m04;

    private Long m05;

    private Long m06;

    private Long m07;

    private Long m08;

    private Long m09;

    private Long m10;

    private Long m11;

    private Long m12;

    private Long m13;

    private Long m14;

    private Long m15;

    private Long m16;

    private Long m17;

    private Long m18;

    private Long m19;

    private Long m20;

    private Long m21;

    private Long m22;

    private Long m23;

	//总在线时长
	private Long totalDuration;


    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据更新时间
     */
    private Date updateTime;

	@Override
	public int compareTo(@NotNull Object o) {
		StatVehOnlineDay so = (StatVehOnlineDay) o;
		if(this.getLicencePlate().hashCode() == so.getLicencePlate().hashCode()){
			return this.getLicenceColor().hashCode() - so.getLicenceColor().hashCode();
		}
		return this.getLicencePlate().hashCode() - so.getLicencePlate().hashCode();
	}
}
