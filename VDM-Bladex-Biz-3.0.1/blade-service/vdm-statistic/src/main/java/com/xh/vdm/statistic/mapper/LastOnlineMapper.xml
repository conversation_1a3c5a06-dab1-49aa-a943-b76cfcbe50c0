<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xh.vdm.statistic.mapper.LastOnlineMapper">

    <insert id="insertFromAll">
        insert into bdm_last_online_record (vehicle_id, licence_color, licence_plate, start_time, end_time)
        select vehicle_id, licence_color, licence_plate, on_line_time, off_line_time from bdm_terminalonlinerecord
        where off_line_time &gt;= #{startTime} and off_line_time &lt;= #{endTime}
    </insert>

    <delete id="truncate">
        truncate table bdm_last_online_record
    </delete>
</mapper>
