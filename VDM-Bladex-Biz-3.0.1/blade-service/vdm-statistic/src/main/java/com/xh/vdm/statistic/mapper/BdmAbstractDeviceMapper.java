package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.BdmAbstractDevice;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (BdmAbstractDevice)表数据库访问层
 */
public interface BdmAbstractDeviceMapper extends BaseMapper<BdmAbstractDevice> {

	/**
	 * 获取可以访问的终端列表
	 * @param account
	 * @return
	 */
	List<BdmAbstractDevice> getAccessDeviceList(@Param("account") String account);

}

