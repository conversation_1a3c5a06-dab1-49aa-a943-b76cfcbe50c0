package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.StatEmailHistory;
import com.xh.vdm.statistic.vo.request.EmailRequest;
import com.xh.vdm.statistic.vo.response.EmailHistoryResponse;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-02
 */
public interface StatEmailMapper extends BaseMapper<StatEmailHistory> {

    /**
     * @description: 查询邮件发送历史
     * @author: zhouxw
     * @date: 2023-03-61 11:45:53
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatEmailHistory>
     **/
    List<EmailHistoryResponse> getEmailHistoryList (EmailRequest request);

    /**
     * @description: 分页查询邮件发送历史
     * @author: zhouxw
     * @date: 2023-03-61 18:31:55
     * @param: [page, request]
     * @return: com.baomidou.mybatisplus.core.metadata.IPage<com.xh.vdm.statistic.entity.StatEmailHistory>
     **/
    IPage<EmailHistoryResponse> getEmailHistoryList(IPage<EmailHistoryResponse> page, EmailRequest request);
}
