package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.StatTaskLog;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 定时任务执行日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
public interface StatTaskLogMapper extends BaseMapper<StatTaskLog> {

    /**
     * @description: 记录跑批任务过程信息
     * @author: zhouxw
     * @date: 2022/9/15 3:13 PM
     * @param: [taskType, content]
     * @return: void
     **/
    void insertTaskProcessLog(@Param("taskType") String taskType , @Param("content") String content);
}
