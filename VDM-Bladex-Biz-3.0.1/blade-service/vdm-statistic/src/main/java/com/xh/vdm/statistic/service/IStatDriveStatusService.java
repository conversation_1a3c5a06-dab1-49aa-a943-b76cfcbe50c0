package com.xh.vdm.statistic.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.wx.TotalMileageAndDurationInMonth;

import java.util.List;

/**
 * <p>
 * 驾驶员驾驶情况表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-16
 */
public interface IStatDriveStatusService extends IService<StatDriveStatus> {

    /**
     * @description: 通过人脸识别获取驾驶时段
     * @author: zhouxw
     * @date: 2022/11/16 11:13 PM
     * @param: [date]
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatDriveStatus>
     **/
    List<DriveDuration> findDriveDurationByFace(String date) throws Exception ;


    /**
     * @description: 通过人脸识别获取驾驶时段，指定驾驶员和日期
     * @author: zhouxw
     * @date: 2022/11/16 11:13 PM
     * @param: [date]
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatDriveStatus>
     **/
    List<DriveDuration> findDriveDurationByFaceWithDateAndIdCard(String date, String idCard) throws Exception ;


    /**
     * @description: 通过IC卡获取驾驶时段
     * @author: zhouxw
     * @date: 2022/11/16 11:14 PM
     * @param: [date]
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatDriveStatus>
     **/
    List<DriveDuration> findDriveDurationByIC(String date) throws Exception;

    /**
     * @description: 通过IC卡获取驾驶时段，指定日期和驾驶员
     * @author: zhouxw
     * @date: 2022/11/16 11:14 PM
     * @param: [date]
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatDriveStatus>
     **/
    List<DriveDuration> findDriveDurationByICWithDateAndIdCard(String date, String idCard) throws Exception;


    /**
     * @description: 统计驾驶情况
     * @author: zhouxw
     * @date: 2022/11/16 11:35 PM
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatDriveStatus>
     **/
    void statDriverStatus(String date) throws Exception ;


    /**
     * @description: 统计驾驶情况，指定时间和驾驶员
     * @author: zhouxw
     * @date: 2022/11/16 11:35 PM
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatDriveStatus>
     **/
    DriveDurationBaseInfo statDriverStatusByDateAndIdCard(String date , String idCard) throws Exception ;

    /**
     * @description: 统计驾驶员在某个月的驾驶情况
     * @author: zhouxw
     * @date: 2022/11/19 3:57 PM
     * @param: [month: yyyy-MM, idCard]
     * @return: com.xh.vdm.statistic.entity.DriveInfo
     **/
    DriveInfo statDriveInfo(String month , String idCard) throws Exception;

    /**
     * @description: 查询驾驶状态信息列表：驾驶时长、驾驶里程
     * @author: zhouxw
     * @date: 2022/11/19 9:12 PM
     * @param: [month：yyyy-MM, idCard]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DriveDurationBaseInfo>
     **/
    List<DriveDurationBaseInfo> findDriveStatusList(String month , String idCard) throws Exception;

	/**
	 * 查询指定驾驶员在指定月份中每天的驾驶里程和驾驶时长
	 * @param month yyyy-MM
	 * @param idCard
	 * @return
	 * @throws Exception
	 */
	List<DateAndMileageAndDuration> findMileageAndDurationEveryDayInMonth(String month, String idCard) throws Exception;

    /**
     * @description: 查询驾驶员一个月内的驾驶天数
     * 如果不指定企业deptId，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 7:41 PM
     * @param: [month: yyyyMM, deptId]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DeptAndIdCardAndCount>
     **/
    List<DeptAndIdCardAndCount> findDriverDriveDaysInMonth(String month, Long deptId) throws Exception;

    /**
     * @description: 查询驾驶员驾驶的总天数
     * 如果不指定企业deptId，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/28 1:33 AM
     * @param: [month, deptId]
     * @return: int
     **/
    int findTotalDriveDaysInMonth(String month, Long deptId) throws Exception;

    /**
     * @description: 查询驾驶员一个月内某时间段的驾驶天数
     * @author: zhouxw
     * @date: 2022/11/20 7:41 PM
     * @param: [month: yyyyMM, deptId , startDate:yyyy-MM-dd , endDate: yyyy-MM-dd]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DeptAndIdCardAndCount>
     **/
    List<DeptAndIdCardAndCount> findDriverDriveDaysInMonthDuration(String month, Long deptId , String startDate , String endDate) throws Exception;


	/**
	 * @description: 查询驾驶员一个月内的驾驶天数，指定驾驶员
	 * @author: zhouxw
	 * @date: 2022/11/20 7:41 PM
	 * @param: [month: yyyy-MM]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DeptAndIdCardAndCount>
	 **/
	int findDriveDaysInMonthDuration(String idCard, String month) throws Exception;


	/**
     * @description: 查询驾驶员一个月内每天的驾驶里程
     * @author: zhouxw
     * @date: 2022/11/20 10:14 PM
     * @param: [deptId, month: yyyyMM, startDate: yyyy-MM-dd, endDate: yyyy-MM-dd]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DeptAndDriveStatus>
     **/
    List<DeptAndDriveStatus> findDriverDriveMileageEveryDay(Long deptId ,String month ,  String startDate , String endDate) throws Exception;

    /**
     * @description: 查询企业在指定月的总里程
     * 如果不指定企业，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 10:39 PM
     * @param: [deptId, month]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndData>
     **/
    List<DateAndData> findCompanyTotalMileageInMonth(Long deptId , String month) throws Exception;


    /**
     * @description: 查询企业在指定月的总里程
     * 如果不指定企业，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 10:39 PM
     * @param: [deptId, month]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndData>
     **/
    double findTotalMileageInMonth(Long deptId , String month) throws Exception;

    /**
     * @description: 查询指定企业的每天的总时长
     * 如果不指定企业，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 11:54 PM
     * @param: [deptId, startDate: yyyy-MM-dd, endDate: yyyy-MM-dd]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DeptAndDateAndData>
     **/
    List<DeptAndDateAndData> findTotalDurationEveryDay(Long deptId , String startDate , String endDate) throws Exception;

    /**
     * @description: 查询每天驾驶员的总数
     * @author: zhouxw
     * @date: 2022/11/21 12:33 AM
     * @param: [deptId, month, startDate, enDate]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
     **/
    List<DateAndCount> findDriverCountEveryDay(Long deptId , String month ,String startDate ,String enDate) throws Exception;

    /**
     * @description: 查询指定日期的出勤驾驶员数量
     * 如果不指定日期 date，则查询全月
     * 如果不指定企业 deptId， 则查询全区域
     * @author: zhouxw
     * @date: 2022/11/27 12:42 AM
     * @param: [month: yyyyMM, date:yyyy-MM-dd]
     * @return: int
     **/
    int findDriverCountByDate(String month , String date , Long deptId) throws Exception;

    /**
     * @description: 获取驾驶员驾驶总时长
     * * 如果不指定日期 date ，则查询全月
     *      * 如果不指定企业 deptId ，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/27 1:22 AM
     * @param: [month: yyyyMM, date:yyyy-MM-dd, deptId]
     * @return: double
     **/
    double findTotalDriveDurationByDate(String month , String date , Long deptId) throws Exception;

    /**
     * @description: 获取企业驾驶员总驾驶里程
     * @author: zhouxw
     * @date: 2022/11/27 1:32 AM
     * @param: [month：yyyyMM, date: yyyy-MM-dd, deptId]
     * @return: double
     **/
    double findTotalDriveMileageByDate(String month , String date , Long deptId) throws Exception;

    /**
     * @description: 获取车辆最新的驾驶员
     * @author: zhouxw
     * @date: 2023-02-55 10:37:49
     * @param: [licencePlate]
     * @return:
     * @return: null
     **/
    DriverFaceResultNode findNewestDriverWithFaceByLicencePlate(String licencePlate) throws Exception;

	/**
	 * 根据身份证号查询驾驶信息
	 * @param idCard
	 * @param month yyyy-MM
	 * @param statDate yyyy-MM-dd
	 * @return
	 * @throws Exception
	 */
	List<StatDriveStatus> findDriveStatusByIdCard (String idCard,String statDate, String month) throws Exception;


	/**
	 * 查询驾驶员指定月份的总驾驶时长和驾驶里程
	 * @param idCard
	 * @param month
	 * @return
	 * @throws Exception
	 */
	TotalMileageAndDurationInMonth findDriveStatusByIdCardMonth (String idCard, String month) throws Exception;

}
