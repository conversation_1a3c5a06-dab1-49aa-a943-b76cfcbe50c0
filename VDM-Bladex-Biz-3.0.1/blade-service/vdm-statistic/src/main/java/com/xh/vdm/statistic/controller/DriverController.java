package com.xh.vdm.statistic.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.wx.DriverDriveBaseMonth;
import com.xh.vdm.statistic.entity.wx.DriverDriveBaseToday;
import com.xh.vdm.statistic.entity.wx.TotalMileageAndDurationInMonth;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.DistanceUtils;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.utils.VdmUserInfoUtil;
import com.xh.vdm.statistic.vo.request.DriverDurationRequest;
import com.xh.vdm.statistic.vo.response.DriveStatusAndAlarm;
import com.xh.vdm.statistic.vo.response.DriverActiveInfo;
import com.xh.vdm.statistic.vo.response.DriverBaseInfo;
import com.xh.vdm.statistic.vo.response.DriverDurationResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @Description: 驾驶员画像
 * @Author: zhouxw
 * @Date: 2022/11/18 3:32 PM
 */
@RestController
@RequestMapping("/bt/statistics/driver")
@Slf4j
public class DriverController {


    @Resource
    private IBdmVehicleDriverService service;

    @Resource
    private IStatDriveStatusService driveStatusService;

    @Resource
    private IBdmSecurityService securityService;

    @Resource
    private IStatDriverDurationService driverDurationService;

	@Resource
	private IBdmVehicleStateService vehicleStateService;

	@Resource
	private IBdmPersonService bdmPersonService;

	@Resource
	private IImpalaAlarmService impalaAlarmService;

	@Resource
	private RedisTemplate<String,HashMap> redisTemplate;

    private static final ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    private static final ThreadLocal<SimpleDateFormat> sdfHolderShort = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));


	/**
	 * 查询驾驶员姓名和车辆状态
	 * @param licencePlate
	 * @param licenceColor
	 * @return
	 */
	@GetMapping("/driverAndVehicleState")
	public R<DriverNameAndVehicleState> driverAndVehicleState( String licencePlate, String licenceColor){
		if(StringUtils.isEmpty(licencePlate) || StringUtils.isEmpty(licenceColor)){
			return R.fail("车牌号、车牌颜色不能为空");
		}
		try{
			//1.查询实时驾驶员
			String key = licencePlate + "~" + licenceColor;
			Object obj = redisTemplate.opsForHash().get(CommonConstant.CACHE_DRIVER_INFO, key);
			String idCard = "";
			BdmPerson person = null;
			JSONObject json = null;
			if(obj != null){
				json = JSON.parseObject(JSON.toJSONString(obj));
				idCard = json.getString("idCard");
				if(StringUtils.isEmpty(idCard)){
					//如果idCard为空，则可能为2013协议，则取credential的数据
					idCard = json.getString("credential");
				}
				if(!StringUtils.isEmpty(idCard)){
					//查询数据库，获取人员信息
					List<BdmPerson> personList = bdmPersonService.findPersonByIdCard(idCard);
					if(personList != null && personList.size() > 0){
						log.info("根据身份证号查询人员信息，根据[{}]查询到多条数据",idCard);
						person = personList.get(0);
					}
				}
			}

			//2.查询车辆状态
			String vehicleState = "";
			BdmVehicleState bvs = vehicleStateService.getOne(Wrappers.lambdaQuery(BdmVehicleState.class).eq(BdmVehicleState::getLicencePlate, licencePlate).eq(BdmVehicleState::getLicenceColor, licenceColor));
			if(bvs == null || bvs.getTeState() == 0){
				vehicleState = "离线";
			}else{
				if(bvs.getSpeed() > 0){
					vehicleState = "行驶";
				}else{
					vehicleState = "停驶";
				}
			}
			DriverNameAndVehicleState response = new DriverNameAndVehicleState();
			//车辆状态
			response.setVehicleState(vehicleState);
			//人员信息
			if(person != null){
				response.setDriverName(person.getName());
				response.setSex(person.getSex());
				response.setPhone(person.getPhone());
				response.setVehicleType(person.getVehicleType());
				response.setCertificateType(person.getCertificateType());
				response.setIdCard(person.getIdCard());
				response.setPhotoUrl(person.getPhotoUrl());
			}else{
				if(json != null){
					response.setDriverName(json.getString("driverName"));
					response.setIdCard(json.getString("idCard"));
				}
			}
			return R.data(response);
		}catch (Exception e){
			log.error("查询失败",e);
			return R.fail("查询失败");
		}
	}


    /**
     * @description: 查询驾驶员基本信息
     * @author: zhouxw
     * @date: 2022/11/18 4:14 PM
     * @param: idCardOrPhone 身份证号或者手机号
     * @return: com.xh.vdm.statistic.utils.R<com.xh.vdm.statistic.vo.response.DriverBaseInfo>
     **/
    @GetMapping("/driverBaseInfo")
    public R<DriverBaseInfo> driverBaseInfo(String idCardOrPhone){
        //查询条件不能为空
        if(StringUtils.isBlank(idCardOrPhone)){
            return R.fail("查询条件不能为空");
        }
        DriverBaseInfo baseInfo = new DriverBaseInfo();
        try{
			//查询驾驶员
			List<BdmPerson> personList = bdmPersonService.findPersonByIdCardOrPhone(idCardOrPhone,CommonConstant.DICT_KEY_JOB_TYPE_DRIVER);
			if(personList == null || personList.size() < 1){
				log.error("查询不到驾驶员信息[idCardOrPhone:"+idCardOrPhone+"]");
				return R.fail("该驾驶员不存在");
			}
			if(personList.size() > 1){
				log.error("查询到多条驾驶员信息，将选取第一条[idCardOrPhone:"+idCardOrPhone+"]");
			}
			BdmPerson driver = personList.get(0);
            baseInfo.setAge(DateUtil.yearCountBetweenSecondTimestamp(driver.getBirthdate().getTime()/1000 , new Date().getTime()/1000));
            baseInfo.setCertId(driver.getCertificateId());
            baseInfo.setName(driver.getName());
            baseInfo.setPhotoUrl(driver.getPhotoUrl());
            baseInfo.setIdCard(driver.getIdCard());
			if(driver.getLicenceGetDate() != null){
				int driverAge = DateUtil.yearCountBetweenSecondTimestamp(driver.getLicenceGetDate().getTime()/1000 , new Date().getTime()/1000);
				baseInfo.setDriveAga(driverAge+"");
			}else{
				baseInfo.setDriveAga("");
			}

            if(driver.getCertificateExpire() == null){
                baseInfo.setExpirationDate("");
            }else{
                baseInfo.setExpirationDate(sdfHolder.get().format(driver.getCertificateExpire()));
            }

        }catch (Exception e){
            log.error("查询驾驶员基本信息失败",e);
            return R.fail("查询驾驶员基本信息失败");
        }
        return R.data(baseInfo);
    }

	/**
	 * 当日驾驶信息
	 * @param idCardOrPhone
	 * @param user
	 * @return
	 */
	@GetMapping("/driveInfoToday")
	public R<DriverDriveBaseToday> driveBaseToday(String idCardOrPhone, BladeUser user){
		if(user == null){
			log.error("用户未登录或未授权");
			return R.fail("用户未登录或未授权");
		}
		//查询条件不能为空
		if(StringUtils.isBlank(idCardOrPhone)){
			return R.fail("查询条件不能为空");
		}

		try{
			BdmPerson driver = null;
			//查询驾驶员
			List<BdmPerson> personList = bdmPersonService.findPersonByIdCardOrPhone(idCardOrPhone,CommonConstant.DICT_KEY_JOB_TYPE_DRIVER);
			if(personList == null || personList.size() < 1){
				log.error("查询不到驾驶员信息[idCardOrPhone:"+idCardOrPhone+"]");
				return R.fail("该驾驶员不存在");
			}
			if(personList.size() > 1){
				log.error("查询到多条驾驶员信息，将选取第一条[idCardOrPhone:"+idCardOrPhone+"]");
			}
			driver = personList.get(0);
			if(driver == null){
				return R.fail("未查询到驾驶员信息");
			}
			//1.查询当日里程和驾驶时长
			String month = DateUtil.getDateString().substring(0,7);
			String statDate = DateUtil.getDateString();
			List<StatDriveStatus> driveList = driveStatusService.findDriveStatusByIdCard(driver.getIdCard(), statDate, month);
			if(driveList == null || driveList.size() < 1){
				log.error("根据身份证号{}未查询到驾驶信息",driver.getIdCard());
				return R.fail("未查询到当日驾驶信息");
			}
			StatDriveStatus driveStatus = null;
			if(driveList.size() > 0){
				log.error("根据身份证号{}查询到多条驾驶信息，将选取第一条",driver.getIdCard());
				driveStatus = driveList.get(0);
			}
			Double mileage = driveStatus.getDriveMileage();
			long duration = driveStatus.getDriveDuration();

			//2.查询当日报警信息
			//获取驾驶车辆及驾驶时段
			List<StatDriverDuration> durationList = driverDurationService.findDriverDurationListByIdCard(driver.getIdCard(), statDate, month);
			//对每个驾驶时段查询报警数量
			List<Integer> alarmTypes = new ArrayList<>();
			alarmTypes.addAll(VdmUserInfoUtil.getFatigueAlarmType());
			alarmTypes.addAll(VdmUserInfoUtil.getOverSpeedAlarmType());
			alarmTypes.addAll(VdmUserInfoUtil.getAdasAlarmType());
			alarmTypes.addAll(VdmUserInfoUtil.getDsmAlarmType());
			List<AlarmTypeAndCount> countList = impalaAlarmService.findVehicleAlarmCountInfoByDriverDuration(durationList, alarmTypes);
			//计算报警数量
			AtomicReference<Long> fatigueAlarmCount = new AtomicReference<>(0L);
			AtomicReference<Long> overSpeedAlarmCount = new AtomicReference<>(0L);
			AtomicReference<Long> adasAlarmCount = new AtomicReference<>(0L);
			AtomicReference<Long> dsmAlarmCount = new AtomicReference<>(0L);
			countList.forEach(item -> {
				int alarmType = Integer.parseInt(item.getAlarmType());
				if(VdmUserInfoUtil.getOverSpeedAlarmType().contains(alarmType)){
					//如果是超速报警
					overSpeedAlarmCount.getAndSet(overSpeedAlarmCount.get() + 1);
				}else if(VdmUserInfoUtil.getFatigueAlarmType().contains(alarmType)){
					//如果是疲劳报警
					fatigueAlarmCount.getAndSet(fatigueAlarmCount.get() + 1);
				}else if(VdmUserInfoUtil.getAdasAlarmType().contains(alarmType)){
					//adas报警
					adasAlarmCount.getAndSet(adasAlarmCount.get() + 1);
				}else if(VdmUserInfoUtil.getDsmAlarmType().contains(alarmType)){
					//dsm报警
					dsmAlarmCount.getAndSet(dsmAlarmCount.get() + 1);
				}
			});
			DriverDriveBaseToday dd = new DriverDriveBaseToday();
			dd.setDriveMileage(MathUtil.roundDouble(mileage/1000,2));
			dd.setDriveDuration(DateUtil.getFormatDateString(duration));
			dd.setOverSpeedAlarmCount(overSpeedAlarmCount.get());
			dd.setFatigueAlarmCount(fatigueAlarmCount.get());
			dd.setAdasAlarmCount(adasAlarmCount.get());
			dd.setDsmAlarmCount(dsmAlarmCount.get());
			return R.data(dd);
		}catch (Exception e){
			log.error("查询失败",e);
			return R.fail("查询失败");
		}
	}

	/**
	 * 统计当月驾驶情况
	 * @param idCardOrPhone
	 * @param user
	 * @return
	 */
	@GetMapping("/driveInfoMonth")
	public R<DriverDriveBaseMonth> driveBaseMonth(String idCardOrPhone, BladeUser user){
		if(user == null){
			log.error("用户未登录或未授权");
			return R.fail("用户未登录或未授权");
		}
		//查询条件不能为空
		if(StringUtils.isBlank(idCardOrPhone)){
			return R.fail("查询条件不能为空");
		}

		try{
			BdmPerson driver = null;
			//根据手机号查询驾驶员
			List<BdmPerson> personList = bdmPersonService.findPersonByIdCardOrPhone(idCardOrPhone,CommonConstant.DICT_KEY_JOB_TYPE_DRIVER);
			if(personList == null || personList.size() < 1){
				log.error("查询不到驾驶员信息[idCardOrPhone:"+idCardOrPhone+"]");
				return R.fail("该驾驶员不存在");
			}
			if(personList.size() > 1){
				log.error("查询到多条驾驶员信息，将选取第一条[idCardOrPhone:"+idCardOrPhone+"]");
			}
			driver = personList.get(0);
			if(driver == null){
				return R.fail("未查询到驾驶员信息");
			}
			//1.查询当月里程和驾驶时长
			String month = DateUtil.getDateString().substring(0,7);
			TotalMileageAndDurationInMonth totalInfo = driveStatusService.findDriveStatusByIdCardMonth(driver.getIdCard(), month);

			Double mileage = totalInfo.getTotalMileage();
			long duration = totalInfo.getTotalDuration();

			//2.查询当日报警信息
			//获取驾驶车辆及驾驶时段
			List<StatDriverDuration> durationList = driverDurationService.findDriverDurationListByIdCardMonth(driver.getIdCard(), month);
			//对每个驾驶时段查询报警数量
			List<Integer> alarmTypes = new ArrayList<>();
			alarmTypes.addAll(VdmUserInfoUtil.getFatigueAlarmType());
			alarmTypes.addAll(VdmUserInfoUtil.getOverSpeedAlarmType());
			alarmTypes.addAll(VdmUserInfoUtil.getAdasAlarmType());
			alarmTypes.addAll(VdmUserInfoUtil.getDsmAlarmType());
			List<AlarmTypeAndCount> countList = impalaAlarmService.findVehicleAlarmCountInfoByDriverDuration(durationList, alarmTypes);
			//计算报警数量
			AtomicReference<Long> fatigueAlarmCount = new AtomicReference<>(0L);
			AtomicReference<Long> overSpeedAlarmCount = new AtomicReference<>(0L);
			AtomicReference<Long> adasAlarmCount = new AtomicReference<>(0L);
			AtomicReference<Long> dsmAlarmCount = new AtomicReference<>(0L);
			countList.forEach(item -> {
				int alarmType = Integer.parseInt(item.getAlarmType());
				if(VdmUserInfoUtil.getOverSpeedAlarmType().contains(alarmType)){
					//如果是超速报警
					overSpeedAlarmCount.getAndSet(overSpeedAlarmCount.get() + 1);
				}else if(VdmUserInfoUtil.getFatigueAlarmType().contains(alarmType)){
					//如果是疲劳报警
					fatigueAlarmCount.getAndSet(fatigueAlarmCount.get() + 1);
				}else if(VdmUserInfoUtil.getAdasAlarmType().contains(alarmType)){
					//adas报警
					adasAlarmCount.getAndSet(adasAlarmCount.get() + 1);
				}else if(VdmUserInfoUtil.getDsmAlarmType().contains(alarmType)){
					//dsm报警
					dsmAlarmCount.getAndSet(dsmAlarmCount.get() + 1);
				}
			});
			//计算驾驶天数
			int daysCount = driveStatusService.findDriveDaysInMonthDuration(driver.getIdCard(),month);
			DriverDriveBaseMonth dd = new DriverDriveBaseMonth();
			//里程（km）
			dd.setDriveMileage(MathUtil.roundDouble(mileage/1000,2));
			dd.setDriveDuration(DateUtil.getFormatDateString(duration));
			dd.setDriveDaysCount(daysCount);
			//平均驾驶时长（分钟）
			dd.setAverageDuration(MathUtil.divideRoundDouble(duration/60,daysCount,2));
			//平均里程（km）
			dd.setAverageMileage(MathUtil.divideRoundDouble(mileage/1000,daysCount,2));
			dd.setOverSpeedAlarmCount(overSpeedAlarmCount.get());
			dd.setFatigueAlarmCount(fatigueAlarmCount.get());
			dd.setAdasAlarmCount(adasAlarmCount.get());
			dd.setDsmAlarmCount(dsmAlarmCount.get());
			return R.data(dd);
		}catch (Exception e){
			log.error("查询失败",e);
			return R.fail("查询失败");
		}
	}


	/**
	 * 统计指定月份指定驾驶员每天的驾驶时长和驾驶里程
	 * @param month
	 * @param idCardOrPhone
	 * @param user
	 * @return
	 */
	@GetMapping("/driveInfoEveryDay")
	R<List<DateAndMileageAndDuration>> mileageAndDurationEveryDayInMonth(String month, String idCardOrPhone, BladeUser user){
		if(user == null){
			log.error("用户未登录或未授权");
			return R.fail("用户未登录或未授权");
		}
		//查询条件不能为空
		if(StringUtils.isBlank(idCardOrPhone)){
			return R.fail("查询条件不能为空");
		}
		try{
			List<DateAndMileageAndDuration> list = driveStatusService.findMileageAndDurationEveryDayInMonth(month, idCardOrPhone);
			Map<String,DateAndMileageAndDuration> map = new HashMap<>();
			list.forEach(item -> {
				String date = item.getStatDate();
				map.put(date,item);
			});
			List<DateAndMileageAndDuration> resList = new ArrayList<>();
			long startTime = DateUtil.getMonthFirstSecondTimestamp(month);
			long endTime = DateUtil.getSecondTimestamp();
			List<String> dateList = DateUtil.getDateList(startTime, endTime);
			dateList.forEach(date -> {
				if(!map.containsKey(date)){
					DateAndMileageAndDuration dd = new DateAndMileageAndDuration();
					dd.setStatDate(date);
					dd.setDuration(0D);
					dd.setDurationStr("");
					dd.setMileage(0D);
					resList.add(dd);
				}else{
					DateAndMileageAndDuration dd = map.get(date);
					//里程（km）
					dd.setMileage(MathUtil.roundDouble(dd.getMileage()/1000,2));
					dd.setDurationStr(DateUtil.getFormatDateString(dd.getDuration().longValue()));
					dd.setDuration(MathUtil.roundDouble(dd.getDuration()/3600,2));
					resList.add(dd);
				}
			});
			return R.data(resList);
		}catch (Exception e){
			log.error("查询失败",e);
			return R.fail("查询失败");
		}
	}

    /**
     * @description: 驾驶员动态信息
     * @author: zhouxw
     * @date: 2022/11/18 5:54 PM
     * @param: [param]
     * @return: com.xh.vdm.statistic.utils.R<com.xh.vdm.statistic.vo.response.DriverActiveInfo>
     **/
    @PostMapping("/driverActiveInfo")
    public R<DriverActiveInfo> driverActiveInfo(@RequestBody @Validated IdCardAndMonth param){
        /*try{
            //身份证号、手机号不能为空
            if(StringUtils.isBlank(param.getIdCardOrPhone())){
				log.error("[驾驶员动态数据]未输入身份证号或者手机号");
                return R.fail("请输入身份证号或者手机号");
            }

            //如果没有指定月份，则设置为当月
            if(StringUtils.isBlank(param.getMonth())){
                String month = sdfHolderShort.get().format(new Date()).substring(0,7);
                param.setMonth(month);
            }

            //1.今日驾驶时长、今日驾驶里程
            DriveDurationBaseInfo durationBaseInfo = driveStatusService.statDriverStatusByDateAndIdCard(sdfHolder.get().format(new Date()).substring(0,10).replace("-","") , param.getIdCardOrPhone());

            //2.今日不规范行为次数
            long startSecondTimestamp = DateUtil.getDayFirstSecondTimestamp(new Date().getTime() / 1000);
            long endSecondTimestamp = DateUtil.getDayLastSecondTimestamp(new Date().getTime() / 1000);
            List<BdmSecurity> securityList = securityService.findSecurityByIdCardAndDate(param.getIdCardOrPhone() , startSecondTimestamp , endSecondTimestamp);
            int todayAlarmCount = securityList.size();

            //3.当月驾驶天数、当月平均驾驶时长、当月平均驾驶里程
            DriveInfo driveInfo = driveStatusService.statDriveInfo(param.getMonth(), param.getIdCardOrPhone());

            //4.当月连续不规范次数（当月连续三天及以上出现同一告警）
            long startMonthSecond = DateUtil.getMonthFirstSecondTimestamp(new Date().getTime() / 1000);
            long endMonthSecond = DateUtil.getMonthLastSecondTimestamp(new Date().getTime() / 1000);
            int continousCount = 0;
            //获取每种报警类型发生在哪几天
            List<AlarmTypeAndDateAndIdCard> alarmTypeList = securityService.findEveryAlarmTypeByIdCard(param.getIdCardOrPhone() , startMonthSecond , endMonthSecond);
            if(alarmTypeList != null){
                Map<String,List<String>> map = new HashMap<>();
                for(int i = 0 ; i < alarmTypeList.size() ; i++){
                    String key = alarmTypeList.get(i).getIdCard()+"~"+alarmTypeList.get(i).getAlarmType();
                    String date = alarmTypeList.get(i).getDate();
                    if(map.get(key) == null){
                        List<String> list = new ArrayList<>();
                        list.add(date);
                        map.put(key , list);
                    }else{
                        map.get(key).add(date);
                    }
                }
                //判断每种类型是否连续发生三天及三天以上
                Calendar cal = Calendar.getInstance();
                for(String key : map.keySet()){
                    List<String> dateList = map.get(key);
                    if(dateList == null || dateList.size() < 3){
                        continue;
                    }
                    //判断日期列表中连续三天及以上的有多少组
                    int conIndexCount = 0;
                    boolean conFlag = false;
                    for(int j = 0 ; j < dateList.size() - 1 ; j++){
                        cal.setTime(sdfHolderShort.get().parse(dateList.get(j)));
                        cal.add(Calendar.DAY_OF_MONTH , 1);
                        if(cal.getTimeInMillis() / 1000 == sdfHolderShort.get().parse(dateList.get(j + 1)).getTime() / 1000){
                            //如果两天连续
                            conIndexCount ++;
                            conFlag = true;
                        }else{
                            conFlag = false;
                        }
                        if(conIndexCount >= 3 && !conFlag){
                            continousCount++;
                        }
                    }
                }
            }

            DriverActiveInfo activeInfo = new DriverActiveInfo();
            activeInfo.setContinuousAlarmCount(continousCount);
            if(durationBaseInfo != null){
                activeInfo.setTodayDriveDuration(durationBaseInfo.getDriveDuration());
                activeInfo.setTodayDriveMileage(durationBaseInfo.getDriveMileage());
            }else{
                activeInfo.setTodayDriveDuration("0时0分");
                activeInfo.setTodayDriveMileage(0D);
            }
            activeInfo.setTodayAlarmCount(todayAlarmCount);
            activeInfo.setMonthDriveDays(driveInfo.getDays());
            activeInfo.setMonthAverageDriveMileage((int)driveInfo.getTotalDriveMileage());
            //格式化时长和里程
            long hours = driveInfo.getTotalDriveDuration() / 3600;
            long minutes = driveInfo.getTotalDriveDuration() / 60;
            String durationStr = hours + "时" + minutes + "分";
            activeInfo.setMonthAverageDriveDuration(durationStr);
            return R.data(activeInfo);

        }catch (Exception e){
            log.error("查询驾驶员动态信息失败", e);
            return R.fail("查询驾驶员动态信息失败");
        }*/
		return null;

    }

    @PostMapping("/testGetDurationAndMileage")
    public R<DriveDurationBaseInfo> getDurationAndMileage(@RequestBody @Validated IdCardAndMonth param) throws Exception {
        String idcard = param.getIdCardOrPhone();
        String date = param.getMonth();
        //1.今日驾驶时长、今日驾驶里程
        DriveDurationBaseInfo durationBaseInfo = driveStatusService.statDriverStatusByDateAndIdCard(date , idcard);
        return R.data(durationBaseInfo);
    }


    /**
     * @description: 驾驶行为明细
     * @author: zhouxw
     * @date: 2022/11/19 8:46 PM
     * @param: [param]
     * @return: com.xh.vdm.statistic.utils.R<java.util.List<com.xh.vdm.statistic.entity.DriveDurationBaseInfo>>
     **/
    @PostMapping("/driveStatusDetail")
    R<List<DriveStatusAndAlarm>> driveStatusDetail(@RequestBody @Validated IdCardAndMonth param){
        try{
            //身份证号不能为空
            if(StringUtils.isBlank(param.getIdCardOrPhone())){
                return R.fail("身份证号不能为空");
            }

            //如果没有指定月份，则设置为当月
            if(StringUtils.isBlank(param.getMonth())){
                String month = sdfHolderShort.get().format(new Date()).substring(0,7);
                param.setMonth(month);
            }



            //1。驾驶时长、驾驶里程
            List<DriveDurationBaseInfo> durationBaseInfoList = driveStatusService.findDriveStatusList(param.getMonth() , param.getIdCardOrPhone());
            Map<String , DriveDurationBaseInfo> durationBaseInfoMap = new HashMap<>();
            durationBaseInfoList.forEach(item -> {
                String date = item.getDate();
                durationBaseInfoMap.put(date , item);
            });

            //2。不规范次数
            long startSecondTimestamp = DateUtil.getMonthFirstSecondTimestamp(new Date().getTime() / 1000);
            long endSecondTimestamp = DateUtil.getMonthLastSecondTimestamp(new Date().getTime() / 1000);
            List<DateAndCount> alarmCountList = securityService.findAlarmCountEveryDay(param.getIdCardOrPhone(), startSecondTimestamp , endSecondTimestamp);
            Map<String, DateAndCount> alarmTypeAndDateAndIdCardMap = new HashMap<>();
            alarmCountList.forEach(item -> {
                String date = item.getStatDate();
                alarmTypeAndDateAndIdCardMap.put(date , item);
            });

            //3。组装数据
            String dateStr = param.getMonth() + "-01" ;
            Calendar cal = Calendar.getInstance();
            cal.setTime(sdfHolderShort.get().parse(dateStr));
            long todayFirstSecondTimestamp = DateUtil.getDayFirstSecondTimestamp(new Date().getTime() / 1000);
            List<DriveStatusAndAlarm> resList = new ArrayList<>();
            while(cal.getTimeInMillis() / 1000 <= todayFirstSecondTimestamp){
                int dateIndex = cal.get(Calendar.DAY_OF_MONTH);
                String date = cal.get(Calendar.YEAR)+"-"+(cal.get(Calendar.MONTH)+1)+"-"+(dateIndex<10?"0"+dateIndex:dateIndex);
                DriveDurationBaseInfo db = durationBaseInfoMap.get(date);
                DateAndCount ai = alarmTypeAndDateAndIdCardMap.get(date);
                DriveStatusAndAlarm da = new DriveStatusAndAlarm();
                da.setDate(date);
                if(db != null){
                    da.setDuration(MathUtil.roundDouble(Double.parseDouble(db.getDriveDuration()) / 3600 , 2));
                    da.setMileage(MathUtil.roundDouble(db.getDriveMileage() / 1000 , 2));
                }
                if(ai != null){
                    da.setAlarmCount(ai.getCount());
                }
                resList.add(da);
                cal.add(Calendar.DAY_OF_MONTH , 1);
            }
            return R.data(resList);
        }catch (Exception e){
            log.error("查询驾驶行为明细失败",e);
            return R.fail("查询驾驶行为明细失败");
        }
    }


    /**
     * @description: 根据车辆和驾驶时段查询驾驶员信息
     * 1.如果参数中不提供起止时间，就查询实时：查询人脸识别表中最新的信息
     * 2.如果参数中提供起止时间，就查询历史驾驶员驾驶时段信息
     * @author: zhouxw
     * @date: 2023-02-54 16:35:08
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.xh.vdm.statistic.vo.response.DriverDurationResponse>
     **/
    @PostMapping("/getDriver")
    public R<DriverDurationResponse> findDriverDuration(@RequestBody DriverDurationRequest request){

        //1.如果参数中不提供起止时间，就查询实时：查询人脸识别表中最新的信息
        if(request.getStartTime() == null || request.getEndTime() == null || request.getStartTime() <= 0 || request.getEndTime() <= 0){
            try{
                //查询人脸识别的最新结果
                DriverFaceResultNode req = driveStatusService.findNewestDriverWithFaceByLicencePlate(request.getLicencePlate());
                if(req == null){
                    log.info("未查询到该车辆的驾驶员信息");
                    return R.data(null);
                }
                DriverDurationResponse response = new DriverDurationResponse();
                response.setLicencePlate(req.getLicencePlate());
                List<String> driverNames = new ArrayList<>();
                driverNames.add(req.getDriverName());
                response.setDriverName(driverNames);
                return R.data(response);
            }catch (Exception e){
                log.error("实时查询驾驶员信息失败",e);
                return R.fail("实时查询驾驶员信息失败");
            }
        }


        //2.如果参数中包含起止时间，就查询历史驾驶时段
        //判断开始时间和结束时间是否在同一月份
        try{
            String startDate = DateUtil.getDateString(request.getStartTime());
            String endDate = DateUtil.getDateString(request.getEndTime());
            String startMonth = startDate.substring(0,7);
            String endMonth = endDate.substring(0,7);
            if(startMonth.equals(endMonth)){
                //如果在同一个月
                DriverDurationResponse req = driverDurationService.findDriverByCondition(startMonth.replace("-",""), request);
                return R.data(req);
            }else{
                //如果不在同一个月
                //查询中间月份
                int monthCount = DateUtil.monthCountBetweenSecondTimestamp(request.getStartTime(), request.getEndTime());
                List<DriverDurationResponse> resList = new ArrayList<>();
                if(monthCount > 1){
                    Calendar cal = Calendar.getInstance();
                    cal.setTimeInMillis(request.getStartTime()*1000);
                    for(int i = 1; i < monthCount; i++){
                        cal.add(Calendar.MONTH, 1);
                        DriverDurationRequest reqTmp = new DriverDurationRequest();
                        reqTmp.setLicencePlate(request.getLicencePlate());
                        reqTmp.setStartTime(DateUtil.getMonthFirstSecondTimestamp(cal.getTimeInMillis()/1000));
                        reqTmp.setEndTime(DateUtil.getMonthLastSecondTimestamp(cal.getTimeInMillis()/1000));
                        DriverDurationResponse resTmp = driverDurationService.findDriverByCondition(cal.get(Calendar.YEAR)+""+cal.get(Calendar.MONTH+1), request);
                        resList.add(resTmp);
                    }
                }
                //查询开始月份
                DriverDurationRequest reqStart = new DriverDurationRequest();
                reqStart.setLicencePlate(request.getLicencePlate());
                reqStart.setStartTime(reqStart.getStartTime());
                reqStart.setEndTime(DateUtil.getMonthLastSecondTimestamp(request.getStartTime()));
                DriverDurationResponse resStart = driverDurationService.findDriverByCondition(startMonth, request);
                resList.add(resStart);

                //查询结束月份
                DriverDurationRequest reqEnd = new DriverDurationRequest();
                reqEnd.setLicencePlate(request.getLicencePlate());
                reqEnd.setEndTime(reqStart.getEndTime());
                reqEnd.setStartTime(DateUtil.getMonthFirstSecondTimestamp(request.getEndTime()));
                DriverDurationResponse resEnd = driverDurationService.findDriverByCondition(endMonth, request);
                resList.add(resEnd);

                Set<String> set = new HashSet<>();
                resList.forEach(item -> {
                    List<String> driverNames = item.getDriverName();
                    driverNames.forEach(itemT -> {
                        set.add(itemT);
                    });
                });
                DriverDurationResponse response = new DriverDurationResponse();
                if(resList == null || resList.size() < 1){
                    response.setLicencePlate(request.getLicencePlate());
                    return R.data(response);
                }
                response.setLicencePlate(resList.get(0).getLicencePlate());
                response.setDriverName(new ArrayList<>(set));
                return R.data(response);
            }
        }catch (Exception e){
            log.error("查询历史驾驶员信息失败",e);
            return R.fail("查询驾驶员信息失败");
        }
    }

}
