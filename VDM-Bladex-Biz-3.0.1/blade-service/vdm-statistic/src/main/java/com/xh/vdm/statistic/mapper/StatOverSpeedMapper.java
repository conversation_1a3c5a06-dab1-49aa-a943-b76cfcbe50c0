package com.xh.vdm.statistic.mapper;

import com.xh.vdm.statistic.entity.OverSpeedWithDeptAndDate;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 超速统计
 * @author: zhouxw
 * @date: 2022/11/15 10:12 PM
 * @param:
 * @return:
 **/
public interface StatOverSpeedMapper {

    /**
     * @description: 根据 企业id 和 开始时间（精确到秒的时间戳）、结束时间（精确到秒的时间戳）统计超速次数
     * @author: zhouxw
     * @date: 2022/11/15 10:15 PM
     * @param: [deptId, startSecondTimestamp, endSecondTImestamp]
     * @return: java.util.List<com.xh.vdm.statistic.entity.OverSpeedWithDeptAndDate>
     **/
    List<OverSpeedWithDeptAndDate> statOverSpeedByDeptAndDuration(@Param("deptIds") List<Long> deptIds , @Param("startSecondTimestamp") Long startSecondTimestamp , @Param("endSecondTimestamp") Long endSecondTImestamp);


    /**
     * @description: 根据 企业id 和 开始时间（精确到秒的时间戳）、结束时间（精确到秒的时间戳）查询超速次数
     * @author: zhouxw
     * @date: 2022/11/15 10:15 PM
     * @param: [deptId, startSecondTimestamp, endSecondTImestamp]
     * @return: java.util.List<com.xh.vdm.statistic.entity.OverSpeedWithDeptAndDate>
     **/
    OverSpeedWithDeptAndDate getOverSpeedByDeptAndDuration(@Param("deptId") Long deptId , @Param("startSecondTimestamp") Long startSecondTimestamp , @Param("endSecondTimestamp") Long endSecondTImestamp);

}
