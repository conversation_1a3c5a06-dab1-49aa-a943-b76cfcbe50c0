package com.xh.vdm.statistic.controller.terminal;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xh.vdm.statistic.entity.BdmLocationHarvest;
import com.xh.vdm.statistic.entity.BdmLocationShare;
import com.xh.vdm.statistic.entity.BdmTargetOdometer;
import com.xh.vdm.statistic.entity.BladeDept;
import com.xh.vdm.statistic.mapper.alarm.AlarmImpalaMapper;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.service.terminal.IScreenDynamicService;
import com.xh.vdm.statistic.utils.AuthUtils;
import com.xh.vdm.statistic.vo.response.LocationHarvestResponse;
import com.xh.vdm.statistic.vo.response.terminal.*;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.utils.MybatisPlusWrapperUitls;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Description: 大屏可视化 -- 北斗动态数据
 */
@RequestMapping("/screen/dynamic/")
@RestController
@Slf4j
public class ScreenDynamicController {
	@Resource
	private StringRedisTemplate stringRedisTemplate;
	@Resource
	private BdmDeviceLinkService bdmDeviceLinkService;
	@Resource
	private AlarmImpalaMapper alarmImpalaMapper;
	@Resource
	private BdmDeviceOnlineService bdmDeviceOnlineService;
	@Resource
	private IBdmLocationHarvestService bdmLocationHarvestService;
	@Resource
	private IBdmLocationShareService bdmLocationShareService;
	@Resource
	private IBdmTargetOdometerService bdmTargetOdometerService;
	@Resource
	private AuthUtils authUtils;
	@Resource
	private IScreenDynamicService screenDynamicService;
	@Resource
	private IBladeDeptService bladeDeptService;

	/**
	 * 统一处理权限验证和获取部门列表
	 */
	private R<List<Long>> handleAuthAndDeptList(BladeUser user) {
		R<String> authResult = authUtils.isValidServiceRole(user);
		if (!authResult.isSuccess()) {
			return R.fail(authResult.getCode(), authResult.getMsg());
		}

		return authUtils.getDeptList(user);
	}

	/**
	 * 1.数据采集/共享量：累计数据采集量（点位），累计到当天：查bdm_location_harvest表月度累计采集量（点位数据），累计到当月；
	 * 共享数据量（点位数量），累积到当天
	 */
	@GetMapping("/location/trend")
	public R<Map<String, Object>> locationTrendered(BladeUser user) {
		R<List<Long>> deptListResult = handleAuthAndDeptList(user);
		if (!deptListResult.isSuccess()) {
			return R.fail(deptListResult.getCode(), deptListResult.getMsg());
		}
		List<Long> deptList = deptListResult.getData();
		Map<String, Object> map = new HashMap<>();
		//数据采集量
		QueryWrapper<BdmLocationHarvest> wrapper = new QueryWrapper<>();
		if (deptList != null && !deptList.isEmpty()) {
			MybatisPlusWrapperUitls.longListIn(wrapper, "dept_id", deptList);
		}
		wrapper.select("SUM(harvest) AS total, SUM(month_harvest) AS monthTotal");
		List<BdmLocationHarvest> harvests = bdmLocationHarvestService.getBaseMapper().selectList(wrapper);

		List<LocationHarvestResponse> result = new ArrayList<>();
		if(!harvests.isEmpty()){
			result = harvests.stream()
				.filter(Objects::nonNull)
				.map(locationHarvest -> {
					LocationHarvestResponse response = new LocationHarvestResponse();
					response.setTotal(locationHarvest.getTotal() != null ? locationHarvest.getTotal():0);
					response.setMonthTotal(locationHarvest.getMonthTotal() != null ? locationHarvest.getMonthTotal():0);
					return response;
				})
				.collect(Collectors.toList());
		}

		map.put("harvests", result);

		// 数据共享量
		QueryWrapper<BdmLocationShare> shareWrapper = new QueryWrapper<>();
		MybatisPlusWrapperUitls.longListIn(shareWrapper, "dept_id", deptList);
		List<BdmLocationShare> shares = bdmLocationShareService.getBaseMapper().selectList(shareWrapper);
		map.put("shares", shares);
		return R.data(map);
	}

	/**
	 * 2.终端上线趋势分析：统计每个小时终端的上线和在线量，近24小时，包括当前小时
	 */
	@GetMapping("/onLine/trend")
	public R<Map<String, Object>> onlineTrendered(BladeUser user) {
		R<String> authResult = authUtils.isValidServiceRole(user);
		if (!authResult.isSuccess()) {
			return R.fail(authResult.getCode(), authResult.getMsg());
		}
		Map<String, Object> map = new HashMap<>();
		// 查询终端的上线数据
		List<LinkCountResponse> linkCountResponse = bdmDeviceLinkService.onlineTrendered(user.getUserId());
		//
		List<OnlineCountResponse> onlineList = bdmDeviceOnlineService.getOnlineTrendered(user.getUserId());
		//获取上线数量
		map.put("link", linkCountResponse);
		// 获取在线终端
		map.put("online", onlineList);
		return R.data(map);
	}

	/**
	 * 3.告警分析：统计每天的终端告警数据量，近7天，包括当天
	 */
	@GetMapping("/alarm/statics")
	public R<List<AlarmCountResponse>> alarmStatics(BladeUser user) {
		R<List<Long>> deptListResult = handleAuthAndDeptList(user);
		if (!deptListResult.isSuccess()) {
			return R.fail(deptListResult.getCode(), deptListResult.getMsg());
		}
		List<Long> deptList = deptListResult.getData();
		//TODO 获取上线数量 大数据 sql in 问题
		List<AlarmCountResponse> alarmCountResponse = alarmImpalaMapper.alarmStatics(deptList);
		return R.data(alarmCountResponse);
	}

	/**
	 * 4.中间地图：显示5大类在线终端的分布，用不同颜色区分
	 * 6.北斗终端在线/上线排名：国能二级单位终端排名  0 为 上线  1为在线
	 */
	@GetMapping("/online/terminal")
	public R<Map<String, Object>> onlineTerminal(BladeUser user) {
		R<List<Long>> deptListResult = handleAuthAndDeptList(user);
		if (!deptListResult.isSuccess()) {
			return R.fail(deptListResult.getCode(), deptListResult.getMsg());
		}
		List<Long> deptList = deptListResult.getData();
		Map<String, Object> map = new HashMap<>();

		Set<String> keys = getKeysFromRedis(CommonConstant.PREFIX_REDIS_ONLINETODAY + "*");
		Set<String> onlineKeys = getKeysFromRedis(CommonConstant.PREFIX_REDIS_ONLINESTATE + "*");

		List<String> onlineKeysList = new ArrayList<>();
		for (String onlineKey : onlineKeys) {
			String keyWithoutPrefix = onlineKey.substring(CommonConstant.PREFIX_REDIS_ONLINESTATE.length());
			onlineKeysList.add(keyWithoutPrefix);
		}

		//在线数据
		List<TerminalInfo> onlineTerminalInfoList = new ArrayList<>();
		//今日上线数据
		List<TerminalInfo> terminalInfoList = new ArrayList<>();
		//今日上线数量
		long linkCount = 0;
		//在线数量
		long onlineCount = 0;

		if (keys != null && !keys.isEmpty()) {
			try {
				for (String key : keys) {
					String data = stringRedisTemplate.opsForValue().get(key);
					TerminalInfo terminalInfo = JSON.parseObject(data, TerminalInfo.class);
					String keyWithoutPrefix = key.substring(CommonConstant.PREFIX_REDIS_ONLINESTATE.length());
					if (onlineKeysList.contains(keyWithoutPrefix)) {
						terminalInfo.setTeState(1);
						onlineTerminalInfoList.add(terminalInfo);
						terminalInfoList.add(terminalInfo);
					} else {
						terminalInfo.setTeState(0);
						terminalInfoList.add(terminalInfo);
					}
				}
			} catch (Exception e) {
				log.error("从缓存中读取数据失败", e);
			}

			// 在deptList不为空时，根据deptList统计onlineCount和linkCount
			if (deptList != null && !deptList.isEmpty()) {
				onlineCount = onlineTerminalInfoList.stream()
					.filter(info -> deptList.contains(info.getDeptId()))
					.count();
				linkCount = terminalInfoList.stream()
					.filter(info -> deptList.contains(info.getDeptId()))
					.count();
			} else {
				onlineCount = onlineTerminalInfoList.size();
				linkCount = terminalInfoList.size();
			}

			QueryWrapper<BladeDept> wrapper = new QueryWrapper<>();
			if (deptList != null && !deptList.isEmpty()) {
				MybatisPlusWrapperUitls.longListIn(wrapper, "id", deptList);
			}
			wrapper.eq("tenant_id", user.getTenantId());
			wrapper.select("id,dept_name");
			Map<Long, String> deptMap = bladeDeptService.getBaseMapper()
				.selectList(wrapper)
				.stream()
				.collect(Collectors.toMap(BladeDept::getId, BladeDept::getDeptName));

			//终端在线量排名	终端上线量排名
			map.put("onlineGroupCount", getGroupCount(onlineTerminalInfoList, deptMap));
			map.put("linkGroupCount", getGroupCount(terminalInfoList, deptMap));
		}

		long terminalTotal = screenDynamicService.count(user.getUserId());
		map.put("terminalTotal", terminalTotal);
		map.put("onlineTerminalInfoList", onlineTerminalInfoList);
		map.put("terminalOnlineCount", onlineCount);
		map.put("terminalLinkCount", linkCount);
		if (terminalTotal != 0) {
			double terminalLinkRate = (double) linkCount / terminalTotal * 100;
			map.put("terminalLinkRate", terminalLinkRate);

			double terminalOnlineRate = (double) onlineCount / terminalTotal * 100;
			map.put("terminalOnlineRate", terminalOnlineRate);
		}

		// 告警量
		Long alarmCount = alarmImpalaMapper.count();
		map.put("alarmCount", alarmCount);

		return R.data(map);
	}

	private Set<String> getKeysFromRedis(String pattern) {
		return stringRedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
			Set<String> result = new HashSet<>();
			Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(pattern).count(1000).build());
			while (cursor.hasNext()) {
				result.add(new String(cursor.next()));
			}
			return result;
		});
	}

	private Map<String, Long> getGroupCount(List<TerminalInfo> terminalInfoList, Map<Long, String> deptMap) {
		if(deptMap.isEmpty()){
			return new HashMap<>();
		}
		Map<Long, Long> groupCount = terminalInfoList.stream()
			.collect(Collectors.groupingBy(TerminalInfo::getDeptId, Collectors.counting()));

		return groupCount.entrySet().stream()
			.filter(entry -> entry.getKey() != null && deptMap.get(entry.getKey()) != null)
			.collect(Collectors.toMap(
				entry -> deptMap.get(entry.getKey()),
				Map.Entry::getValue
			))
			.entrySet().stream()
			.sorted(Map.Entry.<String, Long>comparingByValue().reversed())
			.collect(Collectors.toMap(
				Map.Entry::getKey,
				Map.Entry::getValue,
				(e1, e2) -> e1,
				LinkedHashMap::new
			));
	}

	/**
	 * 5.里程分析：作业车（包括矿卡）、运输车、铁路货车行驶里程数据统计
	 */
	@GetMapping("/target/odometer")
	public R<List<TargetOdometerResponse>> targetOdometer(BladeUser user) {
		R<List<Long>> deptListResult = handleAuthAndDeptList(user);
		if (!deptListResult.isSuccess()) {
			return R.fail(deptListResult.getCode(), deptListResult.getMsg());
		}
		List<Long> deptList = deptListResult.getData();
		QueryWrapper<BdmTargetOdometer> wrapper = new QueryWrapper<>();
		if (deptList != null && !deptList.isEmpty()) {
			MybatisPlusWrapperUitls.longListIn(wrapper, "dept_id", deptList);
		}
		wrapper.select("target_category, SUM(odometer) AS total_odometer")
			.groupBy("target_category")
			.eq("target_type", 1);
		List<BdmTargetOdometer> odometers = bdmTargetOdometerService.getBaseMapper().selectList(wrapper);



		List<TargetOdometerResponse> result = odometers.stream()
			.map(odometer -> {
				TargetOdometerResponse response = new TargetOdometerResponse();
				response.setTargetCategory(odometer.getTargetCategory());
				response.setTotalOdometer(odometer.getTotalOdometer());
				return response;
			})
			.collect(Collectors.toList());
		return R.data(result);
	}

}
