package com.xh.vdm.statistic.controller.terminal;

import com.xh.vdm.statistic.service.terminal.*;
import com.xh.vdm.statistic.utils.AuthUtils;
import com.xh.vdm.statistic.vo.response.terminal.*;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.DictCodeConstant;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Description: 大屏可视化 -- 北斗基础数据
 */
@RequestMapping("/screen/base/info")
@RestController
@Slf4j
public class ScreenBaseInfoController {

	@Resource
	private IMonitDeviceService monitDeviceService;
	@Resource
	private IPntDeviceService pntDeviceService;
	@Resource
	private IRdssDeviceService rdssDeviceService;
	@Resource
	private IRnssDeviceService rnssDeviceService;
	@Resource
	private IWearableDeviceService wearableDeviceService;
	@Resource
	private IDictBizClient dictBizClient;
	@Resource
	private ScreenBaseInfoService screenBaseInfoService;
	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private AuthUtils authUtils;

	/**
	 * 统一处理权限验证和获取部门列表
	 */
	private R<List<Long>> handleAuthAndDeptList(BladeUser user) {
		R<String> authResult = authUtils.isValidServiceRole(user);
		if (!authResult.isSuccess()) {
			return R.fail(authResult.getCode(), authResult.getMsg());
		}

		return authUtils.getDeptList(user);
	}

	/**
	 * 1.北斗短报文应用方向分析：短报文终端的应用方向统计
	 */
	@GetMapping("/rdss/domain")
	public R<List<RdssDomainCountResponse>> getRdssCountByDomain(BladeUser user) {
		R<String> authResult = authUtils.isValidServiceRole(user);
		if (!authResult.isSuccess()) {
			return R.fail(authResult.getCode(), authResult.getMsg());
		}
		return R.data(this.rdssDeviceService.getRdssCountByDomain(user.getUserId()));
	}

	/**
	 * 2.北斗应用年度增长分析：统计北斗终端总数，以年为维度统计，统计近5年
	 */
	@GetMapping("/terminal/year")
	public R<Map<Integer, CountRateResponse>> statisticsTerminalByYear(BladeUser user) {
		R<String> authResult = authUtils.isValidServiceRole(user);
		if (!authResult.isSuccess()) {
			return R.fail(authResult.getCode(), authResult.getMsg());
		}
		Map<Integer, CountRateResponse> map = new HashMap<>();

		Map<Integer, Integer> yearToCountMap = new HashMap<>();
		// 获取当前日期并计算过去5年的年份
		LocalDate currentDate = LocalDate.now();
		for (int i = 0; i < 5; i++) {
			int year = currentDate.minusYears(i).getYear();
			// 初始化年份对应的计数为0
			yearToCountMap.put(year, 0);

			Integer count = screenBaseInfoService.statisticsTerminalByYear(year,user.getUserId());

			yearToCountMap.put(year, count);
		}

		for (int i = 0; i < 5; i++) {
			CountRateResponse countRateResponse = new CountRateResponse();
			int year = currentDate.minusYears(i).getYear();
			int currentCount = yearToCountMap.get(year);
			countRateResponse.setCount(currentCount);

			if (i == 4) {
				countRateResponse.setRate(0.0);
			} else {
				int prevCount = yearToCountMap.get(year - 1);
				double rate = this.calculateGrowthRate(currentCount, prevCount);
				countRateResponse.setRate(rate);
			}
			map.put(year, countRateResponse);
		}
		return R.data(map);
	}

	private double calculateGrowthRate(int currentCount, int prevCount) {
		if (prevCount == 0 && currentCount != 0) {
			return 1.0 * 100;
		}
		if (prevCount == 0) {
			return 0.0;
		}
		return ((double) (currentCount - prevCount) / prevCount) * 100;
	}


	/**
	 * 3.终端定位模式：单北斗、北斗双频、单GPS终端数量统计
	 */
	@GetMapping("/rnss/gnssMode")
	public R<List<RnssGnssModeCountResponse>> getRnssCountByGnssMode(BladeUser user) {
		R<String> authResult = authUtils.isValidServiceRole(user);
		if (!authResult.isSuccess()) {
			return R.fail(authResult.getCode(), authResult.getMsg());
		}
		return R.data(this.rnssDeviceService.getRnssCountByGnssMode(user.getUserId()));
	}


	/**
	 * 4.中间地图：终端数量，数据来源跟首页一样
	 */

	/**
	 * 5.北斗高精度应用方向分析：北斗高精度终端的应用方向统计，
	 */
	@GetMapping("/high/precision")
	public R<List<HighPrecisionCountResponse>> getHighPrecisionCount(BladeUser user) {
		R<String> authResult = authUtils.isValidServiceRole(user);
		if (!authResult.isSuccess()) {
			return R.fail(authResult.getCode(), authResult.getMsg());
		}
		List<DictBiz> list = new ArrayList<>();
		R<List<DictBiz>> rtt = this.dictBizClient.getList(DictCodeConstant.DEVICE_TYPE);
		if (rtt.isSuccess() && (rtt.getData() != null) && (!rtt.getData().isEmpty())) {
			list = rtt.getData();
		}

		List<String> keys = new ArrayList<>();
		list.forEach(dictBiz -> {
			if (dictBiz.getRemark().equals("HF")) {
				keys.add(dictBiz.getDictKey());
			}
		});
		List<HighPrecisionCountResponse> response = this.screenBaseInfoService.getHighPrecisionCount(keys,user.getUserId());
		return R.data(response);
	}

	/**
	 * 6.总接入量排名 TOP10：国能二级单位终端接入量排名
	 */
	@GetMapping("/terminal/dept")
	public R<List<TerminalDeptCountResponse>> getTerminalCountByDept(BladeUser user) {
		R<String> authResult = authUtils.isValidServiceRole(user);
		if (!authResult.isSuccess()) {
			return R.fail(authResult.getCode(), authResult.getMsg());
		}
		return R.data(this.screenBaseInfoService.getTerminalCountByDept(user.getUserId()));
	}
}
