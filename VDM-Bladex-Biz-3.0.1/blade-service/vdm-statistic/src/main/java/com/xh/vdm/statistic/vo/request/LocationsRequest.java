package com.xh.vdm.statistic.vo.request;

import lombok.Data;
import java.io.Serializable;

/**
 * 定位信息入参
 */
@Data
public class LocationsRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long targetId;

	private Integer targetType;

	private Long deviceId;

	private Byte batch;

	/**
	 * 终端设备类型
	 */
	private Integer deviceType;

	/**
	 * 终端赋码号
	 */
	private String deviceNum;

	/**
	 * 状态
	 */
	private Integer action;

	/**
	 * 时间段开始时间
	 */
	private Long startTime;

	/**
	 * 时间段结束时间
	 */
	private Long endTime;

	private String TargetName;

}

