package com.xh.vdm.statistic.utils;

import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.LocationQuality;

import java.util.Date;
import java.util.List;

/**
 * @Description: 定位数据相关工具类
 * @Author: zhouxw
 * @Date: 2022/9/1 10:44 AM
 */
public class LocationUtil {

    /**
     * @description: 校验单条定位数据
     * 校验规则：
         * 日期时间：≤当前时间；
         * 经度范围：73°33′E至135°05′E；(73.55 至 135.083)
         * 纬度范围：3°51′N至53°33′N；(3.85 至 53.55)
         * 速度：0至160间；
         * 海拔：-200至6500之间
     * @author: zhouxw
     * @date: 2022/9/1 11:02 AM
     * @param: [location]
     * @return: com.xh.vdm.statistic.entity.LocationQuality
     **/
    public static LocationQuality validLocation(LocationKudu location){
        //错误出现次数，一条数据中可能出现多次错误
        int errTypeCount = 0;
        LocationQuality quality = new LocationQuality();
        //1。校验定位时间（单位 秒）
        Long time = location.getTime();
        long now = new Date().getTime();
        if( time * 1000 > now ){
            //如果定位时间大于当前系统时间，则校验不通过
            quality.setTimeErrCount(1);
            errTypeCount++;
        }

        //2。校验接收时间（单位 秒）
        Long receiveTime = location.getRecvTime();
        if( receiveTime * 1000 > now ){
            //如果接收时间大于当前系统时间，则校验不通过
            quality.setReceiveTimeErrCount(1);
            errTypeCount++;
        }

        //3。校验经度
        Double longitude = location.getLongitude();
        if(longitude.doubleValue() < StatisticConstants.LONGITUDE_MIN_VALUE || longitude.doubleValue() > StatisticConstants.LONGITUDE_MAX_VALUE){
            //如果经度不满足范围
            quality.setLongitudeErrCount(1);
            errTypeCount++;
        }

        //4。校验纬度
        Double latitude = location.getLatitude();
        if(latitude.doubleValue() < StatisticConstants.LATITUDE_MIN_VALUE || latitude.doubleValue() > StatisticConstants.LATITUDE_MAX_VALUE){
            //如果纬度不满足条件
            quality.setLatitudeErrCount(1);
            errTypeCount++;
        }

        //5。校验速度
        Float speed = location.getSpeed();
        if(speed.intValue() < StatisticConstants.SPEED_MIN_VALUE || speed.intValue() > StatisticConstants.SPEED_MAX_VALUE){
            quality.setSpeedErrCount(1);
            errTypeCount++;
        }

        //6。校验海拔
        Integer altitude = location.getAltitude();
        if(altitude.intValue() < StatisticConstants.ALTITUDE_MIN_VALUE || altitude.intValue() > StatisticConstants.ALTITUDE_MAX_VALUE){
            quality.setAltitudeErrCount(1);
            errTypeCount++;
        }

        quality.setTotalCount(1);
        quality.setTotalErrorCount( errTypeCount > 0 ? 1 : 0 );
        return quality;
    }


    /**
     * @description: 校验批量定位数据
     * @author: zhouxw
     * @date: 2022/9/1 11:53 AM
     * @param: [list]
     * @return: com.xh.vdm.statistic.entity.LocationQuality
     **/
    public static LocationQuality validLocationBatch(List<LocationKudu> list){
        LocationQuality quality = new LocationQuality();
        StringBuffer errList = new StringBuffer();
        for(LocationKudu location : list){
            //错误出现次数，一条数据中可能出现多次错误
            int errTypeCount = 0;

            //1。校验定位时间（单位 秒）
            Long time = location.getTime();
            long now = new Date().getTime();
            if( time * 1000 > now ){
                //如果定位时间大于当前系统时间，则校验不通过
                quality.setTimeErrCount((quality.getTimeErrCount()==null?0:quality.getTimeErrCount()) + 1);
                errTypeCount++;
            }

            //2。校验接收时间（单位 秒）
            Long receiveTime = location.getRecvTime();
            if( receiveTime * 1000 > now ){
                //如果接收时间大于当前系统时间，则校验不通过
                quality.setReceiveTimeErrCount((quality.getReceiveTimeErrCount()==null?0:quality.getReceiveTimeErrCount()) + 1);
                errTypeCount++;
            }

            //3。校验经度
            Double longitude = location.getLongitude();
            if(longitude.doubleValue() < StatisticConstants.LONGITUDE_MIN_VALUE || longitude.doubleValue() > StatisticConstants.LONGITUDE_MAX_VALUE){
                //如果经度不满足范围
                quality.setLongitudeErrCount((quality.getLongitudeErrCount()==null?0:quality.getLongitudeErrCount()) + 1);
                errTypeCount++;
            }

            //4。校验纬度
            Double latitude = location.getLatitude();
            if(latitude.doubleValue() < StatisticConstants.LATITUDE_MIN_VALUE || latitude.doubleValue() > StatisticConstants.LATITUDE_MAX_VALUE){
                //如果纬度不满足条件
                quality.setLatitudeErrCount((quality.getLatitudeErrCount()==null?0:quality.getLatitudeErrCount()) + 1);
                errTypeCount++;
            }

            //5。校验速度
            Float speed = location.getSpeed();
            if(speed.intValue() < StatisticConstants.SPEED_MIN_VALUE || speed.intValue() > StatisticConstants.SPEED_MAX_VALUE){
                quality.setSpeedErrCount((quality.getSpeedErrCount()==null?0:quality.getSpeedErrCount()) + 1);
                errTypeCount++;
            }

            //6。校验海拔
            Integer altitude = location.getAltitude();
            if(altitude.intValue() < StatisticConstants.ALTITUDE_MIN_VALUE || altitude.intValue() > StatisticConstants.ALTITUDE_MAX_VALUE){
                quality.setAltitudeErrCount((quality.getAltitudeErrCount()==null?0:quality.getAltitudeErrCount()) + 1);
                errTypeCount++;
            }
            if(errTypeCount > 0){
                //如果校验发生了错误
                quality.setTotalErrorCount((quality.getTotalErrorCount()==null?0:quality.getTotalErrorCount()) + 1);
				//之前是存储的location的id，现在location存储到了kudu中，定位时间locTime为主键的一部分，所以，这里可以记录定位时间 locTime
                errList.append(location.getTime()+",");
            }
        }
        quality.setErrorLocationList(errList.toString());
        quality.setTotalCount(list.size());
        return quality;
    }

    /*public static void main(String[] args) {
        double lng1 = 87.4534637;
        double lat1 = 40.6767028;
        double lng2 = 87.4560011;
        double lat2 = 40.6732032;
        double distance = DistanceUtils.wgs84Distance(lng1 , lat1 , lng2 , lat2);
        System.out.println(distance);
        //double time = 1646150374 - 1646141193;
        //double speed = distance / time / 3.6;
        //System.out.println(speed);
    }*/

}
