package com.xh.vdm.statistic.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.wx.TotalMileageAndDurationInMonth;
import com.xh.vdm.statistic.mapper.StatDriveStatusMapper;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.DataUtils;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.DistanceUtils;
import com.xh.vdm.statistic.utils.MathUtil;
import org.springblade.common.constant.CommonConstant;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * 驾驶员驾驶情况表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-16
 */
@Service
public class StatDriveStatusServiceImpl extends ServiceImpl<StatDriveStatusMapper, StatDriveStatus> implements IStatDriveStatusService {


    public static final long HALF_HOUR = 1800;

	//跨天跑批后期生成，后期再次跑批时，应该删除
	public static final String STATE_APPEND = "a";

    //插卡
    public static final int IN_CARD = 1;
    //拔卡
    public static final int OUT_CARD = 2;

    //驾驶员最长驾驶时间
    public static final long DEFAULT_MAX_DRIVE_SECOND = 4 * 3600;

	private final ThreadPoolExecutor thread_pool = new ThreadPoolExecutor(10,20,300, TimeUnit.SECONDS,new LinkedBlockingQueue<>());

    @Resource
    private ILocationService locationService;

    @Resource
    private IStatTaskLogService taskLogService;


    @Resource
    private DataUtils dataUtils;

    @Resource
    private IStatStopPointService stopPointService;

    //驾驶时段
    @Resource
    private IStatDriverDurationService driverDurationService;

    private static ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd"));

    /**
     * @description: 通过人脸识别查询驾驶员驾驶时段
     * 返回所有驾驶员在所有车辆上的驾驶时段
     * 思路：纯保守计算方式
     * 1。根据 车牌号、驾驶员姓名、时间（time字段）排序；
     * 2。找连续识别成功的记录作为驾驶时段（result连续为0）；
     * 3。计算从第一个0到最后一个0之间的累计时长作为驾驶时长；
     * 4。如果两个连续的0之间，时间差超过半个小时，则认为无效，驾驶时段结束，不计入驾驶时长；
     *
     * 添加了车辆停止点之后做的优化：
     * （1）增加判断： 0 ~ 1这种情况，0和1之间是否有停止点，如果有停止点则 0 到 停止点之间的时间算作驾驶时段。
     * （2）修改判断：两个连续的0之间，时间差是否超过半个小时，如果超过半个小时，则查找两个0点之间的停止点，如果存在停止点，则前一个 0点 到 停止点 之间的时间作为驾驶时段
     * @author: zhouxw
     * @date: 2022/11/16 11:17 PM
     * @param: [date]
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatDriveStatus>
     **/
    @Override
    public List<DriveDuration> findDriveDurationByFace(String statDate) throws Exception {

        String statDateQuery = statDate + "%";

        //1.查找人脸识别结果
		Date date = new SimpleDateFormat("yyyy-MM-dd").parse(statDate);
        List<DriverFaceResultNode> list = baseMapper.getDriverFaceResult(date);

        //2.对结果进行分析,获取驾驶时段
        List<DriveDuration> durationList = getDriveDurationListByFace(list , statDate);

        return durationList;
    }



    /**
     * @description: 通过人脸识别查询驾驶员驾驶时段，指定驾驶员
     * 返回所有驾驶员在所有车辆上的驾驶时段
     * 思路：纯保守计算方式
     * 1。根据 车牌号、驾驶员姓名、时间（time字段）排序；
     * 2。找连续识别成功的记录作为驾驶时段（result连续为0）；
     * 3。计算从第一个0到最后一个0之间的累计时长作为驾驶时长；
     * 4。如果两个连续的0之间，时间差超过半个小时，则认为无效，驾驶时段结束，不计入驾驶时长；
     * @author: zhouxw
     * @date: 2022/11/16 11:17 PM
     * @param: [date]
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatDriveStatus>
     **/
    @Override
    public List<DriveDuration> findDriveDurationByFaceWithDateAndIdCard(String statDate, String idCard) throws Exception {

        String statDateQuery = statDate + "%";

        //1.查找人脸识别结果
        List<DriverFaceResultNode> list = baseMapper.getDriverFaceResultByDateAndIdCard( statDateQuery , idCard);

        //2.对结果进行分析,获取驾驶时段
        List<DriveDuration> durationList = getDriveDurationListByFace(list , statDate);

        return durationList;
    }


    private List<DriveDuration> getDriveDurationListByFace(List<DriverFaceResultNode> list , String statDate) throws Exception{
        DriverFaceResultNode resNow = null;
        DriverFaceResultNode resPre = null;
        List<DriveDuration> durationList = new ArrayList<>();
        long startSecond = 0;
        long endSecond = 0;
        long totalDuration = 0;
        int index = 0;
        for(int i = 1 ; i < list.size(); i++){
            index ++;
            resPre = list.get(i - 1);
            resNow = list.get(i);

            //同一辆车、同一个驾驶员进行比较
            if(resPre.getLicencePlate().equals(resNow.getLicencePlate()) && resPre.getFaceId().equals(resNow.getFaceId())){
                if(resPre.getResult() == 0 && resNow.getResult() == 0){
                    //连续识别成功，开始记录驾驶时段
                    //记录最开始的时间
                    if(startSecond == 0){
                        startSecond = resPre.getTime();
                    }
                    //如果是连续识别成功，判断两次记录间的时间差是否大于半个小时
                    long duration = resNow.getTime() - resPre.getTime();
                    if(duration <= HALF_HOUR){
                        //记录本驾驶时段驾驶时长
                        totalDuration = totalDuration + duration;
                        //记录本驾驶时段结束时间
                        endSecond = resNow.getTime();

                        //如果最后一条记录，是符合条件的记录
                        if(index == list.size() - 1){
                            //排除只有一个点的情况
                            if(startSecond != 0){
                                DriveDuration dur = new DriveDuration();
                                dur.setLicencePlate(resNow.getLicencePlate());
                                dur.setDriverName(resNow.getDriverName());
                                dur.setIdCard(resNow.getIdCard());
                                dur.setStatDate(statDate);
                                dur.setStartSecondTimestamp(startSecond);
                                dur.setEndSecondTimestamp(resNow.getTime());
                                dur.setDuration(totalDuration);
                                durationList.add(dur);
                            }
                        }
                    }else{

                        //如果大于半个小时，则认为记录无效，本驾驶时段结束，将进入到下一个驾驶时段
                        if(startSecond != 0){

                            DriveDuration dur = new DriveDuration();
                            dur.setLicencePlate(resNow.getLicencePlate());
                            dur.setDriverName(resNow.getDriverName());
                            dur.setIdCard(resNow.getIdCard());
                            dur.setStatDate(statDate);
                            //todo:添加停止点判断
                            //判断两个点之间是否存在停止点，如果存在停止点，则前一个 0点 到 停止点之间的时段，记为驾驶时段
                            //查询停止点
                            List<StatStopPoint> stopList = stopPointService.findStopPointByCondition(resNow.getLicencePlate(), statDate, resPre.getTime(), resNow.getTime());
                            if(stopList != null && stopList.size() > 0){
                                dur.setStartSecondTimestamp(startSecond);
                                dur.setEndSecondTimestamp(stopList.get(0).getStopStartTime());
                                dur.setDuration(stopList.get(0).getStopStartTime() - startSecond);
                            }else{
                                dur.setStartSecondTimestamp(startSecond);
                                dur.setEndSecondTimestamp(resPre.getTime());
                                dur.setDuration(totalDuration);
                            }
                            durationList.add(dur);
                        }

                        startSecond = 0 ;
                        endSecond = 0;
                        totalDuration = 0;
                    }

                }else{
                    //非连续识别成功，结束驾驶时段计算
                    if(resPre.getResult() == 0 && resNow.getResult() != 0){

                        //判断 0 和 非0 点之间是否有停止点，如果有停止点，则 0点 到 第一个停止点 作为驾驶时段
                        List<StatStopPoint> stopList = stopPointService.findStopPointByCondition(resNow.getLicencePlate(), statDate, resPre.getTime(), resNow.getTime());
                        DriveDuration dur = new DriveDuration();
                        dur.setLicencePlate(resNow.getLicencePlate());
                        dur.setDriverName(resNow.getDriverName());
                        dur.setIdCard(resPre.getIdCard());
                        dur.setStatDate(statDate);
                        if(stopList != null && stopList.size() > 0){
                            //如果有停止点
                            dur.setStartSecondTimestamp(startSecond);
                            dur.setEndSecondTimestamp(stopList.get(0).getStopStartTime());
                            dur.setDuration(stopList.get(0).getStopStartTime() - startSecond);
                        }else{
                            //如果没有停止点，则到最后一个 0点 结束
                            dur.setStartSecondTimestamp(startSecond);
                            dur.setEndSecondTimestamp(resPre.getTime());
                            dur.setDuration(totalDuration);
                        }


                        durationList.add(dur);
                    }

                    startSecond = 0 ;
                    endSecond = 0;
                    totalDuration = 0;

                }
            }else{
                //切换到其他车辆或者其他驾驶员
                if(startSecond != 0){
                    DriveDuration dur = new DriveDuration();
                    dur.setLicencePlate(resPre.getLicencePlate());
                    dur.setDriverName(resPre.getDriverName());
                    dur.setStatDate(statDate);
                    dur.setIdCard(resPre.getIdCard());
                    dur.setStartSecondTimestamp(startSecond);
                    dur.setEndSecondTimestamp(resPre.getTime());
                    dur.setDuration(totalDuration);
                    durationList.add(dur);
                }

                startSecond = 0 ;
                endSecond = 0;
                totalDuration = 0;
            }

        }
        return durationList;
    }



    /**
     * @description: 通过IC卡查询驾驶员驾驶时段
     * 返回所有驾驶员在所有车辆上的驾驶时段
     * 思路：
     * 1。按照车牌号、时间（time字段）排序(可能存在驾驶员拔卡不记录身份证号的情况，所以为了保证时间的连续性，这里不按照驾驶员身份证号排序)；
     * 2。取 插卡-拔卡 作为一个驾驶时段（option = 1 / 2 ，result = 0）；
     * 3。连续的插卡，取最后一个；连续的拔卡，取第一个。
     * @author: zhouxw
     * @date: 2022/11/16 11:29 PM
     * @param: [date]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DriveDutaion>
     **/
    @Override
    public List<DriveDuration> findDriveDurationByIC(String date) throws Exception {

        String dateQuery = date.substring(0,4)+"-"+ date.substring(4,6)+"-"+date.substring(6,8)+"%";
        //1。按照车牌号、时间排序，获取IC卡使用记录
		Date dateValue = new SimpleDateFormat("yyyy-MM-dd").parse(dateQuery);
        List<DriverICResultNode> list = baseMapper.getDriverICResult(dateValue);

        //2。查找驾驶时段
        List<DriveDuration> durationList = getDriveDrutaionListByIC(list , date);
        return durationList;
    }


    /**
     * @description: 通过IC卡查询驾驶员驾驶时段，指定驾驶员
     * 返回所有驾驶员在所有车辆上的驾驶时段
     * 思路：
     * 1。按照车牌号、时间（time字段）排序(可能存在驾驶员拔卡不记录身份证号的情况，所以为了保证时间的连续性，这里不按照驾驶员身份证号排序)；
     * 2。取 插卡-拔卡 作为一个驾驶时段（option = 1 / 2 ，result = 0）；
     * 3。连续的插卡，取最后一个；连续的拔卡，取第一个。
     * @author: zhouxw
     * @date: 2022/11/16 11:29 PM
     * @param: [date]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DriveDutaion>
     **/
    @Override
    public List<DriveDuration> findDriveDurationByICWithDateAndIdCard(String date , String idCard) throws Exception {

        String dateQuery = date.substring(0,4)+"-"+ date.substring(4,6)+"-"+date.substring(6,8)+"%";
        //1。按照车牌号、时间排序，获取IC卡使用记录
        List<DriverICResultNode> list = baseMapper.getDriverICResultByDateAndIdCard(dateQuery , idCard);

        //2。查找驾驶时段
        List<DriveDuration> durationList = getDriveDrutaionListByIC(list , date);
        return durationList;
    }

    /**
     * @description: 根据IC记录计算驾驶时段
     * 驾驶时段场景：
     * （1）连续插卡：判断两次连续成功的插卡之间的时间段，如果大于4小时，则查询停止点表，判断停车时间，如果4小时内没有停止，则驾驶时段算作4小时；如果有停止，则 插卡到第一次停止作为驾驶时段。
     * （2）连续的插卡-拔卡：判断成功的插卡-拔卡之间的时间段，如果大于4小时，则查询停止点表，如果4小时内没有停止，则驾驶时段算作4小时；如果有停止，则 插卡到第一次停止作为驾驶时段。
     * @author: zhouxw
     * @date: 2022/11/18 6:22 PM
     * @param: [list, date:yyyyMMdd]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DriveDutaion>
     **/
    private List<DriveDuration> getDriveDrutaionListByIC(List<DriverICResultNode> list , String date) throws Exception{
        DriverICResultNode resNow = null;
        DriverICResultNode resPre = null;
        List<DriveDuration> durationList = new ArrayList<>();

		int index = 0;
        for(int i = 1 ; i < list.size() ; i++){
            resPre = list.get(i - 1);
            resNow = list.get(i);

            //同一辆车进行比较
            if(resPre.getLicencePlate().equals(resNow.getLicencePlate())){
				if(index == 0){
					//查找第一条信息是否是拔卡
					if(resPre.getOperation() == 2){
						//如果是拔卡，向前取最后一条记录，如果是插卡，则记录驾驶时长，否则不再记录
						DriverICResultNode lastRecordPreDays = getLastRecordFromPreDays(date, resNow.getLicencePlate(), resNow.getLicenceColor());
						if(lastRecordPreDays != null && lastRecordPreDays.getOperation() == 1){
							//判断是否超过四个小时，如果超过，则查询停止点
							/*if(resPre.getTime() - lastRecordPreDays.getTime() > 3600 * 4){
								// TODO: 2023/1/10 添加驾驶员驾驶时段 停止点判断逻辑
								//查询从上一次插卡到当前次插卡，中间是否存在停止点
								//如果存在停止点，则驾驶时段为上次插卡到第一个停止点；如果不存在停止点，则认为驾驶了4小时
								DriveDuraion dur = new DriveDuraion();
								dur.setIdCard(resPre.getIdCard());
								dur.setLicencePlate(resPre.getLicencePlate());
								dur.setLicenceColor(Integer.parseInt(resPre.getLicenceColor()));
								dur.setDriverName(resPre.getDriverName());
								dur.setStatDate(date.substring(0,4)+"-"+date.substring(4,6)+"-"+date.substring(6,8));
								//查询停止点
								List<StatStopPoint> stopList = stopPointService.findStopPointByCondition(resPre.getLicencePlate(), DateUtil.getDateString(lastRecordPreDays.getTime()), lastRecordPreDays.getTime(), resPre.getTime());
								if(stopList != null && stopList.size() > 0){
									//如果找到停止点
									dur.setStartSecondTimestamp(resPre.getTime());
									dur.setEndSecondTimestamp(stopList.get(0).getStopStartTime());
									dur.setDuration(stopList.get(0).getStopStartTime() - resPre.getTime());
								}else{
									//如果没有找到停止点，则认为4个小时
									dur.setStartSecondTimestamp(resPre.getTime());
									dur.setEndSecondTimestamp(resPre.getTime() + 3600 * 4);
									dur.setDuration(3600 * 4);
								}
								durationList.add(dur);
							}else{*/
							//补全上次插卡到当前拔卡间，每天的上线日期。如果不是当天，则需要生成记录，保存到之前的数据中
							statPreDaysForIdCard(lastRecordPreDays,resPre, date);

							long lastRecordTimestamp = lastRecordPreDays.getTime();
							String dateF = date.substring(0,4)+"-"+date.substring(4,6)+"-"+date.substring(6,8);
							DriveDuration dd = new DriveDuration();
							dd.setLicencePlate(lastRecordPreDays.getLicencePlate());
							dd.setLicenceColor(Integer.parseInt(lastRecordPreDays.getLicenceColor()));
							dd.setIdCard(lastRecordPreDays.getIdCard());
							dd.setStatDate(date);
							dd.setDriverName(lastRecordPreDays.getDriverName());
							if(lastRecordTimestamp >= DateUtil.getDayFirstSecondTimestamp(dateF)){
								//如果是同一天
								dd.setDuration(resPre.getTime() - lastRecordPreDays.getTime());
								dd.setStartSecondTimestamp(lastRecordPreDays.getTime());
							}else{
								//如果不是同一天，则当天的统计，只取当天的开始时间，否则统计出来的驾驶时长可能会超过24小时
								dd.setDuration(resPre.getTime() - DateUtil.getDayFirstSecondTimestamp(resPre.getTime()));
								dd.setStartSecondTimestamp(DateUtil.getDayFirstSecondTimestamp(resPre.getTime()));
							}

							dd.setEndSecondTimestamp(resPre.getTime());
							durationList.add(dd);
							//}
						}
					}
				}
                //连续插卡：判断两次插卡之间的时间差是否大于4小时
                if(resPre.getOperation() == IN_CARD && resNow.getOperation() == IN_CARD && resPre.getResult() == 0 && resNow.getResult() == 0){
                    if(resNow.getTime() - resPre.getTime() > 3600 * 4){
                        // TODO: 2023/1/10 添加驾驶员驾驶时段 停止点判断逻辑
                        //查询从上一次插卡到当前次插卡，中间是否存在停止点
                        //如果存在停止点，则驾驶时段为上次插卡到第一个停止点
                        DriveDuration dur = new DriveDuration();
                        dur.setIdCard(resPre.getIdCard());
                        dur.setLicencePlate(resPre.getLicencePlate());
						dur.setLicenceColor(Integer.parseInt(resPre.getLicenceColor()));
                        dur.setDriverName(resPre.getDriverName());
                        dur.setStatDate(date.substring(0,4)+"-"+date.substring(4,6)+"-"+date.substring(6,8));
                        //查询停止点
                        List<StatStopPoint> stopList = stopPointService.findStopPointByCondition(resPre.getLicencePlate(), DateUtil.getDateString(resPre.getTime()), resPre.getTime(), resNow.getTime());
                        if(stopList != null && stopList.size() > 0){
                            //如果找到停止点
                            dur.setStartSecondTimestamp(resPre.getTime());
                            dur.setEndSecondTimestamp(stopList.get(0).getStopStartTime());
                            dur.setDuration(stopList.get(0).getStopStartTime() - resPre.getTime());
                        }else{
                            //如果没有找到停止点
                            dur.setStartSecondTimestamp(resPre.getTime());
                            dur.setEndSecondTimestamp(resNow.getTime());
                            dur.setDuration(resNow.getTime() - resPre.getTime());
                        }
                        durationList.add(dur);
                    }
                }

                //连续的插卡-拔卡
                if(resPre.getOperation() == IN_CARD && resNow.getOperation() == OUT_CARD && resPre.getResult() == 0 && resNow.getResult() == 0 && resNow.getTime() > resPre.getTime()) {
                    //找到驾驶时段，记录驾驶时段信息
                    DriveDuration dur = new DriveDuration();
                    dur.setIdCard(resPre.getIdCard());
                    dur.setLicencePlate(resPre.getLicencePlate());
					dur.setLicenceColor(Integer.parseInt(resPre.getLicenceColor()));
                    dur.setDriverName(resPre.getDriverName());
                    dur.setStatDate(date);
                    dur.setStartSecondTimestamp(resPre.getTime());
                    dur.setEndSecondTimestamp(resNow.getTime());
                    dur.setDuration(resNow.getTime() - resPre.getTime());
                    durationList.add(dur);
                }
				index ++;
            }else{
				index = 0;
			}
        }
        return durationList;
    }


	/**
	 * 针对连续在线的终端，统计本次计算之前的在线数据，并直接保存到数据库
	 * @param lastRecordPreDays 之前天中最后一次插卡的记录
	 * @param resPre 本次统计时，第一个统计点
	 * @param date
	 * @throws Exception
	 */
	private void statPreDaysForIdCard(DriverICResultNode lastRecordPreDays, DriverICResultNode resPre, String date) throws Exception{
		long lastRecordTimestamp = lastRecordPreDays.getTime();
		List<String> dateList = DateUtil.getDateList(lastRecordTimestamp, resPre.getTime()-24*3600);
		String dateF = date.substring(0,4)+"-"+date.substring(4,6)+"-"+date.substring(6,8);
		List<DriveDuration> preDriveDurationList = new ArrayList<>();
		if(lastRecordTimestamp < DateUtil.getDayFirstSecondTimestamp(dateF)){
			//如果是之前的日期，表示之前的日期中，开始那一天的时段是从开始时间点到当天最后，后边的都是全天的
			for(int idx = 0 ; idx < dateList.size(); idx++){
				DriveDuration dd = new DriveDuration();
				dd.setLicencePlate(lastRecordPreDays.getLicencePlate());
				dd.setLicenceColor(Integer.parseInt(lastRecordPreDays.getLicenceColor()));
				dd.setIdCard(lastRecordPreDays.getIdCard());
				dd.setStatDate(dateList.get(idx));
				dd.setDriverName(lastRecordPreDays.getDriverName());
				dd.setState(STATE_APPEND);
				if(idx == 0){
					dd.setDuration(DateUtil.getDayLastSecondTimestamp(lastRecordPreDays.getTime()) - lastRecordPreDays.getTime());
					dd.setStartSecondTimestamp(lastRecordPreDays.getTime());
					dd.setEndSecondTimestamp(DateUtil.getDayLastSecondTimestamp(lastRecordPreDays.getTime()));
				}else{
					dd.setDuration(24*3600);
					dd.setStartSecondTimestamp(DateUtil.getDayFirstSecondTimestamp(dateList.get(idx)));
					dd.setEndSecondTimestamp(DateUtil.getDayLastSecondTimestamp(DateUtil.getDayFirstSecondTimestamp(dateList.get(idx))));
				}
				preDriveDurationList.add(dd);
			}
			//计算时长和里程
			//按照统计日期进行分组
			Map<String,List<DriveDuration>> mapR = new HashMap<>();
			preDriveDurationList.forEach(item -> {
				String statDate = item.getStatDate();
				List<DriveDuration> ddList = mapR.get(statDate);
				if(ddList == null){
					ddList = new ArrayList<>();
					mapR.put(statDate, ddList);
				}
				ddList.add(item);
			});

			//删除之前报错过的后期执行追加的数据
			//对日期按照月份分组
			Map<String,List<String>> dateMap = new HashMap<>();
			dateList.forEach(item -> {
				String month = item.substring(0,7);
				List<String> tmpList = dateMap.get(month);
				if(tmpList == null){
					tmpList = new ArrayList<>();
					dateMap.put(month,tmpList);
				}
				tmpList.add(item);
			});
			dateMap.forEach((k,v) -> {
				String month = k.replace("-","");
				if(v != null && v.size() > 0){
					baseMapper.deleteAppendData(month, v);
					try {
						driverDurationService.deleteAppendData(v,month);
					} catch (Exception e) {
						log.error("删除驾驶时段出错",e);
					}
				}
			});

			//对每个驾驶员进行操作：查询驾驶里程
			List<StatDriveStatus> sdsList = new ArrayList<>();
			mapR.forEach((k, v) -> {
				//按照日期分组，驾驶时段内依然是连续的，所以可以调用下边的计算驾驶状态的方法
				StatDriveStatus sds = statDriveStatusForDriver(v);
				sds.setState(STATE_APPEND);
				sds.setCreateDate(new Date());
				try {
					List<StatDriveStatus> sList = new ArrayList<>();
					sList.add(sds);
					baseMapper.saveBatch(sList,sds.getStatDate().substring(0,7).replace("-",""));
				} catch (Exception e) {
					log.error("存储驾驶状态信息失败",e);
				}
			});

			//存储驾驶时段信息
			List<StatDriverDuration> durationList = new ArrayList<>();
			sdsList.forEach(item -> {
				//解析驾驶时段：格式 {"粤A12345":"1667404810~1667404992,1667455553~1667460741,","粤A41022":"1667483208~1667483328,1667484000~1667487600,"}
				HashMap map = JSONObject.parseObject(item.getDurationList(), HashMap.class);
				if(map.size() > 0){

					for(Object v : map.keySet()){
						String licencePlate = v.toString().split("~")[0];
						Integer licenceColor = Integer.parseInt(v.toString().split("~")[1]);
						Object ds = map.get(v);

						String[] durations = ds.toString().split(",");
						if(durations.length > 0){
							for(String d : durations){
								String[] se = d.split("~");
								StatDriverDuration duration = new StatDriverDuration();
								duration.setIdCard(item.getIdCard());
								duration.setDriverName(item.getDriverName());
								duration.setStatDate(item.getStatDate());
								duration.setLicencePlate(licencePlate);
								duration.setLicenceColor(licenceColor);
								duration.setState(STATE_APPEND);
								if(se.length > 1){
									long start = Long.parseLong(se[0]);
									long end = Long.parseLong(se[1]);
									duration.setDurationStartTime(start);
									duration.setDurationEndTime(end);
									duration.setDriveDuration(end - start);
									duration.setCreateTime(new Date());
									durationList.add(duration);
								}
							}
						}
					}
				}
			});
			if(durationList != null && durationList.size() > 0){
				driverDurationService.saveBatch(durationList, date.substring(0,6).replace("-",""));
			}
		}
	}

	/**
	 * 向前反查每天最后一个插拔卡记录
	 * @param nowDate 当前天的日期
	 * @return
	 */
	private DriverICResultNode getLastRecordFromPreDays(String nowDate,String licencePlate,String licenceColor) throws Exception{
		//最大离线时间
		int maxOfflineDaysCount = CommonConstant.LONG_OFFLINE_DAYS_COUNT;

		for(int i = 1 ; i < maxOfflineDaysCount; i++){
			String date = DateUtil.getDateBeforeDayStr(nowDate.substring(0,4)+"-"+nowDate.substring(4,6)+"-"+nowDate.substring(6,8), i);
			DriverICResultNode node = baseMapper.getLastICResultByDate(date+"%", licencePlate, licenceColor);
			if(node != null){
				return node;
			}
		}

		return null;
	}


    /**
     * @description: 统计驾驶情况
     * 思路：
     * 1。综合统计驾驶时段：如果 人脸识别 和 IC卡 时段有重合，则以人脸识别为准，即使是一对多，或者多对一；
     * 2。根据驾驶时段，查找定位点（location表），计算驾驶里程
     * @author: zhouxw
     * @date: 2022/11/16 11:35 PM
     * @param: [date:yyyyMMdd]
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatDriveStatus>
     **/
    @Override
    public void statDriverStatus(String date) throws Exception {

        //校验日期格式
        Date statDate = new Date();
        try {
            statDate = sdfHolder.get().parse(date);
        } catch (ParseException e) {
            log.error("[车辆行驶时长统计]执行出现异常，日期 " + date + " 格式错误 "  , e);
            throw new Exception("日期格式错误：" + date);
        }

        //更改日期格式
        String dateQuery = date.substring(0,4)+"-"+date.substring(4,6)+"-"+date.substring(6,8);
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION , "校验日期格式完成");

        //查询目标月表是否存在，如果不存在，则创建表
        String tableName = StatisticConstants.DRIVER_DRIVE_DURATION_TEMPLATE_TABLE + "_" + date.substring(0 , 6);
        dataUtils.checkTableExistAndCreate(tableName , StatisticConstants.DRIVER_DRIVE_DURATION_TEMPLATE_TABLE);
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION , "校验月表完成");

        String dTableName = StatisticConstants.DRIVER_DRIVER_DURATION_TEMPLATE_TABLE + "_" + date.substring(0 , 6);
        dataUtils.checkTableExistAndCreate(dTableName , StatisticConstants.DRIVER_DRIVER_DURATION_TEMPLATE_TABLE);
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION , "校验驾驶时段表完成");



        //为防止重复执行造成指标数据重复，需要先删除之前写入的指标数据
		Date dateValue = new SimpleDateFormat("yyyyMMdd").parse(date);
        baseMapper.deleteDataByDate(date.substring(0,6) , dateValue);
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION , "删除之前跑过的指标数据完成");

        driverDurationService.deleteDataByDate(date.substring(0,6), date);
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION , "删除之前跑过的驾驶时段数据完成");



        //1.获取人脸识别驾驶时段列表
        List<DriveDuration> faceList = findDriveDurationByFace(dateQuery);
        //按照车牌号-身份证号进行分组
        Map<String,List<DriveDuration>> faceMap = new HashMap<>();
        faceList.stream().forEach(item -> {
            List<DriveDuration> list = faceMap.get(item.getLicencePlate().trim()+"-"+item.getIdCard().trim());
            if(list == null){
                list = new ArrayList<>();
                list.add(item);
                faceMap.put(item.getLicencePlate().trim()+"-"+item.getIdCard().trim() , list);
            }else{
                list.add(item);
            }
        });
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION , "获取人脸识别驾驶时段列表完成");

        //2.获取IC卡驾驶时段列表
        List<DriveDuration> icList = findDriveDurationByIC(date);
        //按照车牌号-身份证号进行分组
        Map<String,List<DriveDuration>> icMap = new HashMap<>();
        icList.stream().forEach(item -> {
            List<DriveDuration> list = icMap.get(item.getLicencePlate().trim()+"-"+item.getIdCard().trim());
            if(list == null){
                list = new ArrayList<>();
                list.add(item);
                icMap.put(item.getLicencePlate().trim()+"-"+item.getIdCard().trim() , list);
            }else{
                list.add(item);
            }
        });
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION , "获取IC卡驾驶时段完成");

        //3.依次对每个驾驶员每辆车进行分析，获取最后的综合驾驶时段
        //驾驶时段存在相交的，以人脸识别为主；ic时段在人脸识别时段之外的，添加到最终时段中
        List<DriveDuration> finalList = new ArrayList<>();
        //以人脸识别为主，所以，首先添加所有人脸识别的结果
        finalList.addAll(faceList);
        //查找在ic中有，并且在人脸识别中没有的，添加到最终list中
        for(String ic : icMap.keySet()){
            List<DriveDuration> ics = icMap.get(ic);
            List<DriveDuration> faces = faceMap.get(ic);
            for(DriveDuration icDuration : ics){
                if(faces == null){
                    finalList.add(icDuration);
                    continue;
                }
                for(DriveDuration faceDuration : faces){
                    if((icDuration.getStartSecondTimestamp() >= faceDuration.getEndSecondTimestamp() && icDuration.getEndSecondTimestamp() >= faceDuration.getEndSecondTimestamp()) ||(icDuration.getStartSecondTimestamp() <= faceDuration.getStartSecondTimestamp() && icDuration.getEndSecondTimestamp() <= faceDuration.getStartSecondTimestamp()) ){
                        finalList.add(icDuration);
                    }
                }
            }
        }
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION , "最终驾驶时段统计完成");

        //4.记录驾驶情况（驾驶时长，驾驶里程）
        //4.1对最终驾驶时段信息，按照驾驶员身份证号进行分组，统计驾驶里程
        Map<String,List<DriveDuration>> finalMap = new HashMap<>();
        finalList.stream().forEach(item -> {
            List<DriveDuration> list = finalMap.get(item.getIdCard().trim());
            if(list == null){
                list = new ArrayList<>();
                list.add(item);
                finalMap.put(item.getIdCard().trim() , list);
            }else{
                list.add(item);
            }
        });

        //4.2对每个驾驶员进行操作：查询驾驶里程
        List<StatDriveStatus> sdsList = new ArrayList<>();
        sdsList = Collections.synchronizedList(sdsList);
        CountDownLatch countDownLatch = new CountDownLatch(finalMap.size());
		List<StatDriveStatus> finalSdsList = sdsList;
        for(String idCard : finalMap.keySet()){
            thread_pool.submit(() -> {
				try {
					StatDriveStatus sds = statDriveStatusForDriver(finalMap.get(idCard));
					sds.setCreateDate(new Date());
					finalSdsList.add(sds);
				}catch (Exception e){
					log.error("执行报错",e);
				}finally{
					countDownLatch.countDown();
				}
            });
        }
        countDownLatch.await();
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION , "对每个驾驶员驾驶时段和驾驶里程统计完成，共 "+finalMap.size()+" 名驾驶员");

        //5.存储到数据表
        //存储驾驶状态信息
        if(sdsList != null && sdsList.size() > 0){
            baseMapper.saveBatch(sdsList,date.substring(0,6).replace("-",""));
        }
        //存储驾驶时段信息
        List<StatDriverDuration> durationList = new ArrayList<>();
        sdsList.forEach(item -> {
            //解析驾驶时段：格式 {"粤A12345":"1667404810~1667404992,1667455553~1667460741,","粤A41022":"1667483208~1667483328,1667484000~1667487600,"}
            HashMap map = JSONObject.parseObject(item.getDurationList(), HashMap.class);
            if(map.size() > 0){

                for(Object v : map.keySet()){
					String licencePlate = v.toString().split("~")[0];
					Integer licenceColor = Integer.parseInt(v.toString().split("~")[1]);
					Object ds = map.get(v);

                    String[] durations = ds.toString().split(",");
                    if(durations.length > 0){
                        for(String d : durations){
							String[] se = d.split("~");
							StatDriverDuration duration = new StatDriverDuration();
							duration.setIdCard(item.getIdCard());
							duration.setDriverName(item.getDriverName());
							duration.setStatDate(item.getStatDate());
							duration.setLicencePlate(licencePlate);
							duration.setLicenceColor(licenceColor);
                            if(se.length > 1){
                                long start = Long.parseLong(se[0]);
                                long end = Long.parseLong(se[1]);
                                duration.setDurationStartTime(start);
                                duration.setDurationEndTime(end);
                                duration.setDriveDuration(end - start);
                                duration.setCreateTime(new Date());
                                durationList.add(duration);
                            }
                        }
                    }
                }
            }
        });
		if(durationList != null && durationList.size() > 0){
			driverDurationService.saveBatch(durationList, date.substring(0,6).replace("-",""));
		}

        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_DRIVER_DRIVE_DURATION , "统计数据入库完成");
    }

    /**
     * @description: 为某一个驾驶员统计驾驶情况
     * 当前list为某个驾驶员的最终驾驶时段list
     * @author: zhouxw
     * @date: 2022/11/17 11:22 AM
     * @param: [list]
     * @return: com.xh.vdm.statistic.entity.StatDriveStatus
     **/
    private StatDriveStatus statDriveStatusForDriver(List<DriveDuration> list){
        if(list == null || list.size() < 1){
            return null;
        }
        long totalDuration = 0;
        double totalMileage = 0;
        String day = list.get(0).getStatDate();
        String licenceplate = list.get(0).getLicencePlate();
        long startSecondTime = list.get(0).getStartSecondTimestamp();
        long endSecondTime = list.get(0).getEndSecondTimestamp();
        //记录驾驶车辆的时段:车牌号-驾驶时段
        Map<String,StringBuffer> durationMap = new HashMap<>();
        for(DriveDuration dd : list){
            //1.查询指定车辆指定天指定时间段的location信息
            //当开始时间不等于结束时间时，才查询location，减少查询量
            if(dd.getStartSecondTimestamp() != dd.getEndSecondTimestamp()){
                //2.记录驾驶时段列表
                StringBuffer sb = durationMap.get(dd.getLicencePlate()+"~"+dd.getLicenceColor());
                if(sb == null){
                    sb = new StringBuffer();
                }
                durationMap.put(dd.getLicencePlate()+"~"+dd.getLicenceColor() , sb.append(dd.getStartSecondTimestamp()+"~"+dd.getEndSecondTimestamp()+","));

                //3.记录总驾驶时长
                totalDuration = totalDuration + (dd.getEndSecondTimestamp() - dd.getStartSecondTimestamp());

                //3.计算总驾驶里程（这里记录的是总里程，不是完整里程）
                List<LocationKudu> locations = locationService.findLocationByCondition(dd.getLicencePlate(), dd.getLicenceColor(), dd.getStartSecondTimestamp() ,dd.getEndSecondTimestamp());
                for(int i = 1 ; i < locations.size() ; i++){
                    LocationKudu locationNow = locations.get(i - 1);
                    LocationKudu locationOld = locations.get(i);
                    double perDistance = DistanceUtils.wgs84Distance(locationNow.getLongitude(),locationNow.getLatitude(),locationOld.getLongitude() , locationOld
                            .getLatitude());
                    totalMileage = totalMileage + perDistance;
                }
            }
        }

        StatDriveStatus ss = new StatDriveStatus();
        ss.setIdCard(list.get(0).getIdCard());
        ss.setDriverName(list.get(0).getDriverName());
        ss.setStatDate(list.get(0).getStatDate());
        ss.setDriveDuration(totalDuration);
        ss.setDriveMileage(totalMileage);
        ss.setDurationList(JSONObject.toJSONString(durationMap));
        return ss;
    }

    /**
     * @description: 统计驾驶情况，指定驾驶员
     * 思路：
     * 1。综合统计驾驶时段：如果 人脸识别 和 IC卡 时段有重合，则以人脸识别为准，即使是一对多，或者多对一；
     * 2。根据驾驶时段，查找定位点（location表），计算驾驶里程
     * @author: zhouxw
     * @date: 2022/11/16 11:35 PM
     * @param: [date:yyyyMMdd]
     * @return: java.util.List<com.xh.vdm.statistic.entity.StatDriveStatus>
     **/
    @Override
    public DriveDurationBaseInfo statDriverStatusByDateAndIdCard(String date , String idCard) throws Exception {

        //校验日期格式
        Date statDate = new Date();
        try {
            statDate = sdfHolder.get().parse(date);
        } catch (ParseException e) {
            log.error("[车辆行驶时长统计]执行出现异常，日期 " + date + " 格式错误 "  , e);
            throw new Exception("日期格式错误：" + date);
        }

        //更改日期格式
        String dateQuery = date.substring(0,4)+"-"+date.substring(4,6)+"-"+date.substring(6,8);

        //查询目标月表是否存在，如果不存在，则创建表
        String tableName = StatisticConstants.DRIVER_DRIVE_DURATION_TEMPLATE_TABLE + "_" + date.substring(0 , 6);
        dataUtils.checkTableExistAndCreate(tableName , StatisticConstants.DRIVER_DRIVE_DURATION_TEMPLATE_TABLE);

        //1.获取人脸识别驾驶时段列表
        List<DriveDuration> faceList = findDriveDurationByFaceWithDateAndIdCard(dateQuery, idCard);
        //按照车牌号-身份证号进行分组
        Map<String,List<DriveDuration>> faceMap = new HashMap<>();
        faceList.stream().forEach(item -> {
            List<DriveDuration> list = faceMap.get(item.getLicencePlate().trim()+"-"+item.getIdCard().trim());
            if(list == null){
                list = new ArrayList<>();
                list.add(item);
                faceMap.put(item.getLicencePlate().trim()+"-"+item.getIdCard().trim() , list);
            }else{
                list.add(item);
            }
        });

        //2.获取IC卡驾驶时段列表
        List<DriveDuration> icList = findDriveDurationByICWithDateAndIdCard(date, idCard);
        //按照车牌号-身份证号进行分组
        Map<String,List<DriveDuration>> icMap = new HashMap<>();
        icList.stream().forEach(item -> {
            List<DriveDuration> list = icMap.get(item.getLicencePlate().trim()+"-"+item.getIdCard().trim());
            if(list == null){
                list = new ArrayList<>();
                list.add(item);
                icMap.put(item.getLicencePlate().trim()+"-"+item.getIdCard().trim() , list);
            }else{
                list.add(item);
            }
        });

        //3.依次对每个驾驶员每辆车进行分析，获取最后的综合驾驶时段
        //驾驶时段存在相交的，以人脸识别为主；ic时段在人脸识别时段之外的，添加到最终时段中
        List<DriveDuration> finalList = new ArrayList<>();
        //以人脸识别为主，所以，首先添加所有人脸识别的结果
        finalList.addAll(faceList);
        //查找在ic中有，并且在人脸识别中没有的，添加到最终list中
        for(String ic : icMap.keySet()){
            List<DriveDuration> ics = icMap.get(ic);
            List<DriveDuration> faces = faceMap.get(ic);
            for(DriveDuration icDuration : ics){
                if(faces == null){
                    finalList.add(icDuration);
                    continue;
                }
                for(DriveDuration faceDuration : faces){
                    if((icDuration.getStartSecondTimestamp() >= faceDuration.getEndSecondTimestamp() && icDuration.getEndSecondTimestamp() >= faceDuration.getEndSecondTimestamp()) ||(icDuration.getStartSecondTimestamp() <= faceDuration.getStartSecondTimestamp() && icDuration.getEndSecondTimestamp() <= faceDuration.getStartSecondTimestamp()) ){
                        finalList.add(icDuration);
                    }
                }
            }
        }

        //4.记录驾驶情况（驾驶时长，驾驶里程）
        //4.1对最终驾驶时段信息，按照驾驶员身份证号进行分组，统计驾驶里程
        Map<String,List<DriveDuration>> finalMap = new HashMap<>();
        finalList.stream().forEach(item -> {
            List<DriveDuration> list = finalMap.get(item.getIdCard().trim());
            if(list == null){
                list = new ArrayList<>();
                list.add(item);
                finalMap.put(item.getIdCard().trim() , list);
            }else{
                list.add(item);
            }
        });

        //4.2对每个驾驶员进行操作：查询驾驶里程
        List<StatDriveStatus> sdsList = new ArrayList<>();
        CountDownLatch countDownLatch = new CountDownLatch(finalMap.size());
        for(String idCardItem : finalMap.keySet()){
            thread_pool.submit(() -> {
                StatDriveStatus sds = statDriveStatusForDriver(finalMap.get(idCardItem));
                sds.setCreateDate(new Date());
                sdsList.add(sds);
                countDownLatch.countDown();
            });
        }
        countDownLatch.await();

        if(sdsList == null || sdsList.size() < 1){
            return null;
        }
        StatDriveStatus sds = sdsList.get(0);
        if(sds == null){
            return null;
        }
        DriveDurationBaseInfo info = new DriveDurationBaseInfo();
        info.setDate(sds.getStatDate());
        info.setIdCard(sds.getIdCard());
        //格式化时长和里程
        long hours = sds.getDriveDuration() / 3600;
        long minutes = (sds.getDriveDuration() % 3600) / 60;
        info.setDriveDuration(hours + "时" + minutes + "分");
        info.setDriveMileage((sds.getDriveMileage() / 1000));
        return info;
    }

    @Override
    public DriveInfo statDriveInfo(String month, String idCard) throws Exception {
        String monthStr = month.replace("-" , "");
        String monthLike = month + "%";

        //1。驾驶天数
        int days = baseMapper.getDriveDaysByMonthAndIdCard(monthStr , monthLike , idCard);

        //2。总驾驶时长
        long totalDuration = baseMapper.getTotalDriveDurationByMonthAndIdCard(monthStr , monthLike , idCard);

        //3。总驾驶里程
        long totalMileage = baseMapper.getTotalDriveMileageByMonthAndIdCard(monthStr , monthLike , idCard);

        //4。平均驾驶时长
        double averageDuration = days==0?0: MathUtil.divideRoundDouble(totalDuration , days , 2);

        //5。平均驾驶里程
        double averageMileage = days==0?0:MathUtil.divideRoundDouble(totalMileage , days , 2);

        DriveInfo info = new DriveInfo();
        info.setDays(days);
        info.setTotalDriveMileage(totalMileage);
        info.setTotalDriveDuration(totalDuration);
        info.setAverageDuration(averageDuration);
        info.setAverageMileage(averageMileage);

        return info;
    }

    @Override
    public List<DriveDurationBaseInfo> findDriveStatusList(String month, String idCard) throws Exception {
        String monthStr = month.replace("-" , "");
        String monthLike = month + "%";

        return baseMapper.getDriveStatusList(monthStr , monthLike , idCard);
    }

	@Override
	public List<DateAndMileageAndDuration> findMileageAndDurationEveryDayInMonth(String month, String idCard) throws Exception {
		month = month.replace("-","");
		return baseMapper.getMileageAndDurationEveryDayInMonth(month, idCard);
	}

	@Override
    public List<DeptAndIdCardAndCount> findDriverDriveDaysInMonth(String month, Long deptId) {
        List<DeptAndIdCardAndCount> list = baseMapper.getDriverDriveDaysInMonth(month , deptId);
        return list;
    }

    @Override
    public int findTotalDriveDaysInMonth(String month, Long deptId) throws Exception {
        return baseMapper.getTotalDriveDaysInMonth(month , deptId);
    }

    @Override
    public List<DeptAndIdCardAndCount> findDriverDriveDaysInMonthDuration(String month, Long deptId , String startDate , String endDate) throws Exception{
        return baseMapper.getDriverDriveDaysInMonthDuration(month , deptId , startDate , endDate);
    }

	@Override
	public int findDriveDaysInMonthDuration(String idCard, String month) throws Exception {
		month = month.replace("-","");
		return baseMapper.getDriveDaysCountInMonthByIdCard(idCard, month);
	}

	@Override
    public List<DeptAndDriveStatus> findDriverDriveMileageEveryDay(Long deptId, String month, String startDate, String endDate) throws Exception {
        return baseMapper.getDeptDriveMileageCount(deptId, month, startDate, endDate);
    }

    @Override
    public List<DateAndData> findCompanyTotalMileageInMonth(Long deptId, String month) throws Exception {
        return baseMapper.getTotalMileageInMonth(deptId , month);
    }

    @Override
    public double findTotalMileageInMonth(Long deptId, String month) throws Exception {
        return baseMapper.getTotalMileageInMonthAll(deptId , month);
    }

    @Override
    public List<DeptAndDateAndData> findTotalDurationEveryDay(Long deptId, String startDate, String endDate) throws Exception {
        return baseMapper.getTotalDurationForDeptEveryDay(deptId , startDate.substring(0 , 7).replace("-" , "") , startDate , endDate);
    }

    @Override
    public List<DateAndCount> findDriverCountEveryDay(Long deptId, String month, String startDate, String enDate) throws Exception {
        return baseMapper.getDriverCountEveryDay(deptId, month, startDate, enDate);
    }

    @Override
    public int findDriverCountByDate(String month, String date , Long deptId) throws Exception {
        return baseMapper.getDriverCountByDate(month , date , deptId);
    }

    @Override
    public double findTotalDriveDurationByDate(String month, String date, Long deptId) throws Exception {
        return baseMapper.getTotalDriveDurationByDate(month , date , deptId);
    }

    @Override
    public double findTotalDriveMileageByDate(String month, String date, Long deptId) throws Exception {
        return baseMapper.getTotalDriveMileageByDate(month ,date ,deptId);
    }

    @Override
    public DriverFaceResultNode findNewestDriverWithFaceByLicencePlate(String licencePlate) throws Exception {
        return baseMapper.getNewestDriverWithFaceByLicencePlate(licencePlate);
    }

	@Override
	public List<StatDriveStatus> findDriveStatusByIdCard(String idCard, String statDate, String month) throws Exception {
		month = month.replace("-","");
		return baseMapper.getDriveStatusByIdCard(idCard, statDate, month);
	}

	@Override
	public TotalMileageAndDurationInMonth findDriveStatusByIdCardMonth(String idCard, String month) throws Exception {
		month = month.replace("-","");
		return baseMapper.getDriveStatusByIdCardMonth(idCard, month);
	}
}

