package com.xh.vdm.statistic.task;

import com.alibaba.nacos.api.utils.StringUtils;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.LogUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class DBCacheTask {


	@Resource
	private ICacheSegLimitSpeedMapService cacheSegLimitSpeedMapService;


	@Resource
	private ICacheSegLimitSpeedTerminalService cacheSegLimitSpeedTerminalService;

	@Resource
	private ICacheVehicleOnlineOrOfflineService cacheVehicleOnlineOrOfflineService;

	@Resource
	private ICacheSecurityInfoService cacheSecurityInfoService;

	@Resource
	private ICacheVehicleOnlineRateService cacheVehicleOnlineRateService;

	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private ICacheStatDriftRateService cacheStatDriftRateService;


	private static final ThreadLocal<SimpleDateFormat> sdfYMD = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd"));
	private static final ThreadLocal<SimpleDateFormat> sdfYM = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM"));

	@Resource
	private LogUtil logUtil;


	//******************************************统计报表类DB缓存*****************************************************

	/**
	 * @description: 统计车辆地图超速 DB缓存
	 * 每天0点5分，统计前一天的数据并入库
	 * @author: zhouxw
	 * @date: 2023-02-41 09:20:10
	 * @param: []
	 * @return: void
	 **/
	//@Scheduled(cron="0 5 0 * * ?")
	//@XxlJob("statisticSegLimitSpeedMapCache")
	public void statisticSegLimitSpeedMapCache(){

		XxlJobHelper.log("将要执行定时任务：statisticSegLimitSpeedMapCache");
		long start = System.currentTimeMillis();
		long end = 0;
		long startTime = 0;
		try{

			//获取前一天的数据
			Date date = new Date();
			long tmpSecondTimestamp = (date.getTime() - 24 * 3600 * 1000) / 1000;
			startTime = DateUtil.getDayFirstSecondTimestamp(tmpSecondTimestamp);
			long endTime = DateUtil.getDayLastSecondTimestamp(tmpSecondTimestamp);

			//2.执行数据统计
			cacheSegLimitSpeedMapService.statisticsSegLimitSpeedMap(startTime * 1000, endTime * 1000);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_MAP,"统计车辆地图超速DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

		}catch (Exception e){
			log.error("[定时任务]统计车辆地图超速DB缓存失败",e);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_MAP,"统计车辆地图超速DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败,"+e.getMessage());
		}
		log.error("[定时任务]统计车辆地图超速DB缓存成功");
		XxlJobHelper.log("定时任务执行结束：statisticSegLimitSpeedMapCache");
	}



	/**
	 * @description: 统计车辆地图超速 DB缓存 执行当天的数据统计
	 * 每个小时的 30 分，执行当天的数据统计，更新数据
	 * @author: zhouxw
	 * @date: 2023-02-41 09:20:10
	 * @param: []
	 * @return: void
	 **/
	//@Scheduled(cron="0 30 * * * ?")
	//@XxlJob("statisticSegLimitSpeedMapCacheForThisDay")
	public void statisticSegLimitSpeedMapCacheForThisDay(){
		XxlJobHelper.log("将要执行定时任务：statisticSegLimitSpeedMapCacheForThisDay");
		long start = System.currentTimeMillis();
		long end = 0;
		long startTime = 0;
		try{

			//获取当天的数据
			Date date = new Date();
			long tmpSecondTimestamp = (date.getTime()) / 1000;
			startTime = DateUtil.getDayFirstSecondTimestamp(tmpSecondTimestamp);
			long endTime = DateUtil.getDayLastSecondTimestamp(tmpSecondTimestamp);

			//2.执行数据统计
			cacheSegLimitSpeedMapService.statisticsSegLimitSpeedMap(startTime * 1000, endTime * 1000);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_MAP,"统计车辆地图超速DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

		}catch (Exception e){
			log.error("[定时任务]统计车辆地图超速DB缓存失败",e);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_MAP,"统计车辆地图超速DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败,"+e.getMessage());
		}
		log.error("[定时任务]统计车辆地图超速DB缓存成功");
		XxlJobHelper.log("定时任务执行结束：statisticSegLimitSpeedMapCacheForThisDay");
	}




	/**
	 * @description: 统计 终端限速 DB缓存
	 * 每天0点10分，统计前一天的数据并入库
	 * @author: zhouxw
	 * @date: 2023-02-41 09:20:10
	 * @param: []
	 * @return: void
	 **/
	//@Scheduled(cron="0 10 0 * * ?")
	//@XxlJob("statisticSegLimitSpeedTerminalCache")
	public void statisticSegLimitSpeedTerminalCache(){
		XxlJobHelper.log("将要执行定时任务：statisticSegLimitSpeedTerminalCache");
		long start = System.currentTimeMillis();
		long end = 0;
		long startTime = 0;

		try{
			//获取前一天的数据
			Date date = new Date();
			long tmpSecondTimestamp = (date.getTime() - 24 * 3600 * 1000) / 1000;
			startTime = DateUtil.getDayFirstSecondTimestamp(tmpSecondTimestamp);
			long endTime = DateUtil.getDayLastSecondTimestamp(tmpSecondTimestamp);

			//2.执行数据统计
			cacheSegLimitSpeedTerminalService.statisticsSegLimitSpeedTerminal(startTime * 1000, endTime * 1000);

			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_TERMINAL,"统计终端限速DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

		}catch (Exception e){
			log.error("[定时任务]统计车辆终端限速DB缓存失败",e);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_TERMINAL,"统计终端限速DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败,"+e.getMessage());
		}
		log.error("[定时任务]统计车辆终端限速DB缓存成功");
		XxlJobHelper.log("定时任务执行结束：statisticSegLimitSpeedTerminalCache");
	}



	/**
	 * @description: 统计 终端限速 DB缓存 统计当天的数据
	 * 每个小时的30分，统计当天的数据
	 * @author: zhouxw
	 * @date: 2023-02-41 09:20:10
	 * @param: []
	 * @return: void
	 **/
	//@Scheduled(cron="0 30 * * * ?")
	//@XxlJob("statisticSegLimitSpeedTerminalCacheForThisDay")
	public void statisticSegLimitSpeedTerminalCacheForThisDay(){
		XxlJobHelper.log("将要执行定时任务：statisticSegLimitSpeedTerminalCacheForThisDay");
		long start = System.currentTimeMillis();
		long end = 0;
		long startTime = 0;

		try{
			//获取前一天的数据
			Date date = new Date();
			long tmpSecondTimestamp = (date.getTime()) / 1000;
			startTime = DateUtil.getDayFirstSecondTimestamp(tmpSecondTimestamp);
			long endTime = DateUtil.getDayLastSecondTimestamp(tmpSecondTimestamp);

			//2.执行数据统计
			cacheSegLimitSpeedTerminalService.statisticsSegLimitSpeedTerminal(startTime * 1000, endTime * 1000);

			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_TERMINAL,"统计终端限速DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

		}catch (Exception e){
			log.error("[定时任务]统计车辆终端限速DB缓存失败",e);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SEG_LIMIT_SPEED_TERMINAL,"统计终端限速DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败,"+e.getMessage());
		}
		log.error("[定时任务]统计车辆终端限速DB缓存成功");
		XxlJobHelper.log("定时任务执行结束：statisticSegLimitSpeedTerminalCacheForThisDay");
	}


	/**
	 * @description: 统计 车辆上下线查询
	 * 每天0点5分，统计前一天的数据并入库
	 * 支持单天统计或者连续多天统计
	 * @author: zhouxw
	 * @date: 2023-02-41 09:20:10
	 * @param: dayDuration yyyy-MM-dd / yyyy-MM-dd~yyyy-MM-dd
	 * @return: void
	 **/
	//@Scheduled(cron="0 15 0 * * ?")
	//@XxlJob("statisticVehicleOnlineOrOfflineCache")
	public void statisticVehicleOnlineOrOfflineCache(String dayDuration){
		//兼容自定义接口和xxl接口调用
		if(StringUtils.isEmpty(dayDuration)){
			//如果没有传递重跑自定义接口参数，则查询xxljob接口参数
			//在xxl-job页面上传参，只需要传入 参数值即可
			dayDuration = XxlJobHelper.getJobParam();
		}
		XxlJobHelper.log("将要执行定时任务：statisticVehicleOnlineOrOfflineCache");
		long start = System.currentTimeMillis();
		long end = 0;
		long startTime = 0;
		long endTime = 0;
		try{
			if(StringUtils.isEmpty(dayDuration)){
				//如果没有指定时间，则统计前一天的数据
				//获取前一天的数据
				Date date = new Date();
				long tmpSecondTimestamp = (date.getTime() - 24 * 3600 * 1000) / 1000;
				startTime = DateUtil.getDayFirstSecondTimestamp(tmpSecondTimestamp);
				endTime = DateUtil.getDayLastSecondTimestamp(tmpSecondTimestamp);
			}else{
				try{
					//如果指定了时间，则统计指定时间的数据
					if(dayDuration.indexOf("~") < 0){
						//如果统计单天的数据
						startTime = DateUtil.getDayFirstSecondTimestamp(dayDuration);
						endTime = DateUtil.getDayLastSecondTimestamp(startTime);
					}else{
						//如果统计连续的多天的数据
						String startDay = dayDuration.split("~")[0];
						String endDay = dayDuration.split("~")[1];
						startTime = DateUtil.getDayFirstSecondTimestamp(startDay);
						endTime = DateUtil.getDayLastSecondTimestamp(DateUtil.getDayFirstSecondTimestamp(endDay));
					}
				}catch (Exception e){
					log.error("日期"+dayDuration+"格式有误");
					XxlJobHelper.log("日期"+dayDuration+"格式有误");
				}
			}
			XxlJobHelper.log("将要执行日期"+dayDuration+"的上下线数据统计");
			log.info("将要执行日期"+dayDuration+"的上下线数据统计");

			//2.执行数据统计
			cacheVehicleOnlineOrOfflineService.statisticsVehicleOnlineOrOffline(startTime * 1000, endTime * 1000, CommonConstant.DEFAULT_TENANT_ID);

			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_OFFLINE,"统计车辆上下线DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

		}catch (Exception e){
			log.error("[定时任务]统计车辆上下线DB缓存",e);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_OFFLINE,"统计车辆上下线DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败,"+e.getMessage());
		}
		log.info("[定时任务]统计车辆上下线DB缓存成功");
		XxlJobHelper.log("定时任务执行结束：statisticVehicleOnlineOrOfflineCache");
	}

	/**
	 * @description: 统计 车辆上下线查询 统计当天的数据
	 * 每个小时的30分，统计当天的数据
	 * @author: zhouxw
	 * @date: 2023-02-41 09:20:10
	 * @param: []
	 * @return: void
	 **/
	//@Scheduled(cron="0 30 * * * ?")
	//@XxlJob("statisticVehicleOnlineOrOfflineCacheForThisDay")
	public void statisticVehicleOnlineOrOfflineCacheForThisDay(){
		XxlJobHelper.log("将要执行定时任务：statisticVehicleOnlineOrOfflineCacheForThisDay");
		long start = System.currentTimeMillis();
		long end = 0;
		long startTime = 0;

		try{
			//获取当天的数据
			Date date = new Date();
			long tmpSecondTimestamp = (date.getTime()) / 1000;
			startTime = DateUtil.getDayFirstSecondTimestamp(tmpSecondTimestamp);
			long endTime = DateUtil.getDayLastSecondTimestamp(tmpSecondTimestamp);

			//2.执行数据统计
			cacheVehicleOnlineOrOfflineService.statisticsVehicleOnlineOrOffline(startTime * 1000, endTime * 1000, CommonConstant.DEFAULT_TENANT_ID);

			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_OFFLINE,"统计车辆上下线DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

		}catch (Exception e){
			log.error("[定时任务]统计车辆上下线DB缓存",e);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_OFFLINE,"统计车辆上下线DB缓存：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败,"+e.getMessage());
		}
		log.error("[定时任务]统计车辆上下线DB缓存成功");
		XxlJobHelper.log("定时任务执行结束：statisticVehicleOnlineOrOfflineCacheForThisDay");
	}



	/**
	 * @description: 统计 车辆安全信息记录
	 * 每天0点20分，统计前一天的数据并入库
	 * @author: zhouxw
	 * @date: 2023-02-41 09:20:10
	 * @param: []
	 * @return: void
	 **/
	//@Scheduled(cron="0 20 0 * * ?")
	//@XxlJob("statisticSecurityInfoCache")
	public void statisticSecurityInfoCache(){
		XxlJobHelper.log("将要执行定时任务：statisticSecurityInfoCache");
		long start = System.currentTimeMillis();
		long end = 0;
		long startTime = 0;

		try{
			//获取前一天的数据
			Date date = new Date();
			long tmpSecondTimestamp = (date.getTime() - 24 * 3600 * 1000) / 1000;
			startTime = DateUtil.getDayFirstSecondTimestamp(tmpSecondTimestamp);
			long endTime = DateUtil.getDayLastSecondTimestamp(tmpSecondTimestamp);

			//2.执行数据统计
			cacheSecurityInfoService.statisticsSecurityInfo(startTime * 1000, endTime * 1000);

			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SECURITY_INFO,"统计车辆安全信息记录：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

		}catch (Exception e){
			log.error("[定时任务]统计车辆上下线DB缓存",e);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SECURITY_INFO,"统计车辆安全信息记录：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败,"+e.getMessage());
		}
		log.error("[定时任务]统计车辆上下线DB缓存成功");
		XxlJobHelper.log("定时任务执行结束：statisticSecurityInfoCache");
	}



	/**
	 * @description: 统计 车辆安全信息记录 统计当天的数据
	 * 每个小时的30分，统计当天数据
	 * @author: zhouxw
	 * @date: 2023-02-41 09:20:10
	 * @param: []
	 * @return: void
	 **/
	//@Scheduled(cron="0 30 * * * ?")
	//@XxlJob("statisticSecurityInfoCacheForThisDay")
	public void statisticSecurityInfoCacheForThisDay(){
		XxlJobHelper.log("将要执行定时任务：statisticSecurityInfoCacheForThisDay");
		long start = System.currentTimeMillis();
		long end = 0;
		long startTime = 0;

		try{
			//获取当天的数据
			Date date = new Date();
			long tmpSecondTimestamp = (date.getTime() - 24 * 3600 * 1000) / 1000;
			startTime = DateUtil.getDayFirstSecondTimestamp(tmpSecondTimestamp);
			long endTime = DateUtil.getDayLastSecondTimestamp(tmpSecondTimestamp);

			//2.执行数据统计
			cacheSecurityInfoService.statisticsSecurityInfo(startTime * 1000, endTime * 1000);

			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SECURITY_INFO,"统计车辆安全信息记录：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

		}catch (Exception e){
			log.error("[定时任务]统计车辆上下线DB缓存",e);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_SECURITY_INFO,"统计车辆安全信息记录：定时执行跑批任务",sdfYMD.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败,"+e.getMessage());
		}
		log.error("[定时任务]统计车辆上下线DB缓存成功");
		XxlJobHelper.log("定时任务执行结束：statisticSecurityInfoCacheForThisDay");
	}


	/**
	 * @description: 统计 车辆在线率统计DB缓存
	 * 每天9点10分，统计前一天的数据并入库；得保证bdm_route 入库完成之后才能执行，否则执行出来木有数据
	 * @author: zhouxw
	 * @date: 2023-02-41 09:20:10
	 * @param: [dateString: yyyy-MM-dd]
	 * @return: void
	 **/
	//@Scheduled(cron="0 10 9 * * ?")
	//@XxlJob("statisticVehicleOnlineRate")
	public void statisticVehicleOnlineRate(String dateString){
		XxlJobHelper.log("将要执行定时任务：statisticVehicleOnlineRate");
		String param = XxlJobHelper.getJobParam();
		long start = System.currentTimeMillis();
		long end = 0;
		String dateStr;
		if (StringUtils.isBlank(param) || (!param.matches("\\d{4}-\\d{2}-\\d{2}"))) {
			//如果没有指定日期
			//获取前一天的数据
			Date date = new Date();
			long tmpSecondTimestamp = date.getTime() - 24 * 3600 * 1000;
			dateStr = DateUtil.getDateString(tmpSecondTimestamp / 1000);
		}else{
			dateStr = param;
		}

		//为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
		Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE);
		if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
			//如果已经有跑批程序在执行，则不再执行
			log.info("[车辆在线率DB缓存]跑批程序正在执行，请等待当前程序执行完毕后再试！");
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE,"统计车辆在线率：定时执行跑批任务",dateStr ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"[车辆在线率DB缓存]跑批程序正在执行，请等待当前程序执行完毕后再试！");
			return ;
		}

		//获取执行权限之后，添加执行标记（有效期为1个小时）
		synchronized (this){
			stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
		}
		Long startTime = 0L;
		Long endTime = 0L;

		try{

			List<String> dateList = new ArrayList<>();
			dateList.add(dateStr);
			startTime = DateUtil.getDayFirstSecondTimestamp(dateStr);
			endTime = DateUtil.getDayLastSecondTimestamp(startTime);

			//2.执行数据统计
			cacheVehicleOnlineRateService.statisticsVehicleOnlineRate(startTime, endTime);

			//3.记录日志
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE,"统计车辆在线率：定时执行跑批任务",dateStr ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

		}catch (Exception e){
			log.error("[定时任务]统计车辆在线率DB缓存",e);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE,"统计车辆在线率：定时执行跑批任务",dateStr ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败,"+e.getMessage());
		}finally {
			//执行完毕后，解除执行锁定
			synchronized (this){
				stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE );
			}
		}
		log.error("[定时任务]统计车辆在线率DB缓存成功");
		XxlJobHelper.log("定时任务执行结束：statisticVehicleOnlineRate");
	}



	/**
	 * @description: 统计 车辆在线率统计DB缓存 统计当天的数据
	 * 每个小时的30分，统计当天数据
	 * @author: zhouxw
	 * @date: 2023-02-41 09:20:10
	 * @param: []
	 * @return: void
	 **/
	//@Scheduled(cron="0 30 * * * ?")
	//@XxlJob("statisticVehicleOnlineRateCacheForThisDay")
	public void statisticVehicleOnlineRateCacheForThisDay(){
		XxlJobHelper.log("将要执行定时任务：statisticVehicleOnlineRateCacheForThisDay");
		long start = System.currentTimeMillis();
		long end = 0;
		String dateStr = "";
		//获取当天的数据
		Date date = new Date();
		dateStr = DateUtil.getDateString(date.getTime()/1000);
		Long startTime = 0L;
		Long endTime = 0L;

		//为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
		Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE);
		if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
			//如果已经有跑批程序在执行，则不再执行
			log.info("[车辆在线率DB缓存]跑批程序正在执行，请等待当前程序执行完毕后再试！");
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE,"统计车辆在线率：定时执行跑批任务",dateStr ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"[车辆在线率DB缓存]跑批程序正在执行，请等待当前程序执行完毕后再试！");
			return ;
		}

		//获取执行权限之后，添加执行标记（有效期为1个小时）
		synchronized (this){
			stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
		}

		try{

			startTime = DateUtil.getDayFirstSecondTimestamp(dateStr);
			endTime = DateUtil.getDayLastSecondTimestamp(startTime);

			List<String> dateList = new ArrayList<>();
			dateList.add(dateStr);

			//2.执行数据统计
			cacheVehicleOnlineRateService.statisticsVehicleOnlineRate(startTime, endTime);

			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE,"统计车辆在线率：定时执行跑批任务",dateStr ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

		}catch (Exception e){
			log.error("[定时任务]统计车辆在线率DB缓存",e);
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE,"统计车辆在线率：定时执行跑批任务",dateStr ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败,"+e.getMessage());
		}
		finally {
			//执行完毕后，解除执行锁定
			synchronized (this){
				stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_DB_CACHE_VEHICLE_ONLINE_RATE );
			}
		}
		log.error("[定时任务]统计车辆在线率DB缓存成功");
		XxlJobHelper.log("定时任务执行结束：statisticVehicleOnlineRateCacheForThisDay");
	}


	//**************************************考核管理类DB缓存**************************************************************************
	/**
	 * @description: 统计当前月份累计的车辆漂移率信息
	 * 每天 11:30 执行
	 * @author: zhouxw
	 * @date: 2023-03-88 16:50:38
	 * @param: []
	 * @return: void
	 **/
    /*@Scheduled(cron="0 10 10 * * ?")
    public void statDriftRateForDay(){

        long start = System.currentTimeMillis();
        long end = 0;
        long startTime = 0;
        String dateStr = "";
        //获取统计当前月份的数据
        Date date = new Date();
        String month = sdfYM.get().format(date);
        dateStr = month;
        //1.为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
        Object executingFlag = stringRedisTemplate.opsForValue().get(CommonConstants.TASK_EXECUTING_PREFIX+CommonConstants.TASK_DB_CACHE_STAT_DRIFT_RATE);
        if(executingFlag != null && CommonConstants.COMMON_TRUE.equals(executingFlag.toString())){
            //如果已经有跑批程序在执行，则不再执行
            log.info("[考核--车辆漂移率DB缓存]跑批程序正在执行，请等待当前程序执行完毕后再试！");
            logUtil.insertStatLog(CommonConstants.TASK_DB_CACHE_STAT_DRIFT_RATE,"统计[考核--车辆漂移率DB缓存]：定时执行跑批任务",dateStr ,new Date(start),new Date(end),CommonConstants.TASK_EXECUTE_RESULT_FAIL,"[考核--车辆漂移率DB缓存]跑批程序正在执行，请等待当前程序执行完毕后再试！");
            return ;
        }

        //获取执行权限之后，添加执行标记（有效期为1个小时）
        synchronized (this){
            stringRedisTemplate.opsForValue().set(CommonConstants.TASK_EXECUTING_PREFIX+CommonConstants.TASK_DB_CACHE_STAT_DRIFT_RATE , CommonConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
        }

        try{

            //2.执行数据统计
            cacheStatDriftRateService.statAndSaveDriftRateCache(month);

            //3.记录日志
            end = System.currentTimeMillis();
            logUtil.insertStatLog(CommonConstants.TASK_DB_CACHE_STAT_DRIFT_RATE,"[考核--车辆漂移率DB缓存]：定时执行跑批任务",dateStr ,new Date(start),new Date(end),CommonConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");

        }catch (Exception e){
            log.error("[定时任务][考核--车辆漂移率DB缓存]",e);
            end = System.currentTimeMillis();
            logUtil.insertStatLog(CommonConstants.TASK_DB_CACHE_STAT_DRIFT_RATE,"[考核--车辆漂移率DB缓存]：定时执行跑批任务",dateStr ,new Date(start),new Date(end),CommonConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败,"+e.getMessage());
        }finally {
            //执行完毕后，解除执行锁定
            synchronized (this){
                stringRedisTemplate.delete(CommonConstants.TASK_EXECUTING_PREFIX+CommonConstants.TASK_DB_CACHE_STAT_DRIFT_RATE );
            }
        }
        log.error("[定时任务][考核--车辆漂移率DB缓存]成功");

    }*/



}
