<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmTerminalonlinerecordMapper">

    <select id="getOnlineRecordInDuration" resultType="com.xh.vdm.statistic.entity.BdmTerminalonlinerecord">
        select *
        from bdm_terminalonlinerecord_${month}
        where 1 = 1
        and (off_line_time is null or off_line_time >= to_timestamp(#{startTime,jdbcType=BIGINT}))
        and on_line_time &lt;= to_timestamp(#{endTime,jdbcType=BIGINT})
    </select>
</mapper>
