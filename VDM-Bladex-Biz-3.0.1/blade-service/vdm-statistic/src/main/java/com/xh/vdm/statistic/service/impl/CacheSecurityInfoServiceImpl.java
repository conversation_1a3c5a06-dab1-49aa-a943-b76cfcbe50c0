package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.CacheSecurityInfoResponse;
import com.xh.vdm.statistic.mapper.CacheSecurityInfoMapper;
import com.xh.vdm.statistic.mapper.StatisticsMapper;
import com.xh.vdm.statistic.service.ICacheSecurityInfoService;
import com.xh.vdm.statistic.vo.request.SecurityInfoRequest;
import com.xh.vdm.statistic.vo.response.SecurityInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 车辆地图超速服务类
 *
 * <AUTHOR>
 * @since 2022-09-14
 */
@Service
@Slf4j
public class CacheSecurityInfoServiceImpl extends ServiceImpl<CacheSecurityInfoMapper, CacheSecurityInfoResponse> implements ICacheSecurityInfoService {


    @Resource
    private StatisticsMapper statisticsMapper;

    @Resource
    private CacheSecurityInfoMapper mapper;

    @Override
    @Transactional
    public void statisticsSecurityInfo(Long startTime, Long endTime) throws Exception {

        Date startDate = new Date();
        startDate.setTime(startTime );
        Date endDate = new Date();
        endDate.setTime(endTime );

        //1.删除指定时间段的数据
        int count = mapper.delete(Wrappers.lambdaQuery(CacheSecurityInfoResponse.class).le(CacheSecurityInfoResponse::getSendTime, endDate).ge(CacheSecurityInfoResponse::getSendTime, startDate));
        log.info("删除指定时间段的数据成功，共删除{}条数据",count);

        //2.统计指定时间段的数据
        List<CacheSecurityInfoResponse> list = statisticsMapper.securityInfoWithTime(startDate, endDate);
        //添加创建时间
        for(CacheSecurityInfoResponse cache : list){
            cache.setCreateTime(new Date());
        }

        /*for(CacheVehicleOnlineOrOfflineResponse cache : list){
            this.save(cache);
        }*/

        boolean saveFlag = this.saveBatch(list);
        if(saveFlag){
            log.info("统计数据保存成功，共保存{}条数据",list==null?0:list.size());
        }
    }


    @Override
    public IPage<SecurityInfoResponse> querySecurityInfoCache(SecurityInfoRequest req) throws Exception {

        //查询车辆上下线查询信息
        Page<SecurityInfoResponse> page = new Page<>();
        page.setCurrent(req.getCurrent());
        page.setSize(req.getSize());
        IPage<SecurityInfoResponse> pageList = mapper.getList(page, req);
        return pageList;
    }

}
