<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmTerminalMapper">

    <select id="getDeptNameByDeviceNum" resultType="string">
        select dept_name
        from blade_dept
        where id in
              (select dept_id from bdm_terminal where device_num = #{deviceNum,jdbcType=VARCHAR});
    </select>

    <select id="getTotalTerminalCount" resultType="long">
        select count(*)
        from bdm_terminal
        where is_del = 0 and dept_id in
            <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
    </select>

    <select id="getNoDeviceNumCount" resultType="long">
        select count(*)
        from bdm_terminal
        where is_del = 0 and length(device_num) &lt; 1
        and dept_id in
        <foreach collection="deptIds" item="deptId" separator="," open="(" close=")">
            #{deptId}
        </foreach>
    </select>
</mapper>
