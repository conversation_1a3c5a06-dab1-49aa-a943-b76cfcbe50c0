package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 上下线记录入参
 */
@Data
public class BdmDeviceLinkRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long deviceId;

	/**
	 * 终端设备类型
	 */
	private Integer deviceType;

	/**
	 * 终端号
	 */
	private String uniqueId;

	/**
	 * 终端赋码号
	 */
	private String deviceNum;

	/**
	 * 监控对象名称
	 */
	private String targetName;

	/**
	 * 状态
	 */
	private Integer action;

	/**
	 * 时间段开始时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startTime;

	/**
	 * 时间段结束时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endTime;

}

