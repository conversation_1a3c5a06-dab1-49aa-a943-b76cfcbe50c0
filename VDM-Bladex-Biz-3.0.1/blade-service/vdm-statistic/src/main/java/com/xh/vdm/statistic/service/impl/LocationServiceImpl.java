package com.xh.vdm.statistic.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.bd.entity.VehicleStopPointBase;
import com.xh.vdm.statistic.entity.AlarmLocation;
import com.xh.vdm.statistic.entity.BdmVehicle;
import com.xh.vdm.statistic.mapper.LocationMapper;
import com.xh.vdm.statistic.service.IBdmVehicleService;
import com.xh.vdm.statistic.service.ILocationService;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.ExcelExport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 *  定位数据相关服务
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
@Service
@Slf4j
public class LocationServiceImpl extends ServiceImpl<LocationMapper, LocationKudu> implements ILocationService {

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

	@Resource
	private IBdmVehicleService vehicleService;


    @Override
    @DS("location")
    public List<VehicleBase> findUploadLicencePlatesByDay(String day) throws Exception {
		Date date = null;
        try {
            date = sdf.parse(day);
        }catch (Exception e){
            log.error("获取定位数据失败：指定的日期格式不正确[{}]" , day , e);
			throw new Exception("日期格式错误："+day);
        }
		long startTime = DateUtil.getDayFirstSecondTimestamp(date.getTime()/1000);
		long endTime = DateUtil.getDayLastSecondTimestamp(date.getTime()/1000);
        return baseMapper.getUploadLicencePlateListByDay(startTime, endTime);
    }

	@Override
	@DS("location")
	public List<VehicleStopPointBase> findStopPointVehicleListByDay(String day) throws Exception {
		Date date = null;
		try {
			date = sdf.parse(day);
		}catch (Exception e){
			log.error("获取定位数据失败：指定的日期格式不正确[{}]" , day , e);
			throw new Exception("日期格式错误："+day);
		}
		long startTime = DateUtil.getDayFirstSecondTimestamp(date.getTime()/1000);
		long endTime = DateUtil.getDayLastSecondTimestamp(date.getTime()/1000);
		//查询车辆信息
		List<VehicleBase> vList = baseMapper.getUploadLicencePlateListByDay(startTime, endTime);
		List<String> vStringList = new ArrayList<>();
		vList.forEach(item -> {
			vStringList.add(item.getLicencePlate()+"~"+item.getLicenceColor());
		});
		//查询车辆基本信息
		List<BdmVehicle> list = vehicleService.findVehicleByLicencePlateAndColor(vStringList);
		List<VehicleStopPointBase> vspList = new ArrayList<>();
		list.forEach(item -> {
			VehicleStopPointBase vsp = new VehicleStopPointBase();
			vsp.setLicencePlate(item.getLicencePlate());
			vsp.setLicenceColor(Long.parseLong(item.getLicenceColor()));
			vsp.setDeptId(item.getDeptId());
			vsp.setVehicleOwnerId(item.getVehicleOwnerId());
			vsp.setVehicleId(item.getId());
			vsp.setAccessMode(item.getAccessMode());
			vsp.setVehicleUseType(Integer.parseInt(item.getVehicleUseType()));
			vspList.add(vsp);
		});
		return vspList;
	}

    @Override
    @DS("location")
    public List<LocationKudu> findUploadLocationPointListByDay(String day, String licencePlate, int plateColor) throws Exception{
		//1.计算开始时间和结束时间
		Date date = null;
		try{
			date = sdf.parse(day);
		}catch (Exception e){
			log.error("数据格式错误：{}", day);
			throw new Exception("数据格式错误: "+day);
		}
		long startTime = DateUtil.getDayFirstSecondTimestamp(date.getTime()/1000);
		long endTime = DateUtil.getDayLastSecondTimestamp(date.getTime()/1000);
        return baseMapper.getUploadLocationListByDay( startTime , endTime, licencePlate, plateColor);
    }

	@Override
	@DS("location")
	public List<LocationKudu> findAllUploadLocationPointListByDay(String day, String licencePlate, int plateColor) throws Exception{
		//1.计算开始时间和结束时间
		Date date = null;
		try{
			date = sdf.parse(day);
		}catch (Exception e){
			log.error("数据格式错误：{}", day);
			throw new Exception("数据格式错误: "+day);
		}
		long startTime = DateUtil.getDayFirstSecondTimestamp(date.getTime()/1000);
		long endTime = DateUtil.getDayLastSecondTimestamp(date.getTime()/1000);
		return baseMapper.getAllUploadLocationListByDay( startTime , endTime, licencePlate, plateColor);
	}

	@DS("location")
	@Override
	public long findLocationCountByCondition(String licencePlate, int licenceColor, long startTime, long endTime) {
		return baseMapper.getLocationCountByCondition(licencePlate, licenceColor, startTime, endTime);
	}

	@Override
	public List<LocationKudu> findLocationByCondition(String licencePlate, int licenceColor, long startTime, long endTime) {
		return baseMapper.getUploadLocationListByDay(startTime,endTime,licencePlate,licenceColor);
	}


	@Override
	public List<AlarmLocation> findLocationByAlarmIdListInThreeMinutes(List<Long> alarmIdList, Long startTime, Long endTime) {
		return baseMapper.getLocationByAlarmIdListInThreeMinutes(alarmIdList, startTime, endTime);
	}

	@Override
	public List<VehicleBase> findAllUploadLicencePlatesByDay(String day) {
		Date date;
		long startTime;
		long endTime;
		try {
			date = sdf.parse(day);
			//查询开始时间和结束时间
			startTime = DateUtil.getDayFirstSecondTimestamp(date.getTime()/1000);
			endTime = DateUtil.getDayLastSecondTimestamp(date.getTime()/1000);
		}catch (Exception e){
			e.printStackTrace();
			log.error("获取定位数据失败：指定的日期格式不正确[{}]" , day , e);
			return null;
		}
		return baseMapper.getUploadLicencePlateListByDay(startTime, endTime);
	}

	@Override
	public List<LocationKudu> findUploadLocationPointListAllByDay(String day, String licencePlate, int licenceColor) throws Exception{
		//查询开始时间和结束时间
		Date date = sdf.parse(day);
		long startTime = DateUtil.getDayFirstSecondTimestamp(date.getTime()/1000);
		long endTime = DateUtil.getDayLastSecondTimestamp(date.getTime()/1000);
		return baseMapper.getUploadLocationListByDay( startTime, endTime,licencePlate, licenceColor);
	}
}
