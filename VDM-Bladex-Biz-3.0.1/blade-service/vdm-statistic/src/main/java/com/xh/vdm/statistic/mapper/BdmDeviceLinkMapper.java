package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.BdmDeviceLink;
import com.xh.vdm.statistic.vo.request.BdmDeviceLinkRequest;
import com.xh.vdm.statistic.vo.response.terminal.LinkCountResponse;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.mp.support.Query;

import java.util.List;

/**
 * (BdmDeviceLink)表数据库访问层
 */
public interface BdmDeviceLinkMapper extends BaseMapper<BdmDeviceLink> {


	/**
	 * 查询指定行数据
	 *
	 * @param bdl
	 * @param query
	 * @param deptIds
	 * @return
	 */
	List<BdmDeviceLink> queryAll(@Param("bdl") BdmDeviceLinkRequest bdl, @Param("query") Query query, @Param("userId") Long userId, @Param("deptIds") String deptIds, @Param("account") String account);

	long countLink(@Param("bdl") BdmDeviceLinkRequest bdl, @Param("userId") Long userId, @Param("deptIds") String deptIds, @Param("account") String account);

	/**
	 * 查询长期离线数
	 *
	 * @param deptList
	 * @return
	 */
	long getLongOfflineCount(@Param("deptList") List<Long> deptList, @Param("secondTimestamp") Long secondTimestamp);

	List<LinkCountResponse> onlineTrendered(@Param("userId") Long userId);
}

