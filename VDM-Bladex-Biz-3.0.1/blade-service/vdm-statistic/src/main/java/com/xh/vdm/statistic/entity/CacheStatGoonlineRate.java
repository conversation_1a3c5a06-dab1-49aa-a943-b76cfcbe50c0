package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 车辆上线率DB缓存表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CacheStatGoonlineRate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 月份
     */
    private String month;

    /**
     * 上级平台
     */
    private Long vehicleOwnerId;

    /**
     * 车组id
     */
    private Long deptId;

    /**
     * 班线入网车辆数
     */
    private Integer busInnetCount;

    /**
     * 班线上线车辆数
     */
    private Integer busGoonlineCount;

    /**
     * 班线上线率
     */
    private Double busGoonlineRate;

    /**
     * 包车入网车辆数
     */
    private Integer charterInnetCount;

    /**
     * 包车上线车辆数
     */
    private Integer charterGoonlineCount;

    /**
     * 包车上线率
     */
    private Double charterGoonlineRate;

    /**
     * 危险品车辆入网数
     */
    private Integer dangerInnetCount;

    /**
     * 危险品车辆上线数
     */
    private Integer dangerGoonlineCount;

    /**
     * 危险品车辆上线率
     */
    private Double dangerGoonlineRate;

    /**
     * 总入网数
     */
    private Integer totalInnetCount;

    /**
     * 总上线车辆数
     */
    private Integer totalOnlineCount;

    /**
     * 考核得分
     */
    private Double goonlineScore;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 备注
     */
    private String note;


}
