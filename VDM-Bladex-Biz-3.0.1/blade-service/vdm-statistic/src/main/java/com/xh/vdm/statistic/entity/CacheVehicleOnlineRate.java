package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆在线率DB缓存表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CacheVehicleOnlineRate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

	/**
	 * 车辆id
	 */
	@TableField(insertStrategy = FieldStrategy.IGNORED)
	private Integer vehicleId;

    /**
     * 企业id
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long enterpriseId;

    /**
     * 企业名称
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String enterprise;

    /**
     * 车队id
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long deptId;

    /**
     * 车队名称
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String deptName;

    /**
     * 车牌号码
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String licencePlate;

    /**
     * 车牌颜色编码
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String licenceColorCode;

    /**
     * 车牌颜色
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String licenceColor;

    /**
     * 行业类型编码
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String vehicleModelCode;

    /**
     * 行业类型
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String vehicleModel;

    /**
     * 行业归属id
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long vehicleOwnerId;

    /**
     * 行业归属
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String vehicleOwner;

    /**
     * 车辆接入方式code
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String accessModeCode;

    /**
     * 车辆接入方式
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String accessMode;

    /**
     * 月份：%y-%m
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String month;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d01;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d02;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d03;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d04;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d05;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d06;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d07;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d08;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d09;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d10;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d11;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d12;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d13;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d14;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d15;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d16;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d17;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d18;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d19;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d20;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d21;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d22;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d23;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d24;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d25;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d26;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d27;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d28;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d29;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d30;

    /**
     * 在线时长(秒)
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Long d31;

    /**
     * 数据创建时间
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private Date createTime;

    /**
     * 备注
     */
    @TableField(insertStrategy = FieldStrategy.IGNORED)
    private String note;


}
