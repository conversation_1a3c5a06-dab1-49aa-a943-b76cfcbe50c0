package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;


/**
 * @Description: 数据详情请求对象
 * @Author: zhouxw
 * @Date: 2022/9/6 8:21 AM
 */
@Data
public class DetailRequest {

    //统计月份
    @NotEmpty(message = "统计月份不能为空")
    private String month;
    //上级平台
    @NotNull(message = "上级平台不能为空")
    private Long ownerId;
    //企业名称
    //@NotNull(message = "企业名称不能为空")
    private Long deptId;
    //车牌号（非必填）
    private String licencePlate;

    //车牌颜色（非必填）
    @JsonProperty("licenceColor")
    private Integer plateColor;

    //每页的数量
    private int count;

    //当前页的开始下标
    private int start;

}
