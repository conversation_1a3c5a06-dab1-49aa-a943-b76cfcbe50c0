<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatVehTravelDurationMapper">

    <delete id="deleteDataByDay">
        delete from stat_veh_travel_duration_${month} where stat_date = #{day,jdbcType=VARCHAR}
    </delete>

    <insert id="saveBatchByDay">
        insert into stat_veh_travel_duration_${month} (licence_plate,stat_date,	h01 ,	h02 ,	h03 ,	h04 ,	h05 ,	h06 ,	h07 ,	h08 ,	h09 ,	h10 ,	h11 ,	h12 ,	h13 ,	h14 ,	h15 ,	h16 ,	h17 ,	h18 ,	h19 ,	h20 ,	h21 ,	h22 ,	h23 ,	h24 ,	total_duration,	create_time )
            values
                <foreach collection="list" item="item" open="" close="" separator=",">
                    (#{item.licencePlate} , #{item.statDate},	#{item.h01 },	#{item.h02} ,	#{item.h03} ,	#{item.h04} ,	#{item.h05} ,	#{item.h06 },	#{item.h07 },	#{item.h08} ,	#{item.h09} ,	#{item.h10} ,	#{item.h11} ,	#{item.h12} ,	#{item.h13} ,	#{item.h14} ,	#{item.h15} ,	#{item.h16} ,	#{item.h17} ,	#{item.h18} ,	#{item.h19 },	#{item.h20 },	#{item.h21 },	#{item.h22 },	#{item.h23 },	#{item.h24} ,	#{item.totalDuration},	#{item.createTime})
                </foreach>
    </insert>


    <select id="getPassengerVehCountInNight" parameterType="com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select stat_date  , count(*) count
        from (
                 select sd.name dept_name , svtd.stat_date , svtd.licence_plate ,  (h03 + h04 + h05) "hour"
                 from stat_veh_travel_duration_${param.month} svtd
                          left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
                          left join sys_dept sd on bv.dept_id = sd.id
                 where 1 = 1 and bv.vehicle_use_type in (10,11,12)
                <!--and bv.vehicle_owner_id = '1'-->
                <if test="param.startTime != null and param.startTime != ''">
                    and extract(epoch from svtd.stat_date) >= #{param.startTime,jdbcType=BIGINT}
                </if>
                <if test="param.endTime != null and param.endTime != ''">
                    and extract(epoch from svtd.stat_date) &lt;= #{param.endTime,jdbcType=BIGINT}
                </if>
                <if test="param.deptId != null and param.deptId != ''">
                    and bv.dept_id = #{param.deptId}
                </if>
             ) a
        where a.hour > 0
        group by stat_date
        order by stat_date desc
    </select>


    <select id="getAverageTravelDurationByDay" parameterType="com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest" resultType="com.xh.vdm.statistic.entity.DateAndData">
        select svtd.stat_date , sum(svtd.total_duration) data
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
        <if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in (10,11,12,30,31,32)
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and epoch(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and epoch(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId,jdbcType=BIGINT}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in (#{request.vehicleUseType,jdbcType=VARCHAR})
        </if>
        group by svtd.stat_date

    </select>




    <select id="getVehicleTranvelDurationCountByCondition" parameterType="com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest" resultType="com.xh.vdm.statistic.entity.VehicleTravelDurationNode">

        select sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h01' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h01 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h02' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
        <if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h02 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id , sd.name deptName , svtd.stat_date statDate , 'h03' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h03 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h04' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h04 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h05' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h05 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h06' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h06 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h07' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h07 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h08' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h08 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h09' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h09 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h10' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h10 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h11' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h11 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h12' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h12 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h13' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h13 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h14' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h14 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h15' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h15 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h16' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h16 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h17' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h17 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h18' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h18 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h19' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h19 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h20' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h20 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h21' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h21 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h22' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h22 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h23' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h23 > 0
        group by sd.id , svtd.stat_date

        union

        select  sd.id dept_id ,sd.name deptName , svtd.stat_date statDate , 'h24' "hour", count(1) count
        from stat_veh_travel_duration_${request.month} svtd
        left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
<if test="request.vehicleUseType == null or request.vehicleUseType == ''">
            and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
        <if test="request.startTime != null and request.startTime != ''">
            and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
        </if>
        <if test="request.endTime != null and request.endTime != ''">
            and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
        </if>
        <if test="request.deptId != null and request.deptId != ''">
            and bv.dept_id = #{request.deptId}
        </if>
        <if test="request.vehicleUseType != null and request.vehicleUseType != ''">
            and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
        and h24 > 0
        group by sd.id , svtd.stat_date

    </select>


    <select id="getTravelVehicleCountByDate" resultType="int" >
        select count(*) from stat_veh_travel_duration_${month} svtd
        left join bdm_vehicle bv on svtd.licence_plate = bv.licence_plate
        where 1 = 1
          and bv.dept_id = #{deptId,jdbcType=BIGINT}
            and bv.vehicle_use_type in (10,11,12,30,31,32)
            and bv.vehicle_owner_id = #{vehicleOwnerId,jdbcType=BIGINT}
          and stat_date = #{date}
          and total_duration > 0
          and bv.is_del = 0
          and svtd.is_del = 0
    </select>

    <select id="getCompanyTotalMileageByDate" resultType="double">
        select sum(coalesce( split_part( split_part( sc.d${index}, '#', 4 ), '#', - 1 ), 0 ))
        from stat_complete_${month} sc
        left join bdm_vehicle bv on sc.licence_plate = bv.licence_plate
        where 1 = 1
          and bv.dept_id = #{deptId,jdbcType=BIGINT}
          and bv.is_del = 0
    </select>

    <select id="getVehicleOnlineCount" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select stat_date , count(*) count from stat_veh_travel_duration_${month} svtd
        left join bdm_vehicle bv on svtd.licence_plate = bv.licence_plate
        where 1 = 1
        and total_duration > 0
        and bv.dept_id = #{deptId,jdbcType=BIGINT}
        and svtd.stat_date >= #{startDate,jdbcType=VARCHAR}
        and svtd.stat_date &lt;= #{endDate,jdbcType=VARCHAR}
        group by stat_date
        order by stat_date
    </select>

    <select id="getVehicleTravelCountByDuration" parameterType="com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest" resultType="int">
        select coalesce(sum(count) , 0)
        from(
                select bv.dept_id , svtd.stat_date , count(*) count from stat_veh_travel_duration_${request.month} svtd
                 left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
                 left join sys_dept sd on bv.dept_id = sd.id
                where 1 = 1
                  <!--and bv.vehicle_owner_id = '1'-->
                    <if test="request.vehicleUseType == null or request.vehicleUseType == ''">
                        and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
                    </if>
                  and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
                  and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
                  and bv.dept_id = #{request.deptId}
                    <if test=" request.vehicleUseType != null and request.vehicleUseType != ''">
                        and bv.vehicle_use_type in ( #{request.vehicleUseType} )
                    </if>
                  and bv.is_del = 0
                group by bv.dept_id , svtd.stat_date
            ) a

    </select>

    <select id="getVehicleTravelCountForDept" resultType="com.xh.vdm.statistic.entity.DeptAndDateAndCount" parameterType="com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest">
        select bv.dept_id , svtd.stat_date , count(*) count from stat_veh_travel_duration_${request.month} svtd
         left join bdm_vehicle bv on bv.licence_plate = svtd.licence_plate
         left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
          <!--and bv.vehicle_owner_id = '1'-->
        <if test="request.vehicleUseType == null or request.vehicleUseType == ''">
          and bv.vehicle_use_type in ( 10,11,12,30,31,32 )
        </if>
          and extract(epoch from svtd.stat_date) >= #{request.startTime,jdbcType=BIGINT}
          and extract(epoch from svtd.stat_date) &lt;= #{request.endTime,jdbcType=BIGINT}
          and bv.dept_id = #{request.deptId}
        <if test=" request.vehicleUseType != null and request.vehicleUseType != ''">
          and bv.vehicle_use_type in ( #{request.vehicleUseType} )
        </if>
          and bv.is_del = 0
        group by bv.dept_id , svtd.stat_date
    </select>


</mapper>
