package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "返回体：统计报表通用返回")
@Data
public class CommonStatResponse {

	@JsonProperty("dept_name")
	@ApiModelProperty(name = "dept_name", value = "单位名称", example = "海格", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"统计报表", "单位名称"})
	private String deptName;

	@JsonProperty("licence_color")
	@ApiModelProperty(name = "licence_color", value = "车牌颜色（颜色值与名称的映射，详见blade_dict_biz表，code=licence_color的记录）", example = "1", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"统计报表", "车牌颜色"})
	private String licenceColor;

	@JsonProperty("licence_plate")
	@ApiModelProperty(name = "licence_plate", value = "车牌号", example = "粤A12345", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"统计报表", "车牌号"})
	private String licencePlate;

	@JsonProperty("vehicle_use_type")
	@ApiModelProperty(name = "vehicle_use_type", value = "行业类型（类型值与名称的映射，详见blade_dict_biz表，code=2的记录）", example = "1", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"统计报表", "行业类型"})
	private String vehicleUseType;

	@JsonProperty("vehicle_owner_name")
	@ApiModelProperty(name = "vehicle_owner_name", value = "车辆归属", example = "星航", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"统计报表", "车辆归属"})
	private String vehicleOwnerName;

	@JsonProperty("access_mode")
	@ApiModelProperty(name = "access_mode", value = "接入方式（方式值与名称的映射，详见blade_dict_biz表，code=access_mode的记录）", example = "1", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"统计报表", "接入方式"})
	private String accessMode;
}
