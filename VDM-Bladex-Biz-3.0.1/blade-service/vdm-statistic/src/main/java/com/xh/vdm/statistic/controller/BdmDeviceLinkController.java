package com.xh.vdm.statistic.controller;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.BdmDeviceLink;
import com.xh.vdm.statistic.service.BdmDeviceLinkService;
import com.xh.vdm.statistic.utils.AuthUtils;
import com.xh.vdm.statistic.vo.request.BdmDeviceLinkRequest;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.entity.DataAuthCE;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 终端上下线记录表
 */
@Slf4j
@RestController
@RequestMapping("/device/link")
public class BdmDeviceLinkController {
	/**
	 * 服务对象
	 */
	@Resource
	private BdmDeviceLinkService bdmDeviceLinkService;

	@Resource
	private CETokenUtil ceTokenUtil;

	/**
	 * 分页查询
	 *
	 * @param bdmDeviceLinkRequest 筛选条件
	 * @param query                分页对象
	 * @return 查询结果
	 */
	@PostMapping("/list")
	public R<IPage<BdmDeviceLink>> list(@RequestBody BdmDeviceLinkRequest bdmDeviceLinkRequest, Query query, BladeUser user) {
		long time1 = System.currentTimeMillis();
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}
		long time2 = System.currentTimeMillis();
		log.info("终端上下线查询，isValidServiceRole:{}",time2-time1);
		// 获取用户权限
		DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
		if(StringUtils.isEmpty(dataAuthCE.getOrgListStr()) && StringUtils.isEmpty(dataAuthCE.getAccount())) {
			return R.fail(403, "用户没有权限");
		}
		long time3 = System.currentTimeMillis();
		log.info("终端上下线查询，getDataAuth:{}",time3-time2);
		IPage<BdmDeviceLink> iPage = new Page<>(query.getCurrent(), query.getSize());
		List<BdmDeviceLink> list = this.bdmDeviceLinkService.queryByPage(dataAuthCE, bdmDeviceLinkRequest, query, user.getUserId());
		iPage.setRecords(list);
		long time4 = System.currentTimeMillis();
		log.info("终端上下线查询，queryByPage:{}",time4-time3);
		long totalCount = bdmDeviceLinkService.countLink(dataAuthCE, bdmDeviceLinkRequest, user.getUserId());
		long time5 = System.currentTimeMillis();
		log.info("终端上下线查询，countLink:{}",time5-time4);
		iPage.setTotal(totalCount);
		iPage.setPages(totalCount / query.getSize() + 1);
		return R.data(iPage);
	}
}

