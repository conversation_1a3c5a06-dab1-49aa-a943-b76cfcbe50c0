package com.xh.vdm.statistic.entity.tg;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (BdmTemporary)实体类
 */
@Data
@ExcelIgnoreUnannotated
public class BdmTemporary implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	@ExcelProperty(value = "姓名")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(12)
	private String name;

	@ExcelProperty(value = "工号/身份证号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(12)
	private String wkno;

	private Integer targetType;

	private Long deptId;

	private Integer post;

	private Integer industry;

	@ExcelProperty(value = "手机号码")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(15)
	private String phone;

	private Long companyId;

	private String company;
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date validFrom;
	@JsonFormat(pattern = "yyyy-MM-dd")
	private Date validTo;

	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	@TableField(exist = false)
	private Integer terminalType;
	@TableField(exist = false)
	private String uniqueId;
	@TableField(exist = false)
	private Integer status;

	/**
	 * 错误信息
	 */
	@TableField(exist = false)
	@ExcelProperty(value = "错误信息")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(12)
	private String msg;
}

