package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.BdmDeviceOnline;
import com.xh.vdm.statistic.mapper.BdmDeviceOnlineMapper;
import com.xh.vdm.statistic.service.BdmDeviceOnlineService;
import com.xh.vdm.statistic.vo.request.BdmDeviceOnlineRequest;
import com.xh.vdm.statistic.vo.response.DeviceOnlineResponse;
import com.xh.vdm.statistic.vo.response.terminal.OnlineCountResponse;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.DataAuthCE;
import org.springframework.stereotype.Service;
import java.util.List;
import javax.annotation.Resource;

/**
 * (BdmDeviceOnline)表服务实现类
 */
@Service
public class BdmDeviceOnlineServiceImpl extends ServiceImpl<BdmDeviceOnlineMapper, BdmDeviceOnline> implements BdmDeviceOnlineService {
	@Resource
	private BdmDeviceOnlineMapper bdmDeviceOnlineMapper;

	@Resource
	private CETokenUtil ceTokenUtil;

	/**
     * 分页查询
     *
     * @param bdmDeviceOnlineRequest 筛选条件
     * @param query                  分页对象
     * @param userId
     * @return 查询结果
     */
	@Override
	public List<DeviceOnlineResponse> queryByPage(BdmDeviceOnlineRequest bdmDeviceOnlineRequest, Query query, Long userId) {
		if (AuthUtil.isAdministrator()) {
			userId = null;
		}
		DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
		return this.bdmDeviceOnlineMapper.queryByPage(bdmDeviceOnlineRequest,query, userId, dataAuthCE.getOrgListStr(), dataAuthCE.getAccount());
	}

	@Override
	public long countOnline(BdmDeviceOnlineRequest bdmDeviceOnlineRequest, Long userId) {
		if (AuthUtil.isAdministrator()) {
			userId = null;
		}
		DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
		return this.bdmDeviceOnlineMapper.countOnline(bdmDeviceOnlineRequest, userId, dataAuthCE.getOrgListStr(), dataAuthCE.getAccount());
	}

    @Override
    public List<OnlineCountResponse> getOnlineTrendered(Long userId) {
        return this.bdmDeviceOnlineMapper.getOnlineTrendered(userId);
    }
}
