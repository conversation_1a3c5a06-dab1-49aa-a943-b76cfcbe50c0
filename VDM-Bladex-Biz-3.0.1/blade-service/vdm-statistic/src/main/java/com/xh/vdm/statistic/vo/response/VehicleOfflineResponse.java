package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.config.ExcelDateConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 车辆离线报表返回类
 * <AUTHOR>
 * @date 2021/10/26 13:37
 */
@ApiModel(value = "车辆离线报表返回类")
@Data
public class VehicleOfflineResponse {

    @ApiModelProperty(value = "车队名称")
    @JsonProperty("dept_name")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆离线报表", "车队名称"})
    @ColumnWidth(40)
    private String deptName;

    @ApiModelProperty(value = "车牌号码")
    @JsonProperty("licence_plate")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆离线报表", "车牌号码"})
    @ColumnWidth(15)
    private String vehicleLicence;

    @ApiModelProperty(value = "车牌颜色")
    @JsonProperty("licence_color")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆离线报表", "车牌颜色"})
    @ColumnWidth(15)
    private String licenceColor;

    @ApiModelProperty(value = "行业类型")
    @JsonProperty("vehicle_mode")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆离线报表", "行业类型"})
    @ColumnWidth(20)
    private String vehicleModel;

    @ApiModelProperty(value = "车辆归属")
    @JsonProperty("vehicle_owner")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆离线报表", "车辆归属"})
    @ColumnWidth(20)
    private String vehicleOwner;

    @ApiModelProperty(value = "车辆接入方式")
    @JsonProperty("access_mode")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆离线报表", "车辆接入方式"})
    @ColumnWidth(15)
    private String accessMode;

	@ApiModelProperty(value = "车辆离线天数")
	@JsonProperty("offline_days")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty({"车辆离线报表", "车辆离线天数"})
	@ColumnWidth(15)
	private String offlineDays;

    @ApiModelProperty(value = "离线时间")
    @JsonProperty("offline_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty(value = {"车辆离线报表", "离线时间"},converter = ExcelDateConverter.class)
    @ColumnWidth(22)
    private Date offLineTime;

    @ApiModelProperty(value = "位置")
    @JsonProperty("address")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆离线报表", "位置"})
    @ColumnWidth(90)
    private String locAddr;

}
