package com.xh.vdm.statistic.config;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import com.alibaba.nacos.api.naming.pojo.Instance;
//import com.netflix.loadbalancer.AbstractLoadBalancerRule;
//import com.netflix.loadbalancer.BaseLoadBalancer;
//import com.netflix.loadbalancer.Server;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

public class NacosWeightLoadBalancerRule /*extends AbstractLoadBalancerRule*/ {

	/**
	 * NacosDiscoveryProperties内置了基于权重的负载均衡算法
	 */
	/*@Resource
	private NacosDiscoveryProperties nacosDiscoveryProperties;*/

	/**
	 * 读取配置文件并初始化NacosWeightedRule
	 */
	//@Override
	/*public void initWithNiwsConfig(IClientConfig iClientConfig) {

	}*/

	/**
	 * 实现基于权重的负载均衡算法
	 */
	/*@Override
	public Server choose(Object o) {
		try {
			BaseLoadBalancer loadBalancer = (BaseLoadBalancer)this.getLoadBalancer();
			//请求的微服务名称
			String name = loadBalancer.getName();
			//获得Nacos的应用
			NamingService namingService = nacosDiscoveryProperties.namingServiceInstance();
			//Nacos client通过基于权重的负载均衡算法，返回一个实例
			Instance instance = namingService.selectOneHealthyInstance(name);
			return new NacosServer(instance);
		} catch (NacosException e) {
			log.error("获取服务实例异常", e);
			return null;
		}
	}*/
}
