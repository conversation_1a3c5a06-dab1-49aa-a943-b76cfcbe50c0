package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.vo.request.LocationsRequest;
import com.xh.vdm.statistic.vo.response.LocationKuduResponse;
import com.xh.vdm.statistic.vo.response.tg.TargetResponse;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.mp.support.Query;
import java.util.List;
import java.util.Map;

/**
 * 服务接口
 */
public interface LocationsService extends IService<LocationKudu> {

	/**
	 * 分页查询
	 *
	 * @param locationsRequest 筛选条件
	 * @param query            分页对象
	 * @param ids
	 * @return 查询结果
	 */
	List<LocationKuduResponse> queryByPage(LocationsRequest locationsRequest, Query query, List<String> ids);

	long countLocations(LocationsRequest locationRequest, List<String> ids);

	/**
	 * 分页查询轨迹信息
	 * @return
	 */
	List<LocationKudu> locationPage(List<List<Long>> deviceIds, List<List<Long>> targetIdList, Integer batch, Long startTime, Long endTime, Query query);

	/**
	 * 根据条件查询定位信息总数
	 * @param deviceIds
	 * @param targetIdList
	 * @param batch
	 * @param startTime
	 * @param endTime
	 * @param query
	 * @return
	 */
	long locationCount(List<List<Long>> deviceIds, List<List<Long>> targetIdList, Integer batch, Long startTime, Long endTime);

}
