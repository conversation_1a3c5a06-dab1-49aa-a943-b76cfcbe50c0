package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "bdm_target_odometer", autoResultMap = true)
public class BdmTargetOdometer implements Serializable {

	@TableField(value = "id")
	private Long id;

	@TableField(value = "dept_id")
	private Long deptId;

	@TableField(value = "target_type")
	private Byte targetType;

	@TableField(value = "target_category")
	private Short targetCategory;

	@TableField(value = "target_id")
	private Long targetId;

	@TableField(value = "odometer")
	private Double odometer;

	@TableField(value = "last_lon")
	private Double lastLon;

	@TableField(value = "last_lat")
	private Double lastLat;

	@TableField(value = "last_alt")
	private Short lastAlt;

	@TableField(value = "last_time")
	private Long lastTime;

	@TableField(exist = false)
	private Double totalOdometer;
}

