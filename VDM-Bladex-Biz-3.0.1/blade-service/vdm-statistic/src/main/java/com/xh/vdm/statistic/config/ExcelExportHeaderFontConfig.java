package com.xh.vdm.statistic.config;

import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;

import java.util.List;

/**
 * @Description: easyexcel 表头配置
 * @Author: zhouxw
 * @Date: 2022/10/8 11:52 AM
 */
public class ExcelExportHeaderFontConfig extends HorizontalCellStyleStrategy {


    private final WriteCellStyle headWriteCellStyle;
    private final WriteCellStyle contentWriteCellStyle;

    /**
     * 操作列
     */
    private final List<Integer> columnIndexes;



    public ExcelExportHeaderFontConfig(List<Integer> columnIndexes, WriteCellStyle headWriteCellStyle, WriteCellStyle contentWriteCellStyle) {
        this.headWriteCellStyle = headWriteCellStyle;
        this.contentWriteCellStyle = contentWriteCellStyle;
        this.columnIndexes = columnIndexes;
    }

    @Override
    protected void setHeadCellStyle( CellWriteHandlerContext context) {
        // 获取字体实例
        WriteFont headWriteFont = new WriteFont();
        //对符合表头的设置（excel中的第一行）
        if (columnIndexes.get(0).equals(context.getRowIndex())) {
            headWriteFont.setFontHeightInPoints((short) 14);
            headWriteFont.setBold(true);
        }
        headWriteCellStyle.setWriteFont(headWriteFont);
        if (stopProcessing(context)) {
            return;
        }
        WriteCellData<?> cellData = context.getFirstCellData();
        WriteCellStyle.merge(headWriteCellStyle, cellData.getOrCreateStyle());
    }
}
