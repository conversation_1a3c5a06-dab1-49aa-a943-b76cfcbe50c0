package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.StatVehRunningStateDay;
import com.xh.vdm.statistic.entity.StatVehRunningStateMonth;
import com.xh.vdm.statistic.vo.request.VehRunningStateDayRequest;
import com.xh.vdm.statistic.vo.request.VehRunningStateRequest;
import com.xh.vdm.statistic.vo.response.alarm.AlarmSortResponse;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.mp.support.Query;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
public interface StatVehRunningStateDayMapper extends BaseMapper<StatVehRunningStateDay> {

	/**
	 * 分页查询车辆运行状态
	 * @param request
	 * @param page
	 * @return
	 */
	IPage<StatVehRunningStateDay> getVehRunningStateDayByPage(@Param("month") String month, @Param("request") VehRunningStateDayRequest request, @Param("page") Page<StatVehRunningStateMonth> page, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);

	/**
	 * 获取当日报警排行信息
	 * @param month
	 * @param deptIds
	 * @param vehicleIds
	 * @param page
	 * @return
	 */
	IPage<AlarmSortResponse> getAlarmSortInfoToday(@Param("month") String month, @Param("date") String date,  @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, IPage<AlarmSortResponse> page);



	/**
	 * @description: 判断表是否存在
	 * @author: zhouxw
	 * @date: 2022/9/2 8:07 AM
	 * @param: []
	 * @return: void
	 **/
	void checkExist(String month);
}
