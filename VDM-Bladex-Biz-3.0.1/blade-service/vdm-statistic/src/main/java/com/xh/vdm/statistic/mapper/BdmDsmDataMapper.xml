<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmDsmDataMapper">

    <select id="getDailyCarAttach" resultType="com.xh.vdm.statistic.entity.BdmDsmData">
        select c.licence_color, a.licence_plate, a.jpg1, a.jpg2, a.jpg3, a.mp4, a.bin
        from bdm_dsm_data_sh as a inner join bdm_terminal as b on a.phone = b.phone inner join bdm_vehicle as c on b.id = c.terminal_id
        where a.`time` &gt;= #{startTime, jdbcType=VARCHAR} and a.`time` &lt;= #{endTime, jdbcType=VARCHAR}
    </select>
</mapper>
