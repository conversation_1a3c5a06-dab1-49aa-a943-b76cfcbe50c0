package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.report.StatReportDay;
import com.xh.vdm.statistic.vo.request.CompanyAndDate;
import com.xh.vdm.statistic.vo.response.ReportInfoResponse;
import org.springblade.core.mp.support.Query;

/**
 * <p>
 * 企业日报表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface IStatReportDayService extends IService<StatReportDay> {

	/**
	 * 查询日报信息
	 * @param cd
	 * @param query
	 * @return
	 * @throws Exception
	 */
	IPage<ReportInfoResponse> findReportDayByPage(CompanyAndDate cd, Query query) throws Exception;
}
