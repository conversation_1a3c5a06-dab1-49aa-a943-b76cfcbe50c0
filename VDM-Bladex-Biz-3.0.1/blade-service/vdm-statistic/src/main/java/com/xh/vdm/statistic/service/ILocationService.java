package com.xh.vdm.statistic.service;

import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.bd.entity.VehicleStopPointBase;
import com.xh.vdm.statistic.entity.AlarmLocation;

import java.util.List;

/**
 *  定位数据相关服务类
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
public interface ILocationService  {

    /**
     * @description: 根据指定日期获取上报的车牌号信息
     * day 格式为 yyyyMMdd
     * @author: zhouxw
     * @date: 2022/9/1 1:53 PM
     * @param: [day：指定的上报日期]
     * @return: java.util.List<java.lang.String>
     **/
    List<VehicleBase> findUploadLicencePlatesByDay(String day) throws Exception;


	/**
	 * @description: 根据指定日期获取上报的车牌号信息
	 * day 格式为 yyyyMMdd
	 * @author: zhouxw
	 * @date: 2022/9/1 1:53 PM
	 * @param: [day：指定的上报日期]
	 * @return: java.util.List<java.lang.String>
	 **/
	List<VehicleStopPointBase> findStopPointVehicleListByDay(String day) throws Exception;

    /**
     * @description: 根据指定日期和车牌号获取上报定位数据
     * day 格式为 yyyyMMdd
     * @author: zhouxw
     * @date: 2022/9/1 2:40 PM
     * @param: [day：指定的日期，licencePlate：指定的车牌号]
     * @return: java.util.List<com.xh.vdm.statistic.entity.Location>
     **/
    List<LocationKudu> findUploadLocationPointListByDay(String day , String licencePlate, int licenceColor) throws Exception;


	List<LocationKudu> findAllUploadLocationPointListByDay(String day , String licencePlate, int licenceColor) throws Exception;

	/**
	 * @description: 根据车牌号、车牌颜色、开始时间、结束时间获取定位点数量
	 * @author: zhouxw
	 * @date: 2023-06-160 15:49:26
	 * @param: [licencePlate, licenceColor, startTime, endTime]
	 * @return: long
	 **/
	long findLocationCountByCondition(String licencePlate, int licenceColor, long startTime, long endTime);

	/**
	 * @description: 根据车牌号、车牌颜色、开始时间、结束时间获取定位点列表
	 * @author: zhouxw
	 * @date: 2023-06-160 15:49:26
	 * @param: [licencePlate, licenceColor, startTime, endTime]
	 * @return: long
	 **/
	List<LocationKudu> findLocationByCondition(String licencePlate, int licenceColor, long startTime, long endTime);

	/**
	 * 根据报警id列表查询在3分钟之后的报警
	 * @param alarmIdList
	 * @return
	 */
	List<AlarmLocation> findLocationByAlarmIdListInThreeMinutes(List<Long> alarmIdList, Long startTime, Long endTime);

	/**
	 * @description: 根据日期查询所有上传数据的车牌号列表，包括有效、无效、纠偏、漂移
	 * @author: zhouxw
	 * @date: 2022/11/7 6:36 PM
	 * @param: [day]
	 * @return: java.util.List<java.lang.String>
	 **/
	List<VehicleBase> findAllUploadLicencePlatesByDay(String day);

	/**
	 * @description: 根据指定日期和车牌号获取所有上报点位信息，包含 无效、有效、纠偏、漂移
	 * @author: zhouxw
	 * @date: 2022/11/7 2:52 PM
	 * @param: [day, licencePlate]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.Location>
	 **/
	List<LocationKudu> findUploadLocationPointListAllByDay(String day , String licencePlate, int licenceColor) throws Exception;

}
