package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.CacheSegLimitSpeedTerminalResponse;
import com.xh.vdm.statistic.vo.request.SegLimitSpeedTerminalRequest;
import com.xh.vdm.statistic.vo.response.SegLimitSpeedTerminalResponse;

public interface ICacheSegLimitSpeedTerminalService extends IService<CacheSegLimitSpeedTerminalResponse> {

    /**
     * @description: 统计车辆地图超速，并入库
     * @author: zhouxw
     * @date: 2023-02-40 15:27:09
     * @param: [request, req]
     * @return: com.xh.vdm.statistic.vo.ObjectRestResponse<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>>
     **/
    void statisticsSegLimitSpeedTerminal(Long startTime, Long endTime) throws Exception;


    /**
     * @description: 查询车辆地图超速
     * @author: zhouxw
     * @date: 2023-02-40 16:20:53
     * @param: [request, req]
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    IPage<SegLimitSpeedTerminalResponse> querySegLimitSpeedTerminalCache(SegLimitSpeedTerminalRequest req) throws Exception;


}
