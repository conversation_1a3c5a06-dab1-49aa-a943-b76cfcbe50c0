<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xh.vdm.statistic.mapper.BdmTrackCalendarMapper">

    <select id="getLocCalendarByObjMonth" resultType="com.xh.vdm.statistic.entity.BdmTrackCalendar">
        select * from bdm_track_calendar
        where target_type = #{targetType} and target_id = #{targetId} and device_type = #{deviceType} and device_id = #{deviceId}
        and date_trunc('month', month::timestamp) = date_trunc('month', to_date(#{time}, 'YYYY-MM'))
    </select>


    <select id="list" resultType="java.lang.Integer">
        SELECT mark
        FROM bdm_track_calendar
        WHERE DATE_TRUNC('month', month::timestamp) = DATE_TRUNC('month',TO_DATE(#{month}, 'YYYY-MM'))
          and device_id = #{deviceId}
          and device_type = #{deviceType}
          and target_id = #{targetId}
          and target_type = #{targetType};
    </select>

</mapper>
