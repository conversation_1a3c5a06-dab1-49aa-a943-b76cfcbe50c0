package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.report.AlarmBaseTypeAndCount;
import com.xh.vdm.statistic.mapper.impala.ImpalaAlarmMapper;
import com.xh.vdm.statistic.service.IImpalaAlarmService;
import com.xh.vdm.statistic.utils.VdmUserInfoUtil;
import org.springblade.common.utils.DateUtil;
import org.springblade.core.secure.BladeUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class ImpalaAlarmServiceImpl implements IImpalaAlarmService {


	@Autowired
	private ImpalaAlarmMapper impalaAlarmMapper;

	@Resource
	private VdmUserInfoUtil vdmUsiUtil;

	@Override
	public List<DateAndCount> findAlarmCountHour(Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds) {
		return impalaAlarmMapper.getAlarmCountHour(startTime, endTime, deptIds, vehicleIds);
	}

	@Override
	public List<DateAndCount> findAlarmHandleCountHour(Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds, String userType) {
		return impalaAlarmMapper.getHandleCountHour(startTime, endTime, deptIds, vehicleIds, userType);
	}

	/**
	 * @description: 查询指定时间段，超过指定时长的疲劳报警
	 * @author: zhouxw
	 * @date: 2023-07-206 09:40:38
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd, duration 超过的秒数]
	 * @return: long
	 **/
	@Override
	public long findFatigueCountByDateAndDuration(Long startTime, Long endTime, long duration, List<Long> deptIds, List<Integer> vehicleIds) throws Exception {
		return impalaAlarmMapper.getFatigueCountByDateAndDuration(startTime, endTime, duration, deptIds, vehicleIds);
	}

	@Override
	public long findFatigueHandleCountByDateAndDuration(Long startTime, Long endTime, long duration, List<Long> deptIds, List<Integer> vehicleIds, String userType) throws Exception {
		return impalaAlarmMapper.getFatigueHandleCountByDateAndDuration(startTime, endTime, duration, deptIds, vehicleIds, userType);
	}

	@Override
	public long findAlarmCountByDate(List<Integer> alarmTypes, Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds) throws Exception {
		return impalaAlarmMapper.getAlarmCountByDate(alarmTypes, startTime, endTime, deptIds, vehicleIds);
	}



	@Override
	public long findAlarmHandleCountByDate(List<Integer> alarmTypes, Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds, String userType) throws Exception {
		return impalaAlarmMapper.getAlarmHandleCountByDate(alarmTypes, startTime, endTime, deptIds, vehicleIds, userType);
	}

	@Override
	public List<DateAndCount> findAlarmAndHandleCountPerDate(Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds, String userType) throws Exception {
		return impalaAlarmMapper.getAlarmHandleCountPerDate(startTime, endTime, deptIds, vehicleIds, userType);
	}

	@Override
	public List<AlarmBase> findAlarmBaseByDate(List<Integer> alarmTypes, Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds) throws Exception {
		return impalaAlarmMapper.getAlarmBaseByDate(alarmTypes, startTime, endTime, deptIds, vehicleIds);
	}

	@Override
	public long findAlarmHandleCountByAlarmIds(List<Long> alarmIds, String userType) throws Exception {
		return impalaAlarmMapper.getAlarmHandleCountByAlarmIds(alarmIds, userType);
	}

	@Override
	public IPage<UnHandleRealTimeAlarm> findUnHandleRealTimeAlarmByPage(List<Integer> alarmTypeList, List<Long> deptIds, List<Integer> vehicleIds, String userType, IPage<BdmSecurity> page) throws Exception {
		//查询一个小时内的未处理报警信息
		long endTime = new Date().getTime() / 1000;
		long startTime = endTime - 3600;
		return impalaAlarmMapper.getUnHandleRealTimeAlarmByPage(alarmTypeList, startTime,endTime,deptIds,vehicleIds,userType, page);
	}

	@Override
	public long findAlarmCountByCondition(List<Long> deptIds, List<Integer> vehicleIds, Long startTime, Long endTime) throws Exception {
		return impalaAlarmMapper.getAlarmCountByDeptIdAndDurationOrHandle(deptIds, vehicleIds, startTime, endTime);
	}

	@Override
	public long findAlarmCountByAlarmTypes(List<Long> deptIds, List<Integer> vehicleIds, List<Integer> alarmTypes, Long startTime, Long endTime) throws Exception {
		return impalaAlarmMapper.getAlarmCountByDeptIdAndDurationAndType(deptIds, vehicleIds, startTime, endTime, alarmTypes);
	}

	@Override
	public long findAlarmHandleCountByAlarmTypes(List<Long> deptIds, List<Integer> vehicleIds, List<Integer> alarmTypes, Long startTime, Long endTime, String userType) throws Exception {
		return impalaAlarmMapper.getAlarmHandleCountByDeptIdAndDurationAndType(deptIds, vehicleIds, startTime, endTime, alarmTypes, userType);
	}

	@Override
	public List<AlarmTypeAndDateAndCount> findAlarmTypeAndDateAndCountOrHandle(List<Integer> alarmTypes, List<Long> deptIds, List<Integer> vehicleIds, Long startTime, Long endTime, String userType) throws Exception {
		return impalaAlarmMapper.getAlarmTypeAndDateAndCountOrHandle(deptIds,vehicleIds,startTime,endTime,alarmTypes,userType);
	}

	@Override
	public List<AlarmBaseTypeAndCount> findAlarmTypeAndCountOrHandle(List<Integer> alarmTypes, List<Long> deptIds, List<Integer> vehicleIds, Long startTime, Long endTime, String userType) throws Exception {
		return impalaAlarmMapper.getAlarmTypeAndCountOrHandle(deptIds,vehicleIds,startTime,endTime,alarmTypes,userType);
	}

	@Override
	public List<VehicleAndCount> findVehicleAlarmCountTop(List<Integer> alarmTypes, List<Long> deptIds, List<Integer> vehicleIds, Long startTime, Long endTime, Integer count) throws Exception {
		return impalaAlarmMapper.getVehicleAlarmCountTop(deptIds, vehicleIds, startTime, endTime, alarmTypes, count);
	}


	@Override
	public List<VehicleAndAlarmTypeAndCount> findVehicleAlarmCountInfo(List<Integer> alarmTypes, List<Integer> vehicleIds, Long startTime, Long endTime) throws Exception {
		return impalaAlarmMapper.getVehicleAlarmCountInfo(vehicleIds, startTime, endTime, alarmTypes);
	}

	@Override
	public long findVehicleCountWithAlarm(List<Long> deptIds, List<Integer> vehicleIds, List<Integer> alarmTypes, Long startTime, Long endTime) throws Exception {
		List<Byte> alarmTypeList = new ArrayList<>();
		if(alarmTypes != null && alarmTypes.size() > 0){
			alarmTypes.forEach(item -> {
				alarmTypeList.add(item.byteValue());
			});
		}
		return impalaAlarmMapper.getVehicleCountByDeptIdsAndAlarmTypes(deptIds, alarmTypeList,startTime, endTime);
	}

	@Override
	public List<AlarmTypeAndCount> findVehicleAlarmCountInfoByDriverDuration(List<StatDriverDuration> list, List<Integer> alarmTypes) throws Exception {
		return impalaAlarmMapper.getVehicleAlarmCountInfoByDriverDuration(list, alarmTypes);
	}

	/**
	 * @description: 根据部门id查询今日报警数量（包含本部门以及子部门、所有部门账户关联的外部车辆）
	 * 查询 impala
	 * @author: zhouxw
	 * @date: 2023-06-164 21:27:36
	 * @param: [deptId]
	 * @return: long
	 **/
	public long findTodayTotalCountByDeptId(BladeUser user, long userId) throws Exception{

		//暂定：先查询所有部门中的车辆，然后查询本账号关联的外部的车辆，
		//1.查询子部门信息
		List<Long> deptIds = vdmUsiUtil.getChildrenAndSelfDeptId(user);

		//2.查询账号关联的车辆
		List<Integer> vehicleIds = vdmUsiUtil.getVehicleIdList(userId);

		//3.查询本部门及子部门、关联车辆的报警数
		//今日日期
		Date date = new Date();
		long startTime = DateUtil.getDayFirstSecondTimestampNoLine(date.getTime()/1000);
		long endTime = DateUtil.getDayLastSecondTimestamp(date.getTime()/1000);
		//查询impala
		long totalCount = impalaAlarmMapper.getAlarmCountByDeptIdAndDuration(deptIds, vehicleIds, startTime, endTime);
		return totalCount;
	}
}
