package com.xh.vdm.statistic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.BdmDsmData;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 映射：告警附件表
 */
public interface BdmDsmDataMapper extends BaseMapper<BdmDsmData> {

    /**
     * 根据时间段获取告警附件
     */
    @DS("master")
    List<BdmDsmData> getDailyCarAttach (@Param("startTime") String startTime, @Param("endTime") String endTime);
}
