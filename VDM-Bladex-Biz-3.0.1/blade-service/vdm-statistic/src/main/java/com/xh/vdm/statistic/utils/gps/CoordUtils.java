package com.xh.vdm.statistic.utils.gps;


/**
 * 坐标转化工具
 *  功能包括：坐标加密解密功能。墨卡托投影功能。 经纬度转化meter功能。 以及局部坐标转wgs84坐标功能。
 * <AUTHOR>
 * @date 2019/11/12.
 * @description
 */
public class CoordUtils {

    public final static double G_PI = 3.14159265358979323846264338327950288419716939937510;
    public final static double M_ORIGIN_SHIFT = 2 * G_PI * 6378137 / 2.0;
    /**
     *参考点坐标
     */
    double refLon = 113.452226017787;
    double refLat = 23.155076679403;
    double originLon, originLat;
    /**
     * 当前纬度半径与赤道半径比值
     */
    double ratioFloat = 0.91944278118242;

    /**
     * 局部坐标 Y与大地坐标正北方向夹角
     */
    double angle = 8.8019;
    /**
     * 局部坐标 中心点坐标（wgs84）
     */
    double centerLon;
    /**
     * 局部坐标 坐标中心点坐标（wgs84）
     */
    double centerLat;

    public CoordUtils() {
    }

    /**
     *局部坐标转wgs84坐标功能。构造函数
     * @param angle  局部坐标相对于地球正北方向的选择角度
     * @param centerLon 局部坐标的原点的-经度
     * @param centerLat 局部坐标的原点的-纬度
     */
    public CoordUtils(double angle, double centerLon, double centerLat) {
        this.angle = angle;
        this.centerLon = centerLon;
        this.centerLat = centerLat;
    }

    private CoorEntity metersToLatLon(double mx, double my) {
        double lon = (mx / M_ORIGIN_SHIFT) * 180.0;
        double lat = (my / M_ORIGIN_SHIFT) * 180.0;
        lat = 180 / G_PI * (2 * Math.atan(Math.exp(lat * G_PI / 180.0)) - G_PI / 2.0);
        return new CoorEntity(lon, lat);
    }


    private CoorEntity latLonToMeters(double lon, double lat) {
        double x = lon * M_ORIGIN_SHIFT / 180.0;
        double y = Math.log(Math.tan((90 + lat) * G_PI / 360.0)) / (G_PI / 180.0);
        y = y * M_ORIGIN_SHIFT / 180.0;
        return new CoorEntity(x, y);
    }

    public CoorEntity wgs2Gcj(double longitude, double latitude) {
        double a = 6378245.0;
        double ee = 0.00669342162296594323;
        double gpsX = longitude;
        double gpsY = latitude;

        double dLat = transformLat(gpsX - 105.0, gpsY - 35.0);
        double dLon = transformLon(gpsX - 105.0, gpsY - 35.0);

        double radLat = gpsY / 180.0 * G_PI;
        double magic = Math.sin(radLat);

        magic = 1 - ee * magic * magic;
        double sqrtMagic = Math.sqrt(magic);
        dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * G_PI);
        dLon = (dLon * 180.0) / (a / sqrtMagic * Math.cos(radLat) * G_PI);
        // 0.000003为高精度纠偏
        double mgLat = gpsY + dLat + 0.000003;
        // 0.000004为高精度纠偏
        double mgLon = gpsX + dLon + 0.000004;

        return new CoorEntity(mgLon, mgLat);
    }

    public CoorEntity gcj2Wgs(double longitude, double latitude) {
        double wgsLon = longitude;
        double wgsLat = latitude;

        CoorEntity coorEntity = wgs2Gcj(wgsLon, wgsLat);
        double dx = coorEntity.getLon() - longitude;
        double dy = coorEntity.getLat() - latitude;

        while (true) {
            if (Math.abs(dx) < 1e-6 && Math.abs(dy) < 1e-6) {
                break;
            }
            wgsLon -= dx;
            wgsLat -= dy;
            CoorEntity ce = wgs2Gcj(wgsLon, wgsLat);
            dx = ce.getLon() - longitude;
            dy = ce.getLat() - latitude;
        }
        return new CoorEntity(wgsLon, wgsLat);
    }


    /**
     * 纬度转换函数
     * @param x
     * @param y
     * @return
     */
    private double transformLat(double x, double y) {
        double ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * G_PI) + 20.0 * Math.sin(2.0 * x * G_PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(y * G_PI) + 40.0 * Math.sin(y / 3.0 * G_PI)) * 2.0 / 3.0;
        ret += (160.0 * Math.sin(y / 12.0 * G_PI) + 320 * Math.sin(y * G_PI / 30.0)) * 2.0 / 3.0;
        return ret;
    }

    /**
     * 经度转换函数
     * @param x
     * @param y
     * @return
     */
    private double transformLon(double x, double y) {
        double ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
        ret += (20.0 * Math.sin(6.0 * x * G_PI) + 20.0 * Math.sin(2.0 * x * G_PI)) * 2.0 / 3.0;
        ret += (20.0 * Math.sin(x * G_PI) + 40.0 * Math.sin(x / 3.0 * G_PI)) * 2.0 / 3.0;
        ret += (150.0 * Math.sin(x / 12.0 * G_PI) + 300.0 * Math.sin(x / 30.0 * G_PI)) * 2.0 / 3.0;
        return ret;
    }


    /**
     * 将经纬度转换为相对于参考点的坐标
     * @param lon
     * @param lat
     * @return
     */
    private CoorEntity translateLnglat(double lon, double lat) {
        CoorEntity coorEntity = latLonToMeters(refLon, refLat);
        originLon = coorEntity.getLon();
        originLat = coorEntity.getLat();

        CoorEntity wgs2Gcj = wgs2Gcj(lon, lat);
        CoorEntity latLonToMeters = latLonToMeters(wgs2Gcj.getLon(), wgs2Gcj.getLat());
        double mctLon = latLonToMeters.getLon();
        double mctLat = latLonToMeters.getLat();
        //计算当前点与参考点墨卡托距离
        double x = (mctLon - originLon) * ratioFloat;
        double y = (mctLat - originLat) * ratioFloat;
        return new CoorEntity(-x, y);
    }

    /**
     * 将目标点相对坐标(相对于建筑物原点)转换为相对于参考点坐标
     * @param centerLon
     * @param centerLat
     * @param x
     * @param y
     * @return
     */
    private CoorEntity translateRelativeCoor(double centerLon, double centerLat, double x, double y) {
        //建筑物中心点与参考点偏移
        CoorEntity translateLnglat = translateLnglat(centerLon, centerLat);
        double buildX = translateLnglat.getLon();
        double buildY = translateLnglat.getLat();
        //建筑原点与目标点的距离
        double radius = Math.sqrt(x * x + y * y);
        //目标点在本地坐标系的角度
        double angleLocal = Math.atan2(y, x);
        //本地坐标系的角度

        //地图坐标系的角度
        double angleMap = angleLocal - angle * Math.PI / 180;
        //目标点相对于参考点偏移值
        double positionX = buildX - Math.cos(angleMap) * radius;
        double positionY = buildY + Math.sin(angleMap) * radius;
        return new CoorEntity(positionX, positionY);
    }

    /**
     * 将目标点相对坐标(相对于建筑物原点)转换为经纬度,沈世华的根据角度旋转方法
     * @param x
     * @param y
     * @return
     */
    public CoorEntity translateRelativeCoor2lnglat(double x, double y) {
        y = -y;
        CoorEntity coorEntity = translateRelativeCoor(centerLon, centerLat, x, y);
        double positionX = coorEntity.getLon();
        double positionY = coorEntity.getLat();
        positionX = originLon - positionX / ratioFloat;
        positionY = originLat + positionY / ratioFloat;

        CoorEntity metersToLatLon = metersToLatLon(positionX, positionY);
        double positionLon = metersToLatLon.getLon();
        double positionLat = metersToLatLon.getLat();
        CoorEntity wgs = gcj2Wgs(positionLon, positionLat);
        return wgs;
    }


//    public static void main(String[] args) {
//        CoordUtils ct = new CoordUtils();
//        for (int i = 0; i < 5; i++) {
//            long st = System.currentTimeMillis();
//
//            CoorEntity coorEntity = ct.TranslateRelativeCoor2lnglat(113.447897993, 23.1577617315, -5.358, 20.7687072);
//            System.out.println(coorEntity.getLon() + "," + coorEntity.getLat());
//            System.out.println("haomiao :" + (System.currentTimeMillis() - st));
//        }
//
//        long st = System.currentTimeMillis();
//        CoorEntity coorEntity = ct.TranslateRelativeCoor2lnglat(113.447897993, 23.1577617315, -8.328, 4.110702479);
//        System.out.println(coorEntity.getLon() + "," + coorEntity.getLat());
//        System.out.println("asdfa :" + (System.currentTimeMillis() - st));
//
//    }

}
