<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BladeUserMapper">

    <select id="getRoleNamesByRoleIds" parameterType="long" resultType="string">
        select role_alias from blade_role where id in
            <foreach collection="roleIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
    </select>

    <select id="getUserIdWithMultiDept" resultType="long">
        select user_id from blade_user_dept group by user_id having count(*) > 1
    </select>

    <select id="getDeptIdsByUserId" parameterType="long" resultType="long">
        select dept_id from blade_user_dept where user_id = #{userId}
    </select>

    <update id="updateDeptIdByUserId">
        update blade_user set dept_id =#{userDept.deptId} where id = #{userDept.userId,jdbcType=BIGINT}
    </update>
</mapper>
