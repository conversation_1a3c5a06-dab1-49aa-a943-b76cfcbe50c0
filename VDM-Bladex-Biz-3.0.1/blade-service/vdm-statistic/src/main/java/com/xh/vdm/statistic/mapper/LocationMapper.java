package com.xh.vdm.statistic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.bd.entity.VehicleStopPointBase;
import com.xh.vdm.statistic.entity.AlarmLocation;
import com.xh.vdm.statistic.entity.Location;
import com.xh.vdm.statistic.entity.VehicleAndDate;
import lombok.Data;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
public interface LocationMapper extends BaseMapper<LocationKudu> {

    /**
     * @description: 根据指定的日期获取上报的车辆列表
     * @author: zhouxw
     * @date: 2022/9/1 1:55 PM
     * @param: [day]
     * @return: java.util.List<java.lang.String>
     **/
    @DS("location")
    List<VehicleBase> getUploadLicencePlateListByDay(@Param("startTime") long startTime, @Param("endTime") long endTime);




	/**
     * @description: 根据指定的日期和车牌号获取上报的定位数据
     * @author: zhouxw
     * @date: 2022/9/1 3:00 PM
     * @param: [day：指定的日期, licencePlate：指定的车牌号]
     * @return: java.util.List<com.xh.vdm.statistic.entity.Location>
     **/
    @DS("location")
    List<LocationKudu> getUploadLocationListByDay(@Param("startTime") long startTime, @Param("endTime") long endTime , @Param("licencePlate") String licencePlate, @Param("licenceColor") int licenceColor);


	@DS("location")
	List<LocationKudu> getAllUploadLocationListByDay(@Param("startTime") long startTime, @Param("endTime") long endTime , @Param("licencePlate") String licencePlate, @Param("licenceColor") int licenceColor);

	/**
	 * 根据报警id列表获取3分钟内的报警数据
	 * @param alarmIdList
	 * @return
	 */
	@DS("location")
	List<AlarmLocation> getLocationByAlarmIdListInThreeMinutes(@Param("alarmIdList") List<Long> alarmIdList, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * @description: 根据指定的 id 列表获取定位信息
     * @author: zhouxw
     * @date: 2022/9/13 9:20 AM
     * @param: [startTime 开始时间; endTime 结束时间; idList 数据id列表]
     * @return: java.util.List<com.xh.vdm.statistic.entity.Location>
     **/
    @DS("location")
    List<LocationKudu> getUploadLocationListById (@Param("startTime") long startTime, @Param("endTime") long endTime, @Param("idList") String idList);


	/**
	 * @description: 根据车辆信息和id列表获取定位点列表
	 * @author: zhouxw
	 * @date: 2023-06-160 11:54:13
	 * @param: [licencePlate, licenceColor, idList]
	 * @return: java.util.List<com.xh.vdm.bd.entity.LocationKudu>
	 **/
	@DS("location")
	List<LocationKudu> getLocationByVehicleAndId(@Param("licencePlate") String licencePlate, @Param("licenceColor") int licenceColor, @Param("idList") String idList);


	/**
	 * @description: 根据车牌号、车牌寒色、开始时间、结束时间查询车辆定位点数量
	 * @author: zhouxw
	 * @date: 2023-06-160 15:51:27
	 * @param: [licencePlate, licenceColor, startTime, endTime]
	 * @return: long
	 **/
	@DS("location")
	long getLocationCountByCondition(@Param("licencePlate") String licencePlate,  @Param("licenceColor") int licenceColor, @Param("startTime") long startTime, @Param("endTime") long endTime);

	/**
	 * 根据车辆列表和时间段查询定位开始时间
	 * @param vehicleList
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@DS("location")
	List<VehicleAndDate> getStartLocationsByVehicleListAndDuration(@Param("vehicleList") List<String> vehicleList, @Param("startTime") Long startTime, @Param("endTime") Long endTime);


	/**
	 * 根据车辆列表和时间段查询结束定位点
	 * @param vehicleList
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@DS("location")
	List<VehicleAndDate> getEndLocationsByVehicleListAndDuration(@Param("vehicleList") List<String> vehicleList, @Param("startTime") Long startTime, @Param("endTime") Long endTime);

	/**
	 * 根据时间段获取车辆每日首次定位时间。
	 */
	@DS("location")
	List<Map<String, Object>> getDailyCarLocStart (@Param("startTime") long startTime, @Param("endTime") long endTime, @Param("vehicleList") List<String> vehicleList);

	/**
	 * 根据时间段获取车辆每日末次定位时间。
	 */
	@DS("location")
	List<Map<String, Object>> getDailyCarLocEnd (@Param("startTime") long startTime, @Param("endTime") long endTime, @Param("vehicleList") List<String> vehicleList);
}
