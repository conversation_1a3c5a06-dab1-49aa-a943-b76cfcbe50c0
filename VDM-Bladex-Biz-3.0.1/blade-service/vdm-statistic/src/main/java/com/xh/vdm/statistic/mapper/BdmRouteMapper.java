package com.xh.vdm.statistic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.entity.BdmRoute;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;

import java.util.HashMap;
import java.util.List;

/**
 * 里程表Mapper
 * <AUTHOR>
 * @date 2021/11/12 12:24
 */
public interface BdmRouteMapper extends BaseMapper<BdmRoute> {

    /**
     * 查询DataMine前一天所有车牌号
     * @param tableName 表名
     * @return
     */
    /*@Select("<script>" +
            " select distinct licence_plate,plate_color from pos_gn.locations where loc_time &lt; #{endTime} and loc_ime >= #{startTime}" +
            "</script>")
	@DS("location")
    List<HashMap> queryLicencePlates(@Param("startTime") long startTime, @Param("endTime") long endTime);*/

    /**
     * 查询DataMine前一天所有车牌号
     * @param tableName 表名
     * @param licencePlate 车牌号码
     * @return
     */
    /*@Select("<script>" +
            " select id,licence_plate,licence_color, phone, longitude,latitude,altitude,speed,bearing,state,mileage,time,receive_time,valid from DataMine.${tableName} where licence_plate=#{licencePlate} and plate_color = #{plateColor} and valid in (1,2) order by time asc" +
            "</script>")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "licencePlate", column = "licence_plate"),
            @Result(property = "plateColor", column = "plate_color"),
            @Result(property = "phone", column = "phone"),
            @Result(property = "longitude", column = "longitude"),
            @Result(property = "latitude", column = "latitude"),
            @Result(property = "altitude", column = "altitude"),
            @Result(property = "speed", column = "speed"),
            @Result(property = "bearing", column = "bearing"),
            @Result(property = "state", column = "state"),
            @Result(property = "mileage", column = "mileage"),
            @Result(property = "time", column = "time"),
            @Result(property = "receiveTime", column = "receive_time"),
            @Result(property = "valid", column = "valid")
    })
    List<Location> queryLocations(String tableName, String licencePlate, Integer plateColor);*/


    /**
     * 查询DataMine前一天所有车牌号
     * @param tableName 表名
     * @param licencePlate 车牌号码
     * @return
     */
    /*@Select("<script>" +
            " select id,licence_plate,plate_color, phone, longitude,latitude,altitude,speed,bearing,mileage,time,receive_time,valid from DataMine.${tableName} where licence_plate=#{licencePlate} and licence_color=#{licenceColor} and valid in (1,2) order by loc_time asc" +
            "</script>")
    @Results({
            @Result(property = "id", column = "id"),
            @Result(property = "licencePlate", column = "licence_plate"),
            @Result(property = "plateColor", column = "plate_color"),
            @Result(property = "phone", column = "phone"),
            @Result(property = "longitude", column = "longitude"),
            @Result(property = "latitude", column = "latitude"),
            @Result(property = "altitude", column = "altitude"),
            @Result(property = "speed", column = "speed"),
            @Result(property = "bearing", column = "bearing"),
            @Result(property = "mileage", column = "mileage"),
            @Result(property = "time", column = "time"),
            @Result(property = "receiveTime", column = "receive_time"),
            @Result(property = "valid", column = "valid")
    })
    List<LocationKudu> queryLocationsByLicencePlate(String tableName, String licencePlate);*/
}
