package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.service.IBdmVehicleDriverService;
import com.xh.vdm.statistic.service.IBladeDeptService;
import com.xh.vdm.statistic.service.IStatDriveStatusService;
import com.xh.vdm.statistic.service.IVehicleStatService;
import com.xh.vdm.statistic.utils.BeanUtil;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.EasyExcelUtils;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.vo.request.DeptAndDurationRequest;
import com.xh.vdm.statistic.vo.request.DeptAndMonthRequest;
import com.xh.vdm.statistic.vo.response.DriveDaysLevelAndCount;
import com.xh.vdm.statistic.vo.response.DriverMileageStatResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: 企业驾驶员分析
 * @Author: zhouxw
 * @Date: 2022/11/20 3:52 PM
 */
@RestController
@RequestMapping("/bt/statistics/companyDriver")
@Slf4j
public class CompanyDriverController {

    @Resource
    private IBdmVehicleDriverService driverService;

    @Resource
    private IStatDriveStatusService driveStatusService;

	@Resource
	private IBladeDeptService deptService;

    @Resource
    private IVehicleStatService vehicleStatService;

    @Value("${static.file.path:/statistic/files/}")
    private String staticFilePath;

    private static ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
    private static ThreadLocal<SimpleDateFormat> sdfHolderShort = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    /**
     * @description: 企业驾驶员基本信息
     * 如果不指定企业，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 6:14 PM
     * @param: [deptId]
     * @return: com.xh.vdm.statistic.utils.R<com.xh.vdm.statistic.entity.CompanyDriverBaseInfo>
     **/
    @GetMapping("/companyDriverBaseInfo")
    public R<CompanyDriverBaseInfo> companyDriverBaseInfo(Long deptId){
        try{
            //1。驾驶员总数
            int driverCount = driverService.findDriverCount(deptId);

            //2。从业资格证过期数量
            int certExpirationCount = driverService.findCertExpirationCount(deptId);

            //3。从业资格证未过期数量
            int certIntimeCount = driverService.findCertIntimeCount(deptId);

            //4。驾驶员年龄分布
            List<DescAndCount> ageList = driverService.findDriverAgeGroup(deptId);

            //5。驾驶员驾龄分布
            List<DescAndCount> driveAgeList = driverService.findDriverDriveAgeGroup(deptId);

            CompanyDriverBaseInfo info = new CompanyDriverBaseInfo();
            info.setDriverTotalCount(driverCount);
            info.setDriverCertExpirationCount(certExpirationCount);
            info.setDriverCertIntimeCount(certIntimeCount);
            info.setAgeList(ageList);
            info.setDriveAgeList(driveAgeList);

            return R.data(info);
        }catch (Exception e){
            log.error("查询企业驾驶员基本信息失败",e);
            return R.fail("查询企业驾驶员基本信息失败");
        }
    }


    /**
     * @description: 统计驾驶员驾驶天数分布
     * @author: zhouxw
     * @date: 2022/11/20 7:51 PM
     * @param: [month, deptId]
     * @return: com.xh.vdm.statistic.utils.R<java.util.List<com.xh.vdm.statistic.vo.response.DriveDaysLevelAndCount>>
     **/
    @PostMapping("driveLevelDays")
    public R<IPage<DriveDaysLevelAndCount>> statDriverDriveDaysLevelAndCount(@RequestBody @Validated DeptAndMonthRequest request, BladeUser user){

        //前端下标从 0 开始，此处要加1
        request.setStart(request.getStart()+1);

        IPage pageF = new Page();
        List<DriveDaysLevelAndCount> listF = new ArrayList<>();

        try{

            listF = getDriveDaysLevelAndCount(request);

            //查询企业总数
			//租户id
			if(user == null){
				log.info("用户未登录或鉴权失败");
				return R.fail("用户未授权或鉴权失败");
			}
			String tenantId = user.getTenantId();
            LambdaQueryWrapper<BladeDept> wrapper = Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId , tenantId).eq(BladeDept::getIsDeleted ,0);
            if(request.getDeptId() != null){
                wrapper.eq(BladeDept::getId,request.getDeptId());
            }
            long totalCount = deptService.count(wrapper);
            pageF.setTotal(totalCount);
            pageF.setRecords(listF);

            return R.data(pageF);
        }catch (Exception e){
            log.error("统计驾驶员驾驶天数分布失败");
            return R.fail("统计驾驶员驾驶天数分布失败");
        }
    }


    /**
     * @description: 导出驾驶员驾驶天数分布信息
     * @author: zhouxw
     * @date: 2022/12/1 11:16 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<java.lang.String>
     **/
    @PostMapping("exportDriveLevelDays")
    public R<String> exportDriverDriveDaysLevelAndCount(@RequestBody @Validated DeptAndMonthRequest request){
        List<DriveDaysLevelAndCount> listF = new ArrayList<>();
        String filePath = "";
        try{
            request.setCount(Integer.MAX_VALUE);
            listF = getDriveDaysLevelAndCount(request);
            String title = "驾驶员驾驶天数分布";
            filePath = EasyExcelUtils.export(staticFilePath , EasyExcelUtils.DEFAULT_PROXY_PATH , title , listF , DriveDaysLevelAndCount.class, false );
        }catch (Exception e){
            log.error("导出驾驶员驾驶天数分布信息失败",e);
            return R.fail("导出驾驶员天数分布信息失败");
        }

        return R.data(filePath);
    }

    /**
     * @description: 获取驾驶员驾驶天数分布信息
     * @author: zhouxw
     * @date: 2022/12/1 11:18 AM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.DriveDaysLevelAndCount>
     **/
    private List<DriveDaysLevelAndCount> getDriveDaysLevelAndCount(DeptAndMonthRequest request) throws Exception {

        List<DriveDaysLevelAndCount> listF = new ArrayList<>();

        List<Long> deptIds = new ArrayList<>();

        //如果不指定月份，则默认为上一个月
        if(StringUtils.isBlank(request.getMonth())){
            request.setMonth(DateUtil.getLastMonthString());
        }

		//租户id
		BladeUser user = AuthUtil.getUser();
		if(user == null){
			log.info("用户未登录或鉴权失败");
			throw new Exception("用户未登录或鉴权失败");
		}
		String tenantId = user.getTenantId();

        //如果指定企业
        if(request.getDeptId() != null){
            List<DriveDaysLevelAndCount> list = getDriveDaysLevel(request.getMonth() , request.getDeptId());
            listF.addAll(list);
        }else {
            //如果不指定企业，则查询所有企业
            //分页查询企业信息
            Page page = new Page();
            page.setSize(request.getCount());
            page.setCurrent(request.getStart()/request.getCount()+(request.getStart() % request.getCount()>0?1:0));

            IPage<BladeDept> depts = deptService.page(page , Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId , tenantId).eq(BladeDept::getIsDeleted ,0));
            for(BladeDept dept : depts.getRecords()){
                deptIds.add(dept.getId());
            }

            //对每个企业都执行查询
            for(long deptId : deptIds){
                List<DriveDaysLevelAndCount> list = getDriveDaysLevel(request.getMonth() , deptId);
                listF.addAll(list);
            }
        }
        return listF;
    }

    /**
     * @description: 获取指定月份和部门的 驾驶员驾驶天数分布
     * @author: zhouxw
     * @date: 2022/11/20 9:47 PM
     * @param: [month, deptIdOri]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.DriveDaysLevelAndCount>
     **/
    private List<DriveDaysLevelAndCount> getDriveDaysLevel (String month , Long deptIdOri) throws Exception{

        List<DriveDaysLevelAndCount> listF = new ArrayList<>();

        //1。查询所有出勤的驾驶员的驾驶天数
        List<DeptAndIdCardAndCount> list = driveStatusService.findDriverDriveDaysInMonth(month.replace("-",""), deptIdOri);

        //2。按照驾驶天数划分级别
        Map<String, DriveDaysLevelAndCount> map = new HashMap<>();
        Map<String , Integer> totalDriverCountMap = new HashMap<>();
        list.forEach(item -> {
            String deptName = item.getDeptName();
            long deptId = item.getDeptId();
            if(map.get(deptName) == null){
                DriveDaysLevelAndCount dd = new DriveDaysLevelAndCount();
                map.put(deptName , dd);
            }
            DriveDaysLevelAndCount dd = map.get(deptName);
            dd.setDeptName(deptName);
            int count = item.getCount();
            if(count > 0 && count <= 10){
                dd.setLevel2Count(dd.getLevel2Count() + 1);
            }else if(count > 10 && count <= 20){
                dd.setLevel3Count(dd.getLevel3Count() + 1);
            }else {
                dd.setLevel4Count(dd.getLevel4Count() + 1);
            }

            //查询驾驶员总数量
            Integer totalDriverCount = totalDriverCountMap.get(deptName);
            if(totalDriverCount == null){
                try {
                    int tdc = driverService.findDriverCount(deptId);
                    totalDriverCountMap.put(deptName , tdc);
                } catch (Exception e) {
                    throw new RuntimeException("查询企业总驾驶员数量失败");
                }
            }
        });

        map.forEach((k , v) -> {
            //计算没有出勤的驾驶员数量
            int totalDriverCount = totalDriverCountMap.get(k);
            int otherDriverCout = map.get(k).getLevel2Count() + map.get(k).getLevel3Count() + map.get(k).getLevel4Count();
            int zeroDriverCount = totalDriverCount - otherDriverCout;
            map.get(k).setLevel1Count(zeroDriverCount);
            listF.add(v);
        });

        if(list == null || list.size() < 1){
            DriveDaysLevelAndCount dda = new DriveDaysLevelAndCount();
            BladeDept dept = deptService.getById(deptIdOri);
            String deptName = "未知";
            if(dept != null){
                deptName = dept.getDeptName();
            }
            dda.setDeptName(deptName);
            dda.setLevel1Count(0);
            dda.setLevel2Count(0);
            dda.setLevel3Count(0);
            dda.setLevel4Count(0);
            listF.add(dda);
        }

        return listF;
    }


    /**
     * @description: 驾驶员驾驶里程分析
     * 按照企业和日期综合分页
     * @author: zhouxw
     * @date: 2022/11/20 9:57 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.core.metadata.IPage<com.xh.vdm.statistic.vo.response.DriverMileageStatResponse>>
     **/
    @PostMapping("/driverDriveMileageStat")
    public R<IPage<DriverMileageStatResponse>> driverDriveMileageStat(@RequestBody @Validated DeptAndDurationRequest request, BladeUser user){
        try{

            if(request.getCount() == null){
                request.setCount(10);
            }
            if(request.getStart() == null ){
                request.setStart(0);
            }
            //1.根据日期判断是否跨月，如果跨月，需要查询多次
            //如果开始时间为空，则查询当月开始
            if(request.getStartSecondTimestamp() == null || request.getStartSecondTimestamp() <= 0){
                String monthStr = sdfHolder.get().format(new Date());
                monthStr = monthStr.substring(0,7)+"-01 00:00:00";
                long secondTimestamp = (sdfHolder.get().parse(monthStr).getTime()) / 1000;
                request.setStartSecondTimestamp(secondTimestamp);
            }

            //如果结束时间为空，则查询截止到前一天
            if(request.getEndSecondTimestamp() == null || request.getEndSecondTimestamp() <= 0){
                long timestamp = new Date().getTime() - 24*3600*1000;
                Date date = new Date();
                date.setTime(timestamp);
                String dayStr = sdfHolder.get().format(date);
                dayStr = dayStr.substring(0,10)+" 23:59:59";
                long secondTimestamp = sdfHolder.get().parse(dayStr).getTime() / 1000;
                request.setEndSecondTimestamp(secondTimestamp);
            }

            IPage pageF = new Page();
            //查询要查询的时间段天数
            int days = DateUtil.getDayCountBetweenSecondTimestamp(request.getStartSecondTimestamp() , request.getEndSecondTimestamp()) + 1;
            //查询企业总数
			//租户id
			if(user == null){
				log.info("用户未登录或鉴权失败");
				return R.fail("用户未登录或鉴权失败");
			}
			String tenantId = user.getTenantId();
            LambdaQueryWrapper<BladeDept> wrapper = Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId , tenantId).eq(BladeDept::getIsDeleted ,0);
            if(request.getDeptId() != null){
                wrapper.eq(BladeDept::getId , request.getDeptId());
            }
            wrapper.orderByAsc(BladeDept::getId);
            long deptCount = deptService.count(wrapper);
            List<BladeDept> deptList = deptService.list(wrapper);
            long totalCount = days * deptCount;
            pageF.setTotal(request.getDeptId()==null?totalCount:days);

            List<DriverMileageStatResponse> listAll = new ArrayList<>();
            //说明：分页以企业为第一维度，日期为第二维度。每个企业对应的日期时间段相同，也就是说每个企业对应的天数相同；以企业分组，每组里边有多个天。

            //根据页码获取要查询的范围
            int start = request.getStart();
            int end = request.getStart() + request.getCount();
            if(end > pageF.getTotal()){
                end = (int)pageF.getTotal();
            }

            int deptListStartIndex = (start-1) / days;
            int daysStartOffset = start % days;
            int deptListEndIndex = (end-1) / days;
            int daysEndOffset = (end-1) % days;

            if(deptListStartIndex == deptListEndIndex){
                //如果开始点和结束点是相同的企业
                long startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartSecondTimestamp()) + daysStartOffset * 24 * 3600;
                long endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getStartSecondTimestamp()) + daysEndOffset * 24 * 3600;
                request.setStartSecondTimestamp(startTimestamp);
                request.setEndSecondTimestamp(endTimestamp);
                request.setDeptId(deptList.get(deptListStartIndex).getId());
                List<DriverMileageStatResponse> onceList = getDriverMileageStatForOnce(request , DateUtil.getDayFirstSecondTimestamp(request.getStartSecondTimestamp()) ,DateUtil.getDayLastSecondTimestamp(request.getEndSecondTimestamp()) );
                listAll.addAll(onceList);
            }else{
                //如果开始点和结束点是不同的企业
                int diff = deptListEndIndex - deptListStartIndex;
                long startTimestamp = 0;
                long endTimestamp = 0;
                long deptId = 0;
                for(int i = 0 ; i <= diff ; i++){
                    if(i == 0){
                        //如果是第一个企业
                        startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartSecondTimestamp()) + daysStartOffset * 24 * 3600;
                        endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getEndSecondTimestamp());
                        deptId = deptList.get(deptListStartIndex).getId();
                    }else if (i == diff){
                        //如果是最后一个企业
                        startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartSecondTimestamp());
                        endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getStartSecondTimestamp()) + daysEndOffset * 24 * 3600;
                        deptId = deptList.get(deptListEndIndex).getId();
                    }else{
                        //如果是中间企业
                        startTimestamp = DateUtil.getDayFirstSecondTimestamp(request.getStartSecondTimestamp());
                        endTimestamp = DateUtil.getDayLastSecondTimestamp(request.getEndSecondTimestamp());
                        deptId = deptList.get(deptListStartIndex + i).getId();
                    }
                    DeptAndDurationRequest req = new DeptAndDurationRequest();
                    req.setStart(request.getStart());
                    req.setCount(request.getCount());
                    req.setDeptId(deptId);
                    req.setStartSecondTimestamp(startTimestamp);
                    req.setEndSecondTimestamp(endTimestamp);
                    List<DriverMileageStatResponse> onceList = getDriverMileageStatForOnce(req , startTimestamp ,endTimestamp );
                    listAll.addAll(onceList);
                }
            }

            //对结果进行排序
            Collections.sort(listAll,(o1 , o2) -> {
                if(o1.getDeptId().longValue() != o2.getDeptId().longValue()){
                    return Math.toIntExact(o1.getDeptId() - o2.getDeptId());
                }else{
                    int diff = 0;
                    try {
                        diff = (int) (sdfHolderShort.get().parse(o1.getStatDate()).getTime() - sdfHolderShort.get().parse(o2.getStatDate()).getTime());
                    }catch (Exception e){
                        log.info("类型转换报错",e);
                    }
                    return diff;
                }
            });

            //字段值为空，设置为0
            BeanUtil.ifNullSetZero(listAll);

            pageF.setRecords(listAll);
            return R.data(pageF);
        }catch (Exception e){
            log.error("统计驾驶员驾驶天数分布失败",e);
            return R.fail("统计驾驶员驾驶天数分布失败");
        }
    }

    /**
     * @description: 驾驶员驾驶里程分析结果导出
     * @author: zhouxw
     * @date: 2022/12/1 4:08 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<java.lang.String>
     **/
    @PostMapping("/exportDriverDriveMileageStat")
    public R<String> exportDriverDriveMileageStat(@RequestBody @Validated DeptAndDurationRequest request){
        String filePath = "";
        try{

            request.setStart(0);
            request.setCount(Integer.MAX_VALUE);
            List<DriverMileageStatResponse> list = getDriverDriveMileageForExport(request);

            String title = "驾驶员驾驶里程分析";
            filePath = EasyExcelUtils.export(staticFilePath , EasyExcelUtils.DEFAULT_PROXY_PATH , title , list , DriverMileageStatResponse.class, false );

            return R.data(filePath);
        }catch (Exception e){
            log.error("统计驾驶员驾驶里程分析失败",e);
            return R.fail("统计驾驶员驾驶里程分析失败");
        }
    }

    private List<DriverMileageStatResponse> getDriverDriveMileageForExport(DeptAndDurationRequest request) throws Exception {
        List<Long> deptIds = new ArrayList<>();
        List<DriverMileageStatResponse> listF = new ArrayList<>();

        //如果不指定时间段，则默认为本月初到前一天
        if(request.getStartSecondTimestamp() == null){
            request.setStartSecondTimestamp(DateUtil.getMonthFirstSecondTimestamp(new Date().getTime()/1000));
        }
        if(request.getEndSecondTimestamp() == null){
            request.setEndSecondTimestamp(DateUtil.getDayFirstSecondTimestamp(new Date().getTime()/1000 - 1));
        }

        long finalStartTime = request.getStartSecondTimestamp();
        long finalEndTime = request.getEndSecondTimestamp();

		//租户id
		BladeUser user = AuthUtil.getUser();
		if(user == null){
			log.info("用户未登录或授权失败");
			throw new Exception("用户未登录或授权失败");
		}
		String tenantId = user.getTenantId();
        //如果指定企业
        if(request.getDeptId() != null){
            List onceList = getDriverMileageStatForOnce(request , finalStartTime ,finalEndTime );
            listF.addAll(onceList);
        }else {
            //如果不指定企业，则查询所有企业
            //查询企业列表
            List<BladeDept> depts = deptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId, tenantId ).eq(BladeDept::getIsDeleted ,0));
            for(BladeDept dept : depts){
                deptIds.add(dept.getId());
            }

            //对每个企业都执行查询
            for(long deptId : deptIds){
                request.setDeptId(deptId);
                List<DriverMileageStatResponse> listO = getDriverMileageStatForOnce(request, finalStartTime ,finalEndTime);
                listF.addAll(listO);
            }

        }
        return listF;
    }

    private List<DriverMileageStatResponse> getDriverDriveMileage(DeptAndDurationRequest request) throws Exception {
        List<Long> deptIds = new ArrayList<>();
        List<DriverMileageStatResponse> listF = new ArrayList<>();

        //如果不指定时间段，则默认为本月初到前一天
        if(request.getStartSecondTimestamp() == null){
            request.setStartSecondTimestamp(DateUtil.getMonthFirstSecondTimestamp(new Date().getTime()/1000));
        }
        if(request.getEndSecondTimestamp() == null){
            request.setEndSecondTimestamp(DateUtil.getDayFirstSecondTimestamp(new Date().getTime()/1000 - 1));
        }

        long finalStartTime = request.getStartSecondTimestamp();
        long finalEndTime = request.getEndSecondTimestamp();

		//租户id
		BladeUser user = AuthUtil.getUser();
		if(user == null){
			log.info("用户未登录或授权失败");
			throw new Exception("用户未登录或授权失败");
		}
		String tenantId = user.getTenantId();

        //如果指定企业
        if(request.getDeptId() != null){
            List onceList = getDriverMileageStatForOnce(request , finalStartTime ,finalEndTime );
            listF.addAll(onceList);
        }else {
            //如果不指定企业，则查询所有企业
            //查询企业列表
            //分页查询企业信息
            Page page = new Page();
            page.setSize(request.getCount());
            page.setCurrent(request.getStart()/request.getCount()+(request.getStart() % request.getCount()>0?1:0));

            IPage<BladeDept> depts = deptService.page(page , Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId , tenantId).eq(BladeDept::getIsDeleted ,0));
            for(BladeDept dept : depts.getRecords()){
                deptIds.add(dept.getId());
            }

            //对每个企业都执行查询
            for(long deptId : deptIds){
                request.setDeptId(deptId);
                List<DriverMileageStatResponse> listO = getDriverMileageStatForOnce(request, finalStartTime ,finalEndTime);
                listF.addAll(listO);
            }

        }
        return listF;
    }


    /**
     * @description: 执行一次查询
     * 判断是否跨月
     * @author: zhouxw
     * @date: 2022/11/20 11:41 PM
     * @param:
     * @return:
     **/
    private List<DriverMileageStatResponse> getDriverMileageStatForOnce(DeptAndDurationRequest request,long finalStartTime ,long finalEndTime) throws Exception{
        List<DriverMileageStatResponse> listF = new ArrayList<>();
        //判断开始时间的月份和结束时间的月份是否跨月，并执行数据统计
        String monthStart = sdfHolder.get().format(request.getStartSecondTimestamp() * 1000).substring(0 , 7);
        String monthEnd = sdfHolder.get().format(request.getEndSecondTimestamp() * 1000).substring(0 , 7);
        if(!monthStart.equals(monthEnd)){
            //如果跨月了，就需要查询多次
            int monthCount = DateUtil.monthCountBetweenSecondTimestamp(request.getStartSecondTimestamp() , request.getEndSecondTimestamp());
            for(int i = 0 ; i < monthCount ; i++){
                List<VehicleTravelDurationNode> list = new ArrayList<>();
                String month = "";
                long startTime = 0;
                long endTime = 0;
                if(i == 0){
                    //如果是首月
                    //查询从开始时间到当月月底
                    long startSecondTimestamp = finalStartTime;
                    long endSecondTimestamp = DateUtil.getMonthLastSecondTimestamp(startSecondTimestamp);
                    month = monthStart.replace("-" , "");
                    startTime = startSecondTimestamp;
                    endTime = endSecondTimestamp;
                }else if(i == monthCount - 1){
                    //如果是尾月
                    //查询从当月月初到结束时间
                    month = monthEnd.replace("-" , "");
                    startTime = DateUtil.getMonthFirstSecondTimestamp(finalEndTime);
                    endTime = finalEndTime;
                }else{
                    //如果是其他月份
                    //查询从当月月初到月尾
                    Calendar cal = Calendar.getInstance();
                    cal.set(Calendar.YEAR , Integer.parseInt(monthStart.split("-")[0]));
                    cal.set(Calendar.MONTH, Integer.parseInt(monthStart.split("-")[1])-1);
                    cal.add(Calendar.MONTH , i);
                    int monthIndex = cal.get(Calendar.MONTH)+1 ;
                    month = cal.get(Calendar.YEAR) + "" + (monthIndex < 10 ? "0"+month : month);
                    startTime = DateUtil.getMonthFirstSecondTimestamp(cal.getTimeInMillis() / 1000);
                    endTime = DateUtil.getMonthLastSecondTimestamp(cal.getTimeInMillis() / 1000);
                }
                List<DriverMileageStatResponse> listO = getDriverMileageStatResponseForDept(request.getDeptId() ,month , startTime ,endTime );
                listF.addAll(listO);
            }

        }else{
            //如果没有跨月，只需要查询一次
            String month = monthStart.replace("-" , "");
            List<DriverMileageStatResponse> listO = getDriverMileageStatResponseForDept(request.getDeptId() ,month , finalStartTime ,finalEndTime );
            listF.addAll(listO);
        }

        return listF;
    }



    /**
     * @description: 查询一个企业的驾驶里程分析
     * @author: zhouxw
     * @date: 2022/11/20 11:23 PM
     * @param: [deptIdOri, month, startSecondTimestamp, endSecondTimestamp]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.DriverMileageStatResponse>
     **/
    private List<DriverMileageStatResponse> getDriverMileageStatResponseForDept(Long deptIdOri ,String month ,  Long startSecondTimestamp , Long endSecondTimestamp) throws Exception{
        List<DriverMileageStatResponse> listF = new ArrayList<>();

        //1。查询所有出勤的驾驶员的驾驶里程信息
        Date startD = new Date();
        startD.setTime(startSecondTimestamp * 1000);
        Date endD = new Date();
        endD.setTime(endSecondTimestamp * 1000);
        String startDate = sdfHolder.get().format(startD).substring(0,10);
        String endDate = sdfHolder.get().format(endD).substring(0,10);
        List<DeptAndDriveStatus> list = driveStatusService.findDriverDriveMileageEveryDay(deptIdOri , month, startDate , endDate);
        Set<String> daySet = new HashSet<>();
        list.stream().forEach(item -> {
            daySet.add(item.getStatDate());
        });

        //2。按照驾驶里程划分级别
        Map<String, DriverMileageStatResponse> map = new HashMap<>();
        Map<String , Double> totalMileageMap = new HashMap<>();
        Map<String , Integer> totalVehicleCountMap = new HashMap<>();
        Map<String, Double> averageMileageMap = new HashMap<>();
        list.forEach(item -> {
            String statDate = item.getStatDate();
            if(map.get(statDate) == null){
                DriverMileageStatResponse dd = new DriverMileageStatResponse();
                map.put(statDate , dd);
            }
            DriverMileageStatResponse dd = map.get(statDate);
            dd.setDeptId(item.getDeptId());
            dd.setDeptName(item.getDeptName());
            dd.setStatDate(statDate);
            double mileage = item.getDriveMileage();
            if(mileage > 0 && mileage <= 100){
                dd.setLevel1Count((dd.getLevel1Count()==null?0:dd.getLevel1Count()) + 1);
            }else if(mileage > 100 && mileage <= 300){
                dd.setLevel2Count((dd.getLevel2Count()==null?0:dd.getLevel2Count()) + 1);
            }else if(mileage > 300 && mileage <= 500) {
                dd.setLevel3Count((dd.getLevel3Count()==null?0:dd.getLevel3Count()) + 1);
            }else if(mileage > 500 && mileage <= 700){
                dd.setLevel4Count((dd.getLevel4Count()==null?0:dd.getLevel4Count()) + 1);
            }else if(mileage > 700 && mileage <= 1000){
                dd.setLevel5Count((dd.getLevel5Count()==null?0:dd.getLevel5Count()) + 1);
            }else{
                dd.setLevel6Count((dd.getLevel6Count()==null?0:dd.getLevel6Count()) + 1);
            }

        });

        //查询企业总里程
        List<DateAndData> tdc = driveStatusService.findCompanyTotalMileageInMonth(deptIdOri, month);
        //转为map
        tdc.forEach(it -> {
            String statDate = it.getStatDate();
            totalMileageMap.put(statDate , MathUtil.roundDouble(it.getData() / 1000 , 2));
        });


        //查询企业总车辆数
        List<DateAndDeptAndCount> goOnlineDeptList = vehicleStatService.findDeptVehicleCountByDay(deptIdOri , startSecondTimestamp , endSecondTimestamp , null);
        goOnlineDeptList.forEach(item -> {
            String statDate = item.getStatDate();
            totalVehicleCountMap.put(statDate , item.getCount());
        });

        //查询企业总驾驶员数量
        Map<Long,Integer> deptDriverCountMap = new HashMap<>();
        int driverCount = driverService.findDriverCount(deptIdOri);

        //平均驾驶里程
        map.forEach((k , v) -> {
            Long deptId = v.getDeptId();
            v.setTotalMileage(totalMileageMap.get(k));
            v.setTotalVehicles(totalVehicleCountMap.get(k));
            if(deptDriverCountMap.get(deptId) == null || deptDriverCountMap.get(deptId) == 0){
                v.setAverageDriveMileage(0D);
            }else{
                v.setAverageDriveMileage(MathUtil.divideRoundDouble(totalMileageMap.get(k)==null?0:totalMileageMap.get(k) , deptDriverCountMap.get(deptId) , 2));
            }

            listF.add(v);
        });


        //补充数据：对于没有驾驶员出勤的天，初始化处理
        int days = DateUtil.getDayCountBetweenSecondTimestamp(startSecondTimestamp , endSecondTimestamp);
        long timestamp = DateUtil.getDayFirstSecondTimestamp(startSecondTimestamp);
        String deptName = "";
        if((list == null || list.size() < 1) && StringUtils.isBlank(deptName)){
            BladeDept dept = deptService.getById(deptIdOri);
            if(dept != null){
                deptName = dept.getDeptName();
            }
        }else{
            deptName = list.get(0).getDeptName();
        }
        for(int i = 0 ; i <= days; i++){
            String date = DateUtil.getDateString(timestamp);
            if(!daySet.contains(date)){
                DriverMileageStatResponse dsr = new DriverMileageStatResponse();
                dsr.setTotalMileage(0D);
                dsr.setDeptId(deptIdOri);
                dsr.setDeptName(deptName);
                dsr.setStatDate(date);
                dsr.setAverageDriveMileage(0D);
                dsr.setTotalVehicles(totalVehicleCountMap.get(date)==null?0:totalVehicleCountMap.get(date));
                dsr.setLevel1Count(driverCount);
                dsr.setLevel2Count(0);
                dsr.setLevel3Count(0);
                dsr.setLevel4Count(0);
                dsr.setLevel5Count(0);
                dsr.setLevel6Count(0);
                listF.add(dsr);
                daySet.add(date);
            }
            timestamp += 24*3600;
        }

        //数据值转换，如果为空，设置为0
        listF.forEach(item -> {
            if(item.getLevel1Count() == null){
                item.setLevel1Count(0);
            }
            if(item.getLevel2Count() == null){
                item.setLevel2Count(0);
            }
            if(item.getLevel3Count() == null){
                item.setLevel3Count(0);
            }
            if(item.getLevel4Count() == null){
                item.setLevel4Count(0);
            }
            if(item.getLevel5Count() == null){
                item.setLevel5Count(0);
            }
            if(item.getLevel6Count() == null){
                item.setLevel6Count(0);
            }
        });

        return listF;
    }


    /**
     * @description: 统计驾驶员驾驶时长趋势
     * @author: zhouxw
     * @date: 2022/11/20 11:46 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<java.util.List<com.xh.vdm.statistic.entity.DateAndData>>
     **/
    @PostMapping("/statAverageDurationTrend")
    public R<List<DateAndData>> statAverageDurationTrend(@RequestBody @Validated DeptAndDurationRequest request){


        try{
            //如果不指定时间段，则默认为本月初到前一天
            if(request.getStartSecondTimestamp() == null){
                request.setStartSecondTimestamp(DateUtil.getMonthFirstSecondTimestamp(new Date().getTime()/1000));
            }
            if(request.getEndSecondTimestamp() == null){
                request.setEndSecondTimestamp(DateUtil.getDayFirstSecondTimestamp(new Date().getTime()/1000 - 1));
            }


            List<DeptAndDateAndData> listF = new ArrayList<>();
            List<DateAndCount> listDCAll = new ArrayList<>();
            //判断开始时间的月份和结束时间的月份是否跨月，并执行数据统计
            String monthStart = sdfHolder.get().format(request.getStartSecondTimestamp() * 1000).substring(0 , 7);
            String monthEnd = sdfHolder.get().format(request.getEndSecondTimestamp() * 1000).substring(0 , 7);

            long finalStartTime = request.getStartSecondTimestamp();
            long finalEndTime = request.getEndSecondTimestamp();

            String finalStartDate = sdfHolderShort.get().format(request.getStartSecondTimestamp()*1000);
            String finalEndDate = sdfHolderShort.get().format(request.getEndSecondTimestamp()*1000);

            if(!monthStart.equals(monthEnd)){
                //如果跨月了，就需要查询多次
                int monthCount = DateUtil.monthCountBetweenSecondTimestamp(request.getStartSecondTimestamp() , request.getEndSecondTimestamp());
                for(int i = 0 ; i < monthCount ; i++){
                    List<VehicleTravelDurationNode> list = new ArrayList<>();
                    String month = "";
                    String startDate = "";
                    String endDate = "";
                    if(i == 0){
                        //如果是首月
                        //查询从开始时间到当月月底
                        long endTImestamp = DateUtil.getMonthLastSecondTimestamp(finalStartTime);
                        month = monthStart.replace("-" , "");
                        startDate = finalStartDate;
                        Date date = new Date();
                        date.setTime(endTImestamp);
                        endDate = sdfHolderShort.get().format(date);
                    }else if(i == monthCount - 1){
                        //如果是尾月
                        //查询从当月月初到结束时间
                        month = monthEnd.replace("-" , "");
                        long startTime = DateUtil.getMonthFirstSecondTimestamp(finalEndTime);
                        Date date = new Date();
                        date.setTime(startTime);
                        startDate = sdfHolderShort.get().format(date);
                        endDate = finalEndDate;
                    }else{
                        //如果是其他月份
                        //查询从当月月初到月尾
                        Calendar cal = Calendar.getInstance();
                        cal.set(Calendar.YEAR , Integer.parseInt(monthStart.split("-")[0]));
                        cal.set(Calendar.MONTH, Integer.parseInt(monthStart.split("-")[1])-1);
                        cal.add(Calendar.MONTH , i);
                        int monthIndex = cal.get(Calendar.MONTH)+1 ;
                        month = cal.get(Calendar.YEAR) + "" + (monthIndex < 10 ? "0"+month : month);
                        long startTime = DateUtil.getMonthFirstSecondTimestamp(cal.getTimeInMillis() / 1000);
                        long endTime = DateUtil.getMonthLastSecondTimestamp(cal.getTimeInMillis() / 1000);
                        startDate = sdfHolderShort.get().format(startTime);
                        endDate = sdfHolderShort.get().format(endTime);
                    }
                    List<DeptAndDateAndData> listO = driveStatusService.findTotalDurationEveryDay(request.getDeptId()  , startDate ,endDate );
                    listF.addAll(listO);

                    //查询每天的驾驶员总数
                    //List<DateAndCount> listDC = driveStatusService.findDriverCountEveryDay(request.getDeptId() , month , startDate , endDate);
                    //listDCAll.addAll(listDC);
                }

            }else{
                //如果没有跨月，只需要查询一次
                String month = monthStart.replace("-" , "");
                List<DeptAndDateAndData> listO = driveStatusService.findTotalDurationEveryDay(request.getDeptId()  , finalStartDate ,finalEndDate );
                listF.addAll(listO);

                //查询每天的驾驶员总数
                //List<DateAndCount> listDC = driveStatusService.findDriverCountEveryDay(request.getDeptId() , month ,  finalStartDate ,finalEndDate);
                //listDCAll.addAll(listDC);

            }

            //查询驾驶员总数
            int driverCount = driverService.findDriverCount(request.getDeptId());

            //统计日平均驾驶时长
            List<DateAndData> resList = new ArrayList<>();
            //转换为map
            Map<String,Double> ddMap = new HashMap<>();
            //Map<String,Integer> dcMap = new HashMap<>();
            listF.forEach(item -> {
                ddMap.put(item.getStatDate() , item.getData());
            });
            //对于不存在数据的，添加默认0
            int durationDays = DateUtil.getDayCountBetweenSecondTimestamp(finalStartTime , finalEndTime);
            Date dateDay = sdfHolderShort.get().parse(DateUtil.getDateString(finalStartTime));
            Calendar cal = Calendar.getInstance();
            cal.setTime(dateDay);
            for(int i = 0 ; i < durationDays ; i++){
                String dateStr = DateUtil.getDateString(cal.getTimeInMillis() / 1000);
                if(!ddMap.containsKey(dateStr)){
                    ddMap.put(dateStr , 0D);
                }
                cal.add(Calendar.DAY_OF_MONTH , 1);
            }
            /*listDCAll.forEach(item -> {
                dcMap.put(item.getStatDate() , item.getCount());
            });*/
            for(String date : ddMap.keySet()){
                DateAndData dd = new DateAndData();
                dd.setStatDate(date);
                if(driverCount == 0){
                    dd.setData(0);
                }else {
                    dd.setData(MathUtil.roundDouble(ddMap.get(date) / 3600 / driverCount, 2));
                }
                resList.add(dd);
            }

            //按时间排序
            Collections.sort(resList , Comparator.comparingInt(o -> Integer.parseInt(o.getStatDate().replace("-", ""))));
            return R.data(resList);

        }catch (Exception e){
            log.error("统计驾驶员驾驶时长趋势失败",e);
            return R.fail("统计驾驶员驾驶时长趋势失败");
        }

    }



}
