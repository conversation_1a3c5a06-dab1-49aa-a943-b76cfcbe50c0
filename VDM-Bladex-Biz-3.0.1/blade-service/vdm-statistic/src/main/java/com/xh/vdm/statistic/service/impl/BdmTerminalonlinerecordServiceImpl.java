package com.xh.vdm.statistic.service.impl;

import com.xh.vdm.statistic.entity.BdmTerminalonlinerecord;
import com.xh.vdm.statistic.mapper.BdmTerminalonlinerecordMapper;
import com.xh.vdm.statistic.service.IBdmTerminalonlinerecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.utils.DateUtil;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 车辆上下线记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 */
@Service
public class BdmTerminalonlinerecordServiceImpl extends ServiceImpl<BdmTerminalonlinerecordMapper, BdmTerminalonlinerecord> implements IBdmTerminalonlinerecordService {

	@Override
	public List<BdmTerminalonlinerecord> findOnlineRecord(long startTime, long endTime) throws Exception {
		String month = DateUtil.getDateString(startTime).replace("-","").substring(0,6);
		return baseMapper.getOnlineRecordInDuration(startTime, endTime, month);
	}
}
