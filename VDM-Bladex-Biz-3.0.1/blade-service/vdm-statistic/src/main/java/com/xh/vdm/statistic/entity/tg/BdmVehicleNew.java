package com.xh.vdm.statistic.entity.tg;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *	车辆实体
 * </p>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("bdm_vehicle_new")
public class BdmVehicleNew implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 车牌编号
     */
    private String number;
	/**
	 * 车辆类型
	 */
	private Integer category;
	/**
	 * 目标类别
	 */
	private Integer targetType;
	/**
	 * 所属机构
	 */
	private Long deptId;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 删除标记：0-未删除，1-已删除
     */
    private Integer deleted;

}
