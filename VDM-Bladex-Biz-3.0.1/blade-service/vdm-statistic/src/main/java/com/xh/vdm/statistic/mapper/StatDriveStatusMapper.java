package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.wx.TotalMileageAndDurationInMonth;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 驾驶员驾驶情况表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-16
 */
public interface StatDriveStatusMapper extends BaseMapper<StatDriveStatus> {

	/**
	 * @description: 获取人脸识别结果
	 * @author: zhouxw
	 * @date: 2022/11/16 11:49 PM
	 * @param: [licencePlate, driverName, date]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DriverFaceResultNode>
	 **/
	List<DriverFaceResultNode> getDriverFaceResult(@Param("date") Date date) throws Exception;


	/**
	 * @description: 获取人脸识别结果，指定驾驶员
	 * @author: zhouxw
	 * @date: 2022/11/16 11:49 PM
	 * @param: [licencePlate, driverName, date]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DriverFaceResultNode>
	 **/
	List<DriverFaceResultNode> getDriverFaceResultByDateAndIdCard(@Param("date") String date, @Param("idCard") String idCard) throws Exception;


	/**
	 * @description: 获取车辆最新的驾驶员信息
	 * @author: zhouxw
	 * @date: 2022/11/16 11:49 PM
	 * @param: [licencePlate, driverName, date]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DriverFaceResultNode>
	 **/
	DriverFaceResultNode getNewestDriverWithFaceByLicencePlate(@Param("licencePlate") String licencePlate) throws Exception;


	/**
	 * @description: 获取IC卡使用结果
	 * @author: zhouxw
	 * @date: 2022/11/16 11:49 PM
	 * @param: [licencePlate, driverName, date]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DriverFaceResultNode>
	 **/
	List<DriverICResultNode> getDriverICResult(@Param("date") Date date) throws Exception;


	/**
	 * @description: 获取IC卡使用结果，指定驾驶员
	 * @author: zhouxw
	 * @date: 2022/11/16 11:49 PM
	 * @param: [licencePlate, driverName, date]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DriverFaceResultNode>
	 **/
	List<DriverICResultNode> getDriverICResultByDateAndIdCard(@Param("date") String date, @Param("idCard") String idCard) throws Exception;

	/**
	 * 查询指定日期车辆的最后插拔卡记录
	 *
	 * @param date
	 * @param licencePlate
	 * @param licenceColor
	 * @return
	 * @throws Exception
	 */
	DriverICResultNode getLastICResultByDate(@Param("date") String date, @Param("licencePlate") String licencePlate, @Param("licenceColor") String licenceColor);

	/**
	 * @description: 批量存储驾驶状态数据
	 * @author: zhouxw
	 * @date: 2022/11/17 11:59 AM
	 * @param: [list]
	 * @return: void
	 **/
	void saveBatch(@Param("list") List<StatDriveStatus> list, @Param("month") String month) throws Exception;

	/**
	 * @description: 根据指定的日期删除统计数据
	 * @author: zhouxw
	 * @date: 2022/11/17 4:35 PM
	 * @param: [date]
	 * @return: void
	 **/
	void deleteDataByDate(@Param("month") String month, @Param("date") Date date) throws Exception;

	/**
	 * 删除跨天的后续执行的追加数据
	 *
	 * @param month
	 * @param dateList
	 */
	void deleteAppendData(@Param("month") String month, @Param("dateList") List<String> dateList);

	/**
	 * @description: 查询驾驶员在某个月的驾驶天数
	 * @author: zhouxw
	 * @date: 2022/11/19 3:59 PM
	 * @param: [month: yyyyMM, monthLike: yyyy-MM%, idCard]
	 * @return: int
	 **/
	int getDriveDaysByMonthAndIdCard(@Param("month") String month, @Param("monthLike") String monthLike, @Param("idCard") String idCard);

	/**
	 * @description: 查询驾驶员在某个月内的总驾驶时长
	 * @author: zhouxw
	 * @date: 2022/11/19 4:10 PM
	 * @param: [month, monthLike, idCard]
	 * @return: long
	 **/
	long getTotalDriveDurationByMonthAndIdCard(@Param("month") String month, @Param("monthLike") String monthLike, @Param("idCard") String idCard) throws Exception;

	/**
	 * @description: 查询驾驶员在某个月内的总驾驶里程
	 * @author: zhouxw
	 * @date: 2022/11/19 4:11 PM
	 * @param: [month, monthLike, idCard]
	 * @return: long
	 **/
	long getTotalDriveMileageByMonthAndIdCard(@Param("month") String month, @Param("monthLike") String monthLike, @Param("idCard") String idCard) throws Exception;

	/**
	 * @description: 查询驾驶时长基本信息列表
	 * @author: zhouxw
	 * @date: 2022/11/19 9:06 PM
	 * @param: [month, monthLike, idCard]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DriveDurationBaseInfo>
	 **/
	List<DriveDurationBaseInfo> getDriveStatusList(@Param("month") String month, @Param("monthLike") String monthLike, @Param("idCard") String idCard) throws Exception;

	/**
	 * 查询驾驶员在月份中每天的里程和驾驶时长，指定驾驶员，指定月份
	 *
	 * @param month  yyyyMM
	 * @param idCard
	 * @return
	 */
	List<DateAndMileageAndDuration> getMileageAndDurationEveryDayInMonth(@Param("month") String month, @Param("idCard") String idCard);

	/**
	 * @description: 统计驾驶员某个月的驾驶天数
	 * * 如果不指定 deptId，则查询全区域
	 * @author: zhouxw
	 * @date: 2022/11/20 5:27 PM
	 * @param: [month: yyyyMM, deptId]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DeptAndIdCardAndCount>
	 **/
	List<DeptAndIdCardAndCount> getDriverDriveDaysInMonth(String month, Long deptId);

	/**
	 * @description: 查询驾驶员驾驶总天数
	 * 如果不指定企业deptId，则查询全区域
	 * @author: zhouxw
	 * @date: 2022/11/28 1:35 AM
	 * @param: [month, deptId]
	 * @return: int
	 **/
	int getTotalDriveDaysInMonth(@Param("month") String month, @Param("deptId") Long deptId);


	/**
	 * @description: 统计驾驶员某个月的驾驶天数
	 * * 如果不指定 deptId，则查询全区域
	 * @author: zhouxw
	 * @date: 2022/11/20 5:27 PM
	 * @param: [month: yyyyMM, deptId, startDate:yyyy-MM-dd , endDate: yyyy-MM-dd]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DeptAndIdCardAndCount>
	 **/
	List<DeptAndIdCardAndCount> getDriverDriveDaysInMonthDuration(String month, Long deptId, String startDate, String endDate);

	/**
	 * 查询驾驶员在指定月的驾驶天数
	 *
	 * @param idCard
	 * @param month  yyyyMM
	 * @return
	 */
	int getDriveDaysCountInMonthByIdCard(@Param("idCard") String idCard, @Param("month") String month);

	/**
	 * @description: 根据企业和时间段查询 驾驶员每天的驾驶里程
	 * * 如果不指定 deptId，则查询全区域
	 * @author: zhouxw
	 * @date: 2022/11/20 5:47 PM
	 * @param: [deptId, month: yyyyMM, startDate: yyyy-MM-dd, endDate: yyyy-MM-dd]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DeptAndDriveStatus>
	 **/
	List<DeptAndDriveStatus> getDeptDriveMileageCount(Long deptId, String month, String startDate, String endDate) throws Exception;

	/**
	 * @description: 查询某个企业 在指定时间段内 每天的 驾驶时长总数
	 * * 如果不指定 deptId，则查询全区域
	 * @author: zhouxw
	 * @date: 2022/11/20 5:57 PM
	 * @param: [deptId, month, startDate, endDate]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndData>
	 **/
	List<DeptAndDateAndData> getTotalDurationForDeptEveryDay(@Param("deptId") Long deptId, @Param("month") String month, @Param("startDate") String startDate, @Param("endDate") String endDate) throws Exception;

	/**
	 * @description: 查询指定企业在指定月份内的总里程
	 * @author: zhouxw
	 * @date: 2022/11/20 10:40 PM
	 * @param: [deptId, month]
	 * @return: long
	 **/
	List<DateAndData> getTotalMileageInMonth(@Param("deptId") Long deptId, @Param("month") String month) throws Exception;

	/**
	 * @description: 查询指定月份的总里程（全部，不按企业分组）
	 * @author: zhouxw
	 * @date: 2022/11/28 1:44 AM
	 * @param: [deptId, month]
	 * @return: double
	 **/
	double getTotalMileageInMonthAll(@Param("deptId") Long deptId, @Param("month") String month) throws Exception;

	/**
	 * @description: 统计每天驾驶员的数量
	 * @author: zhouxw
	 * @date: 2022/11/21 12:25 AM
	 * @param: [deptId, month, startDate, endDate]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
	 **/
	List<DateAndCount> getDriverCountEveryDay(@Param("deptId") Long deptId, @Param("month") String month, @Param("startDate") String startDate, @Param("endDate") String endDate) throws Exception;

	/**
	 * @description: 根据日期统计出勤驾驶员数量
	 * 如果不指定日期 date，则查询全月
	 * 如果不指定企业 deptId， 则查询全区域
	 * @author: zhouxw
	 * @date: 2022/11/27 12:44 AM
	 * @param: [month: yyyyMM, date: yyyy-MM-dd]
	 * @return: int
	 **/
	int getDriverCountByDate(@Param("month") String month, @Param("date") String date, @Param("deptId") Long deptId) throws Exception;

	/**
	 * @description: 根据日期查询企业总驾驶时长
	 * 如果不指定日期 date ，则查询全月
	 * 如果不指定企业 deptId ，则查询全区域
	 * @author: zhouxw
	 * @date: 2022/11/27 1:26 AM
	 * @param: [month: yyyyMM, date: yyyy-MM-dd, deptId]
	 * @return: double
	 **/
	double getTotalDriveDurationByDate(@Param("month") String month, @Param("date") String date, @Param("deptId") Long deptId) throws Exception;

	/**
	 * @description: 根据日期查询企业驾驶员总驾驶里程
	 * @author: zhouxw
	 * @date: 2022/11/27 1:35 AM
	 * @param: [month: yyyyMM, date: yyyy-MM-dd, deptId]
	 * @return: double
	 **/
	double getTotalDriveMileageByDate(@Param("month") String month, @Param("date") String date, @Param("deptId") Long deptId) throws Exception;

	/**
	 * 根据身份证号查询驾驶信息
	 * @param idCard
	 * @param month
	 * @return
	 */
	List<StatDriveStatus> getDriveStatusByIdCard(@Param("idCard") String idCard, @Param("statDate") String statDate, @Param("month") String month);

	/**
	 * 根据身份证号查询驾驶信息，指定月份
	 * @param idCard
	 * @param month
	 * @return
	 */
	TotalMileageAndDurationInMonth getDriveStatusByIdCardMonth(@Param("idCard") String idCard,  @Param("month") String month);
}
