package com.xh.vdm.statistic.entity.report;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 报警处理选项表（作用于服务商或第三方）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmDealAlarmOption implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 选项ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 单位ID
     */
    private Long deptId;

    /**
     * 报警类型（类型值与名称的映射关系，详见blade_dict_biz表code=alarm_type的记录）
     */
    private String alarmType;

    /**
     * 报警等级（等级值与名称的映射关系，详见blade_dict_biz表code=alarm_level的记录）
     */
    private String alarmLevel;

    /**
     * 是否生成日报（0：否，1：是）
     */
    private Integer dailyReport;

    /**
     * 是否生成月报（0：否，1：是）
     */
    private Integer monthlyReport;

    /**
     * 服务商是否自动处理（0：否，1：是）
     */
    private Integer serverAutoDeal;

    /**
     * 服务商自动处理：是否TTS播报（0：否，1：是）
     */
    private Integer serverTtsAudio;

    /**
     * 服务商自动处理：是否终端屏幕展示（0：否，1：是）
     */
    private Integer serverTerminalScreen;

    /**
     * 服务商自动处理：是否紧急（0：否，1：是）
     */
    private Integer serverUrgent;

    /**
     * 服务商自动处理：内容
     */
    private String serverContent;

    /**
     * 第三方是否自动处理（0：否，1：是）
     */
    private Integer thirdAutoDeal;

    /**
     * 第三方自动处理：是否TTS播报（0：否，1：是）
     */
    private Integer thirdTtsAudio;

    /**
     * 第三方自动处理：是否终端屏幕展示（0：否，1：是）
     */
    private Integer thirdTerminalScreen;

    /**
     * 第三方自动处理：是否紧急（0：否，1：是）
     */
    private Integer thirdUrgent;

    /**
     * 第三方自动处理：内容
     */
    private String thirdContent;

    /**
     * 创建者ID
     */
    private Long createId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者ID
     */
    private Long updateId;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 删除者ID
     */
    private Long deleteId;

    /**
     * 删除时间
     */
    private Date deleteTime;

    /**
     * 删除状态（0：未删除，1：已删除）
     */
    private Integer deleteStatus;

    /**
     * 备注
     */
    private String remark;

    private Integer autoDeal;

    private Integer ttsAudio;

    private Integer terminalScreen;

    private Integer urgent;

    private String content;


}
