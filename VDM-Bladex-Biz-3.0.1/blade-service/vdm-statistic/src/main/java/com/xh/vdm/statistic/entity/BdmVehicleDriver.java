package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmVehicleDriver implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String driverCode;

    private String idcard;

    private String name;

    private Integer driverType;

    private Long deptId;

    private String password;

    private Long birthDate;

    private String phone;

    private String nativePlace;

    private String address;

    private Integer driverVehicleMode;

    private Long annualAuditDate;

    private Long getLicenseDate;

    private String certId;

    private Integer practiceMode;

    private String certAuth;

    /**
     * 驾驶证初次发证时间
     */
    private Long firstIssueDate;

    /**
     * 驾驶证发证时间
     */
    private Long issueDate;

    private String regulatoryBodies;

    private String supervisePhone;

    private String serviceStarLevel;

    private String rentalCompany;

    private String secretLabel;

    private String backgroundTitle;

    private String photoUrl;

    private String sex;

    /**
     * 暂定：从业资格证过期时间
     */
    private Long expirationDate;

    private Long licenseValidPeriod;

    private String licensePhotoUrl;

    private String sysPhotoUrl1;

    private String sysPhotoUrl2;

    private String sysPhotoUrl3;

    private String terPhotoUrl1;

    private String terPhotoUrl2;

    private String terPhotoUrl3;

    private String terPhotoUrl4;

    private String terPhotoUrl5;

    private String terPhotoUrl6;

    private Long createTime;

    private Long updateTime;

    private String faceId;

    private Integer faceUrlProtocol;

    private Integer faceFrom;


}
