package com.xh.vdm.statistic.vo.response.alarm;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.vo.response.CommonStatResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(value = "返回体：报警类统计报表通用返回")
@Data
@EqualsAndHashCode(callSuper = true)
public class CommonAlarmStatResponse extends CommonStatResponse {

	@JsonProperty("id")
	@ApiModelProperty(name = "id", value = "报警ID", example = "1", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "报警ID"})
	private Long id;

	@JsonProperty("id_card")
	@ApiModelProperty(name = "id_card", value = "驾驶员身份证", example = "******************", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "驾驶员身份证"})
	private String idCard;

	@JsonProperty("driver_name")
	@ApiModelProperty(name = "driver_name", value = "驾驶员姓名", example = "何斌", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "驾驶员姓名"})
	private String driverName;

	@JsonProperty("alarm_type")
	@ApiModelProperty(name = "alarm_type", value = "报警类型（类型值与名称的映射，详见blade_dict_biz表，code=alarm_type的记录）", example = "1", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "报警类型"})
	private String alarmType;

	@JsonProperty("alarm_level")
	@ApiModelProperty(name = "alarm_level", value = "报警等级（等级值与名称的映射，详见blade_dict_biz表，code=alarm_level的记录）", example = "1", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "报警等级"})
	private String alarmLevel;

	@JsonProperty("alarm_origin")
	@ApiModelProperty(name = "alarm_origin", value = "报警来源", example = "终端", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "报警来源"})
	private String alarmOrigin;

	@JsonProperty("start_time")
	@ApiModelProperty(name = "start_time", value = "报警开始时间", example = "2023-06-27 08:00:00", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "报警开始时间"})
	private String startTime;

	@JsonProperty("end_time")
	@ApiModelProperty(name = "end_time", value = "报警结束时间", example = "2023-06-27 08:00:00", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "报警结束时间"})
	private String endTime;

	@JsonProperty("duration_time")
	@ApiModelProperty(name = "duration_time", value = "报警持续时间（单位：秒）", example = "120", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "报警持续时间（单位：秒）"})
	private Long durationTime;

	@JsonProperty("duration_time_str")
	@ApiModelProperty(name = "duration_time_str", value = "报警持续时间", example = "1天1时1分1秒", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "报警持续时间"})
	private String durationTimeStr;

	@JsonProperty("driving_time")
	@ApiModelProperty(name = "driving_time", value = "行驶开始时间", example = "2023-06-27 08:00:00", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "行驶开始时间"})
	private String drivingTime;

	@JsonProperty("start_address")
	@ApiModelProperty(name = "start_address", value = "开始地点", example = "广东省广州市黄埔区联和街道阅阳一街8号大壮国际广场", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "开始地点"})
	private String startAddress;

	@JsonProperty("end_address")
	@ApiModelProperty(name = "end_address", value = "结束地点", example = "广东省广州市黄埔区联和街道阅阳一街8号大壮国际广场", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "结束地点"})
	private String endAddress;

	@JsonProperty("road_type")
	@ApiModelProperty(name = "road_type", value = "道路类型（类型值与名称的映射，详见blade_dict_biz表，code=road_type的记录）", example = "1", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "道路类型"})
	private String roadType;

	@JsonProperty("speed")
	@ApiModelProperty(name = "speed", value = "速度（单位：km/h）", example = "60.0", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "报警速度"})
	private Double speed;

	@JsonProperty("max_speed")
	@ApiModelProperty(name = "max_speed", value = "最大速度（单位：km/h）", example = "60.5", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "最大速度"})
	private Double maxSpeed;

	@JsonProperty("limit_speed")
	@ApiModelProperty(name = "limit_speed", value = "限制速度（单位：km/h）", example = "60.0", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "限制速度"})
	private Double limitSpeed;

	@JsonProperty("over_speed_percent")
	@ApiModelProperty(name = "over_speed_percent", value = "超速比", example = "10.00%", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "超速比"})
	private String overSpeedPercent;

	@JsonProperty("phone")
	@ApiModelProperty(name = "phone", value = "终端号", example = "75020205382", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "终端号"})
	private String phone;

	@JsonProperty("terminal_type")
	@ApiModelProperty(name = "terminal_type", value = "终端类型（类型值与名称的映射，详见blade_dict_biz表，code=5的记录）", example = "1", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "终端类型"})
	private String terminalType;

	@JsonProperty("deal_measures")
	@ApiModelProperty(name = "deal_measures", value = "处理方式", example = "我都不知道该填啥了！", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "处理方式"})
	private String dealMeasures;

	@JsonProperty("deal_content")
	@ApiModelProperty(name = "deal_content", value = "处理内容", example = "罚死你！", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "处理内容"})
	private String dealContent;

	@JsonProperty("deal_time")
	@ApiModelProperty(name = "deal_time", value = "处理时间", example = "2023-06-27 08:00:00", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警统计报表", "处理时间"})
	private String dealTime;
}
