package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @Description: 车辆行驶里程请求对象
 * @Author: zhouxw
 * @Date: 2022/11/9 2:58 PM
 */
@Data
public class VehicleTravelDurationRequest {

    //企业名称
    private Long deptId;

    //行业类型（如果是多个，中间使用英文逗号,分隔）
    private String vehicleUseType;

    //查询开始时间
    private Long startTime;

    //查询结束时间
    private Long endTime;

    //查询月份(非前台传递)
    @JsonIgnore
    private String month;

    //开始下标
    private Integer start;
    //每页数据数量
    private Integer count;


}
