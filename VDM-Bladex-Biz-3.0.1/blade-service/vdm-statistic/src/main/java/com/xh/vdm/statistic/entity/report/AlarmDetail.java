package com.xh.vdm.statistic.entity.report;

import lombok.Data;

import java.util.Date;

/**
 * 报警详情
 */
@Data
public class AlarmDetail {

	//企业名称
	private String deptName;
	//车牌号
	private String licencePlate;
	//车牌颜色
	private String licenceColor;
	//车牌颜色名称
	private String licenceColorDesc;
	//行业类型
	private String vehicleUseType;
	//行业类型名称
	private String vehicleUseTypeDesc;

	//报警类型
	private String alarmType;
	//报警等级
	private String alarmLevel;
	//报警时间
	private Date alarmTime;
	//报警结束时间
	private Date alarmEndTime;
	//报警位置
	private String alarmAddress;
	//报警结束位置
	private String alarmEndAddress;
	//最大速度
	private Double maxSpeed;
	//速度
	private Double speed;
	//限速
	private Double limitSpeed;
	//道路名称
	private String roadName;
	//报警持续时间
	private String duration;




}
