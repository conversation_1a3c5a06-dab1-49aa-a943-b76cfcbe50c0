package com.xh.vdm.statistic.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Description: 应用方向/领域
 */
@AllArgsConstructor
@Getter
public enum DomainEnum {
	/**
	 * 人员定位
	 */
	WORKER_RNSS(1, "人员定位"),
	/**
	 * 运载定位
	 */
	CARRY_RNSS(2, "运载定位"),

	/**
	 * 安全监测
	 */
	SAFE_MONIT(3, "安全监测"),

	/**
	 * 精密控制
	 */
	PRECISE_CONTROL(4, "精密控制"),

	/**
	 * 资产管理
	 */
	ASSET_MANAGEMENT(5, "资产管理"),

	/**
	 * 智能巡检
	 */
	INTELLIGENT_INSPECTION(6, "智能巡检"),

	/**
	 * 测绘勘探
	 */
	MAPPING_SURVEY(7, "测绘勘探"),

	/**
	 * 时频同步
	 */
	TIME_FREQUENCY_SYNCHRONIZATION(8, "时频同步"),

	/**
	 * 应急通信
	 */
	EMERGENCY_TELECOMMUNICATIONS(9, "应急通信");

	/**
	 * 标识
	 */
	private final Integer value;
	/**
	 * 状态
	 */
	private final String label;

}
