package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.BdmDeviceOnline;
import com.xh.vdm.statistic.vo.request.BdmDeviceOnlineRequest;
import com.xh.vdm.statistic.vo.response.DeviceOnlineResponse;
import com.xh.vdm.statistic.vo.response.terminal.OnlineCountResponse;
import org.springblade.core.mp.support.Query;
import java.util.List;

/**
 * (BdmDeviceOnline)表服务接口
 */
public interface BdmDeviceOnlineService  extends IService<BdmDeviceOnline> {

	/**
	 * 分页查询
	 *
	 * @param bdmDeviceOnlineRequest 筛选条件
	 * @param query                  分页对象
	 * @param userId
	 * @return 查询结果
	 */
	List<DeviceOnlineResponse> queryByPage(BdmDeviceOnlineRequest bdmDeviceOnlineRequest, Query query, Long userId);

	long countOnline(BdmDeviceOnlineRequest bdmDeviceOnlineRequest, Long userId);

    List<OnlineCountResponse> getOnlineTrendered(Long userId);
}
