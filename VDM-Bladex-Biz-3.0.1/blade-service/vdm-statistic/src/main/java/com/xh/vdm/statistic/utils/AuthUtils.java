package com.xh.vdm.statistic.utils;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.statistic.entity.BladeDept;
import com.xh.vdm.statistic.service.IBladeDeptService;
import com.xh.vdm.statistic.service.IUserDeptRegulatesService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.api.ResultCode;
import org.springblade.system.user.entity.UserDeptRegulates;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Description: 用户权限
 */
@Component
@Slf4j
public class AuthUtils {

	private static IBladeDeptService iBladeDeptService;
	private static IUserDeptRegulatesService iUserDeptRegulatesService;

	private static CETokenUtil ceTokenUtil;

	@Resource
	public void setIBladeDeptService(IBladeDeptService iBladeDeptService) {
		AuthUtils.iBladeDeptService = iBladeDeptService;
	}

	@Resource
	public void setIUserDeptRegulatesService(IUserDeptRegulatesService iUserDeptRegulatesService) {
		AuthUtils.iUserDeptRegulatesService = iUserDeptRegulatesService;
	}

	@Resource
	public void setCETokenUtil(CETokenUtil ceTokenUtil) {
		AuthUtils.ceTokenUtil = ceTokenUtil;
	}

	public static R<String> isValidServiceRole(BladeUser user) {
		if(user == null){
			log.info("AuthUtils: user is null");
		}else{
			log.info("AuthUtil: user is {}", JSON.toJSONString(user));
		}
		if ((user == null) || StringUtils.isBlank(user.getRoleName())) {
			return R.fail(ResultCode.FAILURE.getCode(), "用户登录信息异常。");
		}

		return R.success(ResultCode.SUCCESS);
	}

	/**
	 * 按照用户权限合并处理获取最终部门列表
	 *
	 * @param user 当前登录用户
	 * @return 用户权限 -- 获取所有监管部门
	 */
	public static R<List<Long>> getDeptList(BladeUser user) {
		List<Long> deptList = new ArrayList<>();
		// 超级管理员账户，查看所有部门
		if (AuthUtil.isAdministrator()) {
			return R.data(ResultCode.SUCCESS.getCode(), deptList, "");
		}
		if(!ceTokenUtil.isCELogin()){
			if (!AuthUtil.isAdministrator()) {
				deptList = iUserDeptRegulatesService.getBaseMapper()
					.selectList(new QueryWrapper<UserDeptRegulates>()
						.eq("user_id", user.getUserId())).stream()
					.map(UserDeptRegulates::getDeptId)
					.collect(Collectors.toList());
			}
			if (CollectionUtils.isEmpty(deptList)) {
				return R.fail("未能获取该用户的组织架构");
			}
		}

		return R.data(ResultCode.SUCCESS.getCode(), deptList, "");
	}
}
