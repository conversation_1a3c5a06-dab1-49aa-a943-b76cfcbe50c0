package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.StatDriverDuration;
import com.xh.vdm.statistic.mapper.StatDriverDurationMapper;
import com.xh.vdm.statistic.service.IStatDriverDurationService;
import com.xh.vdm.statistic.vo.request.DriverDurationRequest;
import com.xh.vdm.statistic.vo.response.DriverDurationResponse;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-23
 */
@Service
public class StatDriverDurationServiceImpl extends ServiceImpl<StatDriverDurationMapper, StatDriverDuration> implements IStatDriverDurationService {

    @Override
    public void deleteDataByDate(String month, String date) {
        baseMapper.deleteDataByDate(month, date);
    }

    @Override
    public void saveBatch(List<StatDriverDuration> list, String month) throws Exception {
        baseMapper.saveBatch(list, month);
    }

	@Override
	public void deleteAppendData(List<String> dateList, String month) throws Exception {
		baseMapper.deleteAppendData(dateList, month);
	}

	@Override
    public DriverDurationResponse findDriverByCondition(String month, DriverDurationRequest request) {
        List<StatDriverDuration> list = baseMapper.getDriverDurationByCondition(month, request);
        DriverDurationResponse req = new DriverDurationResponse();
        if(list == null || list.size() < 1){
            req.setLicencePlate(request.getLicencePlate());
            return req;
        }
        List<String> driverNames = new ArrayList<>();
        list.forEach(item -> {
            driverNames.add(item.getDriverName());
        });
        req.setLicencePlate(list.get(0).getLicencePlate());
        req.setDriverName(driverNames);
        return req;
    }

	@Override
	public List<StatDriverDuration> findDriverDurationListByIdCard(String idCard, String statDate, String month) throws Exception {
		statDate = statDate.replace("-","");
		month = month.replace("-","");
		return baseMapper.getDriverDurationListByIdCard(idCard, statDate, month);
	}

	@Override
	public List<StatDriverDuration> findDriverDurationListByIdCardMonth(String idCard, String month) throws Exception {
		month = month.replace("-","");
		return baseMapper.getDriverDurationListByIdCardMonth(idCard, month);
	}
}
