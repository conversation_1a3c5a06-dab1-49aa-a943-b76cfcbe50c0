package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <p>
 * 考核评分整体情况DB缓存表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CacheStatCombineScore implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 月份
     */
    private String month;

    /**
     * 上级平台
     */
    private Long vehicleOwnerId;

    /**
     * 入网车辆数
     */
    private Long innetCount;

    /**
     * 车辆上线率
     */
    private Double goonlineRate;

    /**
     * 车辆上线率得分
     */
    private Double goonlineScore;

    /**
     * 车辆联通率
     */
    private Double connectRate;

    /**
     * 车辆连通率得分
     */
    private Double connectScore;

    /**
     * 轨迹完整率
     */
    private Double completeRate;

    /**
     * 轨迹完整率得分
     */
    private Double completeScore;

    /**
     * 数据合格率
     */
    private Double qualityRate;

    /**
     * 数据合格率得分
     */
    private Double qualityScore;

    /**
     * 定位漂移率
     */
    private Double driftRate;

    /**
     * 定位漂移率得分
     */
    private Double driftScore;

    /**
     * 考核得分
     */
    private Double totalScore;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 备注
     */
    private String note;


}
