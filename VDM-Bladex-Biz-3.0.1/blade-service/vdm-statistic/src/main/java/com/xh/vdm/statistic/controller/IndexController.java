package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.alarm.entity.AlarmInfo;
import com.xh.vdm.alarm.feign.IAlarmClient;
import com.xh.vdm.base.feign.IBaseInfoClient;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.*;
import com.xh.vdm.statistic.vo.request.VehRunningStateRequest;
import com.xh.vdm.statistic.vo.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.Dept;
import org.springblade.system.feign.ISysClient;
import org.springblade.system.user.feign.IUserClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description: 首页接口
 * @Author: zhouxw
 * @Date: 2023/6/13 19:14
 */
@RestController
@RequestMapping("/index")
@Slf4j
public class IndexController {


	@Resource
	private ISysClient sysClient;

	@Resource
	private IBaseInfoClient baseInfoClient;

	@Resource
	private IAlarmClient alarmClient;

	@Resource
	private IBladeUserService userService;

	@Resource
	private IVehicleStatService vehicleStatService;


	@Resource
	private IUserClient userClient;


	@Autowired
	private VdmUserInfoUtil userInfoUtil;


	@Resource
	private IAlarmService alarmService;

	@Resource
	private IStatCompleteService statCompleteService;

	@Resource
	private ILocationQualityService locationQualityService;

	@Resource
	private IStatDriftService driftService;

	@Resource
	private IStatVehRunningStateService vehRunningStateService;

	@Resource
	private IBdmVehicleService vehicleService;

	@Resource
	private IStatCountRateUserService alarmCountRateUserService;

	@Resource
	private RedisTemplate<String,Object> redisTemplate;


	/**
	 * @description: 平台基本信息
	 * 数据包含本部门、子部门、本账号关联车辆的信息
	 * @author: zhouxw
	 * @date: 2023-06-166 21:01:50
	 * @param: [user]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.statistic.vo.response.IndexBaseInfoResponse>
	 **/
	@GetMapping("/baseInfo")
	public R<IndexBaseInfoResponse> baseInfo(BladeUser user){

		//1.查询企业数量
		//1.1 获取登录的信息
		long deptCount = 0;
		//本地调试
		String deptId = user.getDeptId();
		/*String deptId = "1123598813738675201";
		user = new BladeUser();
		user.setDeptId(deptId);*/


		List<Long> deptIds = new ArrayList<>();
		deptIds.add(Long.parseLong(deptId));
		try{
			//如果查询子部门失败，则只查询本部门的数据
			deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);
		}catch (Exception e){
			log.error("查询子级部门失败",e);
		}

		List<Long> deptIdList = new ArrayList<>();
		try{
			//1.2 获取子机构信息
			R<List<Dept>> res = sysClient.getDeptChild(Long.parseLong(deptId));
			if(res == null || !res.isSuccess()){
				log.error("[主页--基础信息]查询企业数量失败：调用feign接口失败");
			}
			deptCount = res.getData() == null ? 0 : res.getData().size();
			//同时应该算上自己一级
			deptCount ++;
			//记录部门id
			for(Dept d : res.getData()){
				deptIdList.add(d.getId());
			}
			deptIdList.add(Long.parseLong(user.getDeptId()));
		}catch (Exception e){
			log.error("[主页--基础信息]查询子部门失败",e);
		}

		//2.查询人员总数
		long personCount = 0;
		try{

			long total = userService.count(Wrappers.lambdaQuery(com.xh.vdm.statistic.entity.BladeUser.class)
				.eq(com.xh.vdm.statistic.entity.BladeUser::getIsDeleted,0)
				.eq(com.xh.vdm.statistic.entity.BladeUser::getStatus,1)
				.in(com.xh.vdm.statistic.entity.BladeUser::getDeptId,deptIdList));

			personCount = total;
		}catch (Exception e){
			log.error("[主页--基础信息]查询人员数量失败",e);
		}

		//3.查询车辆总数
		long vehicleCount = 0;
		try{
			vehicleCount = vehicleService.findAllVehicleCount(deptIds, user.getUserId());
		}catch (Exception e){
			log.error("[主页--基础信息]查询车辆总数失败");
		}
		try{
			//如果查询子部门失败，则只查询本部门的数据
			deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);
		}catch (Exception e){
			log.error("查询子级部门失败",e);
		}

		//4.营运车辆数
		long runningCount = 0;
		try{
			runningCount = vehicleService.findRunningCount(deptIds, user.getUserId());
		}catch (Exception e){
			log.error("查询数据出错",e);
		}

		//5.上线车辆数（查询今日上线车辆数）
		long goOnlineCount = 0;
		try{
			goOnlineCount = vehicleService.findGoOnlineCount(deptIds, user.getUserId());
		}catch (Exception e){
			log.error("查询上线车辆数出错",e);
		}

		//6.在线车辆数
		//todo 要更改为读取redis获取在线车辆情况 vehicle:车牌号:车牌颜色
		long onlineCount = 0;
		try{
			onlineCount = vehicleService.findOnlineCount(deptIds, user.getUserId());
		}catch (Exception e){
			log.error("查询数据出错",e);
		}

		//7.离线车辆数（营运车辆数-在线车辆数）
		long offlineCount = 0;
		try{
			offlineCount = runningCount - onlineCount;
		}catch (Exception e){
			log.error("查询数据出错",e);
		}

		IndexBaseInfoResponse response = new IndexBaseInfoResponse();
		response.setDeptCount(deptCount);
		response.setPersonCount(personCount);
		response.setVehicleCount(vehicleCount);
		response.setRunningVehicleCount(runningCount);
		response.setGoOnlineCount(goOnlineCount);
		response.setOnlineVehicleCount(onlineCount);
		response.setOfflineVehicleCount(offlineCount);
		return R.data(response);
	}


	/**
	 * @description: 统计报警情况
	 * 主页统计今日累计报警情况
	 * @author: zhouxw
	 * @date: 2023-06-166 21:25:44
	 * @param: [user]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.alarm.entity.AlarmInfo>
	 **/
	@GetMapping("/alarmInfo")
	public R<AlarmInfo> alarmInfo(BladeUser user){
		if(user == null){
			return R.fail("用户未登录或未授权");
		}
		String deptId = user.getDeptId();
		Long userId = user.getUserId();
		String roleType = user.getRoleName();

		String userType = "";
		if(roleType.contains(StatisticConstants.ROLE_THIRD)){
			//如果是第三方服务商
			userType = "2";
		}else if(roleType.contains(StatisticConstants.ROLE_SERVER)){
			//如果是服务商
			userType = "1";
		}else if(roleType.contains(StatisticConstants.ROLE_COMPANY)){
			//如果是企业
			userType = "3";
		}
		long start1 = System.currentTimeMillis();
		//查询报警情况
		AlarmInfo info = null;
		try {
			R<AlarmInfo> res = alarmClient.getAlarmInfo(Long.parseLong(deptId), userId);
			if(res == null || res.getData() == null){
				return R.fail("查询出错");
			}
			info = res.getData();
		}catch (Exception e){
			log.error("[主页--报警信息]查询报警情况接口报错");
			return R.fail("查询出错");
		}
		long end1 = System.currentTimeMillis();
		log.info("查询报警情况完成，耗时："+ (end1 - start1)  );

		//查询今日报警处理数量
		long start2 = System.currentTimeMillis();
		long handleCount = 0;
		try {
			R<Long> res = alarmClient.getTodayAlarmHandleCount(Long.parseLong(deptId), userId, userType);
			if(res == null || res.getData() == null){
				log.error("[主页--报警信息]查询报警处理数据报错或未查询到数据");
			}
			handleCount = res.getData();
		}catch (Exception e){
			log.error("[主页--报警信息]查询报警处理数量报错",e);
			return R.fail("查询出错");
		}
		double handleRate = 1;
		if(info.getTodayTotalAlarmCount() != null && info.getTodayTotalAlarmCount() > 0){
			handleRate = MathUtil.roundDouble((double)handleCount / (double)info.getTodayTotalAlarmCount(), 4);
		}

		info.setHandleRate(MathUtil.formatToPercent(handleRate,2));

		long end2 = System.currentTimeMillis();
		log.info("查询报警处理情况完成，耗时："+ (end2 - start2));

		return R.data(info);
	}


	/**
	 * @description: 统计近30天上线率 和 统计近30天的报警数和处理数、近30天车辆运行趋势
	 * @author: zhouxw
	 * @date: 2023-07-204 17:25:00
	 * @param: [user]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.statistic.vo.response.IndexAlarmAndGoOnlineResponse>
	 **/
	@GetMapping("/alarmAndGoOnline")
	public R<IndexAlarmAndGoOnlineResponse> alarmAndGoOnline(BladeUser user){

		//部门情况：本部门、子部门、本账号关联的车辆
		//查询本部门、子部门
		if(user == null){
			log.info("获取用户信息为空");
			return R.fail("用户登录失败或未授权");
		}

		List<StatCountRateUser> list = alarmCountRateUserService.list(Wrappers.lambdaQuery(StatCountRateUser.class).eq(StatCountRateUser::getUserId,user.getUserId()));
		//解析数据
		IndexAlarmAndGoOnlineResponse resp = new IndexAlarmAndGoOnlineResponse();
		List<AlarmAndHandleCountResponse> alarmList = new ArrayList<>();
		List<GoOnlineRateBaseResponse> goOnlineList = new ArrayList<>();
		List<DateAndRate> qualityRateList = new ArrayList<>();
		List<DateAndRate> completeRateList = new ArrayList<>();
		List<DateAndRate> driftRateList = new ArrayList<>();
		try{
			for (StatCountRateUser sr : list) {
				//报警信息
				AlarmAndHandleCountResponse alarm = new AlarmAndHandleCountResponse();
				alarm.setAlarmCount(sr.getAlarmCount());
				alarm.setHandleRate(sr.getHandleRate());
				alarm.setDate(sr.getDate());
				alarm.setHandleCount(sr.getHandleCount());
				alarmList.add(alarm);

				//上线信息
				GoOnlineRateBaseResponse goOnline = new GoOnlineRateBaseResponse();
				goOnline.setGoOnlineRate(sr.getGoOnlineRate());
				goOnline.setGoOnlineCount(sr.getGoOnlineCount());
				goOnline.setDate(sr.getDate());
				goOnlineList.add(goOnline);

				//轨迹完整率
				DateAndRate drc = new DateAndRate();
				drc.setDate(sr.getDate());
				if(sr.getCompleteRate() == null){
					drc.setRateStr("0%");
					drc.setRate(0D);
				}else {
					drc.setRateStr(MathUtil.formatToPercent(sr.getCompleteRate(), 4));
					drc.setRate(sr.getCompleteRate());
				}
				completeRateList.add(drc);

				//数据合格率
				DateAndRate drq = new DateAndRate();
				drq.setDate(sr.getDate());
				if(sr.getQualityRate() == null){
					drq.setRateStr("0%");
					drq.setRate(0D);
				}else {
					drq.setRateStr(MathUtil.formatToPercent(sr.getQualityRate(), 4));
					drq.setRate(sr.getQualityRate());
				}
				qualityRateList.add(drq);

				//定位漂移率
				DateAndRate drd = new DateAndRate();
				drd.setDate(sr.getDate());
				if(sr.getDriftRate() == null){
					drd.setRateStr("0%");
					drd.setRate(0D);
				}else{
					drd.setRateStr(MathUtil.formatToPercent(sr.getDriftRate(), 4));
					drd.setRate(sr.getDriftRate());
				}
				driftRateList.add(drd);
			}
		}catch (Exception e){
			log.error("执行数据统计报错",e);
		}

		resp.setAlarmList(alarmList);
		resp.setGoOnlineList(goOnlineList);
		resp.setCompleteRate(completeRateList);
		resp.setDriftRate(driftRateList);
		resp.setQualifiedRate(qualityRateList);
		return R.data(resp);
	}



	/**
	 * 统计车辆运行情况
	 * @return
	 */
	@PostMapping("/statVehRunningState")
	R<IPage<StatVehRunningStateMonth>> statVehRunningState(@RequestBody VehRunningStateRequest request, Query query, BladeUser user){
		try{

			//查询本部门、子部门
			if(user == null){
				log.info("获取用户信息为空");
				return R.fail("用户登录失败或未授权");
			}
			List<Long> list = null;
			try {
				list = userInfoUtil.getChildrenAndSelfDeptId(user);
			}catch (Exception e){
				log.error("查询部门信息失败",e);
				return R.fail("用户登录失败或未授权");
			}

			//设置排序字段
			String orderField = request.getOrderField();
			if(!StringUtils.isEmpty(orderField)){
				orderField = LineAndHumpUtil.humpToLine(orderField);
			}
			request.setOrderField(orderField);
			//设置排序方式
			String oType = "desc";
			if("1".equals(request.getOrderType())){
				//如果是升序
				oType = "asc";
			}
			request.setOrderType(oType);
			IPage<StatVehRunningStateMonth> page = vehRunningStateService.findRunningStateByPage(request, query, list, user.getUserId());
			List<StatVehRunningStateMonth> records = page.getRecords();
			//设置数据格式
			records.forEach(item -> {
				item.setTotalMileage(MathUtil.roundDouble(item.getTotalMileage()/1000,2));
				item.setCompleteRate(MathUtil.roundDouble(item.getCompleteRate(),4));
				item.setQualityRate(MathUtil.roundDouble(item.getQualityRate(),4));
				item.setDriftRate(MathUtil.roundDouble(item.getDriftRate(),4));
			});
			return R.data(page);
		}catch (Exception e){
			log.error("查询数据报错",e);
			return R.fail("查询数据报错");
		}
	}


	/**
	 * 实时统计车辆运行状态
	 * @return
	 */
	@GetMapping("/vehicleState")
	public R<VehicleStateResponse> vehicleState(BladeUser user){

		//0.查询本部门、子部门
		if(user == null){
			log.info("获取用户信息为空");
			return R.fail("用户登录失败或未授权");
		}
		List<Long> list = null;
		try {
			list = userInfoUtil.getChildrenAndSelfDeptId(user);
		}catch (Exception e){
			log.error("查询部门信息失败",e);
			return R.fail("用户登录失败或未授权");
		}

		VehicleStateResponse vsr = new VehicleStateResponse();

		try{
			//1.统计行驶车辆数
			long runningVehicleCount = vehicleService.findRunningVehicleCount(list, user.getUserId());

			//2.统计离线、长期离线、在线车辆数
			VehicleRunningStateResponse resp = vehicleService.statVehicleRunningState(user);
			long offlineCount = resp.getOfflineCount();
			long onlineCount = resp.getOnlineCount();
			long longOfflineCount = resp.getOfflineLongTime();

			//3.统计停驶车辆数
			long stopVehicleCount = onlineCount - runningVehicleCount;
			vsr.setRunningCount(runningVehicleCount);
			vsr.setOfflineCount(offlineCount);
			vsr.setLongOfflineCount(longOfflineCount);
			vsr.setStopCount(stopVehicleCount);
			return R.data(vsr);
		}catch (Exception e){
			log.error("统计车辆运行状态失败",e);
			return R.fail("查询失败");
		}


	}


}
