package com.xh.vdm.statistic.entity.report;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 企业月报表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatReportMonth implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 部门id
     */
    private Long deptId;

	//报表类型
	private String type;

    /**
     * 统计月份
     */
    private String month;

	//文件存储路径
	private String filePath;

    /**
     * 报表文件路径
     */
    private String proxyFileUrl;

    /**
     * 创建日期
     */
    private Date createTime;

    /**
     * 更新日期
     */
    private Date updateTime;


}
