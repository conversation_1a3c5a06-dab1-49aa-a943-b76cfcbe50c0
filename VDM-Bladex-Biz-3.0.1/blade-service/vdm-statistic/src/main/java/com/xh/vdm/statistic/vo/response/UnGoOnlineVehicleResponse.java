package com.xh.vdm.statistic.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 未上线车辆返回内容
 * @Author: zhouxw
 * @Date: 2022/9/6 11:19 AM
 */
@Data
public class UnGoOnlineVehicleResponse {

    //企业名称（支持模糊查询）
    private String deptName;
    //车牌号
    private String licencePlate;
    //车牌颜色
    @JsonProperty("licenceColor")
    private String plateColor;
    //SIM卡号
    private String simId;
    //终端ID
    private String terminalId;
    //统计月份
    private String month;
    //终端型号
    private String terminalModel;

}
