package com.xh.vdm.statistic.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Author: yangjl
 * @Description:
 * @Date: Created in 18:27 2018/9/25
 * @Version: 1.0.0
 */

public class BaseResponse implements Serializable {
    @ApiModelProperty(value = "错误码，0为正常")
    private int code = 0;

    @ApiModelProperty(value = "返回信息，code为0时为success，否则为错误信息")
    private String msg ="success";

    public BaseResponse(int code, String msg){
        this.code = code;
        this.msg = msg;
    }

    public BaseResponse() {
    }

    public String getMsg(){
        return msg;
    }

    public void setMsg(String msg){
        this.msg = msg;
    }

    public int getCode(){
        return code;
    }

    public void setCode(int code){
        this.code=code;
    }
}
