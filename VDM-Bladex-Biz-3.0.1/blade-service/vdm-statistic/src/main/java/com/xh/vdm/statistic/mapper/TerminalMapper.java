package com.xh.vdm.statistic.mapper;


import com.xh.vdm.statistic.entity.TerminalModelAndCount;
import com.xh.vdm.statistic.entity.TerminalTypeAndCount;
import com.xh.vdm.statistic.vo.TerminalBaseVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
public interface TerminalMapper {

    /**
     * @description: 查询定位终端类型和数量
     * @author: zhouxw
     * @date: 2022/11/21 9:14 AM
     * @param: [deptId]
     * @return: java.util.List<com.xh.vdm.statistic.entity.TerminalTypeAndCount>
     **/
    List<TerminalTypeAndCount> getTerminalTypeAndCount(Long deptId);

    /**
     * @description: 查询终端型号和数量
     * @author: zhouxw
     * @date: 2022/11/21 9:23 AM
     * @param: [deptId]
     * @return: java.util.List<com.xh.vdm.statistic.entity.TerminalModelAndCount>
     **/
    List<TerminalModelAndCount> getTerminalModelAndCount(Long deptId);

	/**
	 * 查询终端列表
	 */
	List<Map<String, String>> getTerminalList ();

	/**
	 * 根据条件查询终端基本信息
	 * @param deviceNum
	 * @param deviceType
	 * @param targetName
	 * @param deptIds
	 * @return
	 */
	List<TerminalBaseVO> getTerminals(@Param("deviceNum") String deviceNum, @Param("deviceType") Integer deviceType, @Param("targetName") String targetName, @Param("deptIds") String deptIds, @Param("account") String account);

	/**
	 * 根据条件查询一条终端信息
	 * @param deviceNum
	 * @param deptIds
	 * @return
	 */
	TerminalBaseVO getTerminalOne(@Param("deviceNum") String deviceNum, @Param("deptIds") String deptIds, @Param("account") String account);

}
