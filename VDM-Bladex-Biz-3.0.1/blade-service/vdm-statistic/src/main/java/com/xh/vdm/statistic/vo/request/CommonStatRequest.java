package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.util.List;

@ApiModel(value = "请求体：统计报表通用请求")
@Data
public class CommonStatRequest {

	@JsonProperty("head_name_list")
	@ApiModelProperty(name = "head_name_list", value = "表头字段名称列表", example = "[1, ...]", required = false)
	private List<String> headNameList;

	@JsonProperty("column_name_list")
	@ApiModelProperty(name = "column_name_list", value = "表头字段列表", example = "[1, ...]", required = false)
	private List<String> columnNameList;

	@JsonProperty("service_role")
	@ApiModelProperty(name = "service_role", value = "服务角色（角色值与名称的映射，详见blade_dict_biz表，code=service_role的记录）", example = "1", required = true)
	@NotEmpty(message = "服务角色为空。")
	@Pattern(regexp = "\\d+", message = "服务角色不正确。")
	private String serviceRole;

	@JsonProperty("dept_id_list")
	@ApiModelProperty(name = "dept_id_list", value = "单位ID列表", example = "[1, ...]", required = false)
	private List<Long> deptIdList; // 从请求参数过来的指定单位列表。

	@JsonProperty("dept_id")
	@ApiModelProperty(name = "dept_id", value = "单位ID", example = "1", required = false)
	@DecimalMin(value = "1", message = "单位ID不正确。")
	private Long deptId; // 发送邮件时，指定目标企业。

	@JsonProperty("start_time")
	@ApiModelProperty(name = "start_time", value = "开始时间", example = "1689868799", required = false)
	@DecimalMin(value = "0", message = "开始时间不正确。")
	private Long startTime;

	@JsonProperty("end_time")
	@ApiModelProperty(name = "end_time", value = "结束时间", example = "1689868799", required = false)
	@DecimalMin(value = "0", message = "结束时间不正确。")
	private Long endTime;

	@JsonProperty("vehicle_id_list")
	@ApiModelProperty(name = "vehicle_id_list", value = "车辆ID列表（用户指定）", example = "[1, ...]", required = false)
	private List<Integer> vehicleIdList;

	@JsonProperty("vehicle_use_type")
	@ApiModelProperty(name = "vehicle_use_type", value = "行业类型（类型值与名称的映射，详见blade_dict_biz表，code=2的记录）", example = "1", required = false)
	@Pattern(regexp = "\\d+", message = "行业类型不正确。")
	private String vehicleUseType;

	@JsonProperty("vehicle_owner_id")
	@ApiModelProperty(name = "vehicle_owner_id", value = "车辆归属", example = "1", required = false)
	@DecimalMin(value = "0", message = "车辆归属不正确。")
	private Long vehicleOwnerId;

	@JsonProperty("access_mode")
	@ApiModelProperty(name = "access_mode", value = "接入方式（方式值与名称的映射，详见blade_dict_biz表，code=access_mode的记录）", example = "1", required = false)
	@Pattern(regexp = "\\d+", message = "接入方式不正确。")
	private String accessMode;

	private List<Long> deptList; // 最终确定的单位列表，以限制用户能访问的数据范围。
	private List<Integer> vehicleList; // 用户关联的车辆列表
	private List<List<Integer>> vehicleSliceList; // 对应vehicleList，为了防止SQL的in子句元素过多，需对原始列表切割多个子列表，各自构成SQL的in子句，然后通过or连成整体。
	private List<List<Integer>> vehicleIdSliceList; // 对应vehicleIdList，为了防止SQL的in子句元素过多，需对原始列表切割多个子列表，各自构成SQL的in子句，然后通过or连成整体。
	private List<String> vehicleUseTypeList;
}
