package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.BdmTerminalSim;
import com.xh.vdm.statistic.mapper.BdmTerminalSimMapper;
import com.xh.vdm.statistic.service.IBdmTerminalSimService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.request.SimExpireRequest;
import com.xh.vdm.statistic.vo.response.SimExpireResponse;
import org.springblade.core.mp.support.Query;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Service
public class BdmTerminalSimServiceImpl extends ServiceImpl<BdmTerminalSimMapper, BdmTerminalSim> implements IBdmTerminalSimService {

	@Override
	public IPage<SimExpireResponse> findSimExpireInfo(SimExpireRequest request,  Query query) throws Exception {
		IPage<SimExpireResponse> page = new Page<>();
		page.setSize(query.getSize());
		page.setCurrent(query.getCurrent());
		return baseMapper.getSimExpireInfo(request,  page);
	}
}
