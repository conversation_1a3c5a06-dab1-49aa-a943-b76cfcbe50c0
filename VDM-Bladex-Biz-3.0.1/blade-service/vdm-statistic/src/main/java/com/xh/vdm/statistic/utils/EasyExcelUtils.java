package com.xh.vdm.statistic.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.xh.vdm.statistic.config.ExcelExportFontAndStyleConfig;
import com.xh.vdm.statistic.config.ExcelExportRowHeightConfig;
import org.apache.logging.log4j.message.SimpleMessage;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2022/11/1 3:09 PM
 */
public class EasyExcelUtils {

    public static final String DEFAULT_PROXY_PATH = "/bt/statistics/files/";

    /**
     * @description: 使用 easyexcel 导出数据
     * @author: zhouxw
     * @date: 2022/12/1 9:59 AM
     * @param: [fileSavedPath：文件存储路径, fileProxyPath：文件代理路径，如使用 nginx 代理时通过解析该路径找到目标文件, title：表格标题, list：数据列表, exportClass：文件导出时的数据类，fileNameIsContainDate: 当导出日报、月报时，文件名称中需要带有当日或者当月的日期，而不应该带有当天的日期，普通导出设置为 false]
     * @return: java.lang.String
     **/
    public static String export(String fileSavedPath , String fileProxyPath ,String title , List list , Class exportClass,boolean fileNameIsContainDate) throws FileNotFoundException {
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String dateStr = format.format(date);
        String fileName = "";
        if(fileNameIsContainDate){
            //如果文件名称中带有日期，则不再添加日期
            fileName = title + ".xlsx";
        }else{
            fileName = title + "_" + dateStr + ".xlsx";
        }
        FileOutputStream file = new FileOutputStream(fileSavedPath + fileName);
        fileName =  fileProxyPath + fileName;
        //主标题和副标题在excel中分别是是第0和第1行
        List<Integer> columnIndexes = Arrays.asList(0,1);
        //自定义标题和内容策略(具体定义在下文)
        ExcelExportFontAndStyleConfig cellStyleStrategy =
                new ExcelExportFontAndStyleConfig(columnIndexes,new WriteCellStyle(), new WriteCellStyle());

        EasyExcel.write(file, exportClass)
                .registerWriteHandler(cellStyleStrategy)
                .registerWriteHandler(new ExcelExportRowHeightConfig()).sheet(title).doWrite(list);
        return fileName;
    }







	/**
	 * 写excel，动态字段
	 *
	 * @param fileSavedPath 保存的路径名
	 * @param headList
	 * @param dataList
	 */
	public static String exportDynamicColumn(String fileSavedPath, String fileProxyPath, String title,
											 List<ExcelHead> headList, List<Map> dataList,
											 boolean fileNameIsContainDate) throws Exception {
		// 构建文件名
		StringBuilder builder = new StringBuilder(title);
		if (!fileNameIsContainDate) {
			builder.append("_").append(new SimpleDateFormat("yyyMMdd_HHmmss").format(new Date()));
		}
		String fileName = builder.append(".xlsx").toString();

		// 创建Excel表格标题
		List<List<String>> titles = new ArrayList<>();
		List<List<String>> heads = new ArrayList<>();
		for (ExcelHead eh : headList) {
			titles.add(Collections.singletonList(title));
			heads.add(Collections.singletonList(eh.getTitle()));
		}

		// 生成sheet和表
		WriteSheet writeSheet = EasyExcel.writerSheet(title).needHead(Boolean.TRUE).head(titles).build();
		WriteTable writeTable = EasyExcel.writerTable(1).needHead(Boolean.TRUE).head(heads).build();

		// 生成表数据
		List<List<Object>> datas = convertData(headList, dataList);

		// 定义表样式
		ExcelWriter writer = EasyExcel.write(fileSavedPath + fileName)
			.registerWriteHandler(new ExcelExportFontAndStyleConfig(
				Arrays.asList(0, 1), new WriteCellStyle(), new WriteCellStyle()))
			.registerWriteHandler(new ExcelExportRowHeightConfig())
			.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();

		// 写入表数据
		writer.write(datas, writeSheet, writeTable).finish();

		return fileProxyPath + fileName;

//		Date date = new Date();
//		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
//		String dateStr = format.format(date);
//		String fileName = "";
//		if(fileNameIsContainDate){
//			//如果文件名称中带有日期，则不再添加日期
//			fileName = title + ".xlsx";
//		}else{
//			fileName = title + "_" + dateStr + ".xlsx";
//		}
//		String fileResName =  fileProxyPath + fileName;
//
//		ExcelWriterBuilder writerBuilder = EasyExcel.write();
//		writerBuilder.file(fileSavedPath + fileName);
//		writerBuilder.excelType(ExcelTypeEnum.XLSX);
//		writerBuilder.autoCloseStream(true);
//
//		writerBuilder.head(convertHead(headList)).sheet(title)
//			.doWrite(convertData(headList, dataList));
//		return fileResName;
	}

	/**
	 * 写excel，动态字段
	 *
	 * @param fileSavedPath 保存的路径名
	 * @param headList
	 * @param dataList
	 */
	public static String exportDynamicColumnWithType(String fileSavedPath, String fileProxyPath, String title,
													 List<ExcelHead> headList, List<Map<String,Object>> dataList,
													 boolean fileNameIsContainDate) throws Exception {
		// 构建文件名
		StringBuilder builder = new StringBuilder(title);
		if (!fileNameIsContainDate) {
			builder.append("_").append(new SimpleDateFormat("yyyMMdd_HHmmss").format(new Date()));
		}
		String fileName = builder.append(".xlsx").toString();

		// 创建Excel表格标题
		List<List<String>> titles = new ArrayList<>();
		List<List<String>> heads = new ArrayList<>();
		for (ExcelHead eh : headList) {
			titles.add(Collections.singletonList(title));
			heads.add(Collections.singletonList(eh.getTitle()));
		}

		// 生成sheet和表
		WriteSheet writeSheet = EasyExcel.writerSheet(title).needHead(Boolean.TRUE).head(titles).build();
		WriteTable writeTable = EasyExcel.writerTable(1).needHead(Boolean.TRUE).head(heads).build();

		// 生成表数据
		List<List<Object>> datas = convertDataWithType(headList, dataList);

		// 定义表样式
		ExcelWriter writer = EasyExcel.write(fileSavedPath + fileName)
			.registerWriteHandler(new ExcelExportFontAndStyleConfig(
				Arrays.asList(0, 1), new WriteCellStyle(), new WriteCellStyle()))
			.registerWriteHandler(new ExcelExportRowHeightConfig())
			.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).build();

		// 写入表数据
		writer.write(datas, writeSheet, writeTable).finish();

		return fileProxyPath + fileName;

//		Date date = new Date();
//		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
//		String dateStr = format.format(date);
//		String fileName = "";
//		if(fileNameIsContainDate){
//			//如果文件名称中带有日期，则不再添加日期
//			fileName = title + ".xlsx";
//		}else{
//			fileName = title + "_" + dateStr + ".xlsx";
//		}
//		String fileResName =  fileProxyPath + fileName;
//
//		ExcelWriterBuilder writerBuilder = EasyExcel.write();
//		writerBuilder.file(fileSavedPath + fileName);
//		writerBuilder.excelType(ExcelTypeEnum.XLSX);
//		writerBuilder.autoCloseStream(true);
//
//		writerBuilder.head(convertHead(headList)).sheet(title)
//			.doWrite(convertDataWithType(headList, dataList));
//		return fileResName;
	}


	/**
	 * 会先删除excel所有sheet，再写入
	 */
	public static <T> void writeSheet(String filePath, String sheetName, Class<T> c, List<T> list) {
		EasyExcel.write(filePath, c).sheet(sheetName).doWrite(list);
	}
	public static <T> List<T> read(String fileName, String sheetName, Class c) {

		List<T> list = new ArrayList();
		EasyExcel.read(fileName, c, new ReadListener<T>() {
			@Override
			public void invoke(T o, AnalysisContext analysisContext) {
				list.add(o);
			}

			@Override
			public void doAfterAllAnalysed(AnalysisContext analysisContext) {

			}
		}).sheet(sheetName).doRead();
		return list;
	}



	private static List<List<String>> convertHead(List<ExcelHead> headList) {
		List<List<String>> list = new ArrayList<>();
		for (ExcelHead head : headList) {
			list.add(Lists.newArrayList(head.getTitle()));
		}
		//沒有搞清楚head的参数为List<List<String>>,用List<String>就OK了
		return list;
	}

	/**
	 * @param headList
	 * @param dataList key为head里的fieldName
	 * @return
	 */
	private static List<List<Object>> convertData(List<ExcelHead> headList, List<Map> dataList) {
		List<List<Object>> result = new ArrayList();
		//对dataList转为easyExcel的数据格式
		for (Map<String, Object> data : dataList) {
			List<Object> row = new ArrayList();
			for (ExcelHead h : headList) {
				Object o = data.get(h.getFieldName());
				//需要对null的处理，比如age的null，要转为-1
				row.add(handler(o, h.getNullValue()));
			}
			result.add(row);
		}
		return result;
	}

	/**
	 * @param headList
	 * @param dataList key为head里的fieldName
	 * @return
	 */
	private static List<List<Object>> convertDataWithType(List<ExcelHead> headList, List<Map<String,Object>> dataList) {
		List<List<Object>> result = new ArrayList();
		//对dataList转为easyExcel的数据格式
		for (Map<String, Object> data : dataList) {
			List<Object> row = new ArrayList();
			for (ExcelHead h : headList) {
				Object o = data.get(h.getFieldName());
				//需要对null的处理，比如age的null，要转为-1
				row.add(handler(o, h.getNullValue()));
			}
			result.add(row);
		}
		return result;
	}

	/**
	 * null值处理
	 *
	 * @param o
	 * @param nullValue
	 * @return
	 */
	private static Object handler(Object o, Object nullValue) {
		return o != null ? o : nullValue;
	}


}
