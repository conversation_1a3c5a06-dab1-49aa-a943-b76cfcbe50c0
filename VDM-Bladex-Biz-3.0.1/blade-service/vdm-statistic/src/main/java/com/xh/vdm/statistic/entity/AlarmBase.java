package com.xh.vdm.statistic.entity;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 报警基本信息
 * @Author: zhouxw
 * @Date: 2023/7/25 13:35
 */
@Data
public class AlarmBase {

	//企业名称
	@ColumnWidth(30)
	@ExcelProperty(value = {"企业名称"}, order = 1)
	private String deptName;
	//企业id
	@ExcelIgnore
	private Long deptId;
	//车牌号
	@ColumnWidth(30)
	@ExcelProperty(value = {"车牌号"}, order = 2)
	private String licencePlate;
	//车牌颜色
	@ExcelIgnore
	private String licenceColor;
	//车牌颜色名称
	@ColumnWidth(30)
	@ExcelProperty(value = {"车牌颜色"}, order = 3)
	private String licenceColorDesc;
	//报警id
	@ExcelIgnore
	private Long alarmId;
	//报警类型
	@ExcelIgnore
	private Integer alarmType;
	//报警名称
	@ColumnWidth(30)
	@ExcelProperty(value = {"报警类型"}, order = 4)
	private String alarmTypeName;
	//报警时间
	@ColumnWidth(30)
	@ExcelProperty(value = {"报警开始时间"}, order = 5)
	private Date alarmTime;
	//报警结束时间
	@ColumnWidth(30)
	@ExcelProperty(value = {"报警结束时间"}, order = 6)
	private Date alarmEndTime;

	//行业类型
	@ExcelIgnore
	private String vehicleUseType;
	//行业类型名称
	@ColumnWidth(30)
	@ExcelProperty(value = {"行业类型"}, order = 7)
	private String vehicleUseTypeDesc;

	//报警等级
	@ColumnWidth(30)
	@ExcelProperty(value = {"报警等级"}, order = 8)
	private String alarmLevel;
	//报警位置
	@ColumnWidth(30)
	@ExcelProperty(value = {"报警位置"}, order = 9)
	private String alarmAddress;
	//报警结束位置
	@ColumnWidth(30)
	@ExcelProperty(value = {"报警结束位置"}, order = 10)
	private String alarmEndAddress;
	//最大速度
	@ColumnWidth(30)
	@ExcelProperty(value = {"最大速度"}, order = 11)
	private Double maxSpeed;
	//速度
	@ColumnWidth(30)
	@ExcelProperty(value = {"速度"}, order = 12)
	private Double speed;
	//限速
	@ColumnWidth(30)
	@ExcelProperty(value = {"限速"}, order = 13)
	private Double limitSpeed;
	//道路名称
	@ColumnWidth(30)
	@ExcelProperty(value = {"道路名称"}, order = 14)
	private String roadName;
	//报警持续时间
	@ColumnWidth(30)
	@ExcelProperty(value = {"报警持续时间"}, order = 15)
	private String duration;

}
