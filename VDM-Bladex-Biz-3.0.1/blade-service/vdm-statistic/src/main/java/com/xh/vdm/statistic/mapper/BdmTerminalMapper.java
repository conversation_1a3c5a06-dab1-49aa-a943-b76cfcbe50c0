package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.BdmTerminal;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-20
 */
public interface BdmTerminalMapper extends BaseMapper<BdmTerminal> {

	/**
	 * 根据赋码值查询设备归属单位
	 * @param deviceNum
	 * @return
	 */
	String getDeptNameByDeviceNum(@Param("deviceNum") String deviceNum);

	/**
	 * 根据给定的部门查询终端总数
	 * @param deptIds
	 * @return
	 */
	Long getTotalTerminalCount(@Param("deptIds") List<Long> deptIds);


	/**
	 * 根据给定的部门查询未赋码终端
	 * @param deptIds
	 * @return
	 */
	Long getNoDeviceNumCount(@Param("deptIds") List<Long> deptIds);
}
