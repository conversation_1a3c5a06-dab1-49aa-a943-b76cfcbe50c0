package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.StatTaskLog;
import com.xh.vdm.statistic.mapper.StatTaskLogMapper;
import com.xh.vdm.statistic.service.IStatTaskLogService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 定时任务执行日志表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-15
 */
@Service
public class StatTaskLogServiceImpl extends ServiceImpl<StatTaskLogMapper, StatTaskLog> implements IStatTaskLogService {

    @Value("${task.process.log.enable:false}")
    private boolean enableProcessLog;

    @Override
    public void addStatTaskProcessLog(String taskType, String content) {
        if(enableProcessLog){
            baseMapper.insertTaskProcessLog(taskType , content);
        }
    }
}
