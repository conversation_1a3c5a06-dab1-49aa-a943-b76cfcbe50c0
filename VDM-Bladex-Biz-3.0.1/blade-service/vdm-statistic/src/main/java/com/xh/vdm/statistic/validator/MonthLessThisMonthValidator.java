package com.xh.vdm.statistic.validator;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.statistic.annotation.MonthLtNow;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @Description: 给定月份小于当前月份 校验器
 * @Author: zhouxw
 * @Date: 2022/9/20 4:51 PM
 */
public class MonthLessThisMonthValidator implements ConstraintValidator<MonthLtNow , String> {

    public static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");

    @Override
    public boolean isValid(String s, ConstraintValidatorContext constraintValidatorContext) {
        if(StringUtils.isBlank(s)){
            return false;
        }
        s = s.replace("-","").replace("/","");
        String nowMonth = sdf.format(new Date());
        return nowMonth.compareTo(s)>0;
    }

    @Override
    public void initialize(MonthLtNow constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

}
