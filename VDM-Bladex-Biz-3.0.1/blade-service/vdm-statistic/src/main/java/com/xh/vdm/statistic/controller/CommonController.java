package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.statistic.entity.BamThirdPartyPlatform;
import com.xh.vdm.statistic.entity.BdmVehicleDriver;
import com.xh.vdm.statistic.entity.DriverNameAndIdCard;
import com.xh.vdm.statistic.service.IBamThirdPartyPlatformService;
import com.xh.vdm.statistic.service.IBdmVehicleDriverService;
import com.xh.vdm.statistic.utils.R;
import com.xh.vdm.statistic.vo.response.VehicleOwner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/2/4 15:45
 */
@RestController
@RequestMapping("/bt/statistics/common")
@Slf4j
public class CommonController {
    @Resource
    private IBamThirdPartyPlatformService vehicleOwnerService;


	@Resource
	private IBdmVehicleDriverService driverService;

    /*
     * 查询上级平台列表
     * @return
	*/
    @GetMapping("/findVehicleOwner")
    public R<List<VehicleOwner>> findVehicleOwner(){
        List<BamThirdPartyPlatform> list = vehicleOwnerService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled,0).eq(BamThirdPartyPlatform::getIsTop, 1));
        List<VehicleOwner> ownerList = new ArrayList<>();
        //添加 非营运车辆
        VehicleOwner ownerTmp = new VehicleOwner();
        ownerTmp.setId(0L);
        ownerTmp.setName("非营运车辆");
        ownerList.add(ownerTmp);
        list.forEach(item -> {
            VehicleOwner owner = new VehicleOwner();
            owner.setId(item.getId());
            owner.setName(item.getName());
            ownerList.add(owner);
        });
        return R.data(ownerList);
    }


	/*
	 * 查询系统中的行业类别
	 * @return
	*/
	/*@GetMapping("/findVehicleUseType")
	public R<List<BamDict>> findVehicleUseType(){
		return R.data(dictService.findVehicleUseType());
	}*/


	/*
	 * @description: 根据驾驶员姓名查询驾驶员基本信息
	 * @author: zhouxw
	 * @date: 2022/12/20 5:28 PM
	 * @param: [driverName]
	 * @return: com.xh.vdm.statistic.utils.R<java.util.List<com.xh.vdm.statistic.entity.DriverNameAndIdCard>>
	 */
	@GetMapping("/findDriverBaseInfo")
	public R<List<DriverNameAndIdCard>> findDriverBaseInfo(String driverName){
//		if(StringUtils.isBlank(driverName)){
//			return R.fail("驾驶员姓名不能为空");
//		}
//		List<BdmVehicleDriver> list = driverService.list(int lambdaQuery(BdmVehicleDriver.class).like(BdmVehicleDriver::getName,driverName));
		List<DriverNameAndIdCard> resList = new ArrayList<>();
//		list.forEach(item -> {
//			DriverNameAndIdCard driver = new DriverNameAndIdCard();
//			driver.setDriverName(item.getName());
//			driver.setIdCard(item.getIdcard());
//			resList.add(driver);
//		});
		return R.data(resList);
	}

}
