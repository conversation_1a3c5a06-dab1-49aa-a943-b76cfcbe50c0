package com.xh.vdm.statistic.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 数据操作相关工具类
 * @Author: zhouxw
 * @Date: 2022/9/5 10:13 AM
 */
@Component
@Slf4j
public class DataUtils {

    @Resource
    private JdbcTemplate jdbcTemplate;

    /**
     * @description:
     * @author: zhouxw
     * @date: 2022/9/5 10:14 AM
     * @param: [tableName, tempTableName, list]
     * @return: void
     **/
    private <T> void updateBatchByTempTableWithOneColumn(String tableName , String tempTableName , List<T> list){
        //
    }

    /**
     * @description: 判断表是否存在，如果表不存在，则创建表
     * @author: zhouxw
     * @date: 2022/9/5 10:21 AM
     * @param: [tableName：目标表 ，templateTableName：模板表]
     * @return: void
     **/
    public void checkTableExistAndCreate(String tableName , String templateTableName) throws Exception{
        //1.判断表是否存在
        try{
            jdbcTemplate.queryForList("select * from " + tableName + " limit 1 ");
        }catch (Exception e){
            log.info("表[{}]不存在，将创建该表" , tableName);
            try{
                jdbcTemplate.execute("create table " + tableName +" (like " + templateTableName+" INCLUDING INDEXES INCLUDING DEFAULTS )");
            }catch (Exception ex){
                throw ex;
            }
        }
    }

    /**
     * @description: 清空指定表
     * @author: zhouxw
     * @date: 2022/9/5 11:01 AM
     * @param: [tableName]
     * @return: void
     **/
    public void truncateTable(String tableName){
        jdbcTemplate.execute("truncate table " + tableName);
    }


    /**
     * @description: 对指定的表和字段添加索引
     * @author: zhouxw
     * @date: 2022/9/16 3:25 PM
     * @param: [tableName 表名 , columns 使用 "," 拼接好的字段名称 或者 单个字段名称]
     * @return: void
     **/
    public void createIndex(String tableName , String columns){
        try {
            jdbcTemplate.execute("alter table " + tableName + " add index (" + columns + ")");
        }catch (Exception e){
            log.info("索引已经存在，无需重复创建");
        }
    }
}
