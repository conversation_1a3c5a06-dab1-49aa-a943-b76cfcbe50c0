package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 人员管理
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmPerson implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 从业资格证号
     */
    private String certificateId;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 驾驶员、安全员、押运员、监管员
     */
    private String jobType;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 不可输入，从已有企业中选择
     */
    private Long deptId;

    /**
     * 自动从身份证号码中获取，可编辑
     */
    private Date birthdate;

    private String name;

    /**
     * 男、女，根据身份证号计算
     */
    private String sex;

    /**
     * 学历
     */
    private String education;

    /**
     * 民族
     */
    private String nation;

    /**
     * 住址
     */
    private String address;

    /**
     * 籍贯
     */
    private Integer region;

    /**
     * 个人照片，照片url
     */
    private String photoUrl;

    /**
     * 默认自动带入身份证号，可编辑
            驾驶员必填
     */
    private String driveLicense;

    /**
     * 驾驶证档案编号，驾驶员必填
     */
    private String driveArchive;

    /**
     * 准假车型，驾驶员必填，A1-大型客车、A2-牵引车、A3-城市公交车、B1-中型客车、B2-大型货车、C1-小型汽车、C2-小型自动挡汽车、C3-低速载货汽车
     */
    private String vehicleType;

    /**
     * 驾驶证初次领证日期，驾驶员必填
     */
    private Date licenceGetDate;

    /**
     * 驾驶证有效期开始日期，驾驶员必填
     */
    private Date licenceStartDate;

    /**
     * 驾驶证有效期限，驾驶员必填
     */
    private Date licenceExpire;

    /**
     * 驾驶证发证机构，驾驶员必填
     */
    private String licenceOrg;

    /**
     * 驾驶证图片
     */
    private String licencePhoto;

    /**
     * 从业资格证图片
     */
    private String certificatePhoto;

    /**
     * 从业资格证类别
     */
    private String certificateType;

    /**
     * 从业资格证发证机构
     */
    private String certificateOrg;

    /**
     * 从业资格证有效起始时间
     */
    private Date certificateStartDate;

    /**
     * 从业资格证有效期限
     */
    private Date certificateExpire;

    /**
     * 是否删除
     */
    private Boolean isDel;

    /**
     * 创建人
     */
    private String createUserId;

    /**
     * 创建部门
     */
    private String createDeptId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private Integer centerId;

    private String personId;

    private String busyArea;

    private String busyType;

    private Integer age;

    private Date licencePubDate;

    private Date certificateGetDate;

    private Date yearVerifyDate;

    private String superviseOrg;

    private String supervisePhone;

    private String serviceStar;

    private String taxiCompany;

    private String uniqueLabel;

    private String bgTitle;


}
