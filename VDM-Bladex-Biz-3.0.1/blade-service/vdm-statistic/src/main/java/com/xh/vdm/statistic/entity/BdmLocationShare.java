package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "bdm_location_share", autoResultMap = true)
public class BdmLocationShare implements Serializable {

	@TableField(value = "id")
	private Long id;

	@TableField(value = "target_type")
	private Byte targetType;

	@TableField(value = "target_category")
	private Short targetCategory;

	@TableField(value = "target_id")
	private Long targetId;

	@TableField(value = "dept_id")
	private Long deptId;

	@TableField(value = "quantity")
	private Long quantity;

}

