package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.CacheSegLimitSpeedTerminalResponse;
import com.xh.vdm.statistic.mapper.CacheSegLimitSpeedTerminalMapper;
import com.xh.vdm.statistic.mapper.StatisticsMapper;
import com.xh.vdm.statistic.service.ICacheSegLimitSpeedTerminalService;
import com.xh.vdm.statistic.vo.request.SegLimitSpeedTerminalRequest;
import com.xh.vdm.statistic.vo.response.SegLimitSpeedTerminalResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 终端限速服务类
 *
 * <AUTHOR>
 * @since 2022-09-14
 */
@Service
@Slf4j
public class CacheSegLimitSpeedTerminalServiceImpl extends ServiceImpl<CacheSegLimitSpeedTerminalMapper, CacheSegLimitSpeedTerminalResponse> implements ICacheSegLimitSpeedTerminalService {


    @Resource
    private StatisticsMapper statisticsMapper;

    @Resource
    private CacheSegLimitSpeedTerminalMapper mapper;

    @Override
    @Transactional
    public void statisticsSegLimitSpeedTerminal(Long startTime, Long endTime) throws Exception {

        Date startDate = new Date();
        startDate.setTime(startTime );
        Date endDate = new Date();
        endDate.setTime(endTime );

        //1.删除指定时间段的数据
        Long count = mapper.deleteByDateDuration(startDate, endDate);
        log.info("删除指定时间段的数据成功，共删除{}条数据",count==null?0:count);

        //2.统计指定时间段的数据
        List<CacheSegLimitSpeedTerminalResponse> list = statisticsMapper.getSegLimitSpeedTerminalWithTime(startTime/1000, endTime/1000);
        //添加创建时间
        for(CacheSegLimitSpeedTerminalResponse cache : list){
            cache.setCreateTime(new Date());
        }

        boolean saveFlag = this.saveBatch(list);
        if(saveFlag){
            log.info("统计数据保存成功，共保存{}条数据",list==null?0:list.size());
        }
    }


    @Override
    public IPage<SegLimitSpeedTerminalResponse> querySegLimitSpeedTerminalCache(SegLimitSpeedTerminalRequest req) throws Exception {

        //查询车辆地图超速DB缓存
        Page<SegLimitSpeedTerminalResponse> page = new Page<>();
        page.setCurrent(req.getCurrent());
        page.setSize(req.getSize());
        IPage<SegLimitSpeedTerminalResponse> pageList = mapper.getList(page, req);
        return pageList;
    }

}
