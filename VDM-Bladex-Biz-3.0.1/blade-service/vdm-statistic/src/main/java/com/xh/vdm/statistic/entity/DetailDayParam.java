package com.xh.vdm.statistic.entity;

import lombok.Data;

/**
 * @Description: 明细查询条件
 * @Author: zhouxw
 * @Date: 2022/9/8 4:26 PM
 */
@Data
public class DetailDayParam {

    //统计日期：yyyy-MM-dd
    private String day;
    //统计日期：yyyyMM
    private String monthStrict;
    //上级平台
    private Long ownerId;
    //企业ID
    private Long deptId;
    //车牌号
    private String licencePlate;
    //车牌颜色
    private Integer plateColor;

    //分页条件：当前页数
    private int start;
    //分页条件：每页大小
    private int size;
}
