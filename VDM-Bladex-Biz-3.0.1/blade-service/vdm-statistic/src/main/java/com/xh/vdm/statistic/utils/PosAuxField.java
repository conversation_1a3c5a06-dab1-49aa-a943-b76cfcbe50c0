package com.xh.vdm.statistic.utils;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.statistic.dto.TrackAux;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.Base64;
@Component
@Slf4j
public class PosAuxField {
	public byte wireless;
	public byte gnssNum;
	public short ioState;
	public byte charge;

	public PosAuxField() {
	}

	public static PosAuxField getAuxField(String str,Integer iotProtocol) {
		if (iotProtocol==1){
			return getRnssAuxField(str);
		}
		if (iotProtocol==2){
			return getWearAuxField(str);
		}
		return null;
	}
	public static PosAuxField getRnssAuxField(String str) {
		PosAuxField field = new PosAuxField();
		byte[] bts = hexStringToByteArray(str);
		ByteBuffer buf = ByteBuffer.wrap(bts).order(ByteOrder.BIG_ENDIAN);

		while (buf.hasRemaining()) {
			// 附加项id
			byte auxId = buf.get();
			// 附加项长度
			byte auxLen = buf.get();

			switch (auxId) {
				case 0x30:
					field.wireless = buf.get();
					break;
				case 0x31:
					field.gnssNum = buf.get();
					break;
				case 0x2A:
					field.ioState = buf.getShort();
					break;
				case (byte) 0xf7:
					field.charge = buf.get();
					break;
				default:
					int auxLenInt=auxLen&0xff;
					byte[] value = new byte[auxLenInt];
					buf.get(value);
					break;
			}
		}
		return field;
	}

	public static PosAuxField getWearAuxField(String str) {
		try {
			PosAuxField field = new PosAuxField();
			TrackAux aux=JSON.parseObject(str, TrackAux.class);
			field.wireless = aux.getWireless();
			field.gnssNum = aux.getSateNum();
			field.charge=aux.getBatteryLevel();
			return field;
		} catch (Exception e) {
			log.error("解析JSON时出错: " + e.getMessage());
			return null;
		}
	}

	private static byte[] hexStringToByteArray(String s) {
		int len = s.length();
		byte[] data = new byte[len / 2];
		for (int i = 0; i < len; i += 2) {
			data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
				+ Character.digit(s.charAt(i + 1), 16));
		}
		return data;
	}
}
