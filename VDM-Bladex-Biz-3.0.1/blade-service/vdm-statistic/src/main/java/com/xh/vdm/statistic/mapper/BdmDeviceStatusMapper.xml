<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmDeviceStatusMapper">

    <sql id="deptBaseInfo">
        <if test="userId != null">
            join bdm_user_dept_regulates usr on usr.dept_id = ds.dept_id and usr.user_id = #{userId}
        </if>
    </sql>

    <select id="queryAll" resultType="com.xh.vdm.statistic.entity.BdmDeviceStatus">
        SELECT
            ds.id,
            ds.device_id,
            ds.device_type,
            ds.unique_id,
            ds.device_num,
            ds.target_id,
            ds.target_type,
            ds.target_name,
            ds.action,
            ds.action_time,
            ds.fault_count,
            ds.dept_id
        FROM bdm_device_status ds
        left join bdm_abstract_device bad on ds.unique_id = bad.unique_id
        WHERE 1=1
        <if test="deviceNum != null and deviceNum != ''">
            AND ds.device_num LIKE CONCAT('%', #{deviceNum}, '%')
        </if>
        <if test="targetName != null and targetName != ''">
            AND ds.target_name LIKE CONCAT('%', #{targetName}, '%')
        </if>
        <if test="startTime != null and endTime != null">
            and ds.action_time between #{startTime} and #{endTime}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
        <if test="runningStatus != null">
        <choose>
            <when test="runningStatus == 0">
                and ds.fault_count = 0
            </when>
            <otherwise>
                and ds.fault_count > 0
            </otherwise>
        </choose>
        </if>
        ORDER BY ds.action_time DESC
        <if test="query.current gt 0 and query.size gt 0">
            LIMIT ${query.size} OFFSET ${(query.current - 1) * query.size}
        </if>
    </select>

    <select id="count" resultType="java.lang.Long">
        SELECT count(*) from bdm_device_status ds
        left join bdm_abstract_device bad on ds.unique_id = bad.unique_id
        where 1 = 1
        <if test="deviceNum != null and deviceNum != ''">
            and ds.device_num like concat('%', #{deviceNum}, '%')
        </if>
        <if test="targetName != null and targetName != ''">
            and ds.target_name like concat('%', #{targetName}, '%')
        </if>
        <if test="runningStatus == 0">
           AND ds.fault_count = 0
        </if>
        <if test="runningStatus == 1">
            AND ds.fault_count != 0
        </if>
        <if test="startTime != null and endTime != null">
            and ds.action_time between #{startTime} and #{endTime}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
    </select>

    <select id="getAlarmPage" resultType="com.xh.vdm.statistic.vo.response.AlarmResponse">
        select * from alarm
        where 1 = 1
        and device_id = #{deviceId}
        and device_type = #{deviceType}
        and type in
        <foreach collection="typeList" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        <if test="time != null">
            AND start_time &gt;= #{time}
        </if>
        order by start_time desc
        LIMIT ${query.size} OFFSET ${(query.current - 1) * query.size}
    </select>

    <select id="getAlarmCount" resultType="java.lang.Long">
        select count(*) from alarm
        where 1 = 1
        and device_id = #{deviceId}
        and device_type = #{deviceType}
        and type in
        <foreach collection="typeList" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        <if test="time != null">
            AND start_time &gt;= #{time}
        </if>
    </select>

    <select id="getAlarmPageByAction" resultType="com.xh.vdm.statistic.vo.response.AlarmResponse">
        select * from alarm
        where 1 = 1
        and device_id = #{deviceId}
        and device_type = #{deviceType}
        and type in
        <foreach collection="typeList" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        <if test="time != null and startTime !=null">
            AND start_time between #{startTime} and #{time}
        </if>
        order by start_time desc
        LIMIT ${query.size} OFFSET ${(query.current - 1) * query.size}
    </select>

    <select id="getAlarmPageByActionCount" resultType="java.lang.Long">
        select count(*) from alarm
        where 1 = 1
        and device_id = #{deviceId}
        and device_type = #{deviceType}
        and type in
        <foreach collection="typeList" item="type" open="(" separator="," close=")">
            #{type}
        </foreach>
        <if test="time != null and startTime !=null">
            AND start_time between #{startTime} and #{time}
        </if>
    </select>

</mapper>

