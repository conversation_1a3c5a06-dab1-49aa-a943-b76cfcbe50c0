package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.vo.request.DeptAndDurationNoPageRequest;
import com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest;

import java.util.List;

/**
 * <p>
 * 车辆行驶时长表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
public interface IStatVehTravelDurationService extends IService<StatVehTravelDuration> {

    /**
     * @description: 车辆行驶时长统计
     * @author: zhouxw
     * @date: 2022/11/7 5:25 PM
     * @param: [day]
     * @return: boolean
     **/
    boolean vehTravelDurationStat(String day) throws Exception;

    /**
     * @description: 根据条件查询指定时间内行驶的车辆数量信息
     * @author: zhouxw
     * @date: 2022/11/9 3:36 PM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleTravelDurationNode>
     **/
    List<VehicleTravelDurationNode> findVehicleTravelDurationCountByCondition(VehicleTravelDurationRequest request);

    /**
     * @description: 根据条件查询客车夜间行驶的数量
     * @author: zhouxw
     * @date: 2022/11/10 1:47 PM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
     **/
    List<DateAndCount> findPassengerVehCountInNight(VehicleTravelDurationRequest request);

    /**
     * @description: 根据条件查询平均行驶时长
     * 平均行驶时长 = 总行驶时长 / 总上线车辆数
     * @author: zhouxw
     * @date: 2022/11/10 3:46 PM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndData>
     **/
    List<DateAndData> findAverageTravelDurationByDay(VehicleTravelDurationRequest request);

    /**
     * @description: 按天统计企业行驶总里程
     * @author: zhouxw
     * @date: 2022/11/10 4:55 PM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.TotalMileageNode>
     **/
    List<TotalMileageNode> findTotalMileageByDay(VehicleTravelDurationRequest request);

    /**
     * @description: 查询企业每天的总里程
     * @author: zhouxw
     * @date: 2022/11/21 9:55 AM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.TotalMileageNodeForDept>
     **/
    List<TotalMileageNodeForDept> findTotalMileageByDayForDept(VehicleTravelDurationRequest request);

    /**
     * @description: 根据日期查询行驶车辆数
     * @author: zhouxw
     * @date: 2022/11/27 12:26 AM
     * @param: [date: yyyy-MM-dd , month:yyyyMM]
     * @return: int
     **/
    int findTravelVehicleCountByDate(String month , String date , Long deptId, Long vehicleOwnerId) throws Exception;

    /**
     * @description: 根据日期查询企业总行驶里程
     * @author: zhouxw
     * @date: 2022/11/27 12:48 AM
     * @param: [month: yyyyMM； date: 日期下表，比如10号，就填10]
     * @return: long
     **/
    double findCompanyTotalMileageByDate(String month , String dateIndex , Long deptId) throws Exception;

    /**
     * @description: 查询车辆在线情况
     * @author: zhouxw
     * @date: 2022/11/27 10:54 PM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
     **/
    List<DateAndCount> findVehicleOnlineCount(DeptAndDurationNoPageRequest request) throws Exception;

    /**
     * @description: 查询在某个月指定时间段内车辆上线运行的总次数
     * @author: zhouxw
     * @date: 2022/11/29 4:11 PM
     * @param: [request]
     * @return: long
     **/
    int findVehicleTravelCount(VehicleTravelDurationRequest request) throws Exception;

    /**
     * @description: 查询在某个月内部门车辆上线次数
     * @author: zhouxw
     * @date: 2022/11/29 4:43 PM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DeptAndDateAndData>
     **/
    List<DeptAndDateAndCount> findVehicleTravelCountForDept(VehicleTravelDurationRequest request) throws Exception;


}
