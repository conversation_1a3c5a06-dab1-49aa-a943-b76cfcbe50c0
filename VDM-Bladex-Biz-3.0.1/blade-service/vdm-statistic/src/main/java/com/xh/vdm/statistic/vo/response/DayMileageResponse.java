package com.xh.vdm.statistic.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/1 10:19
 */
@ApiModel(value = "车辆日里程统计查询")
public class DayMileageResponse {
    @ApiModelProperty(value = "车队名称")
    @JsonProperty("dept_name")
    private String deptName;

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("enterprise")
    private String enterprise;

    @ApiModelProperty(value = "车牌号码")
    @JsonProperty("licence_plate")
    private String licencePlate;

    @ApiModelProperty(value = "车牌颜色")
    @JsonProperty("licence_color")
    private String licenceColor;

    @ApiModelProperty(value = "车辆类型")
    @JsonProperty("vehicle_model")
    private String vehicleModel;

    @ApiModelProperty(value = "车辆归属")
    @JsonProperty("vehicle_owner")
    private String vehicleOwner;

    @ApiModelProperty(value = "日里程")
    @JsonProperty("day_mileage")
    private List<Double> day_mileage;

    @ApiModelProperty(value = "总里程")
    @JsonProperty("total_mileage")
    private Double totalMileage;

    @ApiModelProperty(value = "车辆接入方式")
    @JsonProperty("access_mode")
    private String accessMode;

    public String getAccessMode() {
        return accessMode;
    }

    public void setAccessMode(String accessMode) {
        this.accessMode = accessMode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getEnterprise() {
        return enterprise;
    }

    public void setEnterprise(String enterprise) {
        this.enterprise = enterprise;
    }

    public String getLicencePlate() {
        return licencePlate;
    }

    public void setLicencePlate(String licencePlate) {
        this.licencePlate = licencePlate;
    }

    public String getLicenceColor() {
        return licenceColor;
    }

    public void setLicenceColor(String licenceColor) {
        this.licenceColor = licenceColor;
    }

    public String getVehicleModel() {
        return vehicleModel;
    }

    public void setVehicleModel(String vehicleModel) {
        this.vehicleModel = vehicleModel;
    }

    public String getVehicleOwner() {
        return vehicleOwner;
    }

    public void setVehicleOwner(String vehicleOwner) {
        this.vehicleOwner = vehicleOwner;
    }

    public List<Double> getDay_mileage() {
        return day_mileage;
    }

    public void setDay_mileage(List<Double> day_mileage) {
        this.day_mileage = day_mileage;
    }

    public Double getTotalMileage() {
        return totalMileage;
    }

    public void setTotalMileage(Double totalMileage) {
        this.totalMileage = totalMileage;
    }
}
