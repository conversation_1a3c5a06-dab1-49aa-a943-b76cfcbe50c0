<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.CacheSecurityInfoMapper">


    <select id="getList" resultType="com.xh.vdm.statistic.vo.response.SecurityInfoResponse">
        select enterprise, dept_name, licence_plate, licence_color, vehicle_model, vehicle_owner, send_time, send_message, send_state, monitor_person, access_mode
        from cache_security_info
        where 1 = 1
        <if test= "param.licencePlate != null and param.licencePlate !='' ">
            AND licence_plate = #{param.licencePlate}
        </if>
        <if test= "param.licenceColor != null and param.licenceColor != '' ">
            AND licence_color_code = #{param.licenceColor}
        </if>
        <if test= 'param.startTime != null'>
            AND send_time &gt;= to_timestamp(#{param.startTime})
        </if>
        <if test= 'param.endTime != null'>
            AND send_time &lt;= to_timestamp(#{param.endTime})
        </if>
        <if test= "param.vehicleOwnerId != null">
            AND vehicle_owner_id = #{param.vehicleOwnerId}
        </if>
        <if test= "param.accessMode != null and param.accessMode != ''">
            AND access_mode_code = #{param.accessMode}
        </if>
        <if test= "param.deptList != null and param.deptList.size() != 0">
            AND dept_id IN
            <foreach collection="param.deptList" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="param.professionList != null and param.professionList.size() > 0">
            AND vehicle_model_code IN
            <foreach collection="param.professionList" item="profession" index="index" open="(" close=")" separator=",">
                #{profession}
            </foreach>
        </if>
        order by send_time desc
    </select>

</mapper>
