package com.xh.vdm.statistic.entity;

import lombok.Data;

import java.util.Date;
import java.io.Serializable;

/**
 * (BdmDeviceOnline)实体类
 */
@Data
public class BdmDeviceOnline implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private Long deviceId;

	private Integer deviceType;

	private String uniqueId;

	private String deviceNum;

	private Date startTime;

	private Date endTime;

	private Long targetId;

	private Integer targetType;

	private String targetName;

}

