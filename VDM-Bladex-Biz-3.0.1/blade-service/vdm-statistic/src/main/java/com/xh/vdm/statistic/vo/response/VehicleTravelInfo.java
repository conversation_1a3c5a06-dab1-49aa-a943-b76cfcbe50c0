package com.xh.vdm.statistic.vo.response;

import com.xh.vdm.statistic.entity.DateAndComplete;
import com.xh.vdm.statistic.entity.DateAndDrift;
import com.xh.vdm.statistic.entity.DateAndLocationQuality;
import lombok.Data;

import java.util.List;

/**
 * @Description: 车辆运行趋势
 * @Author: zhouxw
 * @Date: 2022/9/13 1:30 PM
 */
@Data
public class VehicleTravelInfo {

    //完整率
    private List<DateAndComplete> completeRate;
    //合格率
    private List<DateAndLocationQuality> qualifiedRate;
    //漂移率
    private List<DateAndDrift> driftRate;
}
