package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.dto.TargetAlarmDTO;
import com.xh.vdm.statistic.dto.alarm.AlarmCount;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.impala.ImpalaAlarm;
import com.xh.vdm.statistic.vo.request.CommonBaseImpalaAlarmRequest;
import com.xh.vdm.statistic.vo.request.CommonStatRequest;
import com.xh.vdm.statistic.vo.request.alarm.CarAlarmRequest;
import com.xh.vdm.statistic.vo.request.alarm.CommonAlarmStatRequest;
import com.xh.vdm.statistic.vo.request.alarm.DriverAlarmRequest;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.entity.DataAuthCE;
import org.springblade.system.vo.DictTreeNodeVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 报警统计
 */
public interface IAlarmService {

	/**
	 * 根据时间段查询每日报警数量
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	List<DateAndCount> findAlarmCount(Long startTime, Long endTime, List<Long> deptIds, Long userId);


	/**
	 * 根据时间段查询每小时报警数量
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	List<DateAndCount> findAlarmCountHour(Long startTime, Long endTime, List<Long> deptIds, Long userId);



	/**
	 * 根据时间段查询每日报警处理数量
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param userId
	 * @param userType
	 * @return
	 */
	List<DateAndCount> findAlarmHandleCount(Long startTime, Long endTime, List<Long> deptIds, Long userId, String userType);


	/**
	 * 根据时间段查询报警处理数量
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param userId
	 * @param userType
	 * @return
	 */
	long findAlarmHandleCountTotal(Long startTime, Long endTime, List<Long> deptIds, Long userId, String userType);


	/**
	 * 根据时间段查询每小时报警处理数量
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param userId
	 * @param userType
	 * @return
	 */
	List<DateAndCount> findAlarmHandleCountHour(Long startTime, Long endTime, List<Long> deptIds, Long userId, String userType);


	/**
	 * @description: 统计报警数和处理数
	 * 数据覆盖更新，只保留最新运行的结果
	 * @author: zhouxw
	 * @date: 2023-07-202 13:44:48
	 * @param: [date 当日的日期 yyyyMMdd，比如 20230702，表示20230702之前30天内的时间，不包含 20230702]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.AlarmCountAndRateWithUser>
	 **/
	void statCountAndRateIn30Day(String date) throws Exception;

	/**
	 * @description: 查询指定日期和超过指定时长的疲劳驾驶报警数量
	 * @author: zhouxw
	 * @date: 2023-07-206 08:36:41
	 * @param: [startDate 开始时间 yyyy-MM-dd, endDate 结束时间 yyyy-MM-dd, duration 超过的秒数]
	 * @return: long
	 **/
	long findFatigueCountByDateAndDuration(String startDate, String endDate, long duration, List<Long> deptIds, Long userId) throws Exception;

	/**
	 * @description: 查询指定日期和超过指定时长的疲劳驾驶报警处理数量
	 * @author: zhouxw
	 * @date: 2023-07-206 08:36:41
	 * @param: [startDate 开始时间 yyyy-MM-dd, endDate 结束时间 yyyy-MM-dd, duration 超过的秒数, userType 用户类型(1 服务商  2 第三方  3 企业)]
	 * @return: long
	 **/
	long findFatigueHandleCountByDateAndDuration(String startDate, String endDate, long duration, List<Long> deptIds, Long userId, String userType) throws Exception;






	/**
	 * @description: 查询指定日期的紧急报警数
	 * @author: zhouxw
	 * @date: 2023-07-206 11:06:07
	 * @param: [startDate: 开始时间 yyyy-MM-dd, endDate: 结束时间 yyyy-MM-dd, deptIds: 部门id, userId: 用户id]
	 * @return: long
	 **/
	long findAlarmCountByDate(List<Integer> alarmTypes, String startDate, String endDate, List<Long> deptIds, Long userId) throws Exception;

	/**
	 * @description: 查询指定日期的紧急报警处理数
	 * @author: zhouxw
	 * @date: 2023-07-206 11:17:45
	 * @param: [startDate: 开始时间 yyyy-MM-dd, endDate: 结束时间 yyyy-MM-dd, deptIds: 部门id, userId: 用户id, userTYpe: 用户类型]
	 * @return: long
	 **/
	long findAlarmHandleCountByDate(List<Integer> alarmTypes, String startDate, String endDate, List<Long> deptIds, Long userId, String userType) throws Exception;


	/**
	 * @description: 查询指定日期的报警基础信息
	 * @author: zhouxw
	 * @date: 2023-07-206 11:06:07
	 * @param: [startDate: 开始时间 yyyy-MM-dd, endDate: 结束时间 yyyy-MM-dd, deptIds: 部门id, userId: 用户id]
	 * @return: long
	 **/
	List<AlarmBase> findAlarmBaseCountByDate(List<Integer> alarmTypes, String startDate, String endDate, List<Long> deptIds, Long userId) throws Exception;

	/**
	 * @description: 查询已经处理过的报警数量
	 * @author: zhouxw
	 * @date: 2023-07-206 11:17:45
	 * @param: [alarmIds: 指定的报警id列表, userTYpe: 用户类型]
	 * @return: long
	 **/
	long findAlarmHandleCountByAlarmIds(List<Long> alarmIds, String userType) throws Exception;

	/**
	 * 分页查询实时未处理的报警
	 * @param deptIds
	 * @param userId
	 * @param userType
	 * @return
	 * @throws Exception
	 */
	IPage<BdmSecurity> findUnHandleRealTimeAlarmByPage(List<String> alarmTypeList, List<Long> deptIds, Long userId, String userType, IPage<BdmSecurity> page) throws Exception;

	/**
	 * 超速分页
	 */
	IPage<ImpalaAlarm> overSpeedPage (CommonAlarmStatRequest request, Query query);

	/**
	 * 超速列表
	 */
	List<ImpalaAlarm> overSpeedList (CommonAlarmStatRequest request);

	/**
	 * 疲劳驾驶分页
	 */
	IPage<ImpalaAlarm> fatigueDrivePage (CommonStatRequest request, Query query);

	/**
	 * 疲劳驾驶列表
	 */
	List<ImpalaAlarm> fatigueDriveList (CommonStatRequest request);

	/**
	 * 夜间异动分页
	 */
	IPage<ImpalaAlarm> nightDrivePage (CommonStatRequest request, Query query);

	/**
	 * 夜间异动列表
	 */
	List<ImpalaAlarm> nightDriveList (CommonStatRequest request);

	/**
	 * 误报报警分页
	 */
	IPage<ImpalaAlarm> wrongPage (CommonAlarmStatRequest request, Query query);

	/**
	 * 误报报警列表
	 */
	List<ImpalaAlarm> wrongList (CommonAlarmStatRequest request);

	/**
	 * 报警统计
	 */
	List<AlarmCount> getCarAlarmStatList (CarAlarmRequest request);

	/**
	 * 报警分析
	 */
	List<ImpalaAlarm> getCarAlarmAnalysisList (CarAlarmRequest request);

	/**
	 * 驾驶员报警统计
	 */
	List<AlarmCount> getDriverAlarmStatList (DriverAlarmRequest request);

	/**
	 * 驾驶员报警分析
	 */
	List<ImpalaAlarm> getDriverAlarmAnalysisList (DriverAlarmRequest request);

	/**
	 * 报警处理率分页列表
	 */
	IPage<Map<String, Long>> getDealRatePage (CommonStatRequest request, Query query);

	/**
	 * 报警处理率列表
	 */
	List<Map<String, Long>> getDealRateList (CommonStatRequest request);

	/**
	 * 根据时间段获取某辆车的定位数据
	 */
	List<Location> getLocationFromTimeSeg (int licenceColor, String licencePlate, long startTime, long endTime);

	/**
	 * 获取单位的车辆数
	 */
	int getNumCarForDept (CommonStatRequest request);

	/**
	 * 获取的报警数
	 */
	int getNumAlarmForDept (CommonStatRequest request);

	/**
	 * 查询超速报警数
	 */
	int getNumOverSpeed (CommonStatRequest request);

	/**
	 * 查询疲劳驾驶报警数
	 */
	int getNumFatigueDrive (CommonStatRequest request);

	/**
	 * 查询夜间异动查询报警数
	 */
	int getNumNightDrive (CommonStatRequest request);

	/**
	 * 查询车辆在线未定位记录数
	 */
	int getNumNoPosition (CommonStatRequest request);

	/**
	 * 获取车辆离线位移记录数
	 */
	int getNumOfflineMove (CommonStatRequest request);

	/**
	 * 获取单位特定报警类型的车辆排行
	 */
	List<AlarmCount> getCarRankOfAlarmType (CommonAlarmStatRequest request);

	/**
	 * 获取车辆报警各处理措施统计
	 */
	List<AlarmCount> getNumEachCarDeal (CommonAlarmStatRequest request);

	/**
	 * 获取单位在线未定位的车辆排行
	 */
	List<AlarmCount> getCarRankOfNoPosition (CommonStatRequest request);

	/**
	 * 获取单位离线位移的车辆排行
	 */
	List<AlarmCount> getCarRankOfOfflineMove (CommonStatRequest request);

	/**
	 * 根据deptId查询报警数量
	 * @param deptIds
	 * @param date yyyyMMdd
	 * @return
	 * @throws Exception
	 */
	long findAlarmCountByDeptIds(List<Long> deptIds, String date) throws Exception;

	/**
	 * 根据deptId查询报警数量
	 * @param deptIds
	 * @return
	 * @throws Exception
	 */
	long findAlarmCountByDeptIds(List<Long> deptIds, Long startSecondTimestamp, Long endSecondTimestamp) throws Exception;

	/**
	 * 根据deptId和alarmType查询报警数量
	 * @param deptIds
	 * @param date
	 * @return
	 * @throws Exception
	 */
	long findAlarmCountByDeptIdsAndAlarmType(List<Long> deptIds, List<Integer> alarmTypes, String date) throws Exception;

	/**
	 * 根据deptId和alarmType查询报警数量
	 * @param deptIds
	 * @return
	 * @throws Exception
	 */
	long findAlarmCountByDeptIdsAndAlarmType(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp) throws Exception;


	/**
	 * 根据deptId和alarmType查询报警列表
	 * @param deptIds
	 * @return
	 * @throws Exception
	 */
	List<VehicleAndCount> findAlarmListByDeptIdsAndAlarmType(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp, Integer limit) throws Exception;


	/**
	 * 分页查询报警排行
	 * @param deptIds
	 * @param alarmTypes
	 * @param startSecondTimestamp
	 * @param endSecondTimestamp
	 * @param query
	 * @return
	 * @throws Exception
	 */
	IPage<VehicleAndCount> findAlarmListByDeptIdsAndAlarmTypePage(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp,String licencePlate, Long licenceColor, Query query) throws Exception;

	/**
	 * 根据deptId和alarmType查询发生报警的车辆数
	 * @param deptIds
	 * @param alarmTypes
	 * @param startSecondTimestamp
	 * @param endSecondTimestamp
	 * @return
	 * @throws Exception
	 */
	long findVehicleCountByDeptIdsAndAlarmType(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp) throws Exception;


	/**
	 * 根据deptId和alarmType查询每个驾驶员的报警数量
	 * @param deptIds
	 * @return
	 * @throws Exception
	 */
	List<DriverNameAndCount> findDriverAlarmListByDeptIdsAndAlarmType(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp, Integer limit) throws Exception;

	/**
	 * 根据deptId和alarmType查询每个驾驶员的报警数量，分页
	 * @param deptIds
	 * @return
	 * @throws Exception
	 */
	IPage<DriverNameAndCount> findDriverAlarmListByDeptIdsAndAlarmTypePage(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp,String driverName, Query query) throws Exception;



	/**
	 * @description: 获取报警类型
	 * @author: zhouxw
	 * @date: 2023-06-166 15:27:16
	 * @param: [code, key]
	 * @return: java.lang.String
	 **/
	Set<DictTreeNodeVO> getChildrenFlatAlarmTypes (String code, String key) ;

	/**
	 * 查询报警类型
	 * @param code
	 * @param key
	 * @param tenantId
	 * @return
	 */
	Set<DictTreeNodeVO> getChildrenFlatAlarmTypesByTenantId (String code, String key, String tenantId) ;

	/**
	 * 分页查询报警信息，impala
	 * @param request
	 * @param query
	 * @return
	 * @throws Exception
	 */
	IPage<ImpalaAlarm> findAlarmByCondition(CommonBaseImpalaAlarmRequest request, Query query) throws Exception;


	/**
	 * 获取报警。
	 * @param dataAuthCE
	 * @param startTime
	 * @param endTime
	 * @param query
	 * @return
	 */
	List<TargetAlarmDTO> getAlarms(DataAuthCE dataAuthCE, String tenantId, Long startTime, Long endTime, Query query);
}


