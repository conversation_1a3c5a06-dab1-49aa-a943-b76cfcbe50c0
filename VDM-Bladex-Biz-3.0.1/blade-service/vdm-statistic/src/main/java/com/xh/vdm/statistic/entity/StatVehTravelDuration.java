package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 车辆行驶时长表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatVehTravelDuration implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 车牌号
     */
    private String licencePlate;

    /**
     * 统计日期
     */
    private String statDate;

    /**
     * 00点 到 01点 时间段内 车辆运行的时长，单位为 秒
     */
    private Long h01;

    private Long h02;

    private Long h03;

    private Long h04;

    private Long h05;

    private Long h06;

    private Long h07;

    private Long h08;

    private Long h09;

    private Long h10;

    private <PERSON> h11;

    private <PERSON> h12;

    private <PERSON> h13;

    private Long h14;

    private <PERSON> h15;

    private Long h16;

    private Long h17;

    private Long h18;

    private Long h19;

    private Long h20;

    private Long h21;

    private Long h22;

    private Long h23;

    private Long h24;

    private Long totalDuration;

    /**
     * 数据创建时间
     */
    private Date createTime;

    /**
     * 数据是否有效：0 有效    1 无效
     */
    private Integer isDel;


}
