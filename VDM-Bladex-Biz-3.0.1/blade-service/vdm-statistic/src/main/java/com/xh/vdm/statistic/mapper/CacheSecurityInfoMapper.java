package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.CacheSecurityInfoResponse;
import com.xh.vdm.statistic.vo.request.SecurityInfoRequest;
import com.xh.vdm.statistic.vo.response.SecurityInfoResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 车辆上下线查询
 *
 * <AUTHOR>
 * @since 2022-09-14
 */
public interface CacheSecurityInfoMapper extends BaseMapper<CacheSecurityInfoResponse> {

    /**
     * @description: 查询车辆上下线信息
     * @author: zhouxw
     * @date: 2023-02-40 17:46:00
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    List<SecurityInfoResponse> getList(@Param("param") SecurityInfoRequest param);


    /**
     * @description: 分页查询车辆上下线信息
     * @author: zhouxw
     * @date: 2023-02-40 17:46:00
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    IPage<SecurityInfoResponse> getList(IPage<SecurityInfoResponse> page, @Param("param") SecurityInfoRequest param);


}
