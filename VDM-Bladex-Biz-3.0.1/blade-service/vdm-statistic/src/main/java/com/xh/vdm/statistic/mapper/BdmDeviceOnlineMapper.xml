<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmDeviceOnlineMapper">

    <sql id="deptBaseInfo">
        <if test="userId != null">
            join bdm_user_dept_regulates usr on usr.dept_id = d.dept_id and usr.user_id = #{userId}
        </if>
    </sql>

    <!--查询指定行数据-->
    <select id="queryByPage" resultType="com.xh.vdm.statistic.vo.response.DeviceOnlineResponse">
        select d.*, EXTRACT(epoch FROM (d.end_time - d.start_time)) AS onlineTime, de.dept_name
        from bdm_device_online d
        left join blade_dept  de on de.id = d.dept_id
        left join bdm_abstract_device bad on d.unique_id = bad.unique_id
        where
        1 = 1
        <if test="bdo.deviceId != null">
            and d.device_id = #{bdo.deviceId}
        </if>
        <if test="bdo.deviceType != null">
            and d.device_type = #{bdo.deviceType}
        </if>
        <if test="bdo.uniqueId != null and bdo.uniqueId != ''">
            and d.unique_id like concat('%', #{bdo.uniqueId}, '%')
        </if>
        <if test="bdo.targetName != null and bdo.targetName != ''">
            and d.target_name like concat('%', #{bdo.targetName}, '%')
        </if>
        <if test="bdo.deviceNum != null and bdo.deviceNum != ''">
            and d.device_num like concat('%', #{bdo.deviceNum}, '%')
        </if>
        <if test="bdo.startTime != null">
            and d.start_time &gt;= #{bdo.startTime}
        </if>
        <if test="bdo.endTime != null">
            and d.start_time &lt;= #{bdo.endTime}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and de.id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
        order by d.start_time desc
        <if test="query.current gt 0 and query.size gt 0">
            limit ${query.size} offset ${(query.current - 1) * query.size}
        </if>
    </select>

    <select id="countOnline" resultType="java.lang.Long">
        select count(*) from bdm_device_online d
        left join bdm_abstract_device bad on bad.unique_id = d.unique_id
        where
        1 = 1
        <if test="bdo.deviceId != null">
            and d.device_id = #{bdo.deviceId}
        </if>
        <if test="bdo.deviceType != null">
            and d.device_type = #{bdo.deviceType}
        </if>
        <if test="bdo.uniqueId != null and bdo.uniqueId != ''">
            and d.unique_id like concat('%', #{bdo.uniqueId}, '%')
        </if>
        <if test="bdo.targetName != null and bdo.targetName != ''">
            and d.target_name like concat('%', #{bdo.targetName}, '%')
        </if>
        <if test="bdo.deviceNum != null and bdo.deviceNum != ''">
            and d.device_num like concat('%', #{bdo.deviceNum}, '%')
        </if>
        <if test="bdo.startTime != null">
            and d.start_time &gt;= #{bdo.startTime}
        </if>
        <if test="bdo.endTime != null">
            and d.start_time &lt;= #{bdo.endTime}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
    </select>

    <select id="getOnlineTrendered" resultType="com.xh.vdm.statistic.vo.response.terminal.OnlineCountResponse">
        <![CDATA[
        WITH current_hour_start
                 AS (SELECT CURRENT_DATE + INTERVAL '1 hour' * EXTRACT(HOUR FROM CURRENT_TIME) AS hour_start),
             hourly_buckets AS (SELECT generate_series(
                                               (SELECT hour_start FROM current_hour_start) - INTERVAL '23 hours',
                                               (SELECT hour_start FROM current_hour_start),
                                               '1 hour'
                                           ) AS hour_start,
                                       generate_series(
                                               (SELECT hour_start FROM current_hour_start) - INTERVAL '22 hours',
                                               (SELECT hour_start FROM current_hour_start) +
                                               INTERVAL '1 hour' - INTERVAL '1 second',
                                               '1 hour'
                                           ) AS hour_end),
             device_online_hours AS (SELECT h.hour_start,
                                            h.hour_end,
                                            COUNT(DISTINCT CASE
                                                               WHEN dev_online.end_time IS NULL OR dev_online.end_time > h.hour_end
                                                                   THEN dev_online.device_id
                                                               ELSE NULL
                                                END) AS online_count
                                     FROM hourly_buckets h
                                              LEFT JOIN
                                          bdm_device_online dev_online
                                          ON
                                                      dev_online.start_time < h.hour_end
                                                  AND
                                                      (dev_online.end_time IS NULL OR dev_online.end_time >= h.hour_start)
                                     GROUP BY h.hour_start,
                                              h.hour_end)
        SELECT TO_CHAR(hour_start, 'HH24')                AS hour_label,
               TO_CHAR(hour_end, 'YYYY-MM-DD HH24:MI:SS') AS hour_end_label,
               online_count
        FROM device_online_hours
        ORDER BY hour_end DESC;
        ]]>
    </select>

</mapper>

