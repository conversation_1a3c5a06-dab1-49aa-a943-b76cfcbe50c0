package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName(value = "bdm_location_harvest", autoResultMap = true)
public class BdmLocationHarvest implements Serializable {

	@TableField(value = "id")
	private Long id;

	@TableField(value = "target_type")
	private Byte targetType;

	@TableField(value = "target_category")
	private Short targetCategory;

	@TableField(value = "target_id")
	private Long targetId;

	@TableField(value = "dept_id")
	private Long deptId;

	@TableField(value = "harvest")
	private Long harvest;

	@TableField(value = "month_harvest")
	private Integer monthHarvest;

	@TableField(exist = false)
	private Long total;

	@TableField(exist = false)
	private Long monthTotal;
}

