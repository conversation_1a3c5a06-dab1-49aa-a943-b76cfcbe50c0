package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.vo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 在线率统计请求类
 * <AUTHOR>
 * @date 2021/11/1 10:19
 */
@ApiModel(value = "在线率统计请求类")
@Data
public class OnlineRateRequest extends PageParam {


    @ApiModelProperty(value = "查询开始时间")
    @JsonProperty("start_time")
    private Long startTime;

    @ApiModelProperty(value = "查询结束时间")
    @JsonProperty("end_time")
    private Long endTime;


    @ApiModelProperty(value = "车辆归属")
    @JsonProperty("vehicle_owner")
    private Long vehicleOwnerId;

    @ApiModelProperty(value = "行业类型")
    @JsonProperty("vehicle_use_type")
    private Long vehicleUseType;

    @ApiModelProperty(value = "车辆接入方式")
    @JsonProperty("access_mode")
    private Integer accessMode;

	@JsonProperty("vehicle_id_list")
	private List<Long> vehicleIdList;

	private List<String> dateList;
	private List<String> yearMonthList;

	//租户id
	private String tenantId;
	//登录用户id
	private Long userId;

    //所有车组，包含子车组
    private List<Long> deptList;
	//行业类型
    private List<String> professionList;

}
