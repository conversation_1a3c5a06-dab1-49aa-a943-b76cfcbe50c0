package com.xh.vdm.statistic.mapper;

import com.xh.vdm.statistic.entity.BdmTerminalonlinerecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 车辆上下线记录 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 */
public interface BdmTerminalonlinerecordMapper extends BaseMapper<BdmTerminalonlinerecord> {

	/**
	 * 查询在指定时间段内上过线的记录
	 * 下线时间为空，或者下线时间大于开始时间，而且上线时间小于等于今日最大时间
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	List<BdmTerminalonlinerecord> getOnlineRecordInDuration(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("month") String month);
}
