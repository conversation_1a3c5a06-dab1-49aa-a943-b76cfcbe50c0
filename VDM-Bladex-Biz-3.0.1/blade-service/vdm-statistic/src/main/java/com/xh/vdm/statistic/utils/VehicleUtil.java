package com.xh.vdm.statistic.utils;

import com.alibaba.fastjson.JSONObject;
import com.xh.vdm.statistic.constant.RedisConstants;
import com.xh.vdm.statistic.entity.Vehicle;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 数据字典工具类
 * <AUTHOR>
 * @date Created in 11:39 2021/4/29
 * @description
 */
@Slf4j
@Component
public class VehicleUtil {

    @Autowired
    protected RedisTemplate<String, String> redisTemplate;

    /**
     * 根据车牌号获取车辆信息
     * 添加车牌颜色限定
     * <AUTHOR>
     * @date 2021-04-29 11:43:24
     * @param licencePlate
     * @return
     **/
    public Vehicle acquireVehicleByLicencePlate(String licencePlate, String licenceColor){
        try{
            Object object = redisTemplate.opsForHash().get(RedisConstants.HashKey.VEHICLE_INFO_HASH_KEY,licencePlate+"~"+licenceColor);
            if(null != object){
                String value =(String) object;
                Vehicle vehicle = JSONObject.parseObject(value,Vehicle.class);
                return vehicle;
            }
        }catch (ClassCastException e){
            log.error("{} error, get Vehicle error, typeCode:{}",CodeInfoUtils._FILE_LINE_FUNC_(), licencePlate+"~"+licenceColor);
        }
        return null;
    }

}
