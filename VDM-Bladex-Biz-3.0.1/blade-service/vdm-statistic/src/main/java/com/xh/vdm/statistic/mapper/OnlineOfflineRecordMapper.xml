<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.OnlineOfflineRecordMapper">

    <select id="getLastOnlineDate" resultType="com.xh.vdm.statistic.entity.VehicleOnlineBase">
        select vehicle_id, max(on_line_time) onlineDate
        from bdm_terminalonlinerecord
        where vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
        )
        group by vehicle_id
    </select>


</mapper>
