package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.LastOnline;
import org.apache.ibatis.annotations.Param;

public interface LastOnlineMapper extends BaseMapper<LastOnline> {

	/**
	 * 取所有车辆上下线记录中，某段时间内的部分，添加到最新车辆上下线记录。
	 */
	void insertFromAll (@Param("startTime") String startTime, @Param("endTime") String endTime);

	/**
	 * 清空最新车辆上下线记录。
	 */
	void truncate ();
}
