package com.xh.vdm.statistic.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(value = "获取返回体：车辆每日数据大小")
@Data
@EqualsAndHashCode(callSuper = true)
public class DailyCarMegaByteResponse extends CarMegaByteResponse {

    @JsonProperty("ymd")
    @ApiModelProperty(name = "ymd", value = "日期", example = "2023-01-01", required = true)
    private String ymd;
}
