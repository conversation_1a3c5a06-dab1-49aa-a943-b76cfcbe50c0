package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.config.ExcelDateConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆在线情况抽查返回类
 * <AUTHOR>
 * @date 2021/10/26 13:37
 */
@ApiModel(value = "车辆在线情况抽查返回类")
public class VehicleOnlineResponse {

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("dept_name")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆在线情况抽查", "企业名称"})
    @ColumnWidth(40)
    private String deptName;

    @ApiModelProperty(value = "抽查时间")
    @JsonProperty("check_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty(value = {"车辆在线情况抽查", "抽查时间"}, converter = ExcelDateConverter.class)
    @ColumnWidth(22)
    private Date checkTime;

    @ApiModelProperty(value = "车辆总数")
    @JsonProperty("vehicle_total")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆在线情况抽查", "车辆总数"})
    @ColumnWidth(15)
    private Long vehicleTotal;

    @ApiModelProperty(value = "在线车辆数")
    @JsonProperty("vehicle_online")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆在线情况抽查", "在线车辆数"})
    @ColumnWidth(15)
    private Long vehicleOnline;

    @ApiModelProperty(value = "离线车辆数")
    @JsonProperty("vehicle_offline")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆在线情况抽查", "离线车辆数"})
    @ColumnWidth(15)
    private Long vehicleOffline;

    @ApiModelProperty(value = "上线率")
    @JsonProperty("online_rate")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆在线情况抽查", "上线率"})
    @ColumnWidth(15)
    private String onlineRate;

    @ApiModelProperty(value = "监控人员")
    @JsonProperty("monitor_person")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆在线情况抽查", "监控人员"})
    @ColumnWidth(20)
    private String monitor;

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }


    public Date getCheckTime() {
        return new Date();
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    public Long getVehicleTotal() {
        return vehicleTotal;
    }

    public void setVehicleTotal(Long vehicleTotal) {
        this.vehicleTotal = vehicleTotal;
    }

    public Long getVehicleOnline() {
        return vehicleOnline;
    }

    public void setVehicleOnline(Long vehicleOnline) {
        this.vehicleOnline = vehicleOnline;
    }

    public Long getVehicleOffline() {
        return (vehicleOffline==null?0:vehicleOffline);
    }

    public void setVehicleOffline(Long vehicleOffline) {
        this.vehicleOffline = vehicleOffline;
    }

    public String getOnlineRate() {
        if(vehicleTotal==null||vehicleTotal==0){
            return "0%";
        }else if(vehicleOnline == null||vehicleOnline == 0){
            return "0%";
        }else{
            return  new BigDecimal((float)vehicleOnline*100/vehicleTotal).setScale(2,BigDecimal.ROUND_HALF_UP).toString()+"%";
        }
    }

    public void setOnlineRate(String onlineRate) {
        this.onlineRate = onlineRate;
    }

    public String getMonitor() {
        return monitor;
    }

    public void setMonitor(String monitor) {
        this.monitor = monitor;
    }
}
