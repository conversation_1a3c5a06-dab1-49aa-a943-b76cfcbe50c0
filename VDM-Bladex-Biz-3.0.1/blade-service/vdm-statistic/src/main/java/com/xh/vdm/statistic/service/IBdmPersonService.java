package com.xh.vdm.statistic.service;

import com.xh.vdm.statistic.entity.BdmPerson;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 人员管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface IBdmPersonService extends IService<BdmPerson> {

	/**
	 * 根据
	 * @param idCardOrPhone
	 * @return
	 * @throws Exception
	 */
	List<BdmPerson> findPersonByIdCardOrPhone(String idCardOrPhone, String type) throws Exception;

	/**
	 * 根据身份证号查询人员信息
	 * @param idCard
	 * @return
	 * @throws Exception
	 */
	List<BdmPerson> findPersonByIdCard(String idCard) throws Exception;
}
