package com.xh.vdm.statistic.entity.report;

import com.xh.vdm.statistic.entity.DriverNameAndCount;
import com.xh.vdm.statistic.entity.VehicleAndCount;
import lombok.Data;

import java.util.List;

/**
 * 日报/月报，企业基本情况
 */
@Data
public class ReportSummary {
	//统计日期：yyyy年MM月 / yyyy年MM月dd日
	private String dateStr;

	//企业名称
	private String deptName;

	//车辆总数
	private Long vehicleTotalCount;

	//日期描述：xxxx年xx月xx日
	private String dateDesc;


	//上线车辆数
	private Long vehicleGoOnlineCount;

	//离线车辆数
	private Long vehicleOfflineCount;

	//上线率
	private String goOnlineRateStr;

	//长时间不在线车辆数
	private Long vehicleLongOfflineCount;

	//总报警次数
	private Long alarmTotalCount;

	//超速报警次数
	private Long overSpeedAlarmCount;

	//疲劳报警次数
	private Long tiredAlarmCount;

	//夜间禁行报警次数
	private Long nightAlarmCount;

	//主动安全报警次数
	private Long activeAlarmCount;

	//其他报警次数
	private Long otherAlarmCount;

	//平均单车报警次数
	private Double averageVehicleAlarmCount;

	//平台下发信息数量
	private Long sendMessageCount;

	//长期不在线车辆list前5
	private List<VehicleAndCount> longOfflineVehicleList;

	//超速车辆数
	private Long overSpeedVehicleCount;

	//超速车辆list前5
	private List<VehicleAndCount> overSpeedList;

	//超速驾驶员list前5
	private List<DriverNameAndCount> overSpeedDriverList;

	//疲劳驾驶员list前5
	private List<DriverNameAndCount> fatigueDriverList;

	//夜间禁行list前5
	private List<DriverNameAndCount> nightDriverList;


}
