package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xh.vdm.statistic.vo.response.VehicleOnlineOrOfflineResponse;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 车辆上下线 DB 缓存
 * @Author: zhouxw
 * @Date: 2023/2/8 17:46
 */
@Data
@TableName("cache_vehicle_online_offline")
public class CacheVehicleOnlineOrOfflineResponse extends VehicleOnlineOrOfflineResponse {

    //车组id
    private Long deptId;
    //车牌颜色code
    private String licenceColorCode;
    //车辆类型code（行业类型code）
    private Long vehicleUseTypeCode;
    //车辆归属id
    private Long vehicleOwnerId;
    //车辆接入方式code
    private String accessModeCode;

    //数据创建时间
    private Date createTime;

    @TableField(exist = false)
    private Date checkTime;

    //备注
    private String note;

}
