package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.BdmTrackCalendar;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface BdmTrackCalendarMapper extends BaseMapper<BdmTrackCalendar> {

	List<BdmTrackCalendar> getLocCalendarByObjMonth (
		@Param("targetType") byte targetType,
		@Param("targetId") long targetId,
		@Param("deviceType") byte deviceType,
		@Param("deviceId") long deviceId,
		@Param("time") String time
	);

	Integer list(@Param("deviceId") Long deviceId, @Param("deviceType") Integer deviceType,@Param("targetId") Long targetId, @Param("targetType") Integer targetType, @Param("month") Date month);

}
