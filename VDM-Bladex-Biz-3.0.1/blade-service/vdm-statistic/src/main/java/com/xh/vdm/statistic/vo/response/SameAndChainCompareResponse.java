package com.xh.vdm.statistic.vo.response;

import lombok.Data;

/**
 * @Description: 同比环比分析结果
 * @Author: zhouxw
 * @Date: 2022/11/27 11:30 PM
 */
@Data
public class SameAndChainCompareResponse {

    //车辆数同比
    private Double vehicleCountSame;
    //车辆数环比
    private Double vehicleCountChain;
    //驾驶员数同比
    private Double driverCountSame;
    //驾驶员数环比
    private Double driverCountChain;
    //平均驾驶时长同比
    private Double averageDriveDurationSame;
    //平均驾驶时长环比
    private Double averageDriveDurationChain;
    //平均驾驶天数同比
    private Double averageDriveDaysSame;
    //平均驾驶天数环比
    private Double averageDriveDaysChain;
    //平均驾驶里程同比
    private Double averageDriveMileageSame;
    //平均驾驶里程环比
    private Double averageDriveMileageChain;

    //车辆数同比
    private String vehicleCountSameStr;
    //车辆数环比
    private String vehicleCountChainStr;
    //驾驶员数同比
    private String driverCountSameStr;
    //驾驶员数环比
    private String driverCountChainStr;
    //平均驾驶时长同比
    private String averageDriveDurationSameStr;
    //平均驾驶时长环比
    private String averageDriveDurationChainStr;
    //平均驾驶天数同比
    private String averageDriveDaysSameStr;
    //平均驾驶天数环比
    private String averageDriveDaysChainStr;
    //平均驾驶里程同比
    private String averageDriveMileageSameStr;
    //平均驾驶里程环比
    private String averageDriveMileageChainStr;
}
