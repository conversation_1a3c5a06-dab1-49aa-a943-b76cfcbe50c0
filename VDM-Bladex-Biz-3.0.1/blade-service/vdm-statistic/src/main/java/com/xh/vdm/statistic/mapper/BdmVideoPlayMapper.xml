<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmVideoPlayMapper">

    <select id="getDailyVideoPlay" resultType="com.xh.vdm.statistic.entity.BdmVideoPlay">
        select licence_color, licence_plate, play_start_time, play_end_time
        from bdm_video_playback_record
        where play_start_time &gt;= #{startTime, jdbcType=INTEGER} and play_start_time &lt;= #{endTime, jdbcType=INTEGER}
    </select>
</mapper>
