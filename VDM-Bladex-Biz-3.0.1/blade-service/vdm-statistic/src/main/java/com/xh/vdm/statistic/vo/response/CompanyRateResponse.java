package com.xh.vdm.statistic.vo.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @Description: 企业评分明细
 * @Author: zhouxw
 * @Date: 2022/9/13 3:42 PM
 */
@Data
public class CompanyRateResponse {

    //企业名称
    private String deptName;

    //企业id
    private Long deptId;
    //入网数
    private int intNetCount;
    //车辆上线率
    private String goOnlineRate;
    //轨迹完整率
    private String completeRate;
    //合格率
    private String qualifiedRate;
    //漂移车辆数
    private int driffCount;
    //漂移率
    private String driftRate;
    //统计月份
    private String statDate;

	//上线数
	@JsonIgnore
	private int goOnlineCount;
	//车辆上线率
	@JsonIgnore
	private double goOnlineRateD;

	//轨迹完整率
	@JsonIgnore
	private double completeRateD;

	//合格率
	@JsonIgnore
	private double qualifiedRateD;

	@JsonIgnore
	//漂移率
	private double driftRateD;

}
