package com.xh.vdm.statistic.entity;

import lombok.Data;

/**
 * @Description: 驾驶员驾驶时段对象
 * @Author: zhouxw
 * @Date: 2022/11/16 11:18 PM
 */
@Data
public class DriveDuration {

    //统计日期(yyyy-MM-dd)
    private String statDate;
    //车牌号
    private String licencePlate;
	//车牌颜色
	private Integer licenceColor;
    //驾驶员姓名
    private String driverName;
    //驾驶员身份证号
    private String idCard;
    //驾驶时段开始时间（秒）
    private long startSecondTimestamp;
    //驾驶时段结束时间（秒）
    private long endSecondTimestamp;
    //驾驶时长（秒）
    private long duration;
    //驾驶里程（米）
    private double mileage;
	//状态： a 跨天跑批后期生成，再次跑批时，应该删除这样的数据
	private String state;
}
