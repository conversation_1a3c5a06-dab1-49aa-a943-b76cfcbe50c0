package com.xh.vdm.statistic.service.terminal;

import com.xh.vdm.statistic.vo.response.terminal.HighPrecisionCountResponse;
import com.xh.vdm.statistic.vo.response.terminal.TerminalDeptCountResponse;

import java.util.List;

/**
 * Description:
 */
public interface ScreenBaseInfoService {

	Integer statisticsTerminalByYear(int year, Long userId);

	List<TerminalDeptCountResponse> getTerminalCountByDept(Long userId);

    List<HighPrecisionCountResponse> getHighPrecisionCount(List<String> keys, Long userId);
}
