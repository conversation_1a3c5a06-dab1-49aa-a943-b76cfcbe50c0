package com.xh.vdm.statistic.vo.request;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2022/11/20 9:48 PM
 */
@Data
public class DeptAndDurationRequest {

    //部门id
    private Long deptId;
    //开始时间戳（秒）
    @JsonProperty("startTime")
    private Long startSecondTimestamp;
    //结束时间戳（秒）
    @JsonProperty("endTime")
    private Long endSecondTimestamp;

    @JsonIgnore
    private String month;

    //每页的数量
    @JSONField(name = "count")
    private Integer count;

    //当前页的开始下标
    private Integer start;
}
