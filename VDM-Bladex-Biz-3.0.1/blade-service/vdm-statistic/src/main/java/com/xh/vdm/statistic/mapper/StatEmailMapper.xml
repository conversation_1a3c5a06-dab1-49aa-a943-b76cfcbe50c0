<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatEmailMapper">

    <select id="getEmailHistoryList" resultType="com.xh.vdm.statistic.vo.response.EmailHistoryResponse" parameterType="com.xh.vdm.statistic.vo.request.EmailRequest">
        select
        id,
        (select dict_name from bam_dict bd where dict_type = 31 and dict_code = seh.type) as email_type,
        (select dict_name from bam_dict bd where dict_type = 32 and dict_code = seh.send_type) as email_send_type,
        username,
        dept_name,
        receive_mail_address,
        data_date,
        send_result_desc,
        send_time,
        note
        from stat_email_history seh
        where 1 = 1
        <if test="request.type != null and request.type != ''">
            and type = #{request.type,jdbcType=VARCHAR}
        </if>
        <if test="request.sendType != null and request.sendType != ''">
            and send_type = #{request.sendType,jdbcType=VARCHAR}
        </if>
        <if test="request.username != null and request.username != ''">
            and username = #{request.username,jdbcType=VARCHAR}
        </if>
        <if test="request.receiveMailAddress != null and request.receiveMailAddress != ''">
            and receive_mail_address = #{request.receiveMailAddress}
        </if>
        <if test="request.dataDate != null and request.dataDate != ''">
            and data_date = #{request.dataDate}
        </if>
        <if test="request.sendResult != null and request.sendResult != ''">
            and send_result = #{request.sendResult}
        </if>
        <if test="request.startTime != null">
            and send_time >= #{request.startTime}
        </if>
        <if test="request.endTime != null">
            and send_time &lt;= #{request.endTime}
        </if>
        order by send_time desc
    </select>
</mapper>
