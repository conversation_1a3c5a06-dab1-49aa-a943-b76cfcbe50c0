package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.CacheSegLimitSpeedMapResponse;
import com.xh.vdm.statistic.mapper.CacheSegLimitSpeedMapMapper;
import com.xh.vdm.statistic.mapper.StatisticsMapper;
import com.xh.vdm.statistic.service.ICacheSegLimitSpeedMapService;
import com.xh.vdm.statistic.vo.request.SegLimitSpeedMapRequest;
import com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 车辆地图超速服务类
 *
 * <AUTHOR>
 * @since 2022-09-14
 */
@Service
@Slf4j
public class CacheSegLimitSpeedMapServiceImpl extends ServiceImpl<CacheSegLimitSpeedMapMapper, CacheSegLimitSpeedMapResponse> implements ICacheSegLimitSpeedMapService {


    @Resource
    private StatisticsMapper statisticsMapper;

    @Resource
    private CacheSegLimitSpeedMapMapper mapper;

    @Override
    @Transactional
    public void statisticsSegLimitSpeedMap(Long startTime, Long endTime) throws Exception {

        Date startDate = new Date();
        startDate.setTime(startTime );
        Date endDate = new Date();
        endDate.setTime(endTime );

        //1.删除指定时间段的数据
        int count = mapper.delete(Wrappers.lambdaQuery(CacheSegLimitSpeedMapResponse.class).le(CacheSegLimitSpeedMapResponse::getStartAlarmTime, endDate).ge(CacheSegLimitSpeedMapResponse::getStartAlarmTime, startDate));
        log.info("删除指定时间段的数据成功，共删除{}条数据",count);

        //2.统计指定时间段的数据
        List<CacheSegLimitSpeedMapResponse> list = statisticsMapper.getSegLimitSpeedMapWithTime(startTime/1000, endTime/1000);
        //添加创建时间
        for(CacheSegLimitSpeedMapResponse cache : list){
            cache.setCreateTime(new Date());
        }

        boolean saveFlag = this.saveBatch(list);
        if(saveFlag){
            log.info("统计数据保存成功，共保存{}条数据",list==null?0:list.size());
        }
    }


    @Override
    public IPage<SegLimitSpeedMapResponse> querySegLimitSpeedMapCache(SegLimitSpeedMapRequest req) throws Exception {

        //查询车辆地图超速DB缓存
        Page<SegLimitSpeedMapResponse> page = new Page<>();
        page.setCurrent(req.getCurrent());
        page.setSize(req.getSize());
        IPage<SegLimitSpeedMapResponse> pageList = mapper.getList(page, req);
        return pageList;
    }

}
