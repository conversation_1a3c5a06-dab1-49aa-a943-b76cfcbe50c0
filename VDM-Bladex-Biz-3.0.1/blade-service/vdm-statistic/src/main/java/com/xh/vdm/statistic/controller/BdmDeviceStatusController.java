package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.BdmDeviceStatus;
import com.xh.vdm.statistic.service.BdmDeviceStatusService;
import com.xh.vdm.statistic.utils.AuthUtils;
import com.xh.vdm.statistic.vo.PushMessageVO;
import com.xh.vdm.statistic.vo.response.AlarmResponse;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Description: 设备状态表
 */
@RestController
@RequestMapping("/statistic/deviceStatus")
public class BdmDeviceStatusController {

	@Resource
	private BdmDeviceStatusService bdmDeviceStatusService;
	@Resource
	private StringRedisTemplate stringRedisTemplate;

	/**
	 * 设备状态查询
	 */
	@GetMapping("/device/status")
	public R<IPage<BdmDeviceStatus>> page(String deviceNum, String targetName, Integer runningStatus, Long startTime, Long endTime, Query query, BladeUser user) {
		IPage<BdmDeviceStatus> iPage = this.bdmDeviceStatusService.page(deviceNum, targetName, runningStatus,startTime, endTime, query, user.getUserId());
		return R.data(iPage);
	}

	/**
	 * 告警查询
	 * action 0：在线 1：离线
	 */
	@GetMapping("/alarm/page")
	public R<IPage<AlarmResponse>> alarmPage(Integer action, Long time, Long deviceId, Integer deviceType, Query query) {
		IPage<AlarmResponse> iPage = this.bdmDeviceStatusService.alarmPage(action, time, deviceId, deviceType, query);
		return R.data(iPage);
	}
}

