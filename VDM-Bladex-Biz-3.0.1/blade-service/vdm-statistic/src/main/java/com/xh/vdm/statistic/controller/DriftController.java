package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.DetailParam;
import com.xh.vdm.statistic.entity.DriftAndGoOnlineCountNode;
import com.xh.vdm.statistic.entity.RateParam;
import com.xh.vdm.statistic.service.ICacheStatDriftRateService;
import com.xh.vdm.statistic.service.IStatDriftService;
import com.xh.vdm.statistic.utils.CacheUtil;
import com.xh.vdm.statistic.utils.ExcelExport;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.utils.R;
import com.xh.vdm.statistic.vo.request.DetailRequest;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.DriftDetailResponse;
import com.xh.vdm.statistic.vo.response.DriftRateResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description: 漂移相关统计
 * @Author: zhouxw
 * @Date: 2022/9/9 9:19 AM
 */
@RestController
@RequestMapping("/bt/statistics/drift")
@Slf4j
public class DriftController {

    @Resource
    private IStatDriftService driftService;

    @Resource
    private CacheUtil cacheUtil;


    @Value("${static.file.path:/statistic/files/}")
    String staticFilePath;

    @Value("${db_cache.enable:true}")
    private boolean dbCacheEnable;

    @Resource
    private ICacheStatDriftRateService cacheStatDriftRateService;

    /**
     * @description: 统计漂移率
     * @author: zhouxw
     * @date: 2022/9/9 9:39 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.xh.vdm.statistic.vo.response.DriftRateResponse>
     **/
    @PostMapping("/driftRate")
    public R<DriftRateResponse> driftRate (@Validated @RequestBody RateRequest request){
        //1.构建查询参数
        DriftRateResponse response = null;
        RateParam param = new RateParam();
        BeanUtils.copyProperties(request , param);
        param.setMonth(request.getMonth().replace("-" , ""));
        List<DriftAndGoOnlineCountNode> list = null;
        //漂移率
        double driftRate = 0;
        //得分
        double score = 0 ;
        //总漂移车辆数
        int totalDrift = 0;
        //总上线车辆数
        int totalGoOnline = 0;
        if(dbCacheEnable){
            //如果开启DB缓存，则查询缓存数据
            //cacheStatDriftRateService.statAndSaveDriftRateCache();

        }else{
            //如果没有开启DB缓存
            response = statDriftRate(request);
        }

        return R.data(response);
    }

    /**
     * @description: 统计车辆漂移率：实时漂移率
     * @author: zhouxw
     * @date: 2023-03-90 16:13:03
     * @param: [request]
     * @return: com.xh.vdm.statistic.vo.response.DriftRateResponse
     **/
    private DriftRateResponse statDriftRate(RateRequest request){
        //漂移率
        double driftRate = 0;
        //得分
        double score = 0 ;
        //总漂移车辆数
        int totalDrift = 0;
        //总上线车辆数
        int totalGoOnline = 0;
        RateParam param = new RateParam();
        BeanUtils.copyProperties(request , param);
        param.setMonth(request.getMonth().replace("-" , ""));
        List<DriftAndGoOnlineCountNode> list = null;
            //2.查询上线车辆数和漂移车辆数
            list = driftService.findDriftAndOnlineCount(param);

            List<Integer> typeList = new ArrayList<>();
            //3.统计漂移率
            for(DriftAndGoOnlineCountNode node : list){
                totalDrift += node.getDriftCount();
                totalGoOnline += node.getGoOnlineCount();
                typeList.add(node.getVehicleUseType());
            }

            //补充数据
            if(!typeList.contains(StatisticConstants.VEHICLE_USE_TYPE_REGULAR)){
                DriftAndGoOnlineCountNode node = new DriftAndGoOnlineCountNode();
                node.setVehicleUseType(StatisticConstants.VEHICLE_USE_TYPE_REGULAR);
                list.add(node);
            }
            if(!typeList.contains(StatisticConstants.VEHICLE_USE_TYPE_CHARTERED)){
                DriftAndGoOnlineCountNode node = new DriftAndGoOnlineCountNode();
                node.setVehicleUseType(StatisticConstants.VEHICLE_USE_TYPE_CHARTERED);
                list.add(node);
            }
            if(!typeList.contains(StatisticConstants.VEHICLE_USE_TYPE_DANGER)){
                DriftAndGoOnlineCountNode node = new DriftAndGoOnlineCountNode();
                node.setVehicleUseType(StatisticConstants.VEHICLE_USE_TYPE_DANGER);
                list.add(node);
            }

            //计算漂移率
            if(totalDrift == 0 || totalGoOnline == 0){
                driftRate = 0;
            }else{
                driftRate = MathUtil.divideRoundDouble(totalDrift , totalGoOnline , 4);
            }
            //计算得分
            if(totalGoOnline > 0 && driftRate <= 0.05){
                //按照服务商标准来计算得分
                score = MathUtil.roundDouble(20 - driftRate * 20 , 2);
            }
        DriftRateResponse response = new DriftRateResponse();
        response.setDriftCountList(list);
        response.setDriftRate(driftRate);
        response.setScore(score);
        response.setTotalDrift(totalDrift);
        response.setTotalGoOnline(totalGoOnline);
        return response;
    }


    /**
     * @description: 查询车辆漂移明细
     * @author: zhouxw
     * @date: 2022/9/9 12:24 PM
     * @param: [request]
     * @return: com.baomidou.mybatisplus.core.metadata.IPage<java.util.List<com.xh.vdm.statistic.vo.response.DriftDetailResponse>>
     **/
    @PostMapping("/driftDetail")
    public R<IPage<DriftDetailResponse>> driftDetail(@Validated @RequestBody DetailRequest request){
        //前端下标从 0 开始，此处要加1
        request.setStart(request.getStart()+1);

        IPage<DriftDetailResponse> list = null;
        try{
            //1.拼接参数
            DetailParam param = new DetailParam();
            BeanUtils.copyProperties(request , param);
            param.setMonthStrict(request.getMonth().replace("-" , ""));
            param.setCurrent(request.getStart()/request.getCount()+(request.getStart() % request.getCount()>0?1:0));
            param.setSize(request.getCount());
            //2.执行查询
            list = driftService.findDriftDetailList(param);

            //颜色名值转换
            Map<String , String> colorMap = cacheUtil.getVehicleColorMap();
            list.getRecords().forEach(item ->{
                item.setPlateColor(colorMap.get(Integer.parseInt(item.getPlateColor())));
            });

        }catch (Exception e){
            e.printStackTrace();
            log.error("查询车辆漂移明细失败" , e);
            return R.fail("查询车辆漂移明细失败");
        }
        return R.data(list);
    }

    /**
     * @description: 导出漂移车辆明细
     * @author: zhouxw
     * @date: 2022/9/15 8:02 AM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.core.metadata.IPage<com.xh.vdm.statistic.vo.response.DriftDetailResponse>>
     **/
    @PostMapping("/driftDetailExport")
    public R<String> driftDetailExport(@Validated @RequestBody DetailRequest request){
        IPage<DriftDetailResponse> list = null;
        try{
            //1.拼接参数
            DetailParam param = new DetailParam();
            BeanUtils.copyProperties(request , param);
            param.setMonthStrict(request.getMonth().replace("-" , ""));
            param.setCurrent(0);
            param.setSize(Integer.MAX_VALUE);
            //2.执行查询
            list = driftService.findDriftDetailList(param);
            List<DriftDetailResponse> driffList = list.getRecords();

            //3.颜色名值转换
            Map<String , String> colorMap = cacheUtil.getVehicleColorMap();
            driffList.forEach(item ->{
                item.setPlateColor(colorMap.get(Integer.parseInt(item.getPlateColor())));
            });

            //4.执行数据导出
            String title = "漂移车辆明细";
            String[] arrs = {"企业名称","车牌号","车辆颜色","SIM卡号","终端ID","终端型号","漂移次数","日期"};
            String fileName = ExcelExport.exportExcelFile(title,driffList,arrs,staticFilePath);
            return R.data(fileName);
        }catch (Exception e){
            e.printStackTrace();
            log.error("导出车辆漂移明细失败" , e);
            return R.fail("导出车辆漂移明细失败");
        }
    }


}
