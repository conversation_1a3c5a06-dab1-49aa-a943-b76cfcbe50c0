package com.xh.vdm.statistic.controller;

import com.xh.vdm.statistic.service.IBdmTrackCalendarService;
import org.springblade.core.tool.api.R;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @Description: 告警监控 -- 轨迹日历
 */
@RestController
@RequestMapping("/track/calendar")
public class BdmTrackCalendarController {
	/**
	 * 服务对象
	 */
	@Resource
	private IBdmTrackCalendarService bdmTrackCalendarService;

	@GetMapping("/list")
	public R<Integer> list(Long deviceId, Integer deviceType,Long targetId, Integer targetType, @RequestParam("month") @DateTimeFormat(pattern = "yyyy-MM") String month) throws ParseException {
		Date parsedMonth = new SimpleDateFormat("yyyy-MM").parse(month);
		return R.data(this.bdmTrackCalendarService.list(deviceId,deviceType,targetId, targetType,parsedMonth));
	}


}

