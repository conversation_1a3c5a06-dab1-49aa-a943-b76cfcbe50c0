package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.utils.ExcelExport;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse;
import com.xh.vdm.statistic.vo.response.VehicleRateDetailResponse;

import java.util.Date;
import java.util.List;

/**
 * @Description: 车辆相关统计
 * @Author: zhouxw
 * @Date: 2022/9/6 9:20 AM
 */
public interface IVehicleStatService extends IService<UnGoOnlineVehicleResponse> {

    /**
     * @description: 获取上线车辆的 类型 和 数量
     * @author: zhouxw
     * @date: 2022/9/6 9:27 AM
     * @param: []
     * @return: com.xh.vdm.statistic.entity.VehicleCountAndType
     **/
    List<VehicleCountAndType> findGoOnlineVehicleCountByType(RateRequest request);

	/**
	 * 根据条件统计每天上线车辆数
	 * 包含本部门、子部门、账号关联车辆
	 * @param startDate yyyy-MM-dd
	 * @param endDate yyyy-MM-dd
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	List<DateAndCount> findGoOnlineCountByDay (String startDate, String endDate, List<Long> deptIds, Long userId) throws Exception;


	/**
	 * 根据条件统计每小时上线车辆数
	 * 包含本部门、子部门、账号关联车辆
	 * @param startDate yyyy-MM-dd
	 * @param endDate yyyy-MM-dd
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	List<DateAndCount> findGoOnlineCountByHour(Date startDate, Date endDate, List<Long> deptIds, Long userId);


	/**
     * @description: 获取入网车辆的 类型 和 数量
     * @author: zhouxw
     * @date: 2022/9/6 9:28 AM
     * @param: []
     * @return: com.xh.vdm.statistic.entity.VehicleCountAndType
     **/
    List<VehicleCountAndType> findInNetVehicleCountByType(RateRequest request);

    /**
     * @description: 查询未上线车辆明细
     * @author: zhouxw
     * @date: 2022/9/6 1:30 PM
     * @param: [query]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse>
     **/
    IPage<UnGoOnlineVehicleResponse> findUnGoOnlineVehicle(UnGoOnlineQuery query);

    /**
     * @description: 查询全部未上线车辆数据
     * @author: zhouxw
     * @date: 2022/9/14 3:58 PM
     * @param: [query]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse>
     **/
    List<UnGoOnlineVehicleResponse> findUnGoOnlineVehicleAll(UnGoOnlineQuery query);


    /**
     * @description: 根据上级平台和查询月份获取入网车辆数
     * @author: zhouxw
     * @date: 2022/9/13 1:44 PM
     * @param: [month, ownerId]
     * @return: int
     **/
    int findInNetCountByOwnerId(String nextMonthFirstDay, long ownerId);

	/**
	 * @description: 根据上级平台和查询月份获取上线车辆数
	 * @author: zhouxw
	 * @date: 2023-07-208 19:21:16
	 * @param: [month, ownerId]
	 * @return: int
	 **/
    int findGoOnlineCountByOwnerId(String month, long ownerId);

    /**
     * @description: 根据 企业id获取入网车辆数量
     * @author: zhouxw
     * @date: 2022/9/13 4:46 PM
     * @param: [nextMonthFirstDay, deptIds]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
     **/
    List<VehicleCountWithDept> findInNetCountByDeptId(String nextMonthFirstDay , List<Long> deptIds , Long ownerId);


	/**
	 * @description: 根据时间查询入网车辆数量
	 * @author: zhouxw
	 * @date: 2022/9/13 4:46 PM
	 * @param: [nextMonthFirstDay, deptIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	long findInNetCountByDate(Date date , List<Long> deptIds , Long userId);


	/**
     * @description: 根据 企业id获取车辆入网数量
     * @author: zhouxw
     * @date: 2022/9/13 5:26 PM
     * @param: [month, deptIds]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
     **/
    List<VehicleCountWithDept> findGoOnlineCountByDeptId(String month , List<Long> deptIds , Long ownerid);


    /**
     * @description: 查询指标异常车辆的详细指标信息
     * @author: zhouxw
     * @date: 2022/9/14 11:35 AM
     * @param: [param]
     * @return: com.baomidou.mybatisplus.core.metadata.IPage<java.util.List<com.xh.vdm.statistic.vo.response.VehicleRateDetailResponse>>
     **/
    IPage<VehicleRateDetailResponse> findVehicleRateDetail(DetailParam param);


    IPage<DeptAndCount> findDeptAndInNetCountByPage(IPage page , String nextMonthFirstDay , Long ownerId);

	/**
	 * @description: 按天统计车辆上线数量(分企业)
	 * @author: zhouxw
	 * @date: 2022/11/10 2:40 PM
	 * @param: [deptId, startTimme, endTime]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
	 **/
	List<DateAndDeptAndCount> findDeptVehicleCountByDay(Long deptId , Long startTime ,Long endTime , String vehicleUseType  ) throws Exception;

	/**
	 * @description: 按天统计客运车辆上线数量
	 * @author: zhouxw
	 * @date: 2022/11/10 2:40 PM
	 * @param: [deptId, startTimme, endTime]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
	 **/
	List<DateAndDeptAndCount> findPassengerVehicleCountByDay(Long deptId , long startTime , long endTime) throws Exception;

	/**
	 * @description: 根据 企业id获取车辆上线数量
	 * 如果没有指定企业，就查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 5:26 PM
	 * @param: [month, deptIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	VehicleCountWithDept findGoOnlineCountByDeptIdDeptOrArea(String month , Long deptId , Long ownerId);


	/**
	 * @description: 根据 企业id统计车辆总数
	 * @author: zhouxw
	 * @date: 2022/9/13 4:46 PM
	 * @param: [nextMonthFirstDay, deptIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	List<VehicleCountWithDept> statVehicleCountByDeptId( Long deptId , long ownerId, int count , int start) throws Exception;

	/**
	 * @description: 根据 企业id获取车辆总数
	 * 如果不指定企业，就查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 4:46 PM
	 * @param: [nextMonthFirstDay, deptIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	VehicleCountWithDept findVehicleCountByDeptIdDeptOrArea( Long deptId , long ownerId);


	/**
	 * @description: 根据 企业id获取车辆入网数量
	 * 如果不指定企业，就查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 4:46 PM
	 * @param: [nextMonthFirstDay, deptIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	VehicleCountWithDept findInNetCountByDeptIdDeptOrArea(String nextMonthFirstDay , Long deptIds , long ownerId);


	/**
	 * @description: 统计近30天内用户的车辆上线数及上线率
	 * @author: zhouxw
	 * @date: 2023-07-204 15:57:57
	 * @param: [deptIds, userId]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCountAndRate>
	 **/
	List<DateAndCountAndRate> statGoOnlineCountAndRateIn30Days(String startDate, String endDate, List<Long> deptIds, Long userId) throws Exception;

	/**
	 * 统计指定日期内企业的总行驶里程
	 * @param statDate yyyyMMdd
	 * @param deptIds
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	double statDeptMileage(String statDate, List<Long> deptIds, Long userId) throws Exception;
	/**
	 * 统计指定日期内企业的总行驶里程
	 * @param statDate yyyyMMdd
	 * @param deptIds
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	double statDeptMileageForApp(String statDate, List<Long> deptIds, Long userId) throws Exception;

	/**
	 * 统计车辆最大熟读列表
	 * @param statDate yyyy-MM-dd
	 * @param vehicleIds
	 * @return
	 * @throws Exception
	 */
	List<VehicleAndData> findMaxSpeedListByVehicleId(String statDate, List<Integer> vehicleIds) throws Exception;

	/**
	 * 按照月份查询部门总里程
	 * @param month yyyyMM
	 * @param deptIds
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	double statDeptMileageMonth(String month,List<Long> deptIds, Long userId) throws Exception;

	/**
	 * 查询车辆在指定月份中的最大速度列表
	 * @param month yyyy-MM
	 * @param vehicleIds
	 * @return
	 * @throws Exception
	 */
	List<VehicleAndData> findMaxSpeedListMonthByVehicleId(String month, List<Integer> vehicleIds) throws Exception;

	/**
	 * 统计企业指定日期的上线时长
	 * @param month yyyy-MM
	 * @param statDate yyyy-MM-dd
	 * @param deptIds
	 * @param vehicleIds
	 * @return
	 * @throws Exception
	 */
	long statDeptDuration(String month, String statDate, List<Long> deptIds, List<Integer> vehicleIds) throws Exception;

	/**
	 * 统计企业指定月份的上线时长
	 * @param month yyyy-MM
	 * @param lastDateInMonth 指定月份的最后一天 yyyy-MM-dd
	 * @param deptIds
	 * @param vehicleIds
	 * @return
	 * @throws Exception
	 */
	long statDeptDurationMonth(String month, String lastDateInMonth, List<Long> deptIds, List<Integer> vehicleIds) throws Exception;

}
