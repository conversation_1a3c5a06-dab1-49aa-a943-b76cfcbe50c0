package com.xh.vdm.statistic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.vo.request.LocationsRequest;
import com.xh.vdm.statistic.vo.response.LocationKuduResponse;
import com.xh.vdm.statistic.vo.response.tg.TargetResponse;
import org.apache.ibatis.annotations.Param;
import org.springblade.core.mp.support.Query;

import java.util.List;
import java.util.Map;

/**
 * 数据库访问层
 */
public interface LocationsMapper extends BaseMapper<LocationKudu> {

	/**
	 * 查询指定行数据
	 *
	 * @param locations
	 * @param query
	 * @param ids
	 * @return
	 */
	@DS("location")
	List<LocationKuduResponse> queryByPage(@Param("locations") LocationsRequest locations, @Param("query") Query query, @Param("ids") List<String> ids);

	@DS("location")
	long countLocations(@Param("locations") LocationsRequest locations, @Param("ids") List<String> ids);

	@DS("location")
	List<LocationKudu> selectLocations(@Param("locations") LocationsRequest locations, @Param("query") Query query, @Param("ids") List<String> ids);


	/**
	 * 分页查询轨迹信息
	 * @param idsList
	 * @param batch
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@DS("location")
	List<LocationKudu> locationPage(@Param("idsList") List<List<Long>> idsList, @Param("targetIdList") List<List<Long>> targetIdList, @Param("batch") Integer batch, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("limit") Long limit, @Param("offset") Long offset);

	/**
	 * 根据条件查询定位数据量
	 * @param idsList
	 * @param batch
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	@DS("location")
	long locationCount(@Param("idsList") List<List<Long>> idsList, @Param("targetIdList") List<List<Long>> targetIdList, @Param("batch") Integer batch, @Param("startTime") Long startTime, @Param("endTime") Long endTime);
}

