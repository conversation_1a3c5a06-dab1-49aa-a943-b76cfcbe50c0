package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.dto.alarm.AlarmCount;
import com.xh.vdm.statistic.entity.Alarm;
import com.xh.vdm.statistic.entity.AlarmBase;
import com.xh.vdm.statistic.entity.BdmSecurity;
import com.xh.vdm.statistic.entity.DateAndCount;
import com.xh.vdm.statistic.vo.request.CommonStatRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AlarmMapper extends BaseMapper<Alarm> {

	/**
	 * 统计 指定时间段内的 报警数
	 * @return
	 */
	List<DateAndCount> getAlarmCount(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);

	/**
	 * 统计 指定时间段内的 报警数
	 * 按小时进行统计
	 * @return
	 */
	List<DateAndCount> getAlarmCountHour(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	/**
	 * 统计 指定时间段内的报警处理数，按天统计
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param userId
	 * @param userType
	 * @return
	 */
	List<DateAndCount> getHandleCount(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId, @Param("userType") String userType);


	/**
	 * 统计 指定时间段内的报警处理数
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param userId
	 * @param userType
	 * @return
	 */
	long getHandleCountTotal(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId, @Param("userType") String userType);



	/**
	 * 分页查询实时未处理的报警
	 * @param deptIds
	 * @param userId
	 * @param userType
	 * @return
	 */
	IPage<BdmSecurity> getUnHandleRealTimeAlarmByPage(@Param("alarmTypeList") List<String> alarmTypeList, @Param("startTime") long startTime, @Param("endTime") long endTime, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId, @Param("userType") String userType, IPage<BdmSecurity> page);


	/**
	 * 统计 指定时间段内的报警处理数
	 * 按小时统计
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param userId
	 * @param userType
	 * @return
	 */
	List<DateAndCount> getHandleCountHour(@Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId, @Param("userType") String userType);

	/**
	 * @description: 查询指定时间段的超过指定时长的疲劳报警数
	 * @author: zhouxw
	 * @date: 2023-07-206 09:44:11
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd , duration: 超过的时长 秒]
	 * @return: long
	 **/
	long getFatigueCountByDateAndDuration(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("duration") long duration, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	/**
	 * @description: 查询指定时间段的超过指定时长的疲劳报警处理数
	 * @author: zhouxw
	 * @date: 2023-07-206 09:44:11
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd , duration: 超过的时长 秒, userType: 用户类型]
	 * @return: long
	 **/
	long getFatigueHandleCountByDateAndDuration(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("duration") long duration, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId, @Param("userType") String userType);



	/**
	 * @description: 查询指定时间段的报警数
	 * @author: zhouxw
	 * @date: 2023-07-206 09:44:11
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd , duration: 超过的时长 秒]
	 * @return: long
	 **/
	long getAlarmCountByDate(@Param("alarmTypes")List<Integer> alarmTypes, @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	/**
	 * @description: 查询指定时间段的报警处理数
	 * @author: zhouxw
	 * @date: 2023-07-206 09:44:11
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd , duration: 超过的时长 秒, userType: 用户类型]
	 * @return: long
	 **/
	long getAlarmHandleCountByDate(@Param("alarmTypes") List<Integer> alarmTypes, @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId, @Param("userType") String userType);


	/**
	 * @description: 查询指定时间段的报警数
	 * @author: zhouxw
	 * @date: 2023-07-206 09:44:11
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd , duration: 超过的时长 秒]
	 * @return: long
	 **/
	List<AlarmBase> getAlarmBaseByDate(@Param("alarmTypes")List<Integer> alarmTypes, @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);

	/**
	 * @description: 根据报警id查询已经处理的报警的数量
	 * @author: zhouxw
	 * @date: 2023-07-206 14:13:07
	 * @param: [alarmIds, userType]
	 * @return: long
	 **/
	long getAlarmHandleCountByAlarmIds(@Param("alarmIds") List<Long> alarmIds, @Param("userType") String userType);

	/**
	 * 获取单位的车辆数
	 */
	int getNumCarForDept (@Param("request") CommonStatRequest request);

	/**
	 * 查询车辆在线未定位记录数
	 */
	int getNumNoPosition (@Param("request") CommonStatRequest request, @Param("yearMonth") String yearMonth);

	/**
	 * 获取车辆离线位移记录数
	 */
	int getNumOfflineMove (@Param("request") CommonStatRequest request);

	/**
	 * 获取单位在线未定位的车辆排行
	 */
	List<AlarmCount> getCarRankOfNoPosition (@Param("request") CommonStatRequest request, @Param("yearMonth") String yearMonth);

	/**
 	 * 获取单位离线位移的车辆排行
 	 */
	List<AlarmCount> getCarRankOfOfflineMove (@Param("request") CommonStatRequest request);


	/**
	 * @description: 根据部门id和时间段获取报警数量（本节点及子节点）
	 * 不包含误报
	 * @author: zhouxw
	 * @date: 2023-06-164 21:05:12
	 * @param: [deptIds 本节点及子节点的deptId]
	 * @return: long
	 **/
	long getAlarmCountByDeptIdAndDuration(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime);


	/**
	 * @description: 根据部门id和时间段、报警类型获取报警数量（本节点及子节点）
	 * 不包含误报
	 * @author: zhouxw
	 * @date: 2023-06-164 21:05:12
	 * @param: [deptIds 本节点及子节点的deptId； alarmTypes 报警类型，多个类型中间使用英文逗号分隔；startTime 开始时间，精确到秒的时间戳，时间范围包含开始时间； endTime 结束时间，精确到秒的时间戳，时间范围不包含结束时间；]
	 * @return: long
	 **/
	long getAlarmCountByDeptIdAndDurationAndType(@Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds, @Param("startTime") Long startTime, @Param("endTime") Long endTime, @Param("alarmTypes") List<Integer> alarmTypes);


}
