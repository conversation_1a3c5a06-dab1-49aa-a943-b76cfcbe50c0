package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.config.ExcelDateConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "获取返回体：报停异动")
public class UnexpectedReportResponse {

    @JsonProperty("dept_name")
    @ApiModelProperty(name = "dept_name", value = "车队名称", example = "星航", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"报停异动统计报表", "车队名称"})
    @ColumnWidth(40)
    private String deptName;

    @JsonProperty("licence_plate")
    @ApiModelProperty(name = "licence_plate", value = "车牌号", example = "粤A12345", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"报停异动统计报表", "车牌号码"})
    @ColumnWidth(15)
    private String licencePlate;

    @JsonProperty("licence_color")
    @ApiModelProperty(name = "licence_color", value = "车牌颜色", example = "黄色", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
    @ColumnWidth(15)
    private String licenceColor;

	@ApiModelProperty(name = "licence_color_desc", value = "车牌颜色", example = "黄色", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty({"报停异动统计报表", "车牌颜色"})
	@JsonIgnore
	@ColumnWidth(15)
	private String licenceColorDesc;

    @JsonProperty("vehicle_use_type")
    @ApiModelProperty(name = "vehicle_model", value = "行业类型", example = "客运", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
    @ColumnWidth(20)
    private String vehicleUseType;

	@ApiModelProperty(name = "vehicle_model", value = "行业类型", example = "客运", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty({"报停异动统计报表", "行业类型"})
	@JsonIgnore
	@ColumnWidth(20)
	private String vehicleUseTypeDesc;

    @JsonProperty("vehicle_owner")
    @ApiModelProperty(name = "vehicle_owner", value = "车辆归属", example = "兵通", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"报停异动统计报表", "车辆归属"})
    @ColumnWidth(20)
    private String vehicleOwner;

    @JsonProperty("access_mode")
    @ApiModelProperty(name = "access_mode", value = "接入方式", example = "直营", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
    @ColumnWidth(15)
    private String accessMode;

	@ApiModelProperty(name = "access_mode", value = "接入方式", example = "直营", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@JsonIgnore
	@ExcelProperty({"报停异动统计报表", "车辆接入方式"})
	@ColumnWidth(15)
	private String accessModeDesc;

    @JsonProperty("alarm_type")
    @ApiModelProperty(name = "alarm_type", value = "报警类型", example = "报停异动", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
    @ColumnWidth(30)
    private String alarmType;

	@ApiModelProperty(name = "alarm_type", value = "报警类型", example = "报停异动", required = true)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty({"报停异动统计报表", "报警类型"})
	@JsonIgnore
	@ColumnWidth(30)
	private String alarmTypeDesc;

    @JsonProperty("start_time")
    @ApiModelProperty(name = "start_time", value = "报停开始时间", example = "2023-01-01 00:00:00", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty(value = {"报停异动统计报表", "报停开始时间"}, converter = ExcelDateConverter.class)
    @ColumnWidth(22)
    private Date startTime;

    @JsonProperty("end_time")
    @ApiModelProperty(name = "end_time", value = "报停结束时间", example = "2023-01-01 23:59:59", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty(value = {"报停异动统计报表", "报停结束时间"}, converter = ExcelDateConverter.class)
    @ColumnWidth(22)
    private Date endTime;

    @JsonProperty("alarm_time")
    @ApiModelProperty(name = "alarm_time", value = "异动时间", example = "2023-01-01 12:00:00", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty(value = {"报停异动统计报表", "报警时间"}, converter = ExcelDateConverter.class)
    @ColumnWidth(22)
    private Date alarmTime;

    @JsonProperty("alarm_address")
    @ApiModelProperty(name = "alarm_address", value = "异动位置", example = "大壮国际广场", required = true)
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"报停异动统计报表", "报警地点"})
    @ColumnWidth(90)
    private String alarmAddress;
}
