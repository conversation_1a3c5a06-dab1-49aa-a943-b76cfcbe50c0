package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.config.ExcelDateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OnlineDaysCountResponse {


	@ApiModelProperty(value = "企业名称")
	@JsonProperty("dept_name")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆在线天数统计报表", "企业名称"})
	@ColumnWidth(22)
	private String deptName;

	@ApiModelProperty(value = "行业类型")
	@JsonProperty("vehicle_use_type")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆在线天数统计报表", "行业类型"})
	@ExcelIgnore
	@ColumnWidth(22)
	private String vehicleUseType;

	@ApiModelProperty(value = "行业类型")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@JsonIgnore
	@ExcelProperty(value = {"车辆在线天数统计报表", "行业类型"})
	@ColumnWidth(22)
	private String vehicleUseTypeDesc;

	@ApiModelProperty(value = "接入方式")
	@JsonProperty("access_mode")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆在线天数统计报表", "接入方式"})
	@ColumnWidth(22)
	private String accessMode;

	/*@ApiModelProperty(value = "接入方式")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆在线天数统计报表", "接入方式"})
	@JsonIgnore
	@ColumnWidth(22)
	private String accessModeDesc;*/

	@ApiModelProperty(value = "车辆归属")
	@JsonProperty("vehicle_owner")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆在线天数统计报表", "车辆归属"})
	@ColumnWidth(22)
	private String vehicleOwner;


	@ApiModelProperty(value = "车牌号")
	@JsonProperty("licence_plate")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆在线天数统计报表", "车牌号"})
	@ColumnWidth(22)
	private String licencePlate;

	@ApiModelProperty(value = "车牌颜色")
	@JsonProperty("licence_color")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆在线天数统计报表", "车牌颜色"})
	@ColumnWidth(22)
	private String licenceColor;

	@ApiModelProperty(value = "车牌颜色编码")
	@JsonProperty("licence_color_code")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆在线天数统计报表", "车牌颜色编码"})
	@ExcelIgnore
	@ColumnWidth(22)
	private String licenceColorCode;

	//在线天数
	@ApiModelProperty(value = "在线天数")
	@JsonProperty("online_days_count")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆在线天数统计报表", "在线天数"})
	@ColumnWidth(22)
	private Integer onlineDaysCount;

	@ApiModelProperty(value = "离线天数")
	@JsonProperty("offline_days_count")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆在线天数统计报表", "离线天数"})
	@ColumnWidth(22)
	private Integer offlineDaysCount;

	@ApiModelProperty(value = "总行驶里程")
	@JsonProperty("total_mileage")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆在线天数统计报表", "总行驶里程"})
	@ColumnWidth(22)
	private Double totalMileage;
}
