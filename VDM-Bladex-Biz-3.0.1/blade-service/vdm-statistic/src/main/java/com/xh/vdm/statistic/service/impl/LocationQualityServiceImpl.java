package com.xh.vdm.statistic.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.LocationMapper;
import com.xh.vdm.statistic.mapper.LocationQualityMapper;
import com.xh.vdm.statistic.mapper.VehicleMapper;
import com.xh.vdm.statistic.service.IBdmVehicleService;
import com.xh.vdm.statistic.service.ILocationQualityService;
import com.xh.vdm.statistic.service.ILocationService;
import com.xh.vdm.statistic.service.IStatTaskLogService;
import com.xh.vdm.statistic.utils.*;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.LocationQualityDetailResponse;
import com.xh.vdm.statistic.vo.response.LocationQualityResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <p>
 * 定位数据质量表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
@Service
@Slf4j
public class LocationQualityServiceImpl extends ServiceImpl<LocationQualityMapper, LocationQuality> implements ILocationQualityService {


    //为提高效率，使用线程池进行数据处理
    private final static ExecutorService threadPool = Executors.newFixedThreadPool(10);
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
    private final SimpleDateFormat sdfSecond = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

    @Resource
    private LocationMapper locationMapper;

    @Resource
    private CacheUtil cacheUtil;

    @Resource
    private ILocationService locationService;

    @Resource
    private VehicleMapper vehicleMapper;

    @Resource
    private IStatTaskLogService taskLogService;

	@Resource
	private IBdmVehicleService vehicleService;

    @Resource
    private DataUtils dataUtils;

    @Override
    public boolean locationQualityStat(String day) throws Exception {

        //校验日期格式
        Date statDate = new Date();
        try {
            statDate = sdf.parse(day);
        } catch (ParseException e) {
            log.error("[数据质量统计]执行出现异常，日期 " + day + " 格式错误 "  , e);
            throw new Exception("日期格式错误：" + day);
        }

        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_QUALITY , "校验日期格式完成");

        //查询目标月表是否存在，如果不存在，则创建表
        checkTableExistAndCreate(day.substring(0 , 6));
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_QUALITY , "校验月表完成");

        //1.为防止重复执行造成指标数据重复，需要先删除之前写入的指标数据
        baseMapper.deleteDataByDay(day.substring(0 , 6) , day);
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_QUALITY , "删除之前跑过的指标数据完成");

        //2.根据指定日期查询车辆列表
        long start1 = System.currentTimeMillis();
        List<VehicleBase> vehicleBases = locationService.findUploadLicencePlatesByDay(day);
        long end1 = System.currentTimeMillis();
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_QUALITY , "查询车辆列表成功，共 "+(vehicleBases==null?"0":vehicleBases.size())+" 辆车，共耗时 " +(end1 - start1)+ " ms");

		//查询车辆基本信息
		List<String> vcList = new ArrayList<>();
		vehicleBases.forEach(item -> {
			vcList.add(item.getLicencePlate()+"~"+item.getLicenceColor());
		});
		List<BdmVehicle> vList = vehicleService.findVehicleByLicencePlateAndColor(vcList);
		Map<String,BdmVehicle> vehicleMap = new HashMap<>();
		if(vList != null){
			vList.forEach(item -> {
				String key = item.getLicencePlate()+"~"+item.getLicenceColor();
				vehicleMap.put(key, item);
			});
		}


        //3.对每辆车的上报信息进行数据质量校验
        long start2 = System.currentTimeMillis();
        List<LocationQuality> locationQualitiesList = new ArrayList<>();
        List<LocationQuality> locationQualities = Collections.synchronizedList(locationQualitiesList);
        CountDownLatch countDownLatch = new CountDownLatch(vehicleBases.size());
        for(VehicleBase vehicle : vehicleBases){
            Date finalStatDate = statDate;
            threadPool.submit(() -> {
                //taskLogService.addStatTaskProcessLog(CommonConstants.TASK_LOCATION_QUALITY , "[数据质量跑批]任务提交了");
                String licencePlate = vehicle.getLicencePlate();
                long plateColor = vehicle.getLicenceColor();
                try {
                    long start = System.currentTimeMillis();
                    //3.1 查询车辆上报数据点信息
                    List<LocationKudu> list = locationService.findUploadLocationPointListByDay(day, licencePlate, (int)plateColor);
                    long end = System.currentTimeMillis();
                    //taskLogService.addStatTaskProcessLog(CommonConstants.TASK_LOCATION_QUALITY, "[数据质量跑批]查询车辆点位信息成功，共" + (list == null ? "0" : list.size()) + "个点位信息，共耗时 " + (end - start) + " ms");
                    long startV = System.currentTimeMillis();
                    //3.2 对数据点列表进行数据质量校验
                    LocationQuality quality = LocationUtil.validLocationBatch(list);
                    quality.setCreateTime(new Date());
                    quality.setStatDate(finalStatDate);
                    quality.setLicencePlate(licencePlate);
                    quality.setPlateColor((int)plateColor);

					String key = licencePlate+"~"+plateColor;
					BdmVehicle ve = vehicleMap.get(key);
					if(ve != null){
						quality.setVehicleId(ve.getId());
						quality.setDeptId(ve.getDeptId());
					}

                    locationQualities.add(quality);
                    long endV = System.currentTimeMillis();
                    //taskLogService.addStatTaskProcessLog(CommonConstants.TASK_LOCATION_QUALITY, "[数据质量跑批]数据质量验证成功，共耗时：" + (endV - startV) + " ms");
                }catch (Exception e){
                    e.printStackTrace();
                    taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_QUALITY, "[数据质量跑批]数据质量验证出现异常:"+e.getMessage());
                }finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        long end2 = System.currentTimeMillis();
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_QUALITY , "[数据质量跑批]对车辆列表中的车辆进行数据校验完成，共耗时" + (end2 - start2) + " ms");

        //4.记录数据质量校验结果
        long start3 = System.currentTimeMillis();
        baseMapper.saveBatchMonth(day.substring(0 , 6), locationQualities);
        long end3 = System.currentTimeMillis();
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_LOCATION_QUALITY , "[数据质量跑批]执行数据质量写入完成，共耗时 "+(end3 - start3)+" ms");
        return true;
    }

    @Override
    public void checkTableExistAndCreate(String month) {
        try{
            baseMapper.checkExist(month);
        }catch (Exception e){
            log.info("月表[location_quality_{}]不存在，将新创建该月表", month);
            baseMapper.createTable(month);
            //创建索引
            dataUtils.createIndex(StatisticConstants.LOCATION_QUALITY_TABLE+"_"+month , StatisticConstants.LICENCE_PLATE);
        }
    }

    @Override
    public LocationQualityResponse findLocaitonQualityRate(RateRequest request) {
        //1.改造请求参数
        request.setMonth(request.getMonth().replace("-",""));

        //2.查询数据
        return baseMapper.getLocationQualityRate(request);
    }

    @Override
    public List<LocationQualityDetailResponse> findUnCompleteDetailList(DetailParam param) {

        //1.计算要查询的数据范围（从第几行到第几行）
        int current = param.getCurrent();
        int size = param.getSize();
        //数据范围（包含头尾）
        int start = (current - 1) * size + 1;
        param.setMonthStrict(param.getMonth().replace("-" , ""));

        //2.判断全月数据量是否小于等于指定数量，如果小于，则查询全月即可
        DetailDayParam dayParam = new DetailDayParam();
        BeanUtils.copyProperties(param , dayParam);
        dayParam.setDay("");
        dayParam.setMonthStrict(param.getMonth().replace("-",""));

        //根据条件查询全月的异常数据条数
        int monthCount = baseMapper.getTotalErrorCount(dayParam);
        //存放查找到的数据
        List<LocationKudu> qualityList = new ArrayList<>();
        Map<String, TerminalAndDept> map = new HashMap<>();
        //2.1 查询有轨迹异常的日期
        List<LocationQuality> dayList = baseMapper.getLocationQualityWithLocationError(param);

        //拼装基础数据
        for(LocationQuality lq: dayList){
            String licencePlate = lq.getLicencePlate();
            int plateColor = lq.getPlateColor();
            if(!map.containsKey(licencePlate+"~"+plateColor)){
                map.put(licencePlate+"~"+plateColor,vehicleMapper.getTerminalAndDept(licencePlate, plateColor));
            }
        }

        //如果整月的异常数据量不足一页，那么就全部查询
        if(monthCount < size){
            //如果 start 比 全月数据量还大，如果不是第一页，那么就不返回数据
            if(start > monthCount){
                return null;
            }
            //2.2 分别查询每天的异常轨迹数据
            for(LocationQuality lq: dayList){
                //查找轨迹异常数据的定位时间 locTime
                String errLocationIds = lq.getErrorLocationList();
                /*String[] errList = errLocationIds.split(",");
                Long[] intList = new Long[errList.length];
                for(int i = 0 ; i < errList.length ; i++){
                    intList[i] = Long.parseLong(errList[i]);
                }*/
                    //List<Location> errLocations = locationMapper.getUploadLocationListById(sdf.format(lq.getStatDate()) , Arrays.asList(intList));

				//根据定位时间查询不合格的定位数据
				//查询kudu
				qualityList.addAll(getErrorLocationList(lq.getLicencePlate(),(long)lq.getPlateColor(),errLocationIds));

            }
            //2.3 组织返回数据
            List<LocationQualityDetailResponse> detailList = new ArrayList<>();
            for(LocationKudu location : qualityList){
                LocationQualityDetailResponse res = new LocationQualityDetailResponse();
                //拼装轨迹相关数据
                if(location != null){
                    //BeanUtils.copyProperties(location,res);
					copyProperties(location, res);
                    //设置海拔
                    res.setMileage(location.getAltitude().intValue());
                    //设置车牌颜色
                    Map<String , String> colorMap = cacheUtil.getVehicleColorMap();
//                    res.setLicenceColor(colorMap.get(location.getLicenceColor()+""));
                }

                //拼装车辆基础数据
				// TODO 后续修改一下
//                if(map.get(location.getLicencePlate()+"~"+location.getLicenceColor()) != null){
//                    BeanUtils.copyProperties(map.get(location.getLicencePlate()+"~"+location.getLicenceColor()) , res);
//                }
                //格式化定位时间（为了保持数据的原始性，不在mapper中转换）
                long timestampSecond = location.getTime();
                String timeFormatted = sdfSecond.format(new Date(timestampSecond*1000));
                res.setTime(timeFormatted);

                detailList.add(res);
            }
            return detailList;
        }


        //3.如果全月数量大于一页，则需要计算数据属于哪几天，然后获取完整天的数据和不完整天的部分数据
        int totalErrorCount = 0 ;
        int nowCount = 0 ;

        boolean hasFindFirst = false;
        List<String> idList = new ArrayList<>();
        //从前往后查询每天的异常数据条数，并且累加前边的
        for(LocationQuality lq : dayList){

            int errorCount = lq.getTotalErrorCount();
            totalErrorCount = totalErrorCount + errorCount;
            if(totalErrorCount >= start){
                //如果找到了开始的数据下标，找到准确下标，并记录下当前页数量（因为包含第一条，所以，最后再加1）
                int countOffset = totalErrorCount - start + 1;
                nowCount = nowCount + countOffset;
                if(!hasFindFirst){
                    //第一天
                    hasFindFirst = true;

                    String errLocationIds = lq.getErrorLocationList();
                    /*String[] errList = errLocationIds.split(",");
                    List<Integer> intList = new ArrayList<>();
                    //判断
                    for(int i = errorCount - (totalErrorCount - start) -1 ; i < (totalErrorCount>=size+start?errorCount-(totalErrorCount-start-size)-1:errorCount); i++){
                        intList.add(Integer.parseInt(errList[i]));
                    }*/
                    //List<LocationKudu> errLocations = locationMapper.getUploadLocationListById(sdf.format(lq.getStatDate()) , intList);
					//查询kudu
					List<LocationKudu> errLocations = getErrorLocationList(lq.getLicencePlate(),(long)lq.getPlateColor(),errLocationIds);
                    qualityList.addAll(errLocations);
                    //如果第一天的数量就大于一页的数量，则不再继续查找，直接推出循环
                    if(errLocations.size() >= size){
                        break;
                    }
                }else{

                    //如果找到了结束的数据下标
                    if(totalErrorCount >= start + size){
                        //最后一天
                        //添加数据
                        String errLocationIds = lq.getErrorLocationList();
                        String[] errList = errLocationIds.split(",");
                        List<Integer> intList = new ArrayList<>();
                        for(int i = 0 ; i < (errorCount - (totalErrorCount - start - size))-1 ; i++){
                            intList.add(Integer.parseInt(errList[i]));
                        }

                        if(intList.size() > 0){
							//查詢kudu
                            List<LocationKudu> errLocations = getErrorLocationList(lq.getLicencePlate(),(long)lq.getPlateColor(),errLocationIds);
                            qualityList.addAll(errLocations);
                        }

                        break;
                    }

                    //中间天
                    //加载全部的数据
                    String errLocationIds = lq.getErrorLocationList();
                    /*String[] errList = errLocationIds.split(",");
                    List<Integer> intList = new ArrayList<>();
                    for(String idStr : errList){
                        intList.add(Integer.parseInt(idStr));
                    }*/
                    //List<Location> errLocations = locationMapper.getUploadLocationListById(sdf.format(lq.getStatDate()) , intList);
					List<LocationKudu> errLocations = getErrorLocationList(lq.getLicencePlate(),(long)lq.getPlateColor(),errLocationIds);
                    qualityList.addAll(errLocations);
                }


            }

        }


        //3.3 组织返回数据
        List<LocationQualityDetailResponse> detailList = new ArrayList<>();
        for(LocationKudu location : qualityList){
            LocationQualityDetailResponse res = new LocationQualityDetailResponse();
            //拼装轨迹相关数据
            if(location != null){
                //BeanUtils.copyProperties(location,res);
				copyProperties(location, res);
                //设置海拔
                res.setMileage(location.getAltitude().intValue());
            }
            //拼装车辆基础数据
			// TODO 后续修改一下
//            if(map.get(location.getLicencePlate()+"~"+location.getLicencePlate()) != null){
//                BeanUtils.copyProperties(map.get(location.getLicencePlate()+"~"+location.getLicenceColor()) , res);
//                //设置颜色
//                res.setLicenceColor(cacheUtil.getVehicleColorMap().get(location.getLicenceColor()));
//            }

            //格式化定位时间（为了保持数据的原始性，不在mapper中转换）
            long timestampSecond = location.getTime();
            String timeFormatted = sdfSecond.format(new Date(timestampSecond*1000));
            res.setTime(timeFormatted);

            detailList.add(res);
        }
        return detailList;
    }



	private void copyProperties(LocationKudu source, LocationQualityDetailResponse dest){
		dest.setMileage(source.getMileage().intValue());
		dest.setTime(source.getTime()+"");
//		dest.setLicenceColor(source.getLicenceColor()+"");
//		dest.setLicencePlate(source.getLicencePlate());
		dest.setBearing(source.getBearing().intValue());
		dest.setSpeed(source.getSpeed().intValue());
		dest.setLatitude(source.getLatitude());
		dest.setLongitude(source.getLongitude());
		dest.setTimeOri(source.getTime());
	}


	/**
	 * @description: 根据车牌号、车牌颜色、异常定位id列表查询定位数据列表
	 * 查询kudu
	 * @author: zhouxw
	 * @date: 2023-06-159 18:44:15
	 * @param: [licencePlate, licenceColor, errorLocationIds]
	 * @return: java.util.List<com.xh.vdm.bd.entity.LocationKudu>
	 **/
	private List<LocationKudu> getErrorLocationList(String licencePlate, long licenceColor, String errorLocationIds){

		List<LocationKudu> errLocations = locationMapper.getLocationByVehicleAndId(licencePlate, (int)licenceColor, errorLocationIds);
		return errLocations;
	}


    @Override
    public int findTotalErrorCount(DetailDayParam param) {
        return baseMapper.getTotalErrorCount(param);
    }


    @Override
    public List<VehicleRateWithDept> findQualityRateByDeptId(String month, List<Long> deptIds , Long ownerId) {
        month = month.replace("-" , "");
        return baseMapper.getQualityRateByDeptId(month , deptIds , ownerId);
    }

	@Override
	public VehicleRateWithDept findQualityRateByDeptIdDeptOrArea(String month, Long deptId , Long ownerId) {
		month = month.replace("-" , "");
		return baseMapper.getQualityRateByDeptIdDeptOrArea(month , deptId , ownerId);
	}


	@Override
	public List<DateAndLocationQuality> findQualityRateByDate(List<String> dateList, List<Long> deptIds, Long userId) throws Exception {

		//1.拼装部门id
		StringBuffer deptIdsStr = new StringBuffer();
		String deptIdsList = "";
		for(Long id : deptIds){
			deptIdsStr.append(id).append(",");
		}
		if(deptIdsStr.length() > 0){
			deptIdsList = deptIdsStr.substring(0,deptIdsStr.length() - 1);
		}

		//2.根据天数拼装sql
		StringBuffer sqls = new StringBuffer();
		int index = 0;
		for(String date : dateList){
			if(index != 0){
				sqls.append(" union ");
			}
			String dateStr = date.substring(6,8);
			String monthStr = date.substring(0,6);
			String stateDate = date.substring(0,4)+"-"+date.substring(4,6)+"-"+date.substring(6,8);
			String template = "select coalesce(sum(coalesce(lq.total_count,0)),0) totalCount , coalesce(sum(coalesce(lq.total_error_count,0)),0) totalErrorCount , " + date + " date " +
				"        from location_quality_"+monthStr+" lq  " +
				"        where (lq.dept_id in ("+deptIdsList+") or lq.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = "+userId+"))" +
				"        and lq.is_del = 0 and lq.stat_date = '"+stateDate+"'";
			sqls.append(template);
			index ++;
		}

		//3.执行sql
		if(!StringUtils.isEmpty(sqls)){
			//List<Map<String, Object>> list = SqlRunner.db().selectList(sqls.toString());
			log.info("sql is "+sqls.toString());
			List<Map<String,Object>> list = vehicleMapper.dynamicQuery(sqls.toString());
			if(list == null || list.size() < 1){
				return null;
			}
			List<DateAndLocationQuality> resList = new ArrayList<>();
			for(Map<String,Object> m : list){
				DateAndLocationQuality dc = new DateAndLocationQuality();
				dc.setDate(m.get("date").toString());
				dc.setTotalCount(Long.parseLong(m.get("totalCount").toString()));
				dc.setTotalErrorCount(Long.parseLong(m.get("totalErrorCount").toString()));
				if(dc.getTotalCount() == 0){
					dc.setRate(1D);
					dc.setRateStr("100%");
				}else{
					dc.setRate(MathUtil.divideRoundDouble((dc.getTotalCount() - dc.getTotalErrorCount()), dc.getTotalCount(), 4));
					dc.setRateStr(MathUtil.formatToPercent(dc.getRate(), 2));
				}
				resList.add(dc);
			}
			return resList;
		}else{
			return null;
		}
	}

	@Override
	public List<VehicleAndQuality> findQualityByCondition(CommonBaseCrossMonthRequest request) throws Exception {
		return baseMapper.getQualityByCondition(request);
	}
}
