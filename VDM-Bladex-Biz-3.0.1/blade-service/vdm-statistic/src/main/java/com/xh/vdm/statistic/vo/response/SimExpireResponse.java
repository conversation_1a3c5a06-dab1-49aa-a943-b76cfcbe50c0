package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 服务到期信息
 */
@Data
public class SimExpireResponse {

	//企业名称
	@ApiModelProperty(value = "企业名称")
	@JsonProperty("dept_name")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"服务到期统计报表", "企业名称"})
	@ColumnWidth(22)
	private String deptName;

	@ApiModelProperty(value = "车牌号")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(22)
	@ExcelProperty(value = {"服务到期统计报表", "车牌号"})
	@JsonProperty("licence_plate")
	private String licencePlate;

	//车牌颜色
	@ApiModelProperty(value = "车牌颜色")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(22)
	@ExcelIgnore
	@JsonProperty("licence_color")
	private String licenceColor;


	@ApiModelProperty(value = "车牌颜色")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"服务到期统计报表", "车牌颜色"})
	@JsonIgnore
	@ColumnWidth(22)
	private String licenceColorDesc;


	@ApiModelProperty(value = "车辆归属")
	@JsonProperty("vehicle_owner")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"服务到期统计报表", "车辆归属"})
	@ColumnWidth(22)
	private String vehicleOwner;

	@ApiModelProperty(value = "行业类型")
	@JsonProperty("vehicle_use_type")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
	@ColumnWidth(22)
	private String vehicleUseType;

	@ApiModelProperty(value = "行业类型")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(22)
	@JsonIgnore
	@ExcelProperty(value = {"服务到期统计报表", "行业类型"})
	private String vehicleUseTypeDesc;

	@ApiModelProperty(value = "接入方式")
	@JsonProperty("access_mode")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
	@ColumnWidth(22)
	private String accessMode;

	@ApiModelProperty(value = "接入方式")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"服务到期统计报表", "接入方式"})
	@JsonIgnore
	@ColumnWidth(22)
	private String accessModeDesc;




	//车辆类型
	@ApiModelProperty(value = "车辆类型")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(22)
	@ExcelIgnore
	@JsonProperty("vehicle_model")
	private String vehicleModel;

	//车辆类型
	@ApiModelProperty(value = "车辆类型")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(22)
	@ExcelProperty(value = {"服务到期统计报表", "车辆类型"})
	@JsonIgnore
	private String vehicleModelDesc;


	//服务开始时间
	@ApiModelProperty(value = "服务开始时间")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(22)
	@ExcelProperty(value = {"服务到期统计报表", "服务开始时间"})
	@JsonProperty("active_date")
	private String activeDate;


	//服务到期时间
	@ApiModelProperty(value = "服务到期时间")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(22)
	@ExcelProperty(value = {"服务到期统计报表", "服务到期时间"})
	@JsonProperty("end_date")
	private String endDate;


	//服务到期天数
	@ApiModelProperty(value = "服务到期天数")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(22)
	@ExcelProperty(value = {"服务到期统计报表", "服务到期天数"})
	@JsonProperty("expire_count")
	private String expireCount;
}
