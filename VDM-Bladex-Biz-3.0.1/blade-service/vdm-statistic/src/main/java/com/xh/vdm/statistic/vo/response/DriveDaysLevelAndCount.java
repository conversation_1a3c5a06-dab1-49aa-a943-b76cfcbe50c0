package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

/**
 * @Description: 驾驶员驾驶天数分布
 * @Author: zhouxw
 * @Date: 2022/11/20 7:48 PM
 */
@Data
public class DriveDaysLevelAndCount {

    //企业名称
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶天数分布", "企业名称"})
    private String deptName;
    //0天驾驶员数量
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶天数分布", "0天"})
    private int level1Count;
    //0～10天驾驶员数量
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶天数分布", "1～10天"})
    private int level2Count;
    //10～20天驾驶员数量
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶天数分布", "11～20天"})
    private int level3Count;
    //20天以上驾驶员数量
    @ColumnWidth(30)
    @ExcelProperty({"驾驶员驾驶天数分布", "20天以上"})
    private int level4Count;
}
