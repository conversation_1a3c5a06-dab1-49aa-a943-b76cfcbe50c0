package com.xh.vdm.statistic.service.impl;

import com.xh.vdm.statistic.entity.BladeDictBiz;
import com.xh.vdm.statistic.mapper.BladeDictBizMapper;
import com.xh.vdm.statistic.service.IBladeDictBizService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 业务字典表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-09
 */
@Service
public class BladeDictBizServiceImpl extends ServiceImpl<BladeDictBizMapper, BladeDictBiz> implements IBladeDictBizService {

	@Override
	public List<String> findProfessionList(Long professionId) {
		return baseMapper.getProfessionList(professionId);
	}
}
