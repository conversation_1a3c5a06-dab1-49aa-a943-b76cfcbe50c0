package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.CacheSecurityInfoResponse;
import com.xh.vdm.statistic.vo.request.SecurityInfoRequest;
import com.xh.vdm.statistic.vo.response.SecurityInfoResponse;

public interface ICacheSecurityInfoService extends IService<CacheSecurityInfoResponse> {

    /**
     * @description: 车辆安全信息记录，并入库
     * @author: zhouxw
     * @date: 2023-02-40 15:27:09
     * @param: [request, req]
     * @return: com.xh.vdm.statistic.vo.ObjectRestResponse<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>>
     **/
    void statisticsSecurityInfo(Long startTime, Long endTime) throws Exception;


    /**
     * @description: 查询车辆安全信息记录
     * @author: zhouxw
     * @date: 2023-02-40 16:20:53
     * @param: [request, req]
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    IPage<SecurityInfoResponse> querySecurityInfoCache(SecurityInfoRequest req) throws Exception;


}
