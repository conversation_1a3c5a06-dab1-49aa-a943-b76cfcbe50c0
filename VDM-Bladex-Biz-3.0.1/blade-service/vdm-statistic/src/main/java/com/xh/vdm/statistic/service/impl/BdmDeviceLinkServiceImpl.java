package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.BdmDeviceLink;
import com.xh.vdm.statistic.mapper.BdmDeviceLinkMapper;
import com.xh.vdm.statistic.service.BdmDeviceLinkService;
import com.xh.vdm.statistic.vo.request.BdmDeviceLinkRequest;
import com.xh.vdm.statistic.vo.response.terminal.LinkCountResponse;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.entity.DataAuthCE;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * (BdmDeviceLink)表服务实现类
 */
@Service
public class BdmDeviceLinkServiceImpl extends ServiceImpl<BdmDeviceLinkMapper, BdmDeviceLink> implements BdmDeviceLinkService {
	@Resource
	private BdmDeviceLinkMapper bdmDeviceLinkMapper;

	/**
	 * 分页查询
	 *
	 * @param bdmDeviceLinkRequest 筛选条件
	 * @param query                分页对象
	 * @param userId
	 * @return 查询结果
	 */
	@Override
	public List<BdmDeviceLink> queryByPage(DataAuthCE dataAuthCE, BdmDeviceLinkRequest bdmDeviceLinkRequest, Query query, Long userId) {
		if (AuthUtil.isAdministrator()) {
			userId = null;
		}

		return this.bdmDeviceLinkMapper.queryAll(bdmDeviceLinkRequest, query, userId, dataAuthCE.getOrgListStr(), dataAuthCE.getAccount());
	}

	@Override
	public long countLink(DataAuthCE dataAuthCE, BdmDeviceLinkRequest bdmDeviceLinkRequest, Long userId) {
		if (AuthUtil.isAdministrator()) {
			userId = null;
		}

		return this.bdmDeviceLinkMapper.countLink(bdmDeviceLinkRequest, userId, dataAuthCE.getOrgListStr(), dataAuthCE.getAccount());
	}

	@Override
	public long findLongOfflineCount(List<Long> deptList, Long secondTimestamp) {
		return baseMapper.getLongOfflineCount(deptList, secondTimestamp);
	}

    @Override
    public List<LinkCountResponse> onlineTrendered(Long userId) {
		if (AuthUtil.isAdministrator()) {
			userId = null;
		}
        return this.bdmDeviceLinkMapper.onlineTrendered(userId);
    }
}
