package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse;
import com.xh.vdm.statistic.vo.response.VehicleRateDetailResponse;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 车辆 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
public interface VehicleMapper extends BaseMapper<UnGoOnlineVehicleResponse> {



	List<Map<String, Object>> dynamicQuery(String sql);





    /**
     * @description: 查询上线车辆的 类型 和 数量
     * @author: zhouxw
     * @date: 2022/9/6 9:31 AM
     * @param: []
     * @return: com.xh.vdm.statistic.entity.VehicleCountAndType
     **/
    List<VehicleCountAndType> getGoOnlineVehicleCountAndType(RateRequest request);

	/**
	 * 根据条件统计上线车辆数
	 * 包含首位
	 * @param startDate 开始日期 yyuy-MM-dd
	 * @param endDate 结束日期 yyyy-MM-dd
	 * @param deptIds 部门id
	 * @param userId 登录用户的id
	 * @return
	 */
	List<DateAndCount> getGoOnlineCountByDay (@Param("yearMonth") String yearMonth, @Param("startDate") String startDate, @Param("endDate") String endDate, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	/**
	 * 根据条件统计上线车辆数--按小时
	 * 包含首位
	 * @param startDate 开始日期 yyuy-MM-dd
	 * @param endDate 结束日期 yyyy-MM-dd
	 * @param deptIds 部门id
	 * @param userId 登录用户的id
	 * @return
	 */
	List<DateAndCount> getGoOnlineCountByHour(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);


	/**
     * @description: 查询入网车辆的 类型 和 数量
     * @author: zhouxw
     * @date: 2022/9/6 9:31 AM
     * @param: []
     * @return: com.xh.vdm.statistic.entity.VehicleCountAndType
     **/
    List<VehicleCountAndType> getInNetVehicleCountAndType(RateRequest request);


    /**
     * @description: 查询未上线车辆明细
     * @author: zhouxw
     * @date: 2022/9/6 1:22 PM
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse>
     **/
    IPage<UnGoOnlineVehicleResponse> getUnGoOnlineVehicle(IPage page , @Param("query") UnGoOnlineQuery query);


    /**
     * 分页查询企业入网车辆数
     * @param page
     * @param nextMonthFirstDay
     * @param ownerId
     * @return
     */
    IPage<DeptAndCount> getDeptAndInNetCountByPage(IPage page , @Param("nextMonthFirstDay") String nextMonthFirstDay , @Param("ownerId") Long ownerId);

    /**
     * @description: 查询全部未上线车辆数据
     * @author: zhouxw
     * @date: 2022/9/14 3:56 PM
     * @param: [query]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse>
     **/
    List<UnGoOnlineVehicleResponse> getUnGoOnlineVehicleAll(@Param("query") UnGoOnlineQuery query);


    /**
     * @description: 根据车牌号获取终端和企业信息
     * @author: zhouxw
     * @date: 2022/9/9 4:30 PM
     * @param: [licencePlate]
     * @return: com.xh.vdm.statistic.entity.TerminalAndDept
     **/
    TerminalAndDept getTerminalAndDept (@Param("licencePlate") String licencePlate,  @Param("plateColor") int plateColor);


    /**
     * @description: 根据查询月份和上级平台查询入网车辆数
     * @author: zhouxw
     * @date: 2022/9/13 1:45 PM
     * @param: [nextMonthFirstDay, ownerId]
     * @return: int
     **/
    int getInNetCountByOwnerId(@Param("nextMonthFirstDay") String nextMonthFirstDay , @Param("ownerId") long ownerId);

    /**
     * @description: 根据查询月份和上级平台查询上线车辆数
     * @author: zhouxw
     * @date: 2022/9/13 2:37 PM
     * @param: [month, ownerId]
     * @return: int
     **/
    int getGoOnlineCountByOwnerId(@Param("month") String month , @Param("ownerId") long ownerId);

    /**
     * @description: 获取指定企业的入网车辆数
     * @author: zhouxw
     * @date: 2022/9/13 4:41 PM
     * @param: [month, dpetIds]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
     **/
    List<VehicleCountWithDept> getInNetCountByDeptId(@Param("nextMonthFirstDay") String nextMonthFirstDay , @Param("deptIds") List<Long> dpetIds , @Param("ownerId") Long ownerId);


	/**
	 * @description: 根据时间查询车辆入网时间
	 * @author: zhouxw
	 * @date: 2022/9/13 4:41 PM
	 * @param: [month, dpetIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	long getInNetCountByDate(@Param("date") Date date , @Param("deptIds") List<Long> dpetIds , @Param("userId") Long userId);



	/**
     * @description: 获取指定企业的上线车辆数
     * @author: zhouxw
     * @date: 2022/9/13 5:15 PM
     * @param: [month, dpetIds]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
     **/
    List<VehicleCountWithDept> getGoOnlineCountByDeptId(@Param("month") String month , @Param("deptIds") List<Long> dpetIds , @Param("ownerId") Long ownerId);


    /**
     * @description: 查询异常车辆明细信息
     * @author: zhouxw
     * @date: 2022/9/14 11:27 AM
     * @param: [page, param]
     * @return: com.baomidou.mybatisplus.core.metadata.IPage<java.util.List<com.xh.vdm.statistic.vo.response.VehicleRateDetailResponse>>
     **/
    IPage<VehicleRateDetailResponse> getVehicleRateDetail(IPage page , @Param("param") DetailParam param);


	/**
	 * @description: 按天统计车辆上线情况(分企业)
	 * @author: zhouxw
	 * @date: 2022/11/10 2:33 PM
	 * @param: [param]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
	 **/
	List<DateAndDeptAndCount> getDeptVehCountByDay(@Param("deptId") Long deptId , @Param("startTime") long startTime ,@Param("vehicleUseType") String vehicleUseType , @Param("endTime") long endTime);

	/**
	 * @description: 按天统计客运车辆上线情况(分企业)
	 * @author: zhouxw
	 * @date: 2022/11/10 2:33 PM
	 * @param: [param]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
	 **/
	List<DateAndDeptAndCount> getDeptPassengerVehCountByDay(@Param("deptId") Long deptId , @Param("startTime") long startTime , @Param("endTime") long endTime);

	/**
	 * @description: 获取指定企业的上线车辆数
	 * 如果不指定企业，则查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 5:15 PM
	 * @param: [month: yyyy-MM, dpetIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	VehicleCountWithDept getGoOnlineCountByDeptIdDeptOrArea(@Param("month") String month , @Param("deptId") Long deptId , @Param("ownerId") Long ownerId);

	/**
	 * @description: 获取指定企业的车辆总数
	 * @author: zhouxw
	 * @date: 2022/9/13 4:41 PM
	 * @param: [month, dpetIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	List<VehicleCountWithDept> getTotalCountByDeptId( @Param("deptIds") List<Long> dpetIds , @Param("ownerId") Long ownerId);

	/**
	 * @description: 获取指定企业的车辆总数
	 * 如果不指定企业，就查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 4:41 PM
	 * @param: [month, dpetIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	VehicleCountWithDept getTotalCountByDeptIdDeptOrArea( @Param("deptId") Long dpetId , @Param("ownerId") Long ownerId);

	/**
	 * @description: 获取指定企业的入网车辆数
	 * 如果不指定企业，就查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 4:41 PM
	 * @param: [month, dpetIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleCountWithDept>
	 **/
	VehicleCountWithDept getInNetCountByDeptIdDeptOrArea(@Param("nextMonthFirstDay") String nextMonthFirstDay , @Param("deptId") Long deptId , @Param("ownerId") Long ownerId);

	/**
	 * 查询企业某日的总行驶里程
 	 * @param statDate yyyyMMdd
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	double getDeptMileage(@Param("statDate") String statDate, @Param("month") String month, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);

	/**
	 * 查询车辆最大速度列表
	 * @param statDate yyyyMMdd
	 * @param month yyyyMM
	 * @param vehicleIds
	 * @return
	 */
	List<VehicleAndData> getMaxSpeedListByVehicleId(@Param("statDate") String statDate, @Param("month") String month, @Param("vehicleIds") List<Integer> vehicleIds);

	/**
	 * 根据月份查询部门行驶里程
	 * @param month
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	double getDeptMileageMonth(@Param("month") String month,@Param("deptIds") List<Long> deptIds, @Param("userId") Long userId );

	/**
	 * 查询车辆指定月份的最大速度列表
	 * @param month yyyyMM
	 * @param vehicleIds
	 * @return
	 */
	List<VehicleAndData> getMaxSpeedMonthByVehicleId(@Param("month") String month, List<Integer> vehicleIds);

	/**
	 * 查询部门指定日期的总上线时长
	 * @param statDate yyyy-MM-dd
	 * @param month yyyyMM
	 * @param deptIds
	 * @param vehicleIds
	 * @return 总运行时长，秒
	 */
	long getDeptDuration(@Param("statDate") String statDate, @Param("month") String month, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds);

	/**
	 * 查询部门指定月份的总上线时长
	 * @param month yyyyMM
	 * @param deptIds
	 * @param vehicleIds
	 * @param lastDate 指定月份的最后一天 yyyy-MM-dd
	 * @return
	 */
	long getDeptDurationMonth (@Param("month") String month, @Param("firstDate") String firstDate, @Param("lastDate") String lastDate, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds);
}

