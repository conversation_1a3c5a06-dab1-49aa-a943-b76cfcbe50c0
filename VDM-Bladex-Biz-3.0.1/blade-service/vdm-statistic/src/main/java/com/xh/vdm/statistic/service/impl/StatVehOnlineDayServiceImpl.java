package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.BdmTerminalonlinerecord;
import com.xh.vdm.statistic.entity.StatVehOnlineDay;
import com.xh.vdm.statistic.mapper.LocationMapper;
import com.xh.vdm.statistic.mapper.StatVehOnlineDayMapper;
import com.xh.vdm.statistic.service.IBdmTerminalonlinerecordService;
import com.xh.vdm.statistic.service.IStatVehOnlineDayService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.utils.BeanUtil;
import com.xh.vdm.statistic.vo.response.OnlineHourCountResponse;
import org.springblade.common.utils.DateUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.beans.PropertyDescriptor;
import java.util.*;

/**
 * <p>
 * 车辆每个小时在线时长统计 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
@Service
public class StatVehOnlineDayServiceImpl extends ServiceImpl<StatVehOnlineDayMapper, StatVehOnlineDay> implements IStatVehOnlineDayService {

	@Resource
	private LocationMapper locationMapper;

	@Resource
	private IBdmTerminalonlinerecordService onlineRecordService;


	@Override
	public void statVehOnlineDay() throws Exception {
		Date date = new Date();
		String dateStr = DateUtil.sdfHolderShort.get().format(date);
		//1.删除不属于当天时间的数据
		baseMapper.delete(Wrappers.lambdaQuery(StatVehOnlineDay.class).ne(StatVehOnlineDay::getStatDate,dateStr));

		//2.查询当天上线的车辆
		long startTime = DateUtil.getDayFirstSecondTimestampNoLine();
		long endTime = DateUtil.getDayLastSecondTimestamp(startTime);

		//查询下线时间为空的，以及下线时间比当日开始时间大的，而且上线时间小于等于今日最大时间
		//下线时间为空，表示还没有下线；下线时间比当日开始时间大，表示在今日上过线
		List<BdmTerminalonlinerecord> list = onlineRecordService.findOnlineRecord(startTime, endTime);
		List<StatVehOnlineDay> ods = new ArrayList<>();
		for(BdmTerminalonlinerecord o : list){
			StatVehOnlineDay od = getOnlineDataInOneDay(o, startTime, endTime);
			if(od != null){
				ods.add(od);
			}
		}

		//4.更新数据库
		int current = 1;
		int size = 1000;
		while(current * size <= ods.size()){
			int start = (current-1)*size;
			int end = current * size;
			List<StatVehOnlineDay> listTmp = ods.subList(start,end);
			baseMapper.saveOrUpdateVehDuration(listTmp);
			current ++;
		}
		List<StatVehOnlineDay> listTmp = ods.subList((current-1)*size,ods.size());
		baseMapper.saveOrUpdateVehDuration(listTmp);
	}


	@Override
	public List<OnlineHourCountResponse> findOnlineHourCount(List<String> hourList,List<Long> deptIds, Long userId) {
		return baseMapper.getOnlineHourCount(hourList, deptIds, userId);
	}

	/**
	 * 计算车辆每个小时的在线时长
	 * @param record
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws Exception
	 */
	private StatVehOnlineDay getOnlineDataInOneDay(BdmTerminalonlinerecord record, long startTime, long endTime) throws Exception{
		//上线时间
		long startT = startTime;
		Date onlineTime = record.getOnLineTime();
		if(onlineTime == null){
			//如果没有上线时间，则认为该数据有误，不再进行计算
			return null;
		}
		if(onlineTime.getTime()/1000 > startTime){
			startT = onlineTime.getTime()/1000;
		}
		//离线时间
		long endT = endTime;
		Date offlineTime = record.getOffLineTime();
		if(offlineTime != null && offlineTime.getTime()/1000 < endTime){
			endT = offlineTime.getTime()/1000;
		}

		//计算每个小时的在线时长
		Map<String,Long> map = new HashMap<>();
		int hourStart = DateUtil.getHour(startT);
		int hourEnd = DateUtil.getHour(endT);
		int hourTmp = hourStart;
		String dateStr = DateUtil.getDateString(startT);

		long totalDuration = 0;
		while(hourEnd >= hourTmp){
			if(hourStart == hourEnd){
				//如果在同一个小时内
				String hour = (hourStart - 1)<10?"0"+(hourStart-1):(hourStart-1)+"";
				map.put("m"+hour, endT - startT);
			}else{
				String hour = (hourTmp - 1)<10?"0"+(hourTmp-1):(hourTmp-1)+"";
				String datetimeStr = dateStr + " " + hour+":00:00";
				long preHourSecond = DateUtil.getSecondTimestampByDatetimeStr(datetimeStr);
				datetimeStr = dateStr + " " + (hourTmp<10?("0"+hourTmp):hourTmp)+":00:00";
				long nextHourSecond = DateUtil.getSecondTimestampByDatetimeStr(datetimeStr);
				//如果不在同一个小时内
				if(hourTmp == hourStart){
					//如果是开始时间
					long duration = nextHourSecond - startT;
					map.put("m"+hour,duration);
					if(duration < 0){
						System.out.println("haha, nextHourSecond="+nextHourSecond+", startT = "+startT);
					}
				}else if(hourTmp == hourEnd){
					//如果是结束时间
					long duration = endT - preHourSecond;
					map.put("m"+hour,duration);
					if(duration < 0){
						System.out.println("haha, endT ="+endT +", preHourSecond="+preHourSecond);
					}
				}else{
					//如果是中间时间
					long duration = 3600;
					map.put("m"+hour,duration);
					if(duration < 0){
						System.out.println("haha");
					}
				}
			}
			hourTmp++;
		}

		//转换到对象中
		StatVehOnlineDay od = new StatVehOnlineDay();
		od.setLicencePlate(record.getLicencePlate());
		od.setLicenceColor(record.getLicenceColor()+"");
		od.setStatDate(dateStr);
		od.setCreateTime(new Date());
		od.setUpdateTime(new Date());
		for(String c : map.keySet()){
			totalDuration += map.get(c);
			PropertyDescriptor pd = new PropertyDescriptor(c,StatVehOnlineDay.class);
			pd.getWriteMethod().invoke(od, map.get(c));
		}
		od.setTotalDuration(totalDuration);
		return od;
	}


	/**
	 * 计算车辆在线时长，算法参考 StatVehTravelDurationServiceImpl:vehTravelDurationStat
	 * @param list
	 * @return
	 * @throws Exception
	 */
	private StatVehOnlineDay getOnlineDataList(List<LocationKudu> list) throws Exception{
		if(list == null || list.size() < 1){
			return null;
		}
//		String licencePlate = list.get(0).getLicencePlate();
//		Long licenceColor = list.get(0).getLicenceColor();
		String statDate = DateUtil.getDateString(list.get(0).getTime());

		long totalDuration = 0;
		Long[] hourDuration = new Long[24];
		for(int i = 1; i < list.size(); i++){
			//当前定位点
			LocationKudu locationNow = list.get(i);
			//前一个定位点
			LocationKudu locationPre = list.get(i - 1);
			//只记录行驶点之间的时长，并且，只记录有效点之间的时长
			if(locationNow.getSpeed() > 0 && locationPre.getSpeed() > 0 && locationNow.getValid() != 0 && locationPre.getValid() != 0){
				//两点间的时长
				long duration = locationNow.getTime() - locationPre.getTime();
				if(duration > 30 * 60){
					//如果两点间的间隔大于30分钟，则认为是无效点，接着从下一个点开始计算
					continue;
				}
				//记录总时长
				totalDuration = totalDuration + duration;

				if(com.xh.vdm.statistic.utils.DateUtil.getHour(locationNow.getTime()) != com.xh.vdm.statistic.utils.DateUtil.getHour(locationPre.getTime())){
					//如果不是同一个小时内的定位点

					//分别将属于各自小时的时间计入到各自时长中
					int hourPre = com.xh.vdm.statistic.utils.DateUtil.getHour(locationPre.getTime());
					int hourNow = com.xh.vdm.statistic.utils.DateUtil.getHour(locationNow.getTime());
					//获取整点的时间戳
					long secondTimestampAtHour = 0;
					try {
						secondTimestampAtHour = com.xh.vdm.statistic.utils.DateUtil.getSecondTimestampAtHour(locationNow.getTime());
					}catch (Exception e){
						System.out.println("--->>>" + locationNow);
					}
					//属于上个小时的时长
					long durationPre = secondTimestampAtHour - locationPre.getTime();
					hourDuration[hourPre-1] = hourDuration[hourPre-1]==null?0:hourDuration[hourPre-1] + durationPre;
					//属于本小时的时长
					long durationNow = locationNow.getTime() - secondTimestampAtHour;
					hourDuration[hourNow-1] = hourDuration[hourNow-1]==null?0:hourDuration[hourNow-1] + durationNow;
				}else{
					//如果是同一个小时内的定位点
					//更新小时内的时长
					int hour = com.xh.vdm.statistic.utils.DateUtil.getHour(locationNow.getTime());
					hourDuration[hour-1] = hourDuration[hour-1]==null?0:hourDuration[hour-1] + duration;
				}
			}
		}

		//组装数据
		StatVehOnlineDay svod = new StatVehOnlineDay();
//		svod.setLicencePlate(licencePlate);
//		svod.setLicenceColor(licenceColor+"");
		svod.setCreateTime(new Date());
		svod.setUpdateTime(new Date());
		svod.setStatDate(statDate);
		svod.setTotalDuration(totalDuration);
		for(int i = 0 ; i < hourDuration.length; i++){
			Long duration = hourDuration[i];
			String hour = ((i)<10)?("0"+(i)):((i)+"");
			PropertyDescriptor pd = new PropertyDescriptor("m"+hour,StatVehOnlineDay.class);
			pd.getWriteMethod().invoke(svod, duration);
		}
		return svod;
	}


}
