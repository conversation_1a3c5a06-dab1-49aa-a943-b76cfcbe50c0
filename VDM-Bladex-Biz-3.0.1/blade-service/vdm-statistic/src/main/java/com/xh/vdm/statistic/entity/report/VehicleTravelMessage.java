package com.xh.vdm.statistic.entity.report;

import lombok.Data;

import java.util.List;

/**
 * 车辆运行情况
 */
@Data
public class VehicleTravelMessage {

	//总里程
	private Double totalMileage;
	//总时长
	private String totalDurationInHour;
	//平均里程
	private Double averageMileage;
	//平均时长
	private String averageDurationInHour;
	//车辆运行情况列表
	private List<VehicleTravelNode> travelList;
	//车辆总数
	private Integer totalCount;
}
