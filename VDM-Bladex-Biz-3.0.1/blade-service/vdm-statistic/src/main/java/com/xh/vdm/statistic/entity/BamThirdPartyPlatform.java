package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-12
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BamThirdPartyPlatform implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 平台名称
     */
    private String name;

    /**
     * 目标IP地址
     */
    private String ip;

    /**
     * 目标端口号
     */
    private Integer port;

    /**
     * 运营商编号
     */
    private String operatorNo;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 是否加密 0-未加密，1-加密
     */
    private Integer isEncrypt;

    private Integer isTop;

    private String remark;

    @TableField("M1")
    private String m1;

    @TableField("IA1")
    private String ia1;

    @TableField("IC1")
    private String ic1;

    private int enabled;

    /**
     * 从链路IP地址
     */
    private String subIp;

    private Integer subPort;

    private Long account;

    private String accountName;

    private Integer isTransmit;


}
