package com.xh.vdm.statistic.vo.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/2/23
 * @description
 */
@Data
@ApiModel("分页查询请求参数Page类")
public class PageParam {

    /**
     * 当前开始行数
     */
    @ApiModelProperty(value = "当前页,默认为1", example = "1")
    private Integer start = 0;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "页大小,默认为10", example = "10")
    private Integer count = 10;

    public Integer getCurrent() {
        return (start/count+1);
    }

    public Integer getSize() {
        return count;
    }

    public Integer getStart() {
        return start;
    }

    public void setStart(Integer start) {
        this.start = start;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
