package com.xh.vdm.statistic.utils;

import com.xh.vdm.statistic.entity.StatTaskLog;
import com.xh.vdm.statistic.service.IStatTaskLogService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @Description: 日志工具类
 * @Author: zhouxw
 * @Date: 2022/9/15 10:52 AM
 */
@Component
public class LogUtil {

    @Resource
    private IStatTaskLogService taskLogService;

    /**
     * @description: 记录跑批任务日志信息
     * @author: zhouxw
     * @date: 2022/9/15 10:54 AM
     * @param: [taskType 任务类型, taskDesc 任务描述, startTime 开始时间, endTime 结束时间]
     * @return: void
     **/
    public void insertStatLog(String taskType , String taskDesc , String taskParam ,  Date startTime , Date endTime , String result , String resultDesc){

        if(endTime.getTime() == 0){
            endTime = new Date();
        }

        long totalTime = endTime.getTime() - startTime.getTime();
        StatTaskLog log = new StatTaskLog();
        log.setResult(result);
        log.setResultDesc(resultDesc);
        log.setTaskType(taskType);
        log.setTaskDesc(taskDesc==null?"":taskDesc.length()>1998?taskDesc.substring(0 , 1998):taskDesc);
        log.setTaskParam(taskParam);
        log.setStartTime(startTime);
        log.setEndTime(endTime);
        log.setCreateTime(new Date());
        log.setTotalTime(totalTime);
        taskLogService.save(log);
    }



    public void insertStatProcessLog(String taskType , String content){
        taskLogService.addStatTaskProcessLog(taskType , content);
    }


}
