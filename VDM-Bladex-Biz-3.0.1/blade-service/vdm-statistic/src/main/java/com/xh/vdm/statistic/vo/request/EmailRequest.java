package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 邮件发送查询条件
 * @Author: zhouxw
 * @Date: 2023/3/2 10:48
 */
@Data
public class EmailRequest {

    //邮件类型：1 日报  2 月报
    private String type;

    //邮件发送类型：1 定时自动发送  2 手动调用接口发送  3 手动页面发送
    @JsonProperty("send_type")
    private String sendType;

    //用户名
    private String username;

    //客户邮箱（接收邮件的邮箱）
    @JsonProperty("receive_mail_address")
    private String receiveMailAddress;

    //数据日期
    private String dataDate;

    //邮件发送结果： 1 成功  2 失败
    @JsonProperty("send_result")
    private String sendResult;

    //开始时间（针对统计时间）
    @JsonProperty("start_time")
    private Date startTime;

    //结束时间（针对统计时间）
    @JsonProperty("end_time")
    private Date endTime;

    private Integer current;

    private Integer size;


}
