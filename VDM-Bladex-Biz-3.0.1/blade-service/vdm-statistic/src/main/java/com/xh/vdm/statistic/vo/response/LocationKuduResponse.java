package com.xh.vdm.statistic.vo.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * @Description: Kudu中的定位数据 pos_gn.locations
 */
@Data
public class LocationKuduResponse {
	private String id;
	private Long targetId;
	private Byte targetType;
	private Long deviceId;
	private String deviceNum;
	private Byte deviceType;
	private Double longitude;
	private Double latitude;
	private Integer altitude;
	private Float speed;
	private Short bearing;
	private Long time;
	private Integer status;
	private Integer alarm;
	private Float mileage;

	private Byte valid;

	private String auxiliary;

	private Long recvTime;

	private Byte batch;

	private Byte correction;

	private Byte posSys;

	private String targetName;

	private Byte gnssNum;
	private Byte wireless;
	private Short ioState;
	private Byte charge;
	private Float realSpeed;
	private Float oilMass;
	private Short ioStatus;
}
