package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse;
import lombok.Data;

import java.util.Date;

/**
 * @Description: 车辆地图超速 DB 缓存
 * @Author: zhouxw
 * @Date: 2023/2/8 17:46
 */
@Data
@TableName("cache_seg_limit_speed_map")
public class CacheSegLimitSpeedMapResponse extends SegLimitSpeedMapResponse {

    //车组id
    private Long deptId;
    //车牌颜色code
    private String licenceColorCode;
    //车辆类型code
    private Long vehicleModelCode;
    //车辆归属id
    private Long vehicleOwnerId;
    //车辆接入方式code
    private String accessModeCode;

    private Date createTime;

    private String note;

}
