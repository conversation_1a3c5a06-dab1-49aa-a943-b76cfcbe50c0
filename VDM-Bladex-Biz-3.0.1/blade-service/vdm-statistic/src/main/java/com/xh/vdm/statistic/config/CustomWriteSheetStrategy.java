package com.xh.vdm.statistic.config;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;

/**
 * 自定义write拦截器
 * 解决在使用write写入数据时设置 sheetName 不生效的问题
 */
public class CustomWriteSheetStrategy implements SheetWriteHandler{

		private Integer sheetNo;
		private String sheetName;
		public CustomWriteSheetStrategy(String sheetName) {
			this.sheetName = sheetName;
		}
		public CustomWriteSheetStrategy(Integer sheetNo, String sheetName) {
			this.sheetNo = sheetNo;
			this.sheetName = sheetName;
		}
		@Override
		public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
		}
		/**
		 * 功能：动态修改模板中sheet的名称
		 * sheet创建完成后调用
		 * @param writeWorkbookHolder
		 * @param writeSheetHolder
		 */
		@Override
		public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
			if (sheetName == null) {
				return;
			}
			if (sheetNo == null) {
				sheetNo = 0;
			}
			writeWorkbookHolder.getWorkbook().setSheetName(sheetNo, sheetName);
		}
}
