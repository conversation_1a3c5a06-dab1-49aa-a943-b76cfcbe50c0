package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthWithLineRequest;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.LocationQualityDetailResponse;
import com.xh.vdm.statistic.vo.response.LocationQualityResponse;

import java.util.List;

/**
 * <p>
 * 定位数据质量表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
public interface ILocationQualityService extends IService<LocationQuality> {

    /**
     * @description: 统计指定日期的定位数据的数据质量
     * day 格式为 yyyyMMdd
     * @author: zhouxw
     * @date: 2022/9/1 10:28 AM
     * @param: [day 指定的定位数据日期]
     * @return: boolean
     **/
    boolean locationQualityStat(String day) throws Exception;


    /**
     * @description: 判断月表是否存在，不存在，则创建新的月表
     * @author: zhouxw
     * @date: 2022/9/2 8:13 AM
     * @param: [month]
     * @return: void
     **/
    void checkTableExistAndCreate(String month);

    /**
     * @description: 查询数据合格率
     * @author: zhouxw
     * @date: 2022/9/6 4:14 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.vo.response.LocationQualityResponse
     **/
    LocationQualityResponse findLocaitonQualityRate(RateRequest request);

    /**
     * @description: 查询车辆轨迹不合格的数据明细
     * @author: zhouxw
     * @date: 2022/9/9 2:46 PM
     * @param: [param]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.LocationCompleteDetailResponse>
     **/
    List<LocationQualityDetailResponse> findUnCompleteDetailList(DetailParam param);

    /**
     * @description: 根据条件查询总异常定位数量
     * @author: zhouxw
     * @date: 2022/9/13 10:31 AM
     * @param: [param]
     * @return: int
     **/
    int findTotalErrorCount(DetailDayParam param);

    /**
     * @description: 根据企业id获取数据合格率
     * @author: zhouxw
     * @date: 2022/9/13 6:00 PM
     * @param: [month, deptIds]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleRateWithDept>
     **/
    List<VehicleRateWithDept> findQualityRateByDeptId(String month , List<Long> deptIds , Long ownerId);


	/**
	 * @description: 根据企业id获取数据合格率
	 * 如果不指定部门，就查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 6:00 PM
	 * @param: [month, deptIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleRateWithDept>
	 **/
	VehicleRateWithDept findQualityRateByDeptIdDeptOrArea(String month , Long deptId , Long ownerId);

	/**
	 * @description:
	 * @author: zhouxw
	 * @date: 2023-07-204 18:25:40
	 * @param: [dateList yyyyMMdd, deptIds, userId]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndLocationQuality>
	 **/
	List<DateAndLocationQuality> findQualityRateByDate(List<String> dateList, List<Long> deptIds, Long userId ) throws Exception;

	/**
	 * 根据条件查询数据质量信息
	 * @param request  dateList: yyyy-MM-dd  month: yyyyMM
	 * @return
	 * @throws Exception
	 */
	List<VehicleAndQuality> findQualityByCondition(CommonBaseCrossMonthRequest request) throws Exception;

}
