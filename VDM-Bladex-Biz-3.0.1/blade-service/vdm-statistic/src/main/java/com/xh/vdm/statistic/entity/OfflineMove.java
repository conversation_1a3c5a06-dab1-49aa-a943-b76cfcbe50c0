package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "bdm_offline_move", autoResultMap = true)
public class OfflineMove implements Serializable {

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

	@TableField(value = "vehicle_id")
	private Integer vehicleId;

	@TableField(value = "licence_color")
	private String licenceColor;

	@TableField(value = "licence_plate")
	private String licencePlate;

	@TableField(value = "start_time")
	private Date startTime;

	@TableField(value = "end_time")
	private Date endTime;

	@TableField(value = "distance")
	private Double distance;
}
