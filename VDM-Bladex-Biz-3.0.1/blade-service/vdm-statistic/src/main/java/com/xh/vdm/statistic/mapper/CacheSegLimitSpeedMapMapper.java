package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.CacheSegLimitSpeedMapResponse;
import com.xh.vdm.statistic.vo.request.SegLimitSpeedMapRequest;
import com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 车辆地图超速
 *
 * <AUTHOR>
 * @since 2022-09-14
 */
public interface CacheSegLimitSpeedMapMapper extends BaseMapper<CacheSegLimitSpeedMapResponse> {

    /**
     * @description: 查询车辆地图超速信息
     * @author: zhouxw
     * @date: 2023-02-40 17:46:00
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    List<SegLimitSpeedMapResponse> getList(@Param("param") SegLimitSpeedMapRequest segLimitSpeedMapRequest);


    /**
     * @description: 分页查询车辆地图超速信息
     * @author: zhouxw
     * @date: 2023-02-40 17:46:00
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    IPage<SegLimitSpeedMapResponse> getList(IPage<SegLimitSpeedMapResponse> page, @Param("param") SegLimitSpeedMapRequest segLimitSpeedMapRequest);


}
