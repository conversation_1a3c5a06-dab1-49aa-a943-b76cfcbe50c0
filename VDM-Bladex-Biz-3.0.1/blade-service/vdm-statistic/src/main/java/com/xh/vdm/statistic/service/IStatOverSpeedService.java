package com.xh.vdm.statistic.service;


import com.xh.vdm.statistic.entity.OverSpeedWithDeptAndDate;

import java.util.List;

/**
 * @Description: 超速统计
 * @Author: zhouxw
 * @Date: 2022/11/15 10:03 PM
 */
public interface IStatOverSpeedService {

    /**
     * @description: 根据 企业id 和 月份 统计当月的平均超速次数
     * 如果没有指定企业，那么就分页查询所有企业的
     * @author: zhouxw
     * @date: 2022/11/15 11:32 PM
     * @param: [deptId, month, count 分页时每页的数量, start 分页开始的下标]
     * @return: java.util.List<com.xh.vdm.statistic.entity.OverSpeedWithDeptAndDate>
     **/
    List<OverSpeedWithDeptAndDate> statOverSpeedAverageCount(Long deptId , String month, int count , int start) throws Exception;


    /**
     * @description: 根据 企业id 和 月份 查询当月的平均超速次数
     * 如果没有指定企业，就查询整个区域的
     * @author: zhouxw
     * @date: 2022/11/15 11:32 PM
     * @param: [deptId, month, count 分页时每页的数量, start 分页开始的下标]
     * @return: java.util.List<com.xh.vdm.statistic.entity.OverSpeedWithDeptAndDate>
     **/
    OverSpeedWithDeptAndDate findOverSpeedAverageCount(Long deptId , Long startSecondTimestamp , Long endSecondTimestamp) throws Exception;


}
