package com.xh.vdm.statistic.task;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.xh.vdm.statistic.config.CustomTemplateSheetStrategy;
import com.xh.vdm.statistic.config.CustomWriteSheetStrategy;
import com.xh.vdm.statistic.config.ExcelExportFontAndStyleConfig;
import com.xh.vdm.statistic.config.ExcelExportRowHeightConfig;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.controller.VehicleRunningController;
import com.xh.vdm.statistic.dto.alarm.report.ReportPath;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.report.*;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.*;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthDurationRequest;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.request.NotLocationRequest;
import com.xh.vdm.statistic.vo.response.ErrLocationMoveResponse;
import com.xh.vdm.statistic.vo.response.NotLocationResponse;
import com.xh.vdm.statistic.vo.response.VehicleOnlineOrOfflineResponse;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.utils.FileUtil;
import org.springblade.core.tool.utils.Func;
import org.springblade.system.cache.SysCache;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 日报、月报跑批任务
 */
@Slf4j
@Component
public class ReportTask {


	@Resource
	private IReportService reportService;

	//日报（word）
	@Value("${report.day.summary.template-path}")
	private String daySummaryTemplatePath;
	@Value("${report.day.summary.file-path}")
	private String daySummaryFilePath;
	@Value("${report.day.summary.proxy-path}")
	private String daySummaryProxyPath;

	//月报（word）
	@Value("${report.month.summary.template-path}")
	private String monthSummaryTemplatePath;
	@Value("${report.month.summary.file-path}")
	private String monthSummaryFilePath;
	@Value("${report.month.summary.proxy-path}")
	private String monthSummaryProxyPath;

	//日报（报警类 excel）
	@Value("${report.day.alarm.template-path}")
	private String dayAlarmTemplatePath;
	@Value("${report.day.alarm.file-path}")
	private String dayAlarmFilePath;
	@Value("${report.day.alarm.proxy-path}")
	private String dayAlarmProxyPath;

	// 月报（报警类 excel）
	@Value("${report.month.alarm.template-path}")
	private String monthAlarmTemplatePath;
	@Value("${report.month.alarm.file-path}")
	private String monthAlarmFilePath;
	@Value("${report.month.alarm.proxy-path}")
	private String monthAlarmProxyPath;

	//日报（车辆运行类 excel）
	@Value("${report.day.running.template-path}")
	private String dayRunningTemplatePath;
	@Value("${report.day.running.file-path}")
	private String dayRunningFilePath;
	@Value("${report.day.running.proxy-path}")
	private String dayRunningProxyPath;
	@Value("${report.month.running.template-path}")
	private String monthRunningTemplatePath;
	@Value("${report.month.running.file-path}")
	private String monthRunningFilePath;
	@Value("${report.month.running.proxy-path}")
	private String monthRunningProxyPath;

	@Autowired
	private StatisticsService statisticsService;

	@Resource
	private CacheUtil cacheUtil;

	@Resource
	private VdmUserInfoUtil vdmUserInfoUtil;

	@Resource
	private IBdmVehicleService vehicleService;


	@Resource
	private IVehicleStatService vehicleStatService;

	@Resource
	private IBamThirdPartyPlatformService thirdPartyPlatformService;

	@Resource
	private IBladeDeptService deptService;

	@Resource
	private ICacheVehicleOnlineOrOfflineService cacheVehicleOnlineOrOfflineService;

	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(10, 20, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(5));


	@Resource
	private CommonBusiUtil commonBusiUtil;

	@Value("${db_cache.enable:true}")
	private boolean dbCacheEnable;

	@Resource
	private VehicleRunningController runningController;

	@Resource
	private LogUtil logUtil;

	@Resource
	private IStatReportDayService reportDayService;

	@Resource
	private IStatReportMonthService reportMonthService;

	@Resource
	private IStatVehRunningStateService statService;



	/**
	 * 执行日报统计
	 * 支持指定多个部门，但不支持多天
	 * @param statDate 指定的跑批日期，如指定 2024-02-01，则跑批2024-02-01的数据
	 * @param deptIds 指定跑批部门，如果有多个部门，使用 , 分隔
	 */
	@XxlJob("reportSummaryDay")
	public void reportSummaryDay(String deptIds, String statDate){
		XxlJobHelper.log("将执行跑批操作，参数为  statDate="+statDate +", deptIds = " + deptIds);
		//1.报表类型判断
		long startTime = 0;
		long start = System.currentTimeMillis();
		long end = 0;
		try{
			//如果指定跑批日期，则跑批该日期的报表，否则跑批前一天的报表
			if(StringUtils.isEmpty(statDate)){
				statDate = DateUtil.getDateBeforeDayStr(new Date(), 1);
			}
			//判断是否是日报
			DateUtil.sdfHolderShort.get().parse(statDate);
		}catch (Exception ex){
			//如果日期格式错误
			log.error("日期["+statDate+"]格式错误",ex);
			XxlJobHelper.log("执行跑批失败，"+ex.getMessage());
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY,"统计企业日报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败" + "日期["+statDate+"]格式错误");
			return;
		}
		//2.执行统计（针对所有企业执行）
		try {
			//2.1 查询要跑批的企业
			List<BladeDept> list;
			if(!StringUtils.isEmpty(deptIds)){
				//如果指定了企业， 则只执行相应企业的跑批
				List<Long> ids = Func.toLongList(deptIds);
				list = deptService.listByIds(ids);
			}else{
				//如果没有指定企业，则查询所有企业
				list = deptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
			}

			//记录执行失败情况
			List<Long> failDeptIds = new ArrayList<>();
			for(BladeDept dept : list){
				long deptId = dept.getId();
				try {
					reportDayForOnce(deptId, statDate);
				}catch (Exception e){
					log.error("执行数据导出失败",e);
					failDeptIds.add(deptId);
				}
			}
			if(list.size() <= failDeptIds.size()){
				//所有部门执行失败，则跑批执行失败
				end = System.currentTimeMillis();
				logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY,"统计企业日报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败：所有部门跑批失败");
				XxlJobHelper.log("执行跑批失败，所有部门跑批失败");
				return;
			}
			if(failDeptIds.size() > 0){
				end = System.currentTimeMillis();
				logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY,"统计企业日报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"部分成功，企业["+ JSON.toJSONString(failDeptIds)+"]执行失败");
				XxlJobHelper.log("部分成功，跑批失败的企业："+JSON.toJSONString(failDeptIds));
				return;
			}
		}catch (Exception ex){
			end = System.currentTimeMillis();
			log.error("统计日报表数据出错",ex);
			logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY,"统计企业日报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败："+ex.getMessage());
			XxlJobHelper.log("执行跑批失败，"+ex.getMessage());
			return ;
		}
		end = System.currentTimeMillis();
		logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY,"统计企业日报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
		XxlJobHelper.log("执行成功");
	}

	private boolean reportDayForOnce(Long deptId, String statDate) throws Exception{
		long startTime = DateUtil.getDayFirstSecondTimestamp(statDate);
		long endTime = DateUtil.getDayLastSecondTimestamp(startTime);
		ReportSummary summary = reportService.reportSummary(deptId, statDate, startTime, endTime);
		summary.setDateDesc(DateUtil.formatDateFromYMD(summary.getDateStr()));
		//3.导出报表数据
		ReportPath path = exportReport(CommonConstant.REPORT_TYPE_DAY, summary);
		//4.保存到数据库中
		//查询数据库中是否已经存在，如果已经存在，就删除原来的文件，并且更新数据库信息
		StatReportDay rDay = new StatReportDay();
		rDay.setDeptId(deptId);
		rDay.setDate(statDate);
		List<StatReportDay> list = reportDayService.list(Wrappers.lambdaQuery(StatReportDay.class).eq(StatReportDay::getDeptId, deptId).eq(StatReportDay::getDate,statDate).eq(StatReportDay::getType,CommonConstant.REPORT_TYPE_DAY_REPORT).orderByDesc(StatReportDay::getUpdateTime));
		if(list != null && list.size() > 0){
			//保存新报表记录
			StatReportDay rdo = list.get(0);
			StatReportDay rd = new StatReportDay();
			BeanUtils.copyProperties(rdo, rd);
			rd.setUpdateTime(new Date());
			rd.setId(null);
			rd.setDeptId(deptId);
			rd.setDate(statDate);
			rd.setFilePath(path.getFilePath());
			rd.setProxyFileUrl(path.getProxyPath());
			rd.setType(CommonConstant.REPORT_TYPE_DAY_REPORT);
			reportDayService.save(rd);
			//5.如果之前已经出过报表，则删除之前的文件，并且更改数据库记录；如果数据多于一条，则删除多余数据
			//删除之前生成的文件和数据
			for(StatReportDay day : list){
				//删除文件
				String fPath = day.getFilePath();
				if(!StringUtils.isEmpty(fPath)){
					boolean flag = FileUtil.deleteQuietly(new File(fPath));
					if(!flag){
						log.error("删除日报文件["+fPath+"]失败");
					}
				}
				//删除数据库数据
				boolean delFlag = reportDayService.removeById(day.getId());
				if(!delFlag){
					log.error("删除日报数据[id="+day.getId()+"]失败");
				}
			}
		}else{
			//如果之前没有统计过
			StatReportDay rd = new StatReportDay();
			rd.setUpdateTime(new Date());
			rd.setDeptId(deptId);
			rd.setDate(statDate);
			rd.setId(null);
			rd.setFilePath(path.getFilePath());
			rd.setProxyFileUrl(path.getProxyPath());
			rd.setType(CommonConstant.REPORT_TYPE_DAY_REPORT);
			rd.setCreateTime(new Date());
			reportDayService.save(rd);
		}
		return true;
	}


	/**
	 * 执行月报统计
	 * 支持指定多个部门，但不支持指定多个月份
	 * @param statMonth 指定的跑批日期，如指定 2024-02，则跑批2024-02的数据
	 * @param deptIds 指定跑批部门，如果有多个部门，使用 , 分隔
	 */
	@XxlJob("reportSummaryMonth")
	public void reportSummaryMonth (String deptIds, String statMonth) {
		log.info("run company month summary report of {} for deptIds: {}", statMonth, deptIds);
		XxlJobHelper.log("任务开始：企业月报汇总数据，参数：statMonth=" + statMonth + ", deptIds=" + deptIds);

		//1.报表类型判断
		long start = System.currentTimeMillis();
		long end;
		try {
			//如果指定跑批日期，则跑批该日期的报表，否则跑批前一天的报表
			if (StringUtils.isBlank(statMonth)) {
				statMonth = DateUtil.getLastMonthString();
			}

			//判断是否是月报
			DateUtil.sdfHolderMonth.get().parse(statMonth);
		} catch (Exception e) {
			//如果日期格式错误
			end = System.currentTimeMillis();
			log.error("fail judge month when reportSummaryMonth: " + e.getMessage(), e);
			XxlJobHelper.log("【企业月报汇总数据】任务初始化失败：" + e.getMessage());
			this.logUtil.insertStatLog(
				StatisticConstants.TASK_REPORT_MONTH,
				"定时任务：企业月报汇总数据",
				statMonth,
				new Date(start),
				new Date(end),
				StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
				"执行失败，月份格式错误：" + statMonth
			);

			return;
		}

		//2.执行统计（针对所有企业执行）
		try {
			//2.1 查询要跑批的企业
			List<BladeDept> deptList;
			if (StringUtils.isNotBlank(deptIds)) {
				//如果指定了企业， 则只执行相应企业的跑批
				deptList = this.deptService.listByIds(Func.toLongList(deptIds));
			} else {
				//如果没有指定企业，则查询所有企业
				deptList = this.deptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
			}

			//记录执行失败情况
			List<Long> failDeptIdList = new ArrayList<>();
			for (BladeDept dept : deptList) {
				long deptId = dept.getId();
				try {
					this.reportMonthForOnce(deptId, statMonth);
				} catch (Exception e) {
					log.error("fail run for dept: " + deptId + " when reportSummaryMonth: " + e.getMessage(), e);
					failDeptIdList.add(deptId);
				}
			}

			end = System.currentTimeMillis();
			if (deptList.size() <= failDeptIdList.size()) {
				//所有部门执行失败，则跑批执行失败
				XxlJobHelper.log("【企业月报汇总数据】任务失败：所有单位均未能统计。");
				this.logUtil.insertStatLog(
					StatisticConstants.TASK_REPORT_MONTH,
					"定时任务：企业月报汇总数据",
					statMonth,
					new Date(start),
					new Date(end),
					StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
					"执行失败：所有单位均未能统计。"
				);

				return;
			} else if (failDeptIdList.size() > 0) {
				XxlJobHelper.log("【企业月报汇总数据】任务部分失败，未能统计的单位：" + JSON.toJSONString(failDeptIdList));
				this.logUtil.insertStatLog(
					StatisticConstants.TASK_REPORT_MONTH,
					"定时任务：企业月报汇总数据",
					statMonth,
					new Date(start),
					new Date(end),
					StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
					"部分执行失败，未能统计的单位：" + JSON.toJSONString(failDeptIdList)
				);

				return;
			}
		} catch (Exception e) {
			end = System.currentTimeMillis();
			log.error("fail run when reportSummaryMonth: " + e.getMessage(), e);
			XxlJobHelper.log("【企业月报汇总数据】任务运行失败：" + e.getMessage());
			this.logUtil.insertStatLog(
				StatisticConstants.TASK_REPORT_MONTH,
				"定时任务：企业月报汇总数据",
				statMonth,
				new Date(start),
				new Date(end),
				StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
				"执行失败，运行阶段：" + e.getMessage()
			);

			return;
		}

		end = System.currentTimeMillis();
		XxlJobHelper.log("【企业月报汇总数据】任务成功。");
		this.logUtil.insertStatLog(
			StatisticConstants.TASK_REPORT_MONTH,
			"定时任务：企业月报汇总数据",
			statMonth,
			new Date(start),
			new Date(end),
			StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,
			"执行成功。"
		);
	}

	private void reportMonthForOnce (Long deptId, String statDate) throws Exception {
		long startTime = DateUtil.getMonthFirstSecondTimestamp(statDate);
		long endTime = DateUtil.getMonthLastSecondTimestamp(statDate);
		ReportSummary summary = this.reportService.reportSummary(deptId, statDate, startTime, endTime);
		//3.导出报表数据
		ReportPath path = this.exportReport(CommonConstant.REPORT_TYPE_MONTH, summary);
		//4.保存到数据库中
		//查询数据库中是否已经存在，如果已经存在，就删除原来的文件，并且更新数据库信息
//		StatReportMonth rMonth = new StatReportMonth();
//		rMonth.setDeptId(deptId);
//		rMonth.setMonth(statDate);
		List<StatReportMonth> list = this.reportMonthService.list(
			Wrappers.lambdaQuery(StatReportMonth.class).eq(StatReportMonth::getDeptId, deptId).eq(StatReportMonth::getMonth, statDate).orderByDesc(StatReportMonth::getUpdateTime)
		);
		if (CollectionUtils.isNotEmpty(list)) {
			//保存新报表记录
			StatReportMonth srm0 = list.get(0);
			StatReportMonth srm1 = new StatReportMonth();
			BeanUtils.copyProperties(srm0, srm1);
			srm1.setId(null);
			srm1.setDeptId(deptId);
			srm1.setMonth(statDate);
			srm1.setType(CommonConstant.REPORT_TYPE_DAY_REPORT);
			srm1.setFilePath(path.getFilePath());
			srm1.setProxyFileUrl(path.getProxyPath());
			srm1.setUpdateTime(new Date());
			this.reportMonthService.save(srm1);
			//5.如果之前已经出过报表，则删除之前的文件，并且更改数据库记录；如果数据多于一条，则删除多余数据
			//删除之前生成的文件和数据
			for (StatReportMonth srm : list) {
				//删除文件
				String fPath = srm.getFilePath();
				if (StringUtils.isNotBlank(fPath)) {
					if (!FileUtil.deleteQuietly(new File(fPath))) {
						log.error("fail delete file when reportMonthForOnce: " + fPath);
					}
				}

				//删除数据库数据
				this.reportMonthService.removeById(srm.getId());
			}
		} else {
			//如果之前没有统计过
			StatReportMonth statReportMonth = new StatReportMonth();
			statReportMonth.setId(null);
			statReportMonth.setDeptId(deptId);
			statReportMonth.setMonth(statDate);
			statReportMonth.setType(CommonConstant.REPORT_TYPE_DAY_REPORT);
			statReportMonth.setFilePath(path.getFilePath());
			statReportMonth.setProxyFileUrl(path.getProxyPath());
			statReportMonth.setCreateTime(new Date());
			statReportMonth.setUpdateTime(new Date());
			this.reportMonthService.save(statReportMonth);
		}
	}


	/**
	 * 导出日报数据到文件，docx文件
	 * @param type 报表类型
	 * @param summary 统计数据
	 * @return 报表文件的代理地址
	 */
	private ReportPath exportReport(String type, ReportSummary summary){

		String fileName = "";
		String filePath = "";
		String resFileUrl = "";
		String templatePath = "";
		ReportPath path = new ReportPath();
		//1.判断导出类型
		if(CommonConstant.REPORT_TYPE_DAY.equals(type)){
			//如果是日报
			//文件名称，防止冲突
			fileName = summary.getDeptName() + "日报表" + "(" + summary.getDateDesc() + ")_" + System.currentTimeMillis() +".docx";
			filePath = this.daySummaryFilePath + fileName;
			//代理地址，nginx上使用的代理地址
			resFileUrl = this.daySummaryProxyPath + fileName;
			templatePath = this.daySummaryTemplatePath;
		}else{
			//如果是月报
			//文件名称，防止冲突
			fileName = summary.getDeptName() + "月报表" + "(" + summary.getDateDesc() + ")" + System.currentTimeMillis() +".docx";
			filePath = this.monthSummaryFilePath + fileName;
			//代理地址，nginx上使用的代理地址
			resFileUrl = this.monthSummaryProxyPath + fileName;
			templatePath = this.monthSummaryTemplatePath;
		}

		XWPFTemplate template = null;
		FileOutputStream fos = null;
		try {
			//2.编译模板，渲染数据
			LoopRowTableRenderPolicy hackLoopTableRenderPolicy = new LoopRowTableRenderPolicy();
			Configure config =
				Configure.builder().bind("longOfflineVehicleList", hackLoopTableRenderPolicy)
					.bind("overSpeedList", hackLoopTableRenderPolicy)
					.bind("overSpeedDriverList", hackLoopTableRenderPolicy)
					.bind("fatigueDriverList", hackLoopTableRenderPolicy)
					.bind("nightDriverList", hackLoopTableRenderPolicy)
					.build();
			template = XWPFTemplate.compile(templatePath, config).render(summary);

			//3.写入到指定目录位置
			fos = new FileOutputStream(filePath);
			template.write(fos);
			path.setFilePath(filePath);
			path.setProxyPath(resFileUrl);
			return path;
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if(fos != null){
				try {
					fos.flush();
					fos.close();
				}catch (Exception e){}
			}
			if(template != null){
				try{
					template.close();
				}catch (Exception e){}
			}
		}
		path.setFilePath(filePath);
		path.setProxyPath(resFileUrl);
		return path;
	}


	/**
	 * 统计企业报警日报
	 * @param deptIds 指定的部门列表
	 * @param statDate 统计日期
	 */
	@XxlJob("reportAlarmDay")
	public void reportAlarmDay(String deptIds, String statDate){
		XxlJobHelper.log("将执行跑批操作，参数为  statDate="+statDate +", deptIds = " + deptIds);
		//1.判断入参
		long startTime = 0;
		long start = System.currentTimeMillis();
		long end = 0;
		try{
			//如果指定跑批日期，则跑批该日期的报表，否则跑批前一天的报表
			if(StringUtils.isEmpty(statDate)){
				statDate = DateUtil.getDateBeforeDayStr(new Date(), 1);
			}
			//判断日期格式是否正确
			DateUtil.sdfHolderShort.get().parse(statDate);
		}catch (Exception ex){
			//如果日期格式错误
			log.error("日期["+statDate+"]格式错误",ex);
			XxlJobHelper.log("执行跑批失败，"+ex.getMessage());
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY,"统计企业日报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败" + "日期["+statDate+"]格式错误");
			return;
		}

		//2.执行统计
		try {
			//2.1 查询要跑批的企业
			List<BladeDept> list;
			if(!StringUtils.isEmpty(deptIds)){
				//如果指定了企业， 则只执行相应企业的跑批
				List<Long> ids = Func.toLongList(deptIds);
				list = deptService.listByIds(ids);
			}else{
				//如果没有指定企业，则查询所有企业
				list = deptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
			}

			//记录执行失败情况
			List<Long> failDeptIds = new ArrayList<>();
			for(BladeDept dept : list){
				long deptId = dept.getId();
				try {
					reportAlarmDayForOnce(deptId, statDate);
				}catch (Exception e){
					log.error("执行数据导出失败",e);
					failDeptIds.add(deptId);
				}
			}
			if(list.size() <= failDeptIds.size()){
				//所有部门执行失败，则跑批执行失败
				end = System.currentTimeMillis();
				logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY_ALARM,"统计企报警日报报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败：所有部门跑批失败");
				XxlJobHelper.log("执行跑批失败，所有部门跑批失败");
				return;
			}
			if(failDeptIds.size() > 0){
				end = System.currentTimeMillis();
				logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY_ALARM,"统计企报警日报报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"部分成功，企业["+ JSON.toJSONString(failDeptIds)+"]执行失败");
				XxlJobHelper.log("部分成功，跑批失败的企业："+JSON.toJSONString(failDeptIds));
				return;
			}
		}catch (Exception ex){
			end = System.currentTimeMillis();
			log.error("统计月报表数据出错",ex);
			logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY_ALARM,"统计企报警日报报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败："+ex.getMessage());
			XxlJobHelper.log("执行跑批失败，"+ex.getMessage());
			return ;
		}
		end = System.currentTimeMillis();
		logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY_ALARM,"统计企报警日报报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
		XxlJobHelper.log("执行成功");

	}


	private void reportAlarmDayForOnce(Long deptId, String statDate) throws Exception{
		//1.统计报警相关数据
		long startTime = DateUtil.getDayFirstSecondTimestamp(statDate);
		long endTime = DateUtil.getDayLastSecondTimestamp(startTime);
		AlarmSummary summary = reportService.alarmSummary(deptId, statDate, startTime, endTime);
		List<AlarmTypeAndList> list = summary.getAlarmDetailList();

		//2.执行数据导出
		String fileName = summary.getDeptName() + "报警日报表" + "(" + summary.getStatDate() + ")" + "_" + System.currentTimeMillis() +".xlsx";
		String filePath = dayAlarmFilePath + fileName;
		//2.1 按照模板导出统计数据
		ExcelWriter writer = this.exportAlarm(CommonConstant.REPORT_TYPE_DAY, summary, filePath);
		String proxyPath = dayAlarmProxyPath + fileName;


		//2.2 导出报警详情数据，每种报警一个sheet，从1开始
		//以write的方式导出
		if(StringUtils.isEmpty(filePath)){
			//如果统计数据导出失败，则不再向下执行
			log.error("[报警日报]导出报警日报表失败[deptId="+deptId+",statDate="+statDate+"]");
			return ;
		}
		int index = 1;
		List<String> failDeptList = new ArrayList<>();
		for(AlarmTypeAndList at : list){
			boolean flag = this.exportAlarmDetail(writer, at, index);
			if(!flag){
				failDeptList.add(at.getAlarmTypeName());
			}
			index++;
		}
		if(failDeptList.size() > 0){
			//如果没有完全导出成功
			log.info("部分报警没有导入成功，deptId="+deptId + ", alarmTypeList ="+JSON.toJSONString(failDeptList));
		}

		//3.保存到数据库
		//查询数据库中是否已经存在，如果已经存在，就删除原来的文件，并且更新数据库信息
		StatReportDay rDay = new StatReportDay();
		rDay.setDeptId(deptId);
		rDay.setDate(statDate);
		List<StatReportDay> listR = reportDayService.list(Wrappers.lambdaQuery(StatReportDay.class).eq(StatReportDay::getDeptId, deptId).eq(StatReportDay::getDate,statDate).eq(StatReportDay::getType, CommonConstant.REPORT_TYPE_DAY_ALARM).orderByDesc(StatReportDay::getUpdateTime));
		if(listR != null && listR.size() > 0){
			//保存新报表记录
			StatReportDay rdo = listR.get(0);
			StatReportDay rd = new StatReportDay();
			BeanUtils.copyProperties(rdo, rd);
			rd.setUpdateTime(new Date());
			rd.setId(null);
			rd.setType(CommonConstant.REPORT_TYPE_DAY_ALARM);
			rd.setFilePath(filePath);
			rd.setProxyFileUrl(proxyPath);
			reportDayService.save(rd);
			//5.如果之前已经出过报表，则删除之前的文件，并且更改数据库记录；如果数据多于一条，则删除多余数据
			//删除之前生成的文件和数据
			for(StatReportDay day : listR){
				//删除文件
				String fPath = day.getFilePath();
				if(!StringUtils.isEmpty(fPath)){
					boolean flag = FileUtil.deleteQuietly(new File(fPath));
					if(!flag){
						log.error("删除报警日报文件["+fPath+"]失败");
					}
				}
				//删除数据库数据
				boolean delFlag = reportDayService.removeById(day.getId());
				if(!delFlag){
					log.error("删除月报数据[id="+day.getId()+"]失败");
				}
			}
		}else{
			//如果之前没有统计过
			StatReportDay rd = new StatReportDay();
			rd.setUpdateTime(new Date());
			rd.setCreateTime(new Date());
			rd.setDate(statDate);
			rd.setDeptId(deptId);
			rd.setId(null);
			rd.setType(CommonConstant.REPORT_TYPE_DAY_ALARM);
			rd.setFilePath(filePath);
			rd.setProxyFileUrl(proxyPath);
			reportDayService.save(rd);
		}
		if(writer != null){
			writer.finish();
		}

	}

	/**
	 * 统计企业报警月报
	 * @param deptIds 指定的部门列表
	 * @param statMonth 统计月份
	 */
	@XxlJob("reportAlarmMonth")
	public void reportAlarmMonth (String deptIds, String statMonth) {
		log.info("run company month alarm report of {} for deptIds: {}", statMonth, deptIds);
		XxlJobHelper.log("任务开始：企业月报报警数据，参数：statMonth=" + statMonth + ", deptIds=" + deptIds);

		//1.判断入参
		long start = System.currentTimeMillis();
		long end;
		try {
			//如果指定跑批日期，则跑批该日期的报表，否则跑批前一天的报表
			if (StringUtils.isBlank(statMonth)) {
				statMonth = DateUtil.getLastMonthString();
			}
			//判断日期格式是否正确
			DateUtil.sdfHolderMonth.get().parse(statMonth);
		} catch (Exception e) {
			//如果日期格式错误
			end = System.currentTimeMillis();
			log.error("fail judge month when reportAlarmMonth: " + e.getMessage(), e);
			XxlJobHelper.log("【企业月报报警数据】任务初始化失败：" + e.getMessage());
			this.logUtil.insertStatLog(
				StatisticConstants.TASK_REPORT_MONTH,
				"定时任务：企业月报报警数据",
				statMonth,
				new Date(start),
				new Date(end),
				StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
				"执行失败，月份格式错误：" + statMonth
			);

			return;
		}

		//2.执行统计
		try {
			//2.1 查询要跑批的企业
			List<BladeDept> deptList;
			if (StringUtils.isNotBlank(deptIds)) {
				//如果指定了企业， 则只执行相应企业的跑批
				deptList = this.deptService.listByIds(Func.toLongList(deptIds));
			} else {
				//如果没有指定企业，则查询所有企业
				deptList = this.deptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
			}

			//记录执行失败情况
			List<Long> failDeptList = new ArrayList<>();
			for (BladeDept dept : deptList) {
				long deptId = dept.getId();
				try {
					this.reportAlarmMonthForOnce(deptId, statMonth);
				} catch (Exception e) {
					log.error("fail run for dept: " + deptId + " when reportAlarmMonth: " + e.getMessage(), e);
					failDeptList.add(deptId);
				}
			}

			end = System.currentTimeMillis();
			if (deptList.size() <= failDeptList.size()) {
				//所有部门执行失败，则跑批执行失败
				XxlJobHelper.log("【企业月报报警数据】任务失败：所有单位均未能统计。");
				this.logUtil.insertStatLog(
					StatisticConstants.TASK_REPORT_MONTH_ALARM,
					"定时任务：企业月报报警数据",
					statMonth,
					new Date(start),
					new Date(end),
					StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
					"执行失败：所有单位均未能统计。"
				);

				return;
			} else if (failDeptList.size() > 0) {
				XxlJobHelper.log("【企业月报报警数据】任务部分失败，未能统计的单位：" + JSON.toJSONString(failDeptList));
				this.logUtil.insertStatLog(
					StatisticConstants.TASK_REPORT_MONTH_ALARM,
					"定时任务：企业月报报警数据",
					statMonth,
					new Date(start),
					new Date(end),
					StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
					"部分执行失败，未能统计的单位：" + JSON.toJSONString(failDeptList)
				);

				return;
			}
		} catch (Exception e) {
			end = System.currentTimeMillis();
			log.error("fail run when reportAlarmMonth: " + e.getMessage(), e);
			XxlJobHelper.log("【企业月报报警数据】任务运行失败：" + e.getMessage());
			this.logUtil.insertStatLog(
				StatisticConstants.TASK_REPORT_MONTH_ALARM,
				"定时任务：企业月报报警数据",
				statMonth,
				new Date(start),
				new Date(end),
				StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
				"执行失败，运行阶段：" + e.getMessage()
			);

			return;
		}

		end = System.currentTimeMillis();
		XxlJobHelper.log("【企业月报报警数据】任务成功。");
		this.logUtil.insertStatLog(
			StatisticConstants.TASK_REPORT_MONTH_ALARM,
			"定时任务：企业月报报警数据",
			statMonth,
			new Date(start),
			new Date(end),
			StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,
			"执行成功。"
		);
	}

	private void reportAlarmMonthForOnce (Long deptId, String statDate) throws Exception {
		//1.统计报警相关数据
		long startTime = DateUtil.getMonthFirstSecondTimestamp(statDate);
		long endTime = DateUtil.getMonthLastSecondTimestamp(statDate);
		AlarmSummary summary = this.reportService.alarmSummary(deptId, statDate, startTime, endTime);
		List<AlarmTypeAndList> list = summary.getAlarmDetailList();

		//2.执行数据导出
		String fileName = summary.getDeptName() + "报警月报表（" + summary.getStatDate() + "）_" + System.currentTimeMillis() + ".xlsx";
		String filePath = this.monthAlarmFilePath + fileName;
		//2.1 按照模板导出统计数据
		ExcelWriter writer = this.exportAlarm(CommonConstant.REPORT_TYPE_MONTH, summary, filePath);
		String proxyPath = this.monthAlarmProxyPath + fileName;

		//2.2 导出报警详情数据，每种报警一个sheet，从1开始
		//以write的方式导出
		int index = 1;
		List<String> failAlarmTypeList = new ArrayList<>();
		for (AlarmTypeAndList atl : list) {
			if (!this.exportAlarmDetail(writer, atl, index)) {
				failAlarmTypeList.add(atl.getAlarmTypeName());
			}

			++index;
		}
		if (failAlarmTypeList.size() > 0) {
			//如果没有完全导出成功
			log.error("some alarm type fail when reportAlarmMonthForOnce, deptId=" + deptId + ", alarmTypeList=" + JSON.toJSONString(failAlarmTypeList));
		}

		//3.保存到数据库
		//查询数据库中是否已经存在，如果已经存在，就删除原来的文件，并且更新数据库信息
		List<StatReportMonth> statReportMonthList = this.reportMonthService.list(
			Wrappers.lambdaQuery(StatReportMonth.class).
				eq(StatReportMonth::getDeptId, deptId).
				eq(StatReportMonth::getMonth, statDate).
				eq(StatReportMonth::getType, CommonConstant.REPORT_TYPE_DAY_ALARM).
				orderByDesc(StatReportMonth::getUpdateTime)
		);
		if (CollectionUtils.isNotEmpty(statReportMonthList)) {
			//保存新报表记录
			StatReportMonth srm0 = statReportMonthList.get(0);
			StatReportMonth srm1 = new StatReportMonth();
			BeanUtils.copyProperties(srm0, srm1);
			srm1.setId(null);
			srm1.setType(CommonConstant.REPORT_TYPE_DAY_ALARM);
			srm1.setFilePath(filePath);
			srm1.setProxyFileUrl(proxyPath);
			srm1.setUpdateTime(new Date());
			this.reportMonthService.save(srm1);

			//5.如果之前已经出过报表，则删除之前的文件，并且更改数据库记录；如果数据多于一条，则删除多余数据
			//删除之前生成的文件和数据
			for (StatReportMonth srm : statReportMonthList) {
				//删除文件
				if (StringUtils.isNotBlank(srm.getFilePath())) {
					if (!FileUtil.deleteQuietly(new File(srm.getFilePath()))) {
						log.error("fail delete file when reportAlarmMonthForOnce: {}", srm);
					}
				}
				//删除数据库数据
				this.reportMonthService.removeById(srm.getId());
			}
		} else {
			//如果之前没有统计过
			StatReportMonth statReportMonth = new StatReportMonth();
			statReportMonth.setId(null);
			statReportMonth.setDeptId(deptId);
			statReportMonth.setMonth(statDate);
			statReportMonth.setType(CommonConstant.REPORT_TYPE_DAY_ALARM);
			statReportMonth.setFilePath(filePath);
			statReportMonth.setProxyFileUrl(proxyPath);
			statReportMonth.setCreateTime(new Date());
			statReportMonth.setUpdateTime(new Date());
			this.reportMonthService.save(statReportMonth);
		}
		if (writer != null) {
			writer.finish();
		}
	}

	/**
	 * 导出报警文件（excel）
	 * @param type 日/月报
	 * @param summary 报警数据
	 * @param filePath 文件路径
	 * @return excel渲染器
	 */
	private ExcelWriter exportAlarm (String type, AlarmSummary summary, String filePath){
		String templatePath = CommonConstant.REPORT_TYPE_DAY.equals(type) ? this.dayAlarmTemplatePath : this.monthAlarmTemplatePath;
		try {
			ExcelWriter excelWriter = EasyExcel.write(filePath).withTemplate(templatePath).build();
			WriteSheet writeSheet = EasyExcel.writerSheet(0,"概况")
				.registerWriteHandler(new CustomTemplateSheetStrategy(0, "概况"))
				.build();
			FillConfig fillConfig = FillConfig.builder().build();
			//写入list
			excelWriter.fill(summary.getAlarmList(), fillConfig, writeSheet);
			//写入普通变量数据
			excelWriter.fill(summary, writeSheet);
			return excelWriter;
		} catch (Exception e) {
			log.error("fail when exportAlarm: " + e.getMessage(), e);
			return null;
		} finally {
			//注意：因为后边还要往同一个文件中写入数据，所以，不可以关闭
			/*if(excelWriter != null){
				excelWriter.finish();
			}*/
		}
	}

	private boolean exportAlarmDetail (ExcelWriter excelWriter, AlarmTypeAndList atl, int index){
		try {
			//主标题和副标题在excel中分别是是第0和第1行
			//自定义标题和内容策略(具体定义在下文)
			ExcelExportFontAndStyleConfig cellStyleStrategy =
				new ExcelExportFontAndStyleConfig(Arrays.asList(0, 1), new WriteCellStyle(), new WriteCellStyle());

			//在代码中设置动态表头（主标题动态化）
			List<List<String>> head = new ArrayList<>();
			String[] array = {"企业名称", "车牌号", "车牌颜色", "报警类型", "报警开始时间", "报警结束时间", "行业类型", "报警等级", "报警位置", "报警结束位置", "最大速度", "速度", "限速", "道路名称", "报警持续时间"};
			for (int i = 0; i < 15; ++i) {
				List<String> list = new ArrayList<>();
				//每添加一次，代表一行标题。因为设置了标题自动合并，所以第一行标题 报警类型 是合并了的
				list.add(atl.getAlarmTypeName());
				list.add(array[i]);
				head.add(list);
			}

			WriteSheet writeSheet = EasyExcel.writerSheet(index, atl.getAlarmTypeName()).head(head)
				.automaticMergeHead(true)
				.registerWriteHandler(cellStyleStrategy)
				.registerWriteHandler(new ExcelExportRowHeightConfig())
				.registerWriteHandler(new CustomWriteSheetStrategy(index, atl.getAlarmTypeName()))
				.build();
			excelWriter.write(atl.getList(), writeSheet);
			return true;
		} catch (Exception e) {
			log.error("fail when exportAlarmDetail: " + e.getMessage(), e);
			return false;
		} finally {
			//注意：后边还要往同一个文件中写入数据，所以，不可以关闭该writer
			/*if(excelWriter != null){
				excelWriter.finish();
			}*/
		}
	}

	/**
	 * 统计企业运行日报
	 * @param deptIds 指定的部门列表
	 * @param statDate 统计日期
	 */
	@XxlJob("reportRunningDay")
	public void reportRunningDay(String deptIds, String statDate){
		XxlJobHelper.log("将执行跑批操作，参数为  statDate="+statDate +", deptIds = " + deptIds);
		//1.判断入参
		long startTime = 0;
		long start = System.currentTimeMillis();
		long end = 0;
		try{
			//如果指定跑批日期，则跑批该日期的报表，否则跑批前一天的报表
			if(StringUtils.isEmpty(statDate)){
				statDate = DateUtil.getDateBeforeDayStr(new Date(), 1);
			}
			//判断日期格式是否正确
			DateUtil.sdfHolderShort.get().parse(statDate);
		}catch (Exception ex){
			//如果日期格式错误
			log.error("日期["+statDate+"]格式错误",ex);
			XxlJobHelper.log("执行跑批失败，"+ex.getMessage());
			end = System.currentTimeMillis();
			logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY_RUNNING,"统计企运行日报报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败" + "日期["+statDate+"]格式错误");
			return;
		}

		//2.执行统计
		try {
			//2.1 查询要跑批的企业
			List<BladeDept> list;
			if(!StringUtils.isEmpty(deptIds)){
				//如果指定了企业， 则只执行相应企业的跑批
				List<Long> ids = Func.toLongList(deptIds);
				list = deptService.listByIds(ids);
			}else{
				//如果没有指定企业，则查询所有企业
				list = deptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
			}

			//记录执行失败情况
			List<Long> failDeptIds = new ArrayList<>();
			for(BladeDept dept : list){
				long deptId = dept.getId();
				try {
					reportRunningDayForOnce(deptId, statDate);
				}catch (Exception e){
					log.error("执行数据导出失败",e);
					failDeptIds.add(deptId);
				}
			}
			if(list.size() <= failDeptIds.size()){
				//所有部门执行失败，则跑批执行失败
				end = System.currentTimeMillis();
				logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY_RUNNING,"统计企运行日报报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败：所有部门跑批失败");
				XxlJobHelper.log("执行跑批失败，所有部门跑批失败");
				return;
			}
			if(failDeptIds.size() > 0){
				end = System.currentTimeMillis();
				logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY_RUNNING,"统计企运行日报报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"部分成功，企业["+ JSON.toJSONString(failDeptIds)+"]执行失败");
				XxlJobHelper.log("部分成功，跑批失败的企业："+JSON.toJSONString(failDeptIds));
				return;
			}
		}catch (Exception ex){
			end = System.currentTimeMillis();
			log.error("统计月报表数据出错",ex);
			logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY_RUNNING,"统计企运行日报报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败："+ex.getMessage());
			XxlJobHelper.log("执行跑批失败，"+ex.getMessage());
			return ;
		}
		end = System.currentTimeMillis();
		logUtil.insertStatLog(StatisticConstants.TASK_REPORT_DAY_RUNNING,"统计企运行日报报表：定时执行跑批任务",DateUtil.sdfHolder.get().format(new Date(startTime*1000)) ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
		XxlJobHelper.log("执行成功");

	}

	private void reportRunningDayForOnce(Long deptId, String statDate) throws Exception{
		//1.统计企业车辆运行整体情况
		long startTime = DateUtil.getDayFirstSecondTimestamp(statDate);
		Query query = new Query();
		query.setCurrent(1);
		query.setSize(Integer.MAX_VALUE);
		VehicleTravelMessage summary = vehicleTravel(deptId, statDate, query);
		//查询企业名称
		BladeDept dept = deptService.getById(deptId);
		String deptName = dept==null?"":dept.getDeptName();

		//2.执行数据导出
		String fileName = deptName + "车辆运行日报表" + "(" + statDate + ")" + "_" + System.currentTimeMillis() +".xlsx";
		String filePath = dayRunningFilePath + fileName;
		//2.1 按照模板导出统计数据
		ExcelWriter writer = this.exportRunning(CommonConstant.REPORT_TYPE_DAY, summary, filePath);
		String proxyPath = dayRunningProxyPath + fileName;

		//2.2 导出运行情况数据，包含异常位移、在线不定位、车辆上下线，每种运行情况一个sheet，从1开始
		//以write的方式导出
		if(StringUtils.isEmpty(filePath)){
			//如果统计数据导出失败，则不再向下执行
			log.error("[报警日报]导出报警日报表失败[deptId="+deptId+",statDate="+statDate+"]");
			return ;
		}

		//导出异常位移
		this.errLocationMoveExportForOnce(writer, deptId, statDate);

		//导出在线不定位
		this.statNotLocationExportForOnce(writer, deptId, statDate);

		//导出车辆上下线
		this.vehicleOnlineOrOfflineExportForOnce(writer, deptId, statDate);


		//3.保存到数据库
		//查询数据库中是否已经存在，如果已经存在，就删除原来的文件，并且更新数据库信息
		StatReportDay rDay = new StatReportDay();
		rDay.setDeptId(deptId);
		rDay.setDate(statDate);
		List<StatReportDay> listR = reportDayService.list(Wrappers.lambdaQuery(StatReportDay.class).eq(StatReportDay::getDeptId, deptId).eq(StatReportDay::getDate,statDate).eq(StatReportDay::getType, CommonConstant.REPORT_TYPE_DAY_RUNNING).orderByDesc(StatReportDay::getUpdateTime));
		if(listR != null && listR.size() > 0){
			//保存新报表记录
			StatReportDay rdo = listR.get(0);
			StatReportDay rd = new StatReportDay();
			BeanUtils.copyProperties(rdo, rd);
			rd.setUpdateTime(new Date());
			rd.setId(null);
			rd.setType(CommonConstant.REPORT_TYPE_DAY_RUNNING);
			rd.setDate(statDate);
			rd.setDeptId(deptId);
			rd.setFilePath(filePath);
			rd.setProxyFileUrl(proxyPath);
			reportDayService.save(rd);
			//5.如果之前已经出过报表，则删除之前的文件，并且更改数据库记录；如果数据多于一条，则删除多余数据
			//删除之前生成的文件和数据
			for(StatReportDay day : listR){
				//删除文件
				String fPath = day.getFilePath();
				if(!StringUtils.isEmpty(fPath)){
					boolean flag = FileUtil.deleteQuietly(new File(fPath));
					if(!flag){
						log.error("删除报警日报文件["+fPath+"]失败");
					}
				}
				//删除数据库数据
				boolean delFlag = reportDayService.removeById(day.getId());
				if(!delFlag){
					log.error("删除月报数据[id="+day.getId()+"]失败");
				}
			}
		}else{
			//如果之前没有统计过
			StatReportDay rd = new StatReportDay();
			rd.setUpdateTime(new Date());
			rd.setId(null);
			rd.setCreateTime(new Date());
			rd.setDeptId(deptId);
			rd.setDate(statDate);
			rd.setType(CommonConstant.REPORT_TYPE_DAY_RUNNING);
			rd.setFilePath(filePath);
			rd.setProxyFileUrl(proxyPath);
			reportDayService.save(rd);
		}
		if(writer != null){
			writer.finish();
		}
	}

	/**
	 * 统计企业运行月报
	 * @param deptIds 指定的部门列表
	 * @param statMonth 统计月份
	 */
	@XxlJob("reportRunningMonth")
	public void reportRunningMonth (String deptIds, String statMonth) {
		log.info("run company month running report of {} for deptIds: {}", statMonth, deptIds);
		XxlJobHelper.log("任务开始：企业月报运行数据，参数：statMonth=" + statMonth + ", deptIds=" + deptIds);

		//1.判断入参
		long start = System.currentTimeMillis();
		long end;
		try {
			//如果指定跑批日期，则跑批该日期的报表，否则跑批前一天的报表
			if (StringUtils.isBlank(statMonth)) {
				statMonth = DateUtil.getLastMonthString();
			}

			//判断日期格式是否正确
			DateUtil.sdfHolderMonth.get().parse(statMonth);
		} catch (Exception e) {
			//如果日期格式错误
			end = System.currentTimeMillis();
			log.error("fail judge month when reportRunningMonth: " + e.getMessage(), e);
			XxlJobHelper.log("【企业月报运行数据】任务初始化失败：" + e.getMessage());
			this.logUtil.insertStatLog(
				StatisticConstants.TASK_REPORT_MONTH_RUNNING,
				"定时任务：企业月报运行数据",
				statMonth,
				new Date(start),
				new Date(end),
				StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
				"执行失败，月份格式错误：" + statMonth
			);

			return;
		}

		//2.执行统计
		try {
			//2.1 查询要跑批的企业
			List<BladeDept> deptList;
			if (StringUtils.isNotBlank(deptIds)) {
				//如果指定了企业， 则只执行相应企业的跑批
				deptList = this.deptService.listByIds(Func.toLongList(deptIds));
			} else {
				//如果没有指定企业，则查询所有企业
				deptList = this.deptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
			}

			//记录执行失败情况
			List<Long> failDeptList = new ArrayList<>();
			for (BladeDept dept : deptList) {
				long deptId = dept.getId();
				try {
					this.reportRunningMonthForOnce(deptId, statMonth);
				} catch (Exception e) {
					log.error("fail run for dept: " + deptId + " when reportRunningMonth: " + e.getMessage(), e);
					failDeptList.add(deptId);
				}
			}

			end = System.currentTimeMillis();
			if (deptList.size() <= failDeptList.size()) {
				//所有部门执行失败，则跑批执行失败
				XxlJobHelper.log("【企业月报运行数据】任务失败：所有单位均未能统计。");
				this.logUtil.insertStatLog(
					StatisticConstants.TASK_REPORT_MONTH_RUNNING,
					"定时任务：企业月报运行数据",
					statMonth,
					new Date(start),
					new Date(end),
					StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
					"执行失败：所有单位均未能统计。"
				);

				return;
			} else if (failDeptList.size() > 0) {
				XxlJobHelper.log("【企业月报运行数据】任务部分失败，未能统计的单位：" + JSON.toJSONString(failDeptList));
				this.logUtil.insertStatLog(
					StatisticConstants.TASK_REPORT_MONTH_RUNNING,
					"定时任务：企业月报运行数据",
					statMonth,
					new Date(start),
					new Date(end),
					StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
					"部分执行失败，未能统计的单位：" + JSON.toJSONString(failDeptList)
				);

				return;
			}
		} catch (Exception e) {
			end = System.currentTimeMillis();
			log.error("fail run when reportRunningMonth" + e.getMessage(), e);
			XxlJobHelper.log("【企业月报运行数据】任务运行失败：" + e.getMessage());
			this.logUtil.insertStatLog(
				StatisticConstants.TASK_REPORT_MONTH_RUNNING,
				"定时任务：企业月报运行数据",
				statMonth,
				new Date(start),
				new Date(end),
				StatisticConstants.TASK_EXECUTE_RESULT_FAIL,
				"执行失败，运行阶段：" + e.getMessage()
			);

			return;
		}

		end = System.currentTimeMillis();
		XxlJobHelper.log("【企业月报运行数据】任务成功。");
		this.logUtil.insertStatLog(
			StatisticConstants.TASK_REPORT_MONTH_RUNNING,
			"定时任务：企业月报运行数据",
			statMonth,
			new Date(start),
			new Date(end),
			StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,
			"执行成功。"
		);
	}

	private void reportRunningMonthForOnce (Long deptId, String statDate) throws Exception {
		//查询企业名称
		BladeDept dept = this.deptService.getById(deptId);
		String deptName = (dept == null || StringUtils.isBlank(dept.getDeptName())) ? "" : dept.getDeptName();

		//1.统计企业车辆运行整体情况
		Query query = new Query();
		query.setCurrent(1);
		query.setSize(Integer.MAX_VALUE);
		VehicleTravelMessage summary = this.vehicleTravel(deptId, statDate, query);

		//2.执行数据导出
		String fileName = deptName + "车辆运行月报表（" + statDate + "）_" + System.currentTimeMillis() +".xlsx";
		String filePath = this.monthRunningFilePath + fileName;
		String proxyPath = this.monthRunningProxyPath + fileName;
		//2.1 按照模板导出统计数据
		ExcelWriter writer = this.exportRunning(CommonConstant.REPORT_TYPE_MONTH, summary, filePath);

		//2.2 导出运行情况数据，包含异常位移、在线不定位、车辆上下线，每种运行情况一个sheet，从1开始
		//以write的方式导出
		//导出异常位移
		this.errLocationMoveExportForOnce(writer, deptId, statDate);

		//导出在线不定位
		this.statNotLocationExportForOnce(writer, deptId, statDate);

		//导出车辆上下线
		this.vehicleOnlineOrOfflineExportForOnce(writer, deptId, statDate);

		//3.保存到数据库
		//查询数据库中是否已经存在，如果已经存在，就删除原来的文件，并且更新数据库信息

		List<StatReportMonth> statReportMonthList = this.reportMonthService.list(
			Wrappers.lambdaQuery(StatReportMonth.class).
				eq(StatReportMonth::getDeptId, deptId).
				eq(StatReportMonth::getMonth, statDate).
				eq(StatReportMonth::getType, CommonConstant.REPORT_TYPE_DAY_RUNNING).
				orderByDesc(StatReportMonth::getUpdateTime)
		);
		if (CollectionUtils.isNotEmpty(statReportMonthList)) {
			//保存新报表记录
			StatReportMonth srm0 = statReportMonthList.get(0);
			StatReportMonth srm1 = new StatReportMonth();
			BeanUtils.copyProperties(srm0, srm1);
			srm1.setId(null);
			srm1.setDeptId(deptId);
			srm1.setType(CommonConstant.REPORT_TYPE_DAY_RUNNING);
			srm1.setMonth(statDate);
			srm1.setFilePath(filePath);
			srm1.setProxyFileUrl(proxyPath);
			srm1.setUpdateTime(new Date());
			this.reportMonthService.save(srm1);
			//5.如果之前已经出过报表，则删除之前的文件，并且更改数据库记录；如果数据多于一条，则删除多余数据
			//删除之前生成的文件和数据
			for (StatReportMonth srm : statReportMonthList) {
				//删除文件
				if (StringUtils.isNotBlank(srm.getFilePath())) {
					if (!FileUtil.deleteQuietly(new File(srm.getFilePath()))) {
						log.error("fail delete file when reportRunningMonthForOnce: {}", srm);
					}
				}
				//删除数据库数据
				this.reportMonthService.removeById(srm.getId());
			}
		} else {
			//如果之前没有统计过
			StatReportMonth statReportMonth = new StatReportMonth();
			statReportMonth.setId(null);
			statReportMonth.setDeptId(deptId);
			statReportMonth.setType(CommonConstant.REPORT_TYPE_DAY_RUNNING);
			statReportMonth.setMonth(statDate);
			statReportMonth.setFilePath(filePath);
			statReportMonth.setProxyFileUrl(proxyPath);
			statReportMonth.setCreateTime(new Date());
			statReportMonth.setUpdateTime(new Date());
			this.reportMonthService.save(statReportMonth);
		}
		if (writer != null) {
			writer.finish();
		}
	}

	/**
	 * 导出运行文件（excel）
	 * @param type 日/月报
	 * @param summary 运行数据
	 * @param filePath 文件路径
	 * @return excel渲染器
	 */
	private ExcelWriter exportRunning (String type, VehicleTravelMessage summary, String filePath) {
		String templatePath = CommonConstant.REPORT_TYPE_DAY.equals(type) ? this.dayRunningTemplatePath : this.monthRunningTemplatePath;
		try {
			ExcelWriter excelWriter = EasyExcel.write(filePath).withTemplate(templatePath).build();
			WriteSheet writeSheet = EasyExcel.writerSheet(0,"概况")
				.registerWriteHandler(new CustomTemplateSheetStrategy(0, "概况"))
				.build();
			FillConfig fillConfig = FillConfig.builder().build();
			//写入list
			excelWriter.fill(summary.getTravelList(), fillConfig, writeSheet);
			//写入普通变量数据
			excelWriter.fill(summary, writeSheet);
			return excelWriter;
		} catch (Exception e) {
			log.error("fail when exportRunning: " + e.getMessage(), e);
			return null;
		} finally {
			//注意：因为后边还要往同一个文件中写入数据，所以，不可以关闭
			/*if(excelWriter != null){
				excelWriter.finish();
			}*/
		}
	}


	/**
	 * 异常位移统计 导出
	 * @param deptId
	 * @param statDate
	 * @return
	 */
	private void errLocationMoveExportForOnce (ExcelWriter excelWriter, long deptId, String statDate) {
		//1.查询条件准备
		CommonBaseCrossMonthRequest request = new CommonBaseCrossMonthRequest();
		try {
			long startTime;
			long endTime;
			if (statDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
				startTime = DateUtil.getDayFirstSecondTimestamp(statDate);
				endTime = DateUtil.getDayLastSecondTimestamp(startTime);
			} else if (statDate.matches("\\d{4}-\\d{2}")) {
				startTime = DateUtil.getMonthFirstSecondTimestamp(statDate);
				endTime = DateUtil.getMonthLastSecondTimestamp(statDate);
			} else {
				log.error("invalid statDate when errLocationMoveExportDayForOnce: {}", statDate);
				return;
			}

			request.setStartTime(startTime);
			request.setEndTime(endTime);

			//部门id
			List<Long> deptList = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
			request.setDeptList(deptList);

			//2.统计异常位移
			Query query = new Query();
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);
			IPage<ErrLocationMoveResponse> pageList = statService.findErrLocationMoveInfo(request, query);
			List<ErrLocationMoveResponse> list = new ArrayList<>();
			if(pageList != null){
				list = pageList.getRecords();
			}

			//补充中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
				list.forEach(item -> {
					item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
					item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
					item.setAccessModeDesc(accessModeMap.get(item.getAccessMode()));
					//设置定位时长
					item.setDurationFormatStr(DateUtil.getFormatDateString(item.getDuration().longValue()));
				});
			}

			//2.执行导出
			//主标题和副标题在excel中分别是是第0和第1行
			List<Integer> columnIndexes = Arrays.asList(0,1);
			ExcelExportFontAndStyleConfig cellStyleStrategy =
				new ExcelExportFontAndStyleConfig(columnIndexes,new WriteCellStyle(), new WriteCellStyle());

			WriteSheet writeSheet = EasyExcel.writerSheet()
				.registerWriteHandler(cellStyleStrategy).sheetNo(1).sheetName("异常位移统计")
				.head(ErrLocationMoveResponse.class)
				.registerWriteHandler(new ExcelExportRowHeightConfig())
				.registerWriteHandler(new CustomWriteSheetStrategy(1,"异常位移统计"))
				.build();
			excelWriter.write(list, writeSheet);


		}catch (Exception e){
			log.error("[日报-运行类报表]导出报错",e);
		}
	}



	/**
	 * 在线不定位统计 导出
	 * @param excelWriter
	 * @param deptId
	 * @param statDate
	 * @return
	 */
	public void statNotLocationExportForOnce (ExcelWriter excelWriter, long deptId, String statDate) {
		try {
			//1.查询数据
			NotLocationRequest request = new NotLocationRequest();
			long startTime;
			long endTime;
			if (statDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
				startTime = DateUtil.getDayFirstSecondTimestamp(statDate);
				endTime = DateUtil.getDayLastSecondTimestamp(startTime);
			} else if (statDate.matches("\\d{4}-\\d{2}")) {
				startTime = DateUtil.getMonthFirstSecondTimestamp(statDate);
				endTime = DateUtil.getMonthLastSecondTimestamp(statDate);
			} else {
				log.error("invalid statDate when errLocationMoveExportDayForOnce: {}", statDate);
				return;
			}

			request.setStartTime(startTime);
			request.setEndTime(endTime);

			//部门id
			List<Long> deptList = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
			request.setDeptList(deptList);

			Query query = new Query();
			query.setCurrent(1);
			query.setSize(Integer.MAX_VALUE);
			IPage<NotLocationResponse> pageList = statService.findNotLocationInfo(request, query);
			List<NotLocationResponse> list = new ArrayList<>();
			if(pageList != null){
				list = pageList.getRecords();
			}
			//补充中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
				list.forEach(item -> {
					item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
					item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
					item.setAccessModeDesc(accessModeMap.get(item.getAccessMode()));
					//设置不定位时长
					item.setDurationFormatStr(DateUtil.getFormatDateString(item.getDuration()));
				});
			}

			//2.执行导出
			//主标题和副标题在excel中分别是是第0和第1行
			List<Integer> columnIndexes = Arrays.asList(0,1);
			ExcelExportFontAndStyleConfig cellStyleStrategy =
				new ExcelExportFontAndStyleConfig(columnIndexes,new WriteCellStyle(), new WriteCellStyle());

			WriteSheet writeSheet = EasyExcel.writerSheet()
				.registerWriteHandler(cellStyleStrategy).sheetNo(2).sheetName("在线不定位统计")
				.head(NotLocationResponse.class)
				.registerWriteHandler(new ExcelExportRowHeightConfig())
				.registerWriteHandler(new CustomWriteSheetStrategy(2,"在线不定位统计"))
				.build();
			excelWriter.write(list, writeSheet);
		}catch (Exception e){
			log.error("[日报-运行类-在线不定位]导出出错",e);
		}
	}



	/**
	 *车辆上下线查询 导出
	 * 车辆上下线查询,需要把在某一时间段内每一辆车的上下线记录查找出来
	 * @return
	 */
	public void vehicleOnlineOrOfflineExportForOnce (ExcelWriter excelWriter, long deptId, String statDate) {
		try{

			//1.查询数据
			List<VehicleOnlineOrOfflineResponse> list = this.vehicleOnlineOrOffline(deptId, statDate);

			//2.执行导出
			//主标题和副标题在excel中分别是是第0和第1行
			List<Integer> columnIndexes = Arrays.asList(0,1);
			ExcelExportFontAndStyleConfig cellStyleStrategy =
				new ExcelExportFontAndStyleConfig(columnIndexes,new WriteCellStyle(), new WriteCellStyle());

			WriteSheet writeSheet = EasyExcel.writerSheet()
				.registerWriteHandler(cellStyleStrategy).sheetNo(3).sheetName("车辆上下线统计")
				.head(VehicleOnlineOrOfflineResponse.class)
				.registerWriteHandler(new ExcelExportRowHeightConfig())
				.registerWriteHandler(new CustomWriteSheetStrategy(3,"车辆上下线统计"))
				.build();
			excelWriter.write(list, writeSheet);
		}catch (Exception e){
			log.error("[日报-运行类]车辆上下线查询出错",e);
		}
	}


	private List<VehicleOnlineOrOfflineResponse> vehicleOnlineOrOffline (long deptId, String statDate) {
		try{
			CommonBaseRequest request = new CommonBaseRequest();
			long startTime;
			long endTime;
			if (statDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
				startTime = DateUtil.getDayFirstSecondTimestamp(statDate);
				endTime = DateUtil.getDayLastSecondTimestamp(startTime);
			} else if (statDate.matches("\\d{4}-\\d{2}")) {
				startTime = DateUtil.getMonthFirstSecondTimestamp(statDate);
				endTime = DateUtil.getMonthLastSecondTimestamp(statDate);
			} else {
				log.error("invalid statDate when errLocationMoveExportDayForOnce: {}", statDate);
				return new ArrayList<>();
			}

			request.setStartTime(startTime);
			request.setEndTime(endTime);

			//部门id
			Query query = new Query();
			query.setCurrent(1);
			query.setSize(Integer.MAX_VALUE);
			List<Long> deptList = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
			deptList.add(deptId);
			request.setDeptList(deptList);
			//在执行查询前，首先查询DB缓存表
			if(dbCacheEnable){
				try{
					IPage<VehicleOnlineOrOfflineResponse> pageList = cacheVehicleOnlineOrOfflineService.queryVehicleOnlineOrOfflineCache(request, query);
					//添加查询时间
					for(VehicleOnlineOrOfflineResponse res : pageList.getRecords()){
						res.setCheckTime(new Date());
					}
					return pageList==null?null:pageList.getRecords();
				}catch (Exception e){
					log.error("查询缓存表[cache_vehicle_online_offline]失败，将要实时查询业务逻辑",e);
				}
			}

			//组装查询条件
			CommonBaseCrossMonthDurationRequest cmdr = new CommonBaseCrossMonthDurationRequest();
			BeanUtils.copyProperties(request, cmdr);
			List<DateDurationAndMonth> dmList = new ArrayList<>();
			cmdr.setDmList(dmList);
			List<DateListAndMonth> dateList = DateUtil.getDateListAndMonth(request.getStartTime(), request.getEndTime());
			for(DateListAndMonth dm : dateList){
				DateDurationAndMonth ddm = new DateDurationAndMonth();
				ddm.setMonth(dm.getMonth().replace("-",""));
				ddm.setDurationStartTime(DateUtil.getDateByDateString(dm.getDateList().get(0)+" 00:00:00"));
				ddm.setDurationEndTime(DateUtil.getDateByDateString(dm.getDateList().get(dm.getDateList().size()-1)+" 23:59:59"));
				cmdr.getDmList().add(ddm);
			}

			CountDownLatch countDownLatch = new CountDownLatch(1);

			//查询部门信息、上级平台信息，用于后续手动增加字段
			Map<String,BladeDept> deptMap = new HashMap<>();
			Map<String, BamThirdPartyPlatform> platMap = new HashMap<>();
			threadPool.submit(() -> {
				long start1 = System.currentTimeMillis();
				try{
					List<BladeDept> depts = null;
					List<BamThirdPartyPlatform> platList = null;
					platList = thirdPartyPlatformService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled, 0));
					depts = deptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
					platList.forEach(item -> {
						platMap.put(item.getId()+"", item);
					});
					depts.forEach(item -> {
						deptMap.put(item.getId()+"", item);
					});
				}catch (Exception e){
					log.error("异步处理失败",e);
				}finally {
					countDownLatch.countDown();
				}
				long end1 = System.currentTimeMillis();
				log.info("-=-=-=time1 is "+ (end1 - start1));
			});

			IPage<VehicleOnlineOrOfflineResponse> page = new Page<>(query.getCurrent(),query.getSize());
			page = statisticsService.findVehicleOnlineOrOffline(cmdr, page);
			countDownLatch.await();

			Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
			Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
			Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);

			page.getRecords().forEach(record ->{
				if(com.baomidou.mybatisplus.core.toolkit.StringUtils.isEmpty(record.getDriver())){
					record.setDriver("未知");
				}
				//车牌颜色
				Map<String, String> colorValueMap = cacheUtil.getVehicleColorValueMap();
				String vehicleColor = colorValueMap.get(record.getLicenceColor());

				//更新字典信息
				record.setVehicleUseType(vehicleUseTypeMap.get(record.getVehicleUseType()));
				record.setLicenceColor(licenceColorMap.get(record.getLicenceColor()));
				record.setAccessMode(accessModeMap.get(record.getAccessMode()));
				if(deptMap != null && deptMap.get(record.getDeptId()+"") != null){
					record.setDeptName(deptMap.get(record.getDeptId()+"").getDeptName());
				}
				String vehicleOwnerName = record.getVehicleOwnerId()==0?"非营运车辆":platMap.get(record.getVehicleOwnerId()+"").getName();
				record.setVehicleOwner(vehicleOwnerName==null?"非营运车辆":vehicleOwnerName);

			});
			return page == null?null:page.getRecords();
		}catch (Exception e){
			log.error("车辆上下线查询出错",e);
			return null;
		}
	}



	private VehicleTravelMessage vehicleTravel(Long deptId, String statDate, Query query) throws Exception {

		VehicleTravelMessage vt = new VehicleTravelMessage();

		long startTime = 0;
		long endTime = 0;
		String statType = "";
		if(statDate.length() == 10){
			statType = "day";
			//如果是日报
			//判断是否是当日
			if(DateUtil.getDateString().equals(statDate)){
				//如果是当日
				startTime = DateUtil.getDayFirstSecondTimestamp();
				endTime = System.currentTimeMillis()/1000;
			}else{
				//如果不是当日
				startTime = DateUtil.getDayFirstSecondTimestamp(statDate);
				endTime = DateUtil.getDayLastSecondTimestamp(startTime);
			}
		}else{
			statType = "month";
			//如果是月报
			//判断是否是当月
			if(DateUtil.getDateString().substring(0,7).equals(statDate)){
				//如果是当月
				startTime = DateUtil.getMonthFirstSecondTimestamp(statDate);
				endTime = System.currentTimeMillis()/1000;
			}else{
				//如果不是当月
				startTime = DateUtil.getMonthFirstSecondTimestamp(statDate);
				endTime = DateUtil.getMonthLastSecondTimestamp(statDate);
			}
		}
		BladeUser user = AuthUtil.getUser();
		List<Long> deptIds = SysCache.getDeptChildIds(deptId);
//		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
		IPage<BdmVehicle> vList = vehicleService.getVehicleListAllByPage(null, deptId, query);
		vt.setTotalCount((int)vList.getTotal());
		List<Integer> vehicleIds = new ArrayList<>();
		vList.getRecords().forEach(item -> {
			vehicleIds.add(item.getId());
		});
		//1.查询总里程
		double totalMileage = 0;
		if(statType.equals("day")){
			//如果是日报
			totalMileage = vehicleStatService.statDeptMileage(statDate, deptIds, null);
		}else{
			//如果是月份
			totalMileage = vehicleStatService.statDeptMileageMonth(statDate,deptIds,null);
		}
		vt.setTotalMileage(MathUtil.roundDouble(totalMileage / 1000,2));

		//2.查询总时长
		long totalDuration = 0;
		if(statType.equals("day")){
			//如果是日报
			totalDuration = vehicleStatService.statDeptDuration(statDate.substring(0,7),statDate, deptIds, null);
		}else{
			//如果是月份
			totalDuration = vehicleStatService.statDeptDurationMonth(statDate, DateUtil.getMonthLastDateStr(statDate),deptIds,null);
		}
		vt.setTotalDurationInHour(DateUtil.getFormatDateString(totalDuration));


		//3.计算平均里程、平均时长
		//查询上线车辆数

		//上线车辆数
		long goOnlineCount = 0;
		if(statDate.length() == 10){
			//如果是日报
			if(DateUtil.getDateString().equals(statDate)){
				//如果统计的是当日
				goOnlineCount = vehicleService.findGoOnlineCountAllToday(deptIds, null);
			}else{
				//如果统计的是之前的日期
				goOnlineCount = vehicleService.findGoOnlineCountByDate(deptIds, null,statDate);
			}
		}else{
			//如果是月报
			goOnlineCount = vehicleService.findGoOnlineCountAllMonth(deptIds,null,statDate);
		}


		long goVehicleCount = goOnlineCount;
		if(goVehicleCount > 0){
			vt.setAverageMileage(MathUtil.divideRoundDouble(totalMileage / 1000, goVehicleCount, 2));
			//平均时长（小时）
			vt.setAverageDurationInHour((MathUtil.divideRoundDouble(totalDuration/3600, goVehicleCount, 2))+"");
		}

		//4.计算每辆车的里程、时长信息、最大速度信息、平均速度信息
		IPage<VehicleAndMileage> mileagePage = null;
		if(statType.equals("day")){
			//如果是日报
			mileagePage = vehicleService.findTotalMileagePage(deptIds, null, statDate.substring(0,7), statDate,query);
		}else{
			//如果是月报
			mileagePage = vehicleService.findTotalMileageMonthPage(deptIds, null,statDate,query);
		}

		//根据车辆里程，查询在线时长、最大速度、上线天数
		List<VehicleAndCount> durationList = null;
		List<VehicleAndData> maxSpeedList = null;
		List<VehicleAndCount> onlineDaysList = null;
		if(mileagePage != null && mileagePage.getRecords() != null){
			//List<Integer> vehicleIds = new ArrayList<>();
			/*mileagePage.getRecords().forEach(item -> {
				vehicleIds.add(item.getVehicleId().intValue());
			});*/
			if(statType.equals("day")){
				//如果是日报
				durationList = vehicleService.findGoOnlineDurationListByDateAndVehicleId(vehicleIds, statDate);
				maxSpeedList = vehicleStatService.findMaxSpeedListByVehicleId(statDate, vehicleIds);
				//查询上线天数
				onlineDaysList = vehicleService.findOnlineDaysCount(statDate, vehicleIds);
			}else{
				//如果是月报
				String lastDateInMonth = "";
				if(DateUtil.getDateString().substring(0,7).equals(statDate)){
					//如果是当月
					lastDateInMonth = DateUtil.getDateString();
				}else{
					//如果不是当月
					lastDateInMonth = DateUtil.getMonthLastDateStr(statDate);
				}
				durationList = vehicleService.findGoOnlineDurationListAllMonthByVehicleId(vehicleIds, statDate,lastDateInMonth);
				maxSpeedList = vehicleStatService.findMaxSpeedListMonthByVehicleId(statDate, vehicleIds);
				//查询上线天数
				//查询上线天数
				onlineDaysList = vehicleService.findOnlineDaysCountMonth(statDate, vehicleIds);
			}
		}
		//整理数据
		//数据转为map
		Map<String,Long> durationMap = new HashMap<>();
		Map<String,Double> maxSpeedMap = new HashMap<>();
		Map<String,Long> onlineDaysMap = new HashMap<>();
		durationList.forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			durationMap.put(key, item.getCount());
		});
		maxSpeedList.forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			maxSpeedMap.put(key, item.getData());
		});
		onlineDaysList.forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			onlineDaysMap.put(key, item.getCount());
		});

		//整合数据
		List<VehicleTravelNode> nodeList = new ArrayList<>();
		Map<String,Double> mileageMap = new HashMap<>();
		mileagePage.getRecords().forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			mileageMap.put(key, item.getMileage());
		});
		Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
		vList.getRecords().forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			VehicleTravelNode node = new VehicleTravelNode();
			node.setLicencePlate(item.getLicencePlate());
			node.setLicenceColor(item.getLicenceColor());
			node.setLicenceColorDesc(licenceColorMap.get(node.getLicenceColor()));
			node.setMaxSpeed(maxSpeedMap.get(key)==null?0:maxSpeedMap.get(key));
			node.setGoOnlineDaysCount(onlineDaysMap.get(key)==null?0:onlineDaysMap.get(key));
			//总里程（km）
			node.setTotalMileage(MathUtil.roundDouble((mileageMap.get(key)==null?0:mileageMap.get(key))/1000,2));
			node.setTotalDuration((double)(durationMap.get(key)==null?0:durationMap.get(key)));
			node.setTotalDurationDesc(DateUtil.getFormatDateString(durationMap.get(key)==null?0:durationMap.get(key)));
			//平均时长（总时长/总上线天数，小时）
			if(onlineDaysMap.get(key) != null && onlineDaysMap.get(key) > 0 ){
				node.setAverageDuration(MathUtil.roundDouble(MathUtil.divideRoundDouble((durationMap.get(key)==null?0:durationMap.get(key))/3600, onlineDaysMap.get(key),2),2));
				//平均里程（km）
				if(durationMap != null && durationMap.get(key) != null){
					Double averageMileage = MathUtil.divideRoundDouble(mileageMap.get(key)==null?0:mileageMap.get(key), onlineDaysMap.get(key), 2);
					averageMileage = averageMileage / 1000;
					node.setAverageMileage(MathUtil.roundDouble(averageMileage==null?0:averageMileage,2));
				}
			}
			//平均速度(km/h)
			if(durationMap.get(key) != null && durationMap.get(key) > 0){
				Double averageSpeed = MathUtil.roundDouble(mileageMap.get(key)==null?0:mileageMap.get(key)/durationMap.get(key)*3.6,2);
				node.setAverageSpeed(averageSpeed==null?0:averageSpeed);
			}
			nodeList.add(node);
		});
		vt.setTravelList(nodeList);
		return vt;
	}

}
