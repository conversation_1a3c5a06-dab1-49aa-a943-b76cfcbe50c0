package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.BdmTerminalSim;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.request.SimExpireRequest;
import com.xh.vdm.statistic.vo.response.SimExpireResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
public interface BdmTerminalSimMapper extends BaseMapper<BdmTerminalSim> {

	IPage<SimExpireResponse> getSimExpireInfo(@Param("request") SimExpireRequest request, IPage<SimExpireResponse> page);
}
