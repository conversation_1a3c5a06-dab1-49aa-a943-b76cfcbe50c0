package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.BdmTerminal;
import com.xh.vdm.statistic.mapper.BdmTerminalMapper;
import com.xh.vdm.statistic.service.IBdmTerminalService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-20
 */
@Service
public class BdmTerminalServiceImpl extends ServiceImpl<BdmTerminalMapper, BdmTerminal> implements IBdmTerminalService {

	@Override
	public String findDeptNameByDeviceNum(String deviceNum) throws Exception {
		return baseMapper.getDeptNameByDeviceNum(deviceNum);
	}

	@Override
	public Long findTotalTerminalCount(List<Long> deptIds) {
		return baseMapper.getTotalTerminalCount(deptIds);
	}

	@Override
	public Long findNoDeviceNumCount(List<Long> deptIds) throws Exception {
		return baseMapper.getNoDeviceNumCount(deptIds);
	}
}
