<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmSecurityMapper">
    <select id="getAlarmTypeAndCount" resultType="com.xh.vdm.statistic.entity.AlarmTypeAndCount">
        select  alarm_type ,  to_timestamp(alarm_time , '%Y-%m-%d') date , count(1) count
        from bdm_security bs
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=BIGINT}
        </if>
        <if test="startSecondTime != null and startSecondTime != ''">
            and alarm_time >= #{startSecondTime,jdbcType=BIGINT}
        </if>
        <if test="endSecondTime != null and endSecondTime != ''">
            and alarm_time &lt;= #{endSecondTime,jdbcType=BIGINT}
        </if>
        group by  alarm_type, to_timestamp(alarm_time , '%Y-%m-%d')
        order by alarm_type , to_timestamp( alarm_time, '%Y-%m-%d' )
    </select>

    <select id="getSecurityByIdCardAndDate" resultType="com.xh.vdm.statistic.entity.BdmSecurity">
        select * from bdm_security bs
                          left join bdm_vehicle_driver bvd on bs.driver_name = bvd.name
        where alarm_time >= #{startSecondTimestamp}   and  alarm_time &lt;= #{endSecondTimestamp}
          and bvd.idcard = #{idCard,jdbcType=VARCHAR}
    </select>

    <select id="getEveryAlarmTypeByIdCard" resultType="com.xh.vdm.statistic.entity.AlarmTypeAndDateAndIdCard">
        select a.idcard idCard , a.alarm_type , a.date from
            (
                select distinct bvd.idcard , alarm_type  , SUBSTRING(to_timestamp(alarm_time) , 1 , 10) date
                from bdm_security bs
                    left join bdm_vehicle_driver bvd on bs.driver_name = bvd.name
                where 1 = 1
                  and alarm_time >= #{startSecondTimestamp}
                  and alarm_time &lt;= #{endSecondTimestamp}
                  and bvd.idcard = #{idCard,jdbcType=VARCHAR}
                  and bvd.idcard is not null
            ) a
        order by a.idcard , a.alarm_type , a.date
    </select>

    <select id="getAlarmCountEveryDay" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select  SUBSTRING(to_timestamp(alarm_time) , 1 , 10) stat_date , count(*) count from bdm_security bs
            left join bdm_vehicle_driver bvd on bs.driver_name = bvd.name
        where 1 = 1
          and alarm_time >= #{startSecondTimestamp}
          and  alarm_time &lt;= #{endSecondTimestamp}
          and bvd.idcard = #{idCard,jdbcType=VARCHAR}
        group by SUBSTRING(to_timestamp(alarm_time) , 1 , 10)
    </select>

    <select id="getAlarmCountForDept" resultType="long">
        select count(*) from bdm_security
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=BIGINT}
        </if>
        and alarm_time >= #{startSecondTimestamp}
        and alarm_time &lt;= #{endSecondTimestamp}
    </select>
</mapper>
