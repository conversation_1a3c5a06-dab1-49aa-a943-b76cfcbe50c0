package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.CacheUtil;
import com.xh.vdm.statistic.utils.ExcelExport;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.utils.R;
import com.xh.vdm.statistic.vo.request.CompanyRateRequest;
import com.xh.vdm.statistic.vo.request.DetailRequest;
import com.xh.vdm.statistic.vo.request.PlatformScoreRequest;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.secure.BladeUser;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 考核评分
 * @Author: zhouxw
 * @Date: 2022/9/13 1:27 PM
 */
@RestController
@RequestMapping("/bt/statistics/score")
@Slf4j
public class ScoreController {

    @Resource
    private IVehicleStatService vehicleStatService;

    @Resource
    private IStatCompleteService completeService;

    @Resource
    private ILocationQualityService locationQualityService;

    @Resource
    private IStatDriftService driftService;

    @Resource
    private IBladeDeptService deptService;

    @Resource
    private CacheUtil cacheUtil;


    @Value("${static.file.path:/statistic/files/}")
    String staticFilePath;

    /**
     * @description: 平台考核整体情况
     * 此处的得分采用服务商计分标准
     * @author: zhouxw
     * @date: 2022/9/13 1:38 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.xh.vdm.statistic.vo.response.AllScore>
     **/
    @PostMapping("/all")
    public R<AllScoreResponse> all(@RequestBody @Validated PlatformScoreRequest request){

        AllScoreResponse response = new AllScoreResponse();

        try{
            //1.入网车辆数
            String nextMonthFirstDay = getNextMonthFirstDay(request.getMonth());
            int inNetCount = vehicleStatService.findInNetCountByOwnerId(nextMonthFirstDay , request.getOwnerId());
            response.setIntNetCount(inNetCount);

            //2.上线情况
            //上线数
            int goOnlineCount = vehicleStatService.findGoOnlineCountByOwnerId(request.getMonth() , request.getOwnerId());
            //如果入网车辆数为0，那么上线率为0
            double goOnlineRate = 0;
            if(inNetCount != 0){
                //上线率
                goOnlineRate = MathUtil.divideRoundDouble(goOnlineCount , inNetCount , 4);
            }
            //上线率得分
            double goOnlineScore = MathUtil.roundDouble(goOnlineRate * 10 , 2);
            response.setGoOnlineRate(goOnlineRate);
            response.setGoOnlineScore(goOnlineScore);


            //3.平台连通率情况（平台连通率情况，已由其他服务提供）

            //4.轨迹完整率情况
            RateParam completeParam = new RateParam();
            completeParam.setMonth(request.getMonth().replace("-",""));
            completeParam.setOwnerId(request.getOwnerId());
            LocationCompleteRateResponse completeRateResponse = completeService.getLocationCompleteRate(completeParam);
            //完整率
            double completeRate = 0;
            if(completeRateResponse != null){
                completeRate = MathUtil.roundDouble(completeRateResponse.getCompleteRate(),4);
            }
            //完整率得分
            double completeScore = 0;
            if(completeRate >= 0.7){
                completeScore = MathUtil.roundDouble(completeRate * 25,2);
            }
            response.setCompleteRate(completeRate);
            response.setCompleteScore(completeScore);


            //5.数据合格率情况
            RateRequest qualityParam = new RateRequest();
            qualityParam.setMonth(request.getMonth());
            qualityParam.setOwnerId(request.getOwnerId());
            LocationQualityResponse qualityResponse = locationQualityService.findLocaitonQualityRate(qualityParam);
            double qualityRate = 0;
            if(qualityResponse != null){
                int totalCount = qualityResponse.getTotalCount();
                int totalRightCount = totalCount - qualityResponse.getTotalErrorCount();
                qualityRate = MathUtil.divideRoundDouble(totalRightCount , totalCount , 4);
            }
            double qualityScore = 0;
            if(qualityRate >= 0.8){
                qualityScore = qualityRate * 25;
            }
            qualityScore = MathUtil.roundDouble(qualityScore , 2);
            //数据合格率
            response.setQualifiedRate(qualityRate);
            //数据合格率得分
            response.setQualifiedScore(qualityScore);


            //6.漂移率情况
            //查询上线车辆数和漂移车辆数
            RateParam driffParam = new RateParam();
            driffParam.setMonth(request.getMonth().replace("-" , ""));
            driffParam.setOwnerId(request.getOwnerId());
            List<DriftAndGoOnlineCountNode> list = driftService.findDriftAndOnlineCount(driffParam);
            //统计漂移率
            int totalDrift = 0 ;
            int totalGoOnline = goOnlineCount;
            double driftRate = 0;
            for(DriftAndGoOnlineCountNode node : list){
                totalDrift += node.getDriftCount();
                totalGoOnline += node.getGoOnlineCount();
            }
            //计算漂移率
            if(totalGoOnline == 0){
                //return R.fail("未查询到上线车辆");
                log.info("[平台考核整体情况]计算漂移：未查询到上线车辆");
                driftRate = 0;
            }else {
                if (totalDrift == 0) {
                    driftRate = 0;
                } else {
                    driftRate = MathUtil.divideRoundDouble(totalDrift, totalGoOnline, 4);
                }
            }
            //计算得分
            double driftScore = 0;
            if(totalGoOnline > 0 && driftRate <= 0.05){
                driftScore = MathUtil.roundDouble(20 - (driftRate * 20) , 2);
            }
            response.setDriftRate(driftRate);
            response.setDriftScore(driftScore);


            //7.最终考核得分（注意此处未添加连通率，需要前台调用其他服务后添加）
            double totalScore = goOnlineScore + completeScore + qualityScore + driftScore;
            response.setTotalScore(MathUtil.roundDouble(totalScore,2));
        }catch (Exception e){
            e.printStackTrace();
            log.error("统计考核得分综合情况失败",e);
            return R.fail("统计考核得分综合情况失败");
        }

        return R.data(response);
    }


    /**
     * @description: 企业考核明细
     * 统计所有企业
     * 先按照企业表对企业进行统计，从而确定每页的企业。然后再按照每页指定的企业来统计各个指标
     * @author: zhouxw
     * @date: 2022/9/13 3:53 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.core.metadata.IPage<com.xh.vdm.statistic.vo.response.CompanyRateResponse>>
     **/
    @PostMapping("/companyRate")
    public R<IPage<CompanyRateResponse>> companyRate (@Validated @RequestBody CompanyRateRequest request, BladeUser user){

        //前端下标从 0 开始，此处要加1
        request.setStart(request.getStart()+1);

		if(user == null){
			log.info("用户未登录或者鉴权失败");
			return R.fail("用户未登录或者鉴权失败");
		}

		//租户id
		String tenantId = user.getTenantId();

        IPage pageF = new Page();
        try{
            List<CompanyRateResponse> list = getCompanyRateList(request , false);

            pageF.setSize(request.getCount());
            pageF.setCurrent(request.getStart()/request.getCount()+(request.getStart() % request.getCount()>0?1:0));
            //查询企业总数
            int totalCount = (int)deptService.count( Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getTenantId , tenantId).eq(BladeDept::getIsDeleted ,0));
            pageF.setTotal(totalCount);
            pageF.setRecords(list);
        }catch (Exception e){
            e.printStackTrace();
            log.error("统计企业考核明细出现异常",e);
            return R.fail("统计企业考核明细失败");
        }

        return R.data(pageF);
    }

    /**
     * @description: 获取企业指标率列表
     * @author: zhouxw
     * @date: 2022/9/22 5:49 PM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.CompanyRateResponse>
     **/
    public List<CompanyRateResponse> getCompanyRateList (CompanyRateRequest request , boolean isExport){
        //1.根据企业表获取分页信息
        List<Long> deptIds = new ArrayList<>();
        Map<Long,String> deptNamesMap = new HashMap<>();
        long ownerId = request.getOwnerId();
        if(request.getDeptId() != null){
            //如果指定了企业
            deptIds.add(request.getDeptId());
            BladeDept dept = deptService.getById(request.getDeptId());
            deptNamesMap.put(request.getDeptId(),dept.getDeptName());
        }else{
            //如果没有指定企业
            //分页查询企业信息，根据企业入网车辆数进行倒序排序
            Page page = new Page();
            if(isExport){
                //如果是导出接口
                page.setSize(Integer.MAX_VALUE);
                page.setCurrent(0);
            }else{
                //如果不是导出接口
                page.setSize(request.getCount());
                page.setCurrent(request.getStart()/request.getCount()+(request.getStart() % request.getCount()>0?1:0));
            }

            //IPage<SysDept> depts = deptService.page(page , Wrappers.lambdaQuery(SysDept.class).eq(SysDept::getEnabled , 1).eq(SysDept::getIsDel ,0));
            String nextMonthFirstDay = getNextMonthFirstDay(request.getMonth());
            IPage<DeptAndCount> depts = vehicleStatService.findDeptAndInNetCountByPage(page , nextMonthFirstDay , request.getOwnerId() );
            for(DeptAndCount dept : depts.getRecords()){
                deptIds.add(dept.getDeptId());
                deptNamesMap.put(dept.getDeptId() , dept.getDeptName());
            }
        }

        //2.入网车辆数
        String nextMonthFirstDay = getNextMonthFirstDay(request.getMonth());
        List<VehicleCountWithDept> inNetCountList = vehicleStatService.findInNetCountByDeptId(nextMonthFirstDay , deptIds , ownerId);
        Map<Long , Integer> inNetCountMap = new HashMap<>();
        for(VehicleCountWithDept vd : inNetCountList){
            inNetCountMap.put(vd.getDeptId() , vd.getCount());
        }

        //3.上线率
        //上线情况
        List<VehicleCountWithDept> goOnlineCountList = vehicleStatService.findGoOnlineCountByDeptId(request.getMonth(),deptIds , ownerId);
        Map<Long , Integer> goOnlineCountMap = new HashMap<>();
        for(VehicleCountWithDept vd : goOnlineCountList){
            goOnlineCountMap.put(vd.getDeptId() , vd.getCount());
        }
        Map<Long , String> goOnlineRateMap = new HashMap<>();
        for(VehicleCountWithDept vd : goOnlineCountList){
            goOnlineRateMap.put(vd.getDeptId() , MathUtil.formatToPercent(MathUtil.divideRoundDouble(vd.getCount() , inNetCountMap.get(vd.getDeptId()) ,4),2));
        }

        //4.轨迹完整率
        List<VehicleRateWithDept> completeRateList = completeService.findCompleteByDeptId(request.getMonth() , deptIds , ownerId);
        Map<Long , String> completeRateMap = new HashMap<>();
        for(VehicleRateWithDept vd : completeRateList){
            if(vd == null){
                continue;
            }
            completeRateMap.put(vd.getDeptId(),MathUtil.formatToPercent(vd.getRate(),2));
        }

        //5.数据合格率
        List<VehicleRateWithDept> qulityRateList = locationQualityService.findQualityRateByDeptId(request.getMonth() , deptIds , ownerId);
        Map<Long , String> qualityRateMap = new HashMap<>();
        for(VehicleRateWithDept vd : qulityRateList){
            if(vd == null){
                continue;
            }
            qualityRateMap.put(vd.getDeptId(),MathUtil.formatToPercent(vd.getRate(),2));
        }


        //6.漂移相关
        //漂移车辆数
        List<VehicleCountWithDept> driffCountList = driftService.findDriffCountByDeptId(request.getMonth() , deptIds , ownerId);
        Map<Long , Integer> driffCountMap = new HashMap<>();
        for(VehicleCountWithDept vd : driffCountList){
            if(vd == null){
                continue;
            }
            driffCountMap.put(vd.getDeptId(),vd.getCount());
        }


        //漂移率(漂移数/上线数)
        Map<Long , String> driffRateMap = new HashMap<>();
        for(VehicleCountWithDept vd : driffCountList){
            if(vd == null){
                continue;
            }
            try {
                driffRateMap.put(vd.getDeptId(), MathUtil.formatToPercent(MathUtil.divideRoundDouble(vd.getCount(), goOnlineCountMap.get(vd.getDeptId()), 4), 2));
            }catch (Exception e){
                log.error("统计漂移率失败，将设置为0 ",e);
                driffRateMap.put(vd.getDeptId(), "0%");
            }
        }

        //7.拼装数据
        List<CompanyRateResponse> list = new ArrayList<>();
        for(long deptId : deptIds){
            CompanyRateResponse response = new CompanyRateResponse();
            response.setDeptId(deptId);
            response.setDeptName(deptNamesMap.get(deptId)==null?"":deptNamesMap.get(deptId));
            response.setDriffCount(driffCountMap.get(deptId)==null?0:driffCountMap.get(deptId));
            //设置轨迹完整率
            if(StringUtils.isBlank(completeRateMap.get(deptId))){
                response.setCompleteRate("0%");
            }else {
                response.setCompleteRate(completeRateMap.get(deptId));
            }
            //设置数据合格率
            if(StringUtils.isBlank(qualityRateMap.get(deptId))){
                response.setQualifiedRate("0%");
            }else {
                response.setQualifiedRate(qualityRateMap.get(deptId));
            }
            //设置漂移率
            String driftRate = driffRateMap.get(deptId);
            if(driftRate == null){
                if(goOnlineRateMap.get(deptId) != null && goOnlineCountMap.get(deptId) > 0 ){
                    //如果上线率大于0 ，说明有车辆上线，如果此时漂移率为空，说明未发生漂移
                    driftRate = "0%";
                }
            }
            if(StringUtils.isBlank(driftRate)){
                response.setDriftRate("0%");
            }else {
                response.setDriftRate(driftRate);
            }
            //设置上线率
            if(StringUtils.isBlank(goOnlineRateMap.get(deptId))){
                response.setGoOnlineRate("0%");
            }else {
                response.setGoOnlineRate(goOnlineRateMap.get(deptId));
            }

            response.setIntNetCount(inNetCountMap.get(deptId)==null?0:inNetCountMap.get(deptId));
            response.setStatDate(request.getMonth());
            list.add(response);
        }
        return list;
    }



    /**
     * @description: 企业考核明细导出
     * 统计所有企业
     * 先按照企业表对企业进行统计，从而确定每页的企业。然后再按照每页指定的企业来统计各个指标
     * @author: zhouxw
     * @date: 2022/9/13 3:53 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.core.metadata.IPage<com.xh.vdm.statistic.vo.response.CompanyRateResponse>>
     **/
    @PostMapping("/companyRateExport")
    public R<String> companyRateExport (@Validated @RequestBody CompanyRateRequest request){
        try{
            List<CompanyRateResponse> list = getCompanyRateList(request , true);
            List<CompanyRateExportResponse> companyRateExportList = new ArrayList<>();
            for (CompanyRateResponse companyRateResponse : list) {
                CompanyRateExportResponse companyRateExport = new CompanyRateExportResponse();
                BeanUtils.copyProperties(companyRateResponse, companyRateExport);
                companyRateExportList.add(companyRateExport);
            }

            //执行数据导出
            String title = "企业评分明细";
            String[] arrs = {"企业名称","入网车辆数","上线率","轨迹完整率","数据合格率","漂移车辆数","定位漂移率","统计月份"};
            String fileName = ExcelExport.exportExcelFile(title, companyRateExportList, arrs, this.staticFilePath);
            return R.data(fileName);
        }catch (Exception e){
            e.printStackTrace();
            log.error("导出企业考核明细出现异常",e);
            return R.fail("导出企业考核明细失败");
        }

    }




    /**
     * @description: 异常车辆明细
     * @author: zhouxw
     * @date: 2022/9/14 12:17 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.core.metadata.IPage<com.xh.vdm.statistic.vo.response.VehicleRateDetailResponse>>
     **/
    @PostMapping("/abnormalVehicleDetail")
    public R<IPage<VehicleRateDetailResponse>> abnormalVehicleDetail(@Validated @RequestBody DetailRequest request){

        //前端下标从 0 开始，此处要加1
        request.setStart(request.getStart()+1);

        DetailParam param = new DetailParam();
        IPage page = null;
        try{
            BeanUtils.copyProperties(request , param);
            param.setMonthStrict(request.getMonth().replace("-" , ""));
            if(request.getCount() < 1){
                //如果不传递页面size，那么就默认设置为10
                request.setCount(10);
            }
            if(request.getStart() < 1){
                request.setStart(1);
            }
            param.setCurrent(request.getStart()/request.getCount()+(request.getStart() % request.getCount()>0?1:0));
            param.setSize(request.getCount());
            page = vehicleStatService.findVehicleRateDetail(param);

            //颜色名值转换
            Map<String , String> colorMap = cacheUtil.getVehicleColorMap();
            List<VehicleRateDetailResponse> list = page.getRecords();
            list.forEach(item ->{
                item.setPlateColor(colorMap.get(item.getPlateColor()));
            });
        }catch (Exception e){
            e.printStackTrace();
            log.error("查询异常车辆明细失败",e);
            return R.fail("查询异常车辆明细失败");
        }
        return R.data(page);
    }


    /**
     * @description: 异常车辆明细导出
     * @author: zhouxw
     * @date: 2022/9/14 12:17 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.core.metadata.IPage<com.xh.vdm.statistic.vo.response.VehicleRateDetailResponse>>
     **/
    @PostMapping("/abnormalVehicleDetailExport")
    public R<String> abnormalVehicleDetailExport(@Validated @RequestBody DetailRequest request){
        DetailParam param = new DetailParam();
        IPage page = null;
        try{
            BeanUtils.copyProperties(request , param);
            param.setMonthStrict(request.getMonth().replace("-" , ""));
            param.setCurrent(0);
            param.setSize(Integer.MAX_VALUE);
            page = vehicleStatService.findVehicleRateDetail(param);

            List<VehicleRateDetailResponse> list = page.getRecords();
            //颜色名值转换
            Map<String , String> colorMap = cacheUtil.getVehicleColorMap();
            list.forEach(item ->{
                item.setPlateColor(colorMap.get(item.getPlateColor()));
            });
            //执行数据导出
            String title = "异常车辆明细";
            String[] arrs = {"企业名称","车牌号","车牌颜色","SIM卡号","终端ID","终端型号","漂移次数","轨迹完整率","数据合格率","异常日期"};
            String fileName = ExcelExport.exportExcelFile(title,list,arrs,staticFilePath);
            return R.data(fileName);
        }catch (Exception e){
            e.printStackTrace();
            log.error("导出异常车辆明细失败",e);
            return R.fail("导出异常车辆明细失败");
        }
    }


    /**
     * @description: 获取下个月第一天的日期
     * @author: zhouxw
     * @date: 2022/9/14 12:24 PM
     * @param: [month]
     * @return: java.lang.String
     **/
    private String getNextMonthFirstDay(String month){
        String nextMonthFirstDay = "";
        int mon = Integer.parseInt(month.replace("-","").substring(4,6));
        //查询入网，日期要限制到下个月1号
        mon = mon + 1;
        if(mon < 10){
            nextMonthFirstDay = month.substring(0,5) + "0" + mon + "-" + "01";
        }else{
            nextMonthFirstDay = month.substring(0,5) + mon + "-" + "01";
        }
        return nextMonthFirstDay;
    }

	/**
	 * @description: 查询指定企业或者区域内得分情况
	 * 如果指定了企业，则查询企业的相关指标；如果没有指定企业，则查询整个区域的相关指标
	 * 查询单个月的指标
	 * @author: zhouxw
	 * @date: 2022/11/16 4:39 PM
	 * @param: [request, isExport]
	 * @return: java.util.List<com.xh.vdm.statistic.vo.response.CompanyRateResponse>
	 **/
	public CompanyRateResponse getCompanyRateListDeptOrArea (Long ownerId , Long deptId , String month) throws Exception{


		//2.入网车辆数
		String nextMonthFirstDay = getNextMonthFirstDay(month);
		VehicleCountWithDept inNetCountDept = null;
		try{
			inNetCountDept = vehicleStatService.findInNetCountByDeptIdDeptOrArea(nextMonthFirstDay , deptId , ownerId);
		}catch (Exception e){
			log.info("查询入网车辆数失败",e);
		}
		String deptName = inNetCountDept==null?"":inNetCountDept.getDeptName();

		//3.上线率
		//上线情况
		VehicleCountWithDept goOnlineCountDept = null;
		try{
			goOnlineCountDept = vehicleStatService.findGoOnlineCountByDeptIdDeptOrArea(month,deptId , ownerId);
		}catch (Exception e){
			log.info("查询上线率失败",e);
		}
		//上线率
		double goOnlineRate = goOnlineCountDept==null||inNetCountDept==null||inNetCountDept.getCount()==0?0:MathUtil.divideRoundDouble(goOnlineCountDept.getCount() , inNetCountDept.getCount() , 4);

		//4.轨迹完整率
		VehicleRateWithDept completeRateDept = null;
		try{
			completeRateDept = completeService.findCompleteByDeptIdDeptOrArea(month , deptId , ownerId);
		}catch (Exception e){
			log.info("查询轨迹完整率失败",e);
		}
		double completeRate = completeRateDept==null?0:completeRateDept.getRate();

		//5.数据合格率
		VehicleRateWithDept qualityRateDept = null;
		try{
			qualityRateDept = locationQualityService.findQualityRateByDeptIdDeptOrArea(month , deptId , ownerId);
		}catch (Exception e){
			log.info("查询数据合格率失败",e);
		}
		double qualityRate = qualityRateDept==null?0:qualityRateDept.getRate();


		//6.漂移相关
		//漂移车辆数
		VehicleCountWithDept driftCountDept = null;
		try{
			driftCountDept = driftService.findDriftCountByDeptIdDeptOrArea(month , deptId , ownerId);
		}catch (Exception e){
			log.info("查询漂移车辆失败",e);
		}
		//漂移率(漂移数/上线数)
		double driftRateD = (goOnlineCountDept==null||driftCountDept==null||goOnlineCountDept.getCount()==0)?0:MathUtil.divideRoundDouble(driftCountDept.getCount() , goOnlineCountDept.getCount() , 4);

		//7.拼装数据
		CompanyRateResponse response = new CompanyRateResponse();
		response.setDeptName(deptName);
		response.setDriffCount(driftCountDept==null?0:driftCountDept.getCount());
		response.setCompleteRate(MathUtil.formatToPercent(completeRate , 2));
		response.setQualifiedRate(MathUtil.formatToPercent(qualityRate , 2));
		//设置漂移率
		String driftRate = MathUtil.formatToPercent(driftRateD , 2);
		if(driftRate == null){
			if(goOnlineCountDept.getCount() > 0 ){
				//如果上线率大于0 ，说明有车辆上线，如果此时漂移率为空，说明未发生漂移
				driftRate = "0%";
			}
		}
		response.setDriftRate(driftRate);
		response.setGoOnlineRate(MathUtil.formatToPercent(goOnlineRate , 2));
		response.setIntNetCount(inNetCountDept==null?0:inNetCountDept.getCount());
		response.setStatDate(month);

		//后续添加属性
		response.setGoOnlineCount(goOnlineCountDept==null?0:goOnlineCountDept.getCount());
		response.setDriftRateD(MathUtil.roundDouble(driftRateD ,2));
		response.setCompleteRateD(MathUtil.roundDouble(completeRate , 2));
		response.setQualifiedRateD(MathUtil.roundDouble(qualityRate , 2));
		response.setGoOnlineRateD(MathUtil.roundDouble(goOnlineRate , 2));

		return response;
	}
}
