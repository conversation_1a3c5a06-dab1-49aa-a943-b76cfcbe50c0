package com.xh.vdm.statistic.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.statistic.entity.Point;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * <AUTHOR>
 * @Date：2019-09-21 11:21
 * @Description：高德调用
 */
@Component
public class MapDistanceUtil {
    private final static Logger logger = LoggerFactory.getLogger(MapDistanceUtil.class);

    @Value("${map.gaode.host:https://restapi.amap.com/v3/geocode/regeo?output=json&location=<points>&key=<key>&radius=1000&extensions=base&batch=true}")
    private String mapUrl;

    @Value("${map.gaode.key:}")
    private String mapKey;

	//国能逆地理编码url
	@Value("${map.guoneng.url:http://*************/api/geocoder?postStr=%7B%22lon%22:%22<lon>%22,%22lat%22:%22<lat>%22,%22ver%22:1%7D&type=geocode}")
	private String mapUrlGN;

    public static final Integer subListSize = 20;

    public static final Integer threads = 5;



	/**
	 * 调用国能逆地理编码接口
	 * @param pointList
	 * @return
	 */
	public List<String> getAddress(List<Point> pointList) {
		List<String> addressList = new ArrayList<>(pointList.size());

		//1.对经纬度去重
		Set<String> set = new HashSet<>();
		pointList.forEach(item -> {
			String locStr = item.getLongitude()+"~"+item.getLatitude();
			set.add(locStr);
		});

		//2.调用接口查询轨迹
		Map<String,String> map = new HashMap<>();
		set.forEach(item -> {
			if(StringUtils.isNotEmpty(item)){
				String lon = item.split("~")[0];
				String lat = item.split("~")[1];
				RestTemplate restTemplate = new RestTemplate();
				String url = mapUrlGN.replace("<lon>", lon);
				url = url.replace("<lat>", lat);
				JSONObject jsonObject = restTemplate.getForObject(url, JSONObject.class);
				String address = jsonObject.getString("formatted_address");
				map.put(item, address);
			}
		});

		//设置返回结果一一对应
		pointList.forEach(item -> {
			String locStr = item.getLongitude()+"~"+item.getLatitude();
			String address = map.get(locStr);
			addressList.add(address);
		});
		return addressList;
	}



    /**
     * 按照20个一次调用高德接口
     * @param pointList
     * @return
     */
    public List<String> getAddressGD(List<Point> pointList) {
        List<String> addressList = new ArrayList<>(pointList.size());
        //除数
        Integer c = pointList.size()/subListSize;
        //余数
        Integer y = pointList.size()%subListSize;
        if(y>0){
            c ++;
        }
        for (int i = 0; i < c; i++) {
            List<Point> subList = null;
            if(((i+1)*subListSize)>pointList.size()){
                subList = pointList.subList(i*subListSize,pointList.size());
            }else{
                subList = pointList.subList(i*subListSize,(i+1)*subListSize);
            }
            List<String> subAddressList = getAddressBySubList(subList);
            addressList.addAll(subAddressList);
        }
        return addressList;
    }

    /**
     * 按照20个一次调用高德接口
     * @param pointList
     * @return
     */
    public List<String> getAddressContainNull(List<Point> pointList) {
        List<String> addressList = new ArrayList<>(pointList.size());
        //除数
        Integer c = pointList.size()/subListSize;
        //余数
        Integer y = pointList.size()%subListSize;
        if(y>0){
            c ++;
        }
        for (int i = 0; i < c; i++) {
            List<Point> subList = null;
            if(((i+1)*subListSize)>pointList.size()){
                subList = pointList.subList(i*subListSize,pointList.size());
            }else{
                subList = pointList.subList(i*subListSize,(i+1)*subListSize);
            }
            List<String> subAddressList = getAddressBySubListContainNull(subList);
            addressList.addAll(subAddressList);
        }
        return addressList;
    }


    /**
     *根据经纬度获取省市区，列表最大20个
     * @param pointList
     * @return
     */
    public List<String> getAddressBySubList(List<Point> pointList) {
        List<String> addressList = new ArrayList<>();
        String points = generatePointStr(pointList);
        String urlString = mapUrl.replace("<points>",points.toString()).replace("<key>",mapKey);
        logger.info(urlString);
        try {
            RestTemplate restTemplate = new RestTemplate();
            JSONObject jsonObject = restTemplate.getForObject(urlString, JSONObject.class);
            logger.info(jsonObject.toJSONString());
            JSONArray address = jsonObject.getJSONArray("regeocodes");

            for(int i=0;i<address.size();i++){
                addressList.add(address.getJSONObject(i).getString("formatted_address"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取地址信息异常{}", e.getMessage());
        }
        return addressList;
    }

    /**
     *根据经纬度获取省市区，列表最大20个
     * 如果无法获取地址，则补充为 ”未知地址“
     * @param pointList
     * @return
     */
    public List<String> getAddressBySubListContainNull(List<Point> pointList) {
        List<String> addressList = new ArrayList<>();
        String points = generatePointStr(pointList);
        String urlString = mapUrl.replace("<points>",points.toString()).replace("<key>",mapKey);
        logger.info(urlString);
        try {
            RestTemplate restTemplate = new RestTemplate();
            JSONObject jsonObject = restTemplate.getForObject(urlString, JSONObject.class);
            logger.info(jsonObject.toJSONString());
            JSONArray address = jsonObject.getJSONArray("regeocodes");
            if(address == null){
                //如果为查询到地址信息，则补充为”未知地址“
                for(int i = 0 ; i < pointList.size(); i++){
                    addressList.add("未知地址");
                }
            }else{
                for(int i=0;i<address.size();i++){
                    addressList.add(address.getJSONObject(i).getString("formatted_address"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取地址信息异常{}", e.getMessage());
        }
        return addressList;
    }

    public String generatePointStr(List<Point> pointList){
        StringBuffer sb = new StringBuffer();
        for(int i=0;i<pointList.size();i++){
            sb.append(pointList.get(i).getLongitude()).append(",").append(pointList.get(i).getLatitude()).append("|");
        }
        if(sb.length()>0){
            sb.deleteCharAt(sb.lastIndexOf("|"));
        }
        return sb.toString();
    }


    /*public static void main (String[] args){
        List<Integer> list = new ArrayList<>();
        list.add(1);
        list.add(2);
        list.add(3);
        list.add(4);
        list.add(5);
        list.add(6);
        list.add(7);
        list.add(8);
        list.add(9);
        list.add(10);
        MapDistanceUtil util = new MapDistanceUtil();
        util.test(list);
    }*/


    /**
     * 按照20个一次调用高德接口
     * @param pointList
     * @return
     */
    public List<String> test(List<Integer> pointList) {
        //除数
        Integer c = pointList.size()/5;
        //余数
        Integer y = pointList.size()%5;
        if(y>0){
            c ++;
        }
        for (int i = 0; i < c; i++) {
            List<Integer> subList = null;
            if(((i+1)*5)>pointList.size()){
                subList = pointList.subList(i*5,pointList.size());
            }else{
                subList = pointList.subList(i*5,(i+1)*5);
            }
            System.out.println(subList);
        }
        return null;
    }
}
