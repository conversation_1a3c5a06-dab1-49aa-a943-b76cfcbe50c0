package com.xh.vdm.statistic.utils;

/**
 * 代码信息工具类
 * <AUTHOR>
 * @date Created in 16:16 2020/11/5
 * @description
 */
public class CodeInfoUtils {
    /**
     * 获取文件名
     * <AUTHOR>
     * @date 2020-11-05 16:19:31
     * @return
     **/
    public static String _FILE_()
    {
        StackTraceElement[] stackTraces = (new Throwable()).getStackTrace();
        return stackTraces[1].getFileName();
    }

    /**
     * 获取当前行数
     * <AUTHOR>
     * @date 2020-11-05 16:19:42
     * @return
     **/
    public static int _LINE_()
    {
        StackTraceElement[] stackTraces = (new Throwable()).getStackTrace();
        return stackTraces[1].getLineNumber();
    }

    /**
     * 获取方法名
     * <AUTHOR>
     * @date 2020-11-05 16:19:51
     * @return
     **/
    public static String _FUNC_()
    {
        StackTraceElement[] stackTraces = (new Throwable()).getStackTrace();
        return stackTraces[1].getMethodName();
    }

    /**
     * 获取当前位置完整信息
     * <AUTHOR>
     * @date 2020-11-05 16:20:04
     * @return
     **/
    public static String _FILE_LINE_FUNC_()
    {
        StackTraceElement[] stackTraces = (new Throwable()).getStackTrace();
        StringBuffer strBuffer = new StringBuffer("[");
        strBuffer.append(stackTraces[1].getFileName()).append(".");
        strBuffer.append(stackTraces[1].getClassName()).append(".");
        strBuffer.append(stackTraces[1].getMethodName()).append(":");
        strBuffer.append(stackTraces[1].getLineNumber()).append("]");
        return strBuffer.toString();
    }
}
