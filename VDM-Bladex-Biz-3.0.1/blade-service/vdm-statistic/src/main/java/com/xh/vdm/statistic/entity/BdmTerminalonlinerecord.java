package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 车辆上下线记录
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmTerminalonlinerecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联的车辆
     */
    private Integer vehicleId;

    private Date onLineTime;

    private Date offLineTime;

    private Date updateTime;

    private Date createTime;

    private String driverName;

    /**
     * 定位条数
     */
    private Integer pointNum;

    private String licencePlate;

    private Integer licenceColor;

    private String phone;

    private Integer isDel;

    private Long createUserId;


}
