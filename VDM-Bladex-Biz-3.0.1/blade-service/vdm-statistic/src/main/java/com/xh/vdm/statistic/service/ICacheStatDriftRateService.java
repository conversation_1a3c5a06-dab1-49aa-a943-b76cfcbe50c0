package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.CacheStatDriftRate;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.DriftRateResponse;

/**
 * <p>
 * 车辆漂移率DB缓存表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
public interface ICacheStatDriftRateService extends IService<CacheStatDriftRate> {

    /**
     * @description: 统计保存漂移率DB缓存数据
     * @author: zhouxw
     * @date: 2023-03-88 11:13:34
     * @param: [month:要统计的数据月份 yyyy-MM]
     * @return: void
     **/
    void statAndSaveDriftRateCache(String month) throws Exception;

    /**
     * @description: 从DB缓存中查询漂移率信息
     * @author: zhouxw
     * @date: 2023-03-90 16:19:53
     * @param: [request]
     * @return: com.xh.vdm.statistic.vo.response.DriftRateResponse
     **/
    DriftRateResponse queryDriftRateInDBCache(RateRequest request) throws Exception;

}
