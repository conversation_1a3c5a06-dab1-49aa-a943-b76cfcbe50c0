package com.xh.vdm.statistic.mapper;

import com.xh.vdm.statistic.entity.StatVehOnlineDay;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.vo.response.OnlineHourCountResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 车辆每个小时在线时长统计 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-23
 */
public interface StatVehOnlineDayMapper extends BaseMapper<StatVehOnlineDay> {

	/**
	 * 保存在线时长数据，存在即更新
	 * @param list
	 */
	void saveOrUpdateVehDuration(@Param("list") List<StatVehOnlineDay> list);

	/**
	 * 查询当日每个小时车辆在线数量
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	List<OnlineHourCountResponse> getOnlineHourCount(@Param("hourList") List<String> hourList, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);
}
