package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.report.AlarmNode;
import com.xh.vdm.statistic.entity.report.BdmDealAlarmOption;

import java.util.List;

/**
 * <p>
 * 报警处理选项表（作用于服务商或第三方） 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
public interface IBdmDealAlarmOptionService extends IService<BdmDealAlarmOption> {

}
