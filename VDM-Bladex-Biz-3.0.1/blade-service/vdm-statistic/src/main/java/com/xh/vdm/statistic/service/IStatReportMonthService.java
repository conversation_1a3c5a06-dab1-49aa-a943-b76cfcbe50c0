package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.report.StatReportMonth;
import com.xh.vdm.statistic.vo.request.CompanyAndDate;
import com.xh.vdm.statistic.vo.response.ReportInfoResponse;
import org.springblade.core.mp.support.Query;

/**
 * <p>
 * 企业月报表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface IStatReportMonthService extends IService<StatReportMonth> {

	IPage<ReportInfoResponse> getMonthReportPage (CompanyAndDate request, Query query);
}
