package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.statistic.entity.BdmRoute;
import com.xh.vdm.statistic.entity.Point;
import com.xh.vdm.statistic.mapper.BdmRouteMapper;
import com.xh.vdm.statistic.service.IBdmRouteService;
import com.xh.vdm.statistic.service.ILocationService;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.DistanceUtils;
import com.xh.vdm.statistic.utils.MapDistanceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/12 12:25
 */
@Service
@Slf4j
public class BdmRouteServiceImpl  extends ServiceImpl<BdmRouteMapper,BdmRoute> implements IBdmRouteService {

	private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

	@Resource
	ILocationService locationService;


	@Autowired
	MapDistanceUtil mapDistanceUtil;



	public void generateBdmRoute(String dateStr) throws Exception{
		//查询开始时间和结束时间
		Date date = sdf.parse(dateStr);
		long startTime = DateUtil.getDayFirstSecondTimestamp(date.getTime()/1000);
		long endTime = DateUtil.getDayLastSecondTimestamp(date.getTime()/1000);

		//String tableName = "location_"+date;
		//查询指定日期上线的车辆
		//List<HashMap> mapList = bdmRouteMapper.queryLicencePlates(tableName);
		List<VehicleBase> mapList = locationService.findUploadLicencePlatesByDay(dateStr);


		List<String> licencePlateList = new ArrayList<>();
		mapList.forEach(item ->{
			licencePlateList.add(item.getLicencePlate()+"~"+item.getLicenceColor());
		});
		List<BdmRoute> bdmRouteList = new ArrayList<>();
		for(String licencePlate : licencePlateList){
			LocationKudu prePoint = null;

			//车牌号
			if(StringUtils.isBlank(licencePlate) || licencePlate.split("~").length < 2){
				continue;
			}
			String vehicleNo = licencePlate.split("~")[0];
			Integer licenceColor = Integer.parseInt(licencePlate.split("~")[1]);

			//根据指定日期和车牌号、车牌颜色查询轨迹信息
			//List<LocationKudu> locationList = bdmRouteMapper.queryLocations(tableName,vehicleNo, vehicleColor);
			List<LocationKudu> locationList = locationService.findUploadLocationPointListByDay(dateStr,vehicleNo, licenceColor);

			if(locationList!=null&&locationList.size()>1){
				Double mileage = 0D;
				for(int i=1;i<locationList.size();i++){
					if(i==1){
						prePoint = locationList.get(i-1);
					}
					LocationKudu currentPoint = locationList.get(i);
					Double distance = DistanceUtils.wgs84Distance(prePoint.getLongitude(),prePoint.getLatitude(),currentPoint.getLongitude(),currentPoint.getLatitude());
					Long time = currentPoint.getTime() - prePoint.getTime();
					Double speed = distance/time;
					if(speed>120){
						log.info(vehicleNo+"定位点异常，速度大于120。");
					}else{
						mileage += distance;
						prePoint = currentPoint;
					}
				}
				BdmRoute bdmRoute = new BdmRoute();
				bdmRoute.setLicencePlate(vehicleNo);
				bdmRoute.setLicenceColor(licenceColor);
				bdmRoute.setStartTime(new Date(locationList.get(0).getTime()*1000));
				bdmRoute.setEndTime(new Date(locationList.get(locationList.size()-1).getTime()*1000));
				bdmRoute.setDeal(0);
				bdmRoute.setForm(0L);
				bdmRoute.setMileage(mileage/1000);
				bdmRoute.setSensitiveArea("");
				bdmRoute.setRemark("");
				bdmRoute.setDepartLongitude(locationList.get(0).getLongitude());
				bdmRoute.setDepartLatitude(locationList.get(0).getLatitude());
				bdmRoute.setDestinationLongitude(locationList.get(locationList.size()-1).getLongitude());
				bdmRoute.setDestinationLatitude(locationList.get(locationList.size()-1).getLatitude());
				bdmRouteList.add(bdmRoute);
			}
		}
		//补充出发地目的地
		List<Point> departList = new ArrayList<>();
		List<Integer> departIndList = new ArrayList<>();
		List<Point> destinationList = new ArrayList<>();
		List<Integer> destinationIndList = new ArrayList<>();
		for(int i=0;i<bdmRouteList.size();i++){
			BdmRoute bdmRoute = bdmRouteList.get(i);
			if(bdmRoute.getDepartLongitude()!=null&&bdmRoute.getDepartLongitude().doubleValue()!=0&&bdmRoute.getDepartLatitude()!=null&&bdmRoute.getDepartLatitude().doubleValue()!=0){
				departList.add(new Point(bdmRoute.getDepartLongitude(),bdmRoute.getDepartLatitude()));
				departIndList.add(i);
			}
			if(bdmRoute.getDestinationLongitude()!=null&&bdmRoute.getDestinationLongitude().doubleValue()!=0&&bdmRoute.getDestinationLatitude()!=null&&bdmRoute.getDestinationLatitude().doubleValue()!=0){
				destinationList.add(new Point(bdmRoute.getDestinationLongitude(),bdmRoute.getDestinationLatitude()));
				destinationIndList.add(i);
			}
		}
		List<String> departAddressList = mapDistanceUtil.getAddressContainNull(departList);
		for(int i=0;i<departIndList.size();i++){
			bdmRouteList.get(departIndList.get(i)).setDepart(departAddressList.get(i));
		}

		List<String> destinationAddressList = mapDistanceUtil.getAddressContainNull(destinationList);
		for(int i=0;i<destinationIndList.size();i++){
			bdmRouteList.get(destinationIndList.get(i)).setDestination(destinationAddressList.get(i));
		}

		saveBatch(bdmRouteList);
	}
}
