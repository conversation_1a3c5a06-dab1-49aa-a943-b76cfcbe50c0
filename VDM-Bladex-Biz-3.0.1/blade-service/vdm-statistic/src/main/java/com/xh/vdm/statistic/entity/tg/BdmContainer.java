package com.xh.vdm.statistic.entity.tg;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springblade.common.utils.compare.Compare;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 集装箱实体
 */
@Data
@ExcelIgnoreUnannotated
public class BdmContainer implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	@Compare("集装箱编号")
	@ExcelProperty(value = "集装箱编号",index = 1)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	private String number;
	@Compare("箱型")
	private Integer model;
	@Compare("尺寸，单位英尺")
	private Integer size;
	@Compare("所属部门id")
	private Long deptId;
	@Compare("总重，单位kg")
	private Float maxGross;
	@Compare("自重/皮重，单位kg")
	private Float tare;
	@Compare("载重/净重，单位kg")
	private Float net;
	@Compare("最大装货容积，单位m³")
	private Integer cuCap;
	@Compare("长度，单位mm")
	private Integer length;
	@Compare("高度，单位mm")
	private Integer height;
	@ExcelProperty(value = "创建时间",index = 5)
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(28)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	private Date updateTime;

	private Integer deleted;

	private Integer targetType;

	//导入错误
	@TableField(exist = false)
	private String msg;

}

