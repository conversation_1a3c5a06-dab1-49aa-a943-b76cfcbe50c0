package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.vo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 疲劳驾驶请求类
 * <AUTHOR>
 * @date 2021/11/1 10:19
 */
@ApiModel(value = "疲劳驾驶请求类")
@Data
public class FatigueDrivingRequest extends PageParam {

    @ApiModelProperty(value = "车牌号码")
    @JsonProperty("licence_plate")
    private String licencePlate;

    @ApiModelProperty(value = "车牌颜色")
    @JsonProperty("licence_color")
    private String licenceColor;

    @ApiModelProperty(value = "查询开始时间")
    @JsonProperty("start_time")
    private Long startTime;

    @ApiModelProperty(value = "查询结束时间")
    @JsonProperty("end_time")
    private Long endTime;


    @ApiModelProperty(value = "车辆归属")
    @JsonProperty("vehicle_owner")
    private Long vehicleOwnerId;

    @ApiModelProperty(value = "行业类型")
    @JsonProperty("vehicle_use_type")
    private Long vehicleUseType;

    @ApiModelProperty(value = "车辆接入方式")
    @JsonProperty("access_mode")
    private Integer accessMode;

    @ApiModelProperty(value = "车组")
    @JsonProperty("dept_id")
    private String deptId;

    //所有车组，包含子车组
    private List<Long> deptList;
    private List<Long> professionList;
}
