<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.FatigueMapper">

    <select id="statFatigueByDeptAndDuration" resultType="com.xh.vdm.statistic.entity.FatigueWithDeptAndDate">
        select dept_id , to_timestamp(alarm_time, '%Y-%m') date , sum((alarm_end_time - alarm_time)) fatigue_second  from bdm_security
        where 1 = 1
        and dept_id in (
        <foreach collection="deptIds" item="item" separator=",">
            #{item}
        </foreach>
        )
        and alarm_type in (2)
        and alarm_time >= #{startSecondTimestamp,jdbcType=BIGINT}
        and alarm_time &lt;= #{endSecondTimestamp,jdbcType=BIGINT}
        group by dept_id , date
    </select>


    <select id="getFatigueByDeptAndDuration" resultType="com.xh.vdm.statistic.entity.FatigueWithDeptAndDate">
        select
        <if test="deptId != null and deptId != ''">
        dept_id , to_timestamp(alarm_time, '%Y-%m') date ,
        </if>

        sum((alarm_end_time - alarm_time)) fatigue_second  from bdm_security
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId}
        </if>
        and alarm_type in (2)
        and alarm_time >= #{startSecondTimestamp,jdbcType=BIGINT}
        and alarm_time &lt;= #{endSecondTimestamp,jdbcType=BIGINT}
        <if test="deptId != null and deptId != ''">
        group by dept_id , date
        </if>

    </select>
</mapper>
