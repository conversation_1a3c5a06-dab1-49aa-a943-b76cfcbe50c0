package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlRunner;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.bd.entity.VehicleBase;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.StatVehTravelDurationMapper;
import com.xh.vdm.statistic.service.ILocationService;
import com.xh.vdm.statistic.service.IStatTaskLogService;
import com.xh.vdm.statistic.service.IStatVehTravelDurationService;
import com.xh.vdm.statistic.utils.BeanUtil;
import com.xh.vdm.statistic.utils.DataUtils;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.vo.request.DeptAndDurationNoPageRequest;
import com.xh.vdm.statistic.vo.request.VehicleTravelAverageDurationResponse;
import com.xh.vdm.statistic.vo.request.VehicleTravelDurationRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;

/**
 * <p>
 * 车辆行驶时长表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@Service
@Slf4j
public class StatVehTravelDurationServiceImpl extends ServiceImpl<StatVehTravelDurationMapper, StatVehTravelDuration> implements IStatVehTravelDurationService {


	private final ThreadPoolExecutor thread_pool = new ThreadPoolExecutor(10,20,300, TimeUnit.SECONDS,new LinkedBlockingQueue<>());

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

    public final String STAT_DATE = "statDate";

    public final String TOTAL_MILEAGE = "totalMileage";

    @Resource
    private IStatTaskLogService taskLogService;

    private static ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    @Resource
    private DataUtils dataUtils;

    @Resource
    private ILocationService locationService;


    @Override
    public boolean vehTravelDurationStat(String day) throws Exception {

        //校验日期格式
        Date statDate = new Date();
        try {
            statDate = sdf.parse(day);
        } catch (ParseException e) {
            log.error("[车辆行驶时长统计]执行出现异常，日期 " + day + " 格式错误 "  , e);
            throw new Exception("日期格式错误：" + day);
        }

        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION , "校验日期格式完成");

        //查询目标月表是否存在，如果不存在，则创建表
        String tableName = StatisticConstants.VEHICLE_TRAVEL_DURATION_TEMPLATE_TABLE + "_" + day.substring(0 , 6);
        dataUtils.checkTableExistAndCreate(tableName , StatisticConstants.VEHICLE_TRAVEL_DURATION_TEMPLATE_TABLE);
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION , "校验月表完成");

        //1.为防止重复执行造成指标数据重复，需要先删除之前写入的指标数据
        baseMapper.deleteDataByDay(day.substring(0 , 6) , day);
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION , "删除之前跑过的指标数据完成");

        //2.根据指定日期查询车辆列表
        long start1 = System.currentTimeMillis();
        List<VehicleBase> licencePlates = locationService.findAllUploadLicencePlatesByDay(day);
        long end1 = System.currentTimeMillis();
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION , "查询车辆列表成功，共 "+(licencePlates==null?"0":licencePlates.size())+" 辆车，共耗时 " +(end1 - start1)+ " ms");

        //3.对每辆车的上报信息进行分析，获取驾驶时长
        long start2 = System.currentTimeMillis();
        CountDownLatch countDownLatch = new CountDownLatch(licencePlates.size());
        //记录每辆车的每个小时的时长
        ConcurrentHashMap<String , Long[]> hourMap = new ConcurrentHashMap<>();
        //记录每辆车的总时长
        ConcurrentHashMap<String, Long> totalMap = new ConcurrentHashMap<>();
        for(VehicleBase vehicle : licencePlates){
			String licencePlate = vehicle.getLicencePlate();
			int licenceColor = vehicle.getLicenceColor().intValue();
            Date finalStatDate = statDate;
            thread_pool.submit(() -> {
                //taskLogService.addStatTaskProcessLog(CommonConstants.TASK_VEHICLE_TRAVEL_DURATION , "[数据质量跑批]任务提交了");
                try {
                    long start = System.currentTimeMillis();
                    //3.1 查询车辆所有上报数据点信息（已排序）
                    List<LocationKudu> list = locationService.findUploadLocationPointListAllByDay(day, licencePlate,licenceColor);
                    if(list == null || list.size() < 1){
                        return ;
                    }
                    long end = System.currentTimeMillis();
                    //taskLogService.addStatTaskProcessLog(CommonConstants.TASK_VEHICLE_TRAVEL_DURATION, "[数据质量跑批]查询车辆点位信息成功，共" + (list == null ? "0" : list.size()) + "个点位信息，共耗时 " + (end - start) + " ms");
                    long startV = System.currentTimeMillis();
                    //3.2 计算车辆每个小时的运行时长与总时长
                    //记录总行驶时长
                    long totalDuration = 0;
                    //记录每个小时的行驶时长
                    Long[] hourDuration = new Long[24];
                    for(int i = 1 ; i < list.size() ; i++){
                        //当前定位点
                        LocationKudu locationNow = list.get(i);
                        //前一个定位点
                        LocationKudu locationPre = list.get(i - 1);
                        //只记录行驶点之间的时长，并且，只记录有效点之间的时长
                        if(locationNow.getSpeed() > 0 && locationPre.getSpeed() > 0 && locationNow.getValid() != 0 && locationPre.getValid() != 0){
                            //两点间的时长
                            long duration = locationNow.getTime() - locationPre.getTime();
                            if(duration > 30 * 60){
                                //如果两点间的间隔大于30分钟，则认为是无效点，接着从下一个点开始计算
                                continue;
                            }
                            //记录总时长
                            totalDuration = totalDuration + duration;

                            if(DateUtil.getHour(locationNow.getTime()) != DateUtil.getHour(locationPre.getTime())){
                                //如果不是同一个小时内的定位点

                                //分别将属于各自小时的时间计入到各自时长中
                                int hourPre = DateUtil.getHour(locationPre.getTime());
                                int hourNow = DateUtil.getHour(locationNow.getTime());
                                //获取整点的时间戳
                                long secondTimestampAtHour = 0;
                                try {
                                    secondTimestampAtHour = DateUtil.getSecondTimestampAtHour(locationNow.getTime());
                                }catch (Exception e){
                                    System.out.println("--->>>" + locationNow);
                                }
                                //属于上个小时的时长
                                long durationPre = secondTimestampAtHour - locationPre.getTime();
                                hourDuration[hourPre-1] = hourDuration[hourPre-1]==null?0:hourDuration[hourPre-1] + durationPre;
                                //属于本小时的时长
                                long durationNow = locationNow.getTime() - secondTimestampAtHour;
                                hourDuration[hourNow-1] = hourDuration[hourNow-1]==null?0:hourDuration[hourNow-1] + durationNow;
                            }else{
                                //如果是同一个小时内的定位点

                                //更新小时内的时长
                                int hour = DateUtil.getHour(locationNow.getTime());
                                hourDuration[hour-1] = hourDuration[hour-1]==null?0:hourDuration[hour-1] + duration;
                            }
                        }
                    }

                    hourMap.put(licencePlate , hourDuration);
                    totalMap.put(licencePlate , totalDuration);
                    long endV = System.currentTimeMillis();
                    //taskLogService.addStatTaskProcessLog(CommonConstants.TASK_VEHICLE_TRAVEL_DURATION, "[数据质量跑批]数据质量验证成功，共耗时：" + (endV - startV) + " ms");
                }catch (Exception e){
                    e.printStackTrace();
                    taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION, "[数据质量跑批]数据质量验证出现异常:"+e.getMessage());
                }finally {
                    countDownLatch.countDown();
                }
            });
        }
        countDownLatch.await();
        long end2 = System.currentTimeMillis();
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION , "[数据质量跑批]对车辆列表中的车辆进行数据校验完成，共耗时" + (end2 - start2) + " ms");

        //4.记录每辆车的时长
        long start3 = System.currentTimeMillis();
        List<StatVehTravelDuration> durations = new ArrayList<>();
        for(String s : hourMap.keySet()){
            StatVehTravelDuration duration = new StatVehTravelDuration();
            duration.setLicencePlate(s);
            duration.setStatDate(day);
            duration.setH01(hourMap.get(s)[0]);
            duration.setH02(hourMap.get(s)[1]);
            duration.setH03(hourMap.get(s)[2]);
            duration.setH04(hourMap.get(s)[3]);
            duration.setH05(hourMap.get(s)[4]);
            duration.setH06(hourMap.get(s)[5]);
            duration.setH07(hourMap.get(s)[6]);
            duration.setH08(hourMap.get(s)[7]);
            duration.setH09(hourMap.get(s)[8]);
            duration.setH10(hourMap.get(s)[9]);
            duration.setH11(hourMap.get(s)[10]);
            duration.setH12(hourMap.get(s)[11]);
            duration.setH13(hourMap.get(s)[12]);
            duration.setH14(hourMap.get(s)[13]);
            duration.setH15(hourMap.get(s)[14]);
            duration.setH16(hourMap.get(s)[15]);
            duration.setH17(hourMap.get(s)[16]);
            duration.setH18(hourMap.get(s)[17]);
            duration.setH19(hourMap.get(s)[18]);
            duration.setH20(hourMap.get(s)[19]);
            duration.setH21(hourMap.get(s)[20]);
            duration.setH22(hourMap.get(s)[21]);
            duration.setH23(hourMap.get(s)[22]);
            duration.setH24(hourMap.get(s)[23]);
            duration.setCreateTime(new Date());
            duration.setTotalDuration(totalMap.get(s));
            durations.add(duration);
        }
        baseMapper.saveBatchByDay(day.substring(0 , 6), durations);
        long end3 = System.currentTimeMillis();
        taskLogService.addStatTaskProcessLog(StatisticConstants.TASK_VEHICLE_TRAVEL_DURATION , "[数据质量跑批]执行数据质量写入完成，共耗时 "+(end3 - start3)+" ms");
        return true;
    }

    @Override
    public List<VehicleTravelDurationNode> findVehicleTravelDurationCountByCondition(VehicleTravelDurationRequest request) {
        return baseMapper.getVehicleTranvelDurationCountByCondition(request);
    }

    @Override
    public List<DateAndCount> findPassengerVehCountInNight(VehicleTravelDurationRequest request) {
        return baseMapper.getPassengerVehCountInNight(request);
    }

    @Override
    public List<DateAndData> findAverageTravelDurationByDay(VehicleTravelDurationRequest request) {
        return baseMapper.getAverageTravelDurationByDay(request);
    }

    //通过动态拼接sql的方式实现
    @Override
    public List<TotalMileageNode> findTotalMileageByDay(VehicleTravelDurationRequest request) {
        Date startDate = new Date();
        startDate.setTime(request.getStartTime() * 1000);
        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);
        List<VehicleTravelAverageDurationResponse> list = new ArrayList<>();
        StringBuffer finalSql = new StringBuffer();
        int index = 0;
        //动态拼接sql
        while(cal.getTimeInMillis()/1000 <= request.getEndTime()){
            String dayIndexStr = sdfHolder.get().format(cal.getTime()).substring(8,10);
             String sql = getSqlNode(request , dayIndexStr);
            if(index == 0){
                finalSql.append(sql);
            }else{
                finalSql.append(" union ");
                finalSql.append(sql);
            }
            cal.add(Calendar.DAY_OF_MONTH , 1);
            index ++;
        }
        //执行sql
        List<Map<String,Object>> listRes = SqlRunner.db().selectList(finalSql.toString());
        List<TotalMileageNode> resList = new ArrayList<>();
        listRes.stream().forEach(item -> {
            try {
                item.put(STAT_DATE , item.get(STAT_DATE)+"");
                item.put(TOTAL_MILEAGE , Double.parseDouble(item.get(TOTAL_MILEAGE)+""));
                TotalMileageNode node = (TotalMileageNode) BeanUtil.mapToBean(item, TotalMileageNode.class);
                resList.add(node);
            }catch (Exception e){
                log.info("map 转 object 失败" , e);
            }

        });
        return resList;
    }


    private String getSqlNode(VehicleTravelDurationRequest request , String dayIndexStr){
        StringBuffer sb = new StringBuffer();
        sb.append(" select sd.id deptId, sd.name deptName ,sc.licence_plate licencePlate ,   " + request.getMonth() + dayIndexStr + " statDate , coalesce(split_part(split_part(sc.d" + dayIndexStr + ",'#',4),'#' , -1),0) totalMileage");
        sb.append(" from stat_complete_" + request.getMonth() + " sc");
        sb.append(" left join bdm_vehicle bv on bv.licence_plate = sc.licence_plate");
        sb.append(" left join sys_dept sd on bv.dept_id = sd.id");
        sb.append(" where 1 = 1");
        //sb.append(" and bv.vehicle_owner_id = '1'");
        if(StringUtils.isNotBlank(request.getVehicleUseType())){
            sb.append(" and bv.vehicle_use_type in ("+request.getVehicleUseType()+")");
        }else{
            sb.append(" and bv.vehicle_use_type in (10,11,12,30,31,32)");
        }
        if(request.getDeptId() != null && request.getDeptId() > 0){
            sb.append(" and bv.dept_id = "+request.getDeptId());
        }
        sb.append(" and sc.d"+dayIndexStr+" is not null");
        return sb.toString();
    }


    @Override
    public List<TotalMileageNodeForDept> findTotalMileageByDayForDept(VehicleTravelDurationRequest request) {
        //查询企业每天每辆车的总里程
        List<TotalMileageNode> list = findTotalMileageByDay(request);
        if( list == null || list.size() < 1){
            return null;
        }
        String deptName = list.get(0).getDeptName();
        long deptId = list.get(0).getDeptId();
        List<TotalMileageNodeForDept> listF = new ArrayList<>();
        //计算企业每天的总里程
        Map<String , Double> map = new HashMap<>();
        list.forEach(item -> {
            Double totalMileage = map.get(item.getStatDate());
            if(totalMileage == null){
                map.put(item.getStatDate() , item.getTotalMileage());
            }else{
                map.put(item.getStatDate() , item.getTotalMileage() + map.get(item.getStatDate()));
            }
        });
        map.forEach((k , v) -> {
            TotalMileageNodeForDept td = new TotalMileageNodeForDept();
            td.setTotalMileage(v);
            td.setStatDate(k);
            td.setDeptName(deptName);
            td.setDeptId(deptId);
            listF.add(td);
        });
        return listF;
    }

    @Override
    public int findTravelVehicleCountByDate(String month , String date , Long deptId , Long vehicleOwnerId) throws Exception{
        return baseMapper.getTravelVehicleCountByDate(month , date , deptId , vehicleOwnerId);
    }

    @Override
    public double findCompanyTotalMileageByDate(String month, String dateIndex, Long deptId) throws Exception {
        return baseMapper.getCompanyTotalMileageByDate(month , dateIndex , deptId);
    }

    @Override
    public List<DateAndCount> findVehicleOnlineCount(DeptAndDurationNoPageRequest request) throws Exception {
        //1.判断是否跨月
        String monthStart = sdfHolder.get().format(request.getStartSecondTimestamp() * 1000).substring(0 , 7);
        String monthEnd = sdfHolder.get().format(request.getEndSecondTimestamp() * 1000).substring(0 , 7);
        long startSecondTime = DateUtil.getDayFirstSecondTimestamp(request.getStartSecondTimestamp());
        long endSecondTime = DateUtil.getDayLastSecondTimestamp(request.getEndSecondTimestamp());

        List<DateAndCount> resList = new ArrayList<>();

        if(monthStart.equals(monthEnd)){
            //如果没有跨月
            String startDate = DateUtil.getDateString(startSecondTime);
            String endDate = DateUtil.getDateString(endSecondTime);
            String month = startDate.substring(0,4)+startDate.substring(5,7);
            resList = baseMapper.getVehicleOnlineCount(month , startDate , endDate , request.getDeptId());
        }else{
            //如果跨月了
            //分别对每个月进行查询
            int monthCount = DateUtil.monthCountBetweenSecondTimestamp(startSecondTime , endSecondTime);
            Calendar cal = Calendar.getInstance();
            Date date = new Date();
            date.setTime(startSecondTime * 1000);
            cal.setTime(date);
            for(int i = 0 ; i <= monthCount; i++){

                String startDate = "";
                String endDate = "";
                if(i == 0){
                    //如果是首月
                    //查询开头到月末
                    startDate = DateUtil.getDateString(startSecondTime);
                    endDate = DateUtil.getDateString(DateUtil.getMonthLastSecondTimestamp(startSecondTime));
                }else if( i == monthCount){
                    //如果是尾月
                    //查询从月初到时间段结尾
                    startDate = DateUtil.getDateString(DateUtil.getMonthFirstSecondTimestamp(endSecondTime));
                    endDate = DateUtil.getDateString(endSecondTime);
                }else{
                    //如果是中间月
                    //查询从当月开始到结束
                    startDate = DateUtil.getDateString(DateUtil.getMonthFirstSecondTimestamp(cal.getTimeInMillis() / 1000));
                    endDate = DateUtil.getDateString(DateUtil.getMonthLastSecondTimestamp(cal.getTimeInMillis() / 1000));
                }
                String month = startDate.substring(0,4)+startDate.substring(5,7);
                List<DateAndCount> list = baseMapper.getVehicleOnlineCount(month , startDate , endDate , request.getDeptId());
                resList.addAll(list);
                cal.add(Calendar.MONTH , 1);
            }
        }
        return resList;
    }

    @Override
    public int findVehicleTravelCount(VehicleTravelDurationRequest request) throws Exception {
        return baseMapper.getVehicleTravelCountByDuration(request);
    }

    @Override
    public List<DeptAndDateAndCount> findVehicleTravelCountForDept(VehicleTravelDurationRequest request) throws Exception {
        return baseMapper.getVehicleTravelCountForDept(request);
    }





}
