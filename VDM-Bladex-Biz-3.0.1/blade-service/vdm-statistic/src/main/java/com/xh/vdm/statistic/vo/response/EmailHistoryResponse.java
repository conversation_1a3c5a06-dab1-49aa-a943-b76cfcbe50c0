package com.xh.vdm.statistic.vo.response;

import lombok.Data;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2023/3/6 15:19
 */
@Data
public class EmailHistoryResponse {

    //邮件发送历史id
    private Long id;

    //邮件类型：1 日报   2 月报
    private String emailType;

    //邮件发送类型：1 定时自动发送  2 手动调用接口发送  3 手动页面发送
    private String emailSendType;

    //用户名
    private String username;

    //企业名称
    private String deptName;

    //用户邮箱
    private String receiveMailAddress;

    //统计日期
    private String dataDate;

    //发送结果
    private String sendResultDesc;

    //发送日期
    private String sendTime;

    //发送情况备注
    private String note;

}
