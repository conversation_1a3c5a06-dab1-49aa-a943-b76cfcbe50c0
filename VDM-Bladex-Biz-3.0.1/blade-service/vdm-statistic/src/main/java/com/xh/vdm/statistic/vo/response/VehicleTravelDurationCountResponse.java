package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @Description: 企业每小时运行车辆数
 * @Author: zhouxw
 * @Date: 2022/11/30 8:42 AM
 */
@Data
public class VehicleTravelDurationCountResponse {


    @JsonIgnore
    @ExcelIgnore
    private Long deptId;

    //企业名称
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "企业名称"})
    private String deptName;

    /**
     * 统计日期
     */
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "日期"})
    private String statDate;

    /**
     * 00点 到 01点 时间段内 行驶车辆数
     */
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "00时～01时"})
    private Integer h01;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "01时～02时"})
    private Integer h02;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "02时～03时"})
    private Integer h03;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "03时～04时"})
    private Integer h04;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "04时～05时"})
    private Integer h05;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "05时～06时"})
    private Integer h06;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "06时～07时"})
    private Integer h07;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "07时～08时"})
    private Integer h08;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "08时～09时"})
    private Integer h09;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "09时～10时"})
    private Integer h10;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "10时～11时"})
    private Integer h11;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "11时～12时"})
    private Integer h12;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "12时～13时"})
    private Integer h13;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "13时～14时"})
    private Integer h14;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "14时～15时"})
    private Integer h15;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "15时～16时"})
    private Integer h16;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "16时～17时"})
    private Integer h17;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "17时～18时"})
    private Integer h18;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "18时～19时"})
    private Integer h19;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "19时～20时"})
    private Integer h20;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "20时～21时"})
    private Integer h21;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "21时～22时"})
    private Integer h22;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "22时～23时"})
    private Integer h23;
    @ColumnWidth(30)
    @ExcelProperty({"企业车辆分时数量分析", "23时～24时"})
    private Integer h24;
}
