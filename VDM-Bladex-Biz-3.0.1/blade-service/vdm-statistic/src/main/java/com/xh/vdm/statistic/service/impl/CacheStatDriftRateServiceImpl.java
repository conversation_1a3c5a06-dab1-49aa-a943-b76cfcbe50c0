package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.CacheStatDriftRate;
import com.xh.vdm.statistic.entity.VehicleOwnerAndDeptAndTypeAndCount;
import com.xh.vdm.statistic.mapper.CacheStatDriftRateMapper;
import com.xh.vdm.statistic.mapper.StatDriftMapper;
import com.xh.vdm.statistic.service.ICacheStatDriftRateService;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.DriftRateResponse;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 * 车辆漂移率DB缓存表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@Service
public class CacheStatDriftRateServiceImpl extends ServiceImpl<CacheStatDriftRateMapper, CacheStatDriftRate> implements ICacheStatDriftRateService {


    @Resource
    private StatDriftMapper statDriftMapper;

    @Override
    //@Transactional
    public void statAndSaveDriftRateCache(String month) throws Exception {

        //0.删除要统计的月份的缓存数据
        remove(Wrappers.lambdaQuery(CacheStatDriftRate.class).eq(CacheStatDriftRate::getMonth, month));


        //1.查询各个企业的上线车辆数与漂移车辆数
        //上线车辆数
        String monthStr = month+"%";
        List<VehicleOwnerAndDeptAndTypeAndCount> goOnlineCountList = statDriftMapper.getGoOnlineCountByGroup(monthStr);
        //漂移车辆数
        monthStr = month.replace("-","");
        List<VehicleOwnerAndDeptAndTypeAndCount> driftCountList = statDriftMapper.getDriftCountByGroup(monthStr);

        //2.计算车辆漂移率
        //处理数据
        Map<String,Integer> goOnlineCountMap = new HashMap<>();
        Map<String,Integer> driftCountMap = new HashMap<>();
        goOnlineCountList.forEach(item -> {
            String k = item.getDeptId()+"~"+item.getVehicleOwnerId()+"~"+item.getVehicleUseType();
            Integer v = item.getCount();
            goOnlineCountMap.put(k,v);
        });
        driftCountList.forEach(item -> {
            String k = item.getDeptId()+"~"+item.getVehicleOwnerId()+"~"+item.getVehicleUseType();
            Integer v = item.getCount();
            driftCountMap.put(k,v);
        });
        List<CacheStatDriftRate> list = new ArrayList<>();
        //计算漂移率
        Map<String,CacheStatDriftRate> rateMap = new HashMap<>();
        //按照dept_id 和 vehicle_owner_id计算漂移率
        goOnlineCountList.forEach(item -> {
            Long deptId = item.getDeptId();
            Long vehicleOwnerId = item.getVehicleOwnerId();
            //获取漂移率对象
            CacheStatDriftRate rate = rateMap.get(deptId+"~"+vehicleOwnerId);
            if(rate == null){
                rate = new CacheStatDriftRate();
                rate.setDeptId(deptId);
                rate.setVehicleOwnerId(vehicleOwnerId);
                rate.setMonth(month);
                //list.add(rate);
                rateMap.put(deptId+"~"+vehicleOwnerId, rate);
                //获取每种类型的车辆信息
                //班线
                Integer busGoOnlineCount = goOnlineCountMap.get(deptId+"~"+vehicleOwnerId+"~"+ StatisticConstants.VEHICLE_USE_TYPE_REGULAR);
                Integer busDriftCount = driftCountMap.get(deptId+"~"+vehicleOwnerId+"~"+ StatisticConstants.VEHICLE_USE_TYPE_REGULAR);
                //包车
                Integer charterGoOnlineCount = goOnlineCountMap.get(deptId+"~"+vehicleOwnerId+"~"+ StatisticConstants.VEHICLE_USE_TYPE_CHARTERED);
                Integer charterDriftCount = driftCountMap.get(deptId+"~"+vehicleOwnerId+"~"+ StatisticConstants.VEHICLE_USE_TYPE_CHARTERED);
                //危险品
                Integer dangerGoOnlineCount = goOnlineCountMap.get(deptId+"~"+vehicleOwnerId+"~"+ StatisticConstants.VEHICLE_USE_TYPE_DANGER);
                Integer dangerDriftCount = driftCountMap.get(deptId+"~"+vehicleOwnerId+"~"+ StatisticConstants.VEHICLE_USE_TYPE_DANGER);

                //总车辆数、总漂移车辆数
                int totalGoOnlineCount = (busGoOnlineCount==null?0:busGoOnlineCount) + (charterGoOnlineCount==null?0:charterGoOnlineCount) + (dangerGoOnlineCount==null?0:dangerGoOnlineCount);
                int totalDriftCount = (busDriftCount==null?0:busDriftCount) + (charterDriftCount==null?0:charterDriftCount) + (dangerDriftCount==null?0:dangerDriftCount);

                //计算漂移率
                double driftRate = 0;
                double score = 0;
                if(totalDriftCount == 0 || totalGoOnlineCount== 0){
                    driftRate = 0;
                }else{
                    driftRate = MathUtil.divideRoundDouble(totalDriftCount , totalGoOnlineCount , 4);
                }
                //计算得分
                if(totalGoOnlineCount > 0 && driftRate <= 0.05){
                    //按照服务商标准来计算得分
                    score = MathUtil.roundDouble(20 - driftRate * 20 , 2);
                }

                rate.setBusGoonlineCount(busGoOnlineCount);
                rate.setBusDriftCount(busDriftCount);
                rate.setCharterGoonlineCount(charterGoOnlineCount);
                rate.setCharterDriftCount(charterDriftCount);
                rate.setDangerGoonlineCount(dangerGoOnlineCount);
                rate.setDangerDriftCount(dangerDriftCount);
                rate.setTotalGoonlineCount(totalGoOnlineCount);
                rate.setTotalDriftCount(totalDriftCount);
                rate.setDriftRate(driftRate);
                rate.setDriftScore(score);
                rate.setCreateTime(new Date());
            }
        });

        //3.存储漂移率缓存信息
        saveBatch(rateMap.values());

    }

    @Override
    public DriftRateResponse queryDriftRateInDBCache(RateRequest request) throws Exception {
        return null;












    }
}
