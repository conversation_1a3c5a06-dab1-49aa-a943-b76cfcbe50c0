<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatStopPointMapper">

    <insert id="saveOrUpdate">
        replace into stat_stop_point_${month} (id , stat_date , licence_plate, licence_color, stop_start_id,stop_end_id,stop_start_time,stop_end_time,stop_longitude,stop_latitude,stop_duration,stop_location_count ,stop_address, update_time,create_time)
        values
            <foreach collection="list" item="item" index="index">
                <if test="index == 0">
                    (#{item.id} , #{item.statDate} , #{item.licencePlate} , #{item.licenceColor}, #{item.stopStartId} , #{item.stopEndId} , #{item.stopStartTime} , #{item.stopEndTime} , #{item.stopLongitude} , #{item.stopLatitude}, #{item.stopDuration} ,#{item.stopLocationCount}, #{item.stopAddress}, #{item.updateTime} , #{item.createTime})
                </if>
                <if test="index > 0">
                    , (#{item.id} , #{item.statDate} , #{item.licencePlate} , #{item.licenceColor}, #{item.stopStartId} , #{item.stopEndId} , #{item.stopStartTime} , #{item.stopEndTime} , #{item.stopLongitude} , #{item.stopLatitude}, #{item.stopDuration} ,#{item.stopLocationCount}, #{item.stopAddress}, #{item.updateTime} , #{item.createTime})
                </if>
            </foreach>
    </insert>

    <delete id="delete">
        delete from stat_stop_point_${month} where stat_date = #{statDate,jdbcType=VARCHAR}
    </delete>

    <select id="getLatestStopPoint" resultType="com.xh.vdm.statistic.entity.StatStopPoint">
        select * from stat_stop_point_${month}
        where licence_plate = #{licencePlate} and licence_color = #{licenceColor} and stat_date = #{statDate} order by stop_end_time desc limit 1
    </select>

    <select id="getStopPointListByCondition" resultType="com.xh.vdm.statistic.entity.StatStopPoint">
        SELECT
            *
        FROM
            stat_stop_point_${month}
        WHERE
            ( licence_plate = #{licencePlate} AND stat_date = #{date} AND stop_start_time >= #{startSecondTimestamp} AND stop_end_time >= #{endSecondTimestamp} )
        ORDER BY
            stop_start_time ASC
    </select>

    <select id="getStopPointInfo" resultType="com.xh.vdm.statistic.vo.response.StopPointStatResponse">
        select a.licence_plate, a.licence_color, sum(a.stop_count) stop_count, sum(a.stop_duration) stop_duration
        from (
            <foreach collection="dm" item="item" separator=" union ">
                select ssp.licence_plate, ssp.licence_color,ssp.stat_date, count(*) stop_count, sum(ssp.stop_duration) stop_duration
                from stat_stop_point_${item.month} ssp
                left join bdm_vehicle bv on ssp.licence_plate = bv.licence_plate and ssp.licence_color = bv.licence_color
                where stat_date in
                <foreach collection="dm.dateList" item="date" separator="," open="(" close=")">
                    #{date}
                </foreach>
                group by ssp.licence_plate, ssp.licence_color
            </foreach>
        ) a
        group by a.licence_plate, a.licence_color
    </select>

    <select id="getStopPointInfoPage" resultType="com.xh.vdm.statistic.vo.response.StopPointStatResponse">
        select a.licence_plate, a.licence_color, a.access_mode, a.dept_id, a.vehicle_owner_id, a.vehicle_use_type,  sum(a.stop_count) stop_count, sum(a.stop_duration) stop_duration
        from (
        <foreach collection="dm" item="item" separator=" union ">
            select ssp.licence_plate, ssp.access_mode, ssp.licence_color,ssp.dept_id, ssp.vehicle_owner_id, ssp.vehicle_use_type, count(*) stop_count, sum(ssp.stop_duration) stop_duration
            from stat_stop_point_${item.month} ssp
            where stat_date in
            <foreach collection="item.dateList" item="date" separator="," open="(" close=")">
                to_date(#{date},'yyyyMMdd')
            </foreach>
            <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
                and ssp.vehicle_id in
                <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                    #{vehicleId}
                </foreach>
            </if>
            <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
                and ( ssp.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
                <if test="request.userId != null">
                    or ssp.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
                </if>
                )
            </if>

            <if test="request.vehicleUseType != null ">
                and ssp.vehicle_use_type in
                <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.vehicleOwnerId != null">
                and ssp.vehicle_owner_id = #{request.vehicleOwnerId}
            </if>
            <if test="request.accessMode != null">
                and ssp.access_mode = #{request.accessMode}
            </if>
            group by ssp.licence_plate, ssp.access_mode, ssp.licence_color,ssp.dept_id, ssp.vehicle_owner_id, ssp.vehicle_use_type
        </foreach>
        ) a
        group by a.licence_plate, a.access_mode, a.licence_color, a.dept_id, a.vehicle_owner_id, a.vehicle_use_type
    </select>

    <select id="getStopPointDetailPage" resultType="com.xh.vdm.statistic.vo.response.StopPointDetailResponse">
        select a.licence_plate, a.licence_color, a.stat_date, a.access_mode, a.stop_start_time, a.stop_end_time, a.stop_duration, a.stop_address, a.dept_id, a.vehicle_use_type,a.vehicle_owner_id
        from (
        <foreach collection="dm" item="item" separator=" union ">
            select ssp.dept_id, ssp.licence_plate, ssp.access_mode, ssp.licence_color, ssp.vehicle_use_type, ssp.vehicle_owner_id, ssp.stat_date, ssp.stop_start_time, ssp.stop_end_time, ssp.stop_duration, ssp.stop_address
            from stat_stop_point_${item.month} ssp
            where stat_date in
            <foreach collection="item.dateList" item="date" separator="," open="(" close=")">
                to_date(#{date},'yyyyMMdd')
            </foreach>
            <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
                and ssp.vehicle_id in
                <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                    #{vehicleId}
                </foreach>
            </if>
            <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
                and ( ssp.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
                <if test="request.userId != null">
                    or ssp.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
                </if>
                )
            </if>

            <if test="request.vehicleUseType != null ">
                and ssp.vehicle_use_type in
                <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.vehicleOwnerId != null">
                and ssp.vehicle_owner_id = #{request.vehicleOwnerId}
            </if>
            <if test="request.accessMode != null">
                and ssp.access_mode = #{request.accessMode}
            </if>
        </foreach>
        ) a
        order by a.stop_start_time desc
    </select>
</mapper>
