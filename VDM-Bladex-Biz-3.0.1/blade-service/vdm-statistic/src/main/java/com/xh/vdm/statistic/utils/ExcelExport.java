package com.xh.vdm.statistic.utils;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.http.HttpServletResponse;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Author：Robin
 * @Date：2019-09-21 11:21
 * @Description：Excel导出工具类，依赖于ClassUtil工具类
 */
public final class ExcelExport {

    /**
     * 将传入的数据导出excel表并下载
     * @param response 返回的HttpServletResponse
     * @param importlist 要导出的对象的集合
     * @param attributeNames 含有每个对象属性在excel表中对应的标题字符串的数组（请按对象中属性排序调整字符串在数组中的位置）
     */
    public static void export(HttpServletResponse response, String title, List<?> importlist, String[] attributeNames) throws UnsupportedEncodingException {
        //获取数据集
        List<?> datalist = importlist;

        //声明一个工作薄
        HSSFWorkbook workbook = new HSSFWorkbook();
        HSSFCellStyle style = workbook.createCellStyle();
        HSSFFont font = workbook.createFont();
        font.setBold(false);
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        //生成一个表格
        HSSFSheet sheet = workbook.createSheet();
        workbook.setSheetName(0,title);
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 18);

        HSSFFont titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        HSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //获取字段名数组
        String[] tableAttributeName = attributeNames;
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints((short) 30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellStyle(titleStyle);
        titleCell.setCellType(CellType.STRING);
        titleCell.setCellValue(transCellType(title));

        //标题行
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, attributeNames.length-1);
        sheet.addMergedRegion(region);
        //循环字段名数组，创建标题行
        Row row = sheet.createRow(1);
        row.setHeightInPoints((float) 25.5);
        for (int j = 0; j< tableAttributeName.length; j++){
            //创建列
            Cell cell = row.createCell(j);
            //设置单元类型为String
            cell.setCellStyle(style);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(transCellType(tableAttributeName[j]));
        }
        if(importlist!=null&&!importlist.isEmpty()){
            //获取对象属性
            Field[] fields = ClassUtil.getClassAttribute(importlist.get(0));
            //获取对象get方法
            List<Method> methodList = ClassUtil.getMethodGet(importlist.get(0));
            //创建普通行
            for (int i = 0;i<datalist.size();i++){
                //因为第一行已经用于创建标题行，故从第二行开始创建
                row = sheet.createRow(i+2);
                row.setHeightInPoints((float) 25.5);
                //如果是第一行就让其为标题行
                Object targetObj = datalist.get(i);
                for (int j = 0;j<fields.length;j++){
                    //创建列
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(style);
                    cell.setCellType(CellType.STRING);
                    //
                    try {
                        Object value = methodList.get(j).invoke(targetObj, new Object[]{});
                        cell.setCellValue(transCellType(value));
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    } catch (InvocationTargetException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        response.setContentType("application/octet-stream");
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String dateStr = format.format(date);
        //默认Excel名称
        response.setHeader("Content-Disposition", "attachment;fileName="+new String(title.getBytes("UTF-8"),"ISO-8859-1")+"_"+dateStr+".xls");

        try {
            response.flushBuffer();
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static String transCellType(Object value){
        String str = null;
        if (value instanceof Date){
            Date date = (Date) value;
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            str = sdf.format(date);
        }else{
            str = String.valueOf(value);
            if (str == "null"){
                str = "";
            }
        }

        return str;
    }

    /**
     * 将传入的数据导出excel表并下载
     * @param response 返回的HttpServletResponse
     * @param importlist 要导出的对象的集合
     * @param attributeNames 含有每个对象属性在excel表中对应的标题字符串的数组（请按对象中属性排序调整字符串在数组中的位置）
     */
    public static void exportList(HttpServletResponse response, String title, List<?> importlist, String[] attributeNames) throws UnsupportedEncodingException {
        //获取数据集
        List<?> datalist = importlist;

        //声明一个工作薄
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(false);
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        //生成一个表格
        XSSFSheet sheet = workbook.createSheet();
        workbook.setSheetName(0,title);
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 18);

        XSSFFont titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        XSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //获取字段名数组
        String[] tableAttributeName = attributeNames;
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints((short) 30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellStyle(titleStyle);
        titleCell.setCellType(CellType.STRING);
        titleCell.setCellValue(transCellType(title));

        //标题行
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, attributeNames.length-1);
        sheet.addMergedRegion(region);
        //循环字段名数组，创建标题行
        Row row = sheet.createRow(1);
        row.setHeightInPoints((float) 25.5);
        for (int j = 0; j< tableAttributeName.length; j++){
            //创建列
            Cell cell = row.createCell(j);
            //设置单元类型为String
            cell.setCellStyle(style);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(transCellType(tableAttributeName[j]));
        }
        if(importlist!=null&&!importlist.isEmpty()){
            //创建普通行
            for (int i = 0;i<datalist.size();i++){
                //因为第一行已经用于创建标题行，故从第二行开始创建
                row = sheet.createRow(i+2);
                row.setHeightInPoints((float) 25.5);
                //如果是第一行就让其为标题行
                ArrayList targetObj = (ArrayList) datalist.get(i);
                for (int j = 0;j<attributeNames.length;j++){
                    //创建列
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(style);
                    cell.setCellType(CellType.STRING);
                    //

                    Object value = targetObj.get(j);
                    cell.setCellValue(transCellType(value));

                }
            }
        }
        response.setContentType("application/octet-stream");
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String dateStr = format.format(date);
        //默认Excel名称
        response.setHeader("Content-Disposition", "attachment;fileName="+new String(title.getBytes("UTF-8"),"ISO-8859-1")+"_"+dateStr+".xls");

        try {
            response.flushBuffer();
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 将传入的数据导出excel表并下载
     * @param importlist 要导出的对象的集合
     * @param attributeNames 含有每个对象属性在excel表中对应的标题字符串的数组（请按对象中属性排序调整字符串在数组中的位置）
     */
    public static String exportExcelFile(String title, List<?> importlist, String[] attributeNames, String path) throws IOException {
        //获取数据集
        List<?> datalist = importlist;

        //声明一个工作薄
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(false);
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        //生成一个表格
        XSSFSheet sheet = workbook.createSheet();
        workbook.setSheetName(0,title);
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 18);

        XSSFFont titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        XSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //获取字段名数组
        String[] tableAttributeName = attributeNames;
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints((short) 30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellStyle(titleStyle);
        titleCell.setCellType(CellType.STRING);
        titleCell.setCellValue(transCellType(title));

        //标题行
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, attributeNames.length-1);
        sheet.addMergedRegion(region);
        //循环字段名数组，创建标题行
        Row row = sheet.createRow(1);
        row.setHeightInPoints((float) 25.5);
        for (int j = 0; j< tableAttributeName.length; j++){
            //创建列
            Cell cell = row.createCell(j);
            //设置单元类型为String
            cell.setCellStyle(style);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(transCellType(tableAttributeName[j]));
        }
        if(importlist!=null&&!importlist.isEmpty()){
            //获取对象属性
            Field[] fields = ClassUtil.getClassAttribute(importlist.get(0));
            //获取对象get方法
            List<Method> methodList = ClassUtil.getMethodGet(importlist.get(0));
            //创建普通行
            for (int i = 0;i<datalist.size();i++){
                //因为第一行已经用于创建标题行，故从第二行开始创建
                row = sheet.createRow(i+2);
                row.setHeightInPoints((float) 25.5);
                //如果是第一行就让其为标题行
                Object targetObj = datalist.get(i);
                for (int j = 0;j<fields.length;j++){
                    //创建列
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(style);
                    cell.setCellType(CellType.STRING);
                    //
                    try {
                        Object value = methodList.get(j).invoke(targetObj, new Object[]{});
                        cell.setCellValue(transCellType(value));
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    } catch (InvocationTargetException e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        //生成文件
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String dateStr = format.format(date);
        String fileName =title+"_"+dateStr+".xlsx";
        FileOutputStream file = new FileOutputStream(path+fileName);
        workbook.write(file);
        //关闭资源
        try {
            workbook.close();
            file.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        return "/bt/statistics/files/"+fileName;

//        response.setContentType("application/octet-stream");
//        Date date = new Date();
//        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
//        String dateStr = format.format(date);
//        //默认Excel名称
//        response.setHeader("Content-Disposition", "attachment;fileName="+new String(title.getBytes("UTF-8"),"ISO-8859-1")+"_"+dateStr+".xls");
//
//        try {
//            response.flushBuffer();
//            workbook.write(response.getOutputStream());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    public static String exportExcelFileWithoutDate(String title, List<?> importlist, String[] attributeNames, String path) throws IOException {
        //获取数据集
        List<?> datalist = importlist;

        //声明一个工作薄
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(false);
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        //生成一个表格
        XSSFSheet sheet = workbook.createSheet();
        workbook.setSheetName(0,title);
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 18);

        XSSFFont titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        XSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //获取字段名数组
        String[] tableAttributeName = attributeNames;
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints((short) 30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellStyle(titleStyle);
        titleCell.setCellType(CellType.STRING);
        titleCell.setCellValue(transCellType(title));

        //标题行
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, attributeNames.length-1);
        sheet.addMergedRegion(region);
        //循环字段名数组，创建标题行
        Row row = sheet.createRow(1);
        row.setHeightInPoints((float) 25.5);
        for (int j = 0; j< tableAttributeName.length; j++){
            //创建列
            Cell cell = row.createCell(j);
            //设置单元类型为String
            cell.setCellStyle(style);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(transCellType(tableAttributeName[j]));
        }
        if(importlist!=null&&!importlist.isEmpty()){
            //获取对象属性
            Field[] fields = ClassUtil.getClassAttribute(importlist.get(0));
            //获取对象get方法
            List<Method> methodList = ClassUtil.getMethodGet(importlist.get(0));
            //创建普通行
            for (int i = 0;i<datalist.size();i++){
                //因为第一行已经用于创建标题行，故从第二行开始创建
                row = sheet.createRow(i+2);
                row.setHeightInPoints((float) 25.5);
                //如果是第一行就让其为标题行
                Object targetObj = datalist.get(i);
                for (int j = 0;j<fields.length;j++){
                    //创建列
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(style);
                    cell.setCellType(CellType.STRING);
                    //
                    try {
                        Object value = methodList.get(j).invoke(targetObj, new Object[]{});
                        cell.setCellValue(transCellType(value));
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    } catch (InvocationTargetException e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        //生成文件
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        String dateStr = format.format(date);
        //String fileName =title+"_"+dateStr+".xlsx";
        String fileName =title+".xlsx";
        FileOutputStream file = new FileOutputStream(path+fileName);
        workbook.write(file);
        //关闭资源
        try {
            workbook.close();
            file.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        return "/bt/statistics/files/"+fileName;

    }


    /**
     * 将传入的数据导出excel表并下载
     * @param importlist 要导出的对象的集合
     * @param attributeNames 含有每个对象属性在excel表中对应的标题字符串的数组（请按对象中属性排序调整字符串在数组中的位置）
     */
    public static String exportExcelList(String title, List<?> importlist, String[] attributeNames,String path) throws IOException {
        //获取数据集
        List<?> datalist = importlist;

        //声明一个工作薄
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(false);
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        //生成一个表格
        XSSFSheet sheet = workbook.createSheet();
        workbook.setSheetName(0,title);
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 18);

        XSSFFont titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        XSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //获取字段名数组
        String[] tableAttributeName = attributeNames;
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints((short) 30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellStyle(titleStyle);
        titleCell.setCellType(CellType.STRING);
        titleCell.setCellValue(transCellType(title));

        //标题行
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, attributeNames.length-1);
        sheet.addMergedRegion(region);
        //循环字段名数组，创建标题行
        Row row = sheet.createRow(1);
        row.setHeightInPoints((float) 25.5);
        for (int j = 0; j< tableAttributeName.length; j++){
            //创建列
            Cell cell = row.createCell(j);
            //设置单元类型为String
            cell.setCellStyle(style);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(transCellType(tableAttributeName[j]));
        }
        if(importlist!=null&&!importlist.isEmpty()){
            //创建普通行
            for (int i = 0;i<datalist.size();i++){
                //因为第一行已经用于创建标题行，故从第二行开始创建
                row = sheet.createRow(i+2);
                row.setHeightInPoints((float) 25.5);
                //如果是第一行就让其为标题行
                ArrayList targetObj = (ArrayList) datalist.get(i);
                for (int j = 0;j<attributeNames.length;j++){
                    //创建列
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(style);
                    cell.setCellType(CellType.STRING);
                    //

                    Object value = targetObj.get(j);
                    cell.setCellValue(transCellType(value));

                }
            }
        }
        //生成文件
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String dateStr = format.format(date);
        String fileName =title+"_"+dateStr+".xlsx";
        FileOutputStream file = new FileOutputStream(path+fileName);
        workbook.write(file);
        //关闭资源
        try {
            workbook.close();
            file.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        return "/bt/statistics/files/"+fileName;
    }

    /**
     * @description: 宽的企业名称、车队名称 导出
     * @author: zhouxw
     * @date: 2023-03-86 14:09:01
     * @param: [title, importlist, attributeNames, path]
     * @return: java.lang.String
     **/
    public static String exportExcelListForFirstTwoWideColumn(String title, List<?> importlist, String[] attributeNames,String path) throws IOException {
        //获取数据集
        List<?> datalist = importlist;

        //声明一个工作薄
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(false);
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        //生成一个表格
        XSSFSheet sheet = workbook.createSheet();
        workbook.setSheetName(0,title);
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 18);

        //设置列宽
        sheet.setColumnWidth(0, 40*256+184);
        sheet.setColumnWidth(1, 40*256+184);

        XSSFFont titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        XSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //获取字段名数组
        String[] tableAttributeName = attributeNames;
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints((short) 30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellStyle(titleStyle);
        titleCell.setCellType(CellType.STRING);
        titleCell.setCellValue(transCellType(title));

        //标题行
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, attributeNames.length-1);
        sheet.addMergedRegion(region);
        //循环字段名数组，创建标题行
        Row row = sheet.createRow(1);
        row.setHeightInPoints((float) 25.5);
        for (int j = 0; j< tableAttributeName.length; j++){
            //创建列
            Cell cell = row.createCell(j);
            //设置单元类型为String
            cell.setCellStyle(style);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(transCellType(tableAttributeName[j]));
        }
        if(importlist!=null&&!importlist.isEmpty()){
            //创建普通行
            for (int i = 0;i<datalist.size();i++){
                //因为第一行已经用于创建标题行，故从第二行开始创建
                row = sheet.createRow(i+2);
                row.setHeightInPoints((float) 25.5);
                //如果是第一行就让其为标题行
                ArrayList targetObj = (ArrayList) datalist.get(i);
                for (int j = 0;j<attributeNames.length;j++){
                    //创建列
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(style);
                    cell.setCellType(CellType.STRING);
                    //

                    Object value = targetObj.get(j);
                    cell.setCellValue(transCellType(value));

                }
            }
        }
        //生成文件
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
        String dateStr = format.format(date);
        String fileName =title+"_"+dateStr+".xlsx";
        FileOutputStream file = new FileOutputStream(path+fileName);
        workbook.write(file);
        //关闭资源
        try {
            workbook.close();
            file.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        return "/bt/statistics/files/"+fileName;
    }

    public static String exportExcelListWithoutDate(String title, List<?> importlist, String[] attributeNames, String path) throws IOException {
        //获取数据集
        List<?> datalist = importlist;

        //声明一个工作薄
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(false);
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        //生成一个表格
        XSSFSheet sheet = workbook.createSheet();
        workbook.setSheetName(0,title);
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 18);

        XSSFFont titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        XSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //获取字段名数组
        String[] tableAttributeName = attributeNames;
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints((short) 30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellStyle(titleStyle);
        titleCell.setCellType(CellType.STRING);
        titleCell.setCellValue(transCellType(title));

        //标题行
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, attributeNames.length-1);
        sheet.addMergedRegion(region);
        //循环字段名数组，创建标题行
        Row row = sheet.createRow(1);
        row.setHeightInPoints((float) 25.5);
        for (int j = 0; j< tableAttributeName.length; j++){
            //创建列
            Cell cell = row.createCell(j);
            //设置单元类型为String
            cell.setCellStyle(style);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(transCellType(tableAttributeName[j]));
        }
        if(importlist!=null&&!importlist.isEmpty()){
            //创建普通行
            for (int i = 0;i<datalist.size();i++){
                //因为第一行已经用于创建标题行，故从第二行开始创建
                row = sheet.createRow(i+2);
                row.setHeightInPoints((float) 25.5);
                //如果是第一行就让其为标题行
                ArrayList targetObj = (ArrayList) datalist.get(i);
                for (int j = 0;j<attributeNames.length;j++){
                    //创建列
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(style);
                    cell.setCellType(CellType.STRING);
                    //

                    Object value = targetObj.get(j);
                    cell.setCellValue(transCellType(value));

                }
            }
        }
        //生成文件
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        String dateStr = format.format(date);
        String fileName =title+".xlsx";
        FileOutputStream file = new FileOutputStream(path+fileName);
        workbook.write(file);
        //关闭资源
        try {
            workbook.close();
            file.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        return "/bt/statistics/files/"+fileName;
    }

    public static String exportExcelListWithoutDateForFirstTwoWideColumn(String title, List<?> importlist, String[] attributeNames, String path) throws IOException {
        //获取数据集
        List<?> datalist = importlist;

        //声明一个工作薄
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(false);
        font.setFontHeightInPoints((short) 10);
        font.setFontName("Arial");
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        //生成一个表格
        XSSFSheet sheet = workbook.createSheet();
        workbook.setSheetName(0,title);
        //设置表格默认列宽度为15个字节
        sheet.setDefaultColumnWidth((short) 18);

        //设置前两列列宽
        sheet.setColumnWidth(0, 40*256+184);
        sheet.setColumnWidth(1, 40*256+184);

        XSSFFont titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        XSSFCellStyle titleStyle = workbook.createCellStyle();
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        //获取字段名数组
        String[] tableAttributeName = attributeNames;
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints((short) 30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellStyle(titleStyle);
        titleCell.setCellType(CellType.STRING);
        titleCell.setCellValue(transCellType(title));

        //标题行
        CellRangeAddress region = new CellRangeAddress(0, 0, 0, attributeNames.length-1);
        sheet.addMergedRegion(region);
        //循环字段名数组，创建标题行
        Row row = sheet.createRow(1);
        row.setHeightInPoints((float) 25.5);
        for (int j = 0; j< tableAttributeName.length; j++){
            //创建列
            Cell cell = row.createCell(j);
            //设置单元类型为String
            cell.setCellStyle(style);
            cell.setCellType(CellType.STRING);
            cell.setCellValue(transCellType(tableAttributeName[j]));
        }
        if(importlist!=null&&!importlist.isEmpty()){
            //创建普通行
            for (int i = 0;i<datalist.size();i++){
                //因为第一行已经用于创建标题行，故从第二行开始创建
                row = sheet.createRow(i+2);
                row.setHeightInPoints((float) 25.5);
                //如果是第一行就让其为标题行
                ArrayList targetObj = (ArrayList) datalist.get(i);
                for (int j = 0;j<attributeNames.length;j++){
                    //创建列
                    Cell cell = row.createCell(j);
                    cell.setCellStyle(style);
                    cell.setCellType(CellType.STRING);
                    //

                    Object value = targetObj.get(j);
                    cell.setCellValue(transCellType(value));

                }
            }
        }
        //生成文件
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
        String dateStr = format.format(date);
        String fileName =title+".xlsx";
        FileOutputStream file = new FileOutputStream(path+fileName);
        workbook.write(file);
        //关闭资源
        try {
            workbook.close();
            file.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        return "/bt/statistics/files/"+fileName;
    }
}
