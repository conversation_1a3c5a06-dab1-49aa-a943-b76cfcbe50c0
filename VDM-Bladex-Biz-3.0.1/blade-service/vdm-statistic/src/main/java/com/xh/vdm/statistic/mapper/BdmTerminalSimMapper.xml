<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmTerminalSimMapper">

    <select id="getSimExpireInfo" resultType="com.xh.vdm.statistic.vo.response.SimExpireResponse">
        select * from
        (
            select bd.dept_name,bv.vehicle_use_type, bv.access_mode, coalesce(btpp.name,'非营运车辆') vehicle_owner, bv.licence_plate,
        bv.licence_color, bv.vehicle_model, bts.active_date, bts.end_date,
        if(date_part('day',now()) - date_part('day', bts.end_date)&lt;0,'未到期',(date_part('day',now()) - date_part('day', bts.end_date))::text) expire_count,
        (date_part('day',now()) - date_part('day', bts.end_date)) exp_count
            from bdm_terminal_sim bts
            left join bdm_terminal bt on bts.id = bt.terminal_sim_id
            left join bdm_vehicle bv on bt.id = bv.terminal_id
            left join blade_dept bd on bv.dept_id = bd.id
            left join bam_third_party_platform btpp on bv.vehicle_owner_id = btpp.id
            where 1 = 1
            <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
                and bv.id in
                <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                    #{vehicleId}
                </foreach>
            </if>
            <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
                and ( bv.dept_id in (
                <foreach collection="request.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
                <if test="request.userId != null">
                    or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
                </if>
                )
            </if>

            <if test="request.vehicleUseType != null ">
                and bv.vehicle_use_type in
                <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.vehicleOwnerId != null">
                and bv.vehicle_owner_id = #{request.vehicleOwnerId}
            </if>
            <if test="request.accessMode != null">
                and bv.access_mode = #{request.accessMode}
            </if>
        ) a
        where 1 = 1
        <if test="request.startTime != null">
            and a.end_date &gt;= to_timestamp(#{request.startTime})
        </if>
        <if test="request.endTime != null">
            and a.end_date &lt;= to_timestamp(#{request.endTime})
        </if>
    </select>
</mapper>
