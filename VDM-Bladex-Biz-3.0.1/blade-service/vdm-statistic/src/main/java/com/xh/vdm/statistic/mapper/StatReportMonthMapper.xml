<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xh.vdm.statistic.mapper.StatReportMonthMapper">
    <select id="getMonthReportPage" resultType="com.xh.vdm.statistic.vo.response.ReportInfoResponse">
        select a.dept_id, bd.dept_name, a.month date,
            a.proxy_file_url summary_url, b.proxy_file_url alarm_url, c.proxy_file_url running_url
        from stat_report_month as a
        inner join stat_report_month as b on a.dept_id = b.dept_id and a.month = b.month and a.type = 'report' and b.type = 'alarm'
        inner join stat_report_month as c on b.dept_id = c.dept_id and b.month = c.month and b.type = 'alarm' and c.type = 'running'
        inner join blade_dept as bd on a.dept_id = bd.id
        where 1 = 1
        <if test="request.deptIds != null and request.deptIds.size() gt 0">
            and a.dept_id in
            <foreach collection="request.deptIds" item="deptId" separator="," open="(" close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="request.date != null and request.date.length() gt 0">
            and a.month = #{request.date}
        </if>
    </select>
</mapper>
