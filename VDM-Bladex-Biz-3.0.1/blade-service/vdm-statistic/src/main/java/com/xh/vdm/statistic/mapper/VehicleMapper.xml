<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.VehicleMapper">


    <select id="dynamicQuery" resultType="map">
        ${sql}
    </select>


    <select id="getGoOnlineVehicleCountAndType" parameterType="com.xh.vdm.statistic.vo.request.RateRequest" resultType="com.xh.vdm.statistic.entity.VehicleCountAndType">
        select bv.trans vehicleUseType , count(distinct bt.vehicle_id) count
        from bdm_terminalonlinerecord bt
        left join bdm_vehicle bv on bt.vehicle_id = bv.id
        where 1 = 1
            <if test="deptId != null and deptId != ''">
                and bv.dept_id = #{deptId}
            </if>
            <if test="ownerId != null and ownerId != ''">
                and bv.vehicle_owner_id = #{ownerId}
            </if>
        and bt.on_line_time like #{month}
        and bv.net_sign_time > 0
        <!-- and bv.is_check = 1 -->
        and bt.is_del = 0 and bv.is_del = 0
        group by bv.trans
    </select>


    <select id="getGoOnlineCountByDay" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select a.date statDate, count(*) count from
        (
        select distinct substring(to_char(btr.on_line_time,'yyyy-mm-dd hh24:mi:ss'),1, 10) date, btr.vehicle_id
        from bdm_terminalonlinerecord_${yearMonth} btr
        where 1 = 1
            and (
                (btr.on_line_time >= to_timestamp(#{startDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss') and btr.on_line_time &lt;= to_timestamp(#{endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'))
                or
                (btr.off_line_time &lt;= to_timestamp(#{endDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss') and btr.off_line_time >= to_timestamp(#{startDate,jdbcType=VARCHAR},'yyyy-mm-dd hh24:mi:ss'))
            )
        ) a,
        (
        select id from bdm_vehicle bv
        where 1 = 1
        and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
        ) b
        where a.vehicle_id = b.id
        group by date order by date
    </select>

    <select id="getGoOnlineCountByHour" resultType="com.xh.vdm.statistic.entity.DateAndCount">
        select rate.date statDate,  count(*) count from
        (
        select distinct substring(btr.on_line_time,12, 2) date, btr.licence_plate, btr.licence_color
        from bdm_terminalonlinerecord btr, bdm_vehicle bv
        where ((btr.on_line_time >= #{startDate} and btr.on_line_time &lt;= #{endDate}) or (btr.off_line_time >= #{startDate} and btr.off_line_time &lt;= #{endDate}))
        and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or btr.vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
        ) rate
        group by rate.date
    </select>

    <select id="getInNetVehicleCountAndType" parameterType="com.xh.vdm.statistic.vo.request.RateRequest" resultType="com.xh.vdm.statistic.entity.VehicleCountAndType">
        select bv.trans vehicle_use_type , count(*) count
        from bdm_vehicle bv
        where 1 = 1
            <if test="deptId != null and deptId != ''">
                and bv.dept_id like #{deptId}
            </if>

        and bv.vehicle_owner_id = #{ownerId}
        and to_timestamp(bv.net_sign_time) &lt; #{month} and bv.net_sign_time > 0
        <!--  and bv.is_check = 1 -->
        and bv.is_del = 0
        group by bv.trans
    </select>

    <select id="getUnGoOnlineVehicle" parameterType="com.xh.vdm.statistic.entity.UnGoOnlineQuery" resultType="com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse">
        select sd.name dept_name, bv.licence_plate , bv.licence_color licence_color ,bt.sim_id ,bt.terminal_id , #{query.month} month, bt.terminal_model  from bdm_vehicle bv
        left join bdm_terminal bt on bt.id = bv.terminal_id
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!-- and bv.is_check = 1 -->
        <if test="query.deptId != null and query.deptId != ''">
            and sd.id like #{query.deptId}
        </if>
        and bv.vehicle_owner_id = #{query.ownerId}
        <if test="query.licencePlate != null and query.licencePlate != ''">
            and bv.licence_plate = #{query.licencePlate,jdbcType=VARCHAR}
        </if>
        <if test="query.plateColor != null and query.plateColor != ''">
            and bv.licence_color = #{query.plateColor,jdbcType=INTEGER}
        </if>
        and not exists (select * from bdm_terminalonlinerecord btr where btr.vehicle_id = bv.id and btr.on_line_time like #{query.monthLike,jdbcType=VARCHAR} and bv.is_del = 0 and btr.is_del = 0)
        and to_timestamp(bv.net_sign_time) &lt;= #{query.firstDayNextMonth,jdbcType=VARCHAR} and bv.net_sign_time > 0
        and bt.is_del = 0 and bv.is_del = 0 and sd.is_del = 0 and bt.un_bind_flag = 0
        order by sd.id, bv.licence_plate, bv.licence_color
    </select>


    <select id="getUnGoOnlineVehicleAll" parameterType="com.xh.vdm.statistic.entity.UnGoOnlineQuery" resultType="com.xh.vdm.statistic.vo.response.UnGoOnlineVehicleResponse">
        select sd.name dept_name, bv.licence_plate , bv.licence_color licence_color ,bt.sim_id ,bt.terminal_id , #{query.month} month, bt.terminal_model  from bdm_vehicle bv
        left join bdm_terminal bt on bt.id = bv.terminal_id
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
        <!-- and bv.is_check = 1 -->
        <if test="query.deptId != null and query.deptId != ''">
            and sd.id like #{query.deptId}
        </if>
        and bv.vehicle_owner_id = #{query.ownerId}
        <if test="query.licencePlate != null and query.licencePlate != ''">
            and bv.licence_plate = #{query.licencePlate,jdbcType=VARCHAR}
        </if>
        <if test="query.plateColor != null and query.plateColor != ''">
            and bv.licence_color = #{query.plateColor,jdbcType=INTEGER}
        </if>
        and not exists (select * from bdm_terminalonlinerecord btr where btr.vehicle_id = bv.id and btr.on_line_time like #{query.monthLike,jdbcType=VARCHAR} and bv.is_del = 0 and btr.is_del = 0)
        and to_timestamp(bv.net_sign_time) &lt;= #{query.firstDayNextMonth,jdbcType=VARCHAR} and bv.net_sign_time > 0
        and bt.is_del = 0 and bv.is_del = 0 and sd.is_del = 0 and bt.un_bind_flag = 0
        order by sd.id , bv.licence_plate, bv.licence_color
    </select>

    <select id="getTerminalAndDept" resultType="com.xh.vdm.statistic.entity.TerminalAndDept">
        select sd.name dept_name, bv.licence_plate , bv.licence_color ,bt.sim_id ,bt.terminal_id ,  bt.terminal_model
        from bdm_vehicle bv
        left join bdm_terminal bt on bt.id = bv.terminal_id
        left join sys_dept sd on bv.dept_id = sd.id
        where 1 = 1
          <!-- and bv.is_check = 1 -->
          and bv.licence_plate = #{licencePlate,jdbcType=VARCHAR}
          and bv.licence_color = #{plateColor}
          and bt.is_del = 0
          and bv.is_del = 0
          and sd.is_del = 0
          and bt.un_bind_flag = 0
    </select>

    <select id="getInNetCountByOwnerId" resultType="int">
        select count(*) count
        from bdm_vehicle bv
        where 1 = 1
          <!-- and bv.is_check = 1 -->
        and bv.vehicle_owner_id = #{ownerId}
        and to_timestamp(bv.net_sign_time) &lt; #{nextMonthFirstDay,jdbcType=VARCHAR} and bv.net_sign_time > 0
        and bv.is_del = 0
    </select>


    <select id="getGoOnlineCountByOwnerId" resultType="int">
        select count(distinct bt.vehicle_id) count
        from bdm_terminalonlinerecord bt
        left join bdm_vehicle bv on bt.vehicle_id = bv.id
        where 1 = 1
          <!-- and bv.is_check = 1 -->
          and bv.vehicle_owner_id = #{ownerId}
          and bt.on_line_time like #{month,jdbcType=VARCHAR}
          and bv.net_sign_time > 0
          and bt.is_del = 0 and bv.is_del = 0
    </select>

    <select id="getInNetCountByDeptId" resultType="com.xh.vdm.statistic.entity.VehicleCountWithDept">
        select bv.dept_id , count(*) count
        from bdm_vehicle bv
        where 1 = 1
        <!-- and bv.is_check = 1 -->
        and bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        <if test="ownerId != null and ownerId != ''">
            and vehicle_owner_id = #{ownerId}
        </if>
        and to_timestamp(bv.net_sign_time) &lt; #{nextMonthFirstDay} and bv.net_sign_time > 0
        and bv.is_del = 0
        group by bv.dept_id
    </select>


    <select id="getInNetCountByDate" resultType="long">
        select count(*) count
        from bdm_vehicle bv
        where 1 = 1
        and bv.net_sign_time &lt; #{date,jdbcType=TIMESTAMP} and bv.net_sign_time > 0
        and bv.is_del = 0
        and ( bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId,jdbcType=BIGINT}
        </foreach>
        ) or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId,jdbcType=BIGINT}) )
    </select>


    <select id="getDeptAndInNetCountByPage" resultType="com.xh.vdm.statistic.entity.DeptAndCount">
        select sd.id deptId , sd.name deptName ,  coalesce(bvd.count,0) count from sys_dept sd
        left join (
        select bv.dept_id , count(*) count
        from bdm_vehicle bv
        where 1 = 1
        <!-- and bv.is_check = 1 -->

        and vehicle_owner_id = #{ownerId,jdbcType=BIGINT}

        and to_timestamp(bv.net_sign_time) &lt; #{nextMonthFirstDay,jdbcType=VARCHAR}
        and bv.net_sign_time > 0
        and bv.is_del = 0
        group by bv.dept_id
        ) bvd on sd.id = bvd.dept_id
        where sd.is_del = 0 and sd.enabled = 1
        order by bvd.count desc, sd.id
    </select>

    <select id="getGoOnlineCountByDeptId" resultType="com.xh.vdm.statistic.entity.VehicleCountWithDept">
        select bv.dept_id , count(distinct bt.vehicle_id) count
        from bdm_terminalonlinerecord bt
        left join bdm_vehicle bv on bt.vehicle_id = bv.id
        where 1 = 1
        <!-- and bv.is_check = 1 -->
        <if test="deptIds != null and deptIds != ''">
          and bv.dept_id in (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId}
            </foreach>
            )
        </if>
        <if test="ownerId != null and ownerId != ''">
            and bv.vehicle_owner_id = #{ownerId}
        </if>
          and bt.on_line_time like #{month,jdbcType=VARCHAR}
          and bv.net_sign_time > 0
          and bt.is_del = 0 and bv.is_del = 0
        group by bv.dept_id
    </select>

    <select id="getVehicleRateDetail" resultType="com.xh.vdm.statistic.vo.response.VehicleRateDetailResponse">

        select dept_name , licence_plate ,licence_color  , sim_id ,terminal_id , drift_count , concat(round(complete_rate * 100,2),'%') complete_rate , concat(round(quality_rate*100,2),'%') quality_rate,  date , terminal_model
        from (

        select sd.id dept_id , bv.vehicle_owner_id ,  sd.name dept_name , v.licence_plate ,bv.licence_color licence_color , bt.sim_id ,bt.terminal_id , sd.drift_count , round(sc.complete_rate,4) complete_rate , coalesce(round(lq.quality_rate,4),1) quality_rate ,  concat(#{param.month} , '-' , v.date) date, bt.terminal_model
        from (

                 select licence_plate , licence_color, '01' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'02' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'03' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'04' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'05' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate ,licence_color, '06' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'07' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate ,licence_color, '08' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate ,licence_color, '09' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'10' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate ,licence_color, '11' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'12' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'13' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'14' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'15' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'16' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'17' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'18' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'19' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'20' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'21' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'22' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate ,licence_color, '23' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'24' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'25' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'26' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'27' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate ,licence_color, '28' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'29' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'30' date from bdm_vehicle <!-- where is_check = 1-->
                 UNION
                 select licence_plate , licence_color,'31' date from bdm_vehicle <!-- where is_check = 1-->
             ) v

        left join (
            select licence_plate ,licence_color, split_part(d01 , '#' , 1)  drift_count , '01' date from stat_drift_${param.monthStrict} where d01 is not null
            UNION
            select  licence_plate , licence_color,split_part(d02 , '#' , 1)  drift_count , '02' date  from stat_drift_${param.monthStrict} where d02 is not null
            UNION
            select  licence_plate , licence_color,split_part(d03 , '#' , 1)  drift_count , '03' date  from stat_drift_${param.monthStrict} where d03 is not null
            UNION
            select  licence_plate , licence_color,split_part(d04 , '#' , 1)  drift_count , '04' date  from stat_drift_${param.monthStrict} where d04 is not null
            UNION
            select  licence_plate , licence_color,split_part(d05 , '#' , 1)  drift_count , '05' date  from stat_drift_${param.monthStrict} where d05 is not null
            UNION
            select  licence_plate , licence_color,split_part(d06 , '#' , 1)  drift_count , '06' date  from stat_drift_${param.monthStrict} where d06 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d07 , '#' , 1)  drift_count , '07' date  from stat_drift_${param.monthStrict} where d07 is not null
            UNION
            select  licence_plate , licence_color,split_part(d08 , '#' , 1)  drift_count , '08' date  from stat_drift_${param.monthStrict} where d08 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d09 , '#' , 1)  drift_count , '09' date  from stat_drift_${param.monthStrict} where d09 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d10 , '#' , 1)  drift_count , '10' date  from stat_drift_${param.monthStrict} where d10 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d11 , '#' , 1)  drift_count , '11' date  from stat_drift_${param.monthStrict} where d11 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d12 , '#' , 1)  drift_count , '12' date  from stat_drift_${param.monthStrict} where d12 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d13 , '#' , 1)  drift_count , '13' date  from stat_drift_${param.monthStrict} where d13 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d14 , '#' , 1)  drift_count , '14' date  from stat_drift_${param.monthStrict} where d14 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d15 , '#' , 1)  drift_count , '15' date  from stat_drift_${param.monthStrict} where d15 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d16 , '#' , 1)  drift_count , '16' date  from stat_drift_${param.monthStrict} where d16 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d17 , '#' , 1)  drift_count , '17' date  from stat_drift_${param.monthStrict} where d17 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d18 , '#' , 1)  drift_count , '18' date  from stat_drift_${param.monthStrict} where d18 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d19 , '#' , 1)  drift_count , '19' date  from stat_drift_${param.monthStrict} where d19 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d20 , '#' , 1)  drift_count , '20' date  from stat_drift_${param.monthStrict} where d20 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d21 , '#' , 1)  drift_count , '21' date  from stat_drift_${param.monthStrict} where d21 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d22 , '#' , 1)  drift_count , '22' date  from stat_drift_${param.monthStrict} where d22 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d23 , '#' , 1)  drift_count , '23'  date from stat_drift_${param.monthStrict} where d23 is not null
            UNION
            select  licence_plate , licence_color,split_part(d24 , '#' , 1)  drift_count , '24'  date from stat_drift_${param.monthStrict} where d24 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d25 , '#' , 1)  drift_count , '25'  date from stat_drift_${param.monthStrict} where d25 is not null
            UNION
            select licence_plate , licence_color,split_part(d26 , '#' , 1)  drift_count , '26'  date from stat_drift_${param.monthStrict} where d26 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d27 , '#' , 1)  drift_count , '27'  date from stat_drift_${param.monthStrict} where d27 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d28 , '#' , 1)  drift_count , '28'  date from stat_drift_${param.monthStrict} where d28 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d29 , '#' , 1)  drift_count , '29'  date from stat_drift_${param.monthStrict} where d29 is not null
            UNION
            select  licence_plate , licence_color,split_part(d30 , '#' , 1)  drift_count , '30'  date from stat_drift_${param.monthStrict} where d30 is not null
            UNION
            select  licence_plate , licence_color,split_part(d31 , '#' , 1)  drift_count , '31'  date from stat_drift_${param.monthStrict} where d31 is not null

        ) sd on v.licence_plate = sd.licence_plate and v.licence_color = sd.licence_color::text and v.date = sd.date
        left join (

            select licence_plate , licence_color,split_part(d01 , '#' , 1)  complete_rate , '01' date from stat_complete_${param.monthStrict} where d01 is not null
            UNION
            select  licence_plate , licence_color,split_part(d02 , '#' , 1)  complete_rate , '02' date  from stat_complete_${param.monthStrict} where d02 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d03 , '#' , 1)  complete_rate , '03' date  from stat_complete_${param.monthStrict} where d03 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d04 , '#' , 1)  complete_rate , '04' date  from stat_complete_${param.monthStrict} where d04 is not null
            UNION
            select  licence_plate , licence_color,split_part(d05 , '#' , 1)  complete_rate , '05' date  from stat_complete_${param.monthStrict} where d05 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d06 , '#' , 1)  complete_rate , '06' date  from stat_complete_${param.monthStrict} where d06 is not null
            UNION
            select  licence_plate , licence_color,split_part(d07 , '#' , 1)  complete_rate , '07' date  from stat_complete_${param.monthStrict} where d07 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d08 , '#' , 1)  complete_rate , '08' date  from stat_complete_${param.monthStrict} where d08 is not null
            UNION
            select  licence_plate , licence_color,split_part(d09 , '#' , 1)  complete_rate , '09' date  from stat_complete_${param.monthStrict} where d09 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d10 , '#' , 1)  complete_rate , '10' date  from stat_complete_${param.monthStrict} where d10 is not null
            UNION
            select  licence_plate , licence_color,split_part(d11 , '#' , 1)  complete_rate , '11' date  from stat_complete_${param.monthStrict} where d11 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d12 , '#' , 1)  complete_rate , '12' date  from stat_complete_${param.monthStrict} where d12 is not null
            UNION
            select  licence_plate , licence_color,split_part(d13 , '#' , 1)  complete_rate , '13' date  from stat_complete_${param.monthStrict} where d13 is not null
            UNION
            select  licence_plate , licence_color,split_part(d14 , '#' , 1)  complete_rate , '14' date  from stat_complete_${param.monthStrict} where d14 is not null
            UNION
            select  licence_plate , licence_color,split_part(d15 , '#' , 1)  complete_rate , '15' date  from stat_complete_${param.monthStrict} where d15 is not null
            UNION
            select  licence_plate , licence_color,split_part(d16 , '#' , 1)  complete_rate , '16' date  from stat_complete_${param.monthStrict} where d16 is not null
            UNION
            select  licence_plate , licence_color,split_part(d17 , '#' , 1)  complete_rate , '17' date  from stat_complete_${param.monthStrict} where d17 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d18 , '#' , 1)  complete_rate , '18' date  from stat_complete_${param.monthStrict} where d18 is not null
            UNION
            select  licence_plate , licence_color,split_part(d19 , '#' , 1)  complete_rate , '19' date  from stat_complete_${param.monthStrict} where d19 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d20 , '#' , 1)  complete_rate , '20' date  from stat_complete_${param.monthStrict} where d20 is not null
            UNION
            select  licence_plate , licence_color,split_part(d21 , '#' , 1)  complete_rate , '21' date  from stat_complete_${param.monthStrict} where d21 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d22 , '#' , 1)  complete_rate , '22' date  from stat_complete_${param.monthStrict} where d22 is not null
            UNION
            select  licence_plate , licence_color,split_part(d23 , '#' , 1)  complete_rate , '23'  date from stat_complete_${param.monthStrict} where d23 is not null
            UNION
            select  licence_plate , licence_color,split_part(d24 , '#' , 1)  complete_rate , '24'  date from stat_complete_${param.monthStrict} where d24 is not null
            UNION
            select  licence_plate , licence_color,split_part(d25 , '#' , 1)  complete_rate , '25'  date from stat_complete_${param.monthStrict} where d25 is not null
            UNION
            select licence_plate , licence_color,split_part(d26 , '#' , 1)  complete_rate , '26'  date from stat_complete_${param.monthStrict} where d26 is not null
            UNION
            select  licence_plate , licence_color,split_part(d27 , '#' , 1)  complete_rate , '27'  date from stat_complete_${param.monthStrict} where d27 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d28 , '#' , 1)  complete_rate , '28'  date from stat_complete_${param.monthStrict} where d28 is not null
            UNION
            select  licence_plate ,licence_color, split_part(d29 , '#' , 1)  complete_rate , '29'  date from stat_complete_${param.monthStrict} where d29 is not null
            UNION
            select  licence_plate , licence_color,split_part(d30 , '#' , 1)  complete_rate , '30'  date from stat_complete_${param.monthStrict} where d30 is not null
            UNION
            select  licence_plate , licence_color,split_part(d31 , '#' , 1)  complete_rate , '31'  date from stat_complete_${param.monthStrict} where d31 is not null

        ) sc on v.licence_plate = sc.licence_plate and v.licence_color = sc.licence_color::text and sc.date = v.date
        left join (

            select licence_plate , licence_color, round((total_count - total_error_count)/total_count , 4) quality_rate , split_part(stat_date , '-' , -1) date
            from location_quality_${param.monthStrict}
            where total_error_count > 0

        ) lq on v.licence_plate = lq.licence_plate and v.licence_color = lq.licence_color::text and v.date = lq.date

         left join bdm_vehicle bv on bv.licence_plate = v.licence_plate and bv.licence_color = v.licence_color

         left join sys_dept sd on bv.dept_id = sd.id

         left join bdm_terminal bt on bv.terminal_id = bt.id

        where 1 = 1
            and (sc.complete_rate is not null and sc.complete_rate &lt; 1) or (sd.drift_count is not null and sd.drift_count > 0) or (lq.quality_rate is not null and lq.quality_rate &lt; 1 )  <!-- and bv.is_check = 1 -->

        ) rate
        where 1 = 1
        <if test="param.deptId != null and param.deptId != ''">
            and dept_id = #{param.deptId,jdbcType=BIGINT}
        </if>
        <if test="param.ownerId != null and param.ownerId != ''">
            and vehicle_owner_id = #{param.ownerId,jdbcType=BIGINT}
        </if>
        <if test="param.licencePlate != null and param.licencePlate != ''">
            and licence_plate = #{param.licencePlate,jdbcType=VARCHAR}
        </if>
        <if test="param.PlateColor != null and param.PlateColor != ''">
            and licence_color = #{param.plateColor,jdbcType=INTEGER}
        </if>
        order by date desc , licence_plate asc , licence_color asc
    </select>


    <select id="getDeptVehCountByDay" resultType="com.xh.vdm.statistic.entity.DateAndDeptAndCount">
        select bv.dept_id , SUBSTR(bt.on_line_time,1,10) stat_date,  count(distinct bt.vehicle_id) count
        from bdm_terminalonlinerecord bt
        left join bdm_vehicle bv on bt.licence_plate = bv.licence_plate
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
        and bv.net_sign_time > 0
        and bt.is_del = 0 and bv.is_del = 0
        <if test="vehicleUseType == null or vehicleUseType == ''">
            and bv.vehicle_use_type in (10,11,12,30,31,32)
        </if>
        <if test="startTime != null and startTime != ''">
            and extract(epoch from bt.on_line_time) >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and extract(epoch from bt.on_line_time) &lt;= #{endTime}
        </if>
        <if test="deptId != null and deptId != ''">
            and bv.dept_id = #{deptId}
        </if>
        <if test="vehicleUseType != null and vehicleUseType != ''">
            and bv.vehicle_use_type in (#{vehicleUseType,jdbcType=VARCHAR})
        </if>
        group by bv.dept_id , SUBSTR(bt.on_line_time,1,10);
    </select>


    <select id="getDeptPassengerVehCountByDay" resultType="com.xh.vdm.statistic.entity.DateAndDeptAndCount">
        select bv.dept_id , SUBSTR(bt.on_line_time,1,10) stat_date,  count(distinct bt.vehicle_id) count
        from bdm_terminalonlinerecord bt
        left join bdm_vehicle bv on bt.licence_plate = bv.licence_plate
        where 1 = 1
        <!--and bv.vehicle_owner_id = '1'-->
        and bv.net_sign_time > 0
        and bv.vehicle_use_type in (10,11,12)
        and bt.is_del = 0 and bv.is_del = 0
        <if test="startTime != null and startTime != ''">
            and extract(epoch from bt.on_line_time) >= #{startTime}
        </if>
        <if test="endTime != null and endTime != ''">
            and extract(epoch from bt.on_line_time) &lt;= #{endTime}
        </if>
        <if test="deptId != null and deptId != ''">
            and bv.dept_id = #{deptId}
        </if>
        group by bv.dept_id , SUBSTR(bt.on_line_time,1,10);
    </select>

    <select id="getGoOnlineCountByDeptIdDeptOrArea" resultType="com.xh.vdm.statistic.entity.VehicleCountWithDept">
        select
        <if test="deptId != null and deptId != ''">
            bv.dept_id ,
        </if>
        count(distinct bt.vehicle_id) count
        from bdm_terminalonlinerecord bt
        left join bdm_vehicle bv on bt.vehicle_id = bv.id
        where 1 = 1
        and bv.vehicle_use_type in (10,11,12,30,31,32)
        <if test="deptId != null and deptId != ''">
            and bv.dept_id = #{deptId}
        </if>
        <if test="ownerId != null and ownerId != ''">
            and bv.vehicle_owner_id = #{ownerId}
        </if>
        and bt.on_line_time like #{month,jdbcType=VARCHAR}
        and bv.net_sign_time > 0
        and bt.is_del = 0 and bv.is_del = 0
        <if test="deptId != null and deptId != ''">
            group by bv.dept_id
        </if>
    </select>

    <select id="getTotalCountByDeptId" resultType="com.xh.vdm.statistic.entity.VehicleCountWithDept">
        select bv.dept_id , sd.name dept_name , count(*) count
        from bdm_vehicle bv
        left join sys_dept sd on sd.id = bv.dept_id
        where 1 = 1
        and bv.vehicle_use_type in (10,11,12,30,31,32)
        <if test="deptIds != null and deptIds != ''">
            and bv.dept_id in (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId}
            </foreach>
            )
        </if>
        <if test="ownerId != null and ownerId != ''">
            and vehicle_owner_id = #{ownerId}
        </if>
        and bv.is_del = 0
        group by bv.dept_id
    </select>

    <select id="getTotalCountByDeptIdDeptOrArea" resultType="com.xh.vdm.statistic.entity.VehicleCountWithDept">
        select
        <if test="deptId != null and deptId != ''">
            bv.dept_id , sd.name dept_name ,
        </if>
        count(*) count
        from bdm_vehicle bv
        left join sys_dept sd on sd.id = bv.dept_id
        where 1 = 1
        and bv.vehicle_use_type in (10,11,12,30,31,32)
        <if test="deptId != null and deptId != ''">
            and bv.dept_id = #{deptId}
        </if>
        <if test="ownerId != null and ownerId != ''">
            and vehicle_owner_id = #{ownerId}
        </if>
        and bv.is_del = 0
        <if test="deptId != null and deptId != ''">
            group by bv.dept_id
        </if>
    </select>

    <select id="getInNetCountByDeptIdDeptOrArea" resultType="com.xh.vdm.statistic.entity.VehicleCountWithDept">
        select
        <if test="deptId != null and deptId != ''">
            bv.dept_id ,
        </if>
        count(*) count
        from bdm_vehicle bv
        left join bdm_terminal bt on bv.terminal_id = bt.id
        where 1 = 1
        and bv.vehicle_use_type in (10,11,12,30,31,32)
        <if test="deptId != null and deptId != ''">
            and bv.dept_id = #{deptId}
        </if>
        <if test="ownerId != null and ownerId != ''">
            and vehicle_owner_id = #{ownerId}
        </if>
        and to_timestamp(bv.net_sign_time) &lt; #{nextMonthFirstDay} and bv.net_sign_time > 0
        and bv.is_del = 0
        and bt.is_del = 0
        and bt.un_bind_flag = 0
        <if test="deptId != null and deptId != ''">
            group by bv.dept_id
        </if>
    </select>


    <select id="getDeptMileage" resultType="double">
        select coalesce(sum(total_mileage),0)
        from stat_veh_running_state_day_${month}
        where 1 = 1
        and date = #{statDate}
        and (
            dept_id in
            <foreach collection="deptIds" item="deptId" index="i" separator="," open="(" close=")">
                #{deptId, jdbcType=BIGINT}
            </foreach>
            <if test="userId != null">
                or (vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId, jdbcType=BIGINT}))
            </if>
        )
    </select>

    <select id="getMaxSpeedListByVehicleId" resultType="com.xh.vdm.statistic.entity.VehicleAndData">
        select licence_plate, licence_color, vehicle_id, max_speed "data"
        from stat_veh_running_state_day_${month}
        where 1 = 1
        and date = #{statDate}
        and vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
        )
    </select>

    <select id="getDeptMileageMonth" resultType="double">
        select coalesce(sum(total_mileage),0)
        from stat_veh_running_state_month
        where 1 = 1
        and month = #{month}
        and (
            dept_id in
            <foreach collection="deptIds" item="deptId" index="i" separator="," open="(" close=")">
                #{deptId, jdbcType=BIGINT}
            </foreach>
            <if test="userId != null">
                or (vehicle_id = any (select vehicle_id from blade_users_vehicles where user_id = #{userId, jdbcType=BIGINT}))
            </if>
        )
    </select>

    <select id="getMaxSpeedMonthByVehicleId" resultType="com.xh.vdm.statistic.entity.VehicleAndData">
        select licence_plate, licence_color, vehicle_id, max_speed "data"
        from stat_veh_running_state_month
        where 1 = 1
        and month = #{month}
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            and vehicle_id in (
            <foreach collection="vehicleIds" item="vehicleId" separator=",">
                #{vehicleId}
            </foreach>
            )
        </if>
    </select>

    <select id="getDeptDuration" resultType="long">
        select (coalesce(sum((extract(epoch from coalesce(off_line_time,now())) - extract(epoch from if(on_line_time &lt; #{statDate},#{statDate},on_line_time)) )),0)) total_duration
        from bdm_terminalonlinerecord_${month} bt, bdm_vehicle bv
        where bt.vehicle_id = bv.id
        and (bv.dept_id in (
            <foreach collection="deptIds" item="deptId" separator=",">
                #{deptId}
            </foreach>
            )
            <if test="vehicleIds != null and vehicleIds.size() > 0">
                or bt.vehicle_id in (
                    <foreach collection="deptIds" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                )
            </if>
        )
        and bt.is_del = 0
        and ((on_line_time >= #{statDate} and on_line_time &lt;= concat(#{statDate}, ' 23:59:59'))
        or (off_line_time >= #{statDate} and (off_line_time &lt;= concat(#{statDate} ,' 23:59:59') or off_line_time is null)))
        and on_line_time is not null
    </select>

    <select id="getDeptDurationMonth" resultType="long">
        select coalesce(sum((extract(epoch from coalesce(off_line_time,now())) - extract(epoch from on_line_time) )),0) total_duration
        from bdm_terminalonlinerecord_${month} bt, bdm_vehicle bv
        where bt.vehicle_id = bv.id
        and (bv.dept_id in (
        <foreach collection="deptIds" item="deptId" separator=",">
            #{deptId}
        </foreach>
        )
        <if test="vehicleIds != null and vehicleIds.size() > 0">
            or bt.vehicle_id in (
                <foreach collection="vehicleIds" item="vehicleId" separator=",">
                    #{vehicleId}
                </foreach>
            )
        </if>
        )
        and bt.is_del = 0
        and ((on_line_time >= to_timestamp(concat(#{firstDate}, ' 00:00:00'),'yyyy-mm-dd hh24:mi:ss') and on_line_time &lt;= to_timestamp(concat(#{lastDate}, ' 23:59:59'),'yyyy-mm-dd hh24:mi:ss'))
        or (off_line_time >= to_timestamp(concat(#{firstDate}, ' 00:00:00'),'yyyy-mm-dd hh24:mi:ss') and (off_line_time &lt;= to_timestamp(concat(#{lastDate}, ' 23:59:59'),'yyyy-mm-dd hh24:mi:ss') or off_line_time is null)))
        and on_line_time is not null
    </select>

</mapper>
