<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.LocationQualityMapper">

    <insert id="saveBatchMonth">
        insert into location_quality_${month} (licence_plate,licence_color,vehicle_id, dept_id, total_count,total_error_count,time_err_count,receive_time_err_count,longitude_err_count,latitude_err_count,speed_err_count,altitude_err_count,stat_date,create_time,error_location_list) values
        <foreach collection="list" item="item" open="" close="" separator=",">
            (#{item.licencePlate} ,#{item.plateColor},#{item.vehicleId}, #{item.deptId}, #{item.totalCount},#{item.totalErrorCount},#{item.timeErrCount},#{item.receiveTimeErrCount},#{item.longitudeErrCount},#{item.latitudeErrCount},#{item.speedErrCount},#{item.altitudeErrCount},#{item.statDate} , #{item.createTime} , #{item.errorLocationList}  )
        </foreach>
    </insert>


    <select id="checkExist" parameterType="string" resultType="com.xh.vdm.statistic.entity.LocationQuality">
        select id from location_quality_${_parameter} limit 1
    </select>

    <select id="createTable" parameterType="string">
        create table location_quality_${_parameter} (like location_quality INCLUDING INDEXES INCLUDING DEFAULTS)
    </select>

    <delete id="deleteDataByDay">
        delete from location_quality_${month} where stat_date = #{day,jdbcType=VARCHAR}
    </delete>

    <select id="getLocationQualityRate" parameterType="com.xh.vdm.statistic.vo.request.RateRequest" resultType="com.xh.vdm.statistic.vo.response.LocationQualityResponse">
        select sum(coalesce(lq.total_count,0)) totalCount , sum(coalesce(lq.total_error_count,0)) totalErrorCount
        from location_quality_${month} lq , bdm_vehicle bv ,
             sys_dept sd , bam_third_party_platform btp
        where lq.licence_plate = bv.licence_plate and lq.licence_color::text = bv.licence_color and bv.dept_id = sd.id and bv.vehicle_owner_id = btp.id
            <if test="deptId != null and deptId != ''">
                and sd.id like #{deptId}
            </if>
          and btp.id = #{ownerId}
        and bv.is_check = 1
          and lq.is_del = 0 and sd.is_del = 0 and bv.is_del = 0
    </select>

    <select id="getTotalErrorCount" parameterType="com.xh.vdm.statistic.entity.DetailDayParam" resultType="int">
        select coalesce(sum(total_error_count),0) from location_quality_${param.monthStrict} lq
        left join bdm_vehicle bv on lq.licence_plate = bv.licence_plate and lq.licence_color::text = bv.licence_color
        where 1 = 1
        and bv.vehicle_owner_id = #{param.ownerId,jdbcType=BIGINT}
        <if test="param.deptId != null and param.deptId != ''">
            and bv.dept_id = #{param.deptId,jdbcType=BIGINT}
        </if>

        and bv.is_check = 1
        and lq.total_error_count > 0
        <if test="param.licencePlate != null and param.licencePlate != ''">
            and bv.licence_plate = #{param.licencePlate,jdbcType=VARCHAR}
        </if>
        <if test="param.plateColor != null and param.plateColor != ''">
            and bv.licence_color = #{param.plateColor,jdbcType=INTEGER}
        </if>
        <if test="param.day != null and param.day != ''">
            and lq.stat_date = #{param.day,jdbcType=VARCHAR}
        </if>
        order by lq.licence_plate, lq.licence_color
    </select>

    <select id="getStatDateWithLocationError" parameterType="com.xh.vdm.statistic.entity.DetailParam" resultType="com.xh.vdm.statistic.entity.DayAndLicencePlate">
        select distinct stat_date day, lq.licence_plate from location_quality_${param.monthStrict} lq
        left join bdm_vehicle bv on lq.licence_plate = bv.licence_plate and lq.licence_color::text = bv.licence_color
        where 1 = 1
        and bv.vehicle_owner_id = #{param.ownerId,jdbcType=BIGINT}
        <if test="param.deptId != null and param.deptId != ''">
            and bv.dept_id = #{param.deptId,jdbcType=BIGINT}
        </if>

        and bv.is_check = 1
        and lq.total_error_count > 0
        <if test="param.licencePlate != null and param.licencePlate != ''">
            and bv.licence_plate = #{param.licencePlate,jdbcType=VARCHAR}
        </if>
        <if test="param.plateColor != null and param.plateColor != ''">
            and bv.licence_color = #{param.plateColor}
        </if>

    </select>


    <select id="getLocationQualityWithLocationError" parameterType="com.xh.vdm.statistic.entity.DetailParam" resultType="com.xh.vdm.statistic.entity.LocationQuality">
        select lq.* from location_quality_${param.monthStrict} lq
        left join bdm_vehicle bv on lq.licence_plate = bv.licence_plate and lq.licence_color::text = bv.licence_color
        where 1 = 1
        and bv.vehicle_owner_id = #{param.ownerId,jdbcType=BIGINT}
        <if test="param.deptId != null and param.deptId != ''">
            and bv.dept_id = #{param.deptId,jdbcType=BIGINT}
        </if>
        and bv.is_check = 1
        and lq.total_error_count > 0
        <if test="param.licencePlate != null and param.licencePlate != ''">
            and bv.licence_plate = #{param.licencePlate,jdbcType=VARCHAR}
        </if>
        <if test="param.plateColor != null and param.plateColor != ''">
            and bv.licence_color = #{param.plateColor,jdbcType=INTEGER}
        </if>
        order by bv.licence_plate, bv.licence_color

    </select>


    <select id="getLocationQualityByCondition" parameterType="com.xh.vdm.statistic.entity.DetailParam" resultType="com.xh.vdm.statistic.entity.LocationQuality">
        select id , licence_plate, total_count,total_error_count, time_err_count,receive_time_err_count,longitude_err_count,latitude_err_count,speed_err_count,altitude_err_count,stat_date,create_time,is_del
        from location_quality_${monthStrict} lq
        left join bdm_vehicle bv on lq.licence_plate = bv.licence_plate and lq.licence_color::text = bv.licence_color
        where 1 = 1
          and bv.vehicle_owner_id = #{param.ownerId,jdbcType=BIGINT}
        <if test="param.deptId != null and param.deptId != ''">
            and bv.dept_id = #{param.deptId,jdbcType=BIGINT}
        </if>

        and bv.is_check = 1
            <if test="param.licencePlate != null and param.licencePlate != '' ">
                and bv.licence_plate = #{param.licencePlate,jdbcType=VARCHAR}
            </if>
        <if test="param.plateColor != null and param.plateColor != '' ">
            and bv.licence_color = #{param.plateColor,jdbcType=INTEGER}
        </if>
          and total_error_count > 0
    </select>


    <select id="getQualityRateByDeptId" resultType="com.xh.vdm.statistic.entity.VehicleRateWithDept">
        select id dept_id , (totalErrorCount / totalCount) rate
        from
            (
                select sd.id ,sum(lq.total_count) totalCount , sum(lq.total_error_count) totalErrorCount
                from location_quality_${month} lq , bdm_vehicle bv ,
                     sys_dept sd
                where lq.licence_plate = bv.licence_plate and lq.licence_color::text = bv.licence_color and bv.dept_id = sd.id
                and bv.is_check = 1
                <if test="deptIds != null and deptIds != ''">
                  and sd.id in (
                    <foreach collection="deptIds" item="deptId" separator=",">
                        #{deptId}
                    </foreach>
                    )
                </if>
        <if test="ownerId != null and ownerId != ''">
            and bv.vehicle_owner_id = #{ownerId}
        </if>
                  and lq.is_del = 0 and sd.is_del = 0 and bv.is_del = 0
                  and lq.total_error_count > 0
                group by sd.id
            ) rate

    </select>


    <select id="getQualityRateByDeptIdDeptOrArea" resultType="com.xh.vdm.statistic.entity.VehicleRateWithDept">
        select
        <if test="deptId != null and deptId != ''">
            id dept_id ,
        </if>
        (totalErrorCount / totalCount) rate
        from
        (
        select
        <if test="deptId != null and deptId != ''">
            sd.id ,
        </if>
        sum(lq.total_count) totalCount , sum(lq.total_error_count) totalErrorCount
        from location_quality_${month} lq , bdm_vehicle bv ,
        sys_dept sd
        where lq.licence_plate = bv.licence_plate and bv.dept_id = sd.id
        and bv.vehicle_use_type in (10,11,12,30,31,32)
        <if test="deptId != null and deptId != ''">
            and sd.id = #{deptId}
        </if>
        <if test="ownerId != null and ownerId != ''">
            and bv.vehicle_owner_id = #{ownerId}
        </if>
        and lq.is_del = 0 and sd.is_del = 0 and bv.is_del = 0
        and lq.total_error_count > 0
        <if test="deptId != null and deptId != ''">
            group by sd.id
        </if>
        ) rate

    </select>

    <select id="getQualityByCondition" resultType="com.xh.vdm.statistic.entity.VehicleAndQuality">
        select licence_plate, licence_color, sum(total_error_count) total_error_count, sum(total_count) total_count
        from
        (
            <foreach collection="request.dmList" item="dm" separator=" union ">
                select lq.licence_plate, lq.licence_color, #{dm.month} "month", coalesce(sum(total_error_count),0) total_error_count ,
                coalesce(sum(total_count) ,0) total_count
                from location_quality_${dm.month} lq
                left join bdm_vehicle bv on lq.licence_plate = bv.licence_plate and lq.licence_color::text = bv.licence_color
                where stat_date in (
                    <foreach collection="dm.dateList" item="date" separator=",">
                        to_date(#{date},'yyyy-mm-dd')
                    </foreach>
                )
                <if test="request.vehicleIdList != null and request.vehicleIdList.size() > 0">
                    and bv.id in
                    <foreach collection="request.vehicleIdList" item="vehicleId" open="(" close=")" separator=",">
                        #{vehicleId}
                    </foreach>
                </if>
                <if test="request.vehicleIdList == null or request.vehicleIdList.size() == 0">
                    and (
                    <if test="request.deptList != null">
                        bv.dept_id in (
                        <foreach collection="request.deptList" item="deptId" separator=",">
                            #{deptId,jdbcType=BIGINT}
                        </foreach>
                        )
                    </if>
                    <if test="request.userId != null">
                        or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{request.userId,jdbcType=BIGINT})
                    </if>
                    )
                </if>

                <if test="request.vehicleUseType != null ">
                    and bv.vehicle_use_type in
                    <foreach collection="request.professionList" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="request.vehicleOwnerId != null">
                    and bv.vehicle_owner_id = #{request.vehicleOwnerId}
                </if>
                <if test="request.accessMode != null">
                    and bv.access_mode = #{request.accessMode}
                </if>
                group by lq.licence_plate, lq.licence_color
            </foreach>
        ) a
        group by a.licence_plate, a.licence_color
    </select>

</mapper>
