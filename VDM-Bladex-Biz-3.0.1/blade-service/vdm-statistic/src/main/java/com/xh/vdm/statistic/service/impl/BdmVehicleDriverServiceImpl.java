package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.BdmVehicleDriver;
import com.xh.vdm.statistic.entity.DescAndCount;
import com.xh.vdm.statistic.entity.DriverAgeAndCount;
import com.xh.vdm.statistic.entity.DriverDriveAgeAndCount;
import com.xh.vdm.statistic.mapper.BdmVehicleDriverMapper;
import com.xh.vdm.statistic.service.IBdmVehicleDriverService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
@Service
public class BdmVehicleDriverServiceImpl extends ServiceImpl<BdmVehicleDriverMapper, BdmVehicleDriver> implements IBdmVehicleDriverService {

    @Override
    public BdmVehicleDriver findDriverBaseInfo(String idCard) throws Exception{


        return baseMapper.selectOne(Wrappers.lambdaQuery(BdmVehicleDriver.class).eq(BdmVehicleDriver::getIdcard,idCard));
    }

    @Override
    public int findDriverCount(Long deptId) throws Exception {
        return baseMapper.getDriverCountByDeptId(deptId);
    }

    @Override
    public int findCertExpirationCount(Long deptId) throws Exception {
        return baseMapper.getCertificateExpirationCount(deptId);
    }

    @Override
    public int findCertIntimeCount(Long deptId) throws Exception {
        return baseMapper.getCertificateIntimeCount(deptId);
    }

    @Override
    public List<DescAndCount> findDriverAgeGroup(Long deptId) throws Exception {
        List<DriverAgeAndCount> list = baseMapper.getDriverAgeAndCount(deptId);
        //对驾驶员的年龄进行分段处理
        List<DescAndCount> descList = new ArrayList<>();
        Map<String ,Integer>  countMap = new HashMap<>();
        list.forEach(item -> {
            String age = item.getAge();
            Integer count = item.getCount();
            if("未知".equals(age) || StringUtils.isBlank(age)){
                countMap.put("未知", (countMap.get("未知")==null?0:countMap.get("未知")) + (count==null?0:count));
            }else{
                int ageInt = Integer.parseInt(age);
                if(ageInt <= 30){
                    countMap.put("level1", (countMap.get("level1")==null?0:countMap.get("level1")) + (count == null?0:count));
                }else if(ageInt > 30 && ageInt <= 40){
                    countMap.put("level2", (countMap.get("level2")==null?0:countMap.get("level2")) + (count == null?0:count));
                }else if(ageInt > 40 && ageInt <= 50){
                    countMap.put("level3", (countMap.get("level3")==null?0:countMap.get("level3")) + (count == null?0:count));
                }else{
                    countMap.put("level4", (countMap.get("level4")==null?0:countMap.get("level4")) + (count == null?0:count));
                }
            }
        });
        countMap.forEach((k ,v) -> {
            DescAndCount dc = new DescAndCount();
            dc.setDesc(k);
            dc.setCount(v);
            descList.add(dc);
        });
        return descList;
    }

    @Override
    public List<DescAndCount> findDriverDriveAgeGroup(Long deptId) throws Exception {
        List<DriverDriveAgeAndCount> list = baseMapper.getDriverDriveAgeAndCount(deptId);

        //对驾驶员的驾龄进行分段处理
        List<DescAndCount> descList = new ArrayList<>();
        Map<String ,Integer>  countMap = new HashMap<>();
        list.forEach(item -> {
            String age = item.getDriveAge();
            Integer count = item.getCount();
            if("未知".equals(age) || StringUtils.isBlank(age)){
                countMap.put("未知", (countMap.get("未知")==null?0:countMap.get("未知")) + (count==null?0:count));
            }else{
                int ageInt = Integer.parseInt(age);
                if(ageInt <= 5){
                    countMap.put("level1", (countMap.get("level1")==null?0:countMap.get("level1")) + (count == null?0:count));
                }else if(ageInt > 5 && ageInt <= 10){
                    countMap.put("level2", (countMap.get("level2")==null?0:countMap.get("level2")) + (count == null?0:count));
                }else if(ageInt > 10 && ageInt <= 15){
                    countMap.put("level3", (countMap.get("level3")==null?0:countMap.get("level3")) + (count == null?0:count));
                }else if(ageInt > 15 && ageInt <= 20){
                    countMap.put("level4", (countMap.get("level4")==null?0:countMap.get("level4")) + (count == null?0:count));
                }else{
                    countMap.put("level5", (countMap.get("level5")==null?0:countMap.get("level5")) + (count == null?0:count));
                }
            }
        });
        countMap.forEach((k ,v) -> {
            DescAndCount dc = new DescAndCount();
            dc.setDesc(k);
            dc.setCount(v);
            descList.add(dc);
        });
        return descList;
    }
}
