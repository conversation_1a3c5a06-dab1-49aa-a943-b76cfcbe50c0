package com.xh.vdm.statistic.constant;

public interface AlarmConstant {

	/* ******************** 字典相关 ******************** */

	// 超速
	short DICT_ALARM_TYPE_OVER_SPEED = 82;

	// 疲劳（终端）
	short DICT_ALARM_TYPE_TIRED_TERMINAL = 2;

	// 疲劳（平台）
	short DICT_ALARM_TYPE_TIRED_PLATFORM = 102;

	// 夜间行驶
	short DICT_ALARM_TYPE_NIGHT = 103;

	// 高级驾驶辅助系统报警（ADAS）
	short DICT_ALARM_TYPE_ADAS = 85;

	// 驾驶员辅助监测类报警（DSM）
	short DICT_ALARM_TYPE_DSM = 86;

	//激烈驾驶
	short DICT_ALARM_TYPE_INTENSE_DRIVING = 87;

	/* ******************** == ******************** */










	/* 字典key */

	byte HANDLE_MEASURES_AUDIO = 1;
	byte HANDLE_MEASURES_SCREEN = 2;
	byte HANDLE_MEASURES_URGENT = 3;

	/**/



	//记录发生告警的终端
	//alarm:device:[日期]:[部门id]
	//alarm:device:20241106:1231234124123
	String CACHE_PREFIX_ALARM_DEVICE = "alarm:device:";

}
