package com.xh.vdm.statistic.vo.response.alarm;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel(value = "返回体：报警处理率导出")
@Data
public class ExportDealRateResponse {

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "单位名称"})
	private String deptName;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "车辆数"})
	private Integer numVehicle;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "已处理报警数/报警数"})
	private String numDealAndAlarm;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "报警处理率"})
	private String dealRate;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "已处理超速报警数/超速报警数"})
	private String numDealAndAlarmOverSpeed;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "超速报警处理率"})
	private String dealRateOverSpeed;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "已处理疲劳驾驶报警数/疲劳驾驶报警数"})
	private String numDealAndAlarmFatigue;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "疲劳驾驶报警处理率"})
	private String dealRateFatigue;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "已处理夜间异动报警数/夜间异动报警数"})
	private String numDealAndAlarmNightDrive;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "夜间异动报警处理率"})
	private String dealRateNightDrive;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "已处理ADAS报警数/ADAS报警数"})
	private String numDealAndAlarmAdas;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "ADAS报警处理率"})
	private String dealRateAdas;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "已处理DSM报警数/DSM报警数"})
	private String numDealAndAlarmDsm;

	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN, borderBottom = BorderStyleEnum.THIN)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ColumnWidth(40)
	@ExcelProperty({"报警处理率", "DSM报警处理率"})
	private String dealRateDsm;
}
