package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.vo.page.PageParam;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

/**
 * 车辆在线情况抽查请求类
 * <AUTHOR>
 * @date 2021/10/26 13:37
 */
@ApiModel(value = "车辆在线情况抽查请求类")
@Data
public class VehicleOnlineRequest extends PageParam {

    //所有车组，包含子车组
	@JsonProperty("dept_id_list")
    private List<Long> deptIdList;
}
