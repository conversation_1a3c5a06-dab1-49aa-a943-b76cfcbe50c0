package com.xh.vdm.statistic.vo.request.alarm;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Pattern;

@ApiModel(value = "请求体：驾驶员报警统计")
@Data
@EqualsAndHashCode(callSuper = true)
public class DriverAlarmRequest extends CommonAlarmStatRequest {

	@JsonProperty("alarm_level")
	@ApiModelProperty(name = "alarm_level", value = "报警等级（等级值与名称的映射，详见blade_dict_biz表，code=alarm_level的记录）", example = "1", required = false)
	@DecimalMin(value = "0", message = "报警等级不正确。")
	private Short alarmLevel;

	@JsonProperty("driver_name")
	@ApiModelProperty(name = "driver_name", value = "驾驶员姓名", example = "何斌", required = false)
	private String driverName;

	@JsonProperty("id_card")
	@ApiModelProperty(name = "id_card", value = "驾驶员身份证", example = "******************", required = false)
	//@Pattern(regexp = "\\d{17}[\\dXx]", message = "驾驶员身份证不正确。")
	private String idCard;

	@JsonProperty("vehicle_id")
	@ApiModelProperty(name = "vehicle_id", value = "车辆ID", example = "1", required = false)
	@DecimalMin(value = "1", message = "车辆ID不正确。")
	private Integer vehicleId;
}
