package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.StatDriverDuration;
import com.xh.vdm.statistic.vo.request.DriverDurationRequest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-23
 */
public interface StatDriverDurationMapper extends BaseMapper<StatDriverDuration> {

	/**
	 * @description: 根据日期删除数据
	 * @author: zhouxw
	 * @date: 2023-02-54 11:36:37
	 * @param: [date]
	 * @return: void
	 **/
	void deleteDataByDate(String month, String date);

	void saveBatch(@Param("list") List<StatDriverDuration> list, @Param("month") String month) throws Exception;

	/**
	 * @param dateList
	 * @param month
	 */
	void deleteAppendData(@Param("dateList") List<String> dateList, @Param("month") String month);

	/**
	 * @description: 根据条件查询驾驶时段信息
	 * @author: zhouxw
	 * @date: 2023-02-54 15:29:05
	 * @param: [request , month:yyyyMM]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.StatDriverDuration>
	 **/
	List<StatDriverDuration> getDriverDurationByCondition(@Param("month") String month, @Param("request") DriverDurationRequest request);

	/**
	 * 根据身份证号查询驾驶时段信息
	 *
	 * @param idCard
	 * @param statDate yyyyMMdd
	 * @param month    yyyyMM
	 * @return
	 */
	List<StatDriverDuration> getDriverDurationListByIdCard(@Param("idCard") String idCard, @Param("statDate") String statDate, @Param("month") String month);


	/**
	 * 根据身份证号查询驾驶时段信息，指定月份
	 * @param idCard
	 * @param month yyyyMM
	 * @return
	 */
	List<StatDriverDuration> getDriverDurationListByIdCardMonth(@Param("idCard") String idCard, @Param("month") String month);
}
