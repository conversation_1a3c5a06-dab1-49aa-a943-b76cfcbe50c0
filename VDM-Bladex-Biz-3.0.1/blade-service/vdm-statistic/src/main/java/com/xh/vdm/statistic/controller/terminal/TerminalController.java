package com.xh.vdm.statistic.controller.terminal;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.statistic.dto.TerminalDataFromCache;
import com.xh.vdm.statistic.entity.terminal.*;
import com.xh.vdm.statistic.service.BdmDeviceLinkService;
import com.xh.vdm.statistic.service.IBdmTerminalService;
import com.xh.vdm.statistic.service.terminal.*;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.vo.response.terminal.TerminalCountResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.Dept;
import org.springblade.system.feign.ISysClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.*;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * 终端信息
 */
@RequestMapping("/terminal")
@RestController
@Slf4j
public class TerminalController {


	@Resource
	private ISysClient sysClient;


	@Resource
	private IBdmTerminalService terminalService;


	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private IRnssDeviceService rnssDeviceService;

	@Resource
	private IRdssDeviceService rdssDeviceService;

	@Resource
	private IMonitDeviceService monitDeviceService;

	@Resource
	private IPntDeviceService pntDeviceService;

	@Resource
	private IWearableDeviceService wearableDeviceService;

	@Resource
	private BdmDeviceLinkService bdmDeviceLinkService;

	/**
	 * @description: 终端数量统计
	 * @author: zhouxw
	 * @date: 2023-06-166 21:01:50
	 * @param: [user]
	 * @return: org.springblade.core.tool.api.R<com.xh.vdm.statistic.vo.response.IndexBaseInfoResponse>
	 **/
	//todo 车辆上下线数据和车辆在线数据未定
	@GetMapping("/terminalCount")
	public R<TerminalCountResponse> baseInfo(BladeUser user){

		//1.查询企业数量
		//1.1 获取登录的信息
		long deptCount = 0;
		//本地调试
		String deptId = user.getDeptId();
		user = new BladeUser();
		user.setDeptId(deptId);


		List<Long> deptIds = new ArrayList<>();
		deptIds.add(Long.parseLong(deptId));
		/*try{
			//如果查询子部门失败，则只查询本部门的数据
			deptIds = userInfoUtil.getChildrenAndSelfDeptId(user);
		}catch (Exception e){
			log.error("查询子级部门失败",e);
		}*/

		List<Long> deptIdList = new ArrayList<>();
		try{
			//1.2 获取子机构信息
			R<List<Dept>> res = sysClient.getDeptChild(Long.parseLong(deptId));
			if(res == null || !res.isSuccess()){
				log.error("[主页--基础信息]查询企业数量失败：调用feign接口失败");
			}
			//记录部门id
			for(Dept d : res.getData()){
				deptIdList.add(d.getId());
			}
			deptIdList.add(Long.parseLong(user.getDeptId()));
		}catch (Exception e){
			log.error("[主页--基础信息]查询子部门失败",e);
		}

		//2.注册终端总数（五种终端的数量和）
		long totalCount = 0;
		try{

			//2.1 定位终端
			//todo 没有绑定对象的终端，暂时不算
			long rnssCount = rnssDeviceService.count(Wrappers.lambdaQuery(RnssDevice.class)
				.eq(RnssDevice::getDeleted, 0)
					.gt(RnssDevice::getTargetId,0)
				.in(RnssDevice::getDeptId, deptIdList));
			//2.2 穿戴式终端
			long wearableCount = wearableDeviceService.count(Wrappers.lambdaQuery(WearableDevice.class)
				.eq(WearableDevice::getDeleted, 0)
				.in(WearableDevice::getDeptId, deptIdList));

			//2.3 北斗短报文终端
			long rdssCount = rdssDeviceService.count(Wrappers.lambdaQuery(RdssDevice.class)
				.eq(RdssDevice::getDeleted, 0)
				.gt(RdssDevice::getTargetId,0)
				.in(RdssDevice::getDeptId, deptIdList));

			//2.4 北斗检测终端
			long monitCount = monitDeviceService.count(Wrappers.lambdaQuery(MonitDevice.class)
				.eq(MonitDevice::getDeleted, 0)
				.in(MonitDevice::getDeptId, deptIdList));

			//2.5 北斗授时终端
			long pntCount = pntDeviceService.count(Wrappers.lambdaQuery(PntDevice.class)
				.eq(PntDevice::getDeleted, 0)
				.in(PntDevice::getDeptId, deptIdList));
			//todo 暂时只查询定位终端
			totalCount = rnssCount;// + wearableCount + rdssCount + pntCount + monitCount;
		}catch (Exception e){
			log.error("[查询终端数量信息]查询终端数量信息失败",e);
		}

		//从redis中读取终端数据
		Set<String> keys = stringRedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
			Set<String> result = new HashSet<>();
			Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(CommonConstant.PREFIX_REDIS_TERMINAL+"*").build());
			while (cursor.hasNext()) {
				result.add(new String(cursor.next()));
			}
			return result;
		});
		//todo 查询redis中的数据时，要考虑数据权限
		//今日上线数量
		long goOnlineCount = 0;
		//在线数量
		long onlineCount = 0;
		//长期离线数量
		long longTimeOfflineCount = 0;
		try{
			for(String key : keys){
				String data = stringRedisTemplate.opsForValue().get(key);
				TerminalDataFromCache tData = JSON.parseObject(data,TerminalDataFromCache.class);
				if(tData.getTeState() == 1){
					//如果在线
					onlineCount ++;
					goOnlineCount ++;
				}else{
					//如果不在线，则判断最后上线时间距离现在是否超过了15天，如果超过，则为长期离线
					if(System.currentTimeMillis()/1000 - tData.getLocTime() > 15 * 24 * 3600){
						longTimeOfflineCount ++;
					}
					//判断今天是否上过线
					try {
						if(tData.getLocTime() >= DateUtil.getDayFirstSecondTimestamp()){
							goOnlineCount ++;
						}
					} catch (Exception e) {
					}
				}
			}
		}catch (Exception e){
			log.error("从缓存中读取数据失败",e);
		}

		//6.未赋码终端数
		long noDeviceNumCount = 0;
		try{
			noDeviceNumCount = terminalService.findNoDeviceNumCount(deptIds);
		}catch (Exception e){
			log.error("[查询终端数量信息]查询未赋码终端数量失败",e);
		}

		//7.长期离线终端数
		try{
			//计算十五天前的日期
			//long secondTimestamp = DateUtil.getDayFirstSecondTimestamp(DateUtil.getDateBeforeDay(new Date(), 15).getTime());
			//longTimeOfflineCount = bdmDeviceLinkService.findLongOfflineCount(deptIdList, secondTimestamp);
		}catch (Exception e){
			log.error("[查询终端数量信息]查询长期未在线终端数量失败");
		}

		TerminalCountResponse response = new TerminalCountResponse();
		response.setTotalCount(totalCount);
		response.setOnlineCount(onlineCount);
		response.setGoOnlineCount(goOnlineCount);
		response.setNoDeviceNumCount(noDeviceNumCount);
		response.setLongTimeOfflineCount(longTimeOfflineCount);
		return R.data(response);
	}
}
