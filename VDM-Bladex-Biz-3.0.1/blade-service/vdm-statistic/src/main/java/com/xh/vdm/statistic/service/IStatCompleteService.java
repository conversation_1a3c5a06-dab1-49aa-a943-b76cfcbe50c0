package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.bd.entity.LocationKudu;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.response.LocationCompleteDetailResponse;
import com.xh.vdm.statistic.vo.response.LocationCompleteRateResponse;

import java.util.List;

/**
 * <p>
 * 轨迹完整率表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
public interface IStatCompleteService extends IService<StatComplete>  {

    /**
     * @description: 统计指定日期的车辆轨迹完整率
     * day 格式为 yyyyMMdd
     * @author: zhouxw
     * @date: 2022/9/1 10:28 AM
     * @param: [day 指定的定位数据日期]
     * @return: boolean
     **/
    boolean locationCompleteStat(String day) throws Exception;

    /**
     * @description: 根据给定的车辆定位数据列表计算轨迹完整率
     * @author: zhouxw
     * @date: 2022/9/5 8:32 AM
     * @param: []
     * @return: boolean
     **/
    CompleteDataNode locationCompleteStatByPositionList(List<LocationKudu> list);

    /**
     * @description: 判断月表是否存在，不存在，则创建新的月表
     * month 格式为 yyyyMM
     * @author: zhouxw
     * @date: 2022/9/2 8:13 AM
     * @param: [month]
     * @return: void
     **/
    void checkTableExistAndCreate(String month);

    /**
     * @description: 查询轨迹完整率
     * @author: zhouxw
     * @date: 2022/9/8 2:04 PM
     * @param: [param]
     * @return: com.xh.vdm.statistic.vo.response.LocationCompleteRateResponse
     **/
    LocationCompleteRateResponse getLocationCompleteRate(RateParam param);

	/**
	 * 根据日期统计轨迹完整率
	 * 拼装动态sql，然后执行统计
	 * @param dateList yyyyMMdd
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	List<DateAndComplete> findCompleteInfoByDate(List<String> dateList, List<Long> deptIds, Long userId) throws Exception;

    /**
     * @description: 查询轨迹不完整的车辆的轨迹详情
     * @author: zhouxw
     * @date: 2022/9/8 4:42 PM
     * @param: [param]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.LocationCompleteDetailResponse>
     **/
    IPage<LocationCompleteDetailResponse> findUnCompleteList(DetailParam param);

    /**
     * @description: 根据 企业id 统计完整率
     * @author: zhouxw
     * @date: 2022/9/13 5:44 PM
     * @param: [month, deptIds]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleRateWithDept>
     **/
    List<VehicleRateWithDept> findCompleteByDeptId(String month , List<Long> deptIds , Long ownerId);

	/**
	 * @description: 根据 企业id 统计完整率
	 * 如果不指定企业，就查询全区域
	 * @author: zhouxw
	 * @date: 2022/9/13 5:44 PM
	 * @param: [month, deptIds]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.VehicleRateWithDept>
	 **/
	VehicleRateWithDept findCompleteByDeptIdDeptOrArea(String month , Long deptId , Long ownerId);

	/**
	 * 查询车辆轨迹完整率里程数据
	 * @param request
	 * @return
	 * @throws Exception
	 */
	List<CompleteMileageNode> findCompleteMileage(CommonBaseCrossMonthRequest request) throws Exception;


}
