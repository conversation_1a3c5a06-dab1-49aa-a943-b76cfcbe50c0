package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.BdmTerminal;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-20
 */
public interface IBdmTerminalService extends IService<BdmTerminal> {

	/**
	 * 根据赋码值查询设备归属单位名称
	 * @param deviceNum
	 * @return
	 * @throws Exception
	 */
	String findDeptNameByDeviceNum(String deviceNum) throws Exception;

	/**
	 * 根据给定的部门查询终端总数
	 * @param deptIds
	 * @return
	 */
	Long findTotalTerminalCount(List<Long> deptIds) throws Exception;

	/**
	 * 查询未赋码终端数量
	 * @param deptIds
	 * @return
	 * @throws Exception
	 */
	Long findNoDeviceNumCount(List<Long> deptIds) throws Exception;
}
