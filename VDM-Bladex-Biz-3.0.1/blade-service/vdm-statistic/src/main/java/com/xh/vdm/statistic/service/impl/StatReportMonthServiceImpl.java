package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.report.StatReportMonth;
import com.xh.vdm.statistic.mapper.StatReportMonthMapper;
import com.xh.vdm.statistic.service.IStatReportMonthService;
import com.xh.vdm.statistic.vo.request.CompanyAndDate;
import com.xh.vdm.statistic.vo.response.ReportInfoResponse;
import org.springblade.core.mp.support.Query;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 企业月报表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Service
public class StatReportMonthServiceImpl extends ServiceImpl<StatReportMonthMapper, StatReportMonth> implements IStatReportMonthService {

	@Override
	public IPage<ReportInfoResponse> getMonthReportPage (CompanyAndDate request, Query query) {
		IPage<ReportInfoResponse> page = new Page<>(query.getCurrent(), query.getSize());
		return this.baseMapper.getMonthReportPage(request, page);
	}
}
