package com.xh.vdm.statistic.mapper.alarm;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.bd.vo.request.AlarmRequest;
import com.xh.vdm.statistic.vo.response.terminal.AlarmCountResponse;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springblade.entity.Alarm;

import java.util.List;

@Mapper
public interface AlarmImpalaMapper extends BaseMapper<Alarm> {

	@DS("location")
	List<AlarmCountResponse> alarmStatics(@Param("deptList") List<Long> deptList);

	@DS("location")
	Long count();
}
