package com.xh.vdm.statistic.service.terminal.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.xh.vdm.statistic.entity.terminal.*;
import com.xh.vdm.statistic.mapper.terminal.MonitDeviceMapper;
import com.xh.vdm.statistic.mapper.terminal.ScreenBaseInfoMapper;
import com.xh.vdm.statistic.service.terminal.*;
import com.xh.vdm.statistic.vo.response.terminal.HighPrecisionCountResponse;
import com.xh.vdm.statistic.vo.response.terminal.TerminalDeptCountResponse;
import org.springblade.core.secure.utils.AuthUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Description:
 *
 * <AUTHOR>
 */
@Service
public class ScreenBaseInfoServiceImpl implements ScreenBaseInfoService {

	@Resource
	private MonitDeviceMapper monitDeviceMapper;
	@Resource
	private ScreenBaseInfoMapper screenBaseInfoMapper;

	@Override
	public Integer statisticsTerminalByYear(int year, Long userId) {
		if (AuthUtil.isAdministrator()) {
			userId = null;
		}
		return monitDeviceMapper.statisticsTerminalByYear(year, userId);
	}

	@Override
	public List<TerminalDeptCountResponse> getTerminalCountByDept(Long userId) {
		if (AuthUtil.isAdministrator()) {
			userId = null;
		}
		return monitDeviceMapper.getTerminalCountByDept(userId);
	}

	@Override
	public List<HighPrecisionCountResponse> getHighPrecisionCount(List<String> keys, Long userId) {
		List<Integer> keyIntegers = keys.stream()
			.map(Integer::parseInt)
			.collect(Collectors.toList());

		List<Integer> allDomains = new ArrayList<>();
		if (AuthUtil.isAdministrator()) {
			userId = null;
		}
		allDomains.addAll(screenBaseInfoMapper.getHighPrecisionCount(keyIntegers,userId));

		Map<Integer, Long> domainCountMap = allDomains.stream().collect(Collectors.groupingBy(Function.identity(), Collectors.counting()));

		List<HighPrecisionCountResponse> responseList = new ArrayList<>();
		for (Map.Entry<Integer, Long> entry : domainCountMap.entrySet()) {
			HighPrecisionCountResponse response = new HighPrecisionCountResponse();
			response.setDomain(entry.getKey());
			response.setCount(entry.getValue().intValue());
			responseList.add(response);
		}

		responseList.sort(Comparator.comparing(HighPrecisionCountResponse::getCount, Comparator.reverseOrder()));

		return responseList;
	}
}
