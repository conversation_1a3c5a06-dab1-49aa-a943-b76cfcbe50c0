package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.CacheVehicleOnlineRateMapper;
import com.xh.vdm.statistic.mapper.StatisticsMapper;
import com.xh.vdm.statistic.service.IBamThirdPartyPlatformService;
import com.xh.vdm.statistic.service.IBdmVehicleService;
import com.xh.vdm.statistic.service.IBladeDeptService;
import com.xh.vdm.statistic.service.ICacheVehicleOnlineRateService;
import com.xh.vdm.statistic.utils.BeanUtil;
import com.xh.vdm.statistic.utils.CommonBusiUtil;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.LineAndHumpUtil;
import com.xh.vdm.statistic.vo.request.VehicleOnlineRateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.tool.api.R;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.beans.PropertyDescriptor;
import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <p>
 * 车辆在线率DB缓存表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-08
 */
@Service
@Slf4j
public class CacheVehicleOnlineRateServiceImpl extends ServiceImpl<CacheVehicleOnlineRateMapper, CacheVehicleOnlineRate> implements ICacheVehicleOnlineRateService {

	@Resource
	private StatisticsMapper statisticsMapper;

	@Resource
	private DataSource dataSource;

	@Resource
	private IBdmVehicleService vehicleService;

	@Resource
	private CommonBusiUtil commonBusiUtil;

	@Resource
	private IDictBizClient dictBizClient;

	@Autowired
	private IBamThirdPartyPlatformService bamThirdPartyPlatformService;

	@Resource
	private IBamThirdPartyPlatformService thirdPartyPlatformService;

	@Resource
	private IBladeDeptService bladeDeptService;

	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(20, 50, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(5));

	@Override
	public void statisticsVehicleOnlineRate(Long startTime, Long endTime) throws Exception {

		//1.删除指定时间的数据（重置为null）
        /*for(String date : dateList){
            StringBuffer sb = new StringBuffer("update "+ CommonConstants.CACHE_DB_VEHICLE_ONLINE_RATE + " set ");
            String month = date.substring(0,7);
            String day = date.substring(8,10);
            sb.append(" d"+ day +" = null ");
            sb.append(" where month = " + month);
            SqlRunner.db().update(sb.toString());
        }*/

		//2.执行数据统计
		/*List<String> yearMonthList = new ArrayList<>();
		for (String date: dateList) {
			yearMonthList.add(date.replace("-", "").substring(0, 6));
		}*/

		log.info("[在线率统计]将要查询统计数据");
		long start2 = System.currentTimeMillis();


		//此方法查询50分钟，需要优化
		//List<HashMap<String,Object>> list = statisticsMapper.rateStatisticsWithTime(dateList, yearMonthList);
		//需要更改参数，传入开始时间和结束时间
		List<Map<String,Object>> list = getOnlineData(startTime, endTime);

		//todo vehicleOnlineRate，数据跑批还是有问题，需要再测试下逻辑

		long end2 = System.currentTimeMillis();
		log.info("[在线率统计]执行数据统计完成，共耗时"+(end2 - start2));

		//构造组合list，查询车辆缓存数据
		log.info("[在线率统计]开始拼装数据");
		Set<String> paramList = new HashSet<>();
		for(Map<String,Object> item : list) {
			Object licencePlate = item.get(StatisticConstants.ONLINE_RATE_LICENCE_PLATE);
			Object licenceColor =  item.get(StatisticConstants.ONLINE_RATE_LICENCE_COLOR);
			String monthTmp = "";
			for(String k : item.keySet()){
				if(k.indexOf("-") > 0){
					monthTmp = k.substring(0,7);
				}
				if(licencePlate == null || licenceColor == null || StringUtils.isBlank(monthTmp)){
					continue;
				}else{
					paramList.add(licencePlate.toString()+"~"+licenceColor.toString()+"~"+monthTmp);
				}
			}
		}
		List<CacheVehicleOnlineRate> cacheList = baseMapper.getCacheVehicleOnlineRateWithCondition(paramList);

		Map<String,CacheVehicleOnlineRate> cacheMap = new HashMap<>();
		cacheList.forEach(item -> {
			cacheMap.put(item.getLicencePlate()+"~"+item.getLicenceColorCode()+"~"+item.getMonth(), item);
		});


		//3.分析数据，构造车辆在线 DB cache 对象
		List<CacheVehicleOnlineRate> addList = new ArrayList<>();
		List<CacheVehicleOnlineRate> updateList = new ArrayList<>();
		Class clazz = CacheVehicleOnlineRate.class;
		for(Map<String,Object> map : list){
			String monthTmp = "";
			CacheVehicleOnlineRate rate = null;
			for(String k : map.keySet()){
				if(k.indexOf("-") > 0){
					monthTmp = k.substring(0,7);

					//根据车牌号+车牌颜色+month查询在线信息
					rate = cacheMap.get(map.get(StatisticConstants.ONLINE_RATE_LICENCE_PLATE)+"~"+map.get(StatisticConstants.ONLINE_RATE_LICENCE_COLOR)+"~"+monthTmp);
					if(rate == null){
						rate = new CacheVehicleOnlineRate();
						rate.setCreateTime(new Date());
						cacheMap.put(map.get(StatisticConstants.ONLINE_RATE_LICENCE_PLATE)+"~"+map.get(StatisticConstants.ONLINE_RATE_LICENCE_COLOR)+"~"+monthTmp, rate);
					}/*else{
                        updateList.add(rate);
                    }*/

					//cacheTmpList.add(rate);

					//如果是日期字段，则存储在线时长
					String day = k.substring(8,10);
					try {
						PropertyDescriptor pd = new PropertyDescriptor("d" + day, clazz);
						pd.getWriteMethod().invoke(rate, map.get(k));
						rate.setMonth(monthTmp);
					}catch (Exception e){
						log.error("反射报错",e);
					}
					//非日期字段的字段值设置
					for(String f : map.keySet()){
						//如果不是日期字段
						if(f.indexOf("-") < 0){
							PropertyDescriptor pd = new PropertyDescriptor(f,clazz);
							Object value = map.get(f);
							try{
								if( value instanceof Long){
									if(f.toUpperCase().equals("VEHICLEMODELCODE")){
										pd.getWriteMethod().invoke(rate , value + "");
									}else{
										pd.getWriteMethod().invoke(rate , Long.parseLong(value==null?"0":value.toString()));
									}
								}else{
									pd.getWriteMethod().invoke(rate , value);
								}
							}catch (Exception e){
								log.error("反射报错",e);
							}
						}
					}

				}
			}
		}
		//3.批量存储 车辆在线情况 DB cache
		long start = System.currentTimeMillis();
		log.info("开始执行批量保存操作");
		//区分保存和更新
		cacheMap.values().forEach(item -> {
			if(item.getId() == null){
				//如果是新增
				addList.add(item);
			}else{
				//如果是修改
				updateList.add(item);
			}
		});
		//saveOrUpdateBatch(rateList);
		CountDownLatch countDownLatch = new CountDownLatch(2);
		//批量新增
		StatisticConstants.threadPool.submit(() -> {
			try {
				saveBatch(addList);
			}catch (Exception e){
				log.error("批量新增报错",e);
			}finally{
				countDownLatch.countDown();
			}
		});
		//批量修改
		AtomicLong updateCount = new AtomicLong();
		StatisticConstants.threadPool.submit(() -> {
			try {
				updateCount.set(updateBatchWithJDBC(updateList));
			}catch (Exception e){
				log.error("批量修改报错",e);
			}finally {
				countDownLatch.countDown();
			}
		});
		countDownLatch.await();
		if(updateCount.get() == -1){
			//如果批量更新失败
			throw new Exception("批量更新失败");
		}
		long end = System.currentTimeMillis();
		log.info("执行批量保存操作结束，耗时"+ (end - start) );
	}

		//todo 现在跑出来数据不准确，需要根据但辆车进行分析

	/**
	 * 查询车辆在线情况
	 * @param startTime 开始时间，精确到秒
	 * @param endTime 结束时间，精确到秒
	 * @return
	 */
	private List<Map<String,Object>> getOnlineData(long startTime, long endTime) throws Exception{
		//1.查询基础数据
		CountDownLatch countDownLatch = new CountDownLatch(1);
		Map<String,BdmVehicle> vehicleMap = new HashMap<>();
		Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
		Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
		Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
		//查询部门信息、上级平台信息，用于后续手动增加字段
		Map<String,BladeDept> deptMap = new HashMap<>();
		Map<String,BamThirdPartyPlatform> platMap = new HashMap<>();
		threadPool.submit(() -> {
			try{
				//查询车辆信息
				List<BdmVehicle> vList = vehicleService.list(Wrappers.lambdaQuery(BdmVehicle.class).eq(BdmVehicle::getIsDel,0));
				for(BdmVehicle v : vList){
					String key = v.getLicencePlate()+"~"+v.getLicenceColor();
					vehicleMap.put(key,v);
				}

				List<BladeDept> depts = null;
				List<BamThirdPartyPlatform> platList = null;
				platList = thirdPartyPlatformService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled, 0));
				depts = bladeDeptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
				platList.forEach(item -> {
					platMap.put(item.getId()+"", item);
				});
				depts.forEach(item -> {
					deptMap.put(item.getId()+"", item);
				});
			}catch (Exception e){
				log.error("查询车辆信息失败");
			}finally {
				countDownLatch.countDown();
			}
		});

		List<MonthAndDuration> mds = DateUtil.getMonthAndDuration(startTime, endTime);
		List<Map<String,Object>> resList = new ArrayList<>();
		//2.查询上线时间数据
		//车牌号~车牌颜色~日期:在线时间
		Map<String,Long> vMap = new HashMap<>();
		for(MonthAndDuration md : mds){
			//查询每个月的上线情况
			List<BdmTerminalonlinerecord> oList = statisticsMapper.getVehicleOnlineDataByMonth(md.getMonth().replace("-",""),md.getStartSecondTime(), md.getEndSecondTime());
			//根据车牌号+车牌颜色分组
			Map<String,List<BdmTerminalonlinerecord>> map = new HashMap<>();
			for(BdmTerminalonlinerecord bo : oList){
				String key = bo.getLicencePlate()+"~"+bo.getLicenceColor();
				List<BdmTerminalonlinerecord> bList = map.get(key);
				if(bList == null || bList.size() < 0){
					bList = new ArrayList<>();
					map.put(key, bList);
				}
				bList = map.get(key);
				bList.add(bo);
			}
			//对每辆车的在线情况进行计算
			for(String key : map.keySet()){
				List<BdmTerminalonlinerecord> bList = map.get(key);
				for(int i = 0; i < bList.size(); i++){
					Date onlineTime = bList.get(i).getOnLineTime();
					Date offlineTime = bList.get(i).getOffLineTime();
					if(offlineTime == null){
						//如果没有离线时间，则查找下一条上线记录，如果存在下一条上线记录，则离线时间记为下一条上线时间；如果没有下一条上线记录，则记为当前系统时间
						if(i < bList.size() - 1){
							Date onlineTimeTmp = bList.get(i+1).getOnLineTime();
							offlineTime = onlineTimeTmp;
						}else{
							offlineTime = new Date();
						}


						Date dateTmp = new Date();
						dateTmp.setTime(endTime);
						String dateStrTmp = DateUtil.sdfHolder.get().format(dateTmp);
						if(dateStrTmp.equals(DateUtil.getDateString())){
							//如果是今日，那么离线时间就定位当前系统时间
							offlineTime = new Date();
						}else{
							//如果不是今日，那么离线时间就定为结束日期
							offlineTime = DateUtil.getDateBySecondTimestamp(endTime);
						}

					}
					long onlineSecondTime = onlineTime.getTime()/1000;
					long offlineSecondTime = offlineTime.getTime()/1000;

					//判断起止时间是否在查询范围内
					//经过上面的处理，online已经不存在空的情况了
					BdmTerminalonlinerecord b = bList.get(i);
					if(onlineSecondTime <= endTime && offlineSecondTime >= startTime){
						//如果在查询范围内容
						List<String> dateList = DateUtil.getDateList(onlineSecondTime, offlineSecondTime);
						if(dateList.size() == 1){
							//如果只有一天
							long timeDuration = offlineSecondTime - onlineSecondTime;
							vMap.put(b.getLicencePlate()+"~"+b.getLicenceColor()+"~"+dateList.get(0),timeDuration);
						}else{
							//如果跨月多天
							long onlineSecondTimeTmp = onlineSecondTime;
							long offlineSecondTimeTmp = offlineSecondTime;
							if(onlineSecondTime < startTime){
								onlineSecondTimeTmp = startTime;
							}
							if(offlineSecondTimeTmp > endTime){
								offlineSecondTimeTmp = endTime;
							}

							dateList = DateUtil.getDateList(onlineSecondTimeTmp, offlineSecondTimeTmp);
							for(int ind = 0 ; ind < dateList.size(); ind++){
								long timeDuration = 0;
								if(ind == 0){
									//如果是第一天
									timeDuration = DateUtil.getDayLastSecondTimestamp(onlineSecondTime) - onlineSecondTime;
								}else if (ind == dateList.size() - 1){
									//如果是最后一天
									timeDuration = offlineSecondTime - DateUtil.getDayFirstSecondTimestamp(offlineSecondTime);
								}else{
									//如果是中间天
									timeDuration = 3600 * 24;
								}
								//记录每辆车每天的在线时长，有可能每天上下线多次，所以要做加法
								String k = b.getLicencePlate()+"~"+b.getLicenceColor()+"~"+dateList.get(ind);
								Long t = vMap.get(k);
								if(t == null || t == 0){
									vMap.put(k, timeDuration);
								}else{
									t = t + timeDuration;
									vMap.put(k, t);
								}
							}
						}
					}

				}
			}
		}
		countDownLatch.await();
		//3.整合数据，转换为map(为满足之前代码的逻辑)
		for(String key : vMap.keySet()){
			String licencePlate = key.split("~")[0];
			String licenceColor = key.split("~")[1];
			String date = key.split("~")[2];
			String k = licencePlate+"~"+licenceColor;
			BdmVehicle bv = vehicleMap.get(k);
			if(bv == null){
				log.info("上下线信息存在，但车辆基础信息不存在："+k);
			}
			//加载基础数据
			Map rMap = new HashMap();
			rMap.put("vehicleId", ((bv == null) || (bv.getId() == null)) ? 0 : bv.getId());
			rMap.put("licencePlate",licencePlate);
			rMap.put("licenceColorCode",licenceColor);
			rMap.put("licenceColor",licenceColorMap.get(licenceColor));
			rMap.put("deptId",bv==null?(long)-1:bv.getDeptId());
			rMap.put("deptName",bv==null?"":deptMap.get(bv.getDeptId()+"")==null?"":deptMap.get(bv.getDeptId()+"").getDeptName());
			rMap.put("vehicleModelCode",bv==null?"":bv.getVehicleUseType());
			rMap.put("vehicleModel",bv==null?"":vehicleUseTypeMap.get(bv.getVehicleUseType()));
			rMap.put("vehicleOwnerId",bv==null?(long)-1:bv.getVehicleOwnerId());
			rMap.put("vehicleOwner",bv==null?"":platMap.get(bv.getVehicleOwnerId()+"")==null?"":platMap.get(bv.getVehicleOwnerId()+"").getName());
			rMap.put("accessModeCode",bv==null?"-1":bv.getAccessMode());
			rMap.put("accessMode",bv==null?"":accessModeMap.get(bv.getAccessMode()));
			//添加在线时长数据
			rMap.put(date,vMap.get(key));
			resList.add(rMap);
		}
		return resList;
	}


	private long updateBatchWithJDBC(List<CacheVehicleOnlineRate> updateList){
		String sql = "update cache_vehicle_online_rate set " +
			" enterprise_id = ? ," +
			" enterprise = ? ," +
			" dept_id = ? ," +
			" dept_name = ? ," +
			" licence_plate = ? ," +
			" licence_color_code = ? ," +
			" licence_color = ? ," +
			" vehicle_model_code = ? ," +
			" vehicle_model = ? ," +
			" vehicle_owner_id = ? ," +
			" vehicle_owner = ? ," +
			" access_mode_code = ? ," +
			" access_mode = ? ," +
			" month = ? ," +
			" d01 = ? ," +
			" d02 = ? ," +
			" d03 = ? ," +
			" d04 = ? ," +
			" d05 = ? ," +
			" d06 = ? ," +
			" d07 = ? ," +
			" d08 = ? ," +
			" d09 = ? ," +
			" d10 = ? ," +
			" d11 = ? ," +
			" d12 = ? ," +
			" d13 = ? ," +
			" d14 = ? ," +
			" d15 = ? ," +
			" d16 = ? ," +
			" d17 = ? ," +
			" d18 = ? ," +
			" d19 = ? ," +
			" d20 = ? ," +
			" d21 = ? ," +
			" d22 = ? ," +
			" d23 = ? ," +
			" d24 = ? ," +
			" d25 = ? ," +
			" d26 = ? ," +
			" d27 = ? ," +
			" d28 = ? ," +
			" d29 = ? ," +
			" d30 = ? ," +
			" d31 = ? ," +
			" note = ? , " +
			" vehicle_id = ?"
			+" where id = ? ";
		Connection connection = null;
		PreparedStatement stmt = null;
		try {
			connection = dataSource.getConnection();
			stmt = connection.prepareStatement(sql);
			// 关闭事务的自动提交
			connection.setAutoCommit(false);
			for(int i = 0; i < updateList.size(); i++){
				try {
					stmt.setLong(1, -1);
				}catch (Exception e){}
				try {
					stmt.setString(2, "");
				}catch (Exception e){}
				try{
					stmt.setLong(3, updateList.get(i).getDeptId());
				}catch (Exception e){}
				try{
					stmt.setString(4, updateList.get(i).getDeptName());
				}catch (Exception e){}
				try{
					stmt.setString(5, updateList.get(i).getLicencePlate());
				}catch (Exception e){}
				try{
					stmt.setString(6, updateList.get(i).getLicenceColorCode());
				}catch (Exception e){}
				try{
					stmt.setString(7, updateList.get(i).getLicenceColor());
				}catch (Exception e){}
				try{
					stmt.setString(8, updateList.get(i).getVehicleModelCode());
				}catch (Exception e){}
				try{
					stmt.setString(9, updateList.get(i).getVehicleModel());
				}catch (Exception e){}
				try{
					stmt.setLong(10, updateList.get(i).getVehicleOwnerId());
				}catch (Exception e){}
				try{
					stmt.setString(11, updateList.get(i).getVehicleOwner());
				}catch (Exception e){}
				try{
					stmt.setString(12, updateList.get(i).getAccessModeCode());
				}catch (Exception e){}
				try{
					stmt.setString(13, updateList.get(i).getAccessMode());
				}catch (Exception e){}
				try{
					stmt.setString(14, updateList.get(i).getMonth());
				}catch (Exception e){}
				stmt.setLong(15, updateList.get(i).getD01()==null?0:updateList.get(i).getD01());
				stmt.setLong(16, updateList.get(i).getD02()==null?0:updateList.get(i).getD02());
				stmt.setLong(17, updateList.get(i).getD03()==null?0:updateList.get(i).getD03());
				stmt.setLong(18, updateList.get(i).getD04()==null?0:updateList.get(i).getD04());
				stmt.setLong(19, updateList.get(i).getD05()==null?0:updateList.get(i).getD05());
				stmt.setLong(20, updateList.get(i).getD06()==null?0:updateList.get(i).getD06());
				stmt.setLong(21, updateList.get(i).getD07()==null?0:updateList.get(i).getD07());
				stmt.setLong(22, updateList.get(i).getD08()==null?0:updateList.get(i).getD08());
				stmt.setLong(23, updateList.get(i).getD09()==null?0:updateList.get(i).getD09());
				stmt.setLong(24, updateList.get(i).getD10()==null?0:updateList.get(i).getD10());
				stmt.setLong(25, updateList.get(i).getD11()==null?0:updateList.get(i).getD11());
				stmt.setLong(26, updateList.get(i).getD12()==null?0:updateList.get(i).getD12());
				stmt.setLong(27, updateList.get(i).getD13()==null?0:updateList.get(i).getD13());
				stmt.setLong(28, updateList.get(i).getD14()==null?0:updateList.get(i).getD14());
				stmt.setLong(29, updateList.get(i).getD15()==null?0:updateList.get(i).getD15());
				stmt.setLong(30, updateList.get(i).getD16()==null?0:updateList.get(i).getD16());
				stmt.setLong(31, updateList.get(i).getD17()==null?0:updateList.get(i).getD17());
				stmt.setLong(32, updateList.get(i).getD18()==null?0:updateList.get(i).getD18());
				stmt.setLong(33, updateList.get(i).getD19()==null?0:updateList.get(i).getD19());
				stmt.setLong(34, updateList.get(i).getD20()==null?0:updateList.get(i).getD20());
				stmt.setLong(35, updateList.get(i).getD21()==null?0:updateList.get(i).getD21());
				stmt.setLong(36, updateList.get(i).getD22()==null?0:updateList.get(i).getD22());
				stmt.setLong(37, updateList.get(i).getD23()==null?0:updateList.get(i).getD23());
				stmt.setLong(38, updateList.get(i).getD24()==null?0:updateList.get(i).getD24());
				stmt.setLong(39, updateList.get(i).getD25()==null?0:updateList.get(i).getD25());
				stmt.setLong(40, updateList.get(i).getD26()==null?0:updateList.get(i).getD26());
				stmt.setLong(41, updateList.get(i).getD27()==null?0:updateList.get(i).getD27());
				stmt.setLong(42, updateList.get(i).getD28()==null?0:updateList.get(i).getD28());
				stmt.setLong(43, updateList.get(i).getD29()==null?0:updateList.get(i).getD29());
				stmt.setLong(44, updateList.get(i).getD30()==null?0:updateList.get(i).getD30());
				stmt.setLong(45, updateList.get(i).getD31()==null?0:updateList.get(i).getD31());
				stmt.setString(46, updateList.get(i).getNote());
				try {
					stmt.setInt(47, updateList.get(i).getVehicleId());
				} catch (Exception e) {}
				stmt.setLong(48, updateList.get(i).getId());
				stmt.addBatch();
				if(i > 0 && i % 1000==0){
					stmt.executeBatch();
					connection.commit();
				}
			}

			// 执行批处理
			stmt.executeBatch();
			// 事务提交
			connection.commit();
		} catch (Exception e) {
			e.printStackTrace();
			return -1;
		}finally {
			if(stmt != null){
				try {
					stmt.close();
				} catch (SQLException e) {
					log.info("自定义批量提交，关闭stmt失败",e);
				}
			}
			if(connection != null){
				try {
					connection.setAutoCommit(true);
					connection.close();
				} catch (SQLException e) {
					log.info("自定义批量提交，关闭连接失败",e);
				}

			}
		}
		return updateList.size();
	}



	@Override
	public IPage<Map> queryVehicleOnlineRateCache(IPage page, VehicleOnlineRateRequest req) throws Exception {

		//1.获取车辆分页情况
		//根据开始时间和结束时间获取查询月份
		int monthCount = DateUtil.monthCountBetweenSecondTimestamp(req.getStartTime(), req.getEndTime());
		int daysCount = DateUtil.getDayCountBetweenSecondTimestamp(req.getStartTime(), req.getEndTime());
		List<String> months = DateUtil.getMonthList(req.getStartTime(), req.getEndTime());

		long start1 = System.currentTimeMillis();
		IPage<VehicleBase> pageList = baseMapper.getVehicleList(page, req, months);

		//如果没有查询到数据，则返回空
		if(pageList.getTotal() < 1){
			IPage<Map> retPageList = new Page<>();
			retPageList.setCurrent(page.getCurrent());
			retPageList.setSize(page.getSize());
			retPageList.setRecords(null);
			retPageList.setTotal(pageList.getTotal());
			return retPageList;
		}

		long end1 = System.currentTimeMillis();
		log.info("-=-=-=-=-=-=查询车辆列表耗时："+(end1 - start1));

		//2.查询每辆车的上线情况
		long start2 = System.currentTimeMillis();
		List<VehicleBase> list = pageList.getRecords();
		List<CacheVehicleOnlineRate> cacheList = new ArrayList<>();
		Set queryList = new HashSet();
		for(VehicleBase v : list){
            /*VehicleOnlineRateDateList vor = new VehicleOnlineRateDateList();
            vor.setLicencePlate(v.getLicencePlate());
            vor.setLicenceColor(v.getPlateColor());*/
			months.forEach(m -> {
				queryList.add(v.getLicencePlate()+"~"+v.getLicenceColor()+"~"+m);
			});
			//vor.setMonthList(monthList);
			//cacheList.addAll(baseMapper.getCacheVehicleOnlineRate(vor));
		}
		cacheList = baseMapper.getCacheVehicleOnlineRateWithCondition(queryList);
		long end2 = System.currentTimeMillis();
		log.info("-=-=-=-=-=-=-=-=-=-=查询车辆缓存耗时："+(end2 - start2));

		R<Map<String, String>> RVehicleUseType = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_VEHICLE_USE_TYPE, "-1");
		if (!RVehicleUseType.isSuccess()) {
			log.error("fail get dict of vehicle_use_type when queryVehicleOnlineRateCache: {}", RVehicleUseType.getMsg());
		}

		R<Map<String, String>> RAccessMode = this.dictBizClient.getDictTreeFlatByCodeAndKeyWithSelfMap(CommonConstant.DICT_ACCESS_MODE, "-1");
		if (!RAccessMode.isSuccess()) {
			log.error("fail get dict of access_mode when queryVehicleOnlineRateCache: {}", RVehicleUseType.getMsg());
		}

		Map<Long, BamThirdPartyPlatform> ownerMap = new HashMap<>();
		List<BamThirdPartyPlatform> ownerList = this.bamThirdPartyPlatformService.list();
		if (CollectionUtils.isNotEmpty(ownerList)) {
			ownerMap = ownerList.parallelStream().collect(Collectors.toMap(BamThirdPartyPlatform::getId, owner -> owner));
		}

		//3.整理上线数据
		//按照月份对车辆上线情况进行分组
		long start3 = System.currentTimeMillis();
		Map<String, List<CacheVehicleOnlineRate>> map = new HashMap<>();
		//resList中保存的为每辆车在每个月中的在线情况，如果查询的是2个月份的数据，那么每辆车会在list中有2条数据
		List<Map<String,Object>> resList = new ArrayList<>();

		Map<Long, BamThirdPartyPlatform> finalOwnerMap = ownerMap;
		cacheList.forEach(item -> {
			item.setVehicleModel(RVehicleUseType.getData().getOrDefault(item.getVehicleModelCode(), ""));
			item.setAccessMode(RAccessMode.getData().getOrDefault(item.getAccessModeCode(), ""));
			if (finalOwnerMap.containsKey(item.getVehicleOwnerId())) {
				BamThirdPartyPlatform btpp = finalOwnerMap.get(item.getVehicleOwnerId());
				if ((btpp != null) && StringUtils.isNotBlank(btpp.getName())) {
					item.setVehicleOwner(btpp.getName());
				} else {
					item.setVehicleOwner("非营运车辆");
				}
			} else {
				item.setVehicleOwner("非营运车辆");
			}

			String month = item.getMonth();
			List<CacheVehicleOnlineRate> tmpList = map.get(month);
			if(tmpList == null){
				tmpList = new ArrayList<>();
			}
			tmpList.add(item);
			map.put(month, tmpList);
		});

		//获取查询时间段
		String startDateStr = DateUtil.getDateString(req.getStartTime());
		String endDateStr = DateUtil.getDateString(req.getEndTime());
		int startDate = Integer.parseInt(startDateStr.substring(8,10));
		int endDate = Integer.parseInt(endDateStr.substring(8,10));


		if(months.size() == 1){
			String month = months.get(0);
			//如果是在同月
			List<Map<String,Object>> hashList = getCacheVehicleOnlineRateDataInMonth(cacheList, month, startDate, endDate);
			resList.addAll(hashList);

		}else if(months.size() > 1){
			//如果大于1个月
			//计算开头的月份
			String monthStart = months.get(0);
			int days = DateUtil.getDaysCountInMonth(monthStart);
			List<Map<String,Object>> hashListStart = getCacheVehicleOnlineRateDataInMonth(map.get(monthStart), monthStart, startDate, days);
			resList.addAll(hashListStart);

			//计算中间的月份
			for(int i = 1; i < months.size() - 1; i++){
				String monthM = months.get(i);
				int daysM = DateUtil.getDaysCountInMonth(monthM);
				List<Map<String,Object>> hashListM = getCacheVehicleOnlineRateDataInMonth(map.get(monthM), monthM, 1, daysM);
				resList.addAll(hashListM);
			}

			//计算结尾的月份
			String monthEnd = months.get(months.size() - 1);
			List<Map<String,Object>> hashListEnd = getCacheVehicleOnlineRateDataInMonth(map.get(monthEnd), monthEnd, 1, endDate);
			resList.addAll(hashListEnd);
		}

		//4.计算平均在线率（总在线时长/总时长）
		//总在线时长
		Map<String,Double> totalOnlineTime = new HashMap<>();
		Map<String,Map<String,Object>> resMap = new HashMap<>();
		//将相同车牌号、车牌颜色的车辆进行合并（多个月份的数据，合并为一条）
		for (Map<String, Object> item : resList) {
			String licencePlate = item.get(StatisticConstants.ONLINE_RATE_LICENCE_PLATE)==null?"":item.get(StatisticConstants.ONLINE_RATE_LICENCE_PLATE).toString();
			String licenceColor = item.get(StatisticConstants.ONLINE_RATE_LICENCE_COLOR)==null?"":item.get(StatisticConstants.ONLINE_RATE_LICENCE_COLOR).toString();
			Map<String, Object> tmpMap = resMap.get(licencePlate+"~"+licenceColor);
			Double tmpOnlineTime = totalOnlineTime.get(licencePlate+"~"+licenceColor);
			if(tmpMap == null){
				resMap.put(licencePlate+"~"+licenceColor, item);
			}else{
				//设置每天的上线率
				List<String> dayNum = (List)tmpMap.get("day_num");
				if(dayNum != null && dayNum.size() > 0){
					dayNum.addAll((List)item.get("day_num"));
				}
				List<String> tmpDayNum = new ArrayList<>();
				tmpDayNum.addAll(dayNum);
				tmpMap.putAll(item);
				tmpMap.put("day_num",tmpDayNum);
			}
			if(tmpOnlineTime == null){
				totalOnlineTime.put(licencePlate+"~"+licenceColor, (double)item.get(StatisticConstants.TOTAL_ONLINE_TIME));
			}else{
				totalOnlineTime.put(licencePlate+"~"+licenceColor, tmpOnlineTime.doubleValue() + (double)item.get(StatisticConstants.TOTAL_ONLINE_TIME));
			}
		}
		resMap.forEach((k,v) -> {
			double totalOnlineTimeItem = totalOnlineTime.get(k);
			String average = new BigDecimal(totalOnlineTimeItem*10000/(86400*(endDate - startDate + 1))).divide(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP).toString()+"%";
			v.put(StatisticConstants.AVERAGE_ONLINE_RATE, average);
			//删除不需要的字段
			v.remove("class");
			v.remove("note");
			v.remove("enterpriseId");
			//v.remove("licenceColorCode");
			v.remove("deptId");
			v.remove("id");
			v.remove("vehicleOwnerId");
			v.remove("vehicleModelCode");
			v.remove("accessModeCode");
			v.remove("createTime");
			v.remove("month");
			v.remove("totalOnlineTime");
			//删除 dxx 字段
			Set<String> set = new HashSet<String>(v.keySet());
			for(String key : set){
				if(key.indexOf("d") == 0 && key.length() == 3){
					v.remove(key);
				}else{
					//更改字段名称为下划线形式
					if(key.indexOf("_") < 0) {
						String keyLine = LineAndHumpUtil.humpToLine(key);
						v.put(keyLine, v.get(key));
						if(!key.equals(keyLine)){
							//如果原来字段中就不包含大写字母，则不进行替换
							v.remove(key);
						}
					}
				}
			}


		});

		List<Map> retList = new ArrayList<>(resMap.values());

		IPage<Map> retPageList = new Page<>();
		retPageList.setCurrent(page.getCurrent());
		retPageList.setSize(page.getSize());
		retPageList.setRecords(retList);
		retPageList.setTotal(pageList.getTotal());

		long end3 = System.currentTimeMillis();
		log.info("-=-=-=-=-=-=-=-=-=数据整理耗时："+(end3 - start3));

		return retPageList;
	}

	/**
	 * @description: 获取缓存数据
	 * @author: zhouxw
	 * @date: 2023-03-67 17:32:38
	 * @param: [cacheList 从数据库查询到的缓存数据, month 月份数据, startDate 开始日期, end 结束日期]
	 * @return: java.util.HashMap
	 **/
	private List<Map<String,Object>> getCacheVehicleOnlineRateDataInMonth(List<CacheVehicleOnlineRate> cacheList , String month, int startDate , int endDate){
		List<Map<String,Object>> resList = new ArrayList<>();
		if(cacheList == null || cacheList.size() < 1){
			return resList;
		}
		cacheList.forEach(item -> {
			try {
				Map<String,Object> tmpMap = BeanUtil.beanToMap(item);
				Map<String,Object> mapT = new HashMap<>();
				//复制基础数据
				for(String key : tmpMap.keySet()){
					if(!(key.length() == 3 && key.indexOf("d") == 0)){
						mapT.put(key, tmpMap.get(key));
					}
				}
				//添加在线率信息
				//在线天数
				int dayCount = 0;
				double totalOnlineTime = 0D;
				List<String> ratePercentList = new ArrayList<>();
				for(int i = startDate; i <= endDate; i++){
					String date = "";
					//在线时长
					long onlineTime = 0;
					if(i < 10){
						date = "0" + i;
						onlineTime = Integer.parseInt(tmpMap.get("d" + date)==null?"0":tmpMap.get("d" + date).toString());
					}else{
						date = "" + i;
						onlineTime = Integer.parseInt(tmpMap.get("d" + i)==null?"0":tmpMap.get("d" + i).toString());
					}
					if(onlineTime > 0){
						dayCount ++;
					}
					totalOnlineTime += (onlineTime>86400?86400:onlineTime);
					//在线率
					String onlineRate = new BigDecimal((onlineTime>86400?86400:onlineTime)*10000/86400).divide(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP).toString()+"%";
					mapT.put(month+"-"+date,onlineTime);
					ratePercentList.add(onlineRate);
				}
				//添加在线天数
				mapT.put(StatisticConstants.ONLINE_NUM, dayCount);
				//添加在线时长
				mapT.put(StatisticConstants.TOTAL_ONLINE_TIME, totalOnlineTime);
				//添加平均在线率
				//mapT.put(CommonConstants.AVERAGE_ONLINE_RATE,new BigDecimal(totalOnlineTime*10000/(86400*(endDate - startDate + 1))).divide(new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP).toString()+"%");
				//添加在线率列表
				mapT.put(StatisticConstants.DAY_NUM, ratePercentList);
				resList.add(mapT);
			} catch (Exception e) {
				log.error("bean 转 map 失败",e);
				throw new RuntimeException(e);
			}
		});
		return resList;
	}


}

