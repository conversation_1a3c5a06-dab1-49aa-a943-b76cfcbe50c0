package com.xh.vdm.statistic.entity;

import lombok.Data;

import java.util.List;

/**
 * @Description: 企业驾驶员基本信息
 * @Author: zhouxw
 * @Date: 2022/11/20 6:10 PM
 */
@Data
public class CompanyDriverBaseInfo {
    //驾驶员总数
    private int driverTotalCount;
    //从业资格证过去驾驶员数量
    private int driverCertExpirationCount;
    //从业资格证未过期驾驶员数量
    private int driverCertIntimeCount;
    //年龄分布
    private List<DescAndCount> ageList;
    //驾龄分布
    private List<DescAndCount> driveAgeList;
}
