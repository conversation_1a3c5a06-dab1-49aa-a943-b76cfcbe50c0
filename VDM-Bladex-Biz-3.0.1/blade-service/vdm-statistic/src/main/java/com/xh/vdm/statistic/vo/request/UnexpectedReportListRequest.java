package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.vo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "请求体：停报异动列表")
public class UnexpectedReportListRequest extends PageParam {

    private List<Long> deptList;
    private List<Long> professionList;

    @JsonProperty("dept_id")
    @ApiModelProperty(name = "dept_id", value = "车组ID列表，英文逗号分隔", example = "1,2,3", required = false)
    private String deptId;

    @JsonProperty("licence_plate")
    @ApiModelProperty(name = "licence_plate", value = "车牌号", example = "粤A12345", required = false)
    private String licencePlate;

    @JsonProperty("licence_color")
    @ApiModelProperty(name = "licence_color", value = "车牌颜色", example = "1", required = false)
    @DecimalMin(value = "1", message = "车牌颜色不正确。")
    private Integer licenceColor;

    @JsonProperty("vehicle_owner")
    @ApiModelProperty(name = "vehicle_owner", value = "车辆归属", example = "1", required = false)
    @DecimalMin(value = "1", message = "车辆归属不正确。")
    private Long vehicleOwnerId;

    @JsonProperty("vehicle_use_type")
    @ApiModelProperty(name = "vehicle_use_type", value = "行业类型", example = "1", required = false)
    @DecimalMin(value = "1", message = "行业类型不正确。")
    private Long vehicleUseType;

    @JsonProperty("access_mode")
    @ApiModelProperty(name = "access_mode", value = "车辆接入方式", example = "1", required = false)
    @DecimalMin(value = "1", message = "车辆接入方式不正确。")
    private Integer accessMode;

    @JsonProperty("start_time")
    @ApiModelProperty(name = "start_time", value = "开始时间", example = "1669824000", required = false)
    @DecimalMin(value = "0", message = "开始时间不正确。")
    private Long startTime;

    @JsonProperty("end_time")
    @ApiModelProperty(name = "end_time", value = "结束时间", example = "1675353599", required = false)
    @DecimalMin(value = "0", message = "结束时间不正确。")
    private Long endTime;
}
