package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.config.ExcelDateConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 车辆上下线查询返回类
 * <AUTHOR>
 * @date 2021/10/30 10:54
 */
@ApiModel(value = "车辆上下线查询返回类")
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(20)
@Data
public class VehicleOnlineOrOfflineResponse {


    @ApiModelProperty(value = "企业名称")
    @JsonProperty("dept_name")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆上下线查询统计报表", "企业名称"})
    @ColumnWidth(40)
    private String deptName;

    @ApiModelProperty(value = "车牌号码")
    @JsonProperty("licence_plate")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆上下线查询统计报表", "车牌号码"})
    @ColumnWidth(15)
    private String licencePlate;

    @ApiModelProperty(value = "车牌颜色")
    @JsonProperty("licence_color")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆上下线查询统计报表", "车牌颜色"})
    @ColumnWidth(15)
    private String licenceColor;

    @ApiModelProperty(value = "行业类型")
    @JsonProperty("vehicle_use_type")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆上下线查询统计报表", "行业类型"})
    @ColumnWidth(20)
    private String vehicleUseType;

    @ApiModelProperty(value = "车辆归属")
    @JsonProperty("vehicle_owner")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆上下线查询统计报表", "车辆归属"})
    @ColumnWidth(20)
    private String vehicleOwner;


    @ApiModelProperty(value = "车辆接入方式")
    @JsonProperty("access_mode")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆上下线查询统计报表", "车辆接入方式"})
    @ColumnWidth(15)
    private String accessMode;

    @ApiModelProperty(value = "在线状态")
    @JsonProperty("te_state")
    /*@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆上下线查询统计报表", "在线状态"})
    @ColumnWidth(15)*/
	@ExcelIgnore
    private String teState;

    @ApiModelProperty(value = "上线时间")
    @JsonProperty("online_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty(value = {"车辆上下线查询统计报表", "上线时间"}, converter = ExcelDateConverter.class)
    @ColumnWidth(22)
    private Date onLineTime;

    @ApiModelProperty(value = "离线时间")
    @JsonProperty("offline_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty(value = {"车辆上下线查询统计报表", "离线时间"}, converter = ExcelDateConverter.class)
    @ColumnWidth(22)
    private Date offLineTime;

//    @ApiModelProperty(value = "定位条数")
//    @JsonProperty("position_count")
//    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
//    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
//    @HeadFontStyle(fontHeightInPoints = 12)
//    @ContentFontStyle(fontHeightInPoints = 12)
//    @ExcelProperty({"车辆上下线查询统计报表", "定位条数"})
//    @ColumnWidth(15)
//    private Long positionCount;

    @ApiModelProperty(value = "驾驶员信息")
    @JsonProperty("driver_info")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆上下线查询统计报表", "驾驶员信息"})
    @ColumnWidth(20)
    private String driver;

    @ApiModelProperty(value = "查询时间")
    @JsonProperty("query_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty(value = {"车辆上下线查询统计报表", "查询时间"}, converter = ExcelDateConverter.class)
    @ColumnWidth(22)
    private Date checkTime;

	@JsonIgnore
	@ExcelIgnore
	private Long vehicleId;

	@JsonIgnore
	@ExcelIgnore
	private Long deptId;
	@JsonIgnore
	@ExcelIgnore
	private Long vehicleOwnerId;

}
