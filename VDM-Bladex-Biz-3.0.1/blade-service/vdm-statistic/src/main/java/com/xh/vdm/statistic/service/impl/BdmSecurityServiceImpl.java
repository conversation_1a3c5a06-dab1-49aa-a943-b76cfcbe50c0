package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.BdmSecurityMapper;
import com.xh.vdm.statistic.service.IBdmSecurityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.MathUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-09
 */
@Service
@Slf4j
public class BdmSecurityServiceImpl extends ServiceImpl<BdmSecurityMapper, BdmSecurity> implements IBdmSecurityService {

	public static final ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

	@Override
	public long findAlarmCountByDay(Long deptId , long secondTimestamp) throws Exception {
		try{
			//指定日期的开始时间戳
			long startTimestamp = DateUtil.getDayFirstSecondTimestamp(secondTimestamp );
			//指定日期的结束时间戳
			long endTimestamp = DateUtil.getDayLastSecondTimestamp(secondTimestamp );
			LambdaQueryWrapper<BdmSecurity> wrapper = Wrappers.lambdaQuery(BdmSecurity.class).ge(BdmSecurity::getAlarmTime , startTimestamp)
				.le(BdmSecurity::getAlarmTime , endTimestamp);
			if(deptId != null){
				wrapper.eq(BdmSecurity::getDeptId , deptId);
			}
			long count = baseMapper.selectCount(wrapper);
			return count;
		}catch (Exception e){
			log.error("根据时间戳[{}]查询预警次数失败" , secondTimestamp , e);
			throw e;
		}
	}

	@Override
	public double findAverageAlarmCountByMonth(Long deptId , long secondTimestampInTheMonth) throws Exception {
		try {
			//1.查询指定月份的总预警数量
			//查询指定月开始的时间戳
			long startTimestamp = DateUtil.getMonthFirstSecondTimestamp(secondTimestampInTheMonth);
			//查询指定月结束的时间戳
			long endTimestamp = DateUtil.getMonthLastSecondTimestamp(secondTimestampInTheMonth);
			LambdaQueryWrapper<BdmSecurity> wrapper = Wrappers.lambdaQuery(BdmSecurity.class).ge(BdmSecurity::getAlarmTime , startTimestamp)
				.le(BdmSecurity::getAlarmTime , endTimestamp);
			if(deptId != null){
				wrapper.eq(BdmSecurity::getDeptId , deptId);
			}
			long alarmCount = baseMapper.selectCount(wrapper);

			//2.查询指定月份的天数
			int dayCount = DateUtil.getDayCountBetweenSecondTimestamp(startTimestamp , endTimestamp);

			//3.计算平均预警次数
			double average = MathUtil.divideRoundDouble(alarmCount , dayCount , 2);

			return average;
		}catch (Exception e){
			log.error("根据给定时间戳[{}]查询平均预警次数报错", secondTimestampInTheMonth , e);
			throw e;
		}
	}

	@Override
	public List<DateAndData> findMonthTrend(Long deptId , String month) throws Exception {
		month = month + "-01";
		Date date = sdfHolder.get().parse(month);
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		List<DateAndData> list = new ArrayList<>();
		for(int i = 0 ; i < 12 ; i++){
			cal.add(Calendar.MONTH , -1);
			int mon = cal.get(Calendar.MONTH) + 1;
			String monStr = mon < 10 ? "0"+mon : mon + "";
			String monthStr = cal.get(Calendar.YEAR) + "-" + monStr;
			DateAndData dc = new DateAndData();
			dc.setStatDate(monthStr);
			dc.setData(findAverageAlarmCountByMonth(deptId , cal.getTimeInMillis() / 1000));
			list.add(dc);
		}
		//对list进行排序
		Collections.sort(list , Comparator.comparingInt(o -> Integer.parseInt(o.getStatDate().replace("-", ""))));
		return list;
	}

	@Override
	public List<AlarmTypeAndCount> findAlarmTypeAndCountEveryDayInMonth(Long deptId , String month) throws Exception {
		//计算指定月份的开始时间戳和结束时间戳
		Date date = sdfHolder.get().parse(month + "-01");
		long startSecondTime = DateUtil.getMonthFirstSecondTimestamp(date.getTime() / 1000);
		long endSecondTime = DateUtil.getMonthLastSecondTimestamp(date.getTime() / 1000);
		return baseMapper.getAlarmTypeAndCount(deptId , startSecondTime , endSecondTime);
	}

	@Override
	public List<BdmSecurity> findSecurityByIdCardAndDate(String idCard, long startSecondTimestamp, long endSecondTimestamp) throws Exception {
		return baseMapper.getSecurityByIdCardAndDate(idCard , startSecondTimestamp , endSecondTimestamp);
	}

	@Override
	public List<AlarmTypeAndDateAndIdCard> findEveryAlarmTypeByIdCard(String idCard, long startSecondTimestamp, long endSecondTimestamp) throws Exception {
		return baseMapper.getEveryAlarmTypeByIdCard(idCard , startSecondTimestamp , endSecondTimestamp);
	}

	@Override
	public List<DateAndCount> findAlarmCountEveryDay(String idCard, long startSecondTimestamp, long endSecondTimestamp) throws Exception{
		return baseMapper.getAlarmCountEveryDay(idCard , startSecondTimestamp ,endSecondTimestamp);
	}

	@Override
	public long findAlarmCountForDept(Long deptId, long startSecondTimestamp, long endSecondTimestamp) throws Exception {
		return baseMapper.getAlarmCountForDept(deptId , startSecondTimestamp , endSecondTimestamp);
	}
}
