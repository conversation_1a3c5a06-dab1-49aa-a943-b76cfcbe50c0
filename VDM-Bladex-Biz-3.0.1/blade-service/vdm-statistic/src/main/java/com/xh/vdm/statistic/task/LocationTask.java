package com.xh.vdm.statistic.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.BdmLocationHarvest;
import com.xh.vdm.statistic.entity.BdmLocationShare;
import com.xh.vdm.statistic.entity.BdmTargetOdometer;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.LogUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.common.constant.RedisConstant;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 定位相关定时任务
 * @Author: zhouxw
 * @Date: 2022/9/1 9:33 AM
 */
@Component
@Slf4j
public class LocationTask {

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

    @Resource
    private ILocationQualityService locationQualityService;

    @Resource
    private IStatCompleteService completeService;

    @Resource
    private IStatDriftService driftService;

	@Resource
	private IStatVehOnlineDayService statVehOnlineDayService;

	@Resource
	private IBdmTargetOdometerService bdmTargetOdometerService;

	@Resource
	private IBdmLocationHarvestService bdmLocationHarvestService;

	@Resource
	private IBdmLocationShareService bdmLocationShareService;

    @Resource
    private LogUtil logUtil;

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * @description: 定位数据质量统计 定时任务
     * 统计前一天的定位数据，将每辆车上报数据的质量信息记录到表中
     * 定位数据允许有8个小时的补传，所以，数据的处理应当在当天的8点之后
     * 每天 10 点执行
     * 开发环境中：location原始数据在 DataMine 数据库中，数据质量信息 在 taxicab 库中
     * @author: zhouxw
     * @date: 2022/9/1 9:36 AM
     * @param: []
     * @return: void
     **/
    //@Scheduled(cron="0 0 10 * * ?")
	//@XxlJob("locationQualityStat")
    public void locationQualityStat(){
		XxlJobHelper.log("将要开始执行定时任务：locationQualityStat");

        //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
        Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_QUALITY);
        if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
            //如果已经有跑批程序在执行，则不再执行
            log.info("[定位数据质量统计]跑批任务已经在执行，请等当前执行程序完成后再试");
            return ;
        }

        //获取执行权限之后，添加执行标记（有效期为1个小时）
        synchronized (this){
            stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_QUALITY , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
        }

        //1。统计前一天的定位数据的数据质量
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_YEAR , -1);
        String day = sdf.format(cal.getTime());
        long start = 0;
        long end = 0;
        boolean success = false;
        try {
            start = System.currentTimeMillis();
            success = locationQualityService.locationQualityStat(day);
            end = System.currentTimeMillis();
            if(success){
                //如果定时任务执行成功
                log.info("[定位数据质量统计-{}]定时任务 执行完成，共耗时{}毫秒" , day , end - start);
                logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_QUALITY,"定位数据质量统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
            }else{
                log.error("[定位数据质量统计-{}]定时任务 执行失败" , day );
                logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_QUALITY,"定位数据质量统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败");
            }
        } catch (Exception e) {
            log.error("[定位数据质量统计-{}]定时任务 执行失败 ，发生异常" , day , e);
            e.printStackTrace();
            logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_QUALITY,"定位数据质量统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行失败，发生异常："+e.getMessage());
        }

        //执行完毕后，解除执行锁定
        synchronized (this){
            stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_QUALITY );
        }

		XxlJobHelper.log("定时任务执行完毕：locationQualityStat");


    }


    /**
     * @description: 轨迹完整率 定时任务
     * 统计前一天的定位数据，将每辆车上报数据的轨迹完整率信息记录到表中
     * 定位数据允许有8个小时的补传，所以，数据的处理应当在当天的8点之后
     * 每天 12 点执行
     * 开发环境中：location原始数据在 DataMine 数据库中，数据质量信息 在 taxicab 库中
     * @author: zhouxw
     * @date: 2022/9/1 9:36 AM
     * @param: []
     * @return: void
     **/
    //@Scheduled(cron="0 0 12 * * ?")
	//@XxlJob("locationCompleteStat")
    public void locationCompleteStat(){

		XxlJobHelper.log("将要开始执行定时任务：locationCompleteStat");
        //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
        Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_COMPLETE);
        if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
            //如果已经有跑批程序在执行，则不再执行
            log.info("[轨迹完整率]跑批程序正在执行，请等待当前程序执行完毕后再试！");
        }

        //获取执行权限之后，添加执行标记（有效期为1个小时）
        synchronized (this){
            stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_COMPLETE , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
        }

        //1。统计前一天的定位数据的轨迹完整率
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_YEAR , -1);
        String day = sdf.format(cal.getTime());
        long start = 0;
        long end = 0;
        try {
            start = System.currentTimeMillis();
            boolean success = completeService.locationCompleteStat(day);
            end = System.currentTimeMillis();
            if(success){
                //如果定时任务执行成功
                log.info("[轨迹完整率统计-{}]定时任务 执行完成，共耗时{}毫秒" , day , end - start);
                logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_COMPLETE,"轨迹完整率统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
            }else{
                log.error("[轨迹完整率统计-{}]定时任务 执行失败" , day);
                logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_COMPLETE,"轨迹完整率统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败");
            }
        } catch (Exception e) {
            log.error("轨迹完整率统计-{}]定时任务 执行失败 ，发生异常" , day , e);
            e.printStackTrace();
            logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_COMPLETE,"轨迹完整率统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败，发生异常："+e.getMessage());
        }

        //执行完毕后，解除执行锁定
        synchronized (this){
            stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_COMPLETE );
        }


		XxlJobHelper.log("定时任务执行完毕：locationCompleteStat");

    }


    /**
     * @description: 漂移率 定时任务
     * 统计前一天的定位数据，将每辆车上报数据的漂移率信息记录到表中
     * 定位数据允许有8个小时的补传，所以，数据的处理应当在当天的8点之后
     * 每天 14 点执行
     * 开发环境中：location原始数据在 DataMine 数据库中，数据质量信息 在 taxicab 库中
     * @author: zhouxw
     * @date: 2022/9/1 9:36 AM
     * @param: []
     * @return: void
     **/
    //@Scheduled(cron="0 0 14 * * ?")
	//@XxlJob("locationDriftStat")
    public void locationDriftStat(){

		XxlJobHelper.log("将要开始执行定时任务：locationDriftStat");

        //为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
        Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_DRIFT);
        if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
            //如果已经有跑批程序在执行，则不再执行
            log.info("[漂移率]跑批程序正在执行，请等待当前程序执行完毕后再试！");
        }

        //获取执行权限之后，添加执行标记（有效期为1个小时）
        synchronized (this){
            stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_DRIFT , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
        }

        //1。统计前一天的定位数据的漂移情况
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.DAY_OF_YEAR , -1);
        String day = sdf.format(cal.getTime());
        long start = 0;
        long end = 0;
        try {
            start = System.currentTimeMillis();
            boolean success = driftService.locationDriftStat(day);
            end = System.currentTimeMillis();
            if(success){
                //如果定时任务执行成功
                log.info("[漂移率率统计-{}]定时任务 执行完成，共耗时{}毫秒" , day , end - start);
                logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_DRIFT,"漂移统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
            }else{
                log.error("[漂移率统计-{}]定时任务 执行失败" , day);
                logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_DRIFT,"漂移统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败");
            }
        } catch (Exception e) {
            log.error("漂移率统计-{}]定时任务 执行失败 ，发生异常" , day , e);
            e.printStackTrace();
            logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_DRIFT,"漂移统计：定时执行跑批任务",day ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败，发生异常："+e.getMessage());
        }

        //执行完毕后，解除执行锁定
        synchronized (this){
            stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_LOCATION_DRIFT );
        }

		XxlJobHelper.log("定时任务执行完毕：locationDriftStat");
    }

	/**
	 * 车辆每日在线时长统计
	 * 通过车辆上下线表来统计每辆车每个小时的在线情况
	 * 表中只记录当日的数据
	 * 每20分钟执行一次
	 */
	//@XxlJob("vehOnlineDayStat")
	public void vehOnlineDayStat(){

		XxlJobHelper.log("将要开始执行定时任务：vehOnlineDayStat");

		//为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
		Object executingFlag = stringRedisTemplate.opsForValue().get(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_VEH_ONLINE_DAY);
		if(executingFlag != null && StatisticConstants.COMMON_TRUE.equals(executingFlag.toString())){
			//如果已经有跑批程序在执行，则不再执行
			log.info("[车辆每日在线时长统计]跑批程序正在执行，请等待当前程序执行完毕后再试！");
		}

		//获取执行权限之后，添加执行标记（有效期为1个小时）
		synchronized (this){
			stringRedisTemplate.opsForValue().set(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_VEH_ONLINE_DAY , StatisticConstants.COMMON_TRUE , 1 , TimeUnit.HOURS);
		}

		//统计车辆在线时长情况
		long start = 0;
		long end = 0;
		try {
			start = System.currentTimeMillis();
			statVehOnlineDayService.statVehOnlineDay();
			end = System.currentTimeMillis();
				//如果定时任务执行成功
				log.info("[车辆每日在线时长统计]定时任务 执行完成，共耗时{}毫秒"  , end - start);
				logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_DRIFT,"车辆每日在线时长统计：定时执行跑批任务","" ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_SUCCESS,"执行成功");
		} catch (Exception e) {
			log.error("车辆每日在线时长统计]定时任务 执行失败 ，发生异常"  , e);
			logUtil.insertStatLog(StatisticConstants.TASK_LOCATION_DRIFT,"车辆每日在线时长统计：定时执行跑批任务","" ,new Date(start),new Date(end), StatisticConstants.TASK_EXECUTE_RESULT_FAIL,"执行失败，发生异常："+e.getMessage());
		}

		//执行完毕后，解除执行锁定
		synchronized (this){
			stringRedisTemplate.delete(StatisticConstants.TASK_EXECUTING_PREFIX+ StatisticConstants.TASK_VEH_ONLINE_DAY );
		}

		XxlJobHelper.log("定时任务执行完毕：vehOnlineDayStat");
	}


	/**
	 * 从缓存同步累计里程数据至数据库
	 * 没分钟执行一次
	 */
	//@XxlJob("syncTargetOdometerFromCacheToDB")
	@Scheduled(cron="0 * * * * ?")
	public void syncTargetOdometerFromCacheToDB () {

		//为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
		Object executingFlag = stringRedisTemplate.opsForValue().get(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SYNC_TARGET_ODOMETER_FROM_CACHE_TO_DB);
		if(executingFlag != null && CommonConstant.COMMON_TRUE.equals(executingFlag.toString())){
			//如果已经有跑批程序在执行，则不再执行
			log.info("[syncTargetOdometerFromCacheToDB]跑批任务已经在执行，请等当前执行程序完成后再试");
			return ;
		}

		//获取执行权限之后，添加执行标记（有效期为5分钟）
		synchronized (this){
			stringRedisTemplate.opsForValue().set(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SYNC_TARGET_ODOMETER_FROM_CACHE_TO_DB , CommonConstant.COMMON_TRUE , 5 , TimeUnit.MINUTES);
		}
		Map<Object, Object> targetOdometerMap = new HashMap<>();
		Map<Object, Object> targetMap = new HashMap<>();
		try{
			if (
				Boolean.FALSE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_TARGET_ODOMETER)) ||
					(targetOdometerMap = this.redisTemplate.opsForHash().entries(RedisConstant.HASH_TARGET_ODOMETER)).isEmpty()
			) {
				return;
			}

			targetMap =
				Boolean.TRUE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_BASE_INFO_TARGET)) ?
					this.redisTemplate.opsForHash().entries(RedisConstant.HASH_BASE_INFO_TARGET) :
					new HashMap<>();

			String k;
			String[] ka;
			Object targetOdometerO;
			JSONObject targetOdometerJ;
			Object targetO;
			JSONObject targetJ;
			byte targetType;
			long targetId;
			long deptId;
			String targetKey;
			BdmTargetOdometer tmp;
			BdmTargetOdometer bdmTargetOdometer;
			for (Object ok : targetOdometerMap.keySet()) {
				k = ok.toString();
				targetOdometerO = targetOdometerMap.get(k);
				targetOdometerJ = JSON.parseObject(JSON.toJSONString(targetOdometerO));
				if (targetOdometerJ == null) {
					log.error("fail parse cache data when sync target odometer: {}", targetOdometerO);
					continue;
				}

				ka = k.split("-");
				targetType = Byte.parseByte(ka[0]);
				targetId = Long.parseLong(ka[1]);
				deptId = Long.parseLong(ka[2]);
				targetKey = ka[0] + "-" + ka[1];
				tmp = this.bdmTargetOdometerService.getOne(
					Wrappers.lambdaQuery(BdmTargetOdometer.class)
						.eq(BdmTargetOdometer::getTargetType, targetType)
						.eq(BdmTargetOdometer::getTargetId, targetId)
						.eq(BdmTargetOdometer::getDeptId, deptId)
				);

				bdmTargetOdometer = (tmp == null) ? new BdmTargetOdometer() : tmp;
				bdmTargetOdometer.setOdometer(targetOdometerJ.getDouble("odometer"));
				bdmTargetOdometer.setLastLon(targetOdometerJ.getDouble("last_lon"));
				bdmTargetOdometer.setLastLat(targetOdometerJ.getDouble("last_lat"));
				bdmTargetOdometer.setLastAlt(targetOdometerJ.getShort("last_alt"));
				bdmTargetOdometer.setLastTime(targetOdometerJ.getLong("last_time"));
				if (tmp == null) {
					bdmTargetOdometer.setTargetType(targetType);
					bdmTargetOdometer.setTargetId(targetId);
					bdmTargetOdometer.setDeptId(deptId);
					if (targetMap.containsKey(targetKey)) {
						targetO = targetMap.get(targetKey);
						if (targetO != null) {
							targetJ = JSON.parseObject(targetO.toString());
							if (targetJ != null) {
								bdmTargetOdometer.setTargetCategory(targetJ.getShort("targetCategory"));
							}
						}
					}

					this.bdmTargetOdometerService.save(bdmTargetOdometer);
				} else {
					this.bdmTargetOdometerService.updateById(bdmTargetOdometer);
				}
			}
			log.info("定时任务【syncTargetOdometerFromCacheToDB】执行完成，将要清理内存");
		}catch (Exception e){
			log.error("定时任务【syncTargetOdometerFromCacheToDB】执行失败",e);
		}finally {
			if(targetOdometerMap != null){
				targetOdometerMap.clear();
				targetOdometerMap = null;
			}
			if(targetMap != null){
				targetMap.clear();
				targetMap = null;
			}
			System.gc();
			//执行完毕后，解除执行锁定
			synchronized (this){
				stringRedisTemplate.delete(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SYNC_TARGET_ODOMETER_FROM_CACHE_TO_DB );
			}
		}


	}

	/**
	 * 从缓存同步定位采集数据至数据库
	 * 每分钟执行一次
	 */
	//@XxlJob("syncLocCollectFromCacheToDB")
	@Scheduled(cron="0 * * * * ?")
	public void syncLocCollectFromCacheToDB () {

		//为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
		Object executingFlag = stringRedisTemplate.opsForValue().get(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SYNC_LOC_COLLECT_FROM_CACHE_TO_DB);
		if(executingFlag != null && CommonConstant.COMMON_TRUE.equals(executingFlag.toString())){
			//如果已经有跑批程序在执行，则不再执行
			log.info("[syncLocCollectFromCacheToDB]跑批任务已经在执行，请等当前执行程序完成后再试");
			return ;
		}

		//获取执行权限之后，添加执行标记（有效期为5分钟）
		synchronized (this){
			stringRedisTemplate.opsForValue().set(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SYNC_LOC_COLLECT_FROM_CACHE_TO_DB , CommonConstant.COMMON_TRUE , 5 , TimeUnit.MINUTES);
		}
		Map<Object, Object> locCollectMap = new HashMap<>();
		Map<Object, Object> targetMap = new HashMap<>();
		try{

			if (
				Boolean.FALSE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_TARGET_LOCATION_COLLECT)) ||
					(locCollectMap = this.redisTemplate.opsForHash().entries(RedisConstant.HASH_TARGET_LOCATION_COLLECT)).isEmpty()
			) {
				return;
			}

			targetMap =
				Boolean.TRUE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_BASE_INFO_TARGET)) ?
					this.redisTemplate.opsForHash().entries(RedisConstant.HASH_BASE_INFO_TARGET) :
					new HashMap<>();

			String k;
			String[] ka;
			Object locCollectO;
			JSONObject locCollectJ;
			Object targetO;
			JSONObject targetJ;
			byte targetType;
			long targetId;
			long deptId;
			String targetKey;
			BdmLocationHarvest tmp;
			BdmLocationHarvest bdmLocationHarvest;
			for (Object ok : locCollectMap.keySet()) {
				k = ok.toString();
				locCollectO = locCollectMap.get(k);
				locCollectJ = JSON.parseObject(JSON.toJSONString(locCollectO));
				if (locCollectJ == null) {
					log.error("fail parse cache data when sync loc collect: {}", locCollectO);
					continue;
				}

				ka = k.split("-");
				targetType = Byte.parseByte(ka[0]);
				targetId = Long.parseLong(ka[1]);
				deptId = Long.parseLong(ka[2]);
				targetKey = ka[0] + "-" + ka[1];
				tmp = this.bdmLocationHarvestService.getOne(
					Wrappers.lambdaQuery(BdmLocationHarvest.class)
						.eq(BdmLocationHarvest::getTargetType, targetType)
						.eq(BdmLocationHarvest::getTargetId, targetId)
						.eq(BdmLocationHarvest::getDeptId, deptId)
				);

				bdmLocationHarvest = (tmp == null) ? new BdmLocationHarvest() : tmp;
				bdmLocationHarvest.setHarvest(locCollectJ.getLongValue("harvest"));
				bdmLocationHarvest.setMonthHarvest(locCollectJ.getIntValue("month_harvest"));
				if (tmp == null) {
					bdmLocationHarvest.setTargetType(targetType);
					bdmLocationHarvest.setTargetId(targetId);
					bdmLocationHarvest.setDeptId(deptId);
					if (targetMap.containsKey(targetKey)) {
						targetO = targetMap.get(targetKey);
						if (targetO != null) {
							targetJ = JSON.parseObject(targetO.toString());
							if (targetJ != null) {
								bdmLocationHarvest.setTargetCategory(targetJ.getShort("targetCategory"));
							}
						}
					}

					this.bdmLocationHarvestService.save(bdmLocationHarvest);
				} else {
					this.bdmLocationHarvestService.updateById(bdmLocationHarvest);
				}
			}
			log.info("定时任务【syncLocCollectFromCacheToDB】执行完成，将要清理内存");
		}catch (Exception e){
			log.error("定时任务【syncLocCollectFromCacheToDB】执行失败",e);
		}finally {
			if(locCollectMap != null){
				locCollectMap.clear();
				locCollectMap = null;
			}
			if(targetMap != null){
				targetMap.clear();
				targetMap = null;
			}
			System.gc();
			//执行完毕后，解除执行锁定
			synchronized (this){
				stringRedisTemplate.delete(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SYNC_LOC_COLLECT_FROM_CACHE_TO_DB );
			}
		}




	}

	/**
	 * 从缓存同步定位共享数据至数据库
	 * 每分钟执行一次
	 */
	//@XxlJob("syncLocShareFromCacheToDB")
	@Scheduled(cron="0 * * * * ?")
	public void syncLocShareFromCacheToDB () {

		//为防止同一时刻多个执行程序相互影响（删除原有记录、清空临时表等），添加跑批执行标记
		Object executingFlag = stringRedisTemplate.opsForValue().get(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SYNC_LOC_SHARE_FROM_CACHE_TO_DB);
		if(executingFlag != null && CommonConstant.COMMON_TRUE.equals(executingFlag.toString())){
			//如果已经有跑批程序在执行，则不再执行
			log.info("[syncLocShareFromCacheToDB]跑批任务已经在执行，请等当前执行程序完成后再试");
			return ;
		}

		//获取执行权限之后，添加执行标记（有效期为5分钟）
		synchronized (this){
			stringRedisTemplate.opsForValue().set(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SYNC_LOC_SHARE_FROM_CACHE_TO_DB , CommonConstant.COMMON_TRUE , 5 , TimeUnit.MINUTES);
		}
		Map<Object, Object> locShareMap = new HashMap<>();
		Map<Object, Object> targetMap = new HashMap<>();
		try{
			if (
				Boolean.FALSE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_TARGET_LOCATION_SHARE)) ||
					(locShareMap = this.redisTemplate.opsForHash().entries(RedisConstant.HASH_TARGET_LOCATION_SHARE)).isEmpty()
			) {
				return;
			}

			targetMap =
				Boolean.TRUE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_BASE_INFO_TARGET)) ?
					this.redisTemplate.opsForHash().entries(RedisConstant.HASH_BASE_INFO_TARGET) :
					new HashMap<>();

			String k;
			String[] ka;
			Object locShareO;
			JSONObject locShareJ;
			Object targetO;
			JSONObject targetJ;
			byte targetType;
			long targetId;
			long deptId;
			String targetKey;
			BdmLocationShare tmp;
			BdmLocationShare bdmLocationShare;
			for (Object ok : locShareMap.keySet()) {
				k = ok.toString();
				locShareO = locShareMap.get(k);
				locShareJ = JSON.parseObject(JSON.toJSONString(locShareO));
				if (locShareJ == null) {
					log.error("fail parse cache data when sync loc share: {}", locShareO);
					continue;
				}

				ka = k.split("-");
				targetType = Byte.parseByte(ka[0]);
				targetId = Long.parseLong(ka[1]);
				deptId = Long.parseLong(ka[2]);
				targetKey = ka[0] + "-" + ka[1];
				tmp = this.bdmLocationShareService.getOne(
					Wrappers.lambdaQuery(BdmLocationShare.class)
						.eq(BdmLocationShare::getTargetType, targetType)
						.eq(BdmLocationShare::getTargetId, targetId)
						.eq(BdmLocationShare::getDeptId, deptId)
				);

				bdmLocationShare = (tmp == null) ? new BdmLocationShare() : tmp;
				bdmLocationShare.setQuantity(locShareJ.getLongValue("quantity"));
				if (tmp == null) {
					bdmLocationShare.setTargetType(targetType);
					bdmLocationShare.setTargetId(targetId);
					bdmLocationShare.setDeptId(deptId);
					if (targetMap.containsKey(targetKey)) {
						targetO = targetMap.get(targetKey);
						if (targetO != null) {
							targetJ = JSON.parseObject(targetO.toString());
							if (targetJ != null) {
								bdmLocationShare.setTargetCategory(targetJ.getShort("targetCategory"));
							}
						}
					}

					this.bdmLocationShareService.save(bdmLocationShare);
				} else {
					this.bdmLocationShareService.updateById(bdmLocationShare);
				}
			}
			log.info("定时任务【syncLocShareFromCacheToDB】执行完成，将要清理内存");
		}catch (Exception e){
			log.error("定时任务【syncLocShareFromCacheToDB】执行失败",e);
		}finally {
			if(locShareMap != null){
				locShareMap.clear();
				locShareMap = null;
			}
			if(targetMap != null){
				targetMap.clear();
				targetMap = null;
			}
			System.gc();
			//执行完毕后，解除执行锁定
			synchronized (this){
				stringRedisTemplate.delete(CommonConstant.TASK_EXECUTING_PREFIX+ CommonConstant.SYNC_LOC_SHARE_FROM_CACHE_TO_DB );
			}
		}




	}
}
