package com.xh.vdm.statistic.vo.response;

import lombok.Data;

/**
 * @Description: 主页--基本情况统计
 * @Author: zhouxw
 * @Date: 2023/6/13 19:17
 */
@Data
public class MonitorVehicleResponse {

	//车辆总数
	private Long vehicleCount = 0L;

	//营运车辆数
	private Long runningVehicleCount = 0L;

	//上线车辆数
	private Long goOnlineVehicleCount = 0L;

	//在线车辆数
	private Long onlineVehicleCount = 0L;

	//离线车辆数
	private Long offlineVehicleCount = 0L;

	//停运车辆数
	private Long stopRunningVehicleCount = 0L;

	//上线率
	private Double goOnlineRate = 0D;

	//上线率字符串表示
	private String goOnlineRateStr;

	//在线率
	private Double onlineRate = 0D;

	//在线率字符串表示
	private String onlineRateStr;
}
