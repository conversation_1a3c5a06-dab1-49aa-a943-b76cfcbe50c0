package com.xh.vdm.statistic.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.xh.vdm.statistic.constant.AlarmConstant;
import com.xh.vdm.statistic.constant.RedisConstants;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.dto.TargetAlarmDTO;
import com.xh.vdm.statistic.dto.alarm.AlarmCount;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.impala.ImpalaAlarm;
import com.xh.vdm.statistic.mapper.AlarmMapper;
import com.xh.vdm.statistic.mapper.BdmAbstractDeviceMapper;
import com.xh.vdm.statistic.mapper.BdmAbstractTargetMapper;
import com.xh.vdm.statistic.mapper.impala.ImpalaAlarmMapper;
import com.xh.vdm.statistic.mapper.impala.ImpalaAlarmMapper2;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.utils.VdmUserInfoUtil;
import com.xh.vdm.statistic.vo.request.CommonBaseImpalaAlarmRequest;
import com.xh.vdm.statistic.vo.request.CommonStatRequest;
import com.xh.vdm.statistic.vo.request.alarm.CarAlarmRequest;
import com.xh.vdm.statistic.vo.request.alarm.CommonAlarmStatRequest;
import com.xh.vdm.statistic.vo.request.alarm.DriverAlarmRequest;
import com.xh.vdm.statistic.vo.response.AlarmAndHandleCountResponse;

import com.xh.vdm.biapi.entity.BdmAbstractTarget;

import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.RedisConstant;
import org.springblade.common.utils.DateUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.tool.api.R;
import org.springblade.entity.DataAuthCE;
import org.springblade.system.cache.SysCache;
import org.springblade.system.entity.DeptNode;
import org.springblade.system.feign.IDictBizClient;
import org.springblade.system.vo.DictTreeNodeVO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.*;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AlarmServiceImpl implements IAlarmService {

	private int numVehiclePerSlice = 100;

	@Resource
	private AlarmMapper mapper;

	@Resource
	private ImpalaAlarmMapper impalaAlarmMapper;

	@Resource
	private ImpalaAlarmMapper2 impalaAlarmMapper2;

	@Autowired
	private VdmUserInfoUtil userInfoUtil;

	@Resource
	private IDictBizClient dictBizClient;

	@Resource
	private RedisTemplate<String, Object> redisTemplate;


	@Resource
	private IStatCountRateUserService scrService;

	@Resource
	private IBladeUserService userService;

	@Resource
	private IVehicleStatService vehicleStatService;

	@Resource
	private IStatCompleteService statCompleteService;

	@Resource
	private ILocationQualityService locationQualityService;

	@Resource
	private IStatDriftService driftService;

	@Resource
	private IBdmVehicleService bdmVehicleService;
	@Resource
	private StringRedisTemplate stringRedisTemplate;

	@Resource
	private BdmAbstractDeviceMapper abstractDeviceMapper;

	@Resource
	private BdmAbstractTargetMapper bdmAbstractTargetMapper;

	private final ThreadPoolExecutor threadPool = new ThreadPoolExecutor(
		20, 50, 120, TimeUnit.SECONDS, new LinkedBlockingQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy()
	);

	@Override
	public List<DateAndCount> findAlarmCount(Long startTime, Long endTime, List<Long> deptIds, Long userId) {
		return mapper.getAlarmCount(startTime, endTime, deptIds, userId);
	}

	@Override
	public List<DateAndCount> findAlarmCountHour(Long startTime, Long endTime, List<Long> deptIds, Long userId) {
		return mapper.getAlarmCountHour(startTime, endTime, deptIds, userId);
	}

	@Override
	public List<DateAndCount> findAlarmHandleCount(Long startTime, Long endTime, List<Long> deptIds, Long userId, String userType) {
		return mapper.getHandleCount(startTime, endTime, deptIds, userId, userType);
	}

	@Override
	public long findAlarmHandleCountTotal(Long startTime, Long endTime, List<Long> deptIds, Long userId, String userType) {
		return mapper.getHandleCountTotal(startTime, endTime, deptIds, userId, userType);
	}

	@Override
	public IPage<BdmSecurity> findUnHandleRealTimeAlarmByPage(List<String> alarmTypeList, List<Long> deptIds, Long userId, String userType, IPage<BdmSecurity> page) throws Exception {
		//查询一个小时内的未处理报警信息
		long endTime = new Date().getTime() / 1000;
		long startTime = endTime - 3600;
		return mapper.getUnHandleRealTimeAlarmByPage(alarmTypeList, startTime,endTime,deptIds,userId,userType, page);
	}

	@Override
	public List<DateAndCount> findAlarmHandleCountHour(Long startTime, Long endTime, List<Long> deptIds, Long userId, String userType) {
		return mapper.getHandleCountHour(startTime, endTime, deptIds, userId, userType);
	}


	@Override
	public void statCountAndRateIn30Day(String date) throws Exception {

		//1.计算统计时段
		Date dateStart = new Date();
		Date dateEnd = new Date();
		dateEnd.setTime(DateUtil.getDayFirstSecondTimestampNoLine() * 1000);
		if(!StringUtils.isEmpty(date)){
			//如果给定日期，则统计指定日期内的
			dateEnd.setTime(DateUtil.getDayFirstSecondTimestampNoLine(date) * 1000);
		}
		long duration = dateEnd.getTime()-(30 * 24 * 3600 * 1000L);
		dateStart.setTime(DateUtil.getDayFirstSecondTimestampNoLine(duration));

		//2.获取要统计的账号id
		List<BladeUser> userList = userService.list(Wrappers.lambdaQuery(BladeUser.class).eq(BladeUser::getIsDeleted,0));

		//3.计算部门信息
		Map<Long,List<Long>> deptMap = new HashMap<>();
		for(BladeUser u : userList){
			String deptIds = u.getDeptId();
			if(StringUtils.isEmpty(deptIds)){
				continue;
			}
			String[] deptArray = deptIds.split(",");
			for(String deptId : deptArray){
				if(deptMap.containsKey(Long.parseLong(deptId))){
					continue;
				}
				List<Long> deptList = userInfoUtil.getChildrenAndSelfDeptId(Long.parseLong(deptId));
				deptMap.put(Long.parseLong(deptId), deptList);
			}
		}

		List<StatCountRateUser> alarmCountList = new ArrayList<>();
		SimpleDateFormat sdf = DateUtil.sdfHolderShort.get();

		//获取统计时段
		List<String> dateList = DateUtil.getDateList(dateStart, dateEnd);

		List<Long> depts = new ArrayList<>();
		//4.执行统计计算
		for(BladeUser u : userList){
			String deptIds = u.getDeptId();
			if(StringUtils.isEmpty(deptIds)){
				continue;
			}
			String[] deptArr = deptIds.split(",");
			for(String deptId : deptArr) {
				depts.addAll(deptMap.get(Long.parseLong(deptId)));
			}

			//查询roleName
			String roleIds = u.getRoleId();
			if(StringUtils.isEmpty(roleIds)){
				log.error("为查询到用户角色信息，任务停止");
				return ;
			}
			String[] ids = roleIds.split(",");
			List<Long> idList = new ArrayList<>();
			for(String id : ids){
				idList.add(Long.parseLong(id));
			}
			List<String> roleNameArr = userService.findRoleStringByRoleIds(idList);
			StringBuffer roleNamesBuffer = new StringBuffer();
			for(String roleName : roleNameArr){
				roleNamesBuffer.append(roleName);
			}
			//4.1 统计近30天内报警情况
			long start1 = System.currentTimeMillis();
			List<AlarmAndHandleCountResponse> list = getAlarmCountAndRate(depts, u.getId(), dateStart.getTime()/1000,dateEnd.getTime()/1000, roleNamesBuffer.toString() );
			//转为map
			Map<String, AlarmAndHandleCountResponse> alarmMap = new HashMap<>();
			for(AlarmAndHandleCountResponse ac : list){
				alarmMap.put(ac.getDate(), ac);
			}
			long end1 = System.currentTimeMillis();
			log.info("-=-=-=-=-=time1 is "+ (end1 - start1));

			//4.2 统计近30天上线情况
			long start2 = System.currentTimeMillis();
			List<DateAndCountAndRate> goOnlineList = vehicleStatService.statGoOnlineCountAndRateIn30Days(sdf.format(dateStart), sdf.format(dateEnd ),depts,u.getId());
			//转为map
			Map<String, DateAndCountAndRate> goOnlineMap = new HashMap<>();
			for(DateAndCountAndRate ac : goOnlineList){
				goOnlineMap.put(ac.getDate(), ac);
			}
			long end2 = System.currentTimeMillis();
			log.info("-=-=-=-=-=-=time2 is "+ (end2 - start2));


			dateList.forEach(item -> {
				StatCountRateUser scr = new StatCountRateUser();
				scr.setUserId(u.getId());
				scr.setDate(item);
				//报警情况
				AlarmAndHandleCountResponse ac = alarmMap.get(item);
				if(ac != null){
					scr.setAlarmCount(ac.getAlarmCount());
					scr.setHandleCount(ac.getHandleCount());
					scr.setHandleRate(ac.getHandleRate());
				}
				//上线情况
				DateAndCountAndRate dc = goOnlineMap.get(item);
				if(dc != null){
					scr.setGoOnlineCount(dc.getCount());
					scr.setGoOnlineRate(dc.getRate());
				}
				alarmCountList.add(scr);
			});


			List<String> dateListNoLine = new ArrayList<>();
			for (String s : dateList) {
				dateListNoLine.add(s.replace("-",""));
			}

			//5.统计指标
			//5.1.数据合格率
			long start3 = System.currentTimeMillis();
			List<DateAndLocationQuality> qualityList = locationQualityService.findQualityRateByDate(dateListNoLine, depts, u.getId());
			//转为map
			Map<String, DateAndLocationQuality> qualityMap = new HashMap<>();
			for (DateAndLocationQuality dl : qualityList) {
				qualityMap.put(dl.getDate(), dl);
			}
			long end3 = System.currentTimeMillis();
			log.info("-=-=-=-=-=time3 is "+ (end3 - start3));

			//5.2.轨迹完整率
			long start4 = System.currentTimeMillis();
			List<DateAndComplete> completeList = statCompleteService.findCompleteInfoByDate(dateListNoLine, depts, u.getId());
			//转为map
			Map<String, DateAndComplete> completeMap = new HashMap<>();
			for (DateAndComplete dc : completeList) {
				completeMap.put(dc.getDate(), dc);
			}
			long end4 = System.currentTimeMillis();
			log.info("-=-=-=-=-= time4 is "+ (end4 - start4));

			//5.3.定位漂移率
			//查询上线车辆数
			long start5 = System.currentTimeMillis();
			List<DateAndCount> goOnlineCountList = vehicleStatService.findGoOnlineCountByDay(sdf.format(dateStart), sdf.format(dateEnd), depts, u.getId());
			//查询漂移车辆数
			List<DateAndCount> driftCountList = driftService.findDriftCountByDate(dateListNoLine, depts, u.getId());
			//计算漂移率
			List<DateAndDrift> driftList = new ArrayList<>();
			Map<String, DateAndCount> driftMap = new HashMap<>();
			if(driftCountList != null){
				for(DateAndCount dc : driftCountList){
					driftMap.put(dc.getStatDate(), dc);
				}
			}
			long end5 = System.currentTimeMillis();
			log.info("-=-=-=-=-= time5 is "+ (end5 - start5));
			for(DateAndCount dc : goOnlineCountList){
				long goOnlineCount = dc.getCount();
				DateAndCount driftTmp = driftMap.get(dc.getStatDate());
				long driftCount = driftTmp == null ? 0 : driftTmp.getCount();
				DateAndDrift dd = new DateAndDrift();
				if(goOnlineCount < 1){
					dd.setRate(0D);
				}else{
					dd.setRate(MathUtil.divideRoundDouble(driftCount, goOnlineCount, 4));
				}
				dd.setDate(dc.getStatDate());
				dd.setDriftCount(dc.getCount());
				dd.setRateStr(MathUtil.formatToPercent(dd.getRate(), 2));
				driftList.add(dd);
			}
			//转为map
			Map<String, DateAndDrift> driftMapF = new HashMap<>();
			for (DateAndDrift dd : driftList) {
				driftMapF.put(dd.getDate(), dd);
			}

			//组装数据
			for (StatCountRateUser sr : alarmCountList) {
				if(sr.getDate() != null && completeMap.get(sr.getDate().replace("-", "")) != null) {
					sr.setCompleteRate(completeMap.get(sr.getDate().replace("-","")).getCompleteRate());
				}
				if(sr.getDate() != null && qualityMap.get(sr.getDate().replace("-", "")) != null){
					sr.setQualityRate(qualityMap.get(sr.getDate().replace("-","")).getRate());
				}
				if(sr.getDate() != null && driftMapF.get(sr.getDate().replace("-", "")) != null){
					sr.setDriftRate(driftMapF.get(sr.getDate()).getRate());
				}
				sr.setCreateTime(new Date());
			}
		}
		System.out.println("lala");
		//5.记录到表中
		scrService.saveBatch(alarmCountList);
	}

	private List<AlarmAndHandleCountResponse> getAlarmCountAndRate(List<Long> deptList, Long userId, Long startTime, Long endTime, String roleName){

		//1.统计报警数量
		List<DateAndCount> alarmList = findAlarmCount(startTime, endTime, deptList, userId);
		Map<String,DateAndCount> alarmMap = new HashMap<>();
		for(DateAndCount dc : alarmList){
			alarmMap.put(dc.getStatDate(), dc);
		}

		//2.统计报警处理数量
		//用户类型
		String userType = "";
		List<String> userTypeList = userInfoUtil.getUserType(roleName);
		if(userTypeList.contains(StatisticConstants.USER_TYPE_THIRD)){
			//如果包含第三方服务商，则按照第三方服务商来统计
			userType = StatisticConstants.USER_TYPE_THIRD;
		}else if(userTypeList.contains(StatisticConstants.USER_TYPE_SERVER)){
			//如果包含服务商，则按照服务商的去查询
			userType = StatisticConstants.USER_TYPE_SERVER;
		} else{
			//如果只包含企业
			userType = StatisticConstants.USER_TYPE_COMPANY;
		}
		List<DateAndCount> handleList = findAlarmHandleCount(startTime, endTime, deptList, userId,userType);
		Map<String, DateAndCount> handleMap = new HashMap<>();
		for(DateAndCount dc : handleList){
			handleMap.put(dc.getStatDate(), dc);
		}

		//3.整理数据
		List<AlarmAndHandleCountResponse> resList = new ArrayList<>();
		for(String date : alarmMap.keySet()){
			AlarmAndHandleCountResponse ac = new AlarmAndHandleCountResponse();
			long alarmCount = alarmMap.get(date).getCount();
			DateAndCount handle = handleMap.get(date);
			long handleCount = handle==null?0:handle.getCount();
			ac.setAlarmCount(alarmCount);
			ac.setHandleCount(handleCount);
			ac.setDate(date);
			ac.setHandleRate(MathUtil.divideRoundDouble(handleCount, alarmCount, 4));
			ac.setHandleRateStr(MathUtil.formatToPercent(ac.getHandleRate(),2));
			resList.add(ac);
		}
		return resList;
	}

	/**
	 * @description: 查询指定时间段，超过指定时长的疲劳报警
	 * @author: zhouxw
	 * @date: 2023-07-206 09:40:38
	 * @param: [startDate: yyyy-MM-dd , endDate: yyyy-MM-dd, duration 超过的秒数]
	 * @return: long
	 **/
	@Override
	public long findFatigueCountByDateAndDuration(String startDate, String endDate, long duration, List<Long> deptIds, Long userId) throws Exception {
		return mapper.getFatigueCountByDateAndDuration(startDate, endDate, duration, deptIds, userId);
	}

	@Override
	public long findFatigueHandleCountByDateAndDuration(String startDate, String endDate, long duration, List<Long> deptIds, Long userId, String userType) throws Exception {
		return mapper.getFatigueHandleCountByDateAndDuration(startDate, endDate, duration, deptIds, userId, userType);
	}

	@Override
	public long findAlarmCountByDate(List<Integer> alarmTypes, String startDate, String endDate, List<Long> deptIds, Long userId) throws Exception {
		return mapper.getAlarmCountByDate(alarmTypes, startDate, endDate, deptIds, userId);
	}

	@Override
	public long findAlarmHandleCountByDate(List<Integer> alarmTypes, String startDate, String endDate, List<Long> deptIds, Long userId, String userType) throws Exception {
		return mapper.getAlarmHandleCountByDate(alarmTypes, startDate, endDate, deptIds, userId, userType);
	}

	@Override
	public List<AlarmBase> findAlarmBaseCountByDate(List<Integer> alarmTypes, String startDate, String endDate, List<Long> deptIds, Long userId) throws Exception {
		return mapper.getAlarmBaseByDate(alarmTypes, startDate, endDate, deptIds, userId);
	}

	@Override
	public long findAlarmHandleCountByAlarmIds(List<Long> alarmIds, String userType) throws Exception {
		return mapper.getAlarmHandleCountByAlarmIds(alarmIds, userType);
	}

	@Override
	public IPage<ImpalaAlarm> overSpeedPage (CommonAlarmStatRequest request, Query query) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		IPage<ImpalaAlarm> res = Condition.getPage(query);
		res.setTotal(this.impalaAlarmMapper.getNumOverSpeed(request));
		if (res.getTotal() <= 0) {
			return res;
		}

		res.setRecords(this.impalaAlarmMapper.getOverSpeedList(request, res.getCurrent(), res.getSize()));
		return res;
	}

	@Override
	public List<ImpalaAlarm> overSpeedList (CommonAlarmStatRequest request) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		return this.impalaAlarmMapper.getOverSpeedList(request, 0, 0);
	}

	@Override
	public IPage<ImpalaAlarm> fatigueDrivePage (CommonStatRequest request, Query query) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		IPage<ImpalaAlarm> res = Condition.getPage(query);
		res.setTotal(this.impalaAlarmMapper.getNumFatigueDrive(request));
		if (res.getTotal() <= 0) {
			return res;
		}

		res.setRecords(this.impalaAlarmMapper.getFatigueDriveList(request, res.getCurrent(), res.getSize()));
		return res;
	}

	@Override
	public List<ImpalaAlarm> fatigueDriveList (CommonStatRequest request) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		return this.impalaAlarmMapper.getFatigueDriveList(request, 0, 0);
	}

	@Override
	public IPage<ImpalaAlarm> nightDrivePage (CommonStatRequest request, Query query) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		IPage<ImpalaAlarm> res = Condition.getPage(query);
		res.setTotal(this.impalaAlarmMapper.getNumNightDrive(request));
		if (res.getTotal() <= 0) {
			return res;
		}

		res.setRecords(this.impalaAlarmMapper.getNightDriveList(request, res.getCurrent(), res.getSize()));
		return res;
	}

	@Override
	public List<ImpalaAlarm> nightDriveList (CommonStatRequest request) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		return this.impalaAlarmMapper.getNightDriveList(request, 0, 0);
	}

	@Override
	public IPage<ImpalaAlarm> wrongPage (CommonAlarmStatRequest request, Query query) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		IPage<ImpalaAlarm> res = Condition.getPage(query);
		res.setTotal(this.impalaAlarmMapper.getNumWrong(request));
		if (res.getTotal() <= 0) {
			return res;
		}

		res.setRecords(this.impalaAlarmMapper.getWrongList(request, res.getCurrent(), res.getSize()));
		return res;
	}

	@Override
	public List<ImpalaAlarm> wrongList (CommonAlarmStatRequest request) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		return this.impalaAlarmMapper.getWrongList(request, 0, 0);
	}

	@Override
	public List<AlarmCount> getCarAlarmStatList (CarAlarmRequest request) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		return this.impalaAlarmMapper.getCarAlarmStatList(request);
	}

	@Override
	public List<ImpalaAlarm> getCarAlarmAnalysisList (CarAlarmRequest request) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		return this.impalaAlarmMapper.getCarAlarmAnalysisList(request);
	}

	@Override
	public List<AlarmCount> getDriverAlarmStatList (DriverAlarmRequest request) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		return this.impalaAlarmMapper.getDriverAlarmStatList(request);
	}

	@Override
	public List<ImpalaAlarm> getDriverAlarmAnalysisList (DriverAlarmRequest request) {
		request.setVehicleSliceList(
			CollectionUtils.isEmpty(request.getVehicleList()) ?
			new ArrayList<>() :
			Lists.partition(new HashSet<>(request.getVehicleList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		return this.impalaAlarmMapper.getDriverAlarmAnalysisList(request);
	}

	@Override
	public IPage<Map<String, Long>> getDealRatePage (CommonStatRequest request, Query query) {
		IPage<Map<String, Long>> res = Condition.getPage(query);
		List<Long> deptIdList = request.getDeptList();
		int numDept = deptIdList.size();
		res.setTotal(numDept);
		if (res.getTotal() <= 0) {
			return res;
		}

		int fromIdx = (query.getCurrent() - 1) * query.getSize();
		int endIdx = Math.min(fromIdx + query.getSize(), numDept);
		if ((fromIdx >= numDept) || (fromIdx >= endIdx)) {
			return res;
		}

		request.setDeptList(deptIdList.subList(fromIdx, endIdx));
		res.setRecords(this.getDealRateList(request));
		return res;
	}

	@Override
	public List<Map<String, Long>> getDealRateList (CommonStatRequest request) {
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		Map<Long, Map<String, Long>> res = new HashMap<>();
		for (Long deptId : request.getDeptList()) {
			Map<String, Long> map = new HashMap<>();
			map.put("dept_id", deptId);
			map.put("num_vehicle", 0L);
			map.put("num_alarm", 0L);
			map.put("num_deal", 0L);
			map.put("num_alarm_over_speed", 0L);
			map.put("num_deal_over_speed", 0L);
			map.put("num_alarm_fatigue", 0L);
			map.put("num_deal_fatigue", 0L);
			map.put("num_alarm_night_drive", 0L);
			map.put("num_deal_night_drive", 0L);
			map.put("num_alarm_adas", 0L);
			map.put("num_deal_adas", 0L);
			map.put("num_alarm_dsm", 0L);
			map.put("num_deal_dsm", 0L);
			res.put(deptId, map);
		}
		try {
			CommonAlarmStatRequest req = new CommonAlarmStatRequest();
			BeanUtils.copyProperties(request, req);
			List<Short> alarmTypeList = new ArrayList<>();
			List<Integer> overSpeedList = VdmUserInfoUtil.getOverSpeedAlarmType();
			for (Integer tmp : overSpeedList) {
				alarmTypeList.add(tmp.shortValue());
			}

			List<Integer> fatigueList = VdmUserInfoUtil.getFatigueAlarmType();
			for (Integer tmp : fatigueList) {
				alarmTypeList.add(tmp.shortValue());
			}

			List<Integer> nightDriveList = VdmUserInfoUtil.getNightLimitAlarmType();
			for (Integer tmp : nightDriveList) {
				alarmTypeList.add(tmp.shortValue());
			}

			List<Integer> adasList = VdmUserInfoUtil.getAdasAlarmType();
			for (Integer tmp : adasList) {
				alarmTypeList.add(tmp.shortValue());
			}

			List<Integer> dsmList = VdmUserInfoUtil.getDsmAlarmType();
			for (Integer tmp : dsmList) {
				alarmTypeList.add(tmp.shortValue());
			}

			req.setAlarmTypeList(alarmTypeList);
			CountDownLatch countDownLatch = new CountDownLatch(4);
			Map<Long, List<Long>> childDeptMap = new HashMap<>();
			AtomicReference<Map<Long, Integer>> deptMapNumVehicleAtomicRefer = new AtomicReference<>();
			Map<String, Integer> deptTypeMapNumAlarm = new HashMap<>();
			Map<String, Integer> deptTypeMapNumDeal = new HashMap<>();
			this.threadPool.submit(() -> {
				try {
					Map<Object, Object> tmpMap = this.redisTemplate.opsForHash().entries(RedisConstants.HashKey.DEPT_INFO_HASH_KEY);
					if (CollectionUtils.isNotEmpty(tmpMap)) {
						for (Object k : tmpMap.keySet()) {
							childDeptMap.put(Long.parseLong(k.toString()), JSONArray.parseArray(tmpMap.get(k).toString(), Long.class));
						}
					}
				} catch (Exception e) {
					log.error("err occur when getChildDeptCache from getDealRateList: " + e.getMessage(), e);
				} finally {
					countDownLatch.countDown();
				}
			});
			this.threadPool.submit(() -> {
				try {
					deptMapNumVehicleAtomicRefer.set(this.bdmVehicleService.getDeptMapNumVehicle(request));
				} catch (Exception e) {
					log.error("err occur when getDeptMapNumVehicle from getDealRateList: " + e.getMessage(), e);
				} finally {
					countDownLatch.countDown();
				}
			});
			this.threadPool.submit(() -> {
				try {
					List<Map<String, Long>> tmpList = this.impalaAlarmMapper.getNumAlarmGroupByDeptType(req);
					for (Map<String, Long> tmp : tmpList) {
						deptTypeMapNumAlarm.put(tmp.get("dept_id") + "-" + tmp.get("alarm_type"), tmp.get("num").intValue());
					}
				} catch (Exception e) {
					log.error("err occur when getNumAlarmGroupByDeptType from getDealRateList: " + e.getMessage(), e);
				} finally {
					countDownLatch.countDown();
				}
			});
			this.threadPool.submit(() -> {
				try {
					List<Map<String, Long>> tmpList = this.impalaAlarmMapper.getNumDealGroupByDeptType(req);
					for (Map<String, Long> tmp : tmpList) {
						deptTypeMapNumDeal.put(tmp.get("dept_id") + "-" + tmp.get("alarm_type"), tmp.get("num").intValue());
					}
				} catch (Exception e) {
					log.error("err occur when getNumDealGroupByDeptType from getDealRateList: " + e.getMessage(), e);
				} finally {
					countDownLatch.countDown();
				}
			});

			countDownLatch.await();
			Map<Long, Integer> deptMapNumVehicle = deptMapNumVehicleAtomicRefer.get();
			for (Long deptId : deptMapNumVehicle.keySet()) {
				List<Long> familyDeptIdList = childDeptMap.get(deptId);
				if (CollectionUtils.isEmpty(familyDeptIdList)) {
					familyDeptIdList = SysCache.getDeptChildIds(deptId);
				}
				if (CollectionUtils.isEmpty(familyDeptIdList)) {
					familyDeptIdList.add(deptId);
				}

				Map<String, Long> map = res.get(deptId);
				for (Long familyDeptId : familyDeptIdList) {
					map.put("num_vehicle", map.get("num_vehicle") + deptMapNumVehicle.getOrDefault(familyDeptId, 0));
				}
			}
			for (String k : deptTypeMapNumAlarm.keySet()) {
				String[] ka = k.split("-");
				Long deptId = Long.parseLong(ka[0]);
				Integer alarmType = Integer.parseInt(ka[1]);
				List<Long> familyDeptIdList = childDeptMap.get(deptId);
				if (CollectionUtils.isEmpty(familyDeptIdList)) {
					familyDeptIdList = SysCache.getDeptChildIds(deptId);
				}
				if (CollectionUtils.isEmpty(familyDeptIdList)) {
					familyDeptIdList.add(deptId);
				}

				Map<String, Long> map = res.get(deptId);
				for (Long familyDeptId : familyDeptIdList) {
					String kk = familyDeptId + "-" + alarmType;
					map.put("num_alarm", map.get("num_alarm") + deptTypeMapNumAlarm.getOrDefault(kk, 0));
					if (overSpeedList.contains(alarmType)) {
						map.put("num_alarm_over_speed", map.get("num_alarm_over_speed") + deptTypeMapNumAlarm.getOrDefault(kk, 0));
					} else if (fatigueList.contains(alarmType)) {
						map.put("num_alarm_fatigue", map.get("num_alarm_fatigue") + deptTypeMapNumAlarm.getOrDefault(kk, 0));
					} else if (nightDriveList.contains(alarmType)) {
						map.put("num_alarm_night_drive", map.get("num_alarm_night_drive") + deptTypeMapNumAlarm.getOrDefault(kk, 0));
					} else if (adasList.contains(alarmType)) {
						map.put("num_alarm_adas", map.get("num_alarm_adas") + deptTypeMapNumAlarm.getOrDefault(kk, 0));
					} else if (dsmList.contains(alarmType)) {
						map.put("num_alarm_dsm", map.get("num_alarm_dsm") + deptTypeMapNumAlarm.getOrDefault(kk, 0));
					}
				}
			}
			for (String k : deptTypeMapNumDeal.keySet()) {
				String[] ka = k.split("-");
				Long deptId = Long.parseLong(ka[0]);
				Integer alarmType = Integer.parseInt(ka[1]);
				List<Long> familyDeptIdList = childDeptMap.get(deptId);
				if (CollectionUtils.isEmpty(familyDeptIdList)) {
					familyDeptIdList = SysCache.getDeptChildIds(deptId);
				}
				if (CollectionUtils.isEmpty(familyDeptIdList)) {
					familyDeptIdList.add(deptId);
				}

				Map<String, Long> map = res.get(deptId);
				for (Long familyDeptId : familyDeptIdList) {
					String kk = familyDeptId + "-" + alarmType;
					map.put("num_deal", map.get("num_deal") + deptTypeMapNumDeal.getOrDefault(kk, 0));
					if (overSpeedList.contains(alarmType)) {
						map.put("num_deal_over_speed", map.get("num_deal_over_speed") + deptTypeMapNumDeal.getOrDefault(kk, 0));
					} else if (fatigueList.contains(alarmType)) {
						map.put("num_deal_fatigue", map.get("num_deal_fatigue") + deptTypeMapNumDeal.getOrDefault(kk, 0));
					} else if (nightDriveList.contains(alarmType)) {
						map.put("num_deal_night_drive", map.get("num_deal_night_drive") + deptTypeMapNumDeal.getOrDefault(kk, 0));
					} else if (adasList.contains(alarmType)) {
						map.put("num_deal_adas", map.get("num_deal_adas") + deptTypeMapNumDeal.getOrDefault(kk, 0));
					} else if (dsmList.contains(alarmType)) {
						map.put("num_deal_dsm", map.get("num_deal_dsm") + deptTypeMapNumDeal.getOrDefault(kk, 0));
					}
				}
			}

			return res.values().parallelStream().collect(Collectors.toList());
		} catch (InterruptedException e) {
			log.error("err occur when getDealRateList: " + e.getMessage(), e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<Location> getLocationFromTimeSeg (int licenceColor, String licencePlate, long startTime, long endTime) {
		return this.impalaAlarmMapper.getLocationFromTimeSeg(licenceColor, licencePlate, startTime, endTime);
	}

	@Override
	public int getNumCarForDept (CommonStatRequest request) {
		return this.mapper.getNumCarForDept(request);
	}

	@Override
	public int getNumAlarmForDept (CommonStatRequest request) {
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		return this.impalaAlarmMapper.getNumAlarmForDept(request);
	}

	@Override
	public int getNumOverSpeed (CommonStatRequest request) {
		CommonAlarmStatRequest req = new CommonAlarmStatRequest();
		BeanUtils.copyProperties(request, req);
		return this.impalaAlarmMapper.getNumOverSpeed(req);
	}

	@Override
	public int getNumFatigueDrive (CommonStatRequest request) {
		return this.impalaAlarmMapper.getNumFatigueDrive(request);
	}

	@Override
	public int getNumNightDrive (CommonStatRequest request) {
		return this.impalaAlarmMapper.getNumNightDrive(request);
	}

	@Override
	public int getNumNoPosition (CommonStatRequest request) {
		return this.mapper.getNumNoPosition(request, new SimpleDateFormat("yyyyMM").format(new Date(request.getStartTime() * 1000)));
	}

	@Override
	public int getNumOfflineMove (CommonStatRequest request) {
		return this.mapper.getNumOfflineMove(request);
	}

	@Override
	public List<AlarmCount> getCarRankOfAlarmType (CommonAlarmStatRequest request) {
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		return this.impalaAlarmMapper.getCarRankOfAlarmType(request);
	}

	@Override
	public List<AlarmCount> getNumEachCarDeal (CommonAlarmStatRequest request) {
		request.setVehicleIdSliceList(
			CollectionUtils.isEmpty(request.getVehicleIdList()) ?
			new ArrayList<>():
			Lists.partition(new HashSet<>(request.getVehicleIdList()).parallelStream().collect(Collectors.toList()), this.numVehiclePerSlice)
		);

		return this.impalaAlarmMapper.getNumEachCarDeal(request);
	}

	@Override
	public List<AlarmCount> getCarRankOfNoPosition (CommonStatRequest request) {
		return this.mapper.getCarRankOfNoPosition(request, new SimpleDateFormat("yyyyMM").format(new Date(request.getStartTime() * 1000)));
	}

	@Override
	public List<AlarmCount> getCarRankOfOfflineMove (CommonStatRequest request) {
		return this.mapper.getCarRankOfOfflineMove(request);
	}

	@Override
	public long findAlarmCountByDeptIds(List<Long> deptIds, String date) throws Exception {
		long startTime = DateUtil.getDayFirstSecondTimestamp(date);
		long endTime = DateUtil.getDayLastSecondTimestamp(startTime);
		return impalaAlarmMapper.getAlarmCountByDeptIds(deptIds, startTime, endTime);
	}

	@Override
	public long findAlarmCountByDeptIds(List<Long> deptIds, Long startSecondTimestamp, Long endSecondTimestamp) throws Exception {
		return impalaAlarmMapper.getAlarmCountByDeptIds(deptIds, startSecondTimestamp, endSecondTimestamp);
	}

	@Override
	public long findAlarmCountByDeptIdsAndAlarmType(List<Long> deptIds, List<Integer> alarmTypes, String date) throws Exception {
		long startTime = DateUtil.getDayFirstSecondTimestamp(date);
		long endTime = DateUtil.getDayLastSecondTimestamp(startTime);
		List<Byte> alarmTypeList = new ArrayList<>();
		alarmTypes.forEach(item -> {
			alarmTypeList.add(item.byteValue());
		});
		return impalaAlarmMapper.getAlarmCountByDeptIdsAndAlarmTypes(deptIds, alarmTypeList, startTime, endTime);
	}


	@Override
	public long findAlarmCountByDeptIdsAndAlarmType(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp) throws Exception {
		List<Byte> alarmTypeList = new ArrayList<>();
		alarmTypes.forEach(item -> {
			alarmTypeList.add(item.byteValue());
		});
		return impalaAlarmMapper.getAlarmCountByDeptIdsAndAlarmTypes(deptIds, alarmTypeList, startSecondTimestamp, endSecondTimestamp);
	}


	@Override
	public List<VehicleAndCount> findAlarmListByDeptIdsAndAlarmType(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp, Integer limit) throws Exception {
		List<Byte> alarmTypeList = new ArrayList<>();
		alarmTypes.forEach(item -> {
			alarmTypeList.add(item.byteValue());
		});
		return impalaAlarmMapper.getAlarmListByDeptIdsAndAlarmTypes(deptIds, alarmTypeList, startSecondTimestamp, endSecondTimestamp, limit);
	}

	@Override
	public IPage<VehicleAndCount> findAlarmListByDeptIdsAndAlarmTypePage(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp, String licencePlate, Long licenceColor, Query query) throws Exception {
		List<Byte> alarmTypeList = new ArrayList<>();
		alarmTypes.forEach(item -> {
			alarmTypeList.add(item.byteValue());
		});
		IPage<VehicleAndCount> page = new Page<>(query.getCurrent(), query.getSize());
		page.setTotal(this.impalaAlarmMapper.getNumAlarmByDeptIdsAndAlarmTypes(deptIds, alarmTypeList, startSecondTimestamp, endSecondTimestamp, licencePlate, (licenceColor == null) ? null : licenceColor.toString()));
		if (page.getTotal() <= 0) {
			return page;
		}

		page.setRecords(this.impalaAlarmMapper.getAlarmListByDeptIdsAndAlarmTypesPage(deptIds, alarmTypeList, startSecondTimestamp, endSecondTimestamp, licencePlate, (licenceColor == null) ? null : licenceColor.toString(), page.getCurrent(), page.getSize()));
		return page;
	}

	@Override
	public long findVehicleCountByDeptIdsAndAlarmType(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp) throws Exception {
		List<Byte> alarmTypeList = new ArrayList<>();
		alarmTypes.forEach(item -> {
			alarmTypeList.add(item.byteValue());
		});
		return impalaAlarmMapper.getVehicleCountByDeptIdsAndAlarmTypes(deptIds, alarmTypeList, startSecondTimestamp, endSecondTimestamp);
	}


	@Override
	public List<DriverNameAndCount> findDriverAlarmListByDeptIdsAndAlarmType(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp, Integer limit) throws Exception {
		List<Byte> alarmTypeList = new ArrayList<>();
		alarmTypes.forEach(item -> {
			alarmTypeList.add(item.byteValue());
		});
		return impalaAlarmMapper.getDriverAlarmListByDeptIdsAndAlarmTypes(deptIds, alarmTypeList, startSecondTimestamp, endSecondTimestamp, limit);
	}

	@Override
	public IPage<DriverNameAndCount> findDriverAlarmListByDeptIdsAndAlarmTypePage(List<Long> deptIds, List<Integer> alarmTypes, Long startSecondTimestamp, Long endSecondTimestamp,String driverName, Query query) throws Exception {
		List<Byte> alarmTypeList = new ArrayList<>();
		alarmTypes.forEach(item -> {
			alarmTypeList.add(item.byteValue());
		});
		IPage<DriverNameAndCount> page = new Page<>(query.getCurrent(),query.getSize());
		return impalaAlarmMapper.getDriverAlarmListByDeptIdsAndAlarmTypesPage(deptIds, alarmTypeList, startSecondTimestamp, endSecondTimestamp, driverName, page);
	}

	/**
	 * @description: 获取报警类型
	 * @author: zhouxw
	 * @date: 2023-06-166 15:27:16
	 * @param: [code, key]
	 * @return: java.lang.String
	 **/
	public Set<DictTreeNodeVO> getChildrenFlatAlarmTypes (String code, String key) {
		//查找所有子报警类型
		DictTreeNodeVO overSpeedTreeNode = null;
		String tenantId = AuthUtil.getTenantId();
		log.info(">> getChildrenFlatAlarmTypes tenantId is {}", tenantId);
		try{
			R<DictTreeNodeVO> res = dictBizClient.getDictTreeByCodeAndKeyCacheTenant(code, key, tenantId);
			if(res == null || res.getData() == null){
				log.error("未查询到子类型");
				overSpeedTreeNode = null;
			}else{
				overSpeedTreeNode = res.getData();
			}
		}catch (Exception e){
			log.error("查询子类型失败",e);
			overSpeedTreeNode = null;
		}
		log.info(">> getChildrenFlatAlarmTypes code={} key={} 获取到的结果为{}", code, key , JSON.toJSONString(overSpeedTreeNode));
		Set<DictTreeNodeVO> set = new HashSet<>();
		if(overSpeedTreeNode != null){
			set = getDictChildFlat(overSpeedTreeNode, set);
		}
		return set;
	}


	/**
	 * @description: 获取报警类型
	 * @author: zhouxw
	 * @date: 2023-06-166 15:27:16
	 * @param: [code, key]
	 * @return: java.lang.String
	 **/
	public Set<DictTreeNodeVO> getChildrenFlatAlarmTypesByTenantId (String code, String key, String tenantId) {
		//查找所有子报警类型
		DictTreeNodeVO overSpeedTreeNode = null;
		log.info(">> getChildrenFlatAlarmTypes tenantId is {}", tenantId);
		try{
			R<DictTreeNodeVO> res = dictBizClient.getDictTreeByCodeAndKeyCacheTenant(code, key, tenantId);
			if(res == null || res.getData() == null){
				log.error("未查询到子类型");
				overSpeedTreeNode = null;
			}else{
				overSpeedTreeNode = res.getData();
			}
		}catch (Exception e){
			log.error("查询子类型失败",e);
			overSpeedTreeNode = null;
		}
		log.info(">> getChildrenFlatAlarmTypes code={} key={} 获取到的结果为{}", code, key , JSON.toJSONString(overSpeedTreeNode));
		Set<DictTreeNodeVO> set = new HashSet<>();
		if(overSpeedTreeNode != null){
			set = getDictChildFlat(overSpeedTreeNode, set);
		}
		return set;
	}
	/**
	 * @description: 递归遍历字典
	 * @author: zhouxw
	 * @date: 2023-06-165 20:17:17
	 * @param: [vo, list]
	 * @return: java.util.List<org.springblade.system.vo.DictTreeNodeVO>
	 **/
	private Set<DictTreeNodeVO> getDictChildFlat(DictTreeNodeVO vo, Set<DictTreeNodeVO> set){
		if(vo != null ){
			if(vo.getChildren() != null && vo.getChildren().size() > 0){
				List<DictTreeNodeVO> tmpList = vo.getChildren();
				for(DictTreeNodeVO v : tmpList){
					set.addAll(getDictChildFlat(v, set));
				}
			}else{
				set.add(vo);
			}
		}
		return set;
	}

	@Override
	public IPage<ImpalaAlarm> findAlarmByCondition(CommonBaseImpalaAlarmRequest request, Query query) throws Exception {
		IPage<ImpalaAlarm> page = new Page<>();
		page.setCurrent(query.getCurrent());
		page.setSize(query.getSize());
		return impalaAlarmMapper.getAlarmsByConditionPage(request, page);
	}

	@Override
	public List<TargetAlarmDTO> getAlarms(DataAuthCE dataAuthCE,  String tenantId, Long startTime, Long endTime, Query query) {
//		// 按组织查找
//		if (DataAuthCE.CE_DATA_AUTH_TYPE_ORG.equals(dataAuthCE.getGnDataAuthType())) {
//			List<Long> deptIds = dataAuthCE.getOrgList().stream().map(Long::parseLong).collect(Collectors.toList());
//			return impalaAlarmMapper2.getAlarmListWithDept(startTime, endTime, deptIds, query.getCurrent(), query.getSize());
//		}
//
//		// 按个人权限查找
//		List<BdmAbstractDevice> deviceList = abstractDeviceMapper.getAccessDeviceList(dataAuthCE.getAccount());
//		if(deviceList == null || deviceList.isEmpty()) {
//			return null;
//		}
//		List<Long> deviceIdList = deviceList.stream().map(item -> item.getId()).collect(Collectors.toList());
//
//		return impalaAlarmMapper2.getAlarmListWithDev(startTime, endTime, deviceIdList, query.getCurrent(), query.getSize());
		//查询当前用户可以检索的 device_id
		List<Long> deviceIdList = new ArrayList<>();
		try {
			deviceIdList = getAlarmDeviceIdList(startTime, endTime, dataAuthCE.getOrgList());
		}catch (Exception e){
			log.error("获取报警相关的终端id失败",e);
		}
		Map<Long, DeptNode> deptMap = new HashMap<>();
		List<TargetAlarmDTO>dtoList=impalaAlarmMapper2.getAlarmListWithDev(startTime, endTime,deviceIdList,query.getCurrent(),query.getSize());

		if (dtoList.isEmpty()){
			return dtoList;
		}
		List<Long> deviceIds = dtoList.stream()
			.map(TargetAlarmDTO::getDeviceId) // 提取
			.distinct() // 去重
			.collect(Collectors.toList()); // 收集到List<String>
		QueryWrapper<BdmAbstractDevice> badQw=new QueryWrapper<>();
		badQw.in("id", deviceIds); // 构造 IN 条件
		List<BdmAbstractDevice>badList=abstractDeviceMapper.selectList(badQw);
		Map<Long, String> deviceMap = badList.stream()
			.collect(Collectors.toMap(BdmAbstractDevice::getId, BdmAbstractDevice::getUniqueId));

		// 根据targetId过滤重复数据，并组装成List<String>
		List<Long> uniqueTargetIds = dtoList.stream()
			.map(TargetAlarmDTO::getTargetId) // 提取targetId
			.distinct() // 去重
			.collect(Collectors.toList()); // 收集到List<String>
		QueryWrapper<BdmAbstractTarget> queryWrapper=new QueryWrapper<>();
		queryWrapper.in("id", uniqueTargetIds); // 构造 IN 条件
		List<BdmAbstractTarget>batList=bdmAbstractTargetMapper.selectList(queryWrapper);

		// 将结果存入 Map
		Map<Long, String> targetMap = batList.stream()
			.collect(Collectors.toMap(BdmAbstractTarget::getId, BdmAbstractTarget::getName));
		for (TargetAlarmDTO dto:dtoList){
			String name=targetMap.get(dto.getTargetId());
			dto.setTargetName(name);
			String uniqueId=deviceMap.get(dto.getDeviceId());
			dto.setUniqueId(uniqueId);
		}

//		List<Object> deptList = stringRedisTemplate.opsForHash().multiGet(
//			RedisConstant.HASH_DEPT_NODE + tenantId,
//			dtoList.parallelStream().map((item) -> {
//				return item.getDeptId()+"";
//			}).collect(Collectors.toSet())
//		);
//		for (Object dept : deptList) {
//			if (dept != null) {
//				DeptNode node = JSON.parseObject(dept.toString(), DeptNode.class);
//				deptMap.put(node.id, node);
//			}
//		}
//		for (TargetAlarmDTO dto:dtoList){
//			DeptNode dept=deptMap.get(dto.getDeptId());
//			dto.setTargetName(dept.name);
//		}
		return dtoList;
	}
	public List<Long> getAlarmDeviceIdList(Long startTime, Long endTime, List<String> deptIdList) throws Exception{
		//优化查询效率
		//1.获取查询日期列表
		List<String> dateList = DateUtil.getDateList(startTime, endTime);

		//2.根据前缀查询redis中的key
		Set<String> keys = new HashSet<>();
		for(String date : dateList){
			String keyPrefix = AlarmConstant.CACHE_PREFIX_ALARM_DEVICE + date.replace("-","") + ":";
			Set<String> set = stringRedisTemplate.execute((RedisCallback<Set<String>>) connection -> {
				Set<String> keysTmp = new HashSet<>();
				Cursor<byte[]> cursor = connection.scan(ScanOptions.scanOptions().match(keyPrefix+"*").count(1000).build());
				while (cursor.hasNext()) {
					keysTmp.add(new String(cursor.next()));
				}
				return keysTmp;
			});
			keys.addAll(set);
		}

		//3.添加数据权限
		Set<String> authDeptIds = new HashSet<>();
		authDeptIds.addAll(deptIdList);
		//只保留在authDeptIds中存在的deptId（两个set取交集）
		Set<String> intersection = new HashSet<>(keys);
		intersection.retainAll(authDeptIds);
		keys = intersection;

		//4.批量查询redis，获取指定时间段、指定部门发生告警的终端号列表
		Set<String> sets = new HashSet<>();
		long start10 = System.currentTimeMillis();
		sets = getSetMembers(keys);
		long end10 = System.currentTimeMillis();
		log.info("redis 查询 发生报警的设备耗时：" + (end10 - start10));
		//类型转换
		List<Long> deviceList = new ArrayList<>();
		if(sets != null){
			for(String bs : sets){
				deviceList.add(Long.parseLong(bs));
			}
		}
		return deviceList;
	}
	private Set<String> getSetMembers(Set<String> keys) {
		// 获取Redis连接
		RedisConnection connection = stringRedisTemplate.getConnectionFactory().getConnection();
		try {
			// 创建pipeline
			connection.openPipeline();

			// 在pipeline中添加SMEMBERS命令
			for(String key : keys){
				connection.sMembers(stringRedisTemplate.getStringSerializer().serialize(key));
			}

			// 执行pipeline
			List<Object> results = connection.closePipeline();
			Set<String> set = new HashSet<>();
			// 解析结果，这里只有一个命令的返回值，所以取第一个元素
			if (!results.isEmpty()) {
				for(Object obj : results){
					LinkedHashSet<byte[]> links = (LinkedHashSet<byte[]>) obj;
					for(byte[] b : links){
						String str = new String(b);
						set.add(str);
					}
				}
				return set;
			}
		} finally {
			// 关闭连接
			connection.close();
		}
		return null;
	}
}
