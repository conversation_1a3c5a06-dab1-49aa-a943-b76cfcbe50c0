package com.xh.vdm.statistic.controller;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.xh.vdm.statistic.constant.Constant;
import com.xh.vdm.statistic.dto.DeviceLinkDTO;
import com.xh.vdm.statistic.dto.TargetAlarmDTO;
import com.xh.vdm.statistic.entity.BdmDeviceLink;
import com.xh.vdm.statistic.service.BdmDeviceLinkService;
import com.xh.vdm.statistic.service.IAlarmService;
import com.xh.vdm.statistic.utils.AuthUtils;
import com.xh.vdm.statistic.vo.PushMessageVO;
import com.xh.vdm.statistic.vo.request.BdmDeviceLinkRequest;
import org.springblade.common.utils.CETokenUtil;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.entity.DataAuthCE;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;

/**
 * 最近终端状态。
 */
@RestController
@RequestMapping("/statistic/lastDevStatus")
public class LastDeviceStatusController {
	@Resource
	private BdmDeviceLinkService bdmDeviceLinkService;

	@Resource
	private IAlarmService alarmService;

	@Resource
	private CETokenUtil ceTokenUtil;


	@GetMapping("/list")
	public R<List<PushMessageVO>> getLastPushMessages(BladeUser user, Long startTime, Long endTime, Integer size) {
		R<String> r0 = AuthUtils.isValidServiceRole(user);
		if (!r0.isSuccess()) {
			return R.fail(r0.getCode(), r0.getMsg());
		}

		// 检查时间范围有效性
		if (null == startTime || null == endTime || startTime >= endTime)
			return R.fail("时间范围无效");

		// 获取用户权限
		DataAuthCE dataAuthCE = ceTokenUtil.getDataAuth();
		if(StringUtils.isEmpty(dataAuthCE.getOrgListStr()) && StringUtils.isEmpty(dataAuthCE.getAccount())) {
			return R.fail(403, "用户没有权限");
		}

		// 获取最近几条终端上下线信息
		Query query = new Query();
		query.setCurrent(1);
		query.setSize(size);
		BdmDeviceLinkRequest request = new BdmDeviceLinkRequest();
		request.setStartTime(new Date(startTime*1000));
		request.setEndTime(new Date(endTime*1000));
		List<BdmDeviceLink> links = bdmDeviceLinkService.queryByPage(dataAuthCE, request, query, user.getUserId());

		List<PushMessageVO> pushMessages = new ArrayList<>();
		if (null != links) {
			PushMessageVO pushMessage;
			DeviceLinkDTO linkDTO = new DeviceLinkDTO();
			for (BdmDeviceLink link : links) {
				linkDTO.setDeviceType(link.getDeviceType());
				linkDTO.setDeviceNum(link.getDeviceNum());
				linkDTO.setTargetName(link.getTargetName());
				linkDTO.setAction(link.getAction());
				linkDTO.setTime(link.getTime());
				linkDTO.setUniqueId(link.getUniqueId());

				pushMessage = new PushMessageVO();
				pushMessage.pushTime = link.getTime().getTime()/1000;
				pushMessage.type = Constant.PUSH_DEV_MSG_TYPE_STATUS;
				pushMessage.content = JSON.toJSONString(linkDTO);
				pushMessages.add(pushMessage);
			}
		}

		// 获取最近几条告警信息
		List<TargetAlarmDTO> alarms = alarmService.getAlarms(dataAuthCE,user.getTenantId(), startTime, endTime, query);
		if (null != alarms) {
			PushMessageVO pushMessage;
			for(TargetAlarmDTO alarm : alarms) {
				pushMessage = new PushMessageVO();
				pushMessage.pushTime = alarm.getStartTime();
				pushMessage.type = Constant.PUSH_DEV_MSG_TYPE_ALARM;
				pushMessage.content = JSON.toJSONString(alarm);
				pushMessages.add(pushMessage);
			}
		}

		// 综合按时间排序，提取最后几条组成推送消息
		pushMessages.sort((o1, o2) -> Long.compare(o2.pushTime, o1.pushTime));
		if (pushMessages.size() > size) {
			pushMessages = pushMessages.subList(0, size);
		}

		return R.data(pushMessages);
	}
}
