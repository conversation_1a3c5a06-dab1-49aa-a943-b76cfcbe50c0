package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.BdmTerminalSim;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.request.SimExpireRequest;
import com.xh.vdm.statistic.vo.response.SimExpireResponse;
import org.springblade.core.mp.support.Query;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
public interface IBdmTerminalSimService extends IService<BdmTerminalSim> {

	/**
	 * 查询服务到期信息
	 * @param request
	 * @param query
	 * @return
	 * @throws Exception
	 */
	IPage<SimExpireResponse> findSimExpireInfo(SimExpireRequest request, Query query) throws Exception;
}
