package com.xh.vdm.statistic.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DeviceLinkDTO {
	private Integer deviceType;
	private String deviceNum;
	private String targetName;
	@JSONField(name = "onOffLine")
	private Integer action;
	private Date time;
	private String uniqueId;
}
