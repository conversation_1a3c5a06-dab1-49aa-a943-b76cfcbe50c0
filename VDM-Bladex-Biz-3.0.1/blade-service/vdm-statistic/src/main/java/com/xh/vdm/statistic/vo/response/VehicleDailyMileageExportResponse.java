package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 车辆日里程统计导出
 */
@Data
public class VehicleDailyMileageExportResponse {

	@ApiModelProperty(value = "企业名称")
	@JsonProperty("dept_name")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆日里程统计报表", "企业名称"})
	@ColumnWidth(22)
	private String deptName;

	@ApiModelProperty(value = "行业类型")
	@JsonProperty("vehicle_use_type")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelIgnore
	@ColumnWidth(22)
	private String vehicleUseType;

	@ApiModelProperty(value = "行业类型")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@JsonIgnore
	@ExcelProperty(value = {"车辆日里程统计报表", "行业类型"})
	@ColumnWidth(22)
	private String vehicleUseTypeDesc;

	@ApiModelProperty(value = "车辆归属")
	@JsonProperty("vehicle_owner")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆日里程统计报表", "车辆归属"})
	@ColumnWidth(22)
	private String vehicleOwner;


	@ApiModelProperty(value = "车牌颜色")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆日里程统计报表", "车牌颜色"})
	@JsonIgnore
	@ColumnWidth(22)
	private String licenceColorDesc;

	@ApiModelProperty(value = "在线天数")
	@JsonProperty("licence_color_desc")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆日里程统计报表", "在线天数"})
	@ColumnWidth(22)
	private Long onlineDaysCount;

	@ApiModelProperty(value = "车牌号")
	@JsonProperty("licence_plate")
	@HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
	@ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
	@HeadFontStyle(fontHeightInPoints = 12)
	@ContentFontStyle(fontHeightInPoints = 12)
	@ExcelProperty(value = {"车辆日里程统计报表", "车牌号"})
	@ColumnWidth(22)
	private String licencePlate;


}
