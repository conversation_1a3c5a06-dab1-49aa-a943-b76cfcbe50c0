package com.xh.vdm.statistic.entity;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *	车辆运行情况--按月份
 * </p>
 * @since 2023-06-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatVehRunningStateMonth implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
	@JsonIgnore
    private Long id;

	//统计月份
	private String month;

    /**
     * 车牌号
     */
    private String licencePlate;

    /**
     * 车牌颜色
     */
    private Integer licenceColor;

    /**
     * 最大速度
     */
    private Double maxSpeed;

    /**
     * 总里程
     */
    private Double totalMileage;

    /**
     * 轨迹完整率
     */
    private Double completeRate;

    /**
     * 数据合格率
     */
    private Double qualityRate;

    /**
     * 漂移率
     */
    private Double driftRate;

    /**
     * 总报警数
     */
    private Long totalAlarmCount;

    /**
     * 超速报警数
     */
    private Long overSpeedCount;

    /**
     * 疲劳报警数
     */
    private Long tiredCount;

    /**
     * 夜间行驶报警数
     */
    private String nightDrivingCount;

    /**
     * 主动安全报警数
     */
    private String activeAlarmCount;

    /**
     * 创建时间
     */
	@JsonIgnore
    private Date createTime;

	//部门id
	@JsonIgnore
	private Long deptId;

	//车辆id
	@JsonIgnore
	private Long vehicleId;

}
