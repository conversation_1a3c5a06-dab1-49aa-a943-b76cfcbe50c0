package com.xh.vdm.statistic.mapper;

import com.xh.vdm.statistic.entity.AlarmTypeAndCount;
import com.xh.vdm.statistic.entity.AlarmTypeAndDateAndIdCard;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.BdmSecurity;
import com.xh.vdm.statistic.entity.DateAndCount;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-09
 */
public interface BdmSecurityMapper extends BaseMapper<BdmSecurity> {

	/**
	 * @description: 获取指定时间内的告警类型及相应的数量
	 * @author: zhouxw
	 * @date: 2022/11/15 8:48 AM
	 * @param: [deptId 车组id, startSecondTime 开始时间戳（精确到秒）, endSecondTime 结束时间戳（精确到秒）]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.AlarmTypeAndCount>
	 **/
	List<AlarmTypeAndCount> getAlarmTypeAndCount(@Param("deptId") Long deptId , @Param("startSecondTime") Long startSecondTime , @Param("endSecondTime") Long endSecondTime);

	/**
	 * @description: 查询给定时间段内指定驾驶员的报警信息
	 * @author: zhouxw
	 * @date: 2022/11/19 3:39 PM
	 * @param: [idCard, startSecondTimestamp, endSecondTimestamp]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.BdmSecurity>
	 **/
	List<BdmSecurity> getSecurityByIdCardAndDate(@Param("idCard") String idCard , @Param("startSecondTimestamp") long startSecondTimestamp , @Param("endSecondTimestamp") long endSecondTimestamp) throws Exception;

	/**
	 * @description: 查询指定时间段内某驾驶员每天的告警类型
	 * @author: zhouxw
	 * @date: 2022/11/19 4:42 PM
	 * @param: [idCard, startSecondTimestamp, endSecondTimestamp]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.AlarmTypeAndDateAndIdCard>
	 **/
	List<AlarmTypeAndDateAndIdCard> getEveryAlarmTypeByIdCard(@Param("idCard") String idCard , @Param("startSecondTimestamp") long startSecondTimestamp , @Param("endSecondTimestamp") long endSecondTimestamp) throws Exception;

	/**
	 * @description: 查询某驾驶员在给定时间段内每天的报警数量
	 * @author: zhouxw
	 * @date: 2022/11/19 9:59 PM
	 * @param: [idCard, startSecondTimestamp, endSecondTimestamp]
	 * @return: java.util.List<com.xh.vdm.statistic.entity.DateAndCount>
	 **/
	List<DateAndCount> getAlarmCountEveryDay(@Param("idCard") String idCard, @Param("startSecondTimestamp") long startSecondTimestamp , @Param("endSecondTimestamp") long endSecondTimestamp)throws Exception;

	/**
	 * @description: 查询驾驶员在某段时间内的不规范驾驶次数
	 * @author: zhouxw
	 * @date: 2022/11/21 11:21 AM
	 * @param: [deptId, startSecondTimestamp, endSecondTimestamp]
	 * @return: long
	 **/
	long getAlarmCountForDept(long deptId , long startSecondTimestamp , long endSecondTimestamp) throws Exception;
}
