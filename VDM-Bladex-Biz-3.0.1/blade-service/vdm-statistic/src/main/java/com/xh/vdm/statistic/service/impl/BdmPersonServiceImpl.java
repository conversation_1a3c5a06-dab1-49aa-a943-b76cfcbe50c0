package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.statistic.entity.BdmPerson;
import com.xh.vdm.statistic.mapper.BdmPersonMapper;
import com.xh.vdm.statistic.service.IBdmPersonService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 人员管理 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
public class BdmPersonServiceImpl extends ServiceImpl<BdmPersonMapper, BdmPerson> implements IBdmPersonService {

	@Override
	public List<BdmPerson> findPersonByIdCardOrPhone(String idCardOrPhone, String jobType) throws Exception {
		String idCard = null;
		String phone = null;
		if(!StringUtils.isEmpty(idCardOrPhone)){
			if(idCardOrPhone.length() > 11){
				idCard = idCardOrPhone;
			}else{
				phone = idCardOrPhone;
			}
		}
		List<BdmPerson> personList = baseMapper.getPerson(idCard, phone, jobType);
		return personList;
	}

	@Override
	public List<BdmPerson> findPersonByIdCard(String idCard) throws Exception {
		if(StringUtils.isEmpty(idCard)){
			log.error("[根据身份证号查询人员信息]查询失败，身份证号为空");
			return null;
		}
		return baseMapper.getPersonByIdCard(idCard);
	}
}
