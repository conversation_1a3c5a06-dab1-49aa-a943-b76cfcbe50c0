package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.report.StatReportMonth;
import com.xh.vdm.statistic.vo.request.CompanyAndDate;
import com.xh.vdm.statistic.vo.response.ReportInfoResponse;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 企业月报表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface StatReportMonthMapper extends BaseMapper<StatReportMonth> {

	IPage<ReportInfoResponse> getMonthReportPage (@Param("request") CompanyAndDate request, IPage<ReportInfoResponse> page);
}
