<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmVehicleDriverMapper">

    <select id="getDriverCountByDeptId" parameterType="long" resultType="int">
        select count(*) from bdm_vehicle_driver
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="getCertificateExpirationCount" parameterType="long" resultType="int">
        select count(*) from bdm_vehicle_driver
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=VARCHAR}
        </if>
        and expiration_date &lt; extract(epoch from now())
    </select>

    <select id="getCertificateIntimeCount" parameterType="long" resultType="int">
        select count(*) from bdm_vehicle_driver
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=VARCHAR}
        </if>
        and expiration_date >= extract(epoch from now())
    </select>

    <select id="getDriverAgeAndCount" parameterType="long" resultType="com.xh.vdm.statistic.entity.DriverAgeAndCount">
        select coalesce(age,'未知') age , count
        from(
                select (substring(now() , 1, 4) - SUBSTRING(to_timestamp(birth_date) , 1 ,4)) age , count(*) count
                from bdm_vehicle_driver
                where 1 = 1
                <if test="deptId != null and deptId != ''">
                    and dept_id = #{deptId,jdbcType=VARCHAR}
                </if>
                group by (substring(now() , 1, 4) - SUBSTRING(to_timestamp(birth_date) , 1 ,4))
            ) a
    </select>

    <select id="getDriverDriveAgeAndCount" parameterType="long" resultType="com.xh.vdm.statistic.entity.DriverDriveAgeAndCount">

        SELECT
        coalesce( drive_age, '未知' ) drive_age,
        count
        FROM
        (
        SELECT

        case first_issue_date
        when null then null
        when 0 then null
        else substring( now(), 1, 4 ) - SUBSTRING( to_timestamp( first_issue_date ), 1, 4 )
        end drive_age,
        count(*) count
        FROM
        bdm_vehicle_driver
        WHERE
        1 = 1
        <if test="deptId != null and deptId != ''">
            and dept_id = #{deptId,jdbcType=VARCHAR}
        </if>
        GROUP BY
        first_issue_date
        ) a

    </select>

</mapper>
