package com.xh.vdm.statistic.vo.request;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * @Description: 平台得分情况
 * @Author: zhouxw
 * @Date: 2022/9/13 1:37 PM
 */
@Data
public class PlatformScoreRequest {

    //日期格式：yyyy-MM
    @NotEmpty(message = "统计月份不能为空")
    private String month;


    @NotNull(message = "上级平台不能为空")
    private Long ownerId;
}
