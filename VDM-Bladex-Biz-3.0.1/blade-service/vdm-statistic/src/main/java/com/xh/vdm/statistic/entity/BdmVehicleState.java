package com.xh.vdm.statistic.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmVehicleState implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 车牌号
     */
    private String licencePlate;

    /**
     * 车牌颜色
     */
    private String licenceColor;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 高度
     */
    private Integer altitude;

    /**
     * 速度
     */
    private Double speed;

    /**
     * 方向
     */
    private Integer bearing;

    /**
     * 行驶状态
     */
    private Integer veState;

    /**
     * 在线状态
     */
    private Integer teState;

    /**
     * 定位地址
     */
    private String locAddr;

    /**
     * 当前里程
     */
    private Double curMileage;

    /**
     * 定位卫星数
     */
    private Integer satelliteNum;

    /**
     * 告警标识
     */
    private Integer alarmFlag;

    /**
     * 状态标识
     */
    private Integer stateFlag;

    /**
     * 定位时间
     */
    private Date locTime;

    /**
     * 定位接收时间
     */
    private Date recvTime;

    /**
     * 心跳时间
     */
    private Date beatTime;

    /**
     * 扩展车辆信号状态位
     */
    private Integer expandSignal;

    /**
     * IO状态位
     */
    private Integer ioStatus;

    /**
     * 温度
     */
    private String temperature;


}
