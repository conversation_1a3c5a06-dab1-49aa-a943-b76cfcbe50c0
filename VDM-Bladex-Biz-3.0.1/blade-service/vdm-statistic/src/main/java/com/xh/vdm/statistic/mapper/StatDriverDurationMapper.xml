<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.StatDriverDurationMapper">


    <delete id="deleteDataByDate" >
        delete from stat_driver_duration_${month} where stat_date = #{date,jdbcType=VARCHAR}
    </delete>


    <insert id="saveBatch" parameterType="com.xh.vdm.statistic.entity.StatDriverDuration" >

        insert into stat_driver_duration_${month}(stat_date , driver_name , id_card , drive_duration ,licence_plate, licence_color, create_time , duration_start_time, duration_end_time, state)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.statDate},#{item.driverName} , #{item.idCard} , #{item.driveDuration} , #{item.licencePlate} ,#{item.licenceColor}, #{item.createTime} , #{item.durationStartTime}, #{item.durationEndTime}, #{item.state})
        </foreach>

    </insert>

    <delete id="deleteAppendData">
        delete from stat_driver_duration_${month}
        where stat_date in (
            <foreach collection="dateList" item="date" separator=",">
                #{date}
            </foreach>
        )
    </delete>

    <select id="getDriverDurationByCondition" resultType="com.xh.vdm.statistic.entity.StatDriverDuration">
        select distinct licence_plate, driver_name from stat_driver_duration_${month}
        where duration_start_time >= #{request.startTime,jdbcType=BIGINT} and duration_end_time &lt;= #{request.endTime,jdbcType=BIGINT} and licence_plate = #{request.licencePlate,jdbcType=VARCHAR}
    </select>

    <select id="getDriverDurationListByIdCard" resultType="com.xh.vdm.statistic.entity.StatDriverDuration">
        select * from stat_driver_duration_${month}
        where id_card = #{idCard} and stat_date = #{statDate}
    </select>

    <select id="getDriverDurationListByIdCardMonth" resultType="com.xh.vdm.statistic.entity.StatDriverDuration">
        select * from stat_driver_duration_${month}
        where id_card = #{idCard}
    </select>

</mapper>
