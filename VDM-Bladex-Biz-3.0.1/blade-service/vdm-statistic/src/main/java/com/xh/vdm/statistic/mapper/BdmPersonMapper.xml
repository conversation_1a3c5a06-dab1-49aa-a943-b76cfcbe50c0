<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmPersonMapper">

    <select id="getPerson" resultType="com.xh.vdm.statistic.entity.BdmPerson">
        select * from bdm_person
        where is_del = 0
        <if test="jobType != null and jobType != ''">
            and job_type = #{jobType}
        </if>
        <if test="idCard != null and idCard != ''">
            and id_card = #{idCard}
        </if>
        <if test="phone != null and phone != ''">
            and phone = #{phone}
        </if>
    </select>

    <select id="getPersonByIdCard" resultType="com.xh.vdm.statistic.entity.BdmPerson">
        select * from bdm_person
        where is_del = 0
        and id_card = #{idCard}
    </select>
</mapper>
