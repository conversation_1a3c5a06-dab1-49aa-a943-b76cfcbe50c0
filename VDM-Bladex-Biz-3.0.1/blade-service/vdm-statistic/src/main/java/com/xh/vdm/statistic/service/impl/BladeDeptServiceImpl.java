
package com.xh.vdm.statistic.service.impl;

import com.xh.vdm.statistic.entity.BladeDept;
import com.xh.vdm.statistic.entity.DeptAndEmail;
import com.xh.vdm.statistic.entity.DeptTree;
import com.xh.vdm.statistic.mapper.BladeDeptMapper;
import com.xh.vdm.statistic.service.IBladeDeptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 机构表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-10
 */
@Service
public class BladeDeptServiceImpl extends ServiceImpl<BladeDeptMapper, BladeDept> implements IBladeDeptService {

	@Override
	public List<DeptAndEmail> findDeptAndEmail() throws Exception {
		return baseMapper.getDeptAndEmail();
	}

	@Override
	public List<DeptTree> findDeptTree(String tenantId) throws Exception {
		return baseMapper.getDeptTreeData(tenantId);
	}
}
