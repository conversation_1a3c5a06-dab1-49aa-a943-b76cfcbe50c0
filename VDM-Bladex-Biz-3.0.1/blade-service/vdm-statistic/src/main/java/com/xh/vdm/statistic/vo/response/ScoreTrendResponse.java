package com.xh.vdm.statistic.vo.response;

import lombok.Data;

/**
 * @Description: 指标评分趋势
 * @Author: zhouxw
 * @Date: 2022/11/16 8:32 PM
 */
@Data
public class ScoreTrendResponse {

    //统计月份
    private String month;
    //入网率得分
    private double inNetRateScore;
    //上线率得分
    private double goOnlineRateScore;
    //轨迹完整率得分
    private double completeRateScore;
    //漂移率得分
    private double driftRateScore;
    //数据合格率得分
    private double qualityRateScore;
    //平均超速次数得分
    private double overSpeedAverageScore;
    //平均疲劳时长得分
    private double fatigueAverageRateScore;
    //查岗响应率得分
    private double checkResponseRateScore;
}
