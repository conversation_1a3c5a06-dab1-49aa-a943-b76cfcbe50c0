<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.TerminalMapper">

    <select id="getTerminalTypeAndCount" parameterType="long" resultType="com.xh.vdm.statistic.entity.TerminalTypeAndCount">
        select terminal_type , count(*) count from bdm_terminal bt
        left join bdm_vehicle bv on bt.id = bv.terminal_id
        where 1 = 1
        <if test="deptId != null and deptId != ''">
          and bv.dept_id = #{deptId,jdbcType=BIGINT}
        </if>
          and bt.is_del = 0
          and bv.is_del = 0
        group by bt.terminal_type
    </select>

    <select id="getTerminalModelAndCount" parameterType="long" resultType="com.xh.vdm.statistic.entity.TerminalModelAndCount">
        select terminal_model , count(*) count from bdm_terminal bt
        left join bdm_vehicle bv on bt.id = bv.terminal_id
        where 1 = 1
        <if test="deptId != null and deptId != ''">
            and bv.dept_id = #{deptId,jdbcType=BIGINT}
        </if>
        and bt.is_del = 0
        and bv.is_del = 0
        group by bt.terminal_model
    </select>

    <select id="getTerminalList" resultType="java.util.Map">
        select phone, terminal_type from bdm_terminal;
    </select>

    <select id="getTerminals" resultType="com.xh.vdm.statistic.vo.TerminalBaseVO">
        <if test="deviceType != null and deviceType != ''">
            <if test="deviceType == 1">
                select device_num, target_type, target_id, device_type, id device_id, target_name,iot_protocol
                from bdm_rnss_device a
                where 1 = 1 and deleted = 0
                <if test="deviceNum != null and deviceNum != ''">
                    and device_num like #{deviceNum,jdbcType=VARCHAR}
                </if>
                <if test="deviceType != null and deviceType != ''">
                    and device_type = #{deviceType}
                </if>
                <if test="targetName != null and targetName != ''">
                    and target_name like #{targetName}
                </if>
                <if test="deptIds != null and deptIds != ''">
                    and dept_id = any(${deptIds})
                </if>
                <if test="account != null and account != ''">
                    and create_account = #{account}
                </if>
            </if>
            <if test="deviceType == 2">
                select device_num, target_type, target_id, device_type, id device_id, target_name,iot_protocol
                from bdm_wearable_device a
                where 1 = 1 and deleted = 0
                <if test="deviceNum != null and deviceNum != ''">
                    and device_num like #{deviceNum,jdbcType=VARCHAR}
                </if>
                <if test="deviceType != null and deviceType != ''">
                    and device_type = #{deviceType}
                </if>
                <if test="targetName != null and targetName != ''">
                    and target_name like #{targetName}
                </if>
                <if test="deptIds != null and deptIds != ''">
                    and dept_id = any(${deptIds})
                </if>
                <if test="account != null and account != ''">
                    and create_account = #{account}
                </if>
            </if>
            <if test="deviceType == 3">
                select device_num, target_type, target_id, device_type, id device_id, target_name,iot_protocol
                from bdm_rdss_device a
                where 1 = 1 and deleted = 0
                <if test="deviceNum != null and deviceNum != ''">
                    and device_num like #{deviceNum,jdbcType=VARCHAR}
                </if>
                <if test="deviceType != null and deviceType != ''">
                    and device_type = #{deviceType}
                </if>
                <if test="targetName != null and targetName != ''">
                    and target_name like #{targetName}
                </if>
                <if test="deptIds != null and deptIds != ''">
                    and dept_id = any(${deptIds})
                </if>
                <if test="account != null and account != ''">
                    and create_account = #{account}
                </if>
            </if>
            <if test="deviceType == 4">
                select device_num, target_type, target_id, device_type, id device_id, '' target_name,iot_protocol
                from bdm_monit_device a
                where 1 = 1 and deleted = 0
                <if test="deviceNum != null and deviceNum != ''">
                    and device_num like #{deviceNum,jdbcType=VARCHAR}
                </if>
                <if test="deviceType != null and deviceType != ''">
                    and device_type = #{deviceType}
                </if>
                <if test="deptIds != null and deptIds != ''">
                    and dept_id = any(${deptIds})
                </if>
                <if test="account != null and account != ''">
                    and create_account = #{account}
                </if>
            </if>
            <if test="deviceType == 5">
                select device_num, target_type, target_id, device_type, id device_id, '' target_name,iot_protocol
                from bdm_pnt_device a
                where 1 = 1 and deleted = 0
                <if test="deviceNum != null and deviceNum != ''">
                    and device_num like #{deviceNum,jdbcType=VARCHAR}
                </if>
                <if test="deviceType != null and deviceType != ''">
                    and device_type = #{deviceType}
                </if>
                <if test="deptIds != null and deptIds != ''">
                    and dept_id = any(${deptIds})
                </if>
                <if test="account != null and account != ''">
                    and create_account = #{account}
                </if>
            </if>
        </if>
        <if test="deviceType == null or deviceType == ''">
            select device_num, target_type, target_id, device_type, id device_id, target_name,iot_protocol
            from bdm_rnss_device a
            where 1 = 1 and deleted = 0
            <if test="deviceNum != null and deviceNum != ''">
                and device_num like #{deviceNum,jdbcType=VARCHAR}
            </if>
            <if test="deviceType != null and deviceType != ''">
                and device_type = #{deviceType}
            </if>
            <if test="targetName != null and targetName != ''">
                and target_name like #{targetName}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and dept_id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and create_account = #{account}
            </if>

            union

            select device_num, target_type, target_id, device_type, id device_id, target_name,iot_protocol
            from bdm_wearable_device a
            where 1 = 1 and deleted = 0
            <if test="deviceNum != null and deviceNum != ''">
                and device_num like #{deviceNum,jdbcType=VARCHAR}
            </if>
            <if test="deviceType != null and deviceType != ''">
                and device_type = #{deviceType}
            </if>
            <if test="targetName != null and targetName != ''">
                and target_name like #{targetName}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and dept_id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and create_account = #{account}
            </if>

            union

            select device_num, target_type, target_id, device_type, id device_id, target_name,iot_protocol
            from bdm_rdss_device a
            where 1 = 1 and deleted = 0
            <if test="deviceNum != null and deviceNum != ''">
                and device_num like #{deviceNum,jdbcType=VARCHAR}
            </if>
            <if test="deviceType != null and deviceType != ''">
                and device_type = #{deviceType}
            </if>
            <if test="targetName != null and targetName != ''">
                and target_name like #{targetName}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and dept_id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and create_account = #{account}
            </if>

            union

            select device_num, target_type, target_id, device_type, id device_id, '' target_name,iot_protocol
            from bdm_monit_device a
            where 1 = 1 and deleted = 0
            <if test="deviceNum != null and deviceNum != ''">
                and device_num like #{deviceNum,jdbcType=VARCHAR}
            </if>
            <if test="deviceType != null and deviceType != ''">
                and device_type = #{deviceType}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and dept_id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and create_account = #{account}
            </if>

            union

            select device_num, target_type, target_id, device_type, id device_id, '' target_name,iot_protocol
            from bdm_pnt_device a
            where 1 = 1 and deleted = 0
            <if test="deviceNum != null and deviceNum != ''">
                and device_num like #{deviceNum,jdbcType=VARCHAR}
            </if>
            <if test="deviceType != null and deviceType != ''">
                and device_type = #{deviceType}
            </if>
            <if test="deptIds != null and deptIds != ''">
                and dept_id = any(${deptIds})
            </if>
            <if test="account != null and account != ''">
                and create_account = #{account}
            </if>

        </if>
    </select>


    <select id="getTerminalOne" resultType="com.xh.vdm.statistic.vo.TerminalBaseVO">
        select bad.device_num, bat.target_type, bat.id target_id, bd.dept_name deptName,  bad.device_type, bad.id device_id, bat.name targetName
        from bdm_abstract_device bad
        left join bdm_abstract_target bat on bad.target_id = bat.id
        left join blade_dept bd on bad.dept_id = bd.id
        where bat.deleted = 0 and bad.deleted = 0
        <if test="deviceNum != null and deviceNum != ''">
            and bad.device_num = #{deviceNum,jdbcType=VARCHAR}
        </if>

        <if test="deptIds != null and deptIds != ''">
            and bd.id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
        limit 1
    </select>

</mapper>
