package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 超速分析报表（终端限速）返回类
 * <AUTHOR>
 * @date 2021/11/1 10:19
 */
@ApiModel(value = "超速分析报表（终端限速）返回类")
public class SegLimitSpeedTerminalResponse {

    @ApiModelProperty(value = "车牌号码")
    @JsonProperty("licence_plate")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "车牌号码"})
    @ColumnWidth(15)
    private String licencePlate;

    @ApiModelProperty(value = "企业名称")
    @JsonProperty("enterprise")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "企业名称"})
    @ColumnWidth(40)
    private String enterprise;

    @ApiModelProperty(value = "车队名称")
    @JsonProperty("dept_name")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "车队名称"})
    @ColumnWidth(40)
    private String deptName;

    @ApiModelProperty(value = "车牌颜色")
    @JsonProperty("licence_color")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "车牌颜色"})
    private String licenceColor;

    @ApiModelProperty(value = "行业类型")
    @JsonProperty("vehicle_model")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "行业类型"})
    @ColumnWidth(20)
    private String vehicleModel;

    @ApiModelProperty(value = "车辆归属")
    @JsonProperty("vehicle_owner")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "车辆归属"})
    @ColumnWidth(20)
    private String vehicleOwner;

    @ApiModelProperty(value = "车辆接入方式")
    @JsonProperty("access_mode")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "车辆接入方式"})
    @ColumnWidth(15)
    private String accessMode;

    @ApiModelProperty(value = "开始时间")
    @JsonProperty("start_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "开始时间"})
    @ColumnWidth(22)
    private Date startAlarmTime;

    @ApiModelProperty(value = "结束时间")
    @JsonProperty("end_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "结束时间"})
    @ColumnWidth(22)
    private Date endAlarmTime;

    @ApiModelProperty(value = "持续时间(秒)")
    @JsonProperty("duration_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "持续时间(秒)"})
    @ColumnWidth(20)
    private String durationTime;

    //去掉超速次数
    /*@JsonProperty("over_speed_count")
    @ApiModelProperty(value = "超速次数")
    private Long overSpeedCount;*/

    @ApiModelProperty(value = "最小速度(km/h)")
    @JsonProperty("min_speed")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "最小速度(km/h)"})
    @ColumnWidth(12)
    private Double minSpeed;

    @ApiModelProperty(value = "最大速度(km/h)")
    @JsonProperty("max_speed")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "最大速度(km/h)"})
    @ColumnWidth(12)
    private Double maxSpeed;

    @ApiModelProperty(value = "平均速度(km/h)")
    @JsonProperty("average_speed")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "平均速度(km/h)"})
    @ColumnWidth(12)
    private Double averageSpeed;

//    @ApiModelProperty(value = "超速里程(千米)")
//    @JsonProperty("over_speed_mileage")
//    private Double overSpeedMileage;

    @ApiModelProperty(value = "限速阈值(km/h)")
    @JsonProperty("limit_speed")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "限速阈值(km/h)"})
    @ColumnWidth(12)
    private Double speedLimit;

    @ApiModelProperty(value = "起点经度")
    @JsonProperty("start_longitude")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "起点经度"})
    @ColumnWidth(20)
    private Double startLongitude;

    @ApiModelProperty(value = "起点纬度")
    @JsonProperty("start_latitude")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "起点纬度"})
    @ColumnWidth(20)
    private Double startLatitude;

    @ApiModelProperty(value = "超速起点")
    @JsonProperty("start_address")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "超速起点"})
    @ColumnWidth(90)
    private String startAlarmAddress;

    @ApiModelProperty(value = "终点经度")
    @JsonProperty("end_longitude")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "终点经度"})
    @ColumnWidth(20)
    private Double endLongitude;

    @ApiModelProperty(value = "终点纬度")
    @JsonProperty("end_latitude")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "终点纬度"})
    @ColumnWidth(20)
    private Double endLatitude;

    @ApiModelProperty(value = "超速终点")
    @JsonProperty("end_address")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "超速终点"})
    @ColumnWidth(90)
    private String endAlarmAddress;

    @ApiModelProperty(value = "驾驶员信息")
    @JsonProperty("driver_info")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"超速分析报表（终端限速）", "驾驶员信息"})
    @ColumnWidth(40)
    private String driverInfo;



    public String getAccessMode() {
        return accessMode;
    }

    public void setAccessMode(String accessMode) {
        this.accessMode = accessMode;
    }

    public String getLicencePlate() {
        return licencePlate;
    }

    public void setLicencePlate(String licencePlate) {
        this.licencePlate = licencePlate;
    }

    public Date getStartAlarmTime() {
        return startAlarmTime;
    }

    public void setStartAlarmTime(Date startAlarmTime) {
        this.startAlarmTime = startAlarmTime;
    }

    public Date getEndAlarmTime() {
        return endAlarmTime;
    }

    public void setEndAlarmTime(Date endAlarmTime) {
        this.endAlarmTime = endAlarmTime;
    }

    public String getDurationTime() {
        return durationTime;
    }

    public void setDurationTime(String durationTime) {
        this.durationTime = durationTime;
    }

    /*public Long getOverSpeedCount() {
        return overSpeedCount;
    }*/

    /*public void setOverSpeedCount(Long overSpeedCount) {
        this.overSpeedCount = overSpeedCount;
    }*/

    public Double getMinSpeed() {
//        if(minSpeed==null||minSpeed==0){
//            return 0D;
//        }else{
////            return new BigDecimal((double)minSpeed/10).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
//            return minSpeed;
//        }
        if(minSpeed==null||minSpeed==0){
            minSpeed = 0D;
        }
        if(( minSpeed % 1) == 0){
            return new BigDecimal(minSpeed).doubleValue();
        }else{
            return new BigDecimal(minSpeed).setScale(1,BigDecimal.ROUND_HALF_UP).doubleValue();
        }
    }

    public void setMinSpeed(Double minSpeed) {
        this.minSpeed = minSpeed;
    }

    public Double getMaxSpeed() {
//        if(maxSpeed==null||maxSpeed==0){
//            return 0D;
//        }else{
////            return new BigDecimal((double)maxSpeed/10).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
//            return maxSpeed;
//        }

        if(maxSpeed==null||maxSpeed==0){
            maxSpeed = 0D;
        }
        if(( maxSpeed % 1) == 0){
            return new BigDecimal(maxSpeed).doubleValue();
        }else{
            return new BigDecimal(maxSpeed).setScale(1,BigDecimal.ROUND_HALF_UP).doubleValue();
        }
    }

    public void setMaxSpeed(Double maxSpeed) {
        this.maxSpeed = maxSpeed;
    }

    public Double getAverageSpeed() {
//        if(averageSpeed==null||averageSpeed==0){
//            return 0D;
//        }else{
////            return new BigDecimal((double)averageSpeed/10).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
//            return averageSpeed;
//        }

        if(averageSpeed==null||averageSpeed==0){
            averageSpeed = 0D;
        }
        if(( averageSpeed % 1) == 0){
            return new BigDecimal(averageSpeed).doubleValue();
        }else{
            return new BigDecimal(averageSpeed).setScale(1,BigDecimal.ROUND_HALF_UP).doubleValue();
        }
    }

    public void setAverageSpeed(Double averageSpeed) {
        this.averageSpeed = averageSpeed;
    }

//    public Double getOverSpeedMileage() {
//        return overSpeedMileage;
//    }
//
//    public void setOverSpeedMileage(Double overSpeedMileage) {
//        this.overSpeedMileage = overSpeedMileage;
//    }

//    public Double getSpeedLimit() {
//        return speedLimit;
//    }
//
//    public void setSpeedLimit(Double speedLimit) {
//        this.speedLimit = speedLimit;
//    }

    public Double getStartLongitude() {
        return startLongitude;
    }

    public void setStartLongitude(Double startLongitude) {
        this.startLongitude = startLongitude;
    }

    public Double getStartLatitude() {
        return startLatitude;
    }

    public void setStartLatitude(Double startLatitude) {
        this.startLatitude = startLatitude;
    }

    public String getStartAlarmAddress() {
        return startAlarmAddress;
    }

    public void setStartAlarmAddress(String startAlarmAddress) {
        this.startAlarmAddress = startAlarmAddress;
    }

    public Double getEndLongitude() {
        return endLongitude;
    }

    public void setEndLongitude(Double endLongitude) {
        this.endLongitude = endLongitude;
    }

    public Double getEndLatitude() {
        return endLatitude;
    }

    public void setEndLatitude(Double endLatitude) {
        this.endLatitude = endLatitude;
    }

    public String getEndAlarmAddress() {
        return endAlarmAddress;
    }

    public void setEndAlarmAddress(String endAlarmAddress) {
        this.endAlarmAddress = endAlarmAddress;
    }

    public String getDriverInfo() {
        return driverInfo;
    }

    public void setDriverInfo(String driverInfo) {
        this.driverInfo = driverInfo;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getLicenceColor() {
        return licenceColor;
    }

    public void setLicenceColor(String licenceColor) {
        this.licenceColor = licenceColor;
    }

    public String getVehicleModel() {
        return vehicleModel;
    }

    public void setVehicleModel(String vehicleModel) {
        this.vehicleModel = vehicleModel;
    }

    public Double getSpeedLimit() {

//        return speedLimit;
        if(speedLimit==null||speedLimit==0){
            speedLimit = 0D;
        }
        if(( speedLimit % 1) == 0){
            return new BigDecimal(speedLimit).doubleValue();
        }else{
            return new BigDecimal(speedLimit).setScale(1,BigDecimal.ROUND_HALF_UP).doubleValue();
        }
    }

    public void setSpeedLimit(Double speedLimit) {
        this.speedLimit = speedLimit;
    }

    public String getEnterprise() {
        return enterprise;
    }

    public void setEnterprise(String enterprise) {
        this.enterprise = enterprise;
    }

    public String getVehicleOwner() {
        return vehicleOwner;
    }

    public void setVehicleOwner(String vehicleOwner) {
        this.vehicleOwner = vehicleOwner;
    }
}
