package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xh.vdm.statistic.entity.report.StatReportDay;
import com.xh.vdm.statistic.mapper.StatReportDayMapper;
import com.xh.vdm.statistic.service.IStatReportDayService;
import com.xh.vdm.statistic.vo.request.CompanyAndDate;
import com.xh.vdm.statistic.vo.response.ReportInfoResponse;
import org.springblade.core.mp.support.Query;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 企业日报表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
@Service
public class StatReportDayServiceImpl extends ServiceImpl<StatReportDayMapper, StatReportDay> implements IStatReportDayService {

	@Override
	public IPage<ReportInfoResponse> findReportDayByPage(CompanyAndDate cd, Query query) throws Exception {
		IPage<ReportInfoResponse> page = new Page<>(query.getCurrent(), query.getSize());
		return baseMapper.getReportDayPage(cd, page);
	}
}
