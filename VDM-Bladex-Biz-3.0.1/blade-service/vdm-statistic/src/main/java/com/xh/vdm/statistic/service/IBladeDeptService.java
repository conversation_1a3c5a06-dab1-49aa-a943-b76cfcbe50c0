package com.xh.vdm.statistic.service;

import com.xh.vdm.statistic.entity.BladeDept;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.DeptAndEmail;
import com.xh.vdm.statistic.entity.DeptTree;

import java.util.List;

/**
 * <p>
 * 机构表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-10
 */
public interface IBladeDeptService extends IService<BladeDept> {


	/**
	 * 查询企业联系人邮箱
	 * @return
	 * @throws Exception
	 */
	List<DeptAndEmail> findDeptAndEmail() throws Exception;

	/**
	 * 查找部门树数据
	 * @param tenantId
	 * @return
	 * @throws Exception
	 */
	List<DeptTree> findDeptTree(String tenantId) throws Exception;

}
