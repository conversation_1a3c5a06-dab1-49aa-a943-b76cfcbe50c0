package com.xh.vdm.statistic.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 报表信息
 */
@ApiModel(value = "返回体：企业报告列表")
@Data
public class ReportInfoResponse {

	@JsonProperty("dept_id")
	@ApiModelProperty(name = "dept_id", value = "单位ID", example = "1", required = true)
	private Long deptId;

	@JsonProperty("dept_name")
	@ApiModelProperty(name = "dept_name", value = "单位名称", example = "海格", required = true)
	private String deptName;

	@JsonProperty("date")
	@ApiModelProperty(name = "date", value = "日期（支持日月，日格式：yyyy-MM，月格式yyyy-MM）", example = "2024-06-27或2024-06", required = true)
	private String date;

	@JsonProperty("summary_url")
	@ApiModelProperty(name = "summary_url", value = "汇总报告URL", example = "http://www.baidu.com", required = true)
	private String summaryUrl;

	@JsonProperty("alarm_url")
	@ApiModelProperty(name = "alarm_url", value = "报警报告URL", example = "http://www.baidu.com", required = true)
	private String alarmUrl;

	@JsonProperty("running_url")
	@ApiModelProperty(name = "running_url", value = "运行报告URL", example = "http://www.baidu.com", required = true)
	private String runningUrl;
}
