package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthRequest;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.response.CarMegaByteResponse;
import com.xh.vdm.statistic.vo.response.OnlineDaysCountResponse;
import com.xh.vdm.statistic.vo.response.UnexpectedReportResponse;
import com.xh.vdm.statistic.vo.response.VehicleRunningStateResponse;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import com.xh.vdm.statistic.vo.request.CommonStatRequest;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-25
 */
public interface IBdmVehicleService extends IService<BdmVehicle> {

	/**
	 * @description: 查询车辆总数
	 * @author: zhouxw
	 * @date: 2023-07-208 19:23:00
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long findAllVehicleCount(List<Long> deptIds, Long userId);

	/**
	 * 根据deptId查询车辆总数
	 * @param deptIds
	 * @return
	 */
	long findAllVehicleCountByDeptId(List<Long> deptIds);

	/**
	 * 查询证件过期车辆数量
	 * @param expireDate 指定过期时间
	 * @param deptIds 部门id列表
	 * @param userId 用户id
	 * @return
	 * @throws Exception
	 */
	long findExpiredCount(Date expireDate, List<Long> deptIds, Long userId) throws Exception;


	/**
	 * 查询已经过期的道路运输证的数量
	 * @param expireDate
	 * @param deptIds
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	long findHasExpiredCount(Date expireDate, List<Long> deptIds, Long userId) throws Exception;

	/**
	 * @description: 获取未知过期时间的证件的车辆数
	 * @author: zhouxw
	 * @date: 2023-07-205 21:56:05
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long findUnKnownExpireCount(List<Long> deptIds, Long userId);

	/**
	 * 分页查询车辆列表
	 * @param vehicleUseTypeList
	 * @param vehicleOwnerId
	 * @param accessMode
	 * @param query
	 * @return
	 */
	IPage<BdmVehicle> getVehicleListByPage (List<String> vehicleUseTypeList, Long vehicleOwnerId, String accessMode, Query query);

	/**
	 * @description: 查询营运车辆数
	 * @author: zhouxw
	 * @date: 2023-07-194 09:51:06
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long findRunningCount(List<Long> deptIds, Long userId);

	/**
	 * @description: 查询营运车辆数
	 * @author: zhouxw
	 * @date: 2023-07-194 09:51:06
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long findRunningCountByDeptId(List<Long> deptIds);

	/**
	 * @description: 查询离线车辆数，指定离线天数
	 * @author: zhouxw
	 * @date: 2023-07-207 14:26:31
	 * @param: [deptIds, userId, offlineDayCount: 离线天数，距离当天多少天未上线]
	 * @return: long
	 **/
	long findOfflineCountByOfflineDayCount(List<Long> deptIds, Long userId, int offlineDayCount) throws Exception;

	/**
	 * @description: 查询离线车辆明细，指定离线天数
	 * @author: zhouxw
	 * @date: 2023-07-207 14:26:31
	 * @param: [deptIds, userId, offlineDayCount: 离线天数，距离当天多少天未上线, limit 查询条数]
	 * @return: long
	 **/
	List<VehicleAndCount> findOfflineListByOfflineDayCount(List<Long> deptIds, Long userId, int offlineDayCount, int limit) throws Exception;

	/**
	 * 分页查询长期未上线车辆
	 * @param deptIds
	 * @return
	 * @throws Exception
	 */
	IPage<VehicleAndCount> findOfflineListByOfflineDayCountPage(List<Long> deptIds, Long userId, Integer offlineDayCount, String licencePlate, Long licenceColor, Query query) throws Exception;

	/**
	 * @description: 查询营运车辆
	 * @author: zhouxw
	 * @date: 2023-07-194 09:51:06
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	List<VehicleBase> findRunningVehicle(List<Long> deptIds, Long userId);

	/**
	 * @description: 查询停运车辆数
	 * @author: zhouxw
	 * @date: 2023-07-205 20:39:38
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long findStopRunningCount(List<Long> deptIds, Long userId);


	/**
	 * @description: 查询入网车辆数
	 * @author: zhouxw
	 * @date: 2023-07-194 10:24:20
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long findInNetCount(List<Long> deptIds, Long userId);

	/**
	 * @description: 上线车辆数
	 * @author: zhouxw
	 * @date: 2023-07-205 20:27:35
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long findGoOnlineCount(List<Long> deptIds, Long userId);

	/**
	 * 统计今日上过线的车辆数
	 * 今日上传过轨迹点的车辆数
	 * @param deptIds
	 * @param userId
	 * @return
	 */
	long findGoOnlineCountAllToday(List<Long> deptIds, Long userId);

	/**
	 * 统计指定月份上过线的车辆数
	 * 暂不考虑连续在线一个月的情况。比如，上个月上线的车辆，这个月没有产生上下线记录，到下个月再下线
	 * @param deptIds
	 * @param userId
	 * @param month yyyy-MM
	 * @return
	 */
	long findGoOnlineCountAllMonth(List<Long> deptIds, Long userId, String month) throws Exception;


	/**
	 * 统计指定月份上过线的车辆列表
	 * 暂不考虑连续在线一个月的情况。比如，上个月上线的车辆，这个月没有产生上下线记录，到下个月再下线
	 * @param month yyyy-MM
	 * @return
	 */
	List<VehicleAndCount> findGoOnlineDurationListAllMonthByVehicleId(List<Integer> vehicleId, String month, String lastDateInMonth) throws Exception;

	/**
	 * 查询指定天的上线车辆数
	 * 指定天累计在线车辆数
	 * @param deptIds
	 * @param date yyyy-MM-dd
	 * @return
	 */
	long findGoOnlineCountByDate(List<Long> deptIds, List<Integer> vehicleIds,String date);


	/**
	 * 查询指定天的上线车辆列表，指定车辆
	 * 指定天累计在线车辆数
	 * @param date yyyy-MM-dd
	 * @return
	 */
	List<VehicleAndCount> findGoOnlineDurationListByDateAndVehicleId(List<Integer> vehicleIds, String date);



	/**
	 * @description: 查询在线车辆数
	 * @author: zhouxw
	 * @date: 2023-07-194 14:41:04
	 * @param: [deptIds, userId]
	 * @return: long
	 **/
	long findOnlineCount(List<Long> deptIds, Long userId);

	/**
	 * 根据行业类型、车辆归属、接入方式获取车辆ID列表。
	 * @param vehicleUseTypeList
	 * @param vehicleOwnerId
	 * @param accessMode
	 * @return
	 */
	List<Integer> getVehicleIdList (List<String> vehicleUseTypeList, Long vehicleOwnerId, String accessMode);

	/**
	 * 获取特定用户可见的车辆，以Map形式返回，每个元素的key为车辆ID。
	 * @param request
	 * @return
	 */
	Map<Integer, BdmVehicle> getVehicleMap (CommonStatRequest request);


	/**
	 * 统计车辆运行状态
	 * @param user
	 * @return
	 */
	VehicleRunningStateResponse statVehicleRunningState(BladeUser user) throws Exception;

	/**
	 * 统计行驶车辆数
	 * @param deptIds
	 * @param userId
	 * @return
	 * @throws Exception
	 */
	long findRunningVehicleCount(List<Long> deptIds, Long userId) throws Exception;

	/**
	 * 根据动态sql查询数量
	 * @param sql
	 * @return
	 * @throws Exception
	 */
	List<OnlineDaysCountResponse> findCountByDynamicSql(String sql) throws Exception;

	/**
	 * 查询指定月份车辆的总里程
	 * @param dateList [yyyyMMdd]
	 * @param vehicleIdList
	 * @param month yyyyMM
	 * @return
	 */
	List<VehicleAndMileage> findTotalMileage(List<String> dateList, List<Long> vehicleIdList, String month);

	/**
	 * 分页查询指定日期的车辆里程情况
	 * @param deptIds
	 * @param vehicleIds
	 * @param month
	 * @param statDate
	 * @return
	 * @throws Exception
	 */
	IPage<VehicleAndMileage> findTotalMileagePage(List<Long> deptIds, List<Integer> vehicleIds, String month ,String statDate, Query query) throws Exception;

	/**
	 * 分页查询指定月份的车辆里程情况
	 * @param deptIds
	 * @param vehicleIds
	 * @param month
	 * @return
	 * @throws Exception
	 */
	IPage<VehicleAndMileage> findTotalMileageMonthPage(List<Long> deptIds, List<Integer> vehicleIds, String month, Query query) throws Exception;

	/**
	 * 根据车牌号、车牌颜色列表查询车辆信息
	 * @param plateAndColorKey 车牌号+"~"+车牌颜色
	 * @return
	 */
	List<BdmVehicle> findVehicleByLicencePlateAndColor(List<String> plateAndColorKey);

	/**
	 * 查询车辆在线详情
	 * @param vehicles 车牌号+"~"+车牌颜色 列表
	 * @param startTime 开始时间
	 * @param endTime 结束时间
	 * @return
	 */
	List<VehicleOnlineCount> findVehicleOnlineCount(List<String> vehicles, long startTime, long endTime) throws Exception;

	/**
	 * 根据deptId查询总里程
	 * @param deptIds
	 * @param date dd 如 02
	 * @param month yyyyMM
	 * @return
	 * @throws Exception
	 */
	double findTotalMileageByDeptIds(List<Long> deptIds, String date, String month) throws Exception;

	/**
	 * 根据条件分页查询车辆
	 * @param request
	 * @return
	 */
	IPage<BdmVehicle> findVehiclesByCondition(CommonBaseCrossMonthRequest request, Query query);

	/**
	 * 根据条件查询车辆总数
	 * @param request
	 * @return
	 */
	long findVehiclesByConditionCount(CommonBaseCrossMonthRequest request);

	/**
	 * 查询车辆基本信息
	 * @param vehicleIds
	 * @return
	 */
	List<VehicleCommonInfo> findVehicleCommonInfo(List<Long> vehicleIds);

	/**
	 * 车辆日流量统计
	 * @return
	 * @throws Exception
	 */
	IPage<CarMegaByteResponse> findVehicleMegaByteDaily(CommonBaseRequest request, Query query) throws Exception;


	/**
	 * 查询车辆报停异动信息
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 * @throws Exception
	 */
	IPage<UnexpectedReportResponse> findUnexpectedReport(CommonBaseRequest request, Query query, BladeUser user) throws Exception;

	/**
	 * 根据部门和车辆列表查询车辆基础信息
	 * @param deptIds
	 * @param vehicleIds
	 * @return
	 * @throws Exception
	 */
	List<VehicleBaseWithId> findVehicleBaseListAll(List<Long> deptIds, List<Integer> vehicleIds, String licencePlate) throws Exception;

	/**
	 * 按单位分组的车辆数
	 */
	Map<Long, Integer> getDeptMapNumVehicle (CommonStatRequest request);


	/**
	 * 统计车辆上线天数
	 * @param statDate yyyy-MM-dd
	 * @param vehicleIds
	 * @return
	 * @throws Exception
	 */
	List<VehicleAndCount> findOnlineDaysCount(String statDate, List<Integer> vehicleIds) throws Exception;

	/**
	 * 统计车辆上线天数
	 * @param month yyyy-MM
	 * @param vehicleIds
	 * @return
	 * @throws Exception
	 */
	List<VehicleAndCount> findOnlineDaysCountMonth(String month, List<Integer> vehicleIds) throws Exception;

	List<Integer> getVehicleIdListAll(BladeUser user) throws Exception;

	List<com.xh.vdm.statistic.entity.BdmVehicle> getVehicleListAll(BladeUser user) throws Exception;

	IPage<com.xh.vdm.statistic.entity.BdmVehicle> getVehicleListAllByPage (BladeUser user, long deptId, Query query) throws Exception;

}
