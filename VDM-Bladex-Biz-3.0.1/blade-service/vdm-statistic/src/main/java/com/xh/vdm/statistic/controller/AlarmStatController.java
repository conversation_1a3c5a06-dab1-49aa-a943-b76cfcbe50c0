package com.xh.vdm.statistic.controller;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xh.vdm.statistic.entity.AlarmTypeAndCount;
import com.xh.vdm.statistic.entity.DateAndData;
import com.xh.vdm.statistic.service.IBdmSecurityService;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.vo.response.TodayAndAverageAlarmCount;
import lombok.extern.slf4j.Slf4j;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description: 车辆安全预警分析
 * @Author: zhouxw
 * @Date: 2022/11/15 9:00 AM
 */
//均未测试
@RestController
@RequestMapping("/statistics/alarm")
@Slf4j
public class AlarmStatController {

    @Resource
    private IBdmSecurityService service;

    public static final ThreadLocal<SimpleDateFormat> sdfHolder = ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd"));

    public static final String EMERGENCY_ALARM = "Emergency";
    public static final String OVERSPEED_ALARM = "overSpeed";
    public static final String LIMITSPEED_ALARM = "limitSpeed";
    public static final String AREALINE_ALARM = "areaLine";
    public static final String ACTIVESECURE_ALARM = "activeSecure";


    /**
     * @description: 查询今日预警数量 和 本月平均每日预警数量
     * @author: zhouxw
     * @date: 2022/11/15 9:18 AM
     * @param: []
     * @return: com.xh.statistics.utils.R<com.xh.statistics.vo.response.TodayAndAverageAlarmCount>
     **/
    @GetMapping("/alarmCount")
    public R<TodayAndAverageAlarmCount> alarmCount(Long deptId){
        TodayAndAverageAlarmCount alarmCount = new TodayAndAverageAlarmCount();
        try {
            //获取今日时间戳
            Date today = new Date();
            //查询今日预警次数
            long todayAlarmCount = service.findAlarmCountByDay(deptId,today.getTime() / 1000);
            //查询本月的平均预警次数
            double averageAlarmCount = service.findAverageAlarmCountByMonth(deptId , today.getTime() / 1000);
            alarmCount.setAverageAlarmCount(MathUtil.roundDouble(averageAlarmCount , 2));
            alarmCount.setTodayAlarmCount(todayAlarmCount);
        }catch (Exception e){
            log.error("查询预警次数失败" , e);
            return R.fail("查询预警次数失败");
        }
        return R.data(alarmCount);
    }

    /**
     * @description: 统计月度平均预警趋势
     * 从当前月开始，向前统计，共12个月，每个月的平均每天的预警次数
     * @author: zhouxw
     * @date: 2022/11/15 9:49 AM
     * @param: []
     * @return: com.xh.statistics.utils.R<java.util.List<com.xh.statistics.entity.DateAndCount>>
     **/
    @GetMapping("/monthTrend")
    public R<List<DateAndData>> monthTrend (Long deptId ){
        List<DateAndData> list = new ArrayList<>();
        try{
            String month = sdfHolder.get().format(new Date()).substring(0 , 7);
            list = service.findMonthTrend(deptId , month);
        }catch (Exception e){
            log.error("查询月度预警趋势失败",e);
            return R.fail("查询月度预警趋势失败");
        }
        return R.data(list);
    }

    /**
     * @description: 统计本月的每日预警情况
     * @author: zhouxw
     * @date: 2022/11/15 9:52 AM
     * @param: []
     * @return: com.xh.statistics.utils.R<java.util.List<com.xh.statistics.entity.AlarmTypeAndCount>>
     **/
    @GetMapping("/dayAlarmInMonth")
    public R<Map<String, Map<String,Integer>>> dayAlarmInMonth(Long deptId){
        List<AlarmTypeAndCount> list = new ArrayList<>();
        try{
            String monthStr = sdfHolder.get().format(new Date()).substring(0,7);
            list = service.findAlarmTypeAndCountEveryDayInMonth(deptId , monthStr);

            //分类
            Map<String, Map<String,Integer>> res = getGroupedList(list);
            return R.data(res);
        }catch (Exception e){
            log.error("统计本月每日预警情况失败",e);
            return R.fail("统计本月每日预警情况失败");
        }
    }

    private Map<String, Map<String , Integer>> getGroupedList(List<AlarmTypeAndCount> list){
        Map<String,Map<String,Integer>> map = new HashMap<>();
        //区域/路线报警
        String[] areaLineAlarm = {"20","21","22","23","63","64","65","104","105","106","107","108","109","110"};
        List<String> areaLineAlarmList = Arrays.asList(areaLineAlarm);

        //主动安全：驾驶员高级辅助
        String[] driverAssist = {"181","211","212","213","214","215","216","217","218","219","274"};

        //主动安全：驾驶员状态监测
        String[] statMonitor = {"3","53","161","162","55","163","56","164","57","165","61","166","58","167","59","168","60","220"};

        //主动安全：激烈驾驶
        String[] sharpDrive = {"171","172","173","174"};

        List<String> activeSecure = new ArrayList<>();
        activeSecure.addAll(Arrays.asList(driverAssist));
        activeSecure.addAll(Arrays.asList(statMonitor));
        activeSecure.addAll(Arrays.asList(sharpDrive));


        //初始化map
        map.put(EMERGENCY_ALARM, new HashMap<>()); //紧急报警
        map.put(OVERSPEED_ALARM, new HashMap<>()); //超速报警
        map.put(LIMITSPEED_ALARM, new HashMap<>()); //分段限速报警
        map.put(AREALINE_ALARM, new HashMap<>()); //区域/线路报警
        map.put(ACTIVESECURE_ALARM , new HashMap<>()); //主动安全报警


        list.stream().forEach(item -> {
            Map<String ,Integer> mapItem = null;
            String alarmType = "";
            if(item.getAlarmType().equals("11")){
                //紧急报警
                alarmType = EMERGENCY_ALARM;
            }else if(item.getAlarmType().equals("1") || item.getAlarmType().equals("13") || item.getAlarmType().equals("101")){
                //超速报警
                //map.get(OVERSPEED_ALARM).add(dc);
                alarmType = OVERSPEED_ALARM;
            }else if(item.getAlarmType().equals("100")){
                //分段限速报警
                //map.get(LIMITSPEED_ALARM).add(dc);
                alarmType = LIMITSPEED_ALARM;
            }else if (areaLineAlarmList.contains(item.getAlarmType())){
                //区域/线路报警
                alarmType = AREALINE_ALARM;
                //map.get(AREALINE_ALARM).add(dc);
            }else if(activeSecure.contains(item.getAlarmType())){
                //主动安全报警
                alarmType = ACTIVESECURE_ALARM;
                //map.get(ACTIVESECURE_ALARM).add(dc);
            }
            mapItem = new HashMap<>();
            mapItem.put(item.getDate() , item.getCount());
            //限定指定的告警类型
            if(!StringUtils.isBlank(alarmType)) {
                if (map.get(alarmType) == null) {
                    map.put(alarmType, new HashMap<>());
                } else {
                    if (map.get(alarmType).containsKey(item.getDate())) {
                        map.get(alarmType).put(item.getDate(), (item.getCount() + map.get(alarmType).get(item.getDate())));
                    } else {
                        map.get(alarmType).put(item.getDate(), item.getCount());
                    }
                }
            }
        });
        return map;
    }
}
