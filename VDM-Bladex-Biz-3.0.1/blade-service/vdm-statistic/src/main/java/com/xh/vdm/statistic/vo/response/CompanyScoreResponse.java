package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @Description: 企业考核分析返回结果
 * @Author: zhouxw
 * @Date: 2022/11/15 3:42 PM
 */
@Data
public class CompanyScoreResponse {

    //企业id
    @JsonIgnore
    @ExcelIgnore
    private String deptId;
    //企业名称
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "企业名称"})
    private String deptName;
    //统计月份
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "统计月份"})
    private String month;
    //入网率
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "入网率"})
    @ExcelIgnore
    private double inNetRate;
    //入网率（百分数表示）
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "入网率"})
    private String inNetRateStr;
    //入网分数
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "入网率考核得分"})
    private double inNetScore;
    //上线率
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "上线率"})
    @ExcelIgnore
    private double goOnlineRate;
    //上线率（百分数表示）
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "上线率"})
    private String goOnlineRateStr;
    //上线分数
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "上线率考核得分"})
    private double goOnlineRateScore;

    //平台连通率
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "平台连通率"})
    @ExcelIgnore
    private double connectionRate;
    //平台连通率率（百分数表示）
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "平台连通率"})
    private String connectionRateStr;
    //平台连通率分数
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "平台连通率得分"})
    private double connectionRateScore;

    //完整率
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "完整率"})
    @ExcelIgnore
    private double completeRate;
    //完整率（百分数表示）
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "完整率"})
    private String completeRateStr;
    //完整率分数
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "完整率考核得分"})
    private double completeScore;
    //漂移率
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "漂移率"})
    @ExcelIgnore
    private double driftRate;
    //漂移率（百分数表示）
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "漂移率"})
    private String driftRateStr;
    //漂移分数
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "漂移率考核得分"})
    private double driftScore;
    //数据合格率
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "数据合格率"})
    @ExcelIgnore
    private double qualityRate;
    //数据合格率（百分数表示）
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "数据合格率"})
    private String qualityRateStr;
    //数据合格率分数
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "数据合格率考核得分"})
    private double qualityScore;
    //平均超速次数
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "平均车辆超速次数"})
    private double overSpeedCount;
    //平均超速分数
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "平均车辆超速次数考核得分"})
    private double overSpeedScore;
    //平均疲劳时长
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "平均疲劳驾驶时长"})
    private double fatigueDuration;
    //平均疲劳分数
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "平均疲劳驾驶时长考核得分"})
    private double fatigueScore;
    //平台查岗响应率 100%
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "平台查岗响应率"})
    private String checkResponseRate;
    //平台查岗响应率得分 10
    @ColumnWidth(30)
    @ExcelProperty({"企业考核分析", "平台查岗响应率考核得分"})
    private double checkResponseScore;

}
