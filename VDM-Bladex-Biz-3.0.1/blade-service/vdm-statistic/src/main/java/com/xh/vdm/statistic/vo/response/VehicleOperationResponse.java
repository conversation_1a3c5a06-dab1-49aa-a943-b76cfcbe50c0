package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.config.ExcelDateConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车辆运行情况巡检返回类
 * <AUTHOR>
 * @date 2021/10/26 13:37
 */
@ApiModel(value = "车辆运行情况巡检")
@Data
public class VehicleOperationResponse {

	@JsonIgnore
	@ExcelIgnore
	private Integer vehicleId;

    @ApiModelProperty(value = "车牌号码")
    @JsonProperty("licence_plate")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "车牌号码"})
    @ColumnWidth(15)
    private String licencePlate;

    @ApiModelProperty(value = "车队名称")
    @JsonProperty("dept_name")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "企业名称"})
    @ColumnWidth(40)
    private String deptName;

	@JsonIgnore
	@ExcelIgnore
	private Long deptId;

    @ApiModelProperty(value = "车牌颜色")
    @JsonProperty("licence_color")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "车牌颜色"})
    @ColumnWidth(15)
    private String licenceColor;

	@JsonIgnore
	@ExcelIgnore
	private String licenceColorCode;

    @ApiModelProperty(value = "行业类型")
    @JsonProperty("vehicle_use_type")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "行业类型"})
    @ColumnWidth(20)
    private String vehicleUseType;

	@JsonIgnore
	@ExcelIgnore
	private String vehicleUseTypeCode;

    @ApiModelProperty(value = "车辆归属")
    @JsonProperty("vehicle_owner")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "车辆归属"})
    @ColumnWidth(20)
    private String vehicleOwner;

	@JsonIgnore
	@ExcelIgnore
	private Long vehicleOwnerId;

    @ApiModelProperty(value = "车辆接入方式")
    @JsonProperty("access_mode")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "车辆接入方式"})
    @ColumnWidth(15)
    private String accessMode;

	@JsonIgnore
	@ExcelIgnore
	private String accessModeCode;

    @ApiModelProperty(value = "抽查时间")
    @JsonProperty("sample_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty(value = {"车辆运行情况巡检报表", "抽查时间"},converter = ExcelDateConverter.class)
    @ColumnWidth(22)
    private Date checkTime;

    @ApiModelProperty(value = "车辆点名")
    @JsonProperty("online_state")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "车辆点名"})
    @ColumnWidth(12)
    private String onOff;

    @ApiModelProperty(value = "行驶轨迹")
    @JsonProperty("driving_track")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "行驶轨迹"})
    @ColumnWidth(12)
    private String track;

    @ApiModelProperty(value = "最后上线时间")
    @JsonProperty("latest_online_time")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty(value = {"车辆运行情况巡检报表", "最后上线时间"}, converter = ExcelDateConverter.class)
    @ColumnWidth(22)
    private Date lastOnlineTime;

    @ApiModelProperty(value = "车速(km/h)")
    @JsonProperty("speed")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "车速(km/h)"})
    @ColumnWidth(12)
    private Double speed;

    @ApiModelProperty(value = "经度")
    @JsonProperty("longitude")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "经度"})
    @ColumnWidth(20)
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    @JsonProperty("latitude")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "纬度"})
    @ColumnWidth(20)
    private Double latitude;

    @ApiModelProperty(value = "地理位置")
    @JsonProperty("address")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "地理位置"})
    @ColumnWidth(90)
    private String locAddr;

    @ApiModelProperty(value = "监控人员")
    @JsonProperty("monitor_persion")
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"车辆运行情况巡检报表", "监控人员"})
    @ColumnWidth(20)
    private String monitor;


    public String getAccessMode() {
        return accessMode;
    }

    public void setAccessMode(String accessMode) {
        this.accessMode = accessMode;
    }

    public String getLicencePlate() {
        return licencePlate;
    }

    public void setLicencePlate(String licencePlate) {
        this.licencePlate = licencePlate;
    }


    public String getDeptName() {
        return deptName;
    }

    public void setTeam(String team) {
        this.deptName = team;
    }

    public Date getCheckTime() {
        return new Date();
    }

    public void setCheckTime(Date checkTime) {
        this.checkTime = checkTime;
    }

    public String getLicenceColor() {
        return licenceColor;
    }

    public void setLicenceColor(String licenceColor) {
        this.licenceColor = licenceColor;
    }

    public String getVehicleUseType() {
        return vehicleUseType;
    }

    public void setVehicleType(String vehicleType) {
        this.vehicleUseType = vehicleType;
    }

    public String getOnOff() {
        return onOff;
    }

    public void setOnOff(String onOff) {
        this.onOff = onOff;
    }

    public String getTrack() {
        return track;
    }

    public void setTrack(String track) {
        this.track = track;
    }

    public Date getLastOnlineTime() {
        return lastOnlineTime;
    }

    public void setLastOnlineTime(Date lastOnlineTime) {
        this.lastOnlineTime = lastOnlineTime;
    }

    public Double getSpeed() {
//        if(speed==null||speed==0){
//            return 0D;
//        }else{
////            return new BigDecimal((double)speed/10).setScale(2,BigDecimal.ROUND_HALF_UP).doubleValue();
//            return speed;
//        }
        if(speed==null||speed==0){
            speed = 0D;
        }
        if(( speed % 1) == 0){
            return new BigDecimal(speed).doubleValue();
        }else{
            return new BigDecimal(speed).setScale(1,BigDecimal.ROUND_HALF_UP).doubleValue();
        }
    }

    public void setSpeed(Double speed) {
        this.speed = speed;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public String getLocAddr() {
        return locAddr;
    }

    public void setLocAddr(String locAddr) {
        this.locAddr = locAddr;
    }

    public String getMonitor() {
        return monitor;
    }

    public void setMonitor(String monitor) {
        this.monitor = monitor;
    }

    public String getVehicleOwner() {
        return vehicleOwner;
    }

    public void setVehicleOwner(String vehicleOwner) {
        this.vehicleOwner = vehicleOwner;
    }
}
