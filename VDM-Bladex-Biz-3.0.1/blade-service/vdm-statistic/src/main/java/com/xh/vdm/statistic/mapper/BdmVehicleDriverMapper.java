package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.BdmVehicleDriver;
import com.xh.vdm.statistic.entity.DriverAgeAndCount;
import com.xh.vdm.statistic.entity.DriverDriveAgeAndCount;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-18
 */
public interface BdmVehicleDriverMapper extends BaseMapper<BdmVehicleDriver> {

    /**
     * @description: 根据企业id查询驾驶员总数
     * 如果不指定 deptId，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 4:01 PM
     * @param: [deptId]
     * @return: int
     **/
    int getDriverCountByDeptId(Long deptId) throws Exception;

    /**
     * @description: 查询从业资格证过期的驾驶员数量
     * 如果不指定 deptId , 则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 4:46 PM
     * @param: [deptId]
     * @return: int
     **/
    int getCertificateExpirationCount(Long deptId) throws Exception;

    /**
     * @description: 查询从业资格证未过期的驾驶员数量
     * 如果不指定 deptId，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 4:50 PM
     * @param: [deptId]
     * @return: int
     **/
    int getCertificateIntimeCount(Long deptId) throws Exception;

    /**
     * @description: 查询驾驶员年龄及数量
     * * 如果不指定 deptId，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 5:04 PM
     * @param: [deptId]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DriverAgeAndCount>
     **/
    List<DriverAgeAndCount> getDriverAgeAndCount(Long deptId) throws Exception;

    /**
     * @description: 查询驾驶员驾龄及数量
     * * 如果不指定 deptId，则查询全区域
     * @author: zhouxw
     * @date: 2022/11/20 5:07 PM
     * @param: [deptId]
     * @return: java.util.List<com.xh.vdm.statistic.entity.DriverDriveAgeAndCount>
     **/
    List<DriverDriveAgeAndCount> getDriverDriveAgeAndCount(Long deptId) throws Exception;



}
