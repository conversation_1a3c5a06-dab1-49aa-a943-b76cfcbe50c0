package com.xh.vdm.statistic.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * @Description: 动态表名配置
 * @Auther: zxing
 * @Date: 2020/10/30 14:27
 */
@Data
@Configuration
@ConfigurationProperties(value = "mp")
public class DynamicTablesProperties {

    /**
       * @Description 年表名称列表
    	* <AUTHOR>
    	* @Date 2020/10/30 14:28
    	* @Company CTTIC
    	* @Param
    	* @return
    	**/
    private List<String> yearTableNames;

    /**
       * @Description 日表名称列表
    	* <AUTHOR>
    	* @Date 2020/10/30 14:29
    	* @Company CTTIC
    	* @Param
    	* @return
    	**/
    private List<String> dayTableNames;

    /**
       * @Description 月表名称列表
    	* <AUTHOR>
    	* @Date 2020/11/18 18:36
    	* @Company CTTIC
    	* @Param
    	* @return
    	**/

    private List<String> monthTableNames;
}
