package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.xh.vdm.statistic.vo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 微信小程序车辆报表查询条件
 * <AUTHOR>
 * @date 2021/11/1 10:19
 */
@ApiModel(value = "微信小程序 车辆报表 查询条件")
@Data
public class VehicleReportRequest extends PageParam {

    @ApiModelProperty(value = "查询开始时间")
    private Long startTime;

    @ApiModelProperty(value = "查询结束时间")
    private Long endTime;

	@JsonProperty("vehicleIdList")
	private List<Long> vehicleIdList;

	private List<String> dateList;

	//租户id
	private String tenantId;
	//登录用户id
	private Long userId;

    //所有车组，包含子车组
    private List<Long> deptList;

}
