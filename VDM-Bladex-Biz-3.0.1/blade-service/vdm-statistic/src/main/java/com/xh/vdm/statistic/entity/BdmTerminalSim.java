package com.xh.vdm.statistic.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-08-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BdmTerminalSim implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * sim卡号
     */
    private String simId;

    /**
     * sim卡iccid
     */
    private String iccid;

    /**
     * SIM卡imsi
     */
    private String imsi;

    /**
     * 客户名称
     */
    private String customer;

    /**
     * 运营商
     */
    private String operator;

    /**
     * 卡状态
     */
    private String cardState;

    /**
     * 发卡日期
     */
    private Date releaseDate;

    /**
     * 激活时间
     */
    private Date activeDate;

    /**
     * 服务期止
     */
    private Date endDate;

    /**
     * 流量套餐，1:30M/月；2：50M/月；3:1G/月；4:2G/月；5:4G/月；10：10G/月；20:20G/月
     */
	@TableField("package")
	@JsonProperty("package")
    private String packageName;

    /**
     * 流量套餐总量MB
     */
    private BigDecimal packageTotal;

    /**
     * 卡类型，1：定向流量卡；2：公网物联网卡；3：电话卡
     */
    private String cardType;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 删除标记：0-未删除，1-已删除
     */
    private Integer isDel;

    /**
     * 创建用户Id
     */
    private Long createUserId;


}
