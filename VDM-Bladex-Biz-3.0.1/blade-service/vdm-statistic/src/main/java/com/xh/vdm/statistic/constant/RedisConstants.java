package com.xh.vdm.statistic.constant;

/**
 * redis的key名称常量类
 * <AUTHOR>
 * @date Created in 16:48 2021/4/23
 * @description
 */
public class RedisConstants {
    /**
     * string类型的key值
     */
    public static class StringKey{

        /**
         * 登录的用户简略信息缓存前缀
         */
        public static final String LOGINED_USER_CACHE_PREFIX = "auth:";
    }

    /**
     * hash类型的key值
     */
    public static class HashKey {

        /**
         * 组织具体信息表
         */
        public static final String VEHICLE_INFO_HASH_KEY = "VEHICLE-INFO";

        /**
         *数据字典表，后面加字典类型
         */
        public static final String DICTIONARY_HASH_KEY = "DICTIONARY-TYPE:";
        /**
         * 部门与子部门关系
         */
        public static final String DEPT_INFO_HASH_KEY ="DEPT-INFO";
    }

}
