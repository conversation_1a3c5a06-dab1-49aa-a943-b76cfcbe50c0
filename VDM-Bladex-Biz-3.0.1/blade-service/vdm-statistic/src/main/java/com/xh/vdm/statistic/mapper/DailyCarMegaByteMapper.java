package com.xh.vdm.statistic.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xh.vdm.statistic.entity.DailyCarMegaByte;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 映射：每日车辆数据大小
 */
public interface DailyCarMegaByteMapper extends BaseMapper<DailyCarMegaByte> {

    /**
     * 写入每日数据
     */
    @DS("master")
    void setDaily (@Param("list") List<DailyCarMegaByte> list);
}
