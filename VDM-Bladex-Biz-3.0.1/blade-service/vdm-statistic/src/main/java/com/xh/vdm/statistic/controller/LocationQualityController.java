package com.xh.vdm.statistic.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.config.ExcelExportHeaderColumnConfig;
import com.xh.vdm.statistic.config.ExcelExportHeaderFontConfig;
import com.xh.vdm.statistic.entity.DetailDayParam;
import com.xh.vdm.statistic.entity.DetailParam;
import com.xh.vdm.statistic.service.ILocationQualityService;
import com.xh.vdm.statistic.utils.CacheUtil;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.utils.R;
import com.xh.vdm.statistic.vo.request.DetailRequest;
import com.xh.vdm.statistic.vo.request.RateRequest;
import com.xh.vdm.statistic.vo.response.LocationQualityDetailResponse;
import com.xh.vdm.statistic.vo.response.LocationQualityResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @Description: 数据合格率相关接口
 * @Author: zhouxw
 * @Date: 2022/8/30 2:47 PM
 */
@RequestMapping("/bt/statistics/data")
@RestController
@Slf4j
public class LocationQualityController {

    @Resource
    private ILocationQualityService locationQualityService;


    @Value("${static.file.path:/statistic/files/}")
    String staticFilePath;


    @Resource
    private CacheUtil cacheUtil;

    /**
     * @description: 数据合格率接口
     * @author: zhouxw
     * @date: 2022/9/6 4:00 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.xh.vdm.statistic.vo.response.LocationQualityResponse>
     **/
    @PostMapping("/qualifiedRate")
    public R<LocationQualityResponse> qualifiedRate(@RequestBody @Validated RateRequest request){

        //1.二次校验
        if(StringUtils.isBlank(request.getMonth()) || request.getOwnerId() == null){
            return R.fail("请求参数不能为空");
        }

        //2.执行数据查询
        LocationQualityResponse response = null;
        try{
            response = locationQualityService.findLocaitonQualityRate(request);
            if(response == null){
                //return R.fail("未查询到数据合格率信息");
                //未查询到数据合格率信息，则初始化返回对象
                log.info("[数据合格率]未查询到数据合格率信息");
                return R.data(new LocationQualityResponse() , "未查询到数据合格率信息");
            }
            int totalCount = response.getTotalCount();
            int totalRightCount = totalCount - response.getTotalErrorCount();
            double qualityRate = MathUtil.divideRoundDouble(totalRightCount , totalCount , 4);
            double score = 0;
            if(qualityRate >= 0.8){
                //按照服务商标准计算得分
                score = qualityRate * 25;
            }else{
                score = 0;
            }
            response.setScore(MathUtil.roundDouble(score, 4));
            response.setQualifiedRate(MathUtil.roundDouble(qualityRate , 4));
        }catch (Exception e){
            e.printStackTrace();
            log.error("查询数据合格率失败",e);
            return R.fail("统计数据合格率失败");
        }
        return R.data(response);
    }

    /**
     * @description: 查询车辆不合格定位数据明细
     * @author: zhouxw
     * @date: 2022/9/6 6:25 PM
     * @param: [request]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.LocationQualityDetailResponse>
     **/
    @PostMapping("/unQualified")
    public R<IPage<List<LocationQualityDetailResponse>>> unQualified(@RequestBody @Validated DetailRequest request){
        //前端下标从 0 开始，此处要加1
        request.setStart(request.getStart()+1);

        //需要查询指定月份的、每天的车辆明细
        DetailParam param = new DetailParam();
        BeanUtils.copyProperties(request , param);
        if(request.getCount() == 0){
            request.setCount(10);
        }
        if(request.getStart() <= 0){
            request.setStart(1);
        }
        param.setCurrent(request.getStart()/request.getCount()+(request.getStart() % request.getCount()>0?1:0));
        param.setSize(request.getCount());
        List<LocationQualityDetailResponse> list = null;
        IPage page = new Page();
        try{
            list = locationQualityService.findUnCompleteDetailList(param);
            page.setCurrent(param.getCurrent());
            page.setSize(param.getSize());
            page.setRecords(list);
            DetailDayParam dayParam = new DetailDayParam();
            BeanUtils.copyProperties(param,dayParam);
            dayParam.setDay("");
            int totalErrorCount = locationQualityService.findTotalErrorCount(dayParam);
            int pages = 0;
            if(totalErrorCount % request.getCount() > 0){
                pages = totalErrorCount / request.getCount() + 1;
            }else{
                pages = totalErrorCount / request.getCount();
            }
            page.setTotal(totalErrorCount);
            page.setPages(pages);
        }catch (Exception e){
            e.printStackTrace();
            log.error("查询车辆不合格定位数据明细失败" , e);
            return R.fail("查询车辆不合格定位数据明细失败");
        }
        return R.data(page);

    }


    /**
     * @description: 导出车辆不合格定位数据明细
     * @author: zhouxw
     * @date: 2022/9/14 4:16 PM
     * @param: [request]
     * @return: com.xh.vdm.statistic.utils.R<com.baomidou.mybatisplus.core.metadata.IPage<java.util.List<com.xh.vdm.statistic.vo.response.LocationQualityDetailResponse>>>
     **/
    @PostMapping("/unQualifiedExport")
    public R<String> unQualifiedExport(@RequestBody @Validated DetailRequest request){

        List<LocationQualityDetailResponse> list = null;

        try{

            //1.查询所有数据

            DetailParam param = new DetailParam();
            BeanUtils.copyProperties(request , param);
            param.setCurrent(0);
            param.setSize(Integer.MAX_VALUE);
            list = locationQualityService.findUnCompleteDetailList(param);



            //3.导出数据
            String title = "不合格车辆明细";
            Date date = new Date();
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HHmmss");
            String dateStr = format.format(date);
            String fileName = title + "_" + dateStr + ".xlsx";
            FileOutputStream file = new FileOutputStream(staticFilePath + fileName);
            fileName =  "/bt/statistics/files/"+fileName;

            //主标题和副标题在excel中分别是是第0和第1行
            List<Integer> columnIndexes = Arrays.asList(0,1);
            //自定义标题和内容策略(具体定义在下文)
            ExcelExportHeaderFontConfig cellStyleStrategy =
                    new ExcelExportHeaderFontConfig(columnIndexes,new WriteCellStyle(), new WriteCellStyle());

            EasyExcel.write(file, LocationQualityDetailResponse.class).registerWriteHandler(cellStyleStrategy).registerWriteHandler(new ExcelExportHeaderColumnConfig()).sheet(title).doWrite(list);
            return R.data(fileName);
        }catch (Exception e){
            e.printStackTrace();
            log.error("查询车辆不合格定位数据明细失败" , e);
            return R.fail("查询车辆不合格定位数据明细失败");
        }

    }


}
