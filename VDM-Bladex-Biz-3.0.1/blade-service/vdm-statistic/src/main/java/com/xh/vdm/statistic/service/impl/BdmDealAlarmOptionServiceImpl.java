package com.xh.vdm.statistic.service.impl;

import com.xh.vdm.statistic.entity.report.BdmDealAlarmOption;
import com.xh.vdm.statistic.mapper.BdmDealAlarmOptionMapper;
import com.xh.vdm.statistic.service.IBdmDealAlarmOptionService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 报警处理选项表（作用于服务商或第三方） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-09
 */
@Service
public class BdmDealAlarmOptionServiceImpl extends ServiceImpl<BdmDealAlarmOptionMapper, BdmDealAlarmOption> implements IBdmDealAlarmOptionService {

}
