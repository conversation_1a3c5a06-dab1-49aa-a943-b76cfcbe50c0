package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.CacheVehicleOnlineOrOfflineResponse;
import com.xh.vdm.statistic.entity.TeState;
import com.xh.vdm.statistic.vo.request.CommonBaseRequest;
import com.xh.vdm.statistic.vo.request.VehicleOnlineOrOfflineRequest;
import com.xh.vdm.statistic.vo.response.VehicleOnlineOrOfflineResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 车辆上下线查询
 *
 * <AUTHOR>
 * @since 2022-09-14
 */
public interface CacheVehicleOnlineOrOfflineMapper extends BaseMapper<CacheVehicleOnlineOrOfflineResponse> {

    /**
     * @description: 查询车辆上下线信息
     * @author: zhouxw
     * @date: 2023-02-40 17:46:00
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    List<CacheVehicleOnlineOrOfflineResponse> getList(@Param("param") VehicleOnlineOrOfflineRequest param);


    /**
     * @description: 分页查询车辆上下线信息
     * @author: zhouxw
     * @date: 2023-02-40 17:46:00
     * @param: []
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    IPage<VehicleOnlineOrOfflineResponse> getList(@Param("request") CommonBaseRequest param, IPage<VehicleOnlineOrOfflineResponse> page);

    /**
     * @description: 查询车辆实时在线状态
     * @author: zhouxw
     * @date: 2023-03-81 10:02:13
     * @param: [vehicle]
     * @return: java.util.List<com.xh.vdm.statistic.entity.VeState>
     **/
    List<TeState> getVeState(List<String> params);


}
