package com.xh.vdm.statistic.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.report.*;
import com.xh.vdm.statistic.entity.wx.AlarmInfoWX;
import com.xh.vdm.statistic.entity.wx.VehicleBaseInfoWX;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.CommonBusiUtil;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.utils.VdmUserInfoUtil;
import com.xh.vdm.statistic.vo.response.OnlineHourCountResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.AlarmConstant;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.system.cache.SysCache;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Slf4j
public class ReportServiceImpl implements IReportService {

	@Resource
	private IBladeDeptService deptService;

	@Resource
	private IBdmVehicleService vehicleService;

	@Resource
	private IStatVehOnlineDayService onlineDayService;

	@Resource
	private IVehicleStatService vehicleStatService;

	@Resource
	private IStatVehRunningStateService vehRunningStateService;

	@Resource
	private VdmUserInfoUtil vdmUserInfoUtil;

	@Resource
	private CommonBusiUtil commonBusiUtil;

	@Resource
	private IAlarmService alarmService;

	@Resource
	private IImpalaAlarmService impalaAlarmService;

	@Resource
	private IBdmDealAlarmOptionService alarmOptionService;

	@Resource
	private IStatVehTravelDurationService vehTravelDurationService;

	@Resource
	private IBamThirdPartyPlatformService thirdPartyPlatformService;

	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(20, 50, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

	/**
	 * 统计企业概括情况
	 * @param deptId 部门id
	 * @param statDate 统计日期 yyyy-MM-dd / yyyy-MM
	 * @param startSecondTime 开始时间戳（精确到秒）
	 * @param endSecondTime 结束时间戳（精确到秒）
	 * 注意：在日报和月报的统计中，不包含账号绑定的车辆
	 * @return
	 * @throws Exception
	 */
	@Override
	public ReportSummary reportSummary(Long deptId, String statDate, Long startSecondTime, Long endSecondTime) throws Exception {

		ReportSummary rs = new ReportSummary();

		//1.企业基本情况
		BladeDept dept = deptService.getById(deptId);
		if(dept == null){
			log.error("该企业不存在");
			return null;
		}
		String deptName = dept.getDeptName();
		rs.setDeptName(deptName);
		rs.setDateStr(statDate);


		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
		//车辆总数
		long vehicleCount = vehicleService.findAllVehicleCount(deptIds, null);
		//上线车辆数
		long goOnlineCount = 0;
		if(statDate.length() == 10){
			//如果是日报
			goOnlineCount = vehicleService.findGoOnlineCountAllToday(deptIds, null);
		}else{
			//如果是月报
			goOnlineCount = vehicleService.findGoOnlineCountAllMonth(deptIds,null,statDate);
		}

		//离线车辆数
		long offlineCount = vehicleCount - goOnlineCount;
		//上线率
		double onlineRate = MathUtil.divideRoundDouble(goOnlineCount, vehicleCount,4);
		String onlineRateStr = MathUtil.formatToPercent(onlineRate,2);
		//长时间不在线（连续15天未上线）
		long longOfflineCount = vehicleService.findOfflineCountByOfflineDayCount(deptIds, null, CommonConstant.LONG_OFFLINE_DAYS_COUNT);
		rs.setVehicleTotalCount(vehicleCount);
		rs.setVehicleGoOnlineCount(goOnlineCount);
		rs.setVehicleOfflineCount(offlineCount);
		rs.setGoOnlineRateStr(onlineRateStr);
		rs.setVehicleLongOfflineCount(longOfflineCount);

		//2.报警相关信息
		//总报警次数
		long totalCount = alarmService.findAlarmCountByDeptIds(deptIds, startSecondTime, endSecondTime);
		//超速报警次数
		long overSpeedCount = getOverSpeedAlarmCount(deptIds, startSecondTime, endSecondTime);
		//疲劳驾驶报警次数
		long fatigueCount = getFatigueAlarmCount(deptIds, startSecondTime, endSecondTime);
		//夜间禁行报警次数
		long nightStopCount = getNightStopAlarmCount(deptIds, startSecondTime, endSecondTime);
		//主动安全报警次数
		long activeCount = getActiveAlarmCount(deptIds,startSecondTime, endSecondTime);
		//其他报警次数
		long otherCount = totalCount - overSpeedCount - fatigueCount - nightStopCount - activeCount;
		//平均单车报警次数
		double averageCount = MathUtil.divideRoundDouble(totalCount, goOnlineCount, 2);
		//平台下发消息数量（暂定为服务商处理数量）
		long sendMsgCount = alarmService.findAlarmHandleCountTotal(startSecondTime, endSecondTime,deptIds, null, CommonConstant.DICT_SERVICE_ROLE_SERVER);
		rs.setAlarmTotalCount(totalCount);
		rs.setOverSpeedAlarmCount(overSpeedCount);
		rs.setTiredAlarmCount(fatigueCount);
		rs.setNightAlarmCount(nightStopCount);
		rs.setActiveAlarmCount(activeCount);
		rs.setOtherAlarmCount(otherCount);
		rs.setAverageVehicleAlarmCount(averageCount);
		rs.setSendMessageCount(sendMsgCount);

		Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);

		//3.企业车辆排行
		//长期不在线排行
		List<VehicleAndCount> longOfflineList = vehicleService.findOfflineListByOfflineDayCount(deptIds, null, CommonConstant.LONG_OFFLINE_DAYS_COUNT,5);
		//设置车牌颜色
		for(VehicleAndCount vc : longOfflineList){
			vc.setLicenceColorDesc(licenceColorMap.get(vc.getLicenceColor()));
		}
		rs.setLongOfflineVehicleList(longOfflineList);

		//超速排行
		List<Integer> overSpeedAlarmList = getOverSpeedAlarmType();
		List<VehicleAndCount> overSpeedList = alarmService.findAlarmListByDeptIdsAndAlarmType(deptIds,overSpeedAlarmList,startSecondTime, endSecondTime,5);
		//设置车牌颜色
		for(VehicleAndCount vc : overSpeedList){
			vc.setLicenceColorDesc(licenceColorMap.get(vc.getLicenceColor()));
		}
		//超速车辆数
		long overSpeedVehicleCount = alarmService.findVehicleCountByDeptIdsAndAlarmType(deptIds,overSpeedAlarmList,startSecondTime, endSecondTime);
		rs.setOverSpeedVehicleCount(overSpeedVehicleCount);
		rs.setOverSpeedList(overSpeedList);


		//4.驾驶员排行
		//超速
		List<DriverNameAndCount> overSpeedDriverList = alarmService.findDriverAlarmListByDeptIdsAndAlarmType(deptIds,overSpeedAlarmList,startSecondTime, endSecondTime,5);
		rs.setOverSpeedDriverList(overSpeedDriverList);

		//疲劳
		List<Integer> fatigueAlarmType = getFatigueAlarmType();
		List<DriverNameAndCount> fatigueDriverList = alarmService.findDriverAlarmListByDeptIdsAndAlarmType(deptIds,fatigueAlarmType,startSecondTime, endSecondTime,5);
		rs.setFatigueDriverList(fatigueDriverList);

		//夜间禁行
		List<Integer> nightAlarmType = getNightStopAlarmType();
		List<DriverNameAndCount> nightList = alarmService.findDriverAlarmListByDeptIdsAndAlarmType(deptIds,nightAlarmType,startSecondTime, endSecondTime,5);
		rs.setNightDriverList(nightList);

		return rs;
	}


	@Override
	public VehicleBaseInfoWX vehicleBaseInfo(Long deptId, String statDate) throws Exception {
		VehicleBaseInfoWX rs = new VehicleBaseInfoWX();
		//1.企业基本情况
		BladeDept dept = deptService.getById(deptId);
		if(dept == null){
			log.error("该企业不存在");
			return null;
		}
		String deptName = dept.getDeptName();
		rs.setDeptName(deptName);
		rs.setDateStr(statDate);


		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
		//车辆总数
		long vehicleCount = vehicleService.findAllVehicleCount(deptIds, null);
		//营运车辆数
		long runningCount = 0;
		try{
			runningCount = vehicleService.findRunningCountByDeptId(deptIds);
		}catch (Exception e){
			log.error("查询营运车辆数出错",e);
		}
		//上线车辆数
		long goOnlineCount = 0;
		if(statDate.length() == 10){
			//如果是日报
			if(DateUtil.getDateString().equals(statDate)){
				//如果统计的是当日
				goOnlineCount = vehicleService.findGoOnlineCountAllToday(deptIds, null);
			}else{
				//如果统计的是之前的日期
				goOnlineCount = vehicleService.findGoOnlineCountByDate(deptIds, null,statDate);
			}
		}else{
			//如果是月报
			goOnlineCount = vehicleService.findGoOnlineCountAllMonth(deptIds,null,statDate);
		}

		//离线车辆数
		long offlineCount = vehicleCount - goOnlineCount;
		//上线率
		double onlineRate = 0;
		if(runningCount > 0){
			onlineRate = MathUtil.divideRoundDouble(goOnlineCount, runningCount,4);
		}
		String onlineRateStr = MathUtil.formatToPercent(onlineRate,2);
		//长时间不在线（连续15天未上线）
		long longOfflineCount = vehicleService.findOfflineCountByOfflineDayCount(deptIds, null, CommonConstant.LONG_OFFLINE_DAYS_COUNT);
		rs.setVehicleTotalCount(vehicleCount);
		rs.setVehicleGoOnlineCount(goOnlineCount);
		rs.setGoOnlineRateStr(onlineRateStr);
		rs.setVehicleLongOfflineCount(longOfflineCount);
		rs.setVehicleRunningCount(runningCount);
		return rs;
	}


	@Override
	public List<OnlineHourCountResponse> onlineHourCount(Long deptId, String date) throws Exception{

		//1.获取部门id、子部门id
		List<Long> deptIds = null;
		try {
			deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
		}catch (Exception e){
			log.error("查询部门信息失败",e);
		}

		//获取查询时段
		long startTime = DateUtil.getDayFirstSecondTimestamp(date);
		long endTime = DateUtil.getDayLastSecondTimestamp(startTime);
		if(DateUtil.getDateString().equals(date)){
			//如果是当前天，则查询到上个小时
			endTime = DateUtil.getSecondTimestampAtHour(DateUtil.getSecondTimestamp()-3600);
		}
		List<String> hourList = DateUtil.getHourList(startTime, endTime);
		if(hourList == null || hourList.size() < 1){
			return null;
		}
		hourList = hourList.subList(0, hourList.size() - 1);
		List<OnlineHourCountResponse> list = onlineDayService.findOnlineHourCount(hourList, deptIds, null);
		return list;
	}


	@Override
	public VehicleTravelMessage vehicleTravel(Long deptId, String statDate, Query query) throws Exception {

		VehicleTravelMessage vt = new VehicleTravelMessage();

		long startTime = 0;
		long endTime = 0;
		String statType = "";
		if(statDate.length() == 10){
			statType = "day";
			//如果是日报
			//判断是否是当日
			if(DateUtil.getDateString().equals(statDate)){
				//如果是当日
				startTime = DateUtil.getDayFirstSecondTimestamp();
				endTime = System.currentTimeMillis()/1000;
			}else{
				//如果不是当日
				startTime = DateUtil.getDayFirstSecondTimestamp(statDate);
				endTime = DateUtil.getDayLastSecondTimestamp(startTime);
			}
		}else{
			statType = "month";
			//如果是月报
			//判断是否是当月
			if(DateUtil.getDateString().substring(0,7).equals(statDate)){
				//如果是当月
				startTime = DateUtil.getMonthFirstSecondTimestamp(statDate);
				endTime = System.currentTimeMillis()/1000;
			}else{
				//如果不是当月
				startTime = DateUtil.getMonthFirstSecondTimestamp(statDate);
				endTime = DateUtil.getMonthLastSecondTimestamp(statDate);
			}
		}
		BladeUser user = AuthUtil.getUser();
		List<Long> deptIds = SysCache.getDeptChildIds(deptId);
//		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
		IPage<com.xh.vdm.statistic.entity.BdmVehicle> vList = vehicleService.getVehicleListAllByPage(user, deptId, query);
		vt.setTotalCount((int)vList.getTotal());
		List<Integer> vehicleIds = new ArrayList<>();
		vList.getRecords().forEach(item -> {
			vehicleIds.add(item.getId());
		});
		//1.查询总里程
		double totalMileage = 0;
		if(statType.equals("day")){
			//如果是日报
			totalMileage = vehicleStatService.statDeptMileage(statDate, deptIds, null);
		}else{
			//如果是月份
			totalMileage = vehicleStatService.statDeptMileageMonth(statDate,deptIds,null);
		}
		vt.setTotalMileage(MathUtil.roundDouble(totalMileage / 1000,2));

		//2.查询总时长
		long totalDuration = 0;
		if(statType.equals("day")){
			//如果是日报
			totalDuration = vehicleStatService.statDeptDuration(statDate.substring(0,7),statDate, deptIds, null);
		}else{
			//如果是月份
			totalDuration = vehicleStatService.statDeptDurationMonth(statDate, DateUtil.getMonthLastDateStr(statDate),deptIds,null);
		}
		vt.setTotalDurationInHour(DateUtil.getFormatDateString(totalDuration));


		//3.计算平均里程、平均时长
		//查询上线车辆数

		//上线车辆数
		long goOnlineCount = 0;
		if(statDate.length() == 10){
			//如果是日报
			if(DateUtil.getDateString().equals(statDate)){
				//如果统计的是当日
				goOnlineCount = vehicleService.findGoOnlineCountAllToday(deptIds, null);
			}else{
				//如果统计的是之前的日期
				goOnlineCount = vehicleService.findGoOnlineCountByDate(deptIds, null,statDate);
			}
		}else{
			//如果是月报
			goOnlineCount = vehicleService.findGoOnlineCountAllMonth(deptIds,null,statDate);
		}


		long goVehicleCount = goOnlineCount;
		if(goVehicleCount > 0){
			vt.setAverageMileage(MathUtil.divideRoundDouble(totalMileage / 1000, goVehicleCount, 2));
			//平均时长（小时）
			vt.setAverageDurationInHour((MathUtil.divideRoundDouble(totalDuration/3600, goVehicleCount, 2))+"");
		}

		//4.计算每辆车的里程、时长信息、最大速度信息、平均速度信息
		IPage<VehicleAndMileage> mileagePage = null;
		if(statType.equals("day")){
			//如果是日报
			mileagePage = vehicleService.findTotalMileagePage(deptIds, null, statDate.substring(0,7), statDate,query);
		}else{
			//如果是月报
			mileagePage = vehicleService.findTotalMileageMonthPage(deptIds, null,statDate,query);
		}

		//根据车辆里程，查询在线时长、最大速度、上线天数
		List<VehicleAndCount> durationList = null;
		List<VehicleAndData> maxSpeedList = null;
		List<VehicleAndCount> onlineDaysList = null;
		if(mileagePage != null && mileagePage.getRecords() != null){
			//List<Integer> vehicleIds = new ArrayList<>();
			/*mileagePage.getRecords().forEach(item -> {
				vehicleIds.add(item.getVehicleId().intValue());
			});*/
			if(statType.equals("day")){
				//如果是日报
				durationList = vehicleService.findGoOnlineDurationListByDateAndVehicleId(vehicleIds, statDate);
				maxSpeedList = vehicleStatService.findMaxSpeedListByVehicleId(statDate, vehicleIds);
				//查询上线天数
				onlineDaysList = vehicleService.findOnlineDaysCount(statDate, vehicleIds);
			}else{
				//如果是月报
				String lastDateInMonth = "";
				if(DateUtil.getDateString().substring(0,7).equals(statDate)){
					//如果是当月
					lastDateInMonth = DateUtil.getDateString();
				}else{
					//如果不是当月
					lastDateInMonth = DateUtil.getMonthLastDateStr(statDate);
				}
				durationList = vehicleService.findGoOnlineDurationListAllMonthByVehicleId(vehicleIds, statDate,lastDateInMonth);
				maxSpeedList = vehicleStatService.findMaxSpeedListMonthByVehicleId(statDate, vehicleIds);
				//查询上线天数
				//查询上线天数
				onlineDaysList = vehicleService.findOnlineDaysCountMonth(statDate, vehicleIds);
			}
		}
		//整理数据
		//数据转为map
		Map<String,Long> durationMap = new HashMap<>();
		Map<String,Double> maxSpeedMap = new HashMap<>();
		Map<String,Long> onlineDaysMap = new HashMap<>();
		durationList.forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			durationMap.put(key, item.getCount());
		});
		maxSpeedList.forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			maxSpeedMap.put(key, item.getData());
		});
		onlineDaysList.forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			onlineDaysMap.put(key, item.getCount());
		});

		//整合数据
		List<VehicleTravelNode> nodeList = new ArrayList<>();
		Map<String,Double> mileageMap = new HashMap<>();
		mileagePage.getRecords().forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			mileageMap.put(key, item.getMileage());
		});
		vList.getRecords().forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			VehicleTravelNode node = new VehicleTravelNode();
			node.setLicencePlate(item.getLicencePlate());
			node.setLicenceColor(item.getLicenceColor());
			node.setMaxSpeed(maxSpeedMap.get(key)==null?0:maxSpeedMap.get(key));
			node.setGoOnlineDaysCount(onlineDaysMap.get(key)==null?0:onlineDaysMap.get(key));
			//总里程（km）
			node.setTotalMileage(MathUtil.roundDouble((mileageMap.get(key)==null?0:mileageMap.get(key))/1000,2));
			node.setTotalDuration((double)(durationMap.get(key)==null?0:durationMap.get(key)));
			node.setTotalDurationDesc(DateUtil.getFormatDateString(durationMap.get(key)==null?0:durationMap.get(key)));
			//平均时长（总时长/总上线天数，小时）
			if(onlineDaysMap.get(key) != null && onlineDaysMap.get(key) > 0 ){
				node.setAverageDuration(MathUtil.roundDouble(MathUtil.divideRoundDouble((durationMap.get(key)==null?0:durationMap.get(key))/3600, onlineDaysMap.get(key),2),2));
				//平均里程（km）
				if(durationMap != null && durationMap.get(key) != null){
					Double averageMileage = MathUtil.divideRoundDouble(mileageMap.get(key)==null?0:mileageMap.get(key), onlineDaysMap.get(key), 2);
					averageMileage = averageMileage / 1000;
					node.setAverageMileage(MathUtil.roundDouble(averageMileage==null?0:averageMileage,2));
				}
			}
			//平均速度(km/h)
			if(durationMap.get(key) != null && durationMap.get(key) > 0){
				Double averageSpeed = MathUtil.roundDouble(mileageMap.get(key)==null?0:mileageMap.get(key)/durationMap.get(key)*3.6,2);
				node.setAverageSpeed(averageSpeed==null?0:averageSpeed);
			}
			nodeList.add(node);
		});
		vt.setTravelList(nodeList);
		return vt;
	}

	@Override
	public AlarmInfoWX findAlarmInfo(Long deptId, String statDate) throws Exception {

		if(StringUtils.isEmpty(statDate)){
			log.error("统计报警信息，未选择日期");
			throw new Exception("未选择日期");
		}

		long startTime = 0;
		long endTime = 0;
		if(statDate.length() == 10){
			//如果是日报
			startTime = DateUtil.getDayFirstSecondTimestamp(statDate);
			endTime = DateUtil.getDayLastSecondTimestamp(startTime);
		}else {
			//如果是月报
			//如果是当前月份，则执行到前1天
			startTime = DateUtil.getDayFirstSecondTimestamp(statDate+"-"+"01");
			endTime = DateUtil.getSecondTimeStampBeforeDay(new Date(), 1);
		}
		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
		CountDownLatch countDownLatch = new CountDownLatch(5);

		AlarmInfoWX info = new AlarmInfoWX();

		//1.报警总数
		AtomicLong totalCount = new AtomicLong();
		long finalStartTime = startTime;
		long finalEndTime = endTime;
		threadPool.submit(() -> {
			try{
				totalCount.set(impalaAlarmService.findAlarmCountByCondition(deptIds, null, finalStartTime, finalEndTime));
			}catch (Exception e){
				log.error("查询报警总数失败",e);
			}finally {
				countDownLatch.countDown();
			}
		});


		//2.处理总数
		AtomicLong handleCount = new AtomicLong();
		long finalStartTime10 = startTime;
		long finalEndTime10 = endTime;
		threadPool.submit(() -> {
			try{
				handleCount.set(impalaAlarmService.findAlarmHandleCountByAlarmTypes(deptIds, null, null, finalStartTime10, finalEndTime10, CommonConstant.DICT_SERVICE_ROLE_SERVER));
			}catch(Exception e){
				log.error("查询报警处理总数失败",e);
			}finally {
				countDownLatch.countDown();
			}
		});

		//3.查询报警车辆数
		AtomicLong vehicleCountWithAlarm = new AtomicLong();
		long finalStartTime9 = startTime;
		long finalEndTime9 = endTime;
		threadPool.submit(() -> {
			try{
				vehicleCountWithAlarm.set(impalaAlarmService.findVehicleCountWithAlarm(deptIds, null, null, finalStartTime9, finalEndTime9));
			}catch (Exception e){
				log.error("查询报警车辆数失败",e);
			}finally {
				countDownLatch.countDown();
			}
		});

		//4.根据报警类型统计报警数量和处理数量
		List<Integer> alarmTypes = new ArrayList<>();
		alarmTypes.addAll(VdmUserInfoUtil.getOverSpeedAlarmType());
		alarmTypes.addAll(VdmUserInfoUtil.getFatigueAlarmType());
		alarmTypes.addAll(VdmUserInfoUtil.getNightLimitAlarmType());
		alarmTypes.addAll(VdmUserInfoUtil.getAdasAlarmType());
		alarmTypes.addAll(VdmUserInfoUtil.getDsmAlarmType());
		alarmTypes.addAll(VdmUserInfoUtil.getEmergencyAlarmType());
		AtomicReference<List<AlarmBaseTypeAndCount>> alarmTypeAndCount = new AtomicReference<>();
		long finalStartTime8 = startTime;
		long finalEndTime8 = endTime;
		threadPool.submit(() -> {
			try{
				alarmTypeAndCount.set(impalaAlarmService.findAlarmTypeAndCountOrHandle(alarmTypes, deptIds, null, finalStartTime8, finalEndTime8, null));
			}catch (Exception e){
				log.error("按照报警类型分组查询报错数量报错",e);
			}finally{
				countDownLatch.countDown();
			}
		});

		AtomicReference<List<AlarmBaseTypeAndCount>> alarmTypeAndHandleCount = new AtomicReference<>();
		long finalStartTime7 = startTime;
		long finalEndTime7 = endTime;
		threadPool.submit(() -> {
			try{
				alarmTypeAndHandleCount.set(impalaAlarmService.findAlarmTypeAndCountOrHandle(alarmTypes, deptIds, null, finalStartTime7, finalEndTime7, CommonConstant.DICT_SERVICE_ROLE_SERVER));
			}catch (Exception e){
				log.error("按照报警类型分组查询报警处理数量报错",e);
			}finally {
				countDownLatch.countDown();
			}
		});

		countDownLatch.await();

		//处理率
		double handleRate = (totalCount.get() <= 0) ? 0 : MathUtil.divideRoundDouble(handleCount.get(), totalCount.get(), 4);
		String handleRateStr = MathUtil.formatToPercent(handleRate, 2);

		//按照报警类型统计报警量和报警处理量
		List<AlarmTypeNode> nodeList = new ArrayList<>();
		if(alarmTypeAndCount != null && alarmTypeAndCount.get() != null){
			Map<String,Long> alarmCountMap = new HashMap<>();
			alarmTypeAndCount.get().forEach(item -> {
				alarmCountMap.put(item.getAlarmType(),item.getCount());
			});
			Map<String,Long> alarmHandleCountMap = new HashMap<>();
			alarmTypeAndHandleCount.get().forEach(item -> {
				alarmHandleCountMap.put(item.getAlarmType(),item.getCount());
			});
			alarmTypeAndCount.get().forEach(item -> {
				AlarmTypeNode node = new AlarmTypeNode();
				long alarmCount = alarmCountMap.get(item.getAlarmType());
				long alarmHandleCount = alarmHandleCountMap.get(item.getAlarmType())==null?0:alarmHandleCountMap.get(item.getAlarmType());
				double rate = alarmCount == 0?0:MathUtil.divideRoundDouble(alarmHandleCount, alarmCount,4);
				String rateStr = MathUtil.formatToPercent(rate, 2);
				node.setAlarmType(item.getAlarmType());
				node.setAlarmCount(alarmCount);
				node.setHandleCount(alarmHandleCount);
				node.setHandleRate(rateStr);
				nodeList.add(node);
			});

		}

		//5.设置数据
		info.setAlarmTotalCount(totalCount.get());
		info.setHandleCount(handleCount.get());
		info.setAlarmVehicleCount(vehicleCountWithAlarm.get());
		info.setHandleRateStr(handleRateStr);
		info.setAlarmList(nodeList);

		return info;
	}

	/**
	 * 统计企业概括情况--微信小程序
	 * @param deptId 部门id
	 * @param statDate 统计日期 yyyy-MM-dd / yyyy-MM
	 * @param startSecondTime 开始时间戳（精确到秒）
	 * @param endSecondTime 结束时间戳（精确到秒）
	 * 注意：在日报和月报的统计中，不包含账号绑定的车辆
	 * @return
	 * @throws Exception
	 */
	@Override
	public ReportSummary reportSummaryWX(Long deptId, String statDate, Long startSecondTime, Long endSecondTime) throws Exception {

		ReportSummary rs = new ReportSummary();

		//1.企业基本情况
		BladeDept dept = deptService.getById(deptId);
		if(dept == null){
			log.error("该企业不存在");
			return null;
		}
		String deptName = dept.getDeptName();
		rs.setDeptName(deptName);
		rs.setDateStr(statDate);


		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
		//车辆总数
		long vehicleCount = vehicleService.findAllVehicleCount(deptIds, null);
		//上线车辆数
		long goOnlineCount = 0;
		if(statDate.length() == 10){
			//如果是日报
			goOnlineCount = vehicleService.findGoOnlineCountAllToday(deptIds, null);
		}else{
			//如果是月报
			goOnlineCount = vehicleService.findGoOnlineCountAllMonth(deptIds,null,statDate);
		}

		//离线车辆数
		long offlineCount = vehicleCount - goOnlineCount;
		//上线率
		double onlineRate = MathUtil.divideRoundDouble(goOnlineCount, vehicleCount,4);
		String onlineRateStr = MathUtil.formatToPercent(onlineRate,2);
		//长时间不在线（连续15天未上线）
		long longOfflineCount = vehicleService.findOfflineCountByOfflineDayCount(deptIds, null, CommonConstant.LONG_OFFLINE_DAYS_COUNT);
		rs.setVehicleTotalCount(vehicleCount);
		rs.setVehicleGoOnlineCount(goOnlineCount);
		rs.setVehicleOfflineCount(offlineCount);
		rs.setGoOnlineRateStr(onlineRateStr);
		rs.setVehicleLongOfflineCount(longOfflineCount);

		//2.报警相关信息
		//总报警次数
		long totalCount = alarmService.findAlarmCountByDeptIds(deptIds, startSecondTime, endSecondTime);
		//超速报警次数
		long overSpeedCount = getOverSpeedAlarmCount(deptIds, startSecondTime, endSecondTime);
		//疲劳驾驶报警次数
		long fatigueCount = getFatigueAlarmCount(deptIds, startSecondTime, endSecondTime);
		//夜间禁行报警次数
		long nightStopCount = getNightStopAlarmCount(deptIds, startSecondTime, endSecondTime);
		//主动安全报警次数
		long activeCount = getActiveAlarmCount(deptIds,startSecondTime, endSecondTime);
		//其他报警次数
		long otherCount = totalCount - overSpeedCount - fatigueCount - nightStopCount - activeCount;
		//平均单车报警次数
		double averageCount = MathUtil.divideRoundDouble(totalCount, goOnlineCount, 2);
		//平台下发消息数量（暂定为服务商处理数量）
		long sendMsgCount = alarmService.findAlarmHandleCountTotal(startSecondTime, endSecondTime,deptIds, null, CommonConstant.DICT_SERVICE_ROLE_SERVER);
		rs.setAlarmTotalCount(totalCount);
		rs.setOverSpeedAlarmCount(overSpeedCount);
		rs.setTiredAlarmCount(fatigueCount);
		rs.setNightAlarmCount(nightStopCount);
		rs.setActiveAlarmCount(activeCount);
		rs.setOtherAlarmCount(otherCount);
		rs.setAverageVehicleAlarmCount(averageCount);
		rs.setSendMessageCount(sendMsgCount);

		//3.企业车辆排行
		//长期不在线排行
		List<VehicleAndCount> longOfflineList = vehicleService.findOfflineListByOfflineDayCount(deptIds, null, CommonConstant.LONG_OFFLINE_DAYS_COUNT,5);
		rs.setLongOfflineVehicleList(longOfflineList);

		//超速排行
		List<Integer> overSpeedAlarmList = getOverSpeedAlarmType();
		List<VehicleAndCount> overSpeedList = alarmService.findAlarmListByDeptIdsAndAlarmType(deptIds,overSpeedAlarmList,startSecondTime, endSecondTime,5);
		//超速车辆数
		long overSpeedVehicleCount = alarmService.findVehicleCountByDeptIdsAndAlarmType(deptIds,overSpeedAlarmList,startSecondTime, endSecondTime);
		rs.setOverSpeedVehicleCount(overSpeedVehicleCount);
		rs.setOverSpeedList(overSpeedList);

		return rs;
	}

	/**
	 * 长期不在线排行，分页
	 * @param query
	 * @param user
	 * @return
	 * @throws Exception
	 */
	@Override
	public IPage<VehicleAndCount> longOfflineVehiclePage(Query query, long deptId, String licencePlate, Long licenceColor, BladeUser user) throws Exception {
		List<Long> deptIds = SysCache.getDeptChildIds(deptId);
		return vehicleService.findOfflineListByOfflineDayCountPage(deptIds, user.getUserId(),CommonConstant.LONG_OFFLINE_DAYS_COUNT, licencePlate, licenceColor,query);
	}


	/**
	 * 超速排行，分页
	 * @param query
	 * @param startTime
	 * @param endTime
	 * @param user
	 * @return
	 * @throws Exception
	 */
	@Override
	public IPage<VehicleAndCount> overSpeedPage(Query query, Long startTime, Long endTime, String licencePlate, Long licenceColor, BladeUser user) throws Exception {
		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
		List<Integer> overSpeedAlarmList = VdmUserInfoUtil.getOverSpeedAlarmType();
		IPage<VehicleAndCount> overSpeedList = alarmService.findAlarmListByDeptIdsAndAlarmTypePage(deptIds,overSpeedAlarmList,startTime, endTime, licencePlate, licenceColor, query);
		return overSpeedList;
	}

	/**
	 * 报警情况统计
	 * @param deptId
	 * @param statDate 统计日期，可以是天，也可以是月 yyyy-MM-dd / yyyy-MM
	 * @param startSecondTime
	 * @param endSecondTime
	 * 注意：在日报和月报的统计中，不包含账号绑定的车辆
	 * @return
	 * @throws Exception
	 */
	@Override
	public AlarmSummary alarmSummary(Long deptId, String statDate, Long startSecondTime, Long endSecondTime) throws Exception {

		AlarmSummary as = new AlarmSummary();

		//1.报警统计信息
		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
		//车辆总数
		long vehicleCount = vehicleService.findAllVehicleCount(deptIds, null);
		//上线车辆数
		long goOnlineCount = vehicleService.findGoOnlineCountAllToday(deptIds, null);
		//离线车辆数
		long offlineCount = vehicleCount - goOnlineCount;
		//上线率
		double onlineRate = MathUtil.divideRoundDouble(goOnlineCount, vehicleCount,4);
		String onlineRateStr = MathUtil.formatToPercent(onlineRate,2);
		//长时间不在线（连续15天未上线）
		long longOfflineCount = vehicleService.findOfflineCountByOfflineDayCount(deptIds, null, CommonConstant.LONG_OFFLINE_DAYS_COUNT);
		//报警总数
		long totalAlarmCount = alarmService.findAlarmCountByDeptIds(deptIds, startSecondTime, endSecondTime);
		//车均报警数
		double averageAlarmCount = MathUtil.divideRoundDouble(totalAlarmCount, goOnlineCount, 2);

		as.setVehicleTotalCount(vehicleCount);
		as.setVehicleGoOnlineCount(goOnlineCount);
		as.setGoOnlineRateStr(onlineRateStr);
		as.setVehicleOfflineCount(offlineCount);
		as.setAlarmPerVehicleCount(averageAlarmCount);
		as.setVehicleLongOfflineCount(longOfflineCount);

		//2.车辆报警概况列表
		CountDownLatch countDownLatch = new CountDownLatch(1);

		//查询部门信息、上级平台信息，用于后续手动增加字段
		Map<String,BladeDept> deptMap = new HashMap<>();
		Map<String,BamThirdPartyPlatform> platMap = new HashMap<>();
		Map<String,String> alarmTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ALARM_TYPE, AlarmConstant.DICT_ALARM_TYPE_ROOT_VALUE+"");
		threadPool.submit(() -> {
			try{
				List<BladeDept> depts = null;
				List<BamThirdPartyPlatform> platList = null;
				platList = thirdPartyPlatformService.list(Wrappers.lambdaQuery(BamThirdPartyPlatform.class).eq(BamThirdPartyPlatform::getEnabled, 0));
				depts = deptService.list(Wrappers.lambdaQuery(BladeDept.class).eq(BladeDept::getIsDeleted, 0));
				platList.forEach(item -> {
					platMap.put(item.getId()+"", item);
				});
				depts.forEach(item -> {
					deptMap.put(item.getId()+"", item);
				});
			}catch (Exception e){
				log.error("异步处理失败",e);
			}finally {
				countDownLatch.countDown();
			}
		});
		//查询报警情况
		List<StatVehRunningStateDay> stateList = null;
		if(statDate.length() == 7){
			//如果查询月报
			String month = statDate.substring(0,7).replace("-","");
			stateList = vehRunningStateService.findStateInfoByDeptWithAlarmMonth(month, deptIds, null);
		}else{
			//如果查询日报
			String month = statDate.substring(0,7).replace("-","");
			String date = statDate.replace("-","");
			stateList = vehRunningStateService.findStateInfoByDeptWithAlarm(month, date, deptIds, null);
		}

		List<AlarmNode> alarmNodes = new ArrayList<>();
		countDownLatch.await();
		as.setStatDate(statDate);
		as.setDeptName(deptMap.get(deptId+"").getDeptName());
		Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
		stateList.forEach(item -> {
			AlarmNode node = new AlarmNode();
			node.setLicencePlate(item.getLicencePlate());
			node.setLicenceColor(item.getLicenceColor()+"");
			node.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()+""));
			BladeDept dept = deptMap.get(item.getDeptId()+"");
			if(dept != null){
				node.setDeptName(dept.getDeptName());
			}
			node.setFatigueAlarmCount(item.getTiredCount());
			node.setNightAlarmCount(item.getNightDrivingCount());
			node.setOverSpeedAlarmCount(item.getOverSpeedCount());
			node.setTotalAlarmCount(item.getTotalAlarmCount());
			node.setOtherAlarmCount(item.getTotalAlarmCount() - (item.getTiredCount()==null?0:item.getTiredCount()) - (item.getNightDrivingCount()==null?0:item.getNightDrivingCount()) - (item.getOverSpeedCount()==null?0:item.getOverSpeedCount()));
			alarmNodes.add(node);
		});
		as.setAlarmList(alarmNodes);

		//3.报警详情
		//获取配置的报警类型
		List<BdmDealAlarmOption> list = alarmOptionService.list(Wrappers.lambdaQuery(BdmDealAlarmOption.class).eq(BdmDealAlarmOption::getDeleteStatus,0).eq(BdmDealAlarmOption::getDeptId,deptId));
		List<Integer> alarmTypes = new ArrayList<>();
		//超速、疲劳、夜间禁行报警详情，三种必填报警类型
		String tenantId = AuthUtil.getTenantId();
		List<Integer> overSpeedAlarmTypes = getOverSpeedAlarmType();
		List<Integer> fatigueAlarmTypes = getFatigueAlarmType();
		List<Integer> nightAlarmTypes = getNightStopAlarmType();
		alarmTypes.addAll(overSpeedAlarmTypes);
		alarmTypes.addAll(fatigueAlarmTypes);
		alarmTypes.addAll(nightAlarmTypes);

		//其他报警类型详情（根据配置查询）
		List<Integer> otherAlarmTypes = new ArrayList<>();
		list.forEach(item -> {
			otherAlarmTypes.add(Integer.parseInt(item.getAlarmType()));
		});
		alarmTypes.addAll(otherAlarmTypes);
		List<AlarmBase> alarmList = impalaAlarmService.findAlarmBaseByDate(alarmTypes,startSecondTime, endSecondTime,deptIds,null);
		as.setAlarmTotalCount(alarmList==null?0:(long)alarmList.size());

		//补充字段
		Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
		alarmList.forEach(item -> {
			item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
			item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
			item.setDeptName(deptMap.get(item.getDeptId()+"")==null?"":deptMap.get(item.getDeptId()+"").getDeptName());
			item.setDuration(DateUtil.getFormatDateString(item.getAlarmEndTime().getTime()/1000-item.getAlarmTime().getTime()/1000));
		});

		//按照报警类型拆分
		Map<String,List<AlarmBase>> map = new HashMap<>();
		for(AlarmBase a : alarmList){
			String alarmTypeName = alarmTypeMap.get(a.getAlarmType()+"");
			a.setAlarmTypeName(alarmTypeName);
			List<AlarmBase> arr = map.get(alarmTypeName);
			if(arr == null){
				arr = new ArrayList<>();
			}
			arr.add(a);
			map.put(alarmTypeName,arr);
		}
		//创建类型及详情列表对象
		List<AlarmTypeAndList> aList = new ArrayList<>();
		for(String alarmTypeName : map.keySet()){
			AlarmTypeAndList al = new AlarmTypeAndList();
			al.setAlarmTypeName(alarmTypeName);
			al.setList(map.get(alarmTypeName));
			al.setDeptId(deptId);
			aList.add(al);
		}
		as.setAlarmDetailList(aList);

		return as;
	}

	/**
	 * 查询超速报警数量
	 * @param deptIds
	 * @param startSecondTimestamp
	 * @param endSecondTimestamp
	 * @return
	 */
	private long getOverSpeedAlarmCount(List<Long> deptIds, Long startSecondTimestamp, Long endSecondTimestamp) throws Exception{
		//获取超速报警类型
		List<Integer> list = getOverSpeedAlarmType();
		long alarmCount = alarmService.findAlarmCountByDeptIdsAndAlarmType(deptIds, list, startSecondTimestamp, endSecondTimestamp);
		return alarmCount;
	}

	/**
	 * 获取超速报警类型
	 * @return
	 */
	private List<Integer> getOverSpeedAlarmType(){
		return VdmUserInfoUtil.getOverSpeedAlarmType();
	}

	/**
	 * 查询疲劳报警数量
	 * @param deptIds
	 * @param startSecondTimestamp
	 * @param endSecondTimestamp
	 * @return
	 */
	private long getFatigueAlarmCount(List<Long> deptIds, Long startSecondTimestamp, Long endSecondTimestamp) throws Exception{
		List<Integer> list = getFatigueAlarmType();
		long alarmCount = alarmService.findAlarmCountByDeptIdsAndAlarmType(deptIds, list, startSecondTimestamp, endSecondTimestamp);
		return alarmCount;
	}

	/**
	 * 获取疲劳驾驶报警类型
	 * @return
	 */
	private List<Integer> getFatigueAlarmType(){
		return VdmUserInfoUtil.getFatigueAlarmType();
	}

	/**
	 * 查询夜间禁行报警数量
	 * @param deptIds
	 * @param startSecondTimestamp
	 * @param endSecondTimestamp
	 * @return
	 */
	private long getNightStopAlarmCount(List<Long> deptIds, Long startSecondTimestamp, Long endSecondTimestamp) throws Exception{
		List<Integer> list = getNightStopAlarmType();
		long alarmCount = alarmService.findAlarmCountByDeptIdsAndAlarmType(deptIds, list, startSecondTimestamp, endSecondTimestamp);
		return alarmCount;
	}


	/**
	 * 查询主动安全报警数
	 * @param deptIds
	 * @param startSecondTimestamp
	 * @param endSecondTimestamp
	 * @return
	 */
	private long getActiveAlarmCount(List<Long> deptIds, Long startSecondTimestamp, Long endSecondTimestamp) throws Exception{
		List<Integer> list = VdmUserInfoUtil.getActiveAlarmType();
		long alarmCount = alarmService.findAlarmCountByDeptIdsAndAlarmType(deptIds, list, startSecondTimestamp, endSecondTimestamp);
		return alarmCount;
	}

	/**
	 * 查询夜间限速报警类型
	 * @return
	 */
	private List<Integer> getNightStopAlarmType(){
		return VdmUserInfoUtil.getNightLimitAlarmType();
	}
}
