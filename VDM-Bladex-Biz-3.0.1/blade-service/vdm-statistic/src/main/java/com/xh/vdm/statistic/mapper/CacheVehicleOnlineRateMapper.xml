<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.CacheVehicleOnlineRateMapper">

    <select id="getVehicleList" resultType="com.xh.vdm.statistic.entity.VehicleBase">
        select distinct cvor.licence_plate , cvor.licence_color_code licenceColor, cvor.vehicle_id
        from cache_vehicle_online_rate cvor, bdm_vehicle bv
        where bv.id = cvor.vehicle_id and bv.licence_plate = cvor.licence_plate and bv.licence_color = cvor.licence_color_code
        and bv.is_del = 0
        <if test="param.vehicleIdList == null or param.vehicleIdList.size() lte 0">
            and ( bv.dept_id in (
            <foreach collection="param.deptList" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
            <if test="param.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{param.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="param.vehicleIdList != null and param.vehicleIdList.size() gt 0">
            and cvor.vehicle_id in
            <foreach collection="param.vehicleIdList" separator="," item="vehicleId" open="(" close=")">
                #{vehicleId}
            </foreach>
        </if>

        <if test="param.accessMode != null and param.accessMode != ''">
            and access_mode_code = #{param.accessMode,jdbcType=INTEGER}
        </if>
        <if test="param.vehicleUseType != null and param.vehicleUseType != ''">
            and vehicle_model_code = #{param.vehicleUseType,jdbcType=BIGINT}
        </if>
        <if test="param.vehicleOwnerId != null and param.vehicleOwnerId != ''">
            and cvor.vehicle_owner_id = #{param.vehicleOwnerId,jdbcType=BIGINT}
        </if>
        <if test="monthList != null and monthList != ''">
            and month in
            <foreach collection="monthList" item="month" separator="," open="(" close=")">
                #{month}
            </foreach>
        </if>
        order by licence_plate
    </select>


    <select id="getVehicleListCount" resultType="long">
        select count(*) from (
            select distinct cvor.licence_plate , cvor.licence_color_code licenceColor
            from cache_vehicle_online_rate cvor, bdm_vehicle bv
            where 1 = 1 and cvor.licence_plate = bv.licence_plate and cvor.licence_color_code = bv.licence_color

            <if test="param.vehicleIdList == null">
                and ( bv.dept_id in (
                <foreach collection="param.deptList" item="deptId" separator=",">
                    #{deptId,jdbcType=BIGINT}
                </foreach>
                )
                <if test="param.userId != null">
                    or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{param.userId,jdbcType=BIGINT})
                </if>
                )
            </if>

            <if test="param.vehicleIdList != null">
                and bv.id in
                <foreach collection="param.vehicleIdList" separator="," item="vehicleId" open="(" close=")">
                    #{vehicleId}
                </foreach>
            </if>

            <if test="param.accessMode != null and param.accessMode != ''">
                and access_mode_code = #{param.accessMode,jdbcType=INTEGER}
            </if>
            <if test="param.vehicleUseType != null and param.vehicleUseType != ''">
                and vehicle_model_code = #{param.vehicleUseType,jdbcType=BIGINT}
            </if>
            <if test="param.vehicleOwnerId != null and param.vehicleOwnerId != ''">
                and cvor.vehicle_owner_id = #{param.vehicleOwnerId,jdbcType=BIGINT}
            </if>
            <if test="monthList != null and monthList != ''">
                and month in
                <foreach collection="monthList" item="month" separator="," open="(" close=")">
                    #{month}
                </foreach>
            </if>
        ) a;
    </select>

    <select id="getVehicles" resultType="com.xh.vdm.statistic.entity.VehicleBase">
        select distinct cvor.licence_plate , cvor.licence_color_code licenceColor
        from cache_vehicle_online_rate cvor, bdm_vehicle bv
        where 1 = 1 and cvor.licence_plate = bv.licence_plate and cvor.licence_color_code = bv.licence_color

        <if test="param.vehicleIdList == null">
            and ( bv.dept_id in (
            <foreach collection="param.deptList" item="deptId" separator=",">
                #{deptId,jdbcType=BIGINT}
            </foreach>
            )
            <if test="param.userId != null">
                or bv.id = any (select vehicle_id from blade_users_vehicles where user_id = #{param.userId,jdbcType=BIGINT})
            </if>
            )
        </if>

        <if test="param.vehicleIdList != null">
            and bv.id in
            <foreach collection="param.vehicleIdList" separator="," item="vehicleId" open="(" close=")">
                #{vehicleId}
            </foreach>
        </if>

        <if test="param.accessMode != null and param.accessMode != ''">
            and access_mode_code = #{param.accessMode,jdbcType=INTEGER}
        </if>
        <if test="param.vehicleUseType != null and param.vehicleUseType != ''">
            and vehicle_model_code = #{param.vehicleUseType,jdbcType=BIGINT}
        </if>
        <if test="param.vehicleOwnerId != null and param.vehicleOwnerId != ''">
            and cvor.vehicle_owner_id = #{param.vehicleOwnerId,jdbcType=BIGINT}
        </if>
        <if test="monthList != null and monthList != ''">
            and month in
            <foreach collection="monthList" item="month" separator="," open="(" close=")">
                #{month}
            </foreach>
        </if>
        order by licence_plate
    </select>

    <select id="getCacheVehicleOnlineRate" resultType="com.xh.vdm.statistic.entity.CacheVehicleOnlineRate">
        select * from cache_vehicle_online_rate
        where licence_plate = #{licencePlate,jdbcType=VARCHAR} and licence_color_code = #{licenceColor,jdbcType=VARCHAR}
        and month in (#{monthList,jdbcType=VARCHAR})
    </select>

    <select id="getCacheVehicleOnlineRateWithCondition" resultType="com.xh.vdm.statistic.entity.CacheVehicleOnlineRate">
        select cvor.id, cvor.vehicle_id, cvor.enterprise_id, cvor.enterprise, cvor.dept_id, cvor.dept_name, cvor.licence_plate, cvor.licence_color_code, cvor.licence_color,
        bv.vehicle_use_type as vehicle_model_code, cvor.vehicle_model, bv.vehicle_owner_id, cvor.vehicle_owner, bv.access_mode as access_mode_code, cvor.access_mode, cvor.month,
        cvor.d01, cvor.d02, cvor.d03, cvor.d04, cvor.d05, cvor.d06, cvor.d07, cvor.d08, cvor.d09, cvor.d10, cvor.d11, cvor.d12, cvor.d13, cvor.d14, cvor.d15, cvor.d16, cvor.d17, cvor.d18,
        cvor.d19, cvor.d20, cvor.d21, cvor.d22, cvor.d23, cvor.d24, cvor.d25, cvor.d26, cvor.d27, cvor.d28, cvor.d29, cvor.d30, cvor.d31, cvor.create_time, cvor.note
        from cache_vehicle_online_rate as cvor left join bdm_vehicle as bv on cvor.vehicle_id = bv.id
        where concat(cvor.licence_plate, '~', cvor.licence_color_code, '~', cvor.month) in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
