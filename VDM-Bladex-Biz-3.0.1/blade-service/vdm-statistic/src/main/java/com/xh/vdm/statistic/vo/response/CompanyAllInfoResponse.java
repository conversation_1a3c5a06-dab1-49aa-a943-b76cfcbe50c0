package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

/**
 * @Description:
 * @Author: zhouxw
 * @Date: 2022/11/25 2:32 PM
 */
@Data
public class CompanyAllInfoResponse {

    //企业id
    @JsonIgnore
    @ExcelIgnore
    private Long deptId;

    //企业名称
    @ColumnWidth(30)
    @ExcelProperty({"企业经营情况", "企业名称"})
    private String deptName;

    //统计日期
    @ColumnWidth(30)
    @ExcelProperty({"企业经营情况", "统计日期"})
    private String statDate;

    //行驶车辆数
    @ColumnWidth(30)
    @ExcelProperty({"企业经营情况", "行驶车辆数"})
    private Integer vehicleCount;

    //出勤驾驶员数
    @ColumnWidth(30)
    @ExcelProperty({"企业经营情况", "出勤驾驶员数"})
    private Integer driverCount;

    //车辆平均行驶里程
    @ColumnWidth(30)
    @ExcelProperty({"企业经营情况", "车辆日平均行驶里程（公里）"})
    private Double averageTravelMileage;

    //驾驶员平均驾驶时长
    @ColumnWidth(30)
    @ExcelProperty({"企业经营情况", "驾驶员日平均驾驶时长"})
    private Double averageDriveDuration;

    //驾驶员平均驾驶里程
    @ColumnWidth(30)
    @ExcelProperty({"企业经营情况", "驾驶员日平均驾驶里程（公里）"})
    private Double averageDriveMileage;

    //驾驶员平均不规范行为次数
    @ColumnWidth(30)
    @ExcelProperty({"企业经营情况", "驾驶员日平均不规范行为次数"})
    private Double averageAlarmCount;



}
