<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.DailyCarMegaByteMapper">

    <insert id="setDaily">
        insert into daily_car_mega_byte (licence_color, licence_plate, ymd, mega_byte,vehicle_id, dept_id, vehicle_use_type,vehicle_owner_id, access_mode) values
        <foreach collection='list' item='obj' index='i' separator=','>
            (#{obj.licenceColor}, #{obj.licencePlate}, #{obj.ymd}, #{obj.megaByte}, #{obj.vehicleId}, #{obj.deptId}, #{obj.vehicleUseType}, #{obj.vehicleOwnerId}, #{obj.accessMode})
        </foreach>
        on duplicate key update mega_byte = mega_byte + values(mega_byte)
    </insert>
</mapper>
