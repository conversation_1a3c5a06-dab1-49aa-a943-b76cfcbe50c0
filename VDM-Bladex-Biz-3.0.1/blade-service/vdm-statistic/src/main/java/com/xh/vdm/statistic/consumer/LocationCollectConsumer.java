package com.xh.vdm.statistic.consumer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springblade.common.constant.DictKeyConstant;
import org.springblade.common.constant.RedisConstant;
import org.springblade.entity.Location;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@EnableKafka
public class LocationCollectConsumer {

	private final SimpleDateFormat monthFormat = new SimpleDateFormat("MM");

	@Resource
	private RedisTemplate<String, Object> redisTemplate;

	@KafkaListener(
		containerFactory = "locationCollectKafkaListenerContainerFactory",
		topics = {"ce.comms.fct.location.0"},
		batch = "true"
	)
	public void recordLocationCollect (List<ConsumerRecord<String, String>> consumerRecords, Acknowledgment acknowledgment) {
		if (CollectionUtils.isEmpty(consumerRecords)) {
			return;
		}

		Map<Object, Object> targetMap =
			Boolean.TRUE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_BASE_INFO_TARGET)) ?
			this.redisTemplate.opsForHash().entries(RedisConstant.HASH_BASE_INFO_TARGET) :
			new HashMap<>();

		String msg;
		Location location;
		String targetKey;
		Object targetO;
		JSONObject targetJ;
		Object locCollectO;
		JSONObject locCollectJ;
		long harvest;
		int monthHarvest;
		long time;
		for (ConsumerRecord<String, String> consumerRecord : consumerRecords) {
			msg = consumerRecord.value();
//			log.info("receive msg when record location collect: {}", msg);
			location = JSON.parseObject(msg, Location.class);
			if (location == null) {
				log.error("fail parse location when record location collect: {}", msg);
				continue;
			}
			if (!location.getTargetType().toString().equals(DictKeyConstant.TARGET_TYPE_VEHICLE)) {
				continue;
			}

			targetKey = location.getTargetType() + "-" + location.getTargetId();
			if ((!targetMap.containsKey(targetKey)) || ((targetO = targetMap.get(targetKey)) == null)) {
				log.error("fail get target info when record location collect: {}", targetKey);
				continue;
			}

			targetJ = JSONObject.parseObject(targetO.toString());
			if ((targetJ == null) || (!targetJ.containsKey("deptId"))) {
				log.error("fail get dept info when record location collect: {}", targetO);
				continue;
			}

			targetKey += ("-" + targetJ.get("deptId"));
			time = System.currentTimeMillis();
			if (
				Boolean.FALSE.equals(this.redisTemplate.hasKey(RedisConstant.HASH_TARGET_LOCATION_COLLECT)) ||
				(!this.redisTemplate.opsForHash().hasKey(RedisConstant.HASH_TARGET_LOCATION_COLLECT, targetKey)) ||
				((locCollectO = this.redisTemplate.opsForHash().get(RedisConstant.HASH_TARGET_LOCATION_COLLECT, targetKey)) == null)
			) {
				locCollectJ = new JSONObject();
				harvest = 0;
				monthHarvest = 0;
			} else {
				locCollectJ = JSON.parseObject(JSON.toJSONString(locCollectO));
				harvest = locCollectJ.getLongValue("harvest");
				if (
					this.monthFormat.format(new Date(time)).equals(
						this.monthFormat.format(new Date(locCollectJ.getLong("time") * 1000))
					)
				) {
					monthHarvest = locCollectJ.getIntValue("month_harvest");
				} else {
					monthHarvest = 0;
				}
			}

			locCollectJ.put("harvest", ++harvest);
			locCollectJ.put("month_harvest", ++monthHarvest);
			locCollectJ.put("time", System.currentTimeMillis() / 1000);
			this.redisTemplate.opsForHash().put(RedisConstant.HASH_TARGET_LOCATION_COLLECT, targetKey, locCollectJ);
		}

		// 有点类似InnoDB事务的提交，如果消费了消息，但消息处理过程出现异常而退出，则消息可被回滚到列队中。只有执行到这步，相当于提交，消息队列才正式认为该消息已被消费。
		acknowledgment.acknowledge();
	}
}
