package com.xh.vdm.statistic.vo.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 异常车辆明细
 * 指标异常的车辆的指标明细信息
 * @Author: zhouxw
 * @Date: 2022/9/14 11:18 AM
 */
@Data
public class VehicleRateDetailResponse {

    //企业名称
    private String deptName;
    //车牌号
    private String licencePlate;
    //车牌颜色
    @JsonProperty("licenceColor")
    private String plateColor;
    //sim卡号
    private String simId;
    //终端id
    private String terminalId;
    //终端型号
    private String terminalModel;
    //漂移数量
    private int driftCount;
    //完整率
    private String completeRate;
    //合格率
    private String qualityRate;
    //统计时间
    private String date;

}
