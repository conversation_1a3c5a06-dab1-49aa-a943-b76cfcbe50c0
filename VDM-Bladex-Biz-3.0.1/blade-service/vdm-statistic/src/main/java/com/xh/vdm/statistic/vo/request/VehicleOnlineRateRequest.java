package com.xh.vdm.statistic.vo.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Description: 车辆在线率查询条件
 * @Author: zhouxw
 * @Date: 2023/3/8 10:25
 */
@Data
public class VehicleOnlineRateRequest {

    @ApiModelProperty(value = "查询开始时间")
    @JsonProperty("start_time")
    private Long startTime;

    @ApiModelProperty(value = "查询结束时间")
    @JsonProperty("end_time")
    private Long endTime;


    @ApiModelProperty(value = "车辆归属")
    @JsonProperty("vehicle_owner")
    private Long vehicleOwnerId;

    @ApiModelProperty(value = "行业类型")
    @JsonProperty("vehicle_use_type")
    private Long vehicleUseType;

    @ApiModelProperty(value = "车辆接入方式")
    @JsonProperty("access_mode")
    private Integer accessMode;


	@JsonProperty("vehicle_id_list")
	private List<Long> vehicleIdList;

	private List<String> dateList;

	//租户id
	private String tenantId;
	//登录用户id
	private Long userId;

	//所有车组，包含子车组
	private List<Long> deptList;
	//行业类型
	private List<String> professionList;



}
