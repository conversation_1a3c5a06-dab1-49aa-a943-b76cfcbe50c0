package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.entity.report.AlarmBaseTypeAndCount;
import org.springblade.core.secure.BladeUser;

import java.util.List;

/**
 * 查询impala报警数据
 */
public interface IImpalaAlarmService {

	/**
	 * 根据时间段查询每小时报警数量
	 * 查询 impala
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param vehicleIds
	 * @return
	 */
	List<DateAndCount> findAlarmCountHour(Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds);


	/**
	 * 根据时间段查询每小时报警处理数量
	 * @param startTime
	 * @param endTime
	 * @param deptIds
	 * @param vehicleIds
	 * @param userType
	 * @return
	 */
	List<DateAndCount> findAlarmHandleCountHour(Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds, String userType);



	/**
	 * @description: 查询指定日期和超过指定时长的疲劳驾驶报警数量
	 * 查询 impala
	 * @author: zhouxw
	 * @date: 2023-07-206 08:36:41
	 * @param: [startDate 开始时间 yyyy-MM-dd, endDate 结束时间 yyyy-MM-dd, duration 超过的秒数]
	 * @return: long
	 **/
	long findFatigueCountByDateAndDuration(Long startTime, Long endTime, long duration, List<Long> deptIds, List<Integer> vehicleIds) throws Exception;


	/**
	 * @description: 查询指定日期和超过指定时长的疲劳驾驶报警处理数量
	 * @author: zhouxw
	 * @date: 2023-07-206 08:36:41
	 * @param: [startDate 开始时间 yyyy-MM-dd, endDate 结束时间 yyyy-MM-dd, duration 超过的秒数, userType 用户类型(1 服务商  2 第三方  3 企业)]
	 * @return: long
	 **/
	long findFatigueHandleCountByDateAndDuration(Long startTime, Long endTime, long duration, List<Long> deptIds, List<Integer> vehicleIds, String userType) throws Exception;

	/**
	 * @description: 查询给定日期的报警数
	 * @author: zhouxw
	 * @date: 2023-07-206 11:06:07
	 * @param: [startDate: 开始时间 yyyy-MM-dd, endDate: 结束时间 yyyy-MM-dd, deptIds: 部门id, userId: 用户id]
	 * @return: long
	 **/
	long findAlarmCountByDate(List<Integer> alarmTypes, Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds) throws Exception;


	/**
	 * @description: 查询指定日期的每日报警数
	 * @author: zhouxw
	 * @date: 2023-07-206 11:06:07
	 * @param: [startDate: 开始时间 yyyy-MM-dd, endDate: 结束时间 yyyy-MM-dd, deptIds: 部门id, userId: 用户id]
	 * @return: long
	 **/
	List<DateAndCount> findAlarmAndHandleCountPerDate(Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds, String userType) throws Exception;


	/**
	 * @description: 查询指定日期的报警处理数
	 * @author: zhouxw
	 * @date: 2023-07-206 11:17:45
	 * @param: [startDate: 开始时间 yyyy-MM-dd, endDate: 结束时间 yyyy-MM-dd, deptIds: 部门id, userId: 用户id, userTYpe: 用户类型]
	 * @return: long
	 **/
	long findAlarmHandleCountByDate(List<Integer> alarmTypes, Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds, String userType) throws Exception;

	/**
	 * @description: 查询指定日期的报警基础信息
	 * @author: zhouxw
	 * @date: 2023-07-206 11:06:07
	 * @param: [startDate: 开始时间 yyyy-MM-dd, endDate: 结束时间 yyyy-MM-dd, deptIds: 部门id, userId: 用户id]
	 * @return: long
	 **/
	List<AlarmBase> findAlarmBaseByDate(List<Integer> alarmTypes, Long startTime, Long endTime, List<Long> deptIds, List<Integer> vehicleIds) throws Exception;

	/**
	 * @description: 查询已经处理过的报警数量
	 * @author: zhouxw
	 * @date: 2023-07-206 11:17:45
	 * @param: [alarmIds: 指定的报警id列表, userTYpe: 用户类型]
	 * @return: long
	 **/
	long findAlarmHandleCountByAlarmIds(List<Long> alarmIds, String userType) throws Exception;


	/**
	 * 分页查询实时未处理的报警
	 * @param deptIds
	 * @param vehicleIds
	 * @param userType
	 * @return
	 * @throws Exception
	 */
	IPage<UnHandleRealTimeAlarm> findUnHandleRealTimeAlarmByPage(List<Integer> alarmTypeList, List<Long> deptIds, List<Integer> vehicleIds, String userType, IPage<BdmSecurity> page) throws Exception;

	/**
	 * 根据条件查询报警数量
	 * @param deptIds
	 * @param vehicleIds
	 * @param startTime 开始时间，精确到秒
	 * @param endTime 结束时间，精确到秒
	 * @return
	 * @throws Exception
	 */
	long findAlarmCountByCondition(List<Long> deptIds, List<Integer> vehicleIds, Long startTime, Long endTime) throws Exception;

	/**
	 * 根据报警类型查询报警数量
	 * @param deptIds
	 * @param vehicleIds
	 * @param alarmTypes
	 * @param startTime 开始时间，精确到秒
	 * @param endTime 结束时间，精确到秒
	 * @return
	 * @throws Exception
	 */
	long findAlarmCountByAlarmTypes(List<Long> deptIds, List<Integer> vehicleIds, List<Integer> alarmTypes, Long startTime, Long endTime) throws Exception;


	/**
	 * 根据报警类型查询报警数量
	 * @param deptIds
	 * @param vehicleIds
	 * @param alarmTypes
	 * @param startTime 开始时间，精确到秒
	 * @param endTime 结束时间，精确到秒
	 * @return
	 * @throws Exception
	 */
	long findAlarmHandleCountByAlarmTypes(List<Long> deptIds, List<Integer> vehicleIds, List<Integer> alarmTypes, Long startTime, Long endTime, String userType) throws Exception;



	/**
	 * 查询报警类型和数量
	 * @param alarmTypes
	 * @param deptIds
	 * @param vehicleIds
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws Exception
	 */
	List<AlarmTypeAndDateAndCount> findAlarmTypeAndDateAndCountOrHandle(List<Integer> alarmTypes, List<Long> deptIds, List<Integer> vehicleIds, Long startTime, Long endTime, String userType) throws Exception;


	/**
	 * 查询报警类型和数量
	 * @param alarmTypes
	 * @param deptIds
	 * @param vehicleIds
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws Exception
	 */
	List<AlarmBaseTypeAndCount> findAlarmTypeAndCountOrHandle(List<Integer> alarmTypes, List<Long> deptIds, List<Integer> vehicleIds, Long startTime, Long endTime, String userType) throws Exception;


	/**
	 * 查询车辆报警排行前n的车辆
	 * @param alarmTypes
	 * @param deptIds
	 * @param vehicleIds
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws Exception
	 */
	List<VehicleAndCount> findVehicleAlarmCountTop(List<Integer> alarmTypes, List<Long> deptIds, List<Integer> vehicleIds, Long startTime, Long endTime, Integer count) throws Exception;

	/**
	 * 查询指定车辆的报警数量情况
	 * @param alarmTypes
	 * @param vehicleIds
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws Exception
	 */
	List<VehicleAndAlarmTypeAndCount> findVehicleAlarmCountInfo(List<Integer> alarmTypes, List<Integer> vehicleIds, Long startTime, Long endTime) throws Exception;


	/**
	 * 查询驾驶员驾驶时段内报警情况
	 * @param list
	 * @return
	 * @throws Exception
	 */
	List<AlarmTypeAndCount> findVehicleAlarmCountInfoByDriverDuration(List<StatDriverDuration> list, List<Integer> alarmTypes) throws Exception;



	/**
	 * 根据条件查询发生了报警的车辆数
	 * @param deptIds
	 * @param vehicleIds
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws Exception
	 */
	long findVehicleCountWithAlarm(List<Long> deptIds, List<Integer> vehicleIds,List<Integer> alarmTypes, Long startTime, Long endTime) throws Exception;

	/**
	 * @description: 根据部门id查询今日报警数量（包含本部门以及子部门、所有部门账户关联的外部车辆）
	 * 查询 impala
	 * @author: zhouxw
	 * @date: 2023-06-164 21:27:36
	 * @param: [deptId]
	 * @return: long
	 **/
	long findTodayTotalCountByDeptId(BladeUser user, long userId) throws Exception;
}
