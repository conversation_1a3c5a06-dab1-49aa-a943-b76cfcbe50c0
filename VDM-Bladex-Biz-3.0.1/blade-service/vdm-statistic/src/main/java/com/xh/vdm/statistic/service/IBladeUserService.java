package com.xh.vdm.statistic.service;

import com.xh.vdm.statistic.entity.BladeUser;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.UserDept;

import java.util.List;

/**
 * <p>
 * 用户表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
public interface IBladeUserService extends IService<BladeUser> {


	/**
	 * 根据roleId列表查询roleName列表
	 * @param roleIds
	 * @return
	 */
	public List<String> findRoleStringByRoleIds(List<Long> roleIds);

	/**
	 * 查询关联多个部门的账户
	 * @return
	 */
	List<Long> findUserIdsWithMultiDeptCount();

	/**
	 * 查询用户关联的部门
	 * @param userId
	 * @return
	 */
	List<Long> findDeptIdsByUserId(Long userId);

	void updateDeptId(UserDept userId);
}
