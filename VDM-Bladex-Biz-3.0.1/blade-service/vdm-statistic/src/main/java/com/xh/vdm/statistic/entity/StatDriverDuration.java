package com.xh.vdm.statistic.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-23
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StatDriverDuration implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 驾驶员身份证号
     */
    private String idCard;

    /**
     * @description: 驾驶员姓名
     * @author: zhouxw
     * @date: 2023-02-54 11:06:16
     * @param: * @param null
     * @return:
     * @return: null
     **/
    private String driverName;

    /**
     * 统计日期（yyyy-mm-dd）
     */
    private String statDate;

    /**
     * 驾驶车辆
     */
    private String licencePlate;

    //车牌颜色
    private Integer licenceColor;

    /**
     * 驾驶时段开始时间
     */
    private Long durationStartTime;

    /**
     * 驾驶时段结束时间
     */
    private Long durationEndTime;

    /**
     * 驾驶时长
     */
    private Long driveDuration;

    /**
     * 创建时间
     */
    private Date createTime;

	//针对跨天上线的车辆，a 标识是补充录入的之前的在线统计数据
	private String state;


}
