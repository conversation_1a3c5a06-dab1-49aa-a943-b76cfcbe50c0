package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xh.vdm.statistic.entity.report.StatReportDay;
import com.xh.vdm.statistic.vo.request.CompanyAndDate;
import com.xh.vdm.statistic.vo.response.ReportInfoResponse;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 企业日报表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-08
 */
public interface StatReportDayMapper extends BaseMapper<StatReportDay> {

	/**
	 * 分页查询日报信息
	 * @param cd
	 * @param page
	 * @return
	 */
	IPage<ReportInfoResponse> getReportDayPage(@Param("cd") CompanyAndDate cd, IPage<ReportInfoResponse> page);
}
