package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.StatDriverDuration;
import com.xh.vdm.statistic.vo.request.DriverDurationRequest;
import com.xh.vdm.statistic.vo.response.DriverDurationResponse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-23
 */
public interface IStatDriverDurationService extends IService<StatDriverDuration> {

    /**
     * @description: 根据指定日期删除数据
     * @author: zhouxw
     * @date: 2023-02-54 11:37:16
     * @param: [date]
     * @return: void
     **/
    void deleteDataByDate(String month, String date);

    /**
     * @description: 批量保存数据
     * @author: zhouxw
     * @date: 2023-02-54 14:04:02
     * @param: [list, month]
     * @return: void
     **/
    void saveBatch(@Param("list") List<StatDriverDuration> list , @Param("month") String month) throws Exception;

	/**
	 * 删除指定日期的追加数据
	 * @param dateList
	 * @throws Exception
	 */
	void deleteAppendData(List<String> dateList,String month) throws Exception;

    /**
     * @description: 根据条件查询驾驶员
     * @author: zhouxw
     * @date: 2023-02-54 15:27:15
     * @param: [request, month:yyyyMM]
     * @return: java.util.List<com.xh.vdm.statistic.vo.response.DriverDurationResponse>
     **/
    DriverDurationResponse findDriverByCondition(String month, DriverDurationRequest request);

	/**
	 * 根据身份证号查询驾驶时段信息
	 * @param idCard
	 * @param statDate yyyy-MM-dd
	 * @param month yyyy-MM
	 * @return
	 * @throws Exception
	 */
	List<StatDriverDuration> findDriverDurationListByIdCard(String idCard, String statDate, String month ) throws Exception;

	/**
	 * 根据身份证号查询驾驶时段信息，指定月份
	 * @param idCard
	 * @param month
	 * @return
	 * @throws Exception
	 */
	List<StatDriverDuration> findDriverDurationListByIdCardMonth(String idCard, String month ) throws Exception;



}
