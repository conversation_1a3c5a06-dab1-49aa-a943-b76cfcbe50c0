<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BdmDeviceLinkMapper">

    <sql id="deptBaseInfo">
        <if test="userId != null">
            join bdm_user_dept_regulates usr on usr.dept_id = dl.dept_id and usr.user_id = #{userId}
        </if>
    </sql>


    <!--查询指定行数据-->
    <select id="queryAll" resultType="com.xh.vdm.statistic.entity.BdmDeviceLink">
        select dl.id, dl.device_id, dl.device_type, dl.unique_id, dl.device_num, dl.target_id, dl.target_type, bat.number as target_name, dl.longitude, dl.latitude, dl.address, dl.action, dl.time, dl.remark, dl.dept_id
        from bdm_device_link dl
        left join bdm_abstract_device bad on dl.device_id = bad.id
        left join bdm_abstract_target bat on dl.target_id = bat.id
        where
        1 = 1
        <if test="bdl.deviceId != null">
            and dl.device_id = #{bdl.deviceId}
        </if>
        <if test="bdl.deviceType != null">
            and dl.device_type = #{bdl.deviceType}
        </if>
        <if test="bdl.uniqueId != null and bdl.uniqueId != ''">
            and dl.unique_id like concat('%', #{bdl.uniqueId}, '%')
        </if>
        <if test="bdl.deviceNum != null and bdl.deviceNum != ''">
            and dl.device_num like concat('%', #{bdl.deviceNum}, '%')
        </if>
        <if test="bdl.targetName != null and bdl.targetName != ''">
            and dl.target_name like concat('%', #{bdl.targetName}, '%')
        </if>
        <if test="bdl.action != null">
            and dl.action = #{bdl.action}
        </if>
        <if test="bdl.startTime != null">
            and dl.time &gt;= #{bdl.startTime}
        </if>
        <if test="bdl.endTime != null">
            and dl.time &lt;= #{bdl.endTime}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
        order by dl.time desc
        <if test="query.current gt 0 and query.size gt 0">
            limit ${query.size} offset ${(query.current - 1) * query.size}
        </if>
    </select>

    <select id="countLink" resultType="java.lang.Long">
        select count(*) from bdm_device_link dl
        left join bdm_abstract_device bad on dl.unique_id = bad.unique_id
        where
        1 = 1
        <if test="bdl.deviceId != null">
            and dl.device_id = #{bdl.deviceId}
        </if>
        <if test="bdl.deviceType != null">
            and dl.device_type = #{bdl.deviceType}
        </if>
        <if test="bdl.uniqueId != null and bdl.uniqueId != ''">
            and dl.unique_id like concat('%', #{bdl.uniqueId}, '%')
        </if>
        <if test="bdl.deviceNum != null and bdl.deviceNum != ''">
            and dl.device_num like concat('%', #{bdl.deviceNum}, '%')
        </if>
        <if test="bdl.targetName != null and bdl.targetName != ''">
            and dl.target_name like concat('%', #{bdl.targetName}, '%')
        </if>
        <if test="bdl.action != null">
            and dl.action = #{bdl.action}
        </if>
        <if test="bdl.startTime != null">
            and dl.time &gt;= #{bdl.startTime}
        </if>
        <if test="bdl.endTime != null">
            and dl.time &lt;= #{bdl.endTime}
        </if>
        <if test="deptIds != null and deptIds != ''">
            and bad.dept_id = any(${deptIds})
        </if>
        <if test="account != null and account != ''">
            and bad.create_account = #{account}
        </if>
    </select>

    <select id="getLongOfflineCount" resultType="long">

        select (
        (select count(*) from bdm_rnss_device a, bdm_device_link b where a.id = b.device_id and b.action = 1 and b.time
        &lt; to_timestamp(#{secondTimestamp})
        and a.dept_id in
        <foreach collection="deptList" item="deptId" separator="," open="(" close=")">
            #{deptId}
        </foreach>
        ) +

        (select count(*) from bdm_rdss_device a, bdm_device_link b where a.id = b.device_id and b.action = 1 and b.time
        &lt; to_timestamp(#{secondTimestamp})
        and a.dept_id in
        <foreach collection="deptList" item="deptId" separator="," open="(" close=")">
            #{deptId}
        </foreach>
        ) +

        (select count(*) from bdm_pnt_device a, bdm_device_link b where a.id = b.device_id and b.action = 1 and b.time
        &lt; to_timestamp(#{secondTimestamp})
        and a.dept_id in
        <foreach collection="deptList" item="deptId" separator="," open="(" close=")">
            #{deptId}
        </foreach>
        ) +

        (select count(*) from bdm_monit_device a, bdm_device_link b where a.id = b.device_id and b.action = 1 and b.time
        &lt; to_timestamp(#{secondTimestamp})
        and a.dept_id in
        <foreach collection="deptList" item="deptId" separator="," open="(" close=")">
            #{deptId}
        </foreach>
        ) +

        (select count(*) from bdm_wearable_device a, bdm_device_link b where a.id = b.device_id and b.action = 1 and
        b.time &lt; to_timestamp(#{secondTimestamp})
        and a.dept_id in
        <foreach collection="deptList" item="deptId" separator="," open="(" close=")">
            #{deptId}
        </foreach>
        )
        ) a
    </select>

    <select id="onlineTrendered" resultType="com.xh.vdm.statistic.vo.response.terminal.LinkCountResponse">
        SELECT DATE_TRUNC('hour', "time")                    AS hour_start,
               EXTRACT(HOUR FROM DATE_TRUNC('hour', "time")) AS hour_of_day,
               SUM(CASE WHEN "action" = 0 THEN 1 ELSE 0 END) AS online_count
        FROM "bdm_device_link" dl
        WHERE "time" >= NOW() - INTERVAL '23 hours'
        <if test="userId != null">
            AND EXISTS (
            SELECT 1
            FROM bdm_user_dept_regulates usr
            WHERE usr.dept_id = dl.dept_id
            AND usr.user_id = #{userId}
            )
        </if>
        GROUP BY hour_start;
    </select>

</mapper>

