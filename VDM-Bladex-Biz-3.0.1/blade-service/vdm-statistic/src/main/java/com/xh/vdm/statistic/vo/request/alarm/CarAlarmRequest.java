package com.xh.vdm.statistic.vo.request.alarm;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Pattern;

@ApiModel(value = "请求体：车辆报警统计")
@Data
@EqualsAndHashCode(callSuper = true)
public class CarAlarmRequest extends CommonAlarmStatRequest {

	@JsonProperty("alarm_level")
	@ApiModelProperty(name = "alarm_level", value = "报警等级（等级值与名称的映射，详见blade_dict_biz表，code=alarm_level的记录）", example = "1", required = false)
	@DecimalMin(value = "0", message = "报警等级不正确。")
	private Short alarmLevel;

	@JsonProperty("alarm_origin")
	@ApiModelProperty(name = "alarm_origin", value = "报警来源", example = "终端", required = false)
	private String alarmOrigin;

	@JsonProperty("duration_time")
	@ApiModelProperty(name = "duration_time", value = "持续时长（单位：秒）", example = "60", required = false)
	@DecimalMin(value = "0", message = "持续时长不正确。")
	private Integer durationTime;

	@JsonProperty("deal_state")
	@ApiModelProperty(name = "deal_state", value = "处理状态（0：未处理，1：已处理）", example = "1", required = false)
	private Byte dealState;

	@JsonProperty("speed")
	@ApiModelProperty(name = "speed", value = "速度（单位：km/h）", example = "60", required = false)
	private Double speed;

	@JsonProperty("vehicle_id")
	@ApiModelProperty(name = "vehicle_id", value = "车辆ID", example = "[1, ...]", required = false)
	private Integer vehicleId;
}
