package com.xh.vdm.statistic.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xh.vdm.statistic.entity.CacheSegLimitSpeedMapResponse;
import com.xh.vdm.statistic.vo.request.SegLimitSpeedMapRequest;
import com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse;

public interface ICacheSegLimitSpeedMapService extends IService<CacheSegLimitSpeedMapResponse> {

    /**
     * @description: 统计车辆地图超速，并入库
     * @author: zhouxw
     * @date: 2023-02-40 15:27:09
     * @param: [request, req]
     * @return: com.xh.vdm.statistic.vo.ObjectRestResponse<com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>>
     **/
    void statisticsSegLimitSpeedMap(Long startTime, Long endTime) throws Exception;


    /**
     * @description: 查询车辆地图超速
     * @author: zhouxw
     * @date: 2023-02-40 16:20:53
     * @param: [request, req]
     * @return: com.baomidou.mybatisplus.extension.plugins.pagination.Page<com.xh.vdm.statistic.vo.response.SegLimitSpeedMapResponse>
     **/
    IPage<SegLimitSpeedMapResponse> querySegLimitSpeedMapCache(SegLimitSpeedMapRequest req) throws Exception;


}
