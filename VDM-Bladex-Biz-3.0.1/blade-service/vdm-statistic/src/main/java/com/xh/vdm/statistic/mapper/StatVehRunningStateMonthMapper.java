package com.xh.vdm.statistic.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.StatVehRunningStateDay;
import com.xh.vdm.statistic.entity.StatVehRunningStateMonth;
import com.xh.vdm.statistic.vo.request.*;
import com.xh.vdm.statistic.vo.response.ErrLocationMoveResponse;
import com.xh.vdm.statistic.vo.response.NotLocationResponse;
import com.xh.vdm.statistic.vo.response.VehicleLastPositionResponse;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-20
 */
public interface StatVehRunningStateMonthMapper extends BaseMapper<StatVehRunningStateMonth> {

	/**
	 * 分页查询车辆运行状态
	 * @param request
	 * @param page
	 * @return
	 */
	IPage<StatVehRunningStateMonth> getVehRunningStateByPage(@Param("request") VehRunningStateRequest request, @Param("page") Page<StatVehRunningStateMonth> page, List<Long> deptIds, Long userId);

	/**
	 * @description: 判断表是否存在
	 * @author: zhouxw
	 * @date: 2022/9/2 8:07 AM
	 * @param: []
	 * @return: void
	 **/
	void checkExist(String month);

	/**
	 * 查询车辆最终定位点信息
	 * @param request
	 * @param page
	 * @return
	 */
	IPage<VehicleLastPositionResponse> getLastPositionInfo(@Param("request") CommonBaseRequest request, @Param("page") IPage<VehicleLastPositionResponse> page);


	/**
	 * 统计在线不定位信息
	 * @param request
	 * @param page
	 * @return
	 */
	IPage<NotLocationResponse> getNotLocationInfo(@Param("request") NotLocationRequest request, @Param("page") IPage<NotLocationResponse> page);


	/**
	 * 统计异常位移信息
	 * @param request
	 * @param page
	 * @return
	 */
	IPage<ErrLocationMoveResponse> getErrLocationMoveInfo(@Param("request") CommonBaseCrossMonthRequest request, @Param("page") IPage<ErrLocationMoveResponse> page);

	/**
	 * 查询车辆日里程
	 * 注意：返回的字段中包含重复的基础数据字段，如licencePlate 、 licenceColor 等，以 licencePlate(1)这样的形式存在
	 * @param request
	 * @return
	 */
	List<LinkedHashMap<String,Object>> getMileageDay(@Param("request")CommonBaseCrossMonthWithLineRequest request, @Param("month") String month, @Param("monthLine") String monthLine, @Param("dateList") List<String> dateList);

	/**
	 * 根据部门id 和 关联车辆id 查询车辆运行情况信息
	 * @param month yyyyMM
	 * @param date yyyyMMdd
	 * @param deptIds
	 * @param vehicleIds
	 * @return
	 */
	List<StatVehRunningStateDay> getStateInfoByDeptWithAlarm(@Param("month") String month, @Param("date") String date, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds);


	/**
	 * 根据部门、月份、关联车辆查询报警信息
	 * @param month
	 * @param deptIds
	 * @param vehicleIds
	 * @return
	 */
	List<StatVehRunningStateDay> getStateInfoByDeptWithAlarmMonth(@Param("month") String month, @Param("deptIds") List<Long> deptIds, @Param("vehicleIds") List<Integer> vehicleIds);
}
