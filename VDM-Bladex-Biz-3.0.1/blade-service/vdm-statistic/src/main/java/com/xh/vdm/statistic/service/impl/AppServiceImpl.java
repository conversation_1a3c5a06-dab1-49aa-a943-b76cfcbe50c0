package com.xh.vdm.statistic.service.impl;

import com.alibaba.nacos.api.utils.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.alarm.entity.AlarmInfo;
import com.xh.vdm.alarm.feign.IAlarmClient;
import com.xh.vdm.statistic.constant.StatisticConstants;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.mapper.CacheVehicleOnlineRateMapper;
import com.xh.vdm.statistic.mapper.LocationMapper;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.BeanUtil;
import com.xh.vdm.statistic.utils.LineAndHumpUtil;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.utils.VdmUserInfoUtil;
import com.xh.vdm.statistic.vo.request.CommonBaseCrossMonthWithLineRequest;
import com.xh.vdm.statistic.vo.request.VehicleOnlineRateRequest;
import com.xh.vdm.statistic.vo.request.VehicleReportRequest;
import com.xh.vdm.statistic.vo.response.VehicleOnlineOrOfflineResponse;
import com.xh.vdm.statistic.vo.response.VehicleRunningStateResponse;
import com.xh.vdm.statistic.vo.response.WxStatResponse;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.utils.DateUtil;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.system.user.entity.BdmVehicle;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;

@Slf4j
@Service
public class AppServiceImpl implements IAppService {


	@Resource
	private VdmUserInfoUtil vdmUserInfoUtil;

	@Resource
	private IBdmVehicleService vehicleService;

	@Resource
	private IVehicleStatService vehicleStatService;


	@Resource
	private CacheVehicleOnlineRateMapper onlineRateMapper;

	@Resource
	private LocationMapper locationMapper;

	@Resource
	private IStatVehRunningStateService vehRunningStateService;

	@Resource
	private IImpalaAlarmService impalaAlarmService;


	private ThreadPoolExecutor threadPool = new ThreadPoolExecutor(20, 50, 120, TimeUnit.SECONDS,  new LinkedBlockingQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());


	@Override
	public WxStatResponse findWxStatInfo(BladeUser user) throws Exception {

		String deptId = user.getDeptId();
		Long userId = user.getUserId();

		//查询本部门、子部门
		if(user == null){
			log.info("获取用户信息为空");
			throw new Exception("用户登录失败或未授权");
		}
		List<Long> deptIds = null;
		try {
			deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
		}catch (Exception e){
			log.error("查询部门信息失败",e);
			throw new Exception("用户登录失败或未授权");
		}

		WxStatResponse resp = new WxStatResponse();

		CountDownLatch countDownLatch = new CountDownLatch(5);

		//1.车辆总数
		List<Long> finalDeptIds2 = deptIds;
		threadPool.submit(() -> {
			long vehicleCount = 0;
			try{
				vehicleCount = vehicleService.findAllVehicleCount(finalDeptIds2, user.getUserId());
			}catch (Exception e){
				log.error("查询车辆总数失败");
			}finally {
				countDownLatch.countDown();
				resp.setTotalVehicleCount(vehicleCount);
			}
		});

		//2.车辆上线率
		List<Long> finalDeptIds = deptIds;
		List<Long> finalDeptIds3 = deptIds;
		threadPool.submit(() -> {
			//营运车辆数
			long runningCount = 0;
			//上线车辆数
			long goOnlineCount = 0;
			try{
				runningCount = vehicleService.findRunningCount(finalDeptIds, user.getUserId());
				goOnlineCount = vehicleService.findGoOnlineCountAllToday(finalDeptIds, user.getUserId());
				double goOnlineRate = 0D;
				if(runningCount != 0){
					goOnlineRate = MathUtil.divideRoundDouble(goOnlineCount, runningCount, 4);
				}
				String goOnlineRateStr = MathUtil.formatToPercent(goOnlineRate, 2);
				resp.setGoOnlineRate(goOnlineRate);
				resp.setGoOnlineRateStr(goOnlineRateStr);

				//4.行驶里程
				String statDate = DateUtil.sdfHolderShortNoLine.get().format(new Date());
				double totalMileage = vehicleStatService.statDeptMileageForApp(statDate, finalDeptIds3, userId);
				resp.setTotalMileage(MathUtil.roundDouble(totalMileage/1000,2));

				//5.车均里程（总里程/上线车辆数）
				double perVehicleMileage = MathUtil.divideRoundDouble(totalMileage/1000, goOnlineCount,2);
				resp.setPerMileage(perVehicleMileage);
			}catch (Exception e){
				log.error("查询车辆上线率出错",e);
			}finally{
				countDownLatch.countDown();
			}

		});

		//3.在线车辆数
		List<Long> finalDeptIds1 = deptIds;
		threadPool.submit(() -> {
			long onlineCount = 0;
			try{
				onlineCount = vehicleService.findOnlineCount(finalDeptIds1, user.getUserId());
			}catch (Exception e){
				log.error("查询在线车辆数出错",e);
			}finally {
				resp.setOnlineCount(onlineCount);
				countDownLatch.countDown();
			}
		});



		//6.报警总数
		threadPool.submit(() -> {
			long totalAlarmCount = 0;
			try {
				totalAlarmCount = impalaAlarmService.findTodayTotalCountByDeptId(user, userId);
			}catch (Exception e){
				log.error("查询报警情况接口报错",e);
			}finally{
				resp.setTotalAlarmCount(totalAlarmCount);
				countDownLatch.countDown();
			}

		});

		//7.长期不在线数量
		threadPool.submit(() -> {
			VehicleRunningStateResponse response = null;
			try{
				response = vehicleService.statVehicleRunningState(user);
			}catch (Exception e){
				log.error("查询长期不在线车辆数失败",e);
			}finally {
				resp.setLongOfflineVehicleCount(response.getOfflineLongTime());
				countDownLatch.countDown();
			}
		});
		countDownLatch.await();
		return resp;
	}


	@Override
	public List<VehicleBaseWithId> findVehicleByDeptIdAndLicencePlate(Long deptId, String licencePlate, BladeUser user) throws Exception {

		List<Long> deptIds = null;
		if(deptId == null){
			//如果没有指定部门，则查询所有部门
			deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
		}else{
			deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(deptId);
		}
		List<BdmVehicle> vList = vdmUserInfoUtil.getVehicleListByUserId(user);
		List<Integer> vehicleIds = new ArrayList<>();
		vList.forEach(item -> {
			vehicleIds.add(item.getId());
		});
		if(deptIds == null || deptIds.size() == 0){
			throw new Exception("查询出错");
		}
		return vehicleService.findVehicleBaseListAll(deptIds, vehicleIds, licencePlate);
	}


	@Override
	public Map<String,Object> findVehicleReportInfo(VehicleReportRequest req, Query query, BladeUser user) throws Exception {
		//1.获取车辆分页情况
		//根据开始时间和结束时间获取查询月份
		List<String> months = com.xh.vdm.statistic.utils.DateUtil.getMonthList(req.getStartTime(), req.getEndTime());
		IPage<VehicleOnlineOrOfflineResponse> page = new Page<>();
		page.setSize(query.getSize());
		page.setCurrent(query.getCurrent());
		VehicleOnlineRateRequest param = new VehicleOnlineRateRequest();
		param.setVehicleIdList(req.getVehicleIdList());
		param.setStartTime(req.getStartTime());
		param.setEndTime(req.getEndTime());

		long start1 = System.currentTimeMillis();
		List<Long> deptIds = vdmUserInfoUtil.getChildrenAndSelfDeptId(user);
		param.setDeptList(deptIds);
		param.setUserId(user.getUserId());
		List<Long> vehicleIds = new ArrayList<>();
		IPage<VehicleBase> pageList = new Page<>();
		if (CollectionUtils.isEmpty(req.getVehicleIdList())) {
			pageList = onlineRateMapper.getVehicleList(page, param, months);
			if(pageList == null || pageList.getRecords() == null || pageList.getRecords().size() == 0){
				IPage<Map> pageRes = new Page<Map>();
				pageRes.setSize(query.getSize());
				pageRes.setCurrent(query.getCurrent());
				Map<String,Object> mapRes = BeanUtil.beanToMap(pageRes);
				return mapRes;
			}
			pageList.getRecords().forEach(item -> {
				vehicleIds.add(item.getVehicleId());
			});
		}else{
			vehicleIds.addAll(req.getVehicleIdList());
			List<VehicleCommonInfo> tmpList = vehicleService.findVehicleCommonInfo(vehicleIds);
			List<VehicleBase> baseList = new ArrayList<>();
			tmpList.forEach(item -> {
				VehicleBase base = new VehicleBase();
				base.setVehicleId(item.getVehicleId());
				base.setLicencePlate(item.getLicencePlate());
				base.setLicenceColor(Integer.parseInt(item.getLicenceColor()));
				baseList.add(base);
			});
			pageList.setCurrent(req.getCurrent());
			pageList.setSize(req.getSize());
			pageList.setRecords(baseList);
			pageList.setTotal(baseList.size());
		}

		long totalStatDaysCount = DateUtil.getDateList(req.getStartTime(), req.getEndTime()).size();
		long end1 = System.currentTimeMillis();
		log.info("-=-=-=-=-=-=查询车辆列表耗时："+(end1 - start1));

		CountDownLatch countDownLatch = new CountDownLatch(4);
		Map<String,Double> mileageMap = new HashMap<>();
		threadPool.submit(() -> {
			try{
				long start = System.currentTimeMillis();
				//查询车辆里程情况
				CommonBaseCrossMonthWithLineRequest mReq = new CommonBaseCrossMonthWithLineRequest();
				mReq.setStartTime(req.getStartTime());
				mReq.setEndTime(req.getEndTime());
				mReq.setVehicleIdList(req.getVehicleIdList());
				List<DateListAndMonth> dateList = com.xh.vdm.statistic.utils.DateUtil.getDateListAndMonth(req.getStartTime(), req.getEndTime());
				List<DateListAndMonthWithLine> dmList = new ArrayList<>();
				dateList.forEach(item -> {
					List<String> dList = item.getDateList();
					List<String> dListTmp = new ArrayList<>();
					dList.forEach(d -> {
						dListTmp.add(d.substring(8,10));
					});
					String monthLine = item.getMonth();
					String month = item.getMonth().replace("-","");
					DateListAndMonthWithLine dm = new DateListAndMonthWithLine();
					dm.setMonth(month);
					dm.setMonthLine(monthLine);
					dm.setDateList(dListTmp);
					dmList.add(dm);
				});
				mReq.setDmList(dmList);
				mReq.setDeptList(deptIds);
				mReq.setUserId(user.getUserId());
				mReq.setVehicleIdList(vehicleIds);
				//里程单位为：km
				List<Map<String,Object>> mileageList = null;
				mileageList = vehRunningStateService.findVehicleMileageDay(mReq, query);
				//使用map记录里程
				mileageList.forEach(item -> {
					String key = item.get("licencePlate")+"~"+item.get("licenceColor");
					item.forEach((k,v) -> {
						String[] arr = k.split("-");
						if(arr != null && arr.length == 3){
							//记录里程数据
							mileageMap.put(key+"~"+k,(Double)v);
						}
					});
				});
				long end = System.currentTimeMillis();
				log.info("-=-=-=-=查询里程耗时"+(end - start));
			}catch (Exception e){
				log.error("查询车辆里程信息报错",e);
			}finally {
				countDownLatch.countDown();
			}
		});

		final List<CacheVehicleOnlineRate>[] cacheList = new List[]{new ArrayList<>()};
		IPage<VehicleBase> finalPageList = pageList;
		threadPool.submit(new Runnable() {
			@Override
			public void run() {
				long start = System.currentTimeMillis();
				try{
					//2.查询每辆车的上线情况
					long start2 = System.currentTimeMillis();
					List<VehicleBase> list = finalPageList.getRecords();
					Set queryList = new HashSet();
					for(VehicleBase v : list){
						months.forEach(m -> {
							queryList.add(v.getLicencePlate()+"~"+v.getLicenceColor()+"~"+m);
						});
					}
					cacheList[0] = onlineRateMapper.getCacheVehicleOnlineRateWithCondition(queryList);
					long end2 = System.currentTimeMillis();
					log.info("-=-=-=-=-=-=-=-=-=-=查询车辆缓存耗时："+(end2 - start2));
				}catch (Exception e){
					log.error("查询车辆上线情况报错",e);
				}finally {
					countDownLatch.countDown();
				}
				long end = System.currentTimeMillis();
				log.info("-=-=-=-=-=-=查询上线耗时："+(end - start));
			}
		});

		IPage<VehicleBase> finalPageList1 = pageList;
		Map<String, Long> startLocationsMap = new HashMap<>();
		Map<String, Long> endLocationsMap = new HashMap<>();
		threadPool.submit(() -> {
			long start = System.currentTimeMillis();
			try{
				//查询车辆定位开始时间和定位结束时间
				List<String> vehicleList = new ArrayList<>();
				finalPageList1.getRecords().forEach(item -> {
					String key = item.getLicencePlate() + "~" + item.getLicenceColor();
					vehicleList.add(key);
				});

				List<Map<String, Object>> dailyCarLocStartList = this.locationMapper.getDailyCarLocStart(req.getStartTime(), req.getEndTime(), vehicleList);
				dailyCarLocStartList.forEach(
					item -> startLocationsMap.put(item.get("licence_color") + "-" + item.get("licence_plate") + "-" + item.get("loc_date"), Long.parseLong(item.get("loc_start").toString()))
				);
			}catch (Exception e){
				log.error("查询定位开始时间报错",e);
			}finally{
				countDownLatch.countDown();
			}
			long end = System.currentTimeMillis();
			log.info("-=-=-=-=-=-=-=查询定位信息耗时："+(end - start));
		});

		threadPool.submit(() -> {
			long start = System.currentTimeMillis();
			try{
				//查询车辆定位开始时间和定位结束时间
				List<String> vehicleList = new ArrayList<>();
				finalPageList1.getRecords().forEach(item -> {
					String key = item.getLicencePlate() + "~" + item.getLicenceColor();
					vehicleList.add(key);
				});

				List<Map<String, Object>> dailyCarLocEndList = this.locationMapper.getDailyCarLocEnd(req.getStartTime(), req.getEndTime(), vehicleList);
				dailyCarLocEndList.forEach(
					item -> endLocationsMap.put(item.get("licence_color") + "-" + item.get("licence_plate") + "-" + item.get("loc_date"), Long.parseLong(item.get("loc_end").toString()))
				);
			}catch (Exception e){
				log.error("查询定位结束时间报错",e);
			}finally{
				countDownLatch.countDown();
			}
			long end = System.currentTimeMillis();
			log.info("-=-=-=-=-=-=-=查询定位结束信息耗时："+(end - start));
		});

		countDownLatch.await();

		//3.整理数据
		//按照月份对车辆上线情况进行分组
		long start3 = System.currentTimeMillis();
		Map<String, List<CacheVehicleOnlineRate>> map = new HashMap<>();
		//resList中保存的为每辆车在每个月中的在线情况，如果查询的是2个月份的数据，那么每辆车会在list中有2条数据
		List<Map<String,Object>> resList = new ArrayList<>();

		cacheList[0].forEach(item -> {
			String month = item.getMonth();
			List<CacheVehicleOnlineRate> tmpList = map.get(month);
			if(tmpList == null){
				tmpList = new ArrayList<>();
			}
			tmpList.add(item);
			map.put(month, tmpList);
		});

		//获取查询时间段
		String startDateStr = com.xh.vdm.statistic.utils.DateUtil.getDateString(req.getStartTime());
		String endDateStr = com.xh.vdm.statistic.utils.DateUtil.getDateString(req.getEndTime());
		int startDate = Integer.parseInt(startDateStr.substring(8,10));
		int endDate = Integer.parseInt(endDateStr.substring(8,10));


		if(months.size() == 1){
			String month = months.get(0);
			//如果是在同月
			List<Map<String,Object>> hashList = getCacheVehicleOnlineRateDataInMonth(cacheList[0], month, startDate, endDate, totalStatDaysCount, mileageMap, startLocationsMap, endLocationsMap);
			resList.addAll(hashList);

		}else if(months.size() > 1){
			//如果大于1个月
			//计算开头的月份
			String monthStart = months.get(0);
			int days = com.xh.vdm.statistic.utils.DateUtil.getDaysCountInMonth(monthStart);
			List<Map<String,Object>> hashListStart = getCacheVehicleOnlineRateDataInMonth(map.get(monthStart), monthStart, startDate, days, totalStatDaysCount,mileageMap, startLocationsMap, endLocationsMap);
			resList.addAll(hashListStart);

			//计算中间的月份
			for(int i = 1; i < months.size() - 1; i++){
				String monthM = months.get(i);
				int daysM = com.xh.vdm.statistic.utils.DateUtil.getDaysCountInMonth(monthM);
				List<Map<String,Object>> hashListM = getCacheVehicleOnlineRateDataInMonth(map.get(monthM), monthM, 1, daysM, totalStatDaysCount,mileageMap, startLocationsMap, endLocationsMap);
				resList.addAll(hashListM);
			}

			//计算结尾的月份
			String monthEnd = months.get(months.size() - 1);
			List<Map<String,Object>> hashListEnd = getCacheVehicleOnlineRateDataInMonth(map.get(monthEnd), monthEnd, 1, endDate, totalStatDaysCount,mileageMap, startLocationsMap, endLocationsMap);
			resList.addAll(hashListEnd);
		}

		//4.计算平均在线率（总在线时长/总时长）
		//总在线时长
		Map<String,Map<String,Object>> resMap = new HashMap<>();
		//将相同车牌号、车牌颜色的车辆进行合并（多个月份的数据，合并为一条）
		for (Map<String, Object> item : resList) {
			String licencePlate = item.get(StatisticConstants.ONLINE_RATE_LICENCE_PLATE)==null?"":item.get(StatisticConstants.ONLINE_RATE_LICENCE_PLATE).toString();
			String licenceColor = item.get(StatisticConstants.ONLINE_RATE_LICENCE_COLOR)==null?"":item.get(StatisticConstants.ONLINE_RATE_LICENCE_COLOR).toString();
			Map<String, Object> tmpMap = resMap.get(licencePlate+"~"+licenceColor);
			if (tmpMap == null) {
				resMap.put(licencePlate + "~" + licenceColor, item);
			} else {
				tmpMap.put("online_num", ((int) tmpMap.getOrDefault("online_num", 0)) + ((int) item.getOrDefault("online_num", 0)));
				tmpMap.put("offline_num", ((long) tmpMap.getOrDefault("offline_num", 0)) + ((long) item.getOrDefault("offline_num", 0)));
				for (String k : item.keySet()) {
					if (k.matches("\\d{4}-\\d{2}-\\d{2}")) {
						tmpMap.put(k, item.get(k));
					}
				}
			}
		}
		//添加日期列表
		Set<String> dateList = new HashSet<>();
		resMap.forEach((k,v) -> {
			//删除不需要的字段
			v.remove("class");
			v.remove("note");
			v.remove("enterpriseId");
			v.remove("licenceColorCode");
			v.remove("deptId");
			v.remove("id");
			v.remove("vehicleOwnerId");
			v.remove("vehicleModelCode");
			v.remove("accessModeCode");
			v.remove("createTime");
			v.remove("month");
			v.remove("totalOnlineTime");
			v.remove("enterprise");
			v.remove("vehicleId");
			v.remove("vehicleOwner");
			//删除 dxx 字段
			Set<String> set = new HashSet<String>(v.keySet());
			for(String key : set){
				String[] dateKey = key.split("-");
				if(dateKey != null && dateKey.length == 3){
					dateList.add(key);
				}
				if(key.indexOf("d") == 0 && key.length() == 3){
					v.remove(key);
				}else{
					//更改字段名称为下划线形式
					if(key.indexOf("_") < 0) {
						String keyLine = LineAndHumpUtil.humpToLine(key);
						v.put(keyLine, v.get(key));
						if(!key.equals(keyLine)){
							//如果原来字段中就不包含大写字母，则不进行替换
							v.remove(key);
						}
					}
				}
			}
			//v.put("dateList",dateList);
		});

		List<Map> retList = new ArrayList<>(resMap.values());

		IPage<Map> retPageList = new Page<>();
		retPageList.setCurrent(page.getCurrent());
		retPageList.setSize(page.getSize());
		retPageList.setRecords(retList);
		retPageList.setTotal(pageList.getTotal());

		long end3 = System.currentTimeMillis();
		log.info("-=-=-=-=-=-=-=-=-=数据整理耗时："+(end3 - start3));

		Map<String,Object> mapRes = BeanUtil.beanToMap(retPageList);
		mapRes.put("dateList",dateList);

		return mapRes;

	}




	/**
	 * @description: 获取车辆在线情况缓存数据
	 * @author: zhouxw
	 * @date: 2023-03-67 17:32:38
	 * @param: [cacheList 从数据库查询到的缓存数据, month 月份数据, startDate 开始日期, end 结束日期]
	 * @return: java.util.HashMap
	 **/
	private List<Map<String,Object>> getCacheVehicleOnlineRateDataInMonth(List<CacheVehicleOnlineRate> cacheList , String month, int startDate , int endDate, long totalStatDaysCount,Map<String,Double> mileageMap, Map<String, Long> startLocationsMap, Map<String, Long> endLocationsMap) {
		List<Map<String,Object>> resList = new ArrayList<>();
		if(cacheList == null || cacheList.size() < 1){
			return resList;
		}
		cacheList.forEach(item -> {
			try {
				Map<String,Object> tmpMap = BeanUtil.beanToMap(item);
				Map<String,Object> mapT = new HashMap<>();
				//复制基础数据
				for(String key : tmpMap.keySet()){
					if(!(key.length() == 3 && key.indexOf("d") == 0)){
						mapT.put(key, tmpMap.get(key));
					}
				}
				//添加在线率信息
				//在线天数
				int dayCount = 0;
				double totalOnlineTime = 0D;
				List<String> ratePercentList = new ArrayList<>();
				for(int i = startDate; i <= endDate; i++){
					String date = "";
					//在线时长
					long onlineTime = 0;
					if(i < 10){
						date = "0" + i;
						onlineTime = Integer.parseInt(tmpMap.get("d" + date)==null?"0":tmpMap.get("d" + date).toString());
					}else{
						date = "" + i;
						onlineTime = Integer.parseInt(tmpMap.get("d" + i)==null?"0":tmpMap.get("d" + i).toString());
					}
					if(onlineTime > 0){
						dayCount ++;
					}
					totalOnlineTime += (onlineTime>86400?86400:onlineTime);
					//整合字段
					String resStr = "";
					String key = mapT.get("licencePlate")+"~"+mapT.get("licenceColorCode");
					//添加里程
					String dateStr = month+"-"+date;
					resStr += (((onlineTime <= 0) ? "0" : DateUtil.timeFormat((onlineTime > 86400) ? 86400 : onlineTime)) + "|" + mileageMap.get(key + "~" + dateStr) + "|");
					//添加定位开始时间、添加定位结束时间
					String k = mapT.get("licenceColorCode") + "-" + mapT.get("licencePlate") + "-" + dateStr;
					resStr += (((startLocationsMap.get(k) == null) ? "未定位" : DateUtil.transDateTo17YMDHMS(new Date(startLocationsMap.get(k) * 1000))) + "|");
					resStr += ((endLocationsMap.get(k) == null) ? "未定位" : DateUtil.transDateTo17YMDHMS(new Date(endLocationsMap.get(k) * 1000)));
					mapT.put(dateStr,resStr);
				}

				//添加在线天数
				mapT.put(StatisticConstants.ONLINE_NUM, dayCount);
				//添加离线天数
				mapT.put(StatisticConstants.OFFLINE_NUM, totalStatDaysCount - dayCount);
				resList.add(mapT);
			} catch (Exception e) {
				log.error("bean 转 map 失败",e);
				throw new RuntimeException(e);
			}
		});
		return resList;
	}

}
