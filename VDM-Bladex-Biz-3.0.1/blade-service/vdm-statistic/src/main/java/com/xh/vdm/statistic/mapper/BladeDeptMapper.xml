<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xh.vdm.statistic.mapper.BladeDeptMapper">

    <select id="getDeptAndEmail" resultType="com.xh.vdm.statistic.entity.DeptAndEmail">
        select sd.id deptId, sd.dept_name deptName, su.email, su.id user_id, su.account username
        from blade_dept sd, blade_user_dept sud, blade_user su
        where sd.id = sud.dept_id and sud.user_id = su.id and sd.is_deleted = 0 and su.is_deleted = 0
          and LENGTH(email) > 0
    </select>

    <select id="getDeptTreeData" resultType="com.xh.vdm.statistic.entity.DeptTree">
        select * from blade_dept where tenant_id = #{tenantId} and is_deleted = 0
    </select>
</mapper>
