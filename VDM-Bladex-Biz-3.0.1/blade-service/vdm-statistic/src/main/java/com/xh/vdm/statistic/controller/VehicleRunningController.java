package com.xh.vdm.statistic.controller;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xh.vdm.statistic.entity.*;
import com.xh.vdm.statistic.service.*;
import com.xh.vdm.statistic.utils.CommonBusiUtil;
import com.xh.vdm.statistic.utils.DateUtil;
import com.xh.vdm.statistic.utils.EasyExcelUtils;
import com.xh.vdm.statistic.utils.MathUtil;
import com.xh.vdm.statistic.vo.request.*;
import com.xh.vdm.statistic.vo.response.*;
import javassist.ClassPool;
import javassist.CtClass;
import javassist.CtField;
import javassist.CtMethod;
import lombok.extern.slf4j.Slf4j;
import org.springblade.common.constant.CommonConstant;
import org.springblade.core.mp.support.Query;
import org.springblade.core.secure.BladeUser;
import org.springblade.core.tool.api.R;
import org.springblade.system.entity.DictBiz;
import org.springblade.system.feign.IDictBizClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 车辆运行状态统计
 */
@RestController
@RequestMapping("/statistics/vehicleRunning")
@Slf4j
public class VehicleRunningController {

	@Resource
	private IBdmVehicleService vehicleService;

	@Resource
	private IStatVehRunningStateService statService;

	@Resource
	private CommonBusiUtil commonBusiUtil;
	@Resource
	private IBladeDeptService deptService;

	@Resource
	private IBamThirdPartyPlatformService thirdPartyPlatformService;

	@Resource
	private IDictBizClient dictBizClient;

	@Resource
	private CommonBusiUtil busiUtil;

	@Value("${static.file.path:/statistic/files/}")
	String staticFilePath;

	//前端代理的文件路径，用于在前端访问或者下载使用，可以使用nginx等工具代理
	@Value("${proxy.file.path:/bt/statistics/files/}")
	private String proxyFilePath ;

	@Resource
	private IBdmTerminalSimService terminalSimService;

	/**
	 * 车辆在线天数统计
	 * @param request
	 * @param query
	 * @return
	 */
	@PostMapping("/statOnlineDaysCount")
	public R<IPage<OnlineDaysCountResponse>> statOnlineDaysCount(@RequestBody CommonBaseRequest request, Query query){

		//查询要统计的车辆列表
		List<Long> vehicleIdList = new ArrayList<>();
		long vehicleCount = 0;
		IPage<BdmVehicle> vehicleList = new Page<>();
		if(request.getVehicleIdList() != null){
			//如果指定了查询的车辆
			int startIndex = (query.getCurrent() - 1) * query.getSize();
			int endIndex = query.getCurrent() * query.getSize();
			endIndex = endIndex > request.getVehicleIdList().size()?request.getVehicleIdList().size():endIndex;
			vehicleIdList = request.getVehicleIdList().subList(startIndex, endIndex );
			vehicleCount = request.getVehicleIdList().size();
			List<BdmVehicle> vList = vehicleService.listByIds(vehicleIdList);
			vehicleList.setRecords(vList);
		}else{
			//如果没有指定查询车辆，则分页查询要统计的车辆
			//查询行业类型
			List<String> vehicleUseTypeList = new ArrayList<>();
			if(!StringUtils.isEmpty(request.getVehicleUseType())){
				R<List<DictBiz>> r = dictBizClient.getDictTreeFlatByCodeAndKeyWithSelf(CommonConstant.DICT_VEHICLE_USE_TYPE, request.getVehicleUseType());
				if(r.isSuccess()){
					List<DictBiz> list = r.getData();
					if(list == null || list.size() < 1){
						DictBiz db = new DictBiz();
						db.setCode(CommonConstant.DICT_VEHICLE_USE_TYPE);
						db.setDictKey(request.getVehicleUseType());
						list.add(db);
					}
					list.forEach(item -> {
						vehicleUseTypeList.add(item.getDictKey());
					});
				}
			}
			vehicleList = vehicleService.getVehicleListByPage(vehicleUseTypeList, request.getVehicleOwnerId(), request.getAccessMode(), query);
			vehicleIdList = vehicleList.getRecords().parallelStream().map(BdmVehicle::getId).map(item -> (long)item).collect(Collectors.toList());
			vehicleCount = vehicleList.getTotal();
		}

		if(vehicleIdList == null || vehicleIdList.size() < 1){
			return R.data(null);
		}

		try {

			//1.获取查询时段
			long startTime = 0L;
			long endTime = 0L;
			//如果没有指定时段，则默认指定到7天前
			if (request.getStartTime() == null) {
				startTime = DateUtil.getSecondTimeStampBeforeDay(new Date(), 7);
			}else{
				startTime = request.getStartTime();
			}
			if(request.getEndTime() == null){
				endTime = DateUtil.getSecondTimeStampBeforeDay(new Date(), 1);
			}else{
				endTime = request.getEndTime();
			}

			int daysCount = DateUtil.getDayCountBetweenSecondTimestamp(startTime, endTime);

			//查询车辆基础数据
			Map<String, BdmVehicle> vehicleMap = new HashMap<>();
			Set<Long> vehicleOwnerIdSet = new HashSet<>();
			Set<Long> deptIdSet = new HashSet<>();
			Map<String,String> vehicleUseTypeMap = new HashMap<>();
			vehicleList.getRecords().forEach(item -> {
				String key = item.getLicencePlate()+"~"+item.getLicenceColor();
				vehicleMap.put(key, item);
				vehicleOwnerIdSet.add(item.getVehicleOwnerId());
				deptIdSet.add(item.getDeptId());
				vehicleUseTypeMap.put(key,item.getVehicleUseType());
			});

			//查询deptName
			List<BladeDept> deptList = deptService.listByIds(deptIdSet);
			Map<String,String> deptNameMap = new HashMap<>();
			deptList.forEach(item -> {
				deptNameMap.put(item.getId()+"", item.getDeptName());
			});

			//查询vehicleOwner
			List<BamThirdPartyPlatform> ownerList = thirdPartyPlatformService.listByIds(vehicleOwnerIdSet);
			Map<String,String> ownerNameMap = new HashMap<>();
			ownerList.forEach(item -> {
				ownerNameMap.put(item.getId()+"",item.getName());
			});

			//2.统计车辆在线天数
			List<OnlineDaysCountResponse> onlineDaysCountList = getOnlineDaysCount(startTime, endTime, vehicleIdList);

			//3.统计车辆总行驶里程
			List<OnlineDaysCountResponse> totalMileageList = getTotalMileage(startTime, endTime,vehicleIdList);

			//4.组合数据
			Map<String,OnlineDaysCountResponse> onlineDaysMap = new HashMap<>();
			Map<String,OnlineDaysCountResponse> mileageMap = new HashMap<>();
			onlineDaysCountList.forEach(item -> {
				String key = item.getLicencePlate() + "~" + item.getLicenceColorCode();
				onlineDaysMap.put(key, item);
			});

			totalMileageList.forEach(item -> {
				String key = item.getLicencePlate() + "~" + item.getLicenceColorCode();
				mileageMap.put(key, item);
			});

			List<OnlineDaysCountResponse> resList = new ArrayList<>();

			onlineDaysMap.forEach((k,v) -> {
				BdmVehicle bv = vehicleMap.get(k);
				v.setOfflineDaysCount(daysCount - v.getOnlineDaysCount());
				v.setVehicleUseType(vehicleUseTypeMap.get(k));
				v.setDeptName(deptNameMap.get(bv.getDeptId()+""));
				if(bv.getVehicleOwnerId() == 0){
					v.setVehicleOwner("非营运车辆");
				}else{
					v.setVehicleOwner(ownerNameMap.get(bv.getVehicleOwnerId()+""));
				}

				OnlineDaysCountResponse tmp = mileageMap.get(k);
				if(tmp != null){
					v.setTotalMileage(tmp.getTotalMileage());
				}else{
					v.setTotalMileage(0D);
				}
				resList.add(v);
			});

			//5.组装分页数据
			IPage<OnlineDaysCountResponse> page = new Page<>();
			page.setCurrent(query.getCurrent());
			page.setSize(query.getSize());
			page.setTotal(vehicleCount);
			page.setRecords(resList);
			return R.data(page);

		}catch (Exception e){
			log.error("查询失败",e);
			return R.fail("查询失败");
		}
	}

	/**
	 * 车辆在线天数统计 导出
	 * @param request
	 * @param query
	 * @return
	 */
	@PostMapping("/statOnlineDaysCount/export")
	public R<String> statOnlineDaysCountExport(@RequestBody CommonBaseRequest request, Query query, String exportPath, String deptName){

		try {

			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<OnlineDaysCountResponse>> pageRes = statOnlineDaysCount(request, query);
			List<OnlineDaysCountResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//补充vehicleUseType中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				list.forEach(item -> {
					item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
				});
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "车辆在线天数统计报表";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , OnlineDaysCountResponse.class,withDate);
			return R.data(fileName);
		}catch (Exception e){
			log.error("查询失败",e);
			return R.fail("查询失败");
		}
	}


	/**
	 * 服务到期统计
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 */
	@PostMapping("/simExpireInfo")
	public R<IPage<SimExpireResponse>> simExpireInfo(@RequestBody SimExpireRequest request, Query query, BladeUser user){
		try{
			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			request.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
			if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
				vehicleUseTypes.add(request.getVehicleUseType());
			}
			request.setProfessionList(vehicleUseTypes);
			request.setUserId(user.getUserId());
			IPage<SimExpireResponse> page = terminalSimService.findSimExpireInfo(request, query);
			return R.data(page);
		}catch (Exception e){
			log.error("查询失败",e);
			return R.fail("查询失败");
		}
	}


	/**
	 * 服务到期统计 导出
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 */
	@PostMapping("/simExpireInfo/export")
	public R<String> simExpireInfoExport(@RequestBody SimExpireRequest request, Query query, BladeUser user, String exportPath, String deptName){
		try{
			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);
			//1.查询数据
			R<IPage<SimExpireResponse>> pageRes = simExpireInfo(request, query, user);
			List<SimExpireResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//补充中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				Map<String, String> vehicleModelMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_MODEL);
				Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
				list.forEach(item -> {
					item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
					item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
					item.setVehicleModelDesc(vehicleModelMap.get(item.getVehicleModel()));
					item.setAccessModeDesc(accessModeMap.get(item.getAccessMode()));
				});
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "服务到期统计";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , SimExpireResponse.class,withDate);
			return R.data(fileName);
		}catch (Exception e){
			return R.fail("查询失败");
		}
	}


	/**
	 * 车辆运行情况统计
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 */
	@PostMapping("/statLastPosition")
	public R<IPage<VehicleLastPositionResponse>> statLastPosition(@RequestBody CommonBaseRequest request, Query query, BladeUser user){
		if(user == null){
			return R.fail(401,"用户未登录或未授权");
		}
		//1.查询条件准备
		try {
			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			request.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
			if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
				vehicleUseTypes.add(request.getVehicleUseType());
			}
			request.setProfessionList(vehicleUseTypes);
			request.setUserId(user.getUserId());

			//2.统计最终定位点
			IPage<VehicleLastPositionResponse> pageList = statService.findLastPositionInfo(request, query);
			return R.data(pageList);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}

	}


	/**
	 * 车辆运行情况统计 导出
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 */
	@PostMapping("/statLastPosition/export")
	public R<String> statLastPositionExport(@RequestBody CommonBaseRequest request, Query query, BladeUser user,String exportPath, String deptName){
		if(user == null){
			return R.fail(401,"用户未登录或未授权");
		}
		//1.查询条件准备
		try {
				//设置最大页码
				query.setSize(Integer.MAX_VALUE);
				query.setCurrent(1);

				//1.查询数据
				R<IPage<VehicleLastPositionResponse>> pageRes = statLastPosition(request, query, user);
				List<VehicleLastPositionResponse> list = new ArrayList<>();
				if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
					list = pageRes.getData().getRecords();
				}

				//补充中文描述
				//从字典中获取数据
				if(list != null && list.size() > 0){
					Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
					Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
					Map<String, String> vehicleModelMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_MODEL);
					Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
					Map<String, String> onlineStateMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ONLINE_STATE);
					list.forEach(item -> {
						item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
						item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
						item.setVehicleModelDesc(vehicleModelMap.get(item.getVehicleModel()));
						item.setAccessModeDesc(accessModeMap.get(item.getAccessMode()));
						item.setTeStateDesc(onlineStateMap.get(item.getTeState()+""));
					});
				}

				//2.执行导出
				boolean withDate = false;
				if(!StringUtils.isBlank(exportPath)){
					withDate = true;
				}

				String title = "车辆运行情况统计报表";
				String fileName = "";

				fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , VehicleLastPositionResponse.class,withDate);
				return R.data(fileName);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}

	}


	/**
	 * 在线不定位统计
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 */
	@PostMapping("/statNotLocation")
	public R<IPage<NotLocationResponse>> statLastPosition(@RequestBody NotLocationRequest request, Query query, BladeUser user){
		if(user == null){
			return R.fail(401,"用户未登录或未授权");
		}
		//1.查询条件准备
		try {

			if(request.getStartTime()==null||request.getStartTime()==0){
				//如果开始时间为空，则设置为7天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 7);
				long secondTimestamp = DateUtil.getDayFirstSecondTimestamp(date.getTime() / 1000);
				request.setStartTime(secondTimestamp);
			}
			if(request.getEndTime()==null||request.getEndTime()==0){
				//如果结束时间为空，则设置为1天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 1);
				long secondTimestamp = DateUtil.getDayLastSecondTimestamp(date.getTime() / 1000);
				request.setEndTime(secondTimestamp);
			}

			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			request.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
			if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
				vehicleUseTypes.add(request.getVehicleUseType());
			}
			request.setProfessionList(vehicleUseTypes);
			request.setUserId(user.getUserId());

			//2.统计最终定位点
			IPage<NotLocationResponse> pageList = statService.findNotLocationInfo(request, query);
			return R.data(pageList);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}

	/**
	 * 在线不定位统计 导出
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 */
	@PostMapping("/statNotLocation/export")
	public R<String> statNotLocationExport(@RequestBody NotLocationRequest request, Query query, BladeUser user,String exportPath, String deptName){
		if(user == null){
			return R.fail(401,"用户未登录或未授权");
		}
		//1.查询条件准备
		try {
			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<NotLocationResponse>> pageRes = statLastPosition(request, query, user);
			List<NotLocationResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//补充中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
				list.forEach(item -> {
					item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
					item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
					item.setAccessModeDesc(accessModeMap.get(item.getAccessMode()));
					//设置不定位时长
					item.setDurationFormatStr(DateUtil.getFormatDateString(item.getDuration()));
				});
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "在线不定位统计报表";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , NotLocationResponse.class,withDate);
			return R.data(fileName);
		}catch (Exception e){
			log.error("导出出错",e);
			return R.fail("导出出错");
		}
	}


	/**
	 * 异常位移统计
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 */
	@PostMapping("/statErrLocationMove")
	public R<IPage<ErrLocationMoveResponse>> statLastPosition(@RequestBody CommonBaseCrossMonthRequest request, Query query, BladeUser user){

		if(user == null){
			return R.fail(401,"用户未登录或未授权");
		}
		//1.查询条件准备
		try {

			if(request.getStartTime()==null||request.getStartTime()==0){
				//如果开始时间为空，则设置为7天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 7);
				long secondTimestamp = DateUtil.getDayFirstSecondTimestamp(date.getTime() / 1000);
				request.setStartTime(secondTimestamp);
			}
			if(request.getEndTime()==null||request.getEndTime()==0){
				//如果结束时间为空，则设置为1天前
				Date date = DateUtil.getDateBeforeDay(new Date(), 1);
				long secondTimestamp = DateUtil.getDayLastSecondTimestamp(date.getTime() / 1000);
				request.setEndTime(secondTimestamp);
			}

			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			request.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
			if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
				vehicleUseTypes.add(request.getVehicleUseType());
			}
			request.setProfessionList(vehicleUseTypes);
			request.setUserId(user.getUserId());

			//2.统计异常位移
			IPage<ErrLocationMoveResponse> pageList = statService.findErrLocationMoveInfo(request, query);
			return R.data(pageList);
		}catch (Exception e){
			log.error("查询出错",e);
			return R.fail("查询出错");
		}
	}

	/**
	 * 异常位移统计 导出
	 * @param request
	 * @param query
	 * @param user
	 * @return
	 */
	@PostMapping("/statErrLocationMove/export")
	public R<String> statErrLocationMoveExport(@RequestBody CommonBaseCrossMonthRequest request, Query query, BladeUser user, String exportPath, String deptName){
		if(user == null){
			return R.fail(401,"用户未登录或未授权");
		}
		//1.查询条件准备
		try {
			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<ErrLocationMoveResponse>> pageRes = statLastPosition(request, query, user);
			List<ErrLocationMoveResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//补充中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
				list.forEach(item -> {
					item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
					item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
					item.setAccessModeDesc(accessModeMap.get(item.getAccessMode()));
					//设置定位时长
					item.setDurationFormatStr(DateUtil.getFormatDateString(item.getDuration().longValue()));
				});
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "异常位移统计报表";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , ErrLocationMoveResponse.class,withDate);
			return R.data(fileName);
		}catch (Exception e){
			log.error("导出出错",e);
			return R.fail("导出出错");
		}
	}

	/**
	 * 车辆日运行统计
	 * @param cd
	 * @param query
	 * @param user
	 * @return
	 */
	@PostMapping("/companyAllInfo")
	public R<IPage<CompanyAllStatInfoResponse>> companyAllInfoStat(@RequestBody CompanyAndDate cd, Query query, BladeUser user){

		try{
			String date = cd.getDate();
			String thisDay = DateUtil.getDateString();
			if(!StringUtils.isEmpty(date) && Integer.parseInt(date.replace("-","")) > Integer.parseInt(thisDay.replace("-",""))){
				return R.fail("日期不能大于当前天");
			}
			IPage<CompanyAllStatInfoResponse> page = statService.findCompanyAllInfo(cd, query, user);
			return R.data(page);
		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询出错");
		}
	}


	/**
	 * 车辆日运行统计 导出
	 * @param cd
	 * @param query
	 * @param user
	 * @return
	 */
	@PostMapping("/companyAllInfo/export")
	public R<String> companyAllInfoStatExport(@RequestBody CompanyAndDate cd, Query query, BladeUser user,String exportPath, String deptName){

		try{
			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<CompanyAllStatInfoResponse>> pageRes = companyAllInfoStat(cd, query,user);
			List<CompanyAllStatInfoResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}


			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "车辆日运行统计报表";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , CompanyAllStatInfoResponse.class,withDate);
			return R.data(fileName);
		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询出错");
		}
	}


	/**
	 * 轨迹完整率统计
	 * @return
	 */
	@PostMapping("/companyAllRate")
	public R<IPage<CompanyAllRateResponse>> companyAllRateInfoStat(@RequestBody CommonBaseCrossMonthRequest request, Query query, BladeUser user){

		try{
			//1.整理查询条件
			//部门id
			List<Long> deptList = busiUtil.getDeptList(user);
			request.setDeptList(deptList);
			//行业类型
			List<String> vehicleUseTypes = busiUtil.getVehicleUseTypes(request.getVehicleUseType()+"");
			if(vehicleUseTypes == null || vehicleUseTypes.size() < 1){
				vehicleUseTypes.add(request.getVehicleUseType());
			}
			request.setProfessionList(vehicleUseTypes);
			request.setUserId(user.getUserId());

			//2.执行查询
			IPage<CompanyAllRateResponse> page = statService.findCompanyAllRateInfo(request, query, user);
			return R.data(page);

		}catch (Exception e){
			log.error("查询报错",e);
			return R.fail("查询出错");
		}
	}


	/**
	 * 轨迹完整率统计 导出
	 * @return
	 */
	@PostMapping("/companyAllRate/export")
	public R<String> companyAllRateExport(@RequestBody CommonBaseCrossMonthRequest request, Query query, BladeUser user, String exportPath, String deptName){

		try{
			//设置最大页码
			query.setSize(Integer.MAX_VALUE);
			query.setCurrent(1);

			//1.查询数据
			R<IPage<CompanyAllRateResponse>> pageRes = companyAllRateInfoStat(request, query, user);
			List<CompanyAllRateResponse> list = new ArrayList<>();
			if(pageRes != null && pageRes.getData() != null && pageRes.getData().getRecords() != null && pageRes.getData().getRecords().size() > 0){
				list = pageRes.getData().getRecords();
			}

			//补充中文描述
			//从字典中获取数据
			if(list != null && list.size() > 0){
				Map<String, String> vehicleUseTypeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_VEHICLE_USE_TYPE);
				Map<String, String> licenceColorMap = commonBusiUtil.getDictMap(CommonConstant.DICT_LICENCE_COLOR);
				Map<String, String> accessModeMap = commonBusiUtil.getDictMap(CommonConstant.DICT_ACCESS_MODE);
				list.forEach(item -> {
					item.setVehicleUseTypeDesc(vehicleUseTypeMap.get(item.getVehicleUseType()));
					item.setLicenceColorDesc(licenceColorMap.get(item.getLicenceColor()));
					item.setAccessModeDesc(accessModeMap.get(item.getAccessMode()));
				});
			}

			//2.执行导出
			boolean withDate = false;
			if(!StringUtils.isBlank(exportPath)){
				withDate = true;
			}

			String title = "轨迹完整率统计报表";
			String fileName = "";

			fileName = EasyExcelUtils.export(StringUtils.isBlank(exportPath)?staticFilePath:exportPath , proxyFilePath , StringUtils.isBlank(deptName)?title:title+"_"+deptName , list , CompanyAllRateResponse.class,withDate);
			return R.data(fileName);

		}catch (Exception e){
			log.error("导出报错",e);
			return R.fail("导出出错");
		}
	}


	private List<OnlineDaysCountResponse> getTotalMileage(long startTime, long endTime, List<Long> vehicleIds) throws Exception{
		//1.判断是否跨月
		List<String> monthList = DateUtil.getMonthList(startTime, endTime);

		//2.对每个月份进行查询
		List<VehicleAndMileage> list = new ArrayList<>();
		//判断是否跨月
		if(monthList.size() < 2){
			//如果没有跨月，则只查询当月
			String startDateStr = DateUtil.getDateString(startTime);
			String endDateStr = DateUtil.getDateString(endTime);
			String month = startDateStr.substring(0,7);
			List<String> dateList = DateUtil.getDateList(startDateStr, endDateStr);
			List<String> dateListTmp = new ArrayList<>();
			for(String date : dateList){
				dateListTmp.add(date.replace("-",""));
			}
			List<VehicleAndMileage> tmpList = vehicleService.findTotalMileage(dateListTmp, vehicleIds, month.replace("-",""));
			list.addAll(tmpList);
		}else{
			for(int i = 0 ; i < monthList.size(); i++){
				String sqlTmp = "";
				if(i == 0){
					//如果是第一个月，则为startDate 到月末
					String startDateStr = DateUtil.getDateString(startTime);
					String endDateStr = DateUtil.getDateString(DateUtil.getMonthLastSecondTimestamp(startTime));
					String month = startDateStr.substring(0,7);
					List<String> dateList = DateUtil.getDateList(startDateStr, endDateStr);
					dateList.stream().map(item -> item.replace("-","")).collect(Collectors.toList());
					List<VehicleAndMileage> tmpList = vehicleService.findTotalMileage(dateList, vehicleIds, month.replace("-",""));
					list.addAll(tmpList);
				}else if(i == monthList.size()-1){
					//如果是最后一个月，则为月初到endDate
					String startDateStr = DateUtil.getDateString(DateUtil.getMonthFirstSecondTimestamp(endTime));
					String endDateStr = DateUtil.getDateString(endTime);
					String month = startDateStr.substring(0,7);
					List<String> dateList = DateUtil.getDateList(startDateStr, endDateStr);
					dateList.stream().map(item -> item.replace("-","")).collect(Collectors.toList());
					List<VehicleAndMileage> tmpList = vehicleService.findTotalMileage(dateList, vehicleIds, month.replace("-",""));
					list.addAll(tmpList);
				}else{
					//中间月份，从月初到月末
					String month = monthList.get(i);
					String startDateStr = month +"-01";
					String endDateStr = DateUtil.getMonthLastDateStr(month);
					List<String> dateList = DateUtil.getDateList(startDateStr, endDateStr);
					dateList.stream().map(item -> item.replace("-","")).collect(Collectors.toList());
					List<VehicleAndMileage> tmpList = vehicleService.findTotalMileage(dateList, vehicleIds, month.replace("-",""));
					list.addAll(tmpList);
				}
			}
		}

		//计算车辆的总里程
		Map<String,List<VehicleAndMileage>> map = new HashMap<>();
		list.forEach(item -> {
			String key = item.getLicencePlate()+"~"+item.getLicenceColor();
			if(map.get(key) == null){
				List<VehicleAndMileage> listT = new ArrayList<>();
				listT.add(item);
				map.put(key, listT);
			}else{
				map.get(key).add(item);
			}
		});

		List<OnlineDaysCountResponse> orList = new ArrayList<>();

		map.forEach((k,v)->{
			String licencePlate = k.split("~")[0];
			String licenceColor = k.split("~")[1];
			double mileage = 0;
			for(VehicleAndMileage vm : v){
				mileage += vm.getMileage();
			}
			OnlineDaysCountResponse or = new OnlineDaysCountResponse();
			or.setLicencePlate(licencePlate);
			or.setLicenceColorCode(licenceColor);
			or.setTotalMileage(MathUtil.roundDouble(mileage/1000, 2));
			orList.add(or);
		});

		return orList;
	}


	private List<OnlineDaysCountResponse> getOnlineDaysCount(long startTime, long endTime, List<Long> vehicleIds) throws Exception{
		//1.判断是否跨月
		List<String> monthList = DateUtil.getMonthList(startTime, endTime);

		//list转为字符串
		StringBuffer vehicleIdStrBuffer = new StringBuffer();
		if(vehicleIds.size() > 0){
			for(int i = 0 ; i < vehicleIds.size()-1; i++){
				vehicleIdStrBuffer.append(vehicleIds.get(i)).append(",");
			}
			vehicleIdStrBuffer.append(vehicleIds.get(vehicleIds.size()-1));
		}

		String sql = "";
		//2.对每个月份进行sql拼接
		//判断是否跨月
		if(monthList.size() < 2){
			//如果没有跨月，则只查询当月
			String startDateStr = DateUtil.getDateString(startTime);
			String endDateStr = DateUtil.getDateString(endTime);
			String month = startDateStr.substring(0,7);
			sql = getOnlineDaysCountSqlForOneMonth(startDateStr, endDateStr, month , vehicleIdStrBuffer.toString());
		}else{
			for(int i = 0 ; i < monthList.size(); i++){
				String sqlTmp = "";
				if(i == 0){
					//如果是第一个月，则为startDate 到月末
					String startDateStr = DateUtil.getDateString(startTime);
					String endDateStr = DateUtil.getDateString(DateUtil.getMonthLastSecondTimestamp(startTime));
					String month = startDateStr.substring(0,7);
					sqlTmp = getOnlineDaysCountSqlForOneMonth(startDateStr, endDateStr, month , vehicleIdStrBuffer.toString());
				}else if(i == monthList.size()-1){
					//如果是最后一个月，则为月初到endDate
					String startDateStr = DateUtil.getDateString(DateUtil.getMonthFirstSecondTimestamp(endTime));
					String endDateStr = DateUtil.getDateString(endTime);
					String month = startDateStr.substring(0,7);
					sqlTmp = getOnlineDaysCountSqlForOneMonth(startDateStr, endDateStr, month , vehicleIdStrBuffer.toString());
				}else{
					//中间月份，从月初到月末
					String month = monthList.get(i);
					String startDateStr = month +"-01";
					String endDateStr = DateUtil.getMonthLastDateStr(month);
					sqlTmp = getOnlineDaysCountSqlForOneMonth(startDateStr, endDateStr, month , vehicleIdStrBuffer.toString());
				}
				if(i == 0){
					sql = sqlTmp;
				}else{
					sql = sql + " union " + sqlTmp;
				}
			}
		}

		//执行sql
		List<OnlineDaysCountResponse> list = vehicleService.findCountByDynamicSql(sql);

		return list;
	}

	private String getOnlineDaysCountSqlForOneMonth(String startDateStr, String endDateStr, String month, String vehicleIdStrBuffer )throws Exception{

		List<String> dateList = DateUtil.getDateList(startDateStr, endDateStr);
		StringBuffer sb = new StringBuffer("select cvor.licence_plate,cvor.access_mode, cvor.licence_color_code, cvor.licence_color, (");

		for(int i = 0 ; i < dateList.size() -1 ; i++){
			String dateStr = dateList.get(i);
			String day = dateStr.substring(8,10);
			sb.append(" if(d"+day+">0,1,0)").append(" + ");
		}
		sb.append(" if(d"+dateList.get(dateList.size()-1).substring(8,10)+">0,1,0)");
		sb.append(") onlineDaysCount from cache_vehicle_online_rate cvor, bdm_vehicle bv \n" +
			"where cvor.licence_plate = bv.licence_plate and cvor.licence_color_code = bv.licence_color\n" +
			"and month = '"+month+"' and bv.id in ("+vehicleIdStrBuffer+")");
		return sb.toString();
	}



}
