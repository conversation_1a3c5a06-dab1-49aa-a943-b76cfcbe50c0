package com.xh.vdm.statistic.vo.response;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.*;
import com.alibaba.excel.enums.poi.BorderStyleEnum;
import com.alibaba.excel.enums.poi.FillPatternTypeEnum;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Description: 异常定位点明细 对象
 * @Author: zhouxw
 * @Date: 2022/9/6 6:10 PM
 */
@Data
@ContentRowHeight(20)
@HeadRowHeight(20)
@ColumnWidth(20)
public class LocationQualityDetailResponse {

    //企业名称（支持模糊查询）
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ColumnWidth(30)
    @ExcelProperty({"不合格车辆明细", "企业名称"})
    private String deptName;

    //车牌号
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"不合格车辆明细", "车牌号"})
    private String licencePlate;

    //车牌颜色
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"不合格车辆明细", "车牌颜色"})
    @JsonProperty("licenceColor")
    private String licenceColor;

    //SIM卡号
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"不合格车辆明细", "SIM卡号"})
    private String simId;

    //终端ID
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"不合格车辆明细", "终端ID"})
    private String terminalId;

    //终端型号
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"不合格车辆明细", "终端型号"})
    private String terminalModel;

    //经度
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"不合格车辆明细", "经度"})
    private double longitude;

    //纬度
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"不合格车辆明细", "纬度"})
    private double latitude;

    //海拔
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"不合格车辆明细", "高程（米）"})
    private int mileage;

    //速度
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"不合格车辆明细", "速度（km/h）"})
    private int speed;

    //方向角
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ExcelProperty({"不合格车辆明细", "方向角"})
    private int bearing;

    //定位时间（原始数据）
    @ExcelIgnore
    private long timeOri;

    //定位时间（格式化后的日期）
    @HeadStyle(fillPatternType = FillPatternTypeEnum.SOLID_FOREGROUND, fillForegroundColor = 9)
    @ContentStyle(borderLeft = BorderStyleEnum.THIN,borderRight = BorderStyleEnum.THIN,borderBottom = BorderStyleEnum.THIN)
    @HeadFontStyle(fontHeightInPoints = 12)
    @ContentFontStyle(fontHeightInPoints = 12)
    @ColumnWidth(25)
    @ExcelProperty({"不合格车辆明细", "定位时间"})
    private String time;

}
