server:
  port: 20007 #server

spring:
  application:
    name: bt-statistics
  activiti:
    check-process-definitions: false
    database-schema-update: true
    history-level: full
    db-history-used: true
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          #生产环境
      #    url: **************************************************************************************************************************************************************************************************
          #测试环境
          #url: **************************************************************************************************************************************************************************************************
          #开发环境
          url: ${blade.datasource.dev.url}
          username: ${blade.datasource.dev.username}
          password: ${blade.datasource.dev.password}
          driver-class-name: com.mysql.cj.jdbc.Driver
          hikari:
            auto-commit: true
            pool-name: DatebookHikariCP
            minimum-idle: 5
            maximum-pool-size: 80
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 400000
            # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 500000
            # 数据库连接超时时间,默认30秒，即30000。配置3s。
            connection-timeout: 60000
            connection-test-query: SELECT 1
            validation-timeout: 10000
        location:
          #测试环境
          #url: ***************************************************************************************************************************************************************************************************
          #开发环境：
          url: ***********************************
          username:
          password:
          driver-class-name: com.cloudera.impala.jdbc41.Driver
          hikari:
            auto-commit: true
            pool-name: DatebookHikariCP
            minimum-idle: 5
            maximum-pool-size: 80
            # 空闲连接存活最大时间，默认600000（10分钟）
            idle-timeout: 400000
            # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
            max-lifetime: 500000
            # 数据库连接超时时间,默认30秒，即30000。配置3s。
            connection-timeout: 60000
            connection-test-query: SELECT 1
            validation-timeout: 10000

        impala:
          driver-class-name: com.cloudera.impala.jdbc41.Driver
          url: ***********************************
          username:
          password:
          # Druid连接池配置
          type: com.alibaba.druid.pool.DruidDataSource
          druid:
            # 初始化
            initial-size: 30
            # 最大
            max-wait: 200
            # 最小
            min-idle: 3
            # 最大连接等待超时时间
            max-active: 60000
            # 周期性剔除长时间呆在池子里未被使用的空闲连接, 1 min 一次,单位毫秒
            time-between-eviction-runs-millis: 60000
            # 配置一个连接在池中最小生存的时间,单位是毫秒
            min-evictable-idle-time-millis: 300000
            # 设置连接在池中最大存活时长，超过上限才会被清理
            max-evictable-idle-time-millis: 600000
            # 验证连接是否可用，使用的SQL语句
            validation-query: SELECT 'x'
            # 连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
            test-while-idle: false
            # 借出连接时不要测试，否则很影响性能
            test-on-borrow: false
            # 指明是否在归还到池中前进行检验
            test-on-return: false

    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      auto-commit: true
      pool-name: DatebookHikariCP
      minimum-idle: 5
      maximum-pool-size: 80
      # 空闲连接存活最大时间，默认600000（10分钟）
      idle-timeout: 400000
      # 池中连接的最长生命周期，值0表示无限生命周期，默认1800000即30分钟
      max-lifetime: 500000
      # 数据库连接超时时间,默认30秒，即30000。配置3s。
      connection-timeout: 60000
      connection-test-query: SELECT 1
      validation-timeout: 10000


  # Redis
  redis:
    host: ************ #************* #*********** #redis
    database: 8
    #port: 20302 #redis -- 测试环境
    port: 20716  # -- 开发环境
    password: xh123456
    timeout: 5000
    jedis:
      pool:
        max-active: 8
        max-idle: -1
    lettuce:
      pool:
        max-idle: 8
        min-idle: 0

  jackson:
      #日期格式化
      date-format: yyyy-MM-dd HH:mm:ss
      time-zone: GMT+8
      serialization:
        #格式化输出
        indent_output: true
        #忽略无法转换的对象
        fail_on_empty_beans: false
        #设置空如何序列化
        #default-property-inclusion: ALWAYS

      deserialization:
        #允许对象忽略json中不存在的属性
        fail_on_unknown_properties: false
      parser:
        #允许出现特殊字符和转义符
        allow_unquoted_control_chars: true
        #允许出现单引号
        allow_single_quotes: true
  mail:
    default-encoding: utf-8
    host: smtp.qq.com
    username: <EMAIL>
    password: zhcqriegfddpeahg
    port: 587



# actuator状态监控配置
#management:
#  endpoints:
#    web:
#      exposure:
#        include: "*"
##  server:
##    port: 10111
##    servlet:
##      context-path: /
##    ssl:
##      enabled: false
#  endpoint:
#    health:
#      show-details: always

logging:
  level:
    root: info
    com.xh.vdm.statistic.mapper.*: info

#报表导出临时地址
static:
  file:
    #path: /home/<USER>/file/alarm/
    path: E:/tmp/

#前端代理nginx路径
proxy:
  file:
    #path: /vdm/alarm/files/
    path: C:/Users/<USER>/Downloads/tmp/

#每日统计数据，邮件发送给客户，数据临时地址
email:
  file:
    tmp_path: C:/Users/<USER>/Desktop/tmp/tmp_path/
  debug: true #debug模式，开启后，只发送 max_count 数量的邮件，防止发送邮件过多，邮箱被封
  max_count: 3 #debug模式下可以发送的邮件数量

map:
  gaode:
    host: https://restapi.amap.com/v3/geocode/regeo?output=json&location=<points>&key=<key>&radius=1000&extensions=base&batch=true
    key: 0d8b91186d173045f186e337cb1289f8



#跑批任务过程日志开关，可用于线上观察运行情况，日志写入到 stat_task_process_log 表中，使用时，可先清空该表
task:
  process:
    log:
      enable: true




#由定时任务统计报表时，默认的系统人员名称
default:
  admin_name: admin

#是否开启DB缓存
db_cache:
  enable: true


#xxl-job
xxl:
  job:
    ### xxl-job, access token
    accessToken: default_token
    ### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
    admin:
      addresses: http://***********:8090/xxl-job-admin
    ### xxl-job executor appname
    executor:
      appname: vdm-statistic-executor
      ### xxl-job executor registry-address: default use address to registry , otherwise use ip:port if address is null
      address:
      ### xxl-job executor server-info
      ip:
      port: 9998
      ### xxl-job executor log-path
      logpath: /data/applogs/xxl-job/jobhandler
      ### xxl-job executor log-retention-days
      logretentiondays: 30


connection-rate:
  ip: **************
  port: 20403
  url: /regulatorycenter/assessment/connectivity

mybatis-plus:
  #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    enable-sql-runner: false
