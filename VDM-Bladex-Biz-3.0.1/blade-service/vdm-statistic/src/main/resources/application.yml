spring:
  application:
    name: bt-statistics
  profiles:
      active: dev




#mybatis-plus配置
mybatis-plus:
  #Mapper路径
  # 如果是放在src/main/java目录下 classpath:/com/yourpackage/*/mapper/*Mapper.xml
  # 如果是放在resource目录 classpath:/mapper/*Mapper.xml
  mapper-locations: classpath:/com/xh/statistics/mapper/*Mapper.xml
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.xh.vdm.statistic.entity
  configuration:
    #这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    #log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    #数据库字段与数据对象字段的映射策略：在下划线和驼峰间进行转换
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    db-config:
      #主键类型  0:"数据库ID自增", 1:"用户输入ID",2:"全局唯一ID (数字类型唯一ID)", 3:"全局唯一ID UUID";
      #id-type: id_worker
      # 字段策略
      #insert-strategy: not_null
      #update-strategy: not_null
      #select-strategy: not_null
      #驼峰下划线转换
      table-underline: true
      #数据库大写下划线转换
      #capital-mode: true
      #逻辑删除配置(默认是 1 和 0)
      #logic-delete-value: E
      #logic-not-delete-value: U
      #数据库类型。支持主流的数据库
      db-type: mysql
      enable-sql-runner: true
    #刷新mapper 调试神器
    refresh: true
    #sql-injector: com.baomidou.mybatisplus.extension.injector.LogicSqlInjector
    #自定义填充策略接口实现
    #meta-object-handler: com.baomidou.springboot.xxx
    #自定义SQL注入器
    #sql-injector: com.baomidou.springboot.xxx

